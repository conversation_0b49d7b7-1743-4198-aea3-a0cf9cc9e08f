package com.wunding.learn.common.jwt;

import com.wunding.learn.common.constant.user.UserErrorNoEnum;
import com.wunding.learn.common.exception.BusinessException;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.util.Base64;
import java.util.Date;
import java.util.Map;
import javax.crypto.spec.SecretKeySpec;
import lombok.extern.slf4j.Slf4j;

/**
 * Secure工具类
 *
 * <AUTHOR>
 * @date 2022/02/21
 */
@Slf4j
public class SecureUtil {

    private SecureUtil() {
    }


    private static String getBase64Security(String signKey) {
        return Base64.getEncoder().encodeToString(signKey.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 解析jsonWebToken
     *
     * @param jsonWebToken jsonWebToken
     * @return Claims
     */
    public static Claims parseJwt(String jsonWebToken, String signKey) {
        try {
            return Jwts.parserBuilder()
                .setSigningKey(Base64.getDecoder().decode(getBase64Security(signKey)))
                .build()
                .parseClaimsJws(jsonWebToken)
                .getBody();
        } catch (Exception ex) {
            log.warn("解析jsonWebToken异常，{}", ex.getMessage());
            throw new BusinessException(UserErrorNoEnum.ERR_JWT);
        }
    }

    /**
     * 创建令牌
     *
     * @param user     user
     * @param audience audience 受众
     * @param issuer   issuer 签发人
     * @param expire   失效时长
     * @return jwt
     */
    public static TokenInfo createJwt(
        Map<String, String> user, String audience, String issuer, Integer expire, String signKey) {

        SignatureAlgorithm signatureAlgorithm = SignatureAlgorithm.HS256;

        long nowMillis = System.currentTimeMillis();
        // 让token提前5秒生效，防止服务器间时间差异
        Date now = new Date(nowMillis - 5000);

        // 生成签名密钥
        byte[] apiKeySecretBytes = Base64.getDecoder().decode(getBase64Security(signKey));
        Key signingKey = new SecretKeySpec(apiKeySecretBytes, signatureAlgorithm.getJcaName());

        // 添加构成JWT的类
        JwtBuilder builder =
            Jwts.builder()
                .setHeaderParam("typ", "JWT")
                .setIssuer(issuer)
                .setAudience(audience)
                .signWith(signingKey);

        // 设置JWT参数
        user.forEach(builder::claim);

        // 添加Token过期时间
        long expMillis = nowMillis + (expire * 1000L);
        Date exp = new Date(expMillis);
        builder.setExpiration(exp).setNotBefore(now);

        // 组装Token信息
        TokenInfo tokenInfo = new TokenInfo();
        tokenInfo.setToken(builder.compact());
        tokenInfo.setExpire(expire);

        return tokenInfo;
    }
}
