package com.wunding.learn.flowable.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * <p> 资源审核计数dto
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2025-06-12
 */
@Data
@Schema(name = "ResourceProcessCountDTO", description = "资源审核计数dto")
public class ResourceProcessCountDTO {

    @Schema(description = "我的申请数量")
    private long myApplyCount;

    @Schema(description = "我的处理数量")
    private long myDealCount;

    @Schema(description = "全部的申请数量")
    private long allApplyCount;

    @Schema(description = "全部数量 应该没什么用")
    private long allCount;

}
