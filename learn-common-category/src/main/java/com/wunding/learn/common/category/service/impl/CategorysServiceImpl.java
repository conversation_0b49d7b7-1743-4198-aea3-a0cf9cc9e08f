package com.wunding.learn.common.category.service.impl;

import static com.wunding.learn.common.util.string.StringUtil.newId;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.wunding.learn.common.category.dao.CategorysDao;
import com.wunding.learn.common.category.dto.CategoryChangeSortDTO;
import com.wunding.learn.common.category.dto.CategoryMonolayerDTO;
import com.wunding.learn.common.category.dto.CategoryResultDTO;
import com.wunding.learn.common.category.mapper.CategorysMapper;
import com.wunding.learn.common.category.model.Categorys;
import com.wunding.learn.common.category.service.ICategorysService;
import com.wunding.learn.common.constant.course.CourseErrorNoEnum;
import com.wunding.learn.common.constant.example.ExampleErrorNoEnum;
import com.wunding.learn.common.constant.other.AvailableEnum;
import com.wunding.learn.common.constant.other.ErrorNoEnum;
import com.wunding.learn.common.constant.user.UserErrorNoEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.dto.CategoryLevelPathUpdateDTO;
import com.wunding.learn.common.dto.CompanySizeVO;
import com.wunding.learn.common.dto.IndustryVO;
import com.wunding.learn.common.dto.SysCategoryDTO;
import com.wunding.learn.common.dto.SysCategorySaveDTO;
import com.wunding.learn.common.enums.category.CategoryType;
import com.wunding.learn.common.enums.other.CategoryTypeEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.i18n.util.I18nUtil;
import com.wunding.learn.common.mybatis.service.impl.BaseServiceImpl;
import com.wunding.learn.common.query.SysCategoryQuery;
import com.wunding.learn.common.util.bean.BeanListUtils;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.util.string.TranslateUtil;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.dto.AbstractExportDataDTO;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

/**
 * <p> 分类管理表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2022-08-02
 */
@Slf4j
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Service("categorysService")
public class CategorysServiceImpl extends BaseServiceImpl<CategorysMapper, Categorys> implements ICategorysService {

    private static final String TAG_SEPARATOR = "/";
    private static final Integer LIMIT_LENGTH = 5;
    private static final Integer LIMIT_LENGTH_V2 = 3;
    private static final String CATEGORYS_SERVICE = "categorysService";
    private static final String DEFAULT_LEVEL_PATH = "全部分类/";
    private static final String CATEGORY_TYPE = "categoryType";
    public static final String CATE_MSG_INFO = ",分类信息:{}";
    public static final String SAVE_OR_UPDATE_CATEGORY_ARG = "save_or_update_category_arg:{}";
    public static final String CATEGORYS_SERVICE_MUST_NOT_BE_NULL = "categorysService_must_not_be_null";
    public static final String DEFAULT_SYS_TAG_CATEGORYS = "default_sys_tag_categorys";

    private final ExportComponent exportComponent;

    @Resource(name = "categorysDao")
    private CategorysDao categorysDao;

    @Override
    public boolean save(Categorys entity) {
        return categorysDao.saveCategory(entity);
    }

    @Override
    public boolean updateById(Categorys entity) {
        return categorysDao.updateCategory(entity);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateCategory(SysCategorySaveDTO saveOrUpdateCategoryDTO) {
        boolean result;
        log.info(SAVE_OR_UPDATE_CATEGORY_ARG, saveOrUpdateCategoryDTO);
        // 验证名称分类是否唯一
        checkTagCategoryOnly(saveOrUpdateCategoryDTO);
        checkoutCategoryParentId(saveOrUpdateCategoryDTO.getParentId(), 1);
        ICategorysService bean = SpringUtil.getBean(CATEGORYS_SERVICE, ICategorysService.class);
        Assert.notNull(bean, CATEGORYS_SERVICE_MUST_NOT_BE_NULL);
        if (StringUtils.isNotEmpty(saveOrUpdateCategoryDTO.getId())) {
            result = bean.updateCategory(saveOrUpdateCategoryDTO);
        } else {
            result = bean.saveCategory(saveOrUpdateCategoryDTO);
        }
        return result;
    }

    /**
     * 保存或更新分类
     *
     * @param saveOrUpdateCategoryDTO 保存分类dto
     * @return 执行结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateCategoryV2(SysCategorySaveDTO saveOrUpdateCategoryDTO) {
        boolean result;
        log.info(SAVE_OR_UPDATE_CATEGORY_ARG, saveOrUpdateCategoryDTO);
        // 验证名称分类是否唯一
        checkTagCategoryOnly(saveOrUpdateCategoryDTO);
        checkoutCategoryParentId(saveOrUpdateCategoryDTO.getParentId(), 2);
        ICategorysService bean = SpringUtil.getBean(CATEGORYS_SERVICE, ICategorysService.class);
        Assert.notNull(bean, CATEGORYS_SERVICE_MUST_NOT_BE_NULL);
        if (StringUtils.isNotEmpty(saveOrUpdateCategoryDTO.getId())) {
            result = bean.updateCategoryV2(saveOrUpdateCategoryDTO);
        } else {
            result = bean.saveCategoryV2(saveOrUpdateCategoryDTO);
        }
        return result;
    }

    @Override
    public boolean updateCategory(SysCategorySaveDTO updateCategory) {
        Categorys category = new Categorys();
        BeanUtils.copyProperties(updateCategory, category);
        // 对于被引用的分类不能进行启用禁用
        if (isQuote(updateCategory.getId())) {
            log.error(ExampleErrorNoEnum.ERR_CATEGORY_NOT_OPERATION.getMessage() + CATE_MSG_INFO, category);
            category.setIsAvailable(null);
        }
        categorysDao.updateCategory(category);
        return updateLevelPath(category.getId());
    }


    @Override
    public boolean updateCategoryV2(SysCategorySaveDTO updateCategory) {
        Categorys category = new Categorys();
        BeanUtils.copyProperties(updateCategory, category);
        // 对于被引用的分类不能进行启用禁用
        if (isQuote(updateCategory.getId())) {
            log.error(ExampleErrorNoEnum.ERR_CATEGORY_NOT_OPERATION.getMessage() + CATE_MSG_INFO, category);
            category.setIsAvailable(null);
        }

        // 编辑分类 - 保存前，排序值处理
        Categorys old = baseMapper.selectById(updateCategory.getId());
        if (updateCategory.getSortNo() != null && old != null && !Objects.equals(updateCategory.getSortNo(),
            old.getSortNo())) {
            if (updateCategory.getSortNo() > old.getSortNo()) {
                // 如果排序值增大，往后移动，则旧序号和新序号之间的活动的序号减1，往前移动
                baseMapper.moveSortNo(updateCategory.getCategoryType(), old.getSortNo(), updateCategory.getSortNo(), 1);
            } else {
                // 如果排序值减少，向前移动，则旧序号和新序号之间的活动的序号加1，往后移动
                baseMapper.moveSortNo(updateCategory.getCategoryType(), updateCategory.getSortNo(), old.getSortNo(), 2);
            }
        }

        categorysDao.updateCategory(category);
        return updateLevelPath(category.getId());
    }

    @Override
    public String updateCategoryV3(SysCategorySaveDTO updateCategory) {
        Categorys category = new Categorys();
        BeanUtils.copyProperties(updateCategory, category);
        // 对于被引用的分类不能进行启用禁用
        if (isQuote(updateCategory.getId())) {
            log.error(ExampleErrorNoEnum.ERR_CATEGORY_NOT_OPERATION.getMessage() + CATE_MSG_INFO, category);
            category.setIsAvailable(null);
        }

        // 编辑分类 - 保存前，排序值处理
        Categorys old = baseMapper.selectById(updateCategory.getId());
        if (updateCategory.getSortNo() != null && old != null && !Objects.equals(updateCategory.getSortNo(),
            old.getSortNo())) {
            if (updateCategory.getSortNo() > old.getSortNo()) {
                // 如果排序值增大，往后移动，则旧序号和新序号之间的活动的序号减1，往前移动
                baseMapper.moveSortNo(updateCategory.getCategoryType(), old.getSortNo(), updateCategory.getSortNo(), 1);
            } else {
                // 如果排序值减少，向前移动，则旧序号和新序号之间的活动的序号加1，往后移动
                baseMapper.moveSortNo(updateCategory.getCategoryType(), updateCategory.getSortNo(), old.getSortNo(), 2);
            }
        }

        categorysDao.updateCategory(category);
        updateLevelPath(category.getId());
        return category.getId();
    }

    @Override
    public boolean saveCategory(SysCategorySaveDTO saveCategory) {
        Categorys category = new Categorys();
        saveCategory.setId(newId());
        BeanUtils.copyProperties(saveCategory, category);
        category.setOrgId(UserThreadContext.getOrgId());
        category.setIsCanDelete(1);
        categorysDao.saveCategory(category);
        return updateLevelPath(category.getId());
    }


    @Override
    public boolean saveCategoryV2(SysCategorySaveDTO saveCategory) {
        Categorys category = new Categorys();
        saveCategory.setId(newId());
        BeanUtils.copyProperties(saveCategory, category);
        category.setOrgId(UserThreadContext.getOrgId());
        category.setIsCanDelete(1);

        // 新增分类 - 保存前，排序值处理：大于等于当前排序值的分类排序值 +1，排序值不可超过9999
        baseMapper.moveSortNo(saveCategory.getCategoryType(), saveCategory.getSortNo(), null, 2);

        categorysDao.saveCategory(category);
        return updateLevelPath(category.getId());
    }

    @Override
    public String saveCategoryV3(SysCategorySaveDTO saveCategory) {
        Categorys category = new Categorys();
        String newId = newId();
        saveCategory.setId(newId);
        BeanUtils.copyProperties(saveCategory, category);
        category.setOrgId(UserThreadContext.getOrgId());
        category.setIsCanDelete(1);

        // 新增分类 - 保存前，排序值处理：大于等于当前排序值的分类排序值 +1，排序值不可超过9999
        baseMapper.moveSortNo(saveCategory.getCategoryType(), saveCategory.getSortNo(), null, 2);

        categorysDao.saveCategory(category);
        updateLevelPath(category.getId());
        return newId;
    }

    @Override
    public List<Categorys> findAllCategory(Collection<String> idList) {
        return baseMapper.selectAllCategory(idList);
    }

    private String getTotalLevelPath(String id) {
        Categorys categorys = baseMapper.selectById(id);
        String totalLevelPath = id + TAG_SEPARATOR;
        totalLevelPath = TAG_SEPARATOR + getLevelPath(categorys.getParentId(), totalLevelPath);
        return totalLevelPath;
    }

    private String getLevelPath(String parentId, String totalLevelPath) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(parentId)) {
            return totalLevelPath;
        }
        Categorys categorys = baseMapper.selectById(parentId);
        totalLevelPath = categorys.getId() + TAG_SEPARATOR + totalLevelPath;
        if (org.apache.commons.lang3.StringUtils.isEmpty(categorys.getParentId())) {
            return totalLevelPath;
        }
        totalLevelPath = getLevelPath(categorys.getParentId(), totalLevelPath);
        return totalLevelPath;
    }

    /**
     * 更新全路径
     *
     * @param id 分类id
     */
    private boolean updateLevelPath(String id) {
        String totalLevelPath = getTotalLevelPath(id);

        LambdaUpdateWrapper<Categorys> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(Categorys::getLevelPath, totalLevelPath);
        updateWrapper.eq(Categorys::getId, id);
        return update(updateWrapper);
    }

    @Override
    public PageInfo<SysCategoryDTO> findCategoryManageListByPage(SysCategoryQuery categoryQuery) {
        // 兼容单体项目分类管理-判断下查询类别，如果没有带指定的类型，则查询下列类型
        List<String> categoryTypeList = new ArrayList<>();
        categoryTypeList.add(CategoryTypeEnum.TrainType.getType());
        categoryTypeList.add(CategoryTypeEnum.ActivityType.getType());
        categoryTypeList.add(CategoryTypeEnum.SupplierTerritory.getType());
        categoryTypeList.add(CategoryTypeEnum.SupplierFile.getType());
        categoryTypeList.add(CategoryTypeEnum.FormTemplateCate.getType());
        categoryTypeList.add(CategoryTypeEnum.TrainWithoutExam.getType());
//        categoryTypeList.add(CategoryTypeEnum.AllocationType.getType())
        categoryTypeList.add(CategoryTypeEnum.ExamCompetitionTrainType.getType());
        categoryTypeList.add(CategoryTypeEnum.LearnMapSpecialty.getType());
        categoryTypeList.add(CategoryTypeEnum.TrainSubjectCategory.getType());
        categoryTypeList.add(CategoryTypeEnum.IndustryType.getType());
        categoryTypeList.add(CategoryTypeEnum.CompanySize.getType());
        categoryTypeList.add(CategoryTypeEnum.ASSESS_PROJECT.getType());
        categoryTypeList.add(CategoryTypeEnum.APPLICABLE_HIERARCHY.getType());
        categoryTypeList.add(CategoryTypeEnum.USAGE_CLASSIFICATION.getType());
        categoryTypeList.add(CategoryTypeEnum.CourseWithoutResource.getType());
        categoryTypeList.add(CategoryTypeEnum.EXAMPLE_BUSINESS.getType());
        categoryTypeList.add(CategoryTypeEnum.ExampleAudit.getType());

        categoryQuery.setCategoryTypeList(categoryTypeList);
        return findCategoryByPage(categoryQuery);
    }

    @Override
    public PageInfo<SysCategoryDTO> findCategoryByPage(SysCategoryQuery categoryQuery) {
        PageInfo<SysCategoryDTO> pageList = PageMethod.startPage(categoryQuery.getPageNo(), categoryQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.selectCategoryByPage(categoryQuery));
        pageList.getList().forEach(p -> {
            String pathName = getPathName(p);
            p.setLevelPathName(pathName);
            p.setParentName(pathName);
        });
        // 课程分类特殊处理
        if (categoryQuery.getExport() && StringUtils.isBlank(categoryQuery.getParentId())
            && categoryQuery.getCategoryType().equals(CategoryType.CourseCate.toString())
            && StringUtils.isBlank(categoryQuery.getKeyWord())) {
            SysCategoryDTO sysCategoryDTO = new SysCategoryDTO();
            sysCategoryDTO.setCategoryName("全部分类");
            sysCategoryDTO.setLevelPath(DEFAULT_LEVEL_PATH);
            sysCategoryDTO.setSortNo(0);
            sysCategoryDTO.setIsAvailable(1);
            sysCategoryDTO.setParentName(DEFAULT_LEVEL_PATH);
            pageList.getList().forEach(dto -> {
                dto.setParentId("all");
                dto.setParentName(DEFAULT_LEVEL_PATH + dto.getParentName());
            });
            pageList.getList().add(0, sysCategoryDTO);
        }
        return pageList;
    }

    @Override
    public PageInfo<SysCategoryDTO> findExportCategoryByPage(SysCategoryQuery categoryQuery) {
        PageInfo<SysCategoryDTO> pageList = PageMethod.startPage(categoryQuery.getPageNo(), categoryQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.selectCategoryByPage(categoryQuery));
        pageList.getList().forEach(p -> {
            String pathName = getPathName(p);
            p.setLevelPathName(pathName);
            p.setParentName(pathName);
        });
        return pageList;
    }

    private String getPathName(SysCategoryDTO p) {
        String path = p.getLevelPath();
        StringBuilder pathName = new StringBuilder();
        if (StringUtils.isNotBlank(path)) {
            String[] ids = path.split("/");
            for (String id : ids) {
                if (StringUtils.isNotBlank(id)) {
                    Categorys categorys = baseMapper.selectById(id);
                    if (categorys != null) {
                        pathName.append(categorys.getCategoryName() + TAG_SEPARATOR);
                    }
                }
            }
        }
        return pathName.toString();
    }

    @Override
    public PageInfo<SysCategoryDTO> findManageCategoryByPage(SysCategoryQuery categoryQuery) {
        return PageMethod.startPage(categoryQuery.getPageNo(), categoryQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.selectManageCategoryByPage(categoryQuery));
    }

    @Override
    public List<SysCategoryDTO> allCategory(String categoryId, String categoryType) {
        return  baseMapper.selectCategoryByPage(new SysCategoryQuery().setCategoryType(categoryType));
    }

    @Override
    public List<SysCategoryDTO> allCategory(SysCategoryQuery categoryQuery) {
        List<SysCategoryDTO> sysCategoryDTOS = baseMapper.selectCategoryByPage(categoryQuery);
        for (SysCategoryDTO sysCategoryDTO : sysCategoryDTOS) {
            // 只有一个
            if (StringUtils.equals(sysCategoryDTO.getId(), DEFAULT_SYS_TAG_CATEGORYS)) {
                sysCategoryDTO.setCategoryName(I18nUtil.getMessage(sysCategoryDTO.getCategoryName()));
                break;
            }
        }
        return sysCategoryDTOS;
    }

    @Override
    public List<SysCategoryDTO> listByParentId(SysCategoryQuery categoryQuery) {
        List<SysCategoryDTO> list = baseMapper.listByParentId(categoryQuery);
        list.forEach(dto -> dto.setIsLeaf(!isExistChild(dto.getId())));
        return list;
    }

    private boolean isExistChild(String parentId) {
        LambdaQueryWrapper<Categorys> query = new LambdaQueryWrapper<>();
        query.eq(Categorys::getParentId, parentId);
        return getBaseMapper().selectCount(query) > 0;
    }

    @Override
    public List<SysCategoryDTO> getParentTagCategory(String categoryId, String categoryType) {
        List<String> idList = new ArrayList<>();
        if (StringUtils.isNotEmpty(categoryId)) {
            idList.add(categoryId);
            List<String> sonIds = baseMapper.getSonIdsByParentId(idList);
            while (null != sonIds && !sonIds.isEmpty()) {
                idList.addAll(sonIds);
                sonIds = baseMapper.getSonIdsByParentId(sonIds);
            }
        }
        return baseMapper.getParentTagCategory(categoryId, idList, categoryType);
    }

    @Override
    public List<CategoryResultDTO> getCategoryList(String categoryType) {
        return getCategoryResultList(
            baseMapper.getCategoryList(categoryType, null, null, GeneralJudgeEnum.CONFIRM.getValue()));
    }

    @Override
    public List<CategoryResultDTO> getCategoryList(String categoryType, String userId, String resourceType) {
        return getCategoryResultList(
            baseMapper.getCategoryList(categoryType, userId, resourceType, GeneralJudgeEnum.CONFIRM.getValue()));
    }

    private List<CategoryResultDTO> getCategoryResultList(List<Categorys> list) {
        Map<String, List<Categorys>> parentIdListMap = list.stream()
            .collect(Collectors.groupingBy(Categorys::getParentId));
        return getCategorySubList(parentIdListMap, "");
    }

    private List<CategoryResultDTO> getCategorySubList(Map<String, List<Categorys>> parentIdListMap, String parentId) {
        List<CategoryResultDTO> result = new ArrayList<>();
        List<Categorys> categorys = parentIdListMap.get(parentId);
        if (categorys == null) {
            return result;
        }
        categorys.sort((o1, o2) -> {
            if (o1 == null || o2 == null) {
                return 0;
            }
            int s = o1.getSortNo() - o2.getSortNo();
            if (s == 0) {
                return StringUtils.compare(o1.getId(), o2.getId());
            }
            return s;
        });
        for (Categorys c : categorys) {
            CategoryResultDTO categoryResultDTO = new CategoryResultDTO();
            categoryResultDTO.setId(c.getId());
            categoryResultDTO.setCategoryName(c.getCategoryName());
            categoryResultDTO.setType(c.getCategoryType());
            categoryResultDTO.setParentId(c.getParentId());
            categoryResultDTO.setSortNo(c.getSortNo());
            categoryResultDTO.setIsAvailable(c.getIsAvailable());
            result.add(categoryResultDTO);
            categoryResultDTO.setList(getCategorySubList(parentIdListMap, c.getId()));
        }
        return result;
    }

    @Override
    public void deleteCategory(String ids) {
        List<String> idList = TranslateUtil.translateBySplit(ids, String.class);
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        Long count = baseMapper.selectCount(
            Wrappers.<Categorys>lambdaQuery().eq(Categorys::getIsCanDelete, 0).in(Categorys::getId, idList));
        if (count > 0) {
            throw new BusinessException(ExampleErrorNoEnum.ERR_CATEGORY_NOT_OPERATION);
        }
        Integer delCount = 0;
        List<Categorys> categorys = baseMapper.selectBatchIds(idList);
        for (Categorys category : categorys) {
            delCount += categorysDao.delCategory(category);
        }

        if (delCount == 0) {
            throw new BusinessException(CourseErrorNoEnum.ERR_CATEGORY_NOT_DELETED);
        }
    }

    @Override
    public List<CategoryMonolayerDTO> getCourseClassifyList(String categoryType, String userId, String resourceType,
        String parentId) {
        return baseMapper.getCourseClassifyList(categoryType, userId, resourceType, parentId);
    }

    @Override
    public List<CategoryLevelPathUpdateDTO> getCategoryIdAndUpdateLevelPath(String categoryId) {
        return baseMapper.getCategoryIdAndUpdateLevelPath(categoryId);
    }

    @Override
    public void updateByIsCanDelCategoryId(Collection<String> ids, String categoryType) {
        log.info("params info: [id:{}, categoryType:{}]", JsonUtil.objToJson(ids), categoryType);
        // 查询同类型所有分类
        List<Categorys> list = list(new LambdaQueryWrapper<Categorys>().eq(Categorys::getCategoryType, categoryType));
        // 已使用分类设置不可删除，否则可以删除
        list.forEach(cate -> cate.setIsCanDelete(
            ids.contains(cate.getId()) ? GeneralJudgeEnum.NEGATIVE.getValue() : GeneralJudgeEnum.CONFIRM.getValue()));
        updateBatchById2(list);
    }

    @Override
    public void isAvailable(String ids, Integer isAvailable) {
        List<String> idList = TranslateUtil.translateBySplit(ids, String.class);
        isAvailable(idList, isAvailable);
    }

    @Override
    public boolean isQuote(String id) {
        return baseMapper.isQuote(id) > 0;
    }

    @Override
    public void isAvailable(List<String> ids, Integer isAvailable) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        Integer count = 0;
        List<Categorys> categorys = listByIds(ids);

        // 启用
        if (GeneralJudgeEnum.CONFIRM.getValue().equals(isAvailable)) {
            for (Categorys category : categorys) {
                count += categorysDao.enable(category);
            }
        }
        // 禁用
        else {
            for (Categorys category : categorys) {
                count += categorysDao.disable(category);
            }
        }

        if (count == 0) {
            throw new BusinessException(ExampleErrorNoEnum.ERR_CATEGORY_NOT_OPERATION);
        }
    }

    @Override
    public String getConcatCategory(Collection<String> ids, String categoryType) {
        return baseMapper.getConcatCategory(ids, categoryType);
    }

    @Override
    public List<SysCategoryDTO> getParentGroupCate(String categoryType) {
        return baseMapper.getParentGroupCate(categoryType);
    }

    @Override
    public List<SysCategoryDTO> getCateByType(String categoryType) {
        return baseMapper.getCateByType(categoryType);
    }

    @Override
    public List<SysCategoryDTO> getLevel3CateByType(String categoryType) {
        return baseMapper.getLevel3CateByType(categoryType);
    }

    @Override
    public Categorys getEntity(String id) {
        return baseMapper.getEntity(id);
    }

    @Override
    public Categorys getCategoryDetail(String id) {
        return baseMapper.getCategoryDetail(id);
    }

    @Override
    public void categoryExportData(SysCategoryQuery query, final String fileName) {
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<ICategorysService, SysCategoryDTO>(query) {
            @Override
            protected ICategorysService getBean() {
                return SpringUtil.getBean(CATEGORYS_SERVICE, ICategorysService.class);
            }

            @Override
            protected PageInfo<SysCategoryDTO> getPageInfo() {
                return getBean().findCategoryByPage((SysCategoryQuery) queryDTO);

            }

            @Override
            public ExportBizType getType() {
                if (!StringUtils.equals(fileName, "")) {
                    return ExportBizType.Category;
                }
                return ExportBizType.EvaluationCategory;
            }

            @Override
            public String getFileName() {
                return fileName + ExportFileNameEnum.Category.getType();
            }

            @Override
            protected void afterCreateBeanMap(Map<String, Object> map) {
                String categoryType = Objects.isNull(map.get(CATEGORY_TYPE)) ? null : (String) map.get(CATEGORY_TYPE);
                //转成中文
                String categoryTypeName = CategoryTypeEnum.getNameByType(categoryType);
                map.put(CATEGORY_TYPE, categoryTypeName);
            }
        };

        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public void exampleCategoryExportData(SysCategoryQuery query, final String fileName) {

        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<ICategorysService, SysCategoryDTO>(query) {
            @Override
            protected ICategorysService getBean() {
                return SpringUtil.getBean(CATEGORYS_SERVICE, ICategorysService.class);
            }

            @Override
            protected PageInfo<SysCategoryDTO> getPageInfo() {
                return getBean().findManageCategoryByPage((SysCategoryQuery) queryDTO);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.ExampleAuditCategory;
            }

            @Override
            public String getFileName() {
                return fileName + ExportFileNameEnum.Category.getType();
            }
        };

        exportComponent.exportRecord(exportDataDTO);
    }

    // 递归方法体
    private void recursionAllCategoryNode(List<SysCategoryDTO> currentResult, String categoryType, String parentId) {
        List<SysCategoryDTO> topCategoryList = baseMapper.getChilderCategorysList(categoryType, parentId);
        if (Objects.nonNull(topCategoryList)) {
            topCategoryList.forEach(sysCategoryDTO -> {
                currentResult.add(sysCategoryDTO);
                recursionAllCategoryNode(currentResult, categoryType, sysCategoryDTO.getId());
            });
        }
    }

    /**
     * 判断父级是否是第3级，数据库字段长度限制，levelpath只能到第3级，不能创建第4级
     *
     * @param parentId 父类id
     */
    private void checkoutCategoryParentId(String parentId, Integer version) {
        if (StringUtils.isNotBlank(parentId)) {
            Categorys parent = baseMapper.selectById(parentId);
            if (parent != null) {
                String parentLevelPath = parent.getLevelPath();
                String[] str = parentLevelPath.split(TAG_SEPARATOR);
                int count = str.length;
                if (version == 1) {
                    if (count >= LIMIT_LENGTH) {
                        throw new BusinessException(ExampleErrorNoEnum.ERR_CATEGORY_LEVEL_PATH_LIMIT_FIVE);
                    }
                } else {
                    if (count >= LIMIT_LENGTH_V2) {
                        throw new BusinessException(UserErrorNoEnum.ERR_CATEGORY_LEVEL_PATH_LIMIT_TWO);
                    }
                }
            }
        }
    }

    private void checkTagCategoryOnly(SysCategorySaveDTO sysCategorySaveDTO) {
        LambdaQueryWrapper<Categorys> queryWrapper = new LambdaQueryWrapper<>();
        if (Objects.equals(sysCategorySaveDTO.getCategoryType(), CategoryType.AbilityType.toString()) ||
            Objects.equals(sysCategorySaveDTO.getCategoryType(), CategoryType.AbilityModeType.toString())) {
            //能力词典和能力模型的分类不允许重名，相同的父分类也不可以,不然导入的时候会有问题
            queryWrapper.eq(Categorys::getCategoryType, sysCategorySaveDTO.getCategoryType());
            queryWrapper.eq(Categorys::getCategoryName, sysCategorySaveDTO.getCategoryName());
        } else if (StringUtils.isEmpty(sysCategorySaveDTO.getParentId())) {
            queryWrapper.eq(Categorys::getCategoryType, sysCategorySaveDTO.getCategoryType());
            queryWrapper.eq(Categorys::getCategoryName, sysCategorySaveDTO.getCategoryName());
            queryWrapper.eq(Categorys::getParentId, "");
        } else {
            queryWrapper.eq(Categorys::getParentId, sysCategorySaveDTO.getParentId());
            queryWrapper.eq(Categorys::getCategoryName, sysCategorySaveDTO.getCategoryName());
        }
        queryWrapper.ne(StringUtils.isNotBlank(sysCategorySaveDTO.getId()), Categorys::getId,
            sysCategorySaveDTO.getId());
        long count = this.count(queryWrapper);
        if (count > 0) {
            throw new BusinessException(ExampleErrorNoEnum.ERR_CATEGORY_EXISTS);
        }
        if (StringUtils.isBlank(sysCategorySaveDTO.getId()) || StringUtils.isBlank(sysCategorySaveDTO.getParentId())) {
            return;
        }
        if (sysCategorySaveDTO.getId().equals(sysCategorySaveDTO.getParentId())) {
            throw new BusinessException(ExampleErrorNoEnum.ERR_CATEGORY_UPDATE_PARENT_FAIL);
        }
    }

    @Override
    public void deleteCategoryByType(String categoryType) {
        LambdaQueryWrapper<Categorys> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Categorys::getCategoryType, categoryType);
        List<Categorys> list = list(queryWrapper);
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(dto -> dto.setIsCanDelete(1));
        }
        saveOrUpdateBatch2(list);
    }

    @Override
    public List<Categorys> getRealityCategoryList(Collection<String> categoryIdList) {
        return baseMapper.getRealityCategoryList(categoryIdList);
    }

    @Override
    public SysCategoryDTO getSysCategoryById(String categoryId) {
        SysCategoryQuery sysCategoryQuery = new SysCategoryQuery();
        sysCategoryQuery.setCategoryId(categoryId);
        List<SysCategoryDTO> list = baseMapper.selectCategoryByPage(sysCategoryQuery);
        if (CollectionUtils.isEmpty(list)) {
            return new SysCategoryDTO();
        }
        list.forEach(p -> {
            String path = p.getLevelPath();
            String pathName = "";
            if (StringUtils.isNotBlank(path)) {
                String[] ids = path.split("/");
                for (String id : ids) {
                    if (StringUtils.isNotBlank(id)) {
                        Categorys categorys = baseMapper.selectById(id);
                        if (categorys != null) {
                            pathName = pathName + categorys.getCategoryName() + TAG_SEPARATOR;
                        }
                    }
                }
            }
            p.setLevelPathName(pathName);
            p.setParentName(pathName);
        });
        return list.get(0);
    }

    @Override
    public List<IndustryVO> findIndustryList() {
        return list(new LambdaQueryWrapper<Categorys>().eq(Categorys::getCategoryType,
            CategoryTypeEnum.IndustryType.getType())).stream().map(categorys -> {
            IndustryVO industryVO = new IndustryVO();
            industryVO.setProfessionId(categorys.getId());
            industryVO.setProfessionName(categorys.getCategoryName());
            return industryVO;
        }).toList();
    }

    @Override
    public List<CompanySizeVO> findCompanySizeList() {
        return list(new LambdaQueryWrapper<Categorys>().eq(Categorys::getCategoryType,
            CategoryTypeEnum.CompanySize.getType())).stream().map(categorys -> {
            CompanySizeVO companySizeVO = new CompanySizeVO();
            companySizeVO.setScaleId(categorys.getId());
            companySizeVO.setScaleName(categorys.getCategoryName());
            return companySizeVO;
        }).toList();
    }

    @Override
    public List<Categorys> getFormTaskCategoryList() {
        return list(new LambdaQueryWrapper<Categorys>().eq(Categorys::getCategoryLevel, 1)
            .eq(Categorys::getIsAvailable, AvailableEnum.AVAILABLE.getValue())
            .eq(Categorys::getCategoryType, "FormTaskCate"));
    }

    @Override
    public List<CategoryResultDTO> findSpecifyCateType(String type) {
        List<Categorys> categorys = list(new LambdaQueryWrapper<Categorys>().eq(Categorys::getCategoryType, type)
            .eq(Categorys::getIsAvailable, AvailableEnum.AVAILABLE.getValue())
            .eq(Categorys::getParentId, StringUtils.EMPTY).orderByAsc(Categorys::getSortNo));
        return BeanListUtils.copyListProperties(categorys, CategoryResultDTO::new);
    }

    @Override
    public Categorys get(String id) {
        Categorys byId = getById(id);
        if (Objects.isNull(byId)) {
            throw new BusinessException(ErrorNoEnum.ERR_NOT_EXISTS);
        }
        return byId;
    }

    @Override
    public Map<String, Categorys> getCategoryMapByIds(Collection<String> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return Collections.emptyMap();
        }
        return list(new LambdaQueryWrapper<Categorys>().in(Categorys::getId, categoryIds))
            .stream().collect(Collectors.toMap(Categorys::getId, Function
                .identity(), (k1, k2) -> k1));
    }

    @Override
    public List<Categorys> getViewLimitCategoryList(String categoryType, String userId, String resourceType) {
        return baseMapper.getCategoryList(categoryType, userId, resourceType, GeneralJudgeEnum.CONFIRM.getValue());
    }

    @Override
    public List<SysCategoryDTO> listCategoryNamePath() {
        return baseMapper.listCategoryNamePath();
    }

    @Override
    public PageInfo<SysCategoryDTO> findExportCategory4CourseByPage(SysCategoryQuery categoryQuery) {
        PageInfo<SysCategoryDTO> pageList = PageMethod.startPage(categoryQuery.getPageNo(), categoryQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.selectCategory4CourseByPage(categoryQuery));
        return pageList;
    }

    @Override
    public void saveOrUpdateSysTagCategory(SysCategorySaveDTO sysCategorySaveDTO) {
        // 判断是否传递有父级id，系统标签分类不允许存在上下级
        if (!StringUtils.isBlank(sysCategorySaveDTO.getParentId())){
            throw new BusinessException(UserErrorNoEnum.ERR_TAG_CATEGORY_NOT_HAS_PARENT);
        }
        if (StringUtils.isBlank(sysCategorySaveDTO.getId())) {
            // 判断标签分类名称是否重复
            boolean exist = lambdaQuery().eq(Categorys::getCategoryName, sysCategorySaveDTO.getCategoryName())
                .eq(Categorys::getCategoryType, sysCategorySaveDTO.getCategoryType())
                .eq(Categorys::getIsDel, 0)
                .exists();
            if (exist) {
                throw new BusinessException(UserErrorNoEnum.ERR_TAG_CATEGORY_NAME_REPEAT);
            }
            // 新增分类(标签分类最多可以添加20个)
            if (count(new LambdaQueryWrapper<Categorys>()
                .eq(Categorys::getCategoryType, sysCategorySaveDTO.getCategoryType())
                .eq(Categorys::getIsAvailable,GeneralJudgeEnum.CONFIRM.getValue())
            ) >= 20) {
                throw new BusinessException(UserErrorNoEnum.ERR_TAG_CATEGORY_MAX_NUM);
            }
            Categorys categorys = new Categorys();
            BeanUtils.copyProperties(sysCategorySaveDTO, categorys);
            categorys.setId(newId());
            categorys.setCategoryLevel(1);
            categorys.setLevelPath(TAG_SEPARATOR + categorys.getId() + TAG_SEPARATOR);
            categorys.setCreateBy(UserThreadContext.getUserId());
            categorys.setCreateTime(new Date());
            categorys.setUpdateBy(UserThreadContext.getUserId());
            categorys.setUpdateTime(new Date());
            baseMapper.insert(categorys);
        } else {
            // 更新分类
            Categorys categorys = getById(sysCategorySaveDTO.getId());
            categorys.setCategoryName(sysCategorySaveDTO.getCategoryName());
            categorys.setSortNo(sysCategorySaveDTO.getSortNo());
            categorys.setIsAvailable(sysCategorySaveDTO.getIsAvailable());
            categorys.setUpdateBy(UserThreadContext.getUserId());
            categorys.setUpdateTime(new Date());
            baseMapper.updateById(categorys);
        }
    }

    @Override
    public CategoryResultDTO getSysTagTree(String type) {
        CategoryResultDTO categoryResultDTO = new CategoryResultDTO();
        categoryResultDTO.setType(type);
        categoryResultDTO.setId("all");
        categoryResultDTO.setKey("all");
        categoryResultDTO.setCategoryName(I18nUtil.getMessage("全部分类"));
        List<Categorys> categorys = list(new LambdaQueryWrapper<Categorys>()
            .eq(Categorys::getCategoryType, type)
            .orderByDesc(Categorys::getSortNo,Categorys::getId)
        );
        List<CategoryResultDTO> categoryResultDTOS = BeanListUtils.copyListProperties(categorys, CategoryResultDTO::new);
        for (CategoryResultDTO children : categoryResultDTOS){
            if (com.baomidou.mybatisplus.core.toolkit.StringUtils.equals(children.getId(), DEFAULT_SYS_TAG_CATEGORYS)) {
                children.setCategoryName(I18nUtil.getMessage(children.getCategoryName()));
            }
            children.setKey(children.getId());
        }
        categoryResultDTO.setList(categoryResultDTOS);
        return categoryResultDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sysTagCategoryIsAvailable(String ids, Integer isAvailable) {
        List<String> idList = TranslateUtil.translateBySplit(ids, String.class);
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        List<Categorys> categorys = listByIds(idList);

        // 启用
        if (GeneralJudgeEnum.CONFIRM.getValue().equals(isAvailable)) {
            checkAndUpdateCategory(isAvailable, categorys);
        }
        // 禁用
        else {
            for (Categorys category : categorys) {
                if (StringUtils.equals(category.getCategoryType(), CategoryTypeEnum.SYS_TAG_CATEGORY.getType())
                    && Objects.equals(category.getIsCanDelete(), GeneralJudgeEnum.NEGATIVE.getValue())) {
                    throw new BusinessException(UserErrorNoEnum.ERR_CATEGORY_CAN_NOT_DELETE);
                }
                category.setIsAvailable(isAvailable);
                category.setUpdateBy(UserThreadContext.getUserId());
                category.setUpdateTime(new Date());
                categorysDao.updateCategory(category);
            }
        }
    }

    private void checkAndUpdateCategory(Integer isAvailable, List<Categorys> categorys) {
        for (Categorys category : categorys) {
            if (StringUtils.equals(category.getCategoryType(), CategoryTypeEnum.SYS_TAG_CATEGORY.getType())){
                long categoryCount = count(new LambdaQueryWrapper<Categorys>()
                    .eq(Categorys::getCategoryType, CategoryTypeEnum.SYS_TAG_CATEGORY.getType())
                    .eq(Categorys::getIsAvailable, GeneralJudgeEnum.CONFIRM.getValue())
                );
                if (categoryCount >= 20) {
                    throw new BusinessException(UserErrorNoEnum.ERR_TAG_CATEGORY_MAX_NUM);
                }
            }
            category.setIsAvailable(isAvailable);
            category.setUpdateBy(UserThreadContext.getUserId());
            category.setUpdateTime(new Date());
            categorysDao.updateCategory(category);
        }
    }

    @Override
    public void updateChildren(String id) {
        baseMapper.updateChildren(id);
    }

    @Override
    public Categorys selectCategoryByName(String tagClassifyName, String type) {
        return categorysDao.getOne(new LambdaQueryWrapper<Categorys>()
            .eq(Categorys::getCategoryName, tagClassifyName)
            .eq(Categorys::getCategoryType, type));
    }

    @Override
    public void changeSortNo(CategoryChangeSortDTO changeSortDTO) {
        Categorys category = baseMapper.selectById(changeSortDTO.getId());
        if (category == null) {
            throw new BusinessException(ErrorNoEnum.ERR_NOT_EXISTS);
        }

        // 分类类型
        String categoryType = category.getCategoryType();

        // 旧父类和旧排序
        Integer oldSortNo = category.getSortNo();
        String oldParentId = category.getParentId();

        // 新父类和新排序
        Integer sortNo = changeSortDTO.getSortNo();
        String parentId = changeSortDTO.getParentId();
        if (null == parentId) {
            parentId = StringUtils.EMPTY;
        }

        if (!parentId.equals(oldParentId)) {
            // 仅同级类目可相互拖动排序
            if (isSubNode(category.getId(), parentId)) {
                throw new BusinessException(UserErrorNoEnum.ERR_CATEGORY_CHANGE_SORT_NO_CROSS_LEVEL);
            }
        } else {
            // 相同父类下的位置移动
            if (sortNo.equals(oldSortNo)) {
                // 排序不变，中断执行
                return;
            }
            if (sortNo > oldSortNo) {
                // 如果排序值增大，往后移动，则旧序号和新序号之间的活动的序号减1，往前移动
                baseMapper.moveSortNo(categoryType, oldSortNo, sortNo, 1);
            } else {
                // 如果排序值减少，向前移动，则旧序号和新序号之间的活动的序号加1，往后移动
                baseMapper.moveSortNo(categoryType, sortNo, oldSortNo, 2);
            }
        }

        // 更新移动后的数据
        category.setSortNo(sortNo);
        categorysDao.updateCategory(category);

        // 排序值优化方案：
        // 1、排序值支持从0开始，不再根据数据库现有最大值自动生成序号的操作；
        // 2、新增和修改时，不可变更当前操作对象的排序，前端传什么就是什么；
        // 3、新增和修改时，如有排序值冲突，自动对其他数据的排序进行调整，但整体排序值不可大于99；
        // 4、新增和修改时，有可能会存在排序值重复的情况，这种情况忽略处理，后续排序拖动时增加补偿操作；
        // 5、拖动排序时，先根据规则进行处理。最后对排序值进行重置补偿操作，确保数据库排序值唯一。
        baseMapper.reSetSortNo(categoryType);
    }

   
    @Override
    public void moveSortNo(String categoryType, Integer minSortNo, Integer maxSortNo, Integer type) {
        baseMapper.moveSortNo(categoryType, minSortNo, maxSortNo, type);
    }

    @Override
    public List<CategoryResultDTO> getAllCategoryList(String categoryType) {
        return getCategoryResultList(
            baseMapper.getCategoryList(categoryType, null, null, null));
    }

    /**
     * 判断目标父节点是否是当前节点的子节点
     *
     * @param currentCategoryId 当前节点ID
     * @param targetParentId    目标父节点ID
     * @return 是否是子节点
     */
    private boolean isSubNode(String currentCategoryId, String targetParentId) {
        Categorys category = baseMapper.selectById(targetParentId);
        if (category == null) {
            return false;
        }

        String levelPath = category.getLevelPath();
        if (StringUtils.isBlank(levelPath)) {
            return false;
        }

        String[] pathArray = levelPath.split(TAG_SEPARATOR);
        for (String id : pathArray) {
            if (currentCategoryId.equals(id)) {
                return true;
            }
        }

        return false;
    }

    public String saveOrUpdateCategoryV3(SysCategorySaveDTO saveOrUpdateCategoryDTO) {
        String result;
        log.info(SAVE_OR_UPDATE_CATEGORY_ARG, saveOrUpdateCategoryDTO);
        // 验证名称分类是否唯一
        checkTagCategoryOnly(saveOrUpdateCategoryDTO);
        checkoutCategoryParentId(saveOrUpdateCategoryDTO.getParentId(), 2);
        ICategorysService bean = SpringUtil.getBean(CATEGORYS_SERVICE, ICategorysService.class);
        Assert.notNull(bean, CATEGORYS_SERVICE_MUST_NOT_BE_NULL);
        if (StringUtils.isNotEmpty(saveOrUpdateCategoryDTO.getId())) {
            result = bean.updateCategoryV3(saveOrUpdateCategoryDTO);
        } else {
            result = bean.saveCategoryV3(saveOrUpdateCategoryDTO);
        }

        return result;
    }

    public Set<String> getCurAndSubCategoryIds(String categoryId, String categoryType) {
        return baseMapper.getCurAndSubCategoryIds(categoryId, categoryType);
    }

    @Override
    public int checkChildrenOrg(Set<String> parentViewOrgIdList, Set<String> childrenViewOrgIdList) {
        if (CollectionUtils.isEmpty(parentViewOrgIdList) || CollectionUtils.isEmpty(childrenViewOrgIdList)) {
            return 0;
        }
        return baseMapper.checkChildrenOrg(parentViewOrgIdList, childrenViewOrgIdList);
    }
}

