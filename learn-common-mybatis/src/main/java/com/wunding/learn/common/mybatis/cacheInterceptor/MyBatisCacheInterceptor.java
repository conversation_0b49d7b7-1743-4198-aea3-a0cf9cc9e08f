package com.wunding.learn.common.mybatis.cacheInterceptor;

import org.apache.ibatis.cache.Cache;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionSynchronizationManager;

/**
 * <AUTHOR>
 */
@Component
@Intercepts({
    @Signature(type = Executor.class,
        method = "update",
        args = {MappedStatement.class, Object.class})
})
public class MyBatisCacheInterceptor implements Interceptor {

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        Object[] args = invocation.getArgs();
        for (Object arg : args) {
            if (arg instanceof MappedStatement) {
                Cache cache = ((MappedStatement) arg).getCache();
                if (cache != null && !TransactionSynchronizationManager.isActualTransactionActive()) {
                    cache.clear();
                }
            }
        }
        return invocation.proceed();
    }
}
