package com.wunding.learn.maxkb.api.service;

/**
 * <AUTHOR>
 */
public interface AiService {

    /**
     * 登陆,返回token
     *
     * @param username 用户名
     * @param password 密码
     * @return token
     */
    String login(String username,String password);

    /**
     * AI 生成内容
     * <p>
     * 已知默认参数
     * <p>
     * dataset_setting:{top_n:10, similarity:0.0, max_paragraph_char_number: 10000}
     *
     * @param filePath  文件路径
     * @param prompt    提示词
     * @param message 消息内容
     * @return 生成的内容
     */
    String generatedContent(String filePath, String prompt, String message);
}
