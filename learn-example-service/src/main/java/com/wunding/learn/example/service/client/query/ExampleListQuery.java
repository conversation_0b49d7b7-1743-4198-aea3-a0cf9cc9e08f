package com.wunding.learn.example.service.client.query;

import com.wunding.learn.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.Parameter;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 案例库某分类查询对象
 *
 * @Author: aixinrong
 * @Date: 2022/8/31 11:19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ExampleListQuery extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Parameter(description = "列表类型 0:案例广场 1:案例库")
    private Integer square;

    @Parameter(description = "分类id")
    private String categoryId;

    @Parameter(description = "排序（new最新 view最热 viewWeek上周最热）")
    private String order;

    @Parameter(description = "按时间 0:时间升序 1:时间降序")
    private Integer orderTime;

    @Parameter(description = "公开级别")
    private String level;

    @Parameter(description = "案例质量")
    private String tagId;

    @Parameter(description = "是否推荐 0:否 1:是")
    private Integer recommend;

    @Parameter(description = "关键字")
    private String keyWord;

    @Parameter(description = "作者id")
    private String authorId;

    @Parameter(hidden = true)
    private String previousMonday;

    @Parameter(hidden = true)
    private String sunday;
}
