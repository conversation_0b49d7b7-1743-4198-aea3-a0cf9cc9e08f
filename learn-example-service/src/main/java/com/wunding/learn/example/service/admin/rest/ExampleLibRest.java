package com.wunding.learn.example.service.admin.rest;

import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.bean.Result;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.i18n.util.I18nUtil;
import com.wunding.learn.example.service.admin.dto.ExampleConfigLevelDTO;
import com.wunding.learn.example.service.admin.dto.ExampleLibDTO;
import com.wunding.learn.example.service.admin.dto.ExampleLibEditDTO;
import com.wunding.learn.example.service.admin.dto.ExampleLibSaveDTO;
import com.wunding.learn.example.service.admin.dto.ExampleReTranscodingDTO;
import com.wunding.learn.example.service.admin.query.ExampleLibQuery;
import com.wunding.learn.example.service.service.IExampleConfigLevelService;
import com.wunding.learn.example.service.service.IExampleService;
import com.wunding.learn.file.api.dto.ExportResultDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: aixinrong
 * @Date: 2022/9/01 09:43
 */
@RestController
@RequestMapping("${module.example.contentPath:/}exampleLib")
@Tag(description = "案例库", name = "ExampleLibRest")
@Validated
public class ExampleLibRest {

    @Resource
    private IExampleConfigLevelService exampleConfigLevelService;

    @Resource
    private IExampleService exampleService;

    @GetMapping("/buttonList")
    @Operation(operationId = "buttonList", summary = "表头按钮列表", description = "表头按钮列表")
    public Result<List<ExampleConfigLevelDTO>> buttonList() {
        List<ExampleConfigLevelDTO> exampleConfigLevelDTOList = exampleConfigLevelService.getConfigLevels();
        exampleConfigLevelDTOList = exampleConfigLevelDTOList.stream().filter(
            exampleConfigLevel -> StringUtils.isNotBlank(exampleConfigLevel.getTitle()) && (
                exampleConfigLevel.getEffType() == 1 || exampleConfigLevel.getScoreType() == 1
                    || exampleConfigLevel.getSquareType() == 1)).collect(Collectors.toList());
        if (StringUtils.isNotBlank(UserThreadContext.getAcceptLanguage())
            && !(UserThreadContext.getAcceptLanguage().equals(Locale.CHINESE.getLanguage()))) {
            exampleConfigLevelDTOList.stream().forEach(config -> {
                config.setTitle(I18nUtil.getDefaultMessage("等级").concat(config.getTitle().replace("等级", "")));
            });
        }

        // 等级名称添加“设为”文字 -  多语言支持
        exampleConfigLevelDTOList.stream().forEach(config -> {
            config.setTitle(I18nUtil.getMessage("setAs").concat(config.getTitle()));
        });

        return Result.success(exampleConfigLevelDTOList);
    }

    @GetMapping("/list")
    @Operation(operationId = "list", summary = "案例库列表", description = "案例库列表")
    public Result<PageInfo<ExampleLibDTO>> list(ExampleLibQuery exampleLibQuery) {
        PageInfo<ExampleLibDTO> data = exampleService.list(exampleLibQuery);
        return Result.success(data);
    }

    @PostMapping("/createOrUpdate")
    @Operation(operationId = "createOrUpdate", summary = "添加/编辑案例库", description = "添加/编辑案例库")
    public Result<Void> createOrUpdate(@Valid @RequestBody ExampleLibSaveDTO exampleLibSaveDTO) {
        exampleService.createOrUpdate(exampleLibSaveDTO);
        return Result.success();
    }

    @GetMapping("/exampleInfo/{id}")
    @Operation(operationId = "exampleInfo", summary = "编辑案例回显", description = "编辑案例回显")
    public Result<ExampleLibEditDTO> exampleInfo(@Parameter(description = "案例id") @PathVariable String id) {
        return Result.success(exampleService.exampleInfo(id));
    }

    @PostMapping(value = "{levelId}/exampleLevel")
    @Operation(operationId = "exampleLevel", summary = "设置案例质量等级 前置条件buttonList接口有值", description = "设置案例质量等级 前置条件buttonList接口有值")
    public Result<Void> exampleLevel(
        @Parameter(description = "案例ids(用,分割)") @RequestParam @NotBlank(message = "案例ids不可为空") String ids,
        @Parameter(description = "案例质量id") @PathVariable String levelId) {
        exampleService.exampleLevel(ids, levelId);
        return Result.success();
    }

    @PostMapping(value = "/unExampleLevel")
    @Operation(operationId = "unExampleLevel", summary = "取消案例质量等级 前置条件buttonList接口有值", description = "取消案例质量等级 前置条件buttonList接口有值")
    public Result<Void> unExampleLevel(
        @Parameter(description = "案例ids(用,分割)") @RequestParam @NotBlank(message = "案例ids不可为空") String ids) {
        exampleService.unExampleLevel(ids);
        return Result.success();
    }

    @PostMapping(value = "{isRecommend}/recommend")
    @Operation(operationId = "recommend", summary = "取消/推荐案例", description = "取消/推荐案例")
    public Result<Void> isRecommend(
        @Parameter(description = "案例ids(用,分割)") @RequestParam @NotBlank(message = "案例ids不可为空") String ids,
        @Parameter(description = "是否推荐:0/1") @PathVariable Integer isRecommend) {
        exampleService.isRecommend(ids, isRecommend);
        return Result.success();
    }

    @DeleteMapping("/delete")
    @Operation(operationId = "ExampleLibRest_delete", summary = "删除案例", description = "删除案例")
    public Result<Void> delete(
        @Parameter(description = "案例ids(用,分割)") @RequestParam @NotBlank(message = "案例ids不可为空") String ids) {
        exampleService.delete(ids);
        return Result.success();
    }

    @GetMapping("/exportExample")
    @Operation(operationId = "exportExample", summary = "导出案例库管理列表", description = "导出案例库管理列表")
    public Result<ExportResultDTO> exportExample(ExampleLibQuery exampleLibQuery) {
        exampleService.exportExample(exampleLibQuery);
        return Result.success();
    }

    @PostMapping("/reTranscoding")
    @Operation(operationId = "reTranscoding", summary = "案例重新转码", description = "案例重新转码")
    public Result<Void> reTranscoding(@RequestBody @Valid ExampleReTranscodingDTO exampleReTranscodingDTO) {
        exampleService.reTranscoding(exampleReTranscodingDTO);
        return Result.success();
    }
}
