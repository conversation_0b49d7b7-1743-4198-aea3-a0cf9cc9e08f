package com.wunding.learn.example.service.admin.query;

import com.wunding.learn.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.format.annotation.DateTimeFormat.ISO;

/**
 * 案例库查询对象
 *
 * @Author: aixinrong
 * @Date: 2022/9/01 10:52
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ExampleLibQuery extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Parameter(description = "案例名称")
    private String exampleName;

    @Parameter(description = "案例编号")
    private String exampleCode;

    @Parameter(description = "作者ids集合,分割")
    private String authorIds;

    @Parameter(hidden = true)
    private List<String> authorIdVo;

    @Parameter(description = "作者部门")
    private String authorOrgId;

    @Parameter(description = "公开等级：ExampleScopeL3(集团级) ExampleScopeL2(公司级) ExampleScopeL1(部门级)")
    private String publicLevel;

    @Parameter(description = "认证状态：0未认证 1部门级认证 2公司级认证 3集团级认证")
    private Integer authStatus;

    @Parameter(description = "审核状态：-3草稿 0已提交 1已审核，审核通过 2已审核，审核不通过 3已发布 4评审中 5已评审，认证通过 6已评审，认证不通过")
    private Integer auditStatus;

    @Parameter(description = "条线id")
    private String businessId;

    @Parameter(description = "案例库分类id")
    private String libCategoryId;

    @Parameter(description = "案例质量id")
    private String contentLevelId;

    @Parameter(description = "添加开始时间")
    @DateTimeFormat(iso = ISO.DATE_TIME)
    private Date createBeginTime;

    @Parameter(description = "添加结束时间")
    @DateTimeFormat(iso = ISO.DATE_TIME)
    private Date createEndTime;

    @Parameter(description = "入库开始时间")
    @DateTimeFormat(iso = ISO.DATE_TIME)
    private Date publishBeginTime;

    @Parameter(description = "入库结束时间")
    @DateTimeFormat(iso = ISO.DATE_TIME)
    private Date publishEndTime;

    @Parameter(description = "所有推荐 1")
    private Integer allRecommend;

    @Parameter(description = "待评审 1")
    private Integer unReview;

    @Parameter(description = "待审核 1")
    private Integer unAudit;

    @Schema(description = " 转码状态 1-转码中 2-转码成功 3-转码失败 4-排队中")
    private Integer transformStatus;

    @Parameter(description = "排除审核状态：-3草稿 0已提交 1已审核，审核通过 2已审核，审核不通过 3已发布 4评审中 5已评审，认证通过 6已评审，认证不通过 以,分割")
    private String excludeAuditStatusStr;

    @Parameter(description = "排除审核状态：-3草稿 0已提交 1已审核，审核通过 2已审核，审核不通过 3已发布 4评审中 5已评审，认证通过 6已评审，认证不通过", hidden = true)
    private List<String> excludeAuditStatusList;
}
