package com.wunding.learn.example.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * @Author: aixinrong
 * @Date: 2022/9/01 10:02
 */
@Data
@Schema(name = "ExampleLibDTO", description = "案例库对象")
public class ExampleLibDTO implements Serializable {

    public static final long serialVersionUID = 1L;

    @Schema(description = "案例id")
    private String id;

    @Schema(description = "案例编号")
    private String exampleCode;

    @Schema(description = "案例名称")
    private String exampleName;

    @Schema(description = "案例分类名")
    private String exampleCateName;

    @Schema(description = "作者id", hidden = true)
    private String authorId;

    @Schema(description = "作者全名")
    private String authorName;

    @Schema(description = "部门名称")
    private String authorOrgName;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "入库时间")
    private Date publishTime;

    @Schema(description = "审核状态：-3草稿，-2已撤回，-1已驳回，0已提交，1已审核，审核通过，2已审核，审核不通过，3已发布， 4评审中，5已评审，认证通过，6已评审，认证不通过")
    private Integer status;

    @Schema(description = "优质类型")
    private String extra;

    @Schema(description = "公开等级", hidden = true)
    private String levelName;

    @Schema(description = "认证状态")
    private String authStatus;

    @Schema(description = "是否推荐 0否 1推荐")
    private Integer recommend;

    @Schema(description = "评论数")
    private Integer commentCount;

    @Schema(description = "案例文件地址,转码后文件/index.html  有则可预览")
    private String fileUrl;

    @Schema(description = "原文件地址  有则可下载")
    private String sourceFileUrl;

    @Schema(description = "文件转码状态 1-转码中 2-转码成功 3-转码失败 4-排队中")
    private Integer transformStatus;

    @Schema(description = "创建、归属部门Id")
    private String orgId;

    @Schema(description = "创建、归属部门")
    private String orgName;

    @Schema(description = "部门全称 orgPath")
    private String orgPath;

    @Schema(description = "作者账号")
    private String authorLoginName;

    @Schema(description = "案例时长")
    private BigDecimal playTime;

    @Schema(description = "案例文件类型")
    private String fileType;
}
