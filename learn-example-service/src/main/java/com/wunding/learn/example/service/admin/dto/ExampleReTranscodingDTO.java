package com.wunding.learn.example.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;
import lombok.Data;

/**
 * 案例重新转码DTO
 *
 * <AUTHOR>
 */
@Data
@Schema(name = "ExampleReTranscodingDTO", description = "案例重新转码请求对象")
public class ExampleReTranscodingDTO {

    @NotEmpty(message = "请选择案例")
    @Schema(description = "案例Id列表")
    private List<String> ids;
}
