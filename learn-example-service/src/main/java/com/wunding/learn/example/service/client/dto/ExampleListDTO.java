package com.wunding.learn.example.service.client.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;

/**
 * 案例详情数据 序列化
 *
 * <AUTHOR>
 * @date 2021-08-01 17:58
 * @since [5.0.0]
 **/
@Data
@Schema(name = "ExampleListDTO", description = "案例列表对象")
public class ExampleListDTO implements Serializable {

    private static final long serialVersionUID = -6908503161778709564L;

    @Schema(description = "id")
    private String id;

    @Schema(description = "案例名称")
    private String exampleName;

    /**
     * 案例状态 -3暂存案例，-2已撤回，-1已驳回，0已提交，1已审核，审核通过，2已审核，审核不通过，3已发布， 4评审中，5已评审，认证通过，6已评审，认证不通过
     */
    @Schema(description = "案例状态 -3暂存案例，-2已撤回，-1已驳回，0已提交，1已审核，审核通过，2已审核，审核不通过，3已发布，4评审中，5已评审，认证通过，6已评审，认证不通过")
    private Integer status;

    /**
     * 学习人数
     */
    @Schema(description = "学习人数")
    private Integer vc;

    @Schema(description = "图片链接")
    private String image;

    /**
     * 点赞人数
     */
    @Schema(description = "点赞人数")
    private Integer likeCount;

    /**
     * 公开级别
     */
    @Schema(description = "公开级别")
    private String level;

    /**
     * 公开级别id
     */
    @Schema(description = "公开级别id ")
    private String levelId;

    /**
     * 案例质量
     */
    @Schema(description = "案例质量 ")
    private String extra;

    /**
     * 案例质量id
     */
    @Schema(description = "案例质量id ")
    private String extraId;

    /**
     * 评论人数
     */
    @Schema(description = "评论人数")
    private Integer commentNumber;

    /**
     * 视频转换状态  1 转换中  2 转换完成 3 转换失败
     */
    @Schema(description = "视频转换状态  1-转码中 2-转码成功 3-转码失败 4-排队中")
    private Integer transformStatus;
}
