package com.wunding.learn.example.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.example.service.admin.dto.ExampleCommentDTO;
import com.wunding.learn.example.service.admin.query.ExampleCommentQuery;
import com.wunding.learn.example.service.model.ExampleComment;

/**
 * <p> 案例评论表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">aixinrong</a>
 * @since 2022-08-29
 */
public interface IExampleCommentService extends IService<ExampleComment> {

    /**
     * 案例评论列表
     *
     * @param exampleCommentQuery
     * @return
     */
    PageInfo<ExampleCommentDTO> list(ExampleCommentQuery exampleCommentQuery);

    /**
     * 有效/无效案例评论
     *
     * @param ids
     * @param isEfficient
     */
    void isEfficient(String ids, Integer isEfficient);

    /**
     * 删除案例评论
     *
     * @param ids
     */
    void delete(String ids);

    /**
     * 导出案例评论管理列表Excel
     *
     * @param exampleCommentQuery
     */
    void exportExampleComment(ExampleCommentQuery exampleCommentQuery);
}
