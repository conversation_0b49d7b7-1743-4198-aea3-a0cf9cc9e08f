package com.wunding.learn.example.service.client.dto;

import com.wunding.learn.file.api.dto.VideoClarityDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 案例详情数据 序列化
 *
 * <AUTHOR>
 * @date 2021-08-01 17:58
 * @since [5.0.0]
 **/
@Data
@Schema(name = "ExampleDetailDTO", description = "案例详情对象")
public class ExampleDetailDTO implements Serializable {

    private static final long serialVersionUID = -6908503161778709564L;

    @Schema(description = "id")
    private String id;

    @Schema(description = "案例名称")
    private String exampleName;

    /**
     * 案例状态 -3暂存案例，-2已撤回，-1已驳回，0已提交，1已审核，审核通过，2已审核，审核不通过，3已发布， 4评审中，5已评审，认证通过，6已评审，认证不通过
     */
    @Schema(description = "案例状态 -3暂存案例，-2已撤回，-1已驳回，0已提交，1已审核，审核通过，2已审核，审核不通过，3已发布，4评审中，5已评审，认证通过，6已评审，认证不通过")
    private Integer status;

    /**
     * 学习人数
     */
    @Schema(description = "学习人数")
    private Integer vc;

    /**
     * 案例作者
     */
    @Schema(description = "案例作者")
    private String author;

    /**
     * 案例作者id
     */
    @Schema(description = "案例作者id/申请人id")
    private String authorId;

    /**
     * 申请人账号
     */
    @Schema(description = "申请人账号")
    private String authorLoginName;

    /**
     * 所在单位
     */
    @Schema(description = "所在单位")
    private String authorOrgName;

    @Schema(description = "分类id")
    private String categoryId;

    @Schema(description = "分类名称")
    private String categoryName;

    /**
     * 案例条线ID
     */
    @Schema(description = "案例条线ID")
    private String businessId;

    /**
     * 案例条线名称
     */
    @Schema(description = "案例条线名称")
    private String businessName;

    /**
     * 案例描述
     */
    @Schema(description = "案例描述")
    private String synopsis;

    @Schema(description = "图片展示地址")
    private String image;

    @Schema(description = "图片保存地址")
    private String imagePath;

    @Schema(description = "案例下载地址")
    private String downloadUrl;

    @Schema(description = "预览地址")
    private String previewUrl;

    @Schema(description = "上传文件地址 编辑时不可修改文件，传值无效")
    private String filePath;

    @Schema(description = "文件名称")
    private String fileName;

    @Schema(description = "文件播放类型")
    private String fileType;

    @Schema(description = "文件类型")
    private String type;

    @Schema(description = "案例时长")
    private String duration;

    /**
     * 是否收藏
     */
    @Schema(description = "是否收藏 null、0：否 1：是")
    private Integer isFavorite;

    /**
     * 是否点赞
     */
    @Schema(description = "是否点赞 null、0：否 1：是")
    private Integer isLike;

    /**
     * 点赞人数
     */
    @Schema(description = "点赞人数")
    private Integer likeCount;

    @Schema(description = "评论人数")
    private Integer commentNumber;

    @Schema(description = "案例点赞人数", hidden = true)
    private Integer voteNum;

    /**
     * 评审分数
     */
    @Schema(description = "评审分数")
    private BigDecimal score;

    /**
     * 公开级别
     */
    @Schema(description = "公开级别")
    private String level;

    /**
     * 公开级别id
     */
    @Schema(description = "公开级别id ")
    private String levelId;

    /**
     * 案例质量
     */
    @Schema(description = "案例质量 ")
    private String extra;

    /**
     * 案例质量id
     */
    @Schema(description = "案例质量id ")
    private String extraId;

    /**
     * 综合评星
     */
    @Schema(description = "综合评星")
    private BigDecimal commonStar;

    /**
     * 自评星级
     */
    @Schema(description = "自评星级")
    private Integer myStar;

    /**
     * 评星人数
     */
    @Schema(description = "评星人数")
    private Integer starCount;

    /**
     * 添加时间
     */
    @Schema(description = "添加时间/申请时间", example = "2018-10-01 12:18")
    private Date createTime;

    /**
     * 发布时间
     */
    @Schema(description = "发布时间", example = "2018-10-01 12:18")
    private Date publishTime;

    @Schema(description = "管理编辑 0：不行 1可以")
    private Integer audit;

    private List<ExampleDetailDTO> authorExampleList;

    @Schema(description = "文件转码状态 1-转码中 2-转码成功 3-转码失败 4-排队中")
    private Integer transformStatus;

    @Schema(description = "视频清晰度地址")
    private List<VideoClarityDTO> videoClarityList;
}
