package com.wunding.learn.example.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.example.api.dto.ExampleDTO;
import com.wunding.learn.example.service.admin.dto.ExampleLibDTO;
import com.wunding.learn.example.service.admin.dto.ExampleLibEditDTO;
import com.wunding.learn.example.service.admin.dto.ExampleLibSaveDTO;
import com.wunding.learn.example.service.admin.dto.ExampleReTranscodingDTO;
import com.wunding.learn.example.service.admin.query.ExampleLibQuery;
import com.wunding.learn.example.service.client.dto.ApprovalJudgesDTO;
import com.wunding.learn.example.service.client.dto.AuditExampleDTO;
import com.wunding.learn.example.service.client.dto.AuditLevelAndExtraDTO;
import com.wunding.learn.example.service.client.dto.ExampleAuditDTO;
import com.wunding.learn.example.service.client.dto.ExampleAuditOptionDTO;
import com.wunding.learn.example.service.client.dto.ExampleDetailDTO;
import com.wunding.learn.example.service.client.dto.ExampleHomePageDTO;
import com.wunding.learn.example.service.client.dto.ExampleLevelDTO;
import com.wunding.learn.example.service.client.dto.ExampleListDTO;
import com.wunding.learn.example.service.client.dto.MyExampleDTO;
import com.wunding.learn.example.service.client.dto.UploadExampleDTO;
import com.wunding.learn.example.service.client.query.ApprovalJudgesQuery;
import com.wunding.learn.example.service.client.query.ExampleAuditQuery;
import com.wunding.learn.example.service.client.query.ExampleDetailQuery;
import com.wunding.learn.example.service.client.query.ExampleHomePageQuery;
import com.wunding.learn.example.service.client.query.ExampleListQuery;
import com.wunding.learn.example.service.client.query.MyExampleQuery;
import com.wunding.learn.example.service.model.Example;
import java.util.Collection;
import java.util.List;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <p> 案例表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">aixinrong</a>
 * @since 2022-08-29
 */
public interface IExampleService extends IService<Example> {

    /**
     * 案例库列表
     *
     * @param exampleLibQuery
     * @return
     */
    PageInfo<ExampleLibDTO> list(ExampleLibQuery exampleLibQuery);

    /**
     * 添加/编辑案例库
     *
     * @param exampleLibSaveDTO
     */
    void createOrUpdate(ExampleLibSaveDTO exampleLibSaveDTO);

    /**
     * 编辑案例回显
     *
     * @param id
     * @return
     */
    ExampleLibEditDTO exampleInfo(String id);

    /**
     * 设置案例质量等级
     *
     * @param ids
     * @param levelId
     */
    void exampleLevel(String ids, String levelId);

    /**
     * 取消案例质量等级
     *
     * @param ids
     */
    void unExampleLevel(String ids);

    /**
     * 取消/推荐案例
     *
     * @param ids
     * @param isRecommend
     */
    void isRecommend(String ids, Integer isRecommend);

    /**
     * 删除案例
     *
     * @param ids
     */
    void delete(String ids);

    /**
     * 上传案例
     *
     * @param uploadExampleDTO
     */
    void uploadExampleFile(UploadExampleDTO uploadExampleDTO);

    /**
     * 此接口用于获取案例库某分类的内容列表
     *
     * @param exampleListQuery
     * @return
     */
    PageInfo<ExampleListDTO> getExampleListByCategory(ExampleListQuery exampleListQuery);

    /**
     * 获取案例详情
     *
     * @param exampleDetailQuery
     * @return
     */
    ExampleDetailDTO detail(ExampleDetailQuery exampleDetailQuery);

    /**
     * 获取我的案例列表
     *
     * @param myExampleQuery
     * @return
     */
    PageInfo<MyExampleDTO> myExample(MyExampleQuery myExampleQuery);

    /**
     * 获取认证评委/案例审批列表
     *
     * @param approvalJudgesQuery
     * @return
     */
    PageInfo<ApprovalJudgesDTO> approvalJudgesList(ApprovalJudgesQuery approvalJudgesQuery);

    /**
     * 资格审批-直接主管/专家审核结果查询
     *
     * @param exampleAuditQuery
     * @return
     */
    List<ExampleAuditDTO> getExampleAuditList(ExampleAuditQuery exampleAuditQuery);

    /**
     * 提交审批结果
     *
     * @param auditExampleDTO
     */
    void auditExample(AuditExampleDTO auditExampleDTO);

    /**
     * 撤回案例
     *
     * @param exampleId
     */
    void withdraw(String exampleId);

    /**
     * 获取主管/专家评审题目
     *
     * @param type
     * @return
     */
    List<ExampleAuditOptionDTO> getAuditOptions(Integer type);

    /**
     * 获取案例开放等级和萃取标签
     *
     * @param square
     * @return
     */
    List<ExampleLevelDTO> getLevelAndScope(Integer square);

    /**
     * 案例管理编辑
     *
     * @param auditLevelAndExtraDTO
     */
    void auditLevelAndExtra(AuditLevelAndExtraDTO auditLevelAndExtraDTO);

    /**
     * 案例内容质量等级自动变更
     */
    void caseExtra();

    /**
     * 导出案例库列表Excel
     *
     * @param exampleLibQuery 案例库管理-查询对象
     */
    void exportExample(ExampleLibQuery exampleLibQuery);

    /**
     * 首页案例库列表
     *
     * @param exampleHomePageQuery
     * @return
     */
    PageInfo<ExampleHomePageDTO> homePageList(ExampleHomePageQuery exampleHomePageQuery);

    /**
     * 更新案例库互动数据
     *
     * @param id    案例库id
     * @param event 互动事件
     */
    void updateExampleInteractNum(String id, String event);

    /**
     * 根据userId查询案例
     *
     * @param userId
     * @return
     */
    List<ExampleDTO> getExampleByUserId(String userId);

    /**
     * 获取实际案例 忽略删除
     *
     * @param id 案例id
     * @return {@link ExampleDTO}
     */
    ExampleDTO getActualExampleById(String id);

    /**
     * 批量获取id (不过滤删除)
     *
     * @param batchIds 案例id集合
     * @return {@link ExampleDTO}
     */
    List<ExampleDTO> getExampleByExampleIdList(@RequestParam("batchIds") Collection<String> batchIds);

    /**
     * 分类被引用数量
     * @param ids
     * @return
     */
    int checkRef(List<String> ids);

    void handelBusinessCategoryCanDel();

    /**
     * 案例重新转码
     *
     */
    void reTranscoding(ExampleReTranscodingDTO exampleReTranscodingDTO);
}
