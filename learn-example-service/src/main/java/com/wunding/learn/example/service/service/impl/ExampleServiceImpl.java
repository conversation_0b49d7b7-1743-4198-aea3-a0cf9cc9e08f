package com.wunding.learn.example.service.service.impl;

import static com.github.houbb.heaven.util.util.DateUtil.now;
import static com.wunding.learn.common.util.date.DateHelper.YYYYMMDD;
import static com.wunding.learn.common.util.string.StringUtil.newId;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import com.wunding.learn.comment.api.service.CommentFeign;
import com.wunding.learn.common.category.model.Categorys;
import com.wunding.learn.common.category.service.ICategorysService;
import com.wunding.learn.common.constant.appraise.AppraiseErrorNoEnum;
import com.wunding.learn.common.constant.example.ExampleErrorNoEnum;
import com.wunding.learn.common.constant.other.BaseErrorNoEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.category.CategoryType;
import com.wunding.learn.common.enums.comment.CommentTypeEnum;
import com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum;
import com.wunding.learn.common.enums.excitation.ExcitationEventEnum;
import com.wunding.learn.common.enums.file.TranscodeStatusEnum;
import com.wunding.learn.common.enums.market.HeadContentRuleEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.other.RouterVisitEnum;
import com.wunding.learn.common.enums.other.SystemConfigCodeEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.i18n.util.I18nUtil;
import com.wunding.learn.common.mq.dto.ExcitationMQEventDTO;
import com.wunding.learn.common.mq.dto.ResourceConfigInitDTO;
import com.wunding.learn.common.mq.event.HomeRouterVisitEvent;
import com.wunding.learn.common.mq.event.ResourceInteractEvent.ResourceInteractEventRoutingKeyConstants;
import com.wunding.learn.common.mq.event.excitation.ExcitationInitMqEvent;
import com.wunding.learn.common.mq.event.excitation.ExcitationMQEvent;
import com.wunding.learn.common.mq.service.MqProducer;
import com.wunding.learn.common.util.bean.BeanListUtils;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.date.DateUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.common.util.string.TranslateUtil;
import com.wunding.learn.example.api.dto.ExampleDTO;
import com.wunding.learn.example.service.admin.dto.ApprovalStatusDTO;
import com.wunding.learn.example.service.admin.dto.ExampleConfigDTO;
import com.wunding.learn.example.service.admin.dto.ExampleConfigLevelDTO;
import com.wunding.learn.example.service.admin.dto.ExampleFileDTO;
import com.wunding.learn.example.service.admin.dto.ExampleLibDTO;
import com.wunding.learn.example.service.admin.dto.ExampleLibEditDTO;
import com.wunding.learn.example.service.admin.dto.ExampleLibSaveDTO;
import com.wunding.learn.example.service.admin.dto.ExampleReTranscodingDTO;
import com.wunding.learn.example.service.admin.dto.IBaseExampleDTO;
import com.wunding.learn.example.service.admin.query.ExampleLibQuery;
import com.wunding.learn.example.service.client.dto.ApprovalJudgesDTO;
import com.wunding.learn.example.service.client.dto.AuditExampleDTO;
import com.wunding.learn.example.service.client.dto.AuditExampleOptionDTO;
import com.wunding.learn.example.service.client.dto.AuditLevelAndExtraDTO;
import com.wunding.learn.example.service.client.dto.ExampleAuditDTO;
import com.wunding.learn.example.service.client.dto.ExampleAuditOptionDTO;
import com.wunding.learn.example.service.client.dto.ExampleDetailCashDTO;
import com.wunding.learn.example.service.client.dto.ExampleDetailDTO;
import com.wunding.learn.example.service.client.dto.ExampleHomePageDTO;
import com.wunding.learn.example.service.client.dto.ExampleLevelDTO;
import com.wunding.learn.example.service.client.dto.ExampleListDTO;
import com.wunding.learn.example.service.client.dto.MyExampleDTO;
import com.wunding.learn.example.service.client.dto.UploadExampleDTO;
import com.wunding.learn.example.service.client.query.ApprovalJudgesQuery;
import com.wunding.learn.example.service.client.query.ExampleAuditQuery;
import com.wunding.learn.example.service.client.query.ExampleDetailQuery;
import com.wunding.learn.example.service.client.query.ExampleHomePageQuery;
import com.wunding.learn.example.service.client.query.ExampleListQuery;
import com.wunding.learn.example.service.client.query.MyExampleQuery;
import com.wunding.learn.example.service.component.ExampleViewLimitComponent;
import com.wunding.learn.example.service.dao.ExampleCategoryDao;
import com.wunding.learn.example.service.dao.ExampleDao;
import com.wunding.learn.example.service.enums.CaseLevelEnum;
import com.wunding.learn.example.service.event.ExampleAuditEvent;
import com.wunding.learn.example.service.event.ExampleLearnEvent;
import com.wunding.learn.example.service.mapper.ExampleMapper;
import com.wunding.learn.example.service.model.Example;
import com.wunding.learn.example.service.model.ExampleAudit;
import com.wunding.learn.example.service.model.ExampleAuditOptions;
import com.wunding.learn.example.service.model.ExampleCategory;
import com.wunding.learn.example.service.model.ExampleConfig;
import com.wunding.learn.example.service.model.ExampleConfigLevel;
import com.wunding.learn.example.service.model.ExampleHistory;
import com.wunding.learn.example.service.model.ExampleRecord;
import com.wunding.learn.example.service.model.ExampleStar;
import com.wunding.learn.example.service.model.ExampleView;
import com.wunding.learn.example.service.model.ExampleVote;
import com.wunding.learn.example.service.model.ExpertCategory;
import com.wunding.learn.example.service.service.IExampleAuditOptionsService;
import com.wunding.learn.example.service.service.IExampleAuditService;
import com.wunding.learn.example.service.service.IExampleCashService;
import com.wunding.learn.example.service.service.IExampleCategoryService;
import com.wunding.learn.example.service.service.IExampleConfigLevelService;
import com.wunding.learn.example.service.service.IExampleConfigService;
import com.wunding.learn.example.service.service.IExampleHistoryService;
import com.wunding.learn.example.service.service.IExampleRecordService;
import com.wunding.learn.example.service.service.IExampleService;
import com.wunding.learn.example.service.service.IExampleStarService;
import com.wunding.learn.example.service.service.IExampleViewService;
import com.wunding.learn.example.service.service.IExampleVoteService;
import com.wunding.learn.example.service.service.IExpertCategoryService;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.component.FileResourceOptComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.constant.FileBizType;
import com.wunding.learn.file.api.constant.FileResourceEnum;
import com.wunding.learn.file.api.constant.ImageBizType;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.file.api.dto.NamePath;
import com.wunding.learn.file.api.service.FileFeign;
import com.wunding.learn.user.api.dto.OrgDTO;
import com.wunding.learn.user.api.dto.OrgShowDTO;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.dto.UserOrgDTO;
import com.wunding.learn.user.api.dto.viewlimit.ViewLimitMainSaveDTO;
import com.wunding.learn.user.api.dto.viewlimit.ViewLimitProgrammeInfoDTO;
import com.wunding.learn.user.api.service.CategoryFeign;
import com.wunding.learn.user.api.service.OrgFeign;
import com.wunding.learn.user.api.service.ParaFeign;
import com.wunding.learn.user.api.service.UserFeign;
import com.wunding.learn.user.api.service.ViewLimitFeign;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;


/**
 * <p> 案例表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">aixinrong</a>
 * @since 2022-08-29
 */
@Slf4j
@Service("exampleService")
public class ExampleServiceImpl extends ServiceImpl<ExampleMapper, Example> implements IExampleService {

    private static final Integer LIMIT = 1000;
    public static final String EXAMPLE_SERVICE = "exampleService";
    @Resource
    private OrgFeign orgFeign;
    @Resource
    private UserFeign userFeign;
    @Resource
    private FileFeign fileFeign;
    @Resource
    private CommentFeign commentFeign;
    @Resource
    private ExportComponent exportComponent;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private IExampleConfigService exampleConfigService;
    @Resource
    private IExampleCategoryService exampleCategoryService;
    @Resource
    private IExampleHistoryService exampleHistoryService;
    @Resource
    private ExampleViewLimitComponent exampleViewLimitComponent;
    @Resource
    private IExampleAuditService exampleAuditService;
    @Resource
    private IExampleRecordService exampleRecordService;
    @Resource
    private IExampleViewService exampleViewService;
    @Resource
    private FileResourceOptComponent fileResourceOptComponent;
    @Resource
    private IExpertCategoryService expertCategoryService;
    @Resource
    private IExampleAuditOptionsService exampleAuditOptionsService;
    @Resource
    private IExampleConfigLevelService exampleConfigLevelService;
    @Resource
    private IExampleStarService exampleStarService;
    @Resource
    private IExampleVoteService exampleVoteService;

    @Resource
    private IExampleCashService exampleCashService;

    @Resource
    private ViewLimitFeign viewLimitFeign;
    @Resource(name = "exampleDao")
    private ExampleDao exampleDao;
    @Resource(name = "exampleCategoryDao")
    private ExampleCategoryDao exampleCategoryDao;

    @Resource
    private ICategorysService categorysService;
    @Resource
    private CategoryFeign categoryFeign;
    @Resource
    private ParaFeign paraFeign;

    @Override
    public PageInfo<ExampleLibDTO> list(ExampleLibQuery exampleLibQuery) {
        String currentUserId = UserThreadContext.getUserId();
        exampleLibQuery.setCurrentUserId(currentUserId);
        exampleLibQuery.setCurrentOrgId(UserThreadContext.getOrgId());
        exampleLibQuery.setManagerAreaOrgIds(orgFeign.findUserManageAreaLevelPath(currentUserId));
        if (StringUtils.isNotBlank(exampleLibQuery.getAuthorIds())) {
            exampleLibQuery.setAuthorIdVo(TranslateUtil.translateBySplit(exampleLibQuery.getAuthorIds(), String.class));
        }
        if (StringUtils.isNotBlank(exampleLibQuery.getExcludeAuditStatusStr())) {
            exampleLibQuery.setExcludeAuditStatusList(
                TranslateUtil.translateBySplit(exampleLibQuery.getExcludeAuditStatusStr(), String.class));
        }
        PageInfo<ExampleLibDTO> pageInfo = PageMethod.startPage(exampleLibQuery.getPageNo(),
            exampleLibQuery.getPageSize()).doSelectPageInfo(() -> baseMapper.list(exampleLibQuery));
        List<String> userIds = pageInfo.getList().stream().map(ExampleLibDTO::getAuthorId)
            .filter(StringUtils::isNotBlank).collect(Collectors.toList());
        Map<String, UserOrgDTO> userMap = orgFeign.getUserAndOrgMapByUserIds(userIds);
        Set<String> orgIdSet = userMap.values().stream().map(UserOrgDTO::getOrgId).collect(Collectors.toSet());
        Map<String, OrgShowDTO> orgShowDTOMap = orgFeign.getOrgShowDTO(orgIdSet);
        for (ExampleLibDTO exampleLibDTO : pageInfo.getList()) {
            Optional.ofNullable(userMap.get(exampleLibDTO.getAuthorId())).ifPresent(author -> {
                exampleLibDTO.setAuthorName(author.getFullName());
                Optional.ofNullable(orgShowDTOMap.get(author.getOrgId())).ifPresent(orgShowDTO -> {
                    exampleLibDTO.setOrgName(orgShowDTO.getOrgShortName());
                    exampleLibDTO.setOrgPath(orgShowDTO.getLevelPathName());
                });
            });
            replenishField(exampleLibDTO);
        }

        //路由访问埋点
        mqProducer.sendMsg(new HomeRouterVisitEvent(RouterVisitEnum.CaselibraryManage.getRouterId(), currentUserId,
            RouterVisitEnum.CaselibraryManage.getName()));

        return pageInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createOrUpdate(ExampleLibSaveDTO exampleLibSaveDTO) {
        IExampleService exampleService = SpringUtil.getBean(EXAMPLE_SERVICE, IExampleService.class);
        if (StringUtils.isNotBlank(exampleLibSaveDTO.getId())) {
            update(exampleLibSaveDTO);
            handleCategoryCanDel();
            assert exampleService != null;
            exampleService.handelBusinessCategoryCanDel();
            return;
        }
        create(exampleLibSaveDTO);
        handleCategoryCanDel();
        assert exampleService != null;
        exampleService.handelBusinessCategoryCanDel();
    }

    /**
     * 处理案例条线是否可删除
     */
    @Async("commonTaskThreadPool")
    public void handelBusinessCategoryCanDel() {
        // 条线有两处地方使用：专家、案例库
        List<Example> exampleList = list(new LambdaQueryWrapper<Example>().select(Example::getBusinessId));
        List<ExpertCategory> expertCategories = expertCategoryService.list(
            new LambdaQueryWrapper<ExpertCategory>().select(ExpertCategory::getCategoryId)
                .eq(ExpertCategory::getCategoryType,
                    CategoryType.ExpertBusinessTags.name()));
        // 没地方使用，可删除
        if (CollectionUtils.isEmpty(exampleList) && CollectionUtils.isEmpty(expertCategories)) {
            categoryFeign.deleteCategoryByType(CategoryType.ExampleBusiness.name());
            return;
        }
        Set<String> collect = exampleList.stream().map(Example::getBusinessId).collect(Collectors.toSet());
        Set<String> collect1 = expertCategories.stream().map(ExpertCategory::getCategoryId).collect(Collectors.toSet());
        Set<String> all = new HashSet<>();
        all.addAll(collect);
        all.addAll(collect1);
        if (CollectionUtils.isEmpty(all)) {
            categoryFeign.deleteCategoryByType(CategoryType.ExampleBusiness.name());
            return;
        }
        categoryFeign.updateCategoryByListCanDel(all, CategoryType.ExampleBusiness.name());
    }

    private void handleCategoryCanDel() {
        List<Example> exampleList = list();
        if (CollectionUtils.isEmpty(exampleList)) {
            categorysService.deleteCategoryByType(CategoryType.ExampleCate.name());
            return;
        }
        List<String> collect = exampleList.stream().map(Example::getExampleCateId)
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            categorysService.deleteCategoryByType(CategoryType.ExampleCate.name());
            return;
        }
        categorysService.updateByIsCanDelCategoryId(collect, CategoryType.ExampleCate.name());
    }

    @Override
    public ExampleLibEditDTO exampleInfo(String id) {
        Example example = getById(id);
        if (null == example) {
            throw new BusinessException(ExampleErrorNoEnum.ERR_EXAMPLE_NULL);
        }
        ExampleLibEditDTO exampleInfo = new ExampleLibEditDTO();
        BeanUtils.copyProperties(example, exampleInfo);
        // 返回归属部门名称
        exampleInfo.setOrgName(orgFeign.getById(exampleInfo.getOrgId()).getOrgName());
        exampleInfo.setAuthorName(userFeign.getUserById(exampleInfo.getAuthorId()).getFullName());
        exampleInfo.setPlayTime(exampleInfo.getPlayTime().divide(new BigDecimal(60)));
        exampleInfo.setImageUrl(fileFeign.getImageUrl(exampleInfo.getId(), ImageBizType.EXAMPLE_IMAGE.name()));
        Optional.ofNullable(fileFeign.getFileNamePath(exampleInfo.getId(), FileBizType.EXAMPLE_FILE.name()))
            .ifPresent(file -> exampleInfo.setFileName(file.getName()));
        return exampleInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void exampleLevel(String ids, String levelId) {
        List<String> idList = TranslateUtil.translateBySplit(ids, String.class);
        LambdaQueryWrapper<ExampleCategory> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.in(ExampleCategory::getExampleId, idList);
        deleteWrapper.eq(ExampleCategory::getCategoryType, CategoryType.ExampleTagExtra.toString());
        exampleCategoryService.remove(deleteWrapper);
        List<Example> exampleList = listByIds(idList);
        Map<String, String> exampleNameMap = exampleList.stream()
            .collect(Collectors.toMap(Example::getId, Example::getExampleName, (k1, k2) -> k1));
        for (String id : idList) {
            ExampleCategory newBean = new ExampleCategory();
            newBean.setId(newId());
            newBean.setExampleId(id);
            newBean.setCategoryId(levelId);
            newBean.setCategoryType(CategoryType.ExampleTagExtra.toString());
            exampleCategoryDao.updateExampleLevel(newBean, id, StringUtils.isNotBlank(exampleNameMap.get(id))
                ? exampleNameMap.get(id) : "");
        }

        updateExampleNotAuto(idList);
        for (Example example : exampleList) {
            mqProducer.sendMsg(new ExcitationMQEvent(
                new ExcitationMQEventDTO(example.getAuthorId(), ExcitationEventEnum.exampleExtra.name(),
                    example.getId(), ExcitationEventCategoryEnum.EXAMPLE.getCode()).setTargetName(
                    example.getExampleName())));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unExampleLevel(String ids) {
        List<String> idList = TranslateUtil.translateBySplit(ids, String.class);
        Map<String, String> exampleNameMap = listByIds(idList).stream()
            .collect(Collectors.toMap(Example::getId, Example::getExampleName, (k1, k2) -> k1));
        idList.forEach(id ->
            exampleCategoryDao.cancelExampleLevel(id, StringUtils.isNotBlank(exampleNameMap.get(id))
                ? exampleNameMap.get(id) : ""));
        updateExampleNotAuto(idList);
        exampleCashService.removeExampleDetailCache(ids);
    }

    @Override
    public void isRecommend(String ids, Integer isRecommend) {
        List<String> idList = TranslateUtil.translateBySplit(ids, String.class);
        List<Example> examples = listByIds(idList);
        String userId = UserThreadContext.getUserId();
        Date date = new Date();
        examples.forEach(example -> exampleDao.updateExample(
            new Example().setId(example.getId()).setExampleName(example.getExampleName()).setRecommend(isRecommend)
                .setUpdateBy(userId).setUpdateTime(date)));
        exampleCashService.removeExampleDetailCache(ids);
    }

    @Override
    public void delete(String ids) {
        List<String> idList = TranslateUtil.translateBySplit(ids, String.class);
        List<Example> examples = listByIds(idList);
        examples.forEach(example -> exampleDao.delExample(example));
        exampleCashService.removeExampleDetailCache(ids);
        handleCategoryCanDel();
        IExampleService exampleService = SpringUtil.getBean(EXAMPLE_SERVICE, IExampleService.class);
        assert exampleService != null;
        exampleService.handelBusinessCategoryCanDel();
        // 物理删除文件
        String physicallyDeleteFileSwitch = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_28009.getCode());
        if( !CollectionUtils.isEmpty(idList) && String.valueOf(GeneralJudgeEnum.CONFIRM.getValue()).equals(Objects.toString(physicallyDeleteFileSwitch, "0"))){
            fileFeign.deletePhysicalFileByIds(idList);
        }
    }

    @Override
    public void uploadExampleFile(UploadExampleDTO uploadExampleDTO) {
        Example example = new Example();
        checkUploadParam(uploadExampleDTO);
        BeanUtils.copyProperties(uploadExampleDTO, example);
        Boolean newExample = replenishParam(example);
        uploadExampleDTO.setNewExample(newExample);
        String uploadReason =
            StringUtils.isNotBlank(uploadExampleDTO.getUploadReason()) ? uploadExampleDTO.getUploadReason() : "";
        Optional.ofNullable(uploadExampleDTO.getDelFile()).ifPresent(delFile -> {
            if (delFile.equals(1)) {
                fileFeign.deleteFileByBizIdAndBizType(example.getId(), FileBizType.EXAMPLE_FILE.name(), 1);
            }
        });
        Optional.ofNullable(uploadExampleDTO.getDelImage()).ifPresent(
            delImage -> fileFeign.deleteImageByBizIdAndBizType(example.getId(), ImageBizType.EXAMPLE_IMAGE.name()));
        saveExampleFile(uploadExampleDTO, example);
        saveImage(uploadExampleDTO, example);
        //新案例上传
        if (uploadExampleDTO.getFlag().equals(1)) {
            saveHistory(example.getId(), "提交", uploadReason);
            if (Boolean.TRUE.equals(newExample)) {
                save(example);
            } else {
                exampleAuditService.remove(
                    new LambdaQueryWrapper<ExampleAudit>().eq(ExampleAudit::getExampleId, example.getId()));
                exampleAuditService.removeById(example.getId());
                updateById(example);
            }
            // 上传案例 积分奖励
            mqProducer.sendMsg(new ExcitationMQEvent(
                new ExcitationMQEventDTO(example.getAuthorId(), ExcitationEventEnum.exampleAdd.name(), example.getId(),
                    ExcitationEventCategoryEnum.EXAMPLE.getCode()).setTargetName(example.getExampleName())));
            // 初始化激励配置规则
            mqProducer.sendMsg(new ExcitationInitMqEvent(new ResourceConfigInitDTO().setResourceId(example.getId())
                .setResourceType(ExcitationEventCategoryEnum.EXAMPLE.getCode())));
            // 暂存案例上传
        } else if (uploadExampleDTO.getFlag().equals(2)) {
            example.setStatus(-3);
            if (Boolean.TRUE.equals(newExample)) {
                save(example);
            } else {
                exampleAuditService.removeById(example.getId());
                updateById(example);
            }
            saveHistory(example.getId(), "暂存案例", "");
        }
        //保存可见范围
        String orgId = orgFeign.getLevelOrgId(example.getAuthorId());
        saveExampleViewLimit(example.getId(), StringUtils.isBlank(orgId) ? example.getOrgId() : orgId);
        saveExampleCategory(example);
        // 清除缓存
        exampleCashService.removeExampleDetailCache(example.getId());
        IExampleService exampleService = SpringUtil.getBean(EXAMPLE_SERVICE, IExampleService.class);
        assert exampleService != null;
        exampleService.handelBusinessCategoryCanDel();
    }

    // 学员端上传案例校验
    private void checkUploadParam(UploadExampleDTO uploadExampleDTO) {
        if (uploadExampleDTO.getFlag().equals(1)
            && (StringUtils.isBlank(uploadExampleDTO.getExampleName())
            || StringUtils.isBlank(uploadExampleDTO.getSynopsis())
            || StringUtils.isBlank(uploadExampleDTO.getExampleCateId())
            || StringUtils.isBlank(uploadExampleDTO.getBusinessId())
            || Optional.ofNullable(uploadExampleDTO.getPlayTime()).isEmpty())) {
            throw new BusinessException(ExampleErrorNoEnum.ERR_EXAMPLE_UPLOAD_ALL);
        }

        if (uploadExampleDTO.getFlag().equals(2)
            && StringUtils.isBlank(uploadExampleDTO.getExampleName())
            && StringUtils.isBlank(uploadExampleDTO.getSynopsis())
            && StringUtils.isBlank(uploadExampleDTO.getExampleCateId())
            && StringUtils.isBlank(uploadExampleDTO.getBusinessId())
            && Optional.ofNullable(uploadExampleDTO.getPlayTime()).isEmpty()) {
            throw new BusinessException(ExampleErrorNoEnum.ERR_EXAMPLE_UPLOAD_ANY);
        }
    }

    @Override
    public PageInfo<ExampleListDTO> getExampleListByCategory(ExampleListQuery exampleListQuery) {
        exampleListQuery.setCurrentUserId(UserThreadContext.getUserId());
        exampleListQuery.setCurrentOrgId(UserThreadContext.getOrgId());
        if (StringUtils.equalsIgnoreCase("viewWeek", exampleListQuery.getOrder())) {
            Calendar cal = Calendar.getInstance();
            cal.setFirstDayOfWeek(Calendar.MONDAY);
            cal.add(Calendar.DATE, -1 * 7);
            cal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
            Date previousMonday = cal.getTime();
            exampleListQuery.setPreviousMonday(DateUtil.formatDate(previousMonday, YYYYMMDD));
            cal.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
            Date sunday = cal.getTime();
            exampleListQuery.setSunday(DateUtil.formatDate(sunday, YYYYMMDD));
        }
        PageInfo<ExampleListDTO> pageInfo = PageMethod.startPage(exampleListQuery.getPageNo(),
                exampleListQuery.getPageSize(), false)
            .doSelectPageInfo(() -> baseMapper.getExampleListByCategory(exampleListQuery));
        pageInfo.setIsLastPage(exampleListQuery.getPageSize() != pageInfo.getList().size());
        // 图片链接
        List<String> idList = pageInfo.getList().stream().map(ExampleListDTO::getId).filter(StringUtils::isNotBlank)
            .collect(Collectors.toList());
        Map<String, String> imgMap = fileFeign.getImageUrlsByIds(idList, ImageBizType.EXAMPLE_IMAGE.name());
        pageInfo.getList().forEach(dto -> Optional.ofNullable(imgMap.get(dto.getId())).ifPresent(dto::setImage));
        return pageInfo;
    }

    @Override
    public ExampleDetailDTO detail(ExampleDetailQuery exampleDetailQuery) {
        exampleDetailQuery.setCurrentUserId(UserThreadContext.getUserId());
        ExampleDetailCashDTO cashById = exampleCashService.getCashById(exampleDetailQuery.getId());
        ExampleDetailDTO detail = Optional.ofNullable(baseMapper.detail(exampleDetailQuery))
            .orElseThrow(() -> new BusinessException(ExampleErrorNoEnum.ERR_EXAMPLE_NULL));
        Categorys business = categoryFeign.getCategoryDetail(detail.getBusinessId());
        if (Objects.nonNull(business)) {
            detail.setBusinessName(business.getCategoryName());
        }
        BeanUtils.copyProperties(cashById, detail);
        NamePath imgInfo = fileFeign.getImageFileNamePath(detail.getId(), ImageBizType.EXAMPLE_IMAGE.name());
        Optional.ofNullable(imgInfo).ifPresent(img -> {
            detail.setImage(img.getUrl());
            detail.setImagePath(img.getPath());
        });
        // 将原文件回显回去
        Optional.ofNullable(fileFeign.getSourceFileInfo(detail.getId(), FileBizType.EXAMPLE_FILE.name()))
            .ifPresent(file -> {
                detail.setDownloadUrl(file.getUrl());
                detail.setFileName(file.getName());
                detail.setFilePath(file.getPath());
            });
        // 转码完成才有预览功能
        if (detail.getTransformStatus().equals(TranscodeStatusEnum.TRANSFORMED.value)) {
            detail.setPreviewUrl(fileFeign.getFileUrl(detail.getId(), FileBizType.EXAMPLE_FILE.name()));

            // 设置视频清晰度URL
            detail.setVideoClarityList(fileFeign.getVideoClarity(detail.getId()));
        }
        ApprovalJudgesQuery queryParam = new ApprovalJudgesQuery().setIsDetail(true).setFlag(1)
            .setExampleId(exampleDetailQuery.getId());
        if (Boolean.TRUE.equals(checkParam(queryParam))) {
            List<ApprovalJudgesDTO> approvalJudgesDTOS = baseMapper.approvalJudgesList(queryParam);
            if (!CollectionUtils.isEmpty(approvalJudgesDTOS)) {
                detail.setAudit(1);
            }
        }
        Optional.ofNullable(exampleDetailQuery.getLearn()).ifPresent(learn -> {
            if (learn.equals(1)) {
                saveExampleRecord(detail);
            }
        });
        LambdaQueryWrapper<Example> exampleQueryWrapper = new LambdaQueryWrapper<>();
        exampleQueryWrapper.eq(Example::getAuthorId, detail.getAuthor());
        exampleQueryWrapper.notIn(Example::getId, detail.getId());
        exampleQueryWrapper.last("limit 0,5");
        List<Example> examples = list(exampleQueryWrapper);
        if (!CollectionUtils.isEmpty(examples)) {
            detail.setAuthorExampleList(examples.stream().map(e -> {
                ExampleDetailDTO exampleDetailDTO = new ExampleDetailDTO();
                exampleDetailDTO.setId(e.getId());
                exampleDetailDTO.setExampleName(e.getExampleName());
                return exampleDetailDTO;
            }).collect(Collectors.toList()));
        }
        return detail;
    }

    @Override
    public PageInfo<MyExampleDTO> myExample(MyExampleQuery myExampleQuery) {
        myExampleQuery.setCurrentUserId(UserThreadContext.getUserId());
        PageInfo<MyExampleDTO> pageInfo = PageMethod.startPage(myExampleQuery.getPageNo(), myExampleQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.myExample(myExampleQuery));
        for (MyExampleDTO dto : pageInfo.getList()) {
            dto.setImage(fileFeign.getImageUrl(dto.getId(), ImageBizType.EXAMPLE_IMAGE.name()));
        }
        return pageInfo;
    }

    @Override
    public PageInfo<ApprovalJudgesDTO> approvalJudgesList(ApprovalJudgesQuery approvalJudgesQuery) {
        if (Boolean.FALSE.equals(checkParam(approvalJudgesQuery))) {
            return new PageInfo<>();
        }
        PageInfo<ApprovalJudgesDTO> pageInfo = PageMethod.startPage(approvalJudgesQuery.getPageNo(),
                approvalJudgesQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.approvalJudgesList(approvalJudgesQuery));
        Set<String> userIds = pageInfo.getList().stream().map(ApprovalJudgesDTO::getAuthorId)
            .filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        Map<String, UserOrgDTO> userMap = orgFeign.getUserAndOrgMapByUserIds(userIds);
        for (ApprovalJudgesDTO dto : pageInfo.getList()) {
            Optional.ofNullable(userMap.get(dto.getAuthorId())).ifPresent(userInfo -> {
                dto.setAuthor(userInfo.getFullName());
                dto.setAuthorOrgName(userInfo.getOrgName());
            });
            dto.setStayTime((System.currentTimeMillis() - dto.getUpdateTime().getTime()) / 1000 / 60 / 60 + "");
            dto.setImage(fileFeign.getImageUrl(dto.getId(), ImageBizType.EXAMPLE_IMAGE.name()));
        }
        return pageInfo;
    }

    @Override
    public List<ExampleAuditDTO> getExampleAuditList(ExampleAuditQuery exampleAuditQuery) {
        List<ExampleAuditDTO> exampleAuditList = baseMapper.getExampleAuditList(exampleAuditQuery);
        List<String> userIds = exampleAuditList.stream().map(ExampleAuditDTO::getAuditUserId)
            .filter(StringUtils::isNotBlank).collect(Collectors.toList());
        Map<String, UserOrgDTO> userMap = orgFeign.getUserAndOrgMapByUserIds(userIds);
        for (ExampleAuditDTO dto : exampleAuditList) {
            Optional.ofNullable(userMap.get(dto.getAuditUserId())).ifPresent(userInfo -> {
                dto.setAuditUserName(userInfo.getFullName());
                dto.setAuditUserLoginName(userInfo.getLoginName());
                dto.setAuditUserOrgName(userInfo.getOrgName());
            });
            List<ExampleAuditOptionDTO> exampleAuditOptionList = dto.getExampleAuditOptionList();
            List<ExampleAuditOptionDTO> exampleAuditOptionList1 = new ArrayList<>();
            log.info("exampleAuditOptionList: " + exampleAuditOptionList);
            if (!CollectionUtils.isEmpty(exampleAuditOptionList)) {
                for (ExampleAuditOptionDTO exampleAuditOptionDTO : exampleAuditOptionList) {
                    log.info("exampleAuditOptionDTO: " + exampleAuditOptionDTO);
                    if (StringUtils.isNotEmpty(exampleAuditOptionDTO.getId())) {
                        exampleAuditOptionList1.add(exampleAuditOptionDTO);
                    }
                }
            }
            dto.setExampleAuditOptionList(exampleAuditOptionList1);
        }
        return exampleAuditList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditExample(AuditExampleDTO auditExampleDTO) {
        String currentUserId = UserThreadContext.getUserId();
        Example existExample = getById(auditExampleDTO.getExampleId());
        if (Optional.ofNullable(existExample).isEmpty()) {
            throw new BusinessException(ExampleErrorNoEnum.ERR_EXAMPLE_NULL);
        }
        ApprovalStatusDTO exampleConfig = exampleConfigService.getApprovalStatus(auditExampleDTO.getExampleId(),
            currentUserId);
        //已审核则不可继续审核 修复问题
        if (auditExampleDTO.getAuditType().equals(1)) {
            checkSuperiorReview(auditExampleDTO, currentUserId, existExample, exampleConfig);
        }

        //已评审不可继续评审
        if (auditExampleDTO.getAuditType().equals(2)) {
            checkExpertReview(currentUserId, existExample, exampleConfig);
        }

        auditExampleDTO.setId(newId());
        auditExampleDTO.getExampleAuditOptionDtoList().forEach(exampleAuditOptionDtO -> {
            // 审核时保存题目id
            exampleAuditOptionDtO.setQuestionId(exampleAuditOptionDtO.getId());
            exampleAuditOptionDtO.setId(newId());
            exampleAuditOptionDtO.setAuditId(auditExampleDTO.getId());
        });
        List<ExampleAuditOptions> exampleAuditOptions = BeanListUtils.copyList(
            auditExampleDTO.getExampleAuditOptionDtoList(), ExampleAuditOptions.class);
        exampleAuditOptionsService.saveBatch(exampleAuditOptions);

        // 更改案例状态
        Example example = new Example();
        example.setId(auditExampleDTO.getExampleId());
        if (auditExampleDTO.getAuditType().equals(1)) {
            handleSuperiorReview(auditExampleDTO, exampleConfig, example);
        } else if (auditExampleDTO.getAuditType().equals(2)) {
            Integer status;
            int avgScore = getAvgScore(auditExampleDTO);

            status = avgScore < exampleConfig.getExpertAvg() ? 0 : 1;
            //修改文案
            String result = avgScore < exampleConfig.getExpertAvg() ? "认证不通过" : "";
            auditExampleDTO.setResult(status);
            //记录历史
            saveHistory(auditExampleDTO.getExampleId(), "通过专家认证，认证分：" + avgScore + "分。" + result, "");
            // 默认为评审中状态
            int exampleStatus = 4;
            LambdaQueryWrapper<ExampleAudit> exampleAuditQueryWrapper = new LambdaQueryWrapper<>();
            exampleAuditQueryWrapper.eq(ExampleAudit::getExampleId, auditExampleDTO.getExampleId());
            exampleAuditQueryWrapper.eq(ExampleAudit::getAuditType, 2);
            exampleAuditQueryWrapper.eq(ExampleAudit::getResult, 1);
            long expertNumPass = exampleAuditService.count(exampleAuditQueryWrapper);

            exampleAuditQueryWrapper.clear();
            exampleAuditQueryWrapper.eq(ExampleAudit::getExampleId, auditExampleDTO.getExampleId());
            exampleAuditQueryWrapper.eq(ExampleAudit::getAuditType, 2);
            exampleAuditQueryWrapper.eq(ExampleAudit::getResult, 0);
            long expertNumRejection = exampleAuditService.count(exampleAuditQueryWrapper);

            // 算上提交的这条通过数据，评审通过人数等于配置中通过人数，案例变为认证通过
            if (status.equals(1) && expertNumPass + 1 == exampleConfig.getExpertNumPass()) {
                exampleStatus = 5;
                example.setIsLib(1);
                Example authorExample = getById(example.getId());

                // 案例认证通过时，获取奖励 入库案例库
                mqProducer.sendMsg(new ExcitationMQEvent(
                    new ExcitationMQEventDTO(authorExample.getAuthorId(), ExcitationEventEnum.exampleAuditOver.name(),
                        example.getId(), ExcitationEventCategoryEnum.EXAMPLE.getCode()).setTargetName(
                        example.getExampleName())));
            }
            // 算上提交的这条不通过数据，评审不通过人数等于配置中通过人数，案例变为认证不通过
            if (status.equals(0) && expertNumRejection + 1 == exampleConfig.getExpertNumRejection()) {
                exampleStatus = 6;
            }
            example.setStatus(exampleStatus);
            example.setAuditScore((long) avgScore);
            updateById(example);
            auditExampleDTO.setAvgScore(avgScore);
        }
        ExampleAudit newBean = new ExampleAudit();
        BeanUtils.copyProperties(auditExampleDTO, newBean);
        newBean.setAnswer(auditExampleDTO.getReason());
        newBean.setScore(auditExampleDTO.getAvgScore());
        exampleAuditService.save(newBean);

        // 评委进行评分时，自己获得的奖励
        mqProducer.sendMsg(new ExcitationMQEvent(
            new ExcitationMQEventDTO(currentUserId, ExcitationEventEnum.exampleAudit.name(), example.getId(),
                ExcitationEventCategoryEnum.EXAMPLE.getCode()).setTargetName(example.getExampleName())));
        // 刷新评审分数
        mqProducer.sendMsg(new ExampleAuditEvent(example.getId()));
        exampleCashService.removeExampleDetailCache(example.getId());
    }

    private int getAvgScore(AuditExampleDTO auditExampleDTO) {
        int avgScore = 0;
        // 计算上传的结果平均分
        List<AuditExampleOptionDTO> exampleAuditOptionDtoList = auditExampleDTO.getExampleAuditOptionDtoList()
            .stream().filter(exampleAuditOptionDto -> exampleAuditOptionDto.getType().equals(1))
            .collect(Collectors.toList());
        if (!exampleAuditOptionDtoList.isEmpty()) {
            //有填空时，分数不能为空
            for (AuditExampleOptionDTO exampleAuditOptionDto : exampleAuditOptionDtoList) {
                if (StringUtils.isBlank(exampleAuditOptionDto.getAnswer())) {
                    throw new BusinessException(AppraiseErrorNoEnum.ERR_PARAMS);
                }
            }
            avgScore = exampleAuditOptionDtoList.stream()
                .mapToInt(exampleAuditOptionDto -> Integer.parseInt(exampleAuditOptionDto.getAnswer())).sum()
                / exampleAuditOptionDtoList.size();
        }
        return avgScore;
    }

    private void handleSuperiorReview(AuditExampleDTO auditExampleDTO, ApprovalStatusDTO exampleConfig,
        Example example) {
        //1已审核，审核通过，2已审核，审核不通过
        Integer status = auditExampleDTO.getResult().equals(0) ? 2 : 1;
        String result = auditExampleDTO.getResult().equals(0) ? "不通过" : "通过";
        example.setStatus(status);
        //审核通过时配置发布时间 并且配置不含评审时，直接入库
        if (status == 1) {
            example.setPublishTime(new Date());
            if (exampleConfig.getExpertReview() == 1) {
                example.setIsLib(1);
                example.setStatus(3);
            }
        }
        //记录历史
        saveHistory(auditExampleDTO.getExampleId(), "审核" + result, auditExampleDTO.getReason());
        updateById(example);
    }

    private void checkExpertReview(String currentUserId, Example existExample, ApprovalStatusDTO exampleConfig) {
        if (exampleConfig.getExpertMeAudit() > 0) {
            throw new BusinessException(AppraiseErrorNoEnum.ERR_ALREADY_AUDIT);
        }

        //启用专家评审认证
        //这里变成了3
        if (exampleConfig.getExpertReview() == 3) {
            //判断当前用户是否是专家
            UserDTO authorInfo = userFeign.getUserById(existExample.getAuthorId());
            UserDTO currentUserInfo = userFeign.getUserById(currentUserId);
            if (Optional.ofNullable(authorInfo).isEmpty() || Optional.ofNullable(currentUserInfo).isEmpty()) {
                throw new BusinessException(AppraiseErrorNoEnum.ERR_NO_AUTH);
            }

            //判断当前专家是不是能管辖案例的作者
            LambdaQueryWrapper<ExpertCategory> expertCategoryQueryWrapper = new LambdaQueryWrapper<>();
            expertCategoryQueryWrapper.eq(ExpertCategory::getExpertId, currentUserId);
            expertCategoryQueryWrapper.eq(ExpertCategory::getCategoryType, CategoryType.ExpertOrgTags.toString());
            Set<String> auditOrgIds = expertCategoryService.list(expertCategoryQueryWrapper).stream()
                .map(ExpertCategory::getCategoryId).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
            Set<String> allAuditOrgIds = orgFeign.getChildrenIdByIds(auditOrgIds);
            if (CollectionUtils.isEmpty(allAuditOrgIds) || !allAuditOrgIds.contains(authorInfo.getOrgId())) {
                throw new BusinessException(AppraiseErrorNoEnum.ERR_NO_AUTH);
            }

            //判断当前专家的条线是否包含审核的案例的条线
            expertCategoryQueryWrapper.clear();
            expertCategoryQueryWrapper.eq(ExpertCategory::getExpertId, currentUserId);
            expertCategoryQueryWrapper.eq(ExpertCategory::getCategoryType,
                CategoryType.ExpertBusinessTags.toString());
            Set<String> auditBusinessIds = expertCategoryService.list(expertCategoryQueryWrapper).stream()
                .map(ExpertCategory::getCategoryId).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(auditBusinessIds) || !auditBusinessIds.contains(
                existExample.getBusinessId())) {
                throw new BusinessException(AppraiseErrorNoEnum.ERR_NO_AUTH);
            }
        }
    }

    private void checkSuperiorReview(AuditExampleDTO auditExampleDTO, String currentUserId, Example existExample,
        ApprovalStatusDTO exampleConfig) {
        if (exampleConfig.getSuperiorAudit() > 0) {
            throw new BusinessException(AppraiseErrorNoEnum.ERR_ALREADY_AUDIT);
        }

        //如果上级审核，校验上级是否是当前操作人
        if (exampleConfig.getSuperior() == 1) {
            UserDTO authorInfo = userFeign.getUserById(existExample.getAuthorId());
            if (Optional.ofNullable(authorInfo).isEmpty() || StringUtils.isBlank(authorInfo.getSuperiorId())
                || !currentUserId.equals(authorInfo.getSuperiorId())) {
                throw new BusinessException(AppraiseErrorNoEnum.ERR_NO_AUTH);
            }
        }

        //如果最近的管理员审核，则校验是否是最近的管理员
        if (exampleConfig.getSuperior() == 2) {

            Set<String> userManageList = orgFeign.findUserManageAreaLevelPath(currentUserId);
            if (CollectionUtils.isEmpty(userManageList)) {
                throw new BusinessException(AppraiseErrorNoEnum.ERR_NO_AUTH);
            }
            ApprovalJudgesQuery queryParam = new ApprovalJudgesQuery();
            queryParam.setCurrentUserId(currentUserId);
            queryParam.setManagerAreaOrgIds(userManageList);
            queryParam.setExampleId(auditExampleDTO.getExampleId());
            List<ApprovalJudgesDTO> approvalJudgesDTOS = baseMapper.approvalJudgesList(queryParam);
            if (CollectionUtils.isEmpty(approvalJudgesDTOS)) {
                throw new BusinessException(AppraiseErrorNoEnum.ERR_NO_AUTH);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void withdraw(String exampleId) {
        businessCheck(exampleId);
        //撤回后变为草稿
        LambdaUpdateWrapper<Example> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Example::getId, exampleId);
        updateWrapper.set(Example::getStatus, -3);
        updateWrapper.set(Example::getPublishTime, null);
        update(updateWrapper);
        //删除所有评审记录
        LambdaQueryWrapper<ExampleAudit> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExampleAudit::getExampleId, exampleId);
        exampleAuditService.remove(queryWrapper);
        //保存操作历史
        saveHistory(exampleId, "撤回案例", "");
        exampleCashService.removeExampleDetailCache(exampleId);
    }

    @Override
    public List<ExampleAuditOptionDTO> getAuditOptions(Integer type) {
        List<ExampleAuditOptionDTO> exampleAuditOptionDTOList = new ArrayList<>();
        if (type.equals(1)) {
            List<Categorys> categorys = categoryFeign.getCategoryByTypeOrderBySortAsc(
                CategoryType.ExampleAudit.name());
            exampleAuditOptionDTOList = categorys.stream().map(category -> {
                ExampleAuditOptionDTO exampleAuditOptionDTO = new ExampleAuditOptionDTO();
                exampleAuditOptionDTO.setId(category.getId());
                exampleAuditOptionDTO.setTitle(category.getCategoryName());
                exampleAuditOptionDTO.setType(3);
                return exampleAuditOptionDTO;
            }).collect(Collectors.toList());
        } else if (type.equals(2)) {
            exampleAuditOptionDTOList = baseMapper.getExpertOptions();
        }
        return exampleAuditOptionDTOList;
    }

    @Override
    public List<ExampleLevelDTO> getLevelAndScope(Integer square) {
        List<ExampleLevelDTO> exampleLevelVos = new ArrayList<>(2);
        // 不传/传0时代表广场,需返回开放范围
        if (null == square || square.equals(GeneralJudgeEnum.NEGATIVE.getValue())) {
            ExampleLevelDTO exampleLevelVo1 = new ExampleLevelDTO();
            exampleLevelVo1.setType(1);
            // 自运营范围
            exampleLevelVo1.setExampleLevelVOS(baseMapper.getScope());
            exampleLevelVos.add(exampleLevelVo1);
        }
        ExampleLevelDTO exampleLevelVo2 = new ExampleLevelDTO();
        exampleLevelVo2.setType(2);
        exampleLevelVo2.setExampleLevelVOS(baseMapper.getLevel());
        exampleLevelVos.add(exampleLevelVo2);
        return exampleLevelVos;
    }

    @Override
    public void auditLevelAndExtra(AuditLevelAndExtraDTO auditLevelAndExtraDTO) {
        String exampleId = auditLevelAndExtraDTO.getExampleId();
        ApprovalJudgesQuery queryParam = new ApprovalJudgesQuery().setIsDetail(true).setFlag(1).setExampleId(exampleId);
        if (Boolean.FALSE.equals(checkParam(queryParam))) {
            throw new BusinessException(AppraiseErrorNoEnum.ERR_NO_AUTH);
        }
        List<ApprovalJudgesDTO> approvalJudgesDTOS = baseMapper.approvalJudgesList(queryParam);
        if (CollectionUtils.isEmpty(approvalJudgesDTOS)) {
            throw new BusinessException(AppraiseErrorNoEnum.ERR_NO_AUTH);
        }
        Example authorExample = Optional.ofNullable(getById(exampleId))
            .orElseThrow(() -> new BusinessException(ExampleErrorNoEnum.ERR_EXAMPLE_NULL));

        handleAuthorExample(auditLevelAndExtraDTO, exampleId, authorExample);

        if (auditLevelAndExtraDTO.getExtra().equals("0")) {
            delCategoryTag(exampleId);
        } else {
            delCategoryTag(exampleId);
            ExampleCategory exampleCategory = new ExampleCategory();
            exampleCategory.setId(newId());
            exampleCategory.setExampleId(exampleId);
            exampleCategory.setCategoryId(auditLevelAndExtraDTO.getExtra());
            exampleCategory.setCategoryType(CategoryType.ExampleTagExtra.toString());
            LambdaQueryWrapper<ExampleConfigLevel> levelQueryWrapper = new LambdaQueryWrapper<>();
            levelQueryWrapper.eq(ExampleConfigLevel::getId, auditLevelAndExtraDTO.getExtra());
            ExampleConfigLevel configLevel = exampleConfigLevelService.getOne(levelQueryWrapper);
            String configLevelTitle = StringUtils.EMPTY;
            if (Optional.ofNullable(configLevel).isPresent()) {
                configLevelTitle = configLevel.getTitle();
            }
            saveHistory(exampleId, "被设置为“" + configLevelTitle + "” 案例", "");
            exampleCategoryService.save(exampleCategory);
            // 变更优质时，发送消息
            String extra = exampleCategoryService.getExtraByExampleId(authorExample.getId());
            if (StringUtils.isNotBlank(extra) && !extra.equals(auditLevelAndExtraDTO.getExtra())) {
                mqProducer.sendMsg(new ExcitationMQEvent(
                    new ExcitationMQEventDTO(authorExample.getAuthorId(), ExcitationEventEnum.exampleCompany.name(),
                        authorExample.getId(), ExcitationEventCategoryEnum.EXAMPLE.getCode()).setTargetName(
                        authorExample.getExampleName())));
            }
        }
    }

    private void handleAuthorExample(AuditLevelAndExtraDTO auditLevelAndExtraDTO, String exampleId,
        Example authorExample) {
        String authorId = authorExample.getAuthorId();
        if (StringUtils.isNotBlank(auditLevelAndExtraDTO.getLevel())) {
            Example example = new Example();
            example.setId(exampleId);
            ExampleCategory exampleCategory = new ExampleCategory();
            exampleCategory.setId(newId());
            exampleCategory.setExampleId(exampleId);
            exampleCategory.setCategoryType(CategoryType.ExampleTagLevel.toString());
            exampleCategoryService.remove(
                new LambdaQueryWrapper<ExampleCategory>().eq(ExampleCategory::getExampleId, exampleId));
            // 公司级
            if (auditLevelAndExtraDTO.getLevel().equals(CaseLevelEnum.EXAMPLESCOPEL2.getType())) {
                String orgId = orgFeign.getOrgCompanyByTypeAndUserId(2, authorId);
                if (StringUtils.isBlank(orgId)) {
                    orgId = "0";
                }
                saveExampleViewLimit(exampleId, orgId);
                saveHistory(exampleId, "升级至公司级案例", "");
                exampleCategory.setCategoryId(auditLevelAndExtraDTO.getLevel());
                delCategoryLevel(exampleId);
                exampleCategoryService.save(exampleCategory);
                //积分消息 升级至公司级
                mqProducer.sendMsg(new ExcitationMQEvent(
                    new ExcitationMQEventDTO(authorExample.getAuthorId(), ExcitationEventEnum.exampleCompany.name(),
                        example.getId(), ExcitationEventCategoryEnum.EXAMPLE.getCode()).setTargetName(
                        authorExample.getExampleName())));
                //集团级
            } else if (auditLevelAndExtraDTO.getLevel().equals(CaseLevelEnum.EXAMPLESCOPEL3.getType())) {
                saveExampleViewLimit(exampleId, "0");
                saveHistory(exampleId, "升级至集团级案例", "");
                exampleCategory.setCategoryId(auditLevelAndExtraDTO.getLevel());
                delCategoryLevel(exampleId);
                exampleCategoryService.save(exampleCategory);
                //积分消息 升级至集团级
                mqProducer.sendMsg(new ExcitationMQEvent(
                    new ExcitationMQEventDTO(authorExample.getAuthorId(), ExcitationEventEnum.exampleGroup.name(),
                        example.getId(), ExcitationEventCategoryEnum.EXAMPLE.getCode()).setTargetName(
                        authorExample.getExampleName())));
                //部门级
            } else if (auditLevelAndExtraDTO.getLevel().equals(CaseLevelEnum.EXAMPLESCOPEL1.getType())) {
                String orgId = orgFeign.getOrgCompanyByTypeAndUserId(1, authorId);
                if (StringUtils.isBlank(orgId)) {
                    orgId = orgFeign.getOrgCompanyByTypeAndUserId(2, authorId);
                    if (StringUtils.isBlank(orgId)) {
                        orgId = "0";
                    }
                }
                saveExampleViewLimit(exampleId, orgId);
                saveHistory(exampleId, "升级至部门级案例", "");
                exampleCategory.setCategoryId(auditLevelAndExtraDTO.getLevel());
                delCategoryLevel(exampleId);
                exampleCategoryService.save(exampleCategory);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void caseExtra() {
        ExampleConfigDTO exampleConfig = exampleConfigService.getExampleConfig();
        caseExtraChange(exampleConfig);
        caseLevelChange(exampleConfig);
    }

    /**
     * 案例内容等级变更
     */
    private void caseExtraChange(ExampleConfigDTO exampleConfig) {
        List<ExampleConfigLevelDTO> exampleConfigLevelList = exampleConfig.getExampleConfigLevels();

        // 过滤无效配置 - 倒序排序
        List<ExampleConfigLevelDTO> configList = exampleConfigLevelList.stream()
            .filter(exampleConfigLevel -> StringUtils.isNotBlank(exampleConfigLevel.getTitle()))
            .sorted((o1, o2) -> o2.getSort() - o1.getSort()).collect(Collectors.toList());

        log.info("有效等级配置:{}", JsonUtil.objToJson(configList));

        //获取待升级案例数据列表
        List<ExampleDetailDTO> exampleExtraChange = baseMapper.getExampleExtraChange();
        Set<String> idList = exampleExtraChange.stream().map(ExampleDetailDTO::getId).filter(StringUtils::isNotBlank)
            .collect(Collectors.toSet());
        log.info("待变更的案例idList:{}", JsonUtil.objToJson(idList));
        Map<String, Integer> commentNumMap = commentFeign.getValidDiscussCount(idList, CommentTypeEnum.EXAMPLE);

        List<ExampleCategory> categories = new ArrayList<>(exampleExtraChange.size());
        ExampleCategory exampleCategory;
        for (ExampleDetailDTO example : exampleExtraChange) {
            exampleCategory = new ExampleCategory();
            exampleCategory.setId(StringUtil.newId());
            exampleCategory.setExampleId(example.getId());
            exampleCategory.setCategoryType(CategoryType.ExampleTagExtra.toString());

            Integer commentNum = commentNumMap.get(example.getId());

            for (ExampleConfigLevelDTO exampleConfigLevel : configList) {
                // 有效评论是否开启
                Boolean effOpen = GeneralJudgeEnum.CONFIRM.getValue().equals(exampleConfigLevel.getEffType());
                // 评审是否开启
                Boolean scoreOpen = GeneralJudgeEnum.CONFIRM.getValue().equals(exampleConfigLevel.getScoreType());
                // 是否达成修改条件
                Boolean change = Boolean.FALSE;

                // 都开启
                if (effOpen && scoreOpen) {
                    change = checkAll(example, commentNum, exampleConfigLevel);
                } else if (Boolean.TRUE.equals(effOpen)) {
                    // 只开启了有效评论
                    change = checkEff(commentNum, exampleConfigLevel);
                } else if (Boolean.TRUE.equals(scoreOpen)) {
                    // 只开启了评审
                    change = checkScore(example, exampleConfigLevel);
                }

                // 满足变更条件
                if (Boolean.TRUE.equals(change)) {
                    log.info("满足质量变更，案例id:{},质量id:{}", example.getId(), exampleConfigLevel.getId());
                    exampleCategory.setCategoryId(exampleConfigLevel.getId());
                    break;
                }
            }
            if (StringUtils.isNotBlank(exampleCategory.getCategoryId()) && !exampleCategory.getCategoryId()
                .equals(example.getExtra())) {
                categories.add(exampleCategory);
                //发送积分消息
                mqProducer.sendMsg(new ExcitationMQEvent(
                    new ExcitationMQEventDTO(example.getAuthorId(), ExcitationEventEnum.exampleExtra.name(),
                        example.getId(), ExcitationEventCategoryEnum.EXAMPLE.getCode()).setTargetName(
                        example.getExampleName())));
            }
        }

        // 分批插入
        handleSaveBatch(categories);
    }

    private void handleSaveBatch(List<ExampleCategory> categories) {
        if (!CollectionUtils.isEmpty(categories)) {
            List<List<ExampleCategory>> partition = Lists.partition(categories, LIMIT);
            for (List<ExampleCategory> exampleCategories : partition) {
                List<String> ids = exampleCategories.stream().map(ExampleCategory::getExampleId)
                    .collect(Collectors.toList());
                //删除案例原有内容标签 1
                exampleCategoryService.delExampleTags(ids);
                exampleCategoryService.saveBatch(exampleCategories);
                log.info("变更的案例等级:{}", JsonUtil.objToJson(exampleCategories));
            }
        }
    }

    private Boolean checkScore(ExampleDetailDTO example, ExampleConfigLevelDTO exampleConfigLevel) {
        if (example.getStatus() == 5 && example.getScore().intValue() >= exampleConfigLevel.getScore()) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    private Boolean checkEff(Integer commentNum, ExampleConfigLevelDTO exampleConfigLevel) {
        if (Optional.ofNullable(commentNum).isPresent() && commentNum >= exampleConfigLevel.getEffNum()) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    private Boolean checkAll(ExampleDetailDTO example, Integer commentNum, ExampleConfigLevelDTO exampleConfigLevel) {
        if ((example.getStatus() == 5 && example.getScore().intValue() >= exampleConfigLevel.getScore()) && (
            Optional.ofNullable(commentNum).isPresent() && commentNum >= exampleConfigLevel.getEffNum())) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }


    /**
     * 案例开放范围变更 下发修改
     */
    private void caseLevelChange(ExampleConfigDTO exampleConfig) {
        List<ExampleDetailDTO> exampleExtraChange = baseMapper.getExampleExtraChange();
        if (exampleConfig.getSelfOperation() == 2) {
            for (ExampleDetailDTO example : exampleExtraChange) {
                // 变更为公司级
                boolean breakCondition = extractedDept(exampleConfig, example);
                // 变更为集团级
                if (extractedComp(exampleConfig, example)) {
                    breakCondition = true;
                }
                // 当前案例有变动
                if (breakCondition) {
                    // 处理当前案例的评星数，清空处理
                    handlerRemoveStar(example);
                    break;
                }
            }
        }
    }

    /**
     * 数据评星清空
     *
     * @param example 实例
     */
    private void handlerRemoveStar(ExampleDetailDTO example) {
        String exampleId = example.getId();
        // 清除评星
        List<ExampleStar> list = exampleStarService.lambdaQuery().select(ExampleStar::getId)
            .eq(ExampleStar::getExampleId, exampleId).list();
        exampleStarService.removeByIds(list);

        // 清除点赞
        List<ExampleVote> list2 = exampleVoteService.lambdaQuery().select(ExampleVote::getId)
            .eq(ExampleVote::getExampleId, exampleId).list();
        exampleVoteService.removeByIds(list2);

        lambdaUpdate()
            .set(Example::getStarNum, 0)
            .set(Example::getLikeNum, 0)
            .set(Example::getCommonStar, 0)
            .eq(Example::getId, exampleId)
            .update();

        exampleCashService.removeExampleDetailCache(exampleId);
    }

    /**
     * 提取公司级案例升级
     *
     * @param exampleConfig 配置
     * @param example       案例
     * @return boolean 是否成功升级
     */
    private boolean extractedComp(ExampleConfigDTO exampleConfig, ExampleDetailDTO example) {
        boolean condition = example.getVoteNum() > exampleConfig.getCompPv()
            && example.getCommonStar().intValue() > exampleConfig.getCompStar();
        boolean isScopeL2 = CaseLevelEnum.EXAMPLESCOPEL2.getType().equals(example.getLevel());
        if (condition && isScopeL2) {
            //范围标签变更,案例升级只能一步一步来
            try {
                //修改可见
                saveExampleViewLimit(example.getId(), "0");
                lambdaUpdate()
                    .set(Example::getExampleOrgId, "0")
                    .eq(Example::getId, example.getId())
                    .update();
                extracted(example.getId(), CaseLevelEnum.EXAMPLESCOPEL3.getType());
                log.info("案例id:{},案例名称{} -> 变更为集团级", example.getId(), example.getExampleName());
                //发送积分消息
                mqProducer.sendMsg(new ExcitationMQEvent(
                    new ExcitationMQEventDTO(example.getAuthorId(), ExcitationEventEnum.exampleGroup.name(),
                        example.getId(), ExcitationEventCategoryEnum.EXAMPLE.getCode()).setTargetName(
                        example.getExampleName())));
                return true;
            } catch (Exception e) {
                log.error("案例id:{},案例名称{} -> 变更为集团级失败！", example.getId(), example.getExampleName());
            }
        }
        return false;
    }

    /**
     * 提取部门级案例升级
     *
     * @param exampleConfig 配置
     * @param example       案例
     * @return boolean 是否成功升级
     */
    private boolean extractedDept(ExampleConfigDTO exampleConfig, ExampleDetailDTO example) {
        boolean condition = example.getVoteNum() > exampleConfig.getDeptPv()
            && example.getCommonStar().doubleValue() > exampleConfig.getDeptStar();
        boolean isScopeL1 = CaseLevelEnum.EXAMPLESCOPEL1.getType().equals(example.getLevel());

        if (condition && isScopeL1) {
            try {
                String orgId = orgFeign.getLevelCompany(example.getAuthorId());
                //如果组织中无公司级
                String categoryId = CaseLevelEnum.EXAMPLESCOPEL2.getType();
                if (StringUtils.isBlank(orgId)) {
                    orgId = "0";
                    categoryId = CaseLevelEnum.EXAMPLESCOPEL3.getType();
                }
                //修改可见
                saveExampleViewLimit(example.getId(), orgId);
                lambdaUpdate()
                    .set(Example::getExampleOrgId, orgId)
                    .eq(Example::getId, example.getId())
                    .update();
                extracted(example.getId(), categoryId);
                log.info("案例id:{},案例名称{} -> 变更为公司级", example.getId(), example.getExampleName());
                //发送积分消息
                mqProducer.sendMsg(new ExcitationMQEvent(new ExcitationMQEventDTO(example.getAuthorId(),
                    ExcitationEventEnum.exampleCompany.name(), example.getId(),
                    ExcitationEventCategoryEnum.EXAMPLE.getCode()).setTargetName(
                    example.getExampleName())));
                return true;
            } catch (Exception e) {
                // 记录日志
                log.error("案例id:{},案例名称{} ->变更为公司级失败！", example.getId(), example.getExampleName());
            }
        }
        return false;
    }

    /**
     * 保存案例级别(删除旧级别)
     *
     * @param exampleId
     * @param categoryId
     */
    private void extracted(String exampleId, String categoryId) {
        //删除原等级
        exampleCategoryService.delExampleLevels(Collections.singletonList(exampleId));
        ExampleCategory newBean = new ExampleCategory();
        newBean.setId(newId());
        newBean.setExampleId(exampleId);
        newBean.setCategoryId(categoryId);
        newBean.setCategoryType(CategoryType.ExampleTagLevel.toString());
        exampleCategoryService.save(newBean);
    }

    private void delCategoryTag(String exampleId) {
        LambdaQueryWrapper<ExampleCategory> delCategory = new LambdaQueryWrapper<>();
        delCategory.eq(ExampleCategory::getExampleId, exampleId);
        delCategory.eq(ExampleCategory::getCategoryType, "ExampleTagExtra");
        exampleCategoryService.remove(delCategory);
    }

    private void delCategoryLevel(String exampleId) {
        LambdaQueryWrapper<ExampleCategory> delCategory = new LambdaQueryWrapper<>();
        delCategory.eq(ExampleCategory::getExampleId, exampleId);
        delCategory.eq(ExampleCategory::getCategoryType, "ExampleTagLevel");
        exampleCategoryService.remove(delCategory);
    }

    private Boolean checkParam(ApprovalJudgesQuery approvalJudgesQuery) {
        // 字段补充
        approvalJudgesQuery.setCurrentUserId(UserThreadContext.getUserId());
        approvalJudgesQuery.setCurrentOrgId(UserThreadContext.getOrgId());
        ExampleConfig exampleConfig = exampleConfigService.getOne(new LambdaQueryWrapper<>());
        if (approvalJudgesQuery.getFlag().equals(1)) {
            approvalJudgesQuery.setType(exampleConfig.getSuperior());
            if (handle(approvalJudgesQuery, exampleConfig)) {
                return Boolean.FALSE;
            }
            // 已审核数据 不为详情时
            if (exampleConfig.getSuperior() == 3) {
                approvalJudgesQuery.setHistory(1);
                approvalJudgesQuery.setType(0);
            }
        } else if (approvalJudgesQuery.getFlag().equals(2)) {
            approvalJudgesQuery.setType(3);
            if (exampleConfig.getExpertReview().equals(1)) {
                approvalJudgesQuery.setHistory(2);
                approvalJudgesQuery.setType(0);
            }
        }
        log.info("案例查询入参:{}", JsonUtil.objToJson(approvalJudgesQuery));
        return Boolean.TRUE;
    }

    private boolean handle(ApprovalJudgesQuery approvalJudgesQuery, ExampleConfig exampleConfig) {
        if (exampleConfig.getSuperior() == 2) {
            Set<String> manageOrgIdList = orgFeign.findUserManageAreaLevelPath(UserThreadContext.getUserId());
            if (CollectionUtils.isEmpty(manageOrgIdList)) {
                return true;
            }
            approvalJudgesQuery.setManagerAreaOrgIds(manageOrgIdList);
        }
        if (exampleConfig.getSuperior() == 1) {
            List<String> underlingIds = userFeign.getUnderlingIdsById(UserThreadContext.getUserId());
            if (CollectionUtils.isEmpty(underlingIds)) {
                return true;
            }
            approvalJudgesQuery.setUnderlingIds(underlingIds);
        }
        if (approvalJudgesQuery.getIsDetail() != null && approvalJudgesQuery.getIsDetail()) {
            Set<String> manageOrgIdList = orgFeign.findUserManageAreaLevelPath(UserThreadContext.getUserId());
            if (CollectionUtils.isEmpty(manageOrgIdList)) {
                return true;
            }
            approvalJudgesQuery.setManagerAreaOrgIds(manageOrgIdList);
            approvalJudgesQuery.setType(4);
        }
        return false;
    }

    private void saveExampleRecord(ExampleDetailDTO detail) {
        LambdaQueryWrapper<ExampleRecord> recordQueryWrapper = new LambdaQueryWrapper<>();
        recordQueryWrapper.eq(ExampleRecord::getExampleId, detail.getId());
        recordQueryWrapper.eq(ExampleRecord::getCreateBy, UserThreadContext.getUserId());
        ExampleRecord exampleRecord = exampleRecordService.getOne(recordQueryWrapper);
        if (Optional.ofNullable(exampleRecord).isEmpty()) {
            ExampleRecord newBean = new ExampleRecord();
            newBean.setId(newId());
            newBean.setUserId(UserThreadContext.getUserId());
            newBean.setExampleId(detail.getId());
            newBean.setViewCount(1);
            exampleRecordService.save(newBean);
            // 刷新学习人数
            mqProducer.sendMsg(new ExampleLearnEvent(detail.getId()));
        } else {
            exampleRecord.setViewCount(exampleRecord.getViewCount() + 1);
            exampleRecordService.updateById(exampleRecord);
        }
        String currentTime = DateUtil.getCurrentTime(YYYYMMDD);
        ExampleView viewRecord = exampleViewService.getExampleView(detail.getId(), UserThreadContext.getUserId(),
            currentTime);
        if (Optional.ofNullable(viewRecord).isEmpty()) {
            ExampleView newBean = new ExampleView();
            newBean.setId(newId());
            newBean.setExampleId(detail.getId());
            newBean.setViewBy(UserThreadContext.getUserId());
            newBean.setViewCount(1);
            newBean.setViewTime(new Date());
            exampleViewService.save(newBean);
        } else {
            viewRecord.setViewCount(viewRecord.getViewCount() + 1);
            exampleViewService.updateById(viewRecord);
        }
    }

    private void updateExampleNotAuto(List<String> idList) {
        LambdaUpdateWrapper<Example> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(Example::getAutoUpLevel, 0);
        updateWrapper.in(Example::getId, idList);
        update(updateWrapper);
    }

    private void update(ExampleLibSaveDTO exampleLibSaveDTO) {
        Example update = new Example();
        BeanUtils.copyProperties(exampleLibSaveDTO, update);
        if (StringUtils.isNotBlank(exampleLibSaveDTO.getAuthorId())) {
            UserDTO author = userFeign.getUserById(exampleLibSaveDTO.getAuthorId());
            if (StringUtils.isBlank(update.getOrgId())) {
                update.setOrgId(author.getOrgId());
            }
            update.setLevelPath(author.getLevelPath());
            String orgId = orgFeign.getLevelOrgId(exampleLibSaveDTO.getAuthorId());
            saveExampleViewLimit(update.getId(), StringUtils.isBlank(orgId) ? author.getOrgId() : orgId);
        }
        String orgId = orgFeign.getLevelDepart(update.getAuthorId());
        update.setExampleOrgId(orgId);
        // 案例编号不可修改
        update.setExampleCode(null);
        update.setPlayTime(update.getPlayTime().multiply(new BigDecimal(60)));
        NamePath odlImage = fileFeign.getImageFileNamePath(update.getId(), ImageBizType.EXAMPLE_IMAGE.name());
        Optional.ofNullable(exampleLibSaveDTO.getCoverImagePath()).ifPresent(coverImagePath -> {
            if (Optional.ofNullable(odlImage).isEmpty() || !coverImagePath.equals(odlImage.getPath())) {
                saveImage(exampleLibSaveDTO, update);
            }
        });
        exampleDao.updateExample(update);
        exampleCashService.removeExampleDetailCache(exampleLibSaveDTO.getId());
    }

    private void create(ExampleLibSaveDTO exampleLibSaveDTO) {
        Example create = new Example();
        BeanUtils.copyProperties(exampleLibSaveDTO, create);
        exampleLibSaveDTO.setNewExample(replenishParam(create));
        exampleLibSaveDTO.setFlag(1);
        saveExampleFile(exampleLibSaveDTO, create);
        saveImage(exampleLibSaveDTO, create);
        saveExampleCategory(create);
        String orgId = orgFeign.getLevelDepart(create.getAuthorId());
        create.setExampleOrgId(orgId);
        exampleDao.saveExample(create);
        saveHistory(create.getId(), "提交", "");
        String levelOrgId = orgFeign.getLevelOrgId(create.getAuthorId());
        saveExampleViewLimit(create.getId(), StringUtils.isBlank(levelOrgId) ? create.getOrgId() : levelOrgId);
        // 上传案例
        mqProducer.sendMsg(new ExcitationMQEvent(
            new ExcitationMQEventDTO(create.getAuthorId(), ExcitationEventEnum.exampleAdd.name(), create.getId(),
                ExcitationEventCategoryEnum.EXAMPLE.getCode()).setTargetName(create.getExampleName())));
        // 初始化资源激励配置规则
        mqProducer.sendMsg(new ExcitationInitMqEvent(new ResourceConfigInitDTO().setResourceId(create.getId())
            .setResourceType(ExcitationEventCategoryEnum.EXAMPLE.getCode())));
    }

    private void saveExampleViewLimit(String exampleId, String orgId) {
        //保存可见范围
        TreeSet<ViewLimitMainSaveDTO> set = new TreeSet<>();

        ViewLimitMainSaveDTO saveViewLimitDTO = new ViewLimitMainSaveDTO();
        saveViewLimitDTO.setViewId(orgId).setViewType(0).setLimitType(0);

        set.add(saveViewLimitDTO);

        ViewLimitProgrammeInfoDTO viewLimitProgrammeInfoDTO = viewLimitFeign.saveViewLimit(set);
        exampleViewLimitComponent.handleNewViewLimit(viewLimitProgrammeInfoDTO.getId(), exampleId);
    }

    private void saveHistory(String id, String title, String reason) {
        ExampleHistory create = new ExampleHistory();
        create.setId(newId());
        create.setExampleId(id);
        create.setTitle(title);
        create.setReason(reason);
        exampleHistoryService.save(create);
    }

    private void saveExampleCategory(Example create) {
        ExampleCategory exampleCategory = new ExampleCategory();
        exampleCategory.setId(newId());
        exampleCategory.setExampleId(create.getId());
        //判断当前用户所在最近的运营级组织
        String orgId = orgFeign.getLevelOrgId(create.getAuthorId());
        if (StringUtils.isNotBlank(orgId)) {
            Optional.ofNullable(orgFeign.getById(orgId)).map(OrgDTO::getDimension).ifPresent(item -> {
                if (item.intValue() == 1) {
                    exampleCategory.setCategoryId(CaseLevelEnum.EXAMPLESCOPEL1.getType());
                }
                if (item.intValue() == 2) {
                    exampleCategory.setCategoryId(CaseLevelEnum.EXAMPLESCOPEL2.getType());
                }
            });
        }
        if (StringUtils.isBlank(orgId)) {
            exampleCategory.setCategoryId(CaseLevelEnum.EXAMPLESCOPEL3.getType());
        }
        exampleCategory.setCategoryType(CategoryType.ExampleTagLevel.toString());
        LambdaUpdateWrapper<ExampleCategory> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ExampleCategory::getExampleId, create.getId());
        exampleCategoryService.remove(updateWrapper);
        exampleCategoryService.save(exampleCategory);
    }

    private void saveImage(IBaseExampleDTO exampleLibSaveDTO, Example create) {
        if (StringUtils.isNotBlank(exampleLibSaveDTO.getCoverImagePath())) {
            if (StringUtils.isNotBlank(create.getId())) {
                fileFeign.deleteImageByBizIdAndBizType(create.getId(), ImageBizType.EXAMPLE_IMAGE.name());
            }
            fileFeign.saveImage(create.getId(), ImageBizType.EXAMPLE_IMAGE.name(),
                exampleLibSaveDTO.getCoverImageName(), exampleLibSaveDTO.getCoverImagePath());
        }
    }

    private void saveExampleFile(IBaseExampleDTO exampleLibSaveDTO, Example create) {
        String filePath = exampleLibSaveDTO.getFilePath();
        if (StringUtils.isNotBlank(filePath)) {
            Optional<Example> exampleOpt = lambdaQuery().select(Example::getTransformStatus)
                .eq(Example::getId, create.getId()).oneOpt();
            // 如果路径不是临时路径则不进行转码,因为正式路径文件会转码失败
            if (exampleOpt.isPresent() && !filePath.contains("tempFile")) {
                return;
            }

            if (StringUtils.isNotBlank(create.getId())) {
                fileFeign.deleteFileByBizIdAndBizType(create.getId(), FileBizType.EXAMPLE_FILE.name(), 1);
            }

            fileFeign.saveSourceFile(create.getId(), FileBizType.EXAMPLE_FILE.name(), exampleLibSaveDTO.getFileName(),
                filePath);
            Integer transformStatus = fileResourceOptComponent.dealWithFileResourceUpload(create.getId(),
                new ExampleFileDTO().setFileName(exampleLibSaveDTO.getFileName())
                    .setFileType(exampleLibSaveDTO.getFileType()).setFilePath(filePath)
                    .setMime(filePath).setMime(exampleLibSaveDTO.getMine()));
            create.setTransformStatus(transformStatus);
            return;
        }
        if (exampleLibSaveDTO.getFlag().equals(1)) {
            throw new BusinessException(ExampleErrorNoEnum.ERR_EXAMPLE_FILE_NULL);
        }
    }

    private Boolean replenishParam(Example create) {
        Boolean newExample = Boolean.FALSE;
        if (StringUtils.isBlank(create.getAuthorId())) {
            create.setAuthorId(UserThreadContext.getUserId());
        }
        UserDTO author = userFeign.getUserById(create.getAuthorId());
        if (StringUtils.isBlank(create.getOrgId())) {
            create.setOrgId(author.getOrgId());
            create.setExampleOrgId(author.getOrgId());
        }
        create.setLevelPath(author.getLevelPath());
        create.setAuthorName(author.getFullName());
        create.setAuthorLoginName(author.getLoginName());
        create.setStatus(0);
        create.setIsAvailable(0);
        create.setPlayTimeStr(String.valueOf(create.getPlayTime()));
        Optional.ofNullable(create.getPlayTime())
            .ifPresent(playTime -> create.setPlayTime(playTime.multiply(new BigDecimal(60))));

        if (StringUtils.isBlank(create.getId())) {
            create.setId(newId());
            newExample = Boolean.TRUE;
        } else {
            //业务校验
            this.businessCheck(create.getId());
        }
        if (Boolean.TRUE.equals(newExample)) {
            create.setExampleCode(StringUtil.getExampleCode());
        }
        ExampleConfig exampleConfig = exampleConfigService.getOne(new LambdaQueryWrapper<>());
        Optional.ofNullable(exampleConfig).ifPresent(e -> {
            if (e.getSuperior() == 3) {
                create.setStatus(3);
                create.setPublishTime(new Date());
                if (e.getExpertReview() == 1) {
                    create.setIsLib(1);
                }
            }
        });
        return newExample;
    }

    private void businessCheck(String id) {
        Example example = getById(id);
        if (Optional.ofNullable(example).isEmpty()) {
            throw new BusinessException(ExampleErrorNoEnum.ERR_EXAMPLE_NULL);
        }
        if ((StringUtils.isBlank(example.getCreateBy()) || !UserThreadContext.getUserId().equals(example.getCreateBy()))
            && (!UserThreadContext.getUserId().equals(example.getAuthorId()))) {
            // 不是创建者，也不是作者
            throw new BusinessException(BaseErrorNoEnum.ERR_NO_POWER);
        }
    }

    private void replenishField(ExampleLibDTO exampleLibDTO) {
        Map<String, Integer> commentMap = commentFeign.getDiscussCount(List.of(exampleLibDTO.getId()),
            CommentTypeEnum.EXAMPLE);
        exampleLibDTO.setCommentCount(Optional.ofNullable(commentMap.get(exampleLibDTO.getId())).orElse(0));
        if (exampleLibDTO.getStatus() == 5) {
            exampleLibDTO.setAuthStatus(I18nUtil.getDefaultMessage(exampleLibDTO.getLevelName() + "认证"));
        } else {
            exampleLibDTO.setAuthStatus(I18nUtil.getDefaultMessage("未认证"));
        }
        if (exampleLibDTO.getTransformStatus().equals(TranscodeStatusEnum.TRANSFORMED.value)) {
            exampleLibDTO.setFileUrl(fileFeign.getFileUrl(exampleLibDTO.getId(), FileBizType.EXAMPLE_FILE.name()));
        }
        exampleLibDTO.setSourceFileUrl(
            fileFeign.getSourceFileUrl(exampleLibDTO.getId(), FileBizType.EXAMPLE_FILE.name()));
    }

    @Override
    @Async
    public void exportExample(ExampleLibQuery exampleLibQuery) {
        // 构建并实现接口类的方法 返回导出的接口类对象
        IExportDataDTO exportDataDTO = buildExportExampleDataDTO(exampleLibQuery);
        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public PageInfo<ExampleHomePageDTO> homePageList(ExampleHomePageQuery exampleHomePageQuery) {
        // 指定分类查时，要包含所有子分类
        if (StringUtils.isNotBlank(exampleHomePageQuery.getCategoryId())) {
            Set<String> categoryIds = categorysService.getCurAndSubCategoryIds(exampleHomePageQuery.getCategoryId(),
                "ExampleCate");
            categoryIds.add(exampleHomePageQuery.getCategoryId());
            exampleHomePageQuery.setCategoryIds(categoryIds);
        }
        exampleHomePageQuery.setCurrentUserId(UserThreadContext.getUserId());
        exampleHomePageQuery.setCurrentOrgId(UserThreadContext.getOrgId());
        checkRule(exampleHomePageQuery);
        PageInfo<ExampleHomePageDTO> pageInfo = PageMethod.startPage(exampleHomePageQuery.getPageNo(),
                exampleHomePageQuery.getPageSize(), false)
            .doSelectPageInfo(() -> baseMapper.homePageList(exampleHomePageQuery));
        Set<String> idList = pageInfo.getList().stream().map(ExampleHomePageDTO::getId).filter(StringUtils::isNotBlank)
            .collect(Collectors.toSet());
        Map<String, String> imgMap = fileFeign.getImageUrlsByIds(idList, ImageBizType.EXAMPLE_IMAGE.name());
        pageInfo.getList().forEach(dto -> Optional.ofNullable(imgMap.get(dto.getId())).ifPresent(dto::setImage));
        return pageInfo;
    }

    private void checkRule(ExampleHomePageQuery exampleHomePageQuery) {
        String contentRule = exampleHomePageQuery.getContentRule();
        // 根据配置规则添加条件查询
        if (HeadContentRuleEnum.HOTCASEOFTHEMONTH1.getRuleType().equals(contentRule)) {
            exampleHomePageQuery.setVc(2);
            exampleHomePageQuery.setCurrMonth(2);
        } else if (HeadContentRuleEnum.HOTCASEOFTHEMONTH2.getRuleType().equals(contentRule)) {
            exampleHomePageQuery.setVc(2);
            exampleHomePageQuery.setCurrMonth(1);
            exampleHomePageQuery.setLevel(CaseLevelEnum.EXAMPLESCOPEL2.getType());
        } else if (HeadContentRuleEnum.HOTCASEOFTHEMONTH3.getRuleType().equals(contentRule)) {
            exampleHomePageQuery.setVc(2);
            exampleHomePageQuery.setCurrMonth(1);
            exampleHomePageQuery.setLevel(CaseLevelEnum.EXAMPLESCOPEL3.getType());
        } else if (HeadContentRuleEnum.HOTCASE1.getRuleType().equals(contentRule)) {
            exampleHomePageQuery.setVc(2);
        } else if (HeadContentRuleEnum.HOTCASE2.getRuleType().equals(contentRule)) {
            exampleHomePageQuery.setVc(2);
            exampleHomePageQuery.setLevel(CaseLevelEnum.EXAMPLESCOPEL2.getType());
        } else if (HeadContentRuleEnum.HOTCASE3.getRuleType().equals(contentRule)) {
            exampleHomePageQuery.setVc(2);
            exampleHomePageQuery.setLevel(CaseLevelEnum.EXAMPLESCOPEL3.getType());
        } else if (HeadContentRuleEnum.RECOMMENDCASE1.getRuleType().equals(contentRule)) {
            exampleHomePageQuery.setTagOrderId(1);
        } else if (HeadContentRuleEnum.RECOMMENDCASE2.getRuleType().equals(contentRule)) {
            exampleHomePageQuery.setTagOrderId(2);
        } else if (HeadContentRuleEnum.RECOMMENDCASE3.getRuleType().equals(contentRule)) {
            exampleHomePageQuery.setTagOrderId(3);
        }
    }

    /**
     * 构建并实现接口类的方法 返回导出的接口类对象
     *
     * @param exampleLibQuery
     * @return
     */
    public IExportDataDTO buildExportExampleDataDTO(ExampleLibQuery exampleLibQuery) {
        Locale locale = new Locale(
            StringUtils.isNotBlank(UserThreadContext.getAcceptLanguage()) ? UserThreadContext.getAcceptLanguage()
                : "zh");
        return new IExportDataDTO() {
            @Override
            public List<Map<String, Object>> getData(Integer pageNo, Integer pageSize) {
                IExampleService exampleService = SpringUtil.getBean(EXAMPLE_SERVICE, IExampleService.class);
                exampleLibQuery.setExport(true);
                exampleLibQuery.setPageNo(pageNo);
                exampleLibQuery.setPageSize(pageSize);
                assert exampleService != null;
                if (StringUtils.isNotBlank(UserThreadContext.getAcceptLanguage())) {
                    LocaleContextHolder.setLocale(locale);
                }
                PageInfo<ExampleLibDTO> exampleListByPage = exampleService.list(exampleLibQuery);
                List<Map<String, Object>> exampleListExportDTOS = new ArrayList<>();
                for (ExampleLibDTO exampleListDTO : exampleListByPage.getList()) {
                    Map<String, Object> map = JsonUtil.parseObjectToMap(exampleListDTO);
                    exampleListExportDTOS.add(map);
                }
                return exampleListExportDTOS;
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.ExampleLib;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.ExampleLib.getType();
            }

        };
    }

    @Override
    public void updateExampleInteractNum(String id, String event) {
        switch (event) {
            case ResourceInteractEventRoutingKeyConstants.EXAMPLE_COMMENT_EVENT:
                Map<String, Integer> commentNumMap = commentFeign.getDiscussCount(Collections.singleton(id),
                    CommentTypeEnum.EXAMPLE);
                lambdaUpdate().set(Example::getCommentNum, commentNumMap.get(id)).eq(Example::getId, id).update();
                break;
            case ResourceInteractEventRoutingKeyConstants.EXAMPLE_LEARN_EVENT:
                baseMapper.updateLearnNum(id);
                break;
            case ResourceInteractEventRoutingKeyConstants.EXAMPLE_LIKE_EVENT:
                baseMapper.updateLikeNum(id);
                break;
            case ResourceInteractEventRoutingKeyConstants.EXAMPLE_STAR_EVENT:
                baseMapper.updateStarNum(id);
                break;
            case ResourceInteractEventRoutingKeyConstants.EXAMPLE_AUDIT_EVENT:
                baseMapper.updateAuditScore(id);
                break;
            default:
        }
        exampleCashService.removeExampleDetailCache(id);
    }

    @Override
    public List<ExampleDTO> getExampleByUserId(String userId) {
        return baseMapper.getExampleByUserId(userId);
    }

    @Override
    public ExampleDTO getActualExampleById(String id) {
        return baseMapper.getActualExampleById(id);
    }

    @Override
    public List<ExampleDTO> getExampleByExampleIdList(Collection<String> batchIds) {
        return baseMapper.getExampleByExampleIdList(batchIds);
    }

    @Override
    public int checkRef(List<String> ids) {
        return baseMapper.checkRef(ids);
    }

    @Override
    public void reTranscoding(ExampleReTranscodingDTO exampleReTranscodingDTO) {
        // 1、 根据参数筛选是否有需要转码
        List<Example> exampleDTOList = baseMapper.selectByIds(exampleReTranscodingDTO.getIds());
        if (CollectionUtils.isEmpty(exampleDTOList)) {
            return;
        }
        List<String> targetExampleIds = new ArrayList<>();
        List<Example> needReTranscodingExampleList = new ArrayList<>();
        for (Example example : exampleDTOList) {
            // 无需转码的类型则跳过
            if (!FileResourceEnum.isNeedTransform(example.getFileType())) {
                continue;
            }
            // 设置此次修改数据的字段（时间、状态、修改人 信息）
            example.setTransformStatus(TranscodeStatusEnum.PENDING.value);
            example.setUpdateBy(UserThreadContext.getUserId());
            example.setUpdateTime(now());
            needReTranscodingExampleList.add(example);
            targetExampleIds.add(example.getId());
        }
        if (needReTranscodingExampleList.isEmpty()) {
            return;
        }

        // 2、更新为转码中状态，根据id批量更新转码状态为转码中
        for (Example ele : needReTranscodingExampleList) {
            // 记录操作日志
            exampleDao.updateExample(ele);
        }

        // 3、调用转码操作
        fileFeign.resetTransFile(targetExampleIds, FileBizType.EXAMPLE_FILE.name());
        for (Example ele : needReTranscodingExampleList) {
            fileResourceOptComponent.reTranscoding(ele.getId(), FileBizType.EXAMPLE_FILE.name(),
                ele.getMine(), ele.getFileType(), null);
        }
    }
}
