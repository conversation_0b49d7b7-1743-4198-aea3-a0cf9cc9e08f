package com.wunding.learn.example.service.feign;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wunding.learn.example.api.dto.ExampleDTO;
import com.wunding.learn.example.api.service.ExampleFeign;
import com.wunding.learn.example.service.model.Example;
import com.wunding.learn.example.service.model.ExampleRecord;
import com.wunding.learn.example.service.service.IExampleRecordService;
import com.wunding.learn.example.service.service.IExampleService;
import jakarta.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @create 2022/5/11 16:14
 * @description
 */
@RestController
@RequestMapping("${module.example.contentPath:/}")
public class ExampleFeignImpl implements ExampleFeign {

    @Resource
    private IExampleService exampleService;

    @Resource
    private IExampleRecordService exampleRecordService;

    @Override
    public List<ExampleDTO> getExampleByUserId(String userId) {
        return exampleService.getExampleByUserId(userId);
    }

    @Override
    public List<String> getEffectiveExampleIds() {
        LambdaQueryWrapper<Example> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Example::getId)
            .eq(Example::getIsDel, 0);
        List<Example> exampleList = exampleService.getBaseMapper().selectList(queryWrapper);
        return exampleList.stream().map(Example::getId).collect(Collectors.toList());
    }

    @Override
    public ExampleDTO getExampleById(String exampleId) {
        return exampleService.getActualExampleById(exampleId);
    }

    @Override
    public List<ExampleDTO> getExampleByExampleIdList(Collection<String> batchIds) {
        return exampleService.getExampleByExampleIdList(batchIds);
    }

    @Override
    public Map<String, Long> countLearnedUserCountByExampleIds(Collection<String> exampleIds) {
        if (CollectionUtils.isEmpty(exampleIds)) {
            return Map.of();
        }
        LambdaQueryWrapper<ExampleRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(ExampleRecord::getId, ExampleRecord::getExampleId);
        queryWrapper.in(ExampleRecord::getExampleId, exampleIds);
        List<ExampleRecord> list = exampleRecordService.list(queryWrapper);
        return list.stream().collect(Collectors.groupingBy(ExampleRecord::getId,
            Collectors.mapping(ExampleRecord::getId, Collectors.counting())));
    }
}
