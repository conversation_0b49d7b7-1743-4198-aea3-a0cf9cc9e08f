package com.wunding.learn.train.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: RemindersDTO
 * @projectName: mlearn
 * @description：
 * @date 2022/7/12 11:02
 */
@Data
@Schema(name = "TrainRemindersDTO", description = "培训项目催办用户返回对象")
public class TrainRemindersDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "账号名称")
    private String loginName;

    @Schema(description = "用户名")
    private String fullName;

    @Schema(description = "部门名")
    private String orgName;

    @Schema(description = "项目参加情况 0-未参加 1-已参加")
    private Integer state;

    @Schema(description = "部门全称 orgPath")
    private String orgPath;

}
