package com.wunding.learn.train.service.client.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/01/07
 */
@Data
@Schema(name = "LearningTargetClientDTO", description = "用户涉及的目标管理列表dto")
public class LearningTargetClientDTO {

    @Schema(description = "目标id")
    private String targetId;

    @Schema(description = "组织部门名称")
    private String orgName;

    @Schema(description = "开始时间")
    private Date startTime;

    @Schema(description = "结束时间")
    private Date endTime;

    @Schema(description = "目标值")
    private Integer targetValue;

    @Schema(description = "学习类型, exam-考试，course-课程，project-学习项目，faceProject-面授培训")
    private List<String> activeType;

    @Schema(description = "目标完成进度")
    private Integer process;

}
