package com.wunding.learn.train.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 学习活动对比列表DTO
 *
 * <AUTHOR>
 * @date 2024/3/21
 */
@Data
@Schema(name = "LearnMapActivityContrastListDTO", description = "学习活动对比列表")
public class LearnMapActivityContrastListDTO {

    @Schema(description = "id")
    private String id;

    @Schema(description = "活动来源 0-自建  1-能力")
    private Integer sourceType;

    @Schema(description = "资源类型")
    private String resourceType;

    @Schema(description = "资源类型名称")
    private String resourceTypeName;

    @Schema(description = "是否必修 0否 1是")
    private Integer isCompulsory;

    @Schema(description = "是否不同 0否 1是")
    private Integer isUnlike;

    @Schema(description = "活动资源编号")
    private String resourceNo;

    @Schema(description = "活动资源id",hidden = true)
    private String resourceId;

    @Schema(description = "活动资源名称")
    private String resourceName;

    @Schema(description = "对比差异类型 1-新增(被对比版本有,对比版本中没有的) 2-缺失(被对比版本没有,对比版本中有)")
    private Integer contrastType;


}
