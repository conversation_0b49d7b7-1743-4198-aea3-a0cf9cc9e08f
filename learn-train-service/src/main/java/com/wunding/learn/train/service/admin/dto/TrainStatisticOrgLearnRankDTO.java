package com.wunding.learn.train.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 部门学习排名统计
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">wgr</a>
 * @date 2022/10/31 11:26
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(name = "TrainStatisticOrgLearnRankDTO", description = "部门学习排名统计")
public class TrainStatisticOrgLearnRankDTO implements Serializable {

    /**
     * 排名
     */
    @Schema(description = "排名")
    private Integer rank;

    /**
     * 组织ID
     */
    @Schema(description = "组织ID", hidden = true)
    private String orgId;

    /**
     * 团队名称
     */
    @Schema(description = "团队名称")
    private String orgName;

    /**
     * 任务分
     */
    @Schema(description = "任务分")
    private Integer score;

    @Schema(description = "部门全称 orgPath")
    private String orgPath;
}
