package com.wunding.learn.train.service.admin.query;

import com.wunding.learn.common.bean.BasePageQuery;
import io.swagger.v3.oas.annotations.Parameter;
import java.io.Serializable;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <p>
 * 个人学习排名
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">wgr</a>
 * @date 2022/9/27 14:07
 */
@Data
public class StatisticPersonRankQuery extends BasePageQuery implements Serializable {

    private static final long serialVersionUID = -2219539143825976079L;


    @Parameter(description = "培训项目ID")
    @NotBlank(message = "培训项目ID不可为空")
    private String trainId;


    @Parameter(description = "用户ID")
    private String userId;

    @Parameter(description = "机构ID")
    private String orgId;
}
