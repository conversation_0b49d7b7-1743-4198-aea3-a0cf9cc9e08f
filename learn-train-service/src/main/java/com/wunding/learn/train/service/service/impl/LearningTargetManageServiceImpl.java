package com.wunding.learn.train.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.wunding.learn.common.constant.dynamicDataSource.DynamicDataSourceConstant;
import com.wunding.learn.common.constant.tenant.TenantRedisKeyConstant;
import com.wunding.learn.common.constant.train.TrainErrorNoEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.dto.MyStatisticListDTO;
import com.wunding.learn.common.dto.UserTargetValueProcessClientDTO;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.i18n.util.I18nUtil;
import com.wunding.learn.common.mybatis.service.impl.BaseServiceImpl;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.date.DateUtil;
import com.wunding.learn.excitation.api.dto.TargetValueGottenDetailClientDTO;
import com.wunding.learn.excitation.api.dto.UserGottenTargetValueDetailDTO;
import com.wunding.learn.excitation.api.query.UserTargetValueQuery;
import com.wunding.learn.excitation.api.service.ExcitationFeign;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.dto.AbstractExportDataDTO;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.train.service.admin.dto.*;
import com.wunding.learn.train.service.admin.query.LearningTargetManageQuery;
import com.wunding.learn.train.service.admin.query.LearningTargetUserListQuery;
import com.wunding.learn.train.service.admin.query.TargetUserAchievementQuery;
import com.wunding.learn.train.service.client.dto.DifferentTypeLearningTargetListDTO;
import com.wunding.learn.train.service.client.dto.LearningTargetClientDTO;
import com.wunding.learn.train.service.client.dto.UserCardInfoDTO;
import com.wunding.learn.train.service.client.query.TargetValueGottenDetailClientQuery;
import com.wunding.learn.train.service.client.query.UserCardInfoQuery;
import com.wunding.learn.train.service.enums.TargetRefActiveTypeEnum;
import com.wunding.learn.train.service.mapper.LearningTargetManageMapper;
import com.wunding.learn.train.service.model.LearningTargetManage;
import com.wunding.learn.train.service.model.StatUserTargetValue;
import com.wunding.learn.train.service.model.TargetRefActive;
import com.wunding.learn.train.service.service.ILearningTargetManageService;
import com.wunding.learn.train.service.service.IStatUserTargetValueService;
import com.wunding.learn.train.service.service.ITargetRefActiveService;
import com.wunding.learn.user.api.dto.LearningTargetManageUserProcessDTO;
import com.wunding.learn.user.api.dto.OrgDTO;
import com.wunding.learn.user.api.dto.OrgShowDTO;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.query.TargetUserProcessQuery;
import com.wunding.learn.user.api.service.IdentityFeign;
import com.wunding.learn.user.api.service.OrgFeign;
import com.wunding.learn.user.api.service.UserFeign;
import com.xxl.job.core.handler.annotation.XxlJob;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;
import org.springframework.util.ObjectUtils;

import static com.wunding.learn.common.util.string.StringUtil.newId;

/**
 * 学习目标管理实现类
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
@Slf4j
@Service("learningTargetManageService")
public class LearningTargetManageServiceImpl extends BaseServiceImpl<LearningTargetManageMapper, LearningTargetManage> implements ILearningTargetManageService {

    private static final String BEAN_NAME = "learningTargetManageService";

    @Resource
    private ITargetRefActiveService targetRefActiveService;

    @Resource
    private ExportComponent exportComponent;

    @Resource
    private OrgFeign orgFeign;

    @Resource
    private IdentityFeign identityFeign;

    @Resource
    private UserFeign userFeign;

    @Resource
    private ExcitationFeign excitationFeign;

    @Resource
    private IStatUserTargetValueService statUserTargetValueService;

    @Resource(name = "redisTemplate")
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public void insertLearningTargetManage(LearningTargetManageDTO dto) {
        List<String> activeList = dto.getActiveList();
        if (!validateActiveList(activeList)) {
            throw new BusinessException(TrainErrorNoEnum.EXIST_ERROR_ACTIVE_TYPE);
        }
        // 目标管理
        LearningTargetManage learningTargetManage = new LearningTargetManage();
        String id = newId();
        dto.setId(id);
        BeanUtils.copyProperties(dto, learningTargetManage);
        save(learningTargetManage);

        // 目标与活动类型对应关系 1->n
        List<TargetRefActive> list = new ArrayList<>();
        activeList.forEach(activeType -> {
            TargetRefActive targetRefActive = new TargetRefActive();
            targetRefActive.setId(newId());
            targetRefActive.setTargetId(id);
            targetRefActive.setActiveType(activeType);
            list.add(targetRefActive);
        });
        targetRefActiveService.saveBatch(list);
    }

    @Override
    public void updateLearningTargetManage(LearningTargetManageDTO dto) {
        List<String> activeList = dto.getActiveList();
        if (!validateActiveList(activeList)) {
            throw new BusinessException(TrainErrorNoEnum.EXIST_ERROR_ACTIVE_TYPE);
        }
        String targetId = dto.getId();
        LearningTargetManage oldData = getById(targetId);
        if (oldData == null) {
            return;
        }
        Long count = statUserTargetValueService.lambdaQuery().eq(StatUserTargetValue::getTargetId, targetId).count();
        // 如果存在统计数据，时间范围只能扩大，不能缩小
        if (count > 0 && (oldData.getEndTime().compareTo(dto.getEndTime()) > 0 || dto.getStartTime().compareTo(oldData.getStartTime()) > 0)) {
            throw new BusinessException(TrainErrorNoEnum.ERROR_TARGET_TIME_RANGE);
        }
        // 目标管理
        LearningTargetManage learningTargetManage = new LearningTargetManage();
        BeanUtils.copyProperties(dto, learningTargetManage);
        updateById(learningTargetManage);

        // 目标与活动类型对应关系 1->n
        targetRefActiveService.remove(new LambdaQueryWrapper<TargetRefActive>().eq(TargetRefActive::getTargetId, targetId));
        List<TargetRefActive> list = new ArrayList<>();
        activeList.forEach(activeType -> {
            TargetRefActive targetRefActive = new TargetRefActive();
            targetRefActive.setId(newId());
            targetRefActive.setTargetId(targetId);
            targetRefActive.setActiveType(activeType);
            list.add(targetRefActive);
        });
        targetRefActiveService.saveBatch(list);

    }

    @Override
    public void deleteLearningTargetManage(String ids) {
        List<String> idList = Arrays.asList(ids.split(","));
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        Long count = lambdaQuery().eq(LearningTargetManage::getIsPublish, 1).in(LearningTargetManage::getId, idList).count();
        if (count > 0) {
            throw new BusinessException(TrainErrorNoEnum.CAN_NOT_DELETE_HAS_BEEN_PUBLISHED_DATA);
        }
        // 删除关联
        targetRefActiveService.remove(new LambdaQueryWrapper<TargetRefActive>().in(TargetRefActive::getTargetId, idList));
        // 删除统计
        statUserTargetValueService.remove(new LambdaQueryWrapper<StatUserTargetValue>().in(StatUserTargetValue::getTargetId, idList));
        // 删自己
        removeBatchByIds2(idList);
    }

    @Override
    public void publishLearningTargetManage(String ids, Integer isPublish) {
        List<String> idList = Arrays.asList(ids.split(","));
        lambdaUpdate().set(LearningTargetManage::getIsPublish, isPublish).in(LearningTargetManage::getId, idList).update();
    }

    @Override
    public LearningTargetManageDetailDTO getLearningTargetManageDetail(String id) {
        LearningTargetManageDetailDTO result = new LearningTargetManageDetailDTO();
        LearningTargetManage byId = getById(id);
        if (byId == null) {
            return result;
        }
        BeanUtils.copyProperties(byId, result);

        List<String> activeTypeList = targetRefActiveService.getActiveTypeByTargetId(id);
        result.setActiveList(activeTypeList);

        if (byId.getTargetUserType() == 0) {
            result.setTargetUserTypeName(orgFeign.getById(byId.getTargetUserTypeId()).getOrgName());
        } else {
            result.setTargetUserTypeName(identityFeign.getNameByIdentityId(byId.getTargetUserTypeId()));
        }
        result.setOrgName(orgFeign.getById(byId.getOrgId()).getOrgName());
        Long count = statUserTargetValueService.lambdaQuery().eq(StatUserTargetValue::getTargetId, id).count();
        result.setCanLimitTime(count <= 0);
        return result;
    }

    @Override
    public PageInfo<LearningTargetManageListDTO> queryLearningTargetManageList(LearningTargetManageQuery query) {
        PageInfo<LearningTargetManageListDTO> pageInfo = PageMethod.startPage(query.getPageNo(), query.getPageSize())
                .doSelectPageInfo(() -> baseMapper.queryLearningTargetManageList(query));
        List<LearningTargetManageListDTO> list = pageInfo.getList();
        if (CollectionUtils.isEmpty(list)) {
            return pageInfo;
        }
        Set<String> orgIdSet = list.stream().map(LearningTargetManageListDTO::getOrgId).collect(Collectors.toSet());
        Map<String, String> orgNameMap = orgFeign.getOrgNameMapByIds(orgIdSet);

        Set<String> targetUserTypeIdSet = list.stream().map(LearningTargetManageListDTO::getTargetUserTypeId).collect(Collectors.toSet());
        Map<String, OrgShowDTO> targetTypeOrgNameMap  = new HashMap<>();
        Map<String, String> targetTypeIdentityNameMap = new HashMap<>();
        if (query.getTargetUserType() == 0) {
            targetTypeOrgNameMap = orgFeign.getOrgShowDTO(targetUserTypeIdSet);
        } else {
            targetTypeIdentityNameMap = identityFeign.getNameByIdentityIds(targetUserTypeIdSet, "");
        }

        Set<String> userIdSet = list.stream().map(LearningTargetManageListDTO::getCreateBy).collect(Collectors.toSet());
        Map<String, UserDTO> simpleUserMap = userFeign.getSimpleUserMapIgnoreDel(userIdSet);
        for (LearningTargetManageListDTO dto : list) {
            String activeType = dto.getActiveType();
            if (StringUtils.isNotEmpty(activeType)) {
                // 转成中文的返回。那么数据库为什么不直接存中文呢，因为要根据它去查激励的，激励事件类型存的英文。
                dto.setActiveType(convertToChinese(activeType));
            }
            // 组织部门名称
            String orgId = dto.getOrgId();
            dto.setOrgName(Optional.ofNullable(orgNameMap.get(orgId)).orElse(""));
            // 目标人群类别的名称：部门名称或者身份名称
            String targetUserTypeId = dto.getTargetUserTypeId();
            if (query.getTargetUserType() == 0) {
                OrgShowDTO orgShowDTO = Optional.ofNullable(targetTypeOrgNameMap.get(targetUserTypeId)).orElse(new OrgShowDTO());
                dto.setTargetUserTypeName(orgShowDTO.getOrgShortName());
            } else {
                dto.setTargetUserTypeName(Optional.ofNullable(targetTypeIdentityNameMap.get(targetUserTypeId)).orElse(""));
            }
            // 添加人的信息
            String userId = dto.getCreateBy();
            UserDTO userInfo = simpleUserMap.get(userId);
            if (userInfo != null) {
                dto.setCreateBy(userInfo.getFullName());
                dto.setLoginName(userInfo.getIsDel() == 0 ? userInfo.getLoginName() : userInfo.getLoginName().replaceAll(">.*", "(已删除)"));
                dto.setCreatorOrg(userInfo.getOrgName());
            }
        }
        return pageInfo;
    }

    @Async("exportTaskThreadPool")
    @Override
    public void exportLearningTargetManageList(LearningTargetManageQuery query) {
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<ILearningTargetManageService, LearningTargetManageListDTO>(query) {

            @Override
            protected ILearningTargetManageService getBean() {
                return SpringUtil.getBean(BEAN_NAME, ILearningTargetManageService.class);
            }

            @Override
            protected PageInfo<LearningTargetManageListDTO> getPageInfo() {
                return getBean().queryLearningTargetManageList((LearningTargetManageQuery) queryDTO);

            }

            @Override
            public ExportBizType getType() {
                if (query.getTargetUserType() == 0) {
                    return ExportBizType.LEARNING_TARGET_MANAGE_ORG;
                } else {
                    return ExportBizType.LEARNING_TARGET_MANAGE_IDENTITY;
                }
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.LEARNING_TARGET_MANAGE.getType();
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public PageInfo<LearningTargetManageUserProcessDTO> queryTargetUserProcessList(TargetUserProcessQuery query) {
        LearningTargetManage learningTargetManage = getById(query.getTargetId());
        if (learningTargetManage == null) {
            return new PageInfo<>();
        }
        PageInfo<LearningTargetManageUserProcessDTO> pageInfo = PageMethod.startPage(query.getPageNo(), query.getPageSize())
                .doSelectPageInfo(() -> statUserTargetValueService.queryTargetUserProcessList(query));
        List<LearningTargetManageUserProcessDTO> list = pageInfo.getList();
        if (CollectionUtils.isEmpty(list)) {
            return pageInfo;
        }
        Set<String> orgIdSet = list.stream().map(LearningTargetManageUserProcessDTO::getOrgId).collect(Collectors.toSet());
        Map<String, OrgShowDTO> orgNameMap = orgFeign.getOrgShowDTO(orgIdSet);
        list.forEach(result -> {
            // 组织部门名称
            String orgId = result.getOrgId();
            OrgShowDTO orgShowDTO = Optional.ofNullable(orgNameMap.get(orgId)).orElse(new OrgShowDTO());
            result.setOrgName(orgShowDTO.getOrgShortName());
        });
        return pageInfo;
    }

    @Async("exportTaskThreadPool")
    @Override
    public void exportTargetUserProcessList(TargetUserProcessQuery query) {
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<ILearningTargetManageService, LearningTargetManageUserProcessDTO>(query) {

            @Override
            protected ILearningTargetManageService getBean() {
                return SpringUtil.getBean(BEAN_NAME, ILearningTargetManageService.class);
            }

            @Override
            protected PageInfo<LearningTargetManageUserProcessDTO> getPageInfo() {
                return getBean().queryTargetUserProcessList((TargetUserProcessQuery) queryDTO);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.LEARNING_TARGET_MANAGE_USER_PROCESS;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.LEARNING_TARGET_MANAGE_USER_PROCESS.getType();
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public TargetDistributionChartDTO distributionChart(String id) {
        TargetDistributionChartDTO result = new TargetDistributionChartDTO();
        TargetUserProcessQuery query = new TargetUserProcessQuery();
        query.setTargetId(id);
        query.setPageNo(1);
        query.setPageSize(Integer.MAX_VALUE);
        query.setProcessBegin(0.0);
        query.setProcessEnd(100.0);
        List<LearningTargetManageUserProcessDTO> list = statUserTargetValueService.queryTargetUserProcessList(query);
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        for (LearningTargetManageUserProcessDTO dto : list) {
            Double process = dto.getProcess();
            getResult(process, result);
        }
        return result;
    }

    private static void getResult(Double process, TargetDistributionChartDTO result) {
        if (process != null) {
            if (process == 100) {
                result.setHundredPercent(result.getHundredPercent() + 1);
            } else if (process >= 80 && process < 100) {
                result.setEightyToHundredPercent(result.getEightyToHundredPercent() + 1);
            } else if (process >= 50 && process < 80) {
                result.setFiftyToEightyPercent(result.getFiftyToEightyPercent() + 1);
            } else if (process >= 30 && process < 50) {
                result.setThirtyToFiftyPercent(result.getThirtyToFiftyPercent() + 1);
            } else if (process >= 10 && process < 30) {
                result.setTenToThirtyPercent(result.getTenToThirtyPercent() + 1);
            } else if (process >= 0 && process < 10) {
                result.setZeroToTenPercent(result.getZeroToTenPercent() + 1);
            }
        }
    }

    @Override
    public DifferentTypeLearningTargetListDTO queryUserLearningTargetListByType(String type) {
        DifferentTypeLearningTargetListDTO result = new DifferentTypeLearningTargetListDTO();
        String userId = UserThreadContext.getUserId();
        List<String> identityIdList = userFeign.getIdentityListByUserId(userId);
        OrgDTO org = orgFeign.getOrgByUserId(userId);
        List<String> orgIdList = Arrays.stream(org.getLevelPath().split("/")).toList();
        List<LearningTargetManage> list = baseMapper.getCurentUserRelateLearningTargetList(identityIdList, orgIdList, type);
        List<String> targetIdList = list.stream().map(LearningTargetManage::getId).toList();
        Map<String, List<String>> activeTypeMap = targetRefActiveService.getTargetIdToActiveTypeMap(targetIdList);
        Set<String> orgIdSet = list.stream().map(LearningTargetManage::getOrgId).collect(Collectors.toSet());
        Map<String, String> orgNameMap = orgFeign.getOrgNameMapByIds(orgIdSet);


        List<LearningTargetClientDTO> targetList = new ArrayList<>();
        int obtainValue = 0;
        for (LearningTargetManage a : list) {
            LearningTargetClientDTO dto = new LearningTargetClientDTO();
            dto.setTargetId(a.getId());
            dto.setActiveType(activeTypeMap.get(a.getId()));
            dto.setTargetValue(a.getTargetValue());
            dto.setStartTime(a.getStartTime());
            dto.setEndTime(a.getEndTime());
            dto.setOrgName(orgNameMap.get(a.getOrgId()));

            UserTargetValueQuery userTargetValueQuery = new UserTargetValueQuery();
            userTargetValueQuery.setUserIdSet(Set.of(userId));
            userTargetValueQuery.setActiveTypeList(activeTypeMap.get(a.getId()));
            userTargetValueQuery.setTargetValueType(a.getTargetValueType());
            userTargetValueQuery.setStartTime(a.getStartTime());
            userTargetValueQuery.setEndTime(a.getEndTime());
            Map<String, Integer> userGottenTargetValueMap = excitationFeign.getUserHasBeenGotTargetValue(userTargetValueQuery);
            Integer gottenTargetValue = userGottenTargetValueMap.getOrDefault(userId, 0);
            double process = (gottenTargetValue > a.getTargetValue()) ? 100 : (double) gottenTargetValue / a.getTargetValue() * 100;
            dto.setProcess((int) Math.round(process));
            targetList.add(dto);
            obtainValue = obtainValue + gottenTargetValue;
        }
        result.setList(targetList);

        Map<String, Integer> totalTargetValueMap = calculateTargetValueSums(list);
        result.setTotalValue(totalTargetValueMap.getOrDefault(type, 0));
        result.setObtainValue(obtainValue);
        result.setTotalProcess((int) Math.round((obtainValue > result.getTotalValue()) ? 100 : (double) obtainValue / result.getTotalValue() * 100));
        return result;
    }

    @Override
    public PageInfo<TargetValueGottenDetailClientDTO> getTargetValueGottenDetails(TargetValueGottenDetailClientQuery query) {
        String userId = UserThreadContext.getUserId();
        String targetId = query.getTargetId();
        LearningTargetManage byId = getById(targetId);
        if (byId == null) {
            return new PageInfo<>();
        }
        List<String> activeTypeList = targetRefActiveService.getActiveTypeByTargetId(targetId);
        UserTargetValueQuery feignQuery = new UserTargetValueQuery();
        feignQuery.setActiveTypeList(activeTypeList);
        feignQuery.setTargetValueType(byId.getTargetValueType());
        feignQuery.setUserId(userId);
        feignQuery.setPageNo(query.getPageNo());
        feignQuery.setPageSize(query.getPageSize());
        // 时间取交集
        feignQuery.setStartTime(DateUtil.getDateByComparison(query.getStartTime(), byId.getStartTime(), true));
        feignQuery.setEndTime(DateUtil.getDateByComparison(query.getEndTime(), byId.getEndTime(), false));
        PageInfo<TargetValueGottenDetailClientDTO> pageInfo = excitationFeign.queryUserGottenTargetValueDetailClientList(feignQuery);

        return pageInfo;
    }

    @Override
    public PageInfo<LearningTargetCompletionStatisticListDTO> getLearningTargetCompletionDetailsList(LearningTargetManageQuery query) {
        PageInfo<LearningTargetCompletionStatisticListDTO> pageInfo = PageMethod.startPage(query.getPageNo(), query.getPageSize())
                .doSelectPageInfo(() -> baseMapper.getLearningTargetCompletionDetailsList(query));
        List<LearningTargetCompletionStatisticListDTO> list = pageInfo.getList();
        if (CollectionUtils.isEmpty(list)) {
            return pageInfo;
        }
        Set<String> orgIdSet = list.stream().map(LearningTargetCompletionStatisticListDTO::getOrgId).collect(Collectors.toSet());
        Map<String, String> orgNameMap = orgFeign.getOrgNameMapByIds(orgIdSet);

        Set<String> targetUserTypeIdSet = list.stream().map(LearningTargetCompletionStatisticListDTO::getTargetUserTypeId).collect(Collectors.toSet());
        Map<String, OrgShowDTO> targetTypeOrgNameMap  = new HashMap<>();
        Map<String, String> targetTypeIdentityNameMap = new HashMap<>();
        if (query.getTargetUserType() == 0) {
            targetTypeOrgNameMap = orgFeign.getOrgShowDTO(targetUserTypeIdSet);
        } else {
            targetTypeIdentityNameMap = identityFeign.getNameByIdentityIds(targetUserTypeIdSet, "");
        }

        Set<String> targetIdSet = list.stream().map(LearningTargetCompletionStatisticListDTO::getTargetId).collect(Collectors.toSet());
        Map<String, TargetRefUserNumDTO> map = statUserTargetValueService.countTargetUserNumWithFinishOrNot(targetIdSet);
        for (LearningTargetCompletionStatisticListDTO result : list) {
            String activeType = result.getActiveType();
            if (StringUtils.isNotEmpty(activeType)) {
                // 转成中文的返回。那么数据库为什么不直接存中文呢，因为要根据它去查激励的，激励事件类型存的英文。
                result.setActiveType(convertToChinese(activeType));
            }
            // 组织部门名称
            String orgId = result.getOrgId();
            result.setOrgName(Optional.ofNullable(orgNameMap.get(orgId)).orElse(""));
            // 目标人群类别的名称：部门名称或者身份名称
            String targetUserTypeId = result.getTargetUserTypeId();
            if (query.getTargetUserType() == 0) {
                OrgShowDTO orgShowDTO = Optional.ofNullable(targetTypeOrgNameMap.get(targetUserTypeId)).orElse(new OrgShowDTO());
                result.setTargetUserTypeName(orgShowDTO.getOrgShortName());
            } else {
                result.setTargetUserTypeName(Optional.ofNullable(targetTypeIdentityNameMap.get(targetUserTypeId)).orElse(""));
            }


            String targetId = result.getTargetId();
            TargetRefUserNumDTO dto = map.getOrDefault(targetId, new TargetRefUserNumDTO());
            result.setTargetUserNum(dto.getTargetUserNum());
            result.setFinishUserNum(dto.getFinishUserNum());
            result.setUnFinishUserNum(dto.getUnFinishUserNum());
            result.setFinishingRate(String.format("%d%%", Math.round(((dto.getTargetUserNum() == 0) ?
                    0 : (double) dto.getFinishUserNum() / dto.getTargetUserNum() * 100))));
        }
        return pageInfo;
    }

    @Async("exportTaskThreadPool")
    @Override
    public void exportLearningTargetCompletionDetailsList(LearningTargetManageQuery query) {
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<ILearningTargetManageService, LearningTargetCompletionStatisticListDTO>(query) {

            @Override
            protected ILearningTargetManageService getBean() {
                return SpringUtil.getBean(BEAN_NAME, ILearningTargetManageService.class);
            }

            @Override
            protected PageInfo<LearningTargetCompletionStatisticListDTO> getPageInfo() {
                return getBean().getLearningTargetCompletionDetailsList((LearningTargetManageQuery) queryDTO);
            }

            @Override
            public ExportBizType getType() {
                if (query.getTargetUserType() == 0) {
                    return ExportBizType.LEARNING_TARGET_COMPLETION_DETAILS_ORG;
                }
                return ExportBizType.LEARNING_TARGET_COMPLETION_DETAILS_IDENTITY;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.LEARNING_TARGET_COMPLETION_DETAILS.getType();
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public PageInfo<LearningTargetUserListDTO> getTargetUserListByTargetId(LearningTargetUserListQuery query) {
        PageInfo<LearningTargetUserListDTO> pageInfo = PageMethod.startPage(query.getPageNo(), query.getPageSize())
                .doSelectPageInfo(() -> statUserTargetValueService.getTargetUserListByTargetId(query));
        List<LearningTargetUserListDTO> list = pageInfo.getList();
        if (CollectionUtils.isEmpty(list)) {
            return pageInfo;
        }
        Set<String> orgIdSet = list.stream().map(LearningTargetUserListDTO::getOrgId).collect(Collectors.toSet());
        Map<String, OrgShowDTO> orgNameMap = orgFeign.getOrgShowDTO(orgIdSet);
        list.forEach(result -> {
            // 组织部门名称
            String orgId = result.getOrgId();
            OrgShowDTO orgShowDTO = Optional.ofNullable(orgNameMap.get(orgId)).orElse(new OrgShowDTO());
            result.setOrgName(orgShowDTO.getOrgShortName());
            result.setOrgPath(orgShowDTO.getLevelPathName());
        });
        return pageInfo;
    }

    @Async("exportTaskThreadPool")
    @Override
    public void exportTargetUserListByTargetId(LearningTargetUserListQuery query) {
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<ILearningTargetManageService, LearningTargetUserListDTO>(query) {

            @Override
            protected ILearningTargetManageService getBean() {
                return SpringUtil.getBean(BEAN_NAME, ILearningTargetManageService.class);
            }

            @Override
            protected PageInfo<LearningTargetUserListDTO> getPageInfo() {
                return getBean().getTargetUserListByTargetId((LearningTargetUserListQuery) queryDTO);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.LEARNING_TARGET_REF_USER_LIST;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.LEARNING_TARGET_REF_USER_LIST.getType();
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public PageInfo<TargetUserAchievementDetailListDTO> getTargetUserAchievementDetailList(TargetUserAchievementQuery query) {
        PageInfo<TargetUserAchievementDetailListDTO> pageInfo = PageMethod.startPage(query.getPageNo(), query.getPageSize())
                .doSelectPageInfo(() -> statUserTargetValueService.getTargetUserAchievementDetailList(query));
        List<TargetUserAchievementDetailListDTO> list = pageInfo.getList();
        if (CollectionUtils.isEmpty(list)) {
            return pageInfo;
        }

        Set<String> targetIdSet = list.stream().map(TargetUserAchievementDetailListDTO::getTargetId).collect(Collectors.toSet());
        List<LearningTargetManage> learningTargetManages = listByIds(targetIdSet);
        Map<String, LearningTargetManage> learningTargetManageMap = learningTargetManages.stream().collect(Collectors.toMap(LearningTargetManage::getId, bean -> bean, (a, b) -> a));
        Set<String> orgIdSet = learningTargetManages.stream().map(LearningTargetManage::getOrgId).collect(Collectors.toSet());
        Map<String, String> orgNameMap = orgFeign.getOrgNameMapByIds(orgIdSet);

        Set<String> targetUserTypeOrg = learningTargetManages.stream().filter(dto ->dto.getTargetUserType() == 0).map(LearningTargetManage::getTargetUserTypeId).collect(Collectors.toSet());
        Set<String> targetUserTypeIdentity = learningTargetManages.stream().filter(dto ->dto.getTargetUserType() == 1).map(LearningTargetManage::getTargetUserTypeId).collect(Collectors.toSet());
        Map<String, String> targetTypeNameMap = new HashMap<>();
        targetTypeNameMap.putAll(orgFeign.getOrgNameMapByIds(targetUserTypeOrg));
        targetTypeNameMap.putAll(identityFeign.getNameByIdentityIds(targetUserTypeIdentity, ""));

        List<TargetUserAchievementDetailListDTO> activeTypeList = baseMapper.getActiveTypeByTargetIdSet(targetIdSet);
        Map<String, String> activeTypeMap = activeTypeList.stream().collect(Collectors.toMap(
                TargetUserAchievementDetailListDTO::getTargetId, TargetUserAchievementDetailListDTO::getActiveType, (a, b) -> a));

        Set<String> userOrgIdSet = list.stream().map(TargetUserAchievementDetailListDTO::getOrgId).collect(Collectors.toSet());
        Map<String, OrgShowDTO> userOrgNameMap = orgFeign.getOrgShowDTO(userOrgIdSet);

        list.forEach(result -> {
            // 用户的部门名称
            OrgShowDTO orgShowDTO = Optional.ofNullable(userOrgNameMap.get(result.getOrgId())).orElse(new OrgShowDTO());
            result.setOrgName(orgShowDTO.getOrgShortName());
            result.setOrgPath(orgShowDTO.getLevelPathName());
            // 组织部门名称
            String targetId = result.getTargetId();
            LearningTargetManage learningTargetManage = learningTargetManageMap.get(targetId);
            String orgId = learningTargetManage.getOrgId();
            result.setBelongOrgName(Optional.ofNullable(orgNameMap.get(orgId)).orElse(""));
            // 目标人群类别的名称：部门名称或者身份名称
            String targetUserTypeId = learningTargetManage.getTargetUserTypeId();
            result.setTargetUserTypeName(Optional.ofNullable(targetTypeNameMap.get(targetUserTypeId)).orElse(""));

            result.setStartTime(learningTargetManage.getStartTime());
            result.setEndTime(learningTargetManage.getEndTime());
            String activeType = activeTypeMap.get(targetId);
            if (StringUtils.isNotEmpty(activeType)) {
                // 转成中文的返回。那么数据库为什么不直接存中文呢，因为要根据它去查激励的，激励事件类型存的英文。
                result.setActiveType(convertToChinese(activeType));
            }
        });
        return pageInfo;
    }

    @Async("exportTaskThreadPool")
    @Override
    public void exportTargetUserAchievementDetailList(TargetUserAchievementQuery query) {
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<ILearningTargetManageService, TargetUserAchievementDetailListDTO>(query) {

            @Override
            protected ILearningTargetManageService getBean() {
                return SpringUtil.getBean(BEAN_NAME, ILearningTargetManageService.class);
            }

            @Override
            protected PageInfo<TargetUserAchievementDetailListDTO> getPageInfo() {
                return getBean().getTargetUserAchievementDetailList((TargetUserAchievementQuery) queryDTO);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.TARGET_USER_ACHIEVEMENT_DETAIL;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.TARGET_USER_ACHIEVEMENT_DETAIL.getType();
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public PageInfo<UserGottenTargetValueDetailDTO> queryUserGottenTargetValueDetailList(UserTargetValueQuery query) {
        String targetId = query.getTargetId();
        LearningTargetManage target = getById(targetId);
        if (target == null) {
            return new PageInfo<>();
        }
        query.setTargetValueType(target.getTargetValueType());
        query.setStartTime(target.getStartTime());
        query.setEndTime(target.getEndTime());
        List<String> activeTypeList = targetRefActiveService.getActiveTypeByTargetId(targetId);
        query.setActiveTypeList(activeTypeList);
        PageInfo<UserGottenTargetValueDetailDTO> pageInfo = excitationFeign.queryUserGottenTargetValueDetailList(query);
        return pageInfo;
    }

    @Async("exportTaskThreadPool")
    @Override
    public void exportUserGottenTargetValueDetailList(UserTargetValueQuery query) {
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<ILearningTargetManageService, UserGottenTargetValueDetailDTO>(query) {

            @Override
            protected ILearningTargetManageService getBean() {
                return SpringUtil.getBean(BEAN_NAME, ILearningTargetManageService.class);
            }

            @Override
            protected PageInfo<UserGottenTargetValueDetailDTO> getPageInfo() {
                return getBean().queryUserGottenTargetValueDetailList((UserTargetValueQuery) queryDTO);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.USER_GOTTEN_TARGET_VALUE_DETAIL;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.USER_GOTTEN_TARGET_VALUE_DETAIL.getType();
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }

    // "course, exam, project" -> "课程, 考试， 学习项目"
    private String convertToChinese(String activityTypes) {
        String[] activityArray = activityTypes.split(",");
        return Arrays.stream(activityArray)
                .map(type -> I18nUtil.getMessage(TargetRefActiveTypeEnum.fromCode(type.trim()).getDescription()))
                .collect(Collectors.joining(", "));
    }

    public Map<String, Integer> calculateTargetValueSums(List<LearningTargetManage> targetList) {
        return targetList.stream()
                .filter(target -> target.getTargetValue() != null)
                .collect(Collectors.groupingBy(
                        LearningTargetManage::getTargetValueType,
                        Collectors.summingInt(LearningTargetManage::getTargetValue)
                ));
    }

    @XxlJob("targetValueSchedule")
    public void targetValueSchedule() {
        for (Object tenantId : redisTemplate.opsForHash().keys(TenantRedisKeyConstant.DB_KEY)) {
            UserThreadContext.setTenantId(
                    ((String) tenantId).replace(DynamicDataSourceConstant.DB_LOOK_UP_KEY_PREFIX, ""));
            refreshUserTargetValue();
        }
        UserThreadContext.remove();
    }


    public void refreshUserTargetValue() {
        // 清数据. 已删除、已取消发布的目标，也需要清理掉统计数据
        List<String> unpublishAndBeenDeletedTargetIdList = baseMapper.getUnpublishAndBeenDeletedTargetIdList();
        if (!unpublishAndBeenDeletedTargetIdList.isEmpty()) {
            statUserTargetValueService.remove(new LambdaQueryWrapper<StatUserTargetValue>().in(StatUserTargetValue::getTargetId, unpublishAndBeenDeletedTargetIdList));
        }
        // 每天凌晨查所有已发布、未删除、已开始且未结束的目标
        List<LearningTargetManage> targetList = lambdaQuery().eq(LearningTargetManage::getIsPublish, 1)
                .le(LearningTargetManage::getStartTime, new Date())
                .ge(LearningTargetManage::getEndTime, new Date()).list();
        if (CollectionUtils.isEmpty(targetList)) {
            return;
        }
        List<String> targetIdList = targetList.stream().map(LearningTargetManage::getId).toList();
        // 清数据
        // 需要统计的目标，先清数据后重新算。
        statUserTargetValueService.remove(new LambdaQueryWrapper<StatUserTargetValue>().in(StatUserTargetValue::getTargetId, targetIdList));

        List<StatUserTargetValue> statList = new ArrayList<>();
        // 查出涉及的目标人群
        targetList.forEach(target -> {
            TargetUserProcessQuery query = new TargetUserProcessQuery();
            query.setTargetUserTypeId(target.getTargetUserTypeId());
            query.setTargetUserType(target.getTargetUserType());
            List<LearningTargetManageUserProcessDTO> userList = userFeign.getTargetUserListByOneTargetManage(query);
            if (CollectionUtils.isEmpty(userList)) {
                return;
            }
            Set<String> userIdSet = userList.stream().map(LearningTargetManageUserProcessDTO::getUserId).collect(Collectors.toSet());
            List<String> activeTypeList = targetRefActiveService.getActiveTypeByTargetId(target.getId());
            UserTargetValueQuery userTargetValueQuery = new UserTargetValueQuery();
            userTargetValueQuery.setUserIdSet(userIdSet);
            userTargetValueQuery.setActiveTypeList(activeTypeList);
            userTargetValueQuery.setTargetValueType(target.getTargetValueType());
            userTargetValueQuery.setStartTime(target.getStartTime());
            userTargetValueQuery.setEndTime(target.getEndTime());
            Map<String, Integer> userGottenTargetValueMap = excitationFeign.getUserHasBeenGotTargetValue(userTargetValueQuery);
            // 查用户获取的目标值
            Integer targetValue = target.getTargetValue();
            userList.forEach(user -> {
                Integer gottenTargetValue = userGottenTargetValueMap.getOrDefault(user.getUserId(), 0);
                int unFinishNum = targetValue - gottenTargetValue;
                double process = (gottenTargetValue > targetValue) ? 100 : (double) gottenTargetValue / targetValue * 100;

                StatUserTargetValue statData = new StatUserTargetValue();
                statData.setId(newId());
                BeanUtils.copyProperties(user, statData);
                statData.setTargetId(target.getId());
                statData.setTargetValue(targetValue);
                statData.setTargetValueType(target.getTargetValueType());
                statData.setFinishNum(gottenTargetValue);
                statData.setUnFinishNum(Math.max(unFinishNum, 0));
                statData.setProcess((double) Math.round(process));
                statData.setProcessStr(String.format("%d%%", Math.round(process)));

                statList.add(statData);
            });
        });

        statUserTargetValueService.saveBatch(statList, 500);
    }

    @Override
    public UserCardInfoDTO getHomePageUserCardInfo(UserCardInfoQuery queryDTO) throws IllegalAccessException {
        UserCardInfoDTO userCardInfoDTO = new UserCardInfoDTO();
        List<MyStatisticListDTO> myStatisticListDTOS = excitationFeign.getPersonalInfo(true);
        Map<String, MyStatisticListDTO> myStatisticMap = myStatisticListDTOS.stream().collect(
            Collectors.toMap(MyStatisticListDTO::getId,  dto -> dto, (key1, key2) -> key1));
        List<MyStatisticListDTO> pageInfo = new ArrayList<>();
        String userId = UserThreadContext.getUserId();
        queryDTO.getCardInfos().forEach(str -> {
            MyStatisticListDTO dto = new MyStatisticListDTO();
            // 任务需要特殊处理
            if (str.equals("task")) {
                dto.setId("task");
                dto.setFlag("task");
                dto.setTitle(I18nUtil.getMessage("任务"));
                dto.setValue(userFeign.getUserTaskNumber(userId));
            } else {
                dto = myStatisticMap.get(str);
                DifferentTypeLearningTargetListDTO targetListDTO = queryUserLearningTargetListByType(str);
                if (Objects.nonNull(targetListDTO)) {
                    dto.setTargetValue(targetListDTO.getTotalValue());
                }
            }
            setMystatisticListDTO(str, dto);
            pageInfo.add(dto);
        });
        userCardInfoDTO.setMyStatisticListDTOS(pageInfo);
        return userCardInfoDTO;
    }

    private void setMystatisticListDTO(String str, MyStatisticListDTO dto) {
        UserTargetValueProcessClientDTO userTargetDTO = statUserTargetValueService.getUserTargetValueProcess();
        if (!ObjectUtils.isEmpty(userTargetDTO)) {
            if (str.equals("learnCredit")) {
                dto.setPercent(userTargetDTO.getCreditPercent());
                dto.setValue(userTargetDTO.getCredit());
            }
            if (str.equals("learnTime")) {
                dto.setPercent(userTargetDTO.getLearnTimePercent());
                dto.setValue(userTargetDTO.getLearnTime());
            }
            if (str.equals("integral")) {
                dto.setPercent(userTargetDTO.getIntegralPercent());
                dto.setValue(userTargetDTO.getIntegral());
            }
        }
    }

    private boolean validateActiveList(List<String> activeList) {
        Set<String> validValues = new HashSet<>();
        validValues.add("course");
        validValues.add("project");
        validValues.add("exam");
        validValues.add("faceProject");

        // 如果有不合法的元素，则返回 false，否则返回 true
        return activeList.stream().allMatch(validValues::contains);
    }
}
