package com.wunding.learn.train.service.admin.dto;

import com.wunding.learn.train.service.model.LearnMapAbilityOperation;
import com.wunding.learn.train.service.model.LearnMapUserOperationRecord;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 用户能力项完成进度查询对象DTO
 *
 * <AUTHOR>
 * @date 2024/5/15 下午3:58
 */
@Data
@Schema(name = "LearnMapAbilityItemRecordListDTO", description = "用户能力项完成进度查询对象")
public class LearnMapAbilityItemRecordListDTO {

    /**
     * 用户操作记录Id {@link LearnMapUserOperationRecord#getId()}
     */
    @Schema(description = "用户操作记录Id")
    private String recordId;

    @Schema(description = "能力id")
    private String abilityId;

    @Schema(description = "完成状态 0-否 1-是")
    private Integer status;

    /**
     * {@link LearnMapAbilityOperation#getId()}
     */
    @Schema(description = "操作记录Id")
    private String operationId;

    @Schema(description = "复训周期")
    private String trainingCycle;

    @Schema(description = "需要完成值")
    private Integer price;

    @Schema(description = "当前完成进度")
    private Integer progress;

    @Schema(description = "学习形式id")
    private String categoryType;

    @Schema(description = "类型 0-初训 1-复训")
    private Integer type;

}
