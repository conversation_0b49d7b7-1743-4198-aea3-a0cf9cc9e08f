package com.wunding.learn.train.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/3/3
 */
@Data
@Schema(name = "LayoutMenuConfigDTO", description = "菜单对象")
public class LayoutMenuConfigDTO {
    /**
     * 主键id
     */
    @Schema(description = "主键id")
    private String id;


    /**
     * 板块配置表layout_config主键id
     */
    @Schema(description = "板块配置表layout_config主键id")
    private String configId;


    /**
     * 栏目类型[INDEX-首页,NOTICE-公告,COURSE-课程,SCHEDULE-日程,ACTIVITY-活动,CLASS-班级,PHOTO-照片墙,DATUM-资料,CASE-案例,TOPIC-话题,RANK-排名,LECTURER-讲师,SPECIAL-专题]
     */
    @Schema(description = "栏目类型[INDEX-首页,NOTICE-公告,COURSE-课程,SCHEDULE-日程,ACTIVITY-活动,CLASS-班级,PHOTO-照片墙,DATUM-资料,CASE-案例,TOPIC-话题,RANK-排名,LECTURER-讲师,SPECIAL-专题]")
    private String menuType;


    /**
     * 栏目名称
     */
    @Schema(description = "栏目名称")
    private String menuName;


    /**
     * 序号
     */
    @Schema(description = "序号")
    private Integer sortNo;


    /**
     * 是否展示会员标记(0为否,1为是)
     */
    @Schema(description = "是否展示会员标记(0为否,1为是)")
    private Integer isMember;
}
