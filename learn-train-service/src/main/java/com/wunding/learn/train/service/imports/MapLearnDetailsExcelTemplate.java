package com.wunding.learn.train.service.imports;

import com.wunding.learn.common.enums.ResourceTypeEnum;
import com.wunding.learn.common.i18n.util.I18nUtil;
import com.wunding.learn.common.i18n.util.ImportTemplateI18nEnum;
import com.wunding.learn.common.util.date.DateUtil;
import com.wunding.learn.common.util.excel.AbstractExcelTemplate;
import com.wunding.learn.common.util.excel.ExcelCheckMessage;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.course.api.dto.CourseInfoDTO;
import com.wunding.learn.course.api.service.CourseFeign;
import com.wunding.learn.project.api.dto.ProjectApiDTO;
import com.wunding.learn.project.api.service.ProjectFeign;
import com.wunding.learn.train.service.admin.dto.ImportMapLearnDetailsDTO;
import com.wunding.learn.train.service.admin.dto.LearnMapUserOperationRecordInfoDTO;
import com.wunding.learn.train.service.model.LearnMap;
import com.wunding.learn.train.service.service.ILearnMapService;
import com.wunding.learn.train.service.service.ILearnMapUserOperationRecordService;
import com.wunding.learn.user.api.dto.AbilityBaseInfoDTO;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.service.AbilityFeign;
import com.wunding.learn.user.api.service.UserFeign;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

/**
 * 实操记录  导入excel
 *
 * <AUTHOR> href="mailto:<EMAIL>">xd</a>
 * @since 2024-03-15
 */
@Slf4j
public class MapLearnDetailsExcelTemplate extends AbstractExcelTemplate {

    private static final String[] IMPORT_TITLES = {"姓名", "账号", "胜任力地图", "能力分类", "能力编码", "能力名称",
        "活动编码", "活动名称"
        , "学习形式", "类型", "开始时间", "结束时间", "手工维护备注"};

    public MapLearnDetailsExcelTemplate(UserFeign userFeign
        , AbilityFeign abilityFeign
        , ProjectFeign projectFeign
        , CourseFeign courseFeign
        , ILearnMapService learnMapService
        , ILearnMapUserOperationRecordService iLearnMapUserOperationRecordService) {
        super(IMPORT_TITLES);
        this.userFeign = userFeign;
        this.abilityFeign = abilityFeign;
        this.projectFeign = projectFeign;
        this.courseFeign = courseFeign;
        this.learnMapService = learnMapService;
        this.iLearnMapUserOperationRecordService = iLearnMapUserOperationRecordService;
    }

    private UserFeign userFeign;
    private AbilityFeign abilityFeign;
    private ProjectFeign projectFeign;
    private CourseFeign courseFeign;
    private ILearnMapService learnMapService;
    private ILearnMapUserOperationRecordService iLearnMapUserOperationRecordService;

    @Override
    protected ExcelCheckMessage doCheck(ExcelCheckMessage excelCheckMessage) {
        List<String> messages = excelCheckMessage.getMessage();
        List<String[]> rows = excelCheckMessage.getRows();
        List<ImportMapLearnDetailsDTO> list = new ArrayList<>();
        int rowNum = 0;
        for (String[] row : rows) {
            rowNum++;
            ImportMapLearnDetailsDTO dto = new ImportMapLearnDetailsDTO();

            // 姓名 账号
            String fullName = row[0].trim();
            String loginName = row[1].trim();
            checkFullNameAndLoginName(loginName, messages, rowNum, fullName, dto);

            // 胜任力地图
            String mapName = row[2].trim();
            checkMapName(mapName, messages, rowNum, dto);

            // 能力分类、编码、名称
            String abilityCategoryName = row[3].trim();
            String abilityCode = row[4].trim();
            String abilityName = row[5].trim();
            checkAbilityCode(abilityCode, messages, rowNum, abilityCategoryName, abilityName, dto);

            // 活动编码
            String proNo = row[6].trim();
            // 活动名称
            String proName = row[7].trim();
            // 学习形式
            String categoryTypeName = row[8].trim();
            // 基于活动编码进行校验
            checkByProNo(proNo, categoryTypeName, messages, rowNum, proName, dto);

            setCategoryType(categoryTypeName, dto, messages, rowNum);

            // 类型
            setType(row, dto, messages, rowNum);

            setStartTimeAndEndTime(row, dto, messages, rowNum);

            // 手工维护备注
            String createNotes = row[12].trim();
            if (StringUtils.hasText(createNotes)) {
                dto.setCreateNotes(createNotes);
            } else {
                messages.add(emptyMessage(rowNum, IMPORT_TITLES[12]));
            }

            // 联合检查，获取操作ID
            setRecordIdAndOperationId(dto, messages, rowNum);

            list.add(dto);
        }
        excelCheckMessage.setObjects(list);
        return excelCheckMessage;
    }

    private void setStartTimeAndEndTime(String[] row, ImportMapLearnDetailsDTO dto, List<String> messages, int rowNum) {
        // 开始时间
        String start = row[10].trim();
        if (StringUtils.hasText(start)) {
            try {
                dto.setStartTime(DateUtil.formatToDate(start));
            } catch (Exception e) {
                messages.add(
                    I18nUtil.getImportMessage(ImportTemplateI18nEnum.AbstractExcelTemplateFillInAsRequired,
                        rowNum, IMPORT_TITLES[10]));
            }
        } else {
            messages.add(emptyMessage(rowNum, IMPORT_TITLES[10]));
        }

        // 结束时间
        String end = row[11].trim();
        if (StringUtils.hasText(end)) {
            try {
                dto.setEndTime(DateUtil.formatToDate(end));
            } catch (Exception e) {
                messages.add(
                    I18nUtil.getImportMessage(ImportTemplateI18nEnum.AbstractExcelTemplateFillInAsRequired,
                        rowNum, IMPORT_TITLES[11]));
            }
        } else {
            messages.add(emptyMessage(rowNum, IMPORT_TITLES[11]));
        }
    }

    private void setType(String[] row, ImportMapLearnDetailsDTO dto, List<String> messages, int rowNum) {
        String typeName = row[9].trim();
        if (StringUtils.hasText(typeName)) {
            if (Objects.equals("初训", typeName)) {
                dto.setType(0);
            } else if (Objects.equals("复训", typeName)) {
                dto.setType(1);
            } else {
                messages.add(
                    I18nUtil.getImportMessage(ImportTemplateI18nEnum.MapLearnDetailsExcelTemplateSelectAsRequired,
                        rowNum, IMPORT_TITLES[9]));
            }
        } else {
            messages.add(emptyMessage(rowNum, IMPORT_TITLES[9]));
        }
    }

    private void setCategoryType(String categoryTypeName, ImportMapLearnDetailsDTO dto, List<String> messages,
        int rowNum) {
        if (StringUtils.hasText(categoryTypeName)) {
            switch (categoryTypeName) {
                case "学习":
                    dto.setCategoryType("0");
                    break;
                case "培训":
                    dto.setCategoryType("1");
                    break;
                case "授课":
                    dto.setCategoryType("2");
                    break;
                case "评价":
                    dto.setCategoryType("3");
                    break;
                case "实操":
                    dto.setCategoryType("4");
                    break;
                default:
                    messages.add(
                        I18nUtil.getImportMessage(ImportTemplateI18nEnum.MapLearnDetailsExcelTemplateSelectAsRequired,
                            rowNum, IMPORT_TITLES[8]));
                    break;
            }
        } else {
            messages.add(emptyMessage(rowNum, IMPORT_TITLES[8]));
        }
    }

    // 基于活动编码进行校验
    private void checkByProNo(String proNo, String categoryTypeName, List<String> messages, int rowNum, String proName,
        ImportMapLearnDetailsDTO dto) {
        if (StringUtils.hasText(proNo)) {
            if (Objects.equals("学习", categoryTypeName)) {
                // 学习 对应课程验证
                checkCourseByCategoryTypeName(proNo, messages, rowNum, proName, dto);
            } else {
                // 学习项目验证
                checkProject(proNo, messages, rowNum, proName, dto);
            }
        } else {
            messages.add(emptyMessage(rowNum, IMPORT_TITLES[6]));
        }
    }

    // 学习项目验证
    private void checkProject(String proNo, List<String> messages, int rowNum, String proName,
        ImportMapLearnDetailsDTO dto) {
        // 学习项目验证
        ProjectApiDTO infoDTO = projectFeign.getProjectByProNo(proNo);
        if (infoDTO == null) {
            messages.add(
                I18nUtil.getImportMessage(ImportTemplateI18nEnum.MapLearnDetailsExcelTemplateResourceProjectNotExist,
                    rowNum, IMPORT_TITLES[6]));
        } else {
            if (StringUtils.hasText(proName) && !Objects
                .equals(proName, infoDTO.getProName())) {
                messages.add(
                    I18nUtil.getImportMessage(ImportTemplateI18nEnum.MapLearnDetailsExcelTemplateImportedTitlesNotMatch,
                        rowNum, IMPORT_TITLES[6], IMPORT_TITLES[7]));
            }
            dto.setResourcesId(infoDTO.getId());
            dto.setResourcesType(ResourceTypeEnum.PROJECT.getType());
        }
    }

    // 学习 对应课程验证
    private void checkCourseByCategoryTypeName(String proNo, List<String> messages, int rowNum, String proName,
        ImportMapLearnDetailsDTO dto) {
        // 学习 对应课程验证
        CourseInfoDTO infoDTO = courseFeign.getByNo(proNo);
        if (infoDTO == null) {
            messages.add(
                I18nUtil.getImportMessage(ImportTemplateI18nEnum.MapLearnDetailsExcelTemplateResourceCourseNotExist,
                    rowNum, IMPORT_TITLES[6]));
        } else {
            if (StringUtils.hasText(proName) && !Objects
                .equals(proName, infoDTO.getCourseName())) {
                messages.add(
                    I18nUtil.getImportMessage(ImportTemplateI18nEnum.MapLearnDetailsExcelTemplateImportedTitlesNotMatch,
                        rowNum, IMPORT_TITLES[6], IMPORT_TITLES[7]));
            }
            dto.setResourcesId(infoDTO.getId());
            dto.setResourcesType(ResourceTypeEnum.COURSE.getType());
        }
    }

    private void checkAbilityCode(String abilityCode, List<String> messages, int rowNum, String abilityCategoryName,
        String abilityName, ImportMapLearnDetailsDTO dto) {
        if (StringUtils.hasText(abilityCode)) {
            AbilityBaseInfoDTO abilityBaseInfoDTO = abilityFeign.getAbilityBaseInfoByCode(abilityCode);
            if (abilityBaseInfoDTO == null) {
                messages.add(
                    I18nUtil.getImportMessage(ImportTemplateI18nEnum.MapLearnDetailsExcelTemplateImportedTitlesNotExist,
                        rowNum, IMPORT_TITLES[4]));
            } else {
                if (StringUtils.hasText(abilityCategoryName) && !Objects
                    .equals(abilityCategoryName, abilityBaseInfoDTO.getAbilityCategoryName())) {
                    messages.add(I18nUtil.getImportMessage(
                        ImportTemplateI18nEnum.MapLearnDetailsExcelTemplateImportedTitlesNotMatch,
                        rowNum, IMPORT_TITLES[3], IMPORT_TITLES[4]));
                }
                if (StringUtils.hasText(abilityName) && !Objects
                    .equals(abilityName, abilityBaseInfoDTO.getName())) {
                    messages.add(I18nUtil.getImportMessage(
                        ImportTemplateI18nEnum.MapLearnDetailsExcelTemplateImportedTitlesNotMatch,
                        rowNum, IMPORT_TITLES[4], IMPORT_TITLES[5]));
                }
                dto.setAbilityId(abilityBaseInfoDTO.getId());
            }
        } else {
            messages.add(emptyMessage(rowNum, IMPORT_TITLES[4]));
        }
    }

    private void checkMapName(String mapName, List<String> messages, int rowNum, ImportMapLearnDetailsDTO dto) {
        if (StringUtils.hasText(mapName)) {
            LearnMap map = learnMapService.lambdaQuery().eq(LearnMap::getMapName, mapName)
                .orderByAsc(LearnMap::getId).last("limit 1").one();
            if (map == null) {
                messages.add(I18nUtil.getImportMessage(
                    ImportTemplateI18nEnum.MapLearnDetailsExcelTemplateImportedTitlesNotExist,
                    rowNum, IMPORT_TITLES[2]));
            } else {
                dto.setMapId(map.getId());
            }
        } else {
            messages.add(emptyMessage(rowNum, IMPORT_TITLES[2]));
        }
    }

    private void checkFullNameAndLoginName(String loginName, List<String> messages, int rowNum, String fullName,
        ImportMapLearnDetailsDTO dto) {
        if (StringUtils.hasText(loginName)) {
            // 数据库校验
            UserDTO sysUser = userFeign.getUserByLoginName(loginName);
            if (null == sysUser) {
                messages.add(I18nUtil.getImportMessage(
                    ImportTemplateI18nEnum.ImportedUserNotExistDisplayAccount,
                    rowNum, IMPORT_TITLES[1]));
            } else {
                //账号与姓名的匹配
                if (StringUtils.hasText(fullName) && !sysUser.getFullName().equals(fullName)) {
                    messages.add(I18nUtil.getImportMessage(
                        ImportTemplateI18nEnum.AbstractExcelTemplateAccountAndNameNotMatch, rowNum));
                } else {
                    dto.setUserId(sysUser.getId());
                }
            }
        } else {
            messages.add(emptyMessage(rowNum, IMPORT_TITLES[1]));
        }
    }

    // 联合检查，获取操作ID
    private void setRecordIdAndOperationId(ImportMapLearnDetailsDTO dto, List<String> messages, int rowNum) {
        // 联合检查，获取操作ID
        if (StringUtils.hasText(dto.getUserId())
            && StringUtils.hasText(dto.getMapId())
            && StringUtils.hasText(dto.getAbilityId())
            && dto.getType() != null
            && StringUtils.hasText(dto.getCategoryType())) {
            LearnMapUserOperationRecordInfoDTO operationRecord = iLearnMapUserOperationRecordService
                .getOperationRecordByParam(dto);
            if (operationRecord == null) {
                messages.add(I18nUtil.getImportMessage(
                    ImportTemplateI18nEnum.MapLearnDetailsExcelTemplateUserAndCompetencyMapAssociatedRecordsNotExist,
                    rowNum));
            } else if (StringUtil.isEmpty(operationRecord.getMapId())) {
                messages.add(
                    I18nUtil.getImportMessage(ImportTemplateI18nEnum.MapLearnDetailsExcelTemplateCompetencyMapNotExist,
                        rowNum));
            } else if (StringUtil.isEmpty(operationRecord.getAskId())) {
                messages.add(I18nUtil.getImportMessage(
                    ImportTemplateI18nEnum.MapLearnDetailsExcelTemplateCompetencyMapAndAbilityNotAssociated, rowNum));
            } else if (StringUtil.isEmpty(operationRecord.getOperationId())) {
                messages.add(I18nUtil.getImportMessage(
                    ImportTemplateI18nEnum.MapLearnDetailsExcelTemplateCompetencyMapNotHaveAbilityLearningRequirements,
                    rowNum));
            } else if (StringUtil.isEmpty(operationRecord.getRecordId())) {
                messages.add(I18nUtil.getImportMessage(
                    ImportTemplateI18nEnum.MapLearnDetailsExcelTemplateCompetencyMapNoUserRecords, rowNum));
            } else {
                dto.setRecordId(operationRecord.getRecordId());
                dto.setOperationId(operationRecord.getOperationId());
            }
        }
    }

}
