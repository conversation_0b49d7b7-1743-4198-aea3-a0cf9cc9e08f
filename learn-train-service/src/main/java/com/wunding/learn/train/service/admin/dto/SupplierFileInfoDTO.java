package com.wunding.learn.train.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @program: mlearn
 * @description:
 * @author: axr
 * @create: 2023-03-03 10:05
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(name = "SupplierFileInfoDTO", description = "供应商资料详情对象")
public class SupplierFileInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "供应商资料ID")
    private String id;

    @Schema(description = "供应商资料标题", required = true)
    private String title;

    @Schema(description = "资料分类id")
    private String categoryId;

    @Schema(description = "文件名称")
    private String fileName;
}
