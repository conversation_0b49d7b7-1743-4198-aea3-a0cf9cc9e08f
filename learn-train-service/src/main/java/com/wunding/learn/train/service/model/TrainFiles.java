package com.wunding.learn.train.service.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 文件表
 *
 * <AUTHOR> href="mailto:<EMAIL>">axr</a>
 * @since 2023-03-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("train_files")
@Schema(name = "TrainFiles对象", description = "文件表")
public class TrainFiles implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 文件id
     */
    @Schema(description = "文件id")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;


    /**
     * 培训项目id
     */
    @Schema(description = "培训项目id")
    @TableField("train_id")
    private String trainId;


    /**
     * 文件名称
     */
    @Schema(description = "文件名称")
    @TableField("title")
    private String title;


    /**
     * 案例文件类型
     */
    @Schema(description = "案例文件类型")
    @TableField("file_type")
    private String fileType;


    /**
     * 案例文件类型
     */
    @Schema(description = "案例文件类型")
    @TableField("mime")
    private String mime;


    /**
     * 文件所属业务
     */
    @Schema(description = "文件所属业务")
    @TableField("resource_type")
    private String resourceType;


    /**
     * 作者
     */
    @Schema(description = "作者")
    @TableField("author")
    private String author;


    /**
     * 文件大小/bit
     */
    @Schema(description = "文件大小/bit")
    @TableField("file_size")
    private Long fileSize;


    /**
     * 视频转换状态  1 转换中  2 转换完成 3 转换失败
     */
    @Schema(description = "视频转换状态  1 转换中  2 转换完成 3 转换失败")
    @TableField("transform_status")
    private Integer transformStatus;

    @Schema(description = "文件下载次数")
    @TableField("download_num")
    private Long downloadNum;

    @Schema(description = "文件预览次数")
    @TableField("view_num")
    private Long viewNum;

    /**
     * 是否启用[0：禁用 1：启用]
     */
    @Schema(description = "是否启用[0：禁用 1：启用]")
    @TableField("is_available")
    private Integer isAvailable;


    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    @TableField("is_del")
    private Integer isDel;


    /**
     * 新增人
     */
    @Schema(description = "新增人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;


    /**
     * 新增时间
     */
    @Schema(description = "新增时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 最后编辑人
     */
    @Schema(description = "最后编辑人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;


    /**
     * 最后编辑时间
     */
    @Schema(description = "最后编辑时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
    * 兑换学习消耗激励数量
    */
    @Schema(description = "兑换学习消耗激励数量")
    @TableField("consume_excitation_num")
    private BigDecimal consumeExcitationNum;

    /**
    *兑换学习消耗激励类型
    */
    @Schema(description = "兑换学习消耗激励类型", example = "学分:credit, 金币:goldCoin, 积分:integral, 学时:learnTime")
    @TableField("consume_excitation_type")
    private String consumeExcitationType;

    /**
     * 没有权限是否展示，0为不展示，1为展示，默认0
     */
    @Schema(description = "没有权限是否展示，0为不展示，1为展示，默认0")
    @TableField("limit_show")
    private Integer limitShow;

    /**
     * 下发方式：0 部分可见 1仅创建者可见 2所有人可见
     */
    @Schema(description = "下发方式：0 部分可见 1仅创建者可见 2所有人可见")
    @TableField("view_type")
    private Integer viewType;



}
