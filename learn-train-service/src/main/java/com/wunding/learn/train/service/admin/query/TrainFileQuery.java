package com.wunding.learn.train.service.admin.query;

import com.wunding.learn.common.bean.BasePageQuery;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @program: mlearn
 * @description:
 * @author: arx
 * @create: 2023-03-03 10:09
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class TrainFileQuery extends BasePageQuery {

    private static final long serialVersionUID = 1L;

    @Parameter(description = "项目ID", required = true)
    @NotBlank(message = "项目id不可为空")
    private String trainId;

    @Parameter(description = "文件类型", hidden = true)
    private String resourceType;

    @Parameter(description = "目录id,多个用逗号分割")
    private String directoryId;

    @Parameter(description = "文件名称")
    private String title;
}
