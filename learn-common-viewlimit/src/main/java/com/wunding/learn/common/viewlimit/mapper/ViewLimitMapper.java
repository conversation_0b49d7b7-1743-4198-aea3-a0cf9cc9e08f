package com.wunding.learn.common.viewlimit.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import com.wunding.learn.common.viewlimit.model.ViewLimit;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Property;

/**
 * <p>
 * 可见范围方案表 Mapper 接口
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">xiao8</a>
 * @date 2022-04-26
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")
})
public interface ViewLimitMapper extends BaseMapper<ViewLimit> {

}
