package com.wunding.learn.special.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.special.service.admin.dto.CompletionHandleDTO;
import com.wunding.learn.special.service.admin.dto.CompletionListDTO;
import com.wunding.learn.special.service.admin.query.CompletionListQuery;
import com.wunding.learn.special.service.model.SpecialCompletionUser;
import org.springframework.scheduling.annotation.Async;

/**
 * <p> 结业人员情况 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">liu<PERSON><PERSON>ong</a>
 * @since 2022-08-09
 */
public interface ISpecialCompletionUserService extends IService<SpecialCompletionUser> {


    /**
     * 数据用户列表
     *
     * @param completionListQuery
     * @return
     */
    PageInfo<CompletionListDTO> listDetail(CompletionListQuery completionListQuery);

    /**
     * 生成预结业名单
     *
     * @param id
     */
    void proCompletionUser(String id);

    /**
     * 全部结业
     *
     * @param completionHandleDTO
     */
    void completionAllUser(CompletionHandleDTO completionHandleDTO);

    /**
     * 部分结业
     *
     * @param completionHandleDTO
     */
    void completionPartUser(CompletionHandleDTO completionHandleDTO);

    /**
     * 导出专题结业情况
     */
    @Async
    void exportData(CompletionListQuery queryDTO);
}
