package com.wunding.learn.common.mq.event.exam;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.wunding.learn.common.mq.event.ResourceFinishEvent;
import lombok.Getter;

/**
 * 考试提交事件（用户提交考试答题）
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Getter
public class ExamSubmitEvent extends ResourceFinishEvent {

    private static final long serialVersionUID = 1423928527360705768L;


    @JsonCreator
    public ExamSubmitEvent(String contentId, String userId, String createBy) {
        super(ResourceEventRoutingKeyConstants.EXAM_SUBMIT_EVENT, contentId, userId, createBy);
    }
}
