package com.wunding.learn.common.mq.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * </p> 认证明细套打图片对象
 *
 * <AUTHOR> href="mailto:<EMAIL>">gcz</a>
 * @since 2024-05-13
 */
@Data
@Accessors(chain = true)
@Schema(name = "CerRelateWaterMarkImgEventDTO", description = "认证明细套打图片对象")
public class CerRelateWaterMarkImgEventDTO {

    @Schema(description = "证书明细id")
    private String relateId;

    @Schema(description = "证书id")
    private String certificationId;

    @Schema(description = "证书的获证时间")
    private String createTime;

    @Schema(description = "获得证书的用户名")
    private String userName;

    @Schema(description = "获得证书的用户名", hidden = true)
    private String userLoginName;

    @Schema(description = "获得证书的用户组织名")
    private String userOrgan;

    @Schema(description = "有效期")
    private String validPeriod;

    @Schema(description = "证书名称")
    private String cerName;

    @Schema(description = "内容名称")
    private String contentName;

    @Schema(description = "账号")
    private String empNo;

    @Schema(description = "证书编号")
    private String certNo;

    @Schema(description = "内容类型(Course、Project等)")
    private String categoryType;

    @Schema(description = "资源开始时间", hidden = true)
    private String startTime;

    @Schema(description = "资源的举办单位", hidden = true)
    private String sponsor;
}
