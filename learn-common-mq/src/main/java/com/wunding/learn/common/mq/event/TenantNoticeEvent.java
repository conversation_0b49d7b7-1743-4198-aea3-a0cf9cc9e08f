package com.wunding.learn.common.mq.event;

import com.fasterxml.jackson.annotation.JsonCreator;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * 更新公告信息更新 事件
 * <AUTHOR> href="mailto:<EMAIL>">gy</a>
 * @since 2024-12-03
 **/
@Getter
@Setter
public class TenantNoticeEvent extends AbstractEvent {

    /**
     * 定义资源修改交换机
     */
    public static final String INTERVIEW_EXCHANGE = "notice_exchange";

    /**
     * 定义资源修改路由键
     */
    public static final String INTERVIEW_KEY = "notice_key";

    /**
     * 资源修改交换机类型
     */
    public static final String RESOURCE_CHANGE_EXCHANGE_TYPE = EXCHANGE_TOPIC;

    /**
     * 更新公告标题
     */
    private final String title;

    /**
     * 更新公告内容
     */
    private final String content;

    /**
     * 更新公告是否启用
     */
    private final Integer isAvailable;

    /**
     * 当前租户ID
     */
    private final String tenantId;

    /**
     * 公告开始时间
     */
    private Date noticeStartTime;

    /**
     * 公告结束时间
     */
    private Date noticeEndTime;


    @JsonCreator
    public TenantNoticeEvent(String title, String content, Date noticeStartTime, Date noticeEncTime,
        Integer isAvailable, String tenantId) {
        super(INTERVIEW_EXCHANGE, RESOURCE_CHANGE_EXCHANGE_TYPE, INTERVIEW_KEY);
        this.title = title;
        this.content = content;
        this.noticeStartTime = noticeStartTime;
        this.noticeEndTime = noticeEncTime;
        this.isAvailable = isAvailable;
        this.tenantId = tenantId;
    }
}