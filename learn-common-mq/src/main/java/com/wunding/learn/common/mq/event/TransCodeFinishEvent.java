package com.wunding.learn.common.mq.event;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;

/**
 * 转码完成
 * <AUTHOR>
 * @date 2022/6/30
 */
@Getter
// 兼容旧内容无新增字段的情况
@JsonIgnoreProperties(ignoreUnknown = true)
public class TransCodeFinishEvent extends AbstractEvent {

    public static final String EXCHANGE = "trans_code_success_event_exchange";

    /**
     * 业务主键
     */
    private String bizId;

    /**
     * 转码状态
     */
    private Integer status;

    /**
     * 转码后文件格式
     */
    private String mime;

    /**
     * 资源库主键(如:课件库)
     */
    private String libId;

    /**
     * 转码失败原因【一些可读性友好的转码失败描述，否则使用 转码失败请联系管理员 兜底文案】
     */
    private String reason;

    @JsonCreator
    public TransCodeFinishEvent(String routingKey, String bizId, Integer status, String mime, String libId) {
        // 默认 reason 为 null
        this(routingKey, bizId, status, mime, libId, null);
    }

    // 新增带 reason 的构造函数
    public TransCodeFinishEvent(String routingKey, String bizId, Integer status, String mime, String libId, String reason) {
        super(EXCHANGE, EXCHANGE_TOPIC, routingKey);
        this.bizId = bizId;
        this.status = status;
        this.mime = mime;
        this.libId = libId;
        this.reason = reason;
    }
}
