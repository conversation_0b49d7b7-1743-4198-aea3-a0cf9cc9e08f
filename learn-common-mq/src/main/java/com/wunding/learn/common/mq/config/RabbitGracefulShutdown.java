package com.wunding.learn.common.mq.config;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.listener.RabbitListenerEndpointRegistry;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class RabbitGracefulShutdown implements ApplicationListener<ContextClosedEvent> {

    @Resource
    private RabbitListenerEndpointRegistry endpointRegistry;


    @Override
    public void onApplicationEvent(ContextClosedEvent event) {
        log.info("关停服务中，MQ优雅停机 START...");
        // 停止所有RabbitMQ监听器，不再接收消息
        stopRabbitListeners();

        log.info("关停服务中，MQ优雅停机 END.");
    }

    private void stopRabbitListeners() {
        endpointRegistry.getListenerContainers().forEach(container -> {
            log.error("关停服务中，停止所有MQ监听器......Stopping listener container: {}", container);
            container.stop();
        });
    }
}