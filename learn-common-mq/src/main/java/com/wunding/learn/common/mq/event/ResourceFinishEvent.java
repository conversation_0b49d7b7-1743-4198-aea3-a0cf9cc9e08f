package com.wunding.learn.common.mq.event;

import com.fasterxml.jackson.annotation.JsonCreator;
import java.util.Date;
import lombok.Getter;
import lombok.ToString;

/**
 * 资源完成事件 采用topic 模式
 *
 * <AUTHOR>
 * @date 2022/6/6
 */
@Getter
@ToString(callSuper = true)
public class ResourceFinishEvent extends AbstractEvent {

    /**
     * 定义资源完成交换机
     */
    public static final String SYSTEM_RESOURCE_FINISH_EXCHANGE = "system_resource_finish_exchange";
    /**
     * 资源完成交换机类型
     */
    public static final String SYSTEM_RESOURCE_FINISH_EXCHANGE_TYPE = EXCHANGE_TOPIC;
    private static final long serialVersionUID = -3189247934016087616L;
    /**
     * 指定资源类型
     */
    private final String resourceType;

    /**
     * 对应内容id eg: 考试id  课程id 调研id
     */
    private final String contentId;

    /**
     * 用户id
     */
    private final String userId;

    /**
     * 触发事件人的id 大部分情况下都是用户自己触发，但有些特殊情况不是由用户触发 eg: 考试改卷这个场景  {@code createBy} 是改卷人   {@code userId} 是被改卷人的id
     */
    private final String createBy;

    /**
     * 创建时间
     */
    private final Date createTime;

    /**
     * 活动ID
     */
    private final String activeId;

    /**
     * 这里都是资源完成的公共属性 针对特异化场景可在子类中扩展增加字段 消费时根据类型强转即可
     *
     * @param resourceType 资源类型 {@link ResourceEventRoutingKeyConstants}
     * @param contentId    对应内容id
     * @param userId       用户id
     * @param createBy     触发事件人的id
     */
    @JsonCreator
    public ResourceFinishEvent(String resourceType, String contentId, String userId, String createBy) {
        // 同时使用resourceType 作为路由键
        super(SYSTEM_RESOURCE_FINISH_EXCHANGE, SYSTEM_RESOURCE_FINISH_EXCHANGE_TYPE, resourceType);
        this.resourceType = resourceType;
        this.contentId = contentId;
        this.userId = userId;
        this.createBy = createBy;
        this.createTime = null;
        this.activeId = "";
    }

    public ResourceFinishEvent(String resourceType, String contentId, String userId, String createBy, Date createTime) {
        // 同时使用resourceType 作为路由键
        super(SYSTEM_RESOURCE_FINISH_EXCHANGE, SYSTEM_RESOURCE_FINISH_EXCHANGE_TYPE, resourceType);
        this.resourceType = resourceType;
        this.contentId = contentId;
        this.userId = userId;
        this.createBy = createBy;
        this.createTime = createTime;
        this.activeId = "";
    }

    public ResourceFinishEvent(String resourceType, String contentId, String userId, String createBy, Date createTime,
        String activeId) {
        // 同时使用resourceType 作为路由键
        super(SYSTEM_RESOURCE_FINISH_EXCHANGE, SYSTEM_RESOURCE_FINISH_EXCHANGE_TYPE, resourceType);
        this.resourceType = resourceType;
        this.contentId = contentId;
        this.userId = userId;
        this.createBy = createBy;
        this.createTime = createTime;
        this.activeId = activeId;
    }

    private ResourceFinishEvent() {
        super(null, null, null);
        this.resourceType = null;
        this.contentId = null;
        this.userId = null;
        this.createBy = null;
        this.createTime = null;
        this.activeId = null;
        throw new IllegalStateException("error create class");
    }

    /**
     * 系统资源相关事件类型常量定义
     */
    public static class ResourceEventRoutingKeyConstants {

        /**
         * 课程完成
         */
        public static final String COURSE_FINISH_EVENT = "CourseFinishEvent";
        /**
         * 练习完成
         */
        public static final String EXERCISE_FINISH_EVENT = "ExerciseFinishEvent";
        /**
         * 考试完成，完成只代表提交了答案不代表通过
         */
        public static final String EXAM_FINISH_EVENT = "ExamFinishEvent";
        /**
         * EvaluationFinishEvent 评估完成
         */
        public static final String EVALUTION_FINISH_EVENT = "EvaluationFinishEvent";
        /**
         * 共读打卡完成
         */
        public static final String READING_SIGN_FINISH_EVENT = "ReadingSignFinishEvent";
        /**
         * 考试通过  事件 用户得分 大于等于 及格分
         */
        public static final String EXAM_PASS_EVENT = "ExamPassEvent";

        /**
         * 考试提交事件(完成答题)
         */
        public static final String EXAM_SUBMIT_EVENT = "ExamSubmitEvent";
        /**
         * 考试不及格  事件 用户得分 小于 及格分
         */
        public static final String EXAM_UN_PASS_EVENT = "ExamUnPassEvent";
        /**
         * 直播播放事件
         */
        public static final String LIVE_PLAY_EVENT = "LivePlayEvent";
        /**
         * 调研
         */
        public static final String SURVEY_FINISH_EVENT = "SurveyFinishEvent";
        /**
         * 学习项目 结业事件
         */
        public static final String PROJECT_COMPLETION_EVENT = "ProjectCompletionEvent";
        /**
         * 学习项目 完成事件
         */
        public static final String PROJECT_COMPLETED_EVENT = "ProjectCompletedEvent";
        /**
         * 评价完成
         */
        public static final String APPRAISE_FINISH_EVENT = "AppraiseFinishEvent";
        /**
         * 招募
         */
        public static final String RECRUIT_FINISH_EVENT = "RecruitFinishEvent";
        /**
         * 表单完成
         */
        public static final String FORM_FINISH_EVENT = "FormFinishEvent";
        /**
         * 签到完成
         */
        public static final String SIGN_FINISH_EVENT = "SignFinishEvent";
        /**
         * 投票完成
         */
        public static final String VOTE_FINISH_EVENT = "VoteFinishEvent";
        /**
         * 闯关完成
         */
        public static final String PROMOTED_GAME_FINISH_EVENT = "PromotedGameFinishEvent";
        /**
         * 讲师等级变更
         */
        public static final String LECTURER_LEVEL_GET_EVENT = "LecturerLevelChangeEvent";
        /**
         * 共读运营分获得事件
         */
        public static final String READING_SCORE_GAIN_EVENT = "ReadingScoreGainEvent";
        /**
         * 外部培训完成
         */
        public static final String TRAIN_WITHOUT_FINISH_EVENT = "TrainWithoutFinishEvent";
        /**
         * 培训项目完成
         */
        public static final String TRAIN_FINISH_EVENT = "TrainFinishEvent";
        /**
         * 专题完成
         */
        public static final String SPECIAL_FINISH_EVENT = "SpecialFinishEvent";
        /**
         * 实操完成事件
         */
        public static final String PRACTICAL_OPERATION_FINISH_EVENT = "PracticalOperationFinishEvent";
        /**
         * 学习地图完成事件
         */
        public static final String LEARN_MAP_FINISH_EVENT = "LearnMapFinishEvent";
        /**
         * 作业完成事件
         */
        public static final String HOMEWORK_FINISH_EVENT = "HomeworkFinishEvent";

        private ResourceEventRoutingKeyConstants() {
            // 警告消除：添加一个私有构造函数来隐藏隐式的已发布构造函数。
        }
    }
}
