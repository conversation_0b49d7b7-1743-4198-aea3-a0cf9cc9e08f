package com.wunding.learn.sync.service.handler.load;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.wunding.learn.common.constant.other.ErrorNoEnum;
import com.wunding.learn.common.constant.user.UserConstant;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.i18n.config.GlobalLocaleResolver;
import com.wunding.learn.common.util.date.DateHelper;
import com.wunding.learn.common.util.date.DateUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.util.net.FTPUtils;
import com.wunding.learn.file.api.service.FileFeign;
import com.wunding.learn.sync.service.handler.SaveData;
import com.wunding.learn.sync.service.handler.conver.GroovyConvertData;
import com.wunding.learn.sync.service.model.DataSource;
import com.wunding.learn.sync.service.model.Task;
import com.wunding.learn.sync.service.service.IDataSourceService;
import com.wunding.learn.sync.service.service.ISourceRecordService;
import com.wunding.learn.sync.service.service.ITaskService;
import com.wunding.learn.sync.service.service.impl.lnns.HrmUserServiceImpl;
import com.wunding.learn.sync.service.service.impl.lnns.SyncPostRecordServiceImpl;
import com.wunding.learn.sync.service.service.lnns.IHrmOrgService;
import com.wunding.learn.user.api.dto.OrgDTO;
import com.wunding.learn.user.api.dto.sync.SyncPostDTO;
import com.wunding.learn.user.api.enums.IdentityCategoryEnum;
import com.wunding.learn.user.api.service.NewIdentityFeign;
import com.wunding.learn.user.api.service.OrgFeign;
import com.wunding.learn.user.api.service.ParaFeign;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import org.apache.commons.compress.archivers.ArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipArchiveInputStream;
import org.apache.commons.io.IOUtils;
import org.apache.commons.net.ftp.FTPClient;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.InjectMocks;
import org.mockito.MockedConstruction;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.util.ReflectionTestUtils;

@SuppressWarnings({"unckecked"})
@SpringBootTest(webEnvironment = WebEnvironment.NONE)
class FileLoadDataTest {

    private IDataSourceService dataSourceService;
    private OrgFeign orgFeign;
    private FileFeign fileFeign;
    private Task mockTask;
    private OrgDTO mockOrg;
    private NewIdentityFeign newIdentityFeign;
    private List<SyncPostDTO> mockPostList;
    @InjectMocks
    private FileLoadData fileLoadData;
    @MockBean
    private GlobalLocaleResolver globalLocaleResolver;
    @MockBean
    private ParaFeign paraFeign;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @MockBean
    private ITaskService taskService;
    @SpyBean
    private SyncPostRecordServiceImpl syncPostRecordService;
    @MockBean
    private SaveData saveData;
    @SpyBean
    private GroovyConvertData groovyConvertData;
    @SpyBean
    private IHrmOrgService hrmOrgService;
    @SpyBean
    private HrmUserServiceImpl hrmUserService;
    @TempDir
    Path tempDir;

    private static final String TEST_FILE_PATH = "testCase/人力数据.zip";


    @BeforeEach
    void setUp() {
        UserThreadContext.setTenantId("default");
        dataSourceService = mock(IDataSourceService.class);
        orgFeign = mock(OrgFeign.class);
        fileFeign = mock(FileFeign.class);
        newIdentityFeign = mock(NewIdentityFeign.class);
        fileLoadData = new FileLoadData(
            dataSourceService,
            Mockito.mock(ISourceRecordService.class),
            saveData,
            taskService,
            groovyConvertData,
            jdbcTemplate,
            hrmOrgService,
            hrmUserService,
            orgFeign,
            fileFeign,
            paraFeign,
            newIdentityFeign,
            syncPostRecordService
        );
        when(paraFeign.getDefaultPassword()).thenReturn("WunDing@1996");
        ReflectionTestUtils.setField(fileLoadData, "orgFeign", orgFeign);
        ReflectionTestUtils.setField(fileLoadData, "fileFeign", fileFeign);
        ReflectionTestUtils.setField(fileLoadData, "newIdentityFeign", newIdentityFeign);
        mockTask = new Task();
        mockTask.setId(1L);
        mockTask.setOrgId(UserConstant.ROOT_ORG_CODE);

        mockOrg = new OrgDTO().setLevelPath("/0/").setLevelPathName("/总部/").setOrgCode(UserConstant.ROOT_ORG_CODE)
            .setOrgName("总部");

        // 初始化一个包含两个 SyncPostDTO 的列表
        mockPostList = new ArrayList<>();
        SyncPostDTO post1 = new SyncPostDTO();
        post1.setName("工程师");
        post1.setCode("ENG001");

        SyncPostDTO post2 = new SyncPostDTO();
        post2.setName("经理");
        post2.setCode("MNG001");

        mockPostList.add(post1);
        mockPostList.add(post2);
    }


    @Test
    void testParseHeadersFromDDL_WithSchema_ShouldReturnTableNameAndFields() {
        // Arrange
        String ddl = """
            CREATE TABLE hr.employees (
            "id" INTEGER PRIMARY KEY,
            "name" VARCHAR(100),
            "salary" DECIMAL(10,2)
            );""";

        // Act
        List<String> result = FileLoadData.parseHeadersFromDDL(ddl);

        // Assert
        Assertions.assertNotNull(result);
        Assertions.assertEquals(4, result.size());
        Assertions.assertEquals("employees", result.getFirst());
        Assertions.assertTrue(result.contains("id"));
        Assertions.assertTrue(result.contains("name"));
        Assertions.assertTrue(result.contains("salary"));
    }

    @Test
    void testParseHeadersFromDDL_OnlyCreateTableStatement_ShouldReturnTableNameOnly() {
        // Arrange
        String ddl = "CREATE TABLE test_table (";

        // Act
        List<String> result = FileLoadData.parseHeadersFromDDL(ddl);

        // Assert
        Assertions.assertNotNull(result);
        Assertions.assertEquals(1, result.size());
        Assertions.assertEquals("test_table", result.getFirst());
    }

    @Test
    void testParseHeadersFromDDL_EmptyInput_ShouldReturnEmptyList() {
        // Arrange
        String ddl = "";

        // Act
        List<String> result = FileLoadData.parseHeadersFromDDL(ddl);

        // Assert
        Assertions.assertNotNull(result);
        Assertions.assertEquals(0, result.size());
    }

    @Test
    void testGetDataSourceData_WhenDataSourceIsNull_ShouldThrowBusinessException() {
        // Arrange
        Long dataSourceId = 1L;
        when(dataSourceService.getById(dataSourceId)).thenReturn(null);

        // Act & Assert
        Assertions.assertThrows(BusinessException.class, () -> fileLoadData.getData(dataSourceId, null));
    }

    @Test
    void testGetDataSourceData_WhenDataSourcePathIsNull_ShouldThrowBusinessException() {
        // Arrange
        Long dataSourceId = 1L;
        DataSource dataSource = new DataSource();
        dataSource.setId(dataSourceId);
        dataSource.setPath(null);
        when(dataSourceService.getById(dataSourceId)).thenReturn(dataSource);

        // Act & Assert
        Assertions.assertThrows(BusinessException.class, () -> fileLoadData.getData(dataSourceId, null));
    }

    @Test
    void testGetDataSourceData_WhenFileNotExists_ShouldThrowBusinessException() {
        // Arrange
        Long dataSourceId = 1L;
        DataSource dataSource = new DataSource();
        dataSource.setId(dataSourceId);
        dataSource.setPath("not_exist_file.gz");
        when(dataSourceService.getById(dataSourceId)).thenReturn(dataSource);

        // Act & Assert
        Assertions.assertThrows(BusinessException.class, () -> fileLoadData.getData(dataSourceId, null));
    }

    @Test
    void testGetDataSourceData_WhenIOExceptionOccurs_ShouldThrowBusinessException() throws Exception {
        // Arrange
        Long dataSourceId = 1L;
        DataSource dataSource = new DataSource();
        dataSource.setId(dataSourceId);
        dataSource.setPath(TEST_FILE_PATH);

        File mockFile = mock(File.class);
        when(mockFile.exists()).thenReturn(true);
        when(dataSourceService.getById(dataSourceId)).thenReturn(dataSource);

        FileInputStream fis = mock(FileInputStream.class);
        when(fis.read()).thenThrow(new IOException());

        ZipArchiveInputStream gzipIn = mock(ZipArchiveInputStream.class);

        ArchiveEntry entry = new ZipArchiveEntry("temp_expdata_hrm_zhuzhi.dat");
        when(gzipIn.getNextEntry()).thenReturn(entry, (ArchiveEntry) null);
        // 使用 PowerMock 或 Mockito-inline 来 mock IOUtils.toString 方法
        // 这里假设我们使用 Mockito-inline 的 mockStatic
        try (MockedStatic<IOUtils> utilities = Mockito.mockStatic(IOUtils.class)) {
            utilities.when(() -> IOUtils.toByteArray(any(InputStream.class)))
                .thenThrow(new IOException());

            // Act & Assert
            Assertions.assertThrows(BusinessException.class, () -> fileLoadData.getData(dataSourceId, null));
        }
    }

    @Test
    void testGetDataSourceData_WhenSuccessfullyReadAndParseFile_ShouldReturnEmptyList() {
        // Arrange
        Long dataSourceId = 1L;
        DataSource dataSource = new DataSource();
        dataSource.setId(dataSourceId);
        dataSource.setPath(TEST_FILE_PATH);

        File mockFile = mock(File.class);
        when(mockFile.exists()).thenReturn(true);
        when(dataSourceService.getById(dataSourceId)).thenReturn(dataSource);

        // Act
        // Assert
        fileLoadData.getData(dataSourceId, null);
        // ACT
        List<Map<String, Object>> queryResult = fileLoadData.query(
            "select * from hrm_user where C_DEPT_HID = 8e6f6a8a27d446d2a1c417fff718fe57 and C_EMPLOYEE_CODE = 20004505");

        System.out.println("queryResult:" + JsonUtil.objToJson(queryResult));
        Assertions.assertNotNull(queryResult);
        Assertions.assertFalse(queryResult.isEmpty());
        // ACT
        List<Map<String, Object>> queryResult2 = fileLoadData.query(
            "select * from hrm_user where C_DEPT_HID = faa1d7c8885122134b and C_EMPLOYEE_CODE = J000000377");

        System.out.println("queryResult2:" + JsonUtil.objToJson(queryResult2));
        Assertions.assertNotNull(queryResult2);
        Assertions.assertTrue(queryResult2.isEmpty());
    }

    @Test
    void testGetDataSourceData_WhenSuccessfullyReadAndParseFileToDb_ShouldReturnEmptyList() {
        // Arrange
        Long dataSourceId = 1L;
        DataSource dataSource = new DataSource();
        dataSource.setId(dataSourceId);
        dataSource.setPath(TEST_FILE_PATH);

        File mockFile = mock(File.class);
        when(mockFile.exists()).thenReturn(true);
        when(dataSourceService.getById(dataSourceId)).thenReturn(dataSource);

        // Act
        Assertions.assertDoesNotThrow(() -> fileLoadData.getData(dataSourceId, true));

        Integer count1 = Assertions.assertDoesNotThrow(
            () -> jdbcTemplate.queryForObject("select count(*) from  hrm_user where insert_date = ?",
                Integer.class,
                DateUtil.formatToStr(new Date(), DateHelper.YYYYMMDD)));
        Assertions.assertTrue(count1 >= 1);

        Integer count2 = Assertions.assertDoesNotThrow(
            () -> jdbcTemplate.queryForObject("select count(*) from hrm_org where insert_date = ?",
                Integer.class,
                DateUtil.formatToStr(new Date(), DateHelper.YYYYMMDD)));
        Assertions.assertTrue(count2 >= 1);
    }

    /**
     * TC01: taskId = null → taskService.getById 返回 null → 日志记录错误
     */
    @Test
    void testReceiveOrgData_TaskIsNull_ShouldLogError() {
        // Arrange
        doNothing().when(saveData).saveSyncOrg(anyLong(), anyList());

        // Act
        Assertions.assertThrows(BusinessException.class,
            () -> fileLoadData.receiveOrgData(Collections.emptyList(), null, null));

        // Assert
        verify(taskService, never()).getById(anyLong());
        verify(orgFeign, never()).getById(anyString());
        verify(groovyConvertData, never()).findChildren(anyList(), any(Queue.class), any(OrgDTO.class));
        verify(saveData, never()).saveSyncOrg(anyLong(), anyList());
    }

    /**
     * TC02: taskId存在但对应task为null → 日志记录错误
     */
    @Test
    void testReceiveOrgData_TaskNotExist_ShouldLogError() {
        // Arrange
        when(taskService.getById(1L)).thenReturn(null);

        // Act
        Assertions.assertThrows(BusinessException.class,
            () -> fileLoadData.receiveOrgData(Collections.emptyList(), 1L, null));

        // Assert
        verify(taskService, atLeast(1)).getById(1L);
        verify(orgFeign, never()).getById(anyString());
        verify(groovyConvertData, never()).findChildren(anyList(), any(Queue.class), any(OrgDTO.class));
        verify(saveData, never()).saveSyncOrg(anyLong(), anyList());
    }

    /**
     * TC03: convertOrgData返回空列表 → 不调用后续方法
     */
    @Test
    void testReceiveOrgData_EmptySyncOrgList_ShouldNotProcessFurther() {
        // Arrange
        doReturn(mockTask).when(taskService).getById(1L);
        when(orgFeign.getById(UserConstant.ROOT_ORG_CODE)).thenReturn(mockOrg);
        when(hrmOrgService.querySyncOrgDTO()).thenReturn(Collections.emptyList());
        ReflectionTestUtils.setField(groovyConvertData, "orgFeign", orgFeign);
        // Act
        fileLoadData.receiveOrgData(Collections.emptyList(), 1L, null);

        // Assert
        verify(taskService, atLeast(1)).getById(1L);
        verify(orgFeign, atLeast(1)).getById(UserConstant.ROOT_ORG_CODE);
        verify(groovyConvertData, never()).findChildren(anyList(), any(Queue.class), any(OrgDTO.class));
        verify(saveData, never()).saveSyncOrg(anyLong(), anyList());
    }

    /**
     * TC04: 所有步骤正常 → 成功调用findChildren和saveSyncOrg
     */
    @Test
    void testReceiveOrgData_NormalFlow_ShouldCallSaveSyncOrg() {
        when(taskService.getById(1L)).thenReturn(mockTask);
        when(orgFeign.getById(UserConstant.ROOT_ORG_CODE)).thenReturn(mockOrg);
        doNothing().when(saveData).saveSyncOrg(eq(1L), any());
        ReflectionTestUtils.setField(groovyConvertData, "orgFeign", orgFeign);
        // Act
        fileLoadData.receiveOrgData(Collections.emptyList(), 1L, null);

        // Assert
        verify(taskService, atLeast(1)).getById(1L);
        verify(orgFeign, atLeast(1)).getById(UserConstant.ROOT_ORG_CODE);
        verify(groovyConvertData).findChildren(any(), any(), any());
        verify(saveData).saveSyncOrg(eq(1L), any());
    }

    /**
     * TC01: 正常流程测试 - queryPostData 返回非空列表 - saveSyncPost 应被调用一次
     */
    @Test
    void testReceivePostData_WithValidData_ShouldCallSaveOnce() {
        // Arrange
        Long taskId = 1L;
        Long mappingId = 100L;

        when(taskService.getById(1L)).thenReturn(mockTask);
        when(hrmUserService.queryPostData()).thenReturn(mockPostList);

        // Act
        fileLoadData.receivePostData(Collections.emptyList(), taskId, mappingId);

        // Assert
        verify(saveData, times(1)).saveSyncPost(eq(taskId), anyList());
    }

    /**
     * TC02: 空数据测试 - queryPostData 返回空列表 - saveSyncPost 不应被调用
     */
    @Test
    void testReceivePostData_WithEmptyList_ShouldNotCallSave() {
        // Arrange
        Long taskId = 1L;
        Long mappingId = 100L;

        when(taskService.getById(1L)).thenReturn(mockTask);
        when(hrmUserService.queryPostData()).thenReturn(Collections.emptyList());

        // Act
        fileLoadData.receivePostData(Collections.emptyList(), taskId, mappingId);

        // Assert
        verify(saveData, never()).saveSyncPost(anyLong(), anyList());
    }

    /**
     * TC03: 异常处理测试 - queryPostData 抛出运行时异常 - 方法应抛出异常
     */
    @Test
    void testReceivePostData_WhenQueryThrowsException_ShouldThrow() {
        // Arrange
        Long taskId = 1L;
        Long mappingId = 100L;

        when(taskService.getById(1L)).thenReturn(mockTask);
        when(hrmUserService.queryPostData()).thenThrow(new BusinessException(ErrorNoEnum.ERR_SERVER));

        // Act & Assert
        Assertions.assertThrows(BusinessException.class, () ->
            fileLoadData.receivePostData(Collections.emptyList(), taskId, mappingId));
    }

    /**
     * TC04: 数据转换验证 - 检查 categoryId 和 directoryId 是否正确设置
     */
    @Test
    void testReceivePostData_DataConversion_ShouldSetCorrectFields() {
        // Arrange
        Long taskId = 1L;
        Long mappingId = 100L;

        when(taskService.getById(1L)).thenReturn(mockTask);
        when(hrmUserService.queryPostData()).thenReturn(mockPostList);

        // Act
        fileLoadData.receivePostData(Collections.emptyList(), taskId, mappingId);

        // Assert
        verify(saveData).saveSyncPost(eq(taskId), argThat(list -> {
            for (SyncPostDTO dto : list) {
                Assertions.assertEquals(IdentityCategoryEnum.POST.getCategoryId(), dto.getCategoryId());
                Assertions.assertEquals("7", dto.getDirectoryId());
            }
            return true;
        }));
    }

    /**
     * TC05: FTP配置为null - 应抛出FTP连接失败异常
     */
    @Test
    void testGetDataSourceData_WhenFtpConfigIsNull_ShouldThrowFtpConnectFailException() {
        // Arrange
        Long dataSourceId = 1L;
        DataSource dataSource = new DataSource();
        dataSource.setId(dataSourceId);
        dataSource.setPath(null); // FTP配置为null
        when(dataSourceService.getById(dataSourceId)).thenReturn(dataSource);

        // Act & Assert
        BusinessException exception = Assertions.assertThrows(BusinessException.class,
            () -> fileLoadData.getData(dataSourceId, null));

        // 验证异常类型
        Assertions.assertTrue(exception.getMessage().contains("数据源不存在") ||
            exception.getMessage().contains("FTP"));
    }

    /**
     * TC06: FTP配置JSON格式错误 - 应抛出异常
     */
    @Test
    void testGetDataSourceData_WhenFtpConfigJsonInvalid_ShouldThrowException() {
        // Arrange
        Long dataSourceId = 1L;
        DataSource dataSource = new DataSource();
        dataSource.setId(dataSourceId);
        dataSource.setPath("{invalid json}"); // 无效的JSON格式
        when(dataSourceService.getById(dataSourceId)).thenReturn(dataSource);

        // Act & Assert
        BusinessException exception = Assertions.assertThrows(BusinessException.class,
            () -> fileLoadData.getData(dataSourceId, null));

        // 验证异常信息
        Assertions.assertTrue(exception.getMessage().contains("数据源不存在"));
    }

    /**
     * TC07: FTP配置有效但连接失败 - 应记录警告并抛出异常
     */
    @Test
    void testGetDataSourceData_WhenFtpConnectionFails_ShouldLogWarningAndThrowException() {
        // Arrange
        Long dataSourceId = 1L;
        DataSource dataSource = new DataSource();
        dataSource.setId(dataSourceId);

        // 构造有效的FTP配置JSON
        String ftpConfig = """
            {
                "host": "invalid-host.com",
                "port": 21,
                "username": "testuser",
                "password": "testpass",
                "filePath": "/remote/path/file.zip"
            }
            """;
        dataSource.setPath(ftpConfig);
        when(dataSourceService.getById(dataSourceId)).thenReturn(dataSource);
        when(fileFeign.getCustomPhysicalPath("syncUser.zip", "sync"))
            .thenReturn("/tmp/syncUser.zip");

        // Act & Assert
        BusinessException exception = Assertions.assertThrows(BusinessException.class,
            () -> fileLoadData.getData(dataSourceId, null));

        // 验证异常信息
        Assertions.assertTrue(exception.getMessage().contains("数据源不存在"));
    }

    /**
     * TC08: FTP下载文件失败 - 应记录警告但继续处理
     */
    @Test
    void testGetDataSourceData_WhenFtpDownloadFails_ShouldLogWarningButContinue() {
        // Arrange
        Long dataSourceId = 1L;
        DataSource dataSource = new DataSource();
        dataSource.setId(dataSourceId);

        // 构造有效的FTP配置JSON，但指向不存在的文件
        String ftpConfig = """
            {
                "host": "*************",
                "port": 21,
                "username": "admin",
                "password": "admin",
                "filePath": "/data/not-exist-file.zip"
            }
            """;
        dataSource.setPath(ftpConfig);
        when(dataSourceService.getById(dataSourceId)).thenReturn(dataSource);
        Path downloadFile = tempDir.resolve("syncUser.zip");
        when(fileFeign.getCustomPhysicalPath("syncUser.zip", "sync"))
            .thenReturn(downloadFile.toString());

        // Act & Assert
        // 由于FTP连接会失败，最终会抛出数据源不存在异常
        BusinessException exception = Assertions.assertThrows(BusinessException.class,
            () -> fileLoadData.getData(dataSourceId, null));

        Assertions.assertTrue(exception.getMessage().contains("下载FTP文件失败"));
    }

    /**
     * TC09: FTP配置完整且成功下载文件 - 应正常处理
     */
    @Test
    void testGetDataSourceData_WhenFtpConfigValidAndDownloadSuccess_ShouldProcessNormally() {
        // Arrange
        Long dataSourceId = 1L;
        DataSource dataSource = new DataSource();
        dataSource.setId(dataSourceId);

        // 构造有效的FTP配置JSON
        String ftpConfig = """
            {
                "host": "*************",
                "port": 21,
                "username": "admin",
                "password": "admin",
                "filePath": "/data/hrm.zip"
            }
            """;
        // 注意文件名称如果是中文在FTP中容易乱码
        dataSource.setPath(ftpConfig);
        when(dataSourceService.getById(dataSourceId)).thenReturn(dataSource);

        // Mock fileFeign 返回一个实际存在的测试文件路径
        Path downloadFile = tempDir.resolve("syncUser.zip");
        when(fileFeign.getCustomPhysicalPath("syncUser.zip", "sync"))
            .thenReturn(downloadFile.toString());

        // Act & Assert
        // 由于实际的FTP连接会失败，但会fallback到使用TEST_FILE_PATH
        // 这个测试主要验证FTP逻辑分支被执行
        Assertions.assertDoesNotThrow(() -> fileLoadData.getData(dataSourceId, null));
    }

    /**
     * TC10: FTP配置中缺少必要字段 - 应抛出异常
     */
    @Test
    void testGetDataSourceData_WhenFtpConfigMissingFields_ShouldThrowException() {
        // Arrange
        Long dataSourceId = 1L;
        DataSource dataSource = new DataSource();
        dataSource.setId(dataSourceId);

        // 构造缺少字段的FTP配置JSON
        String ftpConfig = """
            {
                "host": "localhost"
            }
            """;
        dataSource.setPath(ftpConfig);
        when(dataSourceService.getById(dataSourceId)).thenReturn(dataSource);

        // Act & Assert
        BusinessException exception = Assertions.assertThrows(BusinessException.class,
            () -> fileLoadData.getData(dataSourceId, null));

        Assertions.assertTrue(exception.getMessage().contains("数据源不存在"));
    }

    /**
     * TC11: 使用MockedConstruction测试FTP成功连接和下载场景
     */
    @Test
    void testGetDataSourceData_WhenFtpSuccessfullyConnectsAndDownloads_ShouldProcessFile() throws IOException {
        // Arrange
        Long dataSourceId = 1L;
        DataSource dataSource = new DataSource();
        dataSource.setId(dataSourceId);

        String ftpConfig = """
            {
                "host": "localhost",
                "port": 21,
                "username": "testuser",
                "password": "testpass",
                "filePath": "/remote/path/file.zip"
            }
            """;
        dataSource.setPath(ftpConfig);
        when(dataSourceService.getById(dataSourceId)).thenReturn(dataSource);
        when(fileFeign.getCustomPhysicalPath("syncUser.zip", "sync"))
            .thenReturn(TEST_FILE_PATH);

        // 使用MockedConstruction来mock FTPClient和FTPUtils的创建
        try (MockedConstruction<FTPClient> mockedFtpClient = Mockito.mockConstruction(FTPClient.class);
            MockedConstruction<FTPUtils> mockedFtpUtils = Mockito.mockConstruction(FTPUtils.class,
                (mock, context) -> {
                    // Mock FTPUtils的方法
                    doNothing().when(mock).connect();
                    when(mock.downloadFile(anyString(), anyString())).thenReturn(true);
                    doNothing().when(mock).disconnect();
                })) {

            // Act
            Assertions.assertDoesNotThrow(() -> fileLoadData.getData(dataSourceId, null));

            // Assert
            // 验证FTPClient被创建
            Assertions.assertEquals(1, mockedFtpClient.constructed().size());

            // 验证FTPUtils被创建并调用了相关方法
            Assertions.assertEquals(1, mockedFtpUtils.constructed().size());
            FTPUtils ftpUtils = mockedFtpUtils.constructed().getFirst();
            verify(ftpUtils, times(1)).connect();
            verify(ftpUtils, times(1)).downloadFile(anyString(), anyString());
            verify(ftpUtils, times(1)).disconnect();
        }
    }

}
