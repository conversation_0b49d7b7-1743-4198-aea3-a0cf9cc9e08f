package com.wunding.learn.sync.service.job;

import static com.wunding.learn.common.util.string.StringUtil.newId;
import static org.mockito.Mockito.when;

import com.wunding.learn.common.i18n.config.GlobalLocaleResolver;
import com.wunding.learn.common.util.date.DateUtil;
import com.wunding.learn.file.api.service.FileFeign;
import com.xxl.job.core.biz.model.ReturnT;
import java.nio.file.Path;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
class JobTest {

    @Autowired
    private Job job;
    @MockBean
    FileFeign fileFeign;
    @MockBean
    private GlobalLocaleResolver globalLocaleResolver;
    @TempDir
    Path tempDir;

    @Test
    void testExecuteWithDifferentDataSourceTypes() {
        String param = "dataSourceId:5,mappingId:2,taskId:1,tenantId:default";

        MockedStatic<MDC> mdcMock = Mockito.mockStatic(MDC.class);
        mdcMock.when(() -> MDC.get("batchId")).thenReturn(DateUtil.getYmdhmsSStr());
        mdcMock.when(() -> MDC.get("traceId")).thenReturn(newId());
        Path path = tempDir.resolve("syncUser.zip");
        when(fileFeign.getCustomPhysicalPath("syncUser.zip", "sync"))
            .thenReturn(path.toString());

        // 执行
        ReturnT<String> result = job.execute(param);
        Assertions.assertEquals(ReturnT.SUCCESS_CODE, result.getCode());
    }
}
