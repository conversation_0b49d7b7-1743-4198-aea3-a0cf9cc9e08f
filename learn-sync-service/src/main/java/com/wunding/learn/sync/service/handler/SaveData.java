package com.wunding.learn.sync.service.handler;


import com.wunding.learn.user.api.dto.sync.SyncOrgDTO;
import com.wunding.learn.user.api.dto.sync.SyncPostDTO;
import com.wunding.learn.user.api.dto.sync.SyncUserDTO;
import java.util.List;


/**
 * <AUTHOR>
 */
public interface SaveData {

    /**
     * 保存职级数据
     *
     * @param list
     */
    void saveJobLevel(Long taskId, List<String> list);

    /**
     * 保存组织数据
     * @param list
     */
    void saveSyncOrg(Long taskId,List<SyncOrgDTO> list);

    /**
     * 保存用户数据
     * @param list
     */
    void saveSyncUser(Long taskId,List<SyncUserDTO> list);

    /**
     * 保存岗位数据
     * @param list
     */
    void saveSyncPost(Long taskId,List<SyncPostDTO> list);

}
