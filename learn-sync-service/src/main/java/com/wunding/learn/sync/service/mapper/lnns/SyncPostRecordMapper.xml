<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.sync.service.mapper.lnns.SyncPostRecordMapper">

    <!-- 开启二级缓存 -->
    <!--
<cache type="com.wunding.learn.common.mybatis.cache.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.sync.service.mapper.lnns.SyncPostRecordMapper"/>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, create_time, create_by, update_by, update_time, is_del
    </sql>

</mapper>
