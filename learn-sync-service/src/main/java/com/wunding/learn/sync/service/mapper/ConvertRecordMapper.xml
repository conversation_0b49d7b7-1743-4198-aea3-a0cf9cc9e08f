<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.sync.service.mapper.ConvertRecordMapper">

        <!-- 开启二级缓存 -->
        <!--
    <cache type="com.wunding.learn.common.mybatis.cache.MyBatisPlusRedisCache"/>
    -->

        <!-- 使用缓存 -->
        <cache-ref namespace="com.wunding.learn.sync.service.mapper.ConvertRecordMapper"/>

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.wunding.learn.sync.service.model.ConvertRecord">
            <!--@Table sync_convert_record-->
                    <id column="id" jdbcType="BIGINT" property="id"/>
                    <result column="task_id" jdbcType="BIGINT"
                            property="taskId"/>
                    <result column="trace_id" jdbcType="VARCHAR"
                            property="traceId"/>
                    <result column="before_content" jdbcType="LONGVARCHAR"
                            property="beforeContent"/>
                    <result column="after_content" jdbcType="LONGVARCHAR"
                            property="afterContent"/>
                    <result column="script_id" jdbcType="BIGINT"
                            property="scriptId"/>
                    <result column="status" jdbcType="VARCHAR"
                            property="status"/>
                    <result column="error_msg" jdbcType="VARCHAR"
                            property="errorMsg"/>
                    <result column="is_del" jdbcType="INTEGER"
                            property="isDel"/>
                    <result column="create_by" jdbcType="VARCHAR"
                            property="createBy"/>
                    <result column="create_time" jdbcType="TIMESTAMP"
                            property="createTime"/>
                    <result column="update_by" jdbcType="VARCHAR"
                            property="updateBy"/>
                    <result column="update_time" jdbcType="TIMESTAMP"
                            property="updateTime"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, task_id, trace_id, before_content, after_content, script_id, status, error_msg, is_del, create_by, create_time, update_by, update_time
        </sql>

</mapper>
