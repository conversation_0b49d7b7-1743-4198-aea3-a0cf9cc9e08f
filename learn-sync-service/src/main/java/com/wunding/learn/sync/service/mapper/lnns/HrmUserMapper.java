package com.wunding.learn.sync.service.mapper.lnns;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import com.wunding.learn.sync.service.model.lnns.HrmUser;
import com.wunding.learn.user.api.dto.sync.SyncPostDTO;
import com.wunding.learn.user.api.dto.sync.SyncUserDTO;
import java.util.List;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 员工基本信息 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2025-05-16
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")})
public interface HrmUserMapper extends BaseMapper<HrmUser> {

    /**
     * 查询同步用户dto
     *
     * @return {@link List }<{@link SyncUserDTO }>
     */
    List<SyncUserDTO> querySyncUserDTO();

    /**
     * 查询岗位数据
     *
     * @return {@link List }<{@link SyncPostDTO }>
     */
    List<SyncPostDTO> queryPostData();

    /**
     * 查询用户职级dto
     *
     * @return {@link List }<{@link String }>
     */
    List<String> queryJobLevelUserDTO();
}
