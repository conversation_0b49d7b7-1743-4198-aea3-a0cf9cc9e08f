package com.wunding.learn.sync.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.sync.service.admin.dto.TargetFieldDTO;
import com.wunding.learn.sync.service.mapper.TargetFieldMapper;
import com.wunding.learn.sync.service.model.TargetField;
import com.wunding.learn.sync.service.service.ITargetFieldService;
import java.util.ArrayList;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * 目标字段服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@Service
public class TargetFieldServiceImpl extends ServiceImpl<TargetFieldMapper, TargetField> implements ITargetFieldService {

    @Override
    public List<TargetFieldDTO> list(Long targetId) {

        List<TargetFieldDTO> list = new ArrayList<>();
        LambdaQueryWrapper<TargetField> query = new LambdaQueryWrapper<>();
        query.eq(TargetField::getTargetId, targetId);
        for (TargetField targetField : list(query)) {
            TargetFieldDTO dto = new TargetFieldDTO();
            dto.setId(targetField.getTargetId());
            dto.setTargetId(targetId);
            dto.setFieldName(targetField.getName());
            dto.setFieldType(targetField.getType());
            dto.setDescription(targetField.getFieldDesc());
            list.add(dto);
        }

        return list;
    }
}
