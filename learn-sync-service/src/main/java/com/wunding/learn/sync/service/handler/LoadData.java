package com.wunding.learn.sync.service.handler;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface LoadData {

    Map<String, Object> contextMap = new HashMap<>();

    /**
     * 加载数据
     * @param taskId 任务id
     * @param dataSourceId 数据源id
     * @param mappingId 映射id
     */
    void loadData(Long taskId,Long dataSourceId,Long mappingId);

    /**
     * 接收组织数据
     * @param data 接收的数据
     */
    void receiveOrgData(List<Map<String, Object>> data,Long taskId,Long mappingId);

    /**
     * 接收岗位数据
     * @param data 接收的数据
     */
    void receivePostData(List<Map<String, Object>> data,Long taskId,Long mappingId);

    /**
     * 接收用户数据
     * @param data 接收的数据
     */
    void receiveUserData(List<Map<String, Object>> data,Long taskId,Long mappingId);

    /**
     * 职级数据
     *
     * @param data 岗位级别数据
     */
    default void receiveJobLevelData(List<Map<String, Object>> data, Long taskId, Long mappingId) {
    }


}
