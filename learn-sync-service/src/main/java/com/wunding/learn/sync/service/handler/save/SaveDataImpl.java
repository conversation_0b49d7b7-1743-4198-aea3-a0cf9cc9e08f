package com.wunding.learn.sync.service.handler.save;


import com.wunding.learn.common.constant.other.Oauth2EntryTypeEnum;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.sync.service.handler.SaveData;
import com.wunding.learn.sync.service.service.ITaskRecordService;
import com.wunding.learn.user.api.dto.sync.SyncOrgDTO;
import com.wunding.learn.user.api.dto.sync.SyncPostDTO;
import com.wunding.learn.user.api.dto.sync.SyncUserDTO;
import com.wunding.learn.user.api.service.SyncDataFeign;
import feign.Request;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 数据保存实现类
 * <AUTHOR>
 */
@Service
@Slf4j

public class SaveDataImpl implements SaveData {

    @Resource
    private  SyncDataFeign syncDataFeign;

    @Resource
    private  ITaskRecordService taskRecordService;


    /**
     * 保存同步组织数据
     *
     * @param list 组织数据列表
     */
    @Override
    public void saveJobLevel(Long taskId, List<String> list) {
        System.err.println(JsonUtil.objToJson(list));
        Request.Options options = new Request.Options(60L, TimeUnit.SECONDS, 2, TimeUnit.HOURS, true);
        syncDataFeign.syncJobLevel(list, options);
    }

    /**
     * 保存同步组织数据
     * @param list 组织数据列表
     */
    @Override
    public void saveSyncOrg(Long taskId,List<SyncOrgDTO> list) {
        System.err.println(JsonUtil.objToJson(list));
        Request.Options options = new Request.Options(60L, TimeUnit.SECONDS, 2, TimeUnit.HOURS, true);
        syncDataFeign.syncOrgData(list, Oauth2EntryTypeEnum.CUSTOM.getType(),options);
    }

    /**
     * 保存同步用户数据
     * @param list 用户数据列表
     */
    @Override
    public void saveSyncUser(Long taskId,List<SyncUserDTO> list) {
        System.err.println(JsonUtil.objToJson(list));
        Request.Options options = new Request.Options(60L, TimeUnit.SECONDS, 4, TimeUnit.HOURS, true);
        syncDataFeign.syncUserData(list, Oauth2EntryTypeEnum.CUSTOM.getType(),options);
    }

    /**
     * 保存同步岗位数据
     * @param list 岗位数据列表
     */
    @Override
    public void saveSyncPost(Long taskId,List<SyncPostDTO> list) {
        System.err.println(JsonUtil.objToJson(list));
        Request.Options options = new Request.Options(60L, TimeUnit.SECONDS, 4, TimeUnit.HOURS, true);
        syncDataFeign.syncIdentityData(list, Oauth2EntryTypeEnum.CUSTOM.getType(),options);
    }
    

}
