<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.market.service.mapper.SignMapper">
    <!-- 开启二级缓存 -->
    <!--
<cache type="com.wunding.learn.common.mybatis.cache.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.market.service.mapper.SignMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.market.service.model.Sign">
        <!--@Table sign-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="type" jdbcType="TINYINT"
          property="type"/>
        <result column="sign_name" jdbcType="VARCHAR"
          property="signName"/>
        <result column="sign_type" jdbcType="VARCHAR"
          property="signType"/>
        <result column="sign_code" jdbcType="VARCHAR"
          property="signCode"/>
        <result column="address" jdbcType="VARCHAR"
          property="address"/>
        <result column="start_time" jdbcType="TIMESTAMP"
          property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP"
          property="endTime"/>
        <result column="is_publish" jdbcType="TINYINT"
          property="isPublish"/>
        <result column="publish_by" jdbcType="VARCHAR"
          property="publishBy"/>
        <result column="publish_time" jdbcType="TIMESTAMP"
          property="publishTime"/>
        <result column="is_train" jdbcType="TINYINT"
          property="isTrain"/>
        <result column="is_available" jdbcType="TINYINT"
          property="isAvailable"/>
        <result column="is_del" jdbcType="TINYINT"
          property="isDel"/>
        <result column="view_type" jdbcType="INTEGER"
          property="viewType"/>
        <result column="create_by" jdbcType="VARCHAR"
          property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP"
          property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR"
          property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP"
          property="updateTime"/>
        <result column="org_id" jdbcType="VARCHAR"
          property="orgId"/>
        <result column="customer_id" jdbcType="VARCHAR"
          property="customerId"/>
        <result column="qr_flag" jdbcType="TINYINT"
          property="qrFlag"/>
        <result column="phase_id" jdbcType="VARCHAR"
          property="phaseId"/>
        <result column="project_id" jdbcType="VARCHAR"
          property="projectId"/>
        <result column="train_duration" jdbcType="BIGINT"
          property="trainDuration"/>
        <result column="task_id" jdbcType="VARCHAR"
          property="taskId"/>
        <result column="sign_num" jdbcType="BIGINT"
          property="signNum"/>
        <result column="longitude" jdbcType="DECIMAL"
          property="longitude"/>
        <result column="latitude" jdbcType="DECIMAL"
          property="latitude"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        type,
        sign_name,
        sign_type,
        sign_code,
        address,
        start_time,
        end_time,
        is_publish,
        publish_by,
        publish_time,
        is_train,
        is_available,
        is_del,
        view_type,
        create_by,
        create_time,
        update_by,
        update_time,
        org_id,
        customer_id,
        qr_flag,
        phase_id,
        project_id,
        train_duration,
        task_id,
        sign_num,
        longitude,
        latitude
    </sql>

    <select id="selectListByPage" resultType="com.wunding.learn.market.service.model.Sign"
      parameterType="com.wunding.learn.market.api.query.SignQuery" useCache="false">
        select a.*
        from sign a
                 inner join sys_org o on o.id = a.org_id
        where a.is_del = 0
        <if test="params.signName != null and params.signName != ''">
            and instr(a.sign_name, #{params.signName}) > 0
        </if>
        <if test="params.beginTime != null">
            and a.publish_time >= #{params.beginTime}
        </if>
        <if test="params.endTime != null">
            and #{params.endTime} >= a.publish_time
        </if>
        <if test="params.isPublish != null">
            and a.is_publish = #{params.isPublish}
        </if>
        <if test="params.managerAreaOrgIds != null and params.managerAreaOrgIds.size() > 0">
            <foreach collection="params.managerAreaOrgIds" item="item" open="and (" separator="or">
                o.level_path like concat(#{item}, '%')
            </foreach>
            or (a.org_id = #{params.currentOrgId} or a.create_by = #{params.currentUserId})
                )
        </if>
        <if test="params.filterTaskData != null and params.filterTaskData">
            and a.task_id = ''
        </if>
        <choose>
            <when test="params.projectId != null and params.projectId != ''">
                and a.project_id = #{params.projectId}
            </when>
            <otherwise>
                and a.project_id = ''
            </otherwise>
        </choose>
        order by a.create_time desc
    </select>

    <select id="selectListByClientPage" resultType="com.wunding.learn.market.api.dto.SignListClientDTO"
      parameterType="com.wunding.learn.market.api.query.SignClientQuery" useCache="false">
        select s.id,
               s.type,
               s.sign_name,
               s.start_time,
               s.end_time,
               ss.sign_in_time
        from sign s
                 inner join w_resource_view_limit r on s.id = r.resource_id and r.resource_type = 'signViewLimit'
                 left join w_view_limit_user u on r.view_limit_id = u.view_limit_id
                 left join sign_user ss on r.resource_id = ss.sign_id and ss.user_id = u.user_id and ss.is_del = 0
        where u.user_id = #{params.currentUserId}
          and s.is_del = 0
          and s.is_publish = 1
          and s.is_train = 0
        <if test="params.dataType != null and params.dataType == 1">
            and ss.is_sign is null
            and s.end_time >= now()
        </if>
        <if test="params.dataType != null and params.dataType == 2">
            and ss.is_sign = 1
        </if>
        order by s.publish_time desc
    </select>

    <select id="selectProjectListByClientPage" parameterType="com.wunding.learn.market.api.query.SignClientQuery"
      resultType="com.wunding.learn.market.api.dto.SignListClientDTO" useCache="false">
        select s.id,
               s.type,
               s.sign_name,
               s.start_time,
               s.end_time,
               ss.is_sign,
               ss.sign_in_time,
               s.sign_type,
               s.qr_flag
        from sign s
                 left join sign_user ss
                           on s.id = ss.sign_id and ss.user_id = #{params.currentUserId} and ss.is_del = 0
        where s.is_del = 0
          and s.is_publish = 1
          and s.project_id = #{params.projectId}
        order by ss.is_sign asc, s.publish_time desc
    </select>

        <select id="getMenuTaskCount" resultType="java.lang.Integer" useCache="false">
            select count(1)
            from sign s
            inner join w_resource_view_limit r on s.id = r.resource_id
            and r.resource_type = 'signViewLimit'
            left join w_view_limit_user u on r.view_limit_id = u.view_limit_id
            left join sign_user ss on r.resource_id = ss.sign_id
            and ss.user_id = u.user_id
            where u.user_id = #{userId}
            and s.is_del = 0
            and s.is_publish = 1
            <if test="status != null">
                <if test="status == 0">
                    and ss.is_sign is null
                    <![CDATA[and s.start_time <= now() and s.end_time >= now()]]>
                </if>
                <if test="status == 1">
                    and ss.is_sign = 1
                </if>
                <if test="status == 2">
                    and ss.is_sign is null
                    <![CDATA[and s.end_time <= now()]]>
                </if>
            </if>
        </select>

        <select id="getMenuTask" resultType="com.wunding.learn.market.api.dto.ProjectSignListDTO" useCache="false">
            select s.id,
            s.sign_name,
            s.start_time,
            s.end_time,
            ss.sign_in_time
            from sign s
            inner join w_resource_view_limit r on s.id = r.resource_id
            and r.resource_type = 'signViewLimit'
            left join w_view_limit_user u on r.view_limit_id = u.view_limit_id
            left join sign_user ss on r.resource_id = ss.sign_id
            and ss.user_id = u.user_id
            where u.user_id = #{userId}
            and s.is_del = 0
            and s.is_publish = 1
            <if test="status != null">
                <if test="status == 0">
                    and ss.is_sign is null
                    <![CDATA[and s.start_time <= now() and s.end_time >= now()]]>
                </if>
                <if test="status == 1">
                    and ss.is_sign = 1
                </if>
                <if test="status == 2">
                    and ss.is_sign is null
                    <![CDATA[and s.end_time <= now()]]>
                </if>
            </if>
        </select>

    <!--嵌套子查询-待优化  去重计数问题-->
    <select id="selectSignNumByProjectId"
      resultType="com.wunding.learn.market.api.dto.SignNumDTO">
        select s.project_id                                                                               projectId,
               count(distinct su.user_id)                                                                 signUserNum,
               (select count(a.user_id)
                from (select su.user_id
                      from sign_user su
                               inner join sign ss on su.sign_id = ss.id
                      where ss.is_del = 0
                        and ss.project_id = s.project_id
                        and su.is_del = 0
                        and su.is_sign = 1
                      group by su.user_id
                      having count(su.sign_id) >=
                             (select count(s1.id) from sign s1 where s1.project_id = s.project_id)) a) as userAllSignNum
        from sign s
                 left join sign_user su on s.id = su.sign_id
        where s.is_del = 0
          and s.is_publish = 1
          and su.is_del = 0
          and su.is_sign = 1
          and s.project_id in
        <foreach collection="projectIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        group by s.project_id
    </select>

    <select id="getProjectSign" resultType="com.wunding.learn.market.api.dto.ProjectSignTaskDTO" useCache="false">
        select s.id                                                                               resourceId,
               s.phase_Id                                                                         phaseId,
               s.sign_name                                                                        taskName,
               'sign'                                                                             taskType,
               s.id                                                                               taskContent,
               s.start_time                                                                       startTime,
               s.end_time                                                                         endTime,
               case when s.start_time > now() then 0 when now() > s.end_time then 2 else 1 end as status,
               case
                   when (select su.id
                         from sign_user su
                         where su.is_del = 0
                           and su.sign_id = s.id
                           and su.is_sign = 1
                           and su.user_id =
                               #{params.userId}) is null then 0
                   else 2 end                                                                  as userStatus
        from sign s
                 left join sign_user su on s.id = su.sign_id and su.is_sign = 1 and su.user_id = #{params.userId}
        where
            s.is_del = 0
              and s.is_publish = 1
              and s.is_train = 1
            <if test="params.isComplete == 1">
                and su.id is not null
            </if>
            <if test="params.isComplete == 0">
                and su.id is null
            </if>
            <if test="params.isExpired != null">
                <if test="params.isExpired == 1">
                    and now() > s.end_time
                </if>
                <if test="params.isExpired == 0">
                    and s.end_time > now()
                </if>
            </if>
        and s.is_del = 0 and s.is_publish = 1 and s.is_train = 1
        and s.id in
        <foreach collection="params.signIdList" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        <if test="params.stageId != null and params.stageId != ''">
            and s.phase_Id = #{params.stageId}
        </if>
    </select>

    <select id="getSignNum" resultType="java.lang.Integer" useCache="false">
        select count(su.id)
        from sign s
                 left join sign_user su on s.id = su.sign_id
        where s.id in
        <foreach collection="signIds" item="signId" open="(" close=")" separator=",">
            #{signId}
        </foreach>
        and su.user_id = #{userId}
        and su.is_sign = 1
        and s.is_publish = 1
        and s.is_del = 0
    </select>

        <!-- 查询一段时间内的签到列表 -->
        <select id="selectProTaskSignListByTime"
          resultType="com.wunding.learn.common.dto.LearningCalendarTaskDTO" useCache="false">
            select
            s.id as id,
            s.id as activityId,
            s.sign_name as title,
            'sign' as flag,
            s.start_time startTime,
            s.end_time endTime,
            0 isProjectTask,
            case when s.start_time > now() then 0 WHEN now() > s.end_time then 2 else 1 end as status,
            case when (select su.id from sign_user su where su.sign_id = s.id and su.is_sign = 1 and su.user_id = #{userId})
            is null then 0 else 2 end as userStatus,
            0 isOperation
            from sign s
            inner join w_resource_view_limit r on s.id = r.resource_id and r.resource_type = 'signViewLimit'
            left join w_view_limit_user u on r.view_limit_id = u.view_limit_id
            where u.user_id = #{userId}
            and s.id not in (select su.sign_id from sign_user su inner join sign s on s.id = su.sign_id and su.user_id =#{userId})
            and s.is_del = 0
            and s.is_publish = 1
            and s.project_id is not null
            <if test="null == whichDay">
                and ((date_format(s.start_time, '%Y-%m-%d') >= date_format(#{startTime},'%Y-%m-%d') and
                date_format(s.start_time, '%Y-%m-%d') &lt;=
                date_format(#{endTime},'%Y-%m-%d')) or
                (date_format(s.end_time, '%Y-%m-%d') >= date_format(#{startTime},'%Y-%m-%d') and date_format(s.end_time,
                '%Y-%m-%d') &lt;= date_format(#{endTime},'%Y-%m-%d')) or
                (date_format(s.start_time, '%Y-%m-%d') &lt;= date_format(#{startTime},'%Y-%m-%d') and
                date_format(s.end_time, '%Y-%m-%d') >= date_format(#{endTime},'%Y-%m-%d')))
            </if>
            <if test="null != whichDay and null == today">
                and #{whichDay} = date_format(s.start_time, '%Y-%m-%d')
            </if>
            <if test="null != whichDay and null != today">
                and #{whichDay} >= date_format(s.start_time, '%Y-%m-%d') and #{whichDay} &lt;= date_format(s.end_time,
                '%Y-%m-%d')
            </if>
            order by s.start_time
        </select>

    <select id="getSignListByIdList" resultType="com.wunding.learn.market.api.dto.SignPageDTO">
        select
        <include refid="Base_Column_List">
        </include>
        from sign
        where id in
        <foreach collection="signIdList" separator="," item="signId" open="(" close=")">
            #{signId}
        </foreach>
        and is_del = 0
    </select>

    <select id="getSignCashById" resultType="com.wunding.learn.market.api.dto.SignClientDTO"
      useCache="false">
        select s.id,
               s.sign_name,
               s.start_time,
               s.end_time
        from sign s
        where id = #{id}
    </select>

        <!--关联下发范围查询签到记录-->
        <select id="countSignNumRelateViewLimit" resultType="java.lang.Integer">
            select count(1)
            from w_resource_view_limit r
            left join w_view_limit_user u on r.view_limit_id = u.view_limit_id
            left join sign_user ss on r.resource_id = ss.sign_id and ss.user_id = u.user_id
            where r.resource_id = #{signId}
            and r.resource_type = 'signViewLimit'
            <if test="isSign != null and isSign == 0">
                and ss.is_sign is null
            </if>
            <if test="isSign != null and isSign == 1">
                and ss.is_sign = 1
            </if>
        </select>

    <update id="updateSignNum" parameterType="string">
        update sign a
        set a.sign_num = (select count(*)
                          from sign_user b
                          where b.sign_id = #{id}
                            and b.is_sign = 1
                            and b.is_del = 0)
        where a.id = #{id}
    </update>

    <select id="queryUserIds" resultType="java.lang.String" useCache="false">
        select wvlu.user_id
        from w_resource_view_limit wrvl
                 inner join w_view_limit_user wvlu on wrvl.view_limit_id = wvlu.view_limit_id
                 inner join sys_user su on wvlu.user_id = su.id
        where wrvl.resource_id = #{signId}
          and wrvl.resource_type = 'signViewLimit'
          and su.is_del = 0
    </select>

    <select id="getSignIsDelById" parameterType="java.lang.String" resultType="com.wunding.learn.common.dto.ResourceDeleteInfoDTO">
        select id,
               is_del isDel,
               update_time updateTime,
               update_by updateBy
        from sign
        where id = #{id}
    </select>
</mapper>
