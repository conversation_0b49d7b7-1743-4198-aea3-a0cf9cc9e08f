package com.wunding.learn.market.service.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/12/12 14:05
 */
@Data
@Schema(name = "VoteContentVO", description = "投票内容vo对象")
public class VoteContentVO {

    /**
     * 主键id
     */
    @Schema(description = "主键id")
    private String id;

    /**
     * 投票的主键id
     */
    @Schema(description = "投票的主键id")
    private String voteId;

    /**
     * 编号
     */
    @Schema(description = "编号")
    private Integer optionCode;

    /**
     * 编号字符串，不足三位前面补0
     */
    @Schema(description = "编号")
    private String optionCodeStr;

    /**
     * 投票标题
     */
    @Schema(description = "投票标题")
    private String title;

    /**
     * 票数
     */
    @Schema(description = "票数")
    private String ticketNum;

    /**
     * 图片
     */
    @Schema(description = "图片")
    private String imageExist;

    /**
     * 课件资料
     */
    @Schema(description = "课件资料")
    private String coursewareUrl;

    /**
     * 课件库ID
     */
    @Schema(description = "课件库ID")
    private String libId;

    @Schema(description = "资源库类型：0-上传文件 1-课件库 2-案例库")
    private Integer libType;

    /**
     * 是否入库
     */
    @Schema(description = "是否入库")
    private String isStorage;

    /**
     * 当前总计排名
     */
    @Schema(description = "当前总计排名")
    private String rank;

    @Schema(description = "上传课件ID", hidden = true)
    private String cwId;

    @Schema(description = "转换状态  1-转码中 2-转码成功 3-转码失败 4-排队中")
    private Integer transformStatus;
}
