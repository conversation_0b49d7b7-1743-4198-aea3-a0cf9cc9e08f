package com.wunding.learn.market.service.client.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Author: aixinrong
 * @Date: 2022/10/27 15:21
 */
@Data
@Schema(name = "AdvertClientDTO", description = "app广告客户端对象")
public class AdvertClientDTO {

    @Schema(description = "广告id", hidden = true)
    private String id;

    @Schema(description = "广告图片  用于预览")
    private String advertImgUrl;

    @Schema(description = "停留时长")
    private Integer stayTime;
}
