package com.wunding.learn.common.aop.reentry.aspect;

import com.wunding.learn.common.aop.reentry.annotation.RequestRepeatIntercept;
import com.wunding.learn.common.context.user.UserThreadContext;
import java.util.Optional;
import jakarta.annotation.Resource;
import lombok.extern.log4j.Log4j2;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/2/20
 */
@Aspect
@Component
@Log4j2
public class RequestRepeatAspect {

    @Resource
    RedissonClient redissonClient;

    @Around("@annotation(requestRepeatIntercept)")
    public void intercept(ProceedingJoinPoint joinPoint, RequestRepeatIntercept requestRepeatIntercept) {
        Object[] args = joinPoint.getArgs();
        String api = requestRepeatIntercept.value();
        String userId = args[0].toString();
        String projectId = args[1].toString();
        String lockKey = UserThreadContext.getTenantId() + ":" + api + ":" + userId + ":" + projectId;
        RLock lock = redissonClient.getLock(lockKey);
        Object object = null;
        try {
            lock.lock();
            object = joinPoint.proceed();
        } catch (Throwable e) {
            log.error("requestRepeatIntercept error",e);
        } finally {
            if (lock.isLocked()) {
                try {
                    lock.unlock();
                } catch (Exception e) {
                    log.error("unlock error : ", e);
                }
            }
        }
        Optional.ofNullable(object).ifPresent(o -> log.info("result : " + o.toString()));
    }
}
