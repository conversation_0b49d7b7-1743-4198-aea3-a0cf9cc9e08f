package com.wunding.learn.reading.service.client.query;

import com.wunding.learn.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 图书心得评论列表
 *
 * <AUTHOR>
 * @date 2022/09/06
 */
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
public class BookExpCommentQuery extends BaseEntity {

    private static final long serialVersionUID = 2492069849714510586L;

    /**
     * 心得id
     */
    @Parameter(description = "心得id")
    @NotBlank(message = "心得id不能为空")
    private String expId;


    /**
     * 心得搜索关键字
     */
    @Parameter(description = "心得搜索关键字")
    private String searchKey;

}
