package com.wunding.learn.reading.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.reading.service.admin.dto.ReadingSignDTO;
import com.wunding.learn.reading.service.admin.dto.ReadingSignDetailDTO;
import com.wunding.learn.reading.service.admin.query.ReadingSignDetailQuery;
import com.wunding.learn.reading.service.admin.query.ReadingSignQuery;
import com.wunding.learn.reading.service.client.dto.ReadingSignCalendarDTO;
import com.wunding.learn.reading.service.client.query.ReadingSignCalendarQuery;
import com.wunding.learn.reading.service.model.UserReadingSign;
import java.util.List;
import org.springframework.scheduling.annotation.Async;

/**
 * <p> 共读打卡记录表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">chh</a>
 * @since 2022-09-05
 */
public interface IUserReadingSignService extends IService<UserReadingSign> {


    /**
     * 分页获取打卡记录列表
     *
     * @param readingSignQuery 打卡记录查询对象
     * @return {@link PageInfo}<{@link ReadingSignDTO}>
     */
    PageInfo<ReadingSignDTO> findSignDataByPage(ReadingSignQuery readingSignQuery);

    /**
     * 分页获取打卡详情
     *
     * @param readingSignDetailQuery 阅读签署详细查询
     * @return {@link PageInfo}<{@link ReadingSignDTO}>
     */
    PageInfo<ReadingSignDetailDTO> findSignDetailByPage(ReadingSignDetailQuery readingSignDetailQuery);

    /**
     * 获取签到日历
     *
     * @param readingSignCalendarQuery
     * @return
     */
    List<ReadingSignCalendarDTO> calendarSign(ReadingSignCalendarQuery readingSignCalendarQuery);

    /**
     * 导出打卡列表
     */
    @Async
    void exportData(ReadingSignQuery query);


    /**
     * 查询当天是否签到
     *
     * @param userId
     * @param activityId
     * @return
     */
    UserReadingSign getOneByNow(String userId, String activityId);
}
