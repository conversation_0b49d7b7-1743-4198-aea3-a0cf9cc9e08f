package com.wunding.learn.reading.service.service.impl;

import static com.wunding.learn.common.util.string.StringUtil.newId;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.wunding.learn.common.constant.other.AvailableEnum;
import com.wunding.learn.common.constant.other.BaseErrorNoEnum;
import com.wunding.learn.common.constant.other.CommonConstants;
import com.wunding.learn.common.constant.reading.ReadingErrorNoEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.ResourceTypeEnum;
import com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum;
import com.wunding.learn.common.enums.excitation.ExcitationEventEnum;
import com.wunding.learn.common.enums.file.ImageTypeEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.other.PublishStatusEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.mq.dto.ExcitationMQEventDTO;
import com.wunding.learn.common.mq.event.ResourceInteractEvent.ResourceInteractEventRoutingKeyConstants;
import com.wunding.learn.common.mq.event.excitation.ExcitationMQEvent;
import com.wunding.learn.common.mq.event.reading.ReadingScoreGainEvent;
import com.wunding.learn.common.mq.service.MqProducer;
import com.wunding.learn.common.mybatis.service.impl.BaseServiceImpl;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.string.TranslateUtil;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.constant.FileBizType;
import com.wunding.learn.file.api.constant.ImageBizType;
import com.wunding.learn.file.api.dto.AbstractExportDataDTO;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.file.api.dto.NamePath;
import com.wunding.learn.file.api.service.FileFeign;
import com.wunding.learn.reading.service.admin.dto.BookExperienceCommentDTO;
import com.wunding.learn.reading.service.admin.dto.BookExperienceDTO;
import com.wunding.learn.reading.service.admin.dto.BookExperienceDetailDTO;
import com.wunding.learn.reading.service.admin.dto.BookExperienceReportDTO;
import com.wunding.learn.reading.service.admin.dto.BookExperienceStarDTO;
import com.wunding.learn.reading.service.admin.dto.ReportDetailDTO;
import com.wunding.learn.reading.service.admin.query.BookExpBaseQuery;
import com.wunding.learn.reading.service.admin.query.BookExpCommentQuery;
import com.wunding.learn.reading.service.admin.query.BookExperienceQuery;
import com.wunding.learn.reading.service.admin.query.BookExperienceReportQuery;
import com.wunding.learn.reading.service.client.dto.BookExpClientDTO;
import com.wunding.learn.reading.service.client.dto.BookExpCommentSaveDTO;
import com.wunding.learn.reading.service.client.dto.BookExpSaveDTO;
import com.wunding.learn.reading.service.client.dto.ExperienceDetailDTO;
import com.wunding.learn.reading.service.client.query.BookExpClientQuery;
import com.wunding.learn.reading.service.event.ReadingBookExperienceCommentEvent;
import com.wunding.learn.reading.service.mapper.BookExperienceMapper;
import com.wunding.learn.reading.service.model.BookExperience;
import com.wunding.learn.reading.service.model.ExpComment;
import com.wunding.learn.reading.service.model.ExpLikeRecord;
import com.wunding.learn.reading.service.model.ReadingActivity;
import com.wunding.learn.reading.service.model.ReadingActivityBook;
import com.wunding.learn.reading.service.model.ReadingTask;
import com.wunding.learn.reading.service.model.UserReadingTask;
import com.wunding.learn.reading.service.model.UserReadingTaskDetail;
import com.wunding.learn.reading.service.model.UserReport;
import com.wunding.learn.reading.service.service.IBookExperienceService;
import com.wunding.learn.reading.service.service.IExpCommentService;
import com.wunding.learn.reading.service.service.IExpLikeRecordService;
import com.wunding.learn.reading.service.service.IReadingActivityBookService;
import com.wunding.learn.reading.service.service.IReadingActivityService;
import com.wunding.learn.reading.service.service.IReadingTaskService;
import com.wunding.learn.reading.service.service.IUserReadingTaskDetailService;
import com.wunding.learn.reading.service.service.IUserReadingTaskService;
import com.wunding.learn.reading.service.service.IUserReportService;
import com.wunding.learn.user.api.dto.OrgShowDTO;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.dto.UserOrgDTO;
import com.wunding.learn.user.api.service.OrgFeign;
import com.wunding.learn.user.api.service.RouterFeign;
import com.wunding.learn.user.api.service.SysSensitiveWordFeign;
import com.wunding.learn.user.api.service.UserFeign;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * <p> 图书心得表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">chh</a>
 * @since 2022-09-05
 */
@Slf4j
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Service("bookExperienceService")
public class BookExperienceServiceImpl extends BaseServiceImpl<BookExperienceMapper, BookExperience> implements
    IBookExperienceService {

    private static final Integer COMMENT_STATUS = 2;
    private static final Integer EXPERIENCE_STATUS = 1;
    public static final String BOOK_EXPERIENCE_SERVICE = "bookExperienceService";
    public static final String STATUS = "status";
    private final OrgFeign orgFeign;
    private final UserFeign userFeign;
    private final FileFeign fileFeign;
    private final IUserReportService userReportService;
    private final IReadingActivityBookService readingActivityBookService;
    @Resource
    private IExpCommentService expCommentService;

    @Resource
    @Lazy
    private IReadingActivityService readingActivityService;

    @Resource
    private IReadingTaskService readingTaskService;

    @Resource
    private IUserReadingTaskService userReadingTaskService;

    @Resource
    private MqProducer mqProducer;

    @Resource
    private IUserReadingTaskDetailService userReadingTaskDetailService;

    @Resource
    @Lazy
    private IExpLikeRecordService expLikeRecordService;

    @Resource
    private ExportComponent exportComponent;
    @Resource
    private RouterFeign routerFeign;
    @Resource
    private SysSensitiveWordFeign sysSensitiveWordFeign;

    @Override
    public PageInfo<BookExperienceDTO> findBookExperienceByPage(BookExperienceQuery bookExperience) {
        String userIdQuery = bookExperience.getUserIds();
        if (StringUtils.isNotEmpty(userIdQuery)) {
            String[] userIdArray = userIdQuery.split(",");
            bookExperience.setUserIdList(Arrays.asList(userIdArray));
        }

        if (StringUtils.isNotEmpty(bookExperience.getOrgId())) {
            bookExperience.setOrgIdList(orgFeign.getChildrenId(bookExperience.getOrgId()));
        }

        bookExperience.setCurrentOrgId(UserThreadContext.getOrgId());
        Set<String> orgIdList = orgFeign.getUserManageAreaOrgId(UserThreadContext.getUserId());
        bookExperience.setManagerAreaOrgIds(orgIdList);

        PageInfo<BookExperienceDTO> pageInfo = PageMethod.startPage(bookExperience.getPageNo(),
            bookExperience.getPageSize()).doSelectPageInfo(() -> baseMapper.selectBookExperienceByPage(bookExperience));

        List<BookExperienceDTO> experienceList = pageInfo.getList();
        if (experienceList.isEmpty()) {
            return pageInfo;
        }
        Set<String> userIdList = experienceList.stream().map(BookExperienceDTO::getCreateBy)
            .collect(Collectors.toSet());
        Map<String, UserDTO> userMap = userFeign.getUserNameMapByIds(userIdList);
        Set<String> orgIdSet = userFeign.getUserOrdIdByUserIds(userIdList);
        Map<String, OrgShowDTO> orgShowDTOMap = orgFeign.getOrgShowDTO(orgIdSet);

        experienceList.forEach(experience -> {
            UserDTO userDTO = userMap.get(experience.getCreateBy());
            experience.setLoginName(userDTO.getLoginName()).setFullName(userDTO.getFullName());
            Optional.ofNullable(orgShowDTOMap.get(userDTO.getOrgId())).ifPresent(orgShowDTO -> {
                experience.setOrgName(orgShowDTO.getOrgShortName());
                experience.setOrgPath(orgShowDTO.getLevelPathName());
            });
        });
        return pageInfo;
    }

    @Override
    public void deleteBookExperience(String ids) {
        List<String> idList = TranslateUtil.translateBySplit(ids, String.class);
        removeBatchByIds2(idList);
    }

    @Override
    public PageInfo<BookExperienceReportDTO> findBookReportDataByPage(
        BookExperienceReportQuery bookExperienceReportQuery) {
        PageInfo<BookExperienceReportDTO> pageInfo = PageMethod.startPage(bookExperienceReportQuery.getPageNo(),
                bookExperienceReportQuery.getPageSize())
            .doSelectPageInfo(() -> userReportService.selectBookReportDataByPage(bookExperienceReportQuery));
        List<BookExperienceReportDTO> bookExpReportList = pageInfo.getList();
        if (bookExpReportList.isEmpty()) {
            return pageInfo;
        }
        Set<String> userIdList = bookExpReportList.stream().map(BookExperienceReportDTO::getCreateBy)
            .collect(Collectors.toSet());
        Map<String, UserDTO> userMap = userFeign.getUserNameMapByIds(userIdList);
        Set<String> orgIdSet = userFeign.getUserOrdIdByUserIds(userIdList);
        Map<String, OrgShowDTO> orgShowDTOMap = orgFeign.getOrgShowDTO(orgIdSet);
        // 用户信息设置
        bookExpReportList.forEach(report -> {
            UserDTO userDTO = userMap.get(report.getCreateBy());
            report.setLoginName(userDTO.getLoginName()).setFullName(userDTO.getFullName())
                .setOrgName(userDTO.getOrgName());
            Optional.ofNullable(orgShowDTOMap.get(userDTO.getOrgId())).ifPresent(orgShowDTO -> {
                report.setOrgName(orgShowDTO.getOrgShortName());
                report.setOrgPath(orgShowDTO.getLevelPathName());
            });
        });
        return pageInfo;
    }

    @Override
    public BookExperienceDetailDTO detail(String id) {
        BookExperience bookExperience = getById(id);
        List<NamePath> commentImgList = fileFeign.getImageFileNamePaths(id, ImageTypeEnum.EXPERIENCEFILE.name());
        return new BookExperienceDetailDTO().setContent(bookExperience.getContent()).setCommentImgList(commentImgList);
    }

    @Override
    public PageInfo<BookExperienceStarDTO> star(BookExpBaseQuery bookExpBaseQuery) {
        PageInfo<BookExperienceStarDTO> pageInfo = PageMethod.startPage(bookExpBaseQuery.getPageNo(),
            bookExpBaseQuery.getPageSize()).doSelectPageInfo(() -> baseMapper.selectBookExpStar(bookExpBaseQuery));
        List<BookExperienceStarDTO> starList = pageInfo.getList();
        if (starList.isEmpty()) {
            return pageInfo;
        }
        Set<String> userIdList = starList.stream().map(BookExperienceStarDTO::getCreateBy).collect(Collectors.toSet());
        Map<String, UserDTO> userMap = userFeign.getUserNameMapByIds(userIdList);
        Set<String> orgIdSet = userFeign.getUserOrdIdByUserIds(userIdList);
        Map<String, OrgShowDTO> orgShowDTOMap = orgFeign.getOrgShowDTO(orgIdSet);
        starList.forEach(star -> {
            UserDTO userDTO = userMap.get(star.getCreateBy());
            star.setLoginName(userDTO.getLoginName()).setFullName(userDTO.getFullName());
            Optional.ofNullable(orgShowDTOMap.get(userDTO.getOrgId())).ifPresent(orgShowDTO -> {
                star.setOrgName(orgShowDTO.getOrgShortName());
                star.setOrgPath(orgShowDTO.getLevelPathName());
            });
        });
        return pageInfo;
    }

    @Override
    public PageInfo<BookExperienceCommentDTO> comment(BookExpCommentQuery expCommentQuery) {
        String userIdQuery = expCommentQuery.getUserIds();
        if (StringUtils.isNotEmpty(userIdQuery)) {
            String[] userIdArray = userIdQuery.split(",");
            expCommentQuery.setUserIdList(Arrays.asList(userIdArray));
        }

        PageInfo<BookExperienceCommentDTO> pageInfo = PageMethod.startPage(expCommentQuery.getPageNo(),
            expCommentQuery.getPageSize()).doSelectPageInfo(() -> baseMapper.selectBookExpComment(expCommentQuery));
        List<BookExperienceCommentDTO> commentList = pageInfo.getList();
        if (commentList.isEmpty()) {
            return pageInfo;
        }
        Set<String> userIdList = commentList.stream().map(BookExperienceCommentDTO::getCreateBy)
            .collect(Collectors.toSet());
        Map<String, UserDTO> userMap = userFeign.getUserNameMapByIds(userIdList);
        Set<String> orgIdSet = userFeign.getUserOrdIdByUserIds(userIdList);
        Map<String, OrgShowDTO> orgShowDTOMap = orgFeign.getOrgShowDTO(orgIdSet);
        commentList.forEach(star -> {
            UserDTO userDTO = userMap.get(star.getCreateBy());
            star.setLoginName(userDTO.getLoginName()).setFullName(userDTO.getFullName());
            Optional.ofNullable(orgShowDTOMap.get(userDTO.getOrgId())).ifPresent(orgShowDTO -> {
                star.setOrgName(orgShowDTO.getOrgShortName());
                star.setOrgPath(orgShowDTO.getLevelPathName());
            });
        });
        return pageInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void commentDel(String ids) {
        List<String> commentIdList = TranslateUtil.translateBySplit(ids, String.class);
        List<BookExperience> bookExperienceList = listByIds(commentIdList);
        // 需要扣减评论数量的心得id
        List<String> bookExpIdList = bookExperienceList.stream().map(BookExperience::getId)
            .collect(Collectors.toList());
        lambdaUpdate().setSql("comment_num = comment_num - 1").in(BookExperience::getId, bookExpIdList)
            .ge(BookExperience::getCommentNum, GeneralJudgeEnum.NEGATIVE.getValue()).update();
        expCommentService.removeBatchByIds(commentIdList);
    }

    @Override
    public ReportDetailDTO getReportDetailById(String id) {
        ReportDetailDTO reportDetailDTO = new ReportDetailDTO();
        // 查询出举报信息
        UserReport userReport = userReportService.getById(id);
        UserDTO reportUser = userFeign.getUserById(userReport.getUserId());
        Optional.ofNullable(reportUser).ifPresent(user -> {
            reportDetailDTO.setReportFullName(user.getFullName());
            reportDetailDTO.setReportLoginName(user.getLoginName());
            reportDetailDTO.setReportOrgName(user.getOrgName());
        });
        String expId = StringUtils.EMPTY;
        if (COMMENT_STATUS.equals(userReport.getType())) {
            ExpComment expComment = expCommentService.getById(userReport.getContentId());
            if (Optional.ofNullable(expComment).isEmpty()) {
                return reportDetailDTO;
            }
            reportDetailDTO.setCommentContent(expComment.getContent());
            UserDTO commentUser = userFeign.getUserById(expComment.getCreateBy());
            Optional.ofNullable(commentUser).ifPresent(user -> {
                reportDetailDTO.setCommentFullName(user.getFullName());
                reportDetailDTO.setCommentLoginName(user.getLoginName());
                reportDetailDTO.setCommentOrgName(user.getOrgName());
            });
            expId = expComment.getExpId();
        }
        if (EXPERIENCE_STATUS.equals(userReport.getType())) {
            expId = userReport.getContentId();
        }
        BookExperience bookExperience = baseMapper.selectById(expId);
        if (Optional.ofNullable(bookExperience).isEmpty()) {
            return reportDetailDTO;
        }
        reportDetailDTO.setContent(bookExperience.getContent());
        UserDTO experienceUser = userFeign.getUserById(bookExperience.getCreateBy());
        Optional.ofNullable(experienceUser).ifPresent(user -> {
            reportDetailDTO.setFullName(user.getFullName());
            reportDetailDTO.setLoginName(user.getLoginName());
            reportDetailDTO.setOrgName(user.getOrgName());
        });
        ReadingActivityBook book = readingActivityBookService.getById(bookExperience.getBookId());
        if (Optional.ofNullable(book).isEmpty()) {
            return reportDetailDTO;
        }
        reportDetailDTO.setBookName(book.getName());
        return reportDetailDTO;
    }

    @Override
    public void delExperience(String expId) {
        BookExperience bookExperience = getById(expId);
        if (Optional.ofNullable(bookExperience).isPresent() && bookExperience.getCreateBy().equals(UserThreadContext.getUserId())) {
            removeById(expId);
            return;
        }
        throw new BusinessException(ReadingErrorNoEnum.ERR_DELETED_FAIL);
    }

    @Override
    public ExperienceDetailDTO getExperienceDetail(String expId) {
        ExperienceDetailDTO experienceDetail = new ExperienceDetailDTO();
        BookExperience experience = getById(expId);
        Optional.ofNullable(experience).ifPresent(e -> {
            UserDTO author = userFeign.getUserById(e.getCreateBy());
            Optional.ofNullable(author).ifPresent(user -> {
                experienceDetail.setAuthorName(user.getFullName());
                experienceDetail.setOrgName(user.getOrgName());
                experienceDetail.setHeadUrl(user.getAvatar());
            });
            experienceDetail.setContent(e.getContent());
            experienceDetail.setCreateTime(e.getCreateTime());
            ReadingActivityBook readingActivityBook = readingActivityBookService.getById(e.getBookId());
            Optional.ofNullable(readingActivityBook).ifPresent(r -> {
                experienceDetail.setBookName(r.getName());
                experienceDetail.setBookAuthor(r.getAuthor());
                experienceDetail.setBookUrl(fileFeign.getImageUrl(r.getId(), ImageTypeEnum.bookImageIcon.name()));
            });
            experienceDetail.setBookId(e.getBookId());
            experienceDetail.setActivityId(e.getActivityId());
            experienceDetail.setImgList(
                fileFeign.getImageFileNamePaths(e.getId(), ImageTypeEnum.EXPERIENCEFILE.name()));
        });
        return experienceDetail;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveExperienceComment(BookExpCommentSaveDTO bookExpCommentSaveDTO) {
        BookExperience bookExperience = getById(bookExpCommentSaveDTO.getExpId());
        if (Optional.ofNullable(bookExperience).isEmpty()) {
            throw new BusinessException(ReadingErrorNoEnum.ERR_BOOK_EXPERIENCE_NULL);
        }

        String currentUserId = UserThreadContext.getUserId();
        // 取读书活动id
        String activityId = bookExperience.getActivityId();
        ReadingActivity readingActivity = readingActivityService.getDataById(activityId);
        if (StringUtils.isNotBlank(bookExpCommentSaveDTO.getContentId())) {
            ExpComment updateBean = new ExpComment();
            updateBean.setId(bookExpCommentSaveDTO.getContentId());
            updateBean.setExpId(bookExpCommentSaveDTO.getExpId());
            updateBean.setContent(bookExpCommentSaveDTO.getContent());
            updateBean.setCreateTime(new Date());
            expCommentService.updateById(updateBean);
        } else {
            ExpComment newBean = new ExpComment();
            newBean.setId(newId());
            newBean.setExpId(bookExpCommentSaveDTO.getExpId());
            newBean.setContent(bookExpCommentSaveDTO.getContent());
            newBean.setOrgId(UserThreadContext.getOrgId());
            expCommentService.save(newBean);
            // 添加完成记录
            List<Integer> types = new ArrayList<>();
            types.add(CommonConstants.READING_TASK_TYPE_COMMENT_N);
            IBookExperienceService bookExperienceService = SpringUtil.getBean(BOOK_EXPERIENCE_SERVICE, IBookExperienceService.class);
            bookExperienceService.addUserReadingTask(bookExperience.getActivityId(), UserThreadContext.getUserId(), types,
                bookExpCommentSaveDTO.getExpId());
            // 刷新图书心得评论数
            mqProducer.sendMsg(new ReadingBookExperienceCommentEvent(bookExpCommentSaveDTO.getExpId()));

            // 取用户对共读心得评论数量
            Integer count = baseMapper.getActivityExpireCommentNum(activityId, currentUserId);
            // 增加共读心得提交事件
            ExcitationMQEventDTO eventDTO = new ExcitationMQEventDTO();
            eventDTO.setUserId(currentUserId)
                .setEventId(ExcitationEventEnum.readingCommentExpNum.name())
                .setTargetName(readingActivity.getSubject())
                .setTargetId(activityId)
                .setBizId(activityId)
                .setBizType(ExcitationEventCategoryEnum.READING.getCode())
                .setValue(BigDecimal.valueOf(count));
            mqProducer.sendMsg(new ExcitationMQEvent(eventDTO));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean addUserReadingTask(String activityId, String userId, List<Integer> types, String businessId) {
        boolean result = false;
        // 如果共读活动已过期，那么就不用完成任务了
        LambdaQueryWrapper<ReadingActivity> activityQueryWrapper = new LambdaQueryWrapper<>();
        activityQueryWrapper.eq(ReadingActivity::getId, activityId);
        activityQueryWrapper.eq(ReadingActivity::getIsPublish, PublishStatusEnum.IS_PUBLISH.getValue());
        Date currentTime = new Date();
        activityQueryWrapper.le(ReadingActivity::getStartTime, currentTime);
        activityQueryWrapper.ge(ReadingActivity::getEndTime, currentTime);
        if (readingActivityService.count(activityQueryWrapper) == 0) {
            return false;
        }

        LambdaQueryWrapper<ReadingTask> readingTaskQueryWrapper = new LambdaQueryWrapper<>();
        readingTaskQueryWrapper.eq(ReadingTask::getActivityId, activityId);
        readingTaskQueryWrapper.eq(ReadingTask::getIsAvailable, AvailableEnum.AVAILABLE.getValue());
        readingTaskQueryWrapper.in(ReadingTask::getType, types);
        List<ReadingTask> readingTasks = readingTaskService.list(readingTaskQueryWrapper);

        for (ReadingTask readingTask : readingTasks) {
            if (CommonConstants.READING_TASK_TYPE_COMMENT == readingTask.getType()
                && readingTask.getBookId() != null && !readingTask.getBookId().equals(businessId)) {
                continue;
            }

            if (CommonConstants.READING_TASK_TYPE_READING == readingTask.getType()) {
                ReadingActivityBook readingActivityBook = readingActivityBookService.getById(readingTask.getBookId());
                if (readingTask.getBookId() != null && null != readingActivityBook && !readingActivityBook.getNo()
                    .equals(businessId)) {
                    continue;
                }
            }

            LambdaQueryWrapper<UserReadingTask> userReadingTaskQueryWrapper = new LambdaQueryWrapper<>();
            userReadingTaskQueryWrapper.eq(UserReadingTask::getReadingTaskId, readingTask.getId());
            userReadingTaskQueryWrapper.eq(UserReadingTask::getActivityId, activityId);
            userReadingTaskQueryWrapper.eq(UserReadingTask::getUserId, userId);

            //同一个用户同一个共读 同一个任务只能完成一次
            result = getResult(activityId, userId, businessId, result, readingTask, userReadingTaskQueryWrapper);
        }

        return result;
    }

    private boolean getResult(String activityId, String userId, String businessId, boolean result,
        ReadingTask readingTask, LambdaQueryWrapper<UserReadingTask> userReadingTaskQueryWrapper) {
        if (0 == userReadingTaskService.count(userReadingTaskQueryWrapper)) {
            LambdaQueryWrapper<UserReadingTaskDetail> taskDetailQueryWrapper = new LambdaQueryWrapper<>();
            taskDetailQueryWrapper.eq(UserReadingTaskDetail::getReadingTaskId, readingTask.getId());
            taskDetailQueryWrapper.eq(UserReadingTaskDetail::getActivityId, activityId);
            taskDetailQueryWrapper.eq(UserReadingTaskDetail::getUserId, userId);
            taskDetailQueryWrapper.eq(UserReadingTaskDetail::getBusinessId, businessId);
            UserReadingTaskDetail userReadingTaskDetail = userReadingTaskDetailService.getOne(
                taskDetailQueryWrapper);
            if (Optional.ofNullable(userReadingTaskDetail).isEmpty()) {
                result = true;
                UserReadingTaskDetail insert = new UserReadingTaskDetail();
                String detailId = newId();
                insert.setId(detailId);
                insert.setActivityId(activityId);
                insert.setUserId(userId);
                insert.setReadingTaskId(readingTask.getId());
                insert.setScore(readingTask.getScore());
                insert.setBusinessId(businessId);
                userReadingTaskDetailService.save(insert);
                // 发送共读运营分获得事件
                mqProducer.send(new ReadingScoreGainEvent(readingTask.getScore(), activityId, userId, userId));

                UserReadingTaskDetail countDetail = new UserReadingTaskDetail();
                countDetail.setActivityId(activityId);
                countDetail.setUserId(userId);
                countDetail.setReadingTaskId(readingTask.getId());

                taskDetailQueryWrapper.clear();
                taskDetailQueryWrapper.eq(UserReadingTaskDetail::getReadingTaskId, readingTask.getId());
                taskDetailQueryWrapper.eq(UserReadingTaskDetail::getActivityId, activityId);
                taskDetailQueryWrapper.eq(UserReadingTaskDetail::getUserId, userId);
                if (userReadingTaskDetailService.count(taskDetailQueryWrapper) >= readingTask.getQuantity()) {
                    UserDTO user = userFeign.getUserById(userId);
                    UserReadingTask insertUserReadingTask = new UserReadingTask();
                    insertUserReadingTask.setId(newId());
                    insertUserReadingTask.setUserId(userId);
                    insertUserReadingTask.setOrgId(user == null ? null : user.getOrgId());
                    insertUserReadingTask.setActivityId(activityId);
                    insertUserReadingTask.setReadingTaskId(readingTask.getId());
                    userReadingTaskService.save(insertUserReadingTask);
                }
            }
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateExperience(BookExpSaveDTO bookExpSaveDTO) {
        List<String> routerIds = routerFeign.getRouterNames();
        // 检验系统版本权限是否包含要校验的内容；
        if(routerIds.contains(ResourceTypeEnum.SENSITIVE_CONFIG.getRouter())){
            // 敏感词校验
            String descStr = sysSensitiveWordFeign.checkWordsReturnStr(bookExpSaveDTO.getContent());
            if (!StringUtils.isEmpty(descStr)) {
                throw new BusinessException(BaseErrorNoEnum.ERR_HAS_SENSITIVE_WORD_EXCEPTION);
            }
        }
        if (StringUtils.isNotBlank(bookExpSaveDTO.getExpId())) {
            updateExperience(bookExpSaveDTO);
            return;
        }
        createExperience(bookExpSaveDTO);
    }

    @Override
    public PageInfo<BookExpClientDTO> experience(BookExpClientQuery bookExpClientQuery) {
        PageInfo<BookExpClientDTO> pageInfo = PageMethod.startPage(bookExpClientQuery.getPageNo(),
            bookExpClientQuery.getPageSize()).doSelectPageInfo(() -> baseMapper.experience(bookExpClientQuery));
        List<String> userIds = pageInfo.getList().stream().map(BookExpClientDTO::getCreateBy)
            .filter(StringUtils::isNotBlank).collect(Collectors.toList());
        Map<String, UserOrgDTO> userMap = orgFeign.getUserAndOrgMapByUserIds(userIds);
        for (BookExpClientDTO bookExpClientDTO : pageInfo.getList()) {
            Optional.ofNullable(userMap.get(bookExpClientDTO.getCreateBy())).ifPresent(userInfo -> {
                bookExpClientDTO.setAuthor(userInfo.getFullName());
                bookExpClientDTO.setOrgName(userInfo.getOrgName());
                bookExpClientDTO.setHeadUrl(userInfo.getAvatar());
            });
            bookExpClientDTO.setIsHimself(UserThreadContext.getUserId().equals(bookExpClientDTO.getCreateBy())
                ? GeneralJudgeEnum.CONFIRM.getValue() : GeneralJudgeEnum.NEGATIVE.getValue());
            bookExpClientDTO.setIsLiked(isExpUserLiked(bookExpClientDTO.getId(), UserThreadContext.getUserId()));
            bookExpClientDTO.setBookUrl(
                fileFeign.getImageFileNamePath(bookExpClientDTO.getBookId(), ImageTypeEnum.bookImageIcon.name()));
            bookExpClientDTO.setRecordingUrl(
                fileFeign.getFileUrl(bookExpClientDTO.getId(), FileBizType.ExperienceRecording.name()));
            bookExpClientDTO.setImgPathList(
                fileFeign.getImageFileNamePaths(bookExpClientDTO.getId(), ImageBizType.EXPERIENCEFILE.name()));
        }
        return pageInfo;
    }

    private Integer isExpUserLiked(String expId, String userId) {
        LambdaQueryWrapper<ExpLikeRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExpLikeRecord::getExpId, expId);
        queryWrapper.eq(ExpLikeRecord::getCreateBy, userId);
        return expLikeRecordService.count(queryWrapper) > 0 ? GeneralJudgeEnum.CONFIRM.getValue()
            : GeneralJudgeEnum.NEGATIVE.getValue();
    }

    private void createExperience(BookExpSaveDTO bookExpSaveDTO) {
        String expId = newId();
        String currentUserId = UserThreadContext.getUserId();
        String activityId = bookExpSaveDTO.getActivityId();
        ReadingActivity readingActivity = readingActivityService.getDataById(activityId);
        if (!CollectionUtils.isEmpty(bookExpSaveDTO.getImgList())) {
            fileFeign.saveImages(expId, ImageTypeEnum.EXPERIENCEFILE.name(), bookExpSaveDTO.getImgList());
        }
        if (StringUtils.isNotBlank(bookExpSaveDTO.getRecordingFilePath())) {
            fileFeign.saveFile(expId, FileBizType.ExperienceRecording.name(), bookExpSaveDTO.getRecordingFileName(),
                bookExpSaveDTO.getRecordingFilePath());
        }

        BookExperience newBean = new BookExperience();
        newBean.setId(expId);
        newBean.setActivityId(activityId);
        newBean.setBookId(bookExpSaveDTO.getBookId());
        newBean.setContent(bookExpSaveDTO.getContent());
        newBean.setOrgId(UserThreadContext.getOrgId());
        save(newBean);

        // 取提交心得数量
        long count = count(new LambdaQueryWrapper<BookExperience>().eq(BookExperience::getActivityId, activityId)
            .eq(BookExperience::getCreateBy, currentUserId));
        // 增加共读心得提交事件
        ExcitationMQEventDTO eventDTO = new ExcitationMQEventDTO();
        eventDTO.setUserId(currentUserId)
            .setEventId(ExcitationEventEnum.readingExperienceTimes.name())
            .setTargetName(readingActivity.getSubject())
            .setTargetId(activityId)
            .setBizId(activityId)
            .setBizType(ExcitationEventCategoryEnum.READING.getCode())
            .setValue(BigDecimal.valueOf(count));
        mqProducer.sendMsg(new ExcitationMQEvent(eventDTO));

        // 添加任务完成记录
        List<Integer> types = new ArrayList<>();
        types.add(CommonConstants.READING_TASK_TYPE_EXPERIENCE);
        types.add(CommonConstants.READING_TASK_TYPE_COMMENT);
        IBookExperienceService bookExperienceService = SpringUtil.getBean(BOOK_EXPERIENCE_SERVICE, IBookExperienceService.class);
        bookExperienceService.addUserReadingTask(bookExpSaveDTO.getActivityId(), UserThreadContext.getUserId(), types,
            bookExpSaveDTO.getBookId());
    }

    private void updateExperience(BookExpSaveDTO bookExpSaveDTO) {
        List<NamePath> oldImages = fileFeign.getImageFileNamePaths(bookExpSaveDTO.getExpId(),
            ImageTypeEnum.EXPERIENCEFILE.name());
        if (!CollectionUtils.isEmpty(bookExpSaveDTO.getImgList())) {
            fileFeign.deleteImageByBizIdAndBizType(bookExpSaveDTO.getExpId(), ImageBizType.EXPERIENCEFILE.name());
            fileFeign.saveImages(bookExpSaveDTO.getExpId(), ImageTypeEnum.EXPERIENCEFILE.name(),
                bookExpSaveDTO.getImgList());
        } else if (!CollectionUtils.isEmpty(oldImages)) {
            List<String> ids = oldImages.stream().map(NamePath::getId).collect(Collectors.toList());
            fileFeign.deleteImageByImagesIds(ids);
        }
        BookExperience update = new BookExperience();
        update.setId(bookExpSaveDTO.getExpId());
        update.setContent(bookExpSaveDTO.getContent());
        updateById(update);
    }

    @Override
    public void exportData(BookExperienceQuery query) {

        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IBookExperienceService, BookExperienceDTO>(query) {

            @Override
            protected IBookExperienceService getBean() {
                return SpringUtil.getBean(BOOK_EXPERIENCE_SERVICE, IBookExperienceService.class);
            }

            @Override
            protected PageInfo<BookExperienceDTO> getPageInfo() {
                return getBean().findBookExperienceByPage((BookExperienceQuery) queryDTO);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.ReadingBookExperience;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.ReadingBookExperience.getType();
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public void exportCommentData(BookExpCommentQuery query) {

        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IBookExperienceService, BookExperienceCommentDTO>(
            query) {

            @Override
            protected IBookExperienceService getBean() {
                return SpringUtil.getBean(BOOK_EXPERIENCE_SERVICE, IBookExperienceService.class);
            }

            @Override
            protected PageInfo<BookExperienceCommentDTO> getPageInfo() {
                return getBean().comment((BookExpCommentQuery) queryDTO);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.ReadingBookExperienceComment;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.ReadingBookExperienceComment.getType();
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public void exportStarData(BookExpBaseQuery query) {

        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IBookExperienceService, BookExperienceStarDTO>(query) {

            @Override
            protected IBookExperienceService getBean() {
                return SpringUtil.getBean(BOOK_EXPERIENCE_SERVICE, IBookExperienceService.class);
            }

            @Override
            protected PageInfo<BookExperienceStarDTO> getPageInfo() {
                return getBean().star((BookExpBaseQuery) queryDTO);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.ReadingBookExperienceStar;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.ReadingBookExperienceStar.getType();
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public void exportReportsData(BookExperienceReportQuery query) {

        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IBookExperienceService, BookExperienceReportDTO>(
            query) {

            @Override
            protected IBookExperienceService getBean() {
                return SpringUtil.getBean(BOOK_EXPERIENCE_SERVICE, IBookExperienceService.class);
            }

            @Override
            protected PageInfo<BookExperienceReportDTO> getPageInfo() {
                return getBean().findBookReportDataByPage((BookExperienceReportQuery) queryDTO);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.ReadingReports;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.ReadingReports.getType();
            }

            @Override
            protected void afterCreateBeanMap(Map<String, Object> map) {
                Object status = map.get(STATUS);
                Object type = map.get("type");
                if (Objects.equals(status, 0)) {
                    map.put(STATUS, "未处理");
                } else if (Objects.equals(status, 1)) {
                    map.put(STATUS, "已处理");
                }
                if (Objects.equals(type, 1)) {
                    map.put("type", "心得");
                } else if (Objects.equals(type, 2)) {
                    map.put("type", "评论");
                }

            }
        };

        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public void updateReadingBookExperienceInteractNum(String id, String event) {
        switch (event) {
            case ResourceInteractEventRoutingKeyConstants.READING_BOOK_EXPERIENCE_COMMENT_EVENT:
                baseMapper.updateBookExperienceCommentNum(id);
                break;
            case ResourceInteractEventRoutingKeyConstants.READING_BOOK_EXPERIENCE_LIKE_EVENT:
                baseMapper.updateBookExperienceLikeNum(id);
                break;
            default:
        }
    }

}
