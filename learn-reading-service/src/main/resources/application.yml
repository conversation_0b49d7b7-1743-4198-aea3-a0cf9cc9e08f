# 应用服务 WEB 访问端口
server:
  servlet:
    context-path: /reading/
  tomcat:
    accept-count: 2000
    max-connections: 81920
    threads:
      max: 2000
      min-spare: 100

# 应用名称
spring:
  profiles:
    active: dev
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true

  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher

  application:
    name: learn-reading-service



  # 数据库设置
  datasource:

    # Druid 连接池
    druid:
      initial-size: 10
      max-active: 1000
      max-wait: 30000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: select 'x' from dual
      validation-query-timeout: 3
      test-on-borrow: false
      test-on-return: false
      test-while-idle: true
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      remove-abandoned: true
      remove-abandoned-timeout: 180
      log-abandoned: true
      filters: stat,slf4j
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=2000
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
        session-stat-enable: true
      stat-view-servlet:
        loginUsername: admin
        loginPassword: 123456
        enabled: true
        url-pattern: /druid/*
        allow: "127.0.0.1,192.168.0.0/24,172.16.0.0/16,10.0.0.0/8,59.37.126.22"
        #deny: ""
      filter:
        stat:
          merge-sql: true
          log-slow-sql: true
          slow-sql-millis: 2000
        log4j2:
          result-set-log-enabled: false
          statement-executable-sql-log-enable: true



  #redis
  # Redis服务器地址
  data:
    redis:
      # Lettuce
      # 连接池最大连接数（使用负值表示没有限制）
      lettuce:
        # 关闭超时时间
        shutdown-timeout: 100
        pool:
          max-active: 1000
          # 连接池最大阻塞等待时间（使用负值表示没有限制）
          max-wait: 10000
          # 连接池中的最大空闲连接
          max-idle: 8
          # 连接池中的最小空闲连接
          min-idle: 0

    #rabbitmq 配置
  rabbitmq:
    publisher-returns: true
    publisher-confirm-type: correlated
    connection-timeout: 15000
    listener:
      simple:
        # 消费端有几个纯种
        concurrency: 10
        # 手动ACK
        acknowledge-mode: manual
        # 消费端的监听最大个数
        max-concurrency: 10
        # 每次拉取消息数量
        prefetch: 1
    template:
      exchange: course

feign:
  client:
    config:
      default:
        logger-level: full
  circuitbreaker:
    enabled: true

hystrix:
  command:
    default:
      execution:
        timeout:
          enabled: true
        isolation:
          #必须用信号量模式
          strategy: SEMAPHORE
          thread:
            timeoutInMilliseconds: 600000


mybatis-plus:
  # xml扫描，多个目录用逗号或者分号分隔（告诉 Mapper 所对应的 XML 文件位置） classpath:com/wunding/learn/**/mapper/*.xml
  mapper-locations: classpath*:com/wunding/learn/**/mapper/*.xml
  global-config:
    db-config:
      #主键类型 AUTO:"数据库ID自增" INPUT:"用户输入ID",ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: input
      logic-delete-field: isDel
      logic-delete-value: 1
      logic-not-delete-value: 0
  configuration:
    # 是否开启自动驼峰命名规则映射:从数据库列名到Java属性驼峰命名的类似映射
    map-underscore-to-camel-case: true
    # 如果查询结果中包含空值的列，则 MyBatis 在映射的时候，不会映射这个字段
    call-setters-on-nulls: true
    # 将执行的sql打印出来，在开发或测试的时候可以用，如果要输出到logback里需要注解这个配置，默认输出到logback
    # 输出控制台
    # log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 输出到log4j2
    # log-impl: org.apache.ibatis.logging.log4j2.Log4j2Impl
    # 一级缓存配置 一级缓存是本地或者说局部缓存，它不能被关闭，只能配置缓存范围。SESSION 或者 STATEMENT。
    local-cache-scope: session
    # 二级缓存总开关
    cache-enabled: true
    variables:
      appName: ${spring.application.name}



############# 分页插件PageHelper配置 #############
pagehelper:
  helper-dialect: mysql
  reasonable: false
  support-methods-arguments: true
  pageSizeZero: true
  params: count=countSql

#开启acutor端点
management:
  jmx:
    metrics:
      export:
        enabled: false
  endpoints:
# 禁用所有JMX端点暴露
    jmx:
      exposure:
        exclude: '*'
    web:
      exposure:
        include: 'info,health,metrics,prometheus'
  endpoint:
    health:
      show-details: always
      probes:
        enabled: true
  metrics:
    tags:
      application: ${spring.application.name}
    export:
      appoptics:
        api-token:
sync:
  tables:
    - sourceTable: sys_org
      sourcePk: id
      targetTable: sys_org
      targetPk: id
      field:
        org_name: org_name
        parent_id: parent_id
        level_path: level_path
        level_path_name: level_path_name
        is_available: is_available
        is_del: is_del
    - sourceTable: sys_user
      sourcePk: id
      targetTable: sys_user
      targetPk: id
      field:
        employee_no: employee_no
        login_name: login_name
        full_name: full_name
        nike_name: nike_name
        password: password
        org_id: org_id
        first_name: first_name
        last_name: last_name
        pinyin: pinyin
        sex: sex
        work_phone: work_phone
        telephone: telephone
        short_mobile: short_mobile
        email: email
        is_lock: is_lock
        is_available: is_available
        is_del: is_del
        create_by: create_by
        create_time: create_time
        update_by: update_by
        update_time: update_time
        is_super: is_super
        user_level_id: user_level_id
        is_expert: is_expert
        live_user_id: live_user_id
        ls_depart_admin: ls_depart_admin
        birthday: birthday
        join_date: join_date
        id_number: id_number
        is_work: is_work
        education: education
        system_type: system_type

# 是否启用默认数据源
tenant:
  type: -1
  systemName: 问鼎云学习
