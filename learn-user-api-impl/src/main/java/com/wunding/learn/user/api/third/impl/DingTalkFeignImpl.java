package com.wunding.learn.user.api.third.impl;

import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.util.live.PolyvHttpUtil;
import com.wunding.learn.user.api.dto.third.dingtalk.AccessTokenDTO;
import com.wunding.learn.user.api.dto.third.dingtalk.AdminUserInfoDTO;
import com.wunding.learn.user.api.dto.third.dingtalk.JsapiTicketDTO;
import com.wunding.learn.user.api.dto.third.dingtalk.PersonUserInfoDTO;
import com.wunding.learn.user.api.query.third.dingtalk.AccessTokenQuery;
import com.wunding.learn.user.api.query.third.dingtalk.AdminAccessTokenQuery;
import com.wunding.learn.user.api.query.third.dingtalk.UserAccessTokenQuery;
import com.wunding.learn.user.api.third.DingTalkFeign;
import java.util.Map;
import org.springframework.web.bind.annotation.RestController;

@RestController("dingTalkFeign")
public class DingTalkFeignImpl implements DingTalkFeign {

    private static final String BASE_URL = "https://api.dingtalk.com/v1.0";

    public final String CONTENT_TYPE = "Content-Type";

    public final String APPLICATION_JSON = "application/json";

    public final String X_ACS_DINGTALK_ACCESS_TOKEN = "x-acs-dingtalk-access-token";

    @Override
    public AccessTokenDTO getAccessToken(AccessTokenQuery accessTokenQuery) {
        String url = BASE_URL + "/oauth2/accessToken";
        Map<String, String> headers = Map.of(CONTENT_TYPE, APPLICATION_JSON);
        String dto = PolyvHttpUtil.sendHttpPost(url, headers, null, JsonUtil.objToJson(accessTokenQuery));
        return JsonUtil.jsonToObj(dto, com.wunding.learn.user.api.dto.third.dingtalk.AccessTokenDTO.class);
    }

    @Override
    public AccessTokenDTO getAdminAccessToken(AdminAccessTokenQuery adminAccessTokenQuery) {
        String url = BASE_URL + "/oauth2/ssoAccessToken";
        Map<String, String> headers = Map.of(CONTENT_TYPE, APPLICATION_JSON);
        String dto = PolyvHttpUtil.sendHttpPost(url, headers, null, JsonUtil.objToJson(adminAccessTokenQuery));
        return JsonUtil.jsonToObj(dto, com.wunding.learn.user.api.dto.third.dingtalk.AccessTokenDTO.class);
    }

    @Override
    public AccessTokenDTO getUserAccessToken(UserAccessTokenQuery userAccessTokenQuery) {
        String url = BASE_URL + "/oauth2/userAccessToken";
        Map<String, String> headers = Map.of(CONTENT_TYPE, APPLICATION_JSON);
        String dto = PolyvHttpUtil.sendHttpPost(url, headers, null, JsonUtil.objToJson(userAccessTokenQuery));
        return JsonUtil.jsonToObj(dto, com.wunding.learn.user.api.dto.third.dingtalk.AccessTokenDTO.class);
    }

    @Override
    public JsapiTicketDTO getJsapiTicket(String accessToken) {
        String url = BASE_URL + "/oauth2/jsapiTickets";
        Map<String, String> headers = Map.of(CONTENT_TYPE, APPLICATION_JSON, X_ACS_DINGTALK_ACCESS_TOKEN,
            accessToken);
        String dto = PolyvHttpUtil.sendHttpPost(url, headers, null, null);
        return JsonUtil.jsonToObj(dto, com.wunding.learn.user.api.dto.third.dingtalk.JsapiTicketDTO.class);
    }

    @Override
    public AdminUserInfoDTO getAdminUserInfo(String accessToken, String code) {
        String url = BASE_URL + "/oauth2/ssoUserInfo?code=" + code;
        Map<String, String> headers = Map.of(CONTENT_TYPE, APPLICATION_JSON, X_ACS_DINGTALK_ACCESS_TOKEN,
            accessToken);
        String dto = PolyvHttpUtil.sendHttpPost(url, headers, null, null);
        return JsonUtil.jsonToObj(dto, com.wunding.learn.user.api.dto.third.dingtalk.AdminUserInfoDTO.class);
    }

    @Override
    public PersonUserInfoDTO getPersonUserInfoDTO(String accessToken) {
        String url = BASE_URL + "/contact/users/me";
        Map<String, String> headers = Map.of(CONTENT_TYPE, APPLICATION_JSON, X_ACS_DINGTALK_ACCESS_TOKEN,
            accessToken);
        String dto = PolyvHttpUtil.sendHttpPost(url, headers, null, null);
        return JsonUtil.jsonToObj(dto, com.wunding.learn.user.api.dto.third.dingtalk.PersonUserInfoDTO.class);
    }

}
