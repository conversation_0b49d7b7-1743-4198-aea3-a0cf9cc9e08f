package com.wunding.learn.common.table.partition.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.wunding.learn.common.table.partition.mapper.TablePartitionResourceMapper;
import com.wunding.learn.common.table.partition.model.TablePartitionResource;
import com.wunding.learn.common.table.partition.service.ITablePartitionResourceService;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <p>
 * 资源分表存储策略接口实现类
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">zhongchaoheng</a>
 * @date 2023/9/22 11:30
 */
@Slf4j
@Service("tablePartitionResourceService")
public class TablePartitionResourceServiceImpl extends
    ServiceImpl<TablePartitionResourceMapper, TablePartitionResource> implements
    ITablePartitionResourceService {

    @Override
    public TablePartitionResource getTablePartitionResource(String resourceId) {
        return baseMapper.selectOne(
            new LambdaQueryWrapper<TablePartitionResource>().eq(TablePartitionResource::getResourceId, resourceId));
    }

    @Override
    public void insertPartitionResource(TablePartitionResource partitionResource) {
        baseMapper.insert(partitionResource);
    }

    @Override
    public void updatePartitionResource(TablePartitionResource partitionResource) {
        baseMapper.updateById(partitionResource);
    }

    @Override
    public void deletePartitionData(String resourceId, String tablePartition) {
        List<String> deleteIds = baseMapper.getPartitionDataIds(resourceId,tablePartition);
        if(!CollectionUtils.isEmpty(deleteIds)){
            List<List<String>> deleteIdsBatch = Lists.partition(deleteIds,500);
            log.info("===========删除分区数据开始,resourceId:{},tablePartition:{},数据量:{},共{}批", resourceId, tablePartition,deleteIds.size(),deleteIdsBatch.size());
            for(List<String> list : deleteIdsBatch){
                baseMapper.deletePartitionData(list, tablePartition);
            }
            log.info("===========删除分区数据结束,resourceId:{},tablePartition:{},数据量:{},共{}批", resourceId, tablePartition,deleteIds.size(),deleteIdsBatch.size());
        }

    }
}
