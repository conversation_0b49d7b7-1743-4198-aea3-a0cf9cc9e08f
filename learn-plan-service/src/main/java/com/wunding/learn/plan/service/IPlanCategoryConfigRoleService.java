package com.wunding.learn.plan.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.plan.admin.query.PlanCategoryConfigRoleQuery;
import com.wunding.learn.plan.model.PlanCategoryConfigRole;
import java.util.List;

/**
 * <p> 培训计划类别角色配置 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">ylq</a>
 * @since 2023-03-06
 */
public interface IPlanCategoryConfigRoleService extends IService<PlanCategoryConfigRole> {


    /**
     * 根据分类id删除 角色配置信息
     *
     * @param categoryId
     */
    void deleteCategoryConfigRole(String categoryId);

    /**
     * 查询 角色配置信息
     *
     * @param query
     */
    List<PlanCategoryConfigRole> queryCategoryConfigRole(PlanCategoryConfigRoleQuery query);
}
