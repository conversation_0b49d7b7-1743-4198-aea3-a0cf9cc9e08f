package com.wunding.learn.plan.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.common.mybatis.cache.MyBatisPlusRedisCache;
import com.wunding.learn.plan.model.EnrollMemberResource;
import java.util.List;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
@CacheNamespace(implementation = MyBatisPlusRedisCache.class, eviction = MyBatisPlusRedisCache.class)
public interface EnrollMemberResourceMapper extends BaseMapper<EnrollMemberResource> {

    /**
     * 获取报名记录id
     *
     * @param phone 电话
     * @return {@link List}<{@link String}>
     */
    List<String> getCompanyPhone(@Param("phone") String phone);
}
