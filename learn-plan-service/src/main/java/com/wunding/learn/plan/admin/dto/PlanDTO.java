package com.wunding.learn.plan.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * </p> 培训计划表
 *
 * <AUTHOR> href="mailto:<EMAIL>">ylq</a>
 * @since 2023-03-06
 */
@Data
@Schema(name = "PlanDTO", description = "培训计划表")
public class PlanDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键id
     */
    @Schema(description = "主键id")
    private String id;


    /**
     * 培训计划编号
     */
    @Schema(description = "培训计划编号")
    private String planNo;


    /**
     * 培训计划名称
     */
    @Schema(description = "培训计划名称")
    private String planName;


    /**
     * 培训计划类别
     */
    @Schema(description = "培训计划类别")
    private String planCategoryName;


    /**
     * 培训计划类别id
     */
    @Schema(description = "培训计划类别id", hidden = true)
    private String planCategory;


    /**
     * 培训计划年度
     */
    @Schema(description = "培训计划年度")
    private String planYear;


    /**
     * 培训计划季度
     */
    @Schema(description = "培训计划季度")
    private String planQuarter;

    /**
     * 培训计划部门id
     */
    @Schema(description = "培训计划部门id")
    private String planOrg;


    /**
     * 培训计划部门
     */
    @Schema(description = "培训计划部门")
    private String planOrgName;


    /**
     * 总条数
     */
    @Schema(description = "总条数")
    private String totalNumber;


    /**
     * 状态
     */
    @Schema(description = "状态 0-草稿 1-审核通过 2-驳回 3-审核不通过 4-待审核")
    private Integer status;


    /**
     * 最后审核时间
     */
    @Schema(description = "最后审核时间")
    private Date lastAuditTime;


    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createByName;

    /**
     * 创建人id
     */
    @Schema(description = "创建人id", hidden = true)
    private String createBy;

    /**
     * 账号
     */
    @Schema(description = "账号")
    private String loginName;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 提交人id
     */
    @Schema(description = "提交人id")
    private String auditUser;

    /**
     * 提交人
     */
    @Schema(description = "提交人")
    private String auditUserName;

    @Schema(description = "部门")
    private String orgName;

    @Schema(description = "部门全称 orgPath")
    private String orgPath;
}
