package com.wunding.learn.plan.admin.query;

import com.wunding.learn.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Set;
import lombok.Data;

/**
 * 表单模板管理列表查询对象
 *
 * <AUTHOR>
 */
@Data
@Schema(name = "PlanFormTemplateListQuery", description = "表单模板管理列表查询对象")
public class PlanFormTemplateListQuery extends BaseEntity {


    /**
     * 表单名称
     */
    @Schema(description = "表单名称")
    private String formName;

    /**
     * 分类Id
     */
    @Schema(description = "分类Id")
    private String categoryId;

    /**
     * 当前登录人id
     */
    @Schema(description = "当前登录人id", hidden = true)
    private String userId;

    /**
     * 是否发布 0-否 1-是
     */
    @Schema(description = "是否发布 0-否 1-是")
    private Integer isPublish;

    /**
     * 查询类型 0-表单模板管理 1-其他引用查询
     */
    @Schema(description = "查询类型 0-表单模板管理 1-其他引用查询")
    private Integer queryType;

    @Schema(description = "管辖范围", hidden = true)
    private Set<String> managerAreaPaths;

    @Schema(description = "id", hidden = true)
    private String id;

    /**
     * 不等于id
     */
    @Schema(description = "neId", hidden = true)
    private String neId;
}
