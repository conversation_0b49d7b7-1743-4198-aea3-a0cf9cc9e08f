package com.wunding.learn.plan.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.common.mybatis.cache.MyBatisPlusRedisCache;
import com.wunding.learn.plan.model.PlanCategoryConfigRole;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p> 培训计划类别角色配置 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">ylq</a>
    * @since 2023-03-06
 */
@Mapper
@CacheNamespace(implementation = MyBatisPlusRedisCache.class, eviction = MyBatisPlusRedisCache.class)
public interface PlanCategoryConfigRoleMapper extends BaseMapper<PlanCategoryConfigRole> {

}
