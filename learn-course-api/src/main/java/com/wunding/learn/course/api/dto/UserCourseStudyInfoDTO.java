package com.wunding.learn.course.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 用户课程课时统计对象（已学课时，总计课时）
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Data
@Schema(name = "UserCourseStudyInfoDTO", description = "用户课程课时统计")
@Accessors(chain = true)
public class UserCourseStudyInfoDTO implements Serializable {

    private static final long serialVersionUID = -3686575596882518930L;

    @Schema(description = "课程ID")
    private String id;

    @Schema(description = "课程已学时长（秒）")
    private Long duration;

    @Schema(description = "课程总计时长（秒）")
    private Long totalDuration;
}
