package com.wunding.learn.course.api.service;

import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.directory.dto.SaveOrUpdateDirectoryDTO;
import com.wunding.learn.common.dto.CerDitchDTO;
import com.wunding.learn.common.dto.CertificationContentDTO;
import com.wunding.learn.common.dto.CompleteNumQuery;
import com.wunding.learn.common.dto.LecturerCourseDetailDTO;
import com.wunding.learn.common.dto.ResourceBaseDTO;
import com.wunding.learn.common.dto.ResourceDeleteInfoDTO;
import com.wunding.learn.common.dto.ResourceIdAndUserIdDTO;
import com.wunding.learn.common.dto.ResourceMemberDTO;
import com.wunding.learn.common.query.ResourceBaseQuery;
import com.wunding.learn.common.query.ResourceUserQuery;
import com.wunding.learn.course.api.dto.CourseCategoryDTO;
import com.wunding.learn.course.api.dto.CourseInfoDTO;
import com.wunding.learn.course.api.dto.CourseLearnDetailDTO;
import com.wunding.learn.course.api.dto.CourseSimpleInfoDTO;
import com.wunding.learn.course.api.dto.CourseViewLimitCheckResultDTO;
import com.wunding.learn.course.api.dto.ImportUserCourseRecordDTO;
import com.wunding.learn.course.api.dto.LecturerCourseDTO;
import com.wunding.learn.course.api.dto.MarchCourseTaskDTO;
import com.wunding.learn.course.api.dto.SaveCwLibraryDTO;
import com.wunding.learn.course.api.dto.UserCourseRecordDTO;
import com.wunding.learn.course.api.dto.UserCourseStudyInfoDTO;
import com.wunding.learn.course.api.query.CourseLearnQuery;
import com.wunding.learn.course.api.query.CourseListQuery;
import com.wunding.learn.course.api.query.CourseSummaryListQuery;
import com.wunding.learn.course.api.query.UserCourseListQuery;
import com.wunding.learn.course.api.query.UserCourseStudyInfoQuery;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.format.annotation.DateTimeFormat.ISO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2022-07-22 16:15
 **/
@FeignClient(url = "${learn.service.learn-course-service}", name = "learn-course-service", path = "/course")
public interface CourseFeign {

    /**
     * 获取课程的简单信息
     */
    @PostMapping("/course/getCourseSimpleInfoBatchIds")
    List<CourseInfoDTO> getCourseSimpleInfoBatchIds(@RequestBody Collection<String> batchIds);

    /**
     * 获取有效课程id列表（未删除）
     */
    @GetMapping("/course/getEffectiveCourseIds")
    List<String> getEffectiveCourseIds();

    /**
     * 获取单个课程信息
     *
     * @param id id
     * @return {@link CourseInfoDTO}
     */
    @GetMapping("/course/getById")
    CourseInfoDTO getById(@RequestParam("id") String id);

    /**
     * 获取单个课程信息(不过滤已被删除课程)
     *
     * @param id id
     * @return {@link CourseInfoDTO}
     */
    @GetMapping("/course/getRealityById")
    CourseInfoDTO getRealityById(@RequestParam("id") String id);

    /**
     * 获取单个课程发布状态信息
     *
     * @param id id
     * @return state 0-未发布 1-已发布
     */
    @GetMapping("/course/getPubState")
    int getCoursePubState(@RequestParam("id") String id);

    /**
     * 发布/取消发布课程
     *
     * @param id        课程id
     * @param isPublish 是否发布
     */
    @GetMapping("/course/publish")
    void publishCourse(@RequestParam("id") String id, @RequestParam("isPublish") Integer isPublish);

    /**
     * 移除课程
     */
    @PostMapping("/course/remove")
    void removeCourse(@RequestParam("id") String id);

    /**
     * 批量获取用户指定课程中的已学数
     *
     * @param completeNumQuery 查询
     * @return {@link Map}<{@link String}, {@link Integer}>
     */
    @GetMapping("/course/getCourseLearnNum")
    Map<String, Integer> getCourseLearnNum(@RequestBody CompleteNumQuery completeNumQuery);

    /**
     * 根据课程分类获取课程id列表
     */
    @GetMapping("/course/getCourseIdsByCategory")
    List<String> getCourseIdListByCategory(@RequestParam("courseCategoryId") String courseCategoryId);

    /**
     * 获取用户在某个课程学习的总学时
     */

    @GetMapping("/course/getUserCourseHour")
    Integer getUserCourseHour(@RequestParam("userId") String userId, @RequestParam("courseId") String courseId);

    /**
     * 查询用户总学习记录
     */
    @GetMapping("/course/getUserCourseDruation")
    Double getUserCourseDruation(@RequestParam("userId") String userId);

    /**
     * 查询用户指定课程学习时长统计数据（已学时长、课程总计时长）
     */
    @PostMapping("/course/getUserCourseStudyInfo")
    List<UserCourseStudyInfoDTO> getUserCourseStudyInfo(@RequestBody UserCourseStudyInfoQuery query);

    /**
     * 这个过滤了已删除
     *
     * @param batchIds 课程表ids
     * @return {@link Map}<{@link String}, {@link ResourceMemberDTO}> id与课程信息的映射
     */
    @GetMapping("/course/getNameBatchIds")
    Map<String, String> getNameBatchIds(@RequestParam("batchIds") Collection<String> batchIds);

    /**
     * 这个才包含了已删除的
     *
     * @param batchIds 课程表ids
     * @return {@link Map}<{@link String}, {@link String}> id与课程名称的映射
     */
    @GetMapping("/course/getNameIncludeIsDelByIds")
    Map<String, String> getNameIncludeIsDelByIds(@RequestParam("batchIds") Collection<String> batchIds);

    /**
     * 通过id列表获取课程 不过滤被删除数据
     *
     * @param batchIds 课程表ids
     * @return {@link Map}<{@link String}, {@link ResourceMemberDTO}> id与课程信息的映射
     */
    @GetMapping("/course/getResourceMemberBatchIds")
    Map<String, ResourceMemberDTO> getResourceMemberBatchIds(@RequestParam("batchIds") Collection<String> batchIds);


    /**
     * 查询复制课程的原来课程的ID
     */
    @GetMapping("/course/getAuthenticIdBatchIds")
    Map<String, String> getAuthenticIdBatchIds(@RequestParam("batchIds") Collection<String> batchIds);

    /**
     * 通过id列表获取课程
     */
    @PostMapping("/course/getCourseBatchIds")
    Map<String, CourseInfoDTO> getCourseBatchIds(@RequestBody Collection<String> batchIds);

    /**
     * 修改课程一些属性
     */
    @PostMapping("/course/updateCourseById")
    void updateCourseById(@RequestBody CourseInfoDTO courseInfoDTO);


    /**
     * 通过CourseNo列表获取课程
     */
    @GetMapping("/course/getCourseBatchNos")
    Map<String, CourseInfoDTO> getCourseBatchNos(@RequestParam("batchNos") Collection<String> batchNos);

    /**
     * 获取单个课程信息
     *
     * @param id id
     * @return {@link CourseInfoDTO}
     */
    @GetMapping("/course/getOneCourse")
    CourseInfoDTO getOneCourse(@RequestParam("id") String id);

    /**
     * 同步保存课程下发范围
     */
    @PostMapping("/course/saveSyncCourseViewLimit")
    void saveSyncCourseViewLimit(@RequestParam("resourceId") String resourceId,
        @RequestParam("programmeId") Long programmeId);

    /**
     * 检查下发范围
     *
     * @param userId     用户id
     * @param resourceId 资源id
     * @return boolean
     */
    @GetMapping("/course/checkViewLimit")
    boolean checkViewLimit(@RequestParam("userId") String userId, @RequestParam("resourceId") String resourceId);

    /**
     * 检查下发范围
     *
     * @param userId     用户id
     * @param resourceId 资源id
     * @return boolean
     */
    @GetMapping("/course/checkViewPermision")
    CourseViewLimitCheckResultDTO checkViewPermision(@RequestParam("userId") String userId,
        @RequestParam("resourceId") String resourceId,@RequestParam("bizId") String bizId, @RequestParam("activityId") String activityId);

    /**
     * 根据渠道id 获取一个渠道信息
     *
     * @param contentId 内容识别
     * @return {@link CerDitchDTO}
     */
    @GetMapping("/course/getDitch")
    CerDitchDTO getDitch(@RequestParam("contentId") String contentId);

    /**
     * 获取单个课程信息 -包含已删除数据
     *
     * @param id id
     * @return {@link CourseInfoDTO}
     */
    @GetMapping("/course/getOneCourseIncludeDel")
    CourseInfoDTO getOneCourseIncludeDel(@RequestParam("id") String id);

    /**
     * 根据课程ID集合 查询课程未删除，已经发布得课程Id
     */
    @GetMapping("/course/findCourseStatusByIdList")
    List<String> findCourseStatusByIdList(@RequestBody Collection<String> courseIdList);

    /**
     * * 2520获取课程完成数量
     */
    @GetMapping("/course/getIdpCourseStatistic")
    int getIdpCourseStatistic(@RequestParam("year") String year, @RequestParam("type") String type);

    /**
     * * 2520获取课件完成数量
     */
    @GetMapping("/course/getIdpCourseWareStatistic")
    int getIdpCourseWareStatistic(@RequestParam("year") String year, @RequestParam("type") String type);

    /**
     * 获取浏览数
     *
     * @param resourceId 资源id
     * @return {@link Map}<{@link String}, {@link Integer}>
     */
    @GetMapping("/course/getViewCount")
    Map<String, Long> getViewCount(@RequestParam("resourceId") Collection<String> resourceId);

    /**
     * 过滤获取已被删除的资源id
     *
     * @param courseIdList 资源集合
     */
    @GetMapping("/course/getInvalidCourseId")
    List<String> getInvalidCourseId(@RequestParam("courseIdList") Collection<String> courseIdList);

    /**
     * 获取某个用户是否完成某门课程
     */
    @GetMapping("/course/getUserCourseStatus")
    Boolean getUserCourseStatus(@RequestParam("userId") String userId, @RequestParam("id") String id);

    /**
     * 课件入库
     *
     * @return 入库id
     */
    @PostMapping("/course/coursewareStorage")
    String coursewareStorage(@RequestBody SaveCwLibraryDTO saveCwLibraryDTO);

    /**
     * 课件入库
     *
     * @return 入库id
     */
    @PostMapping("/course/coursewareStorageJob")
    String coursewareStorageJob(@RequestBody SaveCwLibraryDTO saveCwLibraryDTO);

    /**
     * 获取该用户已学课程id
     *
     * @param userId 用户id
     * @param ids    课程id集合
     * @return {@link List}<{@link String}>
     */
    @GetMapping("/course/getLearnedCourseIds")
    List<String> getLearnedCourseIds(@RequestParam("userId") String userId,
        @RequestParam("ids") Collection<String> ids);

    /**
     * 获取课程学习记录
     */
    @GetMapping("/course/getUserCourseRecord")
    UserCourseRecordDTO getUserCourseRecord(@RequestParam("userId") String userId,
        @RequestParam("courseId") String courseId);

    /**
     * 批量获取用户课程学习时长
     */
    @PostMapping("/course/getCourseLearnDuration")
    Map<String, Long> getCourseLearnDuration(@RequestBody Collection<String> userIds,
        @RequestParam("courseId") String courseId);

    @GetMapping("/course/voteCourseWare2Lib")
    String voteCourseWare2Lib(@RequestParam("id") String id);

    /**
     * 批量获取用户开发课程数量
     */
    @PostMapping("/getUserDevelopCourseNumMap")
    Map<String, Long> getUserDevelopCourseNumMap(@RequestBody Collection<String> userIds);

    /**
     * 根据被引用的课程复制一份课程
     *
     * @param courseId 被引用的课程信息主键id
     * @return 复制的课程的主键id
     */
    @GetMapping("/copyCourseByQuotedCourse")
    String copyCourseByQuotedCourse(@RequestParam("courseId") String courseId);


    /**
     * 发布或者取消发布课程
     *
     * @param publishType 0 取消发布  1发布
     */
    @PostMapping("/course/publishOrUnPublishCourse")
    void publishOrUnPublishCourse(@RequestParam("id") String id, @RequestParam("publishType") Integer publishType);

    /**
     * 保存课程目录
     */
    @PostMapping("/saveOrUpdateDirectory")
    void saveOrUpdateDirectory(@RequestBody SaveOrUpdateDirectoryDTO dto);


    @PostMapping("/course/getUserCourseHourByListParams")
    Map<String, Integer> getUserCourseHourByListParams(
        @RequestBody Collection<ResourceIdAndUserIdDTO> userIdAndContendId);


    /**
     * 获取资源不删除且发布
     */
    @GetMapping("/course/getResourceIsNotDeleteAndIsPublish")
    int getResourceIsNotDeleteAndIsPublish(@RequestParam("id") String resourceId);

    /**
     * 获取资源点赞数
     */
    @GetMapping("/course/getLikeCount")
    Map<String, Integer> getLikeCount(@RequestParam("courseIds") Collection<String> courseIds);

    /**
     *
     */
    @GetMapping("/course/getCommentCount")
    Map<String, CourseInfoDTO> getCommentCount(@RequestParam("courseIds") Set<String> courseIds);

    /**
     * 用户开发课程明细
     */
    @PostMapping("/userCourseList")
    PageInfo<LecturerCourseDTO> userCourseList(@RequestBody UserCourseListQuery query);

    /**
     * 取课程分类Map信息
     */
    @PostMapping("/getCourseCategoryMap")
    Map<String, CourseCategoryDTO> getCourseCategoryMap(@RequestBody Collection<String> categoryIds);

    /**
     * 根据课程分类名称获得分类id
     */
    @GetMapping("/getCourseIdByCategoryName")
    String getCourseIdByCategoryName(@RequestParam("categoryName") String categoryName);


    /**
     * 获取用户创建的课程
     */
    @GetMapping("/course/getCourseByUserId")
    List<CourseSimpleInfoDTO> getCourseByUserId(@RequestParam("userId") String userId);

    /**
     * 获取课程的简单信息
     */
    @PostMapping("/course/getRealityCourseListBatchIds")
    List<CourseInfoDTO> getRealityCourseListBatchIds(@RequestBody Collection<String> batchIds);

    /**
     * 获取课程完成状态
     */
    @PostMapping("/course/getCourseFinish")
    Map<String, Integer> getCourseFinish(@RequestBody ResourceUserQuery resourceUserQuery);

    /**
     * 获取总课时(分：向上取整)
     */
    @PostMapping("/course/getTotalCoursewareDuration")
    Long getTotalCoursewareDuration(@RequestBody Collection<String> courseIdList);

    /**
     * 批量获取课程基本信息
     */
    @PostMapping("/course/getCourseBaseInfo")
    Map<String, ResourceBaseDTO> getCourseBaseInfo(@RequestBody ResourceBaseQuery resourceBaseQuery);

    /**
     * 批量获取课程学学习详情
     */
    @PostMapping("/course/getCourseLearnDetail")
    List<CourseLearnDetailDTO> getCourseLearnDetail(@RequestBody CourseLearnQuery courseLearnQuery);

    /**
     * 获取创建的课程的简单信息
     */
    @GetMapping("/course/getCreateCourse")
    List<CourseInfoDTO> getCreateCourse(@RequestParam("userId") String userId);

    /**
     * 获取用户收藏课程数
     */
    @GetMapping("/getCourseCollectCount")
    Integer getCourseCollectCount(@RequestParam("userId") String userId);


    /**
     * 添加导入用户课程学习记录
     */
    @PostMapping("/course/createImportUserCourseRecord")
    void createImportUserCourseRecord(@RequestBody List<ImportUserCourseRecordDTO> userCourseRecordDtoList);

    /**
     * 获取课程是否删除
     */
    @GetMapping("/course/getCourseIsDelById")
    Integer getCourseIsDelById(@RequestParam("id") String id);

    /**
     * 获取课程基本信息
     */
    @GetMapping("/course/getCertificationContentList")
    Map<String, CertificationContentDTO> getCertificationContentList(
        @RequestParam("batchIds") Collection<String> batchIds);

    /**
     * 根据用户管理范围的组织信息和资源ID校验是否具有资源管理权限
     *
     * @param userManageAreaOrgId 户管理范围的组织信息ID列表
     * @param id                  课程ID
     * @return 0 无权限，1 有权限
     */
    @PostMapping("/course/checkCourseManagePermissions")
    Integer checkCourseManagePermissions(
        @RequestParam("userManageAreaOrgId") Collection<String> userManageAreaOrgId, @RequestParam("id") String id);

    /**
     * 根据课程编号获取
     */
    @GetMapping("/course/getByNo")
    CourseInfoDTO getByNo(@RequestParam("courseNo") String courseNo);

    /**
     * 获取课程的简单信息
     *
     * @param batchIds   课程id
     * @param courseName 课程名称
     * @return {@link CourseInfoDTO}
     */
    @PostMapping("/course/getCourseInfoBatchIds")
    List<CourseInfoDTO> getCourseInfoBatchIds(@RequestParam("batchIds") Collection<String> batchIds,
        @RequestParam(value = "courseName", required = false) String courseName);

    /**
     * 根据指定的课程ID列表，分页查询已发布课程信息
     *
     * @param courseListQuery 课程搜索对象
     * @return {@link CourseInfoDTO}
     */
    @PostMapping("/course/getPublishCourseInfoPageByIds")
    PageInfo<CourseInfoDTO> getPublishCourseInfoPageByIds(@RequestBody CourseListQuery courseListQuery);

    /**
     * 根据开始结束时间、课程名称查询用户创建的课程
     *
     * @param courseSummaryListQuery 课程搜索对象
     * @return {@link CourseInfoDTO}
     */
    @PostMapping("/course/getCourseDetailPageByUseId")
    PageInfo<LecturerCourseDetailDTO> getCourseSummaryPageByUseId(
        @RequestBody CourseSummaryListQuery courseSummaryListQuery);

    /**
     * 根据开始结束时间、课程名称导出用户创建的课程
     *
     * @param courseSummaryListQuery 课程搜索对象
     * @return {@link CourseInfoDTO}
     */
    @PostMapping("/course/exportCourseData")
    void exportCourseData(@RequestBody CourseSummaryListQuery courseSummaryListQuery);

    /**
     * 根据用id获取被推荐数量
     *
     * @param userId 用户id
     * @return 被推荐数量
     */
    @GetMapping("/course/getSuggestToMe")
    Long getSuggestToMe(@RequestParam("userId") String userId);


    /**
     * 获取课程是否被推荐的信息（获取一批课程是否被推荐给自己）
     * <p>
     * 此方法接收一个课程ID集合，并返回一个映射，其中包含每个课程ID及其是否被当前用户推荐的标志 使用 HashMap 来存储和返回结果，以提供高效的查找操作
     *
     * @param courseIdSet 课程ID的集合
     * @return 包含课程ID及其是否被推荐标志的映射
     */
    @PostMapping("/course/getCourseIsSuggest")
    Map<String, Integer> getCourseIsSuggest(@RequestParam("courseIdSet") Collection<String> courseIdSet);

    /**
     * 测评推荐课程加入用户学习计划
     *
     * @param courseIdSet 推荐课程Id列表
     * @param userId      目标用户
     */
    @PostMapping("/assessCourseToPlan")
    void assessCourseToPlan(@RequestParam("courseIdSet") Collection<String> courseIdSet,
        @RequestParam("userId") String userId);

    /**
     * 获取课程的浏览数、点赞数、及当前用户是否学完
     *
     * @param courseIdList 课程id
     * @return {@link MarchCourseTaskDTO}
     */
    @PostMapping("/getMarchCourseTaskInfoByCourseIdList")
    List<MarchCourseTaskDTO> getMarchCourseTaskInfoByCourseIdList(@RequestBody List<String> courseIdList);

    @GetMapping("/findUserCourseDurationByDate")
    Long findUserCourseDurationByDate(@RequestParam("date") @DateTimeFormat(iso = ISO.DATE_TIME) Date date);

    /**
     * 获取课程的删除信息
     * @param resourceId
     * @return ResourceDeleteInfoDTO
     */
    @GetMapping("/getCourseDeleteInfoById")
    ResourceDeleteInfoDTO getCourseDeleteInfoById(@RequestParam("resourceId") String resourceId);

    /**
     * 获取单个课程删除状态信息
     *
     * @param resourceId id
     * @return state 0-未删除 1-已删除
     */
    @GetMapping("/course/getResourceIsDel")
    int getResourceIsDel(@RequestParam("resourceId") String resourceId);

    /**
     * 获取资源点赞数
     */
    @GetMapping("/course/getCourseIsDel")
    Map<String, Integer> getCourseIsDel(@RequestParam("courseIds") Collection<String> courseIds);

    @GetMapping("/course/listPublishedCourseIds")
    List<String> listPublishedCourseIds(@RequestParam("courseIds") List<String> courseIds);


    /*
     * 根据用户ID集合查询指定课程的学习时长
     * @param userIds 用户ID集合
     * @param courseIds 课程ID集合
     * @return 用户累计学时集合
     */
    @PostMapping("/course/getCourseDurationMap")
    Map<String, Long> getCourseDurationMap(@RequestParam("userIds") Collection<String> userIds,
        @RequestParam("courseIds") Collection<String> courseIds);
}

