package com.wunding.learn.course.api.query;

import com.wunding.learn.common.bean.BasePageQuery;
import io.swagger.v3.oas.annotations.Parameter;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 课程查询对象
 *
 * <AUTHOR>
 * @date 2023/6/30
 */
@Data
@Accessors(chain = true)
public class CourseListQuery extends BasePageQuery {

    @Parameter(description = "课程id列表")
    private List<String> idList;
}
