package com.wunding.learn.course.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 用户课件课时统计（已学课时，总计课时）
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Data
@Schema(name = "UserCoursewareStudyInfoDTO", description = "用户课件课时统计")
@Accessors(chain = true)
public class UserCoursewareStudyInfoDTO implements Serializable {

    private static final long serialVersionUID = 721338460014983188L;

    @Schema(description = "序号")
    private Integer no;

    @Schema(description = "课件ID")
    private String id;

    @Schema(description = "课件名称")
    private String cwName;

    @Schema(description = "课程已学时长（小时，保留2位小数，不足0.01小时按0计算，否则四舍五入）")
    private BigDecimal duration;

    @Schema(description = "课程总计时长（小时，保留2位小数，不足0.01小时按0计算，否则四舍五入）")
    private BigDecimal totalDuration;
}
