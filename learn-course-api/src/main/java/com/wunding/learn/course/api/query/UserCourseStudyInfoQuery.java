package com.wunding.learn.course.api.query;

import com.wunding.learn.common.bean.BasePageQuery;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 用户指定课程学习时长统计查询对象
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Data
public class UserCourseStudyInfoQuery extends BasePageQuery implements Serializable {


    private static final long serialVersionUID = 8660605976934654028L;

    @Parameter(description = "课程id列表")
    @NotBlank(message = "课程id列表不可为空")
    private List<String> idList;

    /**
     * 用户ID
     */
    @Parameter(description = "用户ID")
    @NotBlank(message = "用户ID不可为空")
    private String userId;
}
