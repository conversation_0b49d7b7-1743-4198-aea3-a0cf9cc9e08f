package com.wunding.learn.user.login.service;

import com.wunding.learn.common.constant.user.UserErrorNoEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.user.api.dto.LoginUserInfo;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.service.UserFeign;
import com.wunding.learn.user.login.config.CasPasswordConfig;
import com.wunding.learn.user.login.greater.LoginService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * CasPasswordService测试类
 *
 * <AUTHOR>
 * @date 2024/12/26
 */
@ExtendWith(MockitoExtension.class)
class CasPasswordServiceTest {

    @Mock
    private CasPasswordConfig casPasswordConfig;

    @Mock
    private LoginService loginService;

    @Mock
    private UserFeign userFeign;

    @InjectMocks
    private CasPasswordService casPasswordService;

    private UserDTO mockUser;
    private LoginUserInfo mockLoginUserInfo;

    @BeforeEach
    void setUp() {
        mockUser = new UserDTO();
        mockUser.setId("test-user-id");
        mockUser.setLoginName("testuser");
        mockUser.setFullName("Test User");
        mockUser.setTelephone("***********");
        mockUser.setEmail("<EMAIL>");

        mockLoginUserInfo = new LoginUserInfo();
        mockLoginUserInfo.setUser(mockUser);
    }

    @Test
    void testAuthenticateAndGetUserInfo_ConfigDisabled() {
        // 配置功能禁用
        when(casPasswordConfig.isConfigValid()).thenReturn(false);

        // 执行测试
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            casPasswordService.authenticateAndGetUserInfo("testuser", "password");
        });

        // 验证结果
        assertEquals(UserErrorNoEnum.ERR_CAS_PASSWORD_CONFIG_ERROR.getErrorCode(), exception.getErrorCode());
    }

    @Test
    void testAuthenticateAndGetUserInfo_ConfigValidationOnly() {
        // 模拟配置有效但不执行实际HTTP调用
        when(casPasswordConfig.isConfigValid()).thenReturn(true);

        // 由于authenticateAndGetUserInfo方法涉及HTTP调用，
        // 在单元测试中我们只验证配置检查逻辑
        // 实际的HTTP调用测试应该在集成测试中进行

        // 验证配置检查通过后会继续执行
        // 这里我们期望会因为HTTP调用失败而抛出异常
        assertThrows(Exception.class, () -> {
            casPasswordService.authenticateAndGetUserInfo("testuser", "password");
        });

        // 验证配置检查被调用
        verify(casPasswordConfig).isConfigValid();
    }
}
