package com.wunding.learn.user.login.rest;

import com.wunding.learn.common.bean.Result;
import com.wunding.learn.user.api.dto.AccountSyncDTO;
import com.wunding.learn.user.api.dto.AccountSyncRespDTO;
import com.wunding.learn.user.api.dto.LoginUserInfo;
import com.wunding.learn.user.login.biz.ILoginBiz;
import com.wunding.learn.user.login.dto.AppInfoListDTO;
import com.wunding.learn.user.login.dto.BindUserReqDTO;
import com.wunding.learn.user.login.dto.CasSyncResult;
import com.wunding.learn.user.login.dto.ClaimAccountDTO;
import com.wunding.learn.user.login.dto.ClaimAccountRespDTO;
import com.wunding.learn.user.login.dto.JsapiTicketReqDTO;
import com.wunding.learn.user.login.dto.LoginRequestDTO;
import com.wunding.learn.user.login.dto.LoginRespDto;
import com.wunding.learn.user.login.dto.LoginSkipDTO;
import com.wunding.learn.user.login.dto.RefreshTokenDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 登录相关接口
 *
 * <AUTHOR>
 * @date 2022/2/16
 */
@Slf4j
@Validated
@RestController
@RequestMapping("${module.login.contentPath:/}")
@Tag(description = "登录", name = "LoginRest")
public class LoginRest {

    @Resource
    private ILoginBiz loginBiz;

    /**
     * 登录接口
     *
     * @param loginRequestDTO 登录参数
     * @return 登录信息
     */
    @PostMapping("/login")
    @Operation(
            operationId = "login_LoginRest",
            summary = "用户登录接口",
            parameters = {
                    @Parameter(in = ParameterIn.HEADER, schema= @Schema(type = "string"), name = "clienttype", example = "api", required = true),
                    @Parameter(in = ParameterIn.HEADER, schema= @Schema(type = "string"), name = "deviceid", example = "deviceId", required = false)
            },
            description = "<p><br></p><p style=\"text-align: start;\">用户登录接口</p><p style=\"text-align: start;\">一、登录类型：password-账号密码</p><p style=\"text-align: start;\">1.学员端请求体Demo：{\"type\":\"password\",\"userName\":\"admin\",\"password\":\"10ADC3949BA59ABBE56E057F20F883\"}</p><p style=\"text-align: start;\">2.管理端开启登录验证码请求体Demo（管理端关闭登录验证码情况下请求体同学员端）：{\"type\":\"password\",\"userName\":\"admin\",\"password\":\"10ADC3949BA59ABBE56E057F20F883\",\"captcha\":\"h2e5g\",\"uuid\":\"20230614100739a314cfc96f948a1eb43338\"}</p><p style=\"text-align: start;\">二、登录类型：sms-短信验证码</p><p style=\"text-align: start;\">暂未接入</p><p style=\"text-align: start;\">三、登录类型：oauth2-第三方</p><p style=\"text-align: start;\">1.企业微信</p><p style=\"text-align: start;\">请求体Demo：{\"type\":\"oauth2\",\"entryType\":0,\"code\":\"VSN2VxmWQrXxNUFvUfhYxr7b0S5yOTQh1AIM6s5Ug\",state:\"\"}</p><p style=\"text-align: start;\">2.微信公众号</p><p style=\"text-align: start;\">请求体Demo：{\"type\":\"oauth2\",\"entryType\":1,\"code\":\"VSN2VxmWQrXxNUFvUfhYxr7b0S5yOTQh1AIM6s5Ug\",state:\"\"}</p><p style=\"text-align: start;\">3.微信小程序（登录凭证校验与登录处理流程拆分，以响应错误码带参实现）</p><p style=\"text-align: start;\">1&gt;登录凭证校验</p><p style=\"text-align: start;\">请求体Demo: {\"type\":\"oauth2\",\"entryType\":2,\"code\":\"VSN2VxmWQrXxNUFvUfhYxr7b0S5yOTQh1AIM6s5Ug\",state:\"\"}</p><p style=\"text-align: start;\">响应Demo：{\"code\":2217,\"message\":\"用户唯一标识\",\"data\":\"woiD49CQAAnvZfOiGvYVtgn-AHFTDhjdYg\"}</p><p style=\"text-align: start;\">2&gt;登录处理流程</p><p style=\"text-align: start;\">请求体Demo: &nbsp;{\"type\":\"oauth2\",\"entryType\":2,\"openId\":\"woiD49CQAAnvZfOiGvYVtgn-AHFTDhjdYg\",state:\"\"}</p><p style=\"text-align: start;\">4.钉钉</p><p style=\"text-align: start;\">1&gt;企业内部应用免登</p><p style=\"text-align: start;\">请求体Demo：{\"type\":\"oauth2\",\"entryType\":3,\"code\":\"46362afa499c3b2fbac2e4e77c138b41\",state:\"1\"}</p><p style=\"text-align: start;\">2&gt;应用管理后台免登</p><p style=\"text-align: start;\">请求体Demo：{\"type\":\"oauth2\",\"entryType\":3,\"code\":\"46362afa499c3b2fbac2e4e77c138b41\",state:\"2\"}</p><p style=\"text-align: start;\">3&gt;登录第三方网站</p><p style=\"text-align: start;\">请求体Demo：{\"type\":\"oauth2\",\"entryType\":3,\"code\":\"46362afa499c3b2fbac2e4e77c138b41\",state:\"3\"}</p><p><br></p>"
    )
    public Result<LoginRespDto> login(@RequestBody LoginRequestDTO loginRequestDTO) {
        return loginBiz.login(loginRequestDTO);
    }

    /**
     * 系统登录-刷新令牌
     *
     * @param refreshTokenDTO 刷新令牌参数
     * @return 登录信息
     */
    @PostMapping("/refreshToken")
    @Operation(operationId = "refreshToken_LoginRest", summary = "系统登录-刷新令牌", description = "系统登录-刷新令牌")
    public Result<LoginRespDto> refreshToken(@RequestBody RefreshTokenDTO refreshTokenDTO) {
        return loginBiz.refreshToken(refreshTokenDTO);
    }

    /**
     * 第三方登录-获取企业jsapi_ticket
     *
     * @param jsapiTicketReqDTO {@link JsapiTicketReqDTO}
     * @return 企业jsapi_ticket
     */
    @PostMapping(value = "/jsapiTicket")
    @Operation(operationId = "jsapiTicket_LoginRest", summary = "第三方登录-获取企业jsapi_ticket", description = "第三方登录-获取企业jsapi_ticket")
    public Result<String> jsapiTicket(@RequestBody JsapiTicketReqDTO jsapiTicketReqDTO) {
        return Result.success(loginBiz.getJsapiTicket(jsapiTicketReqDTO));
    }

    /**
     * 第三方登录-绑定系统账号
     *
     * @param bindUserReqDTO {@link BindUserReqDTO}
     * @return 操作状态
     */
    @PostMapping(value = "/bindSysUser")
    @Operation(operationId = "bindSysUser_LoginRest", summary = "第三方登录-绑定系统账号", description = "第三方登录-绑定系统账号")
    public Result<Void> bindSysUser(@RequestBody BindUserReqDTO bindUserReqDTO) {
        loginBiz.bindSysUser(bindUserReqDTO);
        return Result.success();
    }

    /**
     * 获取当前系统第三方企业应用信息
     *
     * @return 应用信息
     */
    @GetMapping("/appInfo")
    @Operation(operationId = "appInfo_LoginRest", summary = "当前系统第三方企业应用信息", description = "当前系统第三方企业应用信息")
    public Result<AppInfoListDTO> appInfo() {
        return Result.success(loginBiz.getAppInfo());
    }

    /**
     * 获取当前登录的用户信息
     *
     * @return 用户信息
     */
    @GetMapping("/loginUserInfo")
    @Operation(operationId = "loginUserInfo_LoginRest", summary = "当前登录的用户信息", description = "当前登录的用户信息")
    public Result<LoginUserInfo> loginUserInfo() {
        return loginBiz.getLoginUserInfo();
    }

    /**
     * 获取端跳转的登录key
     *
     * @return 登录key
     */
    @GetMapping("/loginKey")
    @Operation(operationId = "loginKey_LoginRest", summary = "获取端跳转的登录key-有效期60s", description = "获取端跳转的登录key-有效期60s")
    public Result<String> getLoginKey() {
        return Result.success(loginBiz.getLoginKey());
    }

    /**
     * 跳转接口
     *
     * @param loginSkipDTO {@link LoginSkipDTO}
     * @return 登录信息
     */
    @PostMapping("/loginSkip")
    @Operation(operationId = "loginSkip_LoginRest", summary = "跳转接口", description = "跳转接口")
    public Result<LoginRespDto> loginSkip(@Valid @RequestBody LoginSkipDTO loginSkipDTO) {
        return Result.success(loginBiz.loginSkip(loginSkipDTO));
    }

    /**
     * CAS账户同步接口
     *
     * @param accountSyncDTO 同步请求参数
     * @return 同步结果
     */
    @PostMapping("/cas/account/sync")
    @Operation(
            operationId = "syncAccount_LoginRest",
            summary = "CAS账户同步接口",
            description = "用于CAS系统与本系统之间同步账户信息"
    )
    public CasSyncResult<AccountSyncRespDTO> syncAccount(@Valid @RequestBody AccountSyncDTO accountSyncDTO) {
        AccountSyncRespDTO result = loginBiz.syncAccount(accountSyncDTO);
        return CasSyncResult.success(result);
    }

    /**
     * CAS账号认领接口
     *
     * @param claimAccountDTO 认领请求参数
     * @return 认领结果
     */
    @PostMapping("/cas/account/claimAccount")
    @Operation(
            operationId = "claimAccount_LoginRest",
            summary = "CAS账号认领接口",
            description = "用于用户认领账号，验证账号密码是否正确"
    )
    public ClaimAccountRespDTO claimAccount(@Valid @RequestBody ClaimAccountDTO claimAccountDTO) {
        return loginBiz.claimAccount(claimAccountDTO);
    }

}