package com.wunding.learn.user.login.biz.impl;


import com.wunding.learn.common.bean.Result;
import com.wunding.learn.common.constant.http.SecureConstant;
import com.wunding.learn.common.constant.http.TokenConstant;
import com.wunding.learn.common.constant.other.ClientTypeEnum;
import com.wunding.learn.common.constant.other.Oauth2EntryTypeEnum;
import com.wunding.learn.common.constant.other.SSOLoginTypeEnum;
import com.wunding.learn.common.constant.user.UserErrorNoEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.other.SystemConfigCodeEnum;
import com.wunding.learn.common.enums.redis.UserRedisKeyEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.jwt.SecureUtil;
import com.wunding.learn.common.jwt.TokenInfo;
import com.wunding.learn.common.jwt.config.SysSingleConfig;
import com.wunding.learn.common.util.http.WebUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.util.signature.MD5Util;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.user.api.dto.AccountSyncDTO;
import com.wunding.learn.user.api.dto.AccountSyncRespDTO;
import com.wunding.learn.user.api.dto.LoginSessionDTO;
import com.wunding.learn.user.api.dto.LoginUserInfo;
import com.wunding.learn.user.api.dto.OrgDTO;
import com.wunding.learn.user.login.dto.ClaimAccountDTO;
import com.wunding.learn.user.login.dto.ClaimAccountRespDTO;
import com.wunding.learn.user.api.dto.ThirdAppConfigDTO;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.dto.third.CropInfoDTO;
import com.wunding.learn.user.api.service.CasSyncUserFeign;
import com.wunding.learn.user.api.service.LoginSessionFeign;
import com.wunding.learn.user.api.service.OrgFeign;
import com.wunding.learn.user.api.service.ParaFeign;
import com.wunding.learn.user.api.service.RoleFeign;
import com.wunding.learn.user.api.service.ThirdFeign;
import com.wunding.learn.user.api.service.UserFeign;
import com.wunding.learn.user.login.biz.ILoginBiz;
import com.wunding.learn.user.login.dto.AccessTokenDTO;
import com.wunding.learn.user.login.dto.AppInfoDTO;
import com.wunding.learn.user.login.dto.AppInfoListDTO;
import com.wunding.learn.user.login.dto.BindUserReqDTO;
import com.wunding.learn.user.login.dto.HangupInfoDTO;
import com.wunding.learn.user.login.dto.JsapiTicketReqDTO;
import com.wunding.learn.user.login.dto.LoginRequestDTO;
import com.wunding.learn.user.login.dto.LoginRespDto;
import com.wunding.learn.user.login.dto.LoginSkipDTO;
import com.wunding.learn.user.login.dto.PushInfoDTO;
import com.wunding.learn.user.login.dto.RefreshTokenDTO;
import com.wunding.learn.user.login.dto.SsoLoginTypeInfo;
import com.wunding.learn.user.login.greater.GrantBuilder;
import com.wunding.learn.user.login.greater.GrantParameter;
import com.wunding.learn.user.login.greater.GrantTypeEnum;
import com.wunding.learn.user.login.greater.ITokenGranter;
import com.wunding.learn.user.login.greater.LoginClientTypeEnum;
import com.wunding.learn.user.login.greater.LoginService;
import io.jsonwebtoken.Claims;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Service;

/**
 * <p> 登录 业务服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @since 2023-05-23
 */
@Slf4j
@Service("loginBiz")
public class LoginBizImpl implements ILoginBiz {

    /**
     * 组织分割符
     */
    private static final String ORG_LEVEL_PATH_SEPARATOR = "/";

    private static final String ADMIN = "admin";

    @Resource
    private OrgFeign orgFeign;
    @Resource
    private LoginSessionFeign loginSessionFeign;
    @Resource
    private LoginService loginService;
    @Resource
    @SuppressWarnings("rawtypes")
    private RedisTemplate redisTemplate;
    @Resource
    private SysSingleConfig sysSingleConfig;
    @Resource
    private ThirdFeign thirdFeign;
    @Resource
    private UserFeign userFeign;
    @Resource
    private RoleFeign roleFeign;
    @Resource
    private ParaFeign paraFeign;

    @Resource
    private UserDetailsService userDetailsService;

    @Resource
    private CasSyncUserFeign casSyncUserFeign;

    @Value("${cas.server.url}")
    private String casServerUrl;

    @Value("${cas.server.logout-url}")
    private String casLogoutUrl;

    @Value("${cas.server.login-url}")
    private String casLoginUrl;

    @Override
    @SuppressWarnings("unchecked")
    public Result<LoginRespDto> login(LoginRequestDTO loginRequestDTO) {
        String deviceId = Objects.toString(WebUtil.getHeader(TokenConstant.DEVICE_ID), StringUtil.newId());
        String clientType = WebUtil.getHeader(TokenConstant.CLIENT_TYPE);
        if (StringUtils.isBlank(clientType)) {
            throw new BusinessException(UserErrorNoEnum.CLIENT_TYPE_NULL);
        }
        // 登录端类型
        LoginClientTypeEnum loginClientType = LoginClientTypeEnum.get(clientType);
        if (null == loginClientType) {
            throw new BusinessException(UserErrorNoEnum.ERR_PARAMS);
        }

        // 登录类型
        GrantTypeEnum grantType = GrantTypeEnum.get(loginRequestDTO.getType());

        // 构建授权参数
        GrantParameter grantParameter = GrantParameter.builder().clientType(loginClientType).type(grantType)
            .userName(loginRequestDTO.getUserName()).password(loginRequestDTO.getPassword())
            .captcha(loginRequestDTO.getCaptcha()).captchaId(loginRequestDTO.getUuid())
            .entryType(loginRequestDTO.getEntryType()).code(loginRequestDTO.getCode())
            .weChatType(loginRequestDTO.getWeChatType())
            .openId(loginRequestDTO.getOpenId())
            .state(loginRequestDTO.getState())
            .encryptToken(loginRequestDTO.getEncryptToken())
            .build();

        // 获取授权方式
        ITokenGranter tokenGranter = GrantBuilder.getGranter(grantParameter.getType());
        // 验证授权
        LoginUserInfo loginUserInfo = tokenGranter.grant(grantParameter);
        // 处理授权用户信息
        return processLoginUserInfo(loginUserInfo, deviceId, clientType);
    }

    @Override
    @SuppressWarnings("unchecked")
    public Result<LoginRespDto> refreshToken(RefreshTokenDTO refreshTokenDTO) {
        String userId = refreshTokenToUserId(refreshTokenDTO.getRefreshToken());
        String deviceId = Objects.toString(WebUtil.getHeader(TokenConstant.DEVICE_ID), StringUtil.newId());
        String clientType = WebUtil.getHeader(TokenConstant.CLIENT_TYPE);
        if (userId == null) {
            throw new BusinessException(UserErrorNoEnum.ERR_NEED_LOGIN);
        }
        LoginUserInfo loginUserInfo = loginService.getLoginUserInfoByUserId(userId);
        if (loginUserInfo == null) {
            return Result.fail(UserErrorNoEnum.ERR_NEED_LOGIN);
        }
        TokenInfo tokenInfo = getToken(loginUserInfo, deviceId, clientType);
        TokenInfo refreshToken = getRefreshToken(loginUserInfo);
        // 构建返回对象
        LoginRespDto loginRespDto = buildResp(tokenInfo, refreshToken, loginUserInfo, deviceId);
        loginRespDto.setDeviceId(deviceId);

        if (refreshToken.getExpire() > 0) {
            String key = UserRedisKeyEnum.LOGIN_USER_INFO.getKey() + userId;
            redisTemplate.expire(key, refreshToken.getExpire(), TimeUnit.SECONDS);
        }
        return Result.success(loginRespDto);
    }

    @Override
    public String getJsapiTicket(JsapiTicketReqDTO jsapiTicketReqDTO) {
        Oauth2EntryTypeEnum oauth2EntryTypeEnum = Oauth2EntryTypeEnum.get(jsapiTicketReqDTO.getEntryType());
        if (null == oauth2EntryTypeEnum) {
            throw new BusinessException(UserErrorNoEnum.ERR_OAUTH2_ILLEGAL_ENTRY_TYPE);
        }
        String jsapiTicket = null;
        switch (oauth2EntryTypeEnum) {
            case WECOM:
                jsapiTicket = thirdFeign.getWeComJsapiTicket(UserThreadContext.getTenantId());
                break;
            case WECHAT_OFFICIAL_ACCOUNTS:
                jsapiTicket = thirdFeign.getWeChatOfficialAccountsJsapiTicket(UserThreadContext.getTenantId());
                break;
            case DING_TALK:
                jsapiTicket = thirdFeign.getDingTalkJsapiTicket(UserThreadContext.getTenantId());
                break;
            default:
        }
        return jsapiTicket;
    }

    @Override
    public void bindSysUser(BindUserReqDTO bindUserReqDTO) {
        Oauth2EntryTypeEnum oAuth2EntryTypeEnum = Oauth2EntryTypeEnum.get(bindUserReqDTO.getEntryType());
        if (null == oAuth2EntryTypeEnum) {
            throw new BusinessException(UserErrorNoEnum.ERR_OAUTH2_ILLEGAL_ENTRY_TYPE);
        }
        // 根据用户名获取用户
        UserDTO userDTO = userFeign.getUserByLoginName(bindUserReqDTO.getUserName());
        if (null == userDTO) {
            throw new BusinessException(UserErrorNoEnum.USER_NOT_EXISTS);
        }
        String userType;
        if (Oauth2EntryTypeEnum.WECOM.getType().equals(bindUserReqDTO.getEntryType())) {
            userType = oAuth2EntryTypeEnum.getType().toString() + bindUserReqDTO.getWeChatType();
        } else {
            userType = oAuth2EntryTypeEnum.getType().toString();
        }
        userFeign.bindSysUser(userType,
            userDTO.getId(),
            bindUserReqDTO.getThirdId());
    }

    @Override
    public AppInfoListDTO getAppInfo() {
        AppInfoListDTO appInfoListDTO = new AppInfoListDTO();
        // 获取第三方应用配置Map
        Map<Integer, List<ThirdAppConfigDTO>> thirdAppConfigMap = userFeign.getThirdAppConfigMap();
        // 第三方应用配置-是否允许账号密码登录 存放各集成平台属性中，任取一种类型即可。
        List<ThirdAppConfigDTO> thirdAppConfigList = new ArrayList<>();
        for (List<ThirdAppConfigDTO> list : thirdAppConfigMap.values()) {
            thirdAppConfigList.addAll(list);
        }
        log.info("thirdAppConfigList: " + thirdAppConfigList);
        // 存在一个已开启就算开启
        appInfoListDTO.setEnableAccountPasswordLogin(
            thirdAppConfigList.stream().anyMatch(item -> item.getEnableAccountPasswordLogin() == 1) ? 1 : 0);
        // 第三方应用配置信息 通过租户服务查询
        List<AppInfoDTO> appInfos = new ArrayList<>();
        thirdAppConfigMap.forEach((key, value) -> {
            for (ThirdAppConfigDTO thirdAppConfigDTO : value) {
                List<CropInfoDTO> cropInfo = thirdFeign.getCropInfo(UserThreadContext.getTenantId());
                for (CropInfoDTO cropInfoDTO : cropInfo) {
                    if (thirdAppConfigDTO.getAppType().equals(cropInfoDTO.getAppType())
                        && thirdAppConfigDTO.getMultiChannelType().equals(cropInfoDTO.getMultiChannelType())) {
                        AppInfoDTO appInfoDTO = new AppInfoDTO();
                        BeanUtils.copyProperties(cropInfoDTO, appInfoDTO);
                        // 第三方应用配置-是否启用二维码分享 0否 1是
                        appInfoDTO.setEnableQrCodeShare(
                            thirdAppConfigDTO.getEnableQrCodeShare());
                        appInfos.add(appInfoDTO);
                    }
                }
            }
        });

        //设置CAS单点登陆方式
        SsoLoginTypeInfo ssoLoginTypeInfo =getSsoLoginTypeInfoList();
        if(ssoLoginTypeInfo!=null){
            AppInfoDTO appInfoDTO = new AppInfoDTO();
            appInfoDTO.setAppType(9);
            appInfoDTO.setEnableQrCodeShare(0);
            appInfoDTO.setCasUrl(ssoLoginTypeInfo.getCasUrl());
            appInfoDTO.setDefaultCasLoginUrl(ssoLoginTypeInfo.getDefaultCasLoginUrl());
            appInfoDTO.setCasLogoutUrl(ssoLoginTypeInfo.getCasLogoutUrl());
            appInfoDTO.setCasEnableLogout(ssoLoginTypeInfo.getEnableLogout());
            appInfos.add(appInfoDTO);
        }

        appInfoListDTO.setAppInfoDTOList(appInfos);

        return appInfoListDTO;
    }

    private SsoLoginTypeInfo getSsoLoginTypeInfoList() {
        String paraValue = paraFeign.getParaValue(SystemConfigCodeEnum.CAS_LOGIN_ENABLE.getCode());
        if (Objects.equals("1",paraValue)) {
            String paramCasServiceUrl = paraFeign.getParaValue(SystemConfigCodeEnum.CAS_LOGIN_SERVICE_URL.getCode());
            String paramCasLoginUrl = paraFeign.getParaValue(SystemConfigCodeEnum.CAS_LOGIN_SERVICE_URL_LOGIN.getCode());
            String paramCasLogoutUrl = paraFeign.getParaValue(SystemConfigCodeEnum.CAS_LOGIN_SERVICE_URL_LOGOUT.getCode());
            String paramCasEnableLogout = paraFeign.getParaValue(SystemConfigCodeEnum.CAS_LOGIN_SERVICE_URL_ENABLE_LOGOUT.getCode());
            SsoLoginTypeInfo cas = new SsoLoginTypeInfo();
            cas.setType(SSOLoginTypeEnum.CAS.getType());
            cas.setCasUrl(casServerUrl);
            if(StringUtils.isNotBlank(paramCasServiceUrl)){
                cas.setCasUrl(paramCasServiceUrl);
            }
            cas.setCasLogoutUrl(casLogoutUrl);
            if(StringUtils.isNotBlank(paramCasLogoutUrl)){
                cas.setCasLogoutUrl(paramCasLogoutUrl);
            }
            String url = casLoginUrl;
            if(StringUtils.isNotBlank(paramCasLoginUrl)){
                url=paramCasLoginUrl;
            }
            cas.setEnableLogout(0);
            if(StringUtils.isNotBlank(paramCasEnableLogout)){
             cas.setEnableLogout(Integer.parseInt(paramCasEnableLogout));
            }
            cas.setDefaultCasLoginUrl(url + "?service=" + getCasService());
            return cas;
        }
        return null;
    }

    private String getCasService(){
        HttpServletRequest request = WebUtil.getRequest();
        if(request==null){
            return "";
        }
        String dynamicUrl = "";
        if(Objects.equals(request.getScheme(),"http") && request.getServerPort()==80) {
            dynamicUrl = request.getScheme() + "://" + request.getServerName();
        }else if(Objects.equals(request.getScheme(),"https") && request.getServerPort()==443){
            dynamicUrl = request.getScheme() + "://" + request.getServerName();
        }else{
            dynamicUrl = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort();
        }
        dynamicUrl += "/login/cas/casLogin";
        String clientType = request.getHeader(TokenConstant.CLIENT_TYPE);
        dynamicUrl += "?"+TokenConstant.CLIENT_TYPE+"="+clientType;
        return URLEncoder.encode(dynamicUrl, StandardCharsets.UTF_8);
    }

    @Override
    public Result<LoginUserInfo> getLoginUserInfo() {
        String userId = UserThreadContext.getUserId();
        LoginUserInfo loginUserInfo = loginService.getLoginUserInfoByUserId(userId);
        return Result.success(loginUserInfo);
    }


    @Override
    public AccessTokenDTO getTokenByAppIdAndSecret(String appId, String secret) {

        // 构建授权参数
        GrantParameter grantParameter = GrantParameter.builder().clientType(LoginClientTypeEnum.OPEN_API)
            .type(GrantTypeEnum.PASSWORD)
            .userName(appId).password(secret).build();
        // 获取授权方式
        ITokenGranter tokenGranter = GrantBuilder.getGranter(grantParameter.getType());
        // 验证授权
        LoginUserInfo loginUserInfo = tokenGranter.grant(grantParameter);

        // 获取令牌
        TokenInfo tokenInfo = getToken(loginUserInfo, "", LoginClientTypeEnum.OPEN_API.getType());
        UserThreadContext.setJwt(tokenInfo.getToken());

        // 设置redis
        //组装结果
        AccessTokenDTO accessTokenDTO = new AccessTokenDTO();
        accessTokenDTO.setAccess_token(tokenInfo.getToken());
        accessTokenDTO.setExpires_in(tokenInfo.getExpire());
        return accessTokenDTO;
    }

    @Override
    @SuppressWarnings("unchecked")
    public Result<LoginRespDto> processLoginUserInfo(LoginUserInfo loginUserInfo, String deviceId, String clientType) {
        // 登录端类型
        LoginClientTypeEnum loginClientType = LoginClientTypeEnum.get(clientType);

        log.info("loginUserInfo：{}, clientType:{}", JsonUtil.objToJson(loginUserInfo), clientType);

        // 检查权限
        assert loginClientType != null;
        checkPermission(loginUserInfo, loginClientType);

        // 获取令牌
        TokenInfo tokenInfo = getToken(loginUserInfo, deviceId, clientType);
        UserThreadContext.setJwt(tokenInfo.getToken());

        TokenInfo refreshToken = getRefreshToken(loginUserInfo);

        // 设置redis
        String key = UserRedisKeyEnum.LOGIN_USER_INFO.getKey() + loginUserInfo.getUser().getId();
        redisTemplate.opsForValue().set(key, loginUserInfo);
        if (StringUtils.isNotBlank(clientType) && StringUtils.isNotBlank(deviceId)) {
            redisTemplate.opsForHash()
                .put(UserRedisKeyEnum.LOGIN_USER_DEVICE_ID.getKey(), clientType + ":" + loginUserInfo.getUser().getId(),
                    deviceId);
        }
        if (refreshToken.getExpire() > 0) {
            redisTemplate.expire(key, refreshToken.getExpire(), TimeUnit.SECONDS);
        }

        // 构建返回对象
        LoginRespDto loginRespDto = buildResp(tokenInfo, refreshToken, loginUserInfo, deviceId);

        LoginSessionDTO loginSessionDTO = new LoginSessionDTO();
        loginSessionDTO.setLoginDate(new Date());
        loginSessionDTO.setAccept("*");

        String userAgent = WebUtil.getHeader("user-agent");
        if (StringUtils.isNotEmpty(userAgent) && userAgent.contains(TokenConstant.MOBILE)) {
            loginSessionDTO.setOs(ClientTypeEnum.CLIECNT_TYPE_APP.getName());
        } else {
            loginSessionDTO.setOs(ClientTypeEnum.CLIECNT_TYPE_PC.getName());
        }
        loginSessionDTO.setUserId(loginUserInfo.getUser().getId());
        loginSessionDTO.setSid(loginRespDto.getDeviceId());
        loginSessionFeign.save(loginSessionDTO);


        // 更新安全上下文
        UserDetails userDetails = userDetailsService.loadUserByUsername(loginUserInfo.getUser().getLoginName());
        UsernamePasswordAuthenticationToken authentication =
            new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());

        // 认证成功后，更新安全上下文
        SecurityContextHolder.getContext().setAuthentication(authentication);

        return Result.success(loginRespDto);
    }

    @Override
    @SuppressWarnings("unchecked")
    public String getLoginKey() {
        String authorization = WebUtil.getHeader(SecureConstant.BASIC_HEADER_KEY);
        if (StringUtils.isBlank(authorization)) {
            throw new BusinessException(UserErrorNoEnum.ERR_NEED_REFRESH_TOKEN);
        }
        String loginKey = StringUtil.newId();
        redisTemplate.opsForValue().set(loginKey, authorization, 60, TimeUnit.SECONDS);
        return loginKey;
    }

    @Override
    @SuppressWarnings("unchecked")
    public LoginRespDto loginSkip(LoginSkipDTO loginSkipDTO) {
        String authorization = (String) redisTemplate.opsForValue().get(loginSkipDTO.getLoginKey());
        if (StringUtils.isBlank(authorization)) {
            throw new BusinessException(UserErrorNoEnum.ERR_LOGIN_KEY_INVALID);
        }
        // 删除登录key
        redisTemplate.delete(loginSkipDTO.getLoginKey());
        if (authorization.startsWith(TokenConstant.BEARER)) {
            authorization = authorization.substring(TokenConstant.BEARER.length());
        }
        String userId = refreshTokenToUserId(authorization);
        String deviceId = Objects.toString(WebUtil.getHeader(TokenConstant.DEVICE_ID), StringUtil.newId());
        if (userId == null) {
            throw new BusinessException(UserErrorNoEnum.ERR_LOGIN_KEY_INVALID);
        }
        LoginUserInfo loginUserInfo = loginService.getLoginUserInfoByUserId(userId);
        if (loginUserInfo == null) {
            throw new BusinessException(UserErrorNoEnum.ERR_LOGIN_KEY_INVALID);
        }
        String clientType = GeneralJudgeEnum.NEGATIVE.getValue().equals(loginSkipDTO.getType()) ? ADMIN : "api";
        TokenInfo tokenInfo = getToken(loginUserInfo, deviceId, clientType);
        TokenInfo refreshToken = getRefreshToken(loginUserInfo);
        // 构建返回对象
        LoginRespDto loginRespDto = buildResp(tokenInfo, refreshToken, loginUserInfo, deviceId);
        loginRespDto.setDeviceId(deviceId);

        // 设置redis
        String key = UserRedisKeyEnum.LOGIN_USER_INFO.getKey() + loginUserInfo.getUser().getId();
        redisTemplate.opsForValue().set(key, loginUserInfo);
        if (StringUtils.isNotBlank(clientType) && StringUtils.isNotBlank(deviceId)) {
            redisTemplate.opsForHash()
                .put(UserRedisKeyEnum.LOGIN_USER_DEVICE_ID.getKey(), clientType + ":" + loginUserInfo.getUser().getId(),
                    deviceId);
        }
        if (refreshToken.getExpire() > 0) {
            redisTemplate.expire(key, refreshToken.getExpire(), TimeUnit.SECONDS);
        }

        LoginSessionDTO loginSessionDTO = new LoginSessionDTO();
        loginSessionDTO.setLoginDate(new Date());
        loginSessionDTO.setAccept("*");

        String userAgent = WebUtil.getHeader("user-agent");
        if (StringUtils.isNotEmpty(userAgent) && userAgent.contains(TokenConstant.MOBILE)) {
            loginSessionDTO.setOs(ClientTypeEnum.CLIECNT_TYPE_APP.getName());
        } else {
            loginSessionDTO.setOs(ClientTypeEnum.CLIECNT_TYPE_PC.getName());
        }
        loginSessionDTO.setUserId(loginUserInfo.getUser().getId());
        loginSessionDTO.setSid(loginRespDto.getDeviceId());
        loginSessionFeign.save(loginSessionDTO);
        return loginRespDto;

    }

    /**
     * 检查权限
     *
     * @param loginUserInfo   登陆的用户信息
     * @param loginClientType 登录端类型
     */
    public void checkPermission(LoginUserInfo loginUserInfo, LoginClientTypeEnum loginClientType) {
        String clientType = loginClientType.getType();
        // 无角色
        if (loginUserInfo.getRoles().isEmpty()) {
            throw new BusinessException(UserErrorNoEnum.NOT_PERMISSION);
        }

        // 只有学员身份且登录的是管理端
        if (loginUserInfo.getRoles().size() == 1 && loginUserInfo.getRoles().contains("10") && Objects.equals(
            clientType, ADMIN)) {
            throw new BusinessException(UserErrorNoEnum.STUDENT_CANNOT_LOGIN);
        }

        // 有管理角色但是无管理端路由权限
        if (Boolean.FALSE.equals(roleFeign.haveRouterByRoleId(loginUserInfo.getRoles())) && Objects.equals(clientType,
            ADMIN)) {
            throw new BusinessException(UserErrorNoEnum.NOT_HAVE_ROUTER);
        }
    }

    /**
     * 构建返回对象
     *
     * @param tokenInfo     访问令牌信息
     * @param refreshToken  刷新令牌信息
     * @param loginUserInfo 用户信息
     * @return 返回对象
     */
    public LoginRespDto buildResp(TokenInfo tokenInfo, TokenInfo refreshToken, LoginUserInfo loginUserInfo,
        String deviceId) {
        LoginRespDto loginRespDto = new LoginRespDto();
        loginRespDto.setToken(tokenInfo.getToken());
        loginRespDto.setRefreshToken(refreshToken.getToken());
        loginRespDto.setExpire(tokenInfo.getExpire());
        loginRespDto.setUserId(loginUserInfo.getUser().getId());
        loginRespDto.setUserName(loginUserInfo.getUser().getLoginName());
        loginRespDto.setChangePassword(
            Objects.equals(loginUserInfo.getUser().getPassword(), SecureConstant.SYSTEM_INIT_PASSWORD) ? 1 : 0);
        loginRespDto.setHangupInfo(getHangupInfo());
        loginRespDto.setPushInfo(getPushInfo(loginUserInfo.getUser().getId()));
        loginRespDto.setDeviceId(deviceId);
        loginRespDto.setIsAdmin(loginUserInfo.getUser().getIsAdmin());
        return loginRespDto;
    }

    /**
     * 获取防挂机相关配置
     *
     * @return 防挂机相关配置
     */
    private HangupInfoDTO getHangupInfo() {
        HangupInfoDTO hangupInfo = new HangupInfoDTO();
        hangupInfo.setIsHangup(0);
        hangupInfo.setHangupDurationTime(1);
        hangupInfo.setHangupCloseTimeout(10);
        return hangupInfo;
    }

    /**
     * 获取推送相关配置
     *
     * @return 推送相关配置
     */
    private PushInfoDTO getPushInfo(String userId) {
        PushInfoDTO pushInfo = new PushInfoDTO();
        pushInfo.setUid(userId);

        // 把 levelPath 转成tar
        OrgDTO orgDTO = orgFeign.getOrgByUserId(userId);
        String tags = "";
        if (orgDTO != null && StringUtils.isNotEmpty(orgDTO.getLevelPath())) {
            for (String s : orgDTO.getLevelPath().split(ORG_LEVEL_PATH_SEPARATOR)) {
                if (StringUtils.isNotBlank(s)) {
                    tags = StringUtils.join(tags, s, "|");
                }
            }
            tags = StringUtils.removeEnd(tags, "|");
        }
        pushInfo.setTags(tags);
        pushInfo.setPushType("JPush");
        return pushInfo;
    }

    /**
     * 生成令牌
     *
     * @param loginUserInfo 用户信息
     * @return 令牌
     */
    public TokenInfo getToken(LoginUserInfo loginUserInfo, String deviceId, String clientType) {
        // 设置jwt参数
        Map<String, String> param = new HashMap<>();
        param.put(TokenConstant.TOKEN_TYPE, TokenConstant.ACCESS_TOKEN);
        param.put(TokenConstant.USER_ID, Objects.toString(loginUserInfo.getUser().getId(), ""));
        param.put(TokenConstant.ORG_ID, Objects.toString(loginUserInfo.getUser().getOrgId(), ""));
        param.put(TokenConstant.TENANT_ID, UserThreadContext.getTenantId());

        param.put(TokenConstant.ROLE_ID, StringUtils.join(loginUserInfo.getRoles(), ","));
        param.put(TokenConstant.DEVICE_ID, deviceId);
        param.put(TokenConstant.CLIENT_TYPE, clientType);
        return SecureUtil.createJwt(param, "audience", "wunding", getJwtExpire() / 2,
            sysSingleConfig.getSignKey());
    }

    public TokenInfo getRefreshToken(LoginUserInfo loginUserInfo) {
        // 设置jwt参数
        Map<String, String> param = new HashMap<>();
        param.put(TokenConstant.TOKEN_TYPE, TokenConstant.REFRESH_TOKEN);
        param.put(TokenConstant.USER_ID, loginUserInfo.getUser().getId());
        return SecureUtil.createJwt(param, "audience", "wunding", getJwtExpire(),
            sysSingleConfig.getSignKey());
    }

    private String refreshTokenToUserId(String refreshToken) {
        try {
            Claims claims = SecureUtil.parseJwt(refreshToken, sysSingleConfig.getSignKey());
            if (claims == null) {
                return null;
            }
            return (String) claims.get(TokenConstant.USER_ID);
        } catch (Exception e) {
            log.warn("refreshToken error", e);
            throw new BusinessException(UserErrorNoEnum.ERR_NEED_LOGIN);
        }
    }

    public Integer getJwtExpire() {
        int expire = sysSingleConfig.getJwtExpire();
        try {
            String jwt = UserThreadContext.getJwt();
            UserThreadContext.setJwt(null);
            expire = Integer.parseInt(paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_JWT_EXPIRE.getCode()));
            if (expire < 600) {
                expire = sysSingleConfig.getJwtExpire();
                log.warn("参数里 jwt 时长小于10分钟，使用默认时长");
            }
            UserThreadContext.setJwt(jwt);
        } catch (NumberFormatException e) {
            log.warn("从参数里获取 jwt 时长出错，使用默认时长");
        }
        return expire;
    }

    /**
     * 根据用户ID获取用户信息
     * 主要用于 CAS 认证成功后获取用户详细信息
     *
     * @param userId 用户ID
     * @return {@link LoginUserInfo} 用户信息
     */
    @Override
    public LoginUserInfo getUserInfo(String userId) {
        log.info("根据用户ID获取用户信息: {}", userId);
        if (StringUtils.isBlank(userId)) {
            throw new BusinessException(UserErrorNoEnum.ERR_PARAMS);
        }
        
        try {
            // 调用 LoginService 中的方法获取用户信息
            return loginService.getLoginUserInfoByUserId(userId);
        } catch (Exception e) {
            log.error("获取用户信息失败: {}", userId, e);
            throw new BusinessException(UserErrorNoEnum.USER_NOT_EXISTS);
        }
    }

    @Override
    public AccountSyncRespDTO syncAccount(AccountSyncDTO accountSyncDTO) {
        log.info("CAS账户同步请求: {}", JsonUtil.objToJson(accountSyncDTO));
        
        // 调用Feign客户端
        return casSyncUserFeign.syncAccount(accountSyncDTO);
    }

    /**
     * 账号认领
     *
     * @param claimAccountDTO 账号认领请求参数
     * @return 认领结果
     */
    @Override
    public ClaimAccountRespDTO claimAccount(ClaimAccountDTO claimAccountDTO) {
        log.info("账号认领请求: {}", JsonUtil.objToJson(claimAccountDTO));
        
        if (claimAccountDTO == null) {
            return ClaimAccountRespDTO.fail("请求参数不能为空");
        }
        
        // 验证必填参数
        if (StringUtils.isBlank(claimAccountDTO.getAppkey())) {
            return ClaimAccountRespDTO.fail("认证标识不能为空");
        }
        
        if (StringUtils.isBlank(claimAccountDTO.getAccountName())) {
            return ClaimAccountRespDTO.fail("账号名不能为空");
        }
        
        if (StringUtils.isBlank(claimAccountDTO.getPassword())) {
            return ClaimAccountRespDTO.fail("密码不能为空");
        }
        
        if (StringUtils.isBlank(claimAccountDTO.getUsername())) {
            return ClaimAccountRespDTO.fail("用户名不能为空");
        }
        
        try {
            claimAccountDTO.setPassword(MD5Util.md5Pwd(claimAccountDTO.getPassword()));
            return validateAccountPassword(claimAccountDTO);
        } catch (Exception e) {
            log.error("账号认领失败", e);
            return ClaimAccountRespDTO.fail("认领失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证账号密码
     * 
     * @param claimAccountDTO 账号认领参数
     * @return 认领结果
     */
    private ClaimAccountRespDTO validateAccountPassword(ClaimAccountDTO claimAccountDTO) {
        try {
            // 调用登录服务验证账号密码
            GrantParameter parameter = GrantParameter.builder()
                    .type(GrantTypeEnum.PASSWORD)
                    .userName(claimAccountDTO.getAccountName())
                    .password(claimAccountDTO.getPassword())
                    .build();
            
            // 获取授权器
            ITokenGranter granter = GrantBuilder.getGranter(GrantTypeEnum.PASSWORD);
            if (granter == null) {
                log.error("获取授权器失败，不支持的授权类型: {}", GrantTypeEnum.PASSWORD);
                return ClaimAccountRespDTO.fail("密码验证失败");
            }
            
            // 验证账号密码
            LoginUserInfo loginUserInfo = granter.grant(parameter);
            if (loginUserInfo == null || loginUserInfo.getUser() == null) {
                log.error("账号密码验证失败: {}", claimAccountDTO.getAccountName());
                return ClaimAccountRespDTO.fail("账户不存在");
            }
            
            // 验证成功，返回成功结果
            Map<String, Object> extendInfo = new HashMap<>();
            extendInfo.put("orgid", loginUserInfo.getUser().getOrgId());
            
            ClaimAccountRespDTO respDTO = ClaimAccountRespDTO.success();
            respDTO.setExtend(extendInfo);
            return respDTO;
        } catch (Exception e) {
            log.error("账号认领过程中验证账号密码失败", e);
            return ClaimAccountRespDTO.fail("密码验证失败");
        }
    }
}
