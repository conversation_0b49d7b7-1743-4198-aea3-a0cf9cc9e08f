package com.wunding.learn.push.service.biz.impl;

import static com.wunding.learn.common.util.string.StringUtil.newId;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wunding.learn.common.constant.other.AvailableEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.util.assertion.CheckObjAllFieldsIsNullUtil;
import com.wunding.learn.push.api.dto.EnablePushNoticeChannelDTO;
import com.wunding.learn.push.api.enums.PushChannelEnum;
import com.wunding.learn.push.service.admin.dto.PushAppConfigDTO;
import com.wunding.learn.push.service.admin.dto.PushAppConfigListDTO;
import com.wunding.learn.push.service.admin.dto.PushAppConfigSwaggerDTO;
import com.wunding.learn.push.service.biz.IPushAppConfigBiz;
import com.wunding.learn.push.service.biz.IPushNoticeChannelBiz;
import com.wunding.learn.push.service.constant.PushPlatformEnum;
import com.wunding.learn.push.service.model.PushAppConfig;
import com.wunding.learn.push.service.service.IPushAppConfigService;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.service.UserFeign;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <p> App推送配置 业务服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @since 2023-05-07
 */
@Slf4j
@Service("pushAppConfigBiz")
public class PushAppConfigBizImpl implements IPushAppConfigBiz {

    @Resource
    private IPushAppConfigService pushAppConfigService;
    @Resource
    private IPushNoticeChannelBiz pushNoticeChannelBiz;
    @Resource
    private UserFeign userFeign;

    @Override
    public void savePushAppConfig(PushAppConfigSwaggerDTO dto) {
        PushAppConfigDTO android = dto.getAndroid();
        PushAppConfigDTO appleStore = dto.getAppleStore();
        PushAppConfigDTO iTunes = dto.getITunes();
        List<PushAppConfigDTO> paramList = new ArrayList<>(3);
        if (!CheckObjAllFieldsIsNullUtil.checkObjAllFieldsIsNull(android)) {
            paramList.add(android);
        }
        if (!CheckObjAllFieldsIsNullUtil.checkObjAllFieldsIsNull(appleStore)) {
            paramList.add(appleStore);
        }
        if (!CheckObjAllFieldsIsNullUtil.checkObjAllFieldsIsNull(iTunes)) {
            paramList.add(iTunes);
        }
        if (CollectionUtils.isEmpty(paramList)) {
            return;
        }
        boolean isEnablePushNoticeChannel = false;
        List<PushAppConfig> result = new ArrayList<>(3);
        for (PushAppConfigDTO appConfigDTO : paramList) {
            String id = appConfigDTO.getId();
            // 如果id不存在则赋予ID
            if (StringUtils.isBlank(id)) {
                appConfigDTO.setId(newId());
            }
            PushAppConfig pushAppConfig = new PushAppConfig();
            BeanUtils.copyProperties(appConfigDTO, pushAppConfig);
            pushAppConfig.setCreateBy(UserThreadContext.getUserId());
            pushAppConfig.setCreateTime(new Date());
            if (!StringUtils.isBlank(id)) {
                pushAppConfig.setUpdateBy(UserThreadContext.getUserId());
                pushAppConfig.setUpdateTime(new Date());
            }
            if (Objects.equals(AvailableEnum.AVAILABLE.getValue(), pushAppConfig.getIsAvailable())) {
                isEnablePushNoticeChannel = true;
            }
            result.add(pushAppConfig);
        }
        pushAppConfigService.saveOrUpdateBatch(result);
        // 开启/关闭通知渠道
        pushNoticeChannelBiz.enablePushNoticeChannel(
            EnablePushNoticeChannelDTO.builder().appType(PushChannelEnum.APP.getAppType()).isAvailable(
                    isEnablePushNoticeChannel ? AvailableEnum.AVAILABLE.getValue() : AvailableEnum.NOT_AVAILABLE.getValue())
                .build());
    }

    @Override
    public PushAppConfigListDTO getPushAppConfig() {
        PushAppConfigListDTO pushAppConfigListDTO = new PushAppConfigListDTO();
        pushAppConfigListDTO.setAndroid(constructReturnData(PushPlatformEnum.ANDROID.getValue()));
        pushAppConfigListDTO.setAppleStore(constructReturnData(PushPlatformEnum.APPLE_STORE.getValue()));
        pushAppConfigListDTO.setITunes(constructReturnData(PushPlatformEnum.ITUNES.getValue()));
        return pushAppConfigListDTO;
    }

    /**
     * 按前端要求，没数据时给他返回type和isAvailable=0信息
     *
     * @param appType app类型
     * @return {@link PushAppConfig}
     */
    private PushAppConfig constructReturnData(String appType) {
        PushAppConfig pushAppConfig = pushAppConfigService.getOne(
            new LambdaQueryWrapper<PushAppConfig>().eq(PushAppConfig::getAppType, appType));
        if (pushAppConfig == null) {
            pushAppConfig = new PushAppConfig();
            pushAppConfig.setAppType(appType);
            pushAppConfig.setIsAvailable(0);
        } else {
            UserDTO userInfo = userFeign.getUserById(pushAppConfig.getUpdateBy());
            if (Optional.ofNullable(userInfo).isPresent()) {
                pushAppConfig.setUpdateBy(userInfo.getFullName());
            }
        }
        return pushAppConfig;
    }

}
