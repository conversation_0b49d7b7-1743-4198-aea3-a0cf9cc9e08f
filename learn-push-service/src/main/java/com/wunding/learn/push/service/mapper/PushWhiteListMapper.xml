<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.push.service.mapper.PushWhiteListMapper">
    <!-- 开启二级缓存 -->
    <!--
<cache type="com.wunding.learn.common.mybatis.cache.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.push.service.mapper.PushWhiteListMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.push.service.model.PushWhiteList">
        <!--@Table push_white_list-->
        <id column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="is_del" jdbcType="TINYINT"
          property="isDel"/>
        <result column="create_by" jdbcType="VARCHAR"
          property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP"
          property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR"
          property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP"
          property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        user_id
             , is_del
             , create_by
             , create_time
             , update_by
             , update_time
    </sql>

    <select id="list" resultType="com.wunding.learn.push.service.admin.dto.PushWhiteListDTO">
        select user_Id
        from push_white_list
        <where>
            is_del = 0
            <if test="query.userIdList != null and query.userIdList.size() > 0">
                and user_id in
                <foreach collection="query.userIdList" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getAllWhiteUser" resultType="com.wunding.learn.common.dto.IdNumberInt">
        select user_id id, is_del number
        from push_white_list
    </select>

    <update id="updateIsDelById">
        update push_white_list set is_del=0
        where user_id=#{userId}
    </update>
</mapper>
