package com.wunding.learn.push.service.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p> 节假日
 *
 * <AUTHOR> href="mailto:<EMAIL>">oyy</a>
 * @since 2025年2月20日
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("holidays")
@Schema(name = "Holidays对象", description = "法定节假日表")
@Builder
public class Holidays implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键id
     */
    @Schema(description = "主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    /**
     * 法定节假日的年份 2025
     */
    @Schema(description = "年份 2025")
    @TableField("year")
    private Integer year;

    /**
     * 节假日日期 2025-01-01
     */
    @Schema(description = "节假日日期")
    @TableField("date")
    private Date date;

    /**
     * 节假日名称 如 国庆节
     */
    @Schema(description = "节假日名称")
    @TableField("name")
    private String name;

    /**
     * 是否为休息日（1: 是, 0: 否）；如是周六且该字段为0则需要正常补班。
     */
    @Schema(description = "是否为休息日 0否 1是")
    @TableField("is_offday")
    private Integer isOffday;

    @Schema(description = "节假日描述")
    @TableField("description")
    private String description;

    @Schema(description = "星期几")
    @TableField("weekday")
    private String weekday;
}
