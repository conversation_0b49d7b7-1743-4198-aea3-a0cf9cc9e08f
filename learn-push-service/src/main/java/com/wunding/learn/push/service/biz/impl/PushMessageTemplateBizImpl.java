package com.wunding.learn.push.service.biz.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.wunding.learn.common.constant.push.PushErrorNoEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.push.api.enums.PushChannelEnum;
import com.wunding.learn.push.service.admin.dto.PushMsgTemplateGlobalDTO;
import com.wunding.learn.push.service.admin.dto.PushMsgTemplateGlobalPutDTO;
import com.wunding.learn.push.service.admin.dto.PushNoticeEventPutDTO;
import com.wunding.learn.push.service.admin.query.PushMessageTemplateGlobalQuery;
import com.wunding.learn.push.service.biz.IPushMessageTemplateBiz;
import com.wunding.learn.push.service.model.PushMessageTemplateGlobal;
import com.wunding.learn.push.service.model.PushNoticeChannel;
import com.wunding.learn.push.service.model.PushNoticeEvent;
import com.wunding.learn.push.service.service.IPushMessageTemplateGlobalService;
import com.wunding.learn.push.service.service.IPushNoticeChannelService;
import com.wunding.learn.push.service.service.IPushNoticeEventService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * <p> 推送消息模板 业务服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @since 2023-07-04
 */
@Slf4j
@Service("pushMessageTemplateBiz")
public class PushMessageTemplateBizImpl implements IPushMessageTemplateBiz {

    @Resource
    private IPushMessageTemplateGlobalService pushMessageTemplateGlobalService;
    @Resource
    private IPushNoticeEventService pushNoticeEventService;

    @Resource
    private PushNoticeEventBizImpl pushNoticeEventBiz;

    @Resource
    private IPushNoticeChannelService pushNoticeChannelService;

    @Override
    public PushMsgTemplateGlobalDTO getPushMsgTemplateGlobal(
        PushMessageTemplateGlobalQuery messageTemplateGlobalQuery) {
        PushMsgTemplateGlobalDTO dto = new PushMsgTemplateGlobalDTO();
        PushMessageTemplateGlobal pushMessageTemplateGlobal = pushMessageTemplateGlobalService.getOne(
            new LambdaQueryWrapper<PushMessageTemplateGlobal>().select(PushMessageTemplateGlobal::getId,
                    PushMessageTemplateGlobal::getEnableContentCoverImg, PushMessageTemplateGlobal::getPushTitle,
                    PushMessageTemplateGlobal::getPushContent, PushMessageTemplateGlobal::getDefaultAutomaticPushTime)
                .eq(PushMessageTemplateGlobal::getLanguage, messageTemplateGlobalQuery.getLanguage())
                .eq(PushMessageTemplateGlobal::getEventId, messageTemplateGlobalQuery.getEventId())
                .eq(PushMessageTemplateGlobal::getChannelId, messageTemplateGlobalQuery.getChannelId()));
        if (pushMessageTemplateGlobal == null) {
            throw new BusinessException(PushErrorNoEnum.CHANNEL_NOT_EXISTS);
        }
        BeanUtils.copyProperties(pushMessageTemplateGlobal, dto);

        // 查询通知事件
        PushNoticeEvent eventDTO = pushNoticeEventService.getById(messageTemplateGlobalQuery.getEventId());
        if (eventDTO == null) {
            throw new BusinessException(PushErrorNoEnum.EVENT_NOT_EXISTS);
        }
        // APP不支持链接，过滤掉
        PushNoticeChannel noticeChannel = pushNoticeChannelService.getById(messageTemplateGlobalQuery.getChannelId());
        if (noticeChannel == null) {
            throw new BusinessException(PushErrorNoEnum.CHANNEL_NOT_EXISTS);
        }
        if (PushChannelEnum.APP.getValue().equals(noticeChannel.getCode()) || PushChannelEnum.WECOM.getValue()
            .equals(noticeChannel.getCode())) {
            dto.setContentLabel(pushNoticeEventBiz.filerLinkContentLabel(eventDTO.getContentLabel()));
            dto.setContentLabelEn(pushNoticeEventBiz.filerLinkContentLabel(eventDTO.getContentLabelEn()));
        } else {
            dto.setContentLabel(eventDTO.getContentLabel());
            dto.setContentLabelEn(eventDTO.getContentLabelEn());
        }

        return dto;
    }

    @Override
    public void updatePushMsgTemplateGlobal(PushMsgTemplateGlobalPutDTO putDTO) {
        // APP不支持链接，过滤掉
        PushNoticeChannel noticeChannel = pushNoticeChannelService.getById(putDTO.getChannelId());
        if (noticeChannel == null) {
            throw new BusinessException(PushErrorNoEnum.CHANNEL_NOT_EXISTS);
        }
        String pushContent = putDTO.getPushContent();
        if ((PushChannelEnum.APP.getValue().equals(noticeChannel.getCode()) || PushChannelEnum.WECOM.getValue()
            .equals(noticeChannel.getCode())) && StringUtils.isNotEmpty(pushContent) && pushContent.contains("链接}}")) {
            throw new BusinessException(PushErrorNoEnum.APP_CAN_NOT_PUSH_LINK);
        }
        PushMessageTemplateGlobal templateGlobal = pushMessageTemplateGlobalService.getById(putDTO.getId());
        BeanUtils.copyProperties(putDTO, templateGlobal);
        // 更新全局消息模板信息
        pushMessageTemplateGlobalService.updateById(templateGlobal);
        if (null != putDTO.getDefaultAutomaticPushTime()) {
            // 同步更新同事件全局模板默认自动发送时间
            pushMessageTemplateGlobalService.update(new LambdaUpdateWrapper<PushMessageTemplateGlobal>().set(
                    PushMessageTemplateGlobal::getDefaultAutomaticPushTime, putDTO.getDefaultAutomaticPushTime())
                .eq(PushMessageTemplateGlobal::getEventId, templateGlobal.getEventId()));
            // 同步更新通知事件默认自动发送时间
            pushNoticeEventService.update(
                new LambdaUpdateWrapper<PushNoticeEvent>().set(PushNoticeEvent::getDefaultAutomaticPushTime,
                    putDTO.getDefaultAutomaticPushTime()).eq(PushNoticeEvent::getId, templateGlobal.getEventId()));
        }
    }

    @Override
    public void enableDefaultAutomaticPush(PushNoticeEventPutDTO putDTO) {
        pushMessageTemplateGlobalService.update(new LambdaUpdateWrapper<PushMessageTemplateGlobal>().set(
                PushMessageTemplateGlobal::getEnableDefaultAutomaticPush, putDTO.getEnableDefaultAutomaticPush())
            .eq(PushMessageTemplateGlobal::getEventId, putDTO.getEventId())
            .eq(PushMessageTemplateGlobal::getChannelId, putDTO.getChannelId()));
    }

}
