package com.wunding.learn.push.service.component;

import com.wunding.learn.common.viewlimit.constant.LimitTable;
import com.wunding.learn.common.viewlimit.service.impl.BaseViewLimitServiceImpl;
import com.wunding.learn.push.service.model.PushViewLimit;
import com.wunding.learn.push.service.service.IPushViewLimitService;
import java.util.List;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <p>推送服务下发范围组件</p>
 */
@Component("pushViewLimitComponent")
public class PushViewLimitComponent extends BaseViewLimitServiceImpl<PushViewLimit> {

    @Resource
    private IPushViewLimitService pushViewLimitService;

    public PushViewLimitComponent() {
        super(LimitTable.PushViewLimit);
    }

    @Override
    public void saveBatch(List<PushViewLimit> baseViewLimits) {
        pushViewLimitService.saveBatch(baseViewLimits);
    }

    @Override
    public void removeBatchByIds(List<PushViewLimit> baseViewLimits) {
        pushViewLimitService.removeBatchByIds(baseViewLimits);
    }

}
