package com.wunding.learn.push.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p> 推送模块dto对象
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @since 2023-07-18
 */
@Data
@Schema(name = "PushModuleDTO", description = "推送模块dto对象")
public class PushModuleDTO {

    @Schema(description = "模块code")
    private String moduleCode;

    @Schema(description = "模块名称")
    private String moduleName;

}
