spring:
  datasource:
    driverClassName: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************
    username: single
    password: lFoKksnOQgvXcDf96K4m
    dynamic:
      primary: DS_STATISTIC
      strict: false
      datasource:
        DS_STATISTIC:
          driverClassName: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************
          username: single
          password: lFoKksnOQgvXcDf96K4m
        DS_PROJECT:
          driverClassName: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************
          username: single
          password: lFoKksnOQgvXcDf96K4m
        DS_FORUM:
          driverClassName: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************
          username: single
          password: lFoKksnOQgvXcDf96K4m
        DS_PROMOTED_GAME:
          driverClassName: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************
          username: single
          password: lFoKksnOQgvXcDf96K4m
        DS_EXCITATION:
          driverClassName: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************
          username: single
          password: lFoKksnOQgvXcDf96K4m
        DS_USER:
          driverClassName: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************
          username: single
          password: lFoKksnOQgvXcDf96K4m
        DS_EXAM:
          driverClassName: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************
          username: single
          password: lFoKksnOQgvXcDf96K4m
        DS_COURSE:
          driverClassName: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************
          username: single
          password: lFoKksnOQgvXcDf96K4m
        DS_COMMENT:
          driverClassName: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************
          username: single
          password: lFoKksnOQgvXcDf96K4m
        DS_SPECIAL:
          driverClassName: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************
          username: single
          password: lFoKksnOQgvXcDf96K4m
        DS_LIVE:
          driverClassName: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************
          username: single
          password: lFoKksnOQgvXcDf96K4m
        DS_SURVEY:
          driverClassName: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************
          username: single
          password: lFoKksnOQgvXcDf96K4m
        DS_INFO:
          driverClassName: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************
          username: single
          password: lFoKksnOQgvXcDf96K4m
        DS_MARKET:
          driverClassName: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************
          username: single
          password: lFoKksnOQgvXcDf96K4m
        DS_LECTURER:
          driverClassName: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************
          username: single
          password: lFoKksnOQgvXcDf96K4m
        DS_EVALUATION:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************
          username: single
          password: lFoKksnOQgvXcDf96K4m
        DS_CERTIFICATION:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************
          username: single
          password: lFoKksnOQgvXcDf96K4m
        DS_APPRAISE:
          driverClassName: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************
          username: single
          password: lFoKksnOQgvXcDf96K4m
        DS_RECRUITING:
          driverClassName: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************
          username: single
          password: lFoKksnOQgvXcDf96K4m
        DS_PUSH:
          driverClassName: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************
          username: single
          password: lFoKksnOQgvXcDf96K4m
        DS_READING:
          driverClassName: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************
          username: single
          password: lFoKksnOQgvXcDf96K4m
        DS_EXAMPLE:
          driverClassName: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************
          username: single
          password: lFoKksnOQgvXcDf96K4m
        DS_PLAN:
          driverClassName: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************
          username: single
          password: lFoKksnOQgvXcDf96K4m
        DS_TRAIN:
          driverClassName: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************
          username: single
          password: lFoKksnOQgvXcDf96K4m
        DS_APPLY:
          driverClassName: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************
          username: single
          password: lFoKksnOQgvXcDf96K4m
        DS_OPERATION:
          driverClassName: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************
          username: single
          password: lFoKksnOQgvXcDf96K4m

  elasticsearch:
    uris: ************:30403
    username: elastic
    password: y8h7M529U324kXT9Mz5zHHTG

  data:
    redis:
      host: 127.0.0.1
      port: 6379
      database: 8
      cache.database: 9
      password:
      timeout: 10000

  rabbitmq:
    host: ************
    port: 30672
    virtual-host: /dev
    username: guest
    password: guest

  # 邮件服务器设置
  mail:
    host: smtp.wunding.com
    port: 25
    username: <EMAIL>
    password: wdjava20170713
    properties:
      auth: false
      from: <EMAIL>
      timeout: 100000
      starttls:
        enable: false

xxl:
  job:
    admin:
      addresses: http://************:8004/xxl-job-admin
      username: admin
      password: 123456
    executor:
      appName: ${spring.application.name}
      ip:
      port: 9999
      logPath: /data/applogs/xxl-job/jobhandler
      logRetentionDays: -1
    accessToken: vopeqn943epfaspdf
    group: 2

learn:
  service:
    learn-websocket: "http://websocket:8080"
    ai-maxkb-service: "http://************:8081/api"

app:
  bizLogEsIndexName: biz-log
  courseEsIndexName: es_course_saas
  signKey: bladexisapowerfulmicroservicearchitectureupgradedandoptimizedfromacommercialproject
  privateKey: MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAKsnfFIV7o9_KITb0_M4qZO1sf1bjp9Sbuiz50TGPFg_fx8Buny8WQflyjIHkO6Z6HRwhGmjoL1Kz5Eknn64pZLV7v3i988C6CHCYHSbT7rc_Xiy6z4v3bEi0WiMQC_yPQDIEVP6y07GDz6zdYkutOayJcEywcU0xXcnfth6CCpvAgMBAAECgYBsJnNEW193hV5RNadklXVyROnHsscYnbo_iQ6mQq13BgiJy0nP8CRB_U4a9vT6EH72tPK23hKACnnGuWD9qifU7JRY1Vl8Rud3wkMPmfl_ocowe43OegmuLWknjZXwuGH_z1wnxZ2rDOx4G5T2P7IapKaYaTXzjGHzph403-xZgQJBANOLpOY95sQOFQq0bhmQDJosaEXEhIEo-5TKiEdneVSyu261NGMlwdRY3rEFyteT5GJOawkj8bqm0mq8ReX4E9ECQQDPHvGRXxMLt7ZCvqIoYMry6XsDprNNxknyq6CBZOCQHwczjWKd3nfiqBONi3OJZaMYa4UL4UCVXBcZkwD4yyo_AkAYYvnAfRRUN5dfY4tpsPxy7XmbyVrJFPNjpLFvIdOP2wbWbVc7sseUdiY93AAVN_xVBNs784PTU5XgLhIUv7NxAkBAkpUdjVaSwKPCC0zi6cpcEQ6ZBM_B36AOWBOiQ6D_Ta0hFWM5dLJLp7rw1hkfLOC8LEk-eut3pU5OWtZiunRhAkBySxnRjxbHx7N6TrNvwt1DDtiiDMBRIZlrqNvnk0-aQ-qc45anyjE4-EOCC6inAVgWSGqpweBXmyN-c8Sdttn1
  publicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCrJ3xSFe6PfyiE29PzOKmTtbH9W46fUm7os-dExjxYP38fAbp8vFkH5coyB5Dumeh0cIRpo6C9Ss-RJJ5-uKWS1e794vfPAughwmB0m0-63P14sus-L92xItFojEAv8j0AyBFT-stOxg8-s3WJLrTmsiXBMsHFNMV3J37YeggqbwIDAQAB
  jwtExpire: 180000
  single:
    - web
    - api
  user:
    maxCount: 300000
  j-push:
    appKey: 93eb6aae084fdee17f1fa3f7
    masterSecret: 44d339adcae786c6a23df0d6
    # 是否生产模式
    productionMode: false
  kettle:
    execPath: classpath:kettle/mysql
    pluginsPath: classpath:kettle/plugins
  storageType: 1
  intranetEndPoint: https://oss-test.wdxuexi.com
  endPoint: https://oss-test.wdxuexi.com
  accessKey: Rh2BL2nK9RWjqiPX
  secretKey: Xu0uU8xiDtr7bnmjpMVa2N7ZxPgGuLEn
  bucketName: single
  location: /data/
  root: single
  zuoKeTemplatePath: /template/zuoke
  transCodingUrl: http://wdweike.com/wps2html/uploadServlet
  ffmpegPath: ffmpeg
  staticBaseUrl: https://oss-test.wdxuexi.com
  # 转码消息者数量
  transConcurrency: 10
  officeTransType: 0
  pathMode : 1
  needContentType: 1
  password:
    default: WunDing@1996
    new: WunDing@2024
  # 企业微信服务商
  wecom:
    # 服务商CorpID
    corpId: wx2b7c2a1edd62766d
    # 服务商密钥
    providerSecret: ORgJE6yo3FhANvTozh1lqzdIpjcsw6i913hrZZZ5Ymiw5NztVDs0wpR2cMY-Zp8A
    # 服务商Token
    providerToken: fTytOfdFDIL
    # 服务商EncodingAESKey
    providerEncodingAesKey: o17xXLP2sxuWywytza1lfaEIJKoS6qoLasVVju0m2M4
    # 代开发应用模板ID
    suiteId: dk8ed8656276290ff0
    # 代开发应用模板密钥
    suiteSecret: oMGCK2e59EwwT61pf74biNgYCtKAyyY3j4sklTDH8NU
    # 代开发应用模板Token
    suiteToken: mIbgdxcp1oty2wEPQE2T
    # 代开发应用模板EncodingAESKey
    suiteEncodingAesKey: KcYoHieUdx7SkHoNDcDEgLIH9N5QBMoHHnct7l4ULCZ
    # 第三方应用
    thirdAppSuiteId: wx7a6dac360b4b956c
    # 第三方应用
    thirdAppSuiteSecret: 04JJBmBp3GjUY1oTcWpyNqwe3vnwY6a_8AI4m8i0sI4


  pdf2html:
    threadNum: 8
    home: D:\ffmpeg\bin
    bin: D:\pdf2htmlEX\pdf2htmlEX.exe
    dataDir: D:\pdf2htmlEX\pdf2htmlEX_data

  # 展示互动直播配置
  live:
    url: http://wending888.gensee.com/integration/site
    username: <EMAIL>
    password: 888888
    adminUsername: <EMAIL>
    adminPassword: 888888

  # 保利威直播配置 https://dev.polyv.net/2020/liveproduct/l-api/zhsz/sso/  (线上接口文档)
  polyvLive:
    appId: ftubxq8gnf
    userId: 383edd5060
    appSecret: 5b4858f292dc41db8a7788b4d8517d7c
    # 频道的相关设置前置路径（创建、删除）
    basicSettingPrefix: http://api.polyv.net/live/v3/channel/basic/
    # 直播观看链接前缀
    watchPrefix: https://live.polyv.cn/watch/
    # 频道单点登录前缀
    signInPrefix: https://live.polyv.net/teacher/auth-login?channelId=%s&token=%s&redirect=
    # 设置频道单点token
    setChannelToken: https://api.polyv.net/live/v2/channels/%s/set-token
    # 设置频道回放开关
    setChannelPlayback: http://api.polyv.net/live/v3/channel/playback/set-setting
    # 直播回放列表
    channelPlaybackList: http://api.polyv.net/live/v2/channel/recordFile/%s/playback/list
    # 批量删除直播
    deleteChannel: http://api.polyv.net/live/v3/channel/basic/batch-delete
    # 频道观看日志
    viewLog: http://api.polyv.net/live/v2/statistics/%s/viewlog
    # 删除回放 新增
    deleteVod: http://api.polyv.net/live/v2/channel/recordFile/%s/playback/delete
    # 修改直播名称 新增
    updateName: http://api.polyv.net/live/v2/channels/%s/update
    # 修改频道转存结果回调设置
    setChannelCallback: http://api.polyv.net/live/v3/channel/callback/update-setting
    # 查询后台频道统计信息
    channelStatistic: http://api.polyv.net/live/v4/channel/statistics/channel-statistic
    # 服务端口 gree-uat为9443，固定写死，其它环境变了，就手动改配置
    serverPort: 443

  # 小鱼直播
  xylink:
    apiHost: https://sdkapi.xylink.com
    enterpriseID: a81909136b88d2f7db63baa5645902e3d75b9e5c
    clientID: I4KdkN4FF8cOdGvxre7aKK33
    clientSecret: MyzQEeSpV8q8WwmDsBoManqxEU6iR39H

  # 百度配置
  baiduAiPlatform:
    clientKey: waVk9BCAwcji4HszKT9oshf3
    clientSecret: rDMfoppsU2mYQnhoGsxlTzVnSnW5tRFW

cas:
  server:
    url: http://cas.wdxuexi.com:8181/cas
    login-url: ${cas.server.url}/login
    logout-url: ${cas.server.url}/logout
  client:
    host-url: http://*************:28002/admin/
    service-url: http://*************:28002/login/cas/casLogin
    validation-type: CAS3

debug: false

operation:
  domain: console.sit.wdxuexi.com

opentelemetry:
  traces:
    exporter: otlp
  service:
    name: ${spring.application.name}
  jaeger:
    endpoint: http://************:30915
    protocol: grpc
  instrumentation:
    feign:
      enabled: true
    spring-webmvc:
      enabled: true

# 广告位数量
adsImageNum: 2

tenant:
  type: 1

############# baidu API配置 #############
baidu:
  tokenUrl: https://aip.baidubce.com/oauth/2.0/token
  createUrl: https://aip.baidubce.com/rpc/2.0/aasr/v1/create
  queryUrl: https://aip.baidubce.com/rpc/2.0/aasr/v1/query
  accessKey: c5ZLGbjHz1QUZSnAoKUYx2p0
  secretKey: omt1SGcOafO7CWc01zf5UhykosZpFmUz

# 人脸识别
face:
  appId: 35784184
  apiKey: iBVaHLjs8gorN529LmTHXMjH
  secretKey: cBQrBXbap3yKBNnIIRqOulCUOXFN0RsM

############# swagger 配置 #############
springdoc:
  version: '@springdoc.version@'
  api-docs:
    enabled: true

dify:
  api:
    url: http://************:8283/v1/workflows/run
    topicKey: app-NygBv18ItBRLFwJO8u8Mex4I
    abstractKey: app-5D7zm44JuWDarF4JvxYH7kCy
    aiRecognizeTopicKey: app-Kc73f5qk3o4SExnBF9oXpCPC
    cwOutlineKey: app-mGLQYeeIGMDKpIBvQGySGoey

  baseUrl: http://************:8283
  email: <EMAIL>
  password: wunding1996*
  templateId: 4828c598-7d98-433a-bffa-4b3c5180da65