package com.wunding.learn.user.api.dto.topic;

import com.wunding.learn.file.api.dto.NamePath;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 个人赛主页dto对象
 *
 * <AUTHOR>
 * @date 2023/09/28
 */
@Data
@Accessors(chain = true)
@Schema(name = "HomePageDTO", description = "首页dto对象")
public class HomePageDTO {

    @Schema(description = "当前页id")
    private String id;

    @Schema(description = "主题id")
    private String topicId;

    /**
     * 针对个人赛首页的这个属性,对应 TopicPageAttribute 实体的同名属性，但意义不太相同
     * <p>
     * 以个人赛首页注释的参数意义为准
     * <p>
     * 注意使用封面图片得学员端接口针对处理逻辑
     */
    @Schema(description = "0-使用封面图片,1-使用自定义图片", hidden = true)
    private Integer useImage;

    @Schema(description = "头部图片")
    private PageAttributeDTO homeHeadImg;

    @Schema(description = "个人赛首页背景图片")
    private PageAttributeDTO homeBackgroundImg;

    @Schema(description = "排行版")
    private PageAttributeDTO homeRankImg;

    @Schema(description = "我要组队")
    private PageAttributeDTO startChallengeButton;

    @Schema(description = "随机加入")
    private PageAttributeDTO randomMatchButton;

    @Schema(description = "邀请码加入")
    private PageAttributeDTO inviteFriendButton;

    @Schema(description = "音乐播放状态")
    private PageAttributeDTO homePageMusicPlay;

    @Schema(description = "音乐暂停状态")
    private PageAttributeDTO homePageMusicStop;

    @Schema(description = "背景颜色")
    private String backgroundColor;

    @Schema(description = "背景音乐")
    private NamePath backgroundMusic;


}
