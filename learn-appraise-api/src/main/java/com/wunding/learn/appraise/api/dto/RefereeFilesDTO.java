package com.wunding.learn.appraise.api.dto;

import com.wunding.learn.file.api.dto.NamePath;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: RefereeFiles
 * @projectName devlop-learn
 * @description: 学员上传材料内容
 * @date 2021/12/5 13:14
 */
@Data
@Schema
public class RefereeFilesDTO {

    @Schema(description = "文件类别料标题")
    private String title;

    @Schema(description = "转码状态 1-转码中 2-转码成功 3-转码失败 4-排队中")
    private Integer transformStatus;

    @Schema(description = "学员上传文件id")
    private String providerFileId;

    @Schema(description = "是否待入库课件 0-否 1-是")
    private Integer isSaveLib;

    @Schema(description = "评价说明")
    private String description;

    @Schema(description = "文件信息")
    private NamePath file;

}
