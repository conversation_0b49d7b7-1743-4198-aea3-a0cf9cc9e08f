<transformation>
  <info>
    <name>stat_analysis_project_student</name>
    <description/>
    <extended_description/>
    <trans_version/>
    <trans_type>Normal</trans_type>
    <trans_status>0</trans_status>
    <directory>/</directory>
    <parameters>
      <parameter>
        <name>endTime</name>
        <default_value>2023-12-31</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnCommentHost</name>
        <default_value>************</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnCommentName</name>
        <default_value>learn_comment</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnCommentPassword</name>
        <default_value>learnTest</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnCommentPort</name>
        <default_value>30306</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnCommentUser</name>
        <default_value>wdxuexi</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnCourseHost</name>
        <default_value>************</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnCourseName</name>
        <default_value>learn_course</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnCoursePassword</name>
        <default_value>learnTest</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnCoursePort</name>
        <default_value>30306</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnCourseUser</name>
        <default_value>wdxuexi</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnEvaluationHost</name>
        <default_value>************</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnEvaluationName</name>
        <default_value>learn_evaluation</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnEvaluationPassword</name>
        <default_value>learnTest</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnEvaluationPort</name>
        <default_value>30306</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnEvaluationUser</name>
        <default_value>wdxuexi</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnExamHost</name>
        <default_value>************</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnExamName</name>
        <default_value>learn_exam</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnExamPassword</name>
        <default_value>learnTest</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnExamPort</name>
        <default_value>30306</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnExamUser</name>
        <default_value>wdxuexi</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnExcitationHost</name>
        <default_value>************</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnExcitationName</name>
        <default_value>learn_excitation</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnExcitationPassword</name>
        <default_value>learnTest</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnExcitationPort</name>
        <default_value>30306</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnExcitationUser</name>
        <default_value>wdxuexi</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnForumHost</name>
        <default_value>************</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnForumName</name>
        <default_value>learn_forum</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnForumPassword</name>
        <default_value>learnTest</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnForumPort</name>
        <default_value>30306</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnForumUser</name>
        <default_value>wdxuexi</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnInfoHost</name>
        <default_value>************</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnInfoName</name>
        <default_value>learn_info</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnInfoPassword</name>
        <default_value>learnTest</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnInfoPort</name>
        <default_value>30306</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnInfoUser</name>
        <default_value>wdxuexi</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnLecturerHost</name>
        <default_value>************</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnLecturerName</name>
        <default_value>learn_lecturer</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnLecturerPassword</name>
        <default_value>learnTest</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnLecturerPort</name>
        <default_value>30306</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnLecturerUser</name>
        <default_value>wdxuexi</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnLiveHost</name>
        <default_value>************</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnLiveName</name>
        <default_value>learn_live</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnLivePassword</name>
        <default_value>learnTest</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnLivePort</name>
        <default_value>30306</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnLiveUser</name>
        <default_value>wdxuexi</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnMarketHost</name>
        <default_value>************</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnMarketName</name>
        <default_value>learn_market</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnMarketPassword</name>
        <default_value>learnTest</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnMarketPort</name>
        <default_value>30306</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnMarketUser</name>
        <default_value>wdxuexi</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnProjectHost</name>
        <default_value>************</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnProjectName</name>
        <default_value>learn_project</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnProjectPassword</name>
        <default_value>learnTest</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnProjectPort</name>
        <default_value>30306</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnProjectUser</name>
        <default_value>wdxuexi</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnStatisticsHost</name>
        <default_value>************</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnStatisticsName</name>
        <default_value>learn_statistics</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnStatisticsPassword</name>
        <default_value>learnTest</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnStatisticsPort</name>
        <default_value>30306</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnStatisticsUser</name>
        <default_value>wdxuexi</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnSurveyHost</name>
        <default_value>************</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnSurveyName</name>
        <default_value>learn_survey</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnSurveyPassword</name>
        <default_value>learnTest</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnSurveyPort</name>
        <default_value>30306</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnSurveyUser</name>
        <default_value>wdxuexi</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnUserHost</name>
        <default_value>************</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnUserName</name>
        <default_value>learn_user</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnUserPassword</name>
        <default_value>learnTest</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnUserPort</name>
        <default_value>30306</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>learnUserUser</name>
        <default_value>wdxuexi</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>startTime</name>
        <default_value>2023-01-01</default_value>
        <description/>
      </parameter>
      <parameter>
        <name>timeStat</name>
        <default_value>4</default_value>
        <description/>
      </parameter>
    </parameters>
    <log>
      <trans-log-table>
        <connection/>
        <schema/>
        <table/>
        <size_limit_lines/>
        <interval/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STATUS</id>
          <enabled>Y</enabled>
          <name>STATUS</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
          <subject/>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
          <subject/>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
          <subject/>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
          <subject/>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
          <subject/>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
          <subject/>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>STARTDATE</id>
          <enabled>Y</enabled>
          <name>STARTDATE</name>
        </field>
        <field>
          <id>ENDDATE</id>
          <enabled>Y</enabled>
          <name>ENDDATE</name>
        </field>
        <field>
          <id>LOGDATE</id>
          <enabled>Y</enabled>
          <name>LOGDATE</name>
        </field>
        <field>
          <id>DEPDATE</id>
          <enabled>Y</enabled>
          <name>DEPDATE</name>
        </field>
        <field>
          <id>REPLAYDATE</id>
          <enabled>Y</enabled>
          <name>REPLAYDATE</name>
        </field>
        <field>
          <id>LOG_FIELD</id>
          <enabled>Y</enabled>
          <name>LOG_FIELD</name>
        </field>
        <field>
          <id>EXECUTING_SERVER</id>
          <enabled>N</enabled>
          <name>EXECUTING_SERVER</name>
        </field>
        <field>
          <id>EXECUTING_USER</id>
          <enabled>N</enabled>
          <name>EXECUTING_USER</name>
        </field>
        <field>
          <id>CLIENT</id>
          <enabled>N</enabled>
          <name>CLIENT</name>
        </field>
      </trans-log-table>
      <perf-log-table>
        <connection/>
        <schema/>
        <table/>
        <interval/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>SEQ_NR</id>
          <enabled>Y</enabled>
          <name>SEQ_NR</name>
        </field>
        <field>
          <id>LOGDATE</id>
          <enabled>Y</enabled>
          <name>LOGDATE</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STEPNAME</id>
          <enabled>Y</enabled>
          <name>STEPNAME</name>
        </field>
        <field>
          <id>STEP_COPY</id>
          <enabled>Y</enabled>
          <name>STEP_COPY</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>INPUT_BUFFER_ROWS</id>
          <enabled>Y</enabled>
          <name>INPUT_BUFFER_ROWS</name>
        </field>
        <field>
          <id>OUTPUT_BUFFER_ROWS</id>
          <enabled>Y</enabled>
          <name>OUTPUT_BUFFER_ROWS</name>
        </field>
      </perf-log-table>
      <channel-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>LOGGING_OBJECT_TYPE</id>
          <enabled>Y</enabled>
          <name>LOGGING_OBJECT_TYPE</name>
        </field>
        <field>
          <id>OBJECT_NAME</id>
          <enabled>Y</enabled>
          <name>OBJECT_NAME</name>
        </field>
        <field>
          <id>OBJECT_COPY</id>
          <enabled>Y</enabled>
          <name>OBJECT_COPY</name>
        </field>
        <field>
          <id>REPOSITORY_DIRECTORY</id>
          <enabled>Y</enabled>
          <name>REPOSITORY_DIRECTORY</name>
        </field>
        <field>
          <id>FILENAME</id>
          <enabled>Y</enabled>
          <name>FILENAME</name>
        </field>
        <field>
          <id>OBJECT_ID</id>
          <enabled>Y</enabled>
          <name>OBJECT_ID</name>
        </field>
        <field>
          <id>OBJECT_REVISION</id>
          <enabled>Y</enabled>
          <name>OBJECT_REVISION</name>
        </field>
        <field>
          <id>PARENT_CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>PARENT_CHANNEL_ID</name>
        </field>
        <field>
          <id>ROOT_CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>ROOT_CHANNEL_ID</name>
        </field>
      </channel-log-table>
      <step-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STEPNAME</id>
          <enabled>Y</enabled>
          <name>STEPNAME</name>
        </field>
        <field>
          <id>STEP_COPY</id>
          <enabled>Y</enabled>
          <name>STEP_COPY</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>LOG_FIELD</id>
          <enabled>N</enabled>
          <name>LOG_FIELD</name>
        </field>
      </step-log-table>
      <metrics-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>METRICS_DATE</id>
          <enabled>Y</enabled>
          <name>METRICS_DATE</name>
        </field>
        <field>
          <id>METRICS_CODE</id>
          <enabled>Y</enabled>
          <name>METRICS_CODE</name>
        </field>
        <field>
          <id>METRICS_DESCRIPTION</id>
          <enabled>Y</enabled>
          <name>METRICS_DESCRIPTION</name>
        </field>
        <field>
          <id>METRICS_SUBJECT</id>
          <enabled>Y</enabled>
          <name>METRICS_SUBJECT</name>
        </field>
        <field>
          <id>METRICS_TYPE</id>
          <enabled>Y</enabled>
          <name>METRICS_TYPE</name>
        </field>
        <field>
          <id>METRICS_VALUE</id>
          <enabled>Y</enabled>
          <name>METRICS_VALUE</name>
        </field>
      </metrics-log-table>
    </log>
    <maxdate>
      <connection/>
      <table/>
      <field/>
      <offset>0.0</offset>
      <maxdiff>0.0</maxdiff>
    </maxdate>
    <size_rowset>10000</size_rowset>
    <sleep_time_empty>50</sleep_time_empty>
    <sleep_time_full>50</sleep_time_full>
    <unique_connections>N</unique_connections>
    <feedback_shown>Y</feedback_shown>
    <feedback_size>50000</feedback_size>
    <using_thread_priorities>Y</using_thread_priorities>
    <shared_objects_file/>
    <capture_step_performance>N</capture_step_performance>
    <step_performance_capturing_delay>1000</step_performance_capturing_delay>
    <step_performance_capturing_size_limit>100</step_performance_capturing_size_limit>
    <dependencies>
    </dependencies>
    <partitionschemas>
    </partitionschemas>
    <slaveservers>
    </slaveservers>
    <clusterschemas>
    </clusterschemas>
    <created_user>-</created_user>
    <created_date>2022/10/31 14:56:10.278</created_date>
    <modified_user>-</modified_user>
    <modified_date>2024/01/11 15:26:47.234</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
  </notepads>
  <connection>
    <name>learn_course</name>
    <server>${learnCourseHost}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${learnCourseName}</database>
    <port>${learnCoursePort}</port>
    <username>${learnCourseUser}</username>
    <password>${learnCoursePassword}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${learnCoursePort}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <connection>
    <name>learn_evaluation</name>
    <server>${learnEvaluationHost}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${learnEvaluationName}</database>
    <port>${learnEvaluationPort}</port>
    <username>${learnEvaluationUser}</username>
    <password>${learnEvaluationPassword}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${learnEvaluationPort}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <connection>
    <name>learn_exam</name>
    <server>${learnExamHost}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${learnExamName}</database>
    <port>${learnExamPort}</port>
    <username>${learnExamUser}</username>
    <password>${learnExamPassword}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${learnExamPort}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <connection>
    <name>learn_market</name>
    <server>${learnMarketHost}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${learnMarketName}</database>
    <port>${learnMarketPort}</port>
    <username>${learnMarketUser}</username>
    <password>${learnMarketPassword}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${learnMarketPort}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <connection>
    <name>learn_project</name>
    <server>${learnProjectHost}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${learnProjectName}</database>
    <port>${learnProjectPort}</port>
    <username>${learnProjectUser}</username>
    <password>${learnProjectPassword}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${learnProjectPort}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <connection>
    <name>learn_statistics</name>
    <server>${learnStatisticsHost}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${learnStatisticsName}</database>
    <port>${learnStatisticsPort}</port>
    <username>${learnStatisticsUser}</username>
    <password>${learnStatisticsPassword}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${learnStatisticsPort}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <connection>
    <name>learn_user</name>
    <server>${learnUserHost}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${learnUserName}</database>
    <port>${learnUserPort}</port>
    <username>${learnUserUser}</username>
    <password>${learnUserPassword}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${learnUserPort}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <order>
    <hop>
      <from>快速培训统计</from>
      <to>快速培训统计排序</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>用户课程学习记录</from>
      <to>course_id升序</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>task_content升序</from>
      <to>快速培训-课程</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>course_id升序</from>
      <to>快速培训-课程</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>快速培训-课程</from>
      <to>user_id排序记录</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组</from>
      <to>user_id升序 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>用户考试</from>
      <to>exam_id升序</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>user_id排序记录</from>
      <to>分组</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>exam_id升序</from>
      <to>快速培训-考试</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>task_content升序</from>
      <to>快速培训-考试</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>快速培训-考试</from>
      <to>user_id排序记录 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 2</from>
      <to>user_id升序 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>用户表</from>
      <to>用户表排序</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>user_id排序记录 2</from>
      <to>分组 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>快速培训项目</from>
      <to>task_content升序</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>用户签到</from>
      <to>排序记录 4</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 4</from>
      <to>快速培训-签到</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>快速培训-签到</from>
      <to>排序记录 5</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 3</from>
      <to>排序记录 6</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 5</from>
      <to>分组 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>替换NULL值</from>
      <to>插入 / 更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>用户评估</from>
      <to>评估记录升序</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>评估记录升序</from>
      <to>快速培训-评估</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>快速培训-评估</from>
      <to>排序记录 8</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 4</from>
      <to>排序记录 9</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 8</from>
      <to>分组 4</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>快速培训-应用</from>
      <to>应用排序</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>应用排序</from>
      <to>快速培训-签到</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>应用排序</from>
      <to>快速培训-评估</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>目标人次</from>
      <to>目标人次排序</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>过滤记录</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>过滤记录</from>
      <to>替换NULL值</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>快速培训统计排序</from>
      <to>记录集连接</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>用户表排序</from>
      <to>记录集连接</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录集连接</from>
      <to>排序记录</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录</from>
      <to>Multiway merge join</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>目标人次排序</from>
      <to>Multiway merge join</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>user_id升序 3</from>
      <to>Multiway merge join</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>user_id升序 3 2</from>
      <to>Multiway merge join</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 6</from>
      <to>Multiway merge join</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9</from>
      <to>Multiway merge join</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Multiway merge join</from>
      <to>字段选择</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>培训完成</from>
      <to>完成排序</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>完成排序</from>
      <to>Multiway merge join</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>快速培训-任务</from>
      <to>排序记录 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>考试记录</from>
      <to>考试记录排序</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 2</from>
      <to>考试连接</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>考试记录排序</from>
      <to>考试连接</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>考试连接</from>
      <to>排序记录 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 3</from>
      <to>分组 5</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 5</from>
      <to>排序记录 7</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 7</from>
      <to>Multiway merge join</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>快速培训-应用2</from>
      <to>应用资源排序</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>签到完成</from>
      <to>排序记录 10</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10</from>
      <to>记录集连接 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>应用资源排序</from>
      <to>记录集连接 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>评估完成</from>
      <to>排序记录 11</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 11</from>
      <to>记录集连接 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>应用资源排序</from>
      <to>记录集连接 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录集连接 2</from>
      <to>排序记录 12</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 12</from>
      <to>分组 6</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6</from>
      <to>签到完成排序</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录集连接 3</from>
      <to>排序记录 13</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 13</from>
      <to>分组 7</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7</from>
      <to>排序记录 14</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>签到完成排序</from>
      <to>Multiway merge join</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 14</from>
      <to>Multiway merge join</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>考试、课程完成</from>
      <to>排序记录 15</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 15</from>
      <to>Multiway merge join</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <step>
    <name>Multiway merge join</name>
    <type>MultiwayMergeJoin</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <join_type>FULL OUTER</join_type>
    <step0>排序记录</step0>
    <step1>目标人次排序</step1>
    <step2>user_id升序 3</step2>
    <step3>user_id升序 3 2</step3>
    <step4>排序记录 6</step4>
    <step5>排序记录 9</step5>
    <step6>完成排序</step6>
    <step7>排序记录 7</step7>
    <step8>签到完成排序</step8>
    <step9>排序记录 14</step9>
    <step10>排序记录 15</step10>
    <number_input>11</number_input>
    <keys>
      <key>user_id</key>
      <key>user_id</key>
      <key>user_id</key>
      <key>user_id</key>
      <key>user_id</key>
      <key>user_id</key>
      <key>user_id</key>
      <key>user_id</key>
      <key>sign_user_id</key>
      <key>user_id</key>
      <key>user_id</key>
    </keys>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>912</xloc>
      <yloc>640</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>course_id升序</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>course_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>304</xloc>
      <yloc>272</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>exam_id升序</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>exam_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>304</xloc>
      <yloc>448</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>task_content升序</name>
    <type>SortRows</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>task_content</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>304</xloc>
      <yloc>352</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>user_id升序 3</name>
    <type>SortRows</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>672</xloc>
      <yloc>272</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>user_id升序 3 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>672</xloc>
      <yloc>448</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>user_id排序记录</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>512</xloc>
      <yloc>272</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>user_id排序记录 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>512</xloc>
      <yloc>448</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total_learn_second</aggregate>
        <subject>duration</subject>
        <type>SUM</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>592</xloc>
      <yloc>272</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>exam_post_count</aggregate>
        <subject>exam_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>592</xloc>
      <yloc>448</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 3</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>sign_count</aggregate>
        <subject>id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>592</xloc>
      <yloc>544</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 4</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>evaluation_count</aggregate>
        <subject>eval_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>592</xloc>
      <yloc>720</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 5</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>exam_pass_count</aggregate>
        <subject>isPass</subject>
        <type>SUM</type>
        <valuefield/>
      </field>
      <field>
        <aggregate>exam_unpass_count</aggregate>
        <subject>isUnPass</subject>
        <type>SUM</type>
        <valuefield/>
      </field>
      <field>
        <aggregate>exam_avg_mark</aggregate>
        <subject>user_score</subject>
        <type>AVERAGE</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>592</xloc>
      <yloc>944</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>sign_user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>sign_done_count</aggregate>
        <subject>id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>592</xloc>
      <yloc>1072</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>evaluation_done_count</aggregate>
        <subject>eval_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
      <field>
        <aggregate>evaluation_mark</aggregate>
        <subject>mark</subject>
        <type>AVERAGE</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>592</xloc>
      <yloc>1232</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>培训完成</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_project</connection>
    <sql>select
	pp.user_id,
	sum(case when (pp.`status` = 1 and pp.app_status = 1) then 1 ELSE 0 END) project_done_count
from
	project p
	INNER join project_progress pp on p.id = pp.pro_id
where
	p.project_type = 1 and
	p.start_time between '${startTime}' and '${endTime}'
	and p.is_del = 0
	and p.is_publish = 1
group by
	pp.user_id;</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>Y</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>user_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>培训、课程、考试完成</origin>
        <comments>user_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>project_done_count</name>
        <length>23</length>
        <precision>0</precision>
        <origin>培训、课程、考试完成</origin>
        <comments>project_done_count</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>144</xloc>
      <yloc>816</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>字段选择</name>
    <type>SelectValues</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>user_id</name>
      </field>
      <field>
        <name>stat_type</name>
      </field>
      <field>
        <name>role_type</name>
      </field>
      <field>
        <name>join_project_num</name>
      </field>
      <field>
        <name>finish_project_num</name>
      </field>
      <field>
        <name>target_count</name>
      </field>
      <field>
        <name>total_learn_second</name>
      </field>
      <field>
        <name>exam_post_count</name>
      </field>
      <field>
        <name>sign_count</name>
      </field>
      <field>
        <name>evaluation_count</name>
      </field>
      <field>
        <name>project_done_count</name>
      </field>
      <field>
        <name>course_done_count</name>
      </field>
      <field>
        <name>exam_done_count</name>
      </field>
      <field>
        <name>exam_pass_count</name>
      </field>
      <field>
        <name>exam_unpass_count</name>
      </field>
      <field>
        <name>exam_avg_mark</name>
      </field>
      <field>
        <name>sign_user_id</name>
      </field>
      <field>
        <name>sign_done_count</name>
      </field>
      <field>
        <name>evaluation_done_count</name>
      </field>
      <field>
        <name>evaluation_mark</name>
      </field>
      <field>
        <name>project_type</name>
      </field>
      <select_unspecified>N</select_unspecified>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1008</xloc>
      <yloc>640</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>完成排序</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>672</xloc>
      <yloc>816</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>应用排序</name>
    <type>SortRows</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>taskContent</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>304</xloc>
      <yloc>624</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>应用资源排序</name>
    <type>SortRows</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>taskContent</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>304</xloc>
      <yloc>1152</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>快速培训-任务</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_project</connection>
    <sql>select
	-- p.create_by user_id,
	pt.task_content 
from
	project p
	INNER join project_task pt ON p.id = pt.pro_id
where
	p.project_type = 1 and
	p.start_time between '${startTime}' and '${endTime}'
	and p.is_del = 0
	and p.is_publish = 1
	and pt.task_content is not null and pt.task_content &lt;&gt; ''</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>Y</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>task_content</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>快速培训-任务</origin>
        <comments>task_content</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>144</xloc>
      <yloc>896</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>快速培训-应用</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_project</connection>
    <sql>select
	pa.resource_id as taskContent
from
	project p
	INNER join project_app pa ON p.id = pa.project_id
	and pa.is_del = 0
where
	p.project_type = 1 and
	p.is_del = 0 
	and p.is_publish =1;</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>taskContent</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>快速培训-应用</origin>
        <comments>taskContent</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>144</xloc>
      <yloc>624</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>快速培训-应用2</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_project</connection>
    <sql>select
	pa.resource_id as taskContent
from
	project p
	INNER join project_app pa ON p.id = pa.project_id
	and pa.is_del = 0
where
	p.project_type = 1 and
	p.start_time between '${startTime}' and '${endTime}'
	and p.is_del = 0
	and p.is_publish =1;</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>Y</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>taskContent</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>快速培训-应用2</origin>
        <comments>taskContent</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>144</xloc>
      <yloc>1152</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>快速培训-签到</name>
    <type>MergeJoin</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <join_type>INNER</join_type>
    <step1>应用排序</step1>
    <step2>排序记录 4</step2>
    <keys_1>
      <key>taskContent</key>
    </keys_1>
    <keys_2>
      <key>id</key>
    </keys_2>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>432</xloc>
      <yloc>544</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>快速培训-考试</name>
    <type>MergeJoin</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <join_type>INNER</join_type>
    <step1>task_content升序</step1>
    <step2>exam_id升序</step2>
    <keys_1>
      <key>task_content</key>
    </keys_1>
    <keys_2>
      <key>exam_id</key>
    </keys_2>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>432</xloc>
      <yloc>448</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>快速培训-评估</name>
    <type>MergeJoin</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <join_type>INNER</join_type>
    <step1>应用排序</step1>
    <step2>评估记录升序</step2>
    <keys_1>
      <key>taskContent</key>
    </keys_1>
    <keys_2>
      <key>eval_id</key>
    </keys_2>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>432</xloc>
      <yloc>720</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>快速培训-课程</name>
    <type>MergeJoin</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <join_type>INNER</join_type>
    <step1>task_content升序</step1>
    <step2>course_id升序</step2>
    <keys_1>
      <key>task_content</key>
    </keys_1>
    <keys_2>
      <key>course_id</key>
    </keys_2>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>432</xloc>
      <yloc>272</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>快速培训统计</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_project</connection>
    <sql>select
	user_id,
-- 	pp.start_time,
	count( 1 ) join_project_num,
	sum(if(`status`=1,1,0)) finish_project_num
from
	project p inner join project_progress pp on p.id = pp.pro_id
-- 	and p.is_del=0 and pp.is_del = 0
where
	p.project_type = 1 and
	pp.is_del = 0
	and p.is_del=0
	 -- pp.start_time  between '2023-06-01' and now()
	 and pp.start_time  between '${startTime}' and '${endTime}'
group by
	pp.user_id; </sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>Y</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>user_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>快速培训统计</origin>
        <comments>user_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>Integer</type>
        <storagetype>normal</storagetype>
        <name>join_project_num</name>
        <length>15</length>
        <precision>0</precision>
        <origin>快速培训统计</origin>
        <comments>join_project_num</comments>
        <conversion_Mask>####0;-####0</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>finish_project_num</name>
        <length>23</length>
        <precision>0</precision>
        <origin>快速培训统计</origin>
        <comments>finish_project_num</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>144</xloc>
      <yloc>112</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>快速培训统计排序</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>304</xloc>
      <yloc>112</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>快速培训项目</name>
    <type>TableInput</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_project</connection>
    <sql>select distinct
	pt.task_content,
	p.flag 
from
	project p
	inner join project_task pt ON p.id = pt.pro_id
where
	p.project_type = 1 and
	p.is_del = 0
	and p.is_publish = 1
	and pt.is_del = 0;</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>task_content</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>快速培训项目</origin>
        <comments>task_content</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>Integer</type>
        <storagetype>normal</storagetype>
        <name>flag</name>
        <length>2</length>
        <precision>0</precision>
        <origin>快速培训项目</origin>
        <comments>flag</comments>
        <conversion_Mask>####0;-####0</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>144</xloc>
      <yloc>352</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>672</xloc>
      <yloc>112</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>304</xloc>
      <yloc>1072</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 11</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>eval_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>304</xloc>
      <yloc>1232</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 12</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>sign_user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>512</xloc>
      <yloc>1072</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 13</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>512</xloc>
      <yloc>1232</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 14</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>672</xloc>
      <yloc>1232</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 15</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>672</xloc>
      <yloc>1312</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>task_content</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>304</xloc>
      <yloc>896</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 3</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>512</xloc>
      <yloc>944</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 4</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>304</xloc>
      <yloc>544</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 5</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>512</xloc>
      <yloc>544</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 6</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>672</xloc>
      <yloc>544</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 7</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>672</xloc>
      <yloc>944</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 8</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>512</xloc>
      <yloc>720</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>672</xloc>
      <yloc>720</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>插入 / 更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>300</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_project</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>stat_type</name>
        <field>stat_type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>role_type</name>
        <field>role_type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>project_type</name>
        <field>project_type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>role_type</name>
        <rename>role_type</rename>
        <update>N</update>
      </value>
      <value>
        <name>stat_type</name>
        <rename>stat_type</rename>
        <update>N</update>
      </value>
      <value>
        <name>project_type</name>
        <rename>project_type</rename>
        <update>N</update>
      </value>
      <value>
        <name>join_project_num</name>
        <rename>join_project_num</rename>
        <update>Y</update>
      </value>
      <value>
        <name>finish_project_num</name>
        <rename>finish_project_num</rename>
        <update>Y</update>
      </value>
      <value>
        <name>total_learn_second</name>
        <rename>total_learn_second</rename>
        <update>Y</update>
      </value>
      <value>
        <name>exam_post_count</name>
        <rename>exam_post_count</rename>
        <update>Y</update>
      </value>
      <value>
        <name>sign_count</name>
        <rename>sign_count</rename>
        <update>Y</update>
      </value>
      <value>
        <name>evaluation_count</name>
        <rename>evaluation_count</rename>
        <update>Y</update>
      </value>
      <value>
        <name>course_target_count</name>
        <rename>target_count</rename>
        <update>Y</update>
      </value>
      <value>
        <name>sign_target_count</name>
        <rename>target_count</rename>
        <update>Y</update>
      </value>
      <value>
        <name>evaluation_target_count</name>
        <rename>target_count</rename>
        <update>Y</update>
      </value>
      <value>
        <name>exam_target_count</name>
        <rename>target_count</rename>
        <update>Y</update>
      </value>
      <value>
        <name>project_done_count</name>
        <rename>project_done_count</rename>
        <update>Y</update>
      </value>
      <value>
        <name>course_done_count</name>
        <rename>course_done_count</rename>
        <update>Y</update>
      </value>
      <value>
        <name>exam_done_count</name>
        <rename>exam_done_count</rename>
        <update>Y</update>
      </value>
      <value>
        <name>project_target_count</name>
        <rename>target_count</rename>
        <update>Y</update>
      </value>
      <value>
        <name>exam_pass_count</name>
        <rename>exam_pass_count</rename>
        <update>Y</update>
      </value>
      <value>
        <name>exam_unpass_count</name>
        <rename>exam_unpass_count</rename>
        <update>Y</update>
      </value>
      <value>
        <name>exam_avg_mark</name>
        <rename>exam_avg_mark</rename>
        <update>Y</update>
      </value>
      <value>
        <name>sign_done_count</name>
        <rename>sign_done_count</rename>
        <update>Y</update>
      </value>
      <value>
        <name>evaluation_done_count</name>
        <rename>evaluation_done_count</rename>
        <update>Y</update>
      </value>
      <value>
        <name>evaluation_mark</name>
        <rename>evaluation_mark</rename>
        <update>Y</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1312</xloc>
      <yloc>640</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>替换NULL值</name>
    <type>IfNull</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <replaceAllByValue/>
    <replaceAllMask/>
    <selectFields>Y</selectFields>
    <selectValuesType>N</selectValuesType>
    <setEmptyStringAll>N</setEmptyStringAll>
    <valuetypes>
      </valuetypes>
    <fields>
      <field>
        <name>stat_type</name>
        <value>0</value>
        <mask/>
        <set_empty_string>N</set_empty_string>
      </field>
      <field>
        <name>role_type</name>
        <value>0</value>
        <mask/>
        <set_empty_string>N</set_empty_string>
      </field>
      <field>
        <name>join_project_num</name>
        <value>0</value>
        <mask/>
        <set_empty_string>N</set_empty_string>
      </field>
      <field>
        <name>finish_project_num</name>
        <value>0</value>
        <mask/>
        <set_empty_string>N</set_empty_string>
      </field>
      <field>
        <name>target_count</name>
        <value>0</value>
        <mask/>
        <set_empty_string>N</set_empty_string>
      </field>
      <field>
        <name>total_learn_second</name>
        <value>0</value>
        <mask/>
        <set_empty_string>N</set_empty_string>
      </field>
      <field>
        <name>exam_post_count</name>
        <value>0</value>
        <mask/>
        <set_empty_string>N</set_empty_string>
      </field>
      <field>
        <name>sign_count</name>
        <value>0</value>
        <mask/>
        <set_empty_string>N</set_empty_string>
      </field>
      <field>
        <name>evaluation_count</name>
        <value>0</value>
        <mask/>
        <set_empty_string>N</set_empty_string>
      </field>
      <field>
        <name>project_done_count</name>
        <value>0</value>
        <mask/>
        <set_empty_string>N</set_empty_string>
      </field>
      <field>
        <name>course_done_count</name>
        <value>0</value>
        <mask/>
        <set_empty_string>N</set_empty_string>
      </field>
      <field>
        <name>exam_done_count</name>
        <value>0</value>
        <mask/>
        <set_empty_string>N</set_empty_string>
      </field>
      <field>
        <name>exam_pass_count</name>
        <value>0</value>
        <mask/>
        <set_empty_string>N</set_empty_string>
      </field>
      <field>
        <name>exam_unpass_count</name>
        <value>0</value>
        <mask/>
        <set_empty_string>N</set_empty_string>
      </field>
      <field>
        <name>exam_avg_mark</name>
        <value>0</value>
        <mask/>
        <set_empty_string>N</set_empty_string>
      </field>
      <field>
        <name>sign_done_count</name>
        <value>0</value>
        <mask/>
        <set_empty_string>N</set_empty_string>
      </field>
      <field>
        <name>evaluation_done_count</name>
        <value>0</value>
        <mask/>
        <set_empty_string>N</set_empty_string>
      </field>
      <field>
        <name>evaluation_mark</name>
        <value>0</value>
        <mask/>
        <set_empty_string>N</set_empty_string>
      </field>
      <field>
        <name>project_type</name>
        <value>0</value>
        <mask/>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1200</xloc>
      <yloc>640</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>用户签到</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_market</connection>
    <sql>select
	s.id,
	user_id
-- 	is_sign,
-- 	start_time,
-- 	end_time,
-- 	sign_in_time 
from
	sign s
	inner join sign_user su ON s.id = su.sign_id
where 
	s.is_del = 0
	and is_sign = 1
	and sign_in_time &lt;= end_time
	and su.sign_in_time between '${startTime}' and '${endTime}'</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>Y</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>50</length>
        <precision>-1</precision>
        <origin>用户签到</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>user_id</name>
        <length>50</length>
        <precision>-1</precision>
        <origin>用户签到</origin>
        <comments>user_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>144</xloc>
      <yloc>544</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>用户考试</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_exam</connection>
    <sql>select
	ex.id exam_id,
	user_id
	-- count(1) exam_post_count
from
	ex_exam ex
	inner join ex_answer_record ar ON ex.id = ar.exam_id
where
	ex.is_del = 0
	and ar.is_del = 0
	and ar.is_post = 1
	and ar.is_check_finish = 1
	-- and ar.start_time between subdate( curdate(), date_format( curdate(), '%w' )- 1 ) and now();
    and ar.start_time between '${startTime}' and '${endTime}';</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>Y</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>exam_id</name>
        <length>50</length>
        <precision>-1</precision>
        <origin>用户考试</origin>
        <comments>exam_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>user_id</name>
        <length>50</length>
        <precision>-1</precision>
        <origin>用户考试</origin>
        <comments>user_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>144</xloc>
      <yloc>448</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>用户表</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_user</connection>
    <sql>select
	id user_id,
	${timeStat} stat_type,
	0 role_type,
	0 project_type
from 
	sys_user</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>Y</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>user_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>用户表</origin>
        <comments>user_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>Integer</type>
        <storagetype>normal</storagetype>
        <name>stat_type</name>
        <length>15</length>
        <precision>0</precision>
        <origin>用户表</origin>
        <comments>stat_type</comments>
        <conversion_Mask>####0;-####0</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>Integer</type>
        <storagetype>normal</storagetype>
        <name>role_type</name>
        <length>15</length>
        <precision>0</precision>
        <origin>用户表</origin>
        <comments>role_type</comments>
        <conversion_Mask>####0;-####0</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>Integer</type>
        <storagetype>normal</storagetype>
        <name>project_type</name>
        <length>15</length>
        <precision>0</precision>
        <origin>用户表</origin>
        <comments>project_type</comments>
        <conversion_Mask>####0;-####0</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>144</xloc>
      <yloc>16</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>用户表排序</name>
    <type>SortRows</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>304</xloc>
      <yloc>16</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>用户评估</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_evaluation</connection>
    <sql>select
	eval_id,
	user_id 
from
	eval_record 
where
	is_finish = 1 
	-- and finish_time between subdate( curdate(), date_format( curdate(), '%w' )- 1 ) and now();
	and finish_time between '${startTime}' and '${endTime}'</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>Y</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>eval_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>用户评估</origin>
        <comments>eval_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>user_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>用户评估</origin>
        <comments>user_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>144</xloc>
      <yloc>720</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>用户课程学习记录</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_course</connection>
    <sql>select
	c.id course_id,
	user_id,
	duration
from
	course c
	inner join user_course_record ucr ON c.id = ucr.course_id
where
	ucr.create_time between '${startTime}' and '${endTime}';</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>Y</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>course_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>用户课程学习记录</origin>
        <comments>course_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>user_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>用户课程学习记录</origin>
        <comments>user_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>Integer</type>
        <storagetype>normal</storagetype>
        <name>duration</name>
        <length>15</length>
        <precision>0</precision>
        <origin>用户课程学习记录</origin>
        <comments>duration</comments>
        <conversion_Mask>####0;-####0</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>144</xloc>
      <yloc>272</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>目标人次</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_project</connection>
    <sql>-- 目标人次
select
	u.user_id,
	count( 1 ) target_count 
from
	project p
	INNER join w_resource_view_limit rv ON p.id = rv.resource_id
	INNER join w_view_limit_user u ON rv.view_limit_id = u.view_limit_id
	and rv.resource_type = 'ProjectViewLimit'
where
	p.project_type = 1 and
	-- date_format( p.start_time, '%Y' ) >= '2023' and '2023' >= date_format( p.start_time, '%Y' )
	p.start_time >= '${startTime}' and p.start_time &lt;= '${endTime}'
	and p.is_del = 0
	and p.is_publish = 1
group by
	u.user_id;</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>Y</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>user_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>目标人次</origin>
        <comments>user_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>Integer</type>
        <storagetype>normal</storagetype>
        <name>target_count</name>
        <length>15</length>
        <precision>0</precision>
        <origin>目标人次</origin>
        <comments>target_count</comments>
        <conversion_Mask>####0;-####0</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>144</xloc>
      <yloc>192</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>目标人次排序</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>672</xloc>
      <yloc>192</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>签到完成</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_market</connection>
    <sql>select
	s.id,
	user_id sign_user_id
from
	sign s
	LEFT join sign_user su ON s.id = su.sign_id
where 
	s.is_del = 0
	and is_sign = 1
	and sign_in_time &lt;= end_time;</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>50</length>
        <precision>-1</precision>
        <origin>签到完成</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>sign_user_id</name>
        <length>50</length>
        <precision>-1</precision>
        <origin>签到完成</origin>
        <comments>sign_user_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>144</xloc>
      <yloc>1072</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>签到完成排序</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>sign_user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>672</xloc>
      <yloc>1072</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>考试、课程完成</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_project</connection>
    <sql>select
	tp.user_id,
	sum(case when task_type = 'course' then 1 ELSE 0 END) course_done_count,
	sum(case when task_type = 'exam' then 1 ELSE 0 END) exam_done_count
from
	project p
	INNER join project_task pt ON p.id = pt.pro_id
	and pt.task_type in( 'course','exam')
	INNER join task_progress tp ON pt.id = tp.task_id
	and tp.is_finish = 1
where
	p.project_type = 1 and
	p.start_time between '${startTime}' and '${endTime}'
	and p.is_del = 0
	and p.is_publish = 1
group by
	tp.user_id;</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>Y</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>user_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>考试、课程完成</origin>
        <comments>user_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>course_done_count</name>
        <length>23</length>
        <precision>0</precision>
        <origin>考试、课程完成</origin>
        <comments>course_done_count</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>exam_done_count</name>
        <length>23</length>
        <precision>0</precision>
        <origin>考试、课程完成</origin>
        <comments>exam_done_count</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>144</xloc>
      <yloc>1312</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>考试记录</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_exam</connection>
    <sql>select
	e.id,
	case when r.user_score >= r.pass_score THEN 1 ELSE 0 END isPass,
	case when r.user_score &lt; r.pass_score THEN 1 ELSE 0 END isUnPass,
	user_score,
	r.user_id
from
	ex_exam e
	INNER join ex_answer_record r ON e.id = r.exam_id
where
	e.is_del = 0 
	and e.is_publish = 1
	and r.is_del = 0
	and r.is_check_finish =1</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>50</length>
        <precision>-1</precision>
        <origin>考试记录</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>Integer</type>
        <storagetype>normal</storagetype>
        <name>isPass</name>
        <length>15</length>
        <precision>0</precision>
        <origin>考试记录</origin>
        <comments>isPass</comments>
        <conversion_Mask>####0;-####0</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>Integer</type>
        <storagetype>normal</storagetype>
        <name>isUnPass</name>
        <length>15</length>
        <precision>0</precision>
        <origin>考试记录</origin>
        <comments>isUnPass</comments>
        <conversion_Mask>####0;-####0</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>Number</type>
        <storagetype>normal</storagetype>
        <name>user_score</name>
        <length>10</length>
        <precision>2</precision>
        <origin>考试记录</origin>
        <comments>user_score</comments>
        <conversion_Mask>####0.0#########;-####0.0#########</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>user_id</name>
        <length>50</length>
        <precision>-1</precision>
        <origin>考试记录</origin>
        <comments>user_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>144</xloc>
      <yloc>976</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>考试记录排序</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>304</xloc>
      <yloc>976</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>考试连接</name>
    <type>MergeJoin</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <join_type>INNER</join_type>
    <step1>排序记录 2</step1>
    <step2>考试记录排序</step2>
    <keys_1>
      <key>task_content</key>
    </keys_1>
    <keys_2>
      <key>id</key>
    </keys_2>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>432</xloc>
      <yloc>944</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录集连接</name>
    <type>MergeJoin</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <join_type>LEFT OUTER</join_type>
    <step1>用户表排序</step1>
    <step2>快速培训统计排序</step2>
    <keys_1>
      <key>user_id</key>
    </keys_1>
    <keys_2>
      <key>user_id</key>
    </keys_2>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>432</xloc>
      <yloc>112</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录集连接 2</name>
    <type>MergeJoin</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <join_type>INNER</join_type>
    <step1>应用资源排序</step1>
    <step2>排序记录 10</step2>
    <keys_1>
      <key>taskContent</key>
    </keys_1>
    <keys_2>
      <key>id</key>
    </keys_2>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>432</xloc>
      <yloc>1072</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录集连接 3</name>
    <type>MergeJoin</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <join_type>INNER</join_type>
    <step1>应用资源排序</step1>
    <step2>排序记录 11</step2>
    <keys_1>
      <key>taskContent</key>
    </keys_1>
    <keys_2>
      <key>eval_id</key>
    </keys_2>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>432</xloc>
      <yloc>1232</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>评估完成</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_evaluation</connection>
    <sql>select
	r.eval_id,
	r.user_id,
	ifnull( sum( rp.mark ), 0 ) mark
from
	evaluation e
	inner join eval_record r on e.id = r.eval_id
	inner join eval_reply rp on r.id = rp.record_id
where
	r.is_finish = 1
	and e.is_publish = 1
	and e.is_del = 0
	and e.evaluation_type = 1
	group by r.eval_id,r.user_id</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>eval_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>评估完成</origin>
        <comments>eval_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>user_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>评估完成</origin>
        <comments>user_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>mark</name>
        <length>27</length>
        <precision>2</precision>
        <origin>评估完成</origin>
        <comments>mark</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>144</xloc>
      <yloc>1232</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>评估记录升序</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>eval_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>304</xloc>
      <yloc>720</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>过滤记录</name>
    <type>FilterRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <send_true_to/>
    <send_false_to/>
    <compare>
      <condition>
        <negated>N</negated>
        <leftvalue>user_id</leftvalue>
        <function>IS NOT NULL</function>
        <rightvalue/>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1136</xloc>
      <yloc>640</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step_error_handling>
  </step_error_handling>
  <slave-step-copy-partition-distribution>
  </slave-step-copy-partition-distribution>
  <slave_transformation>N</slave_transformation>
  <attributes/>
</transformation>
