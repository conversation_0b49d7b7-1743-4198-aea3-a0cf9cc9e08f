<transformation>
  <info>
    <name>stat_analysis_resource_centre1</name>
    <description/>
    <extended_description/>
    <trans_version/>
    <trans_type>Normal</trans_type>
    <trans_status>0</trans_status>
    <directory>/</directory>
    <parameters>
      <parameter>
        <name>learnAppraiseHost</name>
        <default_value>************</default_value>
        <description>评价数据库主机</description>
      </parameter>
      <parameter>
        <name>learnAppraiseName</name>
        <default_value>appraise</default_value>
        <description>评价数据库名称</description>
      </parameter>
      <parameter>
        <name>learnAppraisePassword</name>
        <default_value>learnTest</default_value>
        <description>评价数据库密码</description>
      </parameter>
      <parameter>
        <name>learnAppraisePort</name>
        <default_value>30306</default_value>
        <description>评价数据库端口</description>
      </parameter>
      <parameter>
        <name>learnAppraiseUser</name>
        <default_value>wdxuexi</default_value>
        <description>评价数据库用户</description>
      </parameter>
      <parameter>
        <name>learnCertificationHost</name>
        <default_value>************</default_value>
        <description>证书数据库主机</description>
      </parameter>
      <parameter>
        <name>learnCertificationName</name>
        <default_value>certification</default_value>
        <description>证书数据库名称</description>
      </parameter>
      <parameter>
        <name>learnCertificationPassword</name>
        <default_value>learnTest</default_value>
        <description>证书数据库密码</description>
      </parameter>
      <parameter>
        <name>learnCertificationPort</name>
        <default_value>30306</default_value>
        <description>证书数据库端口</description>
      </parameter>
      <parameter>
        <name>learnCertificationUser</name>
        <default_value>wdxuexi</default_value>
        <description>证书数据库用户</description>
      </parameter>
      <parameter>
        <name>learnCommentHost</name>
        <default_value>************</default_value>
        <description>评论数据库主机</description>
      </parameter>
      <parameter>
        <name>learnCommentName</name>
        <default_value>comment</default_value>
        <description>评论数据库名称</description>
      </parameter>
      <parameter>
        <name>learnCommentPassword</name>
        <default_value>learnTest</default_value>
        <description>评论数据库密码</description>
      </parameter>
      <parameter>
        <name>learnCommentPort</name>
        <default_value>30306</default_value>
        <description>评论数据库端口</description>
      </parameter>
      <parameter>
        <name>learnCommentUser</name>
        <default_value>wdxuexi</default_value>
        <description>评论数据库用户</description>
      </parameter>
      <parameter>
        <name>learnCourseHost</name>
        <default_value>************</default_value>
        <description>课程数据库主机</description>
      </parameter>
      <parameter>
        <name>learnCourseName</name>
        <default_value>course</default_value>
        <description>课程数据库名称</description>
      </parameter>
      <parameter>
        <name>learnCoursePassword</name>
        <default_value>learnTest</default_value>
        <description>课程数据库密码</description>
      </parameter>
      <parameter>
        <name>learnCoursePort</name>
        <default_value>30306</default_value>
        <description>课程数据库端口</description>
      </parameter>
      <parameter>
        <name>learnCourseUser</name>
        <default_value>wdxuexi</default_value>
        <description>课程数据库用户</description>
      </parameter>
      <parameter>
        <name>learnEvaluationHost</name>
        <default_value>************</default_value>
        <description>评估数据库主机</description>
      </parameter>
      <parameter>
        <name>learnEvaluationName</name>
        <default_value>evaluation</default_value>
        <description>评估数据库名称</description>
      </parameter>
      <parameter>
        <name>learnEvaluationPassword</name>
        <default_value>learnTest</default_value>
        <description>评估数据库密码</description>
      </parameter>
      <parameter>
        <name>learnEvaluationPort</name>
        <default_value>30306</default_value>
        <description>评估数据库端口</description>
      </parameter>
      <parameter>
        <name>learnEvaluationUser</name>
        <default_value>wdxuexi</default_value>
        <description>评估数据库用户</description>
      </parameter>
      <parameter>
        <name>learnExamHost</name>
        <default_value>************</default_value>
        <description>考试数据库主机</description>
      </parameter>
      <parameter>
        <name>learnExamName</name>
        <default_value>exam</default_value>
        <description>考试数据库名称</description>
      </parameter>
      <parameter>
        <name>learnExamPassword</name>
        <default_value>learnTest</default_value>
        <description>考试数据库密码</description>
      </parameter>
      <parameter>
        <name>learnExamPort</name>
        <default_value>30306</default_value>
        <description>考试数据库端口</description>
      </parameter>
      <parameter>
        <name>learnExamUser</name>
        <default_value>wdxuexi</default_value>
        <description>考试数据库用户</description>
      </parameter>
      <parameter>
        <name>learnExampleHost</name>
        <default_value>************</default_value>
        <description>案例库数据库主机</description>
      </parameter>
      <parameter>
        <name>learnExampleName</name>
        <default_value>example</default_value>
        <description>案例库数据库名称</description>
      </parameter>
      <parameter>
        <name>learnExamplePassword</name>
        <default_value>learnTest</default_value>
        <description>案例库数据库密码</description>
      </parameter>
      <parameter>
        <name>learnExamplePort</name>
        <default_value>30306</default_value>
        <description>案例库数据库端口</description>
      </parameter>
      <parameter>
        <name>learnExampleUser</name>
        <default_value>wdxuexi</default_value>
        <description>案例库数据库用户</description>
      </parameter>
      <parameter>
        <name>learnExcitationHost</name>
        <default_value>************</default_value>
        <description>激励数据库主机</description>
      </parameter>
      <parameter>
        <name>learnExcitationName</name>
        <default_value>excitation</default_value>
        <description>激励数据库名称</description>
      </parameter>
      <parameter>
        <name>learnExcitationPassword</name>
        <default_value>learnTest</default_value>
        <description>激励数据库密码</description>
      </parameter>
      <parameter>
        <name>learnExcitationPort</name>
        <default_value>30306</default_value>
        <description>激励数据库端口</description>
      </parameter>
      <parameter>
        <name>learnExcitationUser</name>
        <default_value>wdxuexi</default_value>
        <description>激励数据库用户</description>
      </parameter>
      <parameter>
        <name>learnForumHost</name>
        <default_value>************</default_value>
        <description>论坛数据库主机</description>
      </parameter>
      <parameter>
        <name>learnForumName</name>
        <default_value>forum</default_value>
        <description>论坛数据库名称</description>
      </parameter>
      <parameter>
        <name>learnForumPassword</name>
        <default_value>learnTest</default_value>
        <description>论坛数据库密码</description>
      </parameter>
      <parameter>
        <name>learnForumPort</name>
        <default_value>30306</default_value>
        <description>论坛数据库端口</description>
      </parameter>
      <parameter>
        <name>learnForumUser</name>
        <default_value>wdxuexi</default_value>
        <description>论坛数据库用户</description>
      </parameter>
      <parameter>
        <name>learnInfoHost</name>
        <default_value>************</default_value>
        <description>资讯数据库主机</description>
      </parameter>
      <parameter>
        <name>learnInfoName</name>
        <default_value>info</default_value>
        <description>资讯数据库名称</description>
      </parameter>
      <parameter>
        <name>learnInfoPassword</name>
        <default_value>learnTest</default_value>
        <description>资讯数据库密码</description>
      </parameter>
      <parameter>
        <name>learnInfoPort</name>
        <default_value>30306</default_value>
        <description>资讯数据库端口</description>
      </parameter>
      <parameter>
        <name>learnInfoUser</name>
        <default_value>wdxuexi</default_value>
        <description>资讯数据库用户</description>
      </parameter>
      <parameter>
        <name>learnLecturerHost</name>
        <default_value>************</default_value>
        <description>讲师数据库主机</description>
      </parameter>
      <parameter>
        <name>learnLecturerName</name>
        <default_value>lecturer</default_value>
        <description>讲师数据库名称</description>
      </parameter>
      <parameter>
        <name>learnLecturerPassword</name>
        <default_value>learnTest</default_value>
        <description>讲师数据库密码</description>
      </parameter>
      <parameter>
        <name>learnLecturerPort</name>
        <default_value>30306</default_value>
        <description>讲师数据库端口</description>
      </parameter>
      <parameter>
        <name>learnLecturerUser</name>
        <default_value>wdxuexi</default_value>
        <description>讲师数据库用户</description>
      </parameter>
      <parameter>
        <name>learnLiveHost</name>
        <default_value>************</default_value>
        <description>直播数据库主机</description>
      </parameter>
      <parameter>
        <name>learnLiveName</name>
        <default_value>live</default_value>
        <description>直播数据库名称</description>
      </parameter>
      <parameter>
        <name>learnLivePassword</name>
        <default_value>learnTest</default_value>
        <description>直播数据库密码</description>
      </parameter>
      <parameter>
        <name>learnLivePort</name>
        <default_value>30306</default_value>
        <description>直播数据库端口</description>
      </parameter>
      <parameter>
        <name>learnLiveUser</name>
        <default_value>wdxuexi</default_value>
        <description>直播数据库用户</description>
      </parameter>
      <parameter>
        <name>learnMarketHost</name>
        <default_value>************</default_value>
        <description>推广数据库主机</description>
      </parameter>
      <parameter>
        <name>learnMarketName</name>
        <default_value>market</default_value>
        <description>推广数据库名称</description>
      </parameter>
      <parameter>
        <name>learnMarketPassword</name>
        <default_value>learnTest</default_value>
        <description>推广数据库密码</description>
      </parameter>
      <parameter>
        <name>learnMarketPort</name>
        <default_value>30306</default_value>
        <description>推广数据库端口</description>
      </parameter>
      <parameter>
        <name>learnMarketUser</name>
        <default_value>wdxuexi</default_value>
        <description>推广数据库用户</description>
      </parameter>
      <parameter>
        <name>learnProjectHost</name>
        <default_value>************</default_value>
        <description>学习项目数据库主机</description>
      </parameter>
      <parameter>
        <name>learnProjectName</name>
        <default_value>project</default_value>
        <description>学习项目数据库名称</description>
      </parameter>
      <parameter>
        <name>learnProjectPassword</name>
        <default_value>learnTest</default_value>
        <description>学习项目数据库密码</description>
      </parameter>
      <parameter>
        <name>learnProjectPort</name>
        <default_value>30306</default_value>
        <description>学习项目数据库端口</description>
      </parameter>
      <parameter>
        <name>learnProjectUser</name>
        <default_value>wdxuexi</default_value>
        <description>学习项目数据库用户</description>
      </parameter>
      <parameter>
        <name>learnPromotedGameHost</name>
        <default_value>************</default_value>
        <description>闯关数据库主机</description>
      </parameter>
      <parameter>
        <name>learnPromotedGameName</name>
        <default_value>promotedgame</default_value>
        <description>闯关数据库名称</description>
      </parameter>
      <parameter>
        <name>learnPromotedGamePassword</name>
        <default_value>learnTest</default_value>
        <description>闯关数据库密码</description>
      </parameter>
      <parameter>
        <name>learnPromotedGamePort</name>
        <default_value>30306</default_value>
        <description>闯关数据库端口</description>
      </parameter>
      <parameter>
        <name>learnPromotedGameUser</name>
        <default_value>wdxuexi</default_value>
        <description>闯关数据库用户</description>
      </parameter>
      <parameter>
        <name>learnPushHost</name>
        <default_value>************</default_value>
        <description>推送数据库主机</description>
      </parameter>
      <parameter>
        <name>learnPushName</name>
        <default_value>push</default_value>
        <description>推送数据库名称</description>
      </parameter>
      <parameter>
        <name>learnPushPassword</name>
        <default_value>learnTest</default_value>
        <description>推送数据库密码</description>
      </parameter>
      <parameter>
        <name>learnPushPort</name>
        <default_value>30306</default_value>
        <description>推送数据库端口</description>
      </parameter>
      <parameter>
        <name>learnPushUser</name>
        <default_value>wdxuexi</default_value>
        <description>推送数据库用户</description>
      </parameter>
      <parameter>
        <name>learnReadingHost</name>
        <default_value>************</default_value>
        <description>共读数据库主机</description>
      </parameter>
      <parameter>
        <name>learnReadingName</name>
        <default_value>reading</default_value>
        <description>共读数据库名称</description>
      </parameter>
      <parameter>
        <name>learnReadingPassword</name>
        <default_value>learnTest</default_value>
        <description>共读数据库密码</description>
      </parameter>
      <parameter>
        <name>learnReadingPort</name>
        <default_value>30306</default_value>
        <description>共读数据库端口</description>
      </parameter>
      <parameter>
        <name>learnReadingUser</name>
        <default_value>wdxuexi</default_value>
        <description>共读数据库用户</description>
      </parameter>
      <parameter>
        <name>learnRecruitingHost</name>
        <default_value>************</default_value>
        <description>招募数据库主机</description>
      </parameter>
      <parameter>
        <name>learnRecruitingName</name>
        <default_value>recruiting</default_value>
        <description>招募数据库名称</description>
      </parameter>
      <parameter>
        <name>learnRecruitingPassword</name>
        <default_value>learnTest</default_value>
        <description>招募数据库密码</description>
      </parameter>
      <parameter>
        <name>learnRecruitingPort</name>
        <default_value>30306</default_value>
        <description>招募数据库端口</description>
      </parameter>
      <parameter>
        <name>learnRecruitingUser</name>
        <default_value>wdxuexi</default_value>
        <description>招募数据库用户</description>
      </parameter>
      <parameter>
        <name>learnSpecialHost</name>
        <default_value>************</default_value>
        <description>专题数据库主机</description>
      </parameter>
      <parameter>
        <name>learnSpecialName</name>
        <default_value>special</default_value>
        <description>专题数据库名称</description>
      </parameter>
      <parameter>
        <name>learnSpecialPassword</name>
        <default_value>learnTest</default_value>
        <description>专题数据库密码</description>
      </parameter>
      <parameter>
        <name>learnSpecialPort</name>
        <default_value>30306</default_value>
        <description>专题数据库端口</description>
      </parameter>
      <parameter>
        <name>learnSpecialUser</name>
        <default_value>wdxuexi</default_value>
        <description>专题数据库用户</description>
      </parameter>
      <parameter>
        <name>learnStatisticsHost</name>
        <default_value>************</default_value>
        <description>统计分析数据库主机</description>
      </parameter>
      <parameter>
        <name>learnStatisticsName</name>
        <default_value>business-view</default_value>
        <description>统计分析数据库名称</description>
      </parameter>
      <parameter>
        <name>learnStatisticsPassword</name>
        <default_value>learnTest</default_value>
        <description>统计分析数据库密码</description>
      </parameter>
      <parameter>
        <name>learnStatisticsPort</name>
        <default_value>30306</default_value>
        <description>统计分析数据库端口</description>
      </parameter>
      <parameter>
        <name>learnStatisticsUser</name>
        <default_value>wdxuexi</default_value>
        <description>统计分析数据库用户</description>
      </parameter>
      <parameter>
        <name>learnSurveyHost</name>
        <default_value>************</default_value>
        <description>调研数据库主机</description>
      </parameter>
      <parameter>
        <name>learnSurveyName</name>
        <default_value>survey</default_value>
        <description>调研数据库名称</description>
      </parameter>
      <parameter>
        <name>learnSurveyPassword</name>
        <default_value>learnTest</default_value>
        <description>调研数据库密码</description>
      </parameter>
      <parameter>
        <name>learnSurveyPort</name>
        <default_value>30306</default_value>
        <description>调研数据库端口</description>
      </parameter>
      <parameter>
        <name>learnSurveyUser</name>
        <default_value>wdxuexi</default_value>
        <description>调研数据库用户</description>
      </parameter>
      <parameter>
        <name>learnTrainHost</name>
        <default_value>************</default_value>
        <description>培训项目数据库主机</description>
      </parameter>
      <parameter>
        <name>learnTrainName</name>
        <default_value>train</default_value>
        <description>培训项目数据库名称</description>
      </parameter>
      <parameter>
        <name>learnTrainPassword</name>
        <default_value>learnTest</default_value>
        <description>培训项目数据库密码</description>
      </parameter>
      <parameter>
        <name>learnTrainPort</name>
        <default_value>30306</default_value>
        <description>培训项目数据库端口</description>
      </parameter>
      <parameter>
        <name>learnTrainUser</name>
        <default_value>wdxuexi</default_value>
        <description>培训项目数据库培训项目</description>
      </parameter>
      <parameter>
        <name>learnUserHost</name>
        <default_value>************</default_value>
        <description>用户数据库主机</description>
      </parameter>
      <parameter>
        <name>learnUserName</name>
        <default_value>user</default_value>
        <description>用户数据库名称</description>
      </parameter>
      <parameter>
        <name>learnUserPassword</name>
        <default_value>learnTest</default_value>
        <description>用户数据库密码</description>
      </parameter>
      <parameter>
        <name>learnUserPort</name>
        <default_value>30306</default_value>
        <description>用户数据库端口</description>
      </parameter>
      <parameter>
        <name>learnUserUser</name>
        <default_value>wdxuexi</default_value>
        <description>用户数据库用户</description>
      </parameter>
    </parameters>
    <log>
      <trans-log-table>
        <connection/>
        <schema/>
        <table/>
        <size_limit_lines/>
        <interval/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STATUS</id>
          <enabled>Y</enabled>
          <name>STATUS</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
          <subject/>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
          <subject/>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
          <subject/>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
          <subject/>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
          <subject/>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
          <subject/>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>STARTDATE</id>
          <enabled>Y</enabled>
          <name>STARTDATE</name>
        </field>
        <field>
          <id>ENDDATE</id>
          <enabled>Y</enabled>
          <name>ENDDATE</name>
        </field>
        <field>
          <id>LOGDATE</id>
          <enabled>Y</enabled>
          <name>LOGDATE</name>
        </field>
        <field>
          <id>DEPDATE</id>
          <enabled>Y</enabled>
          <name>DEPDATE</name>
        </field>
        <field>
          <id>REPLAYDATE</id>
          <enabled>Y</enabled>
          <name>REPLAYDATE</name>
        </field>
        <field>
          <id>LOG_FIELD</id>
          <enabled>Y</enabled>
          <name>LOG_FIELD</name>
        </field>
        <field>
          <id>EXECUTING_SERVER</id>
          <enabled>N</enabled>
          <name>EXECUTING_SERVER</name>
        </field>
        <field>
          <id>EXECUTING_USER</id>
          <enabled>N</enabled>
          <name>EXECUTING_USER</name>
        </field>
        <field>
          <id>CLIENT</id>
          <enabled>N</enabled>
          <name>CLIENT</name>
        </field>
      </trans-log-table>
      <perf-log-table>
        <connection/>
        <schema/>
        <table/>
        <interval/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>SEQ_NR</id>
          <enabled>Y</enabled>
          <name>SEQ_NR</name>
        </field>
        <field>
          <id>LOGDATE</id>
          <enabled>Y</enabled>
          <name>LOGDATE</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STEPNAME</id>
          <enabled>Y</enabled>
          <name>STEPNAME</name>
        </field>
        <field>
          <id>STEP_COPY</id>
          <enabled>Y</enabled>
          <name>STEP_COPY</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>INPUT_BUFFER_ROWS</id>
          <enabled>Y</enabled>
          <name>INPUT_BUFFER_ROWS</name>
        </field>
        <field>
          <id>OUTPUT_BUFFER_ROWS</id>
          <enabled>Y</enabled>
          <name>OUTPUT_BUFFER_ROWS</name>
        </field>
      </perf-log-table>
      <channel-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>LOGGING_OBJECT_TYPE</id>
          <enabled>Y</enabled>
          <name>LOGGING_OBJECT_TYPE</name>
        </field>
        <field>
          <id>OBJECT_NAME</id>
          <enabled>Y</enabled>
          <name>OBJECT_NAME</name>
        </field>
        <field>
          <id>OBJECT_COPY</id>
          <enabled>Y</enabled>
          <name>OBJECT_COPY</name>
        </field>
        <field>
          <id>REPOSITORY_DIRECTORY</id>
          <enabled>Y</enabled>
          <name>REPOSITORY_DIRECTORY</name>
        </field>
        <field>
          <id>FILENAME</id>
          <enabled>Y</enabled>
          <name>FILENAME</name>
        </field>
        <field>
          <id>OBJECT_ID</id>
          <enabled>Y</enabled>
          <name>OBJECT_ID</name>
        </field>
        <field>
          <id>OBJECT_REVISION</id>
          <enabled>Y</enabled>
          <name>OBJECT_REVISION</name>
        </field>
        <field>
          <id>PARENT_CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>PARENT_CHANNEL_ID</name>
        </field>
        <field>
          <id>ROOT_CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>ROOT_CHANNEL_ID</name>
        </field>
      </channel-log-table>
      <step-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STEPNAME</id>
          <enabled>Y</enabled>
          <name>STEPNAME</name>
        </field>
        <field>
          <id>STEP_COPY</id>
          <enabled>Y</enabled>
          <name>STEP_COPY</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>LOG_FIELD</id>
          <enabled>N</enabled>
          <name>LOG_FIELD</name>
        </field>
      </step-log-table>
      <metrics-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>METRICS_DATE</id>
          <enabled>Y</enabled>
          <name>METRICS_DATE</name>
        </field>
        <field>
          <id>METRICS_CODE</id>
          <enabled>Y</enabled>
          <name>METRICS_CODE</name>
        </field>
        <field>
          <id>METRICS_DESCRIPTION</id>
          <enabled>Y</enabled>
          <name>METRICS_DESCRIPTION</name>
        </field>
        <field>
          <id>METRICS_SUBJECT</id>
          <enabled>Y</enabled>
          <name>METRICS_SUBJECT</name>
        </field>
        <field>
          <id>METRICS_TYPE</id>
          <enabled>Y</enabled>
          <name>METRICS_TYPE</name>
        </field>
        <field>
          <id>METRICS_VALUE</id>
          <enabled>Y</enabled>
          <name>METRICS_VALUE</name>
        </field>
      </metrics-log-table>
    </log>
    <maxdate>
      <connection/>
      <table/>
      <field/>
      <offset>0.0</offset>
      <maxdiff>0.0</maxdiff>
    </maxdate>
    <size_rowset>10000</size_rowset>
    <sleep_time_empty>50</sleep_time_empty>
    <sleep_time_full>50</sleep_time_full>
    <unique_connections>N</unique_connections>
    <feedback_shown>Y</feedback_shown>
    <feedback_size>50000</feedback_size>
    <using_thread_priorities>Y</using_thread_priorities>
    <shared_objects_file/>
    <capture_step_performance>N</capture_step_performance>
    <step_performance_capturing_delay>1000</step_performance_capturing_delay>
    <step_performance_capturing_size_limit>100</step_performance_capturing_size_limit>
    <dependencies>
    </dependencies>
    <partitionschemas>
    </partitionschemas>
    <slaveservers>
    </slaveservers>
    <clusterschemas>
    </clusterschemas>
    <created_user>-</created_user>
    <created_date>2024/07/25 15:00:46.806</created_date>
    <modified_user>-</modified_user>
    <modified_date>2025/06/19 16:49:46.728</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
  </notepads>
  <connection>
    <name>learn_certification</name>
    <server>${learnCertificationHost}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${learnCertificationName}</database>
    <port>${learnCertificationPort}</port>
    <username>${learnCertificationUser}</username>
    <password>${learnCertificationPassword}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${learnCertificationPort}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <connection>
    <name>learn_exam</name>
    <server>${learnExamHost}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${learnExamName}</database>
    <port>${learnExamPort}</port>
    <username>${learnExamUser}</username>
    <password>${learnExamPassword}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${learnExamPort}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <connection>
    <name>learn_lecturer</name>
    <server>${learnLecturerHost}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${learnLecturerName}</database>
    <port>${learnLecturerPort}</port>
    <username>${learnLecturerUser}</username>
    <password>${learnLecturerPassword}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${learnLecturerPort}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <connection>
    <name>learn_live</name>
    <server>${learnLiveHost}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${learnLiveName}</database>
    <port>${learnLivePort}</port>
    <username>${learnLiveUser}</username>
    <password>${learnLivePassword}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${learnLivePort}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <connection>
    <name>learn_special</name>
    <server>${learnSpecialHost}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${learnSpecialName}</database>
    <port>${learnSpecialPort}</port>
    <username>${learnSpecialUser}</username>
    <password>${learnSpecialPassword}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${learnSpecialPort}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <connection>
    <name>learn_statistics</name>
    <server>${learnStatisticsHost}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${learnStatisticsName}</database>
    <port>${learnStatisticsPort}</port>
    <username>${learnStatisticsUser}</username>
    <password>${learnStatisticsPassword}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${learnStatisticsPort}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <connection>
    <name>learn_user</name>
    <server>${learnUserHost}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${learnUserName}</database>
    <port>${learnUserPort}</port>
    <username>${learnUserUser}</username>
    <password>${learnUserPassword}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${learnUserPort}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <connection>
    <name>project</name>
    <server>${learnProjectHost}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${learnProjectName}</database>
    <port>${learnProjectPort}</port>
    <username>${learnProjectUser}</username>
    <password>${learnProjectPassword}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${learnProjectPort}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <order>
    <hop>
      <from>JavaScript代码</from>
      <to>近11个月知识库插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>前11个月知识库新增</from>
      <to>JavaScript代码</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>近30天知识库新增</from>
      <to>近30天知识库插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成</from>
      <to>近30天知识库新增</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 2</from>
      <to>前11个月知识库新增</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>上个月所有专题数量</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>上个月所有直播数量</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 2 2 2</from>
      <to>排序记录 10 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 2 2 2 2</from>
      <to>排序记录 10 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 2 2 2 2 2</from>
      <to>排序记录 10 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 2 3</from>
      <to>排序记录 10 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 2 3 2</from>
      <to>排序记录 10 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 2 3 2 2</from>
      <to>排序记录 10 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 3 2</from>
      <to>排序记录 10 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 3 2 2</from>
      <to>排序记录 10 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 3 2 2 2</from>
      <to>排序记录 10 3 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 4 2</from>
      <to>排序记录 10 4 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 4 2 2</from>
      <to>排序记录 10 4 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 2 2</from>
      <to>增加常量 5 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 2 2 2</from>
      <to>增加常量 5 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 2 2 2 2</from>
      <to>增加常量 5 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 3 2</from>
      <to>增加常量 5 2 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 3 2 2</from>
      <to>增加常量 5 2 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 3 2 2 2</from>
      <to>增加常量 5 2 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 4 2</from>
      <to>增加常量 5 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 4 2 2</from>
      <to>增加常量 5 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 5</from>
      <to>增加常量 5 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 5 2</from>
      <to>增加常量 5 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 5 2 2</from>
      <to>增加常量 5 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 2 2 2 2</from>
      <to>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 2 2 2 2 2</from>
      <to>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 2 2 3</from>
      <to>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 2 2 3 2</from>
      <to>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 2 2 3 2 2</from>
      <to>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 2 3</from>
      <to>阻塞数据直到步骤都完成 3 2 2 2 2 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 2 3 2</from>
      <to>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 2 3 2 2</from>
      <to>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 3</from>
      <to>所有直播插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 3 2</from>
      <to>所有专题插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 3 2 2</from>
      <to>所有考试插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>所有专题数量</from>
      <to>记录关联 (笛卡尔输出) 6 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>所有直播数量</from>
      <to>记录关联 (笛卡尔输出) 6 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>所有考试数量</from>
      <to>记录关联 (笛卡尔输出) 6 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录</from>
      <to>记录集连接</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 2 2 2</from>
      <to>分组 7 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 2 2 2 2</from>
      <to>分组 7 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 2 2 2 2 2</from>
      <to>分组 7 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 2 3</from>
      <to>分组 7 5</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 2 3 2</from>
      <to>分组 7 5 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 2 3 2 2</from>
      <to>分组 7 5 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 3 2</from>
      <to>分组 7 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 3 2 2</from>
      <to>分组 7 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 3 2 2 2</from>
      <to>分组 7 3 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 4 2</from>
      <to>分组 7 4 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 4 2 2</from>
      <to>分组 7 4 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 2</from>
      <to>记录集连接</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 2 2 2</from>
      <to>分组 6 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 2 2 2 2</from>
      <to>分组 6 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 2 2 2 2 2</from>
      <to>分组 6 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 2 3</from>
      <to>分组 6 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 2 3 2</from>
      <to>分组 6 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 2 3 2 2</from>
      <to>分组 6 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 3 2</from>
      <to>分组 6 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 3 2 2</from>
      <to>分组 6 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 3 2 2 2</from>
      <to>分组 6 3 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 4 2</from>
      <to>分组 6 4 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 4 2 2</from>
      <to>分组 6 4 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>昨日所有专题数量</from>
      <to>记录关联 (笛卡尔输出) 6 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>昨日所有直播数量</from>
      <to>记录关联 (笛卡尔输出) 6 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>本月其他考试举办场次</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>本月所有专题数量</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>本月所有直播数量</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>本月考试举办场次</from>
      <to>记录关联 (笛卡尔输出) 6 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>查出全部管理员及其管辖范围</from>
      <to>排序记录 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>查出全部管理员及其部门 2</from>
      <to>排序记录</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 2 2 2 2</from>
      <to>排序记录 9 4 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 2 2 2 2 2</from>
      <to>排序记录 9 4 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 2 2 3</from>
      <to>排序记录 9 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 2 2 3 2</from>
      <to>排序记录 9 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 2 2 3 2 2</from>
      <to>排序记录 9 3 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 2 3</from>
      <to>排序记录 9 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 2 3 2</from>
      <to>排序记录 9 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 2 3 2 2</from>
      <to>排序记录 9 2 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 3</from>
      <to>排序记录 9 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 3 2</from>
      <to>排序记录 9 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 3 2 2</from>
      <to>排序记录 9 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录集连接</from>
      <to>字段选择</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 2 2</from>
      <to>上个月直播插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 2 2 2</from>
      <to>上个月专题插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3</from>
      <to>本月直播插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 2</from>
      <to>本月专题插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 2 2</from>
      <to>本月其他考试举办场次插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 3 2 2 2 2 2 3</from>
      <to>昨日直播插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 2</from>
      <to>昨日专题插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 2 2</from>
      <to>本月考试举办场次插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 2 2 2 2 3</from>
      <to>排序记录 10 2 2 2 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 2 3 2 3</from>
      <to>排序记录 10 2 3 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 3 2 2 3</from>
      <to>排序记录 10 3 2 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 2 2 2 3</from>
      <to>增加常量 5 2 3 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 3 2 2 3</from>
      <to>增加常量 5 2 2 3 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 5 2 3</from>
      <to>增加常量 5 3 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 2 2 3 2 3</from>
      <to>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 2 3 2 3</from>
      <to>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 3 2 3</from>
      <to>所有学习项目插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>所有学习项目数量</from>
      <to>记录关联 (笛卡尔输出) 6 3 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 2 2 2 2 3</from>
      <to>分组 7 2 2 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 2 3 2 3</from>
      <to>分组 7 5 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 3 2 2 3</from>
      <to>分组 7 3 2 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 2 2 2 2 3</from>
      <to>分组 6 2 2 2 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 2 3 2 3</from>
      <to>分组 6 2 3 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 3 2 2 3</from>
      <to>分组 6 3 2 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>所有本周开始的学习项目数量</from>
      <to>记录关联 (笛卡尔输出) 6 2 3 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>所有本月开始学习项目数量</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 3 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 2 2 3 2 3</from>
      <to>排序记录 9 3 2 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 2 3 2 3</from>
      <to>排序记录 9 2 2 2 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 3 2 3</from>
      <to>排序记录 9 2 3 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 2 3</from>
      <to>所有本月开始学习项目插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 2 3</from>
      <to>所有本周开始的学习项目插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 3 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 2 3 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 3 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 2 2 2 2 3 2</from>
      <to>排序记录 10 2 2 2 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 2 3 2 3 2</from>
      <to>排序记录 10 2 3 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 3 2 2 3 2</from>
      <to>排序记录 10 3 2 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 2 2 2 3 2</from>
      <to>增加常量 5 2 3 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 3 2 2 3 2</from>
      <to>增加常量 5 2 2 3 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 5 2 3 2</from>
      <to>增加常量 5 3 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 2 2 3 2 3 2</from>
      <to>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 2 3 2 3 2</from>
      <to>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 3 2 3 2</from>
      <to>所有面授项目插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>所有面授项目数量</from>
      <to>记录关联 (笛卡尔输出) 6 3 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 2 2 2 2 3 2</from>
      <to>分组 7 2 2 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 2 3 2 3 2</from>
      <to>分组 7 5 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 3 2 2 3 2</from>
      <to>分组 7 3 2 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 2 2 2 2 3 2</from>
      <to>分组 6 2 2 2 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 2 3 2 3 2</from>
      <to>分组 6 2 3 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 3 2 2 3 2</from>
      <to>分组 6 3 2 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>所有本周开始的面授项目数量</from>
      <to>记录关联 (笛卡尔输出) 6 2 3 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>所有本月开始面授项目数量</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 3 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 2 2 3 2 3 2</from>
      <to>排序记录 9 3 2 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 2 3 2 3 2</from>
      <to>排序记录 9 2 2 2 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 3 2 3 2</from>
      <to>排序记录 9 2 3 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 2 3 2</from>
      <to>所有本月开始面授项目插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 2 3 2</from>
      <to>所有本周开始的面授项目插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 3 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 2 3 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 3 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 2 2 2 2 3 2 2</from>
      <to>排序记录 10 2 2 2 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 2 3 2 3 2 2</from>
      <to>排序记录 10 2 3 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 3 2 2 3 2 2</from>
      <to>排序记录 10 3 2 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 2 2 2 3 2 2</from>
      <to>增加常量 5 2 3 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 3 2 2 3 2 2</from>
      <to>增加常量 5 2 2 3 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 5 2 3 2 2</from>
      <to>增加常量 5 3 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 2 2 3 2 3 2 2</from>
      <to>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 2 3 2 3 2 2</from>
      <to>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 3 2 3 2 2</from>
      <to>所有快速培训项目插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>所有快速培训项目数量</from>
      <to>记录关联 (笛卡尔输出) 6 3 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 2 2 2 2 3 2 2</from>
      <to>分组 7 2 2 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 2 3 2 3 2 2</from>
      <to>分组 7 5 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 3 2 2 3 2 2</from>
      <to>分组 7 3 2 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 2 2 2 2 3 2 2</from>
      <to>分组 6 2 2 2 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 2 3 2 3 2 2</from>
      <to>分组 6 2 3 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 3 2 2 3 2 2</from>
      <to>分组 6 3 2 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>所有本周开始的快速培训项目数量</from>
      <to>记录关联 (笛卡尔输出) 6 2 3 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>所有本月开始快速培训项目数量</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 3 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 2 2 3 2 3 2 2</from>
      <to>排序记录 9 3 2 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 2 3 2 3 2 2</from>
      <to>排序记录 9 2 2 2 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 3 2 3 2 2</from>
      <to>排序记录 9 2 3 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 2 3 2 2</from>
      <to>所有本月开始快速培训项目插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 2 3 2 2</from>
      <to>所有本周开始的快速培训项目插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 3 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 2 3 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 3 2 3 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 2 3 2 3 2 2 2</from>
      <to>排序记录 10 2 3 2 3 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 5 2 3 2 2 2</from>
      <to>增加常量 5 3 2 3 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 3 2 3 2 2 2</from>
      <to>所有讲师插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>所有内部讲师数量</from>
      <to>记录关联 (笛卡尔输出) 6 3 2 3 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 2 3 2 3 2 2 2</from>
      <to>分组 7 5 2 3 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 2 3 2 3 2 2 2</from>
      <to>分组 6 2 3 2 3 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 3 2 3 2 2 2</from>
      <to>排序记录 9 2 3 2 3 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 3 2 3 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>本年所有讲师数量</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 2 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 2 2 2 3</from>
      <to>排序记录 10 2 2 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 3 2 3</from>
      <to>排序记录 10 3 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 4 2 3</from>
      <to>排序记录 10 4 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 2 2 3</from>
      <to>增加常量 5 2 3 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 3 2 3</from>
      <to>增加常量 5 2 2 3 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 4 2 3</from>
      <to>增加常量 5 2 2 2 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 2 2 2 2 3</from>
      <to>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 2 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 2 2 3 3</from>
      <to>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 2 3 3</from>
      <to>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 2 2 2 3</from>
      <to>分组 7 2 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 3 2 3</from>
      <to>分组 7 3 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 4 2 3</from>
      <to>分组 7 4 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 2 2 2 3</from>
      <to>分组 6 2 2 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 3 2 3</from>
      <to>分组 6 3 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 4 2 3</from>
      <to>分组 6 4 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>本月新增讲师数量</from>
      <to>记录关联 (笛卡尔输出) 6 2 3 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>上个月所有讲师数量</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 3 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 2 2 2 2 3</from>
      <to>排序记录 9 4 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 2 2 3 3</from>
      <to>排序记录 9 3 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 2 3 3</from>
      <to>排序记录 9 2 2 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 2 2 3</from>
      <to>本年新增讲师插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 3</from>
      <to>上个月新增讲师插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 3</from>
      <to>本月新增讲师插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 2 3 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 3 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 2 2 3</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 2 3 2 3 2 2 2 2</from>
      <to>排序记录 10 2 3 2 3 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 5 2 3 2 2 2 2</from>
      <to>增加常量 5 3 2 3 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 3 2 3 2 2 2 2</from>
      <to>所有证书插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>所有证书数量</from>
      <to>记录关联 (笛卡尔输出) 6 3 2 3 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 2 3 2 3 2 2 2 2</from>
      <to>分组 7 5 2 3 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 2 3 2 3 2 2 2 2</from>
      <to>分组 6 2 3 2 3 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 3 2 3 2 2 2 2</from>
      <to>排序记录 9 2 3 2 3 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>本年度发证人数数量</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 2 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 2 2 2 3 2</from>
      <to>排序记录 10 2 2 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 2 2 3 2</from>
      <to>增加常量 5 2 3 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 3 2 3 2</from>
      <to>排序记录 10 3 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 2 3 3 2</from>
      <to>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 6 4 2 3 2</from>
      <to>排序记录 10 4 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 2 2 2 3 2</from>
      <to>分组 7 2 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 2 2 2 3 2</from>
      <to>分组 6 2 2 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 3 2 3 2</from>
      <to>增加常量 5 2 2 3 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>本年度新增证书数量</from>
      <to>记录关联 (笛卡尔输出) 6 2 3 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>分组 7 4 2 3 2</from>
      <to>增加常量 5 2 2 2 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 2 3 3 2</from>
      <to>排序记录 9 2 2 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 2 2 2 2 3 2</from>
      <to>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 2 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 3 2</from>
      <to>本年度新增证书插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>增加常量 5 2 2 3 3 2</from>
      <to>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 3 2 3 2 2 2 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 2 3 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 3 2</from>
      <to>本月发证数量插入更新</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 3 2 3 2</from>
      <to>分组 7 3 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 10 4 2 3 2</from>
      <to>分组 7 4 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 2 2 3 3 2</from>
      <to>排序记录 9 3 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>记录关联 (笛卡尔输出) 6 2 2 2 2 3 2</from>
      <to>排序记录 9 4 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 3 2 3 2</from>
      <to>分组 6 3 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>本月发证数量</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 3 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>排序记录 9 4 2 3 2</from>
      <to>分组 6 4 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 3 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>字段选择</from>
      <to>记录关联 (笛卡尔输出) 6 2 2 2 2 3 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 2 2 3 2</from>
      <to>本年发证数量插入更新</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <step>
    <name>JavaScript代码</name>
    <type>ScriptValueMod</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <compatible>N</compatible>
    <optimizationLevel>9</optimizationLevel>
    <jsScripts>
      <jsScript>
        <jsScript_type>0</jsScript_type>
        <jsScript_name>Script 1</jsScript_name>
        <jsScript_script>//Script here

var month = month + day;
var increase = increase;
var orgId = orgId;</jsScript_script>
      </jsScript>
    </jsScripts>
    <fields>
      <field>
        <name>month</name>
        <rename>month</rename>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
        <replace>Y</replace>
      </field>
      <field>
        <name>increase</name>
        <rename>increase</rename>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
        <replace>Y</replace>
      </field>
      <field>
        <name>orgId</name>
        <rename>orgId</rename>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
        <replace>Y</replace>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>400</xloc>
      <yloc>144</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>上个月专题插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>last_month_increase</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1760</xloc>
      <yloc>1408</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>上个月所有专题数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_special</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from special l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and (l.belong_type = null or l.belong_type=0)
and period_diff( date_format( now( ) , '%Y%m' ) , date_format( l.create_time, '%Y%m' ) ) =1</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>上个月所有知识数量</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>上个月所有知识数量</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>上个月所有知识数量</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>上个月所有知识数量</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>800</xloc>
      <yloc>1344</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>上个月所有直播数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_live</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from live l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and l.is_train = 0
and period_diff( date_format( now( ) , '%Y%m' ) , date_format( l.create_time, '%Y%m' ) ) =1</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>上个月所有知识数量</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>上个月所有知识数量</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>上个月所有知识数量</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>上个月所有知识数量</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>800</xloc>
      <yloc>736</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>上个月所有讲师数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_lecturer</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from lecturer l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and DATE_FORMAT(l.create_time, '%Y-%m') = DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 MONTH), '%Y-%m')  and l.type = 0</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月所有直播数量 2</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月所有直播数量 2</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>本月所有直播数量 2</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月所有直播数量 2</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>816</xloc>
      <yloc>3856</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>上个月新增讲师插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>month_increase</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1776</xloc>
      <yloc>3920</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>上个月直播插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>last_month_increase</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1760</xloc>
      <yloc>800</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1040</xloc>
      <yloc>512</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1040</xloc>
      <yloc>1120</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 2 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1040</xloc>
      <yloc>1728</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 2 2 2 2 3</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1053</xloc>
      <yloc>2259</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 2 2 2 2 3 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1055</xloc>
      <yloc>2756</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 2 2 2 2 3 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1059</xloc>
      <yloc>3264</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 2 2 2 3</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1056</xloc>
      <yloc>3776</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 2 2 2 3 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1054</xloc>
      <yloc>4423</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 2 3</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1056</xloc>
      <yloc>368</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 2 3 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1056</xloc>
      <yloc>976</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 2 3 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1056</xloc>
      <yloc>1584</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 2 3 2 3</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1069</xloc>
      <yloc>2115</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 2 3 2 3 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1071</xloc>
      <yloc>2612</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 2 3 2 3 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1075</xloc>
      <yloc>3120</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 2 3 2 3 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1072</xloc>
      <yloc>3632</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 2 3 2 3 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1070</xloc>
      <yloc>4279</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 3 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1040</xloc>
      <yloc>656</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 3 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1040</xloc>
      <yloc>1264</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 3 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1040</xloc>
      <yloc>1888</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 3 2 2 3</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1053</xloc>
      <yloc>2403</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 3 2 2 3 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1055</xloc>
      <yloc>2900</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 3 2 2 3 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1059</xloc>
      <yloc>3408</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 3 2 3</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1056</xloc>
      <yloc>3920</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 3 2 3 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1054</xloc>
      <yloc>4567</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 4 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1040</xloc>
      <yloc>800</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 4 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1040</xloc>
      <yloc>1408</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 4 2 3</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1056</xloc>
      <yloc>4064</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 6 4 2 3 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>id</name>
      </field>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1054</xloc>
      <yloc>4711</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1216</xloc>
      <yloc>512</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1216</xloc>
      <yloc>1120</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1216</xloc>
      <yloc>1728</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 2 2 2 3</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1229</xloc>
      <yloc>2259</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 2 2 2 3 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1231</xloc>
      <yloc>2756</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 2 2 2 3 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1235</xloc>
      <yloc>3264</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 2 2 3</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1232</xloc>
      <yloc>3776</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 2 2 3 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1230</xloc>
      <yloc>4423</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 3 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1216</xloc>
      <yloc>656</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 3 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1216</xloc>
      <yloc>1264</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 3 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1216</xloc>
      <yloc>1888</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 3 2 2 3</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1229</xloc>
      <yloc>2403</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 3 2 2 3 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1231</xloc>
      <yloc>2900</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 3 2 2 3 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1235</xloc>
      <yloc>3408</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 3 2 3</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1232</xloc>
      <yloc>3920</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 3 2 3 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1230</xloc>
      <yloc>4567</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 4 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1216</xloc>
      <yloc>800</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 4 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1216</xloc>
      <yloc>1408</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 4 2 3</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1232</xloc>
      <yloc>4064</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 4 2 3 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1230</xloc>
      <yloc>4711</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 5</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1232</xloc>
      <yloc>368</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 5 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1232</xloc>
      <yloc>976</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 5 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1232</xloc>
      <yloc>1584</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 5 2 3</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1245</xloc>
      <yloc>2115</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 5 2 3 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1247</xloc>
      <yloc>2612</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 5 2 3 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1251</xloc>
      <yloc>3120</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 5 2 3 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1248</xloc>
      <yloc>3632</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>分组 7 5 2 3 2 2 2 2</name>
    <type>GroupBy</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <all_rows>N</all_rows>
    <ignore_aggregate>N</ignore_aggregate>
    <field_ignore/>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>grp</prefix>
    <add_linenr>N</add_linenr>
    <linenr_fieldname/>
    <give_back_row>N</give_back_row>
    <group>
      <field>
        <name>user_id</name>
      </field>
    </group>
    <fields>
      <field>
        <aggregate>total</aggregate>
        <subject>user_id</subject>
        <type>COUNT_ALL</type>
        <valuefield/>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1246</xloc>
      <yloc>4279</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>前11个月知识库新增</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_lecturer</connection>
    <sql>SELECT
	date_range.MONTH AS month,
	COALESCE ( data_counts.count, 0 ) AS increase,
'-1' day,
IFNULL(data_counts.org_id,' ') orgId
FROM
	(
	SELECT
		DATE_FORMAT( NOW() - INTERVAL ( n - 1 ) MONTH, '%Y-%m' ) AS MONTH 
	FROM
		(
		SELECT
			1 AS n UNION ALL
		SELECT
			2 UNION ALL
		SELECT
			3 UNION ALL
		SELECT
			4 UNION ALL
		SELECT
			5 UNION ALL
		SELECT
			6 UNION ALL
		SELECT
			7 UNION ALL
		SELECT
			8 UNION ALL
		SELECT
			9 UNION ALL
		SELECT
			10 UNION ALL
		SELECT
			11 UNION ALL
		SELECT
			12 
		) AS month_range 
	) AS date_range
	LEFT JOIN (
	SELECT
		DATE_FORMAT( create_time, '%Y-%m' ) AS MONTH,
		COUNT(*) AS count,
		org_id 
	FROM
		lecturer_material 
	WHERE
		create_time >= DATE_FORMAT( NOW() - INTERVAL 12 MONTH, '%Y-%m-01' ) 
	GROUP BY
		DATE_FORMAT( create_time, '%Y-%m' ),org_id 
	) AS data_counts ON date_range.MONTH = data_counts.MONTH 
ORDER BY
	date_range.MONTH,data_counts.org_id;
</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>month</name>
        <length>7</length>
        <precision>-1</precision>
        <origin>前11个月知识库新增</origin>
        <comments>month</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>Integer</type>
        <storagetype>normal</storagetype>
        <name>increase</name>
        <length>15</length>
        <precision>0</precision>
        <origin>前11个月知识库新增</origin>
        <comments>increase</comments>
        <conversion_Mask>####0;-####0</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>day</name>
        <length>2</length>
        <precision>-1</precision>
        <origin>前11个月知识库新增</origin>
        <comments>day</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>orgId</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>前11个月知识库新增</origin>
        <comments>orgId</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>304</xloc>
      <yloc>144</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 2 2 2 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>5</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1360</xloc>
      <yloc>800</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 2 2 2 2 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>4</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1360</xloc>
      <yloc>1408</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 2 2 2 2 3</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>12</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1376</xloc>
      <yloc>4064</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 2 2 2 2 3 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>21</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1374</xloc>
      <yloc>4711</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 2 2 3</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>5</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1344</xloc>
      <yloc>656</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 2 2 3 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>4</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1344</xloc>
      <yloc>1264</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 2 2 3 2 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>6</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1344</xloc>
      <yloc>1888</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 2 2 3 2 3</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>9</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1357</xloc>
      <yloc>2403</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 2 2 3 2 3 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>10</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1359</xloc>
      <yloc>2900</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 2 2 3 2 3 2 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>11</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1363</xloc>
      <yloc>3408</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 2 2 3 3</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>12</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1360</xloc>
      <yloc>3920</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 2 2 3 3 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>21</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1358</xloc>
      <yloc>4567</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 2 3</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>5</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1344</xloc>
      <yloc>512</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 2 3 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>4</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1344</xloc>
      <yloc>1120</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 2 3 2 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>6</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1344</xloc>
      <yloc>1728</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 2 3 2 3</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>9</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1357</xloc>
      <yloc>2259</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 2 3 2 3 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>10</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1359</xloc>
      <yloc>2756</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 2 3 2 3 2 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>11</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1363</xloc>
      <yloc>3264</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 2 3 3</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>12</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1360</xloc>
      <yloc>3776</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 2 3 3 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>21</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1358</xloc>
      <yloc>4423</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 3</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>5</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1344</xloc>
      <yloc>368</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 3 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>4</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1344</xloc>
      <yloc>976</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 3 2 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>6</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1344</xloc>
      <yloc>1584</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 3 2 3</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>9</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1357</xloc>
      <yloc>2115</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 3 2 3 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>10</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1359</xloc>
      <yloc>2612</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 3 2 3 2 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>11</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1363</xloc>
      <yloc>3120</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 3 2 3 2 2 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>12</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1360</xloc>
      <yloc>3632</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>增加常量 5 3 2 3 2 2 2 2</name>
    <type>Constant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>type</name>
        <type>Integer</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif>21</nullif>
        <length>-1</length>
        <precision>-1</precision>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1358</xloc>
      <yloc>4279</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>字段选择</name>
    <type>SelectValues</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <name>level_path</name>
        <rename>user_level_path</rename>
      </field>
      <field>
        <name>user_id</name>
      </field>
      <field>
        <name>org_id</name>
        <rename>user_org_id</rename>
      </field>
      <select_unspecified>N</select_unspecified>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>448</xloc>
      <yloc>4080</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有专题插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>total</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1456</xloc>
      <yloc>976</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有专题数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_special</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from special l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and (l.belong_type = null or l.belong_type=0)</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>800</xloc>
      <yloc>880</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有内部讲师数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_lecturer</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from lecturer l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0   and l.type = 0</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>816</xloc>
      <yloc>3536</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有学习项目插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>total</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1469</xloc>
      <yloc>2115</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有学习项目数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>project</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from project l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and l.project_type = 0 and l.project_type = 0 and l.flag = 0</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>813</xloc>
      <yloc>2019</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有快速培训项目插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>total</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1475</xloc>
      <yloc>3120</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有快速培训项目数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>project</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from project l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and l.project_type = 1</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>819</xloc>
      <yloc>3024</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有本周开始的学习项目插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>yesterday_increase</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1773</xloc>
      <yloc>2259</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有本周开始的学习项目数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>project</connection>
    <sql>select l.id 
	,l.org_id
	,g.level_path
	,l.create_by
from project l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and l.project_type = 0 and l.type = 0 and YEARWEEK(start_time, 1) = YEARWEEK(CURDATE(), 1) and l.project_type = 0 and l.flag = 0</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>昨日所有知识数量</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>昨日所有知识数量</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>昨日所有知识数量</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>昨日所有知识数量</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>813</xloc>
      <yloc>2195</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有本周开始的快速培训项目插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>yesterday_increase</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1779</xloc>
      <yloc>3264</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有本周开始的快速培训项目数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>project</connection>
    <sql>select l.id 
	,l.org_id
	,g.level_path
	,l.create_by
from project l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and l.project_type = 1 and l.type = 0 and YEARWEEK(start_time, 1) = YEARWEEK(CURDATE(), 1)</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>昨日所有知识数量</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>昨日所有知识数量</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>昨日所有知识数量</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>昨日所有知识数量</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>819</xloc>
      <yloc>3200</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有本周开始的面授项目插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>yesterday_increase</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1775</xloc>
      <yloc>2756</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有本周开始的面授项目数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>project</connection>
    <sql>select l.id 
	,l.org_id
	,g.level_path
	,l.create_by
from project l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and l.project_type = 3 and l.type = 0 and YEARWEEK(start_time, 1) = YEARWEEK(CURDATE(), 1)</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>昨日所有知识数量</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>昨日所有知识数量</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>昨日所有知识数量</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>昨日所有知识数量</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>815</xloc>
      <yloc>2692</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有本月开始学习项目插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>month_increase</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1773</xloc>
      <yloc>2403</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有本月开始学习项目数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>project</connection>
    <sql>select l.id 
	,l.org_id
	,g.level_path
	,l.create_by
from project l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and l.project_type = 0 and l.type = 0 and start_time >= DATE_FORMAT(CURDATE(), '%Y-%m-01') and l.project_type = 0 and l.flag = 0</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月所有知识数量</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月所有知识数量</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>本月所有知识数量</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月所有知识数量</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>813</xloc>
      <yloc>2339</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有本月开始快速培训项目插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>month_increase</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1779</xloc>
      <yloc>3408</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有本月开始快速培训项目数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>project</connection>
    <sql>select l.id 
	,l.org_id
	,g.level_path
	,l.create_by
from project l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and l.project_type = 1 and l.type = 0 and start_time >= DATE_FORMAT(CURDATE(), '%Y-%m-01')</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月所有知识数量</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月所有知识数量</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>本月所有知识数量</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月所有知识数量</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>819</xloc>
      <yloc>3344</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有本月开始面授项目插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>month_increase</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1775</xloc>
      <yloc>2900</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有本月开始面授项目数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>project</connection>
    <sql>select l.id 
	,l.org_id
	,g.level_path
	,l.create_by
from project l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and l.project_type = 3 and l.type = 0 and start_time >= DATE_FORMAT(CURDATE(), '%Y-%m-01')</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月所有知识数量</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月所有知识数量</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>本月所有知识数量</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月所有知识数量</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>815</xloc>
      <yloc>2836</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有直播插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>total</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1456</xloc>
      <yloc>368</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有直播数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_live</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from live l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and l.is_train = 0</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>800</xloc>
      <yloc>272</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有考试插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>total</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1456</xloc>
      <yloc>1584</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有考试数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_exam</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from ex_exam l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and l.is_train = 0 and l.exam_type =1 </sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>800</xloc>
      <yloc>1488</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有讲师插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>total</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1488</xloc>
      <yloc>3632</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有证书插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>total</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1486</xloc>
      <yloc>4279</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有证书数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_certification</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from certification l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 </sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>814</xloc>
      <yloc>4183</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有面授项目插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>total</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1471</xloc>
      <yloc>2612</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>所有面授项目数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>project</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from project l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and l.project_type = 3</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>所有知识数量</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>815</xloc>
      <yloc>2516</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录</name>
    <type>SortRows</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>240</xloc>
      <yloc>4128</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1120</xloc>
      <yloc>512</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1120</xloc>
      <yloc>1120</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 2 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1120</xloc>
      <yloc>1728</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 2 2 2 2 3</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1133</xloc>
      <yloc>2259</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 2 2 2 2 3 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1135</xloc>
      <yloc>2756</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 2 2 2 2 3 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1139</xloc>
      <yloc>3264</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 2 2 2 3</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1136</xloc>
      <yloc>3776</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 2 2 2 3 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1134</xloc>
      <yloc>4423</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 2 3</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1136</xloc>
      <yloc>368</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 2 3 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1136</xloc>
      <yloc>976</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 2 3 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1136</xloc>
      <yloc>1584</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 2 3 2 3</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1149</xloc>
      <yloc>2115</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 2 3 2 3 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1151</xloc>
      <yloc>2612</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 2 3 2 3 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1155</xloc>
      <yloc>3120</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 2 3 2 3 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1152</xloc>
      <yloc>3632</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 2 3 2 3 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1150</xloc>
      <yloc>4279</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 3 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1120</xloc>
      <yloc>656</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 3 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1120</xloc>
      <yloc>1264</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 3 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1120</xloc>
      <yloc>1888</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 3 2 2 3</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1133</xloc>
      <yloc>2403</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 3 2 2 3 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1135</xloc>
      <yloc>2900</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 3 2 2 3 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1139</xloc>
      <yloc>3408</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 3 2 3</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1136</xloc>
      <yloc>3920</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 3 2 3 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1134</xloc>
      <yloc>4567</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 4 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1120</xloc>
      <yloc>800</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 4 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1120</xloc>
      <yloc>1408</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 4 2 3</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1136</xloc>
      <yloc>4064</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 10 4 2 3 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1134</xloc>
      <yloc>4711</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>256</xloc>
      <yloc>4000</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>960</xloc>
      <yloc>512</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>960</xloc>
      <yloc>1120</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 2 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>960</xloc>
      <yloc>1728</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 2 2 2 2 3</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>973</xloc>
      <yloc>2259</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 2 2 2 2 3 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>975</xloc>
      <yloc>2756</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 2 2 2 2 3 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>979</xloc>
      <yloc>3264</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 2 2 2 3</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>976</xloc>
      <yloc>3776</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 2 2 2 3 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>974</xloc>
      <yloc>4423</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 2 3</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>976</xloc>
      <yloc>368</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 2 3 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>976</xloc>
      <yloc>976</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 2 3 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>976</xloc>
      <yloc>1584</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 2 3 2 3</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>989</xloc>
      <yloc>2115</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 2 3 2 3 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>991</xloc>
      <yloc>2612</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 2 3 2 3 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>995</xloc>
      <yloc>3120</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 2 3 2 3 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>992</xloc>
      <yloc>3632</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 2 3 2 3 2 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>990</xloc>
      <yloc>4279</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 3 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>960</xloc>
      <yloc>656</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 3 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>960</xloc>
      <yloc>1264</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 3 2 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>960</xloc>
      <yloc>1888</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 3 2 2 3</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>973</xloc>
      <yloc>2403</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 3 2 2 3 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>975</xloc>
      <yloc>2900</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 3 2 2 3 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>979</xloc>
      <yloc>3408</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 3 2 3</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>976</xloc>
      <yloc>3920</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 3 2 3 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>974</xloc>
      <yloc>4567</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 4 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>960</xloc>
      <yloc>800</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 4 2 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>960</xloc>
      <yloc>1408</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 4 2 3</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>976</xloc>
      <yloc>4064</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>排序记录 9 4 2 3 2</name>
    <type>SortRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <sort_size>1000000</sort_size>
    <free_memory/>
    <compress>N</compress>
    <compress_variable/>
    <unique_rows>N</unique_rows>
    <fields>
      <field>
        <name>id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
      <field>
        <name>user_id</name>
        <ascending>Y</ascending>
        <case_sensitive>N</case_sensitive>
        <collator_enabled>N</collator_enabled>
        <collator_strength>0</collator_strength>
        <presorted>N</presorted>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>974</xloc>
      <yloc>4711</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>昨日专题插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>yesterday_increase</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1760</xloc>
      <yloc>1120</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>昨日所有专题数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_special</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from special l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and (l.belong_type = null or l.belong_type=0)
AND date(l.create_time) = date_sub(curdate(),interval 1 day);</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>昨日所有知识数量</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>昨日所有知识数量</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>昨日所有知识数量</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>昨日所有知识数量</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>800</xloc>
      <yloc>1056</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>昨日所有直播数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_live</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from live l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and l.is_train = 0
AND date(l.create_time) = date_sub(curdate(),interval 1 day);</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>昨日所有知识数量</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>昨日所有知识数量</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>昨日所有知识数量</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>昨日所有知识数量</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>800</xloc>
      <yloc>448</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>昨日直播插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>yesterday_increase</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1760</xloc>
      <yloc>512</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本年发证数量插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>last_month_increase</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1776</xloc>
      <yloc>4704</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本年度发证人数数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_certification</connection>
    <sql>select cr.id
	,l.org_id
	,g.level_path
	,l.create_by
from certification_relate cr LEFT JOIN certification l  on l.id = cr.cer_id
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and YEAR(cr.create_time) = YEAR(CURDATE())</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本年所有讲师数量 2</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本年所有讲师数量 2</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>本年所有讲师数量 2</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本年所有讲师数量 2</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>816</xloc>
      <yloc>4640</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本年度新增证书插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>yesterday_increase</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1774</xloc>
      <yloc>4423</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本年度新增证书数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_certification</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from certification l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and YEAR(l.create_time) = YEAR(CURDATE())</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月新增讲师数量</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月新增讲师数量</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>本月新增讲师数量</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月新增讲师数量</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>814</xloc>
      <yloc>4359</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本年所有讲师数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_lecturer</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from lecturer l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and YEAR(l.create_time) = YEAR(CURDATE())  and l.type = 0</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>上个月所有直播数量 2</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>上个月所有直播数量 2</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>上个月所有直播数量 2</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>上个月所有直播数量 2</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>816</xloc>
      <yloc>4000</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本年新增讲师插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>last_month_increase</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1776</xloc>
      <yloc>4064</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本月专题插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>month_increase</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1760</xloc>
      <yloc>1264</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本月其他考试举办场次</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_exam</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from ex_exam l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and l.is_train != 0 and l.exam_type =1 
and date_format( l.start_time, '%Y%m' ) = date_format( curdate( ) , '%Y%m' )</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月所有知识数量</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月所有知识数量</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>本月所有知识数量</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月所有知识数量</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>800</xloc>
      <yloc>1824</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本月其他考试举办场次插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>month_increase</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1760</xloc>
      <yloc>1888</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本月发证数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_certification</connection>
    <sql>select cr.id
	,l.org_id
	,g.level_path
	,l.create_by
from certification_relate cr LEFT JOIN certification l  on l.id = cr.cer_id
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and DATE_FORMAT(cr.create_time, '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m')</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月所有直播数量 2</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月所有直播数量 2</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>本月所有直播数量 2</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月所有直播数量 2</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>814</xloc>
      <yloc>4503</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本月发证数量插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>month_increase</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1774</xloc>
      <yloc>4567</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本月所有专题数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_special</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from special l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and (l.belong_type = null or l.belong_type=0)
and date_format( l.create_time, '%Y%m' ) = date_format( curdate( ) , '%Y%m' )</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月所有知识数量</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月所有知识数量</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>本月所有知识数量</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月所有知识数量</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>800</xloc>
      <yloc>1200</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本月所有直播数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_live</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from live l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and l.is_train = 0
and date_format( l.create_time, '%Y%m' ) = date_format( curdate( ) , '%Y%m' )</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月所有知识数量</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月所有知识数量</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>本月所有知识数量</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月所有知识数量</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>800</xloc>
      <yloc>592</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本月新增讲师插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>yesterday_increase</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1776</xloc>
      <yloc>3776</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本月新增讲师数量</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_lecturer</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from lecturer l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and DATE_FORMAT(l.create_time, '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m')  and l.type = 0</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月新增讲师数量</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月新增讲师数量</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>本月新增讲师数量</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>本月新增讲师数量</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>816</xloc>
      <yloc>3712</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本月直播插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>month_increase</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1760</xloc>
      <yloc>656</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本月考试举办场次</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_exam</connection>
    <sql>select l.id
	,l.org_id
	,g.level_path
	,l.create_by
from ex_exam l
inner join sys_org g on g.id = l.org_id
where l.is_del = 0 and l.is_train = 0 and l.exam_type =1 
and date_format( l.start_time, '%Y%m' ) = date_format( curdate( ) , '%Y%m' )</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>昨日所有知识数量</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>昨日所有知识数量</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>昨日所有知识数量</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>create_by</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>昨日所有知识数量</origin>
        <comments>create_by</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>800</xloc>
      <yloc>1664</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>本月考试举办场次插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource</table>
      <key>
        <name>user_id</name>
        <field>user_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>type</name>
        <field>type</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>user_id</name>
        <rename>user_id</rename>
        <update>N</update>
      </value>
      <value>
        <name>yesterday_increase</name>
        <rename>total</rename>
        <update>Y</update>
      </value>
      <value>
        <name>type</name>
        <rename>type</rename>
        <update>N</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1760</xloc>
      <yloc>1728</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>查出全部管理员及其管辖范围</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_user</connection>
    <sql>select
	suo.user_id,
	so.level_path
from
	sys_user_org suo
	inner join sys_org so on suo.org_id = so.id
where
	suo.user_id in (
	select distinct
		su.id 
	from
		sys_user su
		left join sys_role_user sru on su.id = sru.user_id
	where
		sru.role_id != 10 
		and su.is_del = 0
	) 
	and so.is_del = 0
	and so.is_available = 1
	and suo.relation_type = 'UserManageArea'</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>user_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>查出全部管理员及其管辖范围</origin>
        <comments>user_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>level_path</name>
        <length>500</length>
        <precision>-1</precision>
        <origin>查出全部管理员及其管辖范围</origin>
        <comments>level_path</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>128</xloc>
      <yloc>3984</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>查出全部管理员及其部门 2</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_user</connection>
    <sql>select
	distinct su.org_id,su.id
from
	sys_user su
	left join sys_role_user sru on su.id = sru.user_id
where
	sru.role_id != 10 
	and su.is_del = 0</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>org_id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>查出全部管理员及其部门</origin>
        <comments>org_id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>id</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>查出全部管理员及其部门</origin>
        <comments>id</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>128</xloc>
      <yloc>4160</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>清空记录表</name>
    <type>ExecSQL</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <execute_each_row>N</execute_each_row>
    <single_statement>N</single_statement>
    <replace_variables>N</replace_variables>
    <quoteString>N</quoteString>
    <sql>truncate table stat_analysis_resource_material_day;
truncate table stat_analysis_resource_material_month;</sql>
    <set_params>N</set_params>
    <insert_field/>
    <update_field/>
    <delete_field/>
    <read_field/>
    <arguments>
    </arguments>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>48</xloc>
      <yloc>48</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 2 2 2 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>上个月所有直播数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>800</xloc>
      <yloc>800</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 2 2 2 2 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>上个月所有直播数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>800</xloc>
      <yloc>1408</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 2 2 2 2 3</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>上个月所有直播数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>816</xloc>
      <yloc>4064</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 2 2 2 2 3 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>上个月所有直播数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>814</xloc>
      <yloc>4711</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 2 2 3</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>本月所有直播数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>800</xloc>
      <yloc>656</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 2 2 3 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>本月所有直播数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>800</xloc>
      <yloc>1264</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 2 2 3 2 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>本月所有直播数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>800</xloc>
      <yloc>1888</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 2 2 3 2 3</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>本月所有直播数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>813</xloc>
      <yloc>2403</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 2 2 3 2 3 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>本月所有直播数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>815</xloc>
      <yloc>2900</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 2 2 3 2 3 2 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>本月所有直播数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>819</xloc>
      <yloc>3408</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 2 2 3 3</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>本月所有直播数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>816</xloc>
      <yloc>3920</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 2 2 3 3 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>本月所有直播数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>814</xloc>
      <yloc>4567</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 2 3</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>昨日所有直播数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>800</xloc>
      <yloc>512</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 2 3 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>昨日所有直播数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>800</xloc>
      <yloc>1120</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 2 3 2 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>昨日所有直播数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>800</xloc>
      <yloc>1728</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 2 3 2 3</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>昨日所有直播数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>813</xloc>
      <yloc>2259</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 2 3 2 3 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>昨日所有直播数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>815</xloc>
      <yloc>2756</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 2 3 2 3 2 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>昨日所有直播数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>819</xloc>
      <yloc>3264</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 2 3 3</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>昨日所有直播数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>816</xloc>
      <yloc>3776</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 2 3 3 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>昨日所有直播数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>814</xloc>
      <yloc>4423</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 3</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>所有知识数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>800</xloc>
      <yloc>368</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 3 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>所有知识数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>800</xloc>
      <yloc>976</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 3 2 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>所有知识数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>800</xloc>
      <yloc>1584</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 3 2 3</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>所有知识数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>813</xloc>
      <yloc>2115</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 3 2 3 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>所有知识数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>815</xloc>
      <yloc>2612</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 3 2 3 2 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>所有知识数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>819</xloc>
      <yloc>3120</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 3 2 3 2 2 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>所有知识数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>816</xloc>
      <yloc>3632</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录关联 (笛卡尔输出) 6 3 2 3 2 2 2 2</name>
    <type>JoinRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <directory>%%java.io.tmpdir%%</directory>
    <prefix>out</prefix>
    <cache_size>500</cache_size>
    <main>所有知识数量</main>
    <compare>
      <condition>
        <negated>N</negated>
        <conditions>
          <condition>
            <negated>N</negated>
            <leftvalue>level_path</leftvalue>
            <function>STARTS WITH</function>
            <rightvalue>user_level_path</rightvalue>
          </condition>
          <condition>
            <negated>N</negated>
            <operator>OR</operator>
            <conditions>
              <condition>
                <negated>N</negated>
                <leftvalue>create_by</leftvalue>
                <function>=</function>
                <rightvalue>user_id</rightvalue>
              </condition>
              <condition>
                <negated>N</negated>
                <operator>AND</operator>
                <leftvalue>org_id</leftvalue>
                <function>=</function>
                <rightvalue>user_org_id</rightvalue>
              </condition>
            </conditions>
          </condition>
        </conditions>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>814</xloc>
      <yloc>4279</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>记录集连接</name>
    <type>MergeJoin</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <join_type>INNER</join_type>
    <step1>排序记录 2</step1>
    <step2>排序记录</step2>
    <keys_1>
      <key>user_id</key>
    </keys_1>
    <keys_2>
      <key>id</key>
    </keys_2>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>368</xloc>
      <yloc>4080</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>近11个月知识库插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource_material_month</table>
      <key>
        <name>month</name>
        <field>month</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>increase</name>
        <field>increase</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>orgId</name>
        <field>org_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>month</name>
        <rename>month</rename>
        <update>N</update>
      </value>
      <value>
        <name>increase</name>
        <rename>increase</rename>
        <update>Y</update>
      </value>
      <value>
        <name>org_id</name>
        <rename>orgId</rename>
        <update>Y</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>512</xloc>
      <yloc>144</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>近30天知识库插入更新</name>
    <type>InsertUpdate</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_statistics</connection>
    <commit>100</commit>
    <update_bypassed>N</update_bypassed>
    <lookup>
      <schema/>
      <table>stat_analysis_resource_material_day</table>
      <key>
        <name>day</name>
        <field>day</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>increase</name>
        <field>increase</field>
        <condition>=</condition>
        <name2/>
      </key>
      <key>
        <name>orgId</name>
        <field>org_id</field>
        <condition>=</condition>
        <name2/>
      </key>
      <value>
        <name>day</name>
        <rename>day</rename>
        <update>N</update>
      </value>
      <value>
        <name>increase</name>
        <rename>increase</rename>
        <update>Y</update>
      </value>
      <value>
        <name>org_id</name>
        <rename>orgId</rename>
        <update>Y</update>
      </value>
    </lookup>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>496</xloc>
      <yloc>48</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>近30天知识库新增</name>
    <type>TableInput</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>learn_lecturer</connection>
    <sql>SELECT
	date_range.date AS day,
	COALESCE ( data_counts.count, 0 ) AS increase,
	IFNULL(data_counts.org_id,' ') AS orgId
FROM
	(
	SELECT
		CURDATE() - INTERVAL ( n - 1 ) DAY AS date 
	FROM
		(
		SELECT
			1 AS n UNION ALL
		SELECT
			2 UNION ALL
		SELECT
			3 UNION ALL
		SELECT
			4 UNION ALL
		SELECT
			5 UNION ALL
		SELECT
			6 UNION ALL
		SELECT
			7 UNION ALL
		SELECT
			8 UNION ALL
		SELECT
			9 UNION ALL
		SELECT
			10 UNION ALL
		SELECT
			11 UNION ALL
		SELECT
			12 UNION ALL
		SELECT
			13 UNION ALL
		SELECT
			14 UNION ALL
		SELECT
			15 UNION ALL
		SELECT
			16 UNION ALL
		SELECT
			17 UNION ALL
		SELECT
			18 UNION ALL
		SELECT
			19 UNION ALL
		SELECT
			20 UNION ALL
		SELECT
			21 UNION ALL
		SELECT
			22 UNION ALL
		SELECT
			23 UNION ALL
		SELECT
			24 UNION ALL
		SELECT
			25 UNION ALL
		SELECT
			26 UNION ALL
		SELECT
			27 UNION ALL
		SELECT
			28 UNION ALL
		SELECT
			29 UNION ALL
		SELECT
			30 UNION ALL
		SELECT
			31 
		) AS date_range 
	) AS date_range
	LEFT JOIN (
	SELECT
		DATE( create_time ) AS date,
		COUNT(*) AS count,
		org_id 
	FROM
		lecturer_material  
	WHERE
		create_time >= CURDATE() - INTERVAL 30 DAY 
	GROUP BY
		DATE( create_time ),org_id 
	) AS data_counts ON date_range.date = data_counts.date 
ORDER BY
	date_range.date,data_counts.org_id;</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>Date</type>
        <storagetype>normal</storagetype>
        <name>day</name>
        <length>-1</length>
        <precision>-1</precision>
        <origin>近30天知识库新增</origin>
        <comments>day</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>Integer</type>
        <storagetype>normal</storagetype>
        <name>increase</name>
        <length>15</length>
        <precision>0</precision>
        <origin>近30天知识库新增</origin>
        <comments>increase</comments>
        <conversion_Mask>####0;-####0</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>orgId</name>
        <length>36</length>
        <precision>-1</precision>
        <origin>近30天知识库新增</origin>
        <comments>orgId</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>zh_CN</date_format_locale>
        <date_format_timezone>Asia/Shanghai</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>304</xloc>
      <yloc>48</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>清空记录表</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>176</xloc>
      <yloc>48</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 2</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>清空记录表</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>176</xloc>
      <yloc>144</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 2 2</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>所有直播插入更新</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1568</xloc>
      <yloc>800</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 2 2 2</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>所有专题插入更新</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1568</xloc>
      <yloc>1408</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 2 2 3</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>所有讲师插入更新</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1584</xloc>
      <yloc>4064</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 2 2 3 2</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>所有证书插入更新</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1582</xloc>
      <yloc>4711</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>所有直播插入更新</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1568</xloc>
      <yloc>656</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 2</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>所有专题插入更新</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1568</xloc>
      <yloc>1264</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 2 2</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>所有考试插入更新</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1568</xloc>
      <yloc>1888</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 2 3</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>所有学习项目插入更新</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1581</xloc>
      <yloc>2403</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 2 3 2</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>所有面授项目插入更新</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1583</xloc>
      <yloc>2900</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 2 3 2 2</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>所有快速培训项目插入更新</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1587</xloc>
      <yloc>3408</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 3</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>所有讲师插入更新</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1584</xloc>
      <yloc>3920</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 3 2 2 2 2 2 2 3 3 2</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>所有证书插入更新</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1582</xloc>
      <yloc>4567</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 3 2 2 2 2 2 3</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>所有直播插入更新</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1552</xloc>
      <yloc>512</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 2</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>所有专题插入更新</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1552</xloc>
      <yloc>1120</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 2 2</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>所有考试插入更新</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1552</xloc>
      <yloc>1728</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 2 3</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>所有学习项目插入更新</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1565</xloc>
      <yloc>2259</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 2 3 2</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>所有面授项目插入更新</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1567</xloc>
      <yloc>2756</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 2 3 2 2</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>所有快速培训项目插入更新</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1571</xloc>
      <yloc>3264</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 3</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>所有讲师插入更新</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1568</xloc>
      <yloc>3776</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>阻塞数据直到步骤都完成 3 2 2 2 2 2 3 3 2</name>
    <type>BlockUntilStepsFinish</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <steps>
      <step>
        <name>所有证书插入更新</name>
        <CopyNr>0</CopyNr>
      </step>
    </steps>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1566</xloc>
      <yloc>4423</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step_error_handling>
  </step_error_handling>
  <slave-step-copy-partition-distribution>
  </slave-step-copy-partition-distribution>
  <slave_transformation>N</slave_transformation>
  <attributes/>
</transformation>
