package com.wunding.learn.business.view.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.business.view.service.model.example.Example;
import java.util.List;

/**
 * <p> 案例表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">aixinrong</a>
 * @since 2022-08-29
 */
public interface BusinessViewExampleService extends IService<Example> {

    /**
     * 获取需要公有读的文件地址
     *
     * @return {@link String}
     */
    List<String> getNeedPublicReadFilePathList();
}
