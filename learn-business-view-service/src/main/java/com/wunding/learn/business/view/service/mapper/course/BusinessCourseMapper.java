package com.wunding.learn.business.view.service.mapper.course;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.business.view.service.admin.dto.CourseLearnStatDTO;
import com.wunding.learn.business.view.service.admin.dto.CourseLearnedDTO;
import com.wunding.learn.business.view.service.admin.dto.CoursewareLearnDTO;
import com.wunding.learn.business.view.service.admin.dto.CoursewarePackageMergeRecordDTO;
import com.wunding.learn.business.view.service.admin.dto.CoursewareStarDTO;
import com.wunding.learn.business.view.service.admin.dto.UserCoursewareLearnDetailDTO;
import com.wunding.learn.business.view.service.admin.query.CourseLearnStatQuery;
import com.wunding.learn.business.view.service.admin.query.DetailLearnedQuery;
import com.wunding.learn.business.view.service.model.StatAnalysisStudentUploadCourseware;
import com.wunding.learn.business.view.service.model.course.Categorys;
import com.wunding.learn.business.view.service.model.course.Course;
import com.wunding.learn.business.view.service.model.course.CourseDTO;
import com.wunding.learn.business.view.service.model.course.Courseware;
import com.wunding.learn.business.view.service.model.course.UserCourseRecordStatVO;
import com.wunding.learn.common.dto.SysTagResourceRelation;
import com.wunding.learn.common.dto.SysTemTagDTO;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import java.util.List;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;


/**
 * 课程mapper
 *
 * <AUTHOR>
 * @date 2022/10/17
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")
})
public interface BusinessCourseMapper extends BaseMapper<Course> {

    /**
     * 按类型获取全部分类
     *
     * @param name 名称
     * @return {@link List}<{@link Categorys}>
     */
    List<Categorys> getAllByType(@Param("type") String name);

    /**
     * 查找所有发布课程
     *
     * @return {@link List}<{@link CourseDTO}>
     */
    List<CourseDTO> findAllPublishCourse();

    /**
     * 查找所有课件
     *
     * @return {@link List}<{@link Courseware}>
     */
    List<Courseware> findAllWare();

    /**
     * 用户课程学习记录统计
     *
     * @return {@link List}<{@link UserCourseRecordStatVO}>
     */
    List<UserCourseRecordStatVO> userCourseLearnRecordStat();

    /**
     * 上次学习课程计数
     *
     * @param queryDate 查询日期
     * @return {@link List}<{@link UserCourseRecordStatVO}>
     */
    List<UserCourseRecordStatVO> lastLearnCourseCount(@Param("date") String queryDate);

    /**
     * 最后完成课程
     *
     * @param queryDate 查询日期
     * @return {@link List}<{@link UserCourseRecordStatVO}>
     */
    List<UserCourseRecordStatVO> lastFinishCourseCount(@Param("date") String queryDate);

    /**
     * 课程学习统计查询
     *
     * @param courseLearnStatQuery 课程学习统计查询
     * @return {@link List}<{@link CourseLearnStatDTO}>
     */
    List<CourseLearnStatDTO> selectCourseLearnStat(@Param("params") CourseLearnStatQuery courseLearnStatQuery);

    /**
     * 学习列表
     *
     * @param detailLearnedQuery 学习查询对象
     * @return {@link List}<{@link CourseLearnedDTO}>
     */
    List<CourseLearnedDTO> detailLearned(@Param("params") DetailLearnedQuery detailLearnedQuery);

    /**
     * 获取当前课程分类的全部下属分类id
     */
    List<String> getCourseCateIds(@Param("courseCateId") String courseCateId);

    /**
     * 查询出全部上传记录
     *
     * @return {@link StatAnalysisStudentUploadCourseware}
     */
    List<StatAnalysisStudentUploadCourseware> getAllUploadCourseware();

    /**
     * 查询出合并记录
     *
     * @param packageIdList 上传课件的id
     * @return {@link CoursewarePackageMergeRecordDTO}
     */
    List<CoursewarePackageMergeRecordDTO> getMergeRecordList(@Param("packageIdList") List<String> packageIdList);

    /**
     * 查询出全部评星
     *
     * @return {@link CoursewareStarDTO}
     */
    List<CoursewareStarDTO> getCoursewareStar();

    /**
     * 查询全部学习记录
     *
     * @return {@link CoursewareLearnDTO}
     */
    List<UserCoursewareLearnDetailDTO> getCoursewareLearn();

    Long getRelateCourseNum(@Param("tagId") String tagId);

    List<SysTemTagDTO> getCourseTagList();

    List<String> getCourseTagUsed();

    List<SysTagResourceRelation> getCourseTagRelation();

    /**
     * 获取需要公有读的文件地址
     *
     * @return {@link String}
     */
    List<String> getNeedPublicReadFilePathList();
}
