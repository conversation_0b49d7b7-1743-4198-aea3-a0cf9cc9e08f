<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wunding.learn.business.view.service.mapper.user.BusinessUserMapper">
    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.business.view.service.mapper.user.BusinessUserMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.business.view.service.model.user.User">
        <!--@Table sys_user-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="employee_no" jdbcType="VARCHAR" property="employeeNo"/>
        <result column="login_name" jdbcType="VARCHAR" property="loginName"/>
        <result column="full_name" jdbcType="VARCHAR" property="fullName"/>
        <result column="nike_name" jdbcType="VARCHAR" property="nikeName"/>
        <result column="password" jdbcType="VARCHAR" property="password"/>
        <result column="org_id" jdbcType="VARCHAR" property="orgId"/>
        <result column="working_area" jdbcType="VARCHAR" property="workingArea"/>
        <result column="first_name" jdbcType="VARCHAR" property="firstName"/>
        <result column="last_name" jdbcType="VARCHAR" property="lastName"/>
        <result column="pinyin" jdbcType="VARCHAR" property="pinyin"/>
        <result column="sex" jdbcType="TINYINT" property="sex"/>
        <result column="work_phone" jdbcType="VARCHAR" property="workPhone"/>
        <result column="telephone" jdbcType="VARCHAR" property="telephone"/>
        <result column="short_mobile" jdbcType="VARCHAR" property="shortMobile"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="is_lock" jdbcType="TINYINT" property="isLock"/>
        <result column="is_available" jdbcType="TINYINT" property="isAvailable"/>
        <result column="is_del" jdbcType="TINYINT" property="isDel"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_super" jdbcType="TINYINT" property="isSuper"/>
        <result column="user_level_id" jdbcType="VARCHAR" property="userLevelId"/>
        <result column="is_expert" jdbcType="TINYINT" property="isExpert"/>
        <result column="live_user_id" jdbcType="DECIMAL" property="liveUserId"/>
        <result column="ls_depart_admin" jdbcType="TINYINT" property="lsDepartAdmin"/>
        <result column="birthday" jdbcType="TIMESTAMP" property="birthday"/>
        <result column="join_date" jdbcType="TIMESTAMP" property="joinDate"/>
    </resultMap>

    <select id="findAll" resultType="com.wunding.learn.user.api.dto.OrgDTO" useCache="false">
        select *
        from sys_org
        where is_del = 0
    </select>

    <select id="findAllLoginUserId" resultType="java.lang.String" useCache="false">
        select distinct user_id
        from sys_login_session
    </select>

    <select id="findFirstLoginUserByDateRange" resultType="java.lang.String" useCache="false">
        select distinct user_id
        from sys_login_session
        where first_login_time between #{startTime} and #{endTime}
    </select>

    <select id="findNewLoginMonthStats"
      resultType="com.wunding.learn.business.view.service.model.StatNewLoginNumMonth" useCache="false">
        select u.org_id, count(l.user_id) newLoginNum
        from sys_login_session l,
             sys_user u
        where l.user_id = u.id
          and u.is_available = 1
          and u.is_del = 0
          and l.first_login_time &gt;= #{startDate}
          and l.first_login_time &lt; #{endDate}
        group by u.org_id
    </select>

    <select id="getOnlineNum" resultType="com.wunding.learn.business.view.service.admin.dto.OnlineNumDTO"
      useCache="false">
        select date_format(a.first_login_time, '%Y-%m-%d') accessTime,
        count(distinct a.user_id) newLoginNum
        from sys_login_session a,
        sys_user b,
        sys_org c
        where a.user_id = b.id
        and b.org_id = c.id
        and b.is_available = 1
        and b.is_del = 0
        <if test="orgIds != null and orgIds.size() > 0">
            and c.id in
            <foreach collection="orgIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
        group by date_format(a.first_login_time, '%Y-%m-%d')
    </select>

    <select id="getChildOrg" resultType="java.lang.String" useCache="false">
        select id
        from sys_org
        where parent_id = #{orgId}
    </select>

    <select id="getTotalDepartUser"
      resultType="com.wunding.learn.business.view.service.admin.dto.DepartUserDTO" useCache="false">
        select
        a.org_id,
        count(a.id) as deptTotalUser
        from sys_user a
        where a.is_available = 1
        and a.is_del = 0
        and a.org_id in
        <foreach collection="orgIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        group by org_id
    </select>

    <select id="getOnlineNumGroupById"
      resultType="com.wunding.learn.business.view.service.admin.dto.OnlineNumDTO" useCache="false">
        select o.id orgId,
        (select count(a.user_id)
        from sys_login_session a,
        sys_user b
        where a.user_id = b.id
        and b.org_id = o.id
        and date_format(a.first_login_time, '%Y-%m-%d') = #{timeStr}
        and b.is_available = 1
        and b.is_del = 0) newLoginNum
        from sys_org o
        where o.id in
        <foreach collection="orgIds" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="getManageOrgName" resultType="java.lang.String" useCache="false">
        select b.org_name
        from sys_org b,
             sys_user_org c
        where c.org_id = b.id
          and c.user_id = #{userId}
          and c.relation_type = 'UserManageArea'
    </select>

    <select id="getChildrenId" resultType="java.lang.String" useCache="false">
        select id
        from sys_org
        where id = #{orgId}
           or parent_id = #{orgId}
    </select>

    <select id="getPostName" resultType="java.lang.String">
        select name
        from new_identity
        where id = #{postId}
    </select>

    <select id="getRoleName" resultType="java.lang.String">
        select role_name
        from sys_role
        where id = #{roleId}
    </select>

    <select id="getRoleCount" resultType="java.lang.Long">
        select count(*)
        from sys_role
        where is_del = 0
    </select>

    <select id="getPostByPostId" resultType="com.wunding.learn.business.view.service.admin.dto.PostDTO">
        select id, name
        from new_identity
        where category_id = 4 and id in
        <foreach collection="postIdList" item="postId" open="(" close=")" separator=",">
            #{postId}
        </foreach>
    </select>

    <select id="getOrgById" resultType="com.wunding.learn.user.api.dto.OrgDTO">
        select id
             , org_code
             , org_name
             , parent_id
             , org_type
             , org_level
             , level_path
             , level_path_name
             , sort_no
             , sys_defined
             , is_available
             , is_del
             , create_by
             , create_time
             , update_by
             , update_time
             , min_number
             , max_number
             , dimension
        from sys_org
        where id = #{orgId}
    </select>

    <select id="getParaGetCode" resultType="com.wunding.learn.business.view.service.model.user.Para">
        select id,
               para_code,
               mod_code,
               para_level,
               para_name,
               para_value,
               para_type,
               para_option,
               data_type,
               display_flag,
               remark,
               version_number,
               is_del,
               create_by,
               create_time,
               update_by,
               update_time
        from sys_para
        where para_code = #{code}
          and is_del = 0
    </select>



    <select id="getUserIdByLevelPaths" resultType="java.lang.String">
        select su.id
        from sys_user su
        inner join sys_org so on su.org_id = so.id
        where 1=1
        and su.is_del = 0
        <if test="levelPaths!=null and levelPaths.size()>0">
            and
            <foreach collection="levelPaths" item="item" open="(" close=")" separator=" or ">
                so.level_path like concat( #{item},'%')
            </foreach>
        </if>
    </select>

    <select id="getExpertCount" resultType="java.lang.Integer" useCache="false">
        select count(e.id)
        from sys_expert e
                 inner join sys_user u on e.user_id = u.id and u.is_del = 0
                 inner join sys_org o on u.org_id = o.id and o.is_del = 0
        where e.is_del = 0
    </select>

    <select id="getNeedPublicReadFilePathList" resultType="java.lang.String" useCache="false">
        SELECT content
        FROM sys_privacy
        WHERE content IS NOT NULL
          AND content != ''
        union all
        SELECT content
        FROM sys_notice
        WHERE content IS NOT NULL
          AND content != ''
    </select>
</mapper>