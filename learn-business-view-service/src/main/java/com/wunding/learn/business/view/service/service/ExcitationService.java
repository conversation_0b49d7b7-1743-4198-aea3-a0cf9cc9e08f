package com.wunding.learn.business.view.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.business.view.service.admin.query.IntegralClearQuery;
import com.wunding.learn.business.view.service.model.excitation.ExchangeRecordDTO;
import com.wunding.learn.business.view.service.model.excitation.ExcitationTradeRecord;
import com.wunding.learn.business.view.service.model.excitation.UserCredit;
import com.wunding.learn.business.view.service.model.excitation.UserIntegral;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 激励数据源
 *
 * <AUTHOR>
 * @date 2022/10/14
 */
public interface ExcitationService extends IService<UserCredit> {

    /**
     * 所有用户积分
     *
     * @return {@link List}<{@link UserCredit}>
     */
    List<UserCredit> allUserCredit();

    /**
     * 所有用户的兑换次数
     *
     * @return {@link List}<{@link ExchangeRecordDTO}>
     */
    List<ExchangeRecordDTO> allUserExchangeCount();

    /**
     * 获取用户现有积分
     *
     * @param userIds
     * @return
     */
    List<UserIntegral> getUserIntegralByUserIds(Collection<String> userIds);

    /**
     * 获取用户积分交易记录
     *
     * @param userIds
     * @param type
     * @param startTime
     * @param endTime
     * @return
     */
    List<ExcitationTradeRecord> getCleanTradeRecord(Collection<String> userIds, String type, Date startTime,
        Date endTime);

    /**
     * 添加积分交易记录
     *
     * @param tradeRecord
     * @return
     */
    void insertExcitationTradeRecord(Collection<ExcitationTradeRecord> tradeRecord);

    /**
     * 批量更新用户积分
     *
     * @param userIntegrals
     * @return
     */
    void updateUserIntegralBatchById(Collection<UserIntegral> userIntegrals);

    /**
     * 更新用户积分
     *
     * @param userIntegral
     * @return
     */
    void updateUserIntegralById(UserIntegral userIntegral);

    /**
     * 更新积分清除状态
     *
     * @param cleanTradeRecord
     * @return
     */
    void updateCleanStatus(List<ExcitationTradeRecord> cleanTradeRecord);

    /**
     * 积分清零
     *
     * @param query
     */
    void cleanIntegral(IntegralClearQuery query);

    /**
     * 获取需要公有读的文件地址
     *
     * @return {@link String}
     */
    List<String> getNeedPublicReadFilePathList();
}
