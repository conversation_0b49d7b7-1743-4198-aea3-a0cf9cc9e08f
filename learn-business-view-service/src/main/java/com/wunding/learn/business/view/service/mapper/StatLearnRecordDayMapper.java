package com.wunding.learn.business.view.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.business.view.service.admin.dto.LearnRecordDTO;
import com.wunding.learn.business.view.service.admin.query.LearnRecordQuery;
import com.wunding.learn.business.view.service.model.StatLearnRecordDay;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import com.wunding.learn.user.api.dto.SubordinateFileDTO;
import java.util.Collection;
import java.util.List;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 学员档案日表 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">chh</a>
 * @since 2022-10-08
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")
})
public interface StatLearnRecordDayMapper extends BaseMapper<StatLearnRecordDay> {

    /**
     * 按天查学员档案
     *
     * @param learnRecordQuery
     * @return
     */
    List<LearnRecordDTO> statList(@Param("params") LearnRecordQuery learnRecordQuery);

    /**
     * 按天获取下属档案数据
     *
     * @param userIds
     * @param startTime
     * @param endTime
     * @return
     */
    List<SubordinateFileDTO> getSubordinateFileByDay(@Param("userIds") Collection<String> userIds,
        @Param("startTime") String startTime, @Param("endTime") String endTime);
}
