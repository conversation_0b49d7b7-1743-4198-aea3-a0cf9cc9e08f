package com.wunding.learn.business.view.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;


/**
 * 课程情况统计明细
 *
 * <AUTHOR>
 * @date 2022/10/27
 */
@Data
@Accessors(chain = true)
@Schema(name = "CourseLearnedDTO", description = "课程已学未学dto对象")
public class CourseLearnedDTO {

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private String userId;

    /**
     * 姓名
     */
    @Schema(description = "姓名")
    private String fullName;

    /**
     * 部门名称
     */
    @Schema(description = "部门名称")
    private String orgName;

    /**
     * 开始学习时间
     */
    @Schema(description = "开始学习时间")
    private Date minViewTime;

    /**
     * 最后学习时间
     */
    @Schema(description = "最后学习时间")
    private Date maxViewTime;

    /**
     * 有效学习时长（秒）
     */
    @Schema(description = "有效学习时长（秒）")
    private Integer totalDuration;

    @Schema(description = "部门全称 orgPath")
    private String orgPath;

}
