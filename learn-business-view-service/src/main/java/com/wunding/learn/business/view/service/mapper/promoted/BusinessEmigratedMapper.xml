<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wunding.learn.business.view.service.mapper.promoted.BusinessEmigratedMapper">


    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.business.view.service.mapper.promoted.BusinessEmigratedMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.business.view.service.model.promoted.Emigrated">
        <!--@Table emigrated-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="name" jdbcType="VARCHAR"
          property="name"/>
        <result column="code" jdbcType="VARCHAR"
          property="code"/>
        <result column="type" jdbcType="VARCHAR"
          property="type"/>
        <result column="begin_time" jdbcType="TIMESTAMP"
          property="beginTime"/>
        <result column="end_time" jdbcType="TIMESTAMP"
          property="endTime"/>
        <result column="intro" jdbcType="VARCHAR"
          property="intro"/>
        <result column="description" jdbcType="VARCHAR"
          property="description"/>
        <result column="remark" jdbcType="VARCHAR"
          property="remark"/>
        <result column="class_teacher_id" jdbcType="VARCHAR"
          property="classTeacherId"/>
        <result column="checkpoint_settings" jdbcType="VARCHAR"
          property="checkpointSettings"/>
        <result column="rewards" jdbcType="VARCHAR"
          property="rewards"/>
        <result column="category" jdbcType="TINYINT"
          property="category"/>
        <result column="styles_template" jdbcType="INTEGER"
          property="stylesTemplate"/>
        <result column="is_publish" jdbcType="TINYINT"
          property="isPublish"/>
        <result column="publish_time" jdbcType="TIMESTAMP"
          property="publishTime"/>
        <result column="publish_by" jdbcType="VARCHAR"
          property="publishBy"/>
        <result column="create_by" jdbcType="VARCHAR"
          property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP"
          property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR"
          property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP"
          property="updateTime"/>
        <result column="is_del" jdbcType="TINYINT"
          property="isDel"/>
    </resultMap>

    <select id="allUserCountStat"
            resultType="com.wunding.learn.business.view.service.model.promoted.EmigratedDTO" useCache="false">
        select p.create_by,
               p1.userCount
        from emigrated p,
             (
                 select pp.emigrated_id,
                        count(pp.user_id) userCount
                 from (
                          select emigrated_id,
                                 user_id
                          from emigrated_task_record
                          group by emigrated_id,
                                   user_id) pp
                 group by pp.emigrated_id) p1
        where p.id = p1.emigrated_id
          and p.is_del = 0
    </select>

    <select id="getInProgressPromotedGameCount" resultType="java.lang.Integer" useCache="false">
        select count(e.id)
        from emigrated e
                 inner join sys_org so on so.id = e.org_id
        where e.is_del = 0
          and e.is_publish = 1
          and NOW() BETWEEN e.begin_time AND e.end_time
        <if test="managerAreaOrgIds != null and managerAreaOrgIds.size() > 0">
            <foreach collection="managerAreaOrgIds" item="item" open="and (" separator="or">
                so.level_path like concat(#{item}, '%')
            </foreach>
            or e.create_by = #{currentUserId}
                )
        </if>
    </select>

    <select id="getNeedPublicReadFilePathList" resultType="java.lang.String" useCache="false">
        SELECT intro
        FROM emigrated
        WHERE intro IS NOT NULL
          AND intro != ''
    </select>
</mapper>