package com.wunding.learn.business.view.service.admin.rest;

import com.github.pagehelper.PageInfo;
import com.wunding.learn.business.view.service.admin.dto.ItemVisitRecordStatDTO;
import com.wunding.learn.business.view.service.admin.query.ItemVisitRecordQuery;
import com.wunding.learn.business.view.service.service.IStatAnalysisItemVisitRecordMonthService;
import com.wunding.learn.common.bean.Result;
import com.wunding.learn.file.api.dto.ExportResultDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 栏位统计
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">wgr</a>
 * @date 2023/7/17 16:47
 */
@RestController
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RequestMapping("${module.businessView.contentPath:/}itemVisitRecordStat")
@Tag(description = "栏目访问量统计", name = "ItemVisitRecordStatRest")
public class ItemVisitRecordStatRest {

    private final IStatAnalysisItemVisitRecordMonthService statAnalysisItemVisitRecordMonthService;


    @GetMapping("/findItemVisitRecordStatByPage")
    @Operation(operationId = "findItemVisitRecordStatByPage", summary = "第三方链接访问统计", description = "第三方链接访问统计")
    public Result<PageInfo<ItemVisitRecordStatDTO>> list(@ParameterObject @Valid ItemVisitRecordQuery itemVisitRecordQuery) {
        PageInfo<ItemVisitRecordStatDTO> data = statAnalysisItemVisitRecordMonthService.findItemVisitRecordStatByPage(itemVisitRecordQuery);
        return Result.success(data);
    }

    @PostMapping("/exportItemVisitRecordStatData")
    @Operation(operationId = "exportItemVisitRecordStatData", summary = "导出第三方链接访问统计", description = "导出第三方链接访问统计")
    public Result<ExportResultDTO> exportData(@ParameterObject @Valid ItemVisitRecordQuery itemVisitRecordQuery) {
        statAnalysisItemVisitRecordMonthService.export(itemVisitRecordQuery);
        return Result.success();
    }

}
