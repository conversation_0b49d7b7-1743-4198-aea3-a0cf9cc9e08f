package com.wunding.learn.business.view.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.business.view.service.model.special.Special;
import com.wunding.learn.business.view.service.model.special.SpecialDTO;
import com.wunding.learn.common.dto.SysTagResourceRelation;
import com.wunding.learn.common.dto.SysTemTagDTO;
import java.util.List;

/**
 * 专题数据源
 *
 * <AUTHOR>
 * @date 2022/10/18
 */
public interface BusinessViewSpecialService extends IService<Special> {

    /**
     * 找到所有专题
     *
     * @return {@link List}<{@link SpecialDTO}>
     */
    List<SpecialDTO> findAllSpecial();

    Long getRelateSpecialNum(String tagId);

    /**
     * 旧数据处理的方法（不要调用）
     *
     * @return
     */
    List<SysTemTagDTO> getSpecialTagList();

    List<SysTagResourceRelation> getSpecialTagRelation();

    /**
     * 获取需要公有读的文件地址
     *
     * @return {@link String}
     */
    List<String> getNeedPublicReadFilePathList();
}
