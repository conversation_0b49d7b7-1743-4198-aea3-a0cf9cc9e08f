<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.business.view.service.mapper.train.BusinessLearnMapMapper">
    <!-- 开启二级缓存 -->
    <!--
<cache type="com.wunding.learn.common.mybatis.cache.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.business.view.service.mapper.train.BusinessLearnMapMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.business.view.service.model.train.LearnMap">
        <!--@Table train_learn_map-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="map_name" jdbcType="VARCHAR"
          property="mapName"/>
        <result column="map_no" jdbcType="VARCHAR"
          property="mapNo"/>
        <result column="map_type" jdbcType="TINYINT"
          property="mapType"/>
        <result column="map_mode" jdbcType="TINYINT"
          property="mapMode"/>
        <result column="is_trans" jdbcType="TINYINT"
          property="isTrans"/>
        <result column="source_identity_id" jdbcType="VARCHAR"
          property="sourceIdentityId"/>
        <result column="target_identity_id" jdbcType="VARCHAR"
          property="targetIdentityId"/>
        <result column="org_id" jdbcType="VARCHAR"
          property="orgId"/>
        <result column="remark" jdbcType="VARCHAR"
          property="remark"/>
        <result column="total_courseware_duration" jdbcType="BIGINT"
          property="totalCoursewareDuration"/>
        <result column="is_del" jdbcType="TINYINT"
          property="isDel"/>
        <result column="is_available" jdbcType="TINYINT"
          property="isAvailable"/>
        <result column="is_publish" jdbcType="TINYINT"
          property="isPublish"/>
        <result column="publish_by" jdbcType="VARCHAR"
          property="publishBy"/>
        <result column="publish_time" jdbcType="TIMESTAMP"
          property="publishTime"/>
        <result column="view_type" jdbcType="TINYINT"
          property="viewType"/>
        <result column="create_by" jdbcType="VARCHAR"
          property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP"
          property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR"
          property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP"
          property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        map_name,
        map_no,
        map_type,
        map_mode,
        is_trans,
        source_identity_id,
        target_identity_id,
        org_id,
        remark,
        total_courseware_duration,
        is_del,
        is_available,
        is_publish,
        publish_by,
        publish_time,
        view_type,
        create_by,
        create_time,
        update_by,
        update_time
    </sql>

    <select id="getPostDevelop" resultType="com.wunding.learn.business.view.service.admin.dto.PostDevelopDTO"
      useCache="false">
        select distinct source_identity_id sourcePositionId,
                        target_identity_id targetPositionId
        from train_learn_map
        where map_type = 1
          and is_del = 0
          and is_publish = 1
    </select>

    <select id="getInProgressLearnMapCount" resultType="java.lang.Integer" useCache="false">
        select count(lm.id)
        from train_learn_map lm
                 inner join sys_org o on lm.org_id = o.id
        <where>
            lm.is_del = 0
              and lm.is_publish = 1
            <if test="managerAreaOrgIds != null and managerAreaOrgIds.size() > 0">
                <foreach collection="managerAreaOrgIds" item="item" open="and (" separator="or">
                    o.level_path like concat(#{item}, '%')
                </foreach>
                or lm.create_by = #{userId})
            </if>
        </where>
    </select>

    <select id="getTrainList" resultType="com.wunding.learn.business.view.service.model.train.Train" useCache="false">
        select *
        from train
        where id in (
        <foreach collection="trainIdSet" separator="," item="trainId">
            #{trainId}
        </foreach>
        )
    </select>

    <select id="getNeedPublicReadFilePathList" resultType="java.lang.String" useCache="false">
        SELECT info_content
        FROM train_info
        WHERE info_content IS NOT NULL
          AND info_content != ''
    </select>
</mapper>
