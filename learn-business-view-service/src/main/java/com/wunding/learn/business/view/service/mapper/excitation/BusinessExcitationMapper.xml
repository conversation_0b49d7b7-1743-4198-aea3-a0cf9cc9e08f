<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wunding.learn.business.view.service.mapper.excitation.BusinessExcitationMapper">

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.business.view.service.mapper.excitation.BusinessExcitationMapper"/>

    <select id="allUserCredit"
            resultType="com.wunding.learn.business.view.service.model.excitation.UserCredit" useCache="false">
        select user_id, num, remain_num, origin_num
        from user_credit
    </select>

    <select id="allUserExchangeCount"
            resultType="com.wunding.learn.business.view.service.model.excitation.ExchangeRecordDTO" useCache="false">
        select user_id, count(id) coinNum
        from exchange_record
        group by user_id
    </select>

    <select id="getUserIntegralByUserIds"
            resultType="com.wunding.learn.business.view.service.model.excitation.UserIntegral" useCache="false">
        with temp as (
        <foreach collection="userIds" item="item" separator="union">
            select #{item} as user_id
        </foreach>
        )
        select user_id,num,available_num,origin_num,convertible_num
        from user_integral ui
        where 1=1
        and exists(select 1 from temp where ui.user_id = temp.user_id)
    </select>

    <select id="getCleanTradeRecord"
            resultType="com.wunding.learn.business.view.service.model.excitation.ExcitationTradeRecord">
        with temp as (
        <foreach collection="userIds" item="item" separator="union">
            select #{item} as user_id
        </foreach>
        )
        select id,user_id,excitation_id,operate_type,operate_num,current_num,event_id,create_by,create_time,is_exchange
        from excitation_trade_record etr
        where 1=1
        and clean_status !='1'
        and clean_status !='2'
        and exists(select 1 from temp where etr.user_id = temp.user_id)
        <choose>
            <when test="type=='dept'">
                and excitation_id = 'integral'
            </when>
            <!--讲师类型时只清除讲师积分-->
            <when test="type=='lecture'">
                and excitation_id = 'integral'
                and (event_id in ('lecturerJoinProjectTeach', 'voteLecturer') or (event_id in('manualImportIntegral') and biz_type='lecturer'))
            </when>
        </choose>
        <if test="startTime !=null and endTime!= null">
            and date_format(create_time,'%Y-%m') >= date_format(#{startTime},'%Y-%m')
            and date_format(#{endTime},'%Y-%m') >= date_format(create_time,'%Y-%m')
        </if>
    </select>

    <insert id="insertExcitationTradeRecord">
        insert into excitation_trade_record(id,user_id,excitation_id,operate_type,operate_num,current_num,trade_type,target_id,target_name,event_id,summary,
                                            create_by,create_time,refund_record_id,biz_id,biz_type,is_exchange,clean_status)
        values
               <foreach collection="tradeRecord" item="item" separator=" , ">
                   (#{item.id},#{item.userId},#{item.excitationId},#{item.operateType},#{item.operateNum},#{item.currentNum},#{item.tradeType},#{item.targetId},#{item.targetName},#{item.eventId},#{item.summary},
                   #{item.createBy},#{item.createTime},#{item.refundRecordId},#{item.bizId},#{item.bizType},#{item.isExchange},#{item.cleanStatus})
               </foreach>
    </insert>

    <update id="updateUserIntegralById">
        update user_integral
        set num = #{num},
            available_num = #{availableNum},
            convertible_num = #{convertibleNum}
        where user_id = #{userId}
    </update>

    <update id="updateCleanStatus">
        update excitation_trade_record
        set clean_status = '1'
        where id = #{id}
    </update>

    <select id="getNeedPublicReadFilePathList" resultType="java.lang.String" useCache="false">
        SELECT description
        FROM award
        WHERE description IS NOT NULL
          AND description != ''
    </select>
</mapper>