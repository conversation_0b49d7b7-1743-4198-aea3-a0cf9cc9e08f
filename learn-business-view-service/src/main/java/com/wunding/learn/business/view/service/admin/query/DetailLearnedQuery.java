package com.wunding.learn.business.view.service.admin.query;

import com.wunding.learn.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.Parameter;
import java.util.Collection;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

/**
 * 详细学习查询对象
 *
 * <AUTHOR>
 * @date 2022/10/27
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class DetailLearnedQuery extends BaseEntity {

    @Parameter(description = "课程id")
    @NotBlank
    @Length(max = 36, message = "课程id不能超过36")
    private String courseId;

    @Parameter(description = "查询条件 是否已学 0 未学 1 已学")
    @NotNull
    @Range(min = 0, max = 1, message = "条件非定义数值范围")
    private Integer isLearned;

    @Parameter(description = "userOrLoginNameLike")
    private String userOrLoginNameLike;

    @Parameter(description = "按用户id查询", hidden = true)
    private Collection<String> userIds;
}
