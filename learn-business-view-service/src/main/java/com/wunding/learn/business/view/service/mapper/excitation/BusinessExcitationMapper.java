package com.wunding.learn.business.view.service.mapper.excitation;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.business.view.service.model.excitation.ExchangeRecordDTO;
import com.wunding.learn.business.view.service.model.excitation.ExcitationTradeRecord;
import com.wunding.learn.business.view.service.model.excitation.UserCredit;
import com.wunding.learn.business.view.service.model.excitation.UserIntegral;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Property;


/**
 * 激励mapper接口
 *
 * <AUTHOR>
 * @date 2022/10/14
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")})
public interface BusinessExcitationMapper extends BaseMapper<UserCredit> {

    /**
     * 所有用户积分
     *
     * @return {@link List}<{@link UserCredit}>
     */
    List<UserCredit> allUserCredit();


    /**
     * 所有用户的兑换次数
     *
     * @return {@link List}<{@link ExchangeRecordDTO}>
     */
    List<ExchangeRecordDTO> allUserExchangeCount();

    /**
     * 获取用户现有积分
     *
     * @param userIds
     * @return
     */
    List<UserIntegral> getUserIntegralByUserIds(Collection<String> userIds);

    /**
     * 获取用户积分交易记录
     *
     * @param userIds
     * @param type
     * @param startTime
     * @param endTime
     * @return
     */
    List<ExcitationTradeRecord> getCleanTradeRecord(Collection<String> userIds, String type, Date startTime,
        Date endTime);


    /**
     * 添加积分交易记录
     *
     * @param tradeRecord
     * @return
     */
    void insertExcitationTradeRecord(Collection<ExcitationTradeRecord> tradeRecord);

    /**
     * 批量更新用户积分
     *
     * @param userIntegral
     * @return
     */
    void updateUserIntegralById(UserIntegral userIntegral);

    /**
     * 更新积分清除状态
     *
     * @param info
     * @return
     */
    void updateCleanStatus(ExcitationTradeRecord info);

    /**
     * 获取需要公有读的文件地址
     *
     * @return {@link String}
     */
    List<String> getNeedPublicReadFilePathList();
}
