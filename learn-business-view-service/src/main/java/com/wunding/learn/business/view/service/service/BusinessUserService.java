package com.wunding.learn.business.view.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.business.view.service.admin.dto.PostDTO;
import com.wunding.learn.business.view.service.model.StatNewLoginNumMonth;
import com.wunding.learn.business.view.service.model.user.Para;
import com.wunding.learn.business.view.service.model.user.User;
import com.wunding.learn.user.api.dto.OrgDTO;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 用户数据源
 *
 * <AUTHOR>
 * @date 2022/10/14
 */
public interface BusinessUserService extends IService<User> {

    /**
     * 查找所有组织
     *
     * @return {@link List}<{@link OrgDTO}>
     */
    List<OrgDTO> findAll();


    /**
     * 查找所有登录用户id
     *
     * @return {@link List}<{@link String}>
     */
    List<String> findAllLoginUserId();

    /**
     * 按日期范围查找第一个登录用户
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return {@link List}<{@link String}>
     */
    List<String> findFirstLoginUserByDateRange(String startTime, String endTime);

    /**
     * 获取月度新上线用户统计数据
     *
     * @param startDate 统计开始时间
     * @param endDate   统计结束时间
     * @return 月度新上线用户统计数据
     */
    List<StatNewLoginNumMonth> findNewLoginMonthStats(Date startDate, Date endDate);

    /**
     * 获取在线用户与时间映射
     *
     * @param orgIds 组织ID
     * @return {@link Map}<{@link String}, {@link Integer}>
     */
    Map<String, Integer> getNewLoginNum(Collection<String> orgIds);

    /**
     * 获取子组织
     *
     * @param orgId 组织id
     * @return {@link Set}<{@link String}>
     */
    Set<String> getChildOrg(String orgId);

    /**
     * 获取用户数量
     *
     * @param orgIds 组织ID
     * @return {@link Map}<{@link String}, {@link Integer}>
     */
    Map<String, Integer> getTotalDepartUser(Set<String> orgIds);

    /**
     * 按orgId进行映射获取新登录数量
     *
     * @param orgIds 组织ID
     * @return {@link Map}<{@link String}, {@link Integer}>
     */
    Map<String, Integer> getNewLoginNumGroupById(Collection<String> orgIds, String timeStr);

    /**
     * 获取管辖范围组织名称
     *
     * @param userId 用户id
     * @return {@link List}<{@link String}>
     */
    List<String> getManageOrgName(String userId);

    /**
     * 获取子组织
     *
     * @param orgId 组织id
     * @return {@link Set}<{@link String}>
     */
    Set<String> getChildrenId(String orgId);

    /**
     * 获取岗位名
     *
     * @param postId
     * @return
     */
    String getPostName(String postId);

    /**
     * 获取角色名
     *
     * @param roleId
     * @return
     */
    String getRoleName(String roleId);

    /**
     * 获取角色总数
     *
     * @return
     */
    Long getRoleCount();

    /**
     * 批量获取岗位
     *
     * @param postIdList
     * @return
     */
    List<PostDTO> getPostByPostId(Collection<String> postIdList);

    /**
     * 根据id获取部门
     *
     * @param orgId
     * @return
     */
    OrgDTO getOrgById(String orgId);

    /**
     * 获取系统参数
     *
     * @param code
     * @return
     */
    Para getParaGetCode(String code);

    /**
     * 根据组织路径去获取用户id
     *
     * @param levelPaths
     * @return
     */
    List<String> getUserIdByLevelPaths(Collection<String> levelPaths);

    /**
     * 查询评委数量
     *
     * @return 数量
     */
    Integer getExpertCount();

    /**
     * 获取需要公有读的文件地址
     *
     * @return {@link String}
     */
    List<String> getNeedPublicReadFilePathList();
}
