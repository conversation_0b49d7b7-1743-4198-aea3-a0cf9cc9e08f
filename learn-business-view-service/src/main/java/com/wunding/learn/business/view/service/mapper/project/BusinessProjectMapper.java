package com.wunding.learn.business.view.service.mapper.project;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.business.view.service.admin.dto.PostDevelopDTO;
import com.wunding.learn.business.view.service.client.dto.LecturerDTO;
import com.wunding.learn.business.view.service.model.ProjectJoiningStaticsDTO;
import com.wunding.learn.business.view.service.model.project.OrgUserNumDTO;
import com.wunding.learn.business.view.service.model.project.Project;
import com.wunding.learn.business.view.service.model.project.ProjectDTO;
import com.wunding.learn.business.view.service.model.project.ProjectJoinUserDTO;
import com.wunding.learn.business.view.service.model.project.TrainPlanMain;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;


/**
 * 业务项目映射器
 *
 * <AUTHOR>
 * @date 2022/10/13
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")
})
public interface BusinessProjectMapper extends BaseMapper<Project> {

    /**
     * 查找所有发布项目
     *
     * @return {@link List}<{@link ProjectDTO}>
     */
    List<ProjectDTO> findAllProject();

    /**
     * 查找所有培训计划
     *
     * @return {@link List}<{@link TrainPlanMain}>
     */
    List<TrainPlanMain> findAllTrainPlan();

    /**
     * 讲师排名
     *
     * @return {@link List}<{@link LecturerDTO}>
     */
    List<LecturerDTO> dashboardLecturerRanking();

    /**
     * 岗位发展信息
     *
     * @return
     */
    List<PostDevelopDTO> getPostDevelop();

    /**
     * 取指定月份学习项目参与用户信息
     *
     * @param date 要统计月度的日期 如 2024-03-21
     * @return
     */
    List<ProjectJoinUserDTO> findThisMonthProjectJoinInfo(Date date);

    /**
     * 取指定年份学习项目参与用户信息
     *
     * @param date 要统计年度的日期 如 2024-03-21
     * @return
     */
    List<ProjectJoinUserDTO> findThisYearProjectJoinInfo(Date date);

    /**
     * 取组织用户数量
     *
     * @param deadlineTime 统计截止时间
     * @return
     */
    List<OrgUserNumDTO> getOrgUserNumList(Date deadlineTime);

    /**
     * 批量保存学习项目参加数据
     *
     * @param list 数据列表
     */
    void saveProjectJoiningStat(List<ProjectJoiningStaticsDTO> list);

    /**
     * 删除学习项目参与统计数据
     *
     * @param dates 统计日期列表
     * @param type  统计类型
     */
    void removeByDate(@Param("dates") Collection<Date> dates, @Param("type") Integer type);

    /**
     * 进行中的项目
     *
     * @param userId              用户id
     * @param userManageAreaOrgId 管辖范围
     * @param projectType         项目类型
     * @return 项目数
     */
    Integer getInProgressProjectCount(@Param("userId") String userId,
        @Param("userManageAreaOrgId") Set<String> userManageAreaOrgId,
        @Param("projectType") Integer projectType);

    /**
     * 查询班级
     *
     * @return {@link Project}
     */
    List<Project> getTrainProject();

    /**
     * 修改引用资源名称
     *
     * @param projectList {@link Project}
     */
    void updateReferencedName(@Param("projectList") List<Project> projectList);

    /**
     * 获取需要公有读的文件地址
     *
     * @return {@link String}
     */
    List<String> getNeedPublicReadFilePathList();
}
