<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.business.view.service.mapper.example.BusinessExampleMapper">
    <!-- 开启二级缓存 -->
    <!--
<cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.business.view.service.mapper.example.BusinessExampleMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.business.view.service.model.example.Example">
        <!--@Table example-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="example_code" jdbcType="VARCHAR"
          property="exampleCode"/>
        <result column="example_name" jdbcType="VARCHAR"
          property="exampleName"/>
        <result column="example_cate_id" jdbcType="VARCHAR"
          property="exampleCateId"/>
        <result column="file_type" jdbcType="VARCHAR"
          property="fileType"/>
        <result column="mine" jdbcType="VARCHAR"
          property="mine"/>
        <result column="author_id" jdbcType="VARCHAR"
          property="authorId"/>
        <result column="author_name" jdbcType="VARCHAR"
          property="authorName"/>
        <result column="author_login_name" jdbcType="VARCHAR"
          property="authorLoginName"/>
        <result column="org_id" jdbcType="VARCHAR"
          property="orgId"/>
        <result column="level_path" jdbcType="VARCHAR"
          property="levelPath"/>
        <result column="play_time" jdbcType="DECIMAL"
          property="playTime"/>
        <result column="publish_time" jdbcType="TIMESTAMP"
          property="publishTime"/>
        <result column="status" jdbcType="DECIMAL"
          property="status"/>
        <result column="synopsis" jdbcType="VARCHAR"
          property="synopsis"/>
        <result column="business_id" jdbcType="VARCHAR"
          property="businessId"/>
        <result column="recommend" jdbcType="DECIMAL"
          property="recommend"/>
        <result column="auto_up_level" jdbcType="DECIMAL"
          property="autoUpLevel"/>
        <result column="is_lib" jdbcType="DECIMAL"
          property="isLib"/>
        <result column="play_time_str" jdbcType="VARCHAR"
          property="playTimeStr"/>
        <result column="file_name" jdbcType="VARCHAR"
          property="fileName"/>
        <result column="is_available" jdbcType="DECIMAL"
          property="isAvailable"/>
        <result column="is_del" jdbcType="DECIMAL"
          property="isDel"/>
        <result column="create_by" jdbcType="VARCHAR"
          property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP"
          property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR"
          property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP"
          property="updateTime"/>
        <result column="learn_num" jdbcType="BIGINT"
          property="learnNum"/>
        <result column="like_num" jdbcType="BIGINT"
          property="likeNum"/>
        <result column="comment_num" jdbcType="BIGINT"
          property="commentNum"/>
        <result column="common_star" jdbcType="DECIMAL"
          property="commonStar"/>
        <result column="star_num" jdbcType="BIGINT"
          property="starNum"/>
        <result column="audit_score" jdbcType="DECIMAL"
          property="auditScore"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
          ,
        example_code,
        example_name,
        example_cate_id,
        filetype,
        mine,
        author_id,
        play_time,
        publish_time,
        status,
        synopsis,
        business_id,
        recommend,
        auto_up_level,
        is_lib,
        play_time_str,
        file_name,
        is_available,
        is_del,
        create_by,
        create_time,
        update_by,
        update_time,
        learn_num,
        like_num,
        comment_num,
        common_star,
        star_num,
        audit_score
    </sql>

    <select id="getNeedPublicReadFilePathList" resultType="java.lang.String" useCache="false">
        SELECT square_content FROM example_config WHERE square_content IS NOT NULL AND square_content != ''
    </select>
</mapper>
