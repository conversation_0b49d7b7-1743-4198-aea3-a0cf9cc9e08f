package com.wunding.learn.business.view.service.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("stat_analysis_learn_record_train_detail")
@Schema(name = "StatAnalysisLearnRecordTrainDetail对象", description = "")
public class StatAnalysisLearnRecordTrainDetail implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 自增主键
     */
    @Schema(description = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /**
     * 用户id
     */
    @Schema(description = "用户id")
    @TableField("user_id")
    private String userId;


    /**
     * 培训项目id
     */
    @Schema(description = "培训项目id")
    @TableField("train_id")
    private String trainId;


    /**
     * 培训项目编号
     */
    @Schema(description = "培训项目编号")
    @TableField("train_no")
    private String trainNo;


    /**
     * 培训项目名称
     */
    @Schema(description = "培训项目名称")
    @TableField("train_name")
    private String trainName;


    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    @TableField("start_time")
    private Date startTime;


    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    @TableField("end_time")
    private Date endTime;


    /**
     * 用于做搜索条件时间
     */
    @Schema(description = "用于做搜索条件时间")
    @TableField("search_time")
    private Date searchTime;


}
