package com.wunding.learn.business.view.service.mapper.market;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.business.view.service.model.market.Vote;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import java.util.Set;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 投票表 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">axr</a>
 * @since 2022-11-02
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")
})
public interface BusinessVoteMapper extends BaseMapper<Vote> {

    /**
     * 获取进行中的投票
     *
     * @param currentUserId     用户id
     * @param managerAreaOrgIds 管辖范围
     * @return 数量
     */
    Integer getInProgressVote(@Param("currentUserId") String currentUserId,
        @Param("managerAreaOrgIds") Set<String> managerAreaOrgIds);
}
