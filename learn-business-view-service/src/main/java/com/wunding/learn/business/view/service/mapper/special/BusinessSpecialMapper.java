package com.wunding.learn.business.view.service.mapper.special;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.business.view.service.model.special.Special;
import com.wunding.learn.business.view.service.model.special.SpecialDTO;
import com.wunding.learn.common.dto.SysTagResourceRelation;
import com.wunding.learn.common.dto.SysTemTagDTO;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import java.util.List;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 专题主表 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">liuxiuyong</a>
 * @since 2022-08-09
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")
})
public interface BusinessSpecialMapper extends BaseMapper<Special> {

    /**
     * 查询全部专题
     *
     * @return {@link List}<{@link SpecialDTO}>
     */
    List<SpecialDTO> findAllSpecial();

    Long getRelateSpecialNum(@Param("tagId") String tagId);

    List<SysTemTagDTO> getSpecialTagList();

    List<SysTagResourceRelation> getSpecialTagRelation();

    /**
     * 获取需要公有读的文件地址
     *
     * @return {@link String}
     */
    List<String> getNeedPublicReadFilePathList();
}
