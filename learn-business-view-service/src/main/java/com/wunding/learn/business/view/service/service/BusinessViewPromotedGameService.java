package com.wunding.learn.business.view.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.business.view.service.model.promoted.Emigrated;
import com.wunding.learn.business.view.service.model.promoted.EmigratedDTO;
import java.util.List;
import java.util.Set;

/**
 * 闯关服务
 *
 * <AUTHOR>
 * @date 2022/10/14
 */
public interface BusinessViewPromotedGameService extends IService<Emigrated> {

    /**
     * 所有用户计数统计
     *
     * @return {@link List}<{@link EmigratedDTO}>
     */
    List<EmigratedDTO> allUserCountStat();

    /**
     * 进行中的闯关
     *
     * @param currentUserId     用户id
     * @param managerAreaOrgIds 管辖范围
     * @return 数量
     */
    Integer getInProgressPromotedGameCount(String currentUserId, Set<String> managerAreaOrgIds);

    /**
     * 获取需要公有读的文件地址
     *
     * @return {@link String}
     */
    List<String> getNeedPublicReadFilePathList();
}
