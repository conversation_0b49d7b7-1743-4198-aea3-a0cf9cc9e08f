package com.wunding.learn.business.view.service.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.wunding.learn.business.view.service.admin.query.IntegralClearQuery;
import com.wunding.learn.business.view.service.aspect.DS;
import com.wunding.learn.business.view.service.constant.IntegralStatTypeEnum;
import com.wunding.learn.business.view.service.mapper.excitation.BusinessExcitationMapper;
import com.wunding.learn.business.view.service.model.excitation.ExchangeRecordDTO;
import com.wunding.learn.business.view.service.model.excitation.ExcitationTradeRecord;
import com.wunding.learn.business.view.service.model.excitation.UserCredit;
import com.wunding.learn.business.view.service.model.excitation.UserIntegral;
import com.wunding.learn.business.view.service.service.BusinessLecturerService;
import com.wunding.learn.business.view.service.service.BusinessUserService;
import com.wunding.learn.business.view.service.service.ExcitationService;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.excitation.ExcitationTypeEnum;
import com.wunding.learn.common.enums.excitation.OtherEventCategoryEnum;
import com.wunding.learn.common.enums.other.ExcitationOperationEnum;
import com.wunding.learn.common.util.date.DateUtil;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.common.util.string.TranslateUtil;
import com.wunding.learn.user.api.service.OrgFeign;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;


/**
 * 励磁服务实施
 *
 * <AUTHOR>
 * @date 2022/10/14
 */
@Service
@DS("excitation")
@Slf4j
public class ExcitationServiceImpl extends ServiceImpl<BusinessExcitationMapper, UserCredit> implements
    ExcitationService {

    @Resource
    private OrgFeign orgFeign;
    @Resource
    private BusinessLecturerService businessLecturerService;
    @Resource(name = "businessUserService")
    private BusinessUserService businessUserService;

    @Override
    public List<UserCredit> allUserCredit() {
        return baseMapper.allUserCredit();
    }

    @Override
    public List<ExchangeRecordDTO> allUserExchangeCount() {
        return baseMapper.allUserExchangeCount();
    }

    @Override
    public List<UserIntegral> getUserIntegralByUserIds(Collection<String> userIds) {
        return baseMapper.getUserIntegralByUserIds(userIds);
    }

    @Override
    public List<ExcitationTradeRecord> getCleanTradeRecord(Collection<String> userIds, String type, Date startTime,
        Date endTime) {
        return baseMapper.getCleanTradeRecord(userIds, type, startTime, endTime);
    }

    @Override
    public void insertExcitationTradeRecord(Collection<ExcitationTradeRecord> tradeRecord) {
        baseMapper.insertExcitationTradeRecord(tradeRecord);
    }


    @Override
    public void updateUserIntegralBatchById(Collection<UserIntegral> userIntegrals) {
        for (UserIntegral info : userIntegrals) {
            baseMapper.updateUserIntegralById(info);
        }
    }

    @Override
    public void updateUserIntegralById(UserIntegral userIntegral) {
        baseMapper.updateUserIntegralById(userIntegral);
    }


    @Override
    public void updateCleanStatus(List<ExcitationTradeRecord> cleanTradeRecord) {
        for (ExcitationTradeRecord info : cleanTradeRecord) {
            baseMapper.updateCleanStatus(info);
        }
    }

    /**
     * 积分清零 1,根据条件去获取需要清除的用户id集合 2,遍历清除用户id集合 3,获取用户当前积分，获取待清除的积分交易记录 4,设置当前积分为-99999,以达到上锁目的
     * 5,根据交易记录，计算出待清除的总积分（区分可兑换，不可兑换） 6,创建积分清零的交易记录(区分可兑换，不可兑换) 7,更新待清除的积分交易记录为已清除状态 8,更新用户当前积分
     *
     * @param query
     * @return
     */
    @Override
    @Async
    @DS("excitation")
    public void cleanIntegral(IntegralClearQuery query) {
        // 需要清除的用户id集合
        List<String> userIdCollection;
        if (StringUtils.isNotBlank(query.getUserIds())) {
            //有传用户，就以传的条件为准
            userIdCollection = TranslateUtil.translateBySplit(query.getUserIds(), String.class);
        } else {
            //获取待清除积分用户id
            userIdCollection = getCleanUserIds(query);
        }
        if (CollectionUtils.isEmpty(userIdCollection)) {
            return;
        }
        //待设置积分数据
        List<UserIntegral> updateUserIntegrals = new ArrayList<>(userIdCollection.size());

        //待更新状态的历史记录
        List<ExcitationTradeRecord> updateCleanTradeRecords = new ArrayList<>();
        //待插入清零记录
        List<ExcitationTradeRecord> insertExchangeRecords = new ArrayList<>();

        // 获取数据库当前积分信息
        List<UserIntegral> userIntegralDB = new ArrayList<>();
        List<ExcitationTradeRecord> cleanTradeRecordDB = new ArrayList<>();
        List<List<String>> userIdCollections = Lists.partition(userIdCollection, 5000);
        userIdCollections.stream().forEach(item -> {
            List<UserIntegral> userIntegralTemp = this.getUserIntegralByUserIds(item);
            userIntegralDB.addAll(userIntegralTemp);
            //获取要清0的积分交易记录(根据条件去筛选，获取没有并清除过的，并且是可清除的)
            List<ExcitationTradeRecord> cleanTradeRecordtemp = this.getCleanTradeRecord(item, query.getType(),
                query.getStartTime(), query.getEndTime());
            cleanTradeRecordDB.addAll(cleanTradeRecordtemp);
        });

        Map<String, UserIntegral> userIntegralMap = userIntegralDB.stream().collect(
            Collectors.toMap(UserIntegral::getUserId, item -> item, (key1, key2) -> key1));
        Map<String, List<ExcitationTradeRecord>> cleanTradeRecordMap = cleanTradeRecordDB.stream()
            .collect(Collectors.groupingBy(ExcitationTradeRecord::getUserId));

        for (String id : userIdCollection) {
            UserIntegral userIntegral = userIntegralMap.get(id);
            // 用户积分不存在直接返回
            if (Objects.isNull(userIntegral)) {
                continue;
            }

            //总积分数
            BigDecimal num = userIntegral.getNum();
            //当前可用积分
            BigDecimal availableNum = userIntegral.getAvailableNum();
            //可兑换积分
            BigDecimal convertibleNum = userIntegral.getConvertibleNum();

            //计算出需要被清0的积分数
            List<ExcitationTradeRecord> cleanTradeRecord = cleanTradeRecordMap.get(id);
            if (!CollectionUtils.isEmpty(cleanTradeRecord)) {
                updateCleanTradeRecords.addAll(cleanTradeRecord);
            }
            UserIntegral cleanUserIntegral = calculateTrade(cleanTradeRecord);

            // 创建积分交易记录对象-可兑换积分清0记录
            // 可兑换积分
            BigDecimal exchangeNum = cleanUserIntegral.getConvertibleNum();
            ExcitationTradeRecord exchangeRecord = initExcitationTradeRecord(id, exchangeNum, query, 1);
            insertExchangeRecords.add(exchangeRecord);

            // 创建积分交易记录对象-不可兑换积分清0记录, 不可兑换积分 = 总分 - 可兑换积分
            BigDecimal unExchangeNum = cleanUserIntegral.getNum().subtract(cleanUserIntegral.getConvertibleNum());
            ExcitationTradeRecord unExchangeRecord = initExcitationTradeRecord(id, unExchangeNum, query, 0);
            insertExchangeRecords.add(unExchangeRecord);

            // 创建用户积分更新对象
            UserIntegral updateIntegral = new UserIntegral();

            //积分计算
            //总积分数 = 总积分数 - 被清零的积分数
            num = num.subtract(cleanUserIntegral.getNum());
            //当前可用积分 = 当前可用积分 - 被清零的积分数
            availableNum = availableNum.subtract(cleanUserIntegral.getAvailableNum());
            //可兑换积分 = 可兑换积分 - 被清零的可兑换积分数
            convertibleNum = convertibleNum.subtract(cleanUserIntegral.getConvertibleNum());
            updateIntegral.setUserId(userIntegral.getUserId()).setNum(num).setAvailableNum(availableNum)
                .setConvertibleNum(convertibleNum);
            updateUserIntegrals.add(updateIntegral);
        }

        //插入积分清零记录,分页处理
        List<List<ExcitationTradeRecord>> insertExchangeRecordsCollect = Lists.partition(insertExchangeRecords, 5000);
        insertExchangeRecordsCollect.stream().forEach(item -> {
            this.insertExcitationTradeRecord(item);
        });
        //更新积分交易记录为清除状态,分页处理
        List<List<ExcitationTradeRecord>> updateCleanTradeRecordsCollect = Lists.partition(updateCleanTradeRecords,
            5000);
        updateCleanTradeRecordsCollect.stream().forEach(item -> {
            this.updateCleanStatus(item);
        });
        //更新当前总可用积分为0
        List<List<UserIntegral>> updateUserIntegralsCollect = Lists.partition(updateUserIntegrals, 5000);
        updateUserIntegralsCollect.stream().forEach(item -> {
            this.updateUserIntegralBatchById(item);
        });

    }

    /**
     * 计算总的积分数
     *
     * @param records
     * @return
     */
    private UserIntegral calculateTrade(List<ExcitationTradeRecord> records) {
        UserIntegral result = new UserIntegral().setNum(BigDecimal.ZERO).setAvailableNum(BigDecimal.ZERO)
            .setConvertibleNum(BigDecimal.ZERO);
        BigDecimal num = BigDecimal.ZERO;
        BigDecimal availableNum = BigDecimal.ZERO;
        BigDecimal convertibleNum = BigDecimal.ZERO;
        if (CollectionUtils.isEmpty(records)) {
            return result;
        }
        for (ExcitationTradeRecord item : records) {
            Integer operateType = item.getOperateType();
            BigDecimal operateNum = null == item.getOperateNum() ? BigDecimal.ZERO : item.getOperateNum();
            if (0 == operateType) {
                num = num.add(operateNum);
                availableNum = availableNum.add(operateNum);
                if (1 == item.getIsExchange()) {
                    convertibleNum = convertibleNum.add(operateNum);
                }
            } else {
                num = num.subtract(operateNum);
                availableNum = availableNum.subtract(operateNum);
                if (1 == item.getIsExchange()) {
                    convertibleNum = convertibleNum.subtract(operateNum);
                }
            }
        }
        result.setNum(num).setAvailableNum(availableNum).setConvertibleNum(convertibleNum);
        return result;
    }

    private ExcitationTradeRecord initExcitationTradeRecord(String id, BigDecimal negate, IntegralClearQuery query,
        Integer isExchange) {
        ExcitationTradeRecord tradeRecord = new ExcitationTradeRecord().setId(StringUtil.newId())
            .setExcitationId(ExcitationTypeEnum.INTEGRAL.getCode())
            .setOperateType(ExcitationOperationEnum.DECREASE.getValue()).setOperateNum(negate)
            .setCurrentNum(BigDecimal.ZERO).setTradeType(ExcitationTypeEnum.INTEGRAL.getCode()).setTargetId(id)
            .setTargetName("积分").setEventId(OtherEventCategoryEnum.integralClearing.name()).setUserId(id)
            .setCreateBy(UserThreadContext.getUserId()).setRefundRecordId("").setBizId("")
            .setBizType(query.getType() + "Clean").setIsExchange(isExchange).setCreateTime(new Date())
            .setCleanStatus(2);
        String summary;
        String isExchangeName = 1 == isExchange ? "可兑换" : "不可兑换";
        String operateType = negate.compareTo(BigDecimal.ZERO) >= 0 ? ExcitationOperationEnum.DECREASE.getSymbol()
            : ExcitationOperationEnum.INCREASE.getSymbol();
        if (null != query.getStartTime() && null != query.getEndTime()) {
            String startTime = DateUtil.getYmStr(query.getStartTime());
            String endTime = DateUtil.getYmStr(query.getEndTime());
            summary = IntegralStatTypeEnum.getDisplayName(query.getType()) + "-" + startTime + "至" + endTime + "-"
                + isExchangeName + "-积分清零" + operateType + negate;
        } else {
            summary =
                IntegralStatTypeEnum.getDisplayName(query.getType()) + "-" + isExchangeName + "-积分清零" + operateType
                    + negate;
        }
        tradeRecord.setSummary(summary);
        return tradeRecord;
    }

    /**
     * 获取待清除积分用户id
     *
     * @param query
     * @return
     */
    private List<String> getCleanUserIds(IntegralClearQuery query) {
        List<String> userIds = new ArrayList<>();
        Set<String> levelPaths = new HashSet<>();
        if (StringUtils.isNotEmpty(query.getOrgId())) {
            //传入组织条件时，查询该组织下的所有组织
            String levelPath = orgFeign.getLevelPathByOrgId(query.getOrgId());
            levelPaths.add(levelPath);
        } else {
            //不传入组织条件时，查询当前用户的管辖范围
            levelPaths = orgFeign.findUserManageAreaLevelPath(query.getCurrentUserId());
        }

        if (IntegralStatTypeEnum.DEPT.getCode().equals(query.getType())) {
            //根据部门去筛选用户
            userIds = businessUserService.getUserIdByLevelPaths(levelPaths);
        } else if (IntegralStatTypeEnum.LECTURE.getCode().equals(query.getType())) {
            //根据讲师去筛选用户
            List<String> lectureCategoryCodes = null;
            if (StringUtils.isNoneEmpty(query.getCategoryCodes())) {
                lectureCategoryCodes = Arrays.asList(query.getCategoryCodes().split(","));
            }
            userIds = businessLecturerService.getUserIdByLecturer(lectureCategoryCodes, levelPaths);
        }
        return userIds;
    }

    @Override
    public List<String> getNeedPublicReadFilePathList() {
        return baseMapper.getNeedPublicReadFilePathList();
    }
}
