package com.wunding.learn.business.view.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.business.view.service.model.StatAnalysisStudentUploadCourseware;
import com.wunding.learn.business.view.service.model.course.Categorys;
import com.wunding.learn.business.view.service.model.course.Course;
import com.wunding.learn.business.view.service.model.course.CourseDTO;
import com.wunding.learn.business.view.service.model.course.Courseware;
import com.wunding.learn.business.view.service.model.course.UserCourseRecordStatVO;
import com.wunding.learn.common.dto.SysTagResourceRelation;
import com.wunding.learn.common.dto.SysTemTagDTO;
import java.util.List;

/**
 * 课程数据源
 *
 * <AUTHOR>
 * @date 2022/10/17
 */
public interface BusinessViewCourseService extends IService<Course> {

    /**
     * 按类型获取全部分类
     *
     * @param name 名称
     * @return {@link List}<{@link Categorys}>
     */
    List<Categorys> getAllByType(String name);

    /**
     * 查找所有发布课程
     *
     * @return {@link List}<{@link CourseDTO}>
     */
    List<CourseDTO> findAllPublishCourse();

    /**
     * 查找所有课件
     *
     * @return {@link List}<{@link Courseware}>
     */
    List<Courseware> findAllWare();

    /**
     * 用户课程学习记录统计
     *
     * @return {@link List}<{@link UserCourseRecordStatVO}>
     */
    List<UserCourseRecordStatVO> userCourseLearnRecordStat();

    /**
     * 上次学习课程
     *
     * @param queryDate 查询日期
     * @return {@link List}<{@link UserCourseRecordStatVO}>
     */
    List<UserCourseRecordStatVO> lastLearnCourseCount(String queryDate);

    /**
     * 最后完成课程
     *
     * @param queryDate 查询日期
     * @return {@link List}<{@link UserCourseRecordStatVO}>
     */
    List<UserCourseRecordStatVO> lastFinishCourseCount(String queryDate);

    /**
     * 获取当前课程分类的全部下属分类id
     */
    List<String> getCourseCateIds(String courseCateId);

    /**
     * 学员上传课件统计
     */
    List<StatAnalysisStudentUploadCourseware> statStudentUploadCourseware();

    Long getRelateCourseNum(String tagId);

    /**
     * 旧数据处理的方法（不要调用）
     *
     * @return
     */
    List<SysTemTagDTO> getCourseTagList();

    List<String> getCourseTagUsed();

    List<SysTagResourceRelation> getCourseTagRelation();

    /**
     * 获取需要公有读的文件地址
     *
     * @return {@link String}
     */
    List<String> getNeedPublicReadFilePathList();
}
