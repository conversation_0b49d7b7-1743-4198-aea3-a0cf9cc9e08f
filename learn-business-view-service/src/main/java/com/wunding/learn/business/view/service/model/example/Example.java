package com.wunding.learn.business.view.service.model.example;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 案例表
 *
 * <AUTHOR> href="mailto:<EMAIL>">aixinrong</a>
 * @since 2022-09-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("example")
@Schema(name = "Example对象", description = "案例表")
public class Example implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 案例ID
     */
    @Schema(description = "案例ID")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;


    /**
     * 案例编码
     */
    @Schema(description = "案例编码")
    @TableField("example_code")
    private String exampleCode;


    /**
     * 案例名称
     */
    @Schema(description = "案例名称")
    @TableField("example_name")
    private String exampleName;


    /**
     * 案例分类ID
     */
    @Schema(description = "案例分类ID")
    @TableField("example_cate_id")
    private String exampleCateId;


    /**
     * 案例文件类型
     */
    @Schema(description = "案例文件类型")
    @TableField("file_type")
    private String fileType;


    /**
     * 案例文件类型
     */
    @Schema(description = "案例文件类型")
    @TableField("mine")
    private String mine;


    /**
     * 案例作者id
     */
    @Schema(description = "案例作者id")
    @TableField("author_id")
    private String authorId;


    /**
     * 案例作者名称
     */
    @Schema(description = "案例作者名称")
    @TableField("author_name")
    private String authorName;

    /**
     * 案例作者账号
     */
    @Schema(description = "案例作者账号")
    @TableField("author_login_name")
    private String authorLoginName;


    /**
     * 部门id
     */
    @Schema(description = "部门id")
    @TableField("org_id")
    private String orgId;

    /**
     * 用户组织全路径
     */
    @Schema(description = "用户组织全路径")
    @TableField("level_path")
    private String levelPath;

    /**
     * 案例时长
     */
    @Schema(description = "案例时长")
    @TableField("play_time")
    private BigDecimal playTime;


    /**
     * 发布时间
     */
    @Schema(description = "发布时间")
    @TableField("publish_time")
    private Date publishTime;


    /**
     * 案例状态：0:保存状态 1:主管审核 2:评审阶段（根据是否启用来确定） 3:入库
     */
    @Schema(description = "案例状态：0:保存状态 1:主管审核 2:评审阶段（根据是否启用来确定） 3:入库")
    @TableField("status")
    private Integer status;


    /**
     * 案例描述
     */
    @Schema(description = "案例描述")
    @TableField("synopsis")
    private String synopsis;


    /**
     * 条线id
     */
    @Schema(description = "条线id")
    @TableField("business_id")
    private String businessId;


    /**
     * 是否推荐 1 推荐 0 未推荐
     */
    @Schema(description = "是否推荐 1 推荐 0 未推荐")
    @TableField("recommend")
    private Integer recommend;


    /**
     * 是否自动变更等级：1 自动  0 不自动
     */
    @Schema(description = "是否自动变更等级：1 自动  0 不自动")
    @TableField("auto_up_level")
    private Integer autoUpLevel;


    /**
     * 是否入库 0：否 1：是
     */
    @Schema(description = "是否入库 0：否 1：是")
    @TableField("is_lib")
    private Integer isLib;


    /**
     * 播放时长字符值
     */
    @Schema(description = "播放时长字符值")
    @TableField("play_time_str")
    private String playTimeStr;


    /**
     * 文件名称
     */
    @Schema(description = "文件名称")
    @TableField("file_name")
    private String fileName;


    /**
     * 视频转换状态  1 转换中  2 转换完成 3 转换失败
     */
    @Schema(description = "视频转换状态  1 转换中  2 转换完成 3 转换失败")
    @TableField("transform_status")
    private Integer transformStatus;


    /**
     * 是否启用[0：禁用 1：启用]
     */
    @Schema(description = "是否启用[0：禁用 1：启用]")
    @TableField("is_available")
    private Integer isAvailable;


    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    @TableField("is_del")
    @TableLogic(value = "0", delval = "1")
    private Integer isDel;


    /**
     * 新增人
     */
    @Schema(description = "新增人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;


    /**
     * 新增时间
     */
    @Schema(description = "新增时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 修改人
     */
    @Schema(description = "修改人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;


    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 学习人数
     */
    @Schema(description = "学习人数")
    @TableField("learn_num")
    private Long learnNum;


    /**
     * 喜欢数
     */
    @Schema(description = "喜欢数")
    @TableField("like_num")
    private Long likeNum;


    /**
     * 评论数
     */
    @Schema(description = "评论数")
    @TableField("comment_num")
    private Long commentNum;


    /**
     * 综合星级
     */
    @Schema(description = "综合星级")
    @TableField("common_star")
    private BigDecimal commonStar;


    /**
     * 评星人数
     */
    @Schema(description = "评星人数")
    @TableField("star_num")
    private Long starNum;


    /**
     * 评审分数
     */
    @Schema(description = "评审分数")
    @TableField("audit_score")
    private Long auditScore;

    /**
     * 案例当前可见组织id
     */
    @Schema(description = "案例当前可见组织id")
    @TableField(value = "example_org_id")
    private String exampleOrgId;

}
