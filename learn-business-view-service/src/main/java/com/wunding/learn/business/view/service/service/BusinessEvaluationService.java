package com.wunding.learn.business.view.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.business.view.service.model.evaluation.Evaluation;
import com.wunding.learn.business.view.service.model.project.ProjectJoinUserEvalByTimeQuery;
import com.wunding.learn.business.view.service.model.project.ProjectJoinUserOtherDataDTO;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/2
 */
public interface BusinessEvaluationService extends IService<Evaluation> {

    /**
     * 查找学习项目参与用户评估信息
     *
     * @param query 查询条件 {@link ProjectJoinUserEvalByTimeQuery}
     * @return
     */
    List<ProjectJoinUserOtherDataDTO> findProjectJoinUserEvalInfo(ProjectJoinUserEvalByTimeQuery query);
}
