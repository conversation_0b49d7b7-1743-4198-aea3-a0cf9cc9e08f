package com.wunding.learn.business.view.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.business.view.service.aspect.DS;
import com.wunding.learn.business.view.service.mapper.example.BusinessExampleMapper;
import com.wunding.learn.business.view.service.model.example.Example;
import com.wunding.learn.business.view.service.service.BusinessViewExampleService;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * <p> 案例表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">aixinrong</a>
 * @since 2022-08-29
 */
@Slf4j
@Service("businessViewExampleService")
@DS("example")
public class BusinessViewExampleServiceImpl extends ServiceImpl<BusinessExampleMapper, Example> implements
    BusinessViewExampleService {

    @Override
    public List<String> getNeedPublicReadFilePathList() {
        return baseMapper.getNeedPublicReadFilePathList();
    }
}
