<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.business.view.service.mapper.info.BusinessInfoMapper">
    <!-- 开启二级缓存 -->
    <!--
<cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.business.view.service.mapper.info.BusinessInfoMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.business.view.service.model.info.Info">
        <!--@Table info-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="title" jdbcType="VARCHAR"
          property="title"/>
        <result column="sub_title" jdbcType="VARCHAR"
          property="subTitle"/>
        <result column="info_cate_id" jdbcType="VARCHAR"
          property="infoCateId"/>
        <result column="item_id" jdbcType="VARCHAR"
          property="itemId"/>
        <result column="key_word" jdbcType="VARCHAR"
          property="keyWord"/>
        <result column="info_content" jdbcType="LONGVARCHAR"
          property="infoContent"/>
        <result column="info_intro" jdbcType="VARCHAR"
          property="infoIntro"/>
        <result column="author" jdbcType="VARCHAR"
          property="author"/>
        <result column="source" jdbcType="VARCHAR"
          property="source"/>
        <result column="content_type" jdbcType="VARCHAR"
          property="contentType"/>
        <result column="org_id" jdbcType="VARCHAR"
          property="orgId"/>
        <result column="click_number" jdbcType="BIGINT"
          property="clickNumber"/>
        <result column="comment_number" jdbcType="BIGINT"
          property="commentNumber"/>
        <result column="download_number" jdbcType="BIGINT"
          property="downloadNumber"/>
        <result column="vote_number" jdbcType="BIGINT"
          property="voteNumber"/>
        <result column="favorite_number" jdbcType="BIGINT"
          property="favoriteNumber"/>
        <result column="share_number" jdbcType="BIGINT"
          property="shareNumber"/>
        <result column="is_comment" jdbcType="TINYINT"
          property="isComment"/>
        <result column="is_download" jdbcType="TINYINT"
          property="isDownload"/>
        <result column="is_vote" jdbcType="TINYINT"
          property="isVote"/>
        <result column="is_favorite" jdbcType="TINYINT"
          property="isFavorite"/>
        <result column="is_allow_share" jdbcType="TINYINT"
          property="isAllowShare"/>
        <result column="is_first" jdbcType="TINYINT"
          property="isFirst"/>
        <result column="is_top" jdbcType="TINYINT"
          property="isTop"/>
        <result column="is_recommend" jdbcType="TINYINT"
          property="isRecommend"/>
        <result column="is_share" jdbcType="TINYINT"
          property="isShare"/>
        <result column="is_train" jdbcType="TINYINT"
          property="isTrain"/>
        <result column="is_publish" jdbcType="TINYINT"
          property="isPublish"/>
        <result column="publish_by" jdbcType="VARCHAR"
          property="publishBy"/>
        <result column="publish_time" jdbcType="TIMESTAMP"
          property="publishTime"/>
        <result column="large_image" jdbcType="VARCHAR"
          property="largeImage"/>
        <result column="minddle_image" jdbcType="VARCHAR"
          property="minddleImage"/>
        <result column="small_image" jdbcType="VARCHAR"
          property="smallImage"/>
        <result column="sort_no" jdbcType="INTEGER"
          property="sortNo"/>
        <result column="is_avaliable" jdbcType="TINYINT"
          property="isAvaliable"/>
        <result column="is_del" jdbcType="TINYINT"
          property="isDel"/>
        <result column="view_type" jdbcType="INTEGER"
          property="viewType"/>
        <result column="create_by" jdbcType="VARCHAR"
          property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP"
          property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR"
          property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP"
          property="updateTime"/>
        <result column="customer_id" jdbcType="VARCHAR"
          property="customerId"/>
        <result column="infomation_source" jdbcType="TINYINT"
          property="infomationSource"/>
        <result column="upload_files_id" jdbcType="VARCHAR"
          property="uploadFilesId"/>
        <result column="file_name" jdbcType="VARCHAR"
          property="fileName"/>
        <result column="file_type" jdbcType="VARCHAR"
          property="fileType"/>
        <result column="descriptions" jdbcType="VARCHAR"
          property="descriptions"/>
    </resultMap>

    <select id="getNeedPublicReadFilePathList" resultType="java.lang.String" useCache="false">
        SELECT info_content
        FROM info
        WHERE info_content IS NOT NULL
          AND info_content != ''
    </select>
</mapper>
