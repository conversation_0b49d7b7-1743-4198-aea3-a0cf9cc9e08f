package com.wunding.learn.business.view.service.admin.rest;

import com.github.pagehelper.PageInfo;
import com.wunding.learn.business.view.service.admin.dto.LearnRecordCourseDTO;
import com.wunding.learn.business.view.service.admin.dto.LearnRecordDTO;
import com.wunding.learn.business.view.service.admin.dto.LearnRecordExamDTO;
import com.wunding.learn.business.view.service.admin.dto.LearnRecordFaceProjectDTO;
import com.wunding.learn.business.view.service.admin.dto.LearnRecordInfoDTO;
import com.wunding.learn.business.view.service.admin.dto.LearnRecordProjectDTO;
import com.wunding.learn.business.view.service.admin.dto.LearnRecordSurveyDTO;
import com.wunding.learn.business.view.service.admin.dto.LearnRecordTrainDTO;
import com.wunding.learn.business.view.service.admin.query.LearnRecordBaseQuery;
import com.wunding.learn.business.view.service.admin.query.LearnRecordQuery;
import com.wunding.learn.business.view.service.service.LearnRecordStatService;
import com.wunding.learn.common.bean.Result;
import com.wunding.learn.file.api.dto.ExportResultDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 学员档案
 *
 * <AUTHOR>
 * @date 2022/10/27
 */
@RestController
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RequestMapping("${module.businessView.contentPath:/}learnRecord")
@Tag(description = "学员档案", name = "LearnRecordStatRest")
public class LearnRecordStatRest {

    private final LearnRecordStatService learnRecordStatService;

    @GetMapping("/list")
    @Operation(operationId = "list_LearnRecord", summary = "学员档案统计列表", description = "学员档案统计列表")
    public Result<PageInfo<LearnRecordDTO>> list(@ParameterObject @Valid LearnRecordQuery learnRecordQuery) {
        PageInfo<LearnRecordDTO> data = learnRecordStatService.list(learnRecordQuery);
        return Result.success(data);
    }

    @PostMapping("/exportData")
    @Operation(operationId = "exportData_LearnRecord", summary = "学员档案统计列表的数据", description = "学员档案统计列表的数据")
    public Result<ExportResultDTO> exportData(@ParameterObject @Valid LearnRecordQuery learnRecordQuery) {
        learnRecordStatService.export(learnRecordQuery);
        return Result.success();
    }

    @GetMapping("/course")
    @Operation(operationId = "course_LearnRecord", summary = "学员档案课程列表", description = "学员档案课程列表")
    public Result<PageInfo<LearnRecordCourseDTO>> course(
        @ParameterObject @Valid LearnRecordBaseQuery learnRecordQuery) {
        PageInfo<LearnRecordCourseDTO> data = learnRecordStatService.course(learnRecordQuery);
        return Result.success(data);
    }

    @GetMapping("/exam")
    @Operation(operationId = "exam_LearnRecord", summary = "学员档案考试列表", description = "学员档案考试列表")
    public Result<PageInfo<LearnRecordExamDTO>> exam(
        @ParameterObject @Valid LearnRecordBaseQuery learnRecordQuery) {
        PageInfo<LearnRecordExamDTO> data = learnRecordStatService.exam(learnRecordQuery);
        return Result.success(data);
    }

    @GetMapping("/project")
    @Operation(operationId = "project_LearnRecord", summary = "学员档案学习项目列表", description = "学员档案学习项目列表")
    public Result<PageInfo<LearnRecordProjectDTO>> project(
        @ParameterObject @Valid LearnRecordBaseQuery learnRecordQuery) {
        PageInfo<LearnRecordProjectDTO> data = learnRecordStatService.project(learnRecordQuery);
        return Result.success(data);
    }

    @GetMapping("/faceProject")
    @Operation(operationId = "faceProject_LearnRecord", summary = "学员档案面授班级列表", description = "学员档案面授班级列表")
    public Result<PageInfo<LearnRecordFaceProjectDTO>> faceProject(
        @ParameterObject @Valid LearnRecordBaseQuery learnRecordQuery) {
        PageInfo<LearnRecordFaceProjectDTO> data = learnRecordStatService.faceProject(learnRecordQuery);
        return Result.success(data);
    }

    @GetMapping("/train")
    @Operation(operationId = "train_LearnRecord", summary = "学员档案培训项目列表", description = "学员档案培训项目列表")
    public Result<PageInfo<LearnRecordTrainDTO>> train(
        @ParameterObject @Valid LearnRecordBaseQuery learnRecordQuery) {
        PageInfo<LearnRecordTrainDTO> data = learnRecordStatService.train(learnRecordQuery);
        return Result.success(data);
    }

    @GetMapping("/info")
    @Operation(operationId = "info_LearnRecord", summary = "学员档案资讯列表", description = "学员档案资讯列表")
    public Result<PageInfo<LearnRecordInfoDTO>> info(
        @ParameterObject @Valid LearnRecordBaseQuery learnRecordQuery) {
        PageInfo<LearnRecordInfoDTO> data = learnRecordStatService.info(learnRecordQuery);
        return Result.success(data);
    }

    @GetMapping("/survey")
    @Operation(operationId = "survey_LearnRecord", summary = "学员档案调研列表", description = "学员档案调研列表")
    public Result<PageInfo<LearnRecordSurveyDTO>> survey(
        @ParameterObject @Valid LearnRecordBaseQuery learnRecordQuery) {
        PageInfo<LearnRecordSurveyDTO> data = learnRecordStatService.survey(learnRecordQuery);
        return Result.success(data);
    }

    @PostMapping("/exportData/course")
    @Operation(operationId = "course_exportData_LearnRecord", summary = "导出学员档案课程列表的数据", description = "导出学员档案课程列表的数据")
    public Result<ExportResultDTO> exportCourse(@ParameterObject @Valid LearnRecordBaseQuery learnRecordQuery) {
        learnRecordStatService.exportCourse(learnRecordQuery);
        return Result.success();
    }

    @PostMapping("/exportData/exam")
    @Operation(operationId = "exam_exportData_LearnRecord", summary = "导出学员档案考试列表的数据", description = "导出学员档案考试列表的数据")
    public Result<ExportResultDTO> exportExam(@ParameterObject @Valid LearnRecordBaseQuery learnRecordQuery) {
        learnRecordStatService.exportExam(learnRecordQuery);
        return Result.success();
    }

    @PostMapping("/exportData/info")
    @Operation(operationId = "info_exportData_LearnRecord", summary = "导出学员档案资讯列表的数据", description = "导出学员档案资讯列表的数据")
    public Result<ExportResultDTO> exportInfo(@ParameterObject @Valid LearnRecordBaseQuery learnRecordQuery) {
        learnRecordStatService.exportInfo(learnRecordQuery);
        return Result.success();
    }

    @PostMapping("/exportData/project")
    @Operation(operationId = "project_exportData_LearnRecord", summary = "导出学员档案学习项目列表的数据", description = "导出学员档案学习项目列表的数据")
    public Result<ExportResultDTO> exportProject(@ParameterObject @Valid LearnRecordBaseQuery learnRecordQuery) {
        learnRecordStatService.exportProject(learnRecordQuery);
        return Result.success();
    }

    @PostMapping("/exportData/survey")
    @Operation(operationId = "survey_exportData_LearnRecord", summary = "导出学员档案调研列表的数据", description = "导出学员档案调研列表的数据")
    public Result<ExportResultDTO> exportSurvey(@ParameterObject @Valid LearnRecordBaseQuery learnRecordQuery) {
        learnRecordStatService.exportSurvey(learnRecordQuery);
        return Result.success();
    }

    @PostMapping("/exportData/faceProject")
    @Operation(operationId = "faceProject_exportData_LearnRecord", summary = "导出学员档案面授班级列表的数据", description = "导出学员档案面授班级列表的数据")
    public Result<ExportResultDTO> exportFaceProject(@ParameterObject @Valid LearnRecordBaseQuery learnRecordQuery) {
        learnRecordStatService.exportFaceProject(learnRecordQuery);
        return Result.success();
    }

    @PostMapping("/exportData/train")
    @Operation(operationId = "train_exportData_LearnRecord", summary = "导出学员档案培训项目列表的数据", description = "导出学员档案培训项目列表的数据")
    public Result<ExportResultDTO> exportTrain(@ParameterObject @Valid LearnRecordBaseQuery learnRecordQuery) {
        learnRecordStatService.exportTrain(learnRecordQuery);
        return Result.success();
    }

}
