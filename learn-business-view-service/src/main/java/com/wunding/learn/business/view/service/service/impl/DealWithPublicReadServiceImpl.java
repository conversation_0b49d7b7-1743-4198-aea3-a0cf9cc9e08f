package com.wunding.learn.business.view.service.service.impl;

import com.wunding.learn.business.view.service.service.BusinessLearnMapService;
import com.wunding.learn.business.view.service.service.BusinessUserService;
import com.wunding.learn.business.view.service.service.BusinessViewCourseService;
import com.wunding.learn.business.view.service.service.BusinessViewExampleService;
import com.wunding.learn.business.view.service.service.BusinessViewInfoService;
import com.wunding.learn.business.view.service.service.BusinessViewProjectService;
import com.wunding.learn.business.view.service.service.BusinessViewPromotedGameService;
import com.wunding.learn.business.view.service.service.BusinessViewSpecialService;
import com.wunding.learn.business.view.service.service.ExcitationService;
import com.wunding.learn.business.view.service.service.IDealWithPublicReadService;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">aixinrong</a>
 * @since 2025/6/25
 */
@Slf4j
@Service("dealWithPublicReadService")
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class DealWithPublicReadServiceImpl implements IDealWithPublicReadService {

    private final BusinessViewCourseService businessViewCourseService;
    private final BusinessViewProjectService businessViewProjectService;
    private final BusinessViewSpecialService businessViewSpecialService;
    private final ExcitationService excitationService;
    private final BusinessViewExampleService businessViewExampleService;
    private final BusinessViewInfoService businessViewInfoService;
    private final BusinessViewPromotedGameService businessViewPromotedGameService;
    private final BusinessUserService businessUserService;
    private final BusinessLearnMapService businessLearnMapService;

    @Override
    public List<String> getNeedPublicReadFilePathList() {
        Set<String> filePathList = new HashSet<>();
        List<String> contentList = new ArrayList<>();
        contentList.addAll(businessViewCourseService.getNeedPublicReadFilePathList());
        contentList.addAll(businessViewProjectService.getNeedPublicReadFilePathList());
        contentList.addAll(businessViewSpecialService.getNeedPublicReadFilePathList());
        contentList.addAll(excitationService.getNeedPublicReadFilePathList());
        contentList.addAll(businessViewExampleService.getNeedPublicReadFilePathList());
        contentList.addAll(businessViewInfoService.getNeedPublicReadFilePathList());
        contentList.addAll(businessViewPromotedGameService.getNeedPublicReadFilePathList());
        contentList.addAll(businessUserService.getNeedPublicReadFilePathList());
        contentList.addAll(businessLearnMapService.getNeedPublicReadFilePathList());
        Pattern pattern = Pattern.compile("https?://[^\\s\"']*?(/[^\\s\"'?]+)");
        for (String content : contentList) {
            Matcher matcher = pattern.matcher(content);
            while (matcher.find()) {
                String path = matcher.group(1);
                filePathList.add(path);
            }
        }
        return new ArrayList<>(filePathList);
    }
}
