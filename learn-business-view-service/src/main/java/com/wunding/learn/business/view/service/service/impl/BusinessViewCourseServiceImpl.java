package com.wunding.learn.business.view.service.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.business.view.service.admin.dto.CoursewareCommentDTO;
import com.wunding.learn.business.view.service.admin.dto.CoursewarePackageMergeRecordDTO;
import com.wunding.learn.business.view.service.admin.dto.CoursewareStarDTO;
import com.wunding.learn.business.view.service.admin.dto.UserCoursewareLearnDetailDTO;
import com.wunding.learn.business.view.service.aspect.DS;
import com.wunding.learn.business.view.service.mapper.course.BusinessCourseMapper;
import com.wunding.learn.business.view.service.model.StatAnalysisStudentUploadCourseware;
import com.wunding.learn.business.view.service.model.course.Categorys;
import com.wunding.learn.business.view.service.model.course.Course;
import com.wunding.learn.business.view.service.model.course.CourseDTO;
import com.wunding.learn.business.view.service.model.course.Courseware;
import com.wunding.learn.business.view.service.model.course.UserCourseRecordStatVO;
import com.wunding.learn.business.view.service.service.BusinessViewCourseService;
import com.wunding.learn.business.view.service.service.CommentService;
import com.wunding.learn.common.dto.SysTagResourceRelation;
import com.wunding.learn.common.dto.SysTemTagDTO;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 课程服务实现
 *
 * <AUTHOR>
 * @date 2022/10/17
 */
@Service("businessViewCourseService")
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@DS("course")
public class BusinessViewCourseServiceImpl extends ServiceImpl<BusinessCourseMapper, Course> implements
    BusinessViewCourseService {

    private final CommentService commentService;

    @Override
    public List<Categorys> getAllByType(String name) {
        return baseMapper.getAllByType(name);
    }

    @Override
    public List<CourseDTO> findAllPublishCourse() {
        List<CourseDTO> allPublishCourse = baseMapper.findAllPublishCourse();
        // 从评论库中获取map映射关系，组装起来
        Map<String, CourseDTO> courseStatMap = commentService.getCourseStat();
        allPublishCourse.forEach(course -> {
            // 课程统计的统计信息
            CourseDTO courseStat = courseStatMap.get(course.getId());
            Optional<CourseDTO> statInfoOpt = Optional.ofNullable(courseStat);
            // 如果该课程统计信息为null则给默认值
            course.setCommentNumber(statInfoOpt.map(CourseDTO::getClickNumber).orElse(0L).intValue());
        });

        return allPublishCourse;
    }

    @Override
    public List<Courseware> findAllWare() {
        return baseMapper.findAllWare();
    }

    @Override
    public List<UserCourseRecordStatVO> userCourseLearnRecordStat() {
        return baseMapper.userCourseLearnRecordStat();
    }

    @Override
    public List<UserCourseRecordStatVO> lastLearnCourseCount(String queryDate) {
        return baseMapper.lastLearnCourseCount(queryDate);
    }

    @Override
    public List<UserCourseRecordStatVO> lastFinishCourseCount(String queryDate) {
        return baseMapper.lastFinishCourseCount(queryDate);
    }

    public List<String> getCourseCateIds(String courseCateId) {
        return baseMapper.getCourseCateIds(courseCateId);

    }

    @Override
    public List<StatAnalysisStudentUploadCourseware> statStudentUploadCourseware() {
        // 查询出全部上传记录
        List<StatAnalysisStudentUploadCourseware> allUploadCourseware = baseMapper.getAllUploadCourseware();
        if (CollectionUtils.isEmpty(allUploadCourseware)) {
            return allUploadCourseware;
        }
        // 查询出全部合并生成的课件
        List<String> packageIdList = allUploadCourseware.stream().map(StatAnalysisStudentUploadCourseware::getId)
            .collect(Collectors.toList());
        List<CoursewarePackageMergeRecordDTO> mergeRecordList = baseMapper.getMergeRecordList(packageIdList);
        if (CollectionUtils.isEmpty(mergeRecordList)) {
            return allUploadCourseware;
        }
        Map<String, List<String>> packageMergeMap = mergeRecordList.stream()
            .collect(Collectors.groupingBy(CoursewarePackageMergeRecordDTO::getPackageId,
                Collectors.mapping(CoursewarePackageMergeRecordDTO::getCwId, Collectors.toList())));
        // 查询出全部评论
        Map<String, Set<String>> commentMap = commentService.getCoursewareComment().stream()
            .collect(Collectors.groupingBy(CoursewareCommentDTO::getCwId,
                Collectors.mapping(CoursewareCommentDTO::getCommentBy, Collectors.toSet())));
        //  查询出全部评星
        Map<String, CoursewareStarDTO> starMap = baseMapper.getCoursewareStar().stream()
            .collect(Collectors.toMap(CoursewareStarDTO::getCwId, Function.identity(), (key1, key2) -> key1));
        // 查询全部学习记录
        Map<String, Set<UserCoursewareLearnDetailDTO>> learnMap = baseMapper.getCoursewareLearn().stream()
            .collect(Collectors.groupingBy(UserCoursewareLearnDetailDTO::getCwId,
                Collectors.mapping(Function.identity(), Collectors.toSet())));

        BigDecimal zero = new BigDecimal("0.0");
        allUploadCourseware.forEach(uploadCourseware -> {
            // 合并的课件
            List<String> cwIdList = packageMergeMap.get(uploadCourseware.getId());
            if (!CollectionUtils.isEmpty(cwIdList)) {
                Set<String> commentBySet = new HashSet<>();
                Set<String> learnBySet = new HashSet<>();
                Set<String> learnedBySet = new HashSet<>();
                int peopleNumTotal = 0;
                for (String cwId : cwIdList) {
                    // 总的评论人
                    commentBySet.addAll(
                        Optional.ofNullable(commentMap.get(cwId)).orElseGet(HashSet::new));
                    // 总的综合评星
                    CoursewareStarDTO coursewareStarDTO = Optional.ofNullable(starMap.get(cwId))
                        .orElse(new CoursewareStarDTO());
                    BigDecimal star = Optional.ofNullable(coursewareStarDTO.getOverallScore()).orElse(BigDecimal.ZERO);
                    peopleNumTotal = peopleNumTotal + (Optional.ofNullable(coursewareStarDTO.getPeopleNum()).orElse(0));
                    uploadCourseware.setOverallScore(uploadCourseware.getOverallScore().add(star));
                    // 总的学习人和已学完人
                    Optional.ofNullable(learnMap.get(cwId)).ifPresent(learn -> {
                        learnBySet.addAll(learn.stream().map(UserCoursewareLearnDetailDTO::getUserId).collect(
                            Collectors.toSet()));
                        learnedBySet.addAll(learn.stream()
                            .filter(l -> GeneralJudgeEnum.CONFIRM.getValue().equals(l.getIsLearned()))
                            .map(UserCoursewareLearnDetailDTO::getUserId).collect(
                                Collectors.toSet()));
                    });
                }
                // 平均评星
                if (!uploadCourseware.getOverallScore().equals(zero) && peopleNumTotal > 0) {
                    uploadCourseware.setOverallScore(
                        uploadCourseware.getOverallScore()
                            .divide(BigDecimal.valueOf(peopleNumTotal), 1, RoundingMode.HALF_UP));
                }
                uploadCourseware.setCommentCount((long) commentBySet.size());
                uploadCourseware.setLearnCount((long) learnBySet.size());
                uploadCourseware.setLearnedCount((long) learnedBySet.size());
            }
        });
        return allUploadCourseware;
    }

    @Override
    public Long getRelateCourseNum(String tagId) {
        return baseMapper.getRelateCourseNum(tagId);
    }

    @Override
    public List<SysTemTagDTO> getCourseTagList() {
        return baseMapper.getCourseTagList();
    }

    @Override
    public List<String> getCourseTagUsed() {
        return baseMapper.getCourseTagUsed();
    }

    @Override
    public List<SysTagResourceRelation> getCourseTagRelation() {
        return baseMapper.getCourseTagRelation();
    }

    @Override
    public List<String> getNeedPublicReadFilePathList() {
        return baseMapper.getNeedPublicReadFilePathList();
    }
}
