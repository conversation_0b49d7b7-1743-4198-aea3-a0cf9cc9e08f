package com.wunding.learn.business.view.service.mapper.promoted;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.business.view.service.model.promoted.Emigrated;
import com.wunding.learn.business.view.service.model.promoted.EmigratedDTO;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 闯关游戏表 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">liyihui</a>
 * @since 2022-08-19
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")
})
public interface BusinessEmigratedMapper extends BaseMapper<Emigrated> {

    /**
     * 所有用户计数统计
     *
     * @return {@link List}<{@link EmigratedDTO}>
     */
    List<EmigratedDTO> allUserCountStat();

    /**
     * 进行中的闯关
     *
     * @param currentUserId     用户id
     * @param managerAreaOrgIds 管辖范围
     * @return 数量
     */
    Integer getInProgressPromotedGameCount(@Param("currentUserId") String currentUserId,
        @Param("managerAreaOrgIds") Set<String> managerAreaOrgIds);

    /**
     * 获取需要公有读的文件地址
     *
     * @return {@link String}
     */
    List<String> getNeedPublicReadFilePathList();
}
