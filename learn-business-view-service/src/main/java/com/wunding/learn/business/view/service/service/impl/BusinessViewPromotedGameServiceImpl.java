package com.wunding.learn.business.view.service.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.business.view.service.aspect.DS;
import com.wunding.learn.business.view.service.mapper.promoted.BusinessEmigratedMapper;
import com.wunding.learn.business.view.service.model.promoted.Emigrated;
import com.wunding.learn.business.view.service.model.promoted.EmigratedDTO;
import com.wunding.learn.business.view.service.service.BusinessViewPromotedGameService;
import java.util.List;
import java.util.Set;
import org.springframework.stereotype.Service;

/**
 * 闯关数据源
 *
 * <AUTHOR>
 * @date 2022/10/14
 */
@Service
@DS("promotedgame")
public class BusinessViewPromotedGameServiceImpl extends ServiceImpl<BusinessEmigratedMapper, Emigrated> implements
    BusinessViewPromotedGameService {

    @Override
    public List<EmigratedDTO> allUserCountStat() {
        return baseMapper.allUserCountStat();
    }

    @Override
    public Integer getInProgressPromotedGameCount(String currentUserId, Set<String> managerAreaOrgIds) {
        return baseMapper.getInProgressPromotedGameCount(currentUserId, managerAreaOrgIds);
    }

    @Override
    public List<String> getNeedPublicReadFilePathList() {
        return baseMapper.getNeedPublicReadFilePathList();
    }
}
