package com.wunding.learn.business.view.service.mapper.exam;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.business.view.service.model.exam.Exam;
import com.wunding.learn.business.view.service.model.exam.ExamDTO;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

/**
 * 考试表 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">xiao8</a>
 * @date 2022-03-19
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")
})
public interface BusinessExamMapper extends BaseMapper<Exam> {

    /**
     * 查找所有发布考试
     *
     * @return {@link List}<{@link ExamDTO}>
     */
    List<ExamDTO> findAllExam();

    /**
     * 全部考题数
     *
     * @return
     */
    Long getQuestionTotal();

    /**
     * 获取考题名称
     *
     * @param questionId
     * @return
     */
    String getQuestionName(@Param("questionId") String questionId);

    /**
     * 进行中的考试
     *
     * @param userId 用户id
     * @return 考试数量
     */
    Integer getInProgressExamCount(@Param("userId") String userId,
        @Param("managerAreaOrgIds") Set<String> managerAreaOrgIds);

    /**
     * 查询可见考题库题目数量
     *
     * @param userId           用户id
     * @param currentOrgIdList 所在部门及其上级
     * @param queryType        查询类型 0-全部 1-昨日 2-本月 3-上月
     * @return 考题数
     */
    Integer getLibQuestionCount(@Param("userId") String userId,
        @Param("currentOrgIdList") List<String> currentOrgIdList,
        @Param("queryType") Integer queryType);

    /**
     * 查询可见试卷库数量
     *
     * @param userId           用户id
     * @param currentOrgIdList 所在部门及其上级
     * @param queryType        查询类型 0-全部 1-昨日 2-本月 3-上月
     * @return 试卷数
     */
    Integer getExamLibCount(@Param("userId") String userId,
        @Param("currentOrgIdList") List<String> currentOrgIdList,
        @Param("queryType") Integer queryType);

    /**
     * 进行中的pk赛
     *
     * @param currentUserId     用户id
     * @param managerAreaOrgIds 管辖范围
     * @return 数量
     */
    Integer getInProgressExamCompetitionCount(@Param("currentUserId") String currentUserId,
        @Param("managerAreaOrgIds") Set<String> managerAreaOrgIds);
}
