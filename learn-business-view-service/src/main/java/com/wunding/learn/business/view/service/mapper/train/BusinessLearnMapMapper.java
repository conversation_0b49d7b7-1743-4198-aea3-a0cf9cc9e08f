package com.wunding.learn.business.view.service.mapper.train;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.business.view.service.admin.dto.PostDevelopDTO;
import com.wunding.learn.business.view.service.model.train.LearnMap;
import com.wunding.learn.business.view.service.model.train.Train;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 学习地图表 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @since 2023-12-06
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")})
public interface BusinessLearnMapMapper extends BaseMapper<LearnMap> {

    /**
     * 岗位学习地图发展信息
     *
     * @return
     */
    List<PostDevelopDTO> getPostDevelop();

    /**
     * 获取进行中学习地图数量
     *
     * @param userId            用户id
     * @param managerAreaOrgIds 管辖范围
     * @return 数量
     */
    Integer getInProgressLearnMapCount(@Param("userId") String userId,
        @Param("managerAreaOrgIds") Set<String> managerAreaOrgIds);

    /**
     * 获取培训项目名称
     *
     * @param trainIdSet 培训项目id
     * @return {@link Train}
     */
    List<Train> getTrainList(@Param("trainIdSet") Set<String> trainIdSet);

    /**
     * 获取需要公有读的文件地址
     *
     * @return {@link String}
     */
    List<String> getNeedPublicReadFilePathList();
}
