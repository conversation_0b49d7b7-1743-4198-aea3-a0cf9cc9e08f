package com.wunding.learn.business.view.service.mapper.user;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.business.view.service.admin.dto.DepartUserDTO;
import com.wunding.learn.business.view.service.admin.dto.OnlineNumDTO;
import com.wunding.learn.business.view.service.admin.dto.PostDTO;
import com.wunding.learn.business.view.service.model.StatNewLoginNumMonth;
import com.wunding.learn.business.view.service.model.user.Para;
import com.wunding.learn.business.view.service.model.user.User;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import com.wunding.learn.user.api.dto.OrgDTO;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

/**
 * 系统用户表 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">xiao8</a>
 * @date 2022-02-23
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")
})
public interface BusinessUserMapper extends BaseMapper<User> {

    /**
     * 查找所有组织
     *
     * @return {@link List}<{@link OrgDTO}>
     */
    List<OrgDTO> findAll();

    /**
     * 查找所有登录用户id
     *
     * @return {@link List}<{@link String}>
     */
    List<String> findAllLoginUserId();

    /**
     * 按日期范围查找第一个登录用户
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return {@link List}<{@link String}>
     */
    List<String> findFirstLoginUserByDateRange(String startTime, String endTime);

    /**
     * 获取月度新上线用户统计数据
     *
     * @param startDate 统计开始时间
     * @param endDate   统计结束时间
     * @return 月度新上线用户统计数据
     */
    List<StatNewLoginNumMonth> findNewLoginMonthStats(Date startDate, Date endDate);

    /**
     * 获取在线用户数量
     *
     * @param orgIds 组织ID
     * @return {@link OnlineNumDTO}
     */
    List<OnlineNumDTO> getOnlineNum(Collection<String> orgIds);

    /**
     * 获取子组织
     *
     * @param orgId 组织id
     * @return {@link Set}<{@link String}>
     */
    Set<String> getChildOrg(String orgId);

    /**
     * 获取用户数量
     *
     * @param orgIds 组织ID
     * @return {@link DepartUserDTO}
     */
    List<DepartUserDTO> getTotalDepartUser(Collection<String> orgIds);

    /**
     * 获取在线用户数量
     *
     * @param orgIds  组织ID
     * @param timeStr 时间str
     * @return {@link List}<{@link OnlineNumDTO}>
     */
    List<OnlineNumDTO> getOnlineNumGroupById(Collection<String> orgIds, String timeStr);

    /**
     * 获取管辖范围的组织名称
     *
     * @param userId 用户id
     * @return {@link List}<{@link String}>
     */
    List<String> getManageOrgName(String userId);

    /**
     * 获取子组织
     *
     * @param orgId 组织id
     * @return {@link Set}<{@link String}>
     */
    Set<String> getChildrenId(String orgId);

    /**
     * 获取岗位名
     *
     * @param postId
     * @return
     */
    String getPostName(@Param("postId") String postId);

    /**
     * 获取角色名
     *
     * @param roleId
     * @return
     */
    String getRoleName(@Param("roleId") String roleId);

    /**
     * 获取角色总数
     *
     * @return
     */
    Long getRoleCount();

    /**
     * 批量获取岗位
     *
     * @param postIdList
     * @return
     */
    List<PostDTO> getPostByPostId(@Param("postIdList") Collection<String> postIdList);

    /**
     * 根据id获取部门
     *
     * @param orgId
     * @return
     */
    OrgDTO getOrgById(@Param("orgId") String orgId);

    /**
     * 获取系统参数
     *
     * @param code
     * @return
     */
    Para getParaGetCode(@Param("code") String code);

    /**
     * 根据组织id去获取用户id
     *
     * @param orgIds
     * @return
     */
    List<String> getUserIdByLevelPaths(Collection<String> levelPaths);

    /**
     * 根据工种获取用户id
     *
     * @param workTypes
     * @param levelPaths
     * @return
     */
    List<String> getUserIdByWorkType(Collection<String> workTypes, Collection<String> levelPaths);

    /**
     * 获取评委数量
     *
     * @return 数量
     */
    Integer getExpertCount();

    /**
     * 获取需要公有读的文件地址
     *
     * @return {@link String}
     */
    List<String> getNeedPublicReadFilePathList();
}
