<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.business.view.service.mapper.StatAnalysisInsideLecturerDepartmentMapper">

        <!-- 开启二级缓存 -->
        <!--
    <cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
    -->

        <!-- 使用缓存 -->
        <cache-ref namespace="com.wunding.learn.business.view.service.mapper.StatAnalysisInsideLecturerDepartmentMapper"/>

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.wunding.learn.business.view.service.model.StatAnalysisInsideLecturerDepartment">
            <!--@Table stat_analysis_inside_lecturer_department-->
                    <id column="id" jdbcType="VARCHAR" property="id"/>
                    <result column="code" jdbcType="VARCHAR"
                            property="code"/>
                    <result column="name" jdbcType="VARCHAR"
                            property="name"/>
                    <result column="level_path" jdbcType="VARCHAR"
                            property="levelPath"/>
                    <result column="people_num" jdbcType="BIGINT"
                            property="peopleNum"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, code, name, level_path, people_num
        </sql>

        <select id="getListData" resultType="com.wunding.learn.business.view.service.admin.dto.StatAnalysisInsideLecturerDepartmentDTO">
                select d.id, d.code, d.name, d.level_path, d.people_num
                from stat_analysis_inside_lecturer_department d
                left join sys_org o on d.id=o.id
                where o.is_del=0
        </select>
</mapper>
