<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.business.view.service.mapper.course.BusinessCourseMapper">
    <!-- 开启二级缓存 -->
    <!--
<cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.business.view.service.mapper.course.BusinessCourseMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.business.view.service.model.course.Course">
        <!--@Table course-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="course_no" jdbcType="VARCHAR"
          property="courseNo"/>
        <result column="course_name" jdbcType="VARCHAR"
          property="courseName"/>
        <result column="course_cate_id" jdbcType="VARCHAR"
          property="courseCateId"/>
        <result column="item_id" jdbcType="VARCHAR"
          property="itemId"/>
        <result column="course_type" jdbcType="VARCHAR"
          property="courseType"/>
        <result column="study_type" jdbcType="VARCHAR"
          property="studyType"/>
        <result column="cover_image_url" jdbcType="VARCHAR"
          property="coverImageUrl"/>
        <result column="descriptions" jdbcType="VARCHAR"
          property="descriptions"/>
        <result column="credit" jdbcType="DECIMAL"
          property="credit"/>
        <result column="score" jdbcType="DECIMAL"
          property="score"/>
        <result column="org_id" jdbcType="VARCHAR"
          property="orgId"/>
        <result column="author" jdbcType="VARCHAR"
          property="author"/>
        <result column="is_required" jdbcType="TINYINT"
          property="isRequired"/>
        <result column="is_sign" jdbcType="TINYINT"
          property="isSign"/>
        <result column="is_audit" jdbcType="TINYINT"
          property="isAudit"/>
        <result column="is_download" jdbcType="TINYINT"
          property="isDownload"/>
        <result column="is_comment" jdbcType="TINYINT"
          property="isComment"/>
        <result column="is_vote" jdbcType="TINYINT"
          property="isVote"/>
        <result column="is_first" jdbcType="TINYINT"
          property="isFirst"/>
        <result column="is_recommend" jdbcType="TINYINT"
          property="isRecommend"/>
        <result column="is_share" jdbcType="TINYINT"
          property="isShare"/>
        <result column="is_favorite" jdbcType="TINYINT"
          property="isFavorite"/>
        <result column="is_allow_share" jdbcType="TINYINT"
          property="isAllowShare"/>
        <result column="is_available" jdbcType="TINYINT"
          property="isAvailable"/>
        <result column="is_del" jdbcType="TINYINT"
          property="isDel"/>
        <result column="is_publish" jdbcType="TINYINT"
          property="isPublish"/>
        <result column="publish_by" jdbcType="VARCHAR"
          property="publishBy"/>
        <result column="publish_time" jdbcType="TIMESTAMP"
          property="publishTime"/>
        <result column="publish_path" jdbcType="VARCHAR"
          property="publishPath"/>
        <result column="sort_no" jdbcType="INTEGER"
          property="sortNo"/>
        <result column="click_number" jdbcType="BIGINT"
          property="clickNumber"/>
        <result column="comment_number" jdbcType="INTEGER"
          property="commentNumber"/>
        <result column="download_number" jdbcType="INTEGER"
          property="downloadNumber"/>
        <result column="vote_number" jdbcType="INTEGER"
          property="voteNumber"/>
        <result column="favorite_number" jdbcType="INTEGER"
          property="favoriteNumber"/>
        <result column="share_in_number" jdbcType="INTEGER"
          property="shareInNumber"/>
        <result column="source" jdbcType="VARCHAR"
          property="source"/>
        <result column="create_by" jdbcType="VARCHAR"
          property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP"
          property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR"
          property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP"
          property="updateTime"/>
        <result column="dev_time" jdbcType="VARCHAR"
          property="devTime"/>
        <result column="language" jdbcType="VARCHAR"
          property="language"/>
        <result column="version" jdbcType="VARCHAR"
          property="version"/>
        <result column="course_level" jdbcType="VARCHAR"
          property="courseLevel"/>
        <result column="share_by" jdbcType="VARCHAR"
          property="shareBy"/>
        <result column="share_time" jdbcType="TIMESTAMP"
          property="shareTime"/>
        <result column="is_train" jdbcType="TINYINT"
          property="isTrain"/>
        <result column="csort_no" jdbcType="INTEGER"
          property="csortNo"/>
        <result column="auto_publish_time" jdbcType="TIMESTAMP"
          property="autoPublishTime"/>
        <result column="is_public" jdbcType="TINYINT"
          property="isPublic"/>
        <result column="is_create_sign" jdbcType="TINYINT"
          property="isCreateSign"/>
        <result column="is_create_assess" jdbcType="TINYINT"
          property="isCreateAssess"/>
        <result column="is_hang_up" jdbcType="TINYINT"
          property="isHangUp"/>
        <result column="hang_up_duration_minute" jdbcType="VARCHAR"
          property="hangUpDurationMinute"/>
        <result column="hang_up_duration_second" jdbcType="VARCHAR"
          property="hangUpDurationSecond"/>
        <result column="recommend_time" jdbcType="TIMESTAMP"
          property="recommendTime"/>
    </resultMap>

    <select id="getAllByType" resultType="com.wunding.learn.business.view.service.model.course.Categorys"
      useCache="false">
        select *
        from categorys
        where is_del = 0
          and is_available = 1
          and category_type = #{type}
    </select>
    <select id="findAllPublishCourse" resultType="com.wunding.learn.business.view.service.model.course.CourseDTO"
      useCache="false">
        select c.id                                                                                 id,
               c.course_name                                                                        courseName,
               c.org_id                                                                             orgId,
               c.course_cate_id,
               ifnull((select sum(cv.vote_count) from course_vote cv where cv.course_id = c.id), 0) voteNumber,
               (select count(ur.user_id) from user_course_record ur where ur.course_id = c.id)  clickNumber,
               (select count(cvt.id) from course_favorate cvt where cvt.course_id = c.id)       favoriteNumber,
               (select count(cs.id) from course_star cs where cs.course_id = c.id)              starUserCount,
               (select ifnull(sum(star_count), 0) from course_star cs where cs.course_id = c.id)   starScoreCount
        from course c
        where c.is_del = 0
          and c.is_publish = 1
    </select>

    <select id="findAllWare" resultType="com.wunding.learn.business.view.service.model.course.Courseware" useCache="false">
        select *
        from courseware
        where is_del = 0
    </select>

    <select id="userCourseLearnRecordStat"
      resultType="com.wunding.learn.business.view.service.model.course.UserCourseRecordStatVO" useCache="false">
        select user_id, sum(duration) learnTimeCount, count(id) learnCount, sum(is_learned) finishCourseCount
        from user_course_record tur
        group by user_id
    </select>

    <select id="lastLearnCourseCount"
      resultType="com.wunding.learn.business.view.service.model.course.UserCourseRecordStatVO" useCache="false">
        select user_id userId, count(id) learnCount
        from user_course_record tur
        where create_time > #{date}
        group by userId
    </select>

    <select id="lastFinishCourseCount"
      resultType="com.wunding.learn.business.view.service.model.course.UserCourseRecordStatVO" useCache="false">
        select user_id userId, count(id) finishCourseCount
        from user_course_record tur
        where finish_time > #{date}
          and is_learned = 1
        group by userId
    </select>

    <!--嵌套子查询-待优化-->
    <select id="selectCourseLearnStat"
      resultType="com.wunding.learn.business.view.service.admin.dto.CourseLearnStatDTO" useCache="false">
        select a.course_id,
        b.course_name,
        date_format(b.publish_time, '%Y-%m-%d %H:%i:%s') publishTime,
        (select cate.category_name from categorys cate where cate.id = b.course_cate_id) categoryName,
        (select count(cw.id) from courseware cw where cw.course_id = b.id) totalCw,
        (select count(ucr.id)
        from user_course_record ucr
        where ucr.is_del = 0
        and is_learned = 1
        and ucr.course_id = b.id) finishnum,
        (select count(ucr.id)
        from user_course_record ucr
        where ucr.is_del = 0
        and is_learned = 0
        and ucr.course_id = b.id) underwaynum,
        (select count(cd.id) from course_download cd where cd.course_id = b.id) totalDownload
        from (
        select cvd.course_id
        from course_view_duration cvd
        where cvd.end_time >= date_format(#{params.startTime}, '%Y-%m-%d')
        and cvd.end_time &lt; date_format(#{params.endTime}, '%Y-%m-%d') + interval 1 day
        group by cvd.course_id) a,
        course b
        where a.course_id = b.id
        and b.org_id in
        <foreach collection="params.managerAreaOrgIds" item="orgId" open="(" close=")" separator=",">
            #{orgId}
        </foreach>
        <if test="params.courseName != null and params.courseName != ''">
            and instr(b.course_name,#{params.courseName}) > 0
        </if>
        <if test="params.courseType != null and params.courseType != ''">
            and b.course_cate_id = #{params.courseType}
        </if>
        order by b.update_time desc, b.create_time desc
    </select>


    <select id="detailLearned" resultType="com.wunding.learn.business.view.service.admin.dto.CourseLearnedDTO"
      useCache="false">
        select a.user_id                as id,
               ifnull(d.minViewTime, 0) as minViewTime,
               ifnull(d.maxViewTime, 0) as maxViewTime,
               d.totalDuration
        from user_course_record a,
             (select cvd.view_by,
                     sum(cvd.duration) totalDuration,
                     min(cvd.view_time) minViewTime,
                     max(cvd.view_time) maxViewTime
              from course_view_duration cvd
              where cvd.course_id = #{params.courseId}
              group by cvd.view_by) d
        where a.user_id = d.view_by
          and a.course_id = #{params.courseId}
          and a.is_del = 0
          and a.is_learned = #{params.isLearned}
        order by d.totalDuration desc
    </select>

    <select id="getCourseCateIds" resultType="java.lang.String" useCache="false">
        select id
        from categorys
        where category_type = 'CourseCate'
          and is_del = 0
          and is_available = 1
          and is_display = 1
          and level_path like concat('%', #{courseCateId}, '%')
    </select>

    <select id="getAllUploadCourseware"
      resultType="com.wunding.learn.business.view.service.model.StatAnalysisStudentUploadCourseware" useCache="false">
        select cw.id,
               cw.cw_name,
               cw.create_time,
               cw.active_time,
               cw.is_available as cw_is_available,
               cw.is_del       as cw_is_del,
               su.id           as user_id,
               su.login_name,
               su.full_name,
               su.is_available as user_is_available,
               su.is_del       as user_is_del,
               su.org_id,
               so.org_name,
               so.level_path,
               so.level_path_name,
               0                  learnCount,
               0                  learnedCount,
               0                  commentCount,
               0.0                overallScore
        from courseware cw
                 inner join sys_user su on cw.upload_by = su.id
                 inner join sys_org so on su.org_id = so.id
        where length(cw.upload_by) > 0
          and (
            cw.is_source = 6
                or cw.is_source = 5)
    </select>

    <select id="getMergeRecordList"
      resultType="com.wunding.learn.business.view.service.admin.dto.CoursewarePackageMergeRecordDTO" useCache="false">
        select package_id, cw_id
        from courseware_package_merge_record
        where is_del = 0
        <foreach collection="packageIdList" open=" and package_id in (" close=")" separator="," item="packageId">
            #{packageId}
        </foreach>
    </select>

    <select id="getCoursewareStar" resultType="com.wunding.learn.business.view.service.admin.dto.CoursewareStarDTO"
      useCache="false">
        select cws.cw_id                     as cw_id,
               sum(cws.star_count) as overall_score,
               count(cws.id) as peopleNum
        from courseware_star cws
        group by cws.cw_id;
    </select>

    <select id="getCoursewareLearn"
      resultType="com.wunding.learn.business.view.service.admin.dto.UserCoursewareLearnDetailDTO" useCache="false">
        select cur.courseware_id cwid,
               cur.user_id,
               cur.is_learned
        from courseware_user_record cur
    </select>
    <select id="getRelateCourseNum" resultType="java.lang.Long">
        select count(c.id)
        from course c
        where c.is_del = 0
          and c.is_copy = 0
          and exists (select 1 from course_tag b where b.course_id = c.id and b.tag_id = #{tagId})
    </select>
    <select id="getCourseTagList" resultType="com.wunding.learn.common.dto.SysTemTagDTO" useCache="false">
        select id
             ,'default_sys_tag_categorys' as tag_classify_id
             ,tag_name
             ,create_type
             ,is_optional
             ,is_show
             ,0 as client_display
             ,0 as is_recommend
             ,sort_no
             ,default_type
             ,is_available
             ,is_del
             ,create_by
             ,create_time
             ,update_by
             ,update_time
             ,1 as is_can_delete
        from tag
    </select>
    <select id="getCourseTagUsed" resultType="java.lang.String" useCache="false">
        select distinct(tag_id) from user_course_tag where is_del = 0
    </select>
    <select id="getCourseTagRelation" resultType="com.wunding.learn.common.dto.SysTagResourceRelation">
        select ct.id, ct.tag_id as tagId, ct.course_id as resourceId, 'course' as resourceType
        from  `course_tag` ct
        inner join course c on c.id = ct.course_id
        where c.is_del = 0
    </select>

    <select id="getNeedPublicReadFilePathList" resultType="java.lang.String" useCache="false">
        SELECT cw_content FROM courseware WHERE cw_content IS NOT NULL AND cw_content != ''
        UNION ALL
        SELECT descriptions FROM course WHERE descriptions IS NOT NULL AND descriptions != ''
    </select>
</mapper>
