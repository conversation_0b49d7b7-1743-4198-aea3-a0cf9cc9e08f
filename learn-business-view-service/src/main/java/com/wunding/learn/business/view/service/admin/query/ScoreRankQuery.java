package com.wunding.learn.business.view.service.admin.query;

import com.wunding.learn.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.Parameter;
import java.util.Collection;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

/**
 * 经验排行榜统计
 *
 * <AUTHOR>
 * @date 2022/11/1
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class ScoreRankQuery extends BaseEntity {

    @Parameter(description = "组织id", example = "0")
    private String orgId;

    @Parameter(description = "组织id集合，管辖范围", hidden = true)
    private Collection<String> orgIdCollection;

    @Parameter(description = "姓名/账号, 使用 userId,userId 拼接的字符串", example = "admin,admin")
    @Length(max = 3600)
    private String userIds;

}
