<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.business.view.service.mapper.StatAnalysisTrainCentreMapper">
    <!-- 开启二级缓存 -->
    <!--
<cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.business.view.service.mapper.StatAnalysisTrainCentreMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.business.view.service.model.StatAnalysisTrainCentre">
        <!--@Table stat_analysis_train_centre-->
        <id column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="train_plan_total" jdbcType="INTEGER"
          property="trainPlanTotal"/>
        <result column="train_plan_finish_count" jdbcType="INTEGER"
          property="trainPlanFinishCount"/>
        <result column="lecturer_total" jdbcType="INTEGER"
          property="lecturerTotal"/>
        <result column="lecturer_category_count" jdbcType="INTEGER"
          property="lecturerCategoryCount"/>
        <result column="lecturer_level_count" jdbcType="INTEGER"
          property="lecturerLevelCount"/>
        <result column="certification_total" jdbcType="INTEGER"
          property="certificationTotal"/>
        <result column="certification_system_count" jdbcType="INTEGER"
          property="certificationSystemCount"/>
        <result column="certification_category_count" jdbcType="INTEGER"
          property="certificationCategoryCount"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        user_id, train_plan_total, train_plan_finish_count, lecturer_total, lecturer_category_count, lecturer_level_count, certification_total, certification_system_count, certification_category_count
    </sql>
</mapper>
