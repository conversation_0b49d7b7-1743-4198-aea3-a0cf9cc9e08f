package com.wunding.learn.business.view.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 资讯访问统计
 *
 * <AUTHOR>
 * @date 2022/11/8
 */
@Data
@Accessors(chain = true)
@Schema(name = "InfoStatDTO", description = "资讯访问统计数据dto对象")
public class InfoStatDTO {

    @Schema(description = "主键id")
    private String id;

    @Schema(description = "资讯名称")
    private String infoName;

    @Schema(description = "资讯分类名称")
    private String infoCateName;

    @Schema(description = "发布时间")
    private Date publishTime;

    @Schema(description = "浏览数")
    private Integer totalView;

    @Schema(description = "浏览人数")
    private Integer totalViewPerson;

    @Schema(description = "评论次数")
    private Integer totalComment;

    @Schema(description = "评论人数")
    private Integer totalCommentPerson;

}