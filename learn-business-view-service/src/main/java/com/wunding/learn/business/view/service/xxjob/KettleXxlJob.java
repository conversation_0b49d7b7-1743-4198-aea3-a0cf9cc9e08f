package com.wunding.learn.business.view.service.xxjob;

import com.wunding.learn.business.view.service.model.EnableGame;
import com.wunding.learn.business.view.service.model.StatAnalysisResource;
import com.wunding.learn.business.view.service.model.StatAnalysisResourceExecTime;
import com.wunding.learn.business.view.service.model.StatAnalysisStudentUploadCourseware;
import com.wunding.learn.business.view.service.model.project.Project;
import com.wunding.learn.business.view.service.service.BusinessLearnMapService;
import com.wunding.learn.business.view.service.service.BusinessViewCourseService;
import com.wunding.learn.business.view.service.service.BusinessViewExamService;
import com.wunding.learn.business.view.service.service.BusinessViewLecturerService;
import com.wunding.learn.business.view.service.service.BusinessViewProjectService;
import com.wunding.learn.business.view.service.service.BusinessViewSpecialService;
import com.wunding.learn.business.view.service.service.CertificationService;
import com.wunding.learn.business.view.service.service.IDealWithPublicReadService;
import com.wunding.learn.business.view.service.service.IEnableGameService;
import com.wunding.learn.business.view.service.service.IStatAnalysisResourceExecTimeService;
import com.wunding.learn.business.view.service.service.IStatAnalysisResourceService;
import com.wunding.learn.business.view.service.service.IStatAnalysisStudentUploadCoursewareService;
import com.wunding.learn.business.view.service.util.KettleUtil;
import com.wunding.learn.common.constant.bussinessview.KettleExceptionEnum;
import com.wunding.learn.common.constant.dynamicDataSource.DynamicDataSourceConstant;
import com.wunding.learn.common.constant.other.AvailableEnum;
import com.wunding.learn.common.constant.other.ErrorNoEnum;
import com.wunding.learn.common.constant.tenant.TenantRedisKeyConstant;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.dto.SysTagResourceRelation;
import com.wunding.learn.common.dto.SysTemTagDTO;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.util.date.DateUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.file.api.service.FileFeign;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.service.SysTagFeign;
import com.wunding.learn.user.api.service.UserFeign;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * Kettle任务调度
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @since 2022/10/31
 */
@Slf4j
@Component("kettleXxlJob")
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class KettleXxlJob {

    public static final String SYNC_SYS_USER_GAME = "sync_sys_user_game";
    private final KettleUtil kettleUtil;
    private final IEnableGameService enableGameService;
    private final RedisTemplate<String, Object> redisTemplate;
    private final BusinessViewCourseService businessViewCourseService;
    private final BusinessViewSpecialService businessViewSpecialService;
    private final BusinessViewLecturerService businessViewLecturerService;
    private final IStatAnalysisStudentUploadCoursewareService statAnalysisStudentUploadCoursewareService;
    private final UserFeign userFeign;
    private final SysTagFeign sysTagFeign;
    private final BusinessViewExamService businessViewExamService;
    private final IStatAnalysisResourceService statAnalysisResourceService;
    private final CertificationService certificationService;
    private final IStatAnalysisResourceExecTimeService statAnalysisResourceExecTimeService;
    private final BusinessViewProjectService businessViewProjectService;
    private final BusinessLearnMapService businessLearnMapService;
    private final IDealWithPublicReadService dealWithPublicReadService;
    private final FileFeign fileFeign;

    /**
     * 总体运营统计
     */
    @XxlJob("operationStatAnalysis")
    public ReturnT<String> operationStatAnalysis() throws Exception {
        String param = XxlJobHelper.getJobParam();
        kettleUtil.executeTrans("stat_analysis_operation", KettleUtil.handleDateParam(param));
        return ReturnT.SUCCESS;
    }

    /**
     * 上线用户统计
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("onlineUserStatAnalysis")
    public ReturnT<String> onlineUserStatAnalysis() throws Exception {
        kettleUtil.executeJob("stat_analysis_online_user", List.of(""));
        return ReturnT.SUCCESS;
    }

    /**
     * 课程情况统计
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("courseStateStatAnalysis")
    public ReturnT<String> courseStateStatAnalysis() throws Exception {
        kettleUtil.executeJob("stat_analysis_course_job", List.of(""));
        return ReturnT.SUCCESS;
    }

    /**
     * 部门学习统计
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("departLearnStatAnalysis")
    public ReturnT<String> departLearnStatAnalysis() throws Exception {
        String param = XxlJobHelper.getJobParam();
        kettleUtil.executeTrans("stat_analysis_depart_learn", KettleUtil.handleDateParam(param));
        return ReturnT.SUCCESS;
    }

    /**
     * 积分统计
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("integralStateStatAnalysis")
    public ReturnT<String> integralStateStatAnalysis() throws Exception {
        kettleUtil.executeJob("stat_analysis_integral_state", List.of(""));
        return ReturnT.SUCCESS;
    }


    /**
     * 经验排行榜统计
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("scoreStateStatAnalysis")
    public ReturnT<String> scoreStateStatAnalysis() throws Exception {
        String param = XxlJobHelper.getJobParam();
        param = null == param ? DateUtil.getLastDayYmdStr() : param;
        kettleUtil.executeTrans("stat_analysis_score_rank", KettleUtil.handleDateParam(param));
        return ReturnT.SUCCESS;
    }

    /**
     * 学员档案日报表任务
     */
    @XxlJob("learnRecordDayStatAnalysis")
    public ReturnT<String> learnRecordDayStatAnalysis() throws Exception {
        String jobParam = XxlJobHelper.getJobParam();
        kettleUtil.executeJob("stat_analysis_learn_record_job", KettleUtil.handleDateParam(jobParam));
        return ReturnT.SUCCESS;
    }

    /**
     * 学员档案月报表任务
     */
    @XxlJob("learnRecordMonthStatAnalysis")
    public ReturnT<String> learnRecordMonthStatAnalysis() throws Exception {
        String param = XxlJobHelper.getJobParam();
        kettleUtil.executeTrans("stat_analysis_learn_record_month", KettleUtil.handleMonthParam(param));
        return ReturnT.SUCCESS;
    }

    /**
     * 资讯访问统计
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("infoStateStatAnalysis")
    public ReturnT<String> infoStateStatAnalysis() throws Exception {
        kettleUtil.executeTrans("stat_analysis_info", List.of(""));
        return ReturnT.SUCCESS;
    }

    /**
     * 搜索关键字统计
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("searchKeyStatAnalysis")
    public ReturnT<String> searchKeyStatAnalysis() throws Exception {
        kettleUtil.executeTrans("stat_analysis_search_key", List.of(""));
        return ReturnT.SUCCESS;
    }

    /**
     * 培训计划统计
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("trainPlanStatAnalysis")
    public ReturnT<String> trainPlanStatAnalysis() throws Exception {
        kettleUtil.executeTrans("stat_analysis_train_plan", List.of(""));
        return ReturnT.SUCCESS;
    }

    /**
     * 访问量统计
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("visitUserStatAnalysis")
    public ReturnT<String> visitUserStatAnalysis() throws Exception {
        kettleUtil.executeJob("stat_analysis_visit_user", List.of(""));
        return ReturnT.SUCCESS;
    }

    /**
     * 访问时段分析
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("timeRegionStatAnalysis")
    public ReturnT<String> timeRegionStatAnalysis() throws Exception {
        kettleUtil.executeTrans("stat_analysis_time_region", List.of(""));
        return ReturnT.SUCCESS;
    }

    /**
     * 课件学习统计
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("coursewareLearnStatAnalysis")
    public ReturnT<String> coursewareLearnStatAnalysis() throws Exception {
        kettleUtil.executeJob("stat_analysis_courseware_learn", List.of(""));
        return ReturnT.SUCCESS;
    }

    /**
     * 学员上传课件统计
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("studentUploadCoursewareStatAnalysis")
    public void studentUploadCoursewareStatAnalysis() throws Exception {
        Map<String, String> dataSources = redisTemplate.<String, String>opsForHash()
            .entries(TenantRedisKeyConstant.DB_KEY);
        if (CollectionUtils.isEmpty(dataSources)) {
            log.error("data_source_is_empty,current_job:studentUploadCoursewareStatAnalysis()");
            throw new BusinessException(ErrorNoEnum.ERR_NOT_EXISTS);
        }
        for (String dbName : dataSources.keySet()) {
            UserThreadContext.setTenantId(dbName.replace(DynamicDataSourceConstant.DB_LOOK_UP_KEY_PREFIX, ""));
            List<StatAnalysisStudentUploadCourseware> list = businessViewCourseService.statStudentUploadCourseware();
            if (!CollectionUtils.isEmpty(list)) {
                statAnalysisStudentUploadCoursewareService.saveOrUpdateBatch(list, 1000);
            }
        }
        UserThreadContext.remove();
    }

    /**
     * 内部讲师统计
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("insideLecturerStatAnalysis")
    public ReturnT<String> insideLecturerStatAnalysis() throws Exception {
        kettleUtil.executeJob("stat_analysis_inside_lecturer", List.of(""));
        return ReturnT.SUCCESS;
    }

    /**
     * 目标激励明细查询
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("userExcitationRecordStatAnalysis")
    public ReturnT<String> userExcitationRecordStatAnalysis() throws Exception {
        kettleUtil.executeTrans("stat_analysis_user_excitation_record", List.of(""));
        return ReturnT.SUCCESS;
    }

    /**
     * 学员激励汇总表
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("excitationCollectStatAnalysis")
    public ReturnT<String> excitationCollectStatAnalysis() throws Exception {
        kettleUtil.executeTrans("stat_analysis_excitation_collect", List.of(""));
        return ReturnT.SUCCESS;
    }

    /**
     * 兑换记录查询
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("exchangeRecordStatAnalysis")
    public ReturnT<String> exchangeRecordStatAnalysis() throws Exception {
        kettleUtil.executeTrans("stat_analysis_exchange_record", List.of(""));
        return ReturnT.SUCCESS;
    }

    /**
     * 金币交易查询
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("gldTradeStatAnalysis")
    public ReturnT<String> gldTradeStatAnalysis() throws Exception {
        kettleUtil.executeTrans("stat_analysis_gld_trade", List.of(""));
        return ReturnT.SUCCESS;
    }


    /**
     * 栏目访问日统计
     *
     * @return
     * @throws Exception
     */
    @XxlJob("itemVisitRecordDayStatAnalysis")
    public ReturnT<String> itemVisitRecordDayStatAnalysis() throws Exception {
        kettleUtil.executeTrans("stat_analysis_item_visit_record_day", List.of(""));
        return ReturnT.SUCCESS;
    }


    /**
     * 栏目访问月统计
     *
     * @return
     * @throws Exception
     */
    @XxlJob("itemVisitRecordMonthStatAnalysis")
    public ReturnT<String> itemVisitRecordMonthStatAnalysis() throws Exception {
        kettleUtil.executeTrans("stat_analysis_item_visit_record_month", List.of(""));
        return ReturnT.SUCCESS;
    }

    /**
     * 考试情况统计
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("examStateStatAnalysis")
    public ReturnT<String> examStateStatAnalysis() throws Exception {
        kettleUtil.executeJob("stat_analysis_exam_state", List.of(""));
        return ReturnT.SUCCESS;
    }

    /**
     * 年度学习账单
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("annualStudyBillDataStatAnalysis")
    public ReturnT<String> annualStudyBillDataStatAnalysis() throws Exception {
        String param = XxlJobHelper.getJobParam();
        kettleUtil.executeJob("stat_analysis_annual_study_bill_data", KettleUtil.handleYearParam(param));
        return ReturnT.SUCCESS;
    }

    /**
     * 同步组织数据
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("syncSysOrg")
    @SuppressWarnings("unchecked")
    public ReturnT<String> syncSysOrg() throws Exception {
        kettleUtil.executeTrans("sync_sys_org", List.of(""));
        Map<String, String> dataSources = redisTemplate.<String, String>opsForHash()
            .entries(TenantRedisKeyConstant.DB_KEY);
        if (CollectionUtils.isEmpty(dataSources)) {
            log.error("data_source_is_empty,execute_job_fail,job_name:{}", SYNC_SYS_USER_GAME);
            throw new BusinessException(KettleExceptionEnum.KETTLE_JOB_EXECUTE_ERROR);
        }
        for (Entry<String, String> entry : dataSources.entrySet()) {
            UserThreadContext.setTenantId(entry.getKey().replace(DynamicDataSourceConstant.DB_LOOK_UP_KEY_PREFIX, ""));
            try {
                List<EnableGame> gameList = enableGameService.list();
                List<EnableGame> enableGameList = gameList.stream()
                    .filter(item -> Objects.equals(AvailableEnum.AVAILABLE.getValue(), item.getIsAvailable())).collect(
                        Collectors.toList());
                for (EnableGame gameInfo : enableGameList) {
                    Map<String, Map<String, String>> map = JsonUtil.jsonToObj(entry.getValue(), Map.class);
                    String jdbcFormat = "jdbc:mysql://{0}:{1}/{2}?allowMultiQueries=true&serverTimezone=Asia/Shanghai";
                    Map<String, String> dataSourceInfoMap = HashMap.newHashMap(3);
                    dataSourceInfoMap.put("jdbc",
                        MessageFormat.format(jdbcFormat, gameInfo.getDbHost(), gameInfo.getDbPort(),
                            gameInfo.getGameModuleName()));
                    dataSourceInfoMap.put("username", gameInfo.getDbUserName());
                    dataSourceInfoMap.put("password", gameInfo.getDbPassWord());
                    assert map != null;
                    map.put(gameInfo.getGameModuleName(), dataSourceInfoMap);

                    // 自定义处理游戏数据库信息
                    kettleUtil.executeTransCustomDataSource("sync_sys_org_game", List.of(""), map);
                }
            } catch (Exception e) {
                log.error("exec_sync_sys_org_error", e);
            }
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 同步组织数据
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("syncSysUserOrg")
    public ReturnT<String> syncSysUserOrg() throws Exception {
        kettleUtil.executeTrans("sync_sys_org_relation", List.of(""));
        return ReturnT.SUCCESS;
    }

    /**
     * 同步会员数据
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("syncSysMember")
    public ReturnT<String> syncSysMember() throws Exception {
        kettleUtil.executeTrans("sync_sys_member", List.of(""));
        return ReturnT.SUCCESS;
    }

    /**
     * 同步用户数据
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("syncSysUser")
    @SuppressWarnings("unchecked")
    public ReturnT<String> syncSysUser() throws Exception {
        kettleUtil.executeTrans("sync_sys_user", List.of(""));
        Map<String, String> dataSources = redisTemplate.<String, String>opsForHash()
            .entries(TenantRedisKeyConstant.DB_KEY);
        if (CollectionUtils.isEmpty(dataSources)) {
            log.error("data_source_is_empty,execute_job_fail,job_name:{}", SYNC_SYS_USER_GAME);
            throw new BusinessException(KettleExceptionEnum.KETTLE_JOB_EXECUTE_ERROR);
        }
        for (Entry<String, String> entry : dataSources.entrySet()) {
            UserThreadContext.setTenantId(entry.getKey().replace(DynamicDataSourceConstant.DB_LOOK_UP_KEY_PREFIX, ""));
            try {
                List<EnableGame> gameList = enableGameService.list();
                List<EnableGame> enableGameList = gameList.stream()
                    .filter(item -> Objects.equals(AvailableEnum.AVAILABLE.getValue(), item.getIsAvailable())).collect(
                        Collectors.toList());
                for (EnableGame gameInfo : enableGameList) {
                    Map<String, Map<String, String>> map = JsonUtil.jsonToObj(entry.getValue(), Map.class);
                    String jdbcFormat = "jdbc:mysql://{0}:{1}/{2}?allowMultiQueries=true&serverTimezone=Asia/Shanghai";
                    Map<String, String> dataSourceInfoMap = HashMap.newHashMap(3);
                    dataSourceInfoMap.put("jdbc",
                        MessageFormat.format(jdbcFormat, gameInfo.getDbHost(), gameInfo.getDbPort(),
                            gameInfo.getGameModuleName()));
                    dataSourceInfoMap.put("username", gameInfo.getDbUserName());
                    dataSourceInfoMap.put("password", gameInfo.getDbPassWord());
                    assert map != null;
                    map.put(gameInfo.getGameModuleName(), dataSourceInfoMap);

                    // 自定义处理游戏数据库信息
                    kettleUtil.executeTransCustomDataSource(SYNC_SYS_USER_GAME, List.of(""), map);
                }
            } catch (Exception e) {
                log.error("exec_sync_sys_org_error", e);
            }
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 资源中心统计
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("resourceCentreStatAnalysis")
    public ReturnT<String> resourceCentreStatAnalysis() throws Exception {
        kettleUtil.executeJob("stat_analysis_resource_centre_job", List.of(""));
        for (Object tenantId : redisTemplate.opsForHash().keys(TenantRedisKeyConstant.DB_KEY)) {
            UserThreadContext.setTenantId(
                ((String) tenantId).replace(DynamicDataSourceConstant.DB_LOOK_UP_KEY_PREFIX, ""));
            List<UserDTO> allAdministrator = userFeign.getAllAdministrator();
            // 对考题库、试卷库特殊统计
            List<StatAnalysisResource> saveStatAnalysisResourceList = new ArrayList<>();
            saveStatAnalysisResourceList.addAll(businessViewExamService.statisticsLib(allAdministrator));
            // 对人才测评进行统计
            saveStatAnalysisResourceList.addAll(certificationService.statisticsAssess(allAdministrator));
            // 对外部讲师进行统计
            statAnalysisResourceService.statisticsExternalLecturer();
            // 对任职资格评委统计
            statAnalysisResourceService.statisticsJobAuthExpert();
            if (!CollectionUtils.isEmpty(saveStatAnalysisResourceList)) {
                statAnalysisResourceService.saveBatch(saveStatAnalysisResourceList, 1000);
            }
            statAnalysisResourceExecTimeService.save(new StatAnalysisResourceExecTime().setExecTime(new Date()));
        }
        UserThreadContext.remove();
        return ReturnT.SUCCESS;
    }

    /**
     * 系统管理统计
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("sysManageStatAnalysis")
    public ReturnT<String> sysManageStatAnalysis() throws Exception {
        kettleUtil.executeTrans("stat_analysis_system_manage", List.of(""));
        return ReturnT.SUCCESS;
    }

    /**
     * 培训中心统计
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("trainCentreStatAnalysis")
    public ReturnT<String> trainCentreStatAnalysis() throws Exception {
        kettleUtil.executeTrans("stat_analysis_train_centre", List.of(""));
        return ReturnT.SUCCESS;
    }

    /**
     * 学习任务中心发布数统计
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("studyCenterPublicDataStatAnalysis")
    public ReturnT<String> studyCenterPublicDataStatAnalysis() throws Exception {
        kettleUtil.executeTrans("stat_analysis_study_center", List.of(""));
        return ReturnT.SUCCESS;
    }

    /**
     * 学习任务中心每日新建课程统计
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("studyCreateEverydayStatAnalysis")
    public ReturnT<String> studyCreateEverydayStatAnalysis() throws Exception {
        kettleUtil.executeTrans("stat_analysis_create_everyday", List.of(""));
        return ReturnT.SUCCESS;
    }

    /**
     * 管理任务统计
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("manageTaskStatAnalysis")
    public ReturnT<String> manageTaskStatAnalysis() throws Exception {
        kettleUtil.executeTrans("stat_analysis_manage_task", List.of(""));
        return ReturnT.SUCCESS;
    }

    /**
     * 运营中心统计
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("operationCenterStatAnalysis")
    public ReturnT<String> operationCenterStatAnalysis() throws Exception {
        kettleUtil.executeTrans("stat_analysis_operation_center", List.of(""));
        return ReturnT.SUCCESS;
    }

    /**
     * 快速培训（统计学员）
     *
     * @return
     * @throws Exception
     */
    @XxlJob("stat_analysis_project_student")
    public ReturnT<String> statAnalysisProjectStudent() throws Exception {
        String param = XxlJobHelper.getJobParam();
        // param = "0,1,2,3,4,5"
        log.info("stat_analysis_project_student:{}", param);
        List<Map<String, String>> mapList = KettleUtil.buildMultiParam(param);
        log.info("mapList:{}", JsonUtil.objToJson(mapList));
        kettleUtil.executeMultiParamTrans("stat_analysis_project_student", mapList);
        return ReturnT.SUCCESS;
    }

    /**
     * 快速培训作业（统计培训师、管理员）
     *
     * @return
     * @throws Exception
     */
    @XxlJob("stat_analysis_project_job")
    public ReturnT<String> statAnalysisProjectJob() throws Exception {
        String param = XxlJobHelper.getJobParam();
        log.info("stat_analysis_project_job:{}", param);
        List<Map<String, String>> mapList = KettleUtil.buildMultiParam(param);
        log.info("mapList:{}", JsonUtil.objToJson(mapList));
        kettleUtil.executeMultiParamJob("stat_analysis_project_job", mapList);
        return ReturnT.SUCCESS;
    }


    /**
     * 能力项部门统计
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("statTrainLearnMapAbilityOrg")
    public ReturnT<String> statTrainLearnMapAbilityOrg() throws Exception {
        kettleUtil.executeTrans("stat_train_learn_map_ability_org", List.of(""));
        return ReturnT.SUCCESS;
    }


    public void init() {
        CompletableFuture.runAsync(() -> {
            try {
                syncSysOrg();
                syncSysUser();
            } catch (Exception e) {
                log.error("init_sync_error", e);
            }
        });
    }

    /**
     * 计划项目列表统计
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("planForSourceStatAnalysis")
    public ReturnT<String> planForSourceStatAnalysis() throws Exception {
        kettleUtil.executeTrans("stat_plan", List.of(""));
        return ReturnT.SUCCESS;
    }

    /**
     * 计划执行统计-按月
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("planExecuteMonth")
    public ReturnT<String> planExecuteMonth() throws Exception {
        kettleUtil.executeTrans("stat_plan_execute_month", List.of(""));
        return ReturnT.SUCCESS;
    }

    /**
     * 计划执行统计-按年
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("planExecuteYear")
    public ReturnT<String> planExecuteYear() throws Exception {
        kettleUtil.executeTrans("stat_plan_execute_year", List.of(""));
        return ReturnT.SUCCESS;
    }


    /**
     * 计划执行统计-按年-汇总
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("planExecuteYearSummary")
    public ReturnT<String> planExecuteYearSummary() throws Exception {
        kettleUtil.executeTrans("stat_plan_execute_year_summary", List.of(""));
        return ReturnT.SUCCESS;
    }


    /**
     * 计划执行统计-按月-汇总
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("planExecuteMonthSummary")
    public ReturnT<String> planExecuteMonthSummary() throws Exception {
        kettleUtil.executeTrans("stat_plan_execute_month_summary", List.of(""));
        return ReturnT.SUCCESS;
    }

    /**
     * 讲师培训统计-按月
     *
     * @return
     * @throws Exception
     */
    @XxlJob("lecturerTrainStatisticByMonth")
    public ReturnT<String> lecturerTrainStatisticByMonth() throws Exception {
        String param = XxlJobHelper.getJobParam();
        kettleUtil.executeTrans("stat_analysis_lecturer_train_statistic_by_month",
            KettleUtil.handleCurDateParam(param));
        return ReturnT.SUCCESS;
    }

    /**
     * 讲师培训统计-202205
     *
     * @return
     * @throws Exception
     */
    @XxlJob("lecturerTrainStatisticBy202205")
    public ReturnT<String> lecturerTrainStatisticBy202205() throws Exception {
        kettleUtil.executeTrans("stat_analysis_lecturer_train_statistic_by_202205", List.of(""));
        return ReturnT.SUCCESS;
    }

    /**
     * 讲师培训统计-按年
     *
     * @return
     * @throws Exception
     */
    @XxlJob("lecturerTrainStatisticByYear")
    public ReturnT<String> lecturerTrainStatisticByYear() throws Exception {
        String param = XxlJobHelper.getJobParam();
        kettleUtil.executeTrans("stat_analysis_lecturer_train_statistic_by_year", KettleUtil.handleCurDateParam(param));
        return ReturnT.SUCCESS;
    }

    /**
     * 讲师培训统计-上月
     *
     * @return
     * @throws Exception
     */
    @XxlJob("lecturerTrainStatisticByLastMonth")
    public ReturnT<String> lecturerTrainStatisticByLastMonth() throws Exception {
        kettleUtil.executeTrans("stat_analysis_lecturer_train_statistic_by_last_month", List.of(""));
        return ReturnT.SUCCESS;
    }

    /**
     * 讲师培训统计-去年
     *
     * @return
     * @throws Exception
     */
    @XxlJob("lecturerTrainStatisticByLastYear")
    public ReturnT<String> lecturerTrainStatisticByLastYear() throws Exception {
        kettleUtil.executeTrans("stat_analysis_lecturer_train_statistic_by_last_year", List.of(""));
        return ReturnT.SUCCESS;
    }

    /**
     * 培训用户积分排名统计
     *
     * @return
     * @throws Exception
     */
    @XxlJob("trainUserIntegralRanking")
    public ReturnT<String> trainUserIntegralRanking() throws Exception {
        kettleUtil.executeTrans("stat_analysis_train_user_integral_ranking", List.of(""));
        return ReturnT.SUCCESS;
    }


    /**
     * 学习项目举办统计
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("trainHoldingStatistics")
    public ReturnT<String> trainHoldingStatistics() throws Exception {
        kettleUtil.executeJob("train_holding_statistics_work", List.of(""));
        return ReturnT.SUCCESS;
    }


    /**
     * 积分统计-按部门
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("statAnalysisIntegralMonth")
    public ReturnT<String> statAnalysisIntegralMonth() throws Exception {
        String param = XxlJobHelper.getJobParam();
        if (StringUtils.isNotBlank(param) && "all".equals(param)) {
            // 全部更新，不根据时间筛选
            kettleUtil.executeTrans("stat_analysis_integral_month_all", List.of(""));
        } else {
            kettleUtil.executeTrans("stat_analysis_integral_month", KettleUtil.handleDateParam(param));
        }
        return ReturnT.SUCCESS;
    }


    /**
     * 积分统计-按讲师
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("statAnalysisIntegralLecturerMonth")
    public ReturnT<String> statAnalysisIntegralLecturerMonth() throws Exception {
        String param = XxlJobHelper.getJobParam();
        if (StringUtils.isNotBlank(param) && "all".equals(param)) {
            // 全部更新，不根据时间筛选
            kettleUtil.executeTrans("stat_analysis_integral_lecturer_month_all", List.of(""));
        } else {
            kettleUtil.executeTrans("stat_analysis_integral_lecturer_month", List.of(""));
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 外部培训报名统计定时任务(每天执行，按培训统计时间判断是否执行)
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("statTrainWithout")
    public ReturnT<String> statTrainWithout() throws Exception {
        kettleUtil.executeTrans("stat_train_without", List.of(""));
        return ReturnT.SUCCESS;
    }

    /**
     * 考试竞赛答题统计
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("statAnalysisCompetitionAnswerRecord")
    public ReturnT<String> statAnalysisCompetitionAnswerRecord() throws Exception {
        kettleUtil.executeTrans("stat_analysis_competition_answer_record", List.of(""));
        return ReturnT.SUCCESS;
    }

    /**
     * 同步用户任务资源
     *
     * @return 请求结果
     * @throws Exception 异常
     */
    @XxlJob("syncSysUserTaskResource")
    public ReturnT<String> syncSysUserTaskResource() throws Exception {
        kettleUtil.executeJob("sync_sys_user_task_resource", List.of(""));
        return ReturnT.SUCCESS;
    }

    /**
     * 系统标签数据迁移 （仅执行一次）
     */
    @XxlJob("sysTagDataMigrate")
    public void sysTagDataMigrate() {
        Map<String, String> dataSources = redisTemplate.<String, String>opsForHash()
            .entries(TenantRedisKeyConstant.DB_KEY);
        if (CollectionUtils.isEmpty(dataSources)) {
            log.error("data_source_is_empty,current_job:sysTagDataMigrate()");
            throw new BusinessException(ErrorNoEnum.ERR_NOT_EXISTS);
        }
        for (String dbName : dataSources.keySet()) {
            UserThreadContext.setTenantId(dbName.replace(DynamicDataSourceConstant.DB_LOOK_UP_KEY_PREFIX, ""));
            log.info("当前租户：" + UserThreadContext.getTenantId());

            // 查询课程服务的标签
            // 保存标签到user服务
            List<SysTemTagDTO> sysTemTagCourseDTOS = businessViewCourseService.getCourseTagList();
            int i = 0;
            for (SysTemTagDTO sysTemTagDTO : sysTemTagCourseDTOS) {
                sysTemTagDTO.setClientDisplay(0);
                // 旧数据默认5个为前端筛选
                if (i < 5) {
                    sysTemTagDTO.setClientDisplay(1);
                    i++;
                }
                log.info("saveSysTagCourse:" + sysTemTagDTO);
                sysTagFeign.saveSysTag(sysTemTagDTO);
            }
            // 处理课程与标签的关联关系旧数据
            List<SysTagResourceRelation> sysTagResourceCourseRelations = businessViewCourseService.getCourseTagRelation();
            sysTagFeign.insertSysTagResourceRelation(sysTagResourceCourseRelations);

            // 查询专题服务的标签
            // 保存标签到user服务
            List<SysTemTagDTO> sysTemTagSpecialDTOS = businessViewSpecialService.getSpecialTagList();
            int m = 0;
            for (SysTemTagDTO sysTemTagDTO : sysTemTagSpecialDTOS) {
                sysTemTagDTO.setClientDisplay(0);
                // 旧数据默认5个为前端筛选
                if (m < 5) {
                    sysTemTagDTO.setClientDisplay(1);
                    m++;
                }
                log.info("saveSysTagSpecial:" + sysTemTagDTO);
                sysTagFeign.saveSysTag(sysTemTagDTO);
            }
            // 处理课程与标签的关联关系旧数据
            List<SysTagResourceRelation> sysTagResourceSpecialRelations = businessViewSpecialService.getSpecialTagRelation();
            sysTagFeign.insertSysTagResourceRelation(sysTagResourceSpecialRelations);

            // 查询讲师服务知识库的标签
            // 保存标签到user服务
            List<SysTemTagDTO> sysTemTagLecturerDTOS = businessViewLecturerService.getLecturerTagList();
            sysTemTagLecturerDTOS.forEach(sysTemTagDTO -> {
                log.info("saveSysTagKnowledge:" + sysTemTagDTO);
                sysTagFeign.saveSysTag(sysTemTagDTO);
            });
            // 处理课程与标签的关联关系旧数据
            List<SysTagResourceRelation> sysTagResourceLecturerRelations = businessViewLecturerService.getLecturerTagRelation();
            sysTagFeign.insertSysTagResourceRelation(sysTagResourceLecturerRelations);

            // 维护学习项目数据
            dealWithOldProject();
        }
        UserThreadContext.remove();
    }

    /**
     * 处理需要公有读的文件 （仅执行一次）
     */
    @XxlJob("dealWithPublicReadPath")
    public void dealWithPublicReadPath() {
        Map<String, String> dataSources = redisTemplate.<String, String>opsForHash()
            .entries(TenantRedisKeyConstant.DB_KEY);
        if (CollectionUtils.isEmpty(dataSources)) {
            log.error("data_source_is_empty,current_job:sysTagDataMigrate()");
            throw new BusinessException(ErrorNoEnum.ERR_NOT_EXISTS);
        }
        for (String dbName : dataSources.keySet()) {
            UserThreadContext.setTenantId(dbName.replace(DynamicDataSourceConstant.DB_LOOK_UP_KEY_PREFIX, ""));
            log.info("当前租户：" + UserThreadContext.getTenantId());
            List<String> filePathList = dealWithPublicReadService.getNeedPublicReadFilePathList();
            fileFeign.dealWithPublicReadPath(filePathList);
        }
        UserThreadContext.remove();
    }

    private void dealWithOldProject() {
        List<Project> projectList = businessViewProjectService.getTrainProject();
        if (!CollectionUtils.isEmpty(projectList)) {
            Set<String> trainIdSet = projectList.stream().map(Project::getReferencedId)
                .filter(StringUtils::isNotBlank).collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(trainIdSet)) {
                Map<String, String> trainNameMap = businessLearnMapService.getTrainNameMap(trainIdSet);
                projectList.forEach(project -> project.setReferencedName(
                    Optional.ofNullable(trainNameMap.get(project.getReferencedId())).orElse(StringUtils.EMPTY)));
                businessViewProjectService.updateReferencedName(projectList);
            }
        }
    }
}
