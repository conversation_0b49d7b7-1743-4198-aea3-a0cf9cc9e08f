package com.wunding.learn.business.view.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.business.view.service.admin.dto.PostDevelopTreeDTO;
import com.wunding.learn.business.view.service.model.train.LearnMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p> 学习地图表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @since 2023-12-06
 */
public interface BusinessLearnMapService extends IService<LearnMap> {

    /**
     * 岗位学习地图发展信息
     *
     * @return
     */
    PostDevelopTreeDTO getPostDevelop();

    /**
     * 获取进行中学习地图数量
     *
     * @param userId    用户id
     * @param manageSet 管辖范围
     * @return 数量
     */
    Integer getInProgressLearnMapCount(String userId, Set<String> manageSet);

    /**
     * 获取培训项目名称
     *
     * @param trainIdSet 培训项目id
     * @return {@link Map}
     */
    Map<String, String> getTrainNameMap(Set<String> trainIdSet);

    /**
     * 获取需要公有读的文件地址
     *
     * @return {@link String}
     */
    List<String> getNeedPublicReadFilePathList();
}
