package com.wunding.learn.business.view.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.business.view.service.admin.dto.PostDTO;
import com.wunding.learn.business.view.service.admin.dto.PostDevelopDTO;
import com.wunding.learn.business.view.service.admin.dto.PostDevelopTreeDTO;
import com.wunding.learn.business.view.service.aspect.DS;
import com.wunding.learn.business.view.service.mapper.train.BusinessLearnMapMapper;
import com.wunding.learn.business.view.service.model.train.LearnMap;
import com.wunding.learn.business.view.service.model.train.Train;
import com.wunding.learn.business.view.service.service.BusinessLearnMapService;
import com.wunding.learn.business.view.service.service.BusinessUserService;
import jakarta.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <p> 学习地图表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @since 2023-12-06
 */
@Slf4j
@Service("businessLearnMapService")
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@DS("train")
public class BusinessLearnMapServiceImpl extends ServiceImpl<BusinessLearnMapMapper, LearnMap> implements
    BusinessLearnMapService {

    @Resource(name = "businessUserService")
    private BusinessUserService businessUserService;

    @Override
    public PostDevelopTreeDTO getPostDevelop() {
        PostDevelopTreeDTO postDevelopTreeDTO = new PostDevelopTreeDTO();
        List<PostDevelopDTO> postDevelopDTOSet = baseMapper.getPostDevelop();
        if (!CollectionUtils.isEmpty(postDevelopDTOSet)) {
            postDevelopTreeDTO.setDevelopDTOSet(postDevelopDTOSet);
            Set<String> postIdSet = new HashSet<>();
            Set<String> sourcePostId = postDevelopDTOSet.stream().map(PostDevelopDTO::getSourcePositionId)
                .collect(Collectors.toSet());
            Set<String> targetPostId = postDevelopDTOSet.stream().map(PostDevelopDTO::getTargetPositionId)
                .collect(Collectors.toSet());
            postIdSet.addAll(sourcePostId);
            postIdSet.addAll(targetPostId);
            List<PostDTO> postDTOSet = businessUserService.getPostByPostId(postIdSet);
            postDevelopTreeDTO.setPostDTOSet(postDTOSet);
        }
        return postDevelopTreeDTO;
    }

    @Override
    public Integer getInProgressLearnMapCount(String userId, Set<String> manageSet) {
        return baseMapper.getInProgressLearnMapCount(userId, manageSet);
    }

    @Override
    public Map<String, String> getTrainNameMap(Set<String> trainIdSet) {
        List<Train> list = baseMapper.getTrainList(trainIdSet);
        return list.stream().collect(Collectors.toMap(Train::getId, Train::getTrainName, (key1, key2) -> key1));
    }

    @Override
    public List<String> getNeedPublicReadFilePathList() {
        return baseMapper.getNeedPublicReadFilePathList();
    }
}
