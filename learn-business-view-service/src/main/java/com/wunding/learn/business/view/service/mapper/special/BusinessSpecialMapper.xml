<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.business.view.service.mapper.special.BusinessSpecialMapper">

    <!-- 开启二级缓存 -->
    <!--
<cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.business.view.service.mapper.special.BusinessSpecialMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.business.view.service.model.special.Special">
        <!--@Table special-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="special_name" jdbcType="VARCHAR"
          property="specialName"/>
        <result column="special_no" jdbcType="VARCHAR"
          property="specialNo"/>
        <result column="type" jdbcType="TINYINT"
          property="type"/>
        <result column="start_time" jdbcType="TIMESTAMP"
          property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP"
          property="endTime"/>
        <result column="special_desc" jdbcType="VARCHAR"
          property="specialDesc"/>
        <result column="cover_image" jdbcType="VARCHAR"
          property="coverImage"/>
        <result column="cycle_day" jdbcType="INTEGER"
          property="cycleDay"/>
        <result column="publish_time" jdbcType="TIMESTAMP"
          property="publishTime"/>
        <result column="publish_by" jdbcType="VARCHAR"
          property="publishBy"/>
        <result column="is_publish" jdbcType="TINYINT"
          property="isPublish"/>
        <result column="project_item" jdbcType="VARCHAR"
          property="projectItem"/>
        <result column="person" jdbcType="BIGINT"
          property="person"/>
        <result column="is_completion" jdbcType="TINYINT"
          property="isCompletion"/>
        <result column="is_lock_time" jdbcType="TINYINT"
          property="isLockTime"/>
        <result column="leader" jdbcType="VARCHAR"
          property="leader"/>
        <result column="mark" jdbcType="VARCHAR"
          property="mark"/>
        <result column="the_matic_class" jdbcType="VARCHAR"
          property="theMaticClass"/>
        <result column="label" jdbcType="VARCHAR"
          property="label"/>
        <result column="create_by" jdbcType="VARCHAR"
          property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP"
          property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR"
          property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP"
          property="updateTime"/>
        <result column="is_del" jdbcType="TINYINT"
          property="isDel"/>
    </resultMap>

    <!--嵌套子查询-待优化-->
    <select id="findAllSpecial" resultType="com.wunding.learn.business.view.service.model.special.SpecialDTO" useCache="false">
        select tp.id,
               tp.special_name,
               tp.create_by,
               (
                   select count(id)
                   from special_progress tpp
                   where tpp.special_id = tp.id)                         peopleNum,
               (
                   select count(id)
                   from special_progress tpp
                   where tpp.special_id = tp.id
                     and status = 1)                                finishedCount,
               (select count(id)
                from w_view_limit_user wvlu
                where wvlu.view_limit_id = wrvl.view_limit_id) viewUserCount
        from special tp
                 left join w_resource_view_limit wrvl on tp.id = wrvl.resource_id
    </select>

    <select id="getRelateSpecialNum" resultType="java.lang.Long">
        select count(s.id)
        from special s
        where s.is_del = 0
          and s.belong_type = 0
          and exists (select 1 from special_tag st where st.special_id = s.id and st.tag_id = #{tagId})
    </select>

    <select id="getSpecialTagList" resultType="com.wunding.learn.common.dto.SysTemTagDTO" useCache="false">
        select
            id
             ,'default_sys_tag_categorys' as tag_classify_id
             ,category_name as tagName
             ,0 as create_type
             ,0 as is_optional
             ,0 as is_show
             ,0 as client_display
             ,0 as is_recommend
             ,sort_no
             ,0 as default_type
             ,is_available
             ,is_del
             ,create_by
             ,create_time
             ,update_by
             ,update_time
             ,1 as is_can_delete
        from categorys where category_type = 'ThematicTag'
    </select>

    <select id="getSpecialTagRelation" resultType="com.wunding.learn.common.dto.SysTagResourceRelation">
        select st.id, st.tag_id as tagId, st.special_id as resourceId, 'special' as resourceType
        from  `special_tag` st
        inner join special s on s.id = st.special_id
        where s.is_del = 0
    </select>

    <select id="getNeedPublicReadFilePathList" resultType="java.lang.String" useCache="false">
        SELECT info_content
        FROM special_info
        WHERE info_content IS NOT NULL
          AND info_content != ''
    </select>
</mapper>
