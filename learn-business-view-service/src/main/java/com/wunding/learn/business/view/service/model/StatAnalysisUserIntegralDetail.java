package com.wunding.learn.business.view.service.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 积分统计详情表
 *
 * <AUTHOR> href="mailto:<EMAIL>">suchenyu</a>
 * @since 2022-11-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("stat_analysis_user_integral_detail")
@Schema(name = "StatAnalysisUserIntegralDetail对象", description = "积分统计详情表")
public class StatAnalysisUserIntegralDetail implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;


    /**
     * 用户id
     */
    @Schema(description = "用户id")
    @TableField("user_id")
    private String userId;


    /**
     * 关联内容名称
     */
    @Schema(description = "关联内容名称")
    @TableField("target_name")
    private String targetName;


    /**
     * 用户账号
     */
    @Schema(description = "用户账号")
    @TableField("login_name")
    private String loginName;


    /**
     * 用户全名
     */
    @Schema(description = "用户全名")
    @TableField("full_name")
    private String fullName;


    /**
     * 事件名称
     */
    @Schema(description = "事件名称")
    @TableField("event_name")
    private String eventName;


    /**
     * 获得积分
     */
    @Schema(description = "获得积分")
    @TableField("score")
    private BigDecimal score;


    /**
     * 摘要
     */
    @Schema(description = "摘要")
    @TableField("summary")
    private String summary;


    /**
     * 添加时间
     */
    @Schema(description = "添加时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;


}
