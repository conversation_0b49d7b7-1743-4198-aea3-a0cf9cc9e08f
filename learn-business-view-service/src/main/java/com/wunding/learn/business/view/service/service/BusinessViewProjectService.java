package com.wunding.learn.business.view.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.business.view.service.admin.dto.PostDevelopTreeDTO;
import com.wunding.learn.business.view.service.client.dto.LecturerDTO;
import com.wunding.learn.business.view.service.model.ProjectJoiningStaticsDTO;
import com.wunding.learn.business.view.service.model.project.Project;
import com.wunding.learn.business.view.service.model.project.ProjectDTO;
import com.wunding.learn.business.view.service.model.project.ProjectJoinUserDTO;
import com.wunding.learn.business.view.service.model.project.TrainPlanMain;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 学习项目数据源服务
 *
 * <AUTHOR>
 * @date 2022/10/13
 */
public interface BusinessViewProjectService extends IService<Project> {


    /**
     * 查找所有发布项目
     *
     * @return {@link List}<{@link Project}>
     */
    List<ProjectDTO> findAllProject();

    /**
     * 查找所有培训计划
     *
     * @return {@link List}<{@link TrainPlanMain}>
     */
    List<TrainPlanMain> findAllTrainPlan();

    /**
     * 仪表板讲师排名
     *
     * @return {@link List}<{@link LecturerDTO}>
     */
    List<LecturerDTO> dashboardLecturerRanking();

    /**
     * 岗位发展信息
     *
     * @return
     */
    PostDevelopTreeDTO getPostDevelop();

    /**
     * 查找指定月份学习项目参与信息
     *
     * @param date 要统计月度的日期 如 2024-03-21
     * @return
     */
    List<ProjectJoinUserDTO> findThisMonthProjectJoinInfo(Date date);

    /**
     * 查找指定年度学习项目参与信息
     *
     * @param date 要统计年度的日期 如 2024-03-21
     * @return
     */
    List<ProjectJoinUserDTO> findThisYearProjectJoinInfo(Date date);

    /**
     * 查找指定日期前的组织和用户信息
     *
     * @param deadlineTime 数据统计截止时间
     * @return
     */
    Map<String, Long> getOrgUserNumMap(Date deadlineTime);

    /**
     * 执行学习项目参与统计-月度
     *
     * @param date 要统计月度的日期 如 2024-03-21
     */
    void executeProjectJoinMonthStat(Date date);

    /**
     * 执行学习项目参与统计-年度
     *
     * @param date 要统计年度的日期 如 2024-03-21
     */
    void executeProjectJoinYearStat(Date date);

    /**
     * 批量保存学习项目参加数据
     *
     * @param list 数据列表
     */
    void saveProjectJoiningStat(List<ProjectJoiningStaticsDTO> list);

    /**
     * 删除学习项目参与统计数据
     *
     * @param dates 统计日期列表
     * @param type  统计类型
     */
    void removeByDate(Collection<Date> dates, Integer type);

    /**
     * 进行中的项目
     *
     * @param userId      用户id
     * @param manageSet   管辖范围
     * @param projectType 项目类型
     * @return 项目数
     */
    Integer getInProgressProjectCount(String userId, Set<String> manageSet, Integer projectType);

    /**
     * 查询班级
     *
     * @return {@link Project}
     */
    List<Project> getTrainProject();

    /**
     * 批量修改引用资源名称
     *
     * @param projectList 学习项目
     */
    void updateReferencedName(List<Project> projectList);

    /**
     * 获取需要公有读的文件地址
     *
     * @return {@link String}
     */
    List<String> getNeedPublicReadFilePathList();
}
