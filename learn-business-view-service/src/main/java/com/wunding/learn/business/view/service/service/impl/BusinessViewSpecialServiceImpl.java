package com.wunding.learn.business.view.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.business.view.service.aspect.DS;
import com.wunding.learn.business.view.service.mapper.special.BusinessSpecialMapper;
import com.wunding.learn.business.view.service.model.special.Special;
import com.wunding.learn.business.view.service.model.special.SpecialDTO;
import com.wunding.learn.business.view.service.service.BusinessViewSpecialService;
import com.wunding.learn.common.dto.SysTagResourceRelation;
import com.wunding.learn.common.dto.SysTemTagDTO;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * 专题数据源
 *
 * <AUTHOR>
 * @date 2022/10/18
 */
@Service
@DS("special")
public class BusinessViewSpecialServiceImpl extends ServiceImpl<BusinessSpecialMapper, Special> implements
    BusinessViewSpecialService {

    @Override
    public List<SpecialDTO> findAllSpecial() {
        return baseMapper.findAllSpecial();
    }

    @Override
    public Long getRelateSpecialNum(String tagId) {
        return baseMapper.getRelateSpecialNum(tagId);
    }

    @Override
    public List<SysTemTagDTO> getSpecialTagList() {
        return baseMapper.getSpecialTagList();
    }

    @Override
    public List<SysTagResourceRelation> getSpecialTagRelation() {
        return baseMapper.getSpecialTagRelation();
    }

    @Override
    public List<String> getNeedPublicReadFilePathList() {
        return baseMapper.getNeedPublicReadFilePathList();
    }
}
