package com.wunding.learn.business.view.service.admin.query;

import com.wunding.learn.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.format.annotation.DateTimeFormat.ISO;

/**
 * 兑换记录查询
 *
 * <AUTHOR>
 * @date 2022/11/5 13:22
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class ExchangeRecordQuery extends BaseEntity {

    /**
     * 用户id
     */
    @Schema(description = "用户id")
    private List<String> userId;

    /**
     * 开始时间
     */
    @Schema(description = "开始日期")
    @DateTimeFormat(iso = ISO.DATE_TIME)
    private Date startTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束日期")
    @DateTimeFormat(iso = ISO.DATE_TIME)
    private Date endTime;
}
