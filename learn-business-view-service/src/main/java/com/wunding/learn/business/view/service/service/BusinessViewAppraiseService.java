package com.wunding.learn.business.view.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.business.view.service.model.appraise.Appraise;
import java.util.Set;

/**
 * <p> 评价表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">xiao8</a>
 * @since 2022-08-08
 */
public interface BusinessViewAppraiseService extends IService<Appraise> {

    /**
     * 获取进行中的评价
     *
     * @param currentUserId     用id
     * @param managerAreaOrgIds 管辖范围
     * @return 数量
     */
    Integer getInProgressAppraiseCount(String currentUserId, Set<String> managerAreaOrgIds);
}
