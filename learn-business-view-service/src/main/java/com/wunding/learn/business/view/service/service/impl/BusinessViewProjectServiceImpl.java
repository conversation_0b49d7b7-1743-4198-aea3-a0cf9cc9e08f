package com.wunding.learn.business.view.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.wunding.learn.business.view.service.admin.dto.PostDTO;
import com.wunding.learn.business.view.service.admin.dto.PostDevelopDTO;
import com.wunding.learn.business.view.service.admin.dto.PostDevelopTreeDTO;
import com.wunding.learn.business.view.service.aspect.DS;
import com.wunding.learn.business.view.service.client.dto.LecturerDTO;
import com.wunding.learn.business.view.service.mapper.project.BusinessProjectMapper;
import com.wunding.learn.business.view.service.model.ProjectJoiningStaticsDTO;
import com.wunding.learn.business.view.service.model.project.OrgUserNumDTO;
import com.wunding.learn.business.view.service.model.project.Project;
import com.wunding.learn.business.view.service.model.project.ProjectDTO;
import com.wunding.learn.business.view.service.model.project.ProjectJoinUserDTO;
import com.wunding.learn.business.view.service.model.project.ProjectJoinUserEvalByTimeQuery;
import com.wunding.learn.business.view.service.model.project.ProjectJoinUserEvalQuery;
import com.wunding.learn.business.view.service.model.project.ProjectJoinUserOtherDataDTO;
import com.wunding.learn.business.view.service.model.project.ProjectJoinUserOtherDataQuery;
import com.wunding.learn.business.view.service.model.project.TrainPlanMain;
import com.wunding.learn.business.view.service.service.BusinessEvaluationService;
import com.wunding.learn.business.view.service.service.BusinessLecturerService;
import com.wunding.learn.business.view.service.service.BusinessUserService;
import com.wunding.learn.business.view.service.service.BusinessViewProjectService;
import com.wunding.learn.common.util.date.DateUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;


/**
 * 学习项目数据源
 *
 * <AUTHOR>
 * @date 2022/10/13
 */
@Service("businessViewProjectService")
@Slf4j
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@DS("project")
public class BusinessViewProjectServiceImpl extends ServiceImpl<BusinessProjectMapper, Project> implements
    BusinessViewProjectService {

    @Resource
    private BusinessLecturerService businessLecturerService;
    @Resource
    private BusinessEvaluationService businessEvaluationService;
    @Resource
    @SuppressWarnings("rawtypes")
    private RedisTemplate redisTemplate;

    @Override
    public List<ProjectDTO> findAllProject() {
        return baseMapper.findAllProject();
    }

    @Override
    public List<TrainPlanMain> findAllTrainPlan() {
        return baseMapper.findAllTrainPlan();
    }

    @Override
    @DS("lecturer")
    public List<LecturerDTO> dashboardLecturerRanking() {
        return baseMapper.dashboardLecturerRanking();
    }

    @Resource(name = "businessUserService")
    private BusinessUserService businessUserService;

    @Override
    public PostDevelopTreeDTO getPostDevelop() {
        PostDevelopTreeDTO postDevelopTreeDTO = new PostDevelopTreeDTO();
        List<PostDevelopDTO> postDevelopDTOSet = baseMapper.getPostDevelop();
        if (!CollectionUtils.isEmpty(postDevelopDTOSet)) {
            postDevelopTreeDTO.setDevelopDTOSet(postDevelopDTOSet);
            Set<String> postIdSet = new HashSet<>();
            Set<String> sourcePostId = postDevelopDTOSet.stream().map(PostDevelopDTO::getSourcePositionId)
                .collect(Collectors.toSet());
            Set<String> targetPostId = postDevelopDTOSet.stream().map(PostDevelopDTO::getTargetPositionId)
                .collect(Collectors.toSet());
            postIdSet.addAll(sourcePostId);
            postIdSet.addAll(targetPostId);
            List<PostDTO> postDTOSet = businessUserService.getPostByPostId(postIdSet);
            postDevelopTreeDTO.setPostDTOSet(postDTOSet);
        }
        return postDevelopTreeDTO;
    }

    @Override
    public List<ProjectJoinUserDTO> findThisMonthProjectJoinInfo(Date date) {
        return baseMapper.findThisMonthProjectJoinInfo(date);
    }

    @Override
    public List<ProjectJoinUserDTO> findThisYearProjectJoinInfo(Date date) {
        return baseMapper.findThisYearProjectJoinInfo(date);
    }


    /**
     * 批量保存学习项目参加数据
     *
     * @param list
     */
    @Override
    public void saveProjectJoiningStat(List<ProjectJoiningStaticsDTO> list) {
        baseMapper.saveProjectJoiningStat(list);
    }

    @Override
    public void removeByDate(Collection<Date> dates, Integer type) {
        baseMapper.removeByDate(dates, type);
    }

    @Override
    public Map<String, Long> getOrgUserNumMap(Date deadlineTime) {

        log.info("获取学习项目参与统计人员组织信息，统计截止日期：" + deadlineTime);

        List<OrgUserNumDTO> userNumList = baseMapper.getOrgUserNumList(deadlineTime);
        if (CollectionUtils.isEmpty(userNumList)) {
            return new HashMap<>();
        }
        return userNumList.stream()
            .collect(Collectors.toMap(OrgUserNumDTO::getOrgId, OrgUserNumDTO::getUserNum, (v1, v2) -> v1));
    }


    @Override
    public void
    executeProjectJoinMonthStat(Date date) {

        // 月度统计截止日期
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MONTH, 1);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        Date deadlineTime = cal.getTime();

        // 计算组织人数
        Map<String, Long> orgUserNumMap = getOrgUserNumMap(deadlineTime);

        if (orgUserNumMap.size() > 0) {
            executeProjectJoinMonthStat(orgUserNumMap, 1, date, deadlineTime);
        }
    }

    @Override
    public void executeProjectJoinYearStat(Date date) {

        // 年度统计截止日期
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.YEAR, 1);
        cal.set(Calendar.DAY_OF_YEAR, 1);
        Date deadlineTime = cal.getTime();

        // 计算组织人数
        Map<String, Long> orgUserNumMap = getOrgUserNumMap(deadlineTime);
        if (orgUserNumMap.size() > 0) {
            executeProjectJoinYearStat(orgUserNumMap, 2, date, deadlineTime);
        }
    }

    private void executeProjectJoinMonthStat(Map<String, Long> orgUserNumMap, Integer type, Date date,
        Date deadlineTime) {
        // 取学习项目参与统计
        List<ProjectJoinUserDTO> projectJoinInfo = findThisMonthProjectJoinInfo(date);
        if (CollectionUtils.isEmpty(projectJoinInfo)) {
            return;
        }
        removeOldData(projectJoinInfo, type);
        statByOrgAndCategory(projectJoinInfo, orgUserNumMap, type, deadlineTime);
        statByOrg(projectJoinInfo, orgUserNumMap, type, deadlineTime);
    }

    private void executeProjectJoinYearStat(Map<String, Long> orgUserNumMap, Integer type, Date date,
        Date deadlineTime) {
        // 取学习项目参与统计
        List<ProjectJoinUserDTO> projectJoinInfo = findThisYearProjectJoinInfo(date);
        if (CollectionUtils.isEmpty(projectJoinInfo)) {
            return;
        }
        removeOldData(projectJoinInfo, type);
        statByOrgAndCategory(projectJoinInfo, orgUserNumMap, type, deadlineTime);
        statByOrg(projectJoinInfo, orgUserNumMap, type, deadlineTime);
    }

    private void executeBody(Map<OrgInfo, List<ProjectJoinUserDTO>> listMap, Map<String, Long> orgUserNumMap,
        Integer type, Date deadlineTime) {
        ArrayList<ProjectJoiningStaticsDTO> list = new ArrayList<>();
        for (Entry<OrgInfo, List<ProjectJoinUserDTO>> entry : listMap.entrySet()) {
            // 求该组织人数
            Long orgUserNum = orgUserNumMap.get(entry.getKey().orgId);
            //计算用户参与信息
            ProjectJoiningStaticsDTO joiningStatics = computeUserInfo(entry.getValue());
            String orgId = entry.getKey().getOrgId();
            joiningStatics.setOrgId(orgId);
            joiningStatics.setMonth(entry.getKey().getTime());
            String trainCategoryId = entry.getKey().getTrainCategoryId();
            joiningStatics.setTrainCategoryId(trainCategoryId);
            joiningStatics.setType(type);
            Map<String, Set<String>> stringSetMap = entry.getValue().stream().collect(
                Collectors.groupingBy(ProjectJoinUserDTO::getProjectId,
                    Collectors.mapping(ProjectJoinUserDTO::getUserId, Collectors.toSet())));
            // 构建查询条件
            ProjectJoinUserEvalByTimeQuery query = buildQuery(deadlineTime, stringSetMap);

            log.info("executeBody queries:" + JsonUtil.objToJson(query));

            // 计算用户评估信息
            List<ProjectJoinUserOtherDataDTO> evalList = businessEvaluationService.findProjectJoinUserEvalInfo(query);

            log.info("executeBody evalList:" + JsonUtil.objToJson(evalList));
            log.info("executeBody evalList size:" + evalList.size());

            // 平均满意度
            handleSatisfaction(joiningStatics, evalList);

            // 计算平均课时
            ProjectJoinUserOtherDataQuery studyQuery = new ProjectJoinUserOtherDataQuery()
                .setProjectIds(stringSetMap.keySet())
                .setDeadlineTime(deadlineTime);
            List<ProjectJoinUserOtherDataDTO> studyHoursInfo = businessLecturerService.findProjectJoinUserStudyHoursInfo(
                studyQuery);

            log.info("计算平均课时：" + JsonUtil.objToJson(studyHoursInfo));

            if (!CollectionUtils.isEmpty(studyHoursInfo)) {
                // 计算总课时(分钟)
                BigDecimal sumHours = studyHoursInfo.stream().map(ProjectJoinUserOtherDataDTO::getValue)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

                joiningStatics.setTeachHours(
                    sumHours.divide(BigDecimal.valueOf(orgUserNum).multiply(BigDecimal.valueOf(60)), 2,
                        RoundingMode.HALF_UP));

                log.info("计算平均课时：总课时 " + sumHours + "分钟，部门总人数 " + orgUserNum
                    + "，平均课时（小时） " + joiningStatics.getTeachHours());
            }
            // 计算覆盖率
            if (Objects.isNull(joiningStatics.getUserNum())
                || Objects.equals(0, joiningStatics.getUserNum())
                || Objects.isNull(orgUserNum)
                || Objects.equals(0, orgUserNum)
            ) {
                joiningStatics.setCoverRate(BigDecimal.ZERO);
            } else {
                joiningStatics.setCoverRate(BigDecimal.valueOf(joiningStatics.getUserNum())
                    .divide(BigDecimal.valueOf(orgUserNum), 2, RoundingMode.HALF_UP));
            }
            joiningStatics.setType(type);

            Date startTime = DateUtil.getLastMonthStartTime(new Date());
            String timeStr = DateUtil.getYmStr(startTime);

            //组装数据 一个月针对一个月份只生成一条数据
            String uniqueKey = orgId + "/" + timeStr;
            if (trainCategoryId != null) {
                uniqueKey = orgId + "/" + trainCategoryId + "/" + timeStr;
            }
            joiningStatics.setUniqueKey(uniqueKey);

            list.add(joiningStatics);
        }
        List<List<ProjectJoiningStaticsDTO>> listList = Lists.partition(list, 500);
        listList.forEach(this::saveProjectJoiningStat);
    }

    /**
     * 处理满意度
     *
     * @param joiningStatics 统计数据
     * @param evalList       评估列表
     */
    private void handleSatisfaction(ProjectJoiningStaticsDTO joiningStatics,
        List<ProjectJoinUserOtherDataDTO> evalList) {
        if (!CollectionUtils.isEmpty(evalList)) {
            int num = 0;
            BigDecimal totalScore = new BigDecimal(0);
            for (ProjectJoinUserOtherDataDTO projectEvalAvgScore : evalList) {
                if (projectEvalAvgScore.getValue() != null) {
                    num++;
                    totalScore = totalScore.add(projectEvalAvgScore.getValue());
                }
            }

            // 平均满意度
            joiningStatics.setSatisfaction(
                num != 0 ? totalScore.divide(new BigDecimal(num), 2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
        }
    }

    /**
     * 生成查询条件
     *
     * @param deadlineTime 截止时间
     * @param stringSetMap 字符串集映射
     * @return {@link ProjectJoinUserEvalByTimeQuery }
     */
    private ProjectJoinUserEvalByTimeQuery buildQuery(Date deadlineTime,
        Map<String, Set<String>> stringSetMap) {
        ArrayList<ProjectJoinUserEvalQuery> queries = new ArrayList<>();
        for (Entry<String, Set<String>> setEntry : stringSetMap.entrySet()) {
            queries.add(
                new ProjectJoinUserEvalQuery().setProjectId(setEntry.getKey()).setUserIds(setEntry.getValue()));
        }

        return new ProjectJoinUserEvalByTimeQuery()
            .setDeadlineTime(deadlineTime)
            .setQueryList(queries);
    }

    private void removeOldData(List<ProjectJoinUserDTO> projectJoinInfo, Integer type) {
        Set<Date> timeSet = projectJoinInfo.stream().map(ProjectJoinUserDTO::getTime).collect(Collectors.toSet());
        // 删除旧数据
        removeByDate(timeSet, type);
    }

    private void statByOrgAndCategory(List<ProjectJoinUserDTO> projectJoinInfo,
        Map<String, Long> orgUserNumMap, Integer type, Date deadlineTime) {
        Set<OrgInfo> orgInfos = projectJoinInfo.stream()
            .map(data -> new OrgInfo().setOrgId(data.getOrgId()).setOrgLevelPath(data.getOrgLevelPath())
                .setTime(data.getTime())).collect(Collectors.toSet());
        List<ProjectJoinUserDTO> joinUserDTOS = projectJoinInfo.stream()
            .filter(data -> StringUtils.isNotBlank(data.getTrainCategoryId())).collect(Collectors.toList());

        Set<OrgInfo> orgInfoSet = orgInfos.stream().flatMap(orgInfo -> joinUserDTOS.stream()
            .filter(data -> data.getOrgLevelPath().startsWith(orgInfo.getOrgLevelPath()))
            .map(data -> new OrgInfo().setOrgId(orgInfo.getOrgId()).setTrainCategoryId(data.getTrainCategoryId())
                .setTime(data.getTime()).setOrgLevelPath(orgInfo.getOrgLevelPath()))).collect(Collectors.toSet());

        Map<OrgInfo, List<ProjectJoinUserDTO>> listMap = new HashMap<>(orgInfoSet.size());
        for (OrgInfo orgInfo : orgInfoSet) {
            listMap.put(orgInfo,
                joinUserDTOS.stream().filter(data -> data.getOrgLevelPath().startsWith(orgInfo.getOrgLevelPath())
                        && Objects.equals(data.getTime(), orgInfo.getTime())
                        && Objects.equals(data.getTrainCategoryId(), orgInfo.getTrainCategoryId()))
                    .collect(Collectors.toList()));
        }
        executeBody(listMap, orgUserNumMap, type, deadlineTime);
    }

    private void statByOrg(List<ProjectJoinUserDTO> projectJoinInfo, Map<String, Long> orgUserNumMap, Integer type,
        Date deadlineTime) {
        Set<OrgInfo> orgInfos = projectJoinInfo.stream()
            .map(data -> new OrgInfo().setOrgId(data.getOrgId()).setOrgLevelPath(data.getOrgLevelPath())
                .setTime(data.getTime())).collect(Collectors.toSet());
        Map<OrgInfo, List<ProjectJoinUserDTO>> listMap = new HashMap<>(orgInfos.size());
        for (OrgInfo orgInfo : orgInfos) {
            listMap.put(orgInfo,
                projectJoinInfo.stream().filter(data -> data.getOrgLevelPath().startsWith(orgInfo.getOrgLevelPath())
                    && Objects.equals(data.getTime(), orgInfo.getTime())).collect(Collectors.toList()));
        }
        executeBody(listMap, orgUserNumMap, type, deadlineTime);
    }

    /**
     * 计算用户参与信息
     *
     * @param list
     * @return
     */
    private ProjectJoiningStaticsDTO computeUserInfo(List<ProjectJoinUserDTO> list) {
        // 参与场次
        long projectCount = list.stream().map(ProjectJoinUserDTO::getProjectId)
            .filter(StringUtils::isNotBlank).count();
        // 参与人次
        long userTimes = list.stream().map(ProjectJoinUserDTO::getUserId).filter(StringUtils::isNotBlank).count();
        // 参与人数
        long userNum = list.stream().map(ProjectJoinUserDTO::getUserId).filter(StringUtils::isNotBlank).distinct()
            .count();
        return new ProjectJoiningStaticsDTO().setProjectNum(projectCount).setUserNum(userNum).setUserTimes(userTimes);
    }


    @Data
    @Accessors(chain = true)
    class OrgInfo {

        private String orgId;

        private String orgLevelPath;

        /**
         * 培训分类id
         */
        // 默认为空
        private String trainCategoryId = "";

        private Date time;
    }

    @Override
    public Integer getInProgressProjectCount(String userId, Set<String> manageSet, Integer projectType) {
        return baseMapper.getInProgressProjectCount(userId, manageSet, projectType);
    }

    @Override
    public List<Project> getTrainProject() {
        return baseMapper.getTrainProject();
    }

    @Override
    public void updateReferencedName(List<Project> projectList) {
        List<List<Project>> list = splitList(projectList, 1000);
        list.forEach(project -> baseMapper.updateReferencedName(project));
    }

    @Override
    public List<String> getNeedPublicReadFilePathList() {
        return baseMapper.getNeedPublicReadFilePathList();
    }

    public static <T> List<List<T>> splitList(List<T> list, int batchSize) {
        List<List<T>> result = new ArrayList<>();
        int size = list.size();

        for (int i = 0; i < size; i += batchSize) {
            int end = Math.min(size, i + batchSize); // 确保不会超出列表范围
            result.add(new ArrayList<>(list.subList(i, end)));
        }

        return result;
    }
}
