<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.business.view.service.mapper.project.BusinessProjectMapper">
    <!-- 开启二级缓存 -->
    <!--
<cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.business.view.service.mapper.project.BusinessProjectMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.business.view.service.model.project.Project">
        <!--@Table project-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="pro_name" jdbcType="VARCHAR"
          property="proName"/>
        <result column="pro_no" jdbcType="VARCHAR"
          property="proNo"/>
        <result column="type" jdbcType="TINYINT"
          property="type"/>
        <result column="start_time" jdbcType="TIMESTAMP"
          property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP"
          property="endTime"/>
        <result column="pro_desc" jdbcType="VARCHAR"
          property="proDesc"/>
        <result column="cover_image" jdbcType="VARCHAR"
          property="coverImage"/>
        <result column="cycle_day" jdbcType="BIGINT"
          property="cycleDay"/>
        <result column="publish_time" jdbcType="TIMESTAMP"
          property="publishTime"/>
        <result column="publish_by" jdbcType="VARCHAR"
          property="publishBy"/>
        <result column="is_publish" jdbcType="TINYINT"
          property="isPublish"/>
        <result column="is_available" jdbcType="TINYINT"
          property="isAvailable"/>
        <result column="project_item" jdbcType="VARCHAR"
          property="projectItem"/>
        <result column="person" jdbcType="BIGINT"
          property="person"/>
        <result column="is_completion" jdbcType="TINYINT"
          property="isCompletion"/>
        <result column="pro_method" jdbcType="VARCHAR"
          property="proMethod"/>
        <result column="is_lock_time" jdbcType="TINYINT"
          property="isLockTime"/>
        <result column="leader" jdbcType="VARCHAR"
          property="leader"/>
        <result column="address" jdbcType="VARCHAR"
          property="address"/>
        <result column="mark" jdbcType="VARCHAR"
          property="mark"/>
        <result column="room" jdbcType="VARCHAR"
          property="room"/>
        <result column="the_matic_class" jdbcType="VARCHAR"
          property="theMaticClass"/>
        <result column="flag" jdbcType="TINYINT"
          property="flag"/>
        <result column="adaptive_terminal" jdbcType="VARCHAR"
          property="adaptiveTerminal"/>
        <result column="label" jdbcType="VARCHAR"
          property="label"/>
        <result column="create_by" jdbcType="VARCHAR"
          property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP"
          property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR"
          property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP"
          property="updateTime"/>
        <result column="is_del" jdbcType="TINYINT"
          property="isDel"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
             , pro_name
             , pro_no
             , type
             , start_time
             , end_time
             , pro_desc
             , cover_image
             , cycle_day
             , publish_time
             , publish_by
             , is_publish
             , is_available
             , project_item
             , person
             , is_completion
             , pro_method
             , is_lock_time
             , leader
             , address
             , mark
             , room
             , the_matic_class
             , flag
             , adaptive_terminal
             , label
             , create_by
             , create_time
             , update_by
             , update_time
             , is_del
    </sql>

    <!--嵌套子查询-待优化-->
    <select id="findAllProject" resultType="com.wunding.learn.business.view.service.model.project.ProjectDTO"
      useCache="false">
        select tp.id,
               tp.pro_name,
               tp.is_publish,
               tp.create_by,
               tp.flag,
               (select count(id)
                from project_progress tpp
                where tpp.pro_id = tp.id)                      peopleNum,
               (select count(id)
                from project_progress tpp
                where tpp.pro_id = tp.id
                  and status = 1)                              finishedCount,
               (select count(id)
                from task_progress tpp
                where tpp.pro_id = tp.id
                  and tp.is_del = 0
                  and is_finish = 1)                           taskNum,
               (select count(id)
                from task_progress tpp
                where tpp.pro_id = tp.id
                  and is_finish = 1)                           finishedTaskCount,
               (select count(id)
                from w_view_limit_user wvlu
                where wvlu.view_limit_id = wrvl.view_limit_id) viewUserCount,
               ttppr.id                                        trainPlanId
        from project tp
                 left join train_plan_prj_relation ttppr on
            tp.id = ttppr.tp_id and ttppr.is_del = 0
                 left join w_resource_view_limit wrvl on tp.id = wrvl.resource_id
    </select>

    <select id="findAllTrainPlan"
      resultType="com.wunding.learn.business.view.service.model.project.TrainPlanMain" useCache="false">
        select id, create_by, train_status
        from project_train_plan_main
        where is_del = 0
          and TRAIN_STATUS = 1
    </select>

    <select id="dashboardLecturerRanking"
      resultType="com.wunding.learn.business.view.service.client.dto.LecturerDTO" useCache="false">
        select t.id,
               t.name,
               t.commonScore,
               t.type,
               t.user_id userId
        from (select l.id,
                     l.name,
                     l.eval_score as commonScore,
                     l.type,
                     l.user_id
              from lecturer l
              where l.is_del = 0
                and status = 1) t
        order by t.commonScore desc
    </select>

    <select id="getPostDevelop" resultType="com.wunding.learn.business.view.service.admin.dto.PostDevelopDTO">
        select distinct source_position_id,
                        target_position_id
        from project_position
        where is_publish = 1
    </select>

    <select id="findThisMonthProjectJoinInfo" parameterType="java.util.Date"
      resultType="com.wunding.learn.business.view.service.model.project.ProjectJoinUserDTO">
        select c.org_id,
        d.level_path org_level_path,
        a.user_id,
        a.pro_id project_id,
        b.train_category_id,
        cast(date_add(a.start_time, interval - day(a.start_time) + 1 day) as DATE) time
        from project_progress a
        inner join project b on a.pro_id = b.id
        inner join sys_user c on a.user_id = c.id
        inner join sys_org d on c.org_id = d.id
        where b.is_publish = 1
        and b.is_del = 0
        and b.referenced_id = ''
        and b.type = 0
        and b.project_type = 0
        and c.is_del = 0
        and d.is_del = 0
        and a.start_time <![CDATA[ >= ]]> LAST_day(date_sub(#{date}, interval 1 month)) + interval 1 day
        and a.start_time <![CDATA[ <= ]]> LAST_day(#{date}) + interval 1 day - interval 1 second
        order by b.id
    </select>

    <select id="findThisYearProjectJoinInfo" parameterType="java.util.Date"
      resultType="com.wunding.learn.business.view.service.model.project.ProjectJoinUserDTO">
        select c.org_id,
        d.level_path org_level_path,
        a.user_id,
        a.pro_id project_id,
        b.train_category_id,
        str_to_date(concat(year(a.start_time), "-01-01 00:00:00"), '%Y-%m-%d %H:%i:%s') time
        from project_progress a
        inner join project b on a.pro_id = b.id
        inner join sys_user c on a.user_id = c.id
        inner join sys_org d on c.org_id = d.id
        where b.is_publish = 1
        and b.is_del = 0
        and b.referenced_id = ''
        and b.type = 0
        and b.project_type = 0
        and c.is_del = 0
        and d.is_del = 0
        and a.start_time <![CDATA[ >= ]]> str_to_date(concat(year(#{date}), '-01-01 00:00:00'), '%Y-%m-%d %H:%i:%s')
        and a.start_time <![CDATA[ <= ]]> str_to_date(concat(year(#{date}), '-12-31 23:59:59'), '%Y-%m-%d %H:%i:%s')
        order by b.id
    </select>

    <select id="getOrgUserNumList" parameterType="java.util.Date"
      resultType="com.wunding.learn.business.view.service.model.project.OrgUserNumDTO">
        select o.id orgId, sum(i.num) userNum
        from sys_org o
        inner join (select a.id, a.level_path, count(1) num
        from sys_org a
        inner join sys_user b on a.id = b.org_id
        where a.is_del = 0
        and b.is_del = 0
        and #{deadlineTime} > b.create_time
        group by a.id, a.level_path) i on i.level_path like concat(o.level_path, '%')
        group by o.id
    </select>

    <insert id="saveProjectJoiningStat" parameterType="list"
      useGeneratedKeys="true">
        insert into project_joining_statics (org_id, month, train_category_id,
        project_num, user_num, user_times, cover_rate, satisfaction, teach_hours,
        type, unique_key)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.orgId},
            #{item.month},
            #{item.trainCategoryId},
            #{item.projectNum},
            #{item.userNum},
            #{item.userTimes},
            #{item.coverRate},
            #{item.satisfaction},
            #{item.teachHours},
            #{item.type},
            #{item.uniqueKey})
        </foreach>
    </insert>

    <delete id="removeByDate">
        delete
        from project_joining_statics where month in
        <foreach collection="dates" open="(" item="date" separator="," close=")">
            #{date}
        </foreach>
        and type = #{type}
    </delete>

    <select id="getInProgressProjectCount" resultType="java.lang.Integer" useCache="false">
        select count(tp.id)
        from project tp
        inner join sys_org g on g.id = tp.org_id
        where tp.is_del = 0
        and tp.flag = 0
        and tp.is_publish = 1
        and tp.type = 0
        and NOW() BETWEEN tp.start_time AND tp.end_time
        <if test="projectType != null">
            and tp.project_type = #{projectType}
        </if>
        <if test="userManageAreaOrgId != null and userManageAreaOrgId.size() != 0">
            and (tp.create_by = #{userId}
            <if test="userManageAreaOrgId != null and userManageAreaOrgId.size > 0">
                or
                <foreach collection="userManageAreaOrgId" open="(" close=")" item="levelPath" separator="or">
                    g.level_path like concat(#{levelPath}, '%')
                </foreach>
            </if>
            )
        </if>
    </select>

    <select id="getTrainProject" resultType="com.wunding.learn.business.view.service.model.project.Project"
      useCache="false">
        select id, referenced_id
        from project
        where referenced_type = 1
    </select>

    <update id="updateReferencedName">
        <foreach collection="projectList" item="project" separator=";">
            UPDATE project
            SET referenced_name = #{project.referencedName}
            WHERE id = #{project.id}
        </foreach>
    </update>

    <select id="getNeedPublicReadFilePathList" resultType="java.lang.String" useCache="false">
        SELECT info_content
        FROM pro_info
        WHERE info_content IS NOT NULL
          AND info_content != ''
    </select>
</mapper>
