package com.wunding.learn.business.view.service.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.business.view.service.admin.dto.DepartUserDTO;
import com.wunding.learn.business.view.service.admin.dto.OnlineNumDTO;
import com.wunding.learn.business.view.service.admin.dto.PostDTO;
import com.wunding.learn.business.view.service.aspect.DS;
import com.wunding.learn.business.view.service.mapper.user.BusinessUserMapper;
import com.wunding.learn.business.view.service.model.StatNewLoginNumMonth;
import com.wunding.learn.business.view.service.model.user.Para;
import com.wunding.learn.business.view.service.model.user.User;
import com.wunding.learn.business.view.service.service.BusinessUserService;
import com.wunding.learn.user.api.dto.OrgDTO;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 用户服务实现
 *
 * <AUTHOR>
 * @date 2022/10/14
 */
@Service("businessUserService")
@DS("user")
public class BusinessUserServiceImpl extends ServiceImpl<BusinessUserMapper, User> implements
    BusinessUserService {

    @Override
    public List<OrgDTO> findAll() {
        return baseMapper.findAll();
    }

    @Override
    public List<String> findAllLoginUserId() {
        return baseMapper.findAllLoginUserId();
    }

    @Override
    public List<String> findFirstLoginUserByDateRange(String startTime, String endTime) {
        return baseMapper.findFirstLoginUserByDateRange(startTime, endTime);
    }

    @Override
    public List<StatNewLoginNumMonth> findNewLoginMonthStats(Date startDate, Date endDate) {
        return baseMapper.findNewLoginMonthStats(startDate, endDate);
    }

    @Override
    public Map<String, Integer> getNewLoginNum(Collection<String> orgIds) {
        List<OnlineNumDTO> onlineNumList = baseMapper.getOnlineNum(orgIds);
        return onlineNumList.stream()
            .collect(Collectors.toMap(OnlineNumDTO::getAccessTime, OnlineNumDTO::getNewLoginNum));
    }

    @Override
    public Set<String> getChildOrg(String orgId) {
        return baseMapper.getChildOrg(orgId);
    }

    @Override
    public Map<String, Integer> getTotalDepartUser(Set<String> orgIds) {
        List<DepartUserDTO> departUserDTO = baseMapper.getTotalDepartUser(orgIds);
        return departUserDTO.stream().filter(item -> item.getDeptTotalUser() > 0)
            .collect(Collectors.toMap(DepartUserDTO::getOrgId, DepartUserDTO::getDeptTotalUser));
    }

    @Override
    public Map<String, Integer> getNewLoginNumGroupById(Collection<String> orgIds, String timeStr) {
        List<OnlineNumDTO> onlineNumList = baseMapper.getOnlineNumGroupById(orgIds, timeStr);
        return onlineNumList.stream()
            .collect(Collectors.toMap(OnlineNumDTO::getOrgId, OnlineNumDTO::getNewLoginNum));
    }

    @Override
    public List<String> getManageOrgName(String userId) {
        return baseMapper.getManageOrgName(userId);
    }

    @Override
    public Set<String> getChildrenId(String orgId) {
        return baseMapper.getChildrenId(orgId);
    }

    @Override
    public String getPostName(String postId) {
        return baseMapper.getPostName(postId);
    }

    @Override
    public String getRoleName(String roleId) {
        return baseMapper.getRoleName(roleId);
    }

    @Override
    public List<String> getUserIdByLevelPaths(Collection<String> levelPaths) {
        return baseMapper.getUserIdByLevelPaths(levelPaths);
    }

    @Override
    public Long getRoleCount() {
        return baseMapper.getRoleCount();
    }


    @Override
    public List<PostDTO> getPostByPostId(Collection<String> postIdList) {
        if (CollectionUtils.isEmpty(postIdList)) {
            return new ArrayList<>();
        }
        return baseMapper.getPostByPostId(postIdList);
    }

    @Override
    public OrgDTO getOrgById(String orgId) {
        return baseMapper.getOrgById(orgId);
    }

    @Override
    public Para getParaGetCode(String code) {
        return baseMapper.getParaGetCode(code);
    }

    @Override
    public Integer getExpertCount() {
        return baseMapper.getExpertCount();
    }

    @Override
    public List<String> getNeedPublicReadFilePathList() {
        return baseMapper.getNeedPublicReadFilePathList();
    }
}
