package com.wunding.learn.business.view.service.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wunding.learn.business.view.service.client.dto.LoginDashboardDTO;
import com.wunding.learn.business.view.service.model.StatMonthData;
import com.wunding.learn.business.view.service.model.StatMonthVisit;
import com.wunding.learn.business.view.service.model.StatUserStat;
import com.wunding.learn.business.view.service.service.DashboardCommonService;
import com.wunding.learn.business.view.service.service.IStatMonthDataService;
import com.wunding.learn.business.view.service.service.IStatMonthVisitService;
import com.wunding.learn.business.view.service.service.IStatUserStatService;
import com.wunding.learn.business.view.service.service.LoginDashboardService;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.user.api.dto.OrgDTO;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.service.UserFeign;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 登录仪表板服务impl
 *
 * <AUTHOR>
 * @date 2022/10/11
 */
@Service
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class LoginDashboardServiceImpl implements LoginDashboardService {

    private final UserFeign userFeign;
    private final DashboardCommonService dashboardCommonService;
    private final IStatMonthDataService statMonthDataService;
    private final IStatUserStatService statUserStatService;
    private final IStatMonthVisitService statMonthVisitService;

    @Override
    public LoginDashboardDTO login(String orgId) {
        OrgDTO sysOrg = dashboardCommonService.checkManageArea(orgId);
        orgId = sysOrg.getId();
        LocalDate today = LocalDate.now();
        LoginDashboardDTO loginDashboardVO = new LoginDashboardDTO();
        loginDashboardVO.setOrgId(orgId);
        loginDashboardVO.setDataDate(today.format(DateTimeFormatter.ISO_LOCAL_DATE).concat(" 00:00:00"));

        // 查询loginData相关数据
        List<Integer> months = dashboardCommonService.lastYearMonth();
        loginDashboardVO.setLoginData(fillLoginData(orgId, months));

        // 查询组织登录排行
        loginDashboardVO.setOrganLoginRanking(fillOrganLoginRanking(sysOrg));

        // 查询个人登录排行榜
        loginDashboardVO.setUserLoginRanking(fillUserLoginRanking(sysOrg));

        // 登录时段分析
        loginDashboardVO.setHourVisitStat(hourVisitStat(orgId));
        return loginDashboardVO;
    }

    /**
     * 填充loginData数据
     */
    private LoginDashboardDTO.LoginData fillLoginData(String orgId, List<Integer> months) {
        // 构造返回数据的初始化
        LoginDashboardDTO.LoginData data = new LoginDashboardDTO.LoginData();
        List<LoginDashboardDTO.LoginData.LoginDataItem> items = new ArrayList<>(12);
        for (Integer month : months) {
            LoginDashboardDTO.LoginData.LoginDataItem item = new LoginDashboardDTO.LoginData.LoginDataItem();
            item.setMonth(month);
            items.add(item);
        }
        data.setItems(items);
        // 查询13个月数据，最后一个月放的是最新的数据（跨越时最后一个月没有数据，前一个月数据就是最新的）。
        List<StatMonthData> list = statMonthDataService.list(
            Wrappers.lambdaQuery(StatMonthData.class).ge(StatMonthData::getHandleDate, months.get(0))
                .eq(StatMonthData::getOrgId, orgId).orderByAsc(StatMonthData::getHandleDate));
        // 如果数据库没有数据就直接返回
        if (list.isEmpty()) {
            return data;
        }
        // 不为空，用数据库数据填充
        for (LoginDashboardDTO.LoginData.LoginDataItem item : items) {
            for (StatMonthData l : list) {
                if (item.getMonth() == l.getHandleDate()) {
                    item.setVisitUserCount(l.getVisitUserCount());
                    item.setUserVisitCount(l.getUserVisitCount());
                    item.setOldUserCount(l.getOldUserCount());
                    item.setLoginUserCount(l.getLoginUserCount());
                    item.setActiveUserRatio(l.getActiveUserRatio() / 100.0);
                    break;
                }
            }
        }
        // 如果是当月第一天
        if (LocalDate.now().getDayOfMonth() == 1) {
            data.setVisitUserCount(items.get(11).getVisitUserCount());
            data.setUserVisitCount(items.get(11).getUserVisitCount());
            data.setOldUserCount(items.get(11).getOldUserCount());
            data.setActiveUserRatio(items.get(11).getActiveUserRatio());
            return data;
        }
        // 查询出来的最后一条数据，日期是否等于当月日期，如果等于，则把数据放进去
        LocalDate now = LocalDate.now();
        int currentMonth = now.getYear() * 100 + now.getMonthValue();
        StatMonthData monthData = list.get(list.size() - 1);
        if (monthData.getHandleDate() == currentMonth) {
            data.setVisitUserCount(monthData.getVisitUserCount());
            data.setUserVisitCount(monthData.getUserVisitCount());
            data.setOldUserCount(monthData.getOldUserCount());
            data.setActiveUserRatio(monthData.getActiveUserRatio() / 100.0);
        }
        return data;
    }

    /**
     * 填充组织登录排行榜
     *
     * @param sysOrg 查询的部门信息
     */
    private List<LoginDashboardDTO.OrganLoginRankingItem> fillOrganLoginRanking(OrgDTO sysOrg) {

        Map<String, OrgDTO> childMap = dashboardCommonService.getOrgChildMap(sysOrg);
        LocalDate today = LocalDate.now();
        LocalDate yesterday = today.plusDays(-1);
        int queryMonth;
        if (1 == today.getDayOfMonth()) {
            // 每月1日，查询的上个月的数据
            queryMonth = yesterday.getYear() * 100 + yesterday.getMonthValue();
        } else {
            // 查询当月数据
            queryMonth = today.getYear() * 100 + today.getMonthValue();
        }
        // 查询前10部门排行数据
        List<StatMonthData> monthDataList = statMonthDataService.list(
            Wrappers.lambdaQuery(StatMonthData.class).eq(StatMonthData::getHandleDate, queryMonth)
                .in(StatMonthData::getOrgId, childMap.keySet()).orderByDesc(StatMonthData::getActiveUserRatio));
        List<LoginDashboardDTO.OrganLoginRankingItem> organLoginRanking = new ArrayList<>();
        Set<String> allChildOrgId = new HashSet<>(childMap.keySet());
        // 填充数据到vo
        monthDataList.forEach(monthData -> {
            LoginDashboardDTO.OrganLoginRankingItem organLoginRankingItem = new LoginDashboardDTO.OrganLoginRankingItem();
            organLoginRankingItem.setOrgId(monthData.getOrgId());
            organLoginRankingItem.setOrgName(childMap.get(monthData.getOrgId()).getOrgName());
            organLoginRankingItem.setLoginUserCount(monthData.getLoginUserCount());
            organLoginRankingItem.setVisitUserCount(monthData.getVisitUserCount());
            organLoginRankingItem.setActiveUserRatio(monthData.getActiveUserRatio() / 100.0);
            organLoginRankingItem.setUserVisitCount(monthData.getUserVisitCount());
            allChildOrgId.remove(monthData.getOrgId());
            organLoginRanking.add(organLoginRankingItem);
        });
        // 月报表数据只保存有数据的，没数据的部门，作为填充补满10条数据
        for (String id : allChildOrgId) {
            LoginDashboardDTO.OrganLoginRankingItem organLoginRankingItem = new LoginDashboardDTO.OrganLoginRankingItem();
            organLoginRankingItem.setOrgId(id);
            organLoginRankingItem.setOrgName(childMap.get(id).getOrgName());
            organLoginRanking.add(organLoginRankingItem);
        }
        return organLoginRanking;
    }

    /**
     * 个人登录排行榜
     *
     * @param sysOrg 部门信息
     * @return 排行榜信息
     */
    private List<LoginDashboardDTO.UserLoginRankingItem> fillUserLoginRanking(OrgDTO sysOrg) {
        Page<StatUserStat> page = statUserStatService.page(new Page<>(1, 10, false),
            Wrappers.lambdaQuery(StatUserStat.class).likeRight(StatUserStat::getOrgLevelPath, sysOrg.getLevelPath())
                .orderByDesc(StatUserStat::getVisitCount));
        List<LoginDashboardDTO.UserLoginRankingItem> items = new ArrayList<>(10);
        LoginDashboardDTO.UserLoginRankingItem item;
        for (StatUserStat userStat : page.getRecords()) {
            item = new LoginDashboardDTO.UserLoginRankingItem();
            item.setUserId(userStat.getId());
            item.setUserName(userStat.getName());
            item.setOrgName(
                dashboardCommonService.convertOrgName(sysOrg, userStat.getOrgId(), userStat.getOrgFullName()));
            item.setVisitCount(userStat.getVisitCount());
            UserDTO userById = userFeign.getUserById(userStat.getId());
            if (null == userById || StringUtil.isEmpty(userById.getAvatar())) {
                item.setAvatar("");
            } else {
                item.setAvatar(userById.getAvatar());
            }
            items.add(item);
        }
        return items;

    }

    private List<Long> hourVisitStat(String orgId) {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        int yesterdayMonth = yesterday.getYear() * 100 + yesterday.getMonthValue();
        StatMonthVisit monthVisit = statMonthVisitService.getOne(
            Wrappers.lambdaQuery(StatMonthVisit.class).eq(StatMonthVisit::getOrgId, orgId)
                .eq(StatMonthVisit::getMonth, yesterdayMonth));
        if (null == monthVisit) {
            // 没数据是默认给0
            ArrayList<Long> list = new ArrayList<>(24);
            for (int i = 0; i < 24; i++) {
                list.add(0L);
            }
            return list;
        }
        List<Long> hourVisitStat = new ArrayList<>(24);
        hourVisitStat.add(monthVisit.getHour00());
        hourVisitStat.add(monthVisit.getHour01());
        hourVisitStat.add(monthVisit.getHour02());
        hourVisitStat.add(monthVisit.getHour03());
        hourVisitStat.add(monthVisit.getHour04());
        hourVisitStat.add(monthVisit.getHour05());
        hourVisitStat.add(monthVisit.getHour06());
        hourVisitStat.add(monthVisit.getHour07());
        hourVisitStat.add(monthVisit.getHour08());
        hourVisitStat.add(monthVisit.getHour09());
        hourVisitStat.add(monthVisit.getHour10());
        hourVisitStat.add(monthVisit.getHour11());
        hourVisitStat.add(monthVisit.getHour12());
        hourVisitStat.add(monthVisit.getHour13());
        hourVisitStat.add(monthVisit.getHour14());
        hourVisitStat.add(monthVisit.getHour15());
        hourVisitStat.add(monthVisit.getHour16());
        hourVisitStat.add(monthVisit.getHour17());
        hourVisitStat.add(monthVisit.getHour18());
        hourVisitStat.add(monthVisit.getHour19());
        hourVisitStat.add(monthVisit.getHour20());
        hourVisitStat.add(monthVisit.getHour21());
        hourVisitStat.add(monthVisit.getHour22());
        hourVisitStat.add(monthVisit.getHour23());
        return hourVisitStat;
    }
}
