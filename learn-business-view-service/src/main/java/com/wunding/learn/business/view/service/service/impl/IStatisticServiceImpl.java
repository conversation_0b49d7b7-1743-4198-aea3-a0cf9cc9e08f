package com.wunding.learn.business.view.service.service.impl;

import static com.wunding.learn.common.category.service.impl.CategorysServiceImpl.DEFAULT_SYS_TAG_CATEGORYS;

import com.github.pagehelper.PageInfo;
import com.wunding.learn.business.view.service.admin.stat.AccessStat;
import com.wunding.learn.business.view.service.model.StatAccessDay;
import com.wunding.learn.business.view.service.model.StatAccessMonth;
import com.wunding.learn.business.view.service.model.StatBaseTime;
import com.wunding.learn.business.view.service.model.StatNewLoginNumMonth;
import com.wunding.learn.business.view.service.model.StatTimeRegion;
import com.wunding.learn.business.view.service.service.BusinessViewCourseService;
import com.wunding.learn.business.view.service.service.IStatAccessDayService;
import com.wunding.learn.business.view.service.service.IStatAccessMonthService;
import com.wunding.learn.business.view.service.service.IStatBaseTimeService;
import com.wunding.learn.business.view.service.service.IStatNewLoginNumMonthService;
import com.wunding.learn.business.view.service.service.IStatTimeRegionService;
import com.wunding.learn.business.view.service.service.IStatisticService;
import com.wunding.learn.business.view.service.service.IUserCourseTagService;
import com.wunding.learn.business.view.service.service.BusinessViewLecturerService;
import com.wunding.learn.business.view.service.service.BusinessViewSpecialService;
import com.wunding.learn.business.view.service.service.BusinessUserService;
import com.wunding.learn.common.dto.user.SysTagStatDTO;
import com.wunding.learn.common.i18n.util.I18nUtil;
import com.wunding.learn.common.query.SysTagStatQuery;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.dto.AbstractExportDataDTO;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.user.api.service.SysTagFeign;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * 统计的一个中间汇总
 *
 * <AUTHOR>
 * @date 2022/10/19
 */
@Slf4j
@Service("businessViewStatisticService")
public class IStatisticServiceImpl implements IStatisticService {

    @Resource
    private IStatAccessDayService statAccessDayService;
    @Resource
    private IStatTimeRegionService statTimeRegionService;
    @Resource
    private IStatAccessMonthService statAccessMonthService;
    @Resource
    private IStatBaseTimeService statBaseTimeService;
    @Resource
    private IStatNewLoginNumMonthService statNewLoginNumMonthService;
    @Resource(name = "businessUserService")
    private BusinessUserService businessBusinessUserService;
    @Resource
    private SysTagFeign sysTagFeign;

    @Resource(name = "businessUserCourseTagService")
    private IUserCourseTagService businessUserCourseTagService;
    @Resource(name = "businessViewCourseService")
    private BusinessViewCourseService businessViewCourseService;
    @Resource
    private BusinessViewSpecialService businessViewSpecialService;
    @Resource
    private BusinessViewLecturerService businessViewLecturerService;
    @Resource
    private ExportComponent exportComponent;

    @Override
    public void saveTimeRegionStat(List<StatTimeRegion> list) {
        statTimeRegionService.saveBatch(list);
    }

    @Override
    public void saveAccessDayStat(List<AccessStat> list) {
        List<StatAccessDay> statAccessDayList = list.stream().map(
            item -> new StatAccessDay().setAccessDate(item.getAccessDate()).setAccessNum(item.getAccessNum())
                .setOnlineNum(item.getOnlineNum()).setOrgId(item.getOrgId())).collect(Collectors.toList());
        statAccessDayService.saveBatch(statAccessDayList);
    }

    @Override
    public void saveAccessMonthStat(List<AccessStat> list) {
        List<StatAccessMonth> statAccessMonthList = list.stream().map(
            item -> new StatAccessMonth().setAccessDate(item.getAccessDate()).setAccessNum(item.getAccessNum())
                .setOnlineNum(item.getOnlineNum()).setOrgId(item.getOrgId())).collect(Collectors.toList());
        statAccessMonthService.saveBatch(statAccessMonthList);
    }

    @Override
    public void saveSysBaseTime(StatBaseTime setAccessMonth) {
        statBaseTimeService.save(setAccessMonth);
    }

    @Override
    public List<StatNewLoginNumMonth> getNewLoginMonthStats(Date startDate, Date endDate) {
        // 从用户数据源获取数据
        return businessBusinessUserService.findNewLoginMonthStats(startDate, endDate);
    }

    @Override
    public void saveNewLoginMonthStat(List<StatNewLoginNumMonth> list) {
        statNewLoginNumMonthService.saveBatch(list);
    }

    @Override
    public int getAccessMonthNum(String orgId, Date lastMonth) {
        return statAccessDayService.getAccessMonthNum(orgId, lastMonth);
    }

    @Override
    public PageInfo<SysTagStatDTO> getSysTagStatList(SysTagStatQuery sysTagStatQuery) {
        log.info("sysTagStatQuery: " + sysTagStatQuery);
        PageInfo<SysTagStatDTO> sysTagStatDTO = sysTagFeign.getSysTagStatList(sysTagStatQuery);
        log.info("sysTagStatDTO: " + sysTagStatDTO.getList());
        sysTagStatDTO.getList().forEach(item -> {
            item.setTagHoldNum(businessUserCourseTagService.getUserTagHoldNum(item.getId()));
            item.setTagCollectNum(businessUserCourseTagService.getTagCollectNum(item.getId()));
            item.setRelateCourseNum(businessViewCourseService.getRelateCourseNum(item.getId()));
            item.setRelateSpecialNum(businessViewSpecialService.getRelateSpecialNum(item.getId()));
            item.setRelateKnowledgeNum(businessViewLecturerService.getRelateKnowledgeNum(item.getId()));
            if (StringUtils.equals(item.getTagClassifyId(), DEFAULT_SYS_TAG_CATEGORYS)) {
                item.setTagClassifyName(I18nUtil.getMessage(item.getTagClassifyName()));
            }
        });
        return sysTagStatDTO;
    }

    @Override
    @Async
    public void exportData(SysTagStatQuery sysTagStatQuery) {
        IExportDataDTO exportDataDTO =
            new AbstractExportDataDTO<IStatisticService, SysTagStatDTO>(sysTagStatQuery) {

                @Override
                protected IStatisticService getBean() {
                    return SpringUtil.getBean("businessViewStatisticService", IStatisticService.class);
                }

                @Override
                protected PageInfo<SysTagStatDTO> getPageInfo() {
                    return getBean().getSysTagStatList(sysTagStatQuery);
                }

                @Override
                public ExportBizType getType() {
                    return ExportBizType.SysTemTagStatAnalysis;
                }

                @Override
                public String getFileName() {
                    return ExportFileNameEnum.SysTemTagStatAnalysis.getType();
                }
            };

        exportComponent.exportRecord(exportDataDTO);
    }
}
