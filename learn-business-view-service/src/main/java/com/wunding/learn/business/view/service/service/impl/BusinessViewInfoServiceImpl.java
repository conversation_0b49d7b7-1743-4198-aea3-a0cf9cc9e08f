package com.wunding.learn.business.view.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.business.view.service.aspect.DS;
import com.wunding.learn.business.view.service.mapper.info.BusinessInfoMapper;
import com.wunding.learn.business.view.service.model.info.Info;
import com.wunding.learn.business.view.service.service.BusinessViewInfoService;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p> 资讯表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2022-08-02
 */
@Slf4j
@Service("businessViewInfoService")
@DS("info")
public class BusinessViewInfoServiceImpl extends ServiceImpl<BusinessInfoMapper, Info> implements
    BusinessViewInfoService {

    @Override
    public List<String> getNeedPublicReadFilePathList() {
        return baseMapper.getNeedPublicReadFilePathList();
    }
}
