package com.wunding.learn.business.view.service.model.info;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 资讯表
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2022-08-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("info")
@Schema(name = "Info对象", description = "资讯表")
public class Info implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 资讯ID
     */
    @Schema(description = "资讯ID")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;


    /**
     * 资讯标题
     */
    @Schema(description = "资讯标题")
    @TableField("title")
    private String title;


    /**
     * 副标题
     */
    @Schema(description = "副标题")
    @TableField("sub_title")
    private String subTitle;


    /**
     * 资讯分类ID
     */
    @Schema(description = "资讯分类ID")
    @TableField("info_cate_id")
    private String infoCateId;


    /**
     * 所属栏目
     */
    @Schema(description = "所属栏目")
    @TableField("item_id")
    private String itemId;


    /**
     * 资讯关键词
     */
    @Schema(description = "资讯关键词")
    @TableField("key_word")
    private String keyWord;


    /**
     * 资讯正文 建议存储路径，对应一个txt文件
     */
    @Schema(description = "资讯正文 建议存储路径，对应一个txt文件")
    @TableField("info_content")
    private String infoContent;


    /**
     * 资讯简介
     */
    @Schema(description = "资讯简介")
    @TableField("info_intro")
    private String infoIntro;


    /**
     * 资讯作者
     */
    @Schema(description = "资讯作者")
    @TableField("author")
    private String author;


    /**
     * 资讯来源
     */
    @Schema(description = "资讯来源")
    @TableField("source")
    private String source;


    /**
     * 资讯类型 对应数据字典表NewsType
     */
    @Schema(description = "资讯类型 对应数据字典表NewsType")
    @TableField("content_type")
    private String contentType;


    /**
     * 组织id
     */
    @Schema(description = "组织id")
    @TableField("org_id")
    private String orgId;


    /**
     * 浏览量
     */
    @Schema(description = "浏览量")
    @TableField("click_number")
    private Long clickNumber;


    /**
     * 评论数
     */
    @Schema(description = "评论数")
    @TableField("comment_number")
    private Long commentNumber;


    /**
     * 下载量
     */
    @Schema(description = "下载量")
    @TableField("download_number")
    private Long downloadNumber;


    /**
     * 点赞数
     */
    @Schema(description = "点赞数")
    @TableField("vote_number")
    private Long voteNumber;


    /**
     * 收藏数
     */
    @Schema(description = "收藏数")
    @TableField("favorite_number")
    private Long favoriteNumber;


    /**
     * 分享数
     */
    @Schema(description = "分享数")
    @TableField("share_number")
    private Long shareNumber;


    /**
     * 是否允许评论
     */
    @Schema(description = "是否允许评论")
    @TableField("is_comment")
    private Integer isComment;


    /**
     * 是否允许下载
     */
    @Schema(description = "是否允许下载")
    @TableField("is_download")
    private Integer isDownload;


    /**
     * 是否允许点赞
     */
    @Schema(description = "是否允许点赞")
    @TableField("is_vote")
    private Integer isVote;


    /**
     * 是否允许收藏
     */
    @Schema(description = "是否允许收藏")
    @TableField("is_favorite")
    private Integer isFavorite;


    /**
     * 是否允许分享
     */
    @Schema(description = "是否允许分享")
    @TableField("is_allow_share")
    private Integer isAllowShare;


    /**
     * 是否头条
     */
    @Schema(description = "是否头条")
    @TableField("is_first")
    private Integer isFirst;


    /**
     * 是否置顶
     */
    @Schema(description = "是否置顶")
    @TableField("is_top")
    private Integer isTop;


    /**
     * 是否推荐
     */
    @Schema(description = "是否推荐")
    @TableField("is_recommend")
    private Integer isRecommend;


    /**
     * 是否共享
     */
    @Schema(description = "是否共享")
    @TableField("is_share")
    private Integer isShare;


    /**
     * 是否培训班
     */
    @Schema(description = "是否培训班")
    @TableField("is_train")
    private Integer isTrain;


    /**
     * 是否发布
     */
    @Schema(description = "是否发布")
    @TableField("is_publish")
    private Integer isPublish;


    /**
     * 发布者
     */
    @Schema(description = "发布者")
    @TableField("publish_by")
    private String publishBy;


    /**
     * 发布时间
     */
    @Schema(description = "发布时间")
    @TableField("publish_time")
    private Date publishTime;


    /**
     * 大图
     */
    @Schema(description = "大图")
    @TableField("large_image")
    private String largeImage;


    /**
     * 中图
     */
    @Schema(description = "中图")
    @TableField("minddle_image")
    private String minddleImage;


    /**
     * 小图
     */
    @Schema(description = "小图")
    @TableField("small_image")
    private String smallImage;


    /**
     * 显示顺序
     */
    @Schema(description = "显示顺序")
    @TableField("sort_no")
    private BigDecimal sortNo;


    /**
     * 是否启用
     */
    @Schema(description = "是否启用")
    @TableField("is_avaliable")
    private Integer isAvaliable;


    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    @TableField("is_del")
    @TableLogic(value = "0", delval = "1")
    private Integer isDel;

    /**
     * 下发方式：0 部分可见 1仅创建者可见 2所有人可见
     */
    @Schema(description = "下发方式：0 部分可见 1仅创建者可见 2所有人可见")
    @TableField("view_type")
    private Integer viewType;


    /**
     * 新增人
     */
    @Schema(description = "新增人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;


    /**
     * 新增时间
     */
    @Schema(description = "新增时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 修改人
     */
    @Schema(description = "修改人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;


    /**
     * 最后编辑时间
     */
    @Schema(description = "最后编辑时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 企业ID
     */
    @Schema(description = "企业ID")
    @TableField("customer_id")
    private String customerId;


    /**
     * 资讯格式 0在线编辑 1附件上传
     */
    @Schema(description = "资讯格式 0在线编辑 1附件上传")
    @TableField("infomation_source")
    private Integer infomationSource;

    /**
     * 视频转换状态  1 转换中  2 转换完成 3 转换失败
     */
    @Schema(description = "视频转换状态  1 转换中  2 转换完成 3 转换失败")
    @TableField("transform_status")
    private Integer transformStatus;

    /**
     * 上传文件Files表格ID
     */
    @Schema(description = "上传文件Files表格ID")
    @TableField("upload_files_id")
    private String uploadFilesId;

    /**
     * 上传文件的文件名称
     */
    @Schema(description = "上传文件的文件名称")
    @TableField("file_name")
    private String fileName;

    /**
     * 上传文件类型
     */
    @Schema(description = "上传文件类型")
    @TableField("file_type")
    private String fileType;


    /**
     * 上传文件简介
     */
    @Schema(description = "上传文件简介")
    @TableField("descriptions")
    private String descriptions;


}
