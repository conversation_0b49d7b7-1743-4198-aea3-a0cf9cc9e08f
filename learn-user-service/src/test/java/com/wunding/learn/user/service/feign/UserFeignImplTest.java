package com.wunding.learn.user.service.feign;

import com.wunding.learn.user.api.service.UserFeign;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.wildfly.common.Assert;

@SpringBootTest
class UserFeignImplTest {

    @Resource
    private UserFeign userFeign;

    @Test
    void exec() {
        userFeign.bindSysUser("1", "test", "2");
        Assert.assertTrue(true);
    }
}