package com.wunding.learn.user.service.admin.rest;

import com.wunding.learn.common.bean.Result;
import com.wunding.learn.user.service.admin.dto.LongShortLinkRelationDTO;
import com.wunding.learn.user.service.admin.dto.LongShortLinkRelationQuery;
import com.wunding.learn.user.service.service.ILongShortLinkRelationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
* <p>
  * 长短链接关系表 前端控制器
  *
  * </p>
*
* <AUTHOR>
* @date 2025-06-16
*/
@RestController
@RequestMapping("${module.user.contentPath:/}")
@Tag(description = "长短链接关系表相关接口", name = "LongShortLinkRelationRest")
public class LongShortLinkRelationRest {

	@Resource
	private ILongShortLinkRelationService longShortLinkRelationService;

	@PostMapping("/getShortLink")
	@Operation(operationId = "getShortLink", summary = "根据长链接获取短链接", description = "根据长链接获取短链接")
	public Result<LongShortLinkRelationDTO> getShortLink(@RequestBody LongShortLinkRelationQuery longShortLinkRelationQuery, HttpServletRequest request) {
		return Result.success(longShortLinkRelationService.getShortLink(longShortLinkRelationQuery, request));
	}

}
