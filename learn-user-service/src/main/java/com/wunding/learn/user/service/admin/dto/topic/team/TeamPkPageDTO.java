package com.wunding.learn.user.service.admin.dto.topic.team;

import com.wunding.learn.file.api.dto.NamePath;
import com.wunding.learn.user.service.admin.dto.topic.PageAttributeDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 组队赛pk页dto
 *
 * <AUTHOR>
 * @date 2023/09/28
 */
@Data
@Accessors(chain = true)
@Schema(name = "TeamPkPageDTO", description = "组队赛pk页dto对象")
public class TeamPkPageDTO {

    @Schema(description = "当前页id")
    private String id;

    @Schema(description = "主题id")
    private String topicId;

    @Schema(description = "成员A属性")
    private PageAttributeDTO teamPkMemberA;

    @Schema(description = "成员B")
    private PageAttributeDTO teamPkMemberB;

    @Schema(description = "背景图")
    private PageAttributeDTO teamPkBackGroundImg;

    @Schema(description = "进度条")
    private PageAttributeDTO teamPkProgressBar;

    @Schema(description = "答题区背景图片")
    private PageAttributeDTO teamPkAnswerBackground;

    @Schema(description = "选项按钮,多个元素存在值使用逗号分隔,例如 4个圆角则圆角字段以逗号分隔 8,8,8,8 其他字段以此类推")
    private PageAttributeDTO teamPkOptionButton;

    @Schema(description = "音乐播放状态")
    private PageAttributeDTO teamPkMusicPlay;

    @Schema(description = "音乐暂停状态")
    private PageAttributeDTO teamPkMusicStop;

    @Schema(description = "背景颜色")
    private String backgroundColor;

    @Schema(description = "背景音乐")
    private NamePath backgroundMusic;

}
