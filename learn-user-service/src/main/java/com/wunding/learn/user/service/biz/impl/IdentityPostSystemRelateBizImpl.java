package com.wunding.learn.user.service.biz.impl;

import static com.wunding.learn.common.util.string.StringUtil.newId;

import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.util.string.StringPool;
import com.wunding.learn.user.api.dto.IdentityPostSystemRelateDTO;
import com.wunding.learn.user.api.enums.IdentityCategoryEnum;
import com.wunding.learn.user.service.admin.dto.UserIdentityDTO;
import com.wunding.learn.user.service.admin.query.IdentityPostSystemRelateQuery;
import com.wunding.learn.user.service.admin.query.IdentityPostSystemRelateQuery.IdentityPostSystemRelateInfo;
import com.wunding.learn.user.service.admin.query.IdentityPostTreePanoramaQuery;
import com.wunding.learn.user.service.biz.IIdentityPostSystemRelateBiz;
import com.wunding.learn.user.service.client.dto.IdentityPostTreeDTO;
import com.wunding.learn.user.service.dto.IdentityPostDTO;
import com.wunding.learn.user.service.model.IdentityPostSystemData;
import com.wunding.learn.user.service.model.IdentityPostSystemRelate;
import com.wunding.learn.user.service.model.IdentityPostSystemStructure;
import com.wunding.learn.user.service.model.NewIdentity;
import com.wunding.learn.user.service.service.IIdentityPostSystemDataService;
import com.wunding.learn.user.service.service.IIdentityPostSystemRelateService;
import com.wunding.learn.user.service.service.IIdentityPostSystemStructureService;
import com.wunding.learn.user.service.service.INewIdentityService;
import com.wunding.learn.user.service.service.IUserIdentityService;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * <p> 岗位体系岗位关联表 业务服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @since 2024-02-02
 */
@Slf4j
@Service("identityPostSystemRelateBiz")
public class IdentityPostSystemRelateBizImpl implements IIdentityPostSystemRelateBiz {

    @Resource
    private IIdentityPostSystemRelateService identityPostSystemRelateService;
    @Resource
    private IIdentityPostSystemStructureService identityPostSystemStructureService;
    @Resource
    private IIdentityPostSystemDataService identityPostSystemDataService;
    @Resource
    private INewIdentityService newIdentityService;
    @Resource
    private IUserIdentityService userIdentityService;

    @Override
    public List<IdentityPostSystemRelateDTO> queryListByPostId(String postId) {
        Optional<IdentityPostSystemRelate> relate = identityPostSystemRelateService.lambdaQuery()
            .eq(IdentityPostSystemRelate::getPostId, postId).last("limit 1").oneOpt();
        // 岗位体系结构
        List<IdentityPostSystemStructure> structureList = identityPostSystemStructureService.queryCurrentStructureBelong(
            relate.map(IdentityPostSystemRelate::getStructureId).orElse(""));

        //体系被删除可能就不存在了
        Optional<String> structureOpt = structureList.stream().findFirst()
            .map(item -> StringUtils.split(item.getLevelPath(), StringPool.SLASH)[0]);

        // 岗位体系数据
        List<IdentityPostSystemData> dataList = identityPostSystemDataService.queryAll();
        List<IdentityPostSystemRelateDTO> result = identityPostSystemRelateService.queryRelateDTOListByPostId(
            postId, structureList, dataList);
        result.forEach(item -> {
            item.setBelongStructureId(structureOpt.orElse(StringPool.EMPTY));
            if (!Objects.equals(item.getBelongStructureId(), item.getStructureId())
                && StringUtils.isEmpty(item.getId())) {
                item.setId(newId());
            }
        });

        return result;
    }

    @Override
    public List<IdentityPostDTO> queryPostList(IdentityPostSystemRelateQuery query) {
        List<String> ids = identityPostSystemRelateService.queryPostIdList(query);
        if (!ids.isEmpty()) {
            return newIdentityService.lambdaQuery().in(NewIdentity::getId, ids)
                .eq(NewIdentity::getIsAvailable, GeneralJudgeEnum.CONFIRM.getValue())
                // 避免告警,出处 https://github.com/baomidou/mybatis-plus/issues/5572
                .orderBy(true, true, NewIdentity::getSortNo)
                .orderBy(true, false, NewIdentity::getCreateTime)
                .orderBy(true, true, NewIdentity::getId)
                .list().stream().map(newIdentity -> {
                    IdentityPostDTO dto = new IdentityPostDTO();
                    BeanUtils.copyProperties(newIdentity, dto);
                    return dto;
                }).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public List<String> queryPostIdList(IdentityPostSystemRelateQuery query) {
        return identityPostSystemRelateService.queryPostIdList(query);
    }

    @Override
    public IdentityPostTreeDTO getIdentityPostTreeInfo() {
        IdentityPostTreeDTO dto = new IdentityPostTreeDTO();
        // 查询当前登录用户岗位信息
        UserIdentityDTO userIdentityDTO = userIdentityService.getUserOnlyIdentity(UserThreadContext.getUserId(),
            IdentityCategoryEnum.POST.getCategoryId());
        dto.setPostId(userIdentityDTO.getIdentityId());
        dto.setPostName(userIdentityDTO.getIdentityName());
        // 查询当前登录用户岗位所属体系
        List<IdentityPostSystemRelateDTO> relateDTOList = queryMountListByPostId(dto.getPostId());
        // 体系名称（可挂载节点数据全路径名称）
        Optional<String> mountSystemName = relateDTOList.stream()
            .filter(relateDTO -> Objects.equals(GeneralJudgeEnum.CONFIRM.getValue(), relateDTO.getIsMount()))
            .findFirst()
            .map(IdentityPostSystemRelateDTO::getDataLevelPathName)
            .map(item -> item.replaceAll("(^/+)|(/+$)", ""));
        dto.setSystemName(mountSystemName.orElse(StringPool.EMPTY));

        // 本岗发展岗位列表 查询条件构造
        IdentityPostSystemRelateQuery relateQuery = new IdentityPostSystemRelateQuery();
        List<IdentityPostSystemRelateInfo> relateInfoList = relateDTOList.stream().map(relate -> {
            IdentityPostSystemRelateInfo relateInfo = new IdentityPostSystemRelateInfo();
            relateInfo.setStructureId(relate.getStructureId());
            relateInfo.setDataId(relate.getDataId());
            return relateInfo;
        }).collect(Collectors.toList());
        relateQuery.setList(relateInfoList);
        dto.setList(this.queryPostList(relateQuery));
        return dto;
    }

    /**
     * 查询当前登录用户岗位所属体系的一直到挂载的结构（挂载节点的所有父级数据）
     *
     * @param postId post-id
     * @return {@link List}<{@link IdentityPostSystemRelateDTO}>
     */
    private List<IdentityPostSystemRelateDTO> queryMountListByPostId(String postId) {
        Optional<IdentityPostSystemRelate> relate = identityPostSystemRelateService.lambdaQuery()
            .eq(IdentityPostSystemRelate::getPostId, postId).last("limit 1").oneOpt();
        if (relate.isEmpty()) {
            return Collections.emptyList();
        }

        IdentityPostSystemStructure structure = identityPostSystemStructureService.getById(
            relate.map(IdentityPostSystemRelate::getStructureId).orElse(""));

        String[] split = StringUtils.split(structure.getLevelPath(), StringPool.SLASH);
        // 查询该体系内的挂载岗位的结构节点在哪里
        Optional<IdentityPostSystemStructure> mountOpt = identityPostSystemStructureService.lambdaQuery().likeRight(
                IdentityPostSystemStructure::getLevelPath, StringPool.SLASH + split[0] + StringPool.SLASH)
            .eq(IdentityPostSystemStructure::getIsMount, 1).oneOpt();

        if (mountOpt.isEmpty()) {
            return Collections.emptyList();
        }

        List<String> structureIdList = Arrays.stream(
            StringUtils.split(mountOpt.map(IdentityPostSystemStructure::getLevelPath).orElse(""),
                StringPool.SLASH)).collect(Collectors.toList());

        List<IdentityPostSystemStructure> structureList = identityPostSystemStructureService.lambdaQuery()
            .in(IdentityPostSystemStructure::getId, structureIdList)
            .ne(IdentityPostSystemStructure::getParentId, StringPool.EMPTY).list();
        // 岗位体系数据
        List<IdentityPostSystemData> dataList = identityPostSystemDataService.queryAll();
        return identityPostSystemRelateService.queryRelateDTOListByPostId(postId, structureList, dataList);

    }

    @Override
    public List<IdentityPostDTO> queryPostList(IdentityPostTreePanoramaQuery query) {
        // 查询岗位体系结构信息
        IdentityPostSystemStructure structure = identityPostSystemStructureService.getById(query.getStructureId());
        String[] structureIds = structure.getLevelPath().split("/");
        // 查询岗位体系数据信息
        IdentityPostSystemData data = identityPostSystemDataService.getById(query.getDataId());
        String[] dataIds = data.getLevelPath().split("/");

        List<IdentityPostSystemRelateInfo> relateInfoList = new ArrayList<>();
        for (int i = 1; i < dataIds.length; i++) {
            IdentityPostSystemRelateInfo relateInfo = new IdentityPostSystemRelateInfo();
            relateInfo.setStructureId(structureIds[i + 1]);
            relateInfo.setDataId(dataIds[i]);
            relateInfoList.add(relateInfo);
        }
        // 本岗发展岗位列表
        IdentityPostSystemRelateQuery relateQuery = new IdentityPostSystemRelateQuery();
        relateQuery.setList(relateInfoList);

        return this.queryPostList(relateQuery);
    }

}
