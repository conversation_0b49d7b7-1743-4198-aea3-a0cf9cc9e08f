package com.wunding.learn.user.service.client.rest;

import com.wunding.learn.common.bean.Result;
import com.wunding.learn.user.service.service.ILongShortLinkRelationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import java.net.URI;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">guoyong</a>
 * @since 2025/6/18 14:27
 */
@RestController
@RequestMapping("${module.user.contentPath:/}")
@Tag(description = "长短链接关系学员端接口", name = "LongShortLinkRelationClientRest")
public class LongShortLinkRelationClientRest {
	@Resource
	private ILongShortLinkRelationService longShortLinkRelationService;

	@GetMapping("/SL")
	@Operation(operationId = "getLongLink", summary = "请求重定向", description = "请求重定向")
	public ResponseEntity<Void> redirectRequest(@Parameter(description = "短连接Id")String k) {
		return ResponseEntity.status(HttpStatus.FOUND)
			.location(URI.create(longShortLinkRelationService.redirectRequest(k)))
			.build();
	}

	@GetMapping("/getLongLinkByShortLink")
	@Operation(operationId = "getLongLinkByShortLink", summary = "获取长连接", description = "获取长连接")
	public Result<String> getLongLinkByShortLink(@Parameter(description = "短连接Id")String k) {
		return Result.success(longShortLinkRelationService.redirectRequest(k));
	}

}
