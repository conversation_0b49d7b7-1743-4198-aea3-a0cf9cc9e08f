package com.wunding.learn.user.service.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 系统更新公告表
 *
 * <AUTHOR> href="mailto:<EMAIL>">gy</a>
 * @since 2024-11-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_notice")
@Schema(name = "SysNotice", description = "系统更新公告表")
public class SysNotice implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * id
     */
    @Schema(description = "id")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;


    /**
     * 公告标题
     */
    @Schema(description = "公告标题")
    @TableField("title")
    private String title;


    /**
     * 公告内容
     */
    @Schema(description = "公告内容")
    @TableField("content")
    private String content;

    /**
     * 公告开始时间
     */
    @Schema(description = "公告开始时间")
    @TableField(value = "notice_start_time", fill = FieldFill.INSERT_UPDATE)
    private Date noticeStartTime;

    /**
     * 公告结束时间
     */
    @Schema(description = "公告结束时间")
    @TableField(value = "notice_end_time", fill = FieldFill.INSERT_UPDATE)
    private Date noticeEndTime;

    /**
     * 统一配置
     */
    @Schema(description = "统一配置")
    @TableField("unified_configuration")
    private Integer unifiedConfiguration;


    /**
     * 是否删除 0-否 1-是
     */
    @Schema(description = "是否删除 0-否 1-是 ")
    @TableField("is_del")
    private Integer isDel;


    /**
     * 是否可用 0-否 1-是
     */
    @Schema(description = "是否可用 0-否 1-是 ")
    @TableField("is_available")
    private Integer isAvailable;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;


    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 更新人
     */
    @Schema(description = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;


    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
