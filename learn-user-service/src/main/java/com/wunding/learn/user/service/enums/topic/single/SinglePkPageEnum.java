package com.wunding.learn.user.service.enums.topic.single;

import com.wunding.learn.common.constant.user.UserErrorNoEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.user.service.admin.dto.topic.PageAttributeDTO;
import com.wunding.learn.user.service.admin.dto.topic.single.SinglePkPageDTO;
import com.wunding.learn.user.service.admin.dto.topic.single.UpdateSinglePkPageDTO;
import com.wunding.learn.user.service.enums.topic.BasePageInterface;
import java.util.Objects;
import java.util.function.BiConsumer;
import java.util.function.Function;

/**
 * 个人赛pk页枚举
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
public enum SinglePkPageEnum implements BasePageInterface<SinglePkPageDTO, UpdateSinglePkPageDTO> {


    /**
     * 无意义常数
     */
    MEANINGLESS_CONSTANT(
        "SinglePkPageEnum",
        -1,
        (item, value) -> {
        },
        updateSinglePkPageDTO -> null
    ),

    /**
     * 成员A
     */
    MEMBER_A(
        "singlePkPageMemberA",
        1,
        SinglePkPageDTO::setSinglePkMemberA,
        UpdateSinglePkPageDTO::getSinglePkMemberA
    ),

    /**
     * 成员B
     */
    MEMBER_B(
        "singlePkPageMemberB",
        1,
        SinglePkPageDTO::setSinglePkMemberB,
        UpdateSinglePkPageDTO::getSinglePkMemberB
    ),

    /**
     * 背景图
     */
    BACKGROUND(
        "singlePkPageBackGround",
        1,
        SinglePkPageDTO::setSinglePkBackGroundImg,
        UpdateSinglePkPageDTO::getSinglePkBackGroundImg
    ),

    /**
     * 进度条
     */
    PROGRESS_BAR(
        "singlePkPageProgressBar",
        1,
        SinglePkPageDTO::setSinglePkProgressBar,
        UpdateSinglePkPageDTO::getSinglePkProgressBar
    ),

    /**
     * 进度条
     */
    ANSWER_BACKGROUND(
        "singlePkAnswerBackground",
        1,
        SinglePkPageDTO::setSinglePkAnswerBackground,
        UpdateSinglePkPageDTO::getSinglePkAnswerBackground
    ),

    /**
     * 背景图
     */
    OPTION_BUTTON(
        "singlePkPageOptionButton",
        1,
        SinglePkPageDTO::setSinglePkOptionButton,
        UpdateSinglePkPageDTO::getSinglePkOptionButton
    ),

    /**
     * 背景音乐
     */
    MUSIC_PLAY(
        "singlePkMusicPlay",
        1,
        SinglePkPageDTO::setSinglePkMusicPlay,
        UpdateSinglePkPageDTO::getSinglePkMusicPlay
    ),

    /**
     * 背景音乐
     */
    MUSIC_STOP(
        "singlePkMusicStop",
        1,
        SinglePkPageDTO::setSinglePkMusicStop,
        UpdateSinglePkPageDTO::getSinglePkMusicStop
    ),

    ;

    /**
     * 属性名称
     */
    private final String attributeName;

    /**
     * 是否可编辑
     */
    private final Integer isEdit;

    private final BiConsumer<SinglePkPageDTO, PageAttributeDTO> pageConsumer;

    private final Function<UpdateSinglePkPageDTO, PageAttributeDTO> pageFunction;

    SinglePkPageEnum(String attributeName, Integer isEdit,
        BiConsumer<SinglePkPageDTO, PageAttributeDTO> dtoConsumer,
        Function<UpdateSinglePkPageDTO, PageAttributeDTO> pageFunction) {
        this.attributeName = attributeName;
        this.isEdit = isEdit;
        this.pageConsumer = dtoConsumer;
        this.pageFunction = pageFunction;
    }

    @Override
    public String getAttributeName() {
        return attributeName;
    }

    @Override
    public Integer getIsEdit() {
        return isEdit;
    }

    public BiConsumer<SinglePkPageDTO, PageAttributeDTO> getPageConsumer() {
        return pageConsumer;
    }

    public Function<UpdateSinglePkPageDTO, PageAttributeDTO> getPageFunction() {
        return pageFunction;
    }

    public static SinglePkPageEnum getAttributeConsumer(String value) {
        for (SinglePkPageEnum item : values()) {
            if (Objects.equals(item.getAttributeName(), value)) {
                return item;
            }
        }
        throw new BusinessException(UserErrorNoEnum.ERR_ENUM_VALUE_FAIL);
    }

    @Override
    public BiConsumer<SinglePkPageDTO, PageAttributeDTO> getConsumer(String value) {
        return getAttributeConsumer(value).getPageConsumer();
    }

    @Override
    public Function<UpdateSinglePkPageDTO, PageAttributeDTO> getFunction(String value) {
        return getAttributeConsumer(value).getPageFunction();
    }

    @Override
    public BasePageInterface<SinglePkPageDTO, UpdateSinglePkPageDTO>[] getValues() {
        return values();
    }
}
