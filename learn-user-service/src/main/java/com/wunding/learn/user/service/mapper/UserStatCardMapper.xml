<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.user.service.mapper.UserStatCardMapper">
    <!-- 开启二级缓存 -->
    <!--
    <cache type="com.wunding.learn.common.mybatis.cache.MyBatisPlusRedisCache"/>
    -->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.user.service.mapper.UserStatCardMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.user.service.model.UserStatCard">
        <!-- @Table sys_user_stat_card-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="card_type" jdbcType="VARCHAR" property="cardType"/>
        <result column="center_type" jdbcType="TINYINT" property="centerType"/>
        <result column="is_select" jdbcType="TINYINT" property="isSelect"/>
        <result column="sort_no" jdbcType="TINYINT" property="sortNo"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        user_id,
        card_type,
        center_type,
        is_select,
        sort_no,
        create_by,
        create_time
    </sql>
</mapper>
