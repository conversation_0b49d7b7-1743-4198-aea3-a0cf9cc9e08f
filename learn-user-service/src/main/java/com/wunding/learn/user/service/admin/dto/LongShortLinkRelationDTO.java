package com.wunding.learn.user.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 长短链接 对象
 *
 * <AUTHOR> href="mailto:<EMAIL>">guoyong</a>
 * @since 2025/6/16 21:41
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "LongShortLinkRelationDTO", description = "长短链接DTO")
public class LongShortLinkRelationDTO implements Serializable {

	@Schema(description = "长链接")
	private String longLink;

	@Schema(description = "短链接")
	private String shortLink;


}
