package com.wunding.learn.user.service.feign;

import com.wunding.learn.common.constant.other.Oauth2EntryTypeEnum;
import com.wunding.learn.common.util.bean.BeanListUtils;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.user.api.dto.OrgSimpleInfo;
import com.wunding.learn.user.api.dto.sync.SyncOrgDTO;
import com.wunding.learn.user.api.dto.sync.SyncPostDTO;
import com.wunding.learn.user.api.dto.sync.SyncUserDTO;
import com.wunding.learn.user.api.service.SyncDataFeign;
import com.wunding.learn.user.service.biz.IThirdAppSyncBiz;
import feign.Request.Options;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@Slf4j
public class SyncDataFeignImpl implements SyncDataFeign {


    private final IThirdAppSyncBiz thirdAppSyncBiz;

    @Override
    public void syncJobLevel(Collection<String> jobLevelNameSet, Options options) {
        thirdAppSyncBiz.batchAddJobLevel(new HashSet<>(jobLevelNameSet));
    }

    @Override
    public void syncOrgData(List<SyncOrgDTO> syncOrgList, Integer type, Options options) {
        log.info("同步组织数据：{}", JsonUtil.objToJson(syncOrgList));
        List<com.wunding.learn.user.service.admin.dto.SyncOrgDTO> list = syncOrgList.stream()
            .map(syncOrgDTO -> {
                com.wunding.learn.user.service.admin.dto.SyncOrgDTO dto = new com.wunding.learn.user.service.admin.dto.SyncOrgDTO();
                BeanUtils.copyProperties(syncOrgDTO, dto);
                return dto;
            })
            .collect(Collectors.toList());

        thirdAppSyncBiz.syncOrgData(list, Oauth2EntryTypeEnum.get(type));
    }

    @Override
    public void syncIdentityData(List<SyncPostDTO> syncPostList, Integer type, Options options) {
        log.info("同步身份数据：{}", JsonUtil.objToJson(syncPostList));
        List<com.wunding.learn.user.service.admin.dto.SyncIdentityDTO> list = syncPostList.stream()
            .map(syncPostDTO -> {
                com.wunding.learn.user.service.admin.dto.SyncIdentityDTO dto = new com.wunding.learn.user.service.admin.dto.SyncIdentityDTO();
                BeanUtils.copyProperties(syncPostDTO, dto);
                return dto;
            })
            .collect(Collectors.toList());
        thirdAppSyncBiz.syncIdentityData(list, Oauth2EntryTypeEnum.get(type));
    }

    @Override
    public void syncUserData(List<SyncUserDTO> syncUserList, Integer type, Options options) {
        log.info("同步用户数据：{}", JsonUtil.objToJson(syncUserList));
        List<com.wunding.learn.user.service.admin.dto.SyncUserDTO> list = syncUserList.stream()
            .map(syncUserDTO -> {
                com.wunding.learn.user.service.admin.dto.SyncUserDTO dto = new com.wunding.learn.user.service.admin.dto.SyncUserDTO();
                BeanUtils.copyProperties(syncUserDTO, dto);
                List<OrgSimpleInfo> orgCodeList = BeanListUtils.copyList(syncUserDTO.getOrgCodeList(),
                    OrgSimpleInfo.class);
                dto.setOrgCodeList(orgCodeList);
                return dto;
            })
            .collect(Collectors.toList());
        thirdAppSyncBiz.syncUserData(list, Oauth2EntryTypeEnum.get(type));
    }
}
