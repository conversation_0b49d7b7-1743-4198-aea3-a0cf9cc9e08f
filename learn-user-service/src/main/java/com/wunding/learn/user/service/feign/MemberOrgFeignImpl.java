package com.wunding.learn.user.service.feign;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.user.api.dto.MemberOrgFeignDTO;
import com.wunding.learn.user.api.dto.OrgOrderMemberDTO;
import com.wunding.learn.user.api.dto.OrgOrderMemberNumDTO;
import com.wunding.learn.user.api.query.OrgOrderMemberPageQuery;
import com.wunding.learn.user.api.service.MemberOrgFeign;
import com.wunding.learn.user.service.model.MemberOrg;
import com.wunding.learn.user.service.service.IMemberCardMemberService;
import com.wunding.learn.user.service.service.IMemberOrgAdministratorService;
import com.wunding.learn.user.service.service.IMemberOrgService;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @className MemberOrgFeignImpl
 * @description TODO
 * @date 2023/8/4 15:55
 */
@RestController
@Slf4j
public class MemberOrgFeignImpl implements MemberOrgFeign {

    @Resource
    private IMemberOrgAdministratorService orgAdministratorService;
    @Resource
    private IMemberCardMemberService cardMemberService;
    @Resource
    private IMemberOrgService memberOrgService;

    @Override
    public Collection<String> getMemberOrgIdsByManagerId(String managerId) {
        return orgAdministratorService.getUserOrgIds(managerId);
    }

    @Override
    public OrgOrderMemberNumDTO getOrgOrderMemberNum(String orderId, Integer memberNum) {
        return cardMemberService.getOrgOrderMemberNum(orderId, memberNum);
    }

    @Override
    public void removeOrgOrderMember(String ids) {
        cardMemberService.removeOrgOrderMember(ids);
    }

    @Override
    public PageInfo<OrgOrderMemberDTO> pageOrgOrderMember(OrgOrderMemberPageQuery query) {
        return cardMemberService.pageOrgOrderMember(query);
    }

    @Override
    public Map<String, MemberOrgFeignDTO> getMapByIds(Collection<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new HashMap<>();
        }
        List<MemberOrg> cardMemberList = memberOrgService.listByIds(ids);
        return cardMemberList.stream().map(entity -> {
            MemberOrgFeignDTO dto = new MemberOrgFeignDTO();
            BeanUtils.copyProperties(entity, dto);
            return dto;
        }).collect(Collectors.toMap(MemberOrgFeignDTO::getId, Function.identity(), (v1, v2) -> v1));
    }

    @Override
    public void updateData(MemberOrgFeignDTO dto) {
        if (Objects.isNull(dto) || StringUtils.isBlank(dto.getId())) {
            return;
        }
        MemberOrg memberOrg = new MemberOrg();
        BeanUtils.copyProperties(dto, memberOrg);
        memberOrgService.updateById(memberOrg);
    }

    @Override
    public MemberOrgFeignDTO getMemberOrgFeignDTO(String id) {
        MemberOrg memberOrg = memberOrgService.getById(id);
        if (Objects.isNull(memberOrg)){
            return null;
        }
        MemberOrgFeignDTO orgFeignDTO = new MemberOrgFeignDTO();
        BeanUtils.copyProperties(memberOrg, orgFeignDTO);
        return orgFeignDTO;
    }
}
