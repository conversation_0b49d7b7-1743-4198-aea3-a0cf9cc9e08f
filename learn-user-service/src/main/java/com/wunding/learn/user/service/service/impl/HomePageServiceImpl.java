package com.wunding.learn.user.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wunding.learn.common.constant.other.AvailableEnum;
import com.wunding.learn.common.constant.other.ClientTypeEnum;
import com.wunding.learn.common.constant.other.DelEnum;
import com.wunding.learn.common.constant.redis.CacheRedisDbConst;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.ResourceTypeEnum;
import com.wunding.learn.common.enums.course.SysDictCodeType;
import com.wunding.learn.common.enums.file.ImageTypeEnum;
import com.wunding.learn.common.enums.market.HeadContentRuleEnum;
import com.wunding.learn.common.enums.other.SystemConfigCodeEnum;
import com.wunding.learn.common.enums.user.SysItemCategoryType;
import com.wunding.learn.common.multi.language.model.MultiLangMessage;
import com.wunding.learn.common.multi.language.service.IMultiLangMessageService;
import com.wunding.learn.common.util.redis.RedisDBUtil;
import com.wunding.learn.common.utils.SystemDefaultMenus;
import com.wunding.learn.file.api.constant.ImageBizType;
import com.wunding.learn.file.api.service.FileFeign;
import com.wunding.learn.file.api.service.SysConfigFeign;
import com.wunding.learn.user.api.dto.OrgDTO;
import com.wunding.learn.user.api.dto.RightDTO;
import com.wunding.learn.user.api.service.OrgFeign;
import com.wunding.learn.user.service.client.dto.BackgroundConfigDTO;
import com.wunding.learn.user.service.client.dto.DictInfoDTO;
import com.wunding.learn.user.service.client.dto.HomePageConfigDTO;
import com.wunding.learn.user.service.client.dto.HomePageItemConfigDTO;
import com.wunding.learn.user.service.client.dto.HomePageSwitchConfigDTO;
import com.wunding.learn.user.service.client.dto.ItemClientDTO;
import com.wunding.learn.user.service.client.dto.MenuDTO;
import com.wunding.learn.user.service.client.dto.SysConfigDTO;
import com.wunding.learn.user.service.constant.UserConstant;
import com.wunding.learn.user.service.enums.ItemEnum;
import com.wunding.learn.user.service.enums.ThirdLinkItemEnum;
import com.wunding.learn.user.service.feign.UserFeignImpl;
import com.wunding.learn.user.service.mapper.HomePageConfigMapper;
import com.wunding.learn.user.service.mapper.HomePageItemConfigMapper;
import com.wunding.learn.user.service.mapper.ItemMapper;
import com.wunding.learn.user.service.model.HomeConfigUserSelect;
import com.wunding.learn.user.service.model.HomeDiyMenu;
import com.wunding.learn.user.service.model.HomeMenuItemConfig;
import com.wunding.learn.user.service.model.HomePageConfig;
import com.wunding.learn.user.service.model.HomePageItemConfig;
import com.wunding.learn.user.service.model.Item;
import com.wunding.learn.user.service.model.Org;
import com.wunding.learn.user.service.model.Para;
import com.wunding.learn.user.service.service.IHomeConfigUserSelectService;
import com.wunding.learn.user.service.service.IHomeDiyMenuService;
import com.wunding.learn.user.service.service.IHomeMenuItemConfigService;
import com.wunding.learn.user.service.service.IHomePageService;
import com.wunding.learn.user.service.service.INewIdentityService;
import com.wunding.learn.user.service.service.IOrgService;
import com.wunding.learn.user.service.service.IParaService;
import com.wunding.learn.user.service.service.IRightService;
import com.wunding.learn.user.service.service.IRouterService;
import com.wunding.learn.user.service.service.IUserIdentityService;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @date 2022/7/26
 */
@Service("homePageService")
@Slf4j
public class HomePageServiceImpl implements IHomePageService {

    public static final String GET_MENU = "getMenu";
    public static final String LAST_SQL = "limit 1";

    @Resource
    private ItemMapper itemMapper;
    @Resource
    private FileFeign fileFeign;
    @Resource
    private HomePageConfigMapper homePageConfigMapper;
    @Resource
    private HomePageItemConfigMapper homePageItemConfigMapper;
    @Resource
    private IRightService rightService;
    @Resource
    private INewIdentityService newIdentityService;
    @Resource
    private IUserIdentityService userIdentityService;
    @Resource
    private UserFeignImpl userFeign;
    @Resource
    private IOrgService orgService;
    @Resource
    private IParaService paraService;
    @Resource(name = "redisTemplate")
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private OrgFeign orgFeign;
    @Resource
    private IHomeConfigUserSelectService homeConfigUserSelectService;
    @Resource
    private IHomeMenuItemConfigService menuItemConfigService;
    @Resource
    private IHomeDiyMenuService homeDiyMenuService;
    @Resource
    private IMultiLangMessageService multiLangMessageService;
    @Resource
    private IRouterService routerService;

    @Resource
    private SysConfigFeign sysConfigFeign;

    @Override
    public DictInfoDTO getDictInfo() {
        DictInfoDTO dto = new DictInfoDTO();
        List<String> paraIdList = new ArrayList<>();

        paraIdList.add(SysDictCodeType.GRAPHIC_COURSE_WARE_LEARN_TIME.getValue());
        paraIdList.add(SysDictCodeType.CLASS_TIME_REPORT_INTERVAL.getValue());

        Map<String, String> paraMap = paraService.lambdaQuery().in(Para::getId, paraIdList).list().stream()
            .collect(Collectors.toMap(Para::getId, Para::getParaValue));
        // 课件有效学习时长
        String videoAudioLearnTimeLength = paraMap.get(SysDictCodeType.GRAPHIC_COURSE_WARE_LEARN_TIME.getValue());
        dto.setVideoAudioLearnTimeLength(0);
        if (StringUtils.isNotBlank(videoAudioLearnTimeLength)) {
            dto.setVideoAudioLearnTimeLength(Integer.parseInt(videoAudioLearnTimeLength));
        }
        // 学习时长上报间隔设置
        String reportIntervalTimeLength = paraMap.get(SysDictCodeType.CLASS_TIME_REPORT_INTERVAL.getValue());
        dto.setReportIntervalTimeLength(0);
        if (StringUtils.isNotBlank(reportIntervalTimeLength)) {
            dto.setReportIntervalTimeLength(Integer.parseInt(reportIntervalTimeLength));
        }
        return dto;
    }

    @Override
    @SuppressWarnings("squid:S3776")
    public List<MenuDTO> getMenu(String userId, String configId, String server) {
        String configKey = configId;
        if (StringUtils.isBlank(configKey)) {
            HomeConfigUserSelect userSelect = homeConfigUserSelectService.findByUserId(userId);
            if (userSelect != null) {
                configId = userSelect.getConfigId();
                // 根据 configId 查询DIY配置的归属组织
                // 判断归属组织是否与当前用户所在组织全路径相同
                // 存在,不做处理 - 不存在,删除用户所选的DIY方案.
                HomePageConfig homePageConfig = homePageConfigMapper.selectOne(
                    new LambdaQueryWrapper<HomePageConfig>()
                        .eq(HomePageConfig::getId, configId)
                        .eq(HomePageConfig::getIsDel, DelEnum.NOT_DELETE.getValue())
                        .eq(HomePageConfig::getIsAvailable, AvailableEnum.AVAILABLE.getValue()));
                if (Objects.isNull(homePageConfig)) {
                    homeConfigUserSelectService.removeById(userSelect.getId());
                    userSelect = null;
                    configId = null;
                } else {
                    Org userOrg = orgService.getById(UserThreadContext.getOrgId());
                    if (!userOrg.getLevelPath().contains(homePageConfig.getOrgId())) {
                        homeConfigUserSelectService.removeById(userSelect.getId());
                        userSelect = null;
                        configId = null;
                    }
                }
            }
            log.info("用户结果: " + userSelect + " ---- " + configId);
            configKey = userSelect != null ? userSelect.getConfigId() : "none";
        } else {
            homeConfigUserSelectService.saveOrUpdateUserSelect(userId, configKey);
        }
        String redisKey = RedisDBUtil.getFullKey(CacheRedisDbConst.CACHE, GET_MENU,
            UserThreadContext.getAcceptLanguage(), userId, configKey, server);

        log.info("redisKey{}", redisKey);
        List<MenuDTO> redisList = (List<MenuDTO>) redisTemplate.opsForValue().get(redisKey);
        if (!CollectionUtils.isEmpty(redisList)) {
            return redisList;
        }
        // 获取底部菜单
        List<MenuDTO> list = getMenuListByMenu(server, ItemEnum.BOTTOM_MENU.getId(), userId, configId);
        processMenuDTOList(server, list, userId, configId);

        HomeConfigUserSelect cur = homeConfigUserSelectService.findByUserId(userId);
        if (cur != null) {
            redisKey = RedisDBUtil.getFullKey(CacheRedisDbConst.CACHE, GET_MENU,
                UserThreadContext.getAcceptLanguage(), userId, cur.getConfigId(), server);
        }
        redisTemplate.opsForValue().set(redisKey, list);
        redisTemplate.expire(redisKey, 300, TimeUnit.SECONDS);
        return list;
    }

    private void processMenuDTOList(String server, List<MenuDTO> list, String userId, String configId){
        for (MenuDTO menuDTO : list) {
            if (Objects.equals(menuDTO.getType(), ItemEnum.DISCOVER_MENU.getId())) {
                // 底部菜单 -- ‘发现’ 栏目时不获取中部菜单，由单独接口提供
                // menuDTO.setSubMenu(getMenuByParentId(server, ItemEnum.CENTER_MENU.getId(), userId, configId))
            }
            if (Objects.equals(menuDTO.getType(), ItemEnum.MY_CENTER_MENU.getId())) {
                for (MenuDTO subMenu : menuDTO.getSubMenu()) {
                    if (Objects.equals(subMenu.getType(), ItemEnum.MY_LEARN_MENU.getId())) {
                        subMenu.setSubMenu(getMenuByParentId(server, ItemEnum.MY_LEARN_MENU.getId(), userId, configId));
                    }
                }
            }
        }
    }


    @Override
    public SysConfigDTO getSysConfig() {
        SysConfigDTO sysConfigDTO = new SysConfigDTO();
        Para para = paraService.getById("602");
        if (para != null && StringUtils.isNotBlank(para.getParaValue())) {
            try {
                sysConfigDTO.setCourseElasticSearch(Integer.parseInt(para.getParaValue()));
            } catch (NumberFormatException e) {
                sysConfigDTO.setCourseElasticSearch(0);
                log.error("get para error", e);
            }
        } else {
            sysConfigDTO.setCourseElasticSearch(0);
        }
        return sysConfigDTO;
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<MenuDTO> getSpecifyMenu(String menu, String server, String configId) {
        String configKey = configId;
        String userId = UserThreadContext.getUserId();
        if (StringUtils.isBlank(configId)) {
            HomeConfigUserSelect userSelect = homeConfigUserSelectService.findByUserId(userId);
            if (null != userSelect) {
                configId = userSelect.getConfigId();

                // 根据 configId 查询DIY配置的归属组织
                // 判断归属组织是否与当前用户所在组织全路径相同
                // 存在,不做处理 - 不存在,删除用户所选的DIY方案.
                HomePageConfig homePageConfig = homePageConfigMapper.selectOne(
                    new LambdaQueryWrapper<HomePageConfig>()
                        .eq(HomePageConfig::getId, configId)
                        .eq(HomePageConfig::getIsDel, DelEnum.NOT_DELETE.getValue())
                        .eq(HomePageConfig::getIsAvailable, AvailableEnum.AVAILABLE.getValue()));
                if (Objects.isNull(homePageConfig)) {
                    homeConfigUserSelectService.removeById(userSelect.getId());
                    userSelect = null;
                    configId = null;
                } else {
                    Org userOrg = orgService.getById(UserThreadContext.getOrgId());
                    if (!userOrg.getLevelPath().contains(homePageConfig.getOrgId())) {
                        homeConfigUserSelectService.removeById(userSelect.getId());
                        userSelect = null;
                        configId = null;
                    }
                }
            }
            configKey = userSelect != null ? userSelect.getConfigId() : "none";
        }
        String redisKey = RedisDBUtil.getFullKey(CacheRedisDbConst.CACHE, GET_MENU,
            UserThreadContext.getAcceptLanguage(), menu, userId, configKey, server);
        log.info("redisKey{}", redisKey);
        List<MenuDTO> redisList = (List<MenuDTO>) redisTemplate.opsForValue().get(redisKey);
        if (!CollectionUtils.isEmpty(redisList)) {
            log.info("getSpecifyMenu from cache");
            return redisList;
        }
        List<MenuDTO> menus = getMenuListByMenu(server, menu, userId, configId);
        redisTemplate.opsForValue().set(redisKey, menus);
        redisTemplate.expire(redisKey, 300, TimeUnit.SECONDS);
        return menus;
    }
    private List<MenuDTO> getMenuListByMenu(String server, String menu, String userId, String configId) {
        // 使用的配置
        String finalConfigId = configId;
        List<Item> itemList;
        if (StringUtils.isNotBlank(configId)) {
            itemList = finditemByConfigId(server, menu, configId);
        } else {
            // 获取用户选择的记录
            itemList = findUserSelect(server,menu, userId);
        }

        if (CollectionUtils.isEmpty(itemList) && StringUtils.isBlank(configId)) {
            // 当前用户所在组织配置
            HomePageConfig homePageConfig = homePageConfigMapper.selectOne(
                new LambdaQueryWrapper<HomePageConfig>().select(HomePageConfig::getId)
                    .eq(HomePageConfig::getOrgId, UserThreadContext.getOrgId())
                    .eq(HomePageConfig::getIsAvailable, AvailableEnum.AVAILABLE.getValue()).last(LAST_SQL));
            if (homePageConfig != null) {
                itemList = findByConfigId(server, menu, homePageConfig.getId());
                finalConfigId = homePageConfig.getId();
            } else {
                List<Object> resultList = processItemListAndConfigIdNotEmpty(server, menu, finalConfigId, itemList);
                itemList = (List<Item>) resultList.get(0);
                finalConfigId = (String) resultList.get(1);
            }
        }

        this.saveOrUpdateUserSelect(menu, userId, finalConfigId);

        if (CollectionUtils.isEmpty(itemList)) {
            String limitSize = "100";
            if (ItemEnum.BOTTOM_MENU.getId().equals(menu)) {
                limitSize = "5";
            }
            // 获取所有底部菜单
            itemList = itemMapper.selectList(new LambdaQueryWrapper<Item>().eq(Item::getParentId, menu)
                .eq(Item::getIsDel, DelEnum.NOT_DELETE.getValue())
                .eq(Item::getIsAvailable, AvailableEnum.AVAILABLE.getValue()).last("limit " + limitSize)
                .orderByAsc(Item::getSortNo).orderByDesc(Item::getUpdateTime));
        }
        // 过滤出来有权限的
        itemList = filterItemByUserRight(itemList, userId, menu);
        // 需要使用特定规则的处理，如讲师、下属档案等
        handleSpecialRules(menu, itemList);

        List<MenuDTO> menuList = new ArrayList<>();
        menuList = getMenuDTOS(server, userId, configId, itemList, menuList);

        return menuList;
    }

    private List<MenuDTO> getMenuDTOS(String server, String userId, String configId, List<Item> itemList,
        List<MenuDTO> menuList) {
        if (!CollectionUtils.isEmpty(itemList)) {
            // 获取自定义图标信息
            Set<String> diyItemIds = itemList.stream().map(Item::getHomeMenuItemConfigId).collect(Collectors.toSet());
            Map<String, String> diyItemImgMap = fileFeign.getImageUrlsByIds(diyItemIds, ImageTypeEnum.ItemImage.name());
            Map<String, String> diyItemBotImgMap = fileFeign.getImageUrlsByIds(diyItemIds,
                ImageBizType.ItemBotMenuImage.name());
            // 获取默认图标信息
            Set<String> itemIds = itemList.stream().map(Item::getId).collect(Collectors.toSet());
            Map<String, String> itemImgMap = fileFeign.getImageUrlsByIds(itemIds, ImageTypeEnum.ItemImage.name());
            Map<String, String> itemBotImgMap = fileFeign.getImageUrlsByIds(itemIds,
                ImageBizType.ItemBotMenuImage.name());

            menuList = itemList.stream().map(item -> {
                MenuDTO menuDTO = new MenuDTO();
                BeanUtils.copyProperties(item, menuDTO);
                // 优先自定义图标
                String diyItemImg = diyItemImgMap.get(item.getHomeMenuItemConfigId());

                String url = StringUtils.isBlank(diyItemImg) ? itemImgMap.get(menuDTO.getId()) : diyItemImg;

                String iconUrlIfNull = getIconUrlIfNull(item, url);
                menuDTO.setIconImageUrl(iconUrlIfNull);
                String diyItemBotImg = diyItemBotImgMap.get(item.getHomeMenuItemConfigId());
                menuDTO.setIconBotMenuImageUrl(
                    StringUtils.isBlank(diyItemBotImg) ? itemBotImgMap.get(item.getId()) : diyItemBotImg);

                menuDTO.setType(item.getDisplayMode());
                menuDTO.setSubMenu(getMenuByParentId(server, item.getCategoryType(), userId, configId));
                menuDTO.setIsAuth(item.getIsAuth());
                menuDTO.setLinkUrl(item.getLinkUrl());
                menuDTO.setOpenType(item.getOpenType());
                return menuDTO;
            }).collect(Collectors.toList());
        }
        return menuList;
    }

    private List<Item> finditemByConfigId(String server, String parentId, String configId) {
        return findMenuDIY(server, parentId, configId);
    }

    private List<Item> findMenuDIY(String server, String parentId, String configId) {
        List<Item> itemList;
        HomeDiyMenu homeDiyMenu = null;
        if (StringUtils.isNotBlank(configId)) {
            homeDiyMenu = homeDiyMenuService.findUserDiyMenu(server, configId, parentId);
        }

        if (!Objects.isNull(homeDiyMenu)) {
            List<HomeMenuItemConfig> menuItemConfigs = menuItemConfigService.list(
                new LambdaQueryWrapper<HomeMenuItemConfig>().eq(HomeMenuItemConfig::getDiyMenuId, homeDiyMenu.getId()));

            if (CollectionUtils.isEmpty(menuItemConfigs)) {
                return new ArrayList<>();
            }
            String language = UserThreadContext.getAcceptLanguage();
            List<MultiLangMessage> multiLangMessageList = new ArrayList<>();
            if (StringUtils.isNotBlank(language) && !CollectionUtils.isEmpty(menuItemConfigs)) {
                multiLangMessageList = multiLangMessageService.list(
                    new LambdaQueryWrapper<MultiLangMessage>().eq(MultiLangMessage::getLang, language)
                        .in(MultiLangMessage::getResourceId,
                            menuItemConfigs.stream().map(HomeMenuItemConfig::getId).collect(Collectors.toList()))
                        .eq(MultiLangMessage::getResourceType, UserConstant.HOME_MENU_ITEM_CONFIG)
                        .eq(MultiLangMessage::getProperty, UserConstant.HOME_MENU_ITEM_CONFIG_NAME));
            }
            Map<String, String> langMessageMap = multiLangMessageList.stream().collect(
                Collectors.toMap(MultiLangMessage::getResourceId, MultiLangMessage::getContent, (k1, k2) -> k1));
            menuItemConfigs.stream().forEach(menuItemConfig -> {
                if (StringUtils.isNotBlank(langMessageMap.get(menuItemConfig.getId()))) {
                    menuItemConfig.setName(langMessageMap.get(menuItemConfig.getId()));
                }
            });

            Map<String, HomeMenuItemConfig> itemConfigMap = menuItemConfigs.stream()
                .collect(Collectors.toMap(HomeMenuItemConfig::getItemId, item -> item, (k1, k2) -> k1));

            Set<String> itemIdSet = menuItemConfigs.stream().map(HomeMenuItemConfig::getItemId)
                .collect(Collectors.toSet());
            itemList = itemMapper.selectList(new LambdaQueryWrapper<Item>().in(Item::getId, itemIdSet)
                .eq(Item::getIsDel, DelEnum.NOT_DELETE.getValue())
                .eq(Item::getIsAvailable, AvailableEnum.AVAILABLE.getValue())
                .orderByAsc(Item::getLevel, Item::getSortNo)
                .orderByDesc(Item::getCreateTime));
            // 转换
            itemList.forEach(item -> {
                item.setName(itemConfigMap.get(item.getId()).getName());
                item.setSortNo(itemConfigMap.get(item.getId()).getSort());
                item.setHomeMenuItemConfigId(itemConfigMap.get(item.getId()).getId());
            });
            // 排序
            return itemList.stream().sorted(Comparator.comparingInt(Item::getSortNo)).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }


    private List<MenuDTO> getMenuByParentId(String server, String parentId, String userId, String configId) {
        // 使用的配置
        String finalConfigId = configId;

        List<Item> itemList = initItemList(server, parentId, userId, configId);

        if (CollectionUtils.isEmpty(itemList) && StringUtils.isBlank(configId)) {
            // 当前用户所在组织配置
            HomePageConfig homePageConfig = homePageConfigMapper.selectOne(
                new LambdaQueryWrapper<HomePageConfig>().select(HomePageConfig::getId)
                    .eq(HomePageConfig::getOrgId, UserThreadContext.getOrgId())
                    .eq(HomePageConfig::getIsAvailable, AvailableEnum.AVAILABLE.getValue()).last(LAST_SQL));
            if (homePageConfig != null) {
                itemList = findByConfigId(server, parentId, homePageConfig.getId());
                finalConfigId = homePageConfig.getId();
            } else {
                List<Object> resultList = processItemListAndConfigIdNotEmpty(server, parentId, finalConfigId, itemList);
                itemList = (List<Item>) resultList.get(0);
                finalConfigId = (String) resultList.get(1);
            }
        }

        this.saveOrUpdateUserSelect(parentId, userId, finalConfigId);

        if (CollectionUtils.isEmpty(itemList)) {
            String limitSize = "100";
            if (ItemEnum.BOTTOM_MENU.getId().equals(parentId)) {
                limitSize = "5";
            }
            // 获取所有底部菜单
            itemList = itemMapper.selectList(new LambdaQueryWrapper<Item>().eq(Item::getParentId, parentId)
                .eq(Item::getIsDel, DelEnum.NOT_DELETE.getValue())
                .eq(Item::getIsAvailable, AvailableEnum.AVAILABLE.getValue()).last("limit " + limitSize)
                .orderByAsc(Item::getSortNo).orderByDesc(Item::getUpdateTime));
        }
        // 过滤出来有权限的
        itemList = filterItemByUserRight(itemList, userId, parentId);
        // 需要使用特定规则的处理，如讲师、下属档案等
        handleSpecialRules(parentId, itemList);

        List<MenuDTO> menuList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(itemList)) {
            // 获取自定义图标信息
            Set<String> diyItemIds = itemList.stream().map(Item::getHomeMenuItemConfigId).collect(Collectors.toSet());
            Map<String, String> diyItemImgMap = fileFeign.getImageUrlsByIds(diyItemIds, ImageTypeEnum.ItemImage.name());
            Map<String, String> diyItemBotImgMap = fileFeign.getImageUrlsByIds(diyItemIds,
                ImageBizType.ItemBotMenuImage.name());
            // 获取默认图标信息
            Set<String> itemIds = itemList.stream().map(Item::getId).collect(Collectors.toSet());
            Map<String, String> itemImgMap = fileFeign.getImageUrlsByIds(itemIds, ImageTypeEnum.ItemImage.name());
            Map<String, String> itemBotImgMap = fileFeign.getImageUrlsByIds(itemIds,
                ImageBizType.ItemBotMenuImage.name());

            menuList = itemList.stream().map(item -> {
                MenuDTO menuDTO = new MenuDTO();
                BeanUtils.copyProperties(item, menuDTO);
                // 优先自定义图标
                String diyItemImg = diyItemImgMap.get(item.getHomeMenuItemConfigId());

                String url = StringUtils.isBlank(diyItemImg) ? itemImgMap.get(menuDTO.getId()) : diyItemImg;

                String iconUrlIfNull = getIconUrlIfNull(item, url);
                menuDTO.setIconImageUrl(iconUrlIfNull);
                String diyItemBotImg = diyItemBotImgMap.get(item.getHomeMenuItemConfigId());
                menuDTO.setIconBotMenuImageUrl(
                    StringUtils.isBlank(diyItemBotImg) ? itemBotImgMap.get(item.getId()) : diyItemBotImg);

                menuDTO.setType(item.getDisplayMode());
                menuDTO.setSubMenu(getMenuByParentId(server, item.getCategoryType(), userId, configId));
                menuDTO.setIsAuth(item.getIsAuth());
                menuDTO.setLinkUrl(item.getLinkUrl());
                menuDTO.setOpenType(item.getOpenType());
                return menuDTO;
            }).collect(Collectors.toList());
        }

        return menuList;
    }

    private String getIconUrlIfNull(Item item, String url) {
        if (url == null && SystemDefaultMenus.containsMenu(item.getId())) {
            String sysConfigRoot = sysConfigFeign.getSysConfigRoot();
            url = fileFeign.getFileUrl(
                "/" + sysConfigRoot + "/file/menuIcon/" + item.getId() + ".png");
        }
        return url;
    }

    private List<Object> processItemListAndConfigIdNotEmpty(String server, String parentId, String finalConfigId, List<Item> itemList){
        List<Object> resultList = new ArrayList<>();
        resultList.add(itemList);
        resultList.add(finalConfigId);
        // 获取父组织配置
        Org parentOrg = orgService.getParentOrg(UserThreadContext.getOrgId());
        if (!Objects.isNull(parentOrg)) {
            String parentOrgId = parentOrg.getId();
            HomePageConfig parentConfig = null;
            while (parentOrgId != null && parentConfig == null) {
                parentConfig = homePageConfigMapper.selectOne(
                    new LambdaQueryWrapper<HomePageConfig>().select(HomePageConfig::getId)
                        .eq(HomePageConfig::getOrgId, parentOrgId)
                        .eq(HomePageConfig::getIsAvailable, AvailableEnum.AVAILABLE.getValue())
                        .last(LAST_SQL));
                if (parentConfig != null) {
                    itemList = findByConfigId(server, parentId, parentConfig.getId());
                    resultList.set(0, itemList);
                }
                Org parentOrgTmp = orgService.getParentOrg(parentOrgId);
                if (parentOrgTmp == null) {
                    parentOrgId = null;
                } else {
                    parentOrgId = parentOrgTmp.getId();
                }
            }
            if (parentConfig != null) {
                finalConfigId = parentConfig.getId();
                resultList.set(1, finalConfigId);
            }
        }
        return resultList;
    }

    private List<Item> initItemList(String server, String parentId, String userId, String configId){
        if (StringUtils.isNotBlank(configId)) {
            return findByConfigId(server, parentId, configId);
        } else {
            // 获取用户选择的记录
            return findUserSelect(server, parentId, userId);
        }
    }

    private void saveOrUpdateUserSelect(String parentId, String userId, String finalConfigId){
        if (StringUtils.isNotBlank(finalConfigId) && (StringUtils.equals(ItemEnum.BOTTOM_MENU.getId(), parentId)
            || StringUtils.equals(ItemEnum.CENTER_MENU.getId(), parentId))) {
            homeConfigUserSelectService.saveOrUpdateUserSelect(userId, finalConfigId);
        }
    }

    private List<Item> findUserSelect(String server, String parentId, String userId) {
        return findDIY(server, parentId, userId, null);
    }

    private List<Item> findByConfigId(String server,String parentId, String configId) {
        return findDIY(server, parentId, null, configId);
    }

    private List<Item> findDIY(String server, String parentId, String userId, String configId) {
        List<Item> itemList;
        HomeDiyMenu homeDiyMenu = null;
        if (StringUtils.isNotBlank(userId)) {
            HomeConfigUserSelect userSelect = homeConfigUserSelectService.findByUserId(userId);
            if (!Objects.isNull(userSelect)) {
                homeDiyMenu = homeDiyMenuService.findUserDiyMenu(server, userSelect.getConfigId(), parentId);
            }
        } else if (StringUtils.isNotBlank(configId)) {
            homeDiyMenu = homeDiyMenuService.findUserDiyMenu(server, configId, parentId);
        }

        if (!Objects.isNull(homeDiyMenu)) {
            List<HomeMenuItemConfig> menuItemConfigs = menuItemConfigService.list(
                new LambdaQueryWrapper<HomeMenuItemConfig>().eq(HomeMenuItemConfig::getDiyMenuId, homeDiyMenu.getId()));

            if (CollectionUtils.isEmpty(menuItemConfigs)) {
                return new ArrayList<>();
            }
            String language = UserThreadContext.getAcceptLanguage();
            List<MultiLangMessage> multiLangMessageList = new ArrayList<>();
            if (StringUtils.isNotBlank(language) && !CollectionUtils.isEmpty(menuItemConfigs)) {
                multiLangMessageList = multiLangMessageService.list(
                    new LambdaQueryWrapper<MultiLangMessage>().eq(MultiLangMessage::getLang, language)
                        .in(MultiLangMessage::getResourceId,
                            menuItemConfigs.stream().map(HomeMenuItemConfig::getId).collect(Collectors.toList()))
                        .eq(MultiLangMessage::getResourceType, UserConstant.HOME_MENU_ITEM_CONFIG)
                        .eq(MultiLangMessage::getProperty, UserConstant.HOME_MENU_ITEM_CONFIG_NAME));
            }
            Map<String, String> langMessageMap = multiLangMessageList.stream().collect(
                Collectors.toMap(MultiLangMessage::getResourceId, MultiLangMessage::getContent, (k1, k2) -> k1));
            menuItemConfigs.stream().forEach(menuItemConfig -> {
                if (StringUtils.isNotBlank(langMessageMap.get(menuItemConfig.getId()))) {
                    menuItemConfig.setName(langMessageMap.get(menuItemConfig.getId()));
                }
            });

            Map<String, HomeMenuItemConfig> itemConfigMap = menuItemConfigs.stream()
                .collect(Collectors.toMap(HomeMenuItemConfig::getItemId, item -> item, (k1, k2) -> k1));

            Set<String> itemIdSet = menuItemConfigs.stream().map(HomeMenuItemConfig::getItemId)
                .collect(Collectors.toSet());
            itemList = itemMapper.selectList(new LambdaQueryWrapper<Item>().in(Item::getId, itemIdSet)
                .eq(Item::getIsDel, DelEnum.NOT_DELETE.getValue())
                .eq(Item::getIsAvailable, AvailableEnum.AVAILABLE.getValue())
                .orderByAsc(Item::getLevel, Item::getSortNo)
                .orderByDesc(Item::getCreateTime));
            // 转换
            itemList.forEach(item -> {
                item.setName(itemConfigMap.get(item.getId()).getName());
                item.setSortNo(itemConfigMap.get(item.getId()).getSort());
                item.setHomeMenuItemConfigId(itemConfigMap.get(item.getId()).getId());
            });
            // 排序
            return itemList.stream().sorted(Comparator.comparingInt(Item::getSortNo)).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    /**
     * 处理特殊规则
     *
     * @param parentId 父节点
     * @param itemList 栏目列表
     */
    private void handleSpecialRules(String parentId, List<Item> itemList) {
        String userId = UserThreadContext.getUserId();
        // 过滤我的应用中特殊的规则按钮
        if (Objects.equals(parentId, ItemEnum.MY_APPLICATION_MENU.getId())) {
            Iterator<Item> iterator = itemList.iterator();
            while (iterator.hasNext()) {
                Item item = iterator.next();
                // 下属档案
                if (SysItemCategoryType.SUBORDINATE_FILE.getValue().equals(item.getId())
                    && !newIdentityService.hasPermissionViewSubordinateFile(userId)) {
                    iterator.remove();
                }
                // 讲师
                if (SysItemCategoryType.LECTURER_TOOL.getValue().equals(item.getId())
                    && !userIdentityService.hasPermissionAccessLecturerTool(userId)) {
                    iterator.remove();
                }
            }
        }
    }

    @Override
    public List<ItemClientDTO> getBottomItemList() {
        return getItemListByParentId(ItemEnum.BOTTOM_MENU.getId());
    }

    @Override
    public List<ItemClientDTO> getTopMenuItemList() {
        return getItemListByParentId(ItemEnum.TOP_MENU.getId());
    }

    @Override
    public List<ItemClientDTO> getTopicItemList() {
        return getItemListByParentId(ItemEnum.TOPIC_MENU.getId());
    }

    @Override
    public List<ItemClientDTO> getMyCenterItemList() {
        return getItemListByParentId(ItemEnum.CENTER_MENU.getId());
    }

    @Override
    public List<ItemClientDTO> getMiddleItemList() {
        return getItemListByParentId(ItemEnum.CENTER_MENU.getId());
    }

    @Override
    public List<ItemClientDTO> getAppItemList() {
        String userId = UserThreadContext.getUserId();
        // 获取所有应用菜单
        List<ItemClientDTO> appItemDTOList = getItemListByParentId(ItemEnum.CENTER_MENU.getId());
        // 是否有权限查看下属档案
        boolean hasSubordinatePermission = false;
        if (StringUtils.isNotBlank(userId)) {
            hasSubordinatePermission = userFeign.hasPermissionViewSubordinateFile(userId);
        }
        List<ItemClientDTO> list = new ArrayList<>();
        for (ItemClientDTO itemClientDTO : appItemDTOList) {
            // 下属档案
            if (Objects.equals(SysItemCategoryType.SUBORDINATE_FILE.getValue(), itemClientDTO.getId())
                && !hasSubordinatePermission) {
                continue;
            }
            // 年度账单
            if (Objects.equals(SysItemCategoryType.ACCOUNT_STATEMENT.getValue(), itemClientDTO.getId())
                && Objects.equals(UserThreadContext.getOs(), ClientTypeEnum.CLIECNT_TYPE_PC.getName())) {
                continue;
            }
            list.add(itemClientDTO);
        }
        return list;
    }

    @Override
    public HomePageConfigDTO getHomePageItemConfig(String configId, String server) {
        // 获取用户的历史选择或者管理端配置的首页配置
        String orgId = UserThreadContext.getOrgId();
        String userId = UserThreadContext.getUserId();
        HomePageConfigDTO homePageConfigDTO = new HomePageConfigDTO();
        HomePageConfig homePageConfig = null;
        if (StringUtils.isBlank(configId)) {
            // 获取用户历史选择的配置
            HomeConfigUserSelect userSelect = homeConfigUserSelectService.findUserSelect(userId, null);
            if (userSelect != null && checkConfigIdAvailable(userSelect.getConfigId())) {
                homePageConfig = homePageConfigMapper.selectOne(
                    new LambdaQueryWrapper<HomePageConfig>().eq(HomePageConfig::getId, userSelect.getConfigId())
                        .eq(HomePageConfig::getIsAvailable, AvailableEnum.AVAILABLE.getValue()));
            }
        } else if (checkConfigIdAvailable(configId)) {
            // 根据指定配置Id获取配置
            homePageConfig = homePageConfigMapper.selectOne(
                new LambdaQueryWrapper<HomePageConfig>().eq(HomePageConfig::getId, configId)
                    .eq(HomePageConfig::getIsAvailable, AvailableEnum.AVAILABLE.getValue()));
        }
        // 查询首页配置的信息
        if (Objects.isNull(homePageConfig)) {
            homePageConfig = homePageConfigMapper.selectOne(
                new LambdaQueryWrapper<HomePageConfig>().eq(HomePageConfig::getOrgId, UserThreadContext.getOrgId())
                    .eq(HomePageConfig::getIsAvailable, AvailableEnum.AVAILABLE.getValue()).last(LAST_SQL));
        }
        // 如果当前组织没有首页配置，取上一级的
        while (Objects.isNull(homePageConfig)) {
            Org parentOrg = orgService.getParentOrg(orgId);
            if (Objects.isNull(parentOrg)) {
                return homePageConfigDTO;
            }
            orgId = parentOrg.getId();
            homePageConfig = homePageConfigMapper.selectOne(
                new LambdaQueryWrapper<HomePageConfig>().eq(HomePageConfig::getOrgId, orgId)
                    .eq(HomePageConfig::getIsAvailable, AvailableEnum.AVAILABLE.getValue()).last(LAST_SQL));
        }
        // 保存用户选择的记录
        homeConfigUserSelectService.saveOrUpdateUserSelect(userId, homePageConfig.getId());
        // 根据配置id和服务端获取配置的DIY的内容
        if (StringUtils.equals("PC", server)) {
            homePageConfigDTO.setTPageConfig(homePageConfig.getTPageConfig());
            homePageConfigDTO.setHomePageItemConfigDTOListPC11(getHomePageItemConfigDTOList(server, configId, "PC11"));
            homePageConfigDTO.setHomePageItemConfigDTOListPC14(getHomePageItemConfigDTOList(server, configId, "PC14"));
            homePageConfigDTO.setHomePageItemConfigDTOListPC34(getHomePageItemConfigDTOList(server, configId, "PC34"));
        } else if (StringUtils.equals("H5", server)) {
            homePageConfigDTO.setHomePageItemConfigDTOListH511(getHomePageItemConfigDTOList(server, configId, "H511"));
        }
        return homePageConfigDTO;
    }

    @NotNull
    private List<HomePageItemConfigDTO> getHomePageItemConfigDTOList(String server, String configId, String area) {
        List<HomePageItemConfig> itemConfigList = homePageItemConfigMapper.selectList(
            new LambdaQueryWrapper<HomePageItemConfig>()
                .eq(HomePageItemConfig::getConfigId, configId)
                .eq(HomePageItemConfig::getServer, server)
                .eq(HomePageItemConfig::getBelongArea, area)
                .orderByAsc(HomePageItemConfig::getSortNo));
        Map<String, String> imageUrlsByIds = new HashMap<>();
        if (!CollectionUtils.isEmpty(itemConfigList)) {
            List<String> itemIdList = itemConfigList.stream().map(HomePageItemConfig::getId).collect(Collectors.toList());
            // 查询项目图片
            imageUrlsByIds = fileFeign.getImageUrlsByIds(itemIdList, ImageBizType.HEAD_DIY_IMAGE.name());
        }
        final Map<String, String> imageUrlsByIdsMap = imageUrlsByIds;
        List<HomePageItemConfigDTO> itemConfigDTOList = itemConfigList.stream().map(itemConfig -> {
            HomePageItemConfigDTO dto = new HomePageItemConfigDTO();
            BeanUtils.copyProperties(itemConfig, dto);
            String itemId = itemConfig.getId();
            dto.setCardInfos(Arrays.asList(itemConfig.getCardInfo().split(",")));
            dto.setTitleImageUrl(imageUrlsByIdsMap.get(itemId));
            // 设置规则描述
            if (Objects.equals(itemConfig.getContentRule(), HeadContentRuleEnum.LEARN_CLOCK_IN.getRuleType())) {
                String param6 = paraService.getParaValue(
                    SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_920.getCode());
                dto.setContentRuleName(HeadContentRuleEnum.LEARN_CLOCK_IN.getName().replace("10", param6));
            }
            return dto;
        }).collect(Collectors.toList());

        // 检验系统版本权限是否包含要校验的内容；
        List<String> routerIds = routerService.getAllRouterNames();
        Stream<HomePageItemConfigDTO> stream = itemConfigDTOList.stream();
        if(!routerIds.contains(ResourceTypeEnum.NEWS.getRouter())){
            stream = stream.filter(dto -> dto.getType() != 2);
        }
        if(!routerIds.contains(ResourceTypeEnum.TOPIC_SECTION.getRouter())){
            stream = stream.filter(dto -> dto.getType() != 7);
        }
        if(!routerIds.contains(ResourceTypeEnum.CASE_LIBRARY_MANAGE.getRouter())){
            stream = stream.filter(dto -> dto.getType() != 8);
        }
        if(!routerIds.contains(ResourceTypeEnum.TRAIN_PROGRAM.getRouter())){
            stream = stream.filter(dto -> dto.getType() != 9);
        }
        if(!routerIds.contains(ResourceTypeEnum.FACE_PROJECT.getRouter())){
            stream = stream.filter(dto -> dto.getType() != 17);
        }

        return stream.toList();
    }

    private boolean checkConfigIdAvailable(String configId) {
        HomePageConfig homePageConfig = homePageConfigMapper.selectOne(
            new LambdaQueryWrapper<HomePageConfig>().eq(HomePageConfig::getId, configId)
                .eq(HomePageConfig::getIsAvailable, AvailableEnum.AVAILABLE.getValue()));
        if (Objects.isNull(homePageConfig)) {
            return false;
        } else {
            Org org = orgService.getById(homePageConfig.getOrgId());
            String userOrgId = UserThreadContext.getOrgId();
            if (org != null) {
                String levelPath = org.getLevelPath();
                List<String> orgList = Arrays.stream(levelPath.split("/")).collect(Collectors.toList());
                return !orgList.contains(userOrgId);
            }
        }
        return true;
    }

    /**
     * 根据父类id获取子栏目
     *
     * @param parentId
     */
    private List<ItemClientDTO> getItemListByParentId(String parentId) {
        // 获取所有底部菜单
        List<Item> itemList = itemMapper.selectList(new LambdaQueryWrapper<Item>().eq(Item::getParentId, parentId)
            .eq(Item::getIsDel, DelEnum.NOT_DELETE.getValue())
            .eq(Item::getIsAvailable, AvailableEnum.AVAILABLE.getValue()));
        if (CollectionUtils.isEmpty(itemList)) {
            return new ArrayList<>();
        }
        // 获取图标信息
        Set<String> itemIdSet = itemList.stream().map(Item::getId).collect(Collectors.toSet());
        Map<String, String> imageMap = fileFeign.getImageUrlsByIds(itemIdSet, ImageTypeEnum.ItemImage.name());
        return itemList.stream().map(bottomItem -> {
            ItemClientDTO itemClientDTO = new ItemClientDTO();
            BeanUtils.copyProperties(bottomItem, itemClientDTO);
            itemClientDTO.setIconImageUrl(imageMap.get(itemClientDTO.getId()));
            itemClientDTO.setType(bottomItem.getDisplayMode());
            return itemClientDTO;
        }).collect(Collectors.toList());
    }

    /**
     * 根据用户权限过滤菜单
     *
     * @param itemList
     * @param userId
     */
    private List<Item> filterItemByUserRight(List<Item> itemList, String userId, String parentId) {
        // 如果用户未登录则不过滤
        if (StringUtils.isBlank(userId) || CollectionUtils.isEmpty(itemList)) {
            return new ArrayList<>();
        }
        // 如果是话题的子栏目 子栏目不需要权限控制
        if ("topic".equals(parentId) || "mycenter".equals(parentId)) {
            return itemList;
        }
        // 获取权限
        List<RightDTO> rightDTOList = rightService.getRightByUserId(userId);
        Set<String> rightIdSet = rightDTOList.stream().map(rightDTO -> parentId + "_" + rightDTO.getId())
            .collect(Collectors.toSet());
        // 过滤
        return itemList.stream().filter(bottomItem -> rightIdSet.contains(bottomItem.getId())
                || ThirdLinkItemEnum.THIRD_LINK_ITEM_ENUM.getCateType().equals(bottomItem.getCategoryType())).collect(Collectors.toList());
    }

    @Override
    public List<HomePageSwitchConfigDTO> getHomePageSwitchConfig() {
        String orgId = UserThreadContext.getOrgId();
        List<HomePageConfig> pageConfigList = new ArrayList<>();
        // 当前用户所在组织首页配置
        Org org = orgService.getById(orgId);
        String levelPath = org.getLevelPath();
        if (levelPath == null || levelPath.isEmpty()) {
            return Collections.emptyList();
        }
        String[] pathArray = levelPath.split("/");
        List<String > listOrg = Arrays.asList(pathArray);

        List<HomePageConfig> homePageConfigs = homePageConfigMapper.selectList(
            new LambdaQueryWrapper<HomePageConfig>().in(HomePageConfig::getOrgId, listOrg)
                .eq(HomePageConfig::getIsAvailable, AvailableEnum.AVAILABLE.getValue()));
        Optional.ofNullable(homePageConfigs).ifPresent(pageConfigList::addAll);
        return getHomePageSwitchConfigDTOS(pageConfigList);
    }

    @Override
    public List<HomePageSwitchConfigDTO> getHomePageSwitchConfigDTOS(List<HomePageConfig> pageConfigList) {
        HomeConfigUserSelect userSelect = homeConfigUserSelectService.findByUserId(UserThreadContext.getUserId());
        // 构建组织全路径属性
        Set<String> orgIdSet = pageConfigList.stream().map(HomePageConfig::getOrgId).collect(Collectors.toSet());
        Set<OrgDTO> orgDTOSet = orgFeign.getByIds(orgIdSet);
        Map<String, String> orgDTOMap = orgDTOSet.stream()
            .collect(Collectors.toMap(OrgDTO::getId, OrgDTO::getLevelPathName));

        return pageConfigList.stream().map(pageConfig -> {
            HomePageSwitchConfigDTO switchConfigDTO = new HomePageSwitchConfigDTO();
            switchConfigDTO.setId(pageConfig.getId());
            switchConfigDTO.setName(pageConfig.getName());
            switchConfigDTO.setOrgId(pageConfig.getOrgId());
            switchConfigDTO.setOrgFullPath(orgDTOMap.get(pageConfig.getOrgId()));
            if (userSelect != null && StringUtils.equals(pageConfig.getId(), userSelect.getConfigId())) {
                switchConfigDTO.setActive(true);
            }
            return switchConfigDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public BackgroundConfigDTO getBackgroundConfig(String configId) {
        log.info("getBackgroundConfig---configId:{}", configId);
        if (StringUtils.isBlank(configId)) {
            log.info("getBackgroundConfig---未指定配置Id");
            return new BackgroundConfigDTO();
        }
        BackgroundConfigDTO backgroundConfigDTO = new BackgroundConfigDTO();
        // 根据指定配置Id获取配置
        HomePageConfig homePageConfig = homePageConfigMapper.selectOne(
            new LambdaQueryWrapper<HomePageConfig>().eq(HomePageConfig::getId, configId)
                .eq(HomePageConfig::getIsAvailable, AvailableEnum.AVAILABLE.getValue()));
        if (homePageConfig != null) {
            // 背景配置
            backgroundConfigDTO.setBackgroundColor(homePageConfig.getBackgroundColor());
            backgroundConfigDTO.setBackgroundRepeat(homePageConfig.getBackgroundRepeat());
            backgroundConfigDTO.setCurrentImgPriority(homePageConfig.getCurrentImgPriority());
            backgroundConfigDTO.setBackgroundImageUrl(fileFeign.getImageUrl(homePageConfig.getId(),
                ImageBizType.DIY_PC_BACKGROUND_IMAGE.toString()));
        }
        return backgroundConfigDTO;
    }

}

