<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.user.service.mapper.ThirdAppTemplateMapper">

        <!-- 开启二级缓存 -->
        <!--
    <cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
    -->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.user.service.mapper.ThirdAppTemplateMapper"/>

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.wunding.learn.user.service.model.ThirdAppTemplate">
            <!--@Table sys_third_app_template-->
                    <id column="id" jdbcType="VARCHAR" property="id"/>
                    <result column="app_id" jdbcType="VARCHAR"
                            property="appId"/>
                    <result column="template_id" jdbcType="VARCHAR"
                            property="templateId"/>
                    <result column="type" jdbcType="TINYINT"
                            property="type"/>
                    <result column="sign_name" jdbcType="VARCHAR"
                            property="signName"/>
                    <result column="push_to" jdbcType="VARCHAR"
                            property="pushTo"/>
                    <result column="is_del" jdbcType="TINYINT"
                            property="isDel"/>
                    <result column="create_by" jdbcType="VARCHAR"
                            property="createBy"/>
                    <result column="create_time" jdbcType="TIMESTAMP"
                            property="createTime"/>
                    <result column="update_by" jdbcType="VARCHAR"
                            property="updateBy"/>
                    <result column="update_time" jdbcType="TIMESTAMP"
                            property="updateTime"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, app_id, template_id, type, sign_name, push_to, is_del, create_by, create_time, update_by, update_time
        </sql>

</mapper>
