package com.wunding.learn.user.service.imports;

import com.wunding.learn.common.i18n.util.I18nUtil;
import com.wunding.learn.common.i18n.util.ImportTemplateI18nEnum;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.excel.AbstractExcelTemplate;
import com.wunding.learn.common.util.excel.ExcelCheckMessage;
import com.wunding.learn.user.service.dto.UserDTO;
import com.wunding.learn.user.service.model.User;
import com.wunding.learn.user.service.service.IUserService;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;

/**
 * 用户导入excel模板
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON><PERSON></a>
 * @since [版本号]
 */
public class UserDeleteIdentityExcelTemplate extends AbstractExcelTemplate {

    private static final String[] IMPORT_TITLES = {
        "用户账号", "姓名", "部门名称", "条线编码", "条线名称", "管理者层级编码", "管理者层级名称", "职级编码", "职级名称"
    };

    private final IUserService userService;

    public UserDeleteIdentityExcelTemplate() {
        super(IMPORT_TITLES);
        this.userService = SpringUtil.getBean(IUserService.class);
    }

    @Override
    public ExcelCheckMessage doCheck(ExcelCheckMessage excelCheckMessage) {
        List<UserDTO> userList = new ArrayList<>();
        UserDTO user;
        List<String> messages = excelCheckMessage.getMessage();
        String[][] excel = excelCheckMessage.getExcel();

        Set<String> loginNames = new HashSet<>();
        for (int i = 1; i < excel.length; i++) {
            if (StringUtils.isNotBlank(excel[i][0])) {
                loginNames.add(excel[i][0]);
            }
        }
        List<User> dbUserList = userService.getByLoginName(loginNames);
        Map<String, User> userMap = dbUserList.stream()
            .collect(Collectors.toMap(User::getLoginName, Function.identity(), (key1, key2) -> key1));

        int rowNum;
        for (int i = 1; i < excel.length; i++) {
            rowNum = i + 1;
            user = new UserDTO();
            // 用户账号校验
            boolean isUserAccountWrong = isUserAccountWrong(excel, i, messages, rowNum, userMap, user);
            if (isUserAccountWrong) {
                continue;
            }
            //******删除用户身份不用校验是否存在 ，只要不为空则认为不删除********/
            // 验证条线编码是否为空
            if (StringUtils.isNotBlank(excel[i][3])) {
                user.setIdentityBusinessId(excel[i][3].trim());
            }

            // 验证管理者层级编码是否为空
            if (StringUtils.isNotBlank(excel[i][5])) {
                user.setIdentityManageLevelId(excel[i][5].trim());
            }

            // 验证职级编码是否为空
            if (StringUtils.isNotBlank(excel[i][7])) {
                user.setIdentityJobLevelId(excel[i][7].trim());
            }
            userList.add(user);
        }
        excelCheckMessage.setObjects(userList);
        return excelCheckMessage;
    }

    /**
     * <p>  验证用户账户是否有误，是返回true，无误返回false
     *
     * <AUTHOR>
     * @since 2024/6/22
     */
    private boolean isUserAccountWrong(String[][] excel, int i, List<String> messages, int rowNum,
        Map<String, User> userMap, UserDTO user) {
        if (StringUtils.isNotBlank(excel[i][0])) {
            if (excel[i][0].length() > 30) {
                messages.add(longMessage(rowNum, IMPORT_TITLES[0], 30));
                return true;
            } else {
                // 数据库校验
                User checkUser = userMap.get(excel[i][0]);
                if (checkUser == null) {
                    messages.add(
                        I18nUtil.getImportMessage(ImportTemplateI18nEnum.UserNotExist,
                            rowNum));
                    return true;
                }
                user.setId(checkUser.getId());
                user.setLoginName(excel[i][0].trim());
            }
        } else {
            messages.add(emptyMessage(rowNum, IMPORT_TITLES[0]));
            return true;
        }
        return false;
    }
}
