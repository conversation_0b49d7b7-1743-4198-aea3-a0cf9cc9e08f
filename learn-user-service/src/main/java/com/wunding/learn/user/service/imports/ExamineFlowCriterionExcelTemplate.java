package com.wunding.learn.user.service.imports;

import com.wunding.learn.common.i18n.util.I18nUtil;
import com.wunding.learn.common.i18n.util.ImportTemplateI18nEnum;
import com.wunding.learn.common.util.excel.AbstractExcelTemplate;
import com.wunding.learn.common.util.excel.ExcelCheckMessage;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.user.service.model.ExamineFlowCriterion;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

/**
 * 审核配置流程评分标准导入excel模板
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @date 2024-02-19
 */
@Slf4j
public class ExamineFlowCriterionExcelTemplate extends AbstractExcelTemplate {

    private static final String[] IMPORT_TITLES = {"维度", "评分标准", "分制"};

    public ExamineFlowCriterionExcelTemplate() {
        super(IMPORT_TITLES);
    }

    @Override
    protected ExcelCheckMessage doCheck(ExcelCheckMessage excelCheckMessage) {
        List<String> messages = excelCheckMessage.getMessage();
        List<String[]> rows = excelCheckMessage.getRows();
        List<ExamineFlowCriterion> list = new ArrayList<>();
        List<String> scoreInstitutionStrs = new ArrayList<>();

        int rowNum = 0;
        int sortNo = 0;
        for (String[] row : rows) {
            rowNum++;
            sortNo++;
            ExamineFlowCriterion examineFlowCriterion = new ExamineFlowCriterion();
            examineFlowCriterion.setId(StringUtil.newId());
            examineFlowCriterion.setSortNo(sortNo);
            // 维度校验
            String questionName = row[0].trim();
            verifyQuestionName(questionName, rowNum, messages, examineFlowCriterion);
            // 评分标准校验
            String pointDesc = row[1].trim();
            if (StringUtils.hasText(pointDesc)) {
                // 字符长度校验
                if (pointDesc.length() > 1000) {
                    messages.add(longMessage(rowNum, IMPORT_TITLES[0], 1000));
                }
                examineFlowCriterion.setPointDesc(pointDesc);
            } else {
                messages.add(emptyMessage(rowNum, IMPORT_TITLES[1]));
            }
            // 分制校验
            String scoreInstitutionStr = row[2].trim();
            if (StringUtils.hasText(scoreInstitutionStr)) {
                scoreInstitutionStrs.add(scoreInstitutionStr);
                examineFlowCriterion.setScoreInstitution(Integer.valueOf(scoreInstitutionStr));
            } else {
                messages.add(emptyMessage(rowNum, IMPORT_TITLES[2]));
            }
            list.add(examineFlowCriterion);
        }
        // 基本信息校验完了，如果没有错误继续校验分制信息
        if (messages.isEmpty() && !hasSame(scoreInstitutionStrs)) {
            messages.add(
                I18nUtil.getImportMessage(ImportTemplateI18nEnum.ExamineFlowCriterionExcelTemplatePointSystemError));
        }
        excelCheckMessage.setObjects(list);
        return excelCheckMessage;
    }

    private static boolean hasSame(List<? extends Object> list) {
        if (null == list) {
            return false;
        }
        return 1 == new HashSet<Object>(list).size();
    }

    /**
     * <p>  校验问题名称
     *
     * <AUTHOR>
     * @since 2024/6/22
     */
    private void verifyQuestionName(String questionName, int rowNum, List<String> messages,
        ExamineFlowCriterion examineFlowCriterion) {
        if (StringUtils.hasText(questionName)) {
            // 字符长度校验
            if (questionName.length() > 300) {
                messages.add(longMessage(rowNum, IMPORT_TITLES[0], 300));
            }
            examineFlowCriterion.setQuestionName(questionName);
        } else {
            messages.add(emptyMessage(rowNum, IMPORT_TITLES[0]));
        }
    }

}
