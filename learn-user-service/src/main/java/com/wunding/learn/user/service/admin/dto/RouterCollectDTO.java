package com.wunding.learn.user.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(name = "RouterCollectDTO", description = "菜单收藏")
@Data
public class RouterCollectDTO {

    @Schema(description = "名称")
    private String name;

    @Schema(description = "收藏类型，0首页，1报表中心")
    private Integer type;

    @Schema(description = "是否收藏，1是,0否")
    private Integer isCollect;
}
