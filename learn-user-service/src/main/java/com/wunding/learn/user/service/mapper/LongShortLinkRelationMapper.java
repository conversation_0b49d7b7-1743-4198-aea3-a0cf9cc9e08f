package com.wunding.learn.user.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.common.mybatis.cache.MyBatisPlusRedisCache;
import com.wunding.learn.user.service.model.LongShortLinkRelation;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;

/**
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">guoyong</a>
 * @since 2025/6/16 21:05
 */
@Mapper
@CacheNamespace(implementation = MyBatisPlusRedisCache.class, eviction = MyBatisPlusRedisCache.class)
public interface LongShortLinkRelationMapper extends BaseMapper<LongShortLinkRelation> {

}
