package com.wunding.learn.user.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * </p> 系统更新公告DTO
 *
 * <AUTHOR> href="mailto:<EMAIL>">gy</a>
 * @since 2024-11-27
 */
@Data
@Schema(name = "SysNoticeDTO", description = "系统更新公告DTO")
public class SysNoticeDTO implements Serializable {


    /**
     * id
     */
    @Schema(description = "id")
    private String id;


    /**
     * 公告标题
     */
    @Schema(description = "公告标题")
    private String title;

    /**
     * 统一配置
     */
    @Schema(description = "统一配置")
    private Integer unifiedConfiguration;

    /**
     * 公告内容
     */
    @Schema(description = "公告内容")
    private String content;

    /**
     * 公告开始时间
     */
    @Schema(description = "公告开始时间")
    private Date noticeStartTime;

    /**
     * 公告结束时间
     */
    @Schema(description = "公告结束时间")
    private Date noticeEndTime;

    /**
     * 是否可用 0-否 1-是
     */
    @Schema(description = "是否可用 0-否 1-是 ")
    private Integer isAvailable;

}
