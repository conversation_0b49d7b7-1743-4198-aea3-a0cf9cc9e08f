package com.wunding.learn.user.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wunding.learn.common.constant.user.UserErrorNoEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.dto.FileDTO;
import com.wunding.learn.common.enums.ResourceTypeEnum;
import com.wunding.learn.common.enums.file.ImageTypeEnum;
import com.wunding.learn.common.enums.other.LayoutStyleEnum;
import com.wunding.learn.common.enums.other.SystemConfigCodeEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.i18n.util.I18nUtil;
import com.wunding.learn.common.mybatis.service.impl.BaseServiceImpl;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.file.api.constant.ImageBizType;
import com.wunding.learn.file.api.dto.NamePath;
import com.wunding.learn.file.api.service.FileFeign;
import com.wunding.learn.user.api.service.CategoryFeign;
import com.wunding.learn.user.service.admin.dto.DIYHomePageItemConfigDTO;
import com.wunding.learn.user.service.admin.dto.HeadContentLayoutStyleListDTO;
import com.wunding.learn.user.service.admin.dto.HeadContentListDTO;
import com.wunding.learn.user.service.admin.dto.HeadContentRuleListDTO;
import com.wunding.learn.user.service.admin.dto.HeadItemDetailInfoDTO;
import com.wunding.learn.user.service.admin.dto.HomeMenuItemListDTO;
import com.wunding.learn.user.service.admin.dto.HomePageItemConfigDTO;
import com.wunding.learn.user.service.admin.dto.HomePageItemConfigSaveDTO;
import com.wunding.learn.user.service.admin.query.HeadItemDetailQuery;
import com.wunding.learn.user.service.admin.query.HomeItemQuery;
import com.wunding.learn.user.service.mapper.HomePageItemConfigMapper;
import com.wunding.learn.user.service.model.HeadContent;
import com.wunding.learn.user.service.model.HeadContentRule;
import com.wunding.learn.user.service.model.HomePageConfig;
import com.wunding.learn.user.service.model.HomePageItemConfig;
import com.wunding.learn.user.service.model.ItemStyle;
import com.wunding.learn.user.service.service.IHeadContentRuleService;
import com.wunding.learn.user.service.service.IHeadContentService;
import com.wunding.learn.user.service.service.IHomeMenuItemConfigService;
import com.wunding.learn.user.service.service.IHomePageConfigService;
import com.wunding.learn.user.service.service.IHomePageItemConfigService;
import com.wunding.learn.user.service.service.IItemStyleService;
import com.wunding.learn.user.service.service.IParaService;
import com.wunding.learn.user.service.service.IRouterService;
import com.wunding.learn.user.service.service.IUserItemCashService;
import jakarta.annotation.Resource;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <p> 方案栏目配置表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi</a>
 * @since 2022-07-20
 */
@Slf4j
@Service("homePageItemConfigService")
public class HomePageItemConfigServiceImpl extends
    BaseServiceImpl<HomePageItemConfigMapper, HomePageItemConfig> implements
    IHomePageItemConfigService {

    public static final String FILE_HOME_PAGE_IMG_SHOW_HEAD_STYLE = "/file/homePage/img/showHeadStyle/";
    public static final String FILE_HOME_PAGE_IMG_HEAD_STYLE = "/file/homePage/img/headStyle/";
    @Resource
    private FileFeign fileFeign;

    @Resource
    private IHomeMenuItemConfigService homeMenuItemConfigService;

    @Resource
    private CategoryFeign categoryFeign;

    @Resource
    private IParaService paraService;
    @Resource
    private IHeadContentService headContentService;
    @Resource
    private IItemStyleService itemStyleService;
    @Resource
    private IHeadContentRuleService headContentRuleService;
    @Resource
    private IUserItemCashService userItemCashService;
    @Resource
    private HomePageItemConfigMapper homePageItemConfigMapper;
    @Resource
    private IHomePageConfigService homePageConfigService;
    @Resource
    private IRouterService routerService;

    @Override
    public List<HomePageItemConfigDTO> getItemConfigList(String configId) {
        List<HomePageItemConfigDTO> itemConfigDTOList = new ArrayList<>();
        // 获取原数据
        List<HomePageItemConfig> itemConfigList = list(
            new LambdaQueryWrapper<HomePageItemConfig>().eq(HomePageItemConfig::getConfigId, configId)
                .orderByAsc(HomePageItemConfig::getSortNo));
        if (CollectionUtils.isEmpty(itemConfigList)) {
            return itemConfigDTOList;
        }
        // 获取所有图片
        Set<String> idSet = itemConfigList.stream().map(HomePageItemConfig::getId).collect(Collectors.toSet());
        List<NamePath> imageFileNamePathList = fileFeign
            .getImageFileNamePathsByBizIds(idSet, ImageTypeEnum.HEAD_DIY_IMAGE.name());
        Map<String, NamePath> imageUrlsMap = imageFileNamePathList.stream()
            .collect(Collectors.toMap(NamePath::getCategoryId, namePath -> namePath, (val1, val2) -> val1));

        Set<String> layoutStyle = itemConfigList.stream().map(itemConfig ->
                "/" + UserThreadContext.getTenantId() + FILE_HOME_PAGE_IMG_SHOW_HEAD_STYLE + itemConfig.getLayoutStyle()
                    + ".png")
            .collect(Collectors.toSet());
        Map<String, String> demoImgMap = fileFeign.getFileUrl(layoutStyle);
        Set<String> categoryIds = itemConfigList.stream().map(HomePageItemConfig::getCategoryId)
            .collect(Collectors.toSet());
        Map<String, String> categoryNameMap = new HashMap<>();
        // 获取分类名称
        if (!CollectionUtils.isEmpty(categoryIds)) {
            categoryNameMap = categoryFeign.getCategoryNameMapByIdList(categoryIds);
        }
        Map<String, String> finalCategoryNameMap = categoryNameMap;
        // 获取目标数据
        itemConfigDTOList = itemConfigList.stream().map(itemConfig -> {
            HomePageItemConfigDTO dto = new HomePageItemConfigDTO();
            BeanUtils.copyProperties(itemConfig, dto);
            Optional.ofNullable(dto.getCategoryId()).ifPresent(categoryId ->
                dto.setCategoryName(finalCategoryNameMap.get(categoryId))
            );
            dto.setShowImgUrl(demoImgMap.get("/" + UserThreadContext.getTenantId() + FILE_HOME_PAGE_IMG_SHOW_HEAD_STYLE
                + dto.getLayoutStyle() + ".png"));
            // 补充标题背景图信息
            FileDTO fileDTO = new FileDTO();
            NamePath namePath = imageUrlsMap.get(dto.getId());
            if (Objects.isNull(namePath)) {
                return dto;
            }
            BeanUtils.copyProperties(namePath, fileDTO);
            dto.setTitleBgImg(fileDTO);
            return dto;
        }).toList();
        return itemConfigDTOList;
    }

    @Override
    public List<LayoutStyleEnum> getLayoutStyleList() {
        List<LayoutStyleEnum> layoutStyleEnumList = Arrays.asList(LayoutStyleEnum.values());
        layoutStyleEnumList.forEach(layoutStyleEnum -> layoutStyleEnum.setDemoImgUrl(
            fileFeign.getFileUrl("/file/homePath/img/" + layoutStyleEnum.getType() + ".png")
        ));

        return layoutStyleEnumList;
    }

    @Override
    public void saveItemConfigList(String configId, String area, List<HomePageItemConfigSaveDTO> saveDTOList) {
        // 这里在保存diy的栏目时，只进行删除和插入操作
        // 1.获取这个区域的原数据
        List<HomePageItemConfig> itemConfigDBList = list(
            new LambdaQueryWrapper<HomePageItemConfig>().eq(HomePageItemConfig::getConfigId, configId)
                .eq(HomePageItemConfig::getBelongArea, area)
        );
        log.info("itemConfigDBList: " + itemConfigDBList);
        Set<String> idDeletedSet = itemConfigDBList.stream().map(HomePageItemConfig::getId).collect(Collectors.toSet());
        // 3.删除数据（原有的DIY栏目数据全部删除）
        if (!CollectionUtils.isEmpty(idDeletedSet)) {
            log.info("idDeletedSet: " + idDeletedSet);
            baseMapper.deleteByIds(idDeletedSet);
        }
        // 4.保存全量的diy栏目数据
        List<HomePageItemConfig> saveList = new ArrayList<>();
        saveDTOList.forEach(saveDTO -> {
            saveDTO.setId(StringUtils.isEmpty(saveDTO.getId()) ? StringUtil.newId() : saveDTO.getId());
            HomePageItemConfig config = new HomePageItemConfig();
            BeanUtils.copyProperties(saveDTO, config);
            if (saveDTO.getCardInfos() != null){
                config.setCardInfo(String.join(",", saveDTO.getCardInfos()));
            }
            if (StringUtils.isEmpty(saveDTO.getLayoutStyle())) {
                throw new BusinessException(UserErrorNoEnum.ERR_PARAMETER_NO_NULL);
            }
            config.setConfigId(configId);
            config.setCreateBy(UserThreadContext.getUserId());
            config.setCreateTime(new Date());
            saveList.add(config);
        });
        saveBatch2(saveList);
        // 保存图片之前如果有修改先删除旧图片
        List<String> imageCateIdList = saveDTOList.stream().filter(saveDTO -> Objects.nonNull(saveDTO.getTitleBgImg())
                && StringUtils.isBlank(saveDTO.getTitleBgImg().getId())).map(HomePageItemConfigSaveDTO::getId)
            .toList();
        if (!imageCateIdList.isEmpty()) {
            fileFeign.deleteImageByBizIdListAndBizType(imageCateIdList, ImageTypeEnum.HEAD_DIY_IMAGE.name());
        }
        // 5.把图片保存到正式目录
        saveDTOList.stream().filter(saveDTO -> Objects.nonNull(saveDTO.getTitleBgImg())
                && StringUtils.isBlank(saveDTO.getTitleBgImg().getId()) && StringUtils.isNotBlank(
                saveDTO.getTitleBgImg().getPath()))
            .forEach(saveDTO ->
                fileFeign
                    .saveImage(saveDTO.getId(), ImageTypeEnum.HEAD_DIY_IMAGE.name(), saveDTO.getTitleBgImg().getName(),
                        saveDTO.getTitleBgImg().getPath())
            );
    }

    @Override
    public List<HeadContentListDTO> getHeadContentListDTO(String server) {
        // 这里的首页栏目的数据从数据中获取,需要区分服务端
        List<HeadContent> headContentList = headContentService.list(
            new LambdaQueryWrapper<HeadContent>()
                .eq(HeadContent::getServer, server)
                .eq(HeadContent::getIsAvailable, 1)
                .orderByAsc(HeadContent::getType)
        );
        log.info("headContentList" + headContentList);
        List<HeadContentListDTO> headContentListDTOList = new ArrayList<>();
        // 检验首页栏目是否配置内容
        if (headContentList.isEmpty()) {
            return headContentListDTOList;
        }
        // 获取配置栏目的栏目样式
        Set<Integer> userIdSet = headContentList.stream().map(HeadContent::getType).collect(Collectors.toSet());
        List<ItemStyle> itemStyles = itemStyleService.list(new LambdaQueryWrapper<ItemStyle>()
            .eq(ItemStyle::getServer, server)
            .eq(ItemStyle::getIsAvailable, 1)
            .in(ItemStyle::getType, userIdSet)
        );
        log.info("itemStyles: " + itemStyles);
        Map<Integer, List<ItemStyle>> itemStyleCollect = itemStyles.stream().collect(Collectors.groupingBy(ItemStyle::getType));
        log.info("itemStyleCollect: " + itemStyleCollect);
        // 获取模块的适用区域
        headContentList.forEach(headContentDto ->{
            HeadContentListDTO dto = new HeadContentListDTO();
            dto.setType(headContentDto.getType());
            dto.setName(I18nUtil.getMessage(headContentDto.getName()));
            dto.setCode(headContentDto.getCode());
            // v7.8.8版本对版面样式做出调整，将所有的枚举值添加到数据库中
            // 这里只需要存储栏目可放置的区域
            List<ItemStyle> itemStylesType = itemStyleCollect.get(headContentDto.getType());
            log.info("itemStylesType: " + itemStylesType);
            log.info("headContentDto.getType(): " + headContentDto.getType());
            dto.setApplyArea(itemStylesType.stream().map(ItemStyle::getApplyArea).collect(Collectors.toSet()));

            headContentListDTOList.add(dto);
        });
        return headContentListDTOList;
    }

    @Override
    public DIYHomePageItemConfigDTO getDiyHomePageItemConfig(String id, String server) {
        DIYHomePageItemConfigDTO diyHomePageItemConfigDTO = new DIYHomePageItemConfigDTO();
        diyHomePageItemConfigDTO.setServer(server);
        // 1: 首先判断服务端
        // 2: 根据服务端查询对应的区域数据，按照区域进行数据查询
        if (StringUtils.equals("PC", server)) {
            // PC端 - T型布局
            HomePageConfig homePageConfig = homePageConfigService.getById(id);
            log.info("homePageConfig: " + homePageConfig);
            diyHomePageItemConfigDTO.setTPageConfig(homePageConfig.getTPageConfig());
            // PC端 - 1_1区域
            List<HomePageItemConfigDTO> homePageItemConfigDTOListPC11 = getAreaItemConfigList(id, server, "PC11");
            // PC端 - 1_4区域
            List<HomePageItemConfigDTO> homePageItemConfigDTOListPC14 = getAreaItemConfigList(id, server, "PC14");
            // PC端 - 3_4区域
            List<HomePageItemConfigDTO> homePageItemConfigDTOListPC34 = getAreaItemConfigList(id, server, "PC34");

            diyHomePageItemConfigDTO.setHomePageItemConfigDTOListPC11(homePageItemConfigDTOListPC11);
            diyHomePageItemConfigDTO.setHomePageItemConfigDTOListPC14(homePageItemConfigDTOListPC14);
            diyHomePageItemConfigDTO.setHomePageItemConfigDTOListPC34(homePageItemConfigDTOListPC34);
            //背景配置
            diyHomePageItemConfigDTO.setBackgroundColor(homePageConfig.getBackgroundColor());
            diyHomePageItemConfigDTO.setBackgroundRepeat(homePageConfig.getBackgroundRepeat());
            diyHomePageItemConfigDTO.setCurrentImgPriority(homePageConfig.getCurrentImgPriority());
            diyHomePageItemConfigDTO.setBackgroundImage(fileFeign.getImageFileNamePath(id, ImageBizType.DIY_PC_BACKGROUND_IMAGE.toString()));
        } else if (StringUtils.equals("H5", server)) {
            // H5端 - 1_1区域
            List<HomePageItemConfigDTO> homePageItemConfigDTOListH511 = getAreaItemConfigList(id, server, "H511");
            diyHomePageItemConfigDTO.setHomePageItemConfigDTOListH511(homePageItemConfigDTOListH511);
        }
        // 中部菜单数量
        diyHomePageItemConfigDTO.setHomeNum(homePageItemConfigMapper.getHomeNum(id, server));

        // 3: 查询底部菜单的信息（底部信息采用了同一个对象进行返回，需要注意PC端和H5端配置信息的不同）
        HomeItemQuery botMenuQuery = new HomeItemQuery(id, "botMenu", server);
        HomeMenuItemListDTO botMenuItem = homeMenuItemConfigService.getHomeBotItem(botMenuQuery);
        diyHomePageItemConfigDTO.setBotMenu(botMenuItem);

        // 宣传单id
        LambdaQueryWrapper<HomePageItemConfig> wrapper1 = new LambdaQueryWrapper<>();
        // 19是宣传单
        wrapper1.eq(HomePageItemConfig::getType, "19");
        wrapper1.eq(HomePageItemConfig::getConfigId, id);
        wrapper1.orderByAsc(HomePageItemConfig::getSortNo);
        List<HomePageItemConfig> list = homePageItemConfigMapper.selectList(wrapper1);
        List<String> leafletIds = list.stream().map(HomePageItemConfig::getId).collect(Collectors.toList());
        diyHomePageItemConfigDTO.setLeafletIds(leafletIds);

        return diyHomePageItemConfigDTO;
    }

    @Override
    public HeadItemDetailInfoDTO getHeadItemDetailInfo(HeadItemDetailQuery headItemDetailQuery) {
        HeadItemDetailInfoDTO headItemDetailInfoDTO = new HeadItemDetailInfoDTO();
        headItemDetailInfoDTO.setServer(headItemDetailQuery.getServer());
        headItemDetailInfoDTO.setType(headItemDetailQuery.getType());
        HeadContent headContent = headContentService.getOne(
            new LambdaQueryWrapper<HeadContent>()
                .eq(HeadContent::getServer, headItemDetailQuery.getServer())
                .eq(HeadContent::getType,headItemDetailQuery.getType())
                .eq(HeadContent::getIsAvailable, 1)
        );
        if (Objects.isNull(headContent)){
            throw new IllegalArgumentException("您操作的租户暂未在首页配置这个栏目");
        }
        headItemDetailInfoDTO.setName(I18nUtil.getMessage(headContent.getName()));
        headItemDetailInfoDTO.setCode(headContent.getCode());

        // 查询栏目区域版面样式 IItemStyleService
        List<ItemStyle> itemStyleList = itemStyleService.list(new LambdaQueryWrapper<ItemStyle>()
            .eq(ItemStyle::getType, headItemDetailQuery.getType())
            .eq(ItemStyle::getApplyArea, headItemDetailQuery.getArea())
            .eq(ItemStyle::getServer, headItemDetailQuery.getServer())
            .eq(ItemStyle::getIsAvailable, 1)
        );
        log.info("itemStyleList: " + itemStyleList);
        List<HeadContentLayoutStyleListDTO> layoutStyleListDTOList = new ArrayList<>();
        if (!itemStyleList.isEmpty()){
            itemStyleList.forEach(itemStyle -> {
                HeadContentLayoutStyleListDTO headContentLayoutStyle = new HeadContentLayoutStyleListDTO();
                headContentLayoutStyle.setStyleName(I18nUtil.getMessage(itemStyle.getName()));
                headContentLayoutStyle.setCode(itemStyle.getCode());
                headContentLayoutStyle.setProportion(itemStyle.getProportion());
                headContentLayoutStyle.setMaxSize(itemStyle.getMaxSize());
                headContentLayoutStyle.setMinSize(itemStyle.getMinSize());
                // 排版样式版面展示图地址(PC端无样式图片，H5有样式图片)
                headContentLayoutStyle.setShowImgUrl(
                    fileFeign.getFileUrl("/" + UserThreadContext.getTenantId() + FILE_HOME_PAGE_IMG_SHOW_HEAD_STYLE
                        + itemStyle.getCode() + ".png"));
                // 排版样式版面示意图地址（PC端没返回示意图地址）
                if (StringUtils.equals("PC", headItemDetailQuery.getServer())){
                    headContentLayoutStyle.setDemoImgUrl("");
                } else {
                    headContentLayoutStyle.setDemoImgUrl(
                        fileFeign.getFileUrl(
                            "/" + UserThreadContext.getTenantId() + FILE_HOME_PAGE_IMG_HEAD_STYLE + itemStyle.getCode()
                                + ".png")
                    );
                }

                layoutStyleListDTOList.add(headContentLayoutStyle);
            });
        }
        headItemDetailInfoDTO.setLayoutStyleListDTOList(layoutStyleListDTOList);
        // 查询栏目首页内容规则 -- 这里从数据库查询
        List<HeadContentRule> headContentRules = headContentRuleService.list(
            new LambdaQueryWrapper<HeadContentRule>()
                .eq(HeadContentRule::getItemCode, headContent.getCode())
                .eq(HeadContentRule::getIsAvailable, 1)
        );
        log.info("headContentRules: " + headContentRules);
        List<HeadContentRuleListDTO> ruleListDTOList = new ArrayList<>();
        headContentRules.forEach(headContentRuleDTO -> {
            HeadContentRuleListDTO headContentRuleListDTO = new HeadContentRuleListDTO();
            BeanUtils.copyProperties(headContentRuleDTO, headContentRuleListDTO);
            headContentRuleListDTO.setName(I18nUtil.getMessage(headContentRuleDTO.getName()));
            headContentRuleListDTO.setOrderRuleDesc(headContentRuleDTO.getOrderRuleDesc());
            headContentRuleListDTO.setShowRuleDesc(I18nUtil.getMessage(headContentRuleDTO.getShowRuleDesc()));
            headContentRuleListDTO.setRuleType(headContentRuleDTO.getCode());
            headContentRuleListDTO.setContentType(headContent.getType());
            switch (headContentRuleListDTO.getRuleType()) {
                case "popularCourse1":
                    String param1 = paraService.getParaValue(
                        SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_451.getCode());
                    headContentRuleListDTO.setShowRuleDesc(MessageFormat.format(I18nUtil.getMessage(headContentRuleListDTO.getShowRuleDesc()), param1));
                    break;
                case "popularCourse2":
                    String param2 = paraService.getParaValue(
                        SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_461.getCode());
                    headContentRuleListDTO.setShowRuleDesc(MessageFormat.format(I18nUtil.getMessage(headContentRuleListDTO.getShowRuleDesc()), param2));
                    break;
                case "hotTopics1":
                    String param3 = paraService.getParaValue(
                        SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_471.getCode());
                    String param4 = paraService.getParaValue(
                        SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_481.getCode());
                    headContentRuleListDTO.setShowRuleDesc(MessageFormat.format(I18nUtil.getMessage(headContentRuleListDTO.getShowRuleDesc()), param3, param4));
                    break;
                case "hotTopics2":
                    String param5 = paraService.getParaValue(
                        SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_481.getCode());
                    headContentRuleListDTO.setShowRuleDesc(MessageFormat.format(I18nUtil.getMessage(headContentRuleListDTO.getShowRuleDesc()), param5));
                    break;
                case "learnClockIn":
                    String param6 = paraService.getParaValue(
                        SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_920.getCode());
                    headContentRuleListDTO.setName(headContentRuleListDTO.getName().replace("10",param6));
                    headContentRuleListDTO.setShowRuleDesc(I18nUtil.getMessage(headContentRuleListDTO.getShowRuleDesc()).replace("10",param6));
                    break;
                default:
                    break;
            }
            ruleListDTOList.add(headContentRuleListDTO);
        });
        headItemDetailInfoDTO.setRuleListDTOList(ruleListDTOList);

        return headItemDetailInfoDTO;
    }

    @Override
    public HomeMenuItemListDTO getHomeMenuItemList(String id, String server) {
        HomeItemQuery botMenuQuery = new HomeItemQuery(id, "home", server);
        HomeMenuItemListDTO homeMenuItemListDTO = homeMenuItemConfigService.getHomeBotItem(botMenuQuery);
        homeMenuItemListDTO.setServer(server);
        return homeMenuItemListDTO;
    }

    @Override
    public void saveHomeMenuItemList(HomeMenuItemListDTO homeMenuItemListDTO) {
        if (!homeMenuItemListDTO.getMenuItemDTOS().isEmpty()) {
            homeMenuItemConfigService.createOrUpdateBotMenuItem(homeMenuItemListDTO, homeMenuItemListDTO.getServer());
        }
        userItemCashService.removeItemDetailCache();
    }

    public List<HomePageItemConfigDTO>  getAreaItemConfigList(String configId, String server, String belongArea) {
        List<HomePageItemConfigDTO> itemConfigDTOList = new ArrayList<>();

        // 获取相应区域的配置模块
        List<HomePageItemConfig> itemConfigList = list(
            new LambdaQueryWrapper<HomePageItemConfig>()
                .eq(HomePageItemConfig::getConfigId, configId)
                .eq(HomePageItemConfig::getServer, server)
                .eq(HomePageItemConfig::getBelongArea, belongArea)
                .orderByAsc(HomePageItemConfig::getSortNo));
        if (CollectionUtils.isEmpty(itemConfigList)) {
            return itemConfigDTOList;
        }
        log.info("itemConfigList: " + itemConfigList);
        // 获取所有图片
        Set<String> idSet = itemConfigList.stream().map(HomePageItemConfig::getId).collect(Collectors.toSet());
        List<NamePath> imageFileNamePathList = fileFeign
            .getImageFileNamePathsByBizIds(idSet, ImageTypeEnum.HEAD_DIY_IMAGE.name());
        Map<String, NamePath> imageUrlsMap = imageFileNamePathList.stream()
            .collect(Collectors.toMap(NamePath::getCategoryId, namePath -> namePath, (val1, val2) -> val1));

        Set<String> layoutStyle = itemConfigList.stream().map(itemConfig ->
                "/" + UserThreadContext.getTenantId() + FILE_HOME_PAGE_IMG_SHOW_HEAD_STYLE + itemConfig.getLayoutStyle()
                    + ".png")
            .collect(Collectors.toSet());
        Map<String, String> demoImgMap = fileFeign.getFileUrl(layoutStyle);
        Set<String> categoryIds = itemConfigList.stream().map(HomePageItemConfig::getCategoryId)
            .collect(Collectors.toSet());
        Map<String, String> categoryNameMap = new HashMap<>();
        // 获取分类名称
        if (!CollectionUtils.isEmpty(categoryIds)) {
            categoryNameMap = categoryFeign.getCategoryNameMapByIdList(categoryIds);
        }
        Map<String, String> finalCategoryNameMap = categoryNameMap;
        // 获取目标数据
        itemConfigDTOList = itemConfigList.stream().map(itemConfig -> {
            HomePageItemConfigDTO dto = new HomePageItemConfigDTO();
            BeanUtils.copyProperties(itemConfig, dto);
            Optional.ofNullable(dto.getCategoryId()).ifPresent(categoryId ->
                dto.setCategoryName(finalCategoryNameMap.get(categoryId))
            );
            dto.setCardInfos(Arrays.asList(itemConfig.getCardInfo().split(",")));
            // 调整样式比例
            dto.setProportion(itemStyleService.getById(itemConfig.getLayoutStyle()).getProportion());
            if (itemConfig.getType() != 19) {
                dto.setShowImgUrl(demoImgMap.get("/" + UserThreadContext.getTenantId() + FILE_HOME_PAGE_IMG_SHOW_HEAD_STYLE
                    + dto.getLayoutStyle() + ".png"));
            }
            // 补充标题背景图信息
            FileDTO fileDTO = new FileDTO();
            NamePath namePath = imageUrlsMap.get(dto.getId());
            if (Objects.isNull(namePath)) {
                return dto;
            }
            BeanUtils.copyProperties(namePath, fileDTO);
            dto.setTitleBgImg(fileDTO);
            dto.setBelongArea(belongArea);
            return dto;
        }).toList();

        // 检验系统版本权限是否包含要校验的内容；
        List<String> routerIds = routerService.getAllRouterNames();
        Stream<HomePageItemConfigDTO> stream = itemConfigDTOList.stream();
        if(!routerIds.contains(ResourceTypeEnum.NEWS.getRouter())){
            stream = stream.filter(dto -> dto.getType() != 2);
        }
        if(!routerIds.contains(ResourceTypeEnum.TOPIC_SECTION.getRouter())){
            stream = stream.filter(dto -> dto.getType() != 7);
        }
        if(!routerIds.contains(ResourceTypeEnum.CASE_LIBRARY_MANAGE.getRouter())){
            stream = stream.filter(dto -> dto.getType() != 8);
        }
        if(!routerIds.contains(ResourceTypeEnum.TRAIN_PROGRAM.getRouter())){
            stream = stream.filter(dto -> dto.getType() != 9);
        }
        if(!routerIds.contains(ResourceTypeEnum.FACE_PROJECT.getRouter())){
            stream = stream.filter(dto -> dto.getType() != 17);
        }

        return stream.toList();
    }


}
