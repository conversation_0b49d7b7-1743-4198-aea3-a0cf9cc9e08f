package com.wunding.learn.user.service.admin.dto.identity;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/7/19
 */
@Data
@Schema(name = "IdentityUserInfoSaveDTO", description = "身份-用户信息添加对象")
public class IdentityUserInfoSaveDTO {

    @Schema(description = "身份id", required = true)
    @NotBlank(message = "身份id不能为空")
    private String identityId;

    @Schema(description = "用户id", required = true)
    @Size(min = 1, message = "用户id不能为空")
    @NotNull(message = "用户id不能为空")
    private List<String> userIdList;
}

