package com.wunding.learn.user.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.user.service.model.MemberCardMemberLog;
import java.util.List;

/**
 * <p> 会员会员卡记录表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gq</a>
 * @since 2023-07-13
 */
public interface IMemberCardMemberLogService extends IService<MemberCardMemberLog> {

    /**
     * 扣除
     *
     * @param ids
     * @return 被扣除的日志
     */
    List<MemberCardMemberLog> deduct(String ids);
}
