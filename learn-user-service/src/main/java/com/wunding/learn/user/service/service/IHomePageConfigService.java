package com.wunding.learn.user.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.user.service.admin.dto.HomePageConfigDetailDTO;
import com.wunding.learn.user.service.admin.dto.HomePageConfigListDTO;
import com.wunding.learn.user.service.admin.dto.HomePageConfigSaveDTO;
import com.wunding.learn.user.service.admin.query.HomePageConfigQuery;
import com.wunding.learn.user.service.model.HomePageConfig;

/**
 * <p> 首页配置表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi</a>
 * @since 2022-07-20
 */
public interface IHomePageConfigService extends IService<HomePageConfig> {

    /**
     * 查询首页配置数据列表
     *
     * @param query
     * @return
     */
    PageInfo<HomePageConfigListDTO> queryHomePageConfigDTOList(HomePageConfigQuery query);

    /**
     * 按管辖查询首页配置数据列表
     *
     * @param query
     * @return
     */
    PageInfo<HomePageConfigListDTO> getHomePageConfigListByManageArea(HomePageConfigQuery query);

    /**
     * 查询首页配置数据详情
     *
     * @param id
     * @return
     */
    HomePageConfigDetailDTO getHomePageConfigDetail(String id);

    /**
     * 保存首页配置信息
     *
     * @param saveDTO
     */
    void saveHomePageConfig(HomePageConfigSaveDTO saveDTO);

    /**
     * 更新首页配置信息
     *
     * @param id
     * @param saveDTO
     */
    void updateHomePageConfig(String id, HomePageConfigSaveDTO saveDTO);

    /**
     * 启用/禁用
     *
     * @param ids
     * @param isAvailable
     */
    void updateHomePageAvailableStatus(String ids, Integer isAvailable);

    /**
     * 删除数据
     *
     * @param ids
     */
    void deleteHomePageConfig(String ids);

    /**
     * 导出首页配置列表
     */
    void exportData(HomePageConfigQuery query);
}
