package com.wunding.learn.user.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import com.wunding.learn.user.service.admin.query.IdentityPostSystemRelateQuery;
import com.wunding.learn.user.service.model.IdentityPostSystemRelate;
import java.util.List;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 岗位体系关联表 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @since 2024-01-16
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")})
public interface IdentityPostSystemRelateMapper extends BaseMapper<IdentityPostSystemRelate> {

    /**
     * 查询岗位体系关联岗位列表
     *
     * @param query {@link IdentityPostSystemRelateQuery}
     * @return 岗位Id列表
     */
    List<String> selectPostIds(@Param("params") IdentityPostSystemRelateQuery query);

}
