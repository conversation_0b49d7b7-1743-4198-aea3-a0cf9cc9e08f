package com.wunding.learn.user.service.service.impl;

import static com.wunding.learn.common.util.string.StringUtil.newId;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.user.service.admin.dto.LongShortLinkRelationDTO;
import com.wunding.learn.user.service.admin.dto.LongShortLinkRelationQuery;
import com.wunding.learn.user.service.mapper.LongShortLinkRelationMapper;
import com.wunding.learn.user.service.model.LongShortLinkRelation;
import com.wunding.learn.user.service.service.ILongShortLinkRelationService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

/**
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">guoyong</a>
 * @since 2025/6/16 21:04
 */
@Slf4j
@Service("longShortLinkRelationService")
public class LongShortLinkRelationServiceImpl  extends
    ServiceImpl<LongShortLinkRelationMapper, LongShortLinkRelation> implements ILongShortLinkRelationService {

	@Override
	public LongShortLinkRelationDTO getShortLink(LongShortLinkRelationQuery longShortLinkRelationQuery, HttpServletRequest request) {
		// 查询 长连接对应的短连接是否已经存在
		LongShortLinkRelation longShortLinkRelation = this.baseMapper.selectOne(Wrappers.<LongShortLinkRelation>lambdaQuery()
			.eq(LongShortLinkRelation::getOldLink, longShortLinkRelationQuery.getLongLink())
		);
		LongShortLinkRelationDTO longShortLinkRelationDTO = new LongShortLinkRelationDTO();
		String shortLinkId;
		if (longShortLinkRelation != null) {
			shortLinkId = longShortLinkRelation.getShortLinkId();
			longShortLinkRelationDTO.setLongLink(longShortLinkRelation.getOldLink());
		}
		else{
			LongShortLinkRelation newLongShortLinkRelation = new LongShortLinkRelation();
			newLongShortLinkRelation.setOldLink(longShortLinkRelationQuery.getLongLink());
			log.info("newLongShortLinkRelation: 1: " + newLongShortLinkRelation);
			newLongShortLinkRelation.setId(newId());
			newLongShortLinkRelation.setShortLinkId(newId());
			log.info("newLongShortLinkRelation: 2: " + newLongShortLinkRelation);
			this.baseMapper.insert(newLongShortLinkRelation);
			shortLinkId = newLongShortLinkRelation.getShortLinkId();
			longShortLinkRelationDTO.setLongLink(longShortLinkRelationQuery.getLongLink());
		}
		log.info("shortLinkId" + shortLinkId);

		String basePath = "https://" + request.getHeader("Host") + "/user/SL?k=" + shortLinkId;

		log.info("basePath" + basePath);
		longShortLinkRelationDTO.setShortLink(basePath);
		return longShortLinkRelationDTO;
	}

	@Override
	public String redirectRequest(String linkId) {
		// 根据短连接Id查询长连接
		LongShortLinkRelation longShortLinkRelation = this.baseMapper.selectOne(Wrappers.<LongShortLinkRelation>lambdaQuery()
			.eq(LongShortLinkRelation::getShortLinkId, linkId)
		);
		log.info("redirect: " + longShortLinkRelation.getOldLink());
		return longShortLinkRelation.getOldLink();
	}
}
