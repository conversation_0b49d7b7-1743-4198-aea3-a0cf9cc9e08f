package com.wunding.learn.user.service.service.impl;

import com.wunding.learn.common.constant.other.JudgeEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.mybatis.service.impl.BaseServiceImpl;
import com.wunding.learn.common.util.bean.BeanListUtils;
import com.wunding.learn.user.api.service.RouterFeign;
import com.wunding.learn.user.service.admin.dto.CustomMenuDTO;
import com.wunding.learn.user.service.admin.dto.CustomMenuListDTO;
import com.wunding.learn.user.service.enums.CustomMenuEnum;
import com.wunding.learn.user.service.mapper.CustomMenuMapper;
import com.wunding.learn.user.service.model.CustomMenu;
import com.wunding.learn.user.service.service.ICustomMenuService;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 我的页面配置表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">liangzm</a>
 * @since 2025-06-10
 */
@Slf4j
@Service("customMenuService")
public class CustomMenuServiceImpl extends BaseServiceImpl<CustomMenuMapper, CustomMenu> implements ICustomMenuService {

    @Resource
    private RouterFeign routerFeign;


    @Override
    public CustomMenuListDTO list(String type) {
        List<CustomMenu> customMenuList = lambdaQuery().eq(CustomMenu::getType, type).orderByAsc(CustomMenu::getSortNo)
            .orderByAsc(CustomMenu::getId).list();
        for (CustomMenu customMenu : customMenuList) {
            // 处理多语言
            CustomMenuEnum customMenuEnum = CustomMenuEnum.getByCode(customMenu.getCode());
            if (customMenuEnum != null) {
                customMenu.setName(customMenuEnum.getName());
            }
        }
        // 检验系统版本权限是否包含要校验的内容；
        List<String> routerIds = routerFeign.getRouterNames();
        if(!routerIds.contains(CustomMenuEnum.TOPIC.getRouter())){
            customMenuList = customMenuList.stream().filter(o -> !o.getCode().equals(CustomMenuEnum.TOPIC.getCode()))
                .toList();
        }
        if(!routerIds.contains(CustomMenuEnum.SURVEY.getRouter())){
            customMenuList = customMenuList.stream().filter(o -> !o.getCode().equals(CustomMenuEnum.SURVEY.getCode()))
                .toList();
        }
        // 根据isAvailable 状态分组
        List<CustomMenu> enabledList = customMenuList.stream().filter(menu -> menu.getIsAvailable() == 1).toList();
        List<CustomMenu> disableList = customMenuList.stream().filter(menu -> menu.getIsAvailable() == 0).toList();
        CustomMenuListDTO customMenuListDTO = new CustomMenuListDTO();
        customMenuListDTO.setEnabledList(BeanListUtils.copyList(enabledList, CustomMenuDTO.class));
        customMenuListDTO.setDisableList(BeanListUtils.copyList(disableList, CustomMenuDTO.class));
        return customMenuListDTO;
    }

    @Override
    public void updateCustomMenu(CustomMenuListDTO customMenuListDTO) {
        List<CustomMenuDTO> enabledList = customMenuListDTO.getEnabledList();
        List<CustomMenuDTO> disableList = customMenuListDTO.getDisableList();
        if (!CollectionUtils.isEmpty(enabledList)) {
            // 根据enabledList顺序设置CustomMenuDTO的sortNo，从1开始编号
            updateBatchById2(buildList(enabledList, JudgeEnum.CONFIRM));
        }
        if (!CollectionUtils.isEmpty(disableList)) {
            // 根据disableList顺序设置CustomMenuDTO的sortNo，从1开始编号
            updateBatchById2(buildList(disableList, JudgeEnum.DENY));
        }
    }

    @Override
    public List<CustomMenuDTO> enableMenu(String type) {
        log.info("<<<<<<<<<<<<< enableMenu type:{} <<<<<<<<<<<<<", type);
        List<CustomMenu> customMenuList = lambdaQuery().eq(CustomMenu::getType, type)
            .eq(CustomMenu::getIsAvailable, JudgeEnum.CONFIRM.getValue()).orderByAsc(CustomMenu::getSortNo)
            .orderByAsc(CustomMenu::getId).list();
        for (CustomMenu customMenu : customMenuList) {
            // 处理多语言
            CustomMenuEnum customMenuEnum = CustomMenuEnum.getByCode(customMenu.getCode());
            if (customMenuEnum != null) {
                customMenu.setName(customMenuEnum.getName());
            }
        }
        // 检验系统版本权限是否包含要校验的内容；
        List<String> routerIds = routerFeign.getRouterNames();
        if(!routerIds.contains(CustomMenuEnum.TOPIC.getRouter())){
            customMenuList = customMenuList.stream().filter(o -> !o.getCode().equals(CustomMenuEnum.TOPIC.getCode()))
                .toList();
        }
        if(!routerIds.contains(CustomMenuEnum.SURVEY.getRouter())){
            customMenuList = customMenuList.stream().filter(o -> !o.getCode().equals(CustomMenuEnum.SURVEY.getCode()))
                .toList();
        }
        return BeanListUtils.copyList(customMenuList, CustomMenuDTO.class);
    }

    private List<CustomMenu> buildList(List<CustomMenuDTO> customMenuDTOS, JudgeEnum confirm) {
        List<CustomMenu> customMenuList = new ArrayList<>();
        Date now = new Date();
        String userId = UserThreadContext.getUserId();
        for (int i = 0; i < customMenuDTOS.size(); i++) {
            CustomMenuDTO customMenuDTO = customMenuDTOS.get(i);
            customMenuDTO.setSortNo(i + 1);
            // 修改
            CustomMenu customMenu = new CustomMenu();

            customMenu.setId(customMenuDTO.getId());
            customMenu.setIsAvailable(confirm.getValue());
            customMenu.setUpdateTime(now);
            customMenu.setUpdateBy(userId);
            customMenu.setSortNo(customMenuDTO.getSortNo());

            customMenuList.add(customMenu);
        }
        return customMenuList;
    }
}
