package com.wunding.learn.user.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.common.mybatis.cache.MyBatisPlusRedisCache;
import com.wunding.learn.user.api.dto.viewlimit.MainViewLimitBaseInfoDTO;
import com.wunding.learn.user.service.model.NewViewLimitMainData;
import java.util.List;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 可见范围主数据表 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">axr</a>
 * @since 2023-08-07
 */
@Mapper
@CacheNamespace(implementation = MyBatisPlusRedisCache.class, eviction = MyBatisPlusRedisCache.class, properties = {
    @Property(name = "appName", value = "${appName}")})
public interface NewViewLimitMainDataMapper extends BaseMapper<NewViewLimitMainData> {

    /**
     * 下发主方案
     *
     * @param viewLimitId
     * @return
     */
    List<MainViewLimitBaseInfoDTO> getMainLimitBaseInfo(@Param("viewLimitId") Long viewLimitId);

	/**
	 * 根据下发范围Id获取下发范围数据
	 * @param programmeId
	 * @return
	 */
	List<NewViewLimitMainData> getDataByProgrammeId(@Param("programmeId") Long programmeId);
}
