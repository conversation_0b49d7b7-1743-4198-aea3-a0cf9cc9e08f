package com.wunding.learn.user.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.util.date.DateHelper;
import com.wunding.learn.common.util.date.DateUtil;
import com.wunding.learn.file.api.constant.FileBizType;
import com.wunding.learn.file.api.dto.CoursewareOnlineTempInfo;
import com.wunding.learn.file.api.service.FileFeign;
import com.wunding.learn.file.api.service.ZuoKeFeign;
import com.wunding.learn.user.service.admin.dto.SysNoticeDTO;
import com.wunding.learn.user.service.client.dto.SysNoticeClientDTO;
import com.wunding.learn.user.service.mapper.SysNoticeMapper;
import com.wunding.learn.user.service.model.SysNotice;
import com.wunding.learn.user.service.service.ILogoLanService;
import com.wunding.learn.user.service.service.ISysNoticeService;
import jakarta.annotation.Resource;
import java.util.Date;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * <p> 系统更新公告表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gy</a>
 * @since 2024-11-27
 */
@Slf4j
@Service("sysNoticeService")
public class SysNoticeServiceImpl extends ServiceImpl<SysNoticeMapper, SysNotice> implements
    ISysNoticeService {

    @Resource
    private ILogoLanService logoLanService;
    @Resource
    private FileFeign fileFeign;
    @Resource
    private ZuoKeFeign zuoKeFeign;

    private static final String TENANT_NOTICE = "tenantNotice";
    private static final String DEFAULT = "default";
    private static final String SYSTEM_NAME = "{{平台名称}}";

    @Override
    public SysNotice getSysNotice() {
        SysNotice sysNotice = baseMapper.getSysNotice(TENANT_NOTICE);
        if (sysNotice.getUnifiedConfiguration() == 1) {
            sysNotice = baseMapper.getSysNotice(DEFAULT);
            sysNotice.setUnifiedConfiguration(1);
        }
        return sysNotice;
    }

    @Override
    public void saveOrUpdateSysNotice(SysNoticeDTO sysNoticeDTO) {
        SysNotice sysNotice = new SysNotice();
        if (StringUtils.isNotBlank(sysNoticeDTO.getId())) {
            // 更新
            sysNotice.setId(sysNoticeDTO.getId());
            sysNotice.setTitle(sysNoticeDTO.getTitle());
            sysNotice.setContent(sysNoticeDTO.getContent());
            sysNotice.setNoticeStartTime(sysNoticeDTO.getNoticeStartTime());
            sysNotice.setNoticeEndTime(sysNoticeDTO.getNoticeEndTime());
            sysNotice.setIsAvailable(sysNoticeDTO.getIsAvailable());
            sysNotice.setUpdateBy(UserThreadContext.getUserId());
            sysNotice.setUpdateTime(new Date());
            baseMapper.updateById(sysNotice);
            //删除之前的旧文件
            fileFeign.deleteFileByBizIdAndBizType(sysNoticeDTO.getId(), FileBizType.SysNoticeFile.name(), 0);
        } else {
            // 新增
            sysNotice.setId(TENANT_NOTICE);
            sysNotice.setTitle(sysNoticeDTO.getTitle());
            sysNotice.setContent(sysNoticeDTO.getContent());
            sysNotice.setNoticeStartTime(sysNoticeDTO.getNoticeStartTime());
            sysNotice.setNoticeEndTime(sysNoticeDTO.getNoticeEndTime());
            sysNotice.setIsAvailable(sysNoticeDTO.getIsAvailable());
            sysNotice.setCreateBy(UserThreadContext.getUserId());
            sysNotice.setCreateTime(new Date());
            baseMapper.insert(sysNotice);
        }
        // 查询系统名
        String language = UserThreadContext.getAcceptLanguage();
        String systemName = logoLanService.getSystemName(language);
        sysNotice.setTitle(sysNotice.getTitle().replace(SYSTEM_NAME, systemName));

        if (StringUtils.isNotEmpty(sysNotice.getContent())) {
            // 生成index.html文件
            handleOnline(sysNotice);
        }
    }

    private void handleOnline(SysNotice sysNotice) {
        // 构建html中divAuthor标签里的内容
        StringBuilder divAuthor = new StringBuilder();
        divAuthor.append(DateUtil.formatToStr(new Date(), DateHelper.YYYYMMDD_HHMM));
        // 在线做课课件临时文件信息
        CoursewareOnlineTempInfo coursewareOnlineTempInfo = zuoKeFeign.generateInfoZip(sysNotice.getTitle(),
            sysNotice.getContent(), divAuthor.toString());

        //将zip包临时目录文件转移至正式目录
        fileFeign.saveSourceFile(sysNotice.getId(), FileBizType.SysNoticeFile.name(),
            sysNotice.getTitle() + ".zip", coursewareOnlineTempInfo.getZipTempPath());
        //将index.html临时目录文件转移至正式目录
        fileFeign.saveFile(sysNotice.getId(), FileBizType.SysNoticeFile.name(),
            sysNotice.getTitle(), coursewareOnlineTempInfo.getHtmlTempPath());
    }

    @Override
    public void saveOrUpdateTenantSysNotice(SysNoticeDTO sysNoticeDTO) {
        SysNotice sysNotice = new SysNotice();
        // 更新
        sysNotice.setId(DEFAULT);
        sysNotice.setTitle(sysNoticeDTO.getTitle());
        sysNotice.setContent(sysNoticeDTO.getContent());
        sysNotice.setNoticeStartTime(sysNoticeDTO.getNoticeStartTime());
        sysNotice.setNoticeEndTime(sysNoticeDTO.getNoticeEndTime());
        sysNotice.setIsAvailable(sysNoticeDTO.getIsAvailable());
        sysNotice.setUpdateBy(UserThreadContext.getUserId());
        sysNotice.setUpdateTime(new Date());
        baseMapper.updateById(sysNotice);
        // 更新后，删除原来的通知详情文件
        if (Objects.equals(sysNotice.getId(), DEFAULT) && StringUtils.isNotEmpty(sysNotice.getContent().trim())) {
            //删除之前的旧文件
            fileFeign.deleteFileByBizIdAndBizType(sysNotice.getId(), FileBizType.SysNoticeFile.name(), 0);
        }
    }

    public SysNotice getSysNoticeDetail() {
        SysNotice sysNotice = baseMapper.getSysNotice(TENANT_NOTICE);
        if (sysNotice.getUnifiedConfiguration() == 1) {
            sysNotice = baseMapper.getSysNotice(DEFAULT);
        }
        if (sysNotice == null) {
            return null;
        }
        if (sysNotice.getIsAvailable() == 0) {
            return null;
        }
        if (Objects.nonNull(sysNotice.getNoticeStartTime()) && Objects.nonNull(sysNotice.getNoticeEndTime())) {
            Date now = new Date();
            if (now.before(sysNotice.getNoticeStartTime()) || now.after(sysNotice.getNoticeEndTime())) {
                return null;
            }
        }
        String language = UserThreadContext.getAcceptLanguage();
        // 查询系统名
        String systemName = logoLanService.getSystemName(language);
        sysNotice.setTitle(sysNotice.getTitle().replace(SYSTEM_NAME, systemName));
        return sysNotice;
    }

    @Override
    public void updateUnifiedConfiguration(Integer unifiedConfiguration) {
        SysNotice sysNotice = baseMapper.getSysNotice(TENANT_NOTICE);
        // 更新
        sysNotice.setUnifiedConfiguration(unifiedConfiguration);
        sysNotice.setUpdateBy(UserThreadContext.getUserId());
        sysNotice.setUpdateTime(new Date());
        baseMapper.updateById(sysNotice);
    }

    @Override
    public SysNoticeClientDTO getSysNoticeClientDetail() {
        SysNoticeClientDTO sysNoticeClientDTO = new SysNoticeClientDTO();
        SysNotice sysNotice = getSysNoticeDetail();
        if (Objects.isNull(sysNotice)) {
            return null;
        }
        log.info("sysNotice: " + sysNotice);
        BeanUtils.copyProperties(sysNotice, sysNoticeClientDTO);
        // 判断通知详情文件是否存在
        if (StringUtils.isNotEmpty(sysNotice.getContent().trim())) {
            String url = fileFeign.getFileUrl(sysNoticeClientDTO.getId(), FileBizType.SysNoticeFile.name());
            if (StringUtils.isEmpty(url)) {
                // 生成index.html文件
                handleOnline(sysNotice);
                url = fileFeign.getFileUrl(sysNoticeClientDTO.getId(), FileBizType.SysNoticeFile.name());
            }
            // 获取转码后的 index.html
            sysNoticeClientDTO.setHtmlUrl(url);
        }
        return sysNoticeClientDTO;
    }
}
