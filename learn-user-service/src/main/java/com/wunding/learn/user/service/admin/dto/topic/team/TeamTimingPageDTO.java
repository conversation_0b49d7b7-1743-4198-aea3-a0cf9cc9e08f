package com.wunding.learn.user.service.admin.dto.topic.team;

import com.wunding.learn.file.api.dto.NamePath;
import com.wunding.learn.user.service.admin.dto.topic.PageAttributeDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 组队赛计时赛页dto对象
 *
 * <AUTHOR>
 * @date 2023/09/28
 */
@Data
@Accessors(chain = true)
@Schema(name = "TeamTimingPageDTO", description = "组队赛计时赛页dto对象")
public class TeamTimingPageDTO {

    @Schema(description = "当前页id")
    private String id;

    @Schema(description = "主题id")
    private String topicId;

    @Schema(description = "成员A属性")
    private PageAttributeDTO teamTimingMemberA;

    @Schema(description = "成员B")
    private PageAttributeDTO teamTimingMemberB;

    @Schema(description = "背景图")
    private PageAttributeDTO teamTimingBackGroundImg;

    @Schema(description = "进度条")
    private PageAttributeDTO teamTimingProgressBar;

    @Schema(description = "选项按钮,多个元素存在值使用逗号分隔,例如 4个圆角则圆角字段以逗号分隔 8,8,8,8 其他字段以此类推")
    private PageAttributeDTO teamTimingOptionButton;

    @Schema(description = "音乐播放状态")
    private PageAttributeDTO teamTimingMusicPlay;

    @Schema(description = "音乐暂停状态")
    private PageAttributeDTO teamTimingMusicStop;

    @Schema(description = "背景颜色")
    private String backgroundColor;

    @Schema(description = "背景音乐")
    private NamePath backgroundMusic;

}
