package com.wunding.learn.user.service.service.impl;

import static com.wunding.learn.common.util.string.StringUtil.newId;

import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wunding.learn.common.constant.tenant.TenantRedisKeyConstant;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.dto.FileDTO;
import com.wunding.learn.common.enums.user.DefaultLogoImageEnum;
import com.wunding.learn.common.mybatis.service.impl.BaseServiceImpl;
import com.wunding.learn.common.util.bean.BeanListUtils;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.file.api.constant.ImageBizType;
import com.wunding.learn.file.api.dto.NamePath;
import com.wunding.learn.file.api.service.ExportJsonFeign;
import com.wunding.learn.file.api.service.FileFeign;
import com.wunding.learn.file.api.service.SysConfigFeign;
import com.wunding.learn.user.service.admin.dto.ExportLogoJsonDTO;
import com.wunding.learn.user.service.admin.dto.ExportLogoLanDTO;
import com.wunding.learn.user.service.admin.dto.LogoDTO;
import com.wunding.learn.user.service.admin.dto.LogoLanDTO;
import com.wunding.learn.user.service.admin.dto.PrivacyDTO;
import com.wunding.learn.user.service.biz.ILogoLanBiz;
import com.wunding.learn.user.service.dto.ExportLogoConfigDTO;
import com.wunding.learn.user.service.dto.WatermarkConfigDTO;
import com.wunding.learn.user.service.mapper.LogoMapper;
import com.wunding.learn.user.service.model.Logo;
import com.wunding.learn.user.service.model.LogoLan;
import com.wunding.learn.user.service.service.ILogoLanService;
import com.wunding.learn.user.service.service.ILogoService;
import com.wunding.learn.user.service.service.IPrivacyService;
import com.wunding.learn.user.service.service.IWatermarkConfigService;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <p> 系统Logo设置表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">admin</a>
 * @since 2023-07-25
 */
@Slf4j
@Service("logoService")
public class LogoServiceImpl extends BaseServiceImpl<LogoMapper, Logo> implements ILogoService {

    public static final String FILE_LOGO_DEFAULT_IMAGE = "/file/logoDefaultImage/";
    public static final String TENANT_CODE_SYSTEM_NAME = "tenant_code:[{}],system_name:[{}]";
    @Resource
    private ILogoLanBiz logoLanBiz;
    @Resource
    private ILogoLanService logoLanService;
    @Resource
    private FileFeign fileFeign;
    @Resource
    private ExportJsonFeign exportJsonFeign;
    @Resource
    private IPrivacyService privacyService;
    @Resource
    private SysConfigFeign sysConfigFeign;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private IWatermarkConfigService watermarkConfigService;

    @Override
    public Integer getLogoIsDownByTenantId() {
        LambdaQueryWrapper<Logo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Logo::getTenantId, UserThreadContext.getTenantId());
        Logo one = getOne(queryWrapper);
        if (one != null) {
            return one.getIsAppDownload();
        }
        return null;
    }

    @Override
    public Logo getLogoByTenantId() {
        LambdaQueryWrapper<Logo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Logo::getTenantId, UserThreadContext.getTenantId());
        return getOne(queryWrapper);
    }

    @Override
    public LogoDTO getLogoDTOByTenantId() {
        String tenantId = UserThreadContext.getTenantId();
        LambdaQueryWrapper<Logo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Logo::getTenantId, tenantId);
        Logo one = getOne(queryWrapper);

        if (one != null) {
            return getLogoDTO(one);
        } else {
            // 调用默认配置
            LogoDTO logoDTO = new LogoDTO();

            // 获取默认配置
            ExportLogoConfigDTO exportLogoConfigDTO = new ExportLogoConfigDTO();
            buildDefaultJsonDTO(null, null, null, null, exportLogoConfigDTO);

            // 进行数据转换
            BeanUtils.copyProperties(exportLogoConfigDTO, logoDTO);

            // 获取默认LOGO文件
            FileDTO logoFileDto = new FileDTO();
            logoFileDto.setUrl(fileFeign.getPublicReadFileUrl(
                "/" + sysConfigFeign.getSysConfigRoot() + DefaultLogoImageEnum.DEFAULT_LOGO_IMAGE.getValue()));
            logoFileDto.setPath(
                "/" + sysConfigFeign.getSysConfigRoot() + DefaultLogoImageEnum.DEFAULT_LOGO_IMAGE.getValue());
            logoDTO.setLogoFile(logoFileDto);

            // 获取默认大图
            FileDTO fileDTO = new FileDTO();
            fileDTO.setUrl(fileFeign.getPublicReadFileUrl("/" + sysConfigFeign.getSysConfigRoot()
                + DefaultLogoImageEnum.DEFAULT_LOGIN_LARGE_LOGO_IMAGE.getValue()));
            fileDTO.setPath("/" + sysConfigFeign.getSysConfigRoot()
                + DefaultLogoImageEnum.DEFAULT_LOGIN_LARGE_LOGO_IMAGE.getValue());
            logoDTO.setLoginPageFile(fileDTO);

            // 获取默认H5背景图
            FileDTO h5BgImage = new FileDTO();
            h5BgImage.setUrl(fileFeign.getPublicReadFileUrl("/" + sysConfigFeign.getSysConfigRoot()
                + DefaultLogoImageEnum.DEFAULT_H5_BACKGROUND_LOGO_IMAGE.getValue()));
            h5BgImage.setPath("/" + sysConfigFeign.getSysConfigRoot()
                + DefaultLogoImageEnum.DEFAULT_H5_BACKGROUND_LOGO_IMAGE.getValue());
            logoDTO.setH5backgroundImage(h5BgImage);

            // 默认语言配置
            List<LogoLanDTO> logoLanDTOList = new ArrayList<>();
            Map<String, ExportLogoLanDTO> logoLanMap = exportLogoConfigDTO.getLogoLan();
            logoLanMap.forEach((k, v) -> {
                LogoLanDTO logoLanDTO = new LogoLanDTO();
                BeanUtils.copyProperties(v, logoLanDTO);
                logoLanDTO.setId(newId());
                logoLanDTO.setLanName(k);
                logoLanDTOList.add(logoLanDTO);
            });
            logoDTO.setLogoLan(logoLanDTOList);

            return logoDTO;
        }
    }

    @NotNull
    private LogoDTO getLogoDTO(Logo one) {
        log.info("logoDBConfig:{}", JsonUtil.objToJson(one));
        LogoDTO logoDTO = new LogoDTO();
        BeanUtils.copyProperties(one, logoDTO);
        List<LogoLanDTO> logoLanListByTenantId = logoLanBiz.getLogoLanListByTenantId();
        logoDTO.setLogoLan(logoLanListByTenantId);
        NamePath sysLogoNamePath = fileFeign.getImageFileNamePath(one.getId(), ImageBizType.SYS_LOGO.name());
        if (sysLogoNamePath != null) {
            FileDTO logoFileDto = new FileDTO();
            BeanUtils.copyProperties(sysLogoNamePath, logoFileDto);
            logoDTO.setLogoFile(logoFileDto);
        } else {
            FileDTO logoFileDto = new FileDTO();
            logoFileDto.setUrl(fileFeign.getPublicReadFileUrl(
                "/" + sysConfigFeign.getSysConfigRoot() + DefaultLogoImageEnum.DEFAULT_LOGO_IMAGE.getValue()));
            logoFileDto.setPath(
                "/" + sysConfigFeign.getSysConfigRoot() + DefaultLogoImageEnum.DEFAULT_LOGO_IMAGE.getValue());
            logoDTO.setLogoFile(logoFileDto);
        }
        NamePath sysLargeLogoNamePath = fileFeign.getImageFileNamePath(one.getId(),
            ImageBizType.SYS_LOGIN_PAGE_LOGO.name());
        if (sysLargeLogoNamePath != null) {
            FileDTO logoFileDto = new FileDTO();
            BeanUtils.copyProperties(sysLargeLogoNamePath, logoFileDto);
            logoDTO.setLoginPageFile(logoFileDto);
        } else {
            FileDTO fileDTO = new FileDTO();
            fileDTO.setUrl(fileFeign.getPublicReadFileUrl("/" + sysConfigFeign.getSysConfigRoot()
                + DefaultLogoImageEnum.DEFAULT_LOGIN_LARGE_LOGO_IMAGE.getValue()));
            fileDTO.setPath("/" + sysConfigFeign.getSysConfigRoot()
                + DefaultLogoImageEnum.DEFAULT_LOGIN_LARGE_LOGO_IMAGE.getValue());
            logoDTO.setLoginPageFile(fileDTO);
        }
        NamePath h5Background = fileFeign.getImageFileNamePath(one.getId(),
            ImageBizType.SYS_H5_BACK_GROUND_LOGO.name());
        if (h5Background != null) {
            FileDTO logoFileDto = new FileDTO();
            BeanUtils.copyProperties(h5Background, logoFileDto);
            logoDTO.setH5backgroundImage(logoFileDto);
        } else {
            FileDTO fileDTO = new FileDTO();
            fileDTO.setUrl(fileFeign.getPublicReadFileUrl("/" + sysConfigFeign.getSysConfigRoot()
                + DefaultLogoImageEnum.DEFAULT_H5_BACKGROUND_LOGO_IMAGE.getValue()));
            fileDTO.setPath("/" + sysConfigFeign.getSysConfigRoot()
                + DefaultLogoImageEnum.DEFAULT_H5_BACKGROUND_LOGO_IMAGE.getValue());
            logoDTO.setH5backgroundImage(fileDTO);
        }
        for (LogoLanDTO dto : logoLanListByTenantId) {
            NamePath logoLanNamePath = fileFeign.getImageFileNamePath(dto.getId(),
                ImageBizType.SYS_LARGE_LOGO.name());
            if (logoLanNamePath != null) {
                FileDTO logoLanFileDto = new FileDTO();
                BeanUtils.copyProperties(logoLanNamePath, logoLanFileDto);
                dto.setLargeLogoFile(logoLanFileDto);
            } else {
                FileDTO logoLanFileDto = new FileDTO();
                logoLanFileDto.setUrl(fileFeign.getPublicReadFileUrl("/" + sysConfigFeign.getSysConfigRoot()
                    + DefaultLogoImageEnum.DEFAULT_LARGE_LOGO_IMAGE.getValue()));
                logoLanFileDto.setPath("/" + sysConfigFeign.getSysConfigRoot()
                    + DefaultLogoImageEnum.DEFAULT_LARGE_LOGO_IMAGE.getValue());
                dto.setLargeLogoFile(logoLanFileDto);
            }
        }
        return logoDTO;
    }

    @Override
    public void saveOrUpdateSysLogo(LogoDTO logoDTO, String tenantId) {
        String id = logoDTO.getId();
        boolean flag;
        if (StringUtils.isBlank(id)) {
            flag = true;
            id = newId();
        } else {
            flag = false;
        }
        List<LogoLanDTO> logoLan = logoDTO.getLogoLan();
        Logo logo = new Logo();
        BeanUtils.copyProperties(logoDTO, logo);
        logo.setId(id);
        logo.setTenantId(UserThreadContext.getTenantId());
        saveOrUpdate2(logo);
        // 保存公司LOGO图片
        fileFeign.saveOrUpdatePublicReadOnePic(id, ImageBizType.SYS_LOGO.name(),
            logo.getTenantId() + ImageBizType.SYS_LOGO,
            logoDTO.getLogoFile().getPath(), flag);
        // 保存公司登录页大图
        if (StringUtils.isNotBlank(logoDTO.getLoginPageFile().getPath()) && !logoDTO.getLoginPageFile().getPath()
            .equals("/" + sysConfigFeign.getSysConfigRoot()
                + DefaultLogoImageEnum.DEFAULT_LOGIN_LARGE_LOGO_IMAGE.getValue())) {
            fileFeign.saveOrUpdatePublicReadOnePic(id, ImageBizType.SYS_LOGIN_PAGE_LOGO.name(),
                logo.getTenantId() + ImageBizType.SYS_LOGIN_PAGE_LOGO, logoDTO.getLoginPageFile().getPath(), flag);
        } else {
            fileFeign.saveOrUpdatePublicReadOnePic(id, ImageBizType.SYS_LOGIN_PAGE_LOGO.name(),
                logo.getTenantId() + ImageBizType.SYS_LOGIN_PAGE_LOGO, "", flag);
        }
        // 保存H5页面背景图
        if (StringUtils.isNotBlank(logoDTO.getH5backgroundImage().getPath()) && !logoDTO.getH5backgroundImage()
            .getPath()
            .equals("/" + sysConfigFeign.getSysConfigRoot()
                + DefaultLogoImageEnum.DEFAULT_H5_BACKGROUND_LOGO_IMAGE.getValue())) {
            fileFeign.saveOrUpdatePublicReadOnePic(id, ImageBizType.SYS_H5_BACK_GROUND_LOGO.name(),
                logo.getTenantId() + ImageBizType.SYS_H5_BACK_GROUND_LOGO, logoDTO.getH5backgroundImage().getPath(),
                flag);
        } else {
            fileFeign.saveOrUpdatePublicReadOnePic(id, ImageBizType.SYS_H5_BACK_GROUND_LOGO.name(),
                logo.getTenantId() + ImageBizType.SYS_H5_BACK_GROUND_LOGO, "", flag);
        }
        List<LogoLanDTO> logoLanListByTenantId = logoLanBiz.getLogoLanListByTenantId();
        List<String> dbLogoLanId = logoLanListByTenantId.stream().map(LogoLanDTO::getId).collect(Collectors.toList());
        List<String> addOrUpdateId = logoLan.stream().map(LogoLanDTO::getId).collect(Collectors.toList());
        List<String> deleteId = dbLogoLanId.stream().filter(dto -> !addOrUpdateId.contains(dto))
            .collect(Collectors.toList());
        logoLanBiz.removeByIds(deleteId);
        logoInfo(logoLan, logo);
        List<LogoLan> logoLands = BeanListUtils.copyListProperties(logoLan, LogoLan::new);
        logoLanBiz.saveLogoLanList(logoLands);
        log.info("logoDTO:{}", JsonUtil.objToJson(logoDTO));
        Logo logoInfo = getById(id);
        this.exportLogoConfig(tenantId, logoInfo.getIsAppDownload(), logoInfo.getIsDownloadCourseware(),
            logoInfo.getSmallAppSignin(), null);
    }

    private void logoInfo(List<LogoLanDTO> logoLan, Logo logo) {
        for (LogoLanDTO dto : logoLan) {
            String logoLanId = dto.getId();
            FileDTO largeLogoFile = dto.getLargeLogoFile();
            if (org.apache.commons.lang3.StringUtils.isBlank(logoLanId)) {
                logoLanId = newId();
                dto.setId(logoLanId);
            }
            if (dto.getLogoStyle() == 0) {
                // 删除公司大图图片
                fileFeign.deleteImageByBizIdAndBizType(logoLanId, ImageBizType.SYS_LARGE_LOGO.name());
            } else {
                if (largeLogoFile != null && StringUtils.isBlank(largeLogoFile.getId()) && StringUtils.isNotBlank(
                    largeLogoFile.getPath()) && !largeLogoFile.getPath().equals("/" + sysConfigFeign.getSysConfigRoot()
                    + DefaultLogoImageEnum.DEFAULT_LARGE_LOGO_IMAGE.getValue())) {
                    // 1 编辑公司LOGO图片
                    fileFeign.saveOrUpdatePublicReadOnePic(logoLanId, ImageBizType.SYS_LARGE_LOGO.name(),
                        logo.getTenantId() + ImageBizType.SYS_LARGE_LOGO, dto.getLargeLogoFile().getPath(), false);
                }
            }
        }
    }

    @Override
    public ExportLogoConfigDTO exportLogoConfig(String tenantId, Integer isAppDownload, Integer isDownloadCourseware,
        Integer smallAppSignin, Integer hideH5Title) {
        if (StringUtils.isBlank(tenantId)) {
            tenantId = UserThreadContext.getTenantId();
        }
        ExportLogoJsonDTO logoDTOByTenantId = getExportLogoDTOByTenantId(tenantId);
        ExportLogoConfigDTO exportLogoConfigDTO = new ExportLogoConfigDTO();
        log.info("logoDTOByTenantId:{}", logoDTOByTenantId);
        if (logoDTOByTenantId == null) {
            buildDefaultJsonDTO(isAppDownload, isDownloadCourseware, smallAppSignin, null, exportLogoConfigDTO);
        } else {
            BeanUtils.copyProperties(logoDTOByTenantId, exportLogoConfigDTO);
            exportLogoConfigDTO.setLogoFile(logoDTOByTenantId.getLogoFile().getUrl());
            if (logoDTOByTenantId.getLoginPageFile() != null) {
                exportLogoConfigDTO.setLoginPageFile(logoDTOByTenantId.getLoginPageFile().getUrl());
            }
            if (logoDTOByTenantId.getH5backgroundImage() != null) {
                exportLogoConfigDTO.setH5backgroundImageUrl(logoDTOByTenantId.getH5backgroundImage().getUrl());
            }

            // 下载APP是否开启
            extractedSetIsAppDownload(isAppDownload, exportLogoConfigDTO, logoDTOByTenantId);

            // 下载课件是否开启
            extractedSetIsDownloadCourseware(isDownloadCourseware, exportLogoConfigDTO, logoDTOByTenantId);

            // 小程序是否允许小程序注册
            exportLogoConfigDTO.setSmallAppSignin(smallAppSignin);
            exportLogoConfigDTO.setHideH5Title(0);
            List<LogoLanDTO> logoLan = logoDTOByTenantId.getLogoLan();
            Map<String, LogoLanDTO> collect = logoLan.stream()
                .collect(Collectors.toMap(LogoLanDTO::getLanName, dto -> dto));
            Map<String, ExportLogoLanDTO> map = new HashMap<>();
            for (Entry<String, LogoLanDTO> s : collect.entrySet()) {
                ExportLogoLanDTO exportLogoLanDTO = new ExportLogoLanDTO();
                LogoLanDTO logoLanDTO = collect.get(s.getKey());
                BeanUtils.copyProperties(logoLanDTO, exportLogoLanDTO);
                if (StringUtils.isNotBlank(logoLanDTO.getLargeLogoFile().getUrl())) {
                    exportLogoLanDTO.setLargeLogoFile(logoLanDTO.getLargeLogoFile().getUrl());
                }
                map.put(s.getKey(), exportLogoLanDTO);
            }
            exportLogoConfigDTO.setLogoLan(map);
            log.info("exportLogoConfigDTO:{}", JsonUtil.objToJson(exportLogoConfigDTO));
            LogoLanDTO zhLogoLanDTO = collect.get("zh");
            LogoLanDTO enLogoLanDTO = collect.get("en");
            zhLogoLanInfo(zhLogoLanDTO, enLogoLanDTO);
        }
        log.info("logoDTOByTenantId:{}", exportLogoConfigDTO);

        //水印配置
        WatermarkConfigDTO watermarkConfig = watermarkConfigService.getWatermarkConfig();
        if (Optional.ofNullable(watermarkConfig).isPresent()) {
            exportLogoConfigDTO.setWatermarkConfig(watermarkConfig);
        }
        exportJsonFeign.exportJson(exportLogoConfigDTO);
        return exportLogoConfigDTO;
    }

    private void zhLogoLanInfo(LogoLanDTO zhLogoLanDTO, LogoLanDTO enLogoLanDTO) {
        if (zhLogoLanDTO != null) {
            log.info(TENANT_CODE_SYSTEM_NAME, UserThreadContext.getTenantId(),
                zhLogoLanDTO.getApplicationPlatformName());
            // 生成租户id和系统平台名称绑定
            redisTemplate.opsForHash().put(TenantRedisKeyConstant.SYSTEM_NAME_KEY, UserThreadContext.getTenantId(),
                zhLogoLanDTO.getApplicationPlatformName());
        } else if (enLogoLanDTO != null) {
            log.info(TENANT_CODE_SYSTEM_NAME, UserThreadContext.getTenantId(),
                enLogoLanDTO.getApplicationPlatformName());
            // 生成租户id和系统平台名称绑定
            redisTemplate.opsForHash().put(TenantRedisKeyConstant.SYSTEM_NAME_KEY, UserThreadContext.getTenantId(),
                enLogoLanDTO.getApplicationPlatformName());
        }
    }

    // 下载APP是否开启
    private void extractedSetIsAppDownload(Integer isAppDownload, ExportLogoConfigDTO exportLogoConfigDTO,
        ExportLogoJsonDTO logoDTOByTenantId) {
        if (isAppDownload == null) {
            // 这里获取系统参数配置，如果是空的则是界面配置保存导出的文件，需要取数据表的配置
            exportLogoConfigDTO.setIsAppDownload(logoDTOByTenantId.getIsAppDownload());
        } else {
            // 如果不为空则是消息发送保存的，这时候是需要取更新到参数配置的数据表
            Logo logo = new Logo();
            logo.setId(logoDTOByTenantId.getId());
            logo.setIsAppDownload(isAppDownload);
            updateById(logo);
            exportLogoConfigDTO.setIsAppDownload(isAppDownload);
        }
    }

    // 下载课件是否开启
    private void extractedSetIsDownloadCourseware(Integer isDownloadCourseware, ExportLogoConfigDTO exportLogoConfigDTO,
        ExportLogoJsonDTO logoDTOByTenantId) {
        if (isDownloadCourseware == null) {
            // 这里获取系统参数配置，如果是空的则是界面配置保存导出的文件，需要取数据表的配置
            exportLogoConfigDTO.setIsDownloadCourseware(logoDTOByTenantId.getIsDownloadCourseware());
        } else {
            // 如果不为空则是消息发送保存的，这时候是需要取更新到参数配置的数据表
            Logo logo = new Logo();
            logo.setId(logoDTOByTenantId.getId());
            logo.setIsDownloadCourseware(isDownloadCourseware);
            updateById(logo);
            exportLogoConfigDTO.setIsDownloadCourseware(isDownloadCourseware);
        }
    }

    private void buildDefaultJsonDTO(Integer isAppDownload, Integer isDownloadCourseware, Integer smallAppSignin,
        Integer hideH5Title,
        ExportLogoConfigDTO exportLogoConfigDTO) {
        Map<String, ExportLogoLanDTO> logoLan = new HashMap<>();

        ExportLogoLanDTO zhLogoLanDTO = new ExportLogoLanDTO();
        zhLogoLanDTO.setLogoStyle(0);
        zhLogoLanDTO.setCopyright("Copyright © 问鼎资讯版权所有");
        zhLogoLanDTO.setLargeLogoFile(fileFeign.getPublicReadFileUrl(
            "/" + sysConfigFeign.getSysConfigRoot() + DefaultLogoImageEnum.DEFAULT_LARGE_LOGO_IMAGE.getValue()));
        zhLogoLanDTO.setCompanyName("问鼎");
        zhLogoLanDTO.setSloganName("www.wdxuexi.com");
        zhLogoLanDTO.setApplicationPlatformName("问鼎云学习");
        logoLan.put("zh", zhLogoLanDTO);

        ExportLogoLanDTO enLogoLanDTO = new ExportLogoLanDTO();
        enLogoLanDTO.setLogoStyle(1);
        enLogoLanDTO.setCopyright("Copyright © 问鼎资讯版权所有");
        enLogoLanDTO.setLargeLogoFile(fileFeign.getPublicReadFileUrl(
            "/" + sysConfigFeign.getSysConfigRoot() + DefaultLogoImageEnum.DEFAULT_LARGE_LOGO_IMAGE.getValue()));
        enLogoLanDTO.setCompanyName("WunDing");
        enLogoLanDTO.setSloganName("www.wdxuexi.com");
        enLogoLanDTO.setApplicationPlatformName("WunDing");
        logoLan.put("en", enLogoLanDTO);
        exportLogoConfigDTO.setIsPrivacy(0);
        exportLogoConfigDTO.setIsAppDownload(isAppDownload);
        exportLogoConfigDTO.setIsDownloadCourseware(isDownloadCourseware);
        exportLogoConfigDTO.setSmallAppSignin(smallAppSignin);
        exportLogoConfigDTO.setHideH5Title(hideH5Title);
        exportLogoConfigDTO.setThemeColor("#1E90FFFF");
        exportLogoConfigDTO.setResponseColor("#216CB4FF");
        exportLogoConfigDTO.setLogoLan(logoLan);
        exportLogoConfigDTO.setLoginPageFile(fileFeign.getPublicReadFileUrl(
            "/" + sysConfigFeign.getSysConfigRoot() + DefaultLogoImageEnum.DEFAULT_LOGIN_LARGE_LOGO_IMAGE.getValue()));
        exportLogoConfigDTO.setH5backgroundImageUrl(fileFeign.getPublicReadFileUrl(
            "/" + sysConfigFeign.getSysConfigRoot()
                + DefaultLogoImageEnum.DEFAULT_H5_BACKGROUND_LOGO_IMAGE.getValue()));
        exportLogoConfigDTO.setLogoFile(fileFeign.getPublicReadFileUrl(
            "/" + sysConfigFeign.getSysConfigRoot() + DefaultLogoImageEnum.DEFAULT_LOGO_IMAGE.getValue()));
        exportLogoConfigDTO.setRetrievePassword(0);
        log.info("buildDefaultJsonDTO-exportLogoConfigDTO:{}", exportLogoConfigDTO);
        log.info(TENANT_CODE_SYSTEM_NAME, UserThreadContext.getTenantId(),
            zhLogoLanDTO.getApplicationPlatformName());
        // 生成租户id和系统平台名称绑定
        redisTemplate.opsForHash().put(TenantRedisKeyConstant.SYSTEM_NAME_KEY, UserThreadContext.getTenantId(),
            zhLogoLanDTO.getApplicationPlatformName());
    }

    public ExportLogoJsonDTO getExportLogoDTOByTenantId(String tenantId) {
        Logo one = getLogo(tenantId);
        if (one != null) {
            log.info("logoDBConfig:{}", JsonUtil.objToJson(one));
            ExportLogoJsonDTO logoDTO = new ExportLogoJsonDTO();
            BeanUtils.copyProperties(one, logoDTO);
            List<LogoLanDTO> logoLanListByTenantId = logoLanBiz.getLogoLanListByTenantId();
            logoDTO.setLogoLan(logoLanListByTenantId);
            // 设置隐私协议是否开启
            setIsPrivacy(logoDTO);
            NamePath sysLogoNamePath = fileFeign.getImageFileNamePath(one.getId(), ImageBizType.SYS_LOGO.name());
            if (sysLogoNamePath != null) {
                FileDTO logoFileDto = new FileDTO();
                BeanUtils.copyProperties(sysLogoNamePath, logoFileDto);
                logoDTO.setLogoFile(logoFileDto);
            } else {
                FileDTO logoFIleDTO = new FileDTO();
                logoFIleDTO.setUrl(fileFeign.getPublicReadFileUrl(
                    "/" + sysConfigFeign.getSysConfigRoot() + FILE_LOGO_DEFAULT_IMAGE + "logoIcon.png"));
                logoFIleDTO.setPath(
                    "/" + sysConfigFeign.getSysConfigRoot() + FILE_LOGO_DEFAULT_IMAGE + "logoIcon.png");
                logoDTO.setLogoFile(logoFIleDTO);
            }
            NamePath sysLargeLogoNamePath = fileFeign.getImageFileNamePath(one.getId(),
                ImageBizType.SYS_LOGIN_PAGE_LOGO.name());
            if (sysLargeLogoNamePath != null) {
                FileDTO logoFIleDTO = new FileDTO();
                BeanUtils.copyProperties(sysLargeLogoNamePath, logoFIleDTO);
                logoDTO.setLoginPageFile(logoFIleDTO);
            } else {
                FileDTO fileDTO = new FileDTO();
                fileDTO.setUrl(fileFeign.getPublicReadFileUrl(
                    "/" + sysConfigFeign.getSysConfigRoot() + FILE_LOGO_DEFAULT_IMAGE + "loginLargeLogo.png"));
                fileDTO.setPath(
                    "/" + sysConfigFeign.getSysConfigRoot() + FILE_LOGO_DEFAULT_IMAGE + "loginLargeLogo.png");
                logoDTO.setLoginPageFile(fileDTO);
            }
            NamePath h5Background = fileFeign.getImageFileNamePath(one.getId(),
                ImageBizType.SYS_H5_BACK_GROUND_LOGO.name());
            logoFileInfo(h5Background, logoDTO, logoLanListByTenantId);
            return logoDTO;
        }
        return null;
    }

    private void logoFileInfo(NamePath h5Background, ExportLogoJsonDTO logoDTO,
        List<LogoLanDTO> logoLanListByTenantId) {
        if (h5Background != null) {
            FileDTO logoFIleDTO = new FileDTO();
            BeanUtils.copyProperties(h5Background, logoFIleDTO);
            logoDTO.setH5backgroundImage(logoFIleDTO);
        } else {
            FileDTO fileDTO = new FileDTO();
            fileDTO.setUrl(fileFeign.getPublicReadFileUrl(
                "/" + sysConfigFeign.getSysConfigRoot() + FILE_LOGO_DEFAULT_IMAGE + "h5Background.png"));
            fileDTO.setPath(
                "/" + sysConfigFeign.getSysConfigRoot() + FILE_LOGO_DEFAULT_IMAGE + "h5Background.png");
            logoDTO.setH5backgroundImage(fileDTO);
        }
        for (LogoLanDTO dto : logoLanListByTenantId) {
            NamePath logoLanNamePath = fileFeign.getImageFileNamePath(dto.getId(),
                ImageBizType.SYS_LARGE_LOGO.name());
            if (logoLanNamePath != null) {
                FileDTO logoLanFileDto = new FileDTO();
                BeanUtils.copyProperties(logoLanNamePath, logoLanFileDto);
                dto.setLargeLogoFile(logoLanFileDto);
            } else {
                FileDTO logoLanFileDto = new FileDTO();
                logoLanFileDto.setUrl(fileFeign.getPublicReadFileUrl(
                    "/" + sysConfigFeign.getSysConfigRoot() + FILE_LOGO_DEFAULT_IMAGE + "largeLogo.png"));
                logoLanFileDto.setPath(
                    "/" + sysConfigFeign.getSysConfigRoot() + FILE_LOGO_DEFAULT_IMAGE + "largeLogo.png");
                dto.setLargeLogoFile(logoLanFileDto);
            }
        }
    }

    // 设置隐私协议是否开启
    private void setIsPrivacy(ExportLogoJsonDTO logoDTO) {
        PrivacyDTO privacy = privacyService.getPrivacy(0);
        if (privacy != null && privacy.getIsAvailable() != null) {
            logoDTO.setIsPrivacy(privacy.getIsAvailable());
        } else {
            logoDTO.setIsPrivacy(0);
        }
    }

    private Logo getLogo(String tenantId) {
        Logo one = new Logo();
        if (StringUtils.isBlank(tenantId)) {
            List<Logo> list = list();
            if (!CollectionUtils.isEmpty(list)) {
                one = list.get(0);
            }
        } else {
            LambdaQueryWrapper<Logo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Logo::getTenantId, tenantId);
            one = getOne(queryWrapper);
        }
        return one;
    }
}
