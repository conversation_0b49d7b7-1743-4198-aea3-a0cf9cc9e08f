package com.wunding.learn.user.service.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>支持搜索枚举
 *
 * <AUTHOR> href="mailto:<EMAIL>">liangzm</a>
 * @since 2024/6/21
 */
@AllArgsConstructor
@Getter
public enum SearchItemEnum {
    
    /**
     * 课程
     */
    COURSE("course", "课程"),
    /**
     * 课件
     */
    COURSE_WARE("courseware", "课件"),
    /**
     * 考试
     */
    EXAM("exam", "考试"),
    /**
     * 调研
     */
    SURVEY("survey","调研"),
    /**
     * 闯关
     */
    CHALLENGE("challenge","闯关"),
    /**
     * 法规/资讯
     */
    NEWS("news", "法规/资讯"),
    /**
     * 话题
     */
    TOPIC("topic","话题"),
    /**
     * 直播
     */
    LIVE("live","直播"),
    /**
     * 学习项目
     */
    PROJECT("project","学习项目"),
    ;
    
    private final String searchFlag;
    
    private final String searchTitle;
    
    /**
     * 是否包含
     *
     * @param searchFlag searchFlag
     * @return 是/否
     */
    public static boolean contains (String searchFlag) {
        for (SearchItemEnum value : SearchItemEnum.values()) {
            if (value.searchFlag.equals(searchFlag)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * getBySearchFlag
     *
     * @param searchFlag searchFlag
     * @return SearchItemEnum
     */
    public static SearchItemEnum getBySearchFlag (String searchFlag) {
        for (SearchItemEnum value : SearchItemEnum.values()) {
            if (value.searchFlag.equals(searchFlag)) {
                return value;
            }
        }
        return null;
    }
}
