<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.user.service.mapper.SysNoticeMapper">
    <!-- 开启二级缓存 -->
    <!--
<cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.user.service.mapper.SysNoticeMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.user.service.model.SysNotice">
        <!--@Table sys_notice-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="title" jdbcType="VARCHAR"
          property="title"/>
        <result column="content" jdbcType="VARCHAR"
          property="content"/>
        <result column="notice_start_time" jdbcType="TIMESTAMP"
          property="noticeStartTime"/>
        <result column="notice_end_time" jdbcType="TIMESTAMP"
          property="noticeEndTime"/>
        <result column="is_del" jdbcType="TINYINT"
          property="isDel"/>
        <result column="is_available" jdbcType="TINYINT"
          property="isAvailable"/>
        <result column="unified_configuration" jdbcType="TINYINT"
          property="unifiedConfiguration"/>
        <result column="create_by" jdbcType="VARCHAR"
          property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP"
          property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR"
          property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP"
          property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, title, content, notice_start_time, notice_end_time is_del, is_available, unified_configuration, create_by, create_time, update_by, update_time
    </sql>

    <select id="getSysNotice" resultType="com.wunding.learn.user.service.model.SysNotice" parameterType="java.lang.String" useCache="false">
        select <include refid="Base_Column_List"></include>
        from sys_notice
        where id = #{id}
    </select>

</mapper>
