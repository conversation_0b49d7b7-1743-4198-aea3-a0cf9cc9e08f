package com.wunding.learn.user.service.dto;

import java.io.Serializable;
import lombok.Data;

/**
 * 人脸识别接口返回json数据对象
 *
 * <AUTHOR> href="mailto:<EMAIL>">WangShuai</a>
 * @date 2020/12/23 15:48
 */
@Data
public class FaceJsonResultDTO implements Serializable {

    private static final long serialVersionUID = 1520526778961620563L;

    private int error_code;
    private String error_msg;
    private Object result;
}
