package com.wunding.learn.user.service.service.impl;

import static com.wunding.learn.common.util.string.StringUtil.newId;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.constant.other.JudgeEnum;
import com.wunding.learn.common.constant.user.UserErrorNoEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.ResourceTypeEnum;
import com.wunding.learn.common.enums.language.LanguageModuleEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.multi.language.dto.MultiLangMessageDTO;
import com.wunding.learn.common.multi.language.model.MultiLangMessage;
import com.wunding.learn.common.multi.language.service.IMultiLangMessageService;
import com.wunding.learn.common.mybatis.service.impl.BaseServiceImpl;
import com.wunding.learn.common.util.bean.BeanListUtils;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.file.api.constant.ImageBizType;
import com.wunding.learn.file.api.dto.NamePath;
import com.wunding.learn.file.api.service.FileFeign;
import com.wunding.learn.user.service.admin.dto.HomeMenuItemDTO;
import com.wunding.learn.user.service.admin.dto.HomeMenuItemListDTO;
import com.wunding.learn.user.service.admin.dto.ItemListDTO;
import com.wunding.learn.user.service.admin.query.HomeItemQuery;
import com.wunding.learn.user.service.admin.query.HomeMenuItemQuery;
import com.wunding.learn.user.service.admin.query.ItemListQuery;
import com.wunding.learn.user.service.constant.UserConstant;
import com.wunding.learn.user.service.enums.ItemEnum;
import com.wunding.learn.user.service.mapper.HomeMenuItemConfigMapper;
import com.wunding.learn.user.service.model.HomeDiyMenu;
import com.wunding.learn.user.service.model.HomeMenuItemConfig;
import com.wunding.learn.user.service.model.Item;
import com.wunding.learn.user.service.service.IHomeDiyMenuService;
import com.wunding.learn.user.service.service.IHomeMenuItemConfigService;
import com.wunding.learn.user.service.service.IItemService;
import com.wunding.learn.user.service.service.IRouterService;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

/**
 * <p> DIY菜单配置表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">liangzm</a>
 * @since 2023-07-10
 */
@Slf4j
@Service("homeMenuItemConfigService")
public class HomeMenuItemConfigServiceImpl extends
    BaseServiceImpl<HomeMenuItemConfigMapper, HomeMenuItemConfig> implements
    IHomeMenuItemConfigService {

    @Resource
    private FileFeign fileFeign;
    @Resource
    private IHomeDiyMenuService homeDiyMenuService;
    @Resource
    @Lazy
    private IItemService iItemService;
    @Resource
    private IMultiLangMessageService multiLangMessageService;
    @Resource
    private IRouterService routerService;

    @Override
    public HomeMenuItemListDTO getHomeMenuItem(HomeMenuItemQuery query) {
        String configId = query.getConfigId();
        String menuType = query.getMenuType();
        HomeMenuItemListDTO listDTO = new HomeMenuItemListDTO();
        HomeDiyMenu homeDiyMenu = homeDiyMenuService.lambdaQuery().eq(HomeDiyMenu::getConfigId, configId)
            .eq(HomeDiyMenu::getMenuType, menuType)
            .eq(HomeDiyMenu::getIsAvailable, JudgeEnum.CONFIRM.getValue()).one();
        // 未配置DIY
        if (Objects.isNull(homeDiyMenu)) {
            return defaultMenuItem(configId, menuType);
        }
        List<MultiLangMessage> multiLangMessageList = multiLangMessageService
            .list(new LambdaQueryWrapper<MultiLangMessage>()
                .eq(MultiLangMessage::getResourceType, UserConstant.HOME_MENU_ITEM_CONFIG)
                .eq(MultiLangMessage::getProperty, UserConstant.HOME_MENU_ITEM_CONFIG_NAME));
        BeanUtils.copyProperties(homeDiyMenu, listDTO);
        // 获取DIY菜单栏位
        LambdaQueryWrapper<HomeMenuItemConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HomeMenuItemConfig::getDiyMenuId, homeDiyMenu.getId());
        queryWrapper.orderByAsc(HomeMenuItemConfig::getSort);
        List<HomeMenuItemConfig> homeMenuItemConfigs = baseMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(homeMenuItemConfigs)) {
            HomeMenuItemListDTO homeMenuItemListDTO = new HomeMenuItemListDTO();
            homeMenuItemListDTO.setId(homeDiyMenu.getId());
            homeMenuItemListDTO.setIsAvailable(1);
            homeMenuItemListDTO.setConfigId(configId);
            homeMenuItemListDTO.setMenuType(menuType);
            homeMenuItemListDTO.setMenuItemDTOS(null);
            return homeMenuItemListDTO;
        }
        // 默认图标
        Set<String> defaultIdSet = homeMenuItemConfigs.stream().map(HomeMenuItemConfig::getItemId)
            .collect(Collectors.toSet());
        Map<String, NamePath> defaultImg = fileFeign.getImageFileNamePathsByBizIds(defaultIdSet,
                ImageBizType.ItemImage.name())
            .stream().collect(Collectors.toMap(NamePath::getCategoryId,
                Function.identity(), (key1, key2) -> key1));
        // 底部菜单默认图标
        Set<String> defaultBotMenuIdSet = homeMenuItemConfigs.stream().map(HomeMenuItemConfig::getItemId)
            .collect(Collectors.toSet());
        Map<String, NamePath> defaultBotMenuImg = fileFeign.getImageFileNamePathsByBizIds(defaultBotMenuIdSet,
                ImageBizType.ItemBotMenuImage.name())
            .stream().collect(Collectors.toMap(NamePath::getCategoryId,
                Function.identity(), (key1, key2) -> key1));

        // 自定义配置的图标
        Set<String> idSet = homeMenuItemConfigs.stream().map(HomeMenuItemConfig::getId).collect(Collectors.toSet());
        Map<String, NamePath> newImagMap = fileFeign.getImageFileNamePathsByBizIds(idSet, ImageBizType.ItemImage.name())
            .stream().collect(Collectors.toMap(NamePath::getCategoryId,
                Function.identity(), (key1, key2) -> key1));
        // 自定义配置的底部菜单图标
        Set<String> botMenuIdSet = homeMenuItemConfigs.stream().map(HomeMenuItemConfig::getId)
            .collect(Collectors.toSet());
        Map<String, NamePath> newBotMenuImagMap = fileFeign.getImageFileNamePathsByBizIds(botMenuIdSet,
            ImageBizType.ItemBotMenuImage.name())
            .stream().collect(Collectors.toMap(NamePath::getCategoryId,
                Function.identity(), (key1, key2) -> key1));

        // 默认菜单名称
        Map<String, Item> itemMap = iItemService.listByIds(defaultIdSet).stream()
            .collect(Collectors.toMap(Item::getId, Function.identity()));

        List<HomeMenuItemDTO> menuItemDTOList = homeMenuItemConfigs.stream().map(item -> {
            HomeMenuItemDTO menuItemDTO = new HomeMenuItemDTO();
            BeanUtils.copyProperties(item, menuItemDTO);
            NamePath namePath = newImagMap.get(item.getId());
            if (namePath != null) {
                menuItemDTO.setIcon(namePath);
            } else {
                menuItemDTO.setIcon(defaultImg.get(item.getItemId()));
            }
            NamePath botMenuNamePath = newBotMenuImagMap.get(item.getId());
            if (botMenuNamePath != null) {
                menuItemDTO.setBotMenuIcon(botMenuNamePath);
            } else {
                menuItemDTO.setBotMenuIcon(defaultBotMenuImg.get(item.getItemId()));
            }
            Item oldItem = itemMap.get(item.getItemId());
            if (Optional.ofNullable(oldItem).isPresent()) {
                menuItemDTO.setOldName(oldItem.getName());
            }
            List<MultiLangMessage> tempMessageList = multiLangMessageList.stream().filter(
                multiLangMessage -> multiLangMessage.getResourceId().equals(menuItemDTO.getId()))
                .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(tempMessageList)) {
                menuItemDTO.setMultiLangMessages(BeanListUtils.copyList(tempMessageList
                    , MultiLangMessageDTO.class));
            } else {
                menuItemDTO.setMultiLangMessages(Collections.emptyList());
            }
            return menuItemDTO;
        }).collect(Collectors.toList());

        listDTO.setMenuItemDTOS(menuItemDTOList);
        return listDTO;
    }

    private HomeMenuItemListDTO defaultMenuItem(String configId, String menuType) {
        HomeMenuItemListDTO homeMenuItemListDTO = new HomeMenuItemListDTO();
        ItemListQuery itemListQuery = new ItemListQuery();
        itemListQuery.setParentId(menuType);
        // 为了查询所有数据
        itemListQuery.setPageSize(Integer.MAX_VALUE);
        itemListQuery.setExport(true);
        PageInfo<ItemListDTO> itemListDTOPageInfo = iItemService.queryDataList(itemListQuery);
        List<ItemListDTO> itemListDTOList = itemListDTOPageInfo.getList();
        if (itemListDTOList != null && itemListDTOList.size() >= 5 && StringUtils.equals(menuType,
            LanguageModuleEnum.botMenu.name())) {
            itemListDTOList = itemListDTOList.subList(0, 5);
        }
        if (Objects.isNull(itemListDTOList)) {
            itemListDTOList = Collections.emptyList();
        }
        List<HomeMenuItemDTO> menuItemDTOS = itemListDTOList.stream().map(itemListDTO -> {
            HomeMenuItemDTO itemDTO = new HomeMenuItemDTO();
            itemDTO.setSort(itemListDTO.getSortNo());
            itemDTO.setItemId(itemListDTO.getId());
            BeanUtils.copyProperties(itemListDTO, itemDTO);
            itemDTO.setOldName(itemListDTO.getName());
            return itemDTO;
        }).collect(Collectors.toList());
        homeMenuItemListDTO.setIsAvailable(1);
        homeMenuItemListDTO.setConfigId(configId);
        homeMenuItemListDTO.setMenuType(menuType);
        homeMenuItemListDTO.setMenuItemDTOS(menuItemDTOS);
        return homeMenuItemListDTO;
    }

    @Override
    public void logicDelete(String id) {
        // 未实现代码
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createOrUpdateMenuItem(HomeMenuItemListDTO updateDTO) {
        String userId = UserThreadContext.getUserId();
        check(updateDTO);
        boolean create = false;
        List<HomeDiyMenu> list = homeDiyMenuService.lambdaQuery()
            .eq(HomeDiyMenu::getConfigId, updateDTO.getConfigId())
            .eq(HomeDiyMenu::getMenuType, updateDTO.getMenuType()).list();
        if (CollectionUtils.isEmpty(list)) {
            create = true;
        }
        // 新建
        List<MultiLangMessage> multiLangMessages = new ArrayList<>();
        if (create) {
            String diyMenuId = newId();
            HomeDiyMenu homeDiyMenu = HomeDiyMenu.builder()
                .id(diyMenuId).menuType(updateDTO.getMenuType()).configId(updateDTO.getConfigId())
                .isAvailable(updateDTO.getIsAvailable()).updateBy(userId).createBy(userId).build();
            homeDiyMenuService.save(homeDiyMenu);
            List<HomeMenuItemConfig> collect = updateDTO.getMenuItemDTOS().stream().map(item -> {
                String menuItemId = newId();

                saveIconAndBotMenuIcon(item, menuItemId);

                HomeMenuItemConfig homeMenuItemConfig = new HomeMenuItemConfig();
                BeanUtils.copyProperties(item, homeMenuItemConfig);
                if (StringUtils.isBlank(homeMenuItemConfig.getItemId())) {
                    homeMenuItemConfig.setItemId(item.getId());
                }
                homeMenuItemConfig.setConfigId(updateDTO.getConfigId());
                homeMenuItemConfig.setId(menuItemId);
                homeMenuItemConfig.setDiyMenuId(diyMenuId);
                homeMenuItemConfig.setCreateBy(userId);
                homeMenuItemConfig.setUpdateBy(userId);
                multiLangMessages.addAll(
                    buildMultiLangMessageList(item.getMultiLangMessages(), homeMenuItemConfig.getId(),
                        UserConstant.HOME_MENU_ITEM_CONFIG,
                        UserConstant.HOME_MENU_ITEM_CONFIG_NAME));
                return homeMenuItemConfig;
            }).collect(Collectors.toList());
            saveBatch2(collect);
            multiLangMessageService.saveBatch(multiLangMessages);
            return;
        }
        // 更新
        HomeDiyMenu homeDiyMenu = new HomeDiyMenu();
        BeanUtils.copyProperties(updateDTO, homeDiyMenu);
        String diyMenuId = list.stream().map(HomeDiyMenu::getId).collect(Collectors.toList()).get(0);
        homeDiyMenu.setId(diyMenuId);
        homeDiyMenu.setUpdateBy(userId);
        homeDiyMenuService.updateById(homeDiyMenu);
        // 1.获取原数据
        List<HomeMenuItemConfig> itemConfigDBList = list(
            new LambdaQueryWrapper<HomeMenuItemConfig>().eq(HomeMenuItemConfig::getDiyMenuId, diyMenuId));
        // 菜单数据为空，删除数据
        if (CollectionUtils.isEmpty(updateDTO.getMenuItemDTOS()) && !CollectionUtils.isEmpty(itemConfigDBList)) {
            List<String> delIds = itemConfigDBList.stream().map(HomeMenuItemConfig::getId)
                .collect(Collectors.toList());
            removeBatchByIds2(delIds);
            multiLangMessageService
                .remove(new LambdaQueryWrapper<MultiLangMessage>().in(MultiLangMessage::getResourceId, delIds)
                    .eq(MultiLangMessage::getResourceType, UserConstant.HOME_MENU_ITEM_CONFIG));
            // 删除图片
            fileFeign.deleteImageByBizIdListAndBizType(new ArrayList<>(delIds), ImageBizType.ItemImage.name());
            fileFeign.deleteImageByBizIdListAndBizType(new ArrayList<>(delIds), ImageBizType.ItemBotMenuImage.name());
        }
        if (CollectionUtils.isEmpty(updateDTO.getMenuItemDTOS())) {
            return;
        }
        Set<String> saveIdSet = updateDTO.getMenuItemDTOS().stream().map(HomeMenuItemDTO::getId)
            .filter(StringUtils::isNotBlank)
            .collect(Collectors.toSet());
        // 2.找到被删除的
        Set<String> idDeletedSet = itemConfigDBList.stream().map(HomeMenuItemConfig::getId)
            .filter(id -> !saveIdSet.contains(id)).collect(Collectors.toSet());
        // 3.删除数据
        if (!CollectionUtils.isEmpty(idDeletedSet)) {
            removeBatchByIds2(idDeletedSet);
            multiLangMessageService
                .remove(new LambdaQueryWrapper<MultiLangMessage>().in(MultiLangMessage::getResourceId, idDeletedSet)
                    .eq(MultiLangMessage::getResourceType, UserConstant.HOME_MENU_ITEM_CONFIG));
            // 删除图片
            fileFeign.deleteImageByBizIdListAndBizType(new ArrayList<>(idDeletedSet), ImageBizType.ItemImage.name());
            fileFeign.deleteImageByBizIdListAndBizType(new ArrayList<>(idDeletedSet),
                ImageBizType.ItemBotMenuImage.name());
        }
        // 4.更新数据
        List<MultiLangMessage> updateMultiLangMessages = new ArrayList<>();
        final List<HomeMenuItemConfig> updateList = updateDTO.getMenuItemDTOS().stream()
            .filter(saveDTO -> {
                if (StringUtils.isNotBlank(saveDTO.getId())) {
                    return null != getById(saveDTO.getId());
                }
                return false;
            })
            .map(saveDTO -> {
                HomeMenuItemConfig config = new HomeMenuItemConfig();
                BeanUtils.copyProperties(saveDTO, config);
                config.setDiyMenuId(diyMenuId);
                // 更新图片
                updatePicture(saveDTO, config);

                config.setUpdateBy(UserThreadContext.getUserId());
                multiLangMessageService
                    .remove(
                        new LambdaQueryWrapper<MultiLangMessage>().in(MultiLangMessage::getResourceId, saveDTO.getId())
                            .eq(MultiLangMessage::getResourceType, UserConstant.HOME_MENU_ITEM_CONFIG));
                Optional.ofNullable(saveDTO.getMultiLangMessages()).ifPresent(multiLangMessageDTOS -> {
                    multiLangMessageDTOS.forEach(multiLangMessageDTO -> {
                        multiLangMessageDTO.setId(null);
                    });
                });
                updateMultiLangMessages.addAll(
                    buildMultiLangMessageList(saveDTO.getMultiLangMessages(), config.getId(),
                        UserConstant.HOME_MENU_ITEM_CONFIG,
                        UserConstant.HOME_MENU_ITEM_CONFIG_NAME));
                return config;
            }).collect(Collectors.toList());
        updateBatchById2(updateList);
        multiLangMessageService.saveOrUpdateBatch(updateMultiLangMessages);
        List<HomeMenuItemConfig> saveList = new ArrayList<>();
        // 5.新增数据
        updateDTO.getMenuItemDTOS().stream()
            .filter(saveDTO -> StringUtils.isBlank(saveDTO.getId()) || null == getById(saveDTO.getId()))
            .forEach(saveDTO -> {
                HomeMenuItemConfig config = new HomeMenuItemConfig();
                BeanUtils.copyProperties(saveDTO, config);
                config.setId(StringUtil.newId());
                if (StringUtils.isBlank(config.getItemId())) {
                    config.setItemId(saveDTO.getId());
                }

                updateItemImageAndItemBotMenuImage(saveDTO, config);

                config.setConfigId(updateDTO.getConfigId());
                config.setDiyMenuId(diyMenuId);
                config.setCreateBy(UserThreadContext.getUserId());
                saveList.add(config);
                Optional.ofNullable(saveDTO.getMultiLangMessages()).ifPresent(multiLangMessageDTOS -> {
                    multiLangMessageDTOS.forEach(multiLangMessageDTO -> {
                        multiLangMessageDTO.setId(null);
                    });
                });
                multiLangMessages.addAll(
                    buildMultiLangMessageList(saveDTO.getMultiLangMessages(), config.getId(),
                        UserConstant.HOME_MENU_ITEM_CONFIG,
                        UserConstant.HOME_MENU_ITEM_CONFIG_NAME));
            });
        saveBatch2(saveList);
        multiLangMessageService.saveOrUpdateBatch(multiLangMessages);
    }

    @Override
    public HomeMenuItemListDTO getHomeBotItem(HomeItemQuery botMenuQuery) {
        String configId = botMenuQuery.getConfigId();
        String menuType = botMenuQuery.getMenuType();
        String server = botMenuQuery.getServer();
        HomeMenuItemListDTO listDTO = new HomeMenuItemListDTO();
        HomeDiyMenu homeDiyMenu = homeDiyMenuService.lambdaQuery()
            .eq(HomeDiyMenu::getConfigId, configId)
            .eq(HomeDiyMenu::getMenuType, menuType)
            .eq(HomeDiyMenu::getServer, server)
            .eq(HomeDiyMenu::getIsAvailable, JudgeEnum.CONFIRM.getValue()).one();
        // 未配置DIY
        if (Objects.isNull(homeDiyMenu)) {
            return defaultMenuItem(configId, menuType);
        }
        List<MultiLangMessage> multiLangMessageList = multiLangMessageService
            .list(new LambdaQueryWrapper<MultiLangMessage>()
                .eq(MultiLangMessage::getResourceType, UserConstant.HOME_MENU_ITEM_CONFIG)
                .eq(MultiLangMessage::getProperty, UserConstant.HOME_MENU_ITEM_CONFIG_NAME));
        BeanUtils.copyProperties(homeDiyMenu, listDTO);
        // 获取DIY菜单栏位
        LambdaQueryWrapper<HomeMenuItemConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HomeMenuItemConfig::getDiyMenuId, homeDiyMenu.getId());
        queryWrapper.orderByAsc(HomeMenuItemConfig::getSort);
        List<HomeMenuItemConfig> homeMenuItemConfigs = baseMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(homeMenuItemConfigs)) {
            HomeMenuItemListDTO homeMenuItemListDTO = new HomeMenuItemListDTO();
            homeMenuItemListDTO.setId(homeDiyMenu.getId());
            homeMenuItemListDTO.setIsAvailable(1);
            homeMenuItemListDTO.setConfigId(configId);
            homeMenuItemListDTO.setMenuType(menuType);
            homeMenuItemListDTO.setMenuItemDTOS(null);
            return homeMenuItemListDTO;
        }
        // 默认图标
        Set<String> defaultIdSet = homeMenuItemConfigs.stream().map(HomeMenuItemConfig::getItemId)
            .collect(Collectors.toSet());
        Map<String, NamePath> defaultImg = fileFeign.getImageFileNamePathsByBizIds(defaultIdSet,
                ImageBizType.ItemImage.name())
            .stream().collect(Collectors.toMap(NamePath::getCategoryId,
                Function.identity(), (key1, key2) -> key1));
        // 底部菜单默认图标
        Set<String> defaultBotMenuIdSet = homeMenuItemConfigs.stream().map(HomeMenuItemConfig::getItemId)
            .collect(Collectors.toSet());
        Map<String, NamePath> defaultBotMenuImg = fileFeign.getImageFileNamePathsByBizIds(defaultBotMenuIdSet,
                ImageBizType.ItemBotMenuImage.name())
            .stream().collect(Collectors.toMap(NamePath::getCategoryId,
                Function.identity(), (key1, key2) -> key1));

        // 自定义配置的图标
        Set<String> idSet = homeMenuItemConfigs.stream().map(HomeMenuItemConfig::getId).collect(Collectors.toSet());
        Map<String, NamePath> newImagMap = fileFeign.getImageFileNamePathsByBizIds(idSet, ImageBizType.ItemImage.name())
            .stream().collect(Collectors.toMap(NamePath::getCategoryId,
                Function.identity(), (key1, key2) -> key1));
        // 自定义配置的底部菜单图标
        Set<String> botMenuIdSet = homeMenuItemConfigs.stream().map(HomeMenuItemConfig::getId)
            .collect(Collectors.toSet());
        Map<String, NamePath> newBotMenuImagMap = fileFeign.getImageFileNamePathsByBizIds(botMenuIdSet,
                ImageBizType.ItemBotMenuImage.name())
            .stream().collect(Collectors.toMap(NamePath::getCategoryId,
                Function.identity(), (key1, key2) -> key1));

        // 默认菜单名称
        Map<String, Item> itemMap = iItemService.listByIds(defaultIdSet).stream()
            .collect(Collectors.toMap(Item::getId, Function.identity()));

        List<HomeMenuItemDTO> menuItemDTOList = homeMenuItemConfigs.stream().map(item -> {
            HomeMenuItemDTO menuItemDTO = new HomeMenuItemDTO();
            BeanUtils.copyProperties(item, menuItemDTO);
            NamePath namePath = newImagMap.get(item.getId());
            if (namePath != null) {
                menuItemDTO.setIcon(namePath);
            } else {
                menuItemDTO.setIcon(defaultImg.get(item.getItemId()));
            }
            NamePath botMenuNamePath = newBotMenuImagMap.get(item.getId());
            if (botMenuNamePath != null) {
                menuItemDTO.setBotMenuIcon(botMenuNamePath);
            } else {
                menuItemDTO.setBotMenuIcon(defaultBotMenuImg.get(item.getItemId()));
            }
            Item oldItem = itemMap.get(item.getItemId());
            if (Optional.ofNullable(oldItem).isPresent()) {
                menuItemDTO.setOldName(oldItem.getName());
            }
            List<MultiLangMessage> tempMessageList = multiLangMessageList.stream().filter(
                    multiLangMessage -> multiLangMessage.getResourceId().equals(menuItemDTO.getId()))
                .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(tempMessageList)) {
                menuItemDTO.setMultiLangMessages(BeanListUtils.copyList(tempMessageList
                    , MultiLangMessageDTO.class));
            } else {
                menuItemDTO.setMultiLangMessages(Collections.emptyList());
            }
            return menuItemDTO;
        }).collect(Collectors.toList());



        // 检验系统版本权限是否包含要校验的内容；
        menuItemDTOList = getHomeMenuItemDTOS(menuItemDTOList);

        listDTO.setMenuItemDTOS(menuItemDTOList);
        return listDTO;
    }

    @NotNull
    private List<HomeMenuItemDTO> getHomeMenuItemDTOS(List<HomeMenuItemDTO> menuItemDTOList) {
        Stream<HomeMenuItemDTO> stream = menuItemDTOList.stream();
        List<String> routerIds = routerService.getAllRouterNames();
        if(!routerIds.contains(ResourceTypeEnum.TOPIC_SECTION.getRouter())){
            stream = stream.filter(dto -> !dto.getItemId().contains(ResourceTypeEnum.TOPIC_SECTION.getRight()));
        }
        if(!routerIds.contains(ResourceTypeEnum.QUALIFICATIONS_MANAGEMENT.getRouter())){
            stream = stream.filter(dto -> !dto.getItemId().contains(ResourceTypeEnum.QUALIFICATIONS_MANAGEMENT.getRight()));
        }
        if(!routerIds.contains(ResourceTypeEnum.RAPID_TRAIN.getRouter())){
            stream = stream.filter(dto -> !dto.getItemId().contains(ResourceTypeEnum.RAPID_TRAIN.getRight()));
        }
        if(!routerIds.contains(ResourceTypeEnum.FACE_PROJECT.getRouter())){
            stream = stream.filter(dto -> !dto.getItemId().contains(ResourceTypeEnum.FACE_PROJECT.getRight()));
        }
        if(!routerIds.contains(ResourceTypeEnum.NEWS.getRouter())){
            stream = stream.filter(dto -> !dto.getItemId().contains(ResourceTypeEnum.NEWS.getRight()));
        }
        if(!routerIds.contains(ResourceTypeEnum.SURVEY.getRouter())){
            stream = stream.filter(dto -> !dto.getItemId().contains(ResourceTypeEnum.SURVEY.getRight()));
        }
        if(!routerIds.contains(ResourceTypeEnum.EXERCISE.getRouter())){
            stream = stream.filter(dto -> !dto.getItemId().contains(ResourceTypeEnum.EXERCISE.getRight()));
        }
        if(!routerIds.contains(ResourceTypeEnum.TRAIN_PROGRAM.getRouter())){
            stream = stream.filter(dto -> !dto.getItemId().contains(ResourceTypeEnum.TRAIN_PROGRAM.getRight()));
        }
        if(!routerIds.contains(ResourceTypeEnum.COACH_TMP_MANAGE.getRouter())){
            stream = stream.filter(dto -> !dto.getItemId().contains(ResourceTypeEnum.COACH_TMP_MANAGE.getRight()));
        }
        if(!routerIds.contains(ResourceTypeEnum.CASE_LIBRARY_MANAGE.getRouter())){
            stream = stream.filter(dto -> !dto.getItemId().contains(ResourceTypeEnum.CASE_LIBRARY_MANAGE.getRight()));
        }
        if(!routerIds.contains(ResourceTypeEnum.RECRUIT.getRouter())){
            stream = stream.filter(dto -> !dto.getItemId().contains(ResourceTypeEnum.RECRUIT.getRight()));
        }
        if(!routerIds.contains(ResourceTypeEnum.APPRAISE.getRouter())){
            stream = stream.filter(dto -> !dto.getItemId().contains(ResourceTypeEnum.APPRAISE.getRight()));
        }
        if(!routerIds.contains(ResourceTypeEnum.ACTIVITY_COIN_EXCHANGE.getRouter())){
            stream = stream.filter(dto -> !dto.getItemId().contains(ResourceTypeEnum.ACTIVITY_COIN_EXCHANGE.getRight()));
        }
        if(!routerIds.contains(ResourceTypeEnum.NEW_ABILITY_LEARNING_MAP.getRouter())){
            stream = stream.filter(dto -> !dto.getItemId().contains(ResourceTypeEnum.NEW_ABILITY_LEARNING_MAP.getRight()));
        }
        menuItemDTOList = stream.toList();
        return menuItemDTOList;
    }

    @Override
    public void createOrUpdateBotMenuItem(HomeMenuItemListDTO botMenu, String server) {
        String userId = UserThreadContext.getUserId();
        // 底部菜单时进行校验
        check(botMenu);
        boolean create = false;
        List<HomeDiyMenu> list = homeDiyMenuService.lambdaQuery()
            .eq(HomeDiyMenu::getConfigId, botMenu.getConfigId())
            .eq(HomeDiyMenu::getServer, server)
            .eq(HomeDiyMenu::getMenuType, botMenu.getMenuType()).list();
        if (CollectionUtils.isEmpty(list)) {
            create = true;
        }
        log.info("list: " + list);
        // 新建
        List<MultiLangMessage> multiLangMessages = new ArrayList<>();
        if (create) {
            String diyMenuId = newId();
            HomeDiyMenu homeDiyMenu = HomeDiyMenu.builder()
                .id(diyMenuId)
                .menuType(botMenu.getMenuType())
                .server(server)
                .configId(botMenu.getConfigId())
                .isAvailable(botMenu.getIsAvailable())
                .updateBy(userId)
                .createBy(userId)
                .build();
            homeDiyMenuService.save(homeDiyMenu);
            List<HomeMenuItemConfig> collect = botMenu.getMenuItemDTOS().stream().map(item -> {
                String menuItemId = newId();

                saveIconAndBotMenuIcon(item, menuItemId);

                HomeMenuItemConfig homeMenuItemConfig = new HomeMenuItemConfig();
                BeanUtils.copyProperties(item, homeMenuItemConfig);
                if (StringUtils.isBlank(homeMenuItemConfig.getItemId())) {
                    homeMenuItemConfig.setItemId(item.getId());
                }
                homeMenuItemConfig.setConfigId(botMenu.getConfigId());
                homeMenuItemConfig.setId(menuItemId);
                homeMenuItemConfig.setDiyMenuId(diyMenuId);
                homeMenuItemConfig.setCreateBy(userId);
                homeMenuItemConfig.setUpdateBy(userId);
                multiLangMessages.addAll(
                    buildMultiLangMessageList(item.getMultiLangMessages(), homeMenuItemConfig.getId(),
                        UserConstant.HOME_MENU_ITEM_CONFIG,
                        UserConstant.HOME_MENU_ITEM_CONFIG_NAME));
                return homeMenuItemConfig;
            }).collect(Collectors.toList());
            saveBatch2(collect);
            multiLangMessageService.saveBatch(multiLangMessages);
            return;
        }
        // 更新
        HomeDiyMenu homeDiyMenu = new HomeDiyMenu();
        BeanUtils.copyProperties(botMenu, homeDiyMenu);
        String diyMenuId = list.stream().map(HomeDiyMenu::getId).collect(Collectors.toList()).get(0);
        homeDiyMenu.setId(diyMenuId);
        homeDiyMenu.setUpdateBy(userId);
        homeDiyMenuService.updateById(homeDiyMenu);
        // 1.获取原数据
        List<HomeMenuItemConfig> itemConfigDBList = list(
            new LambdaQueryWrapper<HomeMenuItemConfig>().eq(HomeMenuItemConfig::getDiyMenuId, diyMenuId));
        // 菜单数据为空，删除数据
        if (CollectionUtils.isEmpty(botMenu.getMenuItemDTOS()) && !CollectionUtils.isEmpty(itemConfigDBList)) {
            List<String> delIds = itemConfigDBList.stream().map(HomeMenuItemConfig::getId)
                .collect(Collectors.toList());
            removeBatchByIds2(delIds);
            multiLangMessageService
                .remove(new LambdaQueryWrapper<MultiLangMessage>().in(MultiLangMessage::getResourceId, delIds)
                    .eq(MultiLangMessage::getResourceType, UserConstant.HOME_MENU_ITEM_CONFIG));
            // 删除图片
            fileFeign.deleteImageByBizIdListAndBizType(new ArrayList<>(delIds), ImageBizType.ItemImage.name());
            fileFeign.deleteImageByBizIdListAndBizType(new ArrayList<>(delIds), ImageBizType.ItemBotMenuImage.name());
        }
        if (CollectionUtils.isEmpty(botMenu.getMenuItemDTOS())) {
            return;
        }
        Set<String> saveIdSet = botMenu.getMenuItemDTOS().stream().map(HomeMenuItemDTO::getId)
            .filter(StringUtils::isNotBlank)
            .collect(Collectors.toSet());
        // 2.找到被删除的
        Set<String> idDeletedSet = itemConfigDBList.stream().map(HomeMenuItemConfig::getId)
            .filter(id -> !saveIdSet.contains(id)).collect(Collectors.toSet());
        // 3.删除数据
        if (!CollectionUtils.isEmpty(idDeletedSet)) {
            removeBatchByIds2(idDeletedSet);
            multiLangMessageService
                .remove(new LambdaQueryWrapper<MultiLangMessage>().in(MultiLangMessage::getResourceId, idDeletedSet)
                    .eq(MultiLangMessage::getResourceType, UserConstant.HOME_MENU_ITEM_CONFIG));
            // 删除图片
            fileFeign.deleteImageByBizIdListAndBizType(new ArrayList<>(idDeletedSet), ImageBizType.ItemImage.name());
            fileFeign.deleteImageByBizIdListAndBizType(new ArrayList<>(idDeletedSet),
                ImageBizType.ItemBotMenuImage.name());
        }
        // 4.更新数据
        List<MultiLangMessage> updateMultiLangMessages = new ArrayList<>();
        final List<HomeMenuItemConfig> updateList = botMenu.getMenuItemDTOS().stream()
            .filter(saveDTO -> {
                if (StringUtils.isNotBlank(saveDTO.getId())) {
                    return null != getById(saveDTO.getId());
                }
                return false;
            })
            .map(saveDTO -> {
                HomeMenuItemConfig config = new HomeMenuItemConfig();
                BeanUtils.copyProperties(saveDTO, config);
                config.setDiyMenuId(diyMenuId);
                // 更新图片
                updatePicture(saveDTO, config);

                config.setUpdateBy(UserThreadContext.getUserId());
                multiLangMessageService
                    .remove(
                        new LambdaQueryWrapper<MultiLangMessage>().in(MultiLangMessage::getResourceId, saveDTO.getId())
                            .eq(MultiLangMessage::getResourceType, UserConstant.HOME_MENU_ITEM_CONFIG));
                Optional.ofNullable(saveDTO.getMultiLangMessages()).ifPresent(multiLangMessageDTOS -> {
                    multiLangMessageDTOS.forEach(multiLangMessageDTO -> {
                        multiLangMessageDTO.setId(null);
                    });
                });
                updateMultiLangMessages.addAll(
                    buildMultiLangMessageList(saveDTO.getMultiLangMessages(), config.getId(),
                        UserConstant.HOME_MENU_ITEM_CONFIG,
                        UserConstant.HOME_MENU_ITEM_CONFIG_NAME));
                return config;
            }).collect(Collectors.toList());
        updateBatchById2(updateList);
        multiLangMessageService.saveOrUpdateBatch(updateMultiLangMessages);
        List<HomeMenuItemConfig> saveList = new ArrayList<>();
        // 5.新增数据
        botMenu.getMenuItemDTOS().stream()
            .filter(saveDTO -> StringUtils.isBlank(saveDTO.getId()) || null == getById(saveDTO.getId()))
            .forEach(saveDTO -> {
                HomeMenuItemConfig config = new HomeMenuItemConfig();
                BeanUtils.copyProperties(saveDTO, config);
                config.setId(StringUtil.newId());
                if (StringUtils.isBlank(config.getItemId())) {
                    config.setItemId(saveDTO.getId());
                }

                updateItemImageAndItemBotMenuImage(saveDTO, config);

                config.setConfigId(botMenu.getConfigId());
                config.setDiyMenuId(diyMenuId);
                config.setCreateBy(UserThreadContext.getUserId());
                saveList.add(config);
                Optional.ofNullable(saveDTO.getMultiLangMessages()).ifPresent(multiLangMessageDTOS -> {
                    multiLangMessageDTOS.forEach(multiLangMessageDTO -> {
                        multiLangMessageDTO.setId(null);
                    });
                });
                multiLangMessages.addAll(
                    buildMultiLangMessageList(saveDTO.getMultiLangMessages(), config.getId(),
                        UserConstant.HOME_MENU_ITEM_CONFIG,
                        UserConstant.HOME_MENU_ITEM_CONFIG_NAME));
            });
        saveBatch2(saveList);
        multiLangMessageService.saveOrUpdateBatch(multiLangMessages);
    }


    /**
     * <p>  更新首页栏目图标和首页底部菜单栏目图标
     *
     * <AUTHOR>
     * @since 2024/6/22
     */
    private void updateItemImageAndItemBotMenuImage(HomeMenuItemDTO saveDTO, HomeMenuItemConfig config){
        if (saveDTO.getIcon() != null && StringUtils.isBlank(saveDTO.getIcon().getCategoryId())) {
            fileFeign.saveOrUpdateOnePic(config.getId(), ImageBizType.ItemImage.name(),
                saveDTO.getIcon().getName(),
                saveDTO.getIcon().getPath(), true);
        }
        if (saveDTO.getBotMenuIcon() != null && StringUtils.isBlank(saveDTO.getBotMenuIcon().getCategoryId())) {
            fileFeign.saveOrUpdateOnePic(config.getId(), ImageBizType.ItemBotMenuImage.name(),
                saveDTO.getBotMenuIcon().getName(),
                saveDTO.getBotMenuIcon().getPath(), true);
        }
    }

    /**
     * <p>  更新图片
     *
     * <AUTHOR>
     * @since 2024/6/22
     */
    private void updatePicture(HomeMenuItemDTO saveDTO, HomeMenuItemConfig config){
        // 更新图片
        if (saveDTO.getIcon() != null && StringUtils.isBlank(saveDTO.getIcon().getCategoryId())) {
            fileFeign.saveOrUpdateOnePic(config.getId(), ImageBizType.ItemImage.name(),
                saveDTO.getIcon().getName(),
                saveDTO.getIcon().getPath(), false);
        }
        if (saveDTO.getBotMenuIcon() != null && StringUtils.isBlank(saveDTO.getBotMenuIcon().getCategoryId())) {
            fileFeign.saveOrUpdateOnePic(config.getId(), ImageBizType.ItemBotMenuImage.name(),
                saveDTO.getBotMenuIcon().getName(),
                saveDTO.getBotMenuIcon().getPath(), false);
        }
    }

    /**
     * <p>  自定义图片保存和底部菜单图标
     *
     * <AUTHOR>
     * @since 2024/6/22
     */
    private void saveIconAndBotMenuIcon(HomeMenuItemDTO item, String menuItemId){
        // 自定义图片保存
        if (item.getIcon() != null && StringUtils.isBlank(item.getIcon().getCategoryId())) {
            fileFeign.saveOrUpdateOnePic(menuItemId, ImageBizType.ItemImage.name(), item.getIcon().getName(),
                item.getIcon().getPath(), true);
        }
        // 底部菜单图标
        if (item.getBotMenuIcon() != null && StringUtils.isBlank(item.getBotMenuIcon().getCategoryId())) {
            fileFeign.saveOrUpdateOnePic(menuItemId, ImageBizType.ItemBotMenuImage.name(),
                item.getBotMenuIcon().getName(),
                item.getBotMenuIcon().getPath(), true);
        }
    }

    private void check(HomeMenuItemListDTO updateDTO) {
        if (StringUtils.equals(ItemEnum.BOTTOM_MENU.getId(), updateDTO.getMenuType())
            && !CollectionUtils.isEmpty(updateDTO.getMenuItemDTOS())
            && updateDTO.getMenuItemDTOS().size() > 5) {
            throw new BusinessException(UserErrorNoEnum.ERR_BOT_MENU_MAX_COUNT);
        }
        // 底部菜单必须包含发现栏目
        if (StringUtils.equals(updateDTO.getMenuType(), LanguageModuleEnum.botMenu.name())
            && Objects.equals(updateDTO.getIsAvailable(), 1)
            && !CollectionUtils.isEmpty(updateDTO.getMenuItemDTOS())
            && !updateDTO.getMenuItemDTOS().stream().map(HomeMenuItemDTO::getItemId).collect(Collectors.toList())
            .contains("botMenu_discover")) {
            throw new BusinessException(UserErrorNoEnum.ERR_BOT_MENU_NOT_CONTAIN_DISCOVER);
        }
    }

    /**
     * 构建多语言列表
     */
    public List<MultiLangMessage> buildMultiLangMessageList(List<MultiLangMessageDTO> list, String resourceId,
        String resourceType, String property) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.EMPTY_LIST;
        }
        List<MultiLangMessage> multiLangMessageList = BeanListUtils.copyList(list, MultiLangMessage.class);
        String userId = UserThreadContext.getUserId();
        multiLangMessageList.forEach(multiLangMessage -> {
            multiLangMessage.setResourceId(resourceId).setResourceType(resourceType).setProperty(property);
            if (ObjectUtils.isEmpty(multiLangMessage.getId())) {
                multiLangMessage.setId(StringUtil.newId()).setCreateTime(new Date()).setCreateBy(userId);
            } else {
                multiLangMessage.setUpdateTime(new Date()).setUpdateBy(userId);
            }
        });
        return multiLangMessageList;
    }
}
