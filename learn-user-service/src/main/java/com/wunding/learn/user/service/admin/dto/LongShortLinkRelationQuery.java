package com.wunding.learn.user.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import lombok.Data;

/**
 * </p> 长短链接请求Query
 *
 * <AUTHOR> href="mailto:<EMAIL>">guoyong</a>
 * @since 2025/6/16 21:52
 */
@Data
@Schema(name = "LongShortLinkRelationQuery", description = "长短链接请求Query")
public class LongShortLinkRelationQuery implements Serializable {

	@Schema(description = "长链接")
	@NotNull(message = "长连接参数不能为空")
	private String longLink;

}
