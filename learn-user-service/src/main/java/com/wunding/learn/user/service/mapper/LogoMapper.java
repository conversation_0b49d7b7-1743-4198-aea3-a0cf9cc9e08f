package com.wunding.learn.user.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import com.wunding.learn.user.service.model.Logo;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 系统Logo设置表 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">admin</a>
    * @since 2023-07-25
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")})
public interface LogoMapper extends BaseMapper<Logo> {

}
