package com.wunding.learn.user.service.biz.impl;

import com.wunding.learn.common.mq.event.TenantNoticeEvent;
import com.wunding.learn.user.service.admin.dto.SysNoticeDTO;
import com.wunding.learn.user.service.biz.INoticeBiz;
import com.wunding.learn.user.service.service.ISysNoticeService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("noticeBiz")
public class NoticeBizImpl implements INoticeBiz {

    @Resource
    private ISysNoticeService sysNoticeService;

    @Override
    public void updateSysNotice(TenantNoticeEvent tenantNoticeEvent) {
        SysNoticeDTO sysNoticeDTO = new SysNoticeDTO();
        sysNoticeDTO.setTitle(tenantNoticeEvent.getTitle());
        sysNoticeDTO.setContent(tenantNoticeEvent.getContent());
        sysNoticeDTO.setNoticeStartTime(tenantNoticeEvent.getNoticeStartTime());
        sysNoticeDTO.setNoticeEndTime(tenantNoticeEvent.getNoticeEndTime());
        sysNoticeDTO.setIsAvailable(tenantNoticeEvent.getIsAvailable());
        sysNoticeService.saveOrUpdateTenantSysNotice(sysNoticeDTO);
    }
}
