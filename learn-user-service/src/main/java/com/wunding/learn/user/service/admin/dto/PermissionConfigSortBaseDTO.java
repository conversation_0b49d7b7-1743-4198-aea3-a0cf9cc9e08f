package com.wunding.learn.user.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.List;
import jakarta.validation.Valid;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 权限目录配置排序基础对象
 *
 * <AUTHOR> href="mailto:<EMAIL>">gcz</a>
 * @since 2023-09-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "PermissionConfigSortDTO", description = "权限目录配置排序基础对象")
public class PermissionConfigSortBaseDTO implements Serializable {

    @Valid
    @Schema(description = "权限目录配置排序列表")
    private List<PermissionConfigSortDTO> sortList;

}
