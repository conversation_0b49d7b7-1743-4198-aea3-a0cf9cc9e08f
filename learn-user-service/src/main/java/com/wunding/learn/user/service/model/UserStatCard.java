package com.wunding.learn.user.service.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户自定义中心卡片表
 *
 * <AUTHOR> href="mailto:<EMAIL>">axr</a>
 * @since 2025-05-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_user_stat_card")
@Schema(name = "UserStatCard对象", description = "用户自定义中心卡片表")
public class UserStatCard implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Schema(description = "主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    @Schema(description = "用户id")
    @TableField("user_id")
    private String userId;

    /**
     * 卡片类型
     */
    @Schema(description = "卡片类型")
    @TableField("card_type")
    private String cardType;

    /**
     * 中心类型：0-学习中心，1-考试中心，2-培训中心，3-运营中心，4-人才发展
     */
    @Schema(description = "中心类型：0-学习中心，1-考试中心，2-培训中心，3-运营中心，4-人才发展")
    @TableField("center_type")
    private Integer centerType;


    /**
     * 是否选中：0-否 1-是
     */
    @Schema(description = "是否选中：0-否 1-是")
    @TableField("is_select")
    private Integer isSelect;


    /**
     * 排序
     */
    @Schema(description = "排序")
    @TableField("sort_no")
    private Integer sortNo;


    /**
     * 新增人
     */
    @Schema(description = "新增人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 新增时间
     */
    @Schema(description = "新增时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;


}