package com.wunding.learn.user.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import com.wunding.learn.user.service.model.ExamineFlowCriterion;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 审核配置流程评分标准表 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @since 2024-01-30
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")})
public interface ExamineFlowCriterionMapper extends BaseMapper<ExamineFlowCriterion> {

}
