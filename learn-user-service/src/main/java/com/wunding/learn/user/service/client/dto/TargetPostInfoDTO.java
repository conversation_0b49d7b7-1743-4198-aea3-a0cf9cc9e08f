package com.wunding.learn.user.service.client.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/7/27
 */
@Data
@Schema(name = "TargetPostInfoDTO", description = "目标岗位信息")
public class TargetPostInfoDTO extends TargetPostChangeDTO {

    @Schema(description = "用户id")
    @NotBlank(message = "用户id不可为空")
    private String userId;

    @Schema(description = "目标岗位名称")
    private String targetPostName;
}
