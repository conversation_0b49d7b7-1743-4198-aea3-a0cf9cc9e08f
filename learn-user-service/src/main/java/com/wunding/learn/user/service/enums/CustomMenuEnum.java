package com.wunding.learn.user.service.enums;

import com.wunding.learn.common.annotation.EnumI18nProperty;
import com.wunding.learn.common.i18n.util.EnumI18n;
import lombok.Getter;

/**
 * <p> 对应 sys_custom_menu 数据 </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">liangzm</a>
 * @since 2025/6/11
 */
@Getter
public enum CustomMenuEnum implements EnumI18n {

    // Statistics 类型
    INTEGRAL(Constants.STATISTICS, "积分", "integral", ""),
    HOURS(Constants.STATISTICS, "学时", "learnTime", ""),
    CREDIT(Constants.STATISTICS, "学分", "learnCredit", ""),
    GOLD(Constants.STATISTICS, "金币", "goldCoin", ""),
    CERTIFICATE(Constants.STATISTICS, "证书", "certificate", ""),
    COLLECT(Constants.STATISTICS, "收藏", "collect", ""),

    // MyStudy 类型
    IDP(Constants.MY_STUDY, "IDP", "idp", ""),
    TASK(Constants.MY_STUDY, "任务", "task", ""),
    COURSE(Constants.MY_STUDY, "课程", "course", ""),
    EXAM(Constants.MY_STUDY, "考试", "exam", ""),
    SURVEY(Constants.MY_STUDY, "调研", "survey", "Survey"),
    PROJECT(Constants.MY_STUDY, "项目", "project", ""),
    LIVE(Constants.MY_STUDY, "直播", "live", ""),
    TOPIC(Constants.MY_STUDY, "话题", "topic", "Topic"),
    QUIZ(Constants.MY_STUDY, "闯关", "quiz", ""),
    SIGN(Constants.MY_STUDY, "签到", "sign", ""),

    // MyMenu 类型
    MY_INFO(Constants.MY_MENU, "我的信息", "myInfo", ""),
    CHANGE_PASSWORD(Constants.MY_MENU, "修改密码", "changePassword", ""),
    FEEDBACK(Constants.MY_MENU, "意见反馈", "feedback", ""),
    MY_TAG(Constants.MY_MENU, "我的标签", "myTag", ""),
    INCENTIVE_EXPLANATION(Constants.MY_MENU, "激励说明", "incentiveExplanation", ""),
    THIRD_PARTY_BINDING(Constants.MY_MENU, "第三方绑定", "thirdPartyBinding", ""),
    PRIVACY_POLICY(Constants.MY_MENU, "隐私政策", "privacyPolicy", ""),
    SWITCH_LANGUAGE(Constants.MY_MENU, "选择语言", "switchLanguage", ""),

    // Other 类型
    ENABLE_STUDENT_UPLOAD(Constants.OTHER, "启用PC端 “学员课件上传”", "enableStudentUpload", ""),
    ENABLE_H5_IDP(Constants.OTHER, "启用H5端 “IDP”", "enableH5Idp", ""),
    ENABLE_H5_TARGET(Constants.OTHER, "启用H5端 “目标岗位”", "enableH5Target", "");

    // 枚举属性
    private final String type;

    @EnumI18nProperty
    private final String name;

    private final String code;

    private final String router;


    // 构造函数
    CustomMenuEnum(String type, String name, String code, String router) {
        this.type = type;
        this.name = name;
        this.code = code;
        this.router = router;
    }

    public static CustomMenuEnum getByCode(String code) {
        for (CustomMenuEnum value : CustomMenuEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String getName() {
        return i18n(name(), name);
    }

    private static class Constants {
        public static final String STATISTICS = "Statistics";
        public static final String MY_STUDY = "MyStudy";
        public static final String MY_MENU = "MyMenu";
        public static final String OTHER = "Other";
    }
}
