package com.wunding.learn.user.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.user.service.admin.dto.LongShortLinkRelationDTO;
import com.wunding.learn.user.service.admin.dto.LongShortLinkRelationQuery;
import com.wunding.learn.user.service.model.LongShortLinkRelation;
import jakarta.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">guoyong</a>
 * @since 2025/6/16 21:02
 */
public interface ILongShortLinkRelationService extends IService<LongShortLinkRelation> {

	LongShortLinkRelationDTO getShortLink(LongShortLinkRelationQuery longShortLinkRelationQuery, HttpServletRequest request);

	String redirectRequest(String linkId);
}
