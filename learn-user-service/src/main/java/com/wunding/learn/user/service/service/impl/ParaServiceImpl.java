package com.wunding.learn.user.service.service.impl;

import static com.wunding.learn.common.enums.other.SystemConfigCodeEnum.POSTER_SHARE_COURSE_TEXT;
import static com.wunding.learn.common.enums.other.SystemConfigCodeEnum.POSTER_SHARE_LIVE_TEXT;
import static com.wunding.learn.common.enums.other.SystemConfigCodeEnum.POSTER_SHARE_NAME;
import static com.wunding.learn.common.enums.other.SystemConfigCodeEnum.POSTER_SHARE_PROJECT_TEXT;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.wunding.learn.common.constant.user.UserErrorNoEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.other.SystemConfigCodeEnum;
import com.wunding.learn.common.enums.redis.UserRedisKeyEnum;
import com.wunding.learn.common.enums.user.DisplayFlagEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.date.DateUtil;
import com.wunding.learn.common.util.signature.MD5Util;
import com.wunding.learn.common.xxljob.xxlJob.UpdateXxlJob;
import com.wunding.learn.common.xxljob.xxlJob.XxlJobInfo;
import com.wunding.learn.common.xxljob.xxlJob.XxlJobTemplate;
import com.wunding.learn.file.api.service.FileFeign;
import com.wunding.learn.user.api.dto.ParaDTO;
import com.wunding.learn.user.service.admin.dto.ParaListDTO;
import com.wunding.learn.user.service.admin.dto.ParaUpdateDTO;
import com.wunding.learn.user.service.admin.query.ParaQuery;
import com.wunding.learn.user.service.biz.ParaProcessStrategy;
import com.wunding.learn.user.service.mapper.ParaMapper;
import com.wunding.learn.user.service.model.AiBaseConfig;
import com.wunding.learn.user.service.model.Para;
import com.wunding.learn.user.service.service.IAiBaseConfigService;
import com.wunding.learn.user.service.service.IParaService;
import com.wunding.learn.user.service.service.IViewLimitExecuteService;
import jakarta.annotation.Resource;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.support.CronExpression;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <p> 系统参数表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">陈跃辉</a>
 * @since 2022-06-01
 */
@Slf4j
@Service("paraService")
public class ParaServiceImpl extends ServiceImpl<ParaMapper, Para> implements IParaService {

    @Resource
    private XxlJobTemplate xxlJobTemplate;

    @Resource
    @Lazy
    private FileFeign fileFeign;

    @Resource
    @Lazy
    private IViewLimitExecuteService viewLimitExecuteService;

    @Resource
    RedissonClient redissonClient;

    @Resource(name = "aiBaseConfigService")
    @Lazy
    private IAiBaseConfigService aiBaseConfigService;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Value("${app.password.default}")
    private String defaultPassword;
    @Resource(name = "userService")
    @Lazy
    private UserServiceImpl userService;

    private static void checkJwtExpireConfig(ParaUpdateDTO updateDTO) {
        try {
            long timeout = Integer.parseInt(updateDTO.getParaValue());
            if (timeout < 600) {
                throw new BusinessException(UserErrorNoEnum.ERR_PARA_JWT_TIMEOUT_MIN);
            }
            // 60天
            if (timeout > 2 * 30 * 24 * 60 * 60) {
                throw new BusinessException(UserErrorNoEnum.ERR_PARA_JWT_TIMEOUT_MAX);
            }
        } catch (NumberFormatException e) {
            throw new BusinessException(UserErrorNoEnum.ERR_PARA_JWT_TIMEOUT_NOT_NUM);
        }

    }

    private static void checkQrConfig(ParaUpdateDTO updateDTO) {
        if (Integer.parseInt(updateDTO.getParaValue()) < 30) {
            throw new BusinessException(UserErrorNoEnum.ERR_QR_EXPIRED_TIME_FAIL);
        }
    }

    private static void checkWeComConfig(ParaUpdateDTO updateDTO) {
        // 企微对接企业数量
        ParaProcessStrategy paraProcessStrategy = SpringUtil.getBean("paraProcessFor3002",
            ParaProcessStrategy.class);
        assert paraProcessStrategy != null;
        paraProcessStrategy.checkValue(updateDTO.getParaValue());
    }

    private static void checkLecturerPrivilegeConfig(ParaUpdateDTO updateDTO) {
        if (Objects.equals(updateDTO.getParaValue(), "-1")) {
            updateDTO.setParaValue("");
        }
    }

    @Override
    public PageInfo<ParaListDTO> list(ParaQuery pageQuery) {
        pageQuery.setExcludeDisplayFlag(
            List.of(DisplayFlagEnum.NOT_DISPLAY.getType(), DisplayFlagEnum.ENGINEERING_MODE_DISPLAY.getType()));

        PageInfo<ParaListDTO> result = PageMethod.startPage(pageQuery.getPageNo(), pageQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.getParaList(pageQuery));

        List<ParaListDTO> list = result.getList();
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(dto -> {
                if (Objects.equals(dto.getDataType(), 6) && Objects.nonNull(dto.getParaValue())) {
                    timeParaHandle(dto);
                }
                if (Objects.equals(dto.getParaType(), 6) && Objects.nonNull(dto.getParaValue())) {
                    dto.setParaValue(fileFeign.getFileUrl(dto.getParaValue()));
                }
            });
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ParaUpdateDTO paraUpdateDTO) throws IOException {
        Para para = Optional.ofNullable(getById(paraUpdateDTO.getId()))
            .orElseThrow(() -> new BusinessException(UserErrorNoEnum.ERR_PARAM_CODE_NOT_EXIST));
        paraUpdateDTO.setParaCode(para.getParaCode());
        // 业务校验处理
        businessCheckHandle(paraUpdateDTO);
        LambdaUpdateWrapper<Para> update = new LambdaUpdateWrapper<>();
        update.set(Para::getParaValue, paraUpdateDTO.getParaValue());
        update.eq(Para::getId, paraUpdateDTO.getId());
        baseMapper.update(null, update);
        // 业务特殊处理
        businessSpecialHandle(paraUpdateDTO);

        // 修改默认登录密码
        if (Objects.equals(paraUpdateDTO.getParaCode(), SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_206.getCode())) {
            String oldPwd = MD5Util.md5Pwd(para.getParaValue());
            String newPwd = MD5Util.md5Pwd(paraUpdateDTO.getParaValue());
            userService.update()
                .eq("password", oldPwd)
                .set("password", newPwd)
                .update();
        }
    }

    @Override
    public ParaUpdateDTO getByCode(String paraCode) {
        LambdaQueryWrapper<Para> query = new LambdaQueryWrapper<>();
        query.eq(Para::getParaCode, paraCode);
        Para p = baseMapper.selectOne(query);
        if (p == null) {
            throw new BusinessException(UserErrorNoEnum.ERR_PARAM_CODE_NOT_EXIST);
        }
        ParaUpdateDTO dto = new ParaUpdateDTO();
        BeanUtils.copyProperties(p, dto);
        return dto;
    }

    @Override
    public String getParaValue(String code) {
        Para para = Optional.ofNullable(
                baseMapper.selectOne(new LambdaQueryWrapper<Para>().eq(Para::getParaCode, code)))
            .orElseThrow(() -> new BusinessException(UserErrorNoEnum.ERR_PARA_NOT_EXIST));
        return para.getParaValue();
    }

    @Override
    public Optional<ParaUpdateDTO> getParaValueEmptyNoError(String code) {
        Optional<Para> para = Optional.ofNullable(
            baseMapper.selectOne(new LambdaQueryWrapper<Para>().eq(Para::getParaCode, code)));
        return para.map(item -> {
            ParaUpdateDTO dto = new ParaUpdateDTO();
            BeanUtils.copyProperties(item, dto);
            return dto;
        });
    }

    @Override
    public PageInfo<ParaListDTO> modelParaList(ParaQuery pageQuery) {
        pageQuery.setDisplayFlag(DisplayFlagEnum.ENGINEERING_MODE_DISPLAY.getType());

        PageInfo<ParaListDTO> result = PageMethod.startPage(pageQuery.getPageNo(), pageQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.getParaList(pageQuery));

        List<ParaListDTO> list = result.getList();
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(dto -> {
                if (Objects.equals(dto.getDataType(), 6) && Objects.nonNull(dto.getParaValue())) {
                    timeParaHandle(dto);
                }
                if (Objects.equals(dto.getParaType(), 6) && Objects.nonNull(dto.getParaValue())) {
                    dto.setParaValue(fileFeign.getFileUrl(dto.getParaValue()));
                }
            });
        }
        return result;
    }

    @Override
    public List<ParaUpdateDTO> getByCodeList(List<String> codeList) {
        if (CollectionUtils.isEmpty(codeList)) {
            log.error("参数查询失败: {}", codeList);
            return Collections.emptyList();
        }
        List<Para> valueList = baseMapper.selectList(
            Wrappers.<Para>lambdaQuery().select(Para::getId, Para::getParaValue).in(Para::getParaCode, codeList));
        return valueList.stream().map(item -> {
            ParaUpdateDTO paraUpdateDTO = new ParaUpdateDTO();
            paraUpdateDTO.setId(item.getId());
            paraUpdateDTO.setParaValue(item.getParaValue());
            return paraUpdateDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<ParaDTO> getParaListByCategoryId(String categoryId) {
        return baseMapper.getParaListByCategoryId(categoryId);
    }

    @Override
    public Map<String, List<ParaDTO>> getParaListByCategoryIdList(List<String> categoryIdList) {
        if (CollectionUtils.isEmpty(categoryIdList)) {
            return new HashMap<>();
        }
        List<ParaDTO> list = baseMapper.getParaListByCategoryIdList(categoryIdList);
        if (CollectionUtils.isEmpty(list)) {
            return new HashMap<>();
        }
        return list.stream().collect(Collectors.groupingBy(ParaDTO::getCategoryId));
    }

    private void timeParaHandle(ParaListDTO paraListDTO) {
        Optional.ofNullable(DateUtil.isoDateStr2Date(paraListDTO.getParaValue()))
            .ifPresent(date -> paraListDTO.setParaValue(DateUtil.formatToStr(date, DateUtil.YYMMDD_HHMMSS)));
    }

    /**
     * 业务校验处理
     *
     * @param updateDTO {@link ParaUpdateDTO}
     */
    private void businessCheckHandle(ParaUpdateDTO updateDTO) throws IOException {
        SystemConfigCodeEnum systemConfigCodeEnum = Optional.ofNullable(
                SystemConfigCodeEnum.get(updateDTO.getParaCode()))
            .orElseThrow(() -> new BusinessException(UserErrorNoEnum.ERR_PARA_ENUM_NOT_EXIST));

        // 参数值格式校验
        if (!systemConfigCodeEnum.validate(updateDTO.getParaValue())) {
            throw new BusinessException(UserErrorNoEnum.ERR_PARA_VALUE_FAIL);
        }

        switch (Objects.requireNonNull(systemConfigCodeEnum)) {
            case SYSTEM_CONFIG_CODE_107:
                checkSynchronizationPoolConfig(updateDTO);
                break;
            case SYSTEM_CONFIG_CODE_105:
                checkCronFormat(updateDTO);
                break;
            case SYSTEM_CONFIG_CODE_21001, SYSTEM_CONFIG_CODE_21002, SYSTEM_CONFIG_CODE_21003, SYSTEM_CONFIG_CODE_21004:
                checkJobQualificationConfig(updateDTO, systemConfigCodeEnum);
                break;
            case SYSTEM_CONFIG_CODE_7001:
                checkLecturerPrivilegeConfig(updateDTO);
                break;
            case SYSTEM_CONFIG_CODE_310, SYSTEM_CONFIG_CODE_311, SYSTEM_CONFIG_CODE_312:
                // 禁用时校验是否全部禁用，如果是，拦截提示
                if (Objects.equals(GeneralJudgeEnum.NEGATIVE.getValue(), Integer.parseInt(updateDTO.getParaValue()))) {
                    long disableCount = count(new LambdaQueryWrapper<Para>().in(Para::getParaCode,
                            Arrays.asList(
                                SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_310.getCode(),
                                SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_311.getCode(),
                                SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_312.getCode()))
                        .ne(Para::getParaCode, systemConfigCodeEnum.getCode()).eq(Para::getParaValue, "0"));
                    if (disableCount == 2) {
                        throw new BusinessException(UserErrorNoEnum.ERR_PARA_VALUE_FAIL_VIDEO_TRANSCODING_DISABLE);
                    }
                }
                break;
            case SYSTEM_CONFIG_CODE_30002:
                checkWeComConfig(updateDTO);
                break;
            case SYSTEM_CONFIG_CODE_802:
                checkQrConfig(updateDTO);
                break;
            case SYSTEM_JWT_EXPIRE:
                checkJwtExpireConfig(updateDTO);
                break;
            case POSTER_SHARE_CONTROL:
                enablePosterShareConfig(updateDTO);
                break;
            case POSTER_SHARE_COURSE_TEXT, POSTER_SHARE_PROJECT_TEXT, POSTER_SHARE_LIVE_TEXT:
                checkPosterShareText(updateDTO);
                break;
            case POSTER_SHARE_NAME:
                checkPosterShareName(updateDTO);
                break;
            case AI_MAX_KB_CW_QUESTION_MAX_COUNT:
                checkAiMaxKbCwQuestionMaxCount(updateDTO);
                break;
            case SYSTEM_CONFIG_CODE_206:
                if (org.apache.commons.lang3.StringUtils.isBlank(updateDTO.getParaValue())) {
                    throw new BusinessException(UserErrorNoEnum.ERR_DEFAULT_PASSWORD_FAIL);
                }
                break;
            case SYSTEM_CONFIG_CODE_919:
                if (Integer.parseInt(updateDTO.getParaValue()) < 1) {
                    throw new BusinessException(UserErrorNoEnum.ERR_CONFIG_DATA_FAIL);
                }
                break;
            default:
        }
    }

    private void checkPosterShareName(ParaUpdateDTO updateDTO) {
        String paraValue = updateDTO.getParaValue();
        if (StringUtils.hasText(paraValue) && paraValue.length() > 18) {
            throw new BusinessException(UserErrorNoEnum.ERR_POSTER_SHARE_NAME, null, String.valueOf(18));
        }
    }

    private void checkPosterShareText(ParaUpdateDTO updateDTO) {
        String paraValue = updateDTO.getParaValue();
        if (StringUtils.hasText(paraValue) && paraValue.length() > 21) {
            throw new BusinessException(UserErrorNoEnum.ERR_POSTER_SHARE_TEXT, null, String.valueOf(21));
        }
    }

    private void checkAiMaxKbCwQuestionMaxCount(ParaUpdateDTO updateDTO) {
        int maxCount = Integer.parseInt(updateDTO.getParaValue());
        AiBaseConfig aiBaseConfig = aiBaseConfigService.getById(1L);
        if (maxCount > aiBaseConfig.getAiCwQuestionCount()) {
            throw new BusinessException(UserErrorNoEnum.ERR_AI_MAX_KB_CW_QUESTION_MAX_COUNT_FAIL, null,
                aiBaseConfig.getAiCwQuestionCount() + "");
        }
    }

    /**
     * 启用或禁用海报分享相关配置
     *
     * @param updateDTO
     */
    private void enablePosterShareConfig(ParaUpdateDTO updateDTO) {
        int isAvailable = Integer.parseInt(updateDTO.getParaValue());
        List<String> codes = List
            .of(POSTER_SHARE_NAME.getCode(), POSTER_SHARE_COURSE_TEXT.getCode(), POSTER_SHARE_PROJECT_TEXT.getCode(),
                POSTER_SHARE_LIVE_TEXT.getCode());
        LambdaUpdateWrapper<Para> update = new LambdaUpdateWrapper<>();
        // 海报分享禁用时，海报分享的相关配置不展示
        if (Objects.equals(GeneralJudgeEnum.NEGATIVE.getValue(), isAvailable)) {
            update.set(Para::getDisplayFlag, DisplayFlagEnum.NOT_DISPLAY.getType());
        } else {
            update.set(Para::getDisplayFlag, DisplayFlagEnum.DISPLAY_MODIFIABLE.getType());
        }
        update.in(Para::getId, codes);
        baseMapper.update(null, update);
    }

    /**
     * 校验cron表达式格式
     *
     * @param updateDTO {@link ParaUpdateDTO}
     */

    private void checkCronFormat(ParaUpdateDTO updateDTO) {
        // 和刷新任务执行抢同一把锁
        RLock lock = redissonClient.getLock(UserRedisKeyEnum.VIEW_LIMIT_EXECUTE_KEY.getKey());
        try {
            lock.lock();
            // 修改后应该把当前待执行的那个任务改成新的预计时间
            Date nextTime = Date.from(Objects.requireNonNull(
                    CronExpression.parse(updateDTO.getParaValue()).next(LocalDateTime.now().atZone(ZoneId.systemDefault())))
                .toInstant());
            viewLimitExecuteService.updateAwaitExecuteTask(nextTime);
        } catch (IllegalArgumentException e) {
            throw new BusinessException(UserErrorNoEnum.ERR_CORN_FORMAT_FAIL);
        } finally {
            lock.unlock();
        }
    }

    private void checkJobQualificationConfig(ParaUpdateDTO updateDTO, SystemConfigCodeEnum systemConfigCodeEnum) {
        // 是否启用
        int isAvailable = Integer.parseInt(updateDTO.getParaValue());
        // 禁用时校验是否全部禁用，如果是，拦截提示
        if (Objects.equals(GeneralJudgeEnum.NEGATIVE.getValue(), isAvailable)) {
            // 查询除当前操作项外的其他禁用数量，任职资格必须启用一项内容展示，不允许全部关闭
            long disableCount = count(new LambdaQueryWrapper<Para>().in(Para::getParaCode,
                    Arrays.asList(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_21001.getCode(),
                        SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_21002.getCode(),
                        SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_21003.getCode(),
                        SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_21004.getCode()))
                .ne(Para::getParaCode, systemConfigCodeEnum.getCode()).eq(Para::getParaValue, "0"));
            if (disableCount == 3) {
                throw new BusinessException(UserErrorNoEnum.ERR_PARA_VALUE_FAIL_JOB_QUALIFICATION);
            }
        }
    }

    private void checkSynchronizationPoolConfig(ParaUpdateDTO updateDTO) throws IOException {
        // 数值校验
        int value = Integer.parseInt(updateDTO.getParaValue());
        if (value < 10) {
            throw new BusinessException(UserErrorNoEnum.ERR_PARA_VALUE_FAIL);
        }
        // 修改xxl-job 执行周期
        UpdateXxlJob updateXxlJob = new UpdateXxlJob().setJobGroup(8).setExecutorHandler("synchronizationPool");
        XxlJobInfo jobInfo = xxlJobTemplate.getJob(updateXxlJob);
        updateXxlJob.setId(jobInfo.getId()).setJobDesc(jobInfo.getJobDesc()).setAuthor("system")
            .setScheduleType(jobInfo.getScheduleType()).setJobCron(String.valueOf(value * 60));
        xxlJobTemplate.updateJob(updateXxlJob);
    }

    @Override
    public Boolean getResourcePublishConfig() {
        ParaUpdateDTO paraDTO = getByCode(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_916.getCode());
        Integer paraValue = Integer.valueOf(paraDTO.getParaValue());
        return GeneralJudgeEnum.CONFIRM.getValue().equals(paraValue);
    }

    @Override
    public Boolean getThirdPartyCourseConfig() {
        LambdaQueryWrapper<Para> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Para::getId);
        queryWrapper.in(Para::getId, List.of("31002", "31004", "31006"));
        queryWrapper.ne(Para::getParaValue, org.apache.commons.lang3.StringUtils.EMPTY);
        return !CollectionUtils.isEmpty(baseMapper.selectList(queryWrapper));
    }

    @Override
    public String getDefaultPassword() {
        return Optional.ofNullable(this.getById(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_206.getCode()))
            .orElseGet(() -> new Para().setParaValue(defaultPassword)).getParaValue();
    }

    @Override
    public String getParaSysTagValue() {
        LambdaQueryWrapper<Para> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Para::getParaCode, SystemConfigCodeEnum.SYS_TAG_CONFIGURATION.getCode());
        return baseMapper.selectOne(queryWrapper).getParaValue();
    }

    /**
     * 业务特殊处理
     *
     * @param paraUpdateDTO {@link ParaUpdateDTO}
     */
    private void businessSpecialHandle(ParaUpdateDTO paraUpdateDTO) {
        SystemConfigCodeEnum systemConfigCodeEnum = Optional.ofNullable(
                SystemConfigCodeEnum.get(paraUpdateDTO.getParaCode()))
            .orElseThrow(() -> new BusinessException(UserErrorNoEnum.ERR_PARA_ENUM_NOT_EXIST));
        String paraValue = paraUpdateDTO.getParaValue();
        if (Objects.requireNonNull(systemConfigCodeEnum).equals(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_102)) {
            // 写入缓存
            String multipleSwitchStr = org.apache.commons.lang3.StringUtils.isNotBlank(paraValue) ? paraValue : "0";
            redisTemplate.opsForValue().set(UserRedisKeyEnum.ALLOW_USER_MULTIPLE_LOGIN.getKey(), multipleSwitchStr);
        }
    }
}