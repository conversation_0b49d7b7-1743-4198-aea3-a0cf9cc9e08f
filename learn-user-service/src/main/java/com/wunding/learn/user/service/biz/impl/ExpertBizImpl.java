package com.wunding.learn.user.service.biz.impl;

import com.alibaba.excel.util.ListUtils;
import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.constant.user.UserErrorNoEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.common.util.string.TranslateUtil;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.dto.AbstractExportNoEntityDataDTO;
import com.wunding.learn.file.api.dto.IExportNoEntityDataDTO;
import com.wunding.learn.user.api.dto.ExpertDTO;
import com.wunding.learn.user.api.dto.IdentityPostSystemRelateDTO;
import com.wunding.learn.user.api.dto.OrgShowDTO;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.service.OrgFeign;
import com.wunding.learn.user.api.service.UserFeign;
import com.wunding.learn.user.service.admin.query.ExpertQuery;
import com.wunding.learn.user.service.biz.ExpertBiz;
import com.wunding.learn.user.service.dto.ExpertSaveDTO;
import com.wunding.learn.user.service.dto.IdentityPostSystemExpertRelateDTO;
import com.wunding.learn.user.service.model.Expert;
import com.wunding.learn.user.service.model.IdentityPostSystemData;
import com.wunding.learn.user.service.model.IdentityPostSystemExpertRelate;
import com.wunding.learn.user.service.model.IdentityPostSystemStructure;
import com.wunding.learn.user.service.service.IExpertService;
import com.wunding.learn.user.service.service.IIdentityPostSystemDataService;
import com.wunding.learn.user.service.service.IIdentityPostSystemExpertRelateService;
import com.wunding.learn.user.service.service.IIdentityPostSystemStructureService;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * 评委专家库 业务服务类
 *
 * <AUTHOR>
 */
@Slf4j
@Service("expertBiz")
public class ExpertBizImpl implements ExpertBiz {

    @Resource
    private IExpertService expertService;
    @Resource
    private IIdentityPostSystemExpertRelateService identityPostSystemExpertRelateService;
    @Resource
    private IIdentityPostSystemStructureService identityPostSystemStructureService;
    @Resource
    private IIdentityPostSystemDataService identityPostSystemDataService;
    @Resource
    private UserFeign userFeign;
    @Resource
    private OrgFeign orgFeign;

    @Override
    public PageInfo<ExpertDTO> queryPage(ExpertQuery query) {
        PageInfo<ExpertDTO> page = expertService.queryPage(query);
        if (!page.getList().isEmpty()) {
            List<IdentityPostSystemStructure> structureList;
            String belongStructureId = query.getBelongStructureId();
            if (StringUtils.isEmpty(belongStructureId)) {
                // 全部岗位体系结构
                structureList = identityPostSystemStructureService.queryAll();
            } else {
                // 具体岗位体系结构
                structureList = identityPostSystemStructureService.queryCurrentStructureBelong(belongStructureId);
            }
            // 岗位体系数据
            List<IdentityPostSystemData> dataList = identityPostSystemDataService.queryAll();
            // 岗位体系关联列表
            page.getList().forEach(dto -> dto.setDataList(
                identityPostSystemExpertRelateService.queryRelateDTOListByExpertId(dto.getId(), structureList,
                    dataList)));
            // 用户id
            Set<String> userIds = page.getList().stream().map(ExpertDTO::getUserId).collect(Collectors.toSet());
            Map<String, UserDTO> userMap = userFeign.getSimpleUserMap(userIds);
            // 组织id
            Set<String> orgIds = page.getList().stream().map(ExpertDTO::getOrgId).collect(Collectors.toSet());
            Map<String, OrgShowDTO> orgShowMap = orgFeign.getOrgShowDTO(orgIds);

            page.getList().forEach(expertDTO -> {
                Optional.ofNullable(userMap.get(expertDTO.getUserId())).ifPresent(userDTO -> {
                    expertDTO.setLoginName(userDTO.getLoginName());
                    expertDTO.setFullName(userDTO.getFullName());
                });
                Optional.ofNullable(orgShowMap.get(expertDTO.getOrgId())).ifPresent(orgShowDTO -> {
                    expertDTO.setOrgName(orgShowDTO.getOrgShortName());
                    expertDTO.setOrgPath(orgShowDTO.getLevelPathName());
                });
            });
        }
        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createOrUpdate(ExpertSaveDTO dto) {
        if (CollectionUtils.isEmpty(dto.getDataList())) {
            throw new BusinessException(UserErrorNoEnum.ERR_IDENTITY_POST_SYSTEM_NOT_NULL);
        }
        Expert expert = new Expert();
        BeanUtils.copyProperties(dto, expert);
        if (StringUtil.isEmpty(expert.getId())) {
            if (expertService.count(new LambdaQueryWrapper<Expert>().eq(Expert::getUserId, dto.getUserId())) > 0) {
                throw new BusinessException(UserErrorNoEnum.ERR_USER_REPEAT);
            }

            expert.setId(StringUtil.newId());
            expert.setCreateBy(UserThreadContext.getUserId());
            expert.setCreateTime(new Date());
            expert.setUpdateBy(UserThreadContext.getUserId());
            expert.setUpdateTime(new Date());
            expert.setIsDel(0);
        }
        expert.setUpdateBy(UserThreadContext.getUserId());
        expert.setUpdateTime(new Date());
        expertService.saveOrUpdate(expert);
        List<IdentityPostSystemExpertRelate> list = new ArrayList<>(dto.getDataList().size());

        for (IdentityPostSystemExpertRelateDTO relateDTO : dto.getDataList()) {
            if (StringUtils.isEmpty(relateDTO.getDataId()) ) {
                if (StringUtils.isEmpty(relateDTO.getStructureId())) {
                    throw new BusinessException(UserErrorNoEnum.ERR_REQUIRE_DATA_NOT_NULL);
                } else {
                    // 允许date为空，只选择归属岗位体系
                    relateDTO.setDataId("");
                }
            }

            IdentityPostSystemExpertRelate relate = new IdentityPostSystemExpertRelate();
            relate.setId(StringUtil.newId());
            relate.setExpertId(expert.getId());
            relate.setStructureId(relateDTO.getStructureId());
            relate.setDataId(relateDTO.getDataId());
            list.add(relate);
        }
        identityPostSystemExpertRelateService.remove(
            new LambdaQueryWrapper<IdentityPostSystemExpertRelate>().eq(IdentityPostSystemExpertRelate::getExpertId,
                expert.getId()));
        identityPostSystemExpertRelateService.saveBatch(list);
    }

    @Override
    public void delete(String ids) {

        expertService.removeBatchByIds(TranslateUtil.translateBySplit(ids, String.class));

    }

    @Resource
    private ExportComponent exportComponent;

    @Override
    public void export(ExpertQuery query) {
        List<String> titles = getTitles(query);

        IExportNoEntityDataDTO exportDataDTO = new AbstractExportNoEntityDataDTO<ExpertBiz, ExpertDTO>(query) {
            @Override
            protected ExpertBiz getBean() {
                return SpringUtil.getBean("expertBiz", ExpertBiz.class);
            }

            @Override
            protected List<List<Object>> getPageInfo() {
                List<List<Object>> data = ListUtils.newArrayList();
                PageInfo<ExpertDTO> pageInfo = getBean().queryPage(query);
                pageInfo.getList().forEach(dto -> {
                    List<Object> rowData = ListUtils.newArrayList();
                    rowData.add(dto.getFullName());
                    rowData.add(dto.getLoginName());
                    rowData.add(dto.getOrgName());
                    if (!CollectionUtils.isEmpty(dto.getDataList())) {
                        for (IdentityPostSystemRelateDTO d : dto.getDataList()) {
                            rowData.add(d.getDataName());
                        }
                    }
                    data.add(rowData);
                });
                return data;
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.Expert;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.Expert.getType();
            }
        };
        List<List<String>> head = getExportHeader(titles);
        exportComponent.exportNoEntityRecord(exportDataDTO, head);
    }

    @NotNull
    private List<String> getTitles(ExpertQuery query) {
        List<String> titles = new ArrayList<>();
        PageInfo<ExpertDTO> pageInfo = queryPage(query);
        pageInfo.getList().forEach(dto -> {
            if (!CollectionUtils.isEmpty(dto.getDataList())) {
                for (IdentityPostSystemRelateDTO d : dto.getDataList()) {
                    titles.add(d.getStructureName());
                }
            }
        });
        return titles;
    }

    private List<List<String>> getExportHeader(List<String> titles) {
        List<List<String>> head = new ArrayList<>();
        head.add(List.of("姓名"));
        head.add(List.of("账号"));
        head.add(List.of("部门"));
        for (String title : titles) {
            head.add(List.of(title));
        }
        return head;
    }

}
