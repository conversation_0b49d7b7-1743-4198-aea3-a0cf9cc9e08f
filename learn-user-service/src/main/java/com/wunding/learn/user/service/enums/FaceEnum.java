package com.wunding.learn.user.service.enums;

/**
 * 人脸识别url
 *
 * <AUTHOR> href="mailto:<EMAIL>">WangShuai</a>
 * @date 2020/12/23 10:43
 */
public enum FaceEnum {

    /**
     * 鉴权认证机制接口（获取access_token)
     */
    ACCESS_TOKEN_URL("https://aip.baidubce.com/oauth/2.0/token?"),
    /**
     * 人脸注册接口
     */
    FACE_ADD("https://aip.baidubce.com/rest/2.0/face/v3/faceset/user/add"),
    /**
     * 人脸识别接口
     */
    FACE_SEARCH("https://aip.baidubce.com/rest/2.0/face/v3/search"),
    /**
     * 人脸用户删除接口
     */
    FACE_USER_DELETE("https://aip.baidubce.com/rest/2.0/face/v3/faceset/user/delete"),
    /**
     * access_token
     */
    ACCESS_TOKEN("access_token"),
    /**
     * 人脸是否通过的阈值
     */
    FACE_THRESHOLD("80"),
    ;

    FaceEnum(String value) {
        this.value = value;
    }

    private String value;

    public String getValue() {
        return value;
    }
}
