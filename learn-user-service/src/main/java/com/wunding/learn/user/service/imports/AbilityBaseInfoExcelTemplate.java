package com.wunding.learn.user.service.imports;

import com.google.common.collect.Lists;
import com.wunding.learn.common.category.model.Categorys;
import com.wunding.learn.common.category.service.ICategorysService;
import com.wunding.learn.common.i18n.util.I18nUtil;
import com.wunding.learn.common.i18n.util.ImportTemplateI18nEnum;
import com.wunding.learn.common.util.excel.AbstractExcelTemplate;
import com.wunding.learn.common.util.excel.ExcelCheckMessage;
import com.wunding.learn.user.service.admin.dto.ability.AbilityBaseInfoImportDTO;
import com.wunding.learn.user.service.model.AbilityBaseInfo;
import com.wunding.learn.user.service.service.IAbilityBaseInfoService;
import java.util.ArrayList;
import java.util.List;
import org.springframework.util.StringUtils;

/**
 * @author: chenjinneng
 * @create: 2024-01-02
 **/
public class AbilityBaseInfoExcelTemplate extends AbstractExcelTemplate {

    private static final String[] IMPORT_TITLES = {"能力编码", "能力名称", "能力分类", "能力描述", "关键描述"};

    private final IAbilityBaseInfoService abilityBaseInfoService;

    private final ICategorysService categorysService;

    public AbilityBaseInfoExcelTemplate(IAbilityBaseInfoService abilityBaseInfoService,
        ICategorysService categorysService) {
        super(IMPORT_TITLES);
        this.abilityBaseInfoService = abilityBaseInfoService;
        this.categorysService = categorysService;
    }

    @Override
    protected ExcelCheckMessage doCheck(ExcelCheckMessage excelCheckMessage) {
        String[][] excel = excelCheckMessage.getExcel();
        List<String> messages = Lists.newArrayList();
        List<AbilityBaseInfoImportDTO> list = new ArrayList<>();
        for (int index = 1; index < excel.length; index++) {
            // 行数
            int lineNum = index + 1;
            // 每一行的数据
            String[] rowData = excel[index];

            // 能力编码校验 不为空、长度限制80
            String code = rowData[0];
            // 不能为空 长度限制80
            verifyAbilityCode(code, messages, lineNum);
            AbilityBaseInfo abilityBaseInfo = abilityBaseInfoService.lambdaQuery().eq(AbilityBaseInfo::getCode, code)
                .one();
            if (abilityBaseInfo != null) {
                messages.add(
                    I18nUtil.getImportMessage(ImportTemplateI18nEnum.AbilityBaseInfoExcelTemplateAbilityCodeAlreadyExist,
                        lineNum));
                break;
            }

            // 能力名称校验 不为空、长度限制80
            String name = rowData[1];
            verifyAbilityName(name, messages, lineNum);

            // 能力分类
            Categorys categorys = new Categorys();
            String abilityCategoryName = rowData[2];
            if (StringUtils.hasText(abilityCategoryName)) {
                // 校验
                categorys = categorysService.lambdaQuery().eq(Categorys::getCategoryName, abilityCategoryName)
                    .eq(Categorys::getCategoryType, "AbilityType").one();
                if (categorys == null) {
                    messages.add(I18nUtil.getImportMessage(
                        ImportTemplateI18nEnum.AbilityBaseInfoExcelTemplateAbilityCategoryNotExist, lineNum));
                    break;
                }

            }

            // 能力描述
            String abilityDescription = rowData[3];
            verifyAbilityDescription(abilityDescription, messages, lineNum);

            // 关键描述
            String crucialDescription = rowData[4];
            if (StringUtils.hasText(crucialDescription) && crucialDescription.length() > 500) {
                messages.add(longMessage(lineNum, IMPORT_TITLES[3], 500));
            }

            // 组装数据
            AbilityBaseInfoImportDTO dto = new AbilityBaseInfoImportDTO()
                .setCode(code).setName(name).setAbilityCategoryId(
                    org.apache.commons.lang3.StringUtils.isEmpty(categorys.getId()) ? "AbilityType" : categorys.getId())
                .setAbilityCategoryName(abilityCategoryName).setAbilityDescription(abilityDescription)
                .setCrucialDescription(crucialDescription);
            list.add(dto);
        }
        excelCheckMessage.setObjects(list);
        excelCheckMessage.setMessage(messages);
        return excelCheckMessage;
    }

    private void verifyAbilityCode(String code, List<String> messages, int lineNum) {
        // 不能为空 长度限制80
        if (!StringUtils.hasText(code)) {
            messages.add(emptyMessage(lineNum, IMPORT_TITLES[0]));
        } else {
            if (code.length() > 80) {
                messages.add(longMessage(lineNum, IMPORT_TITLES[0], 80));
            }
            String regex = "^[^\\u4e00-\\u9fa5]{1,80}$";
            boolean matches = code.matches(regex);
            if (!matches) {
                messages.add(I18nUtil.getImportMessage(ImportTemplateI18nEnum.AbilityBaseInfoAbilityCodeContainsChinese,
                    lineNum));
            }
        }
    }

    private void verifyAbilityName(String name, List<String> messages, int lineNum) {
        // 不能为空 长度限制80
        if (!StringUtils.hasText(name)) {
            messages.add(emptyMessage(lineNum, IMPORT_TITLES[1]));
        } else {
            if (name.length() > 80) {
                messages.add(longMessage(lineNum, IMPORT_TITLES[1], 80));
            }
        }
    }

    private void verifyAbilityDescription(String abilityDescription, List<String> messages, int lineNum) {
        if (!StringUtils.hasText(abilityDescription)) {
            messages.add(emptyMessage(lineNum, IMPORT_TITLES[3]));
        } else {
            if (abilityDescription.length() > 500) {
                messages.add(longMessage(lineNum, IMPORT_TITLES[3], 500));
            }
        }
    }
}
