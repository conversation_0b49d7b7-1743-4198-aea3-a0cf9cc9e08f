package com.wunding.learn.user.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.common.mybatis.cache.MyBatisPlusRedisCache;
import com.wunding.learn.user.service.admin.query.CommonTemplateOtherListQuery;
import com.wunding.learn.user.service.dto.CommonTemplateOtherListDTO;
import com.wunding.learn.user.service.model.CommonTemplateOther;
import java.util.List;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p> 公共模板字段属性表 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">liuxiuyong</a>
 * @since 2023-03-14
 */
@Mapper
@CacheNamespace(implementation = MyBatisPlusRedisCache.class, eviction = MyBatisPlusRedisCache.class)
public interface CommonTemplateOtherMapper extends BaseMapper<CommonTemplateOther> {


    /**
     * 表单模板字段列表
     *
     * @param query
     * @return
     */
    List<CommonTemplateOtherListDTO> getTemplateOtherList(@Param("query") CommonTemplateOtherListQuery query);
}
