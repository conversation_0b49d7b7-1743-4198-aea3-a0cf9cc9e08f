package com.wunding.learn.common.aop.event;

import com.wunding.learn.common.aop.mapper.ApiLog;
import com.wunding.learn.common.aop.mapper.ApiLogMapper;
import jakarta.annotation.Resource;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class WdApiLogEventListener {

    @Resource
    private ApiLogMapper apiLogMapper;

    @Async("apiLogTaskThreadPool")
    @Order
    @EventListener
    public void onApplicationEvent(WdApiLogEvent event) {
        try {
            log.info("WdApiLogEvent:{}", event.getWdApiLogVO());
            // 处理日志. 因为单体项目没有ES。 考虑存表方案用于记录日志数据。
            ApiLog apiLog = new ApiLog();
            apiLog.setCreateTime(new Date());
            BeanUtils.copyProperties(event.getWdApiLogVO(), apiLog);
            apiLogMapper.insert(apiLog);
        } catch (Exception e) {
            log.error("WdApiLogEvent error:{}", e.getMessage());
        }
    }
}
