package com.wunding.learn.lecturer.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import com.wunding.learn.lecturer.service.model.LecturerTeachCourse;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 讲师授课课件关联表 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">李毅慧</a>
 * @since 2022-05-11
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")
})
public interface LecturerTeachCourseMapper extends BaseMapper<LecturerTeachCourse> {

}
