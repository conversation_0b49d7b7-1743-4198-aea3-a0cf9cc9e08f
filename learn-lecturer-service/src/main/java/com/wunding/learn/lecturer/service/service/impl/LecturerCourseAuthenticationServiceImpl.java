package com.wunding.learn.lecturer.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.wunding.learn.common.constant.course.CourseErrorNoEnum;
import com.wunding.learn.common.constant.file.FileErrorNoEnum;
import com.wunding.learn.common.constant.lecture.LecturerErrorNoEnum;
import com.wunding.learn.common.constant.other.CommonConstants;
import com.wunding.learn.common.constant.other.JudgeEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.dto.ImportExcelFileDTO;
import com.wunding.learn.common.dto.ImportResultDTO;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.mybatis.service.impl.BaseServiceImpl;
import com.wunding.learn.common.mybatis.util.MybatisParameterUtils;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.date.DateUtil;
import com.wunding.learn.common.util.excel.ExcelCheckMessage;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.course.api.dto.CourseCategoryDTO;
import com.wunding.learn.course.api.dto.CourseInfoDTO;
import com.wunding.learn.course.api.dto.LecturerCourseDTO;
import com.wunding.learn.course.api.query.UserCourseListQuery;
import com.wunding.learn.course.api.service.CourseFeign;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.dto.AbstractExportDataDTO;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.file.api.dto.ImportDataDTO;
import com.wunding.learn.file.api.service.ImportDataFeign;
import com.wunding.learn.lecturer.api.dto.LecturerCourseAuthFeignDTO;
import com.wunding.learn.lecturer.service.admin.bo.LecturerCourseAuthenticationBO;
import com.wunding.learn.lecturer.service.admin.dto.CourseTeacherPageDTO;
import com.wunding.learn.lecturer.service.admin.dto.LecturerCourseAuthByCoursePageDTO;
import com.wunding.learn.lecturer.service.admin.dto.LecturerCourseAuthByLecturerPageDTO;
import com.wunding.learn.lecturer.service.admin.dto.LecturerCourseAuthenticationSaveDTO;
import com.wunding.learn.lecturer.service.admin.query.CourseLecturerListQuery;
import com.wunding.learn.lecturer.service.admin.query.LecturerCourseAuthByCourseQuery;
import com.wunding.learn.lecturer.service.admin.query.LecturerCourseAuthByLecturerQuery;
import com.wunding.learn.lecturer.service.admin.query.LecturerCourseQuery;
import com.wunding.learn.lecturer.service.client.dto.LecturerCourseAuthClientDTO;
import com.wunding.learn.lecturer.service.dao.LecturerCourseAuthenticationDao;
import com.wunding.learn.lecturer.service.imports.LecturerCourseAuthExcelTemplate;
import com.wunding.learn.lecturer.service.mapper.LecturerCourseAuthenticationMapper;
import com.wunding.learn.lecturer.service.model.Lecturer;
import com.wunding.learn.lecturer.service.model.LecturerCourseAuthentication;
import com.wunding.learn.lecturer.service.service.ILecturerCourseAuthenticationService;
import com.wunding.learn.lecturer.service.service.ILecturerExaminationService;
import com.wunding.learn.lecturer.service.service.ILecturerService;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.service.UserFeign;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Service;

/**
 * <p> 讲师课程认证表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi</a>
 * @since 2023-03-15
 */
@Slf4j
@Service("lecturerCourseAuthenticationService")
public class LecturerCourseAuthenticationServiceImpl extends
        BaseServiceImpl<LecturerCourseAuthenticationMapper, LecturerCourseAuthentication> implements
        ILecturerCourseAuthenticationService {


    // 按月查询模式
    private static final int QUERY_BY_MONTH_MODE = 1;
    // 按年查询模式
    private static final int QUERY_BY_YEAR_MODE = 2;
    public static final String LECTURER_COURSE_AUTHENTICATION_SERVICE = "lecturerCourseAuthenticationService";
    public static final String PUBLISHED_COURSE_IDS = "publishedCourseIds: ";

    private final ExecutorService importDataThreadPool = new ThreadPoolExecutor(2, 2, 0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(), new CustomizableThreadFactory("import-offline-teach-data-pool-"));

    @Resource
    private ExportComponent exportComponent;

    @Resource
    private CourseFeign courseFeign;
    @Resource
    private ILecturerService lecturerService;
    @Resource
    private ILecturerExaminationService examinationService;
    @Resource
    private ImportDataFeign importDataFeign;
    @Resource
    private UserFeign userFeign;
    @Resource(name = "lecturerCourseAuthenticationDao")
    private LecturerCourseAuthenticationDao lecturerCourseAuthenticationDao;

    @Override
    public ILecturerCourseAuthenticationService getBean() {
        return SpringUtil.getBean(LECTURER_COURSE_AUTHENTICATION_SERVICE, ILecturerCourseAuthenticationService.class);
    }

    @Override
    public void saveData(LecturerCourseAuthenticationSaveDTO saveDTO) {
        CourseInfoDTO courseInfoDTO = courseFeign.getById(saveDTO.getCourseId());
        if(!Objects.equals(courseInfoDTO.getIsPublish(), JudgeEnum.CONFIRM.getValue())) {
            throw new BusinessException(CourseErrorNoEnum.ERR_COURSE_NOT_PUBLISH);
        }
        // 校验是否已经存在
        LambdaQueryWrapper<LecturerCourseAuthentication> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LecturerCourseAuthentication::getCourseId, saveDTO.getCourseId())
            .eq(LecturerCourseAuthentication::getLecturerId, saveDTO.getLecturerId());
        List<LecturerCourseAuthentication> list = getBaseMapper()
            .selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(list)) {
            throw new BusinessException(LecturerErrorNoEnum.ERR_LECTURER_AUTH_COURSE_EXISTS);
        }

        // 添加
        LecturerCourseAuthentication authentication = new LecturerCourseAuthentication();
        BeanUtils.copyProperties(saveDTO, authentication);
        authentication.setId(StringUtil.newId());
        Optional.ofNullable(courseInfoDTO).ifPresent(
                course -> authentication.setCourseName(course.getCourseName()).setCourseNo(course.getCourseNo())
                        .setCourseCategory(course.getCourseCateId()));
        lecturerCourseAuthenticationDao.saveLecturerCourseAuthentication(authentication);
        // 更新所有的讲师认证课程
        getBean().updateCourseInfo(courseInfoDTO);
    }


    @Override
    public void deleteData(String ids) {
        List<String> idList = Arrays.asList(ids.split(CommonConstants.A_COMMA_IN_ENGLISH));
        List<LecturerCourseAuthentication> lecturerCourseAuthentications = listByIds(idList);
        lecturerCourseAuthentications.forEach(lecturerCourseAuthentication -> lecturerCourseAuthenticationDao
                .delLecturerCourseAuthentication(lecturerCourseAuthentication));
    }

    @Override
    public PageInfo<LecturerCourseAuthByLecturerPageDTO> listByLecturer(LecturerCourseAuthByLecturerQuery query) {
        PageInfo<LecturerCourseAuthByLecturerPageDTO> objectPageInfo = new PageInfo<>();
        // 查询课程认证讲师的课程id
        List<String> courseIds = baseMapper.listCourseIds(query);
        log.info("courseIds: " + courseIds);
        List<String> publishedCourseIds = courseFeign.listPublishedCourseIds(courseIds);
        log.info(PUBLISHED_COURSE_IDS + publishedCourseIds);
        if (CollectionUtils.isEmpty(publishedCourseIds)) {
            return objectPageInfo;
        }
        query.setPublishedCourseIds(publishedCourseIds);
        objectPageInfo = PageMethod.startPage(query.getPageNo(),
                query.getPageSize()).doSelectPageInfo(() -> baseMapper.listByLecturer(query));
        log.info("objectPageInfo.getList(): " + objectPageInfo.getList());
        // 增加评估数据
        if (CollectionUtils.isEmpty(objectPageInfo.getList())) {
            return objectPageInfo;
        }
        objectPageInfo.getList().stream().forEach(data -> data.setEvalScore(
                examinationService.getAvgScoreByLecturerAndCourse(data.getLecturerId(), data.getCourseId())));
        return objectPageInfo;
    }

    @Override
    public void exportByLecturer(LecturerCourseAuthByLecturerQuery query) {
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<ILecturerCourseAuthenticationService, LecturerCourseAuthByLecturerPageDTO>(
                query) {
            @Override
            protected ILecturerCourseAuthenticationService getBean() {
                return SpringUtil.getBean(LECTURER_COURSE_AUTHENTICATION_SERVICE,
                        ILecturerCourseAuthenticationService.class);
            }

            @Override
            protected PageInfo<LecturerCourseAuthByLecturerPageDTO> getPageInfo() {
                return getBean().listByLecturer((LecturerCourseAuthByLecturerQuery) queryDTO);

            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.LecturerCourseAuthByLecturer;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.LecturerCourseAuthByLecturer.getType();
            }

        };
        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public PageInfo<LecturerCourseAuthByCoursePageDTO> listByCourse(LecturerCourseAuthByCourseQuery query) {
        PageInfo<LecturerCourseAuthByCoursePageDTO> pageInfo = new PageInfo<>();
        // 查询课程认证讲师的课程id
        List<String> authenticationCourseIds = baseMapper.listAuthenticationCourseIds(query);
        log.info("authenticationCourseIds: " + authenticationCourseIds);
        List<String> publishedCourseIds = courseFeign.listPublishedCourseIds(authenticationCourseIds);
        log.info(PUBLISHED_COURSE_IDS + publishedCourseIds);
        if (CollectionUtils.isEmpty(publishedCourseIds)) {
            return pageInfo;
        }
        query.setPublishedCourseIds(publishedCourseIds);

        pageInfo = PageMethod.startPage(query.getPageNo(),
                query.getPageSize()).doSelectPageInfo(() -> baseMapper.listByCourse(query));
        List<LecturerCourseAuthByCoursePageDTO> list = pageInfo.getList();
        if (CollectionUtils.isEmpty(list)) {
            return new PageInfo<>();
        }

        Set<String> courseIds = list.stream().map(LecturerCourseAuthByCoursePageDTO::getCourseId)
                .filter(StringUtils::isNotBlank).collect(Collectors.toSet());

        List<CourseInfoDTO> listCourse = courseFeign.getRealityCourseListBatchIds(courseIds);

        if (CollectionUtils.isNotEmpty(listCourse)) {
            Set<String> categoryIds = listCourse.stream().map(CourseInfoDTO::getCourseCateId)
                    .filter(StringUtils::isNotBlank).collect(Collectors.toSet());

            Map<String, CourseInfoDTO> map = listCourse.stream().map(course -> {
                CourseInfoDTO dto = new CourseInfoDTO();
                BeanUtils.copyProperties(course, dto);
                return dto;
            }).collect(Collectors.toMap(CourseInfoDTO::getId, Function.identity(), (v1, v2) -> v1));

            // 获取分类信息
            Map<String, CourseCategoryDTO> courseCategoryMap = courseFeign.getCourseCategoryMap(categoryIds);
            pageInfo.getList().forEach(data -> Optional.ofNullable(map.get(data.getCourseId())).ifPresent(dto -> {
                CourseCategoryDTO categoryDTO = courseCategoryMap.get(dto.getCourseCateId());
                data.setCourseCategory(
                        Optional.ofNullable(categoryDTO).isPresent() ? categoryDTO.getCategoryFullName() : "分类已被删除");
            }));
        }
        return pageInfo;
    }

    @Override
    public void exportByCourse(LecturerCourseAuthByCourseQuery query) {
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<ILecturerCourseAuthenticationService, LecturerCourseAuthByCoursePageDTO>(
                query) {
            @Override
            protected ILecturerCourseAuthenticationService getBean() {
                return SpringUtil.getBean(LECTURER_COURSE_AUTHENTICATION_SERVICE,
                        ILecturerCourseAuthenticationService.class);
            }

            @Override
            protected PageInfo<LecturerCourseAuthByCoursePageDTO> getPageInfo() {
                return getBean().listByCourse((LecturerCourseAuthByCourseQuery) queryDTO);

            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.LecturerCourseAuthByCourse;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.LecturerCourseAuthByCourse.getType();
            }

        };
        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    @Async
    public void updateCourseInfo(CourseInfoDTO courseInfoDTO) {
        if (Objects.isNull(courseInfoDTO)) {
            return;
        }
        update(new LambdaUpdateWrapper<LecturerCourseAuthentication>().eq(LecturerCourseAuthentication::getCourseId,
                        courseInfoDTO.getId()).ne(LecturerCourseAuthentication::getCourseName, courseInfoDTO.getCourseName())
                .ne(LecturerCourseAuthentication::getCourseNo, courseInfoDTO.getCourseNo())
                .ne(LecturerCourseAuthentication::getCourseCategory, courseInfoDTO.getCourseCateId())
                .set(LecturerCourseAuthentication::getCourseName, courseInfoDTO.getCourseName())
                .set(LecturerCourseAuthentication::getCourseNo, courseInfoDTO.getCourseNo())
                .set(LecturerCourseAuthentication::getCourseCategory, courseInfoDTO.getCourseCateId()));
    }

    @Override
    public PageInfo<LecturerCourseDTO> lecturerCourseList(LecturerCourseQuery query) {

        buildQueryTimeZone(query);

        Lecturer lecturer = Optional.ofNullable(lecturerService.getById(query.getLecturerId()))
                .orElseThrow(() -> new BusinessException(LecturerErrorNoEnum.ERR_LECTURER_NOT_EXIST));
        if (StringUtils.isBlank(lecturer.getUserId())) {
            return new PageInfo<>();
        }
        UserCourseListQuery userCourseListQuery = new UserCourseListQuery();
        userCourseListQuery.setPageNo(query.getPageNo());
        userCourseListQuery.setPageSize(query.getPageSize());
        userCourseListQuery.setUserId(lecturer.getUserId());
        userCourseListQuery.setStartTime(query.getStartTime());
        userCourseListQuery.setEndTime(query.getEndTime());
        return courseFeign.userCourseList(userCourseListQuery);
    }

    @Override
    public void exportLecturerCourseList(LecturerCourseQuery query) {
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<ILecturerCourseAuthenticationService, LecturerCourseDTO>(
                query) {
            @Override
            protected ILecturerCourseAuthenticationService getBean() {
                return SpringUtil.getBean(LECTURER_COURSE_AUTHENTICATION_SERVICE,
                        ILecturerCourseAuthenticationService.class);
            }

            @Override
            protected PageInfo<LecturerCourseDTO> getPageInfo() {
                return getBean().lecturerCourseList((LecturerCourseQuery) query);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.LecturerCourseList;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.LecturerCourseList.getType();
            }

        };
        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public PageInfo<CourseTeacherPageDTO> courseLecturerList(CourseLecturerListQuery query) {
        // 取该课程的所有讲师信息
        PageInfo<CourseTeacherPageDTO> pageInfo = PageMethod.startPage(query.getPageNo(), query.getPageSize())
                .doSelectPageInfo(() -> examinationService.getAuthCourseLecturerList(query.getCourseId()));
        List<CourseTeacherPageDTO> list = pageInfo.getList();
        if (CollectionUtils.isEmpty(list)) {
            pageInfo.setList(new ArrayList<>());
            return pageInfo;
        }
        Set<String> lecturerIds = list.stream().map(CourseTeacherPageDTO::getUserId).filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        Map<String, Long> userDevelopCourseNumMap = courseFeign.getUserDevelopCourseNumMap(lecturerIds);
        pageInfo.getList().forEach(data ->
                data.setCourseNum(Optional.ofNullable(userDevelopCourseNumMap.get(data.getUserId())).orElse(0L).intValue())
        );
        return pageInfo;
    }

    @Override
    public void exportCourseLecturerList(CourseLecturerListQuery query) {
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<ILecturerCourseAuthenticationService, CourseTeacherPageDTO>(
                query) {
            @Override
            protected ILecturerCourseAuthenticationService getBean() {
                return SpringUtil.getBean(LECTURER_COURSE_AUTHENTICATION_SERVICE,
                        ILecturerCourseAuthenticationService.class);
            }

            @Override
            protected PageInfo<CourseTeacherPageDTO> getPageInfo() {
                return getBean().courseLecturerList((CourseLecturerListQuery) query);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.LecturerCourseAuthTeach;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.LecturerCourseAuthTeach.getType();
            }

        };
        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public List<LecturerCourseAuthFeignDTO> getLecturerCourseAuthByCourseId(String courseId) {
        String userId  =UserThreadContext.getUserId();
        List<LecturerCourseAuthFeignDTO> list = baseMapper.getLecturerCourseAuthByCourseId(courseId,userId);
        return list;
    }

    @Override
    public List<LecturerCourseAuthClientDTO> getLecturerCourseAuthByLecturerId(String lecturerId){
        List<LecturerCourseAuthClientDTO> list = new ArrayList<>();
        if(StringUtils.isEmpty(lecturerId)){
            return list;
        }
        // 查询课程认证讲师的课程id
        List<String> courseIds = baseMapper.listCourseIdsByLecturerId(lecturerId);
        log.info("courseIds: " + courseIds);
        List<String> publishedCourseIds = courseFeign.listPublishedCourseIds(courseIds);
        log.info(PUBLISHED_COURSE_IDS + publishedCourseIds);

        // 添加判断 防止空指针 SQL会报错的
        if (CollectionUtils.isEmpty(publishedCourseIds)) {
            return list;
        }
        list = baseMapper.getLecturerCourseAuthByPublishedCourseIds(publishedCourseIds);
        return list;
    }

    @Override
    public void exportByLecturerCourse(LecturerCourseAuthByLecturerQuery query) {
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<ILecturerCourseAuthenticationService, LecturerCourseAuthByLecturerPageDTO>(
            query) {
            @Override
            protected ILecturerCourseAuthenticationService getBean() {
                return SpringUtil.getBean(LECTURER_COURSE_AUTHENTICATION_SERVICE,
                    ILecturerCourseAuthenticationService.class);
            }

            @Override
            protected PageInfo<LecturerCourseAuthByLecturerPageDTO> getPageInfo() {
                return getBean().listByLecturer((LecturerCourseAuthByLecturerQuery) queryDTO);

            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.LecturerCourseAuthByLecturerCourse;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.LecturerCourseAuthByLecturerCourse.getType();
            }

        };
        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public Future<ImportResultDTO> importData(ImportExcelFileDTO dto) {
        UserThreadContext.setUserId("admin");
        UserThreadContext.setOs("PC");
        return importDataThreadPool.submit(() -> {
            ImportResultDTO importResultDTO = new ImportResultDTO();
            importResultDTO.setIsSuccess(false);
            long beginTime = System.currentTimeMillis();
            ImportDataDTO importData = importDataFeign.getImportData(dto.getExcelFilePath());

            log.info("获取导入的数据耗时：{}，共计{}条数据", System.currentTimeMillis() - beginTime,
                    importData.getRowCount());
            String[][] excel = Optional.ofNullable(importData.getExcel())
                    .orElseThrow(() -> new BusinessException(FileErrorNoEnum.ERR_POST_CONTENT_NULL));
            log.info("excel:{}", excel);

            beginTime = System.currentTimeMillis();
            ExcelCheckMessage excelCheckMessage = new LecturerCourseAuthExcelTemplate().check(excel);
            log.info("lecturerCourseAuthExcelTemplate check 耗时{}", System.currentTimeMillis() - beginTime);

            if (CollectionUtils.isEmpty(excelCheckMessage.getMessage())) {
                beginTime = System.currentTimeMillis();
                List<LecturerCourseAuthenticationBO> lecturerBOList = (List<LecturerCourseAuthenticationBO>) excelCheckMessage
                        .getObjects();
                log.info("lecturerBOList:{}", JsonUtil.objToJson(lecturerBOList));
                // 数据校验
                Boolean flag = checkImportData(lecturerBOList, importResultDTO);
                if (Boolean.TRUE.equals(flag)) {
                    //导入数据
                    int count = saveImportDataList(lecturerBOList);
                    log.info("saveLecturerCourseAuthDataList 耗时{}", System.currentTimeMillis() - beginTime);
                    importResultDTO.setMsg("成功导入" + count + "条课程认证讲师数据");
                    importResultDTO.setIsSuccess(true);
                }
                return importResultDTO;
            }
            importResultDTO.setIsSuccess(false);
            importResultDTO.setMsg(JsonUtil.objToJson(excelCheckMessage.getMessage()));
            return importResultDTO;
        });
    }

    private boolean checkImportData(List<LecturerCourseAuthenticationBO> list, ImportResultDTO importResultDTO) {
        // 错误Map Map<行号, List<错误信息>>
        Map<Integer, List<String>> errorMap = new HashMap<>();
        // 查找所有讲师已经认证课程的信息Map<讲师id, List<课程id>>
        Map<String, List<String>> lecturerCoursesMap = new HashMap<>();
        // 根据账号获取讲师
        List<String> lecturerLoginNames = list.stream().map(LecturerCourseAuthenticationBO::getLecturerLoginName)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        // 校验讲师编号
        List<String> lecturerNumbers = list.stream().map(LecturerCourseAuthenticationBO::getLecturerNumber)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        // 讲师ids
        List<String> lecturerIds = new ArrayList<>();
        Map<String, Lecturer> lecturerNumberMap = new HashMap<>();
        Map<String, Lecturer> lecturerLoginNameMap = new HashMap<>();
        LambdaQueryWrapper<Lecturer> lecturerQueryWrapper = new LambdaQueryWrapper<>();
        if (CollectionUtils.isNotEmpty(lecturerNumbers)) {
            MybatisParameterUtils.cutInParameter(lecturerQueryWrapper, Lecturer::getLecturerNumber, lecturerNumbers);
            List<Lecturer> lecturerList = lecturerService.list(lecturerQueryWrapper);
            lecturerQueryWrapper.clear();
            lecturerNumberMap = lecturerList.stream()
                    .collect(Collectors.toMap(Lecturer::getLecturerNumber, Function.identity(), (v1, v2) -> v1));
            lecturerIds.addAll(lecturerList.stream().map(Lecturer::getId).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(lecturerLoginNames)) {
            // 取用户Map
            Map<String, UserDTO> userMapByLoginNames = userFeign.getUserMapByLoginNames(lecturerLoginNames);
            List<String> userIds = userMapByLoginNames.entrySet().stream().map(data -> {
                UserDTO dto = data.getValue();
                if (Objects.nonNull(dto)) {
                    return dto.getId();
                }
                return StringUtils.EMPTY;
            }).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(userIds)) {
                MybatisParameterUtils.cutInParameter(lecturerQueryWrapper, Lecturer::getUserId, userIds);
                List<Lecturer> lecturerList = lecturerService.list(lecturerQueryWrapper);
                Map<String, Lecturer> loginNameLecturerMap = lecturerList.stream()
                        .collect(Collectors.toMap(Lecturer::getUserId, Function.identity()));
                lecturerQueryWrapper.clear();
                lecturerLoginNameMap = lecturerLoginNames.stream()
                        .collect(Collectors.toMap(Function.identity(), data -> {
                            UserDTO userDTO = userMapByLoginNames.get(data);
                            if (Objects.nonNull(userDTO) && Objects.nonNull(loginNameLecturerMap.get(userDTO.getId()))) {
                                return loginNameLecturerMap.get(userDTO.getId());
                            }
                            return null;
                        }));
                lecturerIds.addAll(lecturerList.stream().map(Lecturer::getId).collect(Collectors.toList()));
            }

        }

        if (CollectionUtils.isNotEmpty(lecturerIds)) {
            lecturerCoursesMap = list(
                    new LambdaQueryWrapper<LecturerCourseAuthentication>().in(LecturerCourseAuthentication::getLecturerId,
                            lecturerIds)).stream().collect(Collectors.groupingBy(LecturerCourseAuthentication::getLecturerId,
                    Collectors.mapping(LecturerCourseAuthentication::getCourseId, Collectors.toList())));
        }

        // 校验课程编号
        Set<String> courseNos = list.stream().map(LecturerCourseAuthenticationBO::getCourseNo)
                .filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        Map<String, CourseInfoDTO> courseInfoDTOMap = courseFeign.getCourseBatchNos(courseNos);
        // 校验数据并装配数据
        Map<String, Lecturer> finalLecturerNumberIdMap = lecturerNumberMap;
        Map<String, List<String>> finalLecturerCoursesMap = lecturerCoursesMap;
        Map<String, Lecturer> finalLecturerLoginNameMap = lecturerLoginNameMap;
        list.forEach(data -> {
            List<String> errMsgList = Optional.ofNullable(errorMap.get(data.getRowNum())).orElse(new ArrayList<>());
            // 检查讲师编号
            checkLecturerNumber(data, finalLecturerNumberIdMap, errMsgList);

            // 验证讲师账号&装配讲师id
            checkLecturerLoginName(data, finalLecturerLoginNameMap, errMsgList);

            // 验证课程编号&装配课程信息
            checkLecturerCourseNo(data, courseInfoDTOMap, finalLecturerCoursesMap, errMsgList, errorMap);
        });
        if (CollectionUtils.isEmpty(errorMap)) {
            return true;
        }
        importResultDTO.setIsSuccess(false);
        List<String> messageList = errorMap.entrySet().stream()
                .map(entry -> "第" + entry.getKey() + "行" + StringUtils.joinWith("、", entry.getValue()))
                .collect(Collectors.toList());
        importResultDTO.setMsg(JsonUtil.objToJson(messageList));
        return false;
    }

    private void checkLecturerCourseNo(LecturerCourseAuthenticationBO data, Map<String, CourseInfoDTO> courseInfoDTOMap, Map<String, List<String>> finalLecturerCoursesMap, List<String> errMsgList, Map<Integer, List<String>> errorMap) {
        CourseInfoDTO courseInfoDTO = courseInfoDTOMap.get(data.getCourseNo());
        if (Optional.ofNullable(courseInfoDTO).isPresent()) {
            List<String> courses = Optional.ofNullable(finalLecturerCoursesMap.get(data.getLecturerId()))
                    .orElse(new ArrayList<>());
            if (!Objects.equals(courseInfoDTO.getCourseName(), data.getCourseName())) {
                errMsgList.add("课程名称错误");
            }else if (!Objects.equals(courseInfoDTO.getIsPublish(),JudgeEnum.CONFIRM.getValue())) {
                errMsgList.add("课程未发布");
            }else if (courses.contains(courseInfoDTO.getId())) {
                errMsgList.add("课程认证讲师信息已经存在");
            }else {
                data.setCourseId(courseInfoDTO.getId()).setCourseNo(courseInfoDTO.getCourseNo())
                        .setCourseName(courseInfoDTO.getCourseName())
                        .setCourseCategory(courseInfoDTO.getCourseCateId());
            }
        } else {
            errMsgList.add("课程编号无效");
        }
        if (StringUtils.isNotBlank(data.getAuthenticationOrg()) && data.getAuthenticationOrg().length() > 50) {
            errMsgList.add("认证组织(名称)过长，只允许50个字符");
        }
        if (CollectionUtils.isNotEmpty(errMsgList)) {
            errorMap.put(data.getRowNum(), errMsgList);
        }
    }

    private void checkLecturerLoginName(LecturerCourseAuthenticationBO data, Map<String, Lecturer> finalLecturerLoginNameMap, List<String> errMsgList) {
        if (StringUtils.isNotBlank(data.getLecturerLoginName())) {
            Lecturer lecturer = finalLecturerLoginNameMap.get(data.getLecturerLoginName());
            if (Objects.isNull(lecturer)) {
                errMsgList.add("讲师账号无效");
            } else {
                if (StringUtils.isNotBlank(data.getLecturerNumber()) && !Objects.equals(data.getLecturerNumber(),
                        lecturer.getLecturerNumber())) {
                    errMsgList.add("讲师账号与讲师编号不匹配");
                }
                data.setLecturerId(lecturer.getId());
                if (!Objects.equals(data.getLecturerName(), lecturer.getName())) {
                    errMsgList.add("讲师姓名不正确");
                }
            }
        }
    }

    private void checkLecturerNumber(LecturerCourseAuthenticationBO data, Map<String, Lecturer> finalLecturerNumberIdMap, List<String> errMsgList) {
        // 验证讲师编号&装配讲师id
        if (StringUtils.isNotBlank(data.getLecturerNumber())) {
            Lecturer lecturer = finalLecturerNumberIdMap.get(data.getLecturerNumber());
            if (Objects.isNull(lecturer)) {
                errMsgList.add("讲师编号无效");
            } else {
                data.setLecturerId(lecturer.getId());
                if (!Objects.equals(data.getLecturerName(), lecturer.getName())) {
                    errMsgList.add("讲师姓名不正确");
                }
            }
        }
    }

    /**
     * 批量保存
     *
     * @param list
     * @return
     */
    private int saveImportDataList(List<LecturerCourseAuthenticationBO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        saveBatch2(list.stream().map(data -> {
            LecturerCourseAuthentication authentication = new LecturerCourseAuthentication();
            BeanUtils.copyProperties(data, authentication);
            authentication.setId(StringUtil.newId());
            return authentication;
        }).collect(Collectors.toList()));
        return list.size();
    }

    /**
     * 设置查询时间区间值
     *
     * @param query
     */
    private void buildQueryTimeZone(LecturerCourseQuery query) {
        if (Objects.isNull(query.getModel()) || Objects.isNull(query.getQueryDate())) {
            return;
        }
        //初始化查询日期
        initQueryDate(query);

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(query.getQueryDate());

        // 设置查询月度时间区间值
        buildQueryMonthTimeZone(query, calendar);
        // 设置查询年度时间区间
        buildQueryYearTimeZone(query, calendar);
    }


    /**
     * 初始化查询日期
     *
     * @param query
     */
    private void initQueryDate(LecturerCourseQuery query) {
        if (Objects.nonNull(query.getQueryDate())) {
            return;
        }
        query.setQueryDate(new Date());
    }


    /**
     * 设置查询月度时间区间值
     *
     * @param query
     * @param calendar
     */
    private void buildQueryMonthTimeZone(LecturerCourseQuery query, Calendar calendar) {
        if (!Objects.equals(query.getModel(), QUERY_BY_MONTH_MODE)) {
            return;
        }

        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        query.setStartTime(DateUtil.getFirstDayOfMonth(year, month));
        query.setEndTime(DateUtil.getLastDayOfMonth(year, month));
    }

    /**
     * 设置查询年度时间区间
     *
     * @param query
     * @param calendar
     */
    private void buildQueryYearTimeZone(LecturerCourseQuery query, Calendar calendar) {
        if (!Objects.equals(query.getModel(), QUERY_BY_YEAR_MODE)) {
            return;
        }
        int year = calendar.get(Calendar.YEAR);
        query.setStartTime(DateUtil.getFirstDayOfMonth(year, 1));
        query.setEndTime(DateUtil.getLastDayOfMonth(year, 12));
    }
}
