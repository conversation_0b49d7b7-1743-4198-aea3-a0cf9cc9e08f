package com.wunding.learn.lecturer.service.imports;

import com.wunding.learn.common.i18n.util.I18nUtil;
import com.wunding.learn.common.i18n.util.ImportTemplateI18nEnum;
import com.wunding.learn.common.util.date.DateHelper;
import com.wunding.learn.common.util.excel.AbstractExcelTemplate;
import com.wunding.learn.common.util.excel.ExcelCheckMessage;
import com.wunding.learn.lecturer.service.admin.bo.LecturerCourseAuthenticationBO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/4/21
 */
@Slf4j
public class LecturerCourseAuthExcelTemplate extends AbstractExcelTemplate {

    private static final String[] LECTURER_TITLES = {
            "*讲师编号", "*讲师账号", "*讲师姓名", "认证日期", "评分", "认证组织", "课程编号", "课程名称"
    };


    public LecturerCourseAuthExcelTemplate() {
        super(LECTURER_TITLES);
    }

    @Override
    protected ExcelCheckMessage doCheck(ExcelCheckMessage excelCheckMessage) {
        List<String> messages = excelCheckMessage.getMessage();
        String[][] excel = excelCheckMessage.getExcel();
        int rowNum;

        List<LecturerCourseAuthenticationBO> list = new ArrayList<>();
        LecturerCourseAuthenticationBO authentication;

        for (int i = 1; i < excel.length; i++) {
            rowNum = i + 1;
            authentication = new LecturerCourseAuthenticationBO();
            authentication.setRowNum(rowNum); // 讲师编号不能为空
            String lecturerNumber = excel[i][0];
            if (StringUtils.isNotBlank(lecturerNumber)) {
                authentication.setLecturerNumber(lecturerNumber);
            }        // 讲师编号不能为空
            String loginName = excel[i][1];
            if (StringUtils.isBlank(lecturerNumber) && StringUtils.isBlank(loginName)) {
                messages.add(I18nUtil.getImportMessage(
                    ImportTemplateI18nEnum.LecturerCourseAuthExcelTemplateInstructorCannotEmpty, rowNum));
            } else {
                authentication.setLecturerLoginName(loginName);
            }

            buildAuthentication(excel[i], rowNum, messages, authentication);
            list.add(authentication);
        }
        excelCheckMessage.setObjects(list);
        excelCheckMessage.setMessage(messages);
        return excelCheckMessage;
    }

    /**
     * 校验导入Excel
     *
     * @param excel          excel数据
     * @param rowNum         当前行
     * @param messages       错误信息
     * @param authentication 保存记录
     */
    private void buildAuthentication(String[] excel, int rowNum, List<String> messages, LecturerCourseAuthenticationBO authentication) {
        // 讲师姓名不能为空
        String lecturerName = excel[2];
        if (StringUtils.isBlank(lecturerName)) {
            messages.add(I18nUtil.getImportMessage(
                ImportTemplateI18nEnum.LecturerCourseAuthExcelTemplateInstructorNameCannotEmpty, rowNum));
        }
        authentication.setLecturerName(lecturerName);

        // 认证日期不为空时进行格式校验
        String authDateStr = excel[3];
        if (StringUtils.isNotBlank(authDateStr)) {
            try {
                authentication.setAuthenticationTime(DateHelper.toDate(authDateStr, DateHelper.YYYYMMDD_HHMMSS));
            } catch (Exception e) {
                messages.add(I18nUtil.getImportMessage(
                    ImportTemplateI18nEnum.LecturerCourseAuthExcelTemplateAuthenticationDateformatIsIncorrect, rowNum));
                log.error("发生异常", e);
            }
        }

        // 评分必须是数字或者为空且不能大于10
        String scoreStr = excel[4];
        if (StringUtils.isNotBlank(scoreStr)) {
            try {
                BigDecimal score = new BigDecimal(scoreStr);
                authentication.setScore(score);
                if (score.compareTo(BigDecimal.TEN) > 0) {
                    messages.add(I18nUtil.getImportMessage(
                        ImportTemplateI18nEnum.LecturerCourseAuthExcelTemplateScoreCannotGreater10, rowNum));
                }
            } catch (Exception e) {
                messages.add(I18nUtil.getImportMessage(
                    ImportTemplateI18nEnum.LecturerCourseAuthExcelTemplateScoreDataformatIsIncorrect, rowNum));
                log.error("发生异常", e);
            }
        }

        // 认证组织可以为空
        authentication.setAuthenticationOrg(excel[5]);

        // 课程编号(暂时不)允许为空
        String courseNo = excel[6];
        if (StringUtils.isBlank(courseNo)) {
            messages.add(
                I18nUtil.getImportMessage(ImportTemplateI18nEnum.LecturerCourseAuthExcelTemplateCourseNumberCannotEmpty,
                    rowNum));
        } else {
            authentication.setCourseNo(courseNo);
        }

        // 课程名称不允许为空
        String courseName = excel[7];
        if (StringUtils.isBlank(courseName)) {
            messages.add(
                I18nUtil.getImportMessage(ImportTemplateI18nEnum.LecturerCourseAuthExcelTemplateCourseNameCannotEmpty,
                    rowNum));
        } else {
            authentication.setCourseName(courseName);
        }
    }
}
