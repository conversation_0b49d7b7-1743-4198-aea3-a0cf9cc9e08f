package com.wunding.learn.lecturer.service.admin.dto;

import com.wunding.learn.lecturer.service.admin.dto.base.LecturerTeachRewardConfigBase;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/4/14
 */
@Data
@Accessors(chain = true)
@Schema(name = "LecturerTeachRewardConfigEditDTO", description = "讲师课酬规则编辑数据对象")
public class LecturerTeachRewardConfigEditDTO extends LecturerTeachRewardConfigBase {

    @Schema(description = "计划分类id", required = true)
    private String planCategoryId;

    @Schema(description = "培训分类id")
    private String trainCategoryId;

    @Schema(description = "课程分类id")
    private String courseCategoryId;

    @Schema(description = "讲师分类id")
    private String lecturerCategoryId;
}
