package com.wunding.learn.lecturer.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * </p> 非工作日表
 *
 * <AUTHOR> href="mailto:<EMAIL>">yanglequn</a>
 * @since 2023-03-29
 */
@Data
@Schema(name = "NotWorkdayDTO", description = "非工作日DTO")
public class NotWorkdayDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 年份
     */
    @Schema(description = "年份")
    private String year;

    /**
     * 日期
     */
    @Schema(description = "日期")
    private String fillDate;

    /**
     * 类型 0-班 1-休
     */
    @Schema(description = "类型 0-班 1-休")
    private Integer type;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 账号
     */
    @Schema(description = "账号")
    private String loginName;

    /**
     * 姓名
     */
    @Schema(description = "姓名")
    private String fullName;

    /**
     * 新增人
     */
    @Schema(description = "新增人", hidden = true)
    private String createBy;


}
