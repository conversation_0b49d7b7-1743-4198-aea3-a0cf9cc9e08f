package com.wunding.learn.lecturer.service.admin.rest;


import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.bean.BasePageQuery;
import com.wunding.learn.common.bean.Result;
import com.wunding.learn.lecturer.service.admin.dto.LecturerTeachRewardLimitDTO;
import com.wunding.learn.lecturer.service.admin.dto.LecturerTeachRewardLimitEditDTO;
import com.wunding.learn.lecturer.service.admin.dto.LecturerTeachRewardLimitReviewDTO;
import com.wunding.learn.lecturer.service.service.ILecturerTeachRewardLimitService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>  讲师课酬上限配置(月度) 前端控制器
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi</a>
 * @since 2023-03-07
 */
@RestController
@RequestMapping("${module.lecturer.contentPath:/}teachRewardLimit")
@Tag(description = "讲师课酬上限配置管理", name = "LecturerTeachRewardLimitRest")
public class LecturerTeachRewardLimitRest {

    @Resource
    private ILecturerTeachRewardLimitService limitService;

    @GetMapping("/list")
    @Operation(operationId = "LecturerTeachRewardLimit_list", summary = "课酬上限配置规则-获取数据列表接口", description = "课酬上限配置规则-获取数据列表接口")
    public Result<PageInfo<LecturerTeachRewardLimitDTO>> list(@ParameterObject BasePageQuery query) {
        return Result.success(limitService.pageData(query));
    }

    @PostMapping
    @Operation(operationId = "LecturerTeachRewardLimit_save", summary = "课酬上限配置规则-保存数据接口", description = "课酬上限配置规则-保存数据接口")
    public Result<Void> save(@RequestBody @Valid LecturerTeachRewardLimitEditDTO dto) {
        limitService.saveData(dto);
        return Result.success();
    }

    @GetMapping("/{id}")
    @Operation(operationId = "LecturerTeachRewardLimit_get", summary = "课酬上限配置规则-获取复显数据接口", description = "课酬上限配置规则-获取复显数据接口")
    public Result<LecturerTeachRewardLimitReviewDTO> get(
        @PathVariable("id") @Parameter(description = "规则id") @NotBlank String id) {
        return Result.success(limitService.getData(id));
    }

    @PutMapping("/{id}")
    @Operation(operationId = "LecturerTeachRewardLimit_update", summary = "课酬上限配置规则-更新数据接口", description = "课酬上限配置规则-更新数据接口")
    public Result<Void> update(
        @PathVariable("id") @Parameter(description = "规则id") @NotBlank String id,
        @RequestBody @Valid LecturerTeachRewardLimitEditDTO dto) {
        limitService.updateData(dto, id);
        return Result.success();
    }

    @DeleteMapping("/{id}")
    @Operation(operationId = "LecturerTeachRewardLimit_delete", summary = "课酬上限配置规则-删除数据接口", description = "课酬上限配置规则-删除数据接口")
    public Result<Void> delete(
        @PathVariable("id") @Parameter(description = "规则id") @NotBlank String id) {
        limitService.deleteData(id);
        return Result.success();
    }
}
