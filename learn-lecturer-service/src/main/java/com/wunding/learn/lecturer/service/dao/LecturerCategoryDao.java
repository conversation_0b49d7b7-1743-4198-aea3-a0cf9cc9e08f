package com.wunding.learn.lecturer.service.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.lecturer.service.model.LecturerCategory;

/**
 * <p> 讲师分类 数据操作接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">gcz</a>
 * @since 2024-02-20
 */
public interface LecturerCategoryDao extends IService<LecturerCategory> {

    /**
     * 保存讲师分类
     *
     * @param lecturerCategory
     */
    void saveLecturerCategory(LecturerCategory lecturerCategory);


    /**
     * 更新讲师分类
     * @param lecturerCategory
     */
    void updateLecturerCategory(LecturerCategory lecturerCategory);


    /**
     * 删除讲师分类
     * @param lecturerCategory
     */
    void delLecturerCategory(LecturerCategory lecturerCategory);

    /**
     * 启用讲师分类
     *
     * @param lecturerCategory
     */
    void enable(LecturerCategory lecturerCategory);

    /**
     * 禁用讲师分类
     *
     * @param lecturerCategory
     */
    void disable(LecturerCategory lecturerCategory);
}
