package com.wunding.learn.lecturer.service.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * </p> 知识库材料
 *
 * <AUTHOR> href="mailto:<EMAIL>">李毅慧</a>
 * @since 2022-05-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("lecturer_material")
@Schema(name = "LecturerMaterial", description = "知识库材料")
public class LecturerMaterial implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     *
     */
    @Schema(description = "")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;


    /**
     * 材料名称
     */
    @Schema(description = "材料名称")
    @TableField("name")
    private String name;


    /**
     * 材料类型
     */
    @Schema(description = "材料类型")
    @TableField("type")
    private String type;


    /**
     * 材料格式
     */
    @Schema(description = "材料格式")
    @TableField("mime")
    private String mime;


    /**
     * 版本号
     */
    @Schema(description = "版本号")
    @TableField("version")
    private String version;


    /**
     * 新增人
     */
    @Schema(description = "新增人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;


    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 最后更新人
     */
    @Schema(description = "最后更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;


    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 是否启用 0否 1是
     */
    @Schema(description = "是否启用 0否 1是")
    @TableField("is_available")
    private Integer available;


    /**
     * 显示顺序
     */
    @Schema(description = "显示顺序")
    @TableField("sort_no")
    private Integer sortNo;


    /**
     * 是否删除 0否 1是
     */
    @Schema(description = "是否删除 0否 1是")
    @TableField("is_del")
    @TableLogic(value = "0", delval = "1")
    private Integer del;


    /**
     * 下发方式：0 部分可见 1仅创建者可见 2所有人可见
     */
    @Schema(description = "下发方式：0 部分可见 1仅创建者可见 2所有人可见")
    @TableField("view_type")
    private Integer viewType;


    /**
     * 创建组织ID
     */
    @Schema(description = "创建组织ID")
    @TableField("org_id")
    private String orgId;


    /**
     * 知识库类型
     */
    @Schema(description = "知识库类型")
    @TableField("category_type")
    private String categoryType;


    /**
     * 视频转换状态  1 转换中  2 转换完成 3 转换失败
     */
    @Schema(description = "视频转换状态  1 转换中  2 转换完成 3 转换失败")
    @TableField("transform_status")
    private Integer transformStatus;


    /**
     * 材料格式(旧)
     */
    @Schema(description = "材料格式(旧)")
    @TableField("old_mime")
    private String oldMime;



}
