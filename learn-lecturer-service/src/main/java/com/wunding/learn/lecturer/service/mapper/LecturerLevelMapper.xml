<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.lecturer.service.mapper.LecturerLevelMapper">

        <!-- 开启二级缓存 -->
        <!--
    <cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
    -->

        <!-- 使用缓存 -->
        <cache-ref namespace="com.wunding.learn.lecturer.service.mapper.LecturerLevelMapper"/>

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.wunding.learn.lecturer.service.model.LecturerLevel">
            <!--@Table lecturer_level-->
                    <id column="id" jdbcType="VARCHAR" property="id"/>
                    <result column="serial_number" jdbcType="VARCHAR"
                            property="serialNumber"/>
                    <result column="lecturer_category_id" jdbcType="VARCHAR"
                            property="lecturerCategoryId"/>
                    <result column="level_name" jdbcType="VARCHAR"
                            property="levelName"/>
                    <result column="sort_no" jdbcType="INTEGER"
                            property="sortNo"/>
                    <result column="is_interior" jdbcType="TINYINT"
                            property="isInterior"/>
                    <result column="class_fees" jdbcType="DECIMAL"
                            property="classFees"/>
                    <result column="virtual_class_fees" jdbcType="INTEGER"
                            property="virtualClassFees"/>
                    <result column="virtual_class_fees_type" jdbcType="TINYINT"
                            property="virtualClassFeesType"/>
                    <result column="logo_url" jdbcType="VARCHAR"
                            property="logoUrl"/>
                    <result column="is_available" jdbcType="TINYINT"
                            property="isAvailable"/>
                    <result column="create_by" jdbcType="VARCHAR"
                            property="createBy"/>
                    <result column="create_time" jdbcType="TIMESTAMP"
                            property="createTime"/>
                    <result column="update_by" jdbcType="VARCHAR"
                            property="updateBy"/>
                    <result column="update_time" jdbcType="TIMESTAMP"
                            property="updateTime"/>
                    <result column="is_del" jdbcType="TINYINT"
                            property="isDel"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, serial_number, lecturer_category_id, level_name, sort_no, is_interior, class_fees, virtual_class_fees, virtual_class_fees_type, logo_url, is_available, create_by, create_time, update_by, update_time, is_del
        </sql>

        <select id="getDitch" resultType="com.wunding.learn.common.dto.CerDitchDTO">
                select id,level_name name,1 state
                from lecturer_level
                where is_del=0 and  is_available =1  and id = #{contentId}
        </select>



        <select id="getLevelList" resultType="com.wunding.learn.lecturer.service.model.LecturerLevel" useCache="false">
                select
                        ll.id, ll.serial_number, ll.lecturer_category_id, ll.level_name, ll.sort_no, ll.is_interior,
                        ll.class_fees, ll.virtual_class_fees, ll.virtual_class_fees_type, ll.logo_url, ll.is_available,
                        ll.create_by, ll.create_time, ll.update_by, ll.update_time, ll.is_del
                from lecturer_level ll
                <if test="params.sortType != null and params.sortType == 1">
                        inner join lecturer_category lc on lc.id = ll.lecturer_category_id
                </if>
                where ll.is_del = 0
                <if test="params.virtualClassFeesType != null">
                        and ll.virtual_class_fees_type = #{params.virtualClassFeesType}
                </if>
                <if test="params.isAvailable != null">
                        and ll.is_available = #{params.isAvailable}
                        <!-- 特殊业务处理：讲师课酬查询要求先按讲师分类排序，再按讲师等级排序，且要过滤未启用的等级和分类 -->
                        <if test="params.sortType != null and params.sortType == 1">
                                and lc.is_available = #{params.isAvailable}
                        </if>
                </if>
                <if test="params.lecturerCategoryId != null and params.lecturerCategoryId !=''">
                        and ll.lecturer_category_id = #{params.lecturerCategoryId}
                </if>
                <if test="params.isInterior != null">
                        and ll.is_interior = #{params.isInterior}
                </if>
                <if test="params.levelName != null and params.levelName.trim() != ''">
                        and instr(ll.level_name,#{params.levelName}) > 0
                </if>
                <if test="params.serialNumber != null and params.serialNumber.trim() != ''">
                        and instr(ll.serial_number,#{params.serialNumber}) > 0
                </if>
                order by <if test="params.sortType != null and params.sortType == 1">lc.sort_no  asc,</if> ll.sort_no asc, create_time desc
        </select>


        <update id="moveSortNo">
                update lecturer_level
                <if test="type == 1"> set sort_no = sort_no - 1 </if>
                <if test="type == 2"> set sort_no = sort_no + 1 </if>
                where is_del = 0
                <if test="maxSortNo == null">
                        <if test="type == 1"><![CDATA[ and sort_no > #{minSortNo}]]></if>
                        <if test="type == 2"><![CDATA[ and sort_no >= #{minSortNo} and sort_no < 99]]></if>
                </if>
                <if test="maxSortNo != null">
                        <if test="type == 1"><![CDATA[ and sort_no > #{minSortNo} and sort_no <= #{maxSortNo}]]></if>
                        <if test="type == 2"><![CDATA[ and sort_no >= #{minSortNo} and sort_no < #{maxSortNo}]]></if>
                </if>
        </update>
        <update id="reSetSortNo">
                update lecturer_level a
                        join (select id, row_number() over (order by sort_no asc, create_time desc) as sort_no from `lecturer_level` where is_del=0) b on a.id = b.id
                set a.sort_no = b.sort_no;
        </update>

</mapper>
