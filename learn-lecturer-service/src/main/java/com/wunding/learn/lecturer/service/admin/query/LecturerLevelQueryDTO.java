package com.wunding.learn.lecturer.service.admin.query;

import com.wunding.learn.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 讲师等级查询对象
 *
 * <AUTHOR>
 * @title: LecturerCategoryQueryDTO
 * @projectName: mlearn
 * @description： 讲师分类查询对象
 * @date 2022/5/11 16:59
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LecturerLevelQueryDTO extends BaseEntity {

    @Parameter(description = "虚拟课酬类型 1-金币 2-积分")
    private Integer virtualClassFeesType;

    @Parameter(description = "是否启用 0-否 1-是")
    private Integer isAvailable;

    @Parameter(description = "讲师类别id")
    private String lecturerCategoryId;

    @Parameter(description = "是否内部 0-否 1-是")
    private Integer isInterior;

    @Parameter(description = "等级名称")
    private String levelName;

    @Parameter(description = "编码")
    private String serialNumber;

    /**
     * 排序方式 0：按等级排序（默认），1：按分类和等级排序 在进行讲师课酬查询（即讲师等级查询）时，要求先根据讲师分类排序，然后再根据讲师等级排序
     */
    @Parameter(description = "排序方式 0：按等级排序（默认），1：先按讲师分类排序，再按讲师等级排序（如果提供了 isAvailable 参数，则会对讲师分类也进行数据过滤）")
    private Integer sortType = 0;
}
