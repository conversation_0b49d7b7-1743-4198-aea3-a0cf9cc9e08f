package com.wunding.learn.lecturer.service.component;

import com.wunding.learn.common.viewlimit.constant.LimitTable;
import com.wunding.learn.common.viewlimit.service.impl.BaseViewLimitServiceImpl;
import com.wunding.learn.lecturer.service.model.CategorysViewLimit;
import com.wunding.learn.lecturer.service.service.ICategorysViewLimitService;
import java.util.List;
import jakarta.annotation.Resource;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/6/21
 */
@Component("lecturerCategorysViewLimitComponent")
public class CategorysViewLimitComponent extends BaseViewLimitServiceImpl<CategorysViewLimit> implements
    CommandLineRunner {

    @Resource
    private ICategorysViewLimitService categorysViewLimitService;

    public CategorysViewLimitComponent() {
        super(LimitTable.CategorysViewLimit);
    }

    @Override
    public void run(String... args) throws Exception {
        // document why this method is empty
    }

    @Override
    public void saveBatch(List<CategorysViewLimit> baseViewLimits) {
        categorysViewLimitService.saveBatch(baseViewLimits);
    }

    @Override
    public void removeBatchByIds(List<CategorysViewLimit> baseViewLimits) {
        categorysViewLimitService.removeBatchByIds(baseViewLimits);
    }
}
