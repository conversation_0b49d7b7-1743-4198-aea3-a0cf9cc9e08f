package com.wunding.learn.lecturer.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/6/23
 */
@Data
@Schema(name = "LecturerMaterialDTO", description = "知识库材料数据对象")
public class LecturerMaterialDTO implements Serializable {

    public static final long serialVersionUID = 1L;

    @Schema(description = "主键id")
    private String id;

    @Schema(description = "材料名称")
    private String name;

    @Schema(description = "材料类型")
    private String type;

    @Schema(description = "材料格式")
    private String mime;

    @Schema(description = "转换状态 1-转码中 2-转码成功 3-转码失败 4-排队中")
    private Integer transformStatus;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "编辑时间")
    private Date updateTime;

    @Schema(description = "知识库分类名称")
    private String knowledgeType;

    @Schema(description = "是否启用 0:未启用  1:启用")
    private Integer isAvailable;

    @Schema(description = "显示顺序")
    private Integer sortNo;

    @Schema(description = "文件资源地址")
    private String url;

    @Schema(description = "组织id", hidden = true)
    private String orgId;

    @Schema(description = "创建者id", hidden = true)
    private String createBy;

    @Schema(description = "创建、归属部门")
    private String orgName;
}
