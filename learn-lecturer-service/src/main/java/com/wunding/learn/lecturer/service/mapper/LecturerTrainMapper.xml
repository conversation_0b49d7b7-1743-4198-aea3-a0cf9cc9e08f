<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.lecturer.service.mapper.LecturerTrainMapper">

        <!-- 开启二级缓存 -->
        <!--
    <cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
    -->

        <!-- 使用缓存 -->
        <cache-ref namespace="com.wunding.learn.lecturer.service.mapper.LecturerTrainMapper"/>

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.wunding.learn.lecturer.service.model.LecturerTrain">
            <!--@Table lecturer_train-->
                    <id column="id" jdbcType="VARCHAR" property="id"/>
                    <result column="category_type" jdbcType="VARCHAR"
                            property="categoryType"/>
                    <result column="user_id" jdbcType="VARCHAR"
                            property="userId"/>
                    <result column="name" jdbcType="VARCHAR"
                            property="name"/>
                    <result column="level" jdbcType="VARCHAR"
                            property="level"/>
                    <result column="intro" jdbcType="VARCHAR"
                            property="intro"/>
                    <result column="join_time" jdbcType="TIMESTAMP"
                            property="joinTime"/>
                    <result column="mobile_phone" jdbcType="VARCHAR"
                            property="mobilePhone"/>
                    <result column="email" jdbcType="VARCHAR"
                            property="email"/>
                    <result column="is_available" jdbcType="BIT"
                            property="available"/>
                    <result column="is_del" jdbcType="BIT"
                            property="del"/>
                    <result column="create_by" jdbcType="VARCHAR"
                            property="createBy"/>
                    <result column="create_time" jdbcType="TIMESTAMP"
                            property="createTime"/>
                    <result column="update_by" jdbcType="VARCHAR"
                            property="updateBy"/>
                    <result column="update_time" jdbcType="TIMESTAMP"
                            property="updateTime"/>
                    <result column="org_id" jdbcType="VARCHAR"
                            property="orgId"/>
                    <result column="class_repay" jdbcType="DECIMAL"
                            property="classRepay"/>
                    <result column="contact_name" jdbcType="VARCHAR"
                            property="contactName"/>
                    <result column="is_engage" jdbcType="BIT"
                            property="engage"/>
                    <result column="engage_company" jdbcType="VARCHAR"
                            property="engageCompany"/>
                    <result column="engage_type" jdbcType="VARCHAR"
                            property="engageType"/>
                    <result column="engage_begin_time" jdbcType="TIMESTAMP"
                            property="engageBeginTime"/>
                    <result column="engage_end_time" jdbcType="TIMESTAMP"
                            property="engageEndTime"/>
                    <result column="froete" jdbcType="VARCHAR"
                            property="froete"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, category_type, user_id, name, level, intro, join_time, mobile_phone, email, is_available, is_del, create_by, create_time, update_by, update_time, org_id, class_repay, contact_name, is_engage, engage_company, engage_type, engage_begin_time, engage_end_time, froete
        </sql>

</mapper>
