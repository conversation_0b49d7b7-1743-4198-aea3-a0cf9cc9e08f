package com.wunding.learn.comment.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.wunding.learn.comment.api.dto.CommentDetailDTO;
import com.wunding.learn.comment.api.dto.CountCommonDTO;
import com.wunding.learn.comment.service.admin.dto.ResourceCommentListDTO;
import com.wunding.learn.comment.service.admin.dto.ResourceCommentSaveDTO;
import com.wunding.learn.comment.service.admin.query.CommentListQuery;
import com.wunding.learn.comment.service.client.dto.ReplyCommentListDTO;
import com.wunding.learn.comment.service.client.dto.ReplyCountDTO;
import com.wunding.learn.comment.service.client.dto.ResourceCommentApiListDTO;
import com.wunding.learn.comment.service.client.dto.SaveCommentDTO;
import com.wunding.learn.comment.service.client.query.CommentApiListQuery;
import com.wunding.learn.comment.service.dao.ResourceCommentDao;
import com.wunding.learn.comment.service.mapper.ResourceCommentMapper;
import com.wunding.learn.comment.service.model.ResourceComment;
import com.wunding.learn.comment.service.service.IResourceCommentService;
import com.wunding.learn.common.constant.comment.CommentErrorNoEnum;
import com.wunding.learn.common.constant.other.AvailableEnum;
import com.wunding.learn.common.constant.other.BaseErrorNoEnum;
import com.wunding.learn.common.constant.other.CommonConstants;
import com.wunding.learn.common.constant.other.DelEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.dto.AuditDTO;
import com.wunding.learn.common.dto.DeleteDTO;
import com.wunding.learn.common.dto.EffectiveDTO;
import com.wunding.learn.common.enums.ResourceTypeEnum;
import com.wunding.learn.common.enums.comment.CommentTypeEnum;
import com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum;
import com.wunding.learn.common.enums.excitation.ExcitationEventEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.other.ResourceChangeEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.mq.dto.ExcitationMQEventDTO;
import com.wunding.learn.common.mq.event.ResourceChangeEvent;
import com.wunding.learn.common.mq.event.ResourceInteractEvent;
import com.wunding.learn.common.mq.event.ResourceInteractEvent.ResourceInteractEventRoutingKeyConstants;
import com.wunding.learn.common.mq.event.excitation.ExcitationMQEvent;
import com.wunding.learn.common.mq.service.MqProducer;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.constant.FileBizType;
import com.wunding.learn.file.api.constant.ImageBizType;
import com.wunding.learn.file.api.dto.AbstractExportDataDTO;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.file.api.service.FileFeign;
import com.wunding.learn.user.api.dto.OrgShowDTO;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.dto.UserOrgDTO;
import com.wunding.learn.user.api.service.OrgFeign;
import com.wunding.learn.user.api.service.RouterFeign;
import com.wunding.learn.user.api.service.SysSensitiveWordFeign;
import com.wunding.learn.user.api.service.UserFeign;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <p> 资源评论主表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">liuxiuyong</a>
 * @since 2022-09-07
 */
@Slf4j
@Service("resourceCommentService")
public class ResourceCommentServiceImpl extends ServiceImpl<ResourceCommentMapper, ResourceComment> implements
    IResourceCommentService {

    @Resource
    private OrgFeign orgFeign;

    @Resource
    private UserFeign userFeign;

    @Resource
    private FileFeign fileFeign;

    @Resource
    private MqProducer mqProducer;

    @Resource
    private ExportComponent exportComponent;
    @Resource(name = "resourceCommentDao")
    private ResourceCommentDao resourceCommentDao;
    @Resource
    private RouterFeign routerFeign;
    @Resource
    private SysSensitiveWordFeign sysSensitiveWordFeign;

    @Override
    public PageInfo<ResourceCommentListDTO> getCommentList(CommentListQuery commentListQuery) {
        //用户id
        if (StringUtils.isNotBlank(commentListQuery.getUserIds())) {
            commentListQuery.setUserIdsVo(
                Arrays.asList(commentListQuery.getUserIds().split(CommonConstants.A_COMMA_IN_ENGLISH)));
        }
        //资源id
        if (StringUtils.isNotBlank(commentListQuery.getResourceIds())) {
            commentListQuery.setResourceIdsVo(
                Arrays.asList(commentListQuery.getResourceIds().split(CommonConstants.A_COMMA_IN_ENGLISH)));
        }
        PageInfo<ResourceCommentListDTO> pageInfo = PageMethod.startPage(commentListQuery.getPageNo(),
            commentListQuery.getPageSize()).doSelectPageInfo(() -> baseMapper.getCommentList(commentListQuery));
        Set<String> userIdList = pageInfo.getList().stream().map(ResourceCommentListDTO::getUserId)
            .filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        Map<String, UserOrgDTO> userMap = orgFeign.getUserAndOrgMapByUserIds(userIdList);
        Map<String, OrgShowDTO> orgShowDTOMap = orgFeign.getOrgShowDTO(userFeign.getUserOrdIdByUserIds(userIdList));
        pageInfo.getList()
            .forEach(comment -> Optional.ofNullable(userMap.get(comment.getUserId())).ifPresent(userInfo -> {
                comment.setFullName(userInfo.getFullName());
                comment.setLoginName(userInfo.getLoginName());
                Optional.ofNullable(orgShowDTOMap.get(userInfo.getOrgId())).ifPresent(orgShowDTO -> {
                    comment.setOrgName(orgShowDTO.getOrgShortName());
                    comment.setOrgPath(orgShowDTO.getLevelPathName());
                });
                comment.setUserAvatar(userInfo.getAvatar());
                comment.setLevelPathName(userInfo.getLevelPathName());
            }));
        return pageInfo;
    }

    @Override
    public void saveResourceComment(ResourceCommentSaveDTO resourceCommentSaveDTO) {
        String userId = UserThreadContext.getUserId();
        ResourceComment resourceComment = new ResourceComment();
        resourceComment.setId(StringUtil.newId());
        BeanUtils.copyProperties(resourceCommentSaveDTO, resourceComment);
        resourceComment.setCommentBy(userId);
        resourceComment.setCreateBy(userId);
        resourceComment.setCommentTime(new Date());
        resourceCommentDao.saveResourceComment(resourceComment);
    }

    @Override
    public void delResourceComment(Integer type, DeleteDTO deleteDTO) {

        List<String> ids = deleteDTO.getIds();
        CommentListQuery query = new CommentListQuery();
        query.setResourceIdsVo(ids);

        List<ResourceComment> commentList = baseMapper.getCommentListByIds(ids);

        commentList.forEach(e -> {
            e.setIsDel(deleteDTO.getIsDel());
            resourceCommentDao.delOrRecoverComment(e);
        });
    }

    @Override
    public void effectiveResourceComment(Integer type, EffectiveDTO effectiveDTO) {

        List<ResourceComment> list = list(
            new LambdaQueryWrapper<ResourceComment>().eq(ResourceComment::getResourceType, type)
                .in(ResourceComment::getId, effectiveDTO.getIds()));
        list.forEach(e -> {
            e.setIsEfficient(effectiveDTO.getIsEffective());
            resourceCommentDao.effectiveResourceComment(e);
        });
    }

    @Override
    public void checkResourceComment(Integer type, AuditDTO auditDTO) {

        List<ResourceComment> list = list(
            new LambdaQueryWrapper<ResourceComment>().eq(ResourceComment::getResourceType, type)
                .in(ResourceComment::getId, auditDTO.getIds()));
        list.forEach(e -> {
            e.setCheckStatus(auditDTO.getStatus());
            resourceCommentDao.auditRecoverComment(e);
        });
    }

    @Override
    public Map<String, Integer> getReplyCountMap(List<String> commentIdList) {
        if (CollectionUtils.isEmpty(commentIdList)) {
            return Collections.emptyMap();
        }
        List<ReplyCountDTO> data = baseMapper.getReplyCountList(commentIdList);
        Map<String, Integer> map = data.stream()
            .collect(Collectors.toMap(ReplyCountDTO::getId, ReplyCountDTO::getCount));
        return map;
    }

    @Override
    public PageInfo<ResourceCommentApiListDTO> getApiCommentList(CommentApiListQuery commentListQuery) {
        commentListQuery.setCurrentUserId(UserThreadContext.getUserId());
        // 精彩评论只查前十
        if (commentListQuery.getCommentType() == 1) {
            commentListQuery.setPageSize(10);
            commentListQuery.setPageNo(1);
        }
        PageInfo<ResourceCommentApiListDTO> pageInfo = PageMethod.startPage(commentListQuery.getPageNo(),
                commentListQuery.getPageSize(), false)
            .doSelectPageInfo(() -> baseMapper.getApiCommentList(commentListQuery));
        Set<String> userIdList = pageInfo.getList().stream().map(ResourceCommentApiListDTO::getUserId)
            .filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        Map<String, UserOrgDTO> userMap = orgFeign.getUserAndOrgMapByUserIds(userIdList);
        pageInfo.getList().forEach(comment -> {
            Optional.ofNullable(userMap.get(comment.getUserId())).ifPresent(userInfo -> {
                comment.setFullName(userInfo.getFullName());
                comment.setLoginName(userInfo.getLoginName());
                comment.setHeadImage(userInfo.getAvatar());
                comment.setUserTitle("");
            });
            //评论回复总数
            Long sonCommentCount = count(
                new LambdaQueryWrapper<ResourceComment>().eq(ResourceComment::getParentId, comment.getId())
                    .eq(ResourceComment::getCheckStatus, GeneralJudgeEnum.CONFIRM.getValue()));
            comment.setReplyCount(sonCommentCount.intValue());
            //有回复去获取
            if (sonCommentCount > 0) {
                comment.setReplyCommentList(getApiReplyList(comment.getId(), 1, 3).getList());
            }
            comment.setRecordingFile(
                fileFeign.getFileNamePath(comment.getId(), FileBizType.ExperienceRecording.toString()));
            comment.setImgList(fileFeign.getImageFileNamePaths(comment.getId(), ImageBizType.COMMENT_IMAGE.name()));
        });
        pageInfo.setIsLastPage(commentListQuery.getPageSize() > pageInfo.getList().size());
        return pageInfo;
    }

    @Override
    public CommentDetailDTO getDetail(String commentId) {
        CommentDetailDTO detail = Optional.ofNullable(baseMapper.getDetail(commentId, UserThreadContext.getUserId()))
            .orElseThrow(() -> new BusinessException(CommentErrorNoEnum.ERR_COMMENT_NULL));
        UserDTO commentUserInfo = userFeign.getUserById(detail.getUserId());
        Optional.ofNullable(commentUserInfo).ifPresent(userInfo -> {
            detail.setFullName(userInfo.getFullName());
            detail.setLoginName(userInfo.getLoginName());
            detail.setHeadImage(userInfo.getAvatar());
            detail.setUserTitle("");
        });
        //评论回复总数
        Long sonCommentCount = count(
            new LambdaQueryWrapper<ResourceComment>().eq(ResourceComment::getParentId, detail.getId()));
        detail.setReplyCount(sonCommentCount.intValue());
        detail.setRecordingFile(fileFeign.getFileNamePath(detail.getId(), FileBizType.ExperienceRecording.toString()));
        detail.setImgList(fileFeign.getImageFileNamePaths(detail.getId(), ImageBizType.COMMENT_IMAGE.name()));
        return detail;
    }


    @Override
    public Map<String, List<CommentDetailDTO>> findMostLikeCommentByResourceId(Collection<String> resourceIdList) {
        Map<String, List<CommentDetailDTO>> result = new HashMap<>();

        for (String resourceId : resourceIdList) {
            List<CommentDetailDTO> commentDetailDTOList = baseMapper.selectMostLikeCommentByResourceId(resourceId);
            if (!CollectionUtils.isEmpty(commentDetailDTOList)) {
                List<String> userIdList = commentDetailDTOList.stream().map(CommentDetailDTO::getUserId)
                    .toList();

                Map<String, UserDTO> userDTOMap = userFeign.getUserNameMapByIds(userIdList);
                for (CommentDetailDTO commentDetailDTO : commentDetailDTOList) {
                    String userId = commentDetailDTO.getUserId();
                    UserDTO userDTO = userDTOMap.get(userId);
                    if (userDTO != null) {
                        commentDetailDTO.setFullName(userDTO.getFullName());
                        commentDetailDTO.setLoginName(userDTO.getLoginName());
                        commentDetailDTO.setHeadImage(userDTO.getAvatar());
                        commentDetailDTO.setUserTitle("");
                    }
                }
            }
            result.put(resourceId, commentDetailDTOList);
        }

        return result;
    }

    @Override
    public void save(SaveCommentDTO saveCommentDTO) {
        List<String> routerIds = routerFeign.getRouterNames();
        // 检验系统版本权限是否包含要校验的内容；
        if(routerIds.contains(ResourceTypeEnum.SENSITIVE_CONFIG.getRouter())){
            // 敏感词校验
            String descStr = sysSensitiveWordFeign.checkWordsReturnStr(saveCommentDTO.getContent());
            if (!StringUtils.isEmpty(descStr)) {
                throw new BusinessException(BaseErrorNoEnum.ERR_HAS_SENSITIVE_WORD_EXCEPTION);
            }
        }
        ResourceComment comment = new ResourceComment();
        comment.setId(StringUtil.newId());
        comment.setCommentBy(UserThreadContext.getUserId());
        comment.setCommentTime(new Date());
        comment.setResourceId(saveCommentDTO.getResourceId());
        comment.setResourceType(saveCommentDTO.getType());
        comment.setParentId(saveCommentDTO.getParentId());
        comment.setContent(saveCommentDTO.getContent());
        comment.setResourceName(saveCommentDTO.getResourceName());
        comment.setResourceCode(saveCommentDTO.getResourceCode());
        comment.setIsAvailable(AvailableEnum.AVAILABLE.getValue());
        comment.setSource(saveCommentDTO.getSource());
        comment.setIsEfficient(GeneralJudgeEnum.NEGATIVE.getValue());
        comment.setIsDel(DelEnum.NOT_DELETE.getValue());
        if (StringUtils.isNotBlank(saveCommentDTO.getParentId())) {
            ResourceComment dbResource = getById(saveCommentDTO.getParentId());
            if (dbResource != null) {
                comment.setResourceName(dbResource.getResourceName());
            }
        }
        if (!CollectionUtils.isEmpty(saveCommentDTO.getImgList())) {
            fileFeign.saveImages(comment.getId(), ImageBizType.COMMENT_IMAGE.name(), saveCommentDTO.getImgList());
        }
        if (saveCommentDTO.getRecordingFile() != null) {
            fileFeign.saveFile(comment.getId(), FileBizType.ExperienceRecording.toString(),
                saveCommentDTO.getRecordingFile().getName(), saveCommentDTO.getRecordingFile().getPath());
        }
        save(comment);
        sendMqCommentMsg(saveCommentDTO);
    }

    @Override
    public PageInfo<ReplyCommentListDTO> getApiReplyList(String commentId, Integer pageNo, Integer pageSize) {
        PageInfo<ReplyCommentListDTO> pageInfo = PageMethod.startPage(pageNo, pageSize, false)
            .doSelectPageInfo(() -> baseMapper.getReplyCommentList(commentId));
        // 遍历回复
        pageInfo.getList().forEach(reply -> {
            UserDTO user = userFeign.getUserById(reply.getUserId());
            if (user != null) {
                reply.setFullName(user.getFullName());
                reply.setLoginName(user.getLoginName());
                reply.setHeadImage(user.getAvatar());
                reply.setUserTitle("");
            }
        });
        pageInfo.setIsLastPage(pageSize > pageInfo.getList().size());
        return pageInfo;
    }

    @Override
    public Long getApiCount(Integer type, String resourceId) {
        LambdaQueryWrapper<ResourceComment> query = new LambdaQueryWrapper<>();
        query.eq(ResourceComment::getResourceId, resourceId);
        query.eq(ResourceComment::getResourceType, type);
        query.eq(ResourceComment::getParentId, "");
        return baseMapper.selectCount(query);
    }

    @Override
    public List<CountCommonDTO> getDiscussCount(Collection<String> resourceId, Integer resourceType) {
        return baseMapper.getDiscussCount(resourceId, resourceType);
    }

    @Override
    public List<CountCommonDTO> getValidDiscussCount(Collection<String> resourceId, Integer resourceType) {
        return baseMapper.getValidDiscussCount(resourceId, resourceType);
    }

    /**
     * 发送资源评论相关的激励事件
     *
     * @param saveCommentDTO {@link SaveCommentDTO}
     */
    private void sendMqCommentMsg(SaveCommentDTO saveCommentDTO) {
        ExcitationMQEventDTO eventDTO = new ExcitationMQEventDTO().setUserId(UserThreadContext.getUserId())
            .setTargetId(saveCommentDTO.getResourceId()).setTargetName(saveCommentDTO.getResourceName());
        eventDTO.setIsExchange(saveCommentDTO.getIsExchange());
        eventDTO.setBizId(StringUtils.isBlank(saveCommentDTO.getBizId()) ? saveCommentDTO.getResourceId()
            : saveCommentDTO.getBizId());
        eventDTO.setBizType(Optional.ofNullable(saveCommentDTO.getBizType())
            .orElse(CommentTypeEnum.getItemEventCategoryEnumCode(saveCommentDTO.getType())));
        if (StringUtils.isNotEmpty(saveCommentDTO.getParentId())) {
            sendMqReplyCommentMsg(saveCommentDTO, eventDTO);
            return;
        }
        switch (Objects.requireNonNull(CommentTypeEnum.getItem(saveCommentDTO.getType()))) {
            case COURSE:
                eventDTO.setEventId(ExcitationEventEnum.commentCourse.name());// 刷新课程评论数
                mqProducer.sendMsg(
                    new ResourceInteractEvent(ResourceInteractEventRoutingKeyConstants.COURSE_COMMENT_EVENT,
                        saveCommentDTO.getResourceId()));
                break;
            case INFO:
                eventDTO.setEventId(ExcitationEventEnum.commentNews.name());
                // 刷新资讯评论数
                mqProducer.sendMsg(
                    new ResourceInteractEvent(ResourceInteractEventRoutingKeyConstants.INFO_COMMENT_EVENT,
                        saveCommentDTO.getResourceId()));
                break;
            case EXAMPLE:
                eventDTO.setEventId(ExcitationEventEnum.commentExample.name());
                // 刷新案例库评论数
                mqProducer.sendMsg(
                    new ResourceInteractEvent(ResourceInteractEventRoutingKeyConstants.EXAMPLE_COMMENT_EVENT,
                        saveCommentDTO.getResourceId()));
                break;
            case COURSEWARE:
                eventDTO.setCategory(ExcitationEventCategoryEnum.COURSE_WARE.getCode());
                eventDTO.setEventId(ExcitationEventEnum.commentCourseWare.name());
                mqProducer.sendMsg(
                    new ResourceInteractEvent(ResourceInteractEventRoutingKeyConstants.COURSEWARE_COMMENT_EVENT,
                        saveCommentDTO.getResourceId()));
                break;
            default:
                eventDTO.setEventId(StringUtils.EMPTY);
        }
        if (StringUtils.isEmpty(eventDTO.getEventId())) {
            return;
        }
        mqProducer.sendMsg(new ExcitationMQEvent(eventDTO));
    }


    /**
     * 发送资源评论回复相关的激励事件
     *
     * @param saveCommentDTO {@link SaveCommentDTO}
     */
    private void sendMqReplyCommentMsg(SaveCommentDTO saveCommentDTO, ExcitationMQEventDTO eventDTO) {
        if (StringUtils.isEmpty(saveCommentDTO.getParentId())) {
            return;
        }
        switch (Objects.requireNonNull(CommentTypeEnum.getItem(saveCommentDTO.getType()))) {
            case COURSE:
                eventDTO.setCategory(ExcitationEventCategoryEnum.COURSE.getCode());
                eventDTO.setEventId(ExcitationEventEnum.replyCourseComment.name());
                break;
            case COURSEWARE:
                eventDTO.setCategory(ExcitationEventCategoryEnum.COURSE_WARE.getCode());
                eventDTO.setEventId(ExcitationEventEnum.replyCourseWareComment.name());
                break;
            default:
                eventDTO.setEventId(StringUtils.EMPTY);
        }
        if (StringUtils.isEmpty(eventDTO.getEventId())) {
            return;
        }
        mqProducer.sendMsg(new ExcitationMQEvent(eventDTO));
    }

    @Override
    public void exportData(CommentListQuery commentListQuery) {

        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IResourceCommentService, ResourceCommentListDTO>(
            commentListQuery) {

            @Override
            protected IResourceCommentService getBean() {
                return SpringUtil.getBean("resourceCommentService", IResourceCommentService.class);
            }

            @Override
            protected PageInfo<ResourceCommentListDTO> getPageInfo() {
                return getBean().getCommentList((CommentListQuery) queryDTO);
            }

            @Override
            public ExportBizType getType() {
                if (Objects.equals(0, commentListQuery.getResourceType())) {
                    return ExportBizType.CourseComment;
                } else if (Objects.equals(3, commentListQuery.getResourceType())) {
                    return ExportBizType.ExampleComment;
                }
                return ExportBizType.Comment;
            }

            @Override
            public String getFileName() {
                if (Objects.equals(0, commentListQuery.getResourceType())) {
                    return ExportFileNameEnum.CourseComment.getType();
                } else if (Objects.equals(3, commentListQuery.getResourceType())) {
                    return ExportFileNameEnum.ExampleComment.getType();
                }
                return ExportFileNameEnum.Comment.getType();
            }

            @Override
            protected void afterCreateBeanMap(Map<String, Object> map) {
                //课程评论不在下面处理
                if (Objects.equals(0, commentListQuery.getResourceType())) {
                    return;
                }
                String isDelStr = "isDel";
                Object isDel = map.get(isDelStr);
                if (isDel.equals(1)) {
                    map.put(isDelStr, "已删除");
                } else if (isDel.equals(0)) {
                    map.put(isDelStr, "正常");
                }
                String isEfficientStr = "isEfficient";
                Object isEfficient = map.get(isEfficientStr);
                if (isEfficient.equals(1)) {
                    map.put(isEfficientStr, "有效");
                } else if (isEfficient.equals(0)) {
                    map.put(isEfficientStr, "无效");
                }
            }
        };

        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public Map<String, Integer> getSplendidDiscussCount(Collection<String> resourceIdList) {
        Map<String, Integer> map = new HashMap<>();
        if (CollectionUtils.isEmpty(resourceIdList)) {
            return map;
        }
        for (String resourceId : resourceIdList) {
            Integer splendidDiscussCount = baseMapper.getSplendidDiscussCount(resourceId);
            map.put(resourceId, splendidDiscussCount <= 10 ? splendidDiscussCount : 10);
        }
        return map;
    }

    @Override
    public void deleteContentComment(ResourceChangeEvent resourceRemoveEvent) {
        // 物理删除
        Integer type = ResourceChangeEnum.valueOf(resourceRemoveEvent.getResourceType()).getValue();
        if (type == -1) {
            return;
        }
        Integer affectRow = baseMapper.realRemoveResourceComment(type, resourceRemoveEvent.getContentIdList());
        if (log.isDebugEnabled()) {
            log.debug("delete_content_comment_affect_row:[{}]", affectRow);
        }
    }
}
