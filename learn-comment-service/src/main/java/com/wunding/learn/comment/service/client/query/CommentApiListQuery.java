package com.wunding.learn.comment.service.client.query;

import com.wunding.learn.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 获取资源评论查询对象
 *
 * <AUTHOR>
 */
@Data
@Schema(name = "CommentApiListQuery", description = "学员端资源评论查询对象")
public class CommentApiListQuery extends BaseEntity {

    @Schema(description = "资源类型 0 课程,1资讯,2话题,3案例,4共读,5项目,6专题", hidden = true)
    private Integer resourceType;

    @Schema(description = "资源id", hidden = true)
    private String resourceId;

    @Schema(description = "填写内容")
    private String content;

    @Schema(description = "评论类型 0 普通评论 1精彩评论")
    private Integer commentType;
}
