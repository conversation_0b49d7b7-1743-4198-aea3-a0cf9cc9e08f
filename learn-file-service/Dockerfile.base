FROM openjdk:11

ENV TINI_VERSION v0.19.0
ENV TINI_SUBREAPER=true

RUN sed -i 's/deb.debian.org/mirrors.163.com/g' /etc/apt/sources.list
RUN echo 'deb http://mirrors.163.com/debian sid main' >> /etc/apt/sources.list

RUN apt-get update && \
  apt-get install -y --no-install-recommends apt-utils

RUN  apt-get install -y --no-install-recommends tini libglib2.0-0 libfreetype6 libfontconfig1 libcairo2 libpng16-16 libjpeg62-turbo libtiff5 libopenjp2-7 libxml2 fonts-noto-cjk libjpeg62-turbo libreoffice libreoffice-java-common libreoffice-writer ffmpeg


COPY fonts.tar /

#COPY pdf2htmlEX-0.18.8.rc2-master-20220831-debian-11-x86_64.deb /

COPY pdf2htmlex_0.0.18.8.rc2.master.11.20220831-0_amd64.deb /

RUN dpkg -i /pdf2htmlex_0.0.18.8.rc2.master.11.20220831-0_amd64.deb && rm -f /pdf2htmlex_0.0.18.8.rc2.master.11.20220831-0_amd64.deb && tar xf fonts.tar -C /
#RUN apt-get install -y /pdf2htmlex_0.0.18.8.rc2.master.11.20220831-0_amd64.deb &&  rm -f /pdf2htmlex_0.0.18.8.rc2.master.11.20220831-0_amd64.deb && tar xvf fonts.tar -C /

COPY pdf2htmlEX_data /config/pdf2htmlEX_data
# COPY ffmpeg-4.3.1 /app/ffmpeg
COPY template /app/template

RUN chmod +x /app/ffmpeg/*

#RUN apt install -y /pdf2htmlEX-0.18.8.rc2-master-20220831-debian-11-x86_64.deb && \
#    rm -f pdf2htmlEX-0.18.8.rc2-master-20220831-debian-11-x86_64.deb

#RUN groupadd -r trans -g 1000;useradd -d /home/<USER>/bin/bash -g trans trans


# 配置/etc/timezone和/etc/localtime,镜像限制时区在东8区，供java配置
RUN echo 'Asia/Shanghai' > /etc/timezone
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime

#USER trans
