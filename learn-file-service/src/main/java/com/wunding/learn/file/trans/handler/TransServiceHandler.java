package com.wunding.learn.file.trans.handler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.wunding.learn.common.constant.other.DelEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.file.CWTypeEnum;
import com.wunding.learn.common.enums.file.TranscodeStatusEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.other.SystemConfigCodeEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.mq.event.TransCodeEvent;
import com.wunding.learn.common.mq.event.TransCodeFinishEvent;
import com.wunding.learn.common.mq.service.MqProducer;
import com.wunding.learn.common.util.bean.ApplicationContextHelper;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.file.api.mq.event.FileExtractEvent;
import com.wunding.learn.file.config.SysConfig;
import com.wunding.learn.file.mapper.VideoClarityMapper;
import com.wunding.learn.file.model.Files;
import com.wunding.learn.file.model.UploadFileRequest;
import com.wunding.learn.file.model.VideoClarity;
import com.wunding.learn.file.service.IFilesService;
import com.wunding.learn.file.service.UploadFileService;
import com.wunding.learn.file.trans.grpc.lib.TransReply;
import com.wunding.learn.file.trans.grpc.lib.TransRequest;
import com.wunding.learn.file.trans.grpc.lib.TransServiceGrpc.TransServiceStub;
import com.wunding.learn.file.trans.pdf.pojo.TransCodeResult;
import com.wunding.learn.file.trans.service.TransService;
import com.wunding.learn.file.trans.service.impl.MsOfficeTransServiceImpl;
import com.wunding.learn.file.trans.service.impl.WpsOfficeTransServiceImpl;
import com.wunding.learn.file.util.MimeUtil;
import com.wunding.learn.file.util.UrlHelper;
import com.wunding.learn.trans.api.enums.OfficeTransTypeEnum;
import com.wunding.learn.user.api.service.ParaFeign;
import io.grpc.Metadata;
import io.grpc.Metadata.Key;
import io.grpc.StatusRuntimeException;
import io.grpc.stub.StreamObserver;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.devh.boot.grpc.client.inject.GrpcClient;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 转码逻辑处理分发
 *
 * <AUTHOR> href="mailto:<EMAIL>">yuxueyi</a>
 * @version V7.0.0
 * @since 2022/5/26  14:01
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class TransServiceHandler {

    private final MqProducer mqProducer;
    private final SysConfig sysConfig;
    private final ParaFeign paraFeign;
    private final IFilesService filesService;

    private final UrlHelper urlHelper;
    private final UploadFileService uploadFileService;
    private final VideoClarityMapper videoClarityMapper;


    @GrpcClient("learn-trans-server")
    private TransServiceStub transServiceStub;

    @SuppressWarnings("java:S3776")
    public TransCodeResult trans(TransCodeEvent transCodeEvent) {
        log.info(
            "trans Thread name:{},id:{},jwt:{}",
            Thread.currentThread().getName(),
            Thread.currentThread().threadId(),
            UserThreadContext.getJwt()
        );
        //检查媒体类型的合法性
        String oldMime = transCodeEvent.getMime();
        if (StringUtils.isBlank(oldMime)) {
            String suffix = FilenameUtils.getExtension(transCodeEvent.getPath());
            oldMime = MimeUtil.getMimeType(suffix);
        }
        MimeUtil.checkSupportMime(oldMime);

        String type = transCodeEvent.getCwType();
        String path = transCodeEvent.getPath();
        Files needTransFiles = filesService.getById(transCodeEvent.getId());

        uploadFileService.downloadFile(needTransFiles.getCurrentPath());

        TransRequest request = builderTransRequest(transCodeEvent.getCwType(), path, needTransFiles.getCurrentPath());

        TransCodeResult transCodeResult = new TransCodeResult();
        transCodeResult.setStatus(TranscodeStatusEnum.TRANSFORMING);
        synchronized (transCodeResult) {

            CountDownLatch latch = new CountDownLatch(1);
            String tenantId = UserThreadContext.getTenantId();

            StreamObserver<TransReply> streamObserver = new StreamObserver<>() {
                @Override
                public void onNext(TransReply reply) {
                    log.info(
                        "trans streamObserver onNext Thread name:{},id:{},jwt:{},tenantId:{},tenantId:{}",
                        Thread.currentThread().getName(),
                        Thread.currentThread().threadId(),
                        UserThreadContext.getJwt(),
                        UserThreadContext.getTenantId(),
                        tenantId
                    );
                    UserThreadContext.setJwt(null);
                    UserThreadContext.setTenantId(tenantId);
                    transReplyHandler(reply, transCodeResult, transCodeEvent, needTransFiles);
                }

                @Override
                public void onError(Throwable e) {
                    log.info(
                        "trans streamObserver onError Thread name:{},id:{},jwt:{},tenantId:{},tenantId:{}",
                        Thread.currentThread().getName(),
                        Thread.currentThread().threadId(),
                        UserThreadContext.getJwt(),
                        UserThreadContext.getTenantId(),
                        tenantId
                    );
                    // 处理错误信息（可能基于已发现的错误进行枚举或者配置映射，转换为相对友好的提示，否则使用兜底的异常提示-转码失败）
                    handleErr(transCodeResult, e);
                    UserThreadContext.setTenantId(tenantId);
                    transCodeResult.setStatus(TranscodeStatusEnum.TRANSFORM_FAILED);
                    noticeBiz(transCodeEvent, transCodeResult);
                    latch.countDown();
                    log.error("转码异常", e);
                    if (e instanceof StatusRuntimeException) {
                        log.error("转码异常e.getCause().getMessage():{}", e.getCause().getMessage());
                        String courseMsg = e.getCause().getMessage();
                        if (courseMsg.contains("Connection reset by peer") || courseMsg.contains(
                            "Connection refused")) {
                            // 转换中，不认为失败
                            log.error("置为转换中， 不认为失败等待重试--Connection reset by peer");
                            transCodeResult.setStatus(TranscodeStatusEnum.TRANSFORMING);
                            transCodeResult.setErrorMessage("RETRY");
                        }
                        grpcStatusErrorLog((StatusRuntimeException) e);
                    }
                }

                @Override
                public void onCompleted() {
                    log.info(
                        "trans streamObserver onCompleted Thread name:{},id:{},jwt:{},tenantId:{},tenantId:{}",
                        Thread.currentThread().getName(),
                        Thread.currentThread().threadId(),
                        UserThreadContext.getJwt(),
                        UserThreadContext.getTenantId(),
                        tenantId
                    );
                    UserThreadContext.setTenantId(tenantId);
                    latch.countDown();
                }

            };

            if (CWTypeEnum.Audio.name().equals(type)) {
                transCodeResult.setMime("audio/mp3");
                transServiceStub.audioTrans(request, streamObserver);

            } else if (
                CWTypeEnum.Word.name().equals(type)
                    || CWTypeEnum.PPT.name().equals(type)
                    || CWTypeEnum.Excel.name().equals(type)
            ) {
                // office转码
                if (OfficeTransTypeEnum.MS_OFFICE_TRANS.getValue() == sysConfig.getOfficeTransType()) {
                    // ms office
                    return ((TransService) ApplicationContextHelper.getBean(MsOfficeTransServiceImpl.class)).doTrans(
                        transCodeEvent);
                } else if (OfficeTransTypeEnum.WPS_OFFICE_TRANS.getValue() == sysConfig.getOfficeTransType()) {
                    // wps office
                    return ((TransService) ApplicationContextHelper.getBean(WpsOfficeTransServiceImpl.class)).doTrans(
                        transCodeEvent);
                }
                transCodeResult.setMime("text/html");
                transServiceStub.officeTrans(request, streamObserver);
            } else if (CWTypeEnum.PDF.name().equals(type)) {
                transCodeResult.setMime("text/html");
                transServiceStub.pdfTrans(request, streamObserver);
            } else if (CWTypeEnum.Video.name().equals(type)) {
                transCodeResult.setMime("video/mp4");
                String mpsEnable = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_1001.getCode());
                if (Objects.equals(mpsEnable, "1")) {
                    transServiceStub.mpsVideoTrans(request, streamObserver);
                } else {
                    transServiceStub.videoTrans(request, streamObserver);
                }
            }
            try {
                log.info("转码操作结束。BizId={}, Path={}, BizType={}, 转码结果:{}",
                    transCodeEvent.getBizId(), transCodeEvent.getPath(), transCodeEvent.getBizType(),
                    transCodeResult.getStatus());
                latch.await();
            } catch (InterruptedException e) {
                log.error("wait trans error", e);
                Thread.currentThread().interrupt();
            }
        }
        log.info("转码操作结束。BizId={}, Path={}, BizType={}, 转码结果:{}",
            transCodeEvent.getBizId(), transCodeEvent.getPath(), transCodeEvent.getBizType(),
            transCodeResult.getStatus());
        return transCodeResult;
    }

    private void transReplyHandler(TransReply reply, TransCodeResult transCodeResult, TransCodeEvent transCodeEvent,
        Files needTransFiles) {
        log.info("=====reply.getFileSize：转码后的文件大小 ======>{}", reply.getFileSize());
        if (reply.getStatus() == TranscodeStatusEnum.TRANSFORMED.value) {
            transCodeResult.setStatus(TranscodeStatusEnum.TRANSFORMED);
            transCodeResult.setOutFilePath(reply.getFilePath());

            uploadFileToOss(transCodeEvent.getCwType(), reply);

            String mp3Path = reply.getMp3FilePath();
            uploadFile(mp3Path);
            updateFilesInfo(transCodeEvent, reply);
            noticeBiz(transCodeEvent, transCodeResult);

            String filePath = needTransFiles.getCurrentPath();
            if (CWTypeEnum.Video.name().equals(transCodeEvent.getCwType())) {
                filePath = reply.getMp3FilePath();
            }
            extractText(
                transCodeEvent.getCwType(),
                filePath,
                transCodeEvent.getBizId(),
                transCodeEvent.getBizType(),
                transCodeEvent.getCwType()
            );

        } else {
            transCodeResult.setStatus(TranscodeStatusEnum.TRANSFORM_FAILED);
            log.error("转码失败。BizId={}, Path={}, BizType={}, 转码结果:{}, 转码失败原因:{}",
                transCodeEvent.getBizId(), transCodeEvent.getPath(), transCodeEvent.getBizType(),
                transCodeResult.getStatus(),
                transCodeResult.getReason());
            noticeBiz(transCodeEvent, transCodeResult);
        }
    }


    private TransRequest builderTransRequest(String cwType, String path, String currentPath) {
        TransRequest.Builder builder = TransRequest.newBuilder()
            .setBucketName(sysConfig.getBucketName())
            .setTempFilePath(path)
            .setFilePath(currentPath);
        if (Objects.equals(paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_1001.getCode()), "1")) {
            String mpsAccessKeyId = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_1002.getCode());
            String mpsAccessKeySecret = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_1003.getCode());
            if (mpsAccessKeyId != null) {
                builder.setMpsAccessKeyId(mpsAccessKeyId);
            }
            if (mpsAccessKeySecret != null) {
                builder.setMpsAccessKeySecret(mpsAccessKeySecret);
            }
        }
        int pdfNoDrm = 0;
        try {
            pdfNoDrm = Integer.parseInt(paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_1004.getCode()));
        } catch (NumberFormatException e) {
            log.warn("读取是否关闭pdf数字证书异常", e);
        }
        builder.setPdfNoDRM(pdfNoDrm);
        int pdfProof = 0;
        try {
            pdfProof = Integer.parseInt(paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_1005.getCode()));
        } catch (NumberFormatException e) {
            log.warn("读取是否启用PDF兼容式转码异常", e);
        }
        builder.setPdfProof(pdfProof);
        if (CWTypeEnum.Video.name().equals(cwType)) {
            builder.setVideoFormat(getVideoFormat());
            builder.setEnable480(getEnable(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_310.getCode()));
            builder.setEnable720(getEnable(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_311.getCode()));
            builder.setEnable1080(getEnable(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_312.getCode()));
        }
        return builder.build();
    }

    private int getVideoFormat() {
        int videoFormat = 0;
        try {
            videoFormat = Integer.parseInt(
                paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_308.getCode()));
        } catch (NumberFormatException e) {
            log.warn("读取视频转码封装格式异常", e);
        }
        return videoFormat;
    }

    public int getEnable(String key) {
        int enable = 0;
        try {
            enable = Integer.parseInt(paraFeign.getParaValue(key));
        } catch (NumberFormatException e) {
            log.warn("读取是否启用视频转码异常", e);
            if (Objects.equals(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_310.getCode(), key)) {
                enable = 1;
            }
        }
        return enable;
    }

    private void uploadFileToOss(String cwType, TransReply reply) {
        // 上传文件到oss
        if (StringUtils.isNotBlank(reply.getFilePath())) {
            if (Objects.equals(cwType, CWTypeEnum.Video.name()) || Objects.equals(cwType, CWTypeEnum.Audio.name())) {
                UploadFileRequest uploadFileRequest = new UploadFileRequest();
                uploadFileRequest.setFileName(reply.getFilePath());
                uploadFileRequest.setAsyncUpload(false);
                uploadFileRequest.setTempFilePath(sysConfig.getPhysicalPath(reply.getFilePath()));
                uploadFileRequest.setCopyFolderFlag(false);
                uploadFileService.uploadFile(uploadFileRequest);
                for (com.wunding.learn.file.trans.grpc.lib.VideoClarity clarity : reply.getVideoClarityList()) {
                    uploadFileRequest = new UploadFileRequest();
                    uploadFileRequest.setFileName(clarity.getUrl());
                    uploadFileRequest.setAsyncUpload(false);
                    uploadFileRequest.setTempFilePath(sysConfig.getPhysicalPath(clarity.getUrl()));
                    if (clarity.getUrl().toLowerCase().endsWith("mp4")) {
                        uploadFileRequest.setCopyFolderFlag(false);
                    } else {
                        uploadFileRequest.setCopyFolderFlag(true);
                        uploadFileRequest.setDefaultStrategyFileList(
                            List.of(sysConfig.getPhysicalPath(clarity.getUrl())));
                        uploadFileRequest.setFolderSrcPath(
                            sysConfig.getPhysicalPath(FilenameUtils.getPath(clarity.getUrl()))
                        );
                        uploadFileRequest.setFolderTargetPath(FilenameUtils.getPath(clarity.getUrl()));
                    }
                    uploadFileService.uploadFile(uploadFileRequest);
                }
            } else {
                UploadFileRequest uploadFileRequest = new UploadFileRequest();
                uploadFileRequest.setFileName(reply.getFilePath());
                uploadFileRequest.setAsyncUpload(false);
                uploadFileRequest.setTempFilePath(sysConfig.getPhysicalPath(reply.getFilePath()));
                uploadFileRequest.setCopyFolderFlag(true);
                uploadFileRequest.setDefaultStrategyFileList(List.of(sysConfig.getPhysicalPath(reply.getFilePath())));
                uploadFileRequest.setFolderSrcPath(
                    sysConfig.getPhysicalPath(FilenameUtils.getPath(reply.getFilePath()))
                );
                uploadFileRequest.setFolderTargetPath(FilenameUtils.getPath(reply.getFilePath()));
                uploadFileService.uploadFile(uploadFileRequest);
            }
        }
    }

    private void noticeBiz(TransCodeEvent transCodeEvent, TransCodeResult transCodeResult) {
        TransCodeFinishEvent finishEvent = new TransCodeFinishEvent(
            transCodeEvent.getBizType(),
            transCodeEvent.getBizId(),
            transCodeResult.getStatus().value,
            transCodeResult.getMime(),
            transCodeEvent.getCwLibId(),
            transCodeResult.getReason()
        );
        mqProducer.sendMsgNoTransaction(finishEvent);
    }

    private void updateFilesInfo(TransCodeEvent transCodeEvent, TransReply reply) {
        //更新文件信息
        LambdaUpdateWrapper<Files> update = new LambdaUpdateWrapper<>();
        update.set(Files::getCurrentPath, reply.getFilePath());
        update.set(Files::getFileSize, reply.getFileSize());
        update.eq(Files::getCategoryId, transCodeEvent.getBizId());
        update.eq(Files::getCategoryType, transCodeEvent.getBizType());
        update.eq(Files::getIsSource, GeneralJudgeEnum.NEGATIVE.getValue());
        update.eq(Files::getIsAdjunct, GeneralJudgeEnum.NEGATIVE.getValue());
        filesService.update(null, update);

        if (StringUtils.isNotBlank(transCodeEvent.getCwLibId())) {
            // 仅更新转码路文件路径,源文件路径不更新
            filesService.update(new LambdaUpdateWrapper<Files>()
                .eq(Files::getCategoryId, transCodeEvent.getCwLibId())
                .eq(Files::getCategoryType, transCodeEvent.getBizType())
                .eq(Files::getIsSource, 0)
                .eq(Files::getIsAdjunct, 0)
                .set(Files::getCurrentPath, reply.getFilePath())
                .set(Files::getFileSize, reply.getFileSize())
            );
        }
        if (!reply.getVideoClarityList().isEmpty()) {
            for (com.wunding.learn.file.trans.grpc.lib.VideoClarity clarity : reply.getVideoClarityList()) {
                LambdaQueryWrapper<VideoClarity> del = new LambdaQueryWrapper<>();
                del.eq(VideoClarity::getCategoryId, transCodeEvent.getBizId());
                del.eq(VideoClarity::getClarityName, clarity.getName());
                videoClarityMapper.delete(del);
                VideoClarity videoClarity = new VideoClarity();
                videoClarity.setCategoryId(transCodeEvent.getBizId());
                videoClarity.setClarityName(clarity.getName());
                videoClarity.setCurrentPath(clarity.getUrl());
                videoClarity.setIsDel(DelEnum.NOT_DELETE.getValue());
                videoClarityMapper.insert(videoClarity);

                if (StringUtils.isNotBlank(transCodeEvent.getCwLibId())) {
                    LambdaQueryWrapper<VideoClarity> del1 = new LambdaQueryWrapper<>();
                    del1.eq(VideoClarity::getCategoryId, transCodeEvent.getCwLibId());
                    del1.eq(VideoClarity::getClarityName, clarity.getName());
                    videoClarityMapper.delete(del1);
                    VideoClarity videoClarity2 = new VideoClarity();
                    videoClarity2.setCategoryId(transCodeEvent.getCwLibId());
                    videoClarity2.setClarityName(clarity.getName());
                    videoClarity2.setCurrentPath(clarity.getUrl());
                    videoClarity2.setIsDel(DelEnum.NOT_DELETE.getValue());
                    videoClarityMapper.insert(videoClarity2);
                }

            }
        }
    }

    private boolean checkEnableKeyWord(String filePath) {
        String enableKeyWord = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_603.getCode());
        return Objects.equals(enableKeyWord, "1") && !StringUtils.isBlank(filePath);
    }

    private void setMp3FilePath(String type, String filePath, TransRequest.Builder builder) {
        if (CWTypeEnum.Video.name().equals(type) || CWTypeEnum.Audio.name().equals(type)) {
            builder.setFilePath(urlHelper.getStaticFullUrl(filePath));
        } else {
            builder.setFilePath(filePath);
        }
    }

    @SuppressWarnings("squid:S2142")
    private void extractText(String type, String filePath, String bizId, String bizType, String cwType) {

        if (!checkEnableKeyWord(filePath)) {
            return;
        }

        TransRequest.Builder builder = TransRequest.newBuilder();

        setMp3FilePath(type, filePath, builder);

        TransRequest request = builder.build();

        String tenantId = UserThreadContext.getTenantId();

        StreamObserver<TransReply> streamObserver = new StreamObserver<>() {
            @Override
            public void onNext(TransReply reply) {
                log.info(
                    "extractText streamObserver onNext Thread name:{},id:{},jwt:{},tenantId:{},tenantId:{}",
                    Thread.currentThread().getName(),
                    Thread.currentThread().threadId(),
                    UserThreadContext.getJwt(),
                    UserThreadContext.getTenantId(),
                    tenantId
                );
                UserThreadContext.setTenantId(tenantId);
                if (reply.getStatus() == TranscodeStatusEnum.TRANSFORMED.value) {
                    log.info("extractTextPath:{}", reply.getExtractTextPath());

                    String txtPath = reply.getExtractTextPath();
                    uploadFile(txtPath);
                    mqProducer.sendMsg(
                        new FileExtractEvent(
                            bizId,
                            bizType,
                            txtPath,
                            cwType)
                    );

                } else if (reply.getStatus() == TranscodeStatusEnum.TRANSFORM_FAILED.value) {
                    log.info("extractTextPath error status:{}", reply.getStatus());
                }
            }

            @Override
            public void onError(Throwable e) {
                log.info(
                    "extractText streamObserver onError Thread name:{},id:{},jwt:{},tenantId:{},tenantId:{}",
                    Thread.currentThread().getName(),
                    Thread.currentThread().threadId(),
                    UserThreadContext.getJwt(),
                    UserThreadContext.getTenantId(),
                    tenantId
                );
                log.info("extractTextPath error ", e);
                UserThreadContext.setTenantId(tenantId);
                if (e instanceof StatusRuntimeException) {
                    grpcStatusErrorLog((StatusRuntimeException) e);
                }
            }

            @Override
            public void onCompleted() {
                log.info(
                    "extractText streamObserver onCompleted Thread name:{},id:{},jwt:{},tenantId:{},tenantId:{}",
                    Thread.currentThread().getName(),
                    Thread.currentThread().threadId(),
                    UserThreadContext.getJwt(),
                    UserThreadContext.getTenantId(),
                    tenantId
                );
                UserThreadContext.setTenantId(tenantId);
            }
        };

        if (CWTypeEnum.PDF.name().equals(type)) {
            transServiceStub.pdfExtractText(request, streamObserver);
        }

        if (CWTypeEnum.Word.name().equals(type)) {
            transServiceStub.wordExtractText(request, streamObserver);
        }
        if (CWTypeEnum.PPT.name().equals(type)) {
            transServiceStub.pptExtractText(request, streamObserver);
        }

        // 增加开关控制提取音视频文字
        String aiExtractTextSwitch = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_1006.getCode());
        if (String.valueOf(GeneralJudgeEnum.CONFIRM.getValue()).equals(Objects.toString(aiExtractTextSwitch, "0"))) {
            log.info("提取音视频文字开关开启！aiExtractTextSwitch is confirm,start extract text");
            if (CWTypeEnum.Video.name().equals(type)) {
                transServiceStub.audioExtractText(request, streamObserver);
            }

            if (CWTypeEnum.Audio.name().equals(type)) {
                transServiceStub.audioExtractText(request, streamObserver);
            }
        }
    }

    private void grpcStatusErrorLog(StatusRuntimeException exception) {
        Metadata trailers = exception.getTrailers();
        if (trailers != null) {
            String message = trailers.get(Key.of("message", Metadata.ASCII_STRING_MARSHALLER));
            String code = trailers.get(Key.of("code", Metadata.ASCII_STRING_MARSHALLER));
            String data = trailers.get(Key.of("data", Metadata.ASCII_STRING_MARSHALLER));
            log.error("trailers message:{},code:{},data:{}", message, code, data);
        }
    }

    private void uploadFile(String path) {
        log.info("uploadFile path:{}", path);
        if (StringUtils.isNotBlank(path)) {
            UploadFileRequest uploadFileRequest = new UploadFileRequest();
            uploadFileRequest.setFileName(path);
            uploadFileRequest.setAsyncUpload(false);
            uploadFileRequest.setTempFilePath(sysConfig.getPhysicalPath(path));
            uploadFileRequest.setCopyFolderFlag(false);
            uploadFileRequest.setHeaderMap(Map.of("Cache-Control", "no-cache"));
            uploadFileService.uploadFile(uploadFileRequest);
        }
    }

    private void handleErr(TransCodeResult transCodeResult, Throwable e) {
        log.error("转码失败异常，错误转换处理...");
        // 错误的转换处理
        transCodeResult.setReason(StringUtils.EMPTY);
        if (e instanceof BusinessException businessException) {
            String message = businessException.getMessage();
            String truncatedMessage = "";
            if (message != null) {
                if (message.length() > 255) {
                    truncatedMessage = message.substring(0, 255);
                } else {
                    truncatedMessage = message;
                }
            }
            transCodeResult.setReason(truncatedMessage);
        } else {
            // 最长保留255个字符
            String message = e.getMessage();
            String finalReason = getFinalReason(e, message);
            // 限制总长度不超过255
            if (finalReason.length() > 255) {
                finalReason = finalReason.substring(0, 255);
            }
            transCodeResult.setReason(finalReason);
        }
    }

    @NotNull
    private static String getFinalReason(Throwable e, String message) {
        StackTraceElement[] stackTrace = e.getStackTrace();
        String messagePart = "";
        if (message != null) {
            if (message.length() > 255) {
                messagePart = message.substring(0, 255);
            } else {
                messagePart = message;
            }
        }
        String stackInfo = stackTrace != null && stackTrace.length > 0 ? stackTrace[0].toString() : "";
        String stackPart = "";
        if (!stackInfo.isEmpty()) {
            if (stackInfo.length() > 255) {
                stackPart = ", at " + stackInfo.substring(0, 255);
            } else {
                stackPart = ", at " + stackInfo;
            }
        }
        return messagePart + stackPart;
    }


}
