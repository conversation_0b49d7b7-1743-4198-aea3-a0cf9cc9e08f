package com.wunding.learn.file.util;

import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.other.SystemConfigCodeEnum;
import com.wunding.learn.common.util.string.StringPool;
import com.wunding.learn.file.config.SysConfig;
import com.wunding.learn.file.constant.ImageProcessStrategyEnum;
import com.wunding.learn.file.constant.ImageProcessStrategyEnum.ImageHandleParam;
import com.wunding.learn.file.constant.ObjectEndpointFactoryEnum;
import com.wunding.learn.file.dto.TenantBucketInfo;
import com.wunding.learn.file.model.Images;
import com.wunding.learn.user.api.service.ParaFeign;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;

/**
 * 根据request，在request scope中构建url相关的常量，避免每次都要通过request构建请求
 *
 * <AUTHOR>
 */
@Component
//@Scope(value = "request", proxyMode = ScopedProxyMode.TARGET_CLASS)
@Slf4j
public class UrlHelper {

    private static final String SEPARATOR = "/";
    /**
     * 配置里的staticBaseUrl。用于动静态分离
     */
    @Value("${app.staticBaseUrl:}")
    private String staticBaseUrlInConfig;

    /**
     * 存储方式配置
     */
    @Resource
    private SysConfig sysConfig;

    private String staticBaseUrl;

    @Resource
    private ParaFeign paraFeign;

    @PostConstruct
    void init() {
        // 如果有配置，采用配置指定url
        // 如果是对象存储用对象存储的地址
        // 适用于公有读
        // 私有读存在问题
        staticBaseUrl = staticBaseUrlInConfig;

    }

    public String getStaticBaseUrl() {
        return staticBaseUrl;
    }

    public String getCurrentFilePath(String url) {
        if (url.startsWith(staticBaseUrl)) {
            return url.replaceFirst(staticBaseUrl, "");
        } else {
            return url;
        }
    }

    /**
     * 获取文件url并对文件路径进行URL编码
     *
     * @param url
     * @return
     */
    public String getStaticFullUrlCode(String url) {
        String staticFullUrl = getStaticFullUrl(url);
        String fileName = StringUtils.substringAfterLast(staticFullUrl, SEPARATOR);
        log.info("fileName: " + fileName);
        String baseUrl = StringUtils.substringBeforeLast(staticFullUrl, SEPARATOR);
        log.info("baseUrl: " + baseUrl);
        return StringUtils.substringBeforeLast(staticFullUrl, SEPARATOR)
            + SEPARATOR
            + URLEncoder.encode(fileName, StandardCharsets.UTF_8);
    }


    /**
     * 获取文件url
     *
     * @param url
     * @return
     */
    public String getStaticFullUrl(String url) {
        if (StringUtils.isBlank(url)) {
            return url;
        }
        TenantBucketInfo tenantBucketInfo = SysConfig.getTenantBucketInfo();
        String staticBaseUrl = tenantBucketInfo.getStaticBaseUrl();
        String intranetEndPoint = tenantBucketInfo.getIntranetEndPoint();
        if (url.startsWith(intranetEndPoint)) {
            url = url.substring(intranetEndPoint.length());
        }
        if (url.startsWith(staticBaseUrl)) {
            url = url.substring(staticBaseUrl.length());
        }
        // 去掉最前面的/
        if (url.startsWith(SEPARATOR)) {
            url = url.substring(1);
        }
        // 去掉 bucketName
        if (url.startsWith(sysConfig.getBucketName())) {
            url = url.substring(sysConfig.getBucketName().length() + 1);
        }
        // 传进来的地址可能带了签名，去掉地址?后面
        int index = url.indexOf("?");
        if (index != -1) {
            url = url.substring(0, index);
        }
        // 兼容文件名被转码
        url = URLDecoder.decode(url, StandardCharsets.UTF_8);
        // 公有读目录下直接地址拼接
        String rootPath = SysConfig.getRootPath();
        int offset = rootPath.length() + 1;
        boolean isPublicReadPath = url.startsWith(rootPath) &&
            url.regionMatches(offset, "publicRead", 0, "publicRead".length());
        if (isPublicReadPath) {
            return getPublicReadFileUrl(url);
        } else {
            // 过期时间默认2小时
            String second = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_41002.getCode());
            long expiredSeconds = Objects.isNull(second) ? 7200L : Long.parseLong(second);
            // 设置预签名请求
            GetObjectRequest.Builder getObjectRequestBuilder = GetObjectRequest.builder()
                .bucket(sysConfig.getBucketName()).key(url);
            // 构建预签名请求
            GetObjectPresignRequest presignRequest = GetObjectPresignRequest.builder()
                .signatureDuration(Duration.ofSeconds(expiredSeconds))
                .getObjectRequest(getObjectRequestBuilder.build())
                .build();
            return SysConfig.getFileUrl(presignRequest);
        }
    }


    /**
     * 获取图片处理URL
     *
     * @param path            url地址
     * @param qualityTypeEnum 图片处理策略类型
     * @param handleParam     图片处理选填参数
     * @return {@link String}
     */
    public String getImageProcessUrl(String path, ImageProcessStrategyEnum qualityTypeEnum,
        ImageHandleParam handleParam) {

        String resultUrl;

        if (sysConfig.getEndPoint().contains("aliyuncs.com")) {
            // 走 aliyun 自己的处理
            handleParam.setPath(getStaticFullUrl(path));
            resultUrl = qualityTypeEnum.getBiFunction().apply(ObjectEndpointFactoryEnum.ALI_YUN, handleParam);
            return resultUrl;
        }

        // 走minio的处理proxy
        handleParam.setPath(getImgProxyFullUrl(path));
        resultUrl = qualityTypeEnum.getBiFunction().apply(ObjectEndpointFactoryEnum.MINIO, handleParam);
        return resultUrl;
    }

    /**
     * 获取img代理完整url
     *
     * @param path 路径
     * @return {@link String}
     */
    private String getImgProxyFullUrl(String path) {
        String imgProxyUrl = sysConfig.getImgProxyUrl();
        if (path.startsWith(StringPool.SLASH)) {
            path = path.substring(1);
        }

        // 没有配置略缩图URL 则走原图地址
        if (StringUtils.isEmpty(imgProxyUrl)) {
            return getStaticFullUrl(path);
        } else if (imgProxyUrl.endsWith(StringPool.SLASH)) {
            imgProxyUrl = imgProxyUrl.substring(0, imgProxyUrl.length() - 1);
        }

        return imgProxyUrl + "/image/" + "%processing_options/plain/" + "s3://" + path + "@%extension";
    }

    public Map<String, String> getStaticFullUrl(List<Images> images) {
        Map<String, String> map = new HashMap<>();
        for (Images image : images) {
            // 检查关键字段是否为 null
            if (image.getCurrentPath() == null || image.getCategoryId() == null) {
                continue; // 如果关键字段为 null，跳过该对象
            }
            String staticFullUrl = getStaticFullUrl(image.getCurrentPath());
            map.put(image.getCategoryId(), staticFullUrl);
        }
        return map;
    }

    public String getPublicReadFileUrl(String path) {
        if (StringUtils.isBlank(path)) {
            return path;
        }
        TenantBucketInfo tenantBucketInfo = SysConfig.getTenantBucketInfo();
        String staticBaseUrl = tenantBucketInfo.getStaticBaseUrl();
        String intranetEndPoint = tenantBucketInfo.getIntranetEndPoint();
        String baseUrl = SysConfig.checkIntranetNetwork() ? intranetEndPoint : staticBaseUrl;
        if (path.startsWith(intranetEndPoint)) {
            path = path.substring(intranetEndPoint.length());
        }
        if (path.startsWith(staticBaseUrl)) {
            path = path.substring(staticBaseUrl.length());
        }
        // 去掉最前面的/
        if (path.startsWith(SEPARATOR)) {
            path = path.substring(1);
        }
        // virtual host 模式不需要拼桶名
        if (path.startsWith(sysConfig.getBucketName())) {
            if (GeneralJudgeEnum.NEGATIVE.getValue().equals(tenantBucketInfo.getPathMode())) {
                path = path.substring(sysConfig.getBucketName().length() + 1);
            }
        } else {
            if (GeneralJudgeEnum.CONFIRM.getValue().equals(tenantBucketInfo.getPathMode())) {
                path = sysConfig.getBucketName() + SEPARATOR + path;
            }
        }
        // 传进来的地址可能带了签名，去掉地址?后面
        int index = path.indexOf("?");
        if (index != -1) {
            path = path.substring(0, index);
        }
        return baseUrl + SEPARATOR + path;
    }

}
