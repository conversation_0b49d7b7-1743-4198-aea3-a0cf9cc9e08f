<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.file.mapper.ExportRecordMapper">

        <!-- 开启二级缓存 -->
        <!--
    <cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
    -->

        <!-- 使用缓存 -->
        <cache-ref namespace="com.wunding.learn.file.mapper.ExportRecordMapper"/>

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.wunding.learn.file.model.ExportRecord">
            <!--@Table sys_export_record-->
                    <id column="biz_id" jdbcType="VARCHAR" property="bizId"/>
                    <result column="biz_type" jdbcType="VARCHAR"
                            property="bizType"/>
                    <result column="file_name" jdbcType="VARCHAR"
                            property="fileName"/>
                    <result column="current_path" jdbcType="VARCHAR"
                            property="currentPath"/>
                    <result column="status" jdbcType="TINYINT"
                            property="status"/>
                    <result column="is_del" jdbcType="TINYINT"
                            property="isDel"/>
                    <result column="create_by" jdbcType="VARCHAR"
                            property="createBy"/>
                    <result column="create_time" jdbcType="TIMESTAMP"
                            property="createTime"/>
                    <result column="update_by" jdbcType="VARCHAR"
                            property="updateBy"/>
                    <result column="update_time" jdbcType="TIMESTAMP"
                            property="updateTime"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            biz_id, biz_type, file_name, current_path, status, is_del, create_by, create_time, update_by, update_time
        </sql>
        
        <select id="getExportRecordListByPage" resultType="com.wunding.learn.file.dto.ExportRecordListDTO">
                select ser.biz_id,
                        ser.biz_type,
                        ser.file_name,
                        ser.current_path currentUrl,
                        ser.status,
                        ser.is_del,
                        ser.create_by,
                        ser.create_time,
                        ser.update_by,
                        ser.update_time,
                        time_to_sec(timediff(ser.update_time, ser.create_time) )as exportTime
                from sys_export_record ser
                    where ser.is_del = 0
                        and ser.create_time <![CDATA[ <= ]]> sysdate()
                        and ser.create_time > date_add(sysdate(), interval -3 day)
                <if test="params.createBy != null">
                        and ser.create_by = #{params.createBy}
                </if>
                order by ser.create_time desc
        </select>
        
        <select id="findExportRecordDownloadCount" resultType="java.lang.Integer">
                select count(1)
                from sys_export_record ser
                where ser.is_del = 0
                and ser.status = 2
                and ser.create_time <![CDATA[ <= ]]> sysdate()
                and ser.create_time > date_add(sysdate(), interval -3 day)
                <if test="params.createBy != null">
                        and ser.create_by = #{params.createBy}
                </if>
                order by ser.create_time desc
        </select>
        
</mapper>
