<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.file.mapper.CloudFileMapper">
    <!-- 开启二级缓存 -->
    <!--
<cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.file.mapper.CloudFileMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.file.model.CloudFile">
        <!--@Table sys_cloud_file-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="upload_id" jdbcType="VARCHAR"
          property="uploadId"/>
        <result column="merge_status" jdbcType="TINYINT"
          property="mergeStatus"/>
        <result column="md5" jdbcType="VARCHAR"
          property="md5"/>
        <result column="file_name" jdbcType="VARCHAR"
          property="fileName"/>
        <result column="file_size" jdbcType="BIGINT"
          property="fileSize"/>
        <result column="current_path" jdbcType="VARCHAR"
          property="currentPath"/>
        <result column="is_del" jdbcType="TINYINT"
          property="isDel"/>
        <result column="create_by" jdbcType="VARCHAR"
          property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP"
          property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR"
          property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP"
          property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        upload_id,
        merge_status,
        md5,
        file_name,
        file_size,
        current_path,
        is_del,
        create_by,
        create_time,
        update_by,
        update_time
    </sql>
</mapper>
