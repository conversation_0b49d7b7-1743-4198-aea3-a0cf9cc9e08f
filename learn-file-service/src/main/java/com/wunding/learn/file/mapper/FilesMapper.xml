<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.file.mapper.FilesMapper">
    <!-- 开启二级缓存 -->
    <!--
    <cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
    -->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.file.mapper.FilesMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.file.model.Files">
        <!--@Table sys_files-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="file_name" jdbcType="VARCHAR" property="fileName"/>
        <result column="file_size" jdbcType="BIGINT" property="fileSize"/>
        <result column="current_path" jdbcType="VARCHAR" property="currentPath"/>
        <result column="category_id" jdbcType="VARCHAR" property="categoryId"/>
        <result column="category_type" jdbcType="VARCHAR" property="categoryType"/>
        <result column="dpi" jdbcType="VARCHAR" property="dpi"/>
        <result column="width" jdbcType="SMALLINT" property="width"/>
        <result column="height" jdbcType="SMALLINT" property="height"/>
        <result column="sort_no" jdbcType="INTEGER" property="sortNo"/>
        <result column="is_attachment" jdbcType="TINYINT" property="isAttachment"/>
        <result column="video_old_path" jdbcType="VARCHAR" property="videoOldPath"/>
        <result column="is_del" jdbcType="TINYINT" property="isDel"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_adjunct" jdbcType="INTEGER"
          property="isAdjunct"/>
        <result column="is_source" jdbcType="INTEGER"
          property="isSource"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , file_name, file_size, current_path, category_id, category_type, dpi, width, height, sort_no, is_attachment, video_old_path, is_del, create_by, create_time, update_by, update_time, is_adjunct, is_source
    </sql>

    <select id="getFileByCategoryTypeAndIsAdjunct" resultType="com.wunding.learn.file.model.Files">
        select category_id, file_name, current_path
        from sys_files
        where category_type = #{categoryType}
          and is_adjunct = #{isAdjunct}
          and category_id = #{categoryId}
          and is_del = 0
          and is_source = 0
          limit 1
    </select>

    <select id="getFileByCategoryId" resultType="com.wunding.learn.file.model.Files">
        select category_id, file_name, current_path
        from sys_files
        where category_id = #{categoryId}
          and is_del = 0
          and is_source = 0
    </select>

    <select id="getFileByCategoryIdAndIsSourceAndIsAdjunct" resultType="com.wunding.learn.file.model.Files">
        select category_id, file_name, current_path
        from sys_files
        where category_id = #{categoryId}
          and is_del = 0
          and is_source = #{isSource}
          and is_adjunct = #{isAdjunct}
    </select>

    <select id="getFileMapByCategoryIds" resultType="com.wunding.learn.file.model.Files">
        <if test="categoryIds != null and categoryIds.size() > 0">
            select category_id, file_name, current_path, is_adjunct, id
            from sys_files
            <where>
                category_id in
                <foreach collection="categoryIds" item="categoryId" open=" (" close=")" separator=",">
                    #{categoryId}
                </foreach>
                and is_del = 0
                and is_source = 0
            </where>
        </if>
    </select>

    <select id="getSourceFileMapByCategoryIds" resultType="com.wunding.learn.file.model.Files">
        select category_id, file_name, current_path, is_adjunct
        from sys_files
        <where>
            category_id in
            <foreach collection="categoryIds" item="categoryId" open=" (" close=")" separator=",">
                #{categoryId}
            </foreach>
            and is_del = 0
            and is_source = 1
        </where>
    </select>

    <select id="getFileByFileQuery" parameterType="com.wunding.learn.file.api.query.FileQuery"
      resultType="com.wunding.learn.file.api.dto.FileListDTO">
        select *
        from sys_files
        where category_id = #{params.categoryId}
        <if test="params.categoryType != null and params.categoryType != ''">
            and category_type = #{params.categoryType}
        </if>
        <if test="params.fileName != null and params.fileName != ''">
            and instr(file_name,#{params.fileName}) > 0
        </if>
        and is_del = 0
    </select>

    <select id="getMainFileMapByCategoryIds" resultMap="BaseResultMap">
        <if test="categoryIds != null and categoryIds.size() > 0">
            select category_id, file_name, current_path, is_adjunct
            from sys_files
            <where>
                category_id in
                <foreach collection="categoryIds" item="categoryId" open=" (" close=")" separator=",">
                    #{categoryId}
                </foreach>
                and is_del = 0
                and is_source = 0
                and is_adjunct = 0
            </where>
        </if>
    </select>
    <select id="getFileBybizIdAndbizType" resultType="com.wunding.learn.file.model.Files" useCache="false">
        select <include refid="Base_Column_List"></include>
        from sys_files
        where
            is_del = 0
            and is_source = #{i}
        <if test="bizId != null and bizId != ''">
            and category_id = #{bizId}
        </if>
        <if test="bizType != null and bizType != ''">
            and category_type = #{bizType}
        </if>
        order by sort_no
        limit 0,1
    </select>

    <select id="getFileUrlByIdsAndType" resultType="com.wunding.learn.file.model.Files">
        select id, current_path, file_name, category_type, category_id, is_adjunct, is_source
        from sys_files
        where
            is_del = 0
            and is_source = 0
            <if test="categoryIds != null and categoryIds.size() > 0">
                and id in
                <foreach collection="categoryIds" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="bizType != null and bizType != '' ">
                and category_type = #{bizType}
            </if>
    </select>

    <select id="getFilesCategoryIds" resultType="com.wunding.learn.file.model.Files">
        <if test="categoryIds != null and categoryIds.size() > 0">
            select category_id, file_name, current_path, is_adjunct, id
            from sys_files
            <where>
                category_id in
                <foreach collection="categoryIds" item="categoryId" open=" (" close=")" separator=",">
                    #{categoryId}
                </foreach>
            </where>
        </if>
    </select>
</mapper>
