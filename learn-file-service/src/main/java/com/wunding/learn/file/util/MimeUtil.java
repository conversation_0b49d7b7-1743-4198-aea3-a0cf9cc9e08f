package com.wunding.learn.file.util;

import com.wunding.learn.common.constant.file.FileErrorNoEnum;
import com.wunding.learn.common.exception.BusinessException;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 解析mimes工具类
 *
 * <AUTHOR>
 * @date 2022-06-08
 */
public class MimeUtil {

    private MimeUtil() {
        throw new IllegalStateException("Utility class");
    }

    private static final Logger log = LoggerFactory.getLogger(MimeUtil.class);
    private static final Map<String, String> MIME_MAP = new LinkedHashMap<>();

    static {
        SAXReader reader = new SAXReader();
        // 资源文件处理，无需漏洞处理，禁止读取的xml使用xxe和doctype
        try {
            Document document = reader.read(MimeUtil.class.getResourceAsStream("/mimes.xml"));

            Element rootElement = document.getRootElement();
            for (Iterator<?> it = rootElement.elementIterator(); it.hasNext(); ) {
                Element element = (Element) it.next();
                String ext = element.attribute("extension").getText();
                String mime = element.attribute("mime").getText();
                MIME_MAP.put(ext, mime);
            }
        } catch (Exception e) {
            log.error("", e);
        }
    }

    /**
     * 根据文件类型名称获得对应的媒体类型
     */
    public static String getMimeType(String ext) {
        if (StringUtils.isNotBlank(ext)) {
            ext = MIME_MAP.get(ext.toLowerCase());
        }
        return StringUtils.isBlank(ext) ? "" : ext;
    }


    /**
     * 检查mime是否合法
     */
    public static void checkSupportMime(String mime) {
        boolean containsValue = MIME_MAP.containsValue(mime);
        if (!containsValue) {
            log.error("转码检查媒体类型mime:{} 不合法", mime);
            throw new BusinessException(FileErrorNoEnum.ERR_FILE_MIME_NOT_SUPPORT);
        }
    }


}
