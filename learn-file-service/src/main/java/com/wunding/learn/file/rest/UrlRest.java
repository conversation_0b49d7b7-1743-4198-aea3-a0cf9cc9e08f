package com.wunding.learn.file.rest;

import com.wunding.learn.common.bean.Result;
import com.wunding.learn.file.service.IFilesService;
import com.wunding.learn.file.service.StorageStrategyService;
import com.wunding.learn.file.util.UrlHelper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 系统上传相关功能
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON><PERSON></a>
 */
@Slf4j
@RestController
@RequestMapping("${module.file.contentPath:/}url")
@Tag(description = "文件URL相关接口", name = "UrlRest")
public class UrlRest {

    @Resource
    private StorageStrategyService storageStrategyService;
    @Resource
    private IFilesService filesService;

    @Resource
    private UrlHelper urlHelper;


    /**
     * @param path       文件path
     * @param isDownload 是否下载
     * @param fileName   文件名
     * @return 文件URL
     */
    @Operation(operationId = "getUrl", summary = "获取文件URL")
    @GetMapping(value = "getUrl")
    public Result<String> getUrl(
        @Parameter(description = "文件path", required = true) @RequestParam(name = "path") String path,
        @Parameter(description = "是否下载 1:下载") @RequestParam(name = "isDownload", required = false) Integer isDownload,
        @Parameter(description = "文件名") @RequestParam(name = "fileName", required = false) String fileName
    ) {
        return Result.success(storageStrategyService.getUrl(path, isDownload == 1, fileName));
    }

    /**
     * 获取课件下载url
     *
     * @param id id
     * @return {@link Result}<{@link String}>
     */
    @Operation(operationId = "getUrlById", summary = "获取文件URL通过课件id")
    @GetMapping(value = "/getUrlById")
    public Result<String> getUrl(
        @Parameter(description = "关联资id,目前仅限课件id", required = true) @RequestParam(name = "id") String id) {
        return Result.success(filesService.getDownloadUrl(id));
    }
}
