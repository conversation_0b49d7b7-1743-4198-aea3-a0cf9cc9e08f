package com.wunding.learn.file.service.impl;

import static com.wunding.learn.common.util.string.StringUtil.newTimeId;
import static com.wunding.learn.file.rest.UploadRest.ALL_SUFFIX;
import static com.wunding.learn.file.rest.UploadRest.APP_SUFFIX;
import static com.wunding.learn.file.rest.UploadRest.INFO_SUFFIX;
import static com.wunding.learn.file.rest.UploadRest.SPECIAL_SUFFIX;
import static com.wunding.learn.file.rest.UploadRest.TRAIN_PLAN_SUFFIX;
import static com.wunding.learn.file.service.impl.FilesServiceImpl.CERT_RELATE_FILE_SUFFIX;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.common.constant.file.FileErrorNoEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.redis.util.RedisLockUtil;
import com.wunding.learn.common.util.string.StringPool;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.file.api.constant.FileBizType;
import com.wunding.learn.file.api.dto.TempFileDTO;
import com.wunding.learn.file.config.SysConfig;
import com.wunding.learn.file.constant.FileTypeEnum;
import com.wunding.learn.file.dto.UploadCouldChunkDTO;
import com.wunding.learn.file.dto.UploadCouldComposeChunkDTO;
import com.wunding.learn.file.dto.UploadCouldResultDTO;
import com.wunding.learn.file.dto.UploadCourseResultDTO;
import com.wunding.learn.file.dto.UploadResultDTO;
import com.wunding.learn.file.dto.UploadZipResultDTO;
import com.wunding.learn.file.mapper.CloudFileMapper;
import com.wunding.learn.file.model.CloudFile;
import com.wunding.learn.file.model.CloudFileChunk;
import com.wunding.learn.file.model.UploadFileRequest;
import com.wunding.learn.file.service.ICloudFileChunkService;
import com.wunding.learn.file.service.ICloudFileService;
import com.wunding.learn.file.service.IFilesService;
import com.wunding.learn.file.service.UploadFileService;
import com.wunding.learn.file.util.FileUtil;
import com.wunding.learn.file.util.MimeUtil;
import com.wunding.learn.file.util.UrlHelper;
import com.wunding.learn.file.util.ZipUtil;
import jakarta.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.services.s3.model.CreateMultipartUploadRequest;
import software.amazon.awssdk.services.s3.model.CreateMultipartUploadResponse;

/**
 * <p> 云文件 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">wgr</a>
 * @since 2024-01-29
 */
@Slf4j
@Service("cloudFileService")
public class CloudFileServiceImpl extends ServiceImpl<CloudFileMapper, CloudFile> implements ICloudFileService {

    /**
     * 文件后缀类型检测
     */
    private static final Integer ALL_SUFFIXES_CHECK = 1;
    private static final Integer SPECIAL_SUFFIXES_CHECK = 2;
    private static final Integer APP_SUFFIXES_CHECK = 3;
    private static final Integer INFO_SUFFIXES_CHECK = 4;
    private static final Integer TRAIN_PLAN_SUFFIXES_CHECK = 5;
    @Resource
    private ICloudFileChunkService cloudFileChunkService;

    @Resource
    private SysConfig sysConfig;

    @Resource
    private UploadFileService uploadFileService;

    @Resource
    private UrlHelper urlHelper;

    @Resource
    private IFilesService filesService;

    @Override
    public void checkSuffixes(String fileName, Integer checkType) {
        String suffix = FilenameUtils.getExtension(fileName);
        if (StringUtils.isEmpty(suffix)) {
            throw new BusinessException(FileErrorNoEnum.ERR_CLOUD_FILE_SUFFIX);
        }

        Set<String> suffixes = new HashSet<>();
        if (ALL_SUFFIXES_CHECK.equals(checkType)) {
            suffixes = ALL_SUFFIX;
        } else if (SPECIAL_SUFFIXES_CHECK.equals(checkType)) {
            suffixes = SPECIAL_SUFFIX;
        } else if (APP_SUFFIXES_CHECK.equals(checkType)) {
            suffixes = APP_SUFFIX;
        } else if (INFO_SUFFIXES_CHECK.equals(checkType)) {
            suffixes = INFO_SUFFIX;
        } else if (TRAIN_PLAN_SUFFIXES_CHECK.equals(checkType)) {
            suffixes = TRAIN_PLAN_SUFFIX;
        }

        if (!CollectionUtils.isEmpty(suffixes)
            && !suffixes.contains(suffix.toUpperCase())
            && !suffixes.contains(suffix.toLowerCase())) {
            throw new BusinessException(FileErrorNoEnum.ERR_CLOUD_FILE_SUFFIX);
        }
    }

    @Override
    public UploadCouldResultDTO checkCloudFile(String md5, String fileName) {
        // 上锁防止多次初始化
        String lockKey = UserThreadContext.getTenantId() + ":" + md5;
        try {
            RedisLockUtil.acquire(lockKey, 900, 600);
        } catch (Exception e) {
            log.warn("正在查询，等待120秒后异常");
        }

        UploadCouldResultDTO resultDTO;
        try {
            resultDTO = new UploadCouldResultDTO();
            CloudFile cloudFile = lambdaQuery().eq(CloudFile::getMd5, md5).one();
            // 为空则做一次合并任务初始化
            if (Optional.ofNullable(cloudFile).isEmpty()) {
                String composeFileName =
                    SysConfig.getRootPath() + File.separator + FileTypeEnum.FILE.getValue() + File.separator
                        + FileBizType.cloudFile + File.separator + md5 + File.separator + StringUtil.newId() + "."
                        + FilenameUtils.getExtension(fileName);
                composeFileName = FilenameUtils.separatorsToUnix(composeFileName);
                CreateMultipartUploadRequest createRequest = CreateMultipartUploadRequest.builder()
                    .bucket(sysConfig.getBucketName())
                    .key(composeFileName)
                    .build();
                CreateMultipartUploadResponse createResponse = SysConfig.getS3Client()
                    .createMultipartUpload(createRequest);
                String uploadId = createResponse.uploadId();
                CloudFile cloudFileSave = new CloudFile();
                cloudFileSave.setId(newTimeId());
                cloudFileSave.setFileName(fileName);
                cloudFileSave.setUploadId(uploadId);
                cloudFileSave.setMd5(md5);
                cloudFileSave.setCurrentPath(composeFileName);
                save(cloudFileSave);
            } else {
                // 不为空且合并完成才算正式文件
                if (GeneralJudgeEnum.CONFIRM.getValue().equals(cloudFile.getMergeStatus())) {
                    String currentPath = cloudFile.getCurrentPath();
                    resultDTO.setPath(currentPath);
                    resultDTO.setUrl(urlHelper.getStaticFullUrl(currentPath));
                    if (StringUtils.isNotBlank(fileName)) {
                        resultDTO.setFileName(fileName);
                    } else {
                        resultDTO.setFileName(cloudFile.getFileName());
                    }
                    String suffix = FilenameUtils.getExtension(currentPath);
                    resultDTO.setMime(MimeUtil.getMimeType(suffix));
                    resultDTO.setMergeStatus(cloudFile.getMergeStatus());
                } else {
                    resultDTO.setChunkList(getChunkListByMd5(md5));
                }
            }
        } finally {
            RedisLockUtil.release(lockKey);
        }
        return resultDTO;
    }

    /**
     * 根据md5获取文件的分片列表
     *
     * @param md5
     * @return
     */
    private List<Integer> getChunkListByMd5(String md5) {
        List<Integer> chunkList = null;
        List<CloudFileChunk> cloudFileChunkList = cloudFileChunkService.lambdaQuery()
            .eq(CloudFileChunk::getMd5, md5)
            .orderByAsc(CloudFileChunk::getChunkIndex).list();
        if (CollectionUtils.isNotEmpty(cloudFileChunkList)) {
            chunkList = cloudFileChunkList.stream().map(CloudFileChunk::getChunkIndex)
                .collect(Collectors.toList());
        }
        return chunkList;
    }


    @Override
    public UploadCouldResultDTO uploadCloudFileChunk(MultipartFile chunk, UploadCouldChunkDTO uploadCouldChunkDTO) {
        Integer index = uploadCouldChunkDTO.getIndex();
        String md5 = uploadCouldChunkDTO.getMd5();
        String lockKey = UserThreadContext.getTenantId() + ":" + md5 + ":" + index;
        try {
            RedisLockUtil.acquire(lockKey, 900, 600);
        } catch (Exception e) {
            log.warn("正在被上传，等待120秒后异常");
            return checkCloudFile(md5, uploadCouldChunkDTO.getFileName());
        }
        CloudFile cloudFile = Optional.ofNullable(lambdaQuery().eq(CloudFile::getMd5, md5).one())
            .orElseThrow(() -> new BusinessException(FileErrorNoEnum.SAVE_UPLOAD_FILE_ERROR));
        if (GeneralJudgeEnum.CONFIRM.getValue().equals(cloudFile.getMergeStatus())) {
            log.warn("整个文件上传完成,无须在上传");
            return checkCloudFile(md5, uploadCouldChunkDTO.getFileName());
        }
        try {
            LambdaQueryWrapper<CloudFileChunk> query = new LambdaQueryWrapper<>();
            query.eq(CloudFileChunk::getMd5, md5);
            query.eq(CloudFileChunk::getChunkIndex, index);
            long count = cloudFileChunkService.count(query);
            if (count > 0) {
                log.warn("块已上传完成,无须在上传");
                return checkCloudFile(md5, uploadCouldChunkDTO.getFileName());
            }
            //先上传文件，
            String fileName = String.valueOf(index);
            String tmpPath = sysConfig.queryTempPath();
            String indexTempPath = tmpPath + File.separator + md5;
            String pscPath = sysConfig.getPhysicalPath(indexTempPath);
            FileUtil.mkdir(pscPath);
            File file = new File(pscPath + StringPool.SLASH + fileName);
            try {
                chunk.transferTo(file);
            } catch (IOException | IllegalStateException e) {
                log.error("保存上传文件失败", e);
                throw new BusinessException(FileErrorNoEnum.SAVE_UPLOAD_FILE_ERROR);
            }
            String eTag = uploadFileService.uploadPartFile(index, file, cloudFile);
            CloudFileChunk cloudFileChunk = new CloudFileChunk();
            cloudFileChunk.setId(newTimeId());
            cloudFileChunk.setChunkIndex(index);
            cloudFileChunk.setETag(eTag);
            cloudFileChunk.setMd5(md5);
            cloudFileChunkService.save(cloudFileChunk);
            // 检测下，如果当前md5的文件已经被别人合并完成，能够查询到，则直接返回当前文件的路径
            return checkCloudFile(md5, uploadCouldChunkDTO.getFileName());
        } finally {
            RedisLockUtil.release(lockKey);
        }
    }

    @Override
    public UploadCouldResultDTO composeCloudFile(UploadCouldComposeChunkDTO uploadCouldComposeChunkDTO) {

        Integer chunkTotal = uploadCouldComposeChunkDTO.getChunkTotal();
        String md5 = uploadCouldComposeChunkDTO.getMd5();
        String fileName = uploadCouldComposeChunkDTO.getFileName();

        String lockKey = UserThreadContext.getTenantId() + ":" + md5;
        long startTime = System.currentTimeMillis();
        log.info("composeCloudFile md5:{}  fileName:{}", md5, fileName);
        // 如果多个合并请求过来，则进行排队进行合并操作
        try {
            RedisLockUtil.acquire(lockKey, 900, 600);
        } catch (Exception e) {
            throw new BusinessException(FileErrorNoEnum.ERR_CLOUD_FILE_LOCK);
        }
        try {
            //如果获取了锁，则先去检查一下文件是否已经被合并完成，如果已经完成，则直接返回，没必要再次进行合并
            CloudFile cloudFile = Optional.ofNullable(lambdaQuery().eq(CloudFile::getMd5, md5).one())
                .orElseThrow(() -> new BusinessException(FileErrorNoEnum.SAVE_UPLOAD_FILE_ERROR));
            if (GeneralJudgeEnum.CONFIRM.getValue().equals(cloudFile.getMergeStatus())) {
                return checkCloudFile(md5, fileName);
            } else {
                log.info("composeCloudFile md5:{}  fileName:{} start compose file", md5, fileName);
                //查询当前md5的所有分片
                List<CloudFileChunk> cloudFileChunkList = cloudFileChunkService.lambdaQuery()
                    .eq(CloudFileChunk::getMd5, md5)
                    .orderByAsc(CloudFileChunk::getChunkIndex).list();
                if (CollectionUtils.isEmpty(cloudFileChunkList)) {
                    throw new BusinessException(FileErrorNoEnum.ERR_CLOUD_FILE_CHUNK_NOT_EXIST);
                }

                if (chunkTotal != cloudFileChunkList.size()) {
                    throw new BusinessException(FileErrorNoEnum.ERR_CLOUD_FILE_CHUNK_IS_NOT_EQUAL_TOTAL);
                }

                uploadFileService.composeFiles(cloudFileChunkList, cloudFile);

                cloudFile.setMergeStatus(GeneralJudgeEnum.CONFIRM.getValue());
                updateById(cloudFile);
                cloudFileChunkService.removeByMd5(md5);

                UploadCouldResultDTO uploadResultDTO = new UploadCouldResultDTO();
                uploadResultDTO.setPath(cloudFile.getCurrentPath());
                uploadResultDTO.setFileName(fileName);
                String suffix = FilenameUtils.getExtension(fileName);
                uploadResultDTO.setMime(MimeUtil.getMimeType(suffix));
                uploadResultDTO.setUrl(urlHelper.getStaticFullUrl(cloudFile.getCurrentPath()));
                log.info("composeCloudFile md5:{}  fileName:{} cost time:{}", md5, fileName,
                    (System.currentTimeMillis() - startTime));
                return uploadResultDTO;
            }
        } finally {
            RedisLockUtil.release(lockKey);
        }
    }

    @Override
    public UploadCourseResultDTO uploadCourseWareFile(UploadResultDTO uploadResultDTO) {
        return this.buildCourseWareFileInfo(uploadResultDTO);
    }

    private UploadCourseResultDTO buildCourseWareFileInfo(UploadResultDTO uploadResultDTO) {
        UploadCourseResultDTO uploadCourseResultDTO = new UploadCourseResultDTO();
        //需要将文件下载到本地
        uploadFileService.downloadFile(uploadResultDTO.getPath());
        BeanUtils.copyProperties(uploadResultDTO, uploadCourseResultDTO);
        filesService.buildCourseWareFileInfo(uploadCourseResultDTO);
        return uploadCourseResultDTO;
    }

    @Override
    public UploadCourseResultDTO uploadFileInfo(UploadResultDTO uploadResultDTO) {
        return this.buildCourseWareFileInfo(uploadResultDTO);
    }


    @Override
    public UploadZipResultDTO uploadZipFileToDecompress(UploadResultDTO uploadResultDTO) {
        return buildZipResultDTO(uploadResultDTO, false);
    }

    @Override
    public UploadZipResultDTO uploadCertRelateZip(UploadResultDTO uploadResultDTO) {
        return buildZipResultDTO(uploadResultDTO, true);
    }

    @Override
    public UploadZipResultDTO uploadCertPhotoZip(UploadResultDTO uploadResultDTO) {

        return buildZipResultDTO(uploadResultDTO, true);
    }

    /**
     * 根据
     *
     * @param uploadResultDTO
     * @return
     */
    private UploadZipResultDTO buildZipResultDTO(UploadResultDTO uploadResultDTO, boolean isRetainOriginalFileName) {
        //需要将文件下载到本地
        uploadFileService.downloadFile(uploadResultDTO.getPath());

        UploadZipResultDTO result = new UploadZipResultDTO();
        String suffix = FilenameUtils.getExtension(uploadResultDTO.getFileName());
        result.setMime(MimeUtil.getMimeType(suffix));
        result.setFileName(uploadResultDTO.getFileName());
        result.setDiskFileName(uploadResultDTO.getFileName());

        String localFileUrl = sysConfig.getPhysicalPath(uploadResultDTO.getPath());
        result.setChildFilePathList(unzipFile(localFileUrl, isRetainOriginalFileName));

        return result;
    }

    /**
     * 解压文件
     *
     * @param fileUrl                  带解压的文件的本地绝对地址
     * @param isRetainOriginalFileName 是否保持原来的名字
     * @return
     */
    private List<String> unzipFile(String fileUrl, boolean isRetainOriginalFileName) {
        String tmpPath = sysConfig.queryTempPath();
        String pscPath = sysConfig.getPhysicalPath(tmpPath);
        //将压缩包解压
        List<TempFileDTO> urlList = ZipUtil.unZipAndBackUrl(fileUrl, pscPath, isRetainOriginalFileName);
        List<String> childPathList = new ArrayList<>();
        if (!org.springframework.util.CollectionUtils.isEmpty(urlList)) {
            for (TempFileDTO tempFileDTO : urlList) {
                String url = tempFileDTO.getTempPath();
                UploadFileRequest ufr = new UploadFileRequest();
                String childFileName = StringUtils.substring(url, url.lastIndexOf("/") + 1, url.length());
                String childFileSuffix = FilenameUtils.getExtension(childFileName);
                if (isRetainOriginalFileName
                    && !ArrayUtils.contains(CERT_RELATE_FILE_SUFFIX, childFileSuffix.toLowerCase())) {
                    throw new BusinessException(FileErrorNoEnum.ERR_CERT_RELATE_IMAGE_FORMAT);
                }
                String childTempPath = FilenameUtils.separatorsToUnix(tmpPath.concat("/")).concat(childFileName);
                ufr.setFileName(childTempPath);
                ufr.setAsyncUpload(true);
                ufr.setTempFilePath(url);
                ufr.setCopyFolderFlag(false);
                log.info("zipChild {} -> {}", url, childTempPath);
                uploadFileService.uploadFile(ufr);
                childPathList.add(childTempPath);
            }
        }
        return childPathList;
    }

}
