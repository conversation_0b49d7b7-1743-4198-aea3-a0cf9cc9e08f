package com.wunding.learn.file.service.impl;

import com.wunding.learn.file.api.constant.FileBizType;
import com.wunding.learn.file.api.constant.ImageBizType;
import com.wunding.learn.file.config.SysConfig;
import com.wunding.learn.file.model.CloudFile;
import com.wunding.learn.file.model.CloudFileChunk;
import com.wunding.learn.file.model.UploadFileRequest;
import com.wunding.learn.file.model.UploadFileResponse;
import com.wunding.learn.file.service.StorageStrategyService;
import com.wunding.learn.file.service.UploadFileService;
import jakarta.annotation.Resource;
import java.io.File;
import java.util.Collection;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

/**
 * 文件上传
 *
 * <AUTHOR>
 * @since 2021/2/4 15:39
 */
@Service("uploadFileService")
public class UploadFileServiceImpl implements UploadFileService {

    @Resource
    @Lazy
    private StorageStrategyService storageStrategyService;

    @Resource
    private SysConfig sysConfig;

    @Override
    public UploadFileResponse uploadFile(UploadFileRequest uploadFileRequest) {
        return storageStrategyService.uploadFile(uploadFileRequest);
    }

    @Override
    public UploadFileResponse uploadFileAndUnzip(UploadFileRequest uploadFileRequest) {
        return null;
    }

    @Override
    public UploadFileResponse copyFile(String source, String target) {
        return storageStrategyService.copyFile(source, target, false);
    }

    @Override
    public UploadFileResponse copyFile(String source, FileBizType fileBizType) {
        return storageStrategyService.copyFile(source, fileBizType);
    }

    @Override
    public UploadFileResponse copyFile(String source, ImageBizType imageBizType) {
        return storageStrategyService.copyFile(source, imageBizType);
    }

    @Override
    public UploadFileResponse copyDir(String source, String target) {
        return storageStrategyService.copyDir(source, target);
    }

    @Override
    public UploadFileResponse uploadDir(String source, String target, String bizId) {
        return storageStrategyService.uploadDir(source, target, bizId);
    }

    @Override
    public UploadFileResponse findFile(UploadFileRequest uploadFileRequest) {
        return storageStrategyService.findFile(uploadFileRequest);
    }

    @Override
    public void deleteFile(String filePath) {
        storageStrategyService.deleteFile(filePath);
    }

    @Override
    public Integer getStorageType() {
        return sysConfig.getStorageType();
    }

    @Override
    public File downloadFile(String uri) {
        return storageStrategyService.downloadFile(uri);
    }

    @Override
    public String compressorFiles(Collection<String> filePaths, String zipFileName, boolean isImage) {
        return storageStrategyService.compressorFiles(filePaths, zipFileName, isImage);
    }

    @Override
    public Boolean checkFileIsExists(String path) {
        return storageStrategyService.checkFileIsExists(path);
    }

    @Override
    public void composeFiles(Collection<CloudFileChunk> cloudFileChunkList, CloudFile cloudFile) {
        storageStrategyService.composeFiles(cloudFileChunkList, cloudFile);
    }

    @Override
    public String getUrl(String path, boolean isDownload, String fileName) {
        return storageStrategyService.getUrl(path, isDownload, fileName);
    }

    @Override
    public void deleteTempFile(Integer deleteTime) {
        storageStrategyService.deleteTempFile(deleteTime);
    }

    @Override
    public String uploadPartFile(Integer index, File file, CloudFile cloudFile) {
        return storageStrategyService.storageStrategyService(index, file, cloudFile);
    }
}
