package com.wunding.learn.file.util;

import com.wunding.learn.common.util.other.ConfigUtil;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Map.Entry;
import javax.xml.XMLConstants;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.stream.StreamSource;
import org.apache.commons.io.IOUtils;
import org.apache.commons.io.input.BOMInputStream;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.Namespace;
import org.dom4j.io.DocumentResult;
import org.dom4j.io.DocumentSource;
import org.dom4j.io.OutputFormat;
import org.dom4j.io.SAXReader;
import org.dom4j.io.XMLWriter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.xml.sax.SAXException;

/**
 * <AUTHOR>
 * @date 2020/11/05
 */
public class XmlUtil {

    private XmlUtil() {
        throw new IllegalStateException("Utility class");
    }

    private static final Logger log = LoggerFactory.getLogger(XmlUtil.class);

    /**
     * 获取xml根节点
     *
     * @param xmlFile
     * @return
     */
    public static Element getRootElement(File xmlFile) {
        SAXReader reader = new SAXReader();
        reader.setEncoding(StandardCharsets.UTF_8.name());
        try {
            // 漏洞处理，禁止读取的xml使用xxe和doctype
            fillReaderFeature(reader);
            Document doc = reader.read(new FileInputStream(xmlFile));
            return doc.getRootElement();
        } catch (Exception e) {
            log.error("", e);
        }
        return null;
    }

    public static String getAttribute(Element elmt, String elmtName, String elmtAttr) {
        if (elmt == null || StringUtils.isBlank(elmtName) || StringUtils.isBlank(elmtAttr)) {
            return null;
        } else {
            Element element = elmt.element(elmtName);
            if (element == null) {
                return "";
            } else {
                return element.attributeValue(elmtAttr);
            }
        }
    }

    /**
     * 创建xml文件
     *
     * @param path xml文件的路径 doc document对象
     * @return
     */
    public static void createXml(String path, Document doc) {
        try {
            OutputFormat format = OutputFormat.createPrettyPrint();
            // 设置XML文件的编码格式
            format.setEncoding(StandardCharsets.UTF_8.name());
            FileWriter fw = new FileWriter(new File(path));
            XMLWriter writer = new XMLWriter(fw, format);
            writer.write(doc);
            writer.flush();
            writer.close();
        } catch (Exception e) {
            log.error("", e);
            log.info(e.getMessage());
        }
    }

    /**
     * 创建xml文件
     *
     * @param path xml文件的路径 xmlStr xml文件的字符串
     * @return
     */
    public static void createXml(String path, String xmlStr) {
        try {
            Document doc = DocumentHelper.parseText(xmlStr);

            OutputFormat format = OutputFormat.createPrettyPrint();
            // 设置XML文件的编码格式
            format.setEncoding(StandardCharsets.UTF_8.name());

            FileWriter fw = new FileWriter(new File(path));
            XMLWriter writer = new XMLWriter(fw, format);
            writer.write(doc);
            writer.flush();
            writer.close();
        } catch (Exception e) {
            log.error("", e);
            log.error(e.getMessage());
        }
    }

    public static boolean exitsXmlNs(String path) {
        try (FileInputStream fileInputStream = new FileInputStream(new File(path));
            BOMInputStream bomInputStream = new BOMInputStream(fileInputStream);
            InputStreamReader inputStreamReader =
                new InputStreamReader(bomInputStream, StandardCharsets.UTF_8.name());
            BufferedReader bufferedReader = new BufferedReader(inputStreamReader)) {
            SAXReader reader = new SAXReader();
            // 漏洞处理，禁止读取的xml使用xxe和doctype
            fillReaderFeature(reader);
            // 使用BOMInputStream自动去除UTF-8中的BOM

            Document doc = reader.read(bufferedReader);
            Element elmt = doc.getRootElement();
            Namespace ns = elmt.getNamespaceForPrefix("imsss");
            return ns != null;
        } catch (Exception e) {
            log.error("", e);
            return false;
        }
    }

    /**
     * 根据模版文件把imsmanifest.Xml生成新的xml文件
     *
     * @param imsmanifestXmlPath imsmanifest.Xml的路径
     * @param targetXmlPath      生成的新的xml文件的路径
     * @param rootParams         自定义参数
     */
    public static File xmlTransform(String imsmanifestXmlPath, String targetXmlPath, Map<String, String> rootParams) {

        FileWriter fw = null;
        XMLWriter writer = null;

        try (InputStream in = ConfigUtil.class.getResourceAsStream("/scorm/op-scorm13.xsl");
            FileInputStream fileInputStream = new FileInputStream(new File(imsmanifestXmlPath));
            BOMInputStream bomInputStream = new BOMInputStream(fileInputStream);
            InputStreamReader inputStreamReader =
                new InputStreamReader(bomInputStream, StandardCharsets.UTF_8.name());
            BufferedReader bufferedReader = new BufferedReader(inputStreamReader)) {
            TransformerFactory factory = TransformerFactory.newInstance();

            log.error( "factory.getClass() -- >> " + factory.getClass().getCanonicalName());

            //             sonar:XML parsers should not be vulnerable to XXE attacks
            factory.setAttribute(XMLConstants.ACCESS_EXTERNAL_DTD, "");
            factory.setAttribute(XMLConstants.ACCESS_EXTERNAL_STYLESHEET, "");

            Transformer transformer = factory.newTransformer(new StreamSource(in));
            SAXReader reader = new SAXReader();
            // 漏洞处理，禁止读取的xml使用xxe和doctype
            fillReaderFeature(reader);
            // 使用BOMInputStream自动去除UTF-8中的BOM
            Document imsmanifestXmlDoc = reader.read(bufferedReader);
            DocumentSource source = new DocumentSource(imsmanifestXmlDoc);
            DocumentResult result = new DocumentResult();
            transformer.transform(source, result);
            Document transformedDoc = result.getDocument();
            Element rootDocument = transformedDoc.getRootElement();
            for (Entry<String, String> entry : rootParams.entrySet()) {
                rootDocument.addAttribute(entry.getKey(), entry.getValue());
            }
            transformedDoc.setXMLEncoding(StandardCharsets.UTF_8.name());
            OutputFormat format = OutputFormat.createPrettyPrint();
            // 设置XML文件的编码格式
            format.setEncoding(StandardCharsets.UTF_8.name());
            File file = new File(targetXmlPath);
            fw = new FileWriter(file);
            writer = new XMLWriter(fw, format);
            writer.write(transformedDoc);
            writer.flush();
            return file;
        } catch (Exception e) {
            log.error("", e);
            return null;
        } finally {
            try {
                IOUtils.close(fw);
                if (writer != null) {
                    writer.close();
                }
            } catch (IOException e) {
                log.error("close_io_error", e);
            }
        }
    }

    /**
     * 填充读取器功能
     *
     * @param reader 读取对象
     * @throws SAXException SaxException
     */
    private static void fillReaderFeature(SAXReader reader) throws SAXException {
        // 漏洞处理，禁止读取的xml使用xxe和doctype
        reader.setFeature("http://apache.org/xml/features/nonvalidating/load-external-dtd", false);
        reader.setFeature("http://xml.org/sax/features/external-general-entities", false);
        reader.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
    }
}
