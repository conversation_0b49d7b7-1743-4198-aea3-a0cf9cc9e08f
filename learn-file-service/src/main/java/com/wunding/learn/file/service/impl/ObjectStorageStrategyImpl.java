package com.wunding.learn.file.service.impl;

import com.alibaba.ttl.threadpool.TtlExecutors;
import com.wunding.learn.common.constant.file.FileErrorNoEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.other.SystemConfigCodeEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.redis.util.RedisLockUtil;
import com.wunding.learn.common.util.string.StringPool;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.file.api.constant.ExportStatusEnum;
import com.wunding.learn.file.api.constant.FileBizType;
import com.wunding.learn.file.api.constant.ImageBizType;
import com.wunding.learn.file.config.SysConfig;
import com.wunding.learn.file.feign.ExcelRecordFeignImpl;
import com.wunding.learn.file.model.CloudFile;
import com.wunding.learn.file.model.CloudFileChunk;
import com.wunding.learn.file.model.StsRequest;
import com.wunding.learn.file.model.StsResponse;
import com.wunding.learn.file.model.UploadFileRequest;
import com.wunding.learn.file.model.UploadFileResponse;
import com.wunding.learn.file.service.IImagesService;
import com.wunding.learn.file.service.StorageStrategyService;
import com.wunding.learn.file.util.FileUtil;
import com.wunding.learn.file.util.UrlHelper;
import com.wunding.learn.file.util.ZipUtil;
import com.wunding.learn.user.api.service.ParaFeign;
import jakarta.annotation.Nullable;
import jakarta.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.util.CollectionUtils;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.core.sync.ResponseTransformer;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.CompleteMultipartUploadRequest;
import software.amazon.awssdk.services.s3.model.CompletedMultipartUpload;
import software.amazon.awssdk.services.s3.model.CompletedPart;
import software.amazon.awssdk.services.s3.model.CopyObjectRequest;
import software.amazon.awssdk.services.s3.model.CopyObjectRequest.Builder;
import software.amazon.awssdk.services.s3.model.Delete;
import software.amazon.awssdk.services.s3.model.DeleteObjectsRequest;
import software.amazon.awssdk.services.s3.model.DeleteObjectsResponse;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.HeadObjectRequest;
import software.amazon.awssdk.services.s3.model.HeadObjectResponse;
import software.amazon.awssdk.services.s3.model.ListObjectsV2Request;
import software.amazon.awssdk.services.s3.model.ListObjectsV2Response;
import software.amazon.awssdk.services.s3.model.NoSuchKeyException;
import software.amazon.awssdk.services.s3.model.ObjectCannedACL;
import software.amazon.awssdk.services.s3.model.ObjectIdentifier;
import software.amazon.awssdk.services.s3.model.PutObjectAclRequest;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.model.S3Exception;
import software.amazon.awssdk.services.s3.model.S3Object;
import software.amazon.awssdk.services.s3.model.UploadPartRequest;
import software.amazon.awssdk.services.s3.model.UploadPartResponse;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;
import software.amazon.awssdk.services.sts.StsClient;
import software.amazon.awssdk.services.sts.model.AssumeRoleRequest;
import software.amazon.awssdk.services.sts.model.AssumeRoleResponse;

/**
 * 对象存储工具类
 *
 * <AUTHOR>
 * @since 2021/2/4 16:32
 */
@Slf4j
public class ObjectStorageStrategyImpl implements StorageStrategyService {

    private static final String SEPARATOR = "/";
    private static final String FILE_NAME = "; filename=";
    private static final ExecutorService uploadOssThreadPool;
    private static final ExecutorService dealWithOldFileThreadPool;

    static {
        uploadOssThreadPool = TtlExecutors.getTtlExecutorService(
            new ThreadPoolExecutor(2, 2, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(),
                new CustomizableThreadFactory("upload-oss-pool-")));
        dealWithOldFileThreadPool = TtlExecutors
            .getTtlExecutorService(new ThreadPoolExecutor(0, 20, 3L, TimeUnit.MINUTES,
                new LinkedBlockingQueue<>(),
                new CustomizableThreadFactory("dealWithOldFile-pool-")));
    }

    @Resource
    private SysConfig sysConfig;
    @Resource
    private IImagesService imagesService;
    @Resource
    private UrlHelper urlHelper;
    @Resource
    @Lazy
    private ExcelRecordFeignImpl excelRecordFeignImpl;

    @Resource
    private ParaFeign paraFeign;

    public ObjectStorageStrategyImpl() {
        log.info("----ObjectStorageStrategyImpl----初始化");
    }

    /**
     * 复制整个文件夹内容
     *
     * @param srcPath    String 原文件路径 如：c:/fqf
     * @param targetPath String 复制后路径 如：f:/fqf/ff
     * @return boolean
     */
    public String copyFolder(String srcPath, String targetPath, boolean async, List<String> defaultStrategyFileList) {
        String indexPath;
        if (async) {
            uploadOssThreadPool.submit(() -> uploadDir1(srcPath, targetPath, defaultStrategyFileList));
        } else {
            uploadDir1(srcPath, targetPath, defaultStrategyFileList);
        }
        indexPath = targetPath.replace(sysConfig.getLocation(), "");
        indexPath = addBucketFix(indexPath);
        if (!indexPath.endsWith(SEPARATOR)) {
            indexPath = indexPath + SEPARATOR;
        }
        indexPath = indexPath + "index.html";
        return indexPath;
    }

    @Override
    public UploadFileResponse uploadDir(String source, String target, String bizId) {
        UploadFileResponse uploadFileResponse = new UploadFileResponse();
        String path = copyFolder(source, target, false, List.of());
        uploadFileResponse.setFilePath(path);
        uploadFileResponse.setFileUrl(path);
        uploadFileResponse.setFileSize(0L);
        return uploadFileResponse;
    }

    private void uploadDir1(String srcPath, String targetPath, List<String> defaultStrategyFileList) {
        try {
            // 如果文件夹不存在 则建立新文件夹
            File a = new File(srcPath);
            if (a.list() == null) {
                return;
            }
            String[] file = a.list();
            File temp;
            long startTime = System.currentTimeMillis();
            for (int i = 0; i < Objects.requireNonNull(file).length; i++) {
                if (srcPath.endsWith(File.separator)) {
                    temp = new File(srcPath + file[i]);
                } else {
                    temp = new File(srcPath + File.separator + file[i]);
                }
                handleTempIsDir(srcPath, targetPath, file, temp, i, defaultStrategyFileList);
                log.info("耗时：" + (System.currentTimeMillis() - startTime));
            }
        } catch (Exception e) {
            log.error("复制目录出错误", e);
        }
    }


    private void handleTempIsDir(String srcPath, String targetPath, String[] file, File temp, int i,
        List<String> defaultStrategyFileList) throws IOException {
        if (temp.isFile()) {
            // 获取文件类型
            String contentType = Files.probeContentType(temp.toPath());
            if (temp.getAbsolutePath().endsWith("html")) {
                contentType = "text/html";
            }
            String objectName = dealWithTargePath(targetPath, temp);
            log.info("解析的contentType: {},objectName: {}", contentType, objectName);
            PutObjectRequest.Builder requestBuilder = PutObjectRequest.builder()
                .bucket(sysConfig.getBucketName())
                .key(objectName)
                .contentLength(temp.length());

            if (StringUtils.isNotBlank(contentType)) {
                requestBuilder.contentType(contentType);
            }
            // 必须设置成公有读的文件
            if (!CollectionUtils.isEmpty(defaultStrategyFileList) && !defaultStrategyFileList.contains(
                temp.getAbsolutePath())) {
                requestBuilder.acl(ObjectCannedACL.PUBLIC_READ);
            }
            PutObjectRequest putRequest = requestBuilder.build();
            SysConfig.getS3Client().putObject(putRequest, RequestBody.fromFile(temp));
        }
        if (temp.isDirectory()) {
            // 如果是子文件夹
            String filePath = file[i];
            uploadOssThreadPool.submit(() ->
                uploadDir1(srcPath + File.separator + filePath, targetPath + File.separator + filePath,
                    defaultStrategyFileList)
            );
        }
    }

    @NotNull
    private String dealWithTargePath(String targetPath, File temp) {
        String objectName =
            FilenameUtils.separatorsToUnix(targetPath);
        if (objectName.endsWith(File.separator)) {
            objectName = objectName + temp.getName();
        } else {
            objectName = objectName + File.separator + temp.getName();
        }
        if (objectName.startsWith(SEPARATOR)) {
            objectName = objectName.substring(1);
        }
        if (objectName.startsWith(sysConfig.getBucketName())) {
            objectName = objectName.substring(sysConfig.getBucketName().length() + 1);
        }
        return objectName;
    }

    @Override
    public UploadFileResponse uploadFile(UploadFileRequest uploadFileRequest) {
        UploadFileResponse uploadFileResponse = new UploadFileResponse();
        String backUrl = "";
        String folderUrl = "";

        boolean uploadSuccess = false;
        if (StringUtils.isNotBlank(uploadFileRequest.getTempFilePath())) {

            File sourceFile = new File(uploadFileRequest.getTempFilePath());
            uploadFileResponse.setFileSize(sourceFile.length());
            log.info("临时文件：{}, 文件大小:{}", uploadFileRequest.getTempFilePath(), sourceFile.length());

            // 获取对象存储路径
            String objectName = getObjectName(FilenameUtils.separatorsToUnix(uploadFileRequest.getFileName()),
                SEPARATOR, 1, sysConfig.getBucketName() + SEPARATOR, sysConfig.getBucketName().length() + 1);

            // 根据文件大小选择不同的上传策略
            chooseDiffUploadStrategy(uploadFileRequest, sourceFile, objectName);
            // 获取上传后文件路径
            backUrl = getBackUrl(uploadFileRequest.getFileName());
            uploadSuccess = true;
        } else {
            log.error("uploadFileRequest getTempFilePath is null");
        }
        if (uploadFileRequest.isCopyFolderFlag()) {
            String targetPath = uploadFileRequest.getFolderTargetPath();
            if (targetPath.startsWith(sysConfig.getLocation())) {
                targetPath = targetPath.replace(targetPath, StringUtils.EMPTY);
            }
            folderUrl = copyFolder(uploadFileRequest.getFolderSrcPath(), targetPath, uploadFileRequest.isAsyncUpload(),
                uploadFileRequest.getDefaultStrategyFileList());
            uploadSuccess = true;
        } else {
            log.info("uploadFileRequest isCopyFolderFlag is false");
        }

        // 打印日志
        printLog(uploadFileRequest.getFileName(), uploadSuccess);
        // 填充文件url
        uploadFileResponse.setFileUrl(uploadFileRequest.isCopyFolderFlag() ? folderUrl : backUrl);
        return uploadFileResponse;
    }


    @Override
    public UploadFileResponse uploadZipFileAndUnzip(UploadFileRequest uploadFileRequest) {
        return null;
    }

    @NotNull
    private String getObjectName(String objectName, String separator, int separatorSubIndex, String separator2,
        int separator2SubIndex) {
        if (objectName.startsWith(separator)) {
            objectName = objectName.substring(separatorSubIndex);
        }
        if (objectName.startsWith(separator2)) {
            objectName = objectName.substring(separator2SubIndex);
        }
        return objectName;
    }

    /**
     * 获取BackUrl
     *
     * @param fileName 文件名称
     * @return {@link String }
     */
    private String getBackUrl(String fileName) {
        String backUrl = fileName;
        if (!backUrl.startsWith(SEPARATOR)) {
            backUrl = SEPARATOR + backUrl;
        }
        if (!backUrl.startsWith(SEPARATOR + sysConfig.getRoot())) {
            backUrl = SEPARATOR + sysConfig.getRoot() + backUrl;
        }
        return backUrl;
    }

    /**
     * 打印日志
     *
     * @param fileName      文件名
     * @param uploadSuccess 上传成功
     */
    private void printLog(String fileName, boolean uploadSuccess) {
        if (uploadSuccess) {
            log.info(fileName + " is uploaded successfully");
        } else {
            log.error(fileName + " is uploaded failed");
        }
    }

    /**
     * 选择不同的上传策略
     *
     * @param uploadFileRequest 上传文件请求
     * @param sourceFile        源文件
     * @param objectName        对象名称
     */
    private void chooseDiffUploadStrategy(UploadFileRequest uploadFileRequest, File sourceFile, String objectName) {
        final String uploadObjectName = objectName;
        if (sourceFile.length() > 1024 * 1024 * 15 && uploadFileRequest.isAsyncUpload()) {
            log.info("大于15M 且是异步上传");
            uploadOssThreadPool.submit(() -> uploadOss(uploadFileRequest.getTempFilePath(), uploadObjectName,
                uploadFileRequest.getHeaderMap(), uploadFileRequest.isPublicRead()));
        } else if (sourceFile.length() > 1024 * 1024 * 1024) {
            log.info("大于1G异步上传");
            uploadOssThreadPool.submit(() -> uploadOss(uploadFileRequest.getTempFilePath(), uploadObjectName,
                uploadFileRequest.getHeaderMap(), uploadFileRequest.isPublicRead()));
        } else {
            log.info("同步上传");
            uploadOss(uploadFileRequest.getTempFilePath(), objectName, uploadFileRequest.getHeaderMap(),
                uploadFileRequest.isPublicRead());
        }
    }

    /**
     * 添加bucket前缀
     *
     * @param path
     * @return
     */
    private String addBucketFix(String path) {
        if (!path.startsWith(SEPARATOR)) {
            path = SEPARATOR + path;
        }
        if (!path.startsWith(SEPARATOR + sysConfig.getBucketName())) {
            path = SEPARATOR + sysConfig.getBucketName() + path;
        }
        return path;
    }

    /**
     * 移除bucket前缀
     *
     * @param path
     * @return
     */
    private String removeBucketFix(String path) {
        if (path.startsWith(SEPARATOR)) {
            path = path.substring(1);
        }
        if (path.startsWith(sysConfig.getBucketName())) {
            path = path.substring(sysConfig.getBucketName().length() + 1);
        }
        return path;
    }

    @Override
    public UploadFileResponse copyFile(String source, String target, boolean isPublicRead) {
        UploadFileResponse uploadFileResponse = new UploadFileResponse();
        if (StringUtils.isBlank(source) || StringUtils.isBlank(target)) {
            throw new BusinessException(FileErrorNoEnum.COPY_FILE_FAIL);
        }
        String sourcePath = removeBucketFix(source);
        String targetPath = removeBucketFix(target);
        String bucketName = sysConfig.getBucketName();
        try {
            S3Client s3Client = SysConfig.getS3Client();
            // 构建复制请求
            Builder builder = CopyObjectRequest.builder()
                .copySource(bucketName + "/" + sourcePath)  // v2 复制源是 bucket/key 形式字符串
                .bucket(bucketName)
                .key(targetPath);
            if (isPublicRead) {
                builder.acl(ObjectCannedACL.PUBLIC_READ);
            }
            CopyObjectRequest copyRequest = builder.build();
            s3Client.copyObject(copyRequest);
            // 获取复制后对象的元数据（HeadObject）
            HeadObjectRequest headRequest = HeadObjectRequest.builder()
                .bucket(bucketName)
                .key(targetPath)
                .build();
            HeadObjectResponse headResponse = s3Client.headObject(headRequest);
            uploadFileResponse.setFilePath(addBucketFix(targetPath));
            uploadFileResponse.setFileUrl(addBucketFix(targetPath));
            uploadFileResponse.setFileSize(headResponse.contentLength());
        } catch (Exception e) {
            log.error("发生异常", e);
            throw new BusinessException(FileErrorNoEnum.COPY_FILE_FAIL);
        }
        return uploadFileResponse;
    }

    @Override
    public UploadFileResponse copyFile(String source, FileBizType fileBizType) {
        String target = "";
        return copyFile(source, target, false);
    }

    @Override
    public UploadFileResponse copyFile(String source, ImageBizType imageBizType) {
        String target = "";
        return copyFile(source, target, false);
    }

    @Override
    public UploadFileResponse copyDir(String source, String target) {
        UploadFileResponse uploadFileResponse = new UploadFileResponse();
        AtomicInteger errorCount = new AtomicInteger();
        String sourcePath = removeBucketFix(source);
        if (!sourcePath.endsWith(SEPARATOR)) {
            sourcePath = sourcePath + SEPARATOR;
        }
        String targetPath = removeBucketFix(target);
        if (!targetPath.endsWith(SEPARATOR)) {
            targetPath = targetPath + SEPARATOR;
        }
        String bucketName = sysConfig.getBucketName();
        S3Client s3Client = SysConfig.getS3Client();
        String continuationToken = null;
        do {
            ListObjectsV2Request.Builder listRequestBuilder = ListObjectsV2Request.builder()
                .bucket(bucketName)
                .prefix(sourcePath);
            if (continuationToken != null) {
                listRequestBuilder.continuationToken(continuationToken);
            }
            ListObjectsV2Response result = s3Client.listObjectsV2(listRequestBuilder.build());
            List<S3Object> objectSummaries = result.contents();

            for (S3Object objectSummary : objectSummaries) {
                String sourceKey = objectSummary.key();
                if (sourceKey.endsWith("/")) {
                    // 跳过空目录
                    continue;
                }
                try {
                    String targetKey = sourceKey.replaceFirst(Pattern.quote(sourcePath), targetPath);
                    CopyObjectRequest copyRequest = CopyObjectRequest.builder()
                        .copySource(bucketName + "/" + sourceKey) // v2 需要拼接 copySource
                        .destinationBucket(bucketName)
                        .destinationKey(targetKey)
                        .build();

                    s3Client.copyObject(copyRequest);
                } catch (Exception e) {
                    log.error("复制失败，源对象: {}, 错误: {}", objectSummary.key(), e.getMessage(), e);
                    errorCount.incrementAndGet();
                }
            }

            continuationToken = result.nextContinuationToken();
        } while (continuationToken != null);

        uploadFileResponse.setFilePath(target);
        return uploadFileResponse;
    }


    private boolean checkOssExists(String objectName, File tempFile) {
        if (StringUtils.isBlank(objectName) || tempFile == null || !tempFile.exists()) {
            return false;
        }
        try {
            S3Client s3Client = SysConfig.getS3Client();
            String bucketName = sysConfig.getBucketName(); // 获取你的 bucket 名称

            HeadObjectRequest headRequest = HeadObjectRequest.builder()
                .bucket(bucketName)
                .key(objectName)
                .build();

            HeadObjectResponse metadata = s3Client.headObject(headRequest);
            log.info("Storage Object Metadata: {}", metadata);

            if (metadata != null && metadata.contentLength() == tempFile.length()
                && metadata.lastModified().toEpochMilli() == tempFile.lastModified()) {
                log.info("对象存储 中已存在文件：{}", objectName);
                return true;
            }
        } catch (Exception e) {
            log.info("获取 对象存储 文件元数据异常: {}", e.toString());
        }
        return false;
    }

    @Override
    public boolean checkOssIsExists(String source) {
        if (StringUtils.isBlank(source)) {
            return false;
        }
        String sourcePath = removeBucketFix(source);
        S3Client s3Client = SysConfig.getS3Client();
        String bucketName = sysConfig.getBucketName();

        try {
            HeadObjectRequest headRequest = HeadObjectRequest.builder()
                .bucket(bucketName)
                .key(sourcePath)
                .build();
            HeadObjectResponse metadata = s3Client.headObject(headRequest);
            // 获取本地文件
            String filePath = sysConfig.getPhysicalPath(bucketName + "/" + sourcePath);
            File file = new File(filePath);

            return metadata.contentLength() <= 0
                || metadata.contentLength() != file.length()
                || metadata.lastModified().toEpochMilli() != file.lastModified();
        } catch (Exception e) {
            log.info("S3 获取对象元信息异常: {}", e.toString());
        }
        return false;
    }

    private boolean copyOssTempExists(String filePath, String objectName, File tempFile) {
        // 获取对象存储路径
        String tempObjectName = getObjectName(filePath, sysConfig.getLocation(), sysConfig.getLocation().length(),
            SEPARATOR,
            1);
        if (tempObjectName.startsWith(sysConfig.getBucketName())) {
            tempObjectName = tempObjectName.substring(sysConfig.getBucketName().length() + 1);
        }
        if (checkOssExists(tempObjectName, tempFile)) {
            copyFile(tempObjectName, objectName, false);
            return true;
        }
        return false;
    }

    private void uploadOss(String filePath, String objectName) {
        uploadOss(filePath, objectName, null, false);
    }

    private void uploadOss(String filePath, String objectName, @Nullable Map<String, String> headerMap,
        boolean publicRead) {
        try {
            File tmpFile = new File(filePath);
            String contentType = Files.probeContentType(tmpFile.toPath());
            if (filePath.endsWith("html")) {
                contentType = "text/html";
            }
            log.info(
                "解析到的contentType: " + contentType + ",file size:" + tmpFile.length() + ",file path:" + filePath);

            if (checkOssExists(objectName, tmpFile)) {
                return;
            }
            if (copyOssTempExists(filePath, objectName, tmpFile)) {
                return;
            }

            long startTime = System.currentTimeMillis();
            String bucketName = sysConfig.getBucketName();
            PutObjectRequest.Builder requestBuilder = PutObjectRequest.builder()
                .bucket(bucketName)
                .key(objectName)
                .contentLength(tmpFile.length());
            if (StringUtils.isNotBlank(contentType)) {
                requestBuilder.contentType(contentType);
            }
            // 添加用户自定义 metadata
            if (headerMap != null && !headerMap.isEmpty()) {
                // AWS v2 使用 metadata(Map<String, String>) 来设置自定义 header
                requestBuilder.metadata(headerMap);
            }
            // 是否公有读
            if (publicRead) {
                requestBuilder.acl(ObjectCannedACL.PUBLIC_READ);
            }
            PutObjectRequest putRequest = requestBuilder.build();
            // 直接通过 File
            SysConfig.getS3Client().putObject(putRequest, RequestBody.fromFile(tmpFile));
            log.info("putObject time: {}ms", (System.currentTimeMillis() - startTime));
        } catch (S3Exception e) {
            log.error("上传到对象存储失败（S3 端错误）", e);
            throw new BusinessException(FileErrorNoEnum.ERR_BUCKET_QUOTA);
        } catch (IOException e) {
            log.error("上传到对象存储失败（IO 异常）", e);
        } catch (Exception e) {
            log.error("上传到对象存储失败（其他异常）", e);
        }
    }

    @Override
    public UploadFileResponse findFile(UploadFileRequest uploadFileRequest) {
        UploadFileResponse uploadFileResponse = new UploadFileResponse();
        String fileName = uploadFileRequest.getFileName();
        String filePath = null;

        try {
            String bucketName = sysConfig.getBucketName();
            String objectName = fileName.substring(
                fileName.indexOf(bucketName) + bucketName.length() + 1
            );

            S3Client s3Client = SysConfig.getS3Client();

            // 构造本地临时文件路径
            String tempDir = sysConfig.queryTempPhysicalPath();
            String fileBaseName = objectName.substring(objectName.lastIndexOf(SEPARATOR) + 1);
            Path targetPath = Paths.get(tempDir, fileBaseName);

            // 保证临时路径存在
            FileUtil.mkdir(tempDir);
            Files.createDirectories(targetPath.getParent());

            // 获取 S3 对象输入流
            GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                .bucket(bucketName)
                .key(objectName)
                .build();

            s3Client.getObject(getObjectRequest, ResponseTransformer.toFile(targetPath));
            filePath = targetPath.toString();

        } catch (S3Exception e) {
            log.error("S3 客户端异常：", e);
        } catch (Exception e) {
            log.error("下载失败：", e);
        }
        uploadFileResponse.setFilePath(filePath);
        return uploadFileResponse;
    }

    @Override
    public void deleteFile(String filePath) {
        filePath = filePath.replace("\\", "/");
        if (filePath.startsWith("/")) {
            filePath = filePath.substring(1);
        }

        String bucketName = sysConfig.getBucketName();
        if (filePath.startsWith(bucketName + "/")) {
            filePath = filePath.substring(bucketName.length() + 1);
        }

        S3Client s3Client = SysConfig.getS3Client();

        try {
            String continuationToken = null;
            do {
                ListObjectsV2Request.Builder listBuilder = ListObjectsV2Request.builder()
                    .bucket(bucketName)
                    .prefix(filePath);

                if (continuationToken != null) {
                    listBuilder.continuationToken(continuationToken);
                }

                ListObjectsV2Response listResponse = s3Client.listObjectsV2(listBuilder.build());
                List<S3Object> objects = listResponse.contents();

                if (objects.isEmpty()) {
                    break;
                }

                // 批量删除构造keys
                List<ObjectIdentifier> keysToDelete = objects.stream()
                    .map(obj -> ObjectIdentifier.builder().key(obj.key()).build())
                    .collect(Collectors.toList());
                DeleteObjectsRequest deleteRequest = DeleteObjectsRequest.builder()
                    .bucket(bucketName)
                    .delete(Delete.builder().objects(keysToDelete).build())
                    .build();

                try {
                    DeleteObjectsResponse deleteResponse = s3Client.deleteObjects(deleteRequest);
                    log.info("成功删除 {} 个对象", deleteResponse.deleted().size());
                } catch (Exception e) {
                    log.error("批量删除对象失败", e);
                }

                continuationToken = listResponse.nextContinuationToken();
            } while (continuationToken != null);

        } catch (S3Exception e) {
            log.error("删除对象时发生 S3 异常", e);
        } catch (Exception e) {
            log.error("删除对象时发生异常", e);
        }
    }


    @Override
    public StsResponse getSts(StsRequest stsRequest) {
        AwsCredentialsProvider credentialsProvider = StaticCredentialsProvider.create(
            AwsBasicCredentials.create(sysConfig.getAccessKey(), sysConfig.getSecretKey()));
        URI endPointUri;
        try {
            endPointUri = new URI(sysConfig.getEndPoint());
        } catch (URISyntaxException e) {
            log.error("请检查minio接入节点配置是否正确", e);
            return null;
        }
        StsClient stsClient = StsClient.builder().region(Region.US_EAST_1).endpointOverride(endPointUri)
            .credentialsProvider(credentialsProvider).build();

        AssumeRoleRequest.Builder builder = AssumeRoleRequest.builder();
        if (stsRequest.getRoleArn() != null) {
            builder.roleArn(stsRequest.getRoleArn());
        }
        if (stsRequest.getRoleSessionName() != null) {
            builder.roleSessionName(stsRequest.getRoleSessionName());
        }
        if (stsRequest.getPolicy() != null) {
            builder.policy(stsRequest.getPolicy());
        }
        if (stsRequest.getExpiration() == null) {
            builder.durationSeconds(900);
        } else {
            builder.durationSeconds(stsRequest.getExpiration());
        }

        AssumeRoleRequest request = builder.build();
        AssumeRoleResponse assumeRoleResponse = stsClient.assumeRole(request);

        StsResponse stsResponse = new StsResponse();
        stsResponse.setAccessKeyId(assumeRoleResponse.credentials().accessKeyId());
        stsResponse.setSecretAccessKey(assumeRoleResponse.credentials().secretAccessKey());
        stsResponse.setSessionToken(assumeRoleResponse.credentials().sessionToken());

        stsResponse.setExpiration(assumeRoleResponse.credentials().expiration().atZone(ZoneId.systemDefault())
            .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        return stsResponse;
    }

    @Override
    public File downloadFile(String uri) {
        String lockKey = "downloadFile:lockKey:" + uri;
        log.info("downloadFile uri: {}", uri);
        try {
            RedisLockUtil.acquire(lockKey, 10, 30);

            File file = new File(sysConfig.getPhysicalPath(uri));
            if (file.exists()) {
                return file;
            }

            String objectName = uri;
            if (objectName.startsWith(SEPARATOR)) {
                objectName = objectName.substring(1);
            }
            if (objectName.startsWith(sysConfig.getBucketName())) {
                objectName = objectName.substring(sysConfig.getBucketName().length() + 1);
            }

            log.info("downloadFile objectName: {}  bucketName: {}", objectName, sysConfig.getBucketName());

            // 创建目录（防止路径不存在）
            Path targetPath = file.toPath();
            Files.createDirectories(targetPath.getParent());

            S3Client s3Client = SysConfig.getS3Client();
            GetObjectRequest getRequest = GetObjectRequest.builder()
                .bucket(sysConfig.getBucketName())
                .key(objectName)
                .build();
            s3Client.getObject(getRequest, ResponseTransformer.toFile(targetPath));
            return file;
        } catch (Exception e) {
            log.error("发生异常", e);
        } finally {
            RedisLockUtil.release(lockKey);
        }
        return null;
    }

    @Override
    public String compressorFiles(Collection<String> filePaths, String zipFileName, boolean isImage) {
        // 建立临时目录
        String tempDir = sysConfig.queryTempPath() + StringUtil.newId() + SEPARATOR;
        log.info("tempDir path : {}", tempDir);
        FileUtil.mkdir(tempDir);
        String targetDir = sysConfig.queryTempPath() + StringUtil.newId() + SEPARATOR;
        log.info("targetDir path : {}", targetDir);
        FileUtil.mkdir(targetDir);
        // 把文件下载到目录
        for (String filePath : filePaths) {
            String fileName = FilenameUtils.getName(filePath);
            try {
                File file = downloadFile(filePath);
                // 第三方同步用户名存在特殊符号
                String imageName = fileName;
                if (isImage) {
                    imageName = imagesService.getOriginalNameByPath(filePath).replace("*", "");
                    if (StringUtils.isEmpty(imageName)) {
                        imageName = fileName;
                    }
                }
                log.info("check the file: {}", file.exists());
                String destPath = sysConfig.getPhysicalPath(tempDir + imageName);
                File file1 = new File(destPath);
                if (file1.exists()) {
                    FileUtil.deleteFile(destPath);
                }
                FileUtils.moveFile(file, file1);
            } catch (IOException e) {
                throw new BusinessException(FileErrorNoEnum.ERR_FILE_ZIP);
            }
        }

        // 压缩包文件名称
        String zipFilePath = dealZipFile(zipFileName, tempDir);
        // 返回信息
        return sysConfig.getRoot() + zipFilePath;
    }

    @Override
    @Async
    public void compressorFiles(Map<String, String> filePathMap, String zipFileName, String bizId) {
        try {
            // 建立临时目录
            String tempDir = sysConfig.queryTempPath() + StringUtil.newId() + SEPARATOR;
            log.info("tempDir path : {}", tempDir);
            FileUtil.mkdir(tempDir);
            String targetDir = sysConfig.queryTempPath() + StringUtil.newId() + SEPARATOR;
            log.info("targetDir path : {}", targetDir);
            FileUtil.mkdir(targetDir);
            // 把文件下载到目录
            S3Client s3Client = SysConfig.getS3Client();
            for (Entry<String, String> entry : filePathMap.entrySet()) {
                String filePath = entry.getKey();
                File file = new File(sysConfig.getPhysicalPath(filePath));
                if (file.exists()) {
                    // 如果文件大小是0，等文件复杂完成
                    long startTime = System.currentTimeMillis();
                    while (file.length() == 0 && (System.currentTimeMillis() - startTime) < (60 * 1000)) {
                        Thread.sleep(1000);
                        file = new File(sysConfig.getPhysicalPath(filePath));
                        log.info("file size:{}", file.length());
                    }
                }
                // 获取对象存储路径
                String objectName = getObjectName(filePath, SEPARATOR, 1, sysConfig.getBucketName(),
                    sysConfig.getBucketName().length() + 1);
                Path localPath = file.toPath();
                Files.createDirectories(localPath.getParent());
                log.info("downloadFile objectName:{}  bucketName:{}", objectName, sysConfig.getBucketName());
                GetObjectRequest getRequest = GetObjectRequest.builder()
                    .bucket(sysConfig.getBucketName())
                    .key(objectName)
                    .build();

                s3Client.getObject(getRequest, ResponseTransformer.toFile(localPath));

                log.info("check the file: {}", file.exists());
                String destPath = sysConfig.getPhysicalPath(tempDir + filePathMap.get(filePath));
                File destFile = new File(destPath);
                if (destFile.exists()) {
                    FileUtil.deleteFile(destPath);
                }
                FileUtils.moveFile(file, destFile);
            }
            String zipFilePath = dealZipFile(zipFileName, tempDir);

            // 更新导出记录的状态
            excelRecordFeignImpl.updateExportRecord(bizId, ExportStatusEnum.SUCCESS.getStatus(),
                sysConfig.getRoot() + zipFilePath);
        } catch (InterruptedException e) {
            log.error("=====downloadFile error=====", e);
            Thread.currentThread().interrupt();
            throw new BusinessException(FileErrorNoEnum.DOWNLOAD_FILE_ERROR);
        } catch (IOException e) {
            //若有异常 则更新导出记录的状态 为导出失败状态
            excelRecordFeignImpl.updateExportRecord(bizId, ExportStatusEnum.FAILED.getStatus(), "");
            throw new BusinessException(FileErrorNoEnum.ERR_FILE_ZIP);
        } catch (Exception e) {
            log.error("export_zip_error", e);
            //若有异常 则更新导出记录的状态 为导出失败状态
            excelRecordFeignImpl.updateExportRecord(bizId, ExportStatusEnum.FAILED.getStatus(), "");
        }
    }

    /**
     * 处理压缩文件
     *
     * @param zipFileName
     * @param tempDir
     * @return
     */
    @NotNull
    private String dealZipFile(String zipFileName, String tempDir) {
        // 压缩包文件名称
        String suffix = ".zip";
        zipFileName = StringUtils.isBlank(zipFileName) ? StringUtil.newId() + suffix : zipFileName + suffix;

        // 压缩文件
        String zipFilePath = sysConfig.getExportPath(StringUtil.newId() + SEPARATOR + zipFileName);
        // 物理路径
        String zipPhysicalPath = sysConfig.getPhysicalPath(zipFilePath);
        File parentDir = new File(zipPhysicalPath).getParentFile();
        if (!parentDir.exists()) {
            FileUtil.mkdir(parentDir.getAbsolutePath());
        }
        log.info("zipFilePath path : {} ; zipPhysicalPath path : {}", zipFilePath, zipPhysicalPath);
        try {
            ZipUtil.zip(sysConfig.getPhysicalPath(tempDir), zipPhysicalPath);
        } catch (IOException e) {
            log.info("compressor zip file exception : {}", e.toString());
            throw new BusinessException(FileErrorNoEnum.ERR_FILE_ZIP);
        }

        if (zipFilePath.startsWith("/")) {
            zipFilePath = zipFilePath.substring(1);
        }
        if (zipFilePath.startsWith(sysConfig.getBucketName())) {
            zipFilePath = zipFilePath.substring(sysConfig.getBucketName().length() + 1);
        }
        // 上传文件
        uploadOss(zipPhysicalPath, zipFilePath);

        if (zipFilePath.startsWith(UserThreadContext.getTenantId())) {
            zipFilePath = zipFilePath.substring(UserThreadContext.getTenantId().length());
        }
        return zipFilePath;
    }

    /**
     * 获取真实的objectName,不带前面的bucketName
     *
     * @param filePath
     * @return
     */
    private String getRealObjectName(String filePath) {
        if (filePath.startsWith(SEPARATOR)) {
            filePath = filePath.substring(1);
        }
        if (filePath.startsWith(sysConfig.getBucketName() + SEPARATOR)) {
            filePath = filePath.substring(sysConfig.getBucketName().length() + 1);
        }

        return filePath;
    }

    @Override
    public void composeFiles(Collection<CloudFileChunk> cloudFileChunkList, CloudFile cloudFile) {
        List<CompletedPart> completedParts = cloudFileChunkList.stream()
            .map(cf -> CompletedPart.builder()
                .partNumber(cf.getChunkIndex())
                .eTag(cf.getETag())
                .build())
            .toList();

        CompletedMultipartUpload completedMultipartUpload = CompletedMultipartUpload.builder()
            .parts(completedParts)
            .build();

        CompleteMultipartUploadRequest completeRequest = CompleteMultipartUploadRequest.builder()
            .bucket(sysConfig.getBucketName())
            .key(cloudFile.getCurrentPath())
            .uploadId(cloudFile.getUploadId())
            .multipartUpload(completedMultipartUpload)
            .build();

        try {
            SysConfig.getS3Client().completeMultipartUpload(completeRequest);
        } catch (S3Exception e) {
            throw new RuntimeException("合并分片失败", e);
        }
    }

    @Override
    @SuppressWarnings("java:S3776")
    public String getUrl(String path, boolean isDownload, String fileName) {
        if (StringUtils.isBlank(path)) {
            throw new BusinessException(FileErrorNoEnum.FILE_OPERATION_FAIL);
        }
        // 对path进行兼容截取
        path = dealWithPath(path);
        // 过期时间默认2小时
        String second = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_41002.getCode());
        long expiredSeconds = StringUtils.isNotBlank(second) ? Long.parseLong(second) : 7200L;
        try {
            // 构建原始请求
            GetObjectRequest.Builder getObjectRequestBuilder = GetObjectRequest.builder()
                .bucket(sysConfig.getBucketName()).key(path);
            // 下载行为：设置响应类型为文件流
            if (isDownload && GeneralJudgeEnum.CONFIRM.getValue().equals(sysConfig.getNeedContentType())) {
                getObjectRequestBuilder.responseContentType("application/octet-stream");
            }
            if (StringUtils.isNotBlank(fileName)) {
                String suffix = path.substring(path.lastIndexOf("."));
                if (!fileName.endsWith(suffix)) {
                    fileName += suffix;
                }
                log.info("fileName: {}", fileName);

                String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8)
                    .replace("\\+", "%20"); // 避免 + 被当成空格

                String disposition = "attachment; filename*=utf-8''" + encodedFileName + FILE_NAME + fileName;
                getObjectRequestBuilder.responseContentDisposition(disposition);
            }
            // 构建预签名请求
            GetObjectPresignRequest presignRequest = GetObjectPresignRequest.builder()
                .signatureDuration(Duration.ofSeconds(expiredSeconds))
                .getObjectRequest(getObjectRequestBuilder.build())
                .build();
            return SysConfig.getFileUrl(presignRequest);
        } catch (Exception e) {
            throw new BusinessException(FileErrorNoEnum.FILE_OPERATION_FAIL);
        }
    }

    @NotNull
    private String dealWithPath(String path) {
        if (path.startsWith(urlHelper.getStaticBaseUrl())) {
            path = path.substring(urlHelper.getStaticBaseUrl().length());
        }
        // 去掉最前面的/
        if (path.startsWith("/")) {
            path = path.substring(1);
        }
        // 去掉 bucketName
        if (path.startsWith(sysConfig.getBucketName())) {
            path = path.substring(sysConfig.getBucketName().length() + 1);
        }
        // 传进来的地址可能带了签名，去掉地址?后面
        int index = path.indexOf("?");
        if (index != -1) {
            path = path.substring(0, index);
        }
        // 兼容文件名被转码
        path = URLDecoder.decode(path, StandardCharsets.UTF_8);
        return path;
    }


    @Override
    public Boolean checkFileIsExists(String path) {
        if (StringUtils.isBlank(path)) {
            return false;
        }
        // 获取对象存储路径
        String objectName = getObjectName(path, SEPARATOR, 1, sysConfig.getBucketName(),
            sysConfig.getBucketName().length() + 1);
        try {
            HeadObjectRequest headRequest = HeadObjectRequest.builder()
                .bucket(sysConfig.getBucketName())
                .key(objectName)
                .build();
            HeadObjectResponse headResponse = SysConfig.getS3Client().headObject(headRequest);

            long s3ContentLength = headResponse.contentLength();
            Instant s3LastModified = headResponse.lastModified();

            // 获取本地文件
            String filePath = sysConfig.getPhysicalPath(sysConfig.getBucketName() + StringPool.SLASH + objectName);
            File file = new File(filePath);

            // 返回 true 代表文件不一致，需要更新，逻辑和之前保持一致
            return s3ContentLength <= 0 || s3ContentLength != file.length()
                || s3LastModified.toEpochMilli() != file.lastModified();
        } catch (NoSuchKeyException e) {
            log.info("S3 对象不存在: {}", objectName);
        } catch (S3Exception e) {
            log.error("S3 获取文件信息异常", e);
        } catch (Exception e) {
            log.error("未知异常", e);
        }
        return false;
    }

    @Override
    public void deleteTempFile(Integer deleteTime) {
        String objectTempDirectory = UserThreadContext.getTenantId() + SEPARATOR + "tempFile/";
        // 当前时间
        Instant now = Instant.now();
        S3Client s3Client = SysConfig.getS3Client();
        String bucketName = sysConfig.getBucketName();
        try {
            String continuationToken = null;

            do {
                ListObjectsV2Request listRequest = ListObjectsV2Request.builder()
                    .bucket(bucketName)
                    .prefix(objectTempDirectory)
                    .continuationToken(continuationToken)
                    .build();

                ListObjectsV2Response listResult = s3Client.listObjectsV2(listRequest);
                List<S3Object> objectSummaries = listResult.contents();

                if (objectSummaries.isEmpty()) {
                    break;
                }

                // 批量删除构造keys
                List<ObjectIdentifier> keysToDelete = new ArrayList<>();
                for (S3Object summary : objectSummaries) {
                    Instant lastModifiedTime = summary.lastModified();

                    // 计算时间差（基于时间戳）
                    long daysDifference = calculateDaysDifference(lastModifiedTime, now);

                    if (daysDifference >= deleteTime) {
                        log.info("标记为删除: " + summary.key() + ", 最后修改时间: " + lastModifiedTime);
                        keysToDelete.add(ObjectIdentifier.builder().key(summary.key()).build());
                    }
                }
                if (!keysToDelete.isEmpty()) {
                    DeleteObjectsRequest deleteRequest = DeleteObjectsRequest.builder()
                        .bucket(bucketName)
                        .delete(Delete.builder().objects(keysToDelete).build())
                        .build();

                    try {
                        DeleteObjectsResponse deleteResult = s3Client.deleteObjects(deleteRequest);
                        log.info("成功删除 {} 个对象", deleteResult.deleted().size());
                    } catch (Exception e) {
                        log.error("批量删除对象失败", e);
                    }
                }

                continuationToken = listResult.nextContinuationToken();

            } while (continuationToken != null);

        } catch (Exception e) {
            log.error("删除对象时发生异常", e);
        }
    }


    /**
     * 计算两个时间戳之间的天数差
     *
     * @param lastModifiedTime 对象的最后修改时间（时间戳）
     * @param now              当前时间（时间戳）
     * @return 两个时间戳之间的天数差
     */
    private static long calculateDaysDifference(Instant lastModifiedTime, Instant now) {
        // 计算两个时间点之间的时间间隔（以秒为单位）
        long secondsDifference = Duration.between(lastModifiedTime, now).getSeconds();
        // 将秒数转换为天数（1 天 = 24 * 60 * 60 秒）
        // 如果剩余的秒数不足以构成一整天，则不计入天数
        return secondsDifference / (24 * 60 * 60);
    }

    @Override
    public String storageStrategyService(Integer index, File file, CloudFile cloudFile) {
        try (InputStream inputStream = new FileInputStream(file)) {
            UploadPartRequest uploadPartRequest = UploadPartRequest.builder()
                .bucket(sysConfig.getBucketName())
                .key(cloudFile.getCurrentPath())
                .uploadId(cloudFile.getUploadId())
                .partNumber(index)
                .contentLength(file.length())
                .build();

            UploadPartResponse uploadPartResponse = SysConfig.getS3Client()
                .uploadPart(uploadPartRequest, RequestBody.fromInputStream(inputStream, file.length()));

            return uploadPartResponse.eTag();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void dealWithOldFile(List<String> pathList, List<String> filePathList) {
        S3Client s3Client = SysConfig.getS3Client();
        String bucketName = sysConfig.getBucketName();
        List<List<String>> pathListResult = getSubList(pathList);
        List<CompletableFuture<Void>> futureList = new ArrayList<>();
        for (List<String> pathSubList : pathListResult) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                for (String path : pathSubList) {
                    dealWithPublicReadPath(path, bucketName, s3Client);
                }
            }, dealWithOldFileThreadPool);
            futureList.add(future);
        }
        List<List<String>> filePathListResult = getSubList(filePathList);
        for (List<String> filePathSubList : filePathListResult) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                for (String filePath : filePathSubList) {
                    dealWithPublicReadFilePath(filePath, bucketName, s3Client);
                }
            }, dealWithOldFileThreadPool);
            futureList.add(future);
        }
        // 等待所有任务完成
        CompletableFuture<Void> allDone = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0]));

        // 阻塞直到所有任务完成
        try {
            allDone.get();
        } catch (InterruptedException | ExecutionException e) {
            Thread.currentThread().interrupt();
            log.error("dealWithOldFile error", e);
        }
    }

    private void dealWithPublicReadFilePath(String filePath, String bucketName, S3Client s3Client) {
        try {
            String objectName = getObjectName(FilenameUtils.separatorsToUnix(filePath), SEPARATOR, 1,
                bucketName + SEPARATOR, bucketName.length() + 1);
            // 设置对象权限为 public-read
            s3Client.putObjectAcl(PutObjectAclRequest.builder()
                .bucket(bucketName)
                .key(objectName)
                .acl(ObjectCannedACL.PUBLIC_READ)
                .build());
            log.info("set public-read success: " + filePath);
        } catch (Exception e) {
            log.error("set ACL fail " + filePath, e);
        }
    }

    private void dealWithPublicReadPath(String path, String bucketName, S3Client s3Client) {
        try {
            String objectName = getObjectName(
                FilenameUtils.separatorsToUnix(path),
                SEPARATOR,
                1,
                bucketName + SEPARATOR,
                bucketName.length() + 1
            );
            log.info("dealWith oldFile objectName：{}", objectName);

            String prefix = objectName.substring(0, objectName.lastIndexOf('/') + 1);

            String continuationToken = null;
            do {
                ListObjectsV2Request.Builder listRequestBuilder = ListObjectsV2Request.builder()
                    .bucket(bucketName)
                    .prefix(prefix);
                if (continuationToken != null) {
                    listRequestBuilder.continuationToken(continuationToken);
                }
                ListObjectsV2Response response = s3Client.listObjectsV2(listRequestBuilder.build());
                for (S3Object s3Object : response.contents()) {
                    String key = s3Object.key();
                    if (!objectName.equals(key)) {
                        try {
                            s3Client.putObjectAcl(PutObjectAclRequest.builder()
                                .bucket(bucketName)
                                .key(key)
                                .acl(ObjectCannedACL.PUBLIC_READ)
                                .build());
                            log.info("set public-read success: {}", key);
                        } catch (Exception e) {
                            log.error("set ACL fail {}", key, e);
                        }
                    }
                }
                continuationToken = response.nextContinuationToken();
            } while (continuationToken != null);
        } catch (Exception e) {
            log.error("Error processing path: {}", path, e);
        }
    }

    @NotNull
    private static List<List<String>> getSubList(List<String> filePathList) {
        List<List<String>> filePathListResult = new ArrayList<>();
        int total = filePathList.size();
        for (int i = 0; i < total; i += 500) {
            int end = Math.min(i + 500, total);
            filePathListResult.add(filePathList.subList(i, end));
        }
        return filePathListResult;
    }
}
