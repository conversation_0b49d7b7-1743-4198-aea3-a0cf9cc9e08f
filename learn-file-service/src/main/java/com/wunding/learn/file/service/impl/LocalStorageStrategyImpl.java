package com.wunding.learn.file.service.impl;

import com.wunding.learn.common.constant.file.FileErrorNoEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.file.api.constant.FileBizType;
import com.wunding.learn.file.api.constant.ImageBizType;
import com.wunding.learn.file.config.SysConfig;
import com.wunding.learn.file.model.CloudFile;
import com.wunding.learn.file.model.CloudFileChunk;
import com.wunding.learn.file.model.StsRequest;
import com.wunding.learn.file.model.StsResponse;
import com.wunding.learn.file.model.UploadFileRequest;
import com.wunding.learn.file.model.UploadFileResponse;
import com.wunding.learn.file.service.StorageStrategyService;
import com.wunding.learn.file.util.FileUtil;
import com.wunding.learn.file.util.ZipUtil;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;

/**
 * 本地存储工具类
 *
 * <AUTHOR>
 * @since 2021/2/4 16:37
 */
@Slf4j
public class LocalStorageStrategyImpl implements StorageStrategyService, InitializingBean {

    private static final String SEPARATOR = "/";

    private static String locationFile;
    private static String locationRoot;
    private final SysConfig sysConfig;

    public LocalStorageStrategyImpl(SysConfig sysConfig) {
        this.sysConfig = sysConfig;
    }

    @Override
    public void afterPropertiesSet() {
        // 使用静态块初始化静态变量
        setStaticVariables(sysConfig);
    }

    private static void setStaticVariables(SysConfig config) {
        locationFile = config.getLocation();
        locationRoot = config.getRoot();
    }

    /**
     * 复制整个文件夹内容
     *
     * @param srcPath    String 原文件路径 如：c:/fqf
     * @param targetPath String 复制后路径 如：f:/fqf/ff
     * @return boolean
     */
    public static String copyFolder(String srcPath, String targetPath) {
        String indexPath = "";

        // 构建返回路径，去除路径前缀
        indexPath = targetPath.replace(locationFile, "");
        // 判断是否/开关
        if (!indexPath.startsWith(SEPARATOR)) {
            indexPath = SEPARATOR + indexPath;
        }
        // 判断是 / 结尾
        if (!indexPath.endsWith(SEPARATOR)) {
            indexPath = indexPath + SEPARATOR;
        }
        // 路径添加index.html
        indexPath = indexPath + "index.html";
        // 如果原路径和目标路径相同直接返回
        if (Objects.equals(srcPath, targetPath)) {
            return indexPath;
        }

        try {
            // 如果文件夹不存在 则建立新文件夹
            FileUtil.mkdir(targetPath);

            File a = new File(srcPath);
            String[] file = a.list();
            File temp;
            for (int i = 0; i < Objects.requireNonNull(file).length; i++) {
                if (srcPath.endsWith(File.separator)) {
                    temp = new File(srcPath + file[i]);
                } else {
                    temp = new File(srcPath + File.separator + file[i]);
                }
                if (temp.isFile()) {
                    try (FileInputStream input = new FileInputStream(temp);
                        FileOutputStream output =
                            new FileOutputStream(targetPath + File.separator + (temp.getName()))) {
                        byte[] b = new byte[1024 * 5];
                        int len;
                        while ((len = input.read(b)) != -1) {
                            output.write(b, 0, len);
                        }
                        output.flush();
                    }
                }
                if (temp.isDirectory()) {
                    // 如果是子文件夹
                    copyFolder(srcPath + File.separator + file[i], targetPath + File.separator + file[i]);
                }
            }
        } catch (Exception e) {
            log.error("An error occurred FileNotFoundException when copyFolder", e);
        }
        return indexPath;
    }

    @Override
    public UploadFileResponse uploadDir(String source, String target, String bizId) {
        UploadFileResponse uploadFileResponse = new UploadFileResponse();
        String path = copyFolder(source, target);
        uploadFileResponse.setFileSize(0L);
        uploadFileResponse.setFileUrl(path);
        uploadFileResponse.setFilePath(path);
        return uploadFileResponse;
    }

    /**
     * 是目录就递归拷贝目录，文件则直接拷贝
     *
     * @param uploadFileRequest
     * @return
     */
    @Override
    public UploadFileResponse uploadFile(UploadFileRequest uploadFileRequest) {
        UploadFileResponse uploadFileResponse = new UploadFileResponse();
        String folderUrl = "";

        // 1、只上传文件

        // 2、只上传目录

        // 3、目录和文件同时上传

        // 如果是同一文件，直接返回路径，不操作文件
        // 只上传文件并且文件的路径相同
        String filePath = sysConfig.getPhysicalPath(uploadFileRequest.getFileName());
        File sourceFile = new File(sysConfig.getPhysicalPath(uploadFileRequest.getTempFilePath()));
        uploadFileResponse.setFileSize(sourceFile.length());

        // 确保输出目录存在
        // 不存在修改
        uploadFileRequest.setFileName(FilenameUtils.separatorsToUnix(uploadFileRequest.getFileName()));
        FileUtil.mkdir(
            sysConfig.getPhysicalPath(FilenameUtils.getPath(uploadFileRequest.getFileName())));

        if (uploadFileRequest.getTempFilePath() != null
            && Objects.equals(uploadFileRequest.getTempFilePath(), filePath)
            && !uploadFileRequest.isCopyFolderFlag()) {
            log.info("上传路径和原路径相同，不操作文件，直接返回路径");
            uploadFileResponse.setFileUrl(uploadFileRequest.getFileName());
            return uploadFileResponse;
        }

        // 构建返回路径，去除路径前缀
        String indexPath = getIndexPath(uploadFileRequest);

        // 只上传目录并且目录路径相同
        if (uploadFileRequest.getFolderSrcPath() != null
            && Objects.equals(
            uploadFileRequest.getFolderSrcPath(), uploadFileRequest.getFolderTargetPath())
            && uploadFileRequest.isCopyFolderFlag()
            && StringUtils.isBlank(uploadFileRequest.getTempFilePath())) {
            log.info("上传目录路径和原目录路径相同，不操作，直接返回路径");
            uploadFileResponse.setFileUrl(indexPath);
            return uploadFileResponse;
        }

        // 文件和目录同时上传，且都相等时
        if (uploadFileRequest.getFolderSrcPath() != null
            && Objects.equals(
            uploadFileRequest.getFolderSrcPath(), uploadFileRequest.getFolderTargetPath())
            && uploadFileRequest.isCopyFolderFlag()
            && uploadFileRequest.getTempFilePath() != null
            && Objects.equals(uploadFileRequest.getTempFilePath(), filePath)) {
            log.info("文件和目录同时上传，且都相等时,上传目录路径和原目录路径相同，不操作，直接返回路径");
            uploadFileResponse.setFileUrl(indexPath);
            return uploadFileResponse;
        }

        // 上传目录，源目录和目标目录不一样
        if (uploadFileRequest.isCopyFolderFlag()
            && !Objects.equals(
            uploadFileRequest.getFolderSrcPath(), uploadFileRequest.getFolderTargetPath())) {
            folderUrl =
                copyFolder(uploadFileRequest.getFolderSrcPath(), uploadFileRequest.getFolderTargetPath());
        }

        // 上传文件，源文件和目标文件不一样
        if (StringUtils.isNotEmpty(uploadFileRequest.getTempFilePath())
            && !Objects.equals(uploadFileRequest.getTempFilePath(), filePath)) {
            try (FileInputStream fileInputStream =
                new FileInputStream(uploadFileRequest.getTempFilePath());
                FileOutputStream fileOutputStream = new FileOutputStream(filePath);
                BufferedOutputStream outBuff = new BufferedOutputStream(fileOutputStream)) {
                // 读取文件流
                IOUtils.copy(fileInputStream, outBuff);
            } catch (IOException e) {
                log.error("An error occurred FileNotFoundException when uploadFile", e);
            }
        }

        // 保证返回的url必须以 /locationRoot/开始
        String backUrl = getBackUrl(uploadFileRequest);

        // 本地存储，返回没有根目录的相对路径
        uploadFileResponse.setFileUrl(uploadFileRequest.isCopyFolderFlag() ? folderUrl : backUrl);
        return uploadFileResponse;
    }

    /**
     * 获取backUrl
     *
     * @param uploadFileRequest 上传文件请求
     * @return {@link String }
     */
    private String getBackUrl(UploadFileRequest uploadFileRequest) {
        String backUrl = "";
        if (StringUtils.isNotBlank(uploadFileRequest.getFileName())) {
            backUrl = uploadFileRequest.getFileName();
            if (!backUrl.startsWith(SEPARATOR)) {
                backUrl = SEPARATOR + backUrl;
            }
            if (!backUrl.startsWith(SEPARATOR + locationRoot)) {
                backUrl = SEPARATOR + locationRoot + backUrl;
            }
        }
        return backUrl;
    }

    /**
     * 获取索引路径
     *
     * @param uploadFileRequest 上传文件请求
     * @return {@link String }
     */
    private String getIndexPath(UploadFileRequest uploadFileRequest) {
        String indexPath = "";
        if (uploadFileRequest.isCopyFolderFlag()
            && StringUtils.isNotBlank(uploadFileRequest.getFolderTargetPath())) {
            indexPath = uploadFileRequest.getFolderTargetPath().replace(locationFile, "");
        }
        // 判断是否/开关
        if (!indexPath.startsWith(SEPARATOR)) {
            indexPath = SEPARATOR + indexPath;
        }
        // 判断是 / 结尾
        if (!indexPath.endsWith(SEPARATOR)) {
            indexPath = indexPath + SEPARATOR;
        }
        // 路径添加index.html
        indexPath = indexPath + "index.html";
        return indexPath;
    }

    @Override
    public UploadFileResponse uploadZipFileAndUnzip(UploadFileRequest uploadFileRequest) {
        //解压缩
        return uploadFile(uploadFileRequest);
    }

    @Override
    public UploadFileResponse copyFile(String source, String target, boolean isPublicRead) {
        UploadFileResponse uploadFileResponse = new UploadFileResponse();
        String sourcePath = sysConfig.getPhysicalPath(source);
        String targetPath = sysConfig.getPhysicalPath(target);
        try {
            FileUtils.copyFile(new File(sourcePath), new File(targetPath));
        } catch (IOException e) {
            throw new BusinessException(FileErrorNoEnum.COPY_FILE_FAIL);
        }
        uploadFileResponse.setFilePath(target);
        uploadFileResponse.setFileUrl(target);
        uploadFileResponse.setFileSize(new File(sourcePath).length());
        return uploadFileResponse;
    }

    @Override
    public UploadFileResponse copyFile(String source, FileBizType fileBizType) {
        return null;
    }

    @Override
    public UploadFileResponse copyFile(String source, ImageBizType imageBizType) {
        return null;
    }

    @Override
    public UploadFileResponse copyDir(String source, String target) {
        UploadFileResponse uploadFileResponse = new UploadFileResponse();
        String sourcePath = sysConfig.getPhysicalPath(source);
        String targetPath = sysConfig.getPhysicalPath(target);
        copyFolder(sourcePath, targetPath);
        uploadFileResponse.setFilePath(target);
        return uploadFileResponse;
    }

    @Override
    public UploadFileResponse findFile(UploadFileRequest uploadFileRequest) {
        return null;
    }

    @Override
    public void deleteFile(String filePath) {
        File file = new File(filePath);
        if (file.isDirectory()) {
            File[] listFiles = file.listFiles();
            for (int i = 0; i < Objects.requireNonNull(listFiles).length; i++) {
                deleteFile(listFiles[i].getPath());
            }
        }
        try {
            Files.delete(file.toPath());
        } catch (IOException e) {
            log.error("An error occurred Exception when deleteFile, filePath : " + filePath);
        }
    }

    /**
     * 本地存储不支持获取临时凭证
     *
     * @return
     */
    @Override
    public StsResponse getSts(StsRequest stsRequest) {
        log.warn("本地存储不支持获取临时凭证");
        return null;
    }

    @Override
    public File downloadFile(String uri) {
        return new File(sysConfig.getPhysicalPath(uri));
    }

    @Override
    public String compressorFiles(Collection<String> filePaths, String zipFileName, boolean isImage) {
        // 建立临时目录
        String tempDir = sysConfig.queryTempPath() + StringUtil.newId() + SEPARATOR;
        String sourceDir = tempDir + "sourceDir" + SEPARATOR;
        FileUtil.mkdir(sourceDir);
        log.info("sourceDir:" + sourceDir);
        String targetDir = tempDir + "targetDir" + SEPARATOR;
        log.info("targetDir:" + targetDir);
        FileUtil.mkdir(targetDir);
        // 把文件拷贝到目录
        for (String filePath : filePaths) {
            try {
                String fileName = FilenameUtils.getName(filePath);
                FileUtils.copyFile(downloadFile(filePath), new File(sourceDir + fileName));
            } catch (IOException e) {
                log.error("copyFile fail", e);
                throw new BusinessException(FileErrorNoEnum.ERR_FILE_ZIP);
            }
        }

        if (StringUtils.isBlank(zipFileName)) {
            zipFileName = StringUtil.newId();
        }
        // 压缩文件
        //目标压缩文件的目录必须不能在待压缩文件的目录下，会造成资源无法释放，因为后面用的是parentFile.listFiles()
        String zipFilePath = targetDir + zipFileName + ".zip";
        try {
            ZipUtil.zip(sourceDir, zipFilePath);
        } catch (IOException e) {
            throw new BusinessException(FileErrorNoEnum.ERR_FILE_ZIP);
        }
        // 返回信息
        return zipFilePath;
    }

    @Override
    public void compressorFiles(Map<String, String> filePathMap, String zipFileName, String bizId) {
        // comment explaining why the method is empty
    }

    @Override
    public void composeFiles(Collection<CloudFileChunk> cloudFileChunkList, CloudFile cloudFile) {
        // comment explaining why the method is empty
    }

    @Override
    public String getUrl(String path, boolean isDownload, String fileName) {
        return null;
    }

    @Override
    public boolean checkOssIsExists(String sourceObjectName) {
        return false;
    }

    @Override
    public Boolean checkFileIsExists(String path) {
        File file = new File(sysConfig.getPhysicalPath(path));
        return file.exists();
    }

    @Override
    public void deleteTempFile(Integer deleteTime) {
        // comment explaining why the method is empty
    }

    @Override
    public String storageStrategyService(Integer index, File file, CloudFile cloudFile) {
        // comment explaining why the method is empty
        return null;
    }

    @Override
    public void dealWithOldFile(List<String> pathList, List<String> filePathList) {
        // comment explaining why the method is empty
    }
}
