package com.wunding.learn.file.service;

import com.wunding.learn.file.api.constant.FileBizType;
import com.wunding.learn.file.api.constant.ImageBizType;
import com.wunding.learn.file.model.CloudFile;
import com.wunding.learn.file.model.CloudFileChunk;
import com.wunding.learn.file.model.StsRequest;
import com.wunding.learn.file.model.StsResponse;
import com.wunding.learn.file.model.UploadFileRequest;
import com.wunding.learn.file.model.UploadFileResponse;
import java.io.File;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021/2/4 16:49
 */
public interface StorageStrategyService {

    /**
     * 上传文件
     *
     * @param uploadFileRequest
     * @return
     */
    UploadFileResponse uploadFile(UploadFileRequest uploadFileRequest);

    /**
     * 复制文件
     *
     * @param source       原文件path
     * @param target       目标文件path
     * @param isPublicRead 是否公有读 0-否 1-是
     * @return 复制文件后的目标信息
     */
    UploadFileResponse copyFile(String source, String target, boolean isPublicRead);

    /**
     * 复制文件
     *
     * @param source      原文件path
     * @param fileBizType 目标文件类型
     * @return 复制文件后的目标信息
     */
    UploadFileResponse copyFile(String source, FileBizType fileBizType);

    /**
     * 复制文件
     *
     * @param source       原文件path
     * @param imageBizType 目标文件类型
     * @return 复制文件后的目标信息
     */
    UploadFileResponse copyFile(String source, ImageBizType imageBizType);

    /**
     * 复制目录
     *
     * @param source 原目录path
     * @param target 目标目录path
     * @return 复制文件后的目标信息
     */
    UploadFileResponse copyDir(String source, String target);

    /**
     * 上传文件压缩包文件并解压
     */
    UploadFileResponse uploadZipFileAndUnzip(UploadFileRequest uploadFileRequest);


    /**
     * 从服务器上面获取文件
     *
     * @param uploadFileRequest
     * @return
     */
    UploadFileResponse findFile(UploadFileRequest uploadFileRequest);

    /**
     * 删除文件
     *
     * @param filePath
     */
    void deleteFile(String filePath);

    /**
     * 获取minio临时凭证
     *
     * @return
     */
    StsResponse getSts(StsRequest stsRequest);

    /**
     * 下载文件
     *
     * @param uri
     * @return
     */
    File downloadFile(String uri);

    /**
     * 上传目录
     *
     * @param source 本地目录
     * @param target 上标目录
     * @return 目录主页
     */
    UploadFileResponse uploadDir(String source, String target, String bizId);

    /**
     * 压缩文件
     *
     * @param filePaths   要压缩的文件列表
     * @param zipFileName 压缩包名称
     * @return 压缩后的文件信息
     */
    String compressorFiles(Collection<String> filePaths, String zipFileName, boolean isImage);

    /**
     * 压缩文件
     *
     * @param filePathMap 要压缩的文件列表Map(key为上传路径，value为下载路径)
     * @param zipFileName 压缩包名称
     * @return 压缩后的文件信息
     */
    void compressorFiles(Map<String, String> filePathMap, String zipFileName, String bizId);


    /**
     * 合并文件
     *
     * @param cloudFileChunkList
     * @param cloudFile
     * @return
     */
    void composeFiles(Collection<CloudFileChunk> cloudFileChunkList, CloudFile cloudFile);

    /**
     * 获取url
     *
     * @param path       文件path
     * @param isDownload 是否是下载
     * @param fileName   文件名
     * @return 文件URL
     */
    String getUrl(String path, boolean isDownload, String fileName);

    /**
     * 判断文件是否存在（此方法为对象存储服务专用方法 LocalStorageStrategyImpl 直接返回 False）
     *
     * @param sourceObjectName 资源存储名称（全路径）
     * @return 对象存储是否存在
     */
    boolean checkOssIsExists(String sourceObjectName);

    /**
     * 判断文件存不存在
     *
     * @param path
     * @return
     */
    Boolean checkFileIsExists(String path);

    /**
     * 删除指定时间以前的临时文件
     *
     * @param deleteTime 指定天数
     */
    void deleteTempFile(Integer deleteTime);

    /**
     * 上传分片文件
     *
     * @param index     分片序号
     * @param file      分片文件
     * @param cloudFile 主文件信息
     * @return eTag
     */
    String storageStrategyService(Integer index, File file, CloudFile cloudFile);

    /**
     * 处理公有读
     *
     * @param pathList     目录地址
     * @param filePathList 文件地址
     */
    void dealWithOldFile(List<String> pathList, List<String> filePathList);
}
