package com.wunding.learn.file.feign;

import com.alibaba.excel.EasyExcelFactory;
import com.carrotsearch.sizeof.RamUsageEstimator;
import com.spire.xls.Chart;
import com.spire.xls.ExcelChartType;
import com.spire.xls.ExcelVersion;
import com.spire.xls.HorizontalAlignType;
import com.spire.xls.LegendPositionType;
import com.spire.xls.Workbook;
import com.spire.xls.Worksheet;
import com.wunding.learn.common.constant.file.FileErrorNoEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.redis.util.RedisLockUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.dto.CustomCellWriteHandler;
import com.wunding.learn.file.api.dto.ExcelHead;
import com.wunding.learn.file.api.dto.ExportData;
import com.wunding.learn.file.api.dto.ExportDataAndHeadDTO;
import com.wunding.learn.file.api.dto.ExportResultDTO;
import com.wunding.learn.file.api.dto.HeadWriteHandler;
import com.wunding.learn.file.api.dto.TextCellWriteHandler;
import com.wunding.learn.file.api.dto.export.ExportDataDTO;
import com.wunding.learn.file.api.dto.export.certification.AssessReportData;
import com.wunding.learn.file.api.dto.export.certification.AssessReportExportDTO;
import com.wunding.learn.file.api.excel.handler.I18nCellWriteHandler;
import com.wunding.learn.file.api.service.ExcelExportFeign;
import com.wunding.learn.file.config.SysConfig;
import com.wunding.learn.file.model.UploadFileRequest;
import com.wunding.learn.file.model.UploadFileResponse;
import com.wunding.learn.file.service.UploadFileService;
import com.wunding.learn.file.util.FileUtil;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.service.UserFeign;
import jakarta.annotation.Resource;
import java.awt.Color;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @program: mlearn
 * @description: <p>导出数据</p>
 * @author: 赖卓成
 * @create: 2022-08-31 15:08
 **/
@RestController
@RequestMapping("${module.file.contentPath:/}")
@Slf4j
public class ExcelExportFeignImpl implements ExcelExportFeign {

    private static final String SEPARATOR = "/";
    /**
     * 文件类型后缀
     */
    private static final String SUFFIX_XLSX = ".xlsx";
    private static final String DELETE_TEMP_FILE_SUCCESS_LOG = "删除临时文件成功";
    private static final String DELETE_TEMP_FILE_FAIL_LOG = "删除临时文件失败";
    private static final String LOG_FORMAT = "filePath:{}, fileName{}, templateFile{}, destFile:{}";
    private static final String RENAME_SUCCESS_LOG_FORMAT = "重命名成功,{}-->{}";


    @Resource
    private SysConfig sysConfig;
    @Resource
    private UploadFileService uploadFileService;

    @Resource(name = "redisTemplate")
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private UserFeign userFeign;


    @Override
    public ExportResultDTO export(ExportDataDTO exportDataDTO, String bizId, String fileName, Integer finish,
        ExportBizType exportBizType) {
        ArrayList<Object> objects = new ArrayList<>();
        for (Map<String, Object> map : exportDataDTO.getList()) {
            objects.add(JsonUtil.parseMapToObj(map, exportBizType.getType()));
        }
        // 创建临时文件，并对文件进行追加写
        this.exportExcelFile(objects, exportBizType.getType(), bizId, finish);

        // 传输完成后 把临时文件存储到正式路径 并返回路径信息
        return getExportResultDTO(fileName, bizId, finish, exportBizType, SUFFIX_XLSX);
    }


    @Override
    @Nullable
    public ExportResultDTO getExportResultDTO(String name, String bizId, Integer finish, ExportBizType exportBizType,
        String fileType) {
        if (finish == 1) {
            String fileName = bizId + fileType;

            String tmpPath = sysConfig.queryTempPath();
            String pscPath = sysConfig.queryTempPhysicalPath();
            FileUtil.mkdir(pscPath);
            File file = new File(pscPath + SEPARATOR + fileName);
            log.error("filePath:{}, fileName:{}, templateFile:{}, file:{}", tmpPath,
                fileName, tmpPath, file.getAbsolutePath());
            if (file.exists()) {
                UploadFileRequest uploadFileRequest = new UploadFileRequest();
                uploadFileRequest.setFileName(sysConfig.getExportPath(bizId, name, exportBizType) + fileType);
                uploadFileRequest.setTempFilePath(file.getAbsolutePath());
                uploadFileRequest.setCopyFolderFlag(false);
                uploadFileRequest.setFolderSrcPath(tmpPath);
                uploadFileRequest.setFolderTargetPath(null);
                uploadFileRequest.setAsyncUpload(false);
                uploadFileRequest.setDelTempFile(true);
                uploadFileRequest.setBizId(bizId);
                UploadFileResponse uploadFileResponse = uploadFileService.uploadFile(uploadFileRequest);
                String path = uploadFileResponse.getFileUrl();
                ExportResultDTO exportResultDTO = new ExportResultDTO();
                exportResultDTO.setUrl(path);
                exportResultDTO.setStatus(1);
                exportResultDTO.setBizId(bizId);
                exportResultDTO.setExportDate(new Date());
                //由于格力文件双写需要该临时文件，如果双写没有打开，则在此删除临时文件，否则在格力双写完再删除文件
                //@Link com.wunding.learn.file.service.impl.ObjectStorageCopyImpl.uploadOss
                try {
                    Files.delete(file.toPath());
                    log.info(DELETE_TEMP_FILE_SUCCESS_LOG);
                } catch (IOException e) {
                    log.error(DELETE_TEMP_FILE_FAIL_LOG, e);
                }
                return exportResultDTO;
            } else {
                throw new BusinessException(FileErrorNoEnum.EXPORT_DATA_ERROR);
            }
        }
        return null;
    }

    private void exportExcelFile(List<Object> list, Class<?> clazz, String taskId, Integer finish) {
        String redisKey = "export:" + taskId;

        String keyPrefix = ":lock";

        try {
            RedisLockUtil.acquire(redisKey + keyPrefix, 10 * 60, 10 * 60);

            List<Object> redisList = redisTemplate.opsForList().range(redisKey, 0, -1);

            long startTime = System.currentTimeMillis();
            long redisListRamSize = RamUsageEstimator.sizeOf(redisList);
            log.info("redisListRamSize:{},time:{}", redisListRamSize, System.currentTimeMillis() - startTime);
            // 50M 写一次
            boolean condition = redisListRamSize > (50 * 1024 * 1024) || finish == 1;
            if (!condition) {
                redisTemplate.opsForList().rightPushAll(redisKey, list);
                return;
            }

            if (redisList == null) {
                redisList = new ArrayList<>();
            }
            redisList.addAll(list);

            redisTemplate.delete(redisKey);

            String filePath = sysConfig.queryTempPhysicalPath() + SEPARATOR;
            FileUtil.mkdir(filePath);
            String fileName = taskId + SUFFIX_XLSX;
            File templateFile = new File(filePath, fileName);
            File destFile = new File(filePath, "temp" + fileName);
            log.info(LOG_FORMAT, filePath, fileName,
                templateFile.getAbsolutePath(), destFile.getAbsolutePath());
            if (templateFile.exists()) {
                EasyExcelFactory.write(destFile, clazz).needHead(false).withTemplate(templateFile).file(destFile)
                    .sheet("导出数据")
                    .doWrite(redisList);
            } else {
                EasyExcelFactory.write(filePath + fileName, clazz).registerWriteHandler(new I18nCellWriteHandler())
                    .sheet("导出数据")
                    .doWrite(redisList);
            }
            if (destFile.exists()) {
                try {
                    Files.delete(templateFile.toPath());
                    log.info(DELETE_TEMP_FILE_SUCCESS_LOG);
                } catch (IOException e) {
                    log.error(DELETE_TEMP_FILE_FAIL_LOG, e);
                }
                if (destFile.renameTo(templateFile)) {
                    log.info(RENAME_SUCCESS_LOG_FORMAT, destFile.getName(), templateFile.getName());
                }
            }
        } finally {
            RedisLockUtil.release(redisKey + keyPrefix);
        }

    }

    // 非固定模板导出excel
    @Override
    public ExportResultDTO exportNoEntity(ExportDataAndHeadDTO dto, String bizId,
        String fileName, Integer finish, Integer customerHandler, Boolean automaticMergeHead,
        ExportBizType exportBizType) {
        List<List<Object>> data = dto.getData();
        List<List<String>> head = dto.getHead();
        // 创建临时文件，并对文件进行追加写
        this.exportNoEntityExcelFile(head, data, bizId, customerHandler, automaticMergeHead);
        // 传输完成后 把临时文件存储到正式路径 并返回路径信息
        return getExportResultDTO(fileName, bizId, finish, exportBizType, SUFFIX_XLSX);
    }

    // 非固定模板导出excel
    @Override
    public ExportResultDTO exportRecordCustom(ExportData exportData, String bizId, String fileName, Integer finish,
        ExportBizType exportBizType) {
        List<List<Object>> data = exportData.getData();
        List<ExcelHead> head = exportData.getHead();
        log.info("exportRecordCustom,head:{}", head);
        // 创建临时文件，并对文件进行追加写
        this.exportRecordCustomExcelFile(head, data, bizId);
        //文件类型后缀
        String fileType = SUFFIX_XLSX;
        // 传输完成后 把临时文件存储到正式路径 并返回路径信息
        ExportResultDTO exportResultDTO = getExportResultDTO(fileName, bizId, finish, exportBizType, fileType);
        if (exportResultDTO != null) {
            return exportResultDTO;
        }
        return null;
    }

    private void exportNoEntityExcelFile(List<List<String>> head, List<List<Object>> data, String taskId,
        Integer customerHandler, Boolean automaticMergeHead) {
        String filePath = sysConfig.queryTempPhysicalPath() + SEPARATOR;
        FileUtil.mkdir(filePath);
        String fileName = taskId + SUFFIX_XLSX;
        File templateFile = new File(filePath, fileName);
        File destFile = new File(filePath, "temp" + fileName);
        log.info(LOG_FORMAT, filePath, fileName,
            templateFile.getAbsolutePath(), destFile.getAbsolutePath());
        if (templateFile.exists()) {
            EasyExcelFactory.write(destFile).needHead(false).withTemplate(templateFile).file(destFile).sheet("导出数据")
                .doWrite(data);
        } else {
            if (customerHandler != 0) {
                EasyExcelFactory.write(filePath + fileName)
                    .registerWriteHandler(new CustomCellWriteHandler(customerHandler))
                    .head(head).automaticMergeHead(automaticMergeHead).sheet("导出数据").doWrite(data);
            } else {
                EasyExcelFactory.write(filePath + fileName).registerWriteHandler(new TextCellWriteHandler()).head(head)
                    .automaticMergeHead(automaticMergeHead).sheet("导出数据").doWrite(data);
            }

        }
        if (destFile.exists()) {
            try {
                Files.delete(templateFile.toPath());
                log.info(DELETE_TEMP_FILE_SUCCESS_LOG);
            } catch (IOException e) {
                log.error(DELETE_TEMP_FILE_FAIL_LOG, e);
            }
            if (destFile.renameTo(templateFile)) {
                log.info(RENAME_SUCCESS_LOG_FORMAT, destFile.getName(), templateFile.getName());
            }
        }
    }

    private void exportRecordCustomExcelFile(List<ExcelHead> head, List<List<Object>> data, String taskId) {
        log.info("exportRecordCustomExcelFile,head:{}", head);
        String filePath = sysConfig.queryTempPhysicalPath() + SEPARATOR;
        FileUtil.mkdir(filePath);
        String fileName = taskId + SUFFIX_XLSX;
        File templateFile = new File(filePath, fileName);
        File destFile = new File(filePath, "temp" + fileName);

        log.info(LOG_FORMAT, filePath, fileName,
            templateFile.getAbsolutePath(), destFile.getAbsolutePath());
        if (templateFile.exists()) {
            EasyExcelFactory.write(templateFile).needHead(false).file(destFile).sheet("导出数据").doWrite(data);
        } else {
            //自定义导出策略
            HeadWriteHandler headWriteHandler = new HeadWriteHandler(head);
            log.info("headWriteHandler,head:{}", headWriteHandler.getHeadNameList());
            EasyExcelFactory.write(filePath + fileName).registerWriteHandler(headWriteHandler)
                .head(headWriteHandler.getHeadNameList()).automaticMergeHead(false).sheet("导出数据").doWrite(data);
        }
        if (destFile.exists()) {
            try {
                Files.delete(templateFile.toPath());
                log.info(DELETE_TEMP_FILE_SUCCESS_LOG);
            } catch (IOException e) {
                log.error(DELETE_TEMP_FILE_FAIL_LOG, e);
            }
            if (destFile.renameTo(templateFile)) {
                log.info(RENAME_SUCCESS_LOG_FORMAT, destFile.getName(), templateFile.getName());
            }
        }
    }

    @Override
    public ExportResultDTO exportByteToFile(byte[] bytes, String bizId, String fileName, String fileType,
        ExportBizType exportBizType) {
        ExportResultDTO exportResultDTO = new ExportResultDTO();

        //定义输出路径
        String filePath = sysConfig.queryTempPhysicalPath() + SEPARATOR;
        File outFile = new File(filePath, bizId + fileType);
        try (FileOutputStream fos = new FileOutputStream(outFile)) {
            fos.write(bytes);
            exportResultDTO = getExportResultDTO(fileName, bizId, 1, exportBizType, fileType);
        } catch (IOException ex) {
            log.error("发生异常", ex);
        }
        return exportResultDTO;
    }

    @Override
    public ExportResultDTO exportAssessReport(AssessReportData assessReportData, String bizId) {
        List<AssessReportExportDTO> assessReportExportDTOList = assessReportData.getList();
        //通用分类能力测评数据
        List<AssessReportExportDTO> universalDtoList = assessReportExportDTOList.stream()
            .filter(dto -> Objects.equals("universal", dto.getAbilityType())).collect(
                Collectors.toList());
        //专业分类能力测评数据
        List<AssessReportExportDTO> majorDtoList = assessReportExportDTOList.stream()
            .filter(dto -> Objects.equals("major", dto.getAbilityType())).collect(
                Collectors.toList());
        //输出excel示例代码
        //创建Excel工作簿
        Workbook wb = new Workbook();
        Worksheet sheet = wb.getWorksheets().get(0);
        Worksheet sheet1 = wb.getWorksheets().get(1);
        Worksheet sheet2 = wb.getWorksheets().get(2);
        wb.getWorksheets().remove(sheet1);
        wb.getWorksheets().remove(sheet2);

        //添加表格数据
        int initIndex = 21;

        sheet.getRange().get("A" + initIndex + ":H" + initIndex).merge();
        sheet.getRange().get("A" + initIndex + ":H" + initIndex).setValue("通用能力测评明细");
        sheet.getRange().get("A" + (initIndex + 1)).setValue("序号");
        sheet.getRange().get("B" + (initIndex + 1)).setValue("分类");
        sheet.getRange().get("C" + (initIndex + 1)).setValue("能力编码");
        sheet.getRange().get("D" + (initIndex + 1)).setValue("能力名称");
        sheet.getRange().get("E" + (initIndex + 1)).setValue("得分");
        sheet.getRange().get("F" + (initIndex + 1)).setValue("标准");
        sheet.getRange().get("G" + (initIndex + 1)).setValue("平均");
        sheet.getRange().get("H" + (initIndex + 1)).setValue("建议");

        int indexNum = 1;
        //填充通用能力数据
        for (AssessReportExportDTO universalDto : universalDtoList) {
            int rowIndex = initIndex + indexNum + 1;
            sheet.getRange().get("A" + rowIndex).setNumberValue(indexNum);
            sheet.getRange().get("B" + rowIndex).setValue(universalDto.getAbilityCategoryName());
            sheet.getRange().get("C" + rowIndex).setValue(universalDto.getAbilityCode());
            sheet.getRange().get("D" + rowIndex).setValue(universalDto.getAbilityName());
            sheet.getRange().get("E" + rowIndex).setNumberValue(universalDto.getScore().doubleValue());
            sheet.getRange().get("F" + rowIndex).setNumberValue(universalDto.getStandardValue().doubleValue());
            sheet.getRange().get("G" + rowIndex).setNumberValue(universalDto.getAverageValue().doubleValue());
            sheet.getRange().get("H" + rowIndex).setValue(universalDto.getSuggestion());
            indexNum++;
        }

        //添加通用能力雷达图表到表格
        Chart universalChart = sheet.getCharts().add(ExcelChartType.Radar);
        universalChart.setLeftColumn(1);
        universalChart.setTopRow(1);
        universalChart.setRightColumn(5);
        universalChart.setBottomRow(21);
        if (!universalDtoList.isEmpty()) {
            universalChart.setDataRange(
                sheet.getRange().get("D" + (initIndex + 1) + ":" + "G" + (initIndex + indexNum)));
        }
        universalChart.setSeriesDataFromRange(false);
        universalChart.setChartTitle("通用能力");
        universalChart.getChartTitleArea().isBold();
        universalChart.getChartTitleArea().setSize(12);
        universalChart.getPlotArea().getFill().setVisible(false);
        universalChart.getLegend().setPosition(LegendPositionType.Corner);

        //格式化表格数据
        sheet.getRange().get("A" + initIndex + ":H" + initIndex).getStyle().getFont().isBold(true);
        sheet.getRange().get("A" + initIndex + ":H" + (initIndex + indexNum)).getStyle().getFont().setSize(12);
        sheet.getRange().get("A" + initIndex + ":H" + initIndex).setRowHeight(17);
        sheet.getRange().get("A" + initIndex + ":H" + (initIndex + indexNum)).setColumnWidth(16);
        sheet.getRange().get("A" + initIndex + ":H" + initIndex).getStyle().setColor(new Color(176, 224, 230));
        sheet.getRange().get("A" + (initIndex + 1) + ":H" + (initIndex + 1)).getStyle()
            .setColor(new Color(232, 232, 232));
        sheet.getRange().get("A" + initIndex + ":H" + (initIndex + indexNum)).getStyle().setHorizontalAlignment(
            HorizontalAlignType.Center);

        //添加专业能力表头
        int majorInitIndex = initIndex + indexNum + 1;

        sheet.getRange().get("A" + majorInitIndex + ":H" + majorInitIndex).merge();
        sheet.getRange().get("A" + majorInitIndex + ":H" + majorInitIndex).setValue("专业能力测评明细");
        sheet.getRange().get("A" + (majorInitIndex + 1)).setValue("序号");
        sheet.getRange().get("B" + (majorInitIndex + 1)).setValue("分类");
        sheet.getRange().get("C" + (majorInitIndex + 1)).setValue("能力编码");
        sheet.getRange().get("D" + (majorInitIndex + 1)).setValue("能力名称");
        sheet.getRange().get("E" + (majorInitIndex + 1)).setValue("得分");
        sheet.getRange().get("F" + (majorInitIndex + 1)).setValue("标准");
        sheet.getRange().get("G" + (majorInitIndex + 1)).setValue("平均");
        sheet.getRange().get("H" + (majorInitIndex + 1)).setValue("建议");

        //填充专业能力数据
        indexNum = 1;

        for (AssessReportExportDTO majorDto : majorDtoList) {
            int rowIndex = majorInitIndex + indexNum + 1;
            sheet.getRange().get("A" + rowIndex).setNumberValue(indexNum);
            sheet.getRange().get("B" + rowIndex).setValue(majorDto.getAbilityCategoryName());
            sheet.getRange().get("C" + rowIndex).setValue(majorDto.getAbilityCode());
            sheet.getRange().get("D" + rowIndex).setValue(majorDto.getAbilityName());
            sheet.getRange().get("E" + rowIndex).setNumberValue(majorDto.getScore().doubleValue());
            sheet.getRange().get("F" + rowIndex).setNumberValue(majorDto.getStandardValue().doubleValue());
            sheet.getRange().get("G" + rowIndex).setNumberValue(majorDto.getAverageValue().doubleValue());
            sheet.getRange().get("H" + rowIndex).setValue(majorDto.getSuggestion());
            indexNum++;
        }

        //添加专业能力雷达图表到表格
        Chart majorChart = sheet.getCharts().add(ExcelChartType.Radar);
        majorChart.setLeftColumn(5);
        majorChart.setTopRow(1);
        majorChart.setRightColumn(9);
        majorChart.setBottomRow(21);
        if (!majorDtoList.isEmpty()) {
            majorChart.setDataRange(
                sheet.getRange().get("D" + (majorInitIndex + 1) + ":" + "G" + (majorInitIndex + indexNum)));
        }
        majorChart.setSeriesDataFromRange(false);
        majorChart.setChartTitle("专业能力");
        majorChart.getChartTitleArea().isBold();
        majorChart.getChartTitleArea().setSize(12);
        majorChart.getPlotArea().getFill().setVisible(false);
        majorChart.getLegend().setPosition(LegendPositionType.Corner);

        //格式化表格数据
        sheet.getRange().get("A" + majorInitIndex + ":H" + majorInitIndex).getStyle().getFont().isBold(true);
        sheet.getRange().get("A" + majorInitIndex + ":H" + (majorInitIndex + indexNum)).getStyle().getFont()
            .setSize(12);
        sheet.getRange().get("A" + majorInitIndex + ":H" + majorInitIndex).setRowHeight(17);
        sheet.getRange().get("A" + majorInitIndex + ":H" + (majorInitIndex + indexNum)).setColumnWidth(16);
        sheet.getRange().get("A" + majorInitIndex + ":H" + majorInitIndex).getStyle()
            .setColor(new Color(176, 224, 230));
        sheet.getRange().get("A" + (majorInitIndex + 1) + ":H" + (majorInitIndex + 1)).getStyle()
            .setColor(new Color(232, 232, 232));
        sheet.getRange().get("A" + majorInitIndex + ":H" + (majorInitIndex + indexNum)).getStyle()
            .setHorizontalAlignment(HorizontalAlignType.Center);

        String filePath = sysConfig.queryTempPhysicalPath() + SEPARATOR;
        String fileName = bizId + SUFFIX_XLSX;

        wb.saveToFile(filePath + fileName, ExcelVersion.Version2013);
        wb.dispose();
        // 传输完成后 把临时文件存储到正式路径 并返回路径信息
        //被测用户id
        String userId = assessReportData.getList().get(0).getUserId();
        UserDTO assessedUser = userFeign.getUserById(userId);

        return getExportResultDTO(assessedUser.getLoginName() + "_" + ExportFileNameEnum.ASSESS_REPORT.getType(),
            bizId, 1, ExportBizType.ASSESS_REPORT, SUFFIX_XLSX);
    }

}
