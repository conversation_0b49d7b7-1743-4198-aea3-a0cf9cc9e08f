package com.wunding.learn.file.config;

import com.wunding.learn.common.constant.tenant.TenantRedisKeyConstant;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.util.string.StringPool;
import com.wunding.learn.file.dto.TenantBucketInfo;
import java.io.File;
import java.time.Instant;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Executor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.hc.core5.http.ContentType;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.model.HeadObjectRequest;
import software.amazon.awssdk.services.s3.model.HeadObjectResponse;
import software.amazon.awssdk.services.s3.model.ObjectCannedACL;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.model.S3Exception;

/**
 * 初始化存储桶映射配置
 *
 * <AUTHOR>
 * @date 2023/03/20
 */
@Component
@Slf4j
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class InitBucketMapConfig implements InitializingBean {

    private final SysConfig sysConfig;
    private final Executor commonTaskThreadPool;


    private final RedisTemplate<String, Object> redisTemplate;

    private static final String LOGO_DEFAULT_IMAGE = "/file/logoDefaultImage/";
    private static final String HEAD_STYLE = "/file/homePage/img/headStyle/";
    private static final String SHOW_HEAD_STYLE = "/file/homePage/img/showHeadStyle/";
    private static final String QUICK_PROJECT_DEFAULT_IMAGE = "/file/quickProjectDefaultImage/";
    private static final String REFERENCE_TOPIC_IMAGE = "/file/referenceTopicImage/";
    private static final String SCORM_PLUGIN = "/file/scormPlugin/";
    private static final String MENU_ICON = "/file/menuIcon/";
    private static final String HOME_ROUTER_ICON = "/file/homeRouterIcon/";
    private static final String DEFAULT_ADS_IMAGE = "/file/defaultAdsImage/";

    @Value("${tenant.type}")
    private int tenantType;

    @Override
    @SuppressWarnings("unchecked")
    public void afterPropertiesSet() {
        Map<String, String> bucketSources = redisTemplate.<String, String>opsForHash()
            .entries(TenantRedisKeyConstant.BUCKET_KEY);
        if (log.isDebugEnabled()) {
            log.debug("all_bucket_source_map:{}", bucketSources);
        }
        switch (tenantType) {
            case -1:
                // 单租户部署
            case 1:
                handlerSingleTenantBucket(bucketSources);
                break;
            case 2:
                // TODO 不同租户部署方式
                break;
            default:
                break;
        }

        bucketSources.forEach((key, value) -> {
            if (StringUtils.isBlank(value)) {
                return;
            }
            UserThreadContext.setTenantId(key);
            // 初始化 租户id->桶 映射
            if (!value.startsWith("{")) {
                sysConfig.addTenantBucket(key, value);
            } else {
                TenantBucketInfo tenantBucketInfo = JsonUtil.jsonToObj(value, TenantBucketInfo.class);
                if (tenantBucketInfo == null) {
                    return;
                }
                sysConfig.addTenantBucketInfo(key, tenantBucketInfo);
            }

            // 初始化模板那些东西
            initTemplate();
        });
    }

    /**
     * 单租户部署,直接从配置文件中读取桶名
     *
     * @param bucketSources 桶的 map
     */
    private void handlerSingleTenantBucket(Map<String, String> bucketSources) {
        if (bucketSources.isEmpty()) {
            TenantBucketInfo tenantBucketInfo = new TenantBucketInfo();
            tenantBucketInfo.setAccessKey(sysConfig.getAccessKey());
            tenantBucketInfo.setRegion(sysConfig.getRegion());
            tenantBucketInfo.setType(sysConfig.getType());
            tenantBucketInfo.setIntranetEndPoint(sysConfig.getIntranetEndPoint());
            tenantBucketInfo.setEndPoint(sysConfig.getEndPoint());
            tenantBucketInfo.setStaticBaseUrl(sysConfig.getStaticBaseUrl());
            tenantBucketInfo.setSecretKey(sysConfig.getSecretKey());
            tenantBucketInfo.setPathMode(sysConfig.getPathMode());
            tenantBucketInfo.setRootPath("default");
            bucketSources.put("default", JsonUtil.objToJson(tenantBucketInfo));
        }
    }


    /**
     * 初始化模板
     */
    public void initTemplate() {
        commonTaskThreadPool.execute(() -> {
            String bucketName = sysConfig.getBucketName();
            // 初始化导入模板
            initImportTemplate(bucketName);

            // 初始化闯关图片
            initPromotedGameImage(bucketName);

            // 初始化首页DIY默认图
            initHeadStyle(bucketName);

            // 初始化首页DIY展示图
            initShowHeadStyle(bucketName);

            // 初始化快速培训默认图
            initQuickProjectDefaultImage(bucketName);

            // 初始化logo默认图
            initLogoDefaultImage(bucketName);

            // 初始化考试竞赛主题参考图
            initReferenceTopicImage(bucketName);

            // 初始化scormPlugin
            initScormPlugin(bucketName);

            // 初始化菜单图标
            initMenuIcon(bucketName);

            // 初始化首页常用功能路由图标
            initHomeRouterIcon(bucketName);

            // 初始化默认广告图
            initDefaultAdsImage(bucketName);

            // 初始化培训项目模板
            initTrainTemplate(bucketName);
        });
    }

    @SuppressWarnings("java:S3776")
    private void initScormPlugin(String bucketName) {
        File scormPluginDir = new File("/app/scormPlugin");
        if (!scormPluginDir.exists() || !scormPluginDir.isDirectory()) {
            return;
        }
        for (File file : Objects.requireNonNull(scormPluginDir.listFiles())) {
            if (file.isDirectory()) {
                for (File file1 : Objects.requireNonNull(file.listFiles())) {
                    if (checkFileNotExists(
                        SysConfig.getRootPath() + SCORM_PLUGIN + file.getName() + "/" + file1.getName(), bucketName)) {
                        String key = SysConfig.getRootPath() + SCORM_PLUGIN + file.getName() + "/" + file1.getName();
                        try {
                            PutObjectRequest putRequest = PutObjectRequest.builder()
                                .bucket(bucketName)
                                .key(key)
                                .contentType("application/javascript; charset=utf-8")
                                .acl(ObjectCannedACL.PUBLIC_READ)
                                .build();
                            SysConfig.getS3Client().putObject(putRequest, RequestBody.fromFile(file1));
                        } catch (Exception e) {
                            log.error("scormPlugin上传失败", e);
                        }
                    }
                }
            } else {
                if (checkFileNotExists(
                    SysConfig.getRootPath() + SCORM_PLUGIN
                        + file.getName(), bucketName)) {
                    try {
                        PutObjectRequest putRequest = PutObjectRequest.builder()
                            .bucket(bucketName)
                            .key(SysConfig.getRootPath() + SCORM_PLUGIN + file.getName())
                            .contentType(ContentType.TEXT_HTML.getMimeType())
                            .acl(ObjectCannedACL.PUBLIC_READ) // 设置成公有读
                            .build();
                        SysConfig.getS3Client().putObject(putRequest, RequestBody.fromFile(file));
                    } catch (Exception e) {
                        log.error("scormPlugin上传失败", e);
                    }
                }
            }
        }
        // 初始化重走长征路图片
        initMarchImage(bucketName);
    }

    private void initReferenceTopicImage(String bucketName) {
        File topicReferenceImage = new File("/app/referenceTopicImage");
        if (!topicReferenceImage.exists() || !topicReferenceImage.isDirectory()) {
            return;
        }
        for (File file : Objects.requireNonNull(topicReferenceImage.listFiles())) {
            if (!file.isDirectory()) {
                continue;
            }
            for (File image : Objects.requireNonNull(file.listFiles())) {
                if (checkFileNotExists(
                    SysConfig.getRootPath() + REFERENCE_TOPIC_IMAGE
                        + file.getName() + "/" + image.getName(), bucketName)) {
                    try {
                        PutObjectRequest putRequest = PutObjectRequest.builder()
                            .bucket(bucketName)
                            .key(SysConfig.getRootPath() + REFERENCE_TOPIC_IMAGE + file.getName() + "/"
                                + image.getName())
                            .build();
                        SysConfig.getS3Client().putObject(putRequest, RequestBody.fromFile(image));
                    } catch (Exception e) {
                        log.error("主题参考图上传失败", e);
                    }
                }
            }
        }
    }

    private void initLogoDefaultImage(String bucketName) {
        File logoDefaultImage = new File("/app/logoDefaultImage");
        if (!logoDefaultImage.exists() || !logoDefaultImage.isDirectory()) {
            return;
        }
        for (File image : Objects.requireNonNull(logoDefaultImage.listFiles())) {
            if (checkFileNotExists(
                SysConfig.getRootPath() + LOGO_DEFAULT_IMAGE
                    + image.getName(), bucketName)) {
                try {
                    PutObjectRequest putRequest = PutObjectRequest.builder()
                        .bucket(bucketName)
                        .key(SysConfig.getRootPath() + LOGO_DEFAULT_IMAGE + image.getName())
                        .acl(ObjectCannedACL.PUBLIC_READ)
                        .build();
                    SysConfig.getS3Client().putObject(putRequest, RequestBody.fromFile(image));
                } catch (Exception e) {
                    log.error("logo默认图上传失败", e);
                }
            }
        }
    }

    private void initQuickProjectDefaultImage(String bucketName) {
        File quickProjectDefaultImg = new File("/app/quickProjectDefaultImage");
        if (!quickProjectDefaultImg.exists() || !quickProjectDefaultImg.isDirectory()) {
            return;
        }
        for (File image : Objects.requireNonNull(quickProjectDefaultImg.listFiles())) {
            if (checkFileNotExists(
                SysConfig.getRootPath() + QUICK_PROJECT_DEFAULT_IMAGE
                    + image.getName(), bucketName)) {
                try {
                    PutObjectRequest putRequest = PutObjectRequest.builder()
                        .bucket(bucketName)
                        .key(SysConfig.getRootPath() + QUICK_PROJECT_DEFAULT_IMAGE + image.getName())
                        .build();
                    SysConfig.getS3Client().putObject(putRequest, RequestBody.fromFile(image));
                } catch (Exception e) {
                    log.error("快速培训默认图上传失败", e);
                }
            }
        }

    }

    private void initShowHeadStyle(String bucketName) {
        File showHomePage = new File("/app/homePage/img/showHeadStyle");
        if (!showHomePage.exists() || !showHomePage.isDirectory()) {
            return;
        }
        for (File image : Objects.requireNonNull(showHomePage.listFiles())) {
            if (checkFileNotExists(
                SysConfig.getRootPath() + SHOW_HEAD_STYLE
                    + image.getName(), bucketName)) {
                try {
                    PutObjectRequest putRequest = PutObjectRequest.builder()
                        .bucket(bucketName)
                        .key(SysConfig.getRootPath() + SHOW_HEAD_STYLE + image.getName())
                        .build();
                    SysConfig.getS3Client().putObject(putRequest, RequestBody.fromFile(image));
                } catch (Exception e) {
                    log.error("首页DIY展示图上传失败" + image.getAbsolutePath(), e);
                }
            }
        }

    }

    private void initHeadStyle(String bucketName) {
        File homePage = new File("/app/homePage/img/headStyle");
        if (!homePage.exists() || !homePage.isDirectory()) {
            return;
        }
        for (File image : Objects.requireNonNull(homePage.listFiles())) {
            if (checkFileNotExists(
                SysConfig.getRootPath() + HEAD_STYLE
                    + image.getName(), bucketName)) {
                try {
                    PutObjectRequest putRequest = PutObjectRequest.builder()
                        .bucket(bucketName)
                        .key(SysConfig.getRootPath() + HEAD_STYLE + image.getName())
                        .build();
                    SysConfig.getS3Client().putObject(putRequest, RequestBody.fromFile(image));
                } catch (Exception e) {
                    log.error("首页DIY默认图上传失败" + image.getAbsolutePath(), e);
                }
            }
        }

    }

    private void initPromotedGameImage(String bucketName) {
        File promotedGameImage = new File("/app/promotedGameImage");
        if (!promotedGameImage.exists() || !promotedGameImage.isDirectory()) {
            return;
        }
        for (File file : Objects.requireNonNull(promotedGameImage.listFiles())) {
            if (!file.isDirectory()) {
                continue;
            }
            for (File image : Objects.requireNonNull(file.listFiles())) {
                if (checkFileNotExists(
                    SysConfig.getRootPath() + "/file/emigratedgameImage/"
                        + file.getName() + "/" + image.getName(), bucketName)) {
                    try {
                        PutObjectRequest putRequest = PutObjectRequest.builder()
                            .bucket(bucketName)
                            .key(SysConfig.getRootPath() + "/file/emigratedgameImage/" + file.getName() + "/"
                                + image.getName())
                            .build();
                        SysConfig.getS3Client().putObject(putRequest, RequestBody.fromFile(image));
                    } catch (Exception e) {
                        log.error("闯关默认图上传失败", e);
                    }
                }
            }
        }
    }

    private void initImportTemplate(String bucketName) {
        File importTemplateDir = new File("/app/importTemplate");
        if (!importTemplateDir.exists() || !importTemplateDir.isDirectory()) {
            return;
        }
        String uploadFilePath = "/file/importTemplate/";
        for (File file : Objects.requireNonNull(importTemplateDir.listFiles())) {
            if (file.isFile()) {
                // 文件处理
                handleIsFile(bucketName, file, uploadFilePath);
            } else if (file.isDirectory()) {
                // 文件夹内的文件夹
                handleIsDir(bucketName, file, uploadFilePath);
            }
        }

    }

    private void handleIsDir(String bucketName, File file, String uploadFilePath) {
        for (File f : Objects.requireNonNull(file.listFiles())) {
            if (checkFileNotExists(
                SysConfig.getRootPath() + uploadFilePath
                    + file.getName() + StringPool.SLASH + f.getName(), bucketName)) {
                try {
                    PutObjectRequest putRequest = PutObjectRequest.builder()
                        .bucket(bucketName)
                        .key(SysConfig.getRootPath() + uploadFilePath + file.getName() + StringPool.SLASH + f.getName())
                        .build();
                    SysConfig.getS3Client().putObject(putRequest, RequestBody.fromFile(f));
                } catch (Exception e) {
                    log.error("handleIsDir 导入模板上传失败", e);
                }
            }
        }
    }

    private void handleIsFile(String bucketName, File file, String uploadFilePath) {
        if (checkFileNotExists(
            SysConfig.getRootPath() + uploadFilePath
                + file.getName(), bucketName)) {
            try {
                PutObjectRequest putRequest = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(SysConfig.getRootPath() + uploadFilePath + file.getName())
                    .build();
                SysConfig.getS3Client().putObject(putRequest, RequestBody.fromFile(file));
                log.info("handleIsFile bucketName:{},fileName:{},uploadFilePath:{}", bucketName, file.getName(),
                    uploadFilePath);
            } catch (Exception e) {
                log.error("handleIsFile 导入模板上传失败", e);
            }
        }
    }

    private void initMarchImage(String bucketName) {
        File quickProjectDefaultImg = new File("/app/marchImage");
        if (!quickProjectDefaultImg.exists() || !quickProjectDefaultImg.isDirectory()) {
            return;
        }
        for (File image : Objects.requireNonNull(quickProjectDefaultImg.listFiles())) {
            if (checkFileNotExists(
                SysConfig.getRootPath() + "/file/marchImage/"
                    + image.getName(), bucketName)) {
                try {
                    PutObjectRequest putRequest = PutObjectRequest.builder()
                        .bucket(bucketName)
                        .key(SysConfig.getRootPath() + "/file/marchImage/" + image.getName())
                        .build();
                    SysConfig.getS3Client().putObject(putRequest, RequestBody.fromFile(image));
                } catch (Exception e) {
                    log.error("重走长征路默认图上传失败", e);
                }
            }
        }
    }

    /**
     * 检查文件不存在
     *
     * @param key        文件名
     * @param bucketName 存储桶
     * @return boolean
     */
    private boolean checkFileNotExists(String key, String bucketName) {
        try {
            HeadObjectRequest headObjectRequest = HeadObjectRequest.builder()
                .bucket(bucketName)
                .key(key)
                .build();
            HeadObjectResponse response = SysConfig.getS3Client().headObject(headObjectRequest);

            long s3ContentLength = response.contentLength();
            Instant s3LastModified = response.lastModified();
            String filePath = sysConfig.getPhysicalPath(bucketName + StringPool.SLASH + key);
            File file = new File(filePath);
            return s3ContentLength <= 0
                || s3ContentLength != file.length()
                || s3LastModified.toEpochMilli() != file.lastModified();
        } catch (S3Exception e) {
            return true;
        }
    }

    private void initMenuIcon(String bucketName) {
        File logoDefaultImage = new File("/app/menuIcon");
        if (!logoDefaultImage.exists() || !logoDefaultImage.isDirectory()) {
            return;
        }
        for (File image : Objects.requireNonNull(logoDefaultImage.listFiles())) {
            if (checkFileNotExists(
                SysConfig.getRootPath() + MENU_ICON
                    + image.getName(), bucketName)) {
                try {
                    PutObjectRequest putRequest = PutObjectRequest.builder()
                        .bucket(bucketName)
                        .key(SysConfig.getRootPath() + MENU_ICON + image.getName())
                        .build();
                    SysConfig.getS3Client().putObject(putRequest, RequestBody.fromFile(image));
                } catch (Exception e) {
                    log.error("logo默认图上传失败", e);
                }
            }
        }
    }

    private void initHomeRouterIcon(String bucketName) {
        File file = new File("/app/homeRouterIcon");
        if (!file.exists() || !file.isDirectory()) {
            return;
        }
        for (File image : Objects.requireNonNull(file.listFiles())) {
            if (checkFileNotExists(
                SysConfig.getRootPath() + HOME_ROUTER_ICON
                    + image.getName(), bucketName)) {
                try {
                    PutObjectRequest putRequest = PutObjectRequest.builder()
                        .bucket(bucketName)
                        .key(SysConfig.getRootPath() + HOME_ROUTER_ICON + image.getName())
                        .build();
                    SysConfig.getS3Client().putObject(putRequest, RequestBody.fromFile(image));
                } catch (Exception e) {
                    log.error("常用功能路由默认图上传失败", e);
                }
            }
        }
    }

    private void initDefaultAdsImage(String bucketName) {
        File file = new File("/app/defaultAdsImage");
        if (!file.exists() || !file.isDirectory()) {
            return;
        }
        for (File image : Objects.requireNonNull(file.listFiles())) {
            if (checkFileNotExists(
                SysConfig.getRootPath() + DEFAULT_ADS_IMAGE
                    + image.getName(), bucketName)) {
                try {
                    PutObjectRequest putRequest = PutObjectRequest.builder()
                        .bucket(bucketName)
                        .key(SysConfig.getRootPath() + DEFAULT_ADS_IMAGE + image.getName())
                        .build();
                    SysConfig.getS3Client().putObject(putRequest, RequestBody.fromFile(image));
                } catch (Exception e) {
                    log.error("默认广告图上传失败", e);
                }
            }
        }
    }

    private void initTrainTemplate(String bucketName) {
        File baseDir = new File("/app/trainTemplate");
        if (!baseDir.exists() || !baseDir.isDirectory()) {
            return;
        }
        try {
            uploadMultilevelFile(bucketName, baseDir, "/file/trainTemplate");
        } catch (Exception e) {
            log.error("trainTemplate上传失败", e);
        }
    }

    private void uploadMultilevelFile(String bucketName, File baseDir, String parentFileName) {
        for (File file : Objects.requireNonNull(baseDir.listFiles())) {
            if (file.isDirectory()) {
                uploadMultilevelFile(bucketName, file, parentFileName + "/" + file.getName());
            } else if (checkFileNotExists(SysConfig.getRootPath() + parentFileName + file.getName(), bucketName)) {
                try {
                    PutObjectRequest putRequest = PutObjectRequest.builder()
                        .bucket(bucketName)
                        .key(SysConfig.getRootPath() + parentFileName + "/" + file.getName())
                        .build();
                    SysConfig.getS3Client().putObject(putRequest, RequestBody.fromFile(file));
                } catch (Exception e) {
                    log.error("trainTemplate上传失败");
                }
            }
        }
    }
}
