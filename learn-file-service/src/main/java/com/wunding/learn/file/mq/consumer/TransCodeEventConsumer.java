package com.wunding.learn.file.mq.consumer;

import com.rabbitmq.client.Channel;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.file.TranscodeStatusEnum;
import com.wunding.learn.common.mq.event.TransCodeEvent;
import com.wunding.learn.common.mq.event.TransCodeFinishEvent;
import com.wunding.learn.common.mq.service.MqProducer;
import com.wunding.learn.common.mq.util.ConsumerAckUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.file.trans.handler.TransServiceHandler;
import com.wunding.learn.file.trans.pdf.pojo.TransCodeResult;
import jakarta.annotation.Resource;
import java.io.IOException;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

/**
 * 转码事件消费者
 *
 * <AUTHOR> href="mailto:<EMAIL>">yuxueyi</a>
 * @version V7.0.0
 * @since 2022/5/25  13:42
 */
@Component
@Slf4j
public class TransCodeEventConsumer {

    @Resource
    private TransServiceHandler transServiceHandler;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Resource
    private MqProducer mqProducer;

    @Value("${app.transRetry:1}")
    private Integer transRetry;

    @Value("${app.maxRetryPeriod:7}")
    private Integer maxRetryPeriod;


    private static final String TRANS_CODE_EVENT_CONSUMER_QUEUE = "transCodeEventConsumerQueue";

    private static final String REDIS_KEY_PREFIX = "transcode:processed:";

    private static final String MAINTENANCE_KEY = "sys:maintenance:gap";

    private String acquireRedisKey(String businessId) {
        return REDIS_KEY_PREFIX + UserThreadContext.getTenantId() + ":" + businessId;
    }

    /**
     * 资源创建时，同步生成方案以及方案对应的用户
     * <pre>
        需要处理 普通转码存在的消息，在优先队列中处理时，二者的排他性
     *      * 1 、 消息状态检查
     *      * 2 、 业务幂等性处理【一定时间窗口内已完成的不再额外处理，超过则可以继续转码，如超过一天内的可以继续转码】
*      </pre>
     */
    @RabbitListener(
        concurrency = "${app.transConcurrency:1-2}",
        bindings =
        @QueueBinding(
            value = @Queue(value = TRANS_CODE_EVENT_CONSUMER_QUEUE),
            exchange = @Exchange(value = TransCodeEvent.CHANGE, type = ExchangeTypes.TOPIC),
            key = TransCodeEvent.ROUTING_KEY
        ), id = "transCodeEventConsumer")
    public void transCodeEventConsumer(
        @Payload TransCodeEvent transCodeEvent,
        @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
        Channel channel) throws IOException {
        log.info("TransServiceHandler receive transCodeEvent :{}", JsonUtil.objToJson(transCodeEvent));

        UserThreadContext.setTenantId(transCodeEvent.getTenantId());

        //执行转码
        try {

            String redisKey = acquireRedisKey(transCodeEvent.getBizId());
            // 1. 检查 Redis 中是否已处理 - 优先队列已经先处理了 。
            if ("PROCESSING".equals(redisTemplate.opsForValue().get(redisKey))) {
                log.warn("消息已在优先队列处理，跳过 BizId:{}", transCodeEvent.getBizId());
                ConsumerAckUtil.basicAck(transCodeEvent, channel, deliveryTag, false);
                return;
            }

            // 部署窗口期间，控制转码消息，暂停处理
            if ("true".equalsIgnoreCase(redisTemplate.opsForValue().get(MAINTENANCE_KEY))) {
                log.error("系统维护中，暂停转码处理 tenantId:{}, BizId:{}", transCodeEvent.getTenantId(), transCodeEvent.getBizId());
                ConsumerAckUtil.basicNack(transCodeEvent, channel, deliveryTag, false, true);
                UserThreadContext.remove();
                return;
            }

            noticeBiz(transCodeEvent, TranscodeStatusEnum.TRANSFORMING);
            TransCodeResult transCodeResult = transServiceHandler.trans(transCodeEvent);
            log.info("transCodeResult:{}", JsonUtil.objToJson(transCodeResult));
            if ("RETRY".equals(transCodeResult.getErrorMessage())) {
                log.error("转码异常连接问题 , 重入队列延迟重试.");
                Thread.sleep(30000);
                ConsumerAckUtil.basicNack(transCodeEvent, channel, deliveryTag, false, true);
                UserThreadContext.remove();
            }
        } catch (InterruptedException e){
            Thread.currentThread().interrupt();
            log.error("转码异常，线程中断--", e);
        }catch (Exception e) {
            log.error("转码异常，转码信息:BizId:{},Path:{},BizType:{},tenantId:{}-->>",
                transCodeEvent.getBizId(), transCodeEvent.getPath(), transCodeEvent.getBizType(), transCodeEvent.getTenantId(),
                e);

            // 获取重试次数
            int retryCount = 0;
            // 基于 Redis 存储重试次数
            String retryKey = "transcode:retry_count:" + transCodeEvent.getId();
            String retryStr = redisTemplate.opsForValue().get(retryKey);
            if (retryStr != null) {
                retryCount = Integer.parseInt(retryStr);
            }

            if (retryCount < transRetry) {
                try {
                    // 重新入队，改为排队中的状态
                    noticeBiz(transCodeEvent, TranscodeStatusEnum.PENDING);
                    ConsumerAckUtil.basicNack(transCodeEvent, channel, deliveryTag, false, true);
                    redisTemplate.opsForValue().set(retryKey, String.valueOf(retryCount + 1), maxRetryPeriod, TimeUnit.DAYS);
                } catch (IOException ex) {
                    log.error("转码失败，重试发送 basicNack 失败--", ex);
                }
            } else {
                try {
                    // 一次重试的机会，则不再处理，直接失败了。
                    ConsumerAckUtil.basicAck(transCodeEvent, channel, deliveryTag, false);
                } catch (IOException ex) {
                    log.error("转码失败，拒绝消息失败--", ex);
                }
            }
        }
        UserThreadContext.remove();
        //手动ack确认
        ConsumerAckUtil.basicAck(transCodeEvent, channel, deliveryTag, false);
    }

    private void noticeBiz(TransCodeEvent transCodeEvent, TranscodeStatusEnum transcodeStatusEnum) {
        TransCodeFinishEvent finishEvent = new TransCodeFinishEvent(
            transCodeEvent.getBizType(),
            transCodeEvent.getBizId(),
            transcodeStatusEnum.value,
            transCodeEvent.getMime(),
            transCodeEvent.getCwLibId()
        );
        mqProducer.sendMsgNoTransaction(finishEvent);
    }
}
