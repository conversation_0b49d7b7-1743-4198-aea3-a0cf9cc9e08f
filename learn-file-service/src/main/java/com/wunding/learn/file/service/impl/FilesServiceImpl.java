package com.wunding.learn.file.service.impl;

import static com.wunding.learn.common.constant.file.FileErrorNoEnum.ERR_CER_BACKGROUND_IMAGE_NOT_FOUND;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ListenableFuture;
import com.wunding.learn.common.constant.excel.ExcelTitleBaseCheckUtil;
import com.wunding.learn.common.constant.file.FileErrorNoEnum;
import com.wunding.learn.common.enums.file.CWTypeEnum;
import com.wunding.learn.common.enums.file.ImageTypeEnum;
import com.wunding.learn.common.enums.file.ImportExcelTitleEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.util.date.DateUtil;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.file.api.constant.FileBizType;
import com.wunding.learn.file.api.dto.FileListDTO;
import com.wunding.learn.file.api.dto.ImportDataDTO;
import com.wunding.learn.file.api.dto.MimeInfoDTO;
import com.wunding.learn.file.api.dto.TempFileDTO;
import com.wunding.learn.file.api.query.FileQuery;
import com.wunding.learn.file.api.service.FileFeign;
import com.wunding.learn.file.api.service.ImportDataFeign;
import com.wunding.learn.file.config.SysConfig;
import com.wunding.learn.file.constant.FileTypeEnum;
import com.wunding.learn.file.dto.HomeWorkUploadZipResultDTO;
import com.wunding.learn.file.dto.RelateWaterMarkImgDTO;
import com.wunding.learn.file.dto.ScormInfoDTO;
import com.wunding.learn.file.dto.UploadCourseResultDTO;
import com.wunding.learn.file.dto.UploadResultDTO;
import com.wunding.learn.file.dto.UploadZipResultDTO;
import com.wunding.learn.file.mapper.FilesMapper;
import com.wunding.learn.file.model.Files;
import com.wunding.learn.file.model.UploadFileRequest;
import com.wunding.learn.file.query.ImportExcelQuery;
import com.wunding.learn.file.service.IFilesService;
import com.wunding.learn.file.service.UploadFileService;
import com.wunding.learn.file.trans.grpc.lib.FileParseInfo;
import com.wunding.learn.file.trans.grpc.lib.FileParseInfoParams;
import com.wunding.learn.file.trans.grpc.lib.InfoServiceGrpc.InfoServiceFutureStub;
import com.wunding.learn.file.trans.grpc.lib.TransServiceGrpc.TransServiceBlockingStub;
import com.wunding.learn.file.trans.grpc.lib.TransWaterMarkImageReply;
import com.wunding.learn.file.trans.grpc.lib.TransWaterMarkImageRequest;
import com.wunding.learn.file.util.FileUtil;
import com.wunding.learn.file.util.MimeUtil;
import com.wunding.learn.file.util.UrlHelper;
import com.wunding.learn.file.util.WordFileReplaceText;
import com.wunding.learn.file.util.XmlUtil;
import com.wunding.learn.file.util.ZipUtil;
import jakarta.annotation.Nullable;
import jakarta.annotation.Resource;
import java.awt.Color;
import java.awt.Font;
import java.awt.FontMetrics;
import java.awt.Graphics2D;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.attribute.BasicFileAttributes;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import javax.imageio.ImageIO;
import lombok.extern.slf4j.Slf4j;
import net.devh.boot.grpc.client.inject.GrpcClient;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ooxml.POIXMLDocument;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">xiao8</a>
 * @since 2022-03-02
 */
@Slf4j
@Service("filesService")
public class FilesServiceImpl extends ServiceImpl<FilesMapper, Files> implements IFilesService {

    /**
     * 证书关联文件后缀
     */
    protected static final String[] CERT_RELATE_FILE_SUFFIX = {"png", "jpg", "jpeg", "xlsx"};
    protected static final String[] HOME_WORK_FILE_SUFFIX = {"png", "jpg", "jpeg", "xlsx", "mp3", "mp4", "xls", "pdf",
        "doc", "docx", "ppt", "pptx"};
    private static final String SAVE_UPLOAD_FILE_ERROR = "保存上传文件失败";
    private static final String SEPARATOR = "/";
    private static final String TITLE = "title";
    private static final String[] IMG_SUFFIX = {"png", "jpg", "jpeg"};
    public static final String PARSE_INFO_BY_FFMPEG_ERROR = "infoServiceFutureStub.getFileParseInfoByFFmpeg error";


    public static void generateText(RelateWaterMarkImgDTO relateWaterMarkImgDTO, BufferedImage image, Graphics2D g) {
        String textZW = String.format("%s于%s参加了%s《%s》，特颁发此证书。", relateWaterMarkImgDTO.getUserName(),
            relateWaterMarkImgDTO.getCreateTime(), relateWaterMarkImgDTO.getCategoryType(),
            relateWaterMarkImgDTO.getContentName());
        String text1 = "获证人: " + relateWaterMarkImgDTO.getUserName();
        String text2 = "颁发单位: " + relateWaterMarkImgDTO.getUserOrgan();
        String text3 = "发证日期: " + relateWaterMarkImgDTO.getCreateTime();
        String text5 = "有效期: " + relateWaterMarkImgDTO.getValidPeriod();
        //截取换行
        int lineCount = 18;
        int x = 190;
        int y = 587;
        int lineHeight = 56;
        for (int start = 0; start < textZW.length(); start = start + lineCount) {
            if (start == 0) {
                addTextWaterMark(image, Color.RED, new Font("微软雅黑", Font.ITALIC, 42),
                    StringUtils.substring(textZW, start, lineCount), 218, 531, g);
            } else {
                addTextWaterMark(image, Color.RED, new Font("微软雅黑", Font.ITALIC, 42),
                    StringUtils.substring(textZW, start, start + lineCount), x, y, g);
                y += lineHeight;
            }
        }
        addTextWaterMark(image, Color.BLACK, new Font("微软雅黑", Font.ITALIC, 28), text1, 605, 1024, g);
        addTextWaterMark(image, Color.BLACK, new Font("微软雅黑", Font.ITALIC, 28), text2, 605, 1067, g);
        addTextWaterMark(image, Color.BLACK, new Font("微软雅黑", Font.ITALIC, 28), text3, 605, 1109, g);
        addTextWaterMark(image, Color.BLACK, new Font("微软雅黑", Font.ITALIC, 28), text5, 605, 1151, g);

        // 证书名称
        String text4 = relateWaterMarkImgDTO.getCerName();
        Font font = new Font("微软雅黑", Font.ITALIC, 60);
        // 计算文字长度，计算居中的x点坐标
        FontMetrics fm = g.getFontMetrics(font);
        int textWidth = fm.stringWidth(text4);
        int widthX = (image.getWidth() - textWidth) / 2;
        addTextWaterMark(image, Color.RED, font, text4, widthX, 240, g);
    }

    public static void addTextWaterMark(BufferedImage targetImg, Color textColor, Font font, String text, int x, int y,
        Graphics2D g) {
        g.drawImage(targetImg, 0, 0, targetImg.getWidth(), targetImg.getHeight(), null);
        g.setColor(textColor); // 水印颜色
        g.setFont(font);
        g.drawString(text, x, y);
    }


    @Resource
    private SysConfig sysConfig;
    @Resource
    private UrlHelper urlHelper;
    @Resource
    private UploadFileService uploadFileService;
    @Lazy
    @Resource
    private FileFeign fileFeign;
    @Resource
    private ImportDataFeign importDataFeign;


    @GrpcClient("learn-trans-server")
    private InfoServiceFutureStub infoServiceFutureStub;

    @GrpcClient("learn-trans-server")
    private TransServiceBlockingStub transServiceBlockingStub;

    @Override
    public UploadResultDTO saveUploadFile(MultipartFile uploadFile, String name) {
        UploadResultDTO result = new UploadResultDTO();
        result.setFileName(name);
        String suffix = FilenameUtils.getExtension(name);
        String fileId = StringUtil.newId();
        String fileName = fileId + "." + suffix;
        String tmpPath = sysConfig.queryTempPath();
        String pscPath = sysConfig.queryTempPhysicalPath();
        FileUtil.mkdir(pscPath);
        File file = new File(pscPath + SEPARATOR + fileName);
        try {
            uploadFile.transferTo(file);
            result.setPath(FilenameUtils.separatorsToUnix(tmpPath.concat("/").concat(fileName)));
            result.setUrl(urlHelper.getStaticFullUrl(result.getPath()));
            if (ArrayUtils.contains(IMG_SUFFIX, suffix.toLowerCase())) {
                BufferedImage bufferedImage = ImageIO.read(file);
                if (Objects.isNull(bufferedImage)) {
                    throw new BusinessException(FileErrorNoEnum.IMAGE_FILE_ERROR);
                }
                result.setHeight(bufferedImage.getHeight());
                result.setWidth(bufferedImage.getWidth());
            }
            UploadFileRequest uploadFileRequest = new UploadFileRequest();
            uploadFileRequest.setFileName(result.getPath());
            uploadFileRequest.setAsyncUpload(false);
            uploadFileRequest.setTempFilePath(file.getAbsolutePath());
            uploadFileRequest.setCopyFolderFlag(false);
            uploadFileRequest.setBizId(fileId);
            uploadFileService.uploadFile(uploadFileRequest);
        } catch (IOException | IllegalStateException e) {
            log.error(SAVE_UPLOAD_FILE_ERROR, e);
            throw new BusinessException(FileErrorNoEnum.SAVE_UPLOAD_FILE_ERROR);
        }
        return result;
    }

    @Override
    public boolean checkFileType(String originalFilename, String[] types) {
        String suffix = FilenameUtils.getExtension(originalFilename);
        if (null == suffix || !ArrayUtils.contains(types, suffix.toLowerCase())) {
            throw new BusinessException(FileErrorNoEnum.ERR_FILE_FORMAT);
        }
        return true;
    }

    @Override
    public boolean checkFileTypeInLine(String originalFilename, String[] types) {
        String suffix = FilenameUtils.getExtension(originalFilename);
        if (null == suffix || !ArrayUtils.contains(types, suffix.toLowerCase())) {
            throw new BusinessException(FileErrorNoEnum.ERR_PHOTO_FORMAT);
        }
        return true;
    }

    @Override
    public Files getFileByBizId(String bizId, String bizType) {
        return getFileByBizId(bizId, bizType, false);
    }

    @Override
    public Files getFileLoadByBizId(String bizId, String bizType) {
        return getFileByBizId(bizId, bizType, true);
    }

    @Override
    public Files getFileByBizId(String bizId, String bizType, Boolean isSource) {
        return baseMapper.getFileBybizIdAndbizType(bizId, bizType, Boolean.TRUE.equals(isSource) ? 1 : 0);
    }

    @Override
    public List<Files> getFileListByBizId(String bizId, String bizType) {
        LambdaQueryWrapper<Files> query = new LambdaQueryWrapper<>();
        query.eq(Files::getCategoryId, bizId);
        query.eq(Files::getCategoryType, bizType);
        query.orderByAsc(Files::getSortNo);
        return baseMapper.selectList(query);
    }

    @Override
    public void deleteFileByBizId(String buzId) {
        LambdaQueryWrapper<Files> query = new LambdaQueryWrapper<>();
        query.eq(Files::getCategoryId, buzId);
        baseMapper.delete(query);
    }

    @Override
    public Files getFileByCategoryTypeAndIsAdjunct(String categoryId, String categoryType, Integer isAdjunct) {
        return baseMapper.getFileByCategoryTypeAndIsAdjunct(categoryId, categoryType, isAdjunct);
    }

    @Override
    public Files getFileByCategoryId(String categoryId) {
        return baseMapper.getFileByCategoryId(categoryId);
    }

    @Override
    public Files getFileByCategoryIdAndIsSourceAndIsAdjunct(String categoryId, Integer isSource, Integer isAdjunct) {
        return baseMapper.getFileByCategoryIdAndIsSourceAndIsAdjunct(categoryId, isSource, isAdjunct);
    }

    @Override
    public List<Files> getFileMapByCategoryIds(Collection<String> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return new ArrayList<>();
        }
        return baseMapper.getFileMapByCategoryIds(categoryIds);
    }

    @Override
    public List<Files> getSourceFileMapByCategoryIds(Collection<String> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return new ArrayList<>();
        }
        return baseMapper.getSourceFileMapByCategoryIds(categoryIds);
    }

    @Override
    public PageInfo<FileListDTO> getFileByFileQuery(FileQuery fileQuery) {
        return PageMethod.startPage(fileQuery.getPageNo(), fileQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.getFileByFileQuery(fileQuery));
    }

    @Override
    public UploadCourseResultDTO uploadCourseWareFile(MultipartFile uploadFile) {

        //保存文件
        UploadResultDTO uploadResultDTO = saveUploadFile(uploadFile, CWTypeEnum.getUploadCwTypeSet());
        UploadCourseResultDTO uploadCourseResultDTO = new UploadCourseResultDTO();
        BeanUtils.copyProperties(uploadResultDTO, uploadCourseResultDTO);
        this.buildCourseWareFileInfo(uploadCourseResultDTO);
        return uploadCourseResultDTO;
    }

    @Override
    public UploadZipResultDTO uploadZipFileToDecompress(MultipartFile uploadFile) {
        return saveDecopressFile(uploadFile, false);
    }

    @Override
    public UploadCourseResultDTO uploadFileInfo(MultipartFile uploadFile, Set<String> suffix) {
        UploadResultDTO uploadResultDTO = saveUploadFile(uploadFile, suffix);
        UploadCourseResultDTO uploadCourseResultDTO = new UploadCourseResultDTO();
        BeanUtils.copyProperties(uploadResultDTO, uploadCourseResultDTO);
        this.buildCourseWareFileInfo(uploadCourseResultDTO);
        return uploadCourseResultDTO;
    }

    /**
     * 保存文件至临时目录
     *
     * @param multipartFile 文件
     * @param suffixes      校验的格式
     * @return 文件相关信息
     */
    @Override
    public UploadResultDTO saveUploadFile(MultipartFile multipartFile, Set<String> suffixes) {
        UploadResultDTO result = new UploadResultDTO();
        String originalFilename = multipartFile.getOriginalFilename();
        String suffix = FilenameUtils.getExtension(originalFilename);
        result.setMime(MimeUtil.getMimeType(suffix));
        if (null == suffix) {
            //后缀为空
            throw new BusinessException(FileErrorNoEnum.ERR_CLOUD_FILE_SUFFIX);
        }

        if (!CollectionUtils.isEmpty(suffixes)
            && !suffixes.contains(suffix.toUpperCase())
            && !suffixes.contains(suffix.toLowerCase())) {
            throw new BusinessException(FileErrorNoEnum.ERR_CLOUD_FILE_SUFFIX);
        }

        String fileId = StringUtil.newId();
        String fileName = fileId + "." + suffix;
        String tmpPath = sysConfig.queryTempPath();
        String pscPath = sysConfig.getPhysicalPath(tmpPath);
        FileUtil.mkdir(pscPath);
        File file = new File(String.format("%s/%s", pscPath, fileName));
        result.setPath(FilenameUtils.separatorsToUnix(tmpPath.concat("/").concat(fileName)));
        result.setFileName(originalFilename);
        result.setDiskFileName(fileName);
        result.setUrl(urlHelper.getStaticFullUrl(result.getPath()));

        try {
            multipartFile.transferTo(file);
        } catch (IOException | IllegalStateException e) {
            log.error(SAVE_UPLOAD_FILE_ERROR, e);
            throw new BusinessException(FileErrorNoEnum.SAVE_UPLOAD_FILE_ERROR);
        }

        UploadFileRequest uploadFileRequest = new UploadFileRequest();
        uploadFileRequest.setFileName(result.getPath());
        uploadFileRequest.setAsyncUpload(true);
        uploadFileRequest.setTempFilePath(file.getAbsolutePath());
        uploadFileRequest.setCopyFolderFlag(false);
        uploadFileRequest.setBizId(fileId);
        uploadFileService.uploadFile(uploadFileRequest);
        return result;
    }

    /**
     * 保存文件至临时目录
     *
     * @param multipartFile
     */
    private UploadZipResultDTO saveDecopressFile(MultipartFile multipartFile, boolean isRetainOriginalFileName) {
        UploadZipResultDTO result = new UploadZipResultDTO();
        String originalFilename = multipartFile.getOriginalFilename();
        String suffix = FilenameUtils.getExtension(originalFilename);
        result.setMime(MimeUtil.getMimeType(suffix));
        if (!"zip".equalsIgnoreCase(suffix)) {
            //后缀为空
            throw new BusinessException(FileErrorNoEnum.ERR_CLOUD_FILE_SUFFIX);
        }

        String fileId = StringUtil.newId();
        String fileName = fileId + "." + suffix;
        String tmpPath = sysConfig.queryTempPath();
        String pscPath = sysConfig.getPhysicalPath(tmpPath);
        FileUtil.mkdir(pscPath);
        String fileUrl = pscPath + "/" + fileName;
        File file = new File(fileUrl);
        result.setFileName(originalFilename);
        result.setDiskFileName(fileName);

        try {
            multipartFile.transferTo(file);
        } catch (IOException | IllegalStateException e) {
            log.error(SAVE_UPLOAD_FILE_ERROR, e);
            throw new BusinessException(FileErrorNoEnum.SAVE_UPLOAD_FILE_ERROR);
        }
        List<TempFileDTO> urlList = ZipUtil.unZipAndBackUrl(fileUrl, pscPath, isRetainOriginalFileName);
        List<String> childPathList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(urlList)) {
            for (TempFileDTO tempFileDTO : urlList) {
                String url = tempFileDTO.getTempPath();
                UploadFileRequest ufr = new UploadFileRequest();
                String childFileName = StringUtils.substring(url, url.lastIndexOf("/") + 1, url.length());
                String childFileSuffix = FilenameUtils.getExtension(childFileName);
                if (isRetainOriginalFileName && !ArrayUtils.contains(CERT_RELATE_FILE_SUFFIX,
                    childFileSuffix.toLowerCase())) {
                    throw new BusinessException(FileErrorNoEnum.ERR_CERT_RELATE_IMAGE_FORMAT);
                }

                String childTempPath = FilenameUtils.separatorsToUnix(tmpPath.concat("/")).concat(childFileName);
                ufr.setFileName(childTempPath);
                ufr.setAsyncUpload(true);
                ufr.setTempFilePath(url);
                ufr.setCopyFolderFlag(false);
                log.info("zipChild {} -> {}", url, childTempPath);
                ufr.setBizId(fileId);
                uploadFileService.uploadFile(ufr);
                childPathList.add(childTempPath);
            }
        }
        result.setChildFilePathList(childPathList);
        return result;
    }

    private HomeWorkUploadZipResultDTO saveHomeWorkDecopressFile(MultipartFile multipartFile,
        boolean isRetainOriginalFileName) {
        HomeWorkUploadZipResultDTO result = new HomeWorkUploadZipResultDTO();
        String originalFilename = multipartFile.getOriginalFilename();
        String suffix = FilenameUtils.getExtension(originalFilename);
        result.setMime(MimeUtil.getMimeType(suffix));
        if (!"zip".equalsIgnoreCase(suffix)) {
            //后缀为空
            throw new BusinessException(FileErrorNoEnum.ERR_CLOUD_FILE_SUFFIX);
        }

        String fileName = StringUtil.newId() + "." + suffix;
        String tmpPath = sysConfig.queryTempPath();
        String pscPath = sysConfig.getPhysicalPath(tmpPath);
        FileUtil.mkdir(pscPath);
        String fileUrl = pscPath + "/" + fileName;
        File file = new File(fileUrl);
        result.setFileName(originalFilename);
        result.setDiskFileName(fileName);

        try {
            multipartFile.transferTo(file);
        } catch (IOException | IllegalStateException e) {
            log.error(SAVE_UPLOAD_FILE_ERROR, e);
            throw new BusinessException(FileErrorNoEnum.SAVE_UPLOAD_FILE_ERROR);
        }
        List<TempFileDTO> urlList = ZipUtil.unZipAndBackUrl(fileUrl, pscPath, isRetainOriginalFileName);
        List<TempFileDTO> childFilePathList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(urlList)) {
            for (TempFileDTO tempFileDTO : urlList) {
                String url = tempFileDTO.getTempPath();
                UploadFileRequest ufr = new UploadFileRequest();
                String childFileName = StringUtils.substring(url, url.lastIndexOf("/") + 1, url.length());
                String childFileSuffix = FilenameUtils.getExtension(childFileName);
                if (!ArrayUtils.contains(HOME_WORK_FILE_SUFFIX, childFileSuffix.toLowerCase())) {
                    throw new BusinessException(FileErrorNoEnum.CW_PIC_FORMAT_NO_MSG);
                }
                String childTempPath = FilenameUtils.separatorsToUnix(tmpPath.concat("/")).concat(childFileName);
                ufr.setFileName(childTempPath);
                ufr.setAsyncUpload(true);
                ufr.setTempFilePath(url);
                ufr.setCopyFolderFlag(false);
                log.info("zipChild {} -> {}", url, childTempPath);
                uploadFileService.uploadFile(ufr);
                TempFileDTO fileDTO = new TempFileDTO();
                fileDTO.setFileName(tempFileDTO.getFileName());
                fileDTO.setTempPath(childTempPath);
                childFilePathList.add(fileDTO);
            }
        }
        result.setChildFilePathList(childFilePathList);
        return result;
    }


    @Override
    public MimeInfoDTO getMineInfo(String path) {
        MimeInfoDTO mimeInfoDTO = new MimeInfoDTO();
        String fileName = FilenameUtils.getName(path);
        String suffix = FilenameUtils.getExtension(fileName);
        String cwType = CWTypeEnum.checkCourseWareType(suffix);
        mimeInfoDTO.setMime(MimeUtil.getMimeType(suffix));
        if (null == cwType) {
            return mimeInfoDTO;
        }
        if (cwType.equals(CWTypeEnum.Pic.name())) {
            mimeInfoDTO.setType(CWTypeEnum.Scorm.name());
            mimeInfoDTO.setCwVersion("");
        }

        if (cwType.equals(CWTypeEnum.Video.name())) {
            mimeInfoDTO.setType(CWTypeEnum.Video.name());
            getFileInfoByFFmpeg(path, mimeInfoDTO);
        }

        if (cwType.equals(CWTypeEnum.Audio.name())) {
            //
            mimeInfoDTO.setType(CWTypeEnum.Audio.name());
            getFileInfoByFFmpeg(path, mimeInfoDTO);
        }

        fillTypeIfEquals(mimeInfoDTO, cwType, CWTypeEnum.Word);
        fillTypeIfEquals(mimeInfoDTO, cwType, CWTypeEnum.PPT);
        fillTypeIfEquals(mimeInfoDTO, cwType, CWTypeEnum.PDF);
        fillTypeIfEquals(mimeInfoDTO, cwType, CWTypeEnum.Excel);
        fillTypeIfEquals(mimeInfoDTO, cwType, CWTypeEnum.IMG);
        return mimeInfoDTO;
    }

    /**
     * 用ffmpeg获取文件播放时长信息
     *
     * @param path        路径
     * @param mimeInfoDTO mime信息dto
     */
    private void getFileInfoByFFmpeg(String path, MimeInfoDTO mimeInfoDTO) {
        try {
            ListenableFuture<FileParseInfo> fileParseInfoByFFmpeg = infoServiceFutureStub.getFileParseInfoByFFmpeg(
                FileParseInfoParams.newBuilder().setFilePath(path).build());
            FileParseInfo fileParseInfo = fileParseInfoByFFmpeg.get();
            mimeInfoDTO.setPlayTime(fileParseInfo.getDuration());
        } catch (InterruptedException e) {
            log.error(PARSE_INFO_BY_FFMPEG_ERROR, e);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error(PARSE_INFO_BY_FFMPEG_ERROR, e);
        }
    }

    /**
     * 填充类型如果等于该枚举的类型
     *
     * @param mimeInfoDTO mime信息dto
     * @param cwType      cw类型
     * @param cwTypeEnum  cw类型枚举
     */
    private void fillTypeIfEquals(MimeInfoDTO mimeInfoDTO, String cwType, CWTypeEnum cwTypeEnum) {
        if (cwType.equals(cwTypeEnum.toString())) {
            mimeInfoDTO.setType(cwTypeEnum.name());
        }
    }

    /**
     * 构造课件文件返回信息
     *
     * @param uploadCourseResultDTO
     */
    @Override
    public void buildCourseWareFileInfo(UploadCourseResultDTO uploadCourseResultDTO) {
        String suffix = FilenameUtils.getExtension(uploadCourseResultDTO.getFileName());
        String cwType = CWTypeEnum.checkCourseWareType(suffix);
        if (null == cwType) {
            //课件格式不正确
            throw new BusinessException(FileErrorNoEnum.CW_FORMAT_ERROR_MSG);
        }

        uploadCourseResultDTO.setMime(MimeUtil.getMimeType(suffix));
        if (cwType.equals(CWTypeEnum.Pic.name())) {
            //压缩包文件处理
            zipCourseWareBuild(uploadCourseResultDTO);
            return;
        }

        if (Objects.equals(cwType, CWTypeEnum.IMG.name())) {
            uploadCourseResultDTO.setType(CWTypeEnum.IMG.name());
            return;
        }

        if (cwType.equals(CWTypeEnum.Video.name())) {
            uploadCourseResultDTO.setType(CWTypeEnum.Video.name());
            parseFileInfo(uploadCourseResultDTO);
            return;
        }

        if (cwType.equals(CWTypeEnum.Audio.name())) {
            uploadCourseResultDTO.setType(CWTypeEnum.Audio.name());
            parseFileInfo(uploadCourseResultDTO);
            return;
        }

        //根据 cwType 设置对应的 type 值
        setTypeByCwType(uploadCourseResultDTO, cwType);
    }

    /**
     * 解析文件信息并设置播放时长
     *
     * @param uploadCourseResultDTO 上传课程dto
     */
    private void parseFileInfo(UploadCourseResultDTO uploadCourseResultDTO) {
        String path = sysConfig.getPhysicalPath(uploadCourseResultDTO.getPath());
        if (!new File(path).exists()) {
            path = urlHelper.getStaticFullUrl(uploadCourseResultDTO.getPath());
        }
        try {
            ListenableFuture<FileParseInfo> fileParseInfoByFFmpeg = infoServiceFutureStub.getFileParseInfoByFFmpeg(
                FileParseInfoParams.newBuilder().setFilePath(path).build());
            FileParseInfo fileParseInfo = fileParseInfoByFFmpeg.get();
            uploadCourseResultDTO.setPlayTime(fileParseInfo.getDuration() + "");
        } catch (InterruptedException e) {
            log.error(PARSE_INFO_BY_FFMPEG_ERROR, e);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error(PARSE_INFO_BY_FFMPEG_ERROR, e);
        }
    }

    private void setTypeByCwType(UploadCourseResultDTO uploadCourseResultDTO, String cwType) {
        if (cwType.equals(CWTypeEnum.Word.toString())) {
            uploadCourseResultDTO.setType(CWTypeEnum.Word.name());
        }

        if (cwType.equals(CWTypeEnum.PPT.toString())) {
            uploadCourseResultDTO.setType(CWTypeEnum.PPT.name());
        }

        if (cwType.equals(CWTypeEnum.Excel.toString())) {
            uploadCourseResultDTO.setType(CWTypeEnum.Excel.name());
        }

        if (cwType.equals(CWTypeEnum.PDF.toString())) {
            uploadCourseResultDTO.setType(CWTypeEnum.PDF.name());

            String newId = StringUtil.newId();
            String tmpCourseWarePath = sysConfig.queryTempPath(newId);
            String physicalCourseWarePath = sysConfig.getPhysicalPath(tmpCourseWarePath);
            //将pdf文件移动至physicalCourseWarePath 目录，并将文件重命名
            FileUtil.removeFileToTargetArea(sysConfig.getPhysicalPath(uploadCourseResultDTO.getPath()),
                physicalCourseWarePath);

            //
            uploadCourseResultDTO.setPath(tmpCourseWarePath + "/index.pdf");
            uploadCourseResultDTO.setUrl(urlHelper.getStaticFullUrl(uploadCourseResultDTO.getPath()));
        }
    }

    /**
     * zip课件文件处理
     */
    private void zipCourseWareBuild(UploadCourseResultDTO uploadCourseResultDTO) {

        ScormInfoDTO scormInfo = getScormInfo(uploadCourseResultDTO.getPath());
        if (scormInfo != null) {
            uploadCourseResultDTO.setType(scormInfo.getType());
            uploadCourseResultDTO.setCwVersion(scormInfo.getCwVersion());
            uploadCourseResultDTO.setHref(scormInfo.getHref());
        }
    }

    @Override
    public ScormInfoDTO getScormInfo(String zipFilePath) {
        // 获取临时目录文件 同级别与文件同名的目录
        String tmpCourseWarePath = zipFilePath.substring(0, zipFilePath.lastIndexOf("."));
        String physicalCourseWarePath = sysConfig.getPhysicalPath(tmpCourseWarePath);
        try {
            //将压缩包解压
            ZipUtil.unZip(sysConfig.getPhysicalPath(zipFilePath), physicalCourseWarePath);
        } catch (Exception e) {
            log.error("解压课件失败...", e);
            throw new BusinessException(FileErrorNoEnum.CW_UNZIP_FAIL);
        }
        String imsmPath = physicalCourseWarePath + "/imsmanifest.xml";
        String scormPath = physicalCourseWarePath + "/scorm.xml";
        File imsmFile = new File(imsmPath);
        File scormFile = new File(scormPath);
        if (!imsmFile.exists() && !scormFile.exists()) {
            //两个文件都不存在是不允许的
            throw new BusinessException(FileErrorNoEnum.CW_FORMAT_ERROR_MSG);
        }

        ScormInfoDTO scormInfoDTO = new ScormInfoDTO();

        if (scormFile.exists()) {
            log.info("{}是图文课", zipFilePath);
            Element rootElement = XmlUtil.getRootElement(scormFile);
            String rootElementName = rootElement.getName();
            if (!"scorm".equals(rootElementName)) {
                throw new BusinessException(FileErrorNoEnum.CW_FORMAT_ERROR_MSG);
            }

            String fileName = "index.html";
            Element node = rootElement.element("item");
            if (node != null && StringUtils.isNotBlank(node.attribute("href").getValue())) {
                fileName = node.attribute("href").getValue();
            }
            scormInfoDTO.setFileName(fileName);
            scormInfoDTO.setType(CWTypeEnum.Pic.name());
        }

        if (imsmFile.exists()) {
            log.info("{}是scrom课件", zipFilePath);
            String newXmlPath = physicalCourseWarePath + "/imsmanifest_new.xml";
            String cwVersion = XmlUtil.exitsXmlNs(imsmPath) ? "2004" : "1.2";
            Map<String, String> rootParams = new HashMap<>();
            rootParams.put("cwVersion", cwVersion);
            File file526 = XmlUtil.xmlTransform(imsmPath, newXmlPath, rootParams);
            // 根据新生成的xml文件产生新的scrom的xml文件
            Element srcRoot = XmlUtil.getRootElement(file526);
            String title = XmlUtil.getAttribute(srcRoot, "organization", TITLE);
            String id = XmlUtil.getAttribute(srcRoot, "resource", "id");
            String href = XmlUtil.getAttribute(srcRoot, "resource", "href");
            // 根据sysMime表的后缀名取得mime作为type
            String type = MimeUtil.getMimeType(FilenameUtils.getExtension(href));
            // 写入新的xml文件
            String targetPath = physicalCourseWarePath + "/scorm.xml";
            Document doc = DocumentHelper.createDocument();
            Element root = doc.addElement("scorm");
            root.addAttribute("baseUrl", "");
            Element titleElement = root.addElement(TITLE);
            titleElement.setText(title);
            Element itemElement = root.addElement("item");
            itemElement.addAttribute("id", id);
            itemElement.addAttribute("type", type);
            itemElement.addAttribute("href", href);
            itemElement.addAttribute("progress", "0");
            itemElement.addAttribute(TITLE, title);
            XmlUtil.createXml(targetPath, doc);
            scormInfoDTO.setFileName(href);
            scormInfoDTO.setCwVersion(cwVersion);
            scormInfoDTO.setType(CWTypeEnum.Scorm.name());
            scormInfoDTO.setHref(href);
        }

        return scormInfoDTO;
    }

    @Override
    public List<Files> getFileByBizIds(Collection<String> bizIds, String bizType, @Nullable Boolean isSource) {
        LambdaQueryWrapper<Files> query = new LambdaQueryWrapper<>();
        query.in(Files::getCategoryId, bizIds);
        query.eq(Files::getCategoryType, bizType);
        query.eq(Objects.nonNull(isSource), Files::getIsSource, Boolean.TRUE.equals(isSource) ? 1 : 0);
        return baseMapper.selectList(query);
    }

    @Override
    public void createCertRelateWaterMarkImg(RelateWaterMarkImgDTO relateWaterMarkImgDTO) throws IOException {

        // 证书id
        String certificationId = relateWaterMarkImgDTO.getCertificationId();

        // 获取证书明细模板word文档
        String filePath;
        Files files = baseMapper.getFileByCategoryId(certificationId);
        if (files != null) {
            filePath = sysConfig.getPhysicalPath(files.getCurrentPath());
            //原来上传没有复制到本地服务器，所以这里要进行一次下载
            uploadFileService.downloadFile(files.getCurrentPath());
        } else {
            // 用户没有上传模板则拿默认的word模板文档
            String downLoadPath = sysConfig.getRoot() + "/file/importTemplate/证书明细模板.docx";
            File exampleDocx = uploadFileService.downloadFile(downLoadPath);
            filePath = exampleDocx.getAbsolutePath();
        }

        // 执行证书模板套打
        createCertRelateWaterMarkImgByCertRelateWord(relateWaterMarkImgDTO, filePath);
    }

    @Override
    public UploadZipResultDTO uploadCertRelateZip(MultipartFile uploadFile) {
        return saveDecopressFile(uploadFile, true);
    }

    @Override
    public UploadZipResultDTO uploadCertPhotoZip(MultipartFile uploadFile) {
        return saveDecopressFile(uploadFile, true);
    }

    @Override
    public HomeWorkUploadZipResultDTO uploadHomeWorkFile(MultipartFile uploadFile) {
        String originalFilename = uploadFile.getOriginalFilename();
        String suffix = FilenameUtils.getExtension(originalFilename);
        if ("zip".equalsIgnoreCase(suffix)) {
            return saveHomeWorkDecopressFile(uploadFile, false);
        } else if ("xls".equalsIgnoreCase(suffix) || "xlsx".equalsIgnoreCase(suffix)) {
            UploadResultDTO uploadRes = saveUploadFile(uploadFile, Set.of("xls", "xlsx"));

            HomeWorkUploadZipResultDTO zipRes = new HomeWorkUploadZipResultDTO();
            BeanUtils.copyProperties(uploadRes, zipRes);

            List<TempFileDTO> childFilePathList = new ArrayList<>();
            childFilePathList.add(new TempFileDTO().setTempPath(zipRes.getPath()).setFileName(zipRes.getFileName()));
            zipRes.setChildFilePathList(childFilePathList);
            return zipRes;
        } else {
            throw new BusinessException(FileErrorNoEnum.ERR_CLOUD_FILE_SUFFIX);
        }
    }

    @NotNull
    private String[] handleExcelTitle(ImportDataDTO importDataDTO, Row row) {
        String[] title;
        title = new String[row.getPhysicalNumberOfCells()];
        int index = 0;
        for (Cell cell : row) {
            title[index] = cell.getStringCellValue();
            index++;
        }
        importDataDTO.setTitle(title);
        return title;
    }

    private void createCertRelateWaterMarkImgByCertRelateWord(RelateWaterMarkImgDTO relateWaterMarkImgDTO,
        String filePath) throws IOException {
        log.info("cert relate word file path is : {}", filePath);
        // 1. 先替换文档中的变量
        XWPFDocument docx = new XWPFDocument(POIXMLDocument.openPackage(filePath));

        // 处理发证机构名称（学员端证书的页面显示控制，考虑到中英文和模板样式，此处字符长度控制为33）
        String orgName = relateWaterMarkImgDTO.getUserOrgan();
        orgName = StringUtil.subChineseString(orgName, 33);

        // docx模板中的变量及对应值
        Map<String, String> textMap = HashMap.newHashMap(5);
        textMap.put("{username}",
            StringUtil.isEmpty(relateWaterMarkImgDTO.getUserName()) ? "" : relateWaterMarkImgDTO.getUserName());
        textMap.put("{account}",
            StringUtil.isEmpty(relateWaterMarkImgDTO.getUserLoginName()) ? ""
                : relateWaterMarkImgDTO.getUserLoginName());
        textMap.put("{orgName}", orgName);
        textMap.put("{certNo}",
            StringUtil.isEmpty(relateWaterMarkImgDTO.getCertNo()) ? "" : relateWaterMarkImgDTO.getCertNo());
        textMap.put("{createTime}",
            StringUtil.isEmpty(relateWaterMarkImgDTO.getCreateTime()) ? "" : relateWaterMarkImgDTO.getCreateTime());
        textMap.put("{validPeriod}",
            StringUtil.isEmpty(relateWaterMarkImgDTO.getValidPeriod()) ? "" : relateWaterMarkImgDTO.getValidPeriod());
        textMap.put("{activity}",
            StringUtil.isEmpty(relateWaterMarkImgDTO.getContentName()) ? "" : relateWaterMarkImgDTO.getContentName());
        textMap.put("{startDate}",
            StringUtil.isEmpty(relateWaterMarkImgDTO.getStartTime()) ? "" : relateWaterMarkImgDTO.getStartTime());
        textMap.put("{sponsor}",
            StringUtil.isEmpty(relateWaterMarkImgDTO.getSponsor()) ? "" : relateWaterMarkImgDTO.getSponsor());

        WordFileReplaceText.changeTextBox(docx, textMap);

        String newId = StringUtil.newId();
        // 临时目录
        String tempDir = sysConfig.queryTempPath() + newId + SEPARATOR;
        log.info("tempDir path : {}", tempDir);
        FileUtil.mkdir(tempDir);

        // 定义水印图片名称
        String newImageName =
            relateWaterMarkImgDTO.getUserName().replace(" ", "") + "_" + relateWaterMarkImgDTO.getEmpNo() + "_"
                + relateWaterMarkImgDTO.getUserOrgan().replace(" ", "") + "_" + relateWaterMarkImgDTO.getCerName()
                + "_" + newId;
        log.info("watermark image name : {}", newImageName);
        // 修改完后的证书明细文档路径
        String savePath = sysConfig.getPhysicalPath(tempDir + newImageName + ".docx");
        File parentDir = new File(savePath).getParentFile();
        if (!parentDir.exists()) {
            FileUtil.mkdir(parentDir.getAbsolutePath());
        }
        FileOutputStream fos = new FileOutputStream(savePath);
        docx.write(fos);
        fos.flush();
        fos.close();
        // 2.用cmd命令转图片 参数2是图片输出路径，参数3是文档输入路径
        String waterMarkImgPath = transWaterMarkImage(tempDir, savePath, newImageName);
        log.info("waterMarkImgPath : {}", waterMarkImgPath);

        // 存图片记录到图片表中
        fileFeign.saveImage(relateWaterMarkImgDTO.getRelateId(), ImageTypeEnum.CERTIFICATION_WATERMARK_IMG.name(),
            newImageName + ".jpg", waterMarkImgPath);
    }

    private @NotNull String transWaterMarkImage(String tempDir, String sourcePath, String newImageName)
        throws IOException {

        TransWaterMarkImageReply transWaterMarkImageReply = transServiceBlockingStub.transWaterMarkImage(
            TransWaterMarkImageRequest.newBuilder()
                .setTempDir(tempDir)
                .setSourcePath(sourcePath)
                .setNewImageName(newImageName)
                .build());
        return transWaterMarkImageReply.getWaterMarkImgPath();
    }

    private LambdaQueryWrapper<Files> getLambdaQueryWrapper(String bizId, String bizType, Boolean isSource,
        SFunction<Files, Object> sortField, Boolean isAsc) {
        LambdaQueryWrapper<Files> query = new LambdaQueryWrapper<>();
        query.eq(Files::getCategoryId, bizId);
        query.eq(Files::getCategoryType, bizType);
        // 如果获取原文件，那么isSource字段为1，否则是0，即获取转码(处理)后的文件
        if (Optional.ofNullable(isSource).isPresent()) {
            query.eq(Files::getIsSource, Boolean.TRUE.equals(isSource) ? 1 : 0);
        }
        if (Optional.ofNullable(sortField).isPresent() && Optional.ofNullable(isAsc).isPresent()) {
            if (Boolean.TRUE.equals(isAsc)) {
                query.orderByAsc(sortField);
            } else {
                query.orderByDesc(sortField);
            }
        }
        query.last("limit 0,1");
        return query;
    }

    @Override
    public Files getFileByBizId(String bizId, String bizType, Boolean isSource, SFunction<Files, Object> sortField,
        Boolean isAsc) {
        LambdaQueryWrapper<Files> query = getLambdaQueryWrapper(bizId, bizType, isSource, sortField, isAsc);
        return baseMapper.selectOne(query);
    }

    @Override
    public void checkExcel(ImportExcelQuery query) {
        ImportDataDTO importData = importDataFeign.getImportData(query.getExcelFile());

        ExcelTitleBaseCheckUtil.baseCheck(importData.getExcel(), ImportExcelTitleEnum.getTitleByCode(query.getCode()));
    }

    @Override
    public String getImgBase64(String imgPath, RelateWaterMarkImgDTO dto) throws IOException {
        // 获取背景图
        File certImage = uploadFileService.downloadFile(imgPath);
        if (certImage == null) {
            throw new BusinessException(ERR_CER_BACKGROUND_IMAGE_NOT_FOUND);
        }
        String filename = certImage.getName();
        String type = filename.substring(filename.indexOf(".") + 1);
        // 添加水印
        BufferedImage image = ImageIO.read(certImage);

        Graphics2D g = image.createGraphics();

        generateText(dto, image, g);
        g.dispose();
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ImageIO.write(image, type, out);
        //将文件转换为Base64
        String imgBase64 = Base64.getEncoder().encodeToString(out.toByteArray());
        out.close();
        return imgBase64;
    }


    @Override
    public List<Files> getMainFileMapByCategoryIds(Collection<String> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return new ArrayList<>();
        }
        return baseMapper.getMainFileMapByCategoryIds(categoryIds);
    }

    @Override
    public UploadCourseResultDTO uploadFormFile(MultipartFile uploadFile) {
        //保存文件
        UploadResultDTO uploadResultDTO = saveUploadFile(uploadFile, CWTypeEnum.getAllFormSupportType());
        UploadCourseResultDTO uploadCourseResultDTO = new UploadCourseResultDTO();
        BeanUtils.copyProperties(uploadResultDTO, uploadCourseResultDTO);
        this.buildCourseWareFileInfo(uploadCourseResultDTO);
        return uploadCourseResultDTO;
    }

    @Override
    public String getDownloadUrl(String id) {
        Files file = getFileByBizId(id, FileBizType.CourseWareFile.name(), true, Files::getCreateTime, false);

        if (Objects.isNull(file)) {
            log.error("file_down_load_fail_bizId:{}", id);
            throw new BusinessException(FileErrorNoEnum.FILE_ERR_DOWNLOAD);
        }

        return uploadFileService.getUrl(file.getCurrentPath(), true, file.getFileName());
    }

    @Override
    public List<Files> getFileUrlByIdsAndType(Set<String> ids, String bizType) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return baseMapper.getFileUrlByIdsAndType(ids, bizType);
    }

    @Override
    public List<Files> getFileListByBizIds(Collection<String> bizIds, String bizType) {
        if (CollectionUtils.isEmpty(bizIds)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<Files> query = new LambdaQueryWrapper<>();
        query.in(Files::getCategoryId, bizIds);
        query.eq(Files::getCategoryType, bizType);
        query.orderByAsc(Files::getSortNo);
        return baseMapper.selectList(query);
    }

    @Override
    public List<Files> getSourceFileListByBizIds(Collection<String> bizIds, String bizType) {
        if (CollectionUtils.isEmpty(bizIds)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<Files> query = new LambdaQueryWrapper<>();
        query.in(Files::getCategoryId, bizIds);
        query.eq(Files::getCategoryType, bizType);
        query.eq(Files::getIsSource, 1);
        query.orderByAsc(Files::getSortNo);
        return baseMapper.selectList(query);
    }

    @Override
    public UploadResultDTO saveUploadFileToFormalPath(MultipartFile uploadFile, String name,
        FileTypeEnum fileTypeEnum) {
        UploadResultDTO result = new UploadResultDTO();
        result.setFileName(name);
        String suffix = FilenameUtils.getExtension(name);
        String fileName = StringUtil.newId() + "." + suffix;
        // 正式路径的相对地址
        String tmpPath =
            SEPARATOR + sysConfig.getRoot() + SEPARATOR + "publicRead" + SEPARATOR + DateUtil.getYmStr() + SEPARATOR
                + fileTypeEnum.getValue();
        String pscPath = sysConfig.getPhysicalPath(tmpPath);
        FileUtil.mkdir(pscPath);
        File file = new File(pscPath + SEPARATOR + fileName);
        try {
            uploadFile.transferTo(file);
            result.setPath(FilenameUtils.separatorsToUnix(tmpPath.concat("/").concat(fileName)));
            result.setUrl(fileFeign.getFileUrl(result.getPath()));
            if (ArrayUtils.contains(IMG_SUFFIX, suffix.toLowerCase())) {
                BufferedImage bufferedImage = ImageIO.read(file);
                if (Objects.isNull(bufferedImage)) {
                    throw new BusinessException(FileErrorNoEnum.IMAGE_FILE_ERROR);
                }
                result.setHeight(bufferedImage.getHeight());
                result.setWidth(bufferedImage.getWidth());
            }
            UploadFileRequest uploadFileRequest = new UploadFileRequest();
            uploadFileRequest.setFileName(result.getPath());
            uploadFileRequest.setAsyncUpload(false);
            uploadFileRequest.setTempFilePath(file.getAbsolutePath());
            uploadFileRequest.setCopyFolderFlag(false);
            uploadFileRequest.setPublicRead(true);
            uploadFileService.uploadFile(uploadFileRequest);
        } catch (IOException | IllegalStateException e) {
            log.error(SAVE_UPLOAD_FILE_ERROR, e);
            throw new BusinessException(FileErrorNoEnum.SAVE_UPLOAD_FILE_ERROR);
        }
        return result;
    }

    @Override
    public void deleteTempFile(Integer deleteTime) {
        // 1. 删除本地服务器临时文件
        String localTempDirectoryPath = sysConfig.queryLocalTempDirectoryPath();
        log.info("del local localTempDirectoryPath:{}", localTempDirectoryPath);
        File localTempDirectory = new File(localTempDirectoryPath);
        if (localTempDirectory.exists() && localTempDirectory.isDirectory()) {
            // 计算截止时间（当前时间减去指定天数）
            LocalDateTime cutoffTime = LocalDateTime.now().minusDays(deleteTime);
            // 调用递归方法删除符合条件的文件
            deleteLocalFilesRecursively(localTempDirectory, cutoffTime);
        }

        // 2. 删除对象存储临时文件
        uploadFileService.deleteTempFile(deleteTime);
    }

    private void deleteLocalFilesRecursively(File directory, LocalDateTime cutoffTime) {
        File[] files = directory.listFiles();
        if (files == null) {
            // 如果目录为空，直接返回
            return;
        }

        for (File file : files) {
            if (file.isDirectory()) {
                // 如果是子目录，递归调用
                deleteLocalFilesRecursively(file, cutoffTime);
            } else {
                try {
                    // 获取文件的创建时间
                    BasicFileAttributes attributes = java.nio.file.Files.readAttributes(file.toPath(),
                        BasicFileAttributes.class);
                    Instant creationTime = attributes.creationTime().toInstant();
                    LocalDateTime fileCreationTime = LocalDateTime.ofInstant(creationTime, ZoneId.systemDefault());

                    // 判断文件创建时间是否早于截止时间
                    if (fileCreationTime.isBefore(cutoffTime)) {
                        log.info("del local tempFile:{}", file.getAbsolutePath());
                        // 删除文件
                        java.nio.file.Files.delete(file.toPath());
                    }
                } catch (Exception e) {
                    log.error("delete local tempFile error:{}", file.getAbsolutePath(), e);
                }
            }
        }

        // 检查当前目录是否为空，如果为空则删除目录
        if (directory.list() != null && Objects.requireNonNull(directory.list()).length == 0) {
            try {
                log.info("del empty directory:{}", directory.getAbsolutePath());
                java.nio.file.Files.delete(directory.toPath());
            } catch (Exception e) {
                log.error("delete empty directory error:{}", directory.getAbsolutePath(), e);
            }
        }
    }

    @Override
    public List<Files> getFilesCategoryIds(Collection<String> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return new ArrayList<>();
        }
        return baseMapper.getFilesCategoryIds(categoryIds);
    }
}
