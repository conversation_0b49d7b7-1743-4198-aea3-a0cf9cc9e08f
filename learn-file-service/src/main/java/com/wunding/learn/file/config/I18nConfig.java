//package com.wunding.learn.file.config;
//
//import java.nio.charset.StandardCharsets;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.support.ReloadableResourceBundleMessageSource;
//
///**
// * <AUTHOR>
// */
//@Configuration
//public class I18nConfig {
//
//    @Bean("messageSource")
//    @ConditionalOnMissingBean
//    public ReloadableResourceBundleMessageSource messageSource() {
//        ReloadableResourceBundleMessageSource messageSource = new ReloadableResourceBundleMessageSource();
//        messageSource.setDefaultEncoding(StandardCharsets.UTF_8.toString());
//        messageSource.setBasenames("classpath:i18n/messages");
//        messageSource.addBasenames("classpath:i18n/enum_messages");
//        messageSource.addBasenames("classpath:i18n/error_messages");
//        messageSource.addBasenames("classpath:i18n/export_message");
//        messageSource.addBasenames("classpath:i18n/templateFileName_message");
//        messageSource.setCacheSeconds(60);
//        return messageSource;
//    }
//}
