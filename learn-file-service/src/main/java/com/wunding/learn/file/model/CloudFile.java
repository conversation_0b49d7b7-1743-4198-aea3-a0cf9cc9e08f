package com.wunding.learn.file.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 云文件
 *
 * <AUTHOR> href="mailto:<EMAIL>">wgr</a>
 * @since 2024-01-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_cloud_file")
@Schema(name = "CloudFile对象", description = "云文件")
public class CloudFile implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 文件id
     */
    @Schema(description = "文件id")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;


    /**
     * 上传uploadId
     */
    @Schema(description = "上传uploadId")
    @TableField("upload_id")
    private String uploadId;

    /**
     * 合并状态 0-否 1-是
     */
    @Schema(description = "合并状态 0-否 1-是")
    @TableField("merge_status")
    private Integer mergeStatus;


    /**
     * 云上文件的MD码，唯一标识
     */
    @Schema(description = "云上文件的MD码，唯一标识")
    @TableField("md5")
    private String md5;


    /**
     * 文件名称
     */
    @Schema(description = "文件名称")
    @TableField("file_name")
    private String fileName;


    /**
     * 文件大小/bit
     */
    @Schema(description = "文件大小/bit")
    @TableField("file_size")
    private Long fileSize;


    /**
     * 当前相对路径
     */
    @Schema(description = "当前相对路径")
    @TableField("current_path")
    private String currentPath;


    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    @TableField("is_del")
    @TableLogic(value = "0", delval = "1")
    private Integer isDel;


    /**
     * 新增人
     */
    @Schema(description = "新增人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;


    /**
     * 新增时间
     */
    @Schema(description = "新增时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 最后编辑人
     */
    @Schema(description = "最后编辑人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;


    /**
     * 最后编辑时间
     */
    @Schema(description = "最后编辑时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


}
