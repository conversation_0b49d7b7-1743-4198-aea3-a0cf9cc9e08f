package com.wunding.learn.file.config;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wunding.learn.common.constant.user.UserErrorNoEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.util.date.DateUtil;
import com.wunding.learn.common.util.http.WebUtil;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.dto.TenantBucketInfo;
import com.wunding.learn.file.service.StorageStrategyService;
import com.wunding.learn.file.service.impl.LocalStorageStrategyImpl;
import com.wunding.learn.file.service.impl.ObjectStorageStrategyImpl;
import com.wunding.learn.file.util.FileUtil;
import com.wunding.learn.trans.api.enums.OfficeTransTypeEnum;
import jakarta.servlet.http.HttpServletRequest;
import java.io.File;
import java.net.URI;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3ClientBuilder;
import software.amazon.awssdk.services.s3.S3Configuration;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.S3Presigner.Builder;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;

/**
 * 配置文件读取
 *
 * <AUTHOR>
 * @date 2022/2/28
 */
@Configuration
@ConfigurationProperties("app")
@Data
@Slf4j
@JsonIgnoreProperties(ignoreUnknown = true)
public class SysConfig {

    private static final String SEPARATOR = "/";

    private static final String TEMP_FILE = "tempFile";
    private static final String INNER_SUFFIX = "Inner";

    /**
     * 对象存储
     */
    private static final Integer OBJECT_STORAGE_TYPE = 1;

    /**
     * 本地存储
     */
    private static final Integer LOCAL_STORAGE_TYPE = 2;

    /**
     * 存储方式 1 对象存储 2 本地存储
     */
    private Integer storageType;

    /**
     * 对象存储机器内网地址
     */
    private String intranetEndPoint;

    /**
     * 对象存储机器地址
     */
    private String endPoint;

    /**
     * 对象存储访问key
     */
    private String accessKey;

    /**
     * 对象存储访问秘钥
     */
    private String secretKey;

    /**
     * 对象存储桶名
     */
    private String bucketName;

    /**
     * 上传目录的系统绝对路径
     */
    private String location;
    /**
     * 上传目录
     */
    private String root;

    /**
     * 转码服务器地址
     */
    private String transCodingUrl;

    /**
     * ffmpeg执行插件
     */
    private String ffmpegPath;

    /**
     * 做课模板path
     */
    private String zuoKeTemplatePath;

    /**
     * 区域
     */
    private String region;

    /**
     * 对象存储类型
     */
    private String type;

    /**
     * 静态资源访问URL
     */
    private String staticBaseUrl;

    /**
     * 静态资源访问URL
     */
    private String imgProxyUrl;

    /**
     * office转码类型：{@link OfficeTransTypeEnum}
     */
    private Integer officeTransType;

    /**
     * 对象存储path模式
     */
    private Integer pathMode;

    /**
     * 是否在下载需要response-content-type
     */
    private Integer needContentType;

    /**
     * 租户根路径
     */
    private static final Map<String, String> TENANT_ROOT_PATH_MAP = new HashMap<>();

    /**
     * 租户的s3Client对象
     */
    private static final Map<String, S3Client> CLIENT_MAP = new ConcurrentHashMap<>();

    /**
     * 静态资源访问URL
     */
    private static final Map<String, TenantBucketInfo> TENANT_BUCKET_INFO_MAP = new ConcurrentHashMap<>();

    /**
     * 租户外网的s3Presigner对象
     */
    private static final Map<String, S3Presigner> PRESIGNER_MAP = new ConcurrentHashMap<>();

    public void addTenantBucket(String tenantId, String bucketName) {
        TENANT_ROOT_PATH_MAP.put(tenantId, bucketName);
    }

    public void addTenantBucketInfo(String tenantId, TenantBucketInfo bucketInfo) {
        // 读取配置并设置默认值
        // 对象存储模式 0-virtual host   1-path
        Integer practicalPathMode = Objects.requireNonNullElse(bucketInfo.getPathMode(), getPathMode());
        // 内网接入点
        String bucketInnerPoint = defaultIfBlank(bucketInfo.getIntranetEndPoint(), getIntranetEndPoint());
        // 外网接入点
        String bucketEndPoint = defaultIfBlank(bucketInfo.getEndPoint(), getEndPoint());
        // 有内网配置地址时，文件操作用内网接入点
        String practicalEndPoint = defaultIfBlank(bucketInnerPoint, bucketEndPoint);
        // 凭证信息
        String bucketAccessKey = defaultIfBlank(bucketInfo.getAccessKey(), getAccessKey());
        String bucketSecretKey = defaultIfBlank(bucketInfo.getSecretKey(), getSecretKey());
        // 区域
        String bucketRegion = defaultIfBlank(bucketInfo.getRegion(), getRegion());
        // 基础路径/静态文件基础地址
        TENANT_ROOT_PATH_MAP.put(tenantId, bucketInfo.getRootPath());
        TENANT_BUCKET_INFO_MAP.put(tenantId, bucketInfo);
        try {
            // 创建认证凭证
            StaticCredentialsProvider credentialsProvider = StaticCredentialsProvider.create(
                AwsBasicCredentials.create(bucketAccessKey, bucketSecretKey));
            // 创建模式配置
            S3Configuration.Builder s3ConfigurationBuilder = S3Configuration.builder().chunkedEncodingEnabled(false);

            // S3Client 构建(用于上传下载等操作)
            S3ClientBuilder s3Builder = S3Client.builder()
                .region(Region.of(bucketRegion))
                .credentialsProvider(credentialsProvider)
                .endpointOverride(URI.create(practicalEndPoint));

            // S3Presigner 构建(用于生成签名地址)
            Builder s3Presignerbuilder = S3Presigner.builder()
                .region(Region.of(bucketRegion))
                .credentialsProvider(credentialsProvider)
                .endpointOverride(URI.create(bucketEndPoint));

            // 是否path模式
            if (GeneralJudgeEnum.CONFIRM.getValue().equals(practicalPathMode)) {
                s3ConfigurationBuilder.pathStyleAccessEnabled(true);
            }
            S3Configuration s3Configuration = s3ConfigurationBuilder.build();
            s3Builder.serviceConfiguration(s3Configuration);
            s3Presignerbuilder.serviceConfiguration(s3Configuration);

            S3Client s3Client = s3Builder.build();
            CLIENT_MAP.put(tenantId, s3Client);
            S3Presigner s3Presigner = s3Presignerbuilder.build();
            PRESIGNER_MAP.put(tenantId, s3Presigner);
            // 有内网配置地址时，文件操作用内网接入点
            s3Presignerbuilder.endpointOverride(URI.create(bucketInnerPoint));
            S3Presigner s3InnerPresigner = s3Presignerbuilder.build();
            PRESIGNER_MAP.put(tenantId + INNER_SUFFIX, s3InnerPresigner);
        } catch (Exception e) {
            log.error("初始化 S3Client/S3Presigner 出错", e);
        }
    }

    public static S3Client getS3Client() {
        return CLIENT_MAP.get(UserThreadContext.getTenantId());
    }

    public static S3Presigner getS3Presigner() {
        String suffix = checkIntranetNetwork() ? INNER_SUFFIX : "";
        return PRESIGNER_MAP.get(UserThreadContext.getTenantId() + suffix);
    }

    public static String getRootPath() {
        return TENANT_ROOT_PATH_MAP.get(UserThreadContext.getTenantId());
    }

    public static TenantBucketInfo getTenantBucketInfo() {
        return TENANT_BUCKET_INFO_MAP.get(UserThreadContext.getTenantId());
    }

    public static String getFileUrl(GetObjectPresignRequest presignRequest) {
        String url = getS3Presigner().presignGetObject(presignRequest).url()
            .toString();
        return replaceDomainWithTargetUrl(url, getTenantBucketInfo());
    }

    public String getRoot() {
        String tenantBucketName = TENANT_ROOT_PATH_MAP.get(UserThreadContext.getTenantId());
        return root + SEPARATOR + tenantBucketName;
    }

    /**
     * 获取做课模板的path
     */
    public String getZuoKeTemplatePath() {
        String templateAllPath = getRoot();
        if (!StringUtils.startsWith(getRoot(), SEPARATOR)) {
            templateAllPath = SEPARATOR + getRoot();
        }
        return templateAllPath + zuoKeTemplatePath;
    }

    public File queryZuoKeTemplate() {
        File file = new File(zuoKeTemplatePath);
        if (file.exists()) {
            return file;
        } else {
            return new File(getPhysicalPath(getZuoKeTemplatePath()));
        }
    }

    public String queryTempPath() {
        String tempPath = getRoot();
        if (!StringUtils.startsWith(getRoot(), SEPARATOR)) {
            tempPath = SEPARATOR + getRoot();
        }
        if (!StringUtils.endsWith(getRoot(), SEPARATOR)) {
            return tempPath + SEPARATOR + TEMP_FILE + SEPARATOR + DateUtil.getYmStr();
        }
        return tempPath + TEMP_FILE + SEPARATOR + DateUtil.getYmStr();
    }

    /**
     * 获得临时目录的绝对路径
     */
    public String queryTempPhysicalPath() {
        return getPhysicalPath(queryTempPath());
    }

    public String queryTempPath(String path) {
        String tempPath = queryTempPath();
        if (!StringUtils.startsWith(path, SEPARATOR)) {
            tempPath = tempPath + SEPARATOR + path;
        } else {
            tempPath = tempPath + path;
        }
        return tempPath;
    }

    public String getPhysicalPath(String path) {
        String physicalPath = location;
        if (StringUtils.endsWith(physicalPath, SEPARATOR)) {
            physicalPath = physicalPath.substring(0, physicalPath.length() - 1);
        }
        if (!StringUtils.startsWith(path, SEPARATOR)) {
            physicalPath = physicalPath + SEPARATOR + path;
        } else {
            physicalPath = physicalPath + path;
        }
        return physicalPath;
    }

    public String getExportPath(String fileName) {
        String path = getRoot();
        if (!StringUtils.startsWith(getRoot(), SEPARATOR)) {
            path = SEPARATOR + getRoot();
        }
        path =
            path
                + SEPARATOR
                + "file"
                + SEPARATOR
                + DateUtil.getYmStr()
                + SEPARATOR
                + "export"
                + SEPARATOR;
        FileUtil.mkdir(getPhysicalPath(path));
        return path + fileName;
    }

    //获取导出文件的路径
    public String getExportPath(String bizId, String fileName, ExportBizType exportBizType) {
        String path = getRoot();
        if (!StringUtils.startsWith(getRoot(), SEPARATOR)) {
            path = SEPARATOR + getRoot();
        }
        // /upload/exportExcel/202210/typeName/fileName
        path =
            path
                + SEPARATOR
                + "exportExcel"
                + SEPARATOR
                + DateUtil.getYmStr()
                + SEPARATOR
                + exportBizType.name()
                + SEPARATOR
                + bizId
                + SEPARATOR;
        FileUtil.mkdir(getPhysicalPath(path));
        return path + fileName;
    }

    public String queryLecturerPath() {
        String path = getRoot();
        if (!StringUtils.startsWith(getRoot(), SEPARATOR)) {
            path = SEPARATOR + getRoot();
        }
        if (!StringUtils.endsWith(getRoot(), SEPARATOR)) {
            return path
                + SEPARATOR
                + "file"
                + SEPARATOR
                + "lecturerfile"
                + DateUtil.getYmStr()
                + SEPARATOR;
        }
        return path + "file" + SEPARATOR + "lecturerfile" + SEPARATOR + DateUtil.getYmStr() + SEPARATOR;
    }

    public String getFilePathByType(String fileName, String bizType) {
        String path = getRoot();
        if (!StringUtils.startsWith(getRoot(), SEPARATOR)) {
            path = SEPARATOR + getRoot();
        }
        if (!StringUtils.endsWith(getRoot(), SEPARATOR)) {
            return path
                + SEPARATOR
                + "file"
                + SEPARATOR
                + DateUtil.getYmStr()
                + SEPARATOR
                + bizType
                + SEPARATOR + fileName;
        }
        return path + "file" + SEPARATOR + DateUtil.getYmStr() + SEPARATOR + bizType + SEPARATOR + fileName;
    }

    public String getImagePathByType(String imageName, String bizType) {
        String path = getRoot();
        if (!StringUtils.startsWith(getRoot(), SEPARATOR)) {
            path = SEPARATOR + getRoot();
        }
        if (!StringUtils.endsWith(getRoot(), SEPARATOR)) {
            return path
                + SEPARATOR
                + "image"
                + SEPARATOR
                + DateUtil.getYmStr()
                + SEPARATOR
                + bizType
                + SEPARATOR + imageName;
        }
        return path + "image" + SEPARATOR + DateUtil.getYmStr() + SEPARATOR + bizType + SEPARATOR + imageName;
    }

    /**
     * 是对象存储
     *
     * @return
     */
    public boolean checkObjectStorage() {
        return OBJECT_STORAGE_TYPE.equals(storageType);
    }

    /**
     * 是本地存储
     *
     * @return
     */
    public boolean checkLocalStorage() {
        return LOCAL_STORAGE_TYPE.equals(storageType);
    }

    @Bean
    @ConditionalOnProperty(
        prefix = "app",
        name = "storageType",
        havingValue = "1",
        matchIfMissing = true)
    public StorageStrategyService objectStorageStrategy() {
        return new ObjectStorageStrategyImpl();
    }

    @Bean
    @ConditionalOnProperty(
        prefix = "app",
        name = "storageType",
        havingValue = "2",
        matchIfMissing = true)
    public StorageStrategyService localStorageStrategy(SysConfig sysConfig) {
        return new LocalStorageStrategyImpl(sysConfig);
    }

    public String queryLocalTempDirectoryPath() {
        String path = queryTempDirectoryPath();
        String physicalPath = location;
        if (StringUtils.endsWith(physicalPath, SEPARATOR)) {
            physicalPath = physicalPath.substring(0, physicalPath.length() - 1);
        }
        return physicalPath + path;
    }

    public String queryTempDirectoryPath() {
        String path;
        String tempPath = getRoot();
        if (!StringUtils.startsWith(getRoot(), SEPARATOR)) {
            tempPath = SEPARATOR + getRoot();
        }
        if (!StringUtils.endsWith(getRoot(), SEPARATOR)) {
            path = tempPath + SEPARATOR + TEMP_FILE;
        } else {
            path = tempPath + TEMP_FILE;
        }
        return path;
    }

    public String getRegion() {
        // 需要默认值
        return defaultIfBlank(region, "us-east-1");
    }

    public String defaultIfBlank(String value, String defaultValue) {
        return StringUtils.isBlank(value) ? defaultValue : value;
    }

    public static String replaceDomainWithTargetUrl(String originalUrlString, TenantBucketInfo tenantBucketInfo) {
        if (tenantBucketInfo == null || StringUtils.isBlank(tenantBucketInfo.getStaticBaseUrl())
            || tenantBucketInfo.getStaticBaseUrl().equals(tenantBucketInfo.getEndPoint())
            // 内网环境不替换域名
            || checkIntranetNetwork()) {
            return originalUrlString;
        }
        return originalUrlString.replace(tenantBucketInfo.getEndPoint(), tenantBucketInfo.getStaticBaseUrl());
    }

    // 获取Host 地址
    public static Boolean checkIntranetNetwork() {
        try {
            HttpServletRequest request = WebUtil.getRequest();
            // 非HTTP请求过来，拿不到request抛出异常
            if (Objects.isNull(request)) {
                throw new BusinessException(UserErrorNoEnum.ERR_OAUTH2_ILLEGAL_ENTRY_TYPE);
            }

            // IPv4地址正则表达式
            String regex = "((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})(\\.((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})){3}";
            // 编译regex这个正则表达式，得到代表此正则表达式的对象
            Pattern pattern = Pattern.compile(regex);
            // 看data数据里面有没有和该正则表达式相匹配的内容
            String serverName = request.getServerName();
            // 检查常见的本地主机名
            if ("localhost".equals(serverName) || "::1".equals(serverName)) {
                return true;
            }
            Matcher m = pattern.matcher(serverName);
            // 匹配器的find方法若返回true，则客户机提交的数据里面有和正则表达式相匹配的内容
            return m.find();
        } catch (Exception e) {
            log.error("网络来源host解析失败 error", e);
            // 默认为外网
            return false;
        }
    }
}
