package com.wunding.learn.file.job;

import com.wunding.learn.common.constant.dynamicDataSource.DynamicDataSourceConstant;
import com.wunding.learn.common.constant.other.ErrorNoEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.other.SystemConfigCodeEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.file.service.IFilesService;
import com.wunding.learn.user.api.service.ParaFeign;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import jakarta.annotation.Resource;
import java.util.Map;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * <p>
 * 文件服务定时任务
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">z<PERSON><PERSON><PERSON><PERSON></a>
 * @date 2024/6/6 11:38
 */
@Slf4j
@Component("fileJob")
public class Job {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private ParaFeign paraFeign;

    @Resource
    private IFilesService filesService;

    /**
     * 删除指定临时文件
     */
    @XxlJob("deleteTempFile")
    public ReturnT<String> deleteTempFile() {
        Map<String, String> dataSources = redisTemplate.<String, String>opsForHash().entries(
            com.wunding.learn.common.constant.tenant.TenantRedisKeyConstant.DB_KEY);
        if (CollectionUtils.isEmpty(dataSources)) {
            throw new BusinessException(ErrorNoEnum.ERR_NOT_EXISTS);
        }
        for (String dbName : dataSources.keySet()) {
            UserThreadContext.setTenantId(dbName.replace(DynamicDataSourceConstant.DB_LOOK_UP_KEY_PREFIX, ""));
            // 查询配置的删除时间
            String paraValue = Optional.ofNullable(
                paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_919.getCode())).orElse("7");
            filesService.deleteTempFile(Integer.parseInt(paraValue));
        }
        UserThreadContext.remove();
        return ReturnT.SUCCESS;
    }
}
