package com.wunding.learn.file.service;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.file.api.dto.FileListDTO;
import com.wunding.learn.file.api.dto.MimeInfoDTO;
import com.wunding.learn.file.api.query.FileQuery;
import com.wunding.learn.file.constant.FileTypeEnum;
import com.wunding.learn.file.dto.HomeWorkUploadZipResultDTO;
import com.wunding.learn.file.dto.RelateWaterMarkImgDTO;
import com.wunding.learn.file.dto.ScormInfoDTO;
import com.wunding.learn.file.dto.UploadCourseResultDTO;
import com.wunding.learn.file.dto.UploadResultDTO;
import com.wunding.learn.file.dto.UploadZipResultDTO;
import com.wunding.learn.file.model.Files;
import com.wunding.learn.file.query.ImportExcelQuery;
import jakarta.annotation.Nullable;
import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">xiao8</a>
 * @since 2022-03-02
 */
public interface IFilesService extends IService<Files> {


    /**
     * 保存文件
     *
     * @param uploadFile 上传的文件
     * @return 文件路径
     */
    UploadResultDTO saveUploadFile(MultipartFile uploadFile, String fileName);

    /**
     * 验证文件类型
     *
     * @param originalFilename 原始文件名
     * @param types            支持的文件类型
     * @return 检查结果
     */
    boolean checkFileType(String originalFilename, String[] types);

    /**
     * 验证文件类型
     *
     * @param originalFilename 原始文件名
     * @param types            支持的文件类型
     * @return 检查结果
     */
    boolean checkFileTypeInLine(String originalFilename, String[] types);

    /**
     * 按业务id和业务类型获取文件，如果多条返回第一条
     *
     * @param bizId   业务id
     * @param bizType 业务类型
     * @return 文件实体对象
     */
    Files getFileByBizId(String bizId, String bizType);

    /**
     * 按业务id和业务类型获取文件，如果多条返回第一条
     *
     * @param bizId   业务id
     * @param bizType 业务类型
     * @return 文件实体对象
     */
    Files getFileLoadByBizId(String bizId, String bizType);

    /**
     * 按业务id和业务类型获取文件，如果多条返回第一条
     *
     * @param bizId    业务id
     * @param bizType  业务类型
     * @param isSource 是否是原文件
     * @return 文件实体对象
     */
    Files getFileByBizId(String bizId, String bizType, Boolean isSource);

    /**
     * 按业务id和业务类型获取文件列表
     *
     * @param bizId   业务id
     * @param bizType 业务类型
     * @return 文件实体列表
     */
    List<Files> getFileListByBizId(String bizId, String bizType);

    /**
     * 按业务id和业务类型获取文件列表 批量
     *
     * @param bizIds  业务ids
     * @param bizType 业务类型
     * @return 文件实体列表
     */
    List<Files> getFileListByBizIds(Collection<String> bizIds, String bizType);

    /**
     * 按业务id和业务类型获取文件列表 批量 isSource=1
     *
     * @param bizIds  业务ids
     * @param bizType 业务类型
     * @return 文件实体列表
     */
    List<Files> getSourceFileListByBizIds(Collection<String> bizIds, String bizType);


    /**
     * 按业务ID删除文件
     *
     * @param buzId 业务id
     */
    void deleteFileByBizId(String buzId);

    /**
     * 根据关联模块和附件类别筛选文件
     *
     * @param categoryId   关联模块Id
     * @param categoryType 关联模块
     * @param isAdjunct    是否为附件
     * @return 文件
     */
    Files getFileByCategoryTypeAndIsAdjunct(String categoryId, String categoryType, Integer isAdjunct);

    /**
     * 根据关联模块id获取文件
     *
     * @param categoryId 关联模块Id
     * @return 文件信息
     */
    Files getFileByCategoryId(String categoryId);

    /**
     * 根据关联模块id获取文件
     *
     * @param categoryId 关联模块Id
     * @param isSource   是否源文件
     * @param isAdjunct  是否附件
     * @return 文件信息
     */
    Files getFileByCategoryIdAndIsSourceAndIsAdjunct(String categoryId, Integer isSource, Integer isAdjunct);

    /**
     * 根据关联模块ids批量获取文件
     *
     * @param categoryIds 关联模块id集合
     * @return 文件列表
     */
    List<Files> getFileMapByCategoryIds(Collection<String> categoryIds);

    /**
     * 根据关联模块ids和类型精确批量获取文件
     *
     * @param categoryIds 关联模块id集合
     * @return 文件信息
     */
    List<Files> getSourceFileMapByCategoryIds(Collection<String> categoryIds);

    /**
     * 根据id和类型以及文件名模糊分页查询
     *
     * @param fileQuery 文件查询对象
     * @return 文件对象对象
     */
    PageInfo<FileListDTO> getFileByFileQuery(FileQuery fileQuery);

    /**
     * 上传课件
     *
     * @param uploadFile 课件文件
     * @return 上传文件信息
     */
    UploadCourseResultDTO uploadCourseWareFile(MultipartFile uploadFile);

    /**
     * 新闻资讯上传文件
     *
     * @param uploadFile 文件
     * @param suffix     文件后缀
     * @return 上传文件信息
     */
    UploadCourseResultDTO uploadFileInfo(MultipartFile uploadFile, Set<String> suffix);

    /**
     * 创建课件文件信息
     *
     * @param uploadCourseResultDTO 课件上传文件统一返回对象
     */
    void buildCourseWareFileInfo(UploadCourseResultDTO uploadCourseResultDTO);

    /**
     * 保存文件至临时目录
     *
     * @param multipartFile 文件
     * @param suffixes      校验的格式
     * @return 文件相关信息
     */
    UploadResultDTO saveUploadFile(MultipartFile multipartFile, Set<String> suffixes);

    MimeInfoDTO getMineInfo(String path);

    ScormInfoDTO getScormInfo(String zipFilePath);

    /**
     * 按bizId获取文件
     *
     * @param bizIds   业务id
     * @param bizType  业务类型
     * @param isSource 来源(可以为空，为空查出全部)
     * @return {@link List }<{@link Files }>
     */
    List<Files> getFileByBizIds(Collection<String> bizIds, String bizType, @Nullable Boolean isSource);

    /**
     * 上传zip文件并解压返回
     *
     * @param uploadFile 上传文件
     * @return 课件上传文件统一返回对象
     */
    UploadZipResultDTO uploadZipFileToDecompress(MultipartFile uploadFile);

    /**
     * 认证明细证书证书套打
     *
     * @param relateWaterMarkImgDTO 认证明细套打图片对象 {@link  RelateWaterMarkImgDTO}
     * @throws IOException IO异常
     */
    void createCertRelateWaterMarkImg(RelateWaterMarkImgDTO relateWaterMarkImgDTO) throws IOException;

    /**
     * 按业务id和业务类型获取文件，如果多条返回第一条
     *
     * @param bizId     业务id
     * @param bizType   业务类型
     * @param isSource  是否是原文件
     * @param sortField 排序字段
     * @param isAsc     是否正序排列
     * @return 文件实体对象
     */
    Files getFileByBizId(String bizId, String bizType, Boolean isSource, SFunction<Files, Object> sortField,
        Boolean isAsc);

    /**
     * 通过外部证书图片添加学员证书
     *
     * @param uploadFile 上传文件
     * @return 文件上传文件统一返回对象
     */
    UploadZipResultDTO uploadCertRelateZip(MultipartFile uploadFile);

    /**
     * 上传zip更新外部证书图片
     *
     * @param uploadFile 上传文件
     * @return 课件上传文件统一返回对象
     */
    UploadZipResultDTO uploadCertPhotoZip(MultipartFile uploadFile);

    /**
     * 通用检查文件导入
     *
     * @param query 文件查询对象
     */
    void checkExcel(ImportExcelQuery query);

    /**
     * 根据关联模块ids批量获取主文件
     *
     * @param relateWaterMarkImgDTO
     * @return 预览图片base64
     * @throws IOException
     */
    String getImgBase64(String imgPath, RelateWaterMarkImgDTO relateWaterMarkImgDTO)
        throws IOException;

    /**
     * 根据关联模块ids批量获取主文件
     *
     * @param categoryIds 关联模块id集合
     * @return 文件列表
     */
    List<Files> getMainFileMapByCategoryIds(Collection<String> categoryIds);

    /**
     * 上传辅导文件
     *
     * @param uploadFile 上传文件
     * @return 课件上传文件统一返回对象
     */
    UploadCourseResultDTO uploadFormFile(MultipartFile uploadFile);

    /**
     * 面授上传作业（zip、excel）
     *
     * @param uploadFile 上传文件
     * @return 作业上传文件统一返回对象
     */
    HomeWorkUploadZipResultDTO uploadHomeWorkFile(MultipartFile uploadFile);

    /**
     * 根据id获取下载url（一般下载课件源文件）
     *
     * @param id id
     * @return {@link String}
     */
    String getDownloadUrl(String id);


    List<Files> getFileUrlByIdsAndType(Set<String> ids, String bizType);

    /**
     * 保存文件至正式路径
     *
     * @param uploadFile 上传的文件
     * @return 文件路径
     */
    UploadResultDTO saveUploadFileToFormalPath(MultipartFile uploadFile, String fileName, FileTypeEnum fileTypeEnum);

    /**
     * 删除指定时间以前的临时文件
     *
     * @param deleteTime 指定天数
     */
    void deleteTempFile(Integer deleteTime);

    /**
     * 根据关联模块ids批量获取文件
     *
     * @param categoryIds 关联模块id集合
     * @return 文件列表
     */
    List<Files> getFilesCategoryIds(Collection<String> categoryIds);
}
