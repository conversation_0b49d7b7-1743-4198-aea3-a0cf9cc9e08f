package com.wunding.learn.file.feign;

import static com.wunding.learn.common.util.string.StringUtil.newId;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.constant.file.FileErrorNoEnum;
import com.wunding.learn.common.constant.other.ErrorNoEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.file.FileType;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.util.assertion.CheckObjAllFieldsIsNullUtil;
import com.wunding.learn.common.util.bean.BeanListUtils;
import com.wunding.learn.common.util.date.DateUtil;
import com.wunding.learn.common.util.http.DifyApiClient;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.util.string.StringPool;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.file.api.constant.FileBizType;
import com.wunding.learn.file.api.constant.ImageBizType;
import com.wunding.learn.file.api.dto.CertRelateWaterMarkImgInfoDTO;
import com.wunding.learn.file.api.dto.CopyFileDTO;
import com.wunding.learn.file.api.dto.FileDownloadDTO;
import com.wunding.learn.file.api.dto.FileListDTO;
import com.wunding.learn.file.api.dto.MimeInfoDTO;
import com.wunding.learn.file.api.dto.NamePath;
import com.wunding.learn.file.api.dto.SaveBatchFileDTO;
import com.wunding.learn.file.api.dto.SaveFileDTO;
import com.wunding.learn.file.api.dto.SaveOrUpdatePicListDTO;
import com.wunding.learn.file.api.dto.UserAvatarsDTO;
import com.wunding.learn.file.api.dto.VideoClarityDTO;
import com.wunding.learn.file.api.dto.ZipDownloadDTO;
import com.wunding.learn.file.api.dto.export.project.TrainHomeWorkFileDownloadDTO;
import com.wunding.learn.file.api.dto.export.project.TrainHomeWorkZipDownloadDTO;
import com.wunding.learn.file.api.query.FileQuery;
import com.wunding.learn.file.api.service.FileFeign;
import com.wunding.learn.file.config.SysConfig;
import com.wunding.learn.file.constant.FileTypeEnum;
import com.wunding.learn.file.dao.FilesDao;
import com.wunding.learn.file.dto.RelateWaterMarkImgDTO;
import com.wunding.learn.file.dto.ScormInfoDTO;
import com.wunding.learn.file.mapper.VideoClarityMapper;
import com.wunding.learn.file.model.ExportRecord;
import com.wunding.learn.file.model.Files;
import com.wunding.learn.file.model.Images;
import com.wunding.learn.file.model.UploadFileRequest;
import com.wunding.learn.file.model.UploadFileResponse;
import com.wunding.learn.file.model.VideoClarity;
import com.wunding.learn.file.service.IExportRecordService;
import com.wunding.learn.file.service.IFilesService;
import com.wunding.learn.file.service.IImagesService;
import com.wunding.learn.file.service.StorageStrategyService;
import com.wunding.learn.file.service.UploadFileService;
import com.wunding.learn.file.util.FileUtil;
import com.wunding.learn.file.util.UrlHelper;
import com.wunding.learn.file.util.ZipCompressorUtil;
import com.wunding.learn.file.util.ZipUtil;
import jakarta.annotation.Resource;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.extractor.WordExtractor;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @date 2022/3/2
 */
@RestController
@RequestMapping("${module.file.contentPath:/}")
@Slf4j
public class FileFeignImpl implements FileFeign {

    public static final String THE_FILE_CATEGORY_INFO_FORMAT = "the file category id is {}";

    private static final String SEPARATOR = "/";
    private static final String UNDERLINE = "_";
    private static final String NEW_CHECK = "/new/";
    private static final String NEW = "new";
    private static final String ADMIN = "admin";
    public static final String SCRIPT_SRC = "<script src=\"";
    public static final String JQUERY_1_10_2_MIN_JS_SCRIPT = "jquery-1.10.2.min.js\"></script>";
    public static final String ML_SCORM_JS_SCRIPT = "ml-scorm.js\"></script>";
    public static final String POSTMESSAGE_PLUGIN_JS_SCRIPT = "postmessage.plugin.js\"></script>";
    public static final String COPY_FILE_INFO_FORMAT = "the file name {} and it's current path is {}";
    public static final String COMPRESSOR_FILE_LIST_EXCEPTION = "compressorFileList exception";

    @Resource
    @Lazy
    private IFilesService filesService;

    @Resource
    @Lazy
    private IImagesService imagesService;

    @Resource
    private UrlHelper urlHelper;

    @Resource
    private UploadFileService uploadFileService;

    @Resource
    private SysConfig sysConfig;

    @Resource
    private IExportRecordService exportRecordService;

    @Resource
    private VideoClarityMapper videoClarityMapper;

    @Resource
    private StorageStrategyService storageStrategyService;

    @Resource(name = "filesDao")
    private FilesDao filesDao;

    @Resource
    private Executor commonTaskThreadPool;

    @Resource
    private DifyApiClient difyApiClient;

    @Override
    public SaveFileDTO saveFileWithLog(String bizId, String bizType, String name, String tempFilePath) {
        UploadFileResponse uploadFileResponse = saveFile(FileTypeEnum.FILE, tempFilePath, bizType, bizId);
        log.info("正式文件信息：{}", JsonUtil.objToJson(uploadFileResponse));
        Files files = buildFiles(name, bizId, bizType, uploadFileResponse.getFileUrl(),
            uploadFileResponse.getFileSize());
        filesDao.saveFiles(files);
        return buildSaveFileDTO(uploadFileResponse.getFileUrl(), tempFilePath, files.getId(), bizId, bizType,
            files.getFileSize());
    }

    @Override
    public SaveFileDTO saveFile(String bizId, String bizType, String name, String tempFilePath) {
        UploadFileResponse uploadFileResponse = saveFile(FileTypeEnum.FILE, tempFilePath, bizType, bizId);
        log.info("正式文件信息：{}", JsonUtil.objToJson(uploadFileResponse));
        Files files = buildFiles(name, bizId, bizType, uploadFileResponse.getFileUrl(),
            uploadFileResponse.getFileSize());
        filesService.save(files);
        return buildSaveFileDTO(uploadFileResponse.getFileUrl(), tempFilePath, files.getId(), bizId, bizType,
            files.getFileSize());
    }

    @Override
    public void saveOrUpdateOneFile(String bizId, String bizType, String name, String tempFilePath, Boolean isInsert,
        Integer deleteAttachment) {
        if (StringUtils.isNotEmpty(tempFilePath) && Boolean.TRUE.equals(isInsert)) {
            saveFile(bizId, bizType, name, tempFilePath);
        }
        if (Boolean.FALSE.equals(isInsert)) {
            NamePath dbNamePath = getFileNamePath(bizId, bizType);
            // 编辑
            boolean hasPathCondition =
                dbNamePath != null && dbNamePath.getPath() != null && StringUtils.isNotBlank(tempFilePath);
            if (hasPathCondition && !dbNamePath.getPath().equals(tempFilePath)) {
                deleteFileByBizIdAndBizType(bizId, bizType, deleteAttachment);
                saveFile(bizId, bizType, name, tempFilePath);
            }
            // 保存
            if (CheckObjAllFieldsIsNullUtil.checkObjAllFieldsIsNull(dbNamePath) && StringUtils.isNotBlank(
                tempFilePath)) {
                saveFile(bizId, bizType, name, tempFilePath);
            }
            // 删除
            if (!CheckObjAllFieldsIsNullUtil.checkObjAllFieldsIsNull(dbNamePath) && StringUtils.isBlank(tempFilePath)) {
                deleteFileByBizIdAndBizType(bizId, bizType, deleteAttachment);
            }
        }
    }

    @Override
    public long getFileSize(String tempFilePath) {
        UploadFileResponse uploadFileResponse = saveFile(FileTypeEnum.FILE, tempFilePath, "", "");
        Files files = buildFiles("", "", "", uploadFileResponse.getFileUrl(), uploadFileResponse.getFileSize());
        return files.getFileSize();
    }

    @Override
    public SaveFileDTO saveSourceFile(String bizId, String bizType, String name, String tempFilePath) {
        UploadFileResponse uploadFileResponse = saveFile(FileTypeEnum.FILE, tempFilePath, bizType, false, true, bizId);
        Files files = buildFiles(name, bizId, bizType, uploadFileResponse.getFileUrl(),
            uploadFileResponse.getFileSize(), true);
        filesService.save(files);
        return buildSaveFileDTO(uploadFileResponse.getFileUrl(), tempFilePath, files.getId(), bizId, bizType,
            files.getFileSize());
    }

    @Override
    public SaveFileDTO saveDir(String bizId, String bizType, String name, String tempDirPath) {
        UploadFileResponse uploadFileResponse = saveDir(FileTypeEnum.FILE, tempDirPath, bizType, bizId);
        Files files = buildFiles(name, bizId, bizType, uploadFileResponse.getFileUrl(),
            uploadFileResponse.getFileSize());
        filesService.save(files);
        return buildSaveFileDTO(uploadFileResponse.getFileUrl(), tempDirPath, files.getId(), bizId, bizType,
            files.getFileSize());
    }

    @Override
    public SaveFileDTO saveAttachmentFile(String bizId, String bizType, String attachmentFileTempPath,
        String attachmentFileTempName) {

        //必须保证主文件已经存在
        Files fileByBizId = filesService.getFileByBizId(bizId, bizType);
        if (null == fileByBizId) {
            throw new BusinessException(ErrorNoEnum.ERR_FILE_NOT_EXIST);
        }
        //更新主文件
        fileByBizId.setIsAttachment(1);
        filesService.updateById(fileByBizId);

        UploadFileResponse uploadFileResponse = saveFile(FileTypeEnum.FILE, attachmentFileTempPath, bizType, bizId);
        Files files = buildFiles(attachmentFileTempName, bizId, bizType, uploadFileResponse.getFileUrl(),
            uploadFileResponse.getFileSize());

        //保存附件
        files.setIsAdjunct(1);
        filesService.save(files);
        return buildSaveFileDTO(uploadFileResponse.getFileUrl(), attachmentFileTempPath, files.getId(), bizId, bizType,
            files.getFileSize());
    }

    @Override
    public List<SaveFileDTO> saveFiles(String bizId, String bizType, List<NamePath> tempFileList) {
        List<SaveFileDTO> resultList = new ArrayList<>();
        List<Files> filesList = new ArrayList<>();
        for (NamePath namePath : tempFileList) {
            UploadFileResponse uploadFileResponse = saveFile(FileTypeEnum.FILE, namePath.getPath(), bizType, bizId);
            Files files = buildFiles(namePath.getName(), bizId, bizType, uploadFileResponse.getFileUrl(),
                uploadFileResponse.getFileSize());
            filesList.add(files);
            resultList.add(
                buildSaveFileDTO(uploadFileResponse.getFileUrl(), namePath.getPath(), files.getId(), bizId, bizType,
                    files.getFileSize()));
        }
        //这里只保存一条数据
        filesService.saveBatch(filesList);
        return resultList;
    }

    @Override
    public void deleteFileByFileId(String fileId) {
        LambdaQueryWrapper<Files> query = new LambdaQueryWrapper<>();
        query.eq(Files::getId, fileId);
        filesService.remove(query);
    }

    @Override
    public void deleteFileByBizIdAndBizType(String bizId, String bizType, Integer deleteAttachment) {
        LambdaQueryWrapper<Files> query = new LambdaQueryWrapper<>();
        query.eq(Files::getCategoryId, bizId);
        query.eq(Files::getCategoryType, bizType);

        if (deleteAttachment == 0) {
            query.eq(Files::getIsAdjunct, 0);
        }

        if (deleteAttachment == -1) {
            query.eq(Files::getIsAdjunct, 1);
        }
        filesService.remove(query);
    }

    @Override
    public void deleteFileByBizIdListAndBizType(String bizIds, String bizType, Integer deleteAttachment) {
        List<String> bizIdList = Collections.singletonList(bizIds);
        LambdaQueryWrapper<Files> query = new LambdaQueryWrapper<>();
        query.in(Files::getCategoryId, bizIdList);
        query.eq(Files::getCategoryType, bizType);

        if (deleteAttachment == 0) {
            query.eq(Files::getIsAdjunct, 0);
        }

        if (deleteAttachment == -1) {
            query.eq(Files::getIsAdjunct, 1);
        }
        filesService.remove(query);
    }

    @Override
    public void deleteFileByFileIds(Collection<String> fileIds) {
        List<Files> files = filesService.listByIds(fileIds);
        files.forEach(f -> filesDao.delFiles(f));
    }

    @Override
    public SaveFileDTO saveImage(String bizId, String bizType, String name, String tempFilePath) {
        UploadFileResponse uploadFileResponse = saveFile(FileTypeEnum.IMAGE, tempFilePath, bizType, bizId);
        Images images = buildImages(name, bizId, bizType, uploadFileResponse.getFileUrl(),
            uploadFileResponse.getFileSize(), 0, 0);
        imagesService.save(images);
        return buildSaveFileDTO(uploadFileResponse.getFileUrl(), tempFilePath, images.getId(), bizId, bizType,
            images.getImageSize());
    }

    @Override
    public void saveDefaultImage(String bizId, String bizType, String name, String oosFilePath) {
        Images images = buildImages(name, bizId, bizType, SEPARATOR + sysConfig.getRoot() + oosFilePath, 0L, 0, 0);
        imagesService.save(images);
    }

    @Override
    public SaveFileDTO saveImage2(String bizId, String bizType, String name, String tempFilePath, Integer width,
        Integer height) {
        UploadFileResponse uploadFileResponse = saveFile(FileTypeEnum.IMAGE, tempFilePath, bizType, bizId);
        Images images = buildImages(name, bizId, bizType, uploadFileResponse.getFileUrl(),
            uploadFileResponse.getFileSize(), width, height);
        imagesService.save(images);
        return buildSaveFileDTO(uploadFileResponse.getFileUrl(), tempFilePath, images.getId(), bizId, bizType,
            images.getImageSize());
    }

    @Override
    public void deleteImageByBizIdAndBizType(String bizId, String bizType) {
        imagesService.remove(
            Wrappers.<Images>lambdaQuery().eq(Images::getCategoryId, bizId).eq(Images::getCategoryType, bizType));
    }

    @Override
    public void deleteImageByBizIdListAndBizType(List<String> bizIdList, String bizType) {
        imagesService.remove(
            Wrappers.<Images>lambdaQuery().in(Images::getCategoryId, bizIdList).eq(Images::getCategoryType, bizType));
    }

    @Override
    public void saveOrUpdateOnePic(String bizId, String bizType, String name, String tempFilePath, Boolean isInsert) {
        if (StringUtils.isNotEmpty(tempFilePath) && Boolean.TRUE.equals(isInsert)) {
            saveImage(bizId, bizType, name, tempFilePath);
        }
        if (Boolean.FALSE.equals(isInsert)) {
            NamePath dbNamePath = getImageFileNamePath(bizId, bizType);
            // 编辑
            boolean hasPathCondition =
                dbNamePath != null && dbNamePath.getPath() != null && StringUtils.isNotBlank(tempFilePath);
            if (hasPathCondition && !dbNamePath.getPath().equals(tempFilePath)) {
                deleteImageByBizIdAndBizType(bizId, bizType);
                saveImage(bizId, bizType, name, tempFilePath);
            }
            // 保存
            if (CheckObjAllFieldsIsNullUtil.checkObjAllFieldsIsNull(dbNamePath) && StringUtils.isNotBlank(
                tempFilePath)) {
                saveImage(bizId, bizType, name, tempFilePath);
            }
            // 删除
            if (!CheckObjAllFieldsIsNullUtil.checkObjAllFieldsIsNull(dbNamePath) && StringUtils.isBlank(tempFilePath)) {
                deleteImageByBizIdAndBizType(bizId, bizType);
            }
        }
    }

    @Override
    public void deleteImageByImageId(String imageId) {
        imagesService.deleteImageByImageId(imageId);
    }

    @Override
    public void deleteImageByImagesIds(List<String> imagesIds) {
        imagesService.deleteImageByImagesIds(imagesIds);
    }

    @Override
    public List<SaveFileDTO> saveImages(String bizId, String bizType, List<NamePath> tempFileList) {
        List<SaveFileDTO> resultList = new ArrayList<>();
        List<Images> imagesList = new ArrayList<>();
        Integer beginIndex = 0;
        // 该资源下的最大序号
        Integer maxSortNo = imagesService.getMaxSortNoByBizIdAndBizType(bizId, bizType);
        if (Optional.ofNullable(maxSortNo).isPresent()) {
            beginIndex = ++maxSortNo;
        }
        for (NamePath namePath : tempFileList) {
            UploadFileResponse uploadFileResponse = saveFile(FileTypeEnum.IMAGE, namePath.getPath(), bizType, bizId);
            Images images = buildImages(namePath.getName(), bizId, bizType, uploadFileResponse.getFileUrl(),
                uploadFileResponse.getFileSize(), 0, 0);
            images.setSortNo(beginIndex++);
            imagesList.add(images);
            resultList.add(
                buildSaveFileDTO(uploadFileResponse.getFileUrl(), namePath.getPath(), images.getId(), bizId, bizType,
                    images.getImageSize()));
        }
        imagesService.saveOrUpdateBatch(imagesList);
        return resultList;
    }

    @Override
    public List<SaveFileDTO> saveOrUpdateImages(String bizId, String bizType, List<NamePath> list) {
        Set<String> imagesIdSet = list.stream().map(NamePath::getId).filter(StringUtils::isNotBlank)
            .collect(Collectors.toSet());
        Map<String, Images> imagesMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(imagesIdSet)) {
            imagesMap.putAll(imagesService.listByIds(imagesIdSet).stream()
                .collect(Collectors.toMap(Images::getId, Function.identity(), (key1, key2) -> key1)));
        }
        List<SaveFileDTO> resultList = new ArrayList<>();
        List<Images> imagesList = new ArrayList<>();
        int beginIndex = 0;
        Set<String> idSet = new HashSet<>();
        for (NamePath namePath : list) {
            if (StringUtils.isEmpty(namePath.getId())) {
                // 新增的图片
                UploadFileResponse uploadFileResponse = saveFile(FileTypeEnum.IMAGE, namePath.getPath(), bizType,
                    bizId);
                Images images = buildImages(namePath.getName(), bizId, bizType, uploadFileResponse.getFileUrl(),
                    uploadFileResponse.getFileSize(), 0, 0);
                images.setSortNo(beginIndex++);
                imagesList.add(images);
                resultList.add(
                    buildSaveFileDTO(uploadFileResponse.getFileUrl(), namePath.getPath(), images.getId(), bizId,
                        bizType,
                        images.getImageSize()));
            } else {
                // 保留的图片
                Images images = Optional.ofNullable(imagesMap.get(namePath.getId())).orElse(new Images().setId(""));
                // 更新新的排序
                images.setSortNo(beginIndex++);
                imagesList.add(images);
                idSet.add(namePath.getId());
                resultList.add(
                    buildSaveFileDTO(namePath.getPath(), namePath.getPath(), namePath.getId(), bizId, bizType,
                        namePath.getFileSize()));
            }
        }

        // 删除不存在的图片
        imagesService.remove(
            Wrappers.<Images>lambdaQuery().eq(Images::getCategoryId, bizId).eq(Images::getCategoryType, bizType)
                .notIn(!CollectionUtils.isEmpty(idSet), Images::getId, idSet));

        imagesService.saveOrUpdateBatch(imagesList);
        return resultList;
    }

    @Override
    public List<SaveFileDTO> saveImageBatch(Collection<SaveFileDTO> saveFileDTOList) {
        if (CollectionUtils.isEmpty(saveFileDTOList)) {
            return Collections.emptyList();
        }
        List<SaveFileDTO> result = new ArrayList<>();
        for (SaveFileDTO saveFileDTO : saveFileDTOList) {
            String path =
                StringUtils.isEmpty(saveFileDTO.getPath()) ? saveFileDTO.getTempPath() : saveFileDTO.getPath();
            SaveFileDTO fileDTO = saveImage(saveFileDTO.getBizId(), saveFileDTO.getBizType(), "", path);
            result.add(fileDTO);
        }
        return result;
    }

    @Override
    public String getFileUrl(String path) {
        return urlHelper.getStaticFullUrl(path);
    }

    @Override
    public Map<String, String> getFileUrl(Collection<String> paths) {
        return paths.stream().collect(Collectors.toMap(path -> path, this::getFileUrl));
    }

    /**
     * 保存文件到正式目录(解压.zip包)
     *
     * @param fileType   文件保存目录类型
     * @param sourceFile 原文件路径
     * @param bizType    文件业务类型
     * @return 移动后的路径
     */
    private UploadFileResponse saveFile(FileTypeEnum fileType, String sourceFile, String bizType, String bizId) {
        return saveFile(fileType, sourceFile, bizType, true, false, bizId);
    }

    /**
     * 保存文件到正式目录
     *
     * @param fileType   件类型
     * @param sourceFile 文件路径
     * @param bizType    业务类型
     * @param unzip      是否需要解压zip包
     * @param isAsync    是否异步上传
     * @return
     */
    private UploadFileResponse saveFile(FileTypeEnum fileType, String sourceFile, String bizType, boolean unzip,
        boolean isAsync, String bizId) {

        // 定义 File 对象
        File source = new File(sysConfig.getPhysicalPath(sourceFile));

        // 验证对象存储是否存在
        boolean ossExists = storageStrategyService.checkOssIsExists(sourceFile);

        log.info("验证对象存储是否存在。对象：{} 文件类型：{}  bizType：{} unzip：{}  是否存在：{}", sourceFile,
            fileType.getValue(), bizType,
            unzip, ossExists);

//        // 兼容旧代码（可能是多余的错误逻辑）
//        if (!ossExists) {
//            source = uploadFileService.downloadFile(sourceFile);
//            log.info("非对象存储，执行文件下载：{}", sourceFile);
//        }

        String lastPath = FilenameUtils.getName(sourceFile);

        if (sourceFile.endsWith("html")) {
            source = new File(sysConfig.getPhysicalPath(FilenameUtils.getFullPathNoEndSeparator(sourceFile)));
        }

        if ("index".equalsIgnoreCase(FilenameUtils.getBaseName(sourceFile))) {
            lastPath = sourceFile.substring(
                FilenameUtils.getFullPathNoEndSeparator(sourceFile).lastIndexOf(SEPARATOR) + 1);
        }

        String strBuilder =
            SEPARATOR + sysConfig.getRoot() + SEPARATOR + fileType.getValue() + SEPARATOR + DateUtil.getYmStr()
                + SEPARATOR + bizType;
        if (sourceFile.contains(NEW_CHECK)) {
            strBuilder += SEPARATOR + NEW;
        }
        strBuilder += SEPARATOR + lastPath;

        UploadFileRequest uploadFileRequest = new UploadFileRequest();
        uploadFileRequest.setFileName(strBuilder);
        uploadFileRequest.setAsyncUpload(isAsync);
        if (sourceFile.endsWith("html")) {
            // 在线做课，其他文件要公有读
            uploadFileRequest.setDefaultStrategyFileList(List.of(sysConfig.getPhysicalPath(sourceFile)));
        }

        if (source.isDirectory()) {
            uploadFileRequest.setCopyFolderFlag(true);
            uploadFileRequest.setFolderSrcPath(
                sysConfig.getPhysicalPath(FilenameUtils.getFullPathNoEndSeparator(sourceFile)));
            uploadFileRequest.setFolderTargetPath(FilenameUtils.getFullPathNoEndSeparator(strBuilder));
        } else {
            uploadFileRequest.setCopyFolderFlag(false);
            uploadFileRequest.setTempFilePath(sysConfig.getPhysicalPath(sourceFile));
        }

        UploadFileResponse uploadFileResponse;

        if (ossExists) {

            if (sourceFile.equals(strBuilder)) {

                log.info("目标对象存储已存在，直接返回。目标路径：{}", sourceFile);

                uploadFileResponse = new UploadFileResponse();
                uploadFileResponse.setFileUrl(strBuilder);
                uploadFileResponse.setFilePath(strBuilder);
            } else {

                log.info("执行对象存储Copy。{} 到 {}", sourceFile, strBuilder);

                // 执行对象存储 Copy
                uploadFileResponse = storageStrategyService.copyFile(sourceFile, strBuilder, false);
            }
        } else {
            uploadFileRequest.setBizId(bizId);
            log.info("非对象存储，执行文件上传：{}", uploadFileRequest);

            uploadFileResponse = uploadFileService.uploadFile(uploadFileRequest);
        }

        // 无需解压，直接返回
        if (!unzip) {
            return uploadFileResponse;
        }
        // 需要解压，返回路径
        String path = unZipAndUpload(sourceFile, strBuilder, bizId);
        if (StringUtils.isNotBlank(path)) {
            uploadFileResponse.setFilePath(path);
            uploadFileResponse.setFileUrl(path);
        }

        return uploadFileResponse;
    }

    public String unZipAndUpload(String tempFilePath, String zipFilePath, String bizId) {
        if (!"zip".equalsIgnoreCase(FilenameUtils.getExtension(zipFilePath))) {
            return null;
        }
        try {
            log.info("saveFile sourceFilePath:{},targetFilePath:{}", sysConfig.getPhysicalPath(tempFilePath),
                sysConfig.getPhysicalPath(zipFilePath));
            if (!Objects.equals(tempFilePath, zipFilePath)) {
                File srcFile = new File(sysConfig.getPhysicalPath(tempFilePath));
                File targetFile = new File(sysConfig.getPhysicalPath(zipFilePath));
                if (!targetFile.getParentFile().exists()) {
                    //确保目标目录存在
                    FileUtil.mkdir(targetFile.getParentFile().getAbsolutePath());
                }
                if (targetFile.exists()) {
                    if (targetFile.length() != srcFile.length()) {
                        FileUtil.deleteFile(sysConfig.getPhysicalPath(zipFilePath));
                        FileUtils.copyFile(srcFile, targetFile);
                    }
                } else {
                    FileUtils.copyFile(srcFile, targetFile);
                }
            }
            File srcFile = new File(sysConfig.getPhysicalPath(tempFilePath));
            File targetFile = new File(sysConfig.getPhysicalPath(zipFilePath));
            log.info("unZipAndUpload srcFile size:{},targetFile size:{}", srcFile.length(), targetFile.length());
        } catch (IOException e) {
            throw new BusinessException(FileErrorNoEnum.COPY_FILE_FAIL);
        }
        try {
            String outDirPath = StringUtils.substring(zipFilePath, 0, zipFilePath.length() - 4);
            String outDirPhysicalPath = sysConfig.getPhysicalPath(
                StringUtils.substring(zipFilePath, 0, zipFilePath.length() - 4));
            //将压缩包解压

            ZipUtil.unZip(sysConfig.getPhysicalPath(zipFilePath), outDirPhysicalPath);
            UploadFileRequest dirUploadFileRequest = new UploadFileRequest();
            dirUploadFileRequest.setFolderTargetPath(outDirPath);
            dirUploadFileRequest.setAsyncUpload(false);
            dirUploadFileRequest.setFolderSrcPath(outDirPhysicalPath);
            dirUploadFileRequest.setCopyFolderFlag(true);
            dirUploadFileRequest.setBizId(bizId);
            ScormInfoDTO scormInfo = filesService.getScormInfo(zipFilePath);
            dirUploadFileRequest.setDefaultStrategyFileList(
                List.of(outDirPhysicalPath + StringPool.SLASH + scormInfo.getHref()));
            UploadFileResponse uploadFileResponse = uploadFileService.uploadFile(dirUploadFileRequest);
            log.info("uploadFileResponse:{}", JsonUtil.objToJson(uploadFileResponse));
            if (scormInfo != null && StringUtils.isNotBlank(scormInfo.getFileName())) {
                log.info("path:" + outDirPath + SEPARATOR + scormInfo.getFileName());
                return outDirPath + SEPARATOR + scormInfo.getFileName();
            }
        } catch (Exception e) {
            log.error("解压课件失败...", e);
            throw new BusinessException(FileErrorNoEnum.ERR_FILE_UNZIP);
        }

        return null;
    }


    /**
     * 保存文件夹到正式目录
     *
     * @param fileType   文件保存目录类型
     * @param sourceFile 原文件路径
     * @param bizType    文件业务类型
     * @return 移动后的路径
     */
    private UploadFileResponse saveDir(FileTypeEnum fileType, String sourceFile, String bizType, String bizId) {
        String targetDir =
            SEPARATOR + fileType.getValue() + SEPARATOR + DateUtil.getYmStr() + SEPARATOR + bizType + SEPARATOR
                + FilenameUtils.getName(sourceFile);
        return uploadFileService.uploadDir(sysConfig.getPhysicalPath(sourceFile), targetDir, bizId);
    }

    /**
     * 构建实体对象
     *
     * @param fileName    文件名
     * @param bizId       业务id
     * @param bizType     业务类型
     * @param currentPath 文件路径
     * @param fileSize    文件大小
     * @return 文件实体对象
     */
    private Files buildFiles(String fileName, String bizId, String bizType, String currentPath, Long fileSize) {
        return buildFiles(fileName, bizId, bizType, currentPath, fileSize, false);
    }

    /**
     * 构建实体对象
     *
     * @param fileName    文件名
     * @param bizId       业务id
     * @param bizType     业务类型
     * @param currentPath 文件路径
     * @param fileSize    文件大小
     * @param isSource    是否是原文件
     * @return 文件实体对象
     */
    private Files buildFiles(String fileName, String bizId, String bizType, String currentPath, Long fileSize,
        Boolean isSource) {
        Files files = new Files();
        files.setId(newId());
        files.setFileSize(fileSize);
        files.setFileName(fileName);
        files.setCreateTime(new Date());
        files.setCreateBy(UserThreadContext.getUserId());
        files.setCategoryId(bizId);
        files.setCategoryType(bizType);
        files.setCurrentPath(currentPath);
        if (null != isSource && isSource) {
            files.setIsSource(GeneralJudgeEnum.CONFIRM.getValue());
        }
        return files;
    }

    /**
     * 构建实体对象
     *
     * @param fileName    文件名
     * @param bizId       业务id
     * @param bizType     业务类型
     * @param currentPath 文件路径
     * @param fileSize    文件大小
     * @param width       宽
     * @param height      高
     * @return 文件实体对象
     */
    private Images buildImages(String fileName, String bizId, String bizType, String currentPath, Long fileSize,
        Integer width, Integer height) {
        Images images = new Images();
        images.setId(newId());
        images.setImageName(fileName);
        images.setImageSize(fileSize);
        images.setCreateTime(new Date());
        images.setCreateBy(UserThreadContext.getUserId());
        images.setCategoryId(bizId);
        images.setCategoryType(bizType);
        images.setCurrentPath(currentPath);
        images.setWidth(width);
        images.setHeight(height);
        return images;
    }

    /**
     * 构建返回对象
     *
     * @param path         正式文件路径
     * @param tempFilePath 临时文件路径
     * @param fileId       文件id
     * @param bizId        业务id
     * @param bizType      业务类型
     * @param fileSize     文件大小
     * @return 返回对象
     */
    private SaveFileDTO buildSaveFileDTO(String path, String tempFilePath, String fileId, String bizId, String bizType,
        Long fileSize) {
        SaveFileDTO saveFileDTO = new SaveFileDTO();
        saveFileDTO.setPath(path);
        saveFileDTO.setId(fileId);
        saveFileDTO.setTempPath(tempFilePath);
        saveFileDTO.setBizId(bizId);
        saveFileDTO.setBizType(bizType);
        saveFileDTO.setUrl(urlHelper.getStaticFullUrl(path));
        saveFileDTO.setFileSize(fileSize);
        return saveFileDTO;
    }

    @Override
    public String getFileUrl(String bizId, String bizType) {
        Files files = filesService.getFileByBizId(bizId, bizType);
        if (files == null) {
            return null;
        }
        return urlHelper.getStaticFullUrl(files.getCurrentPath());
    }

    @Override
    public String getSourceFileUrl(String bizId, String bizType) {
        Files files = filesService.getFileByBizId(bizId, bizType, true);
        if (files == null) {
            return null;
        }
        return urlHelper.getStaticFullUrl(files.getCurrentPath());
    }

    @Override
    public Map<String, String> getSourceFileUrls(List<String> bizIds, String bizType) {
        if (CollectionUtils.isEmpty(bizIds)) {
            return Collections.emptyMap();
        }
        List<Files> fileList = filesService.getFileByBizIds(bizIds, bizType, true);
        return fileList.stream().collect(
            Collectors.toMap(Files::getCategoryId, files -> urlHelper.getStaticFullUrl(files.getCurrentPath())));
    }

    @Override
    public NamePath getSourceFileInfo(String bizId, String bizType) {
        Files fileByBizId = filesService.getFileByBizId(bizId, bizType, true);
        if (fileByBizId == null) {
            return null;
        }
        NamePath namePath = new NamePath();
        namePath.setId(fileByBizId.getId());
        namePath.setName(fileByBizId.getFileName());
        namePath.setPath(fileByBizId.getCurrentPath());
        namePath.setUrl(urlHelper.getStaticFullUrl(fileByBizId.getCurrentPath()));
        namePath.setFileSize(fileByBizId.getFileSize());
        return namePath;
    }

    @Override
    public NamePath getSourceFileInfoOfPhysicalPath(String bizId, String bizType) {
        NamePath sourceFileInfo = getSourceFileInfo(bizId, bizType);
        if (null != sourceFileInfo) {
            sourceFileInfo.setPath(sysConfig.getPhysicalPath(sourceFileInfo.getPath()));
        }
        return sourceFileInfo;
    }

    @Override
    public List<NamePath> getSourceFileInfos(List<String> bizIds, String bizType, boolean isSource) {
        List<NamePath> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(bizIds)) {
            return list;
        }
        List<Files> fileList = filesService.getFileByBizIds(bizIds, bizType, isSource);
        fileList.forEach(files -> {
            NamePath namePath = new NamePath();
            namePath.setId(files.getId());
            namePath.setName(files.getFileName());
            namePath.setPath(files.getCurrentPath());
            namePath.setUrl(urlHelper.getStaticFullUrl(files.getCurrentPath()));
            namePath.setFileSize(files.getFileSize());
            namePath.setCategoryId(files.getCategoryId());
            list.add(namePath);
        });

        return list;
    }

    @Override
    public NamePath getVideoNamePath(String bizId, String bizType) {
        Files fileByBizId = filesService.getFileByBizId(bizId, bizType);
        if (fileByBizId == null) {
            return null;
        }
        NamePath namePath = new NamePath();
        namePath.setId(fileByBizId.getId());
        namePath.setName(fileByBizId.getFileName());
        namePath.setPath(fileByBizId.getCurrentPath());
        namePath.setUrl(urlHelper.getStaticFullUrl(fileByBizId.getCurrentPath()));
        namePath.setFileSize(fileByBizId.getFileSize());
        return namePath;
    }

    @Override
    public NamePath getImageFileNamePath(String bizId, String bizType) {
        Images images = imagesService.getFileByBizId(bizId, bizType);
        if (Objects.isNull(images)) {
            return null;
        }
        NamePath namePath = new NamePath();
        namePath.setId(images.getId());
        namePath.setName(images.getImageName());
        namePath.setPath(images.getCurrentPath());
        namePath.setUrl(urlHelper.getStaticFullUrl((images.getCurrentPath())));
        namePath.setWidth(images.getWidth());
        namePath.setHeight(images.getHeight());
        return namePath;
    }


    @Override
    public NamePath getFileNamePath(String bizId, String bizType) {
        Files file = filesService.getFileByBizId(bizId, bizType);
        NamePath namePath = new NamePath();
        if (Objects.isNull(file)) {
            return null;
        }
        namePath.setId(file.getId());
        namePath.setName(file.getFileName());
        namePath.setPath(file.getCurrentPath());
        namePath.setUrl(urlHelper.getStaticFullUrl((file.getCurrentPath())));
        namePath.setFileSize(file.getFileSize());
        return namePath;
    }


    @Override
    public List<NamePath> getFileNamePaths(String bizId, String bizType) {
        List<Files> filesList = filesService.getFileListByBizId(bizId, bizType);
        List<NamePath> list = new ArrayList<>();
        for (Files files : filesList) {
            NamePath namePath = new NamePath();
            namePath.setId(files.getId());
            namePath.setName(files.getFileName());
            namePath.setPath(files.getCurrentPath());
            namePath.setUrl(urlHelper.getStaticFullUrl((files.getCurrentPath())));
            namePath.setSortNo(files.getSortNo());
            namePath.setFileSize(files.getFileSize());
            list.add(namePath);
        }
        return list;
    }

    @Override
    public List<NamePath> getImageFileNamePaths(String bizId, String bizType) {
        List<Images> fileListByBizId = imagesService.getFileListByBizId(bizId, bizType);
        List<NamePath> namePaths = new ArrayList<>();
        if (!CollectionUtils.isEmpty(fileListByBizId)) {
            for (Images images : fileListByBizId) {
                NamePath namePath = new NamePath();
                namePath.setId(images.getId());
                namePath.setName(images.getImageName());
                namePath.setPath(images.getCurrentPath());
                namePath.setUrl(urlHelper.getStaticFullUrl((images.getCurrentPath())));
                namePath.setFileSize(images.getImageSize());
                namePath.setWidth(images.getWidth());
                namePath.setHeight(images.getHeight());
                namePath.setSortNo(images.getSortNo());
                namePaths.add(namePath);
            }
        }
        return namePaths;
    }


    @Override
    public List<NamePath> getImageFileNamePathsByBizIds(Collection<String> bizId, String bizType) {
        List<NamePath> namePaths = new ArrayList<>();
        if (CollectionUtils.isEmpty(bizId)) {
            return namePaths;
        }
        List<Images> fileListByBizId = imagesService.getFileListByBizIds(bizId, bizType);
        fileListByBizId.sort(Comparator.comparingInt(Images::getSortNo));
        if (!CollectionUtils.isEmpty(fileListByBizId)) {
            for (Images images : fileListByBizId) {
                NamePath namePath = new NamePath();
                namePath.setName(images.getImageName());
                namePath.setPath(images.getCurrentPath());
                namePath.setId(images.getId());
                namePath.setCategoryId(images.getCategoryId());
                namePath.setUrl(urlHelper.getStaticFullUrl((images.getCurrentPath())));
                namePath.setFileSize(images.getImageSize());
                namePath.setWidth(images.getWidth());
                namePath.setHeight(images.getHeight());
                namePaths.add(namePath);
            }
        }
        return namePaths;
    }

    @Override
    public Map<String, List<NamePath>> getImageFileNamePathsByBizIdsOfMap(Collection<String> bizId, String bizType) {
        if (CollectionUtils.isEmpty(bizId)) {
            return new HashMap<>();
        }
        List<Images> fileListByBizId = imagesService.getFileListByBizIds(bizId, bizType);
        fileListByBizId.sort(Comparator.comparingInt(Images::getSortNo));
        return fileListByBizId.stream()
            .collect(Collectors.groupingBy(Images::getCategoryId, Collectors.mapping(images -> {
                NamePath namePath = new NamePath();
                namePath.setName(images.getImageName());
                namePath.setPath(images.getCurrentPath());
                namePath.setId(images.getId());
                namePath.setCategoryId(images.getCategoryId());
                namePath.setUrl(urlHelper.getStaticFullUrl((images.getCurrentPath())));
                namePath.setFileSize(images.getImageSize());
                namePath.setWidth(images.getWidth());
                namePath.setHeight(images.getHeight());
                return namePath;
            }, Collectors.toList())));
    }

    @Override
    public Map<String, NamePath> getImageFileNamePathMapByBizIds(Collection<String> bizId, String bizType) {
        List<NamePath> fileNamePathsByBizIds = getImageFileNamePathsByBizIds(bizId, bizType);
        return fileNamePathsByBizIds.stream()
            .collect(Collectors.toMap(NamePath::getCategoryId, Function.identity(), (key1, key2) -> key1));
    }

    @Override
    public Map<String, String> getFileUrls(List<String> bizIds, String bizType) {
        if (CollectionUtils.isEmpty(bizIds)) {
            return Collections.emptyMap();
        }
        List<Files> fileList = filesService.getFileByBizIds(bizIds, bizType, false);
        return fileList.stream().collect(
            Collectors.toMap(Files::getCategoryId, files -> urlHelper.getStaticFullUrl(files.getCurrentPath())));
    }

    @Override
    public String getImageUrl(String bizId, String bizType) {
        Images images = imagesService.getFileByBizId(bizId, bizType);
        if (images == null) {
            return null;
        }
        return urlHelper.getStaticFullUrl(images.getCurrentPath());
    }

    @Override
    public String getImagePath(String bizId, String bizType) {
        Images images = imagesService.getFileByBizId(bizId, bizType);
        if (images == null) {
            return null;
        }
        return images.getCurrentPath();
    }

    @Override
    public Map<String, String> getUserAvatar(Collection<String> userIds) {
        Map<String, String> avatarMap = new HashMap<>();
        if (CollectionUtils.isEmpty(userIds)) {
            return avatarMap;
        }
        List<Images> userAvatar = imagesService.getUserAvatar(userIds);
        Optional.ofNullable(userAvatar).ifPresent(list -> {
            for (Images images : list) {
                avatarMap.put(images.getCategoryId(), urlHelper.getStaticFullUrl(images.getCurrentPath()));
            }
        });
        return avatarMap;
    }

    @Override
    public List<String> getImageUrls(String bizId, String bizType) {
        List<String> resultList = new ArrayList<>();
        List<Images> imagesList = imagesService.getFileListByBizId(bizId, bizType);
        if (CollectionUtils.isEmpty(imagesList)) {
            return new ArrayList<>();
        }
        for (Images images : imagesList) {
            resultList.add(urlHelper.getStaticFullUrl(images.getCurrentPath()));
        }
        return resultList;
    }

    @Override
    public Map<String, String> getImageUrlsByIds(Collection<String> ids, String bizType) {
        if (CollectionUtils.isEmpty(ids)) {
            return new HashMap<>(0);
        }
        List<Images> imagesList = imagesService.getFileListByBizIds(ids, bizType);
        return urlHelper.getStaticFullUrl(imagesList);
    }

    @Override
    public Map<String, String> getImagePathsByIds(Collection<String> ids, String bizType) {
        if (CollectionUtils.isEmpty(ids)) {
            return new HashMap<>(0);
        }
        List<Images> imagesList = imagesService.getFileListByBizIds(ids, bizType);
        return imagesList.stream()
            .collect(Collectors.toMap(Images::getCategoryId, Images::getCurrentPath, (key1, key2) -> key1));
    }

    @Override
    public Map<String, List<String>> getImagesUrlsByIds(Collection<String> ids, String bizType) {
        if (CollectionUtils.isEmpty(ids)) {
            return new HashMap<>(0);
        }
        List<Images> imagesList = imagesService.getFileListByBizIds(ids, bizType);
        return imagesList.stream().collect(Collectors.groupingBy(Images::getCategoryId,
            Collectors.mapping(i -> urlHelper.getStaticFullUrl(i.getCurrentPath()), Collectors.toList())));
    }

    /**
     * 获取用户头像列表
     *
     * @param userIds 用户id
     * @return 用户头像列表
     */
    @Override
    public List<UserAvatarsDTO> getUserAvatars(List<String> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return new ArrayList<>();
        }
        List<Images> list = imagesService.getFileListByBizIds(userIds, ImageBizType.Avatar.name());
        return list.stream().map(v -> UserAvatarsDTO.builder().Avatar(urlHelper.getStaticFullUrl(v.getCurrentPath()))
            .userId(v.getCategoryId()).build()).collect(Collectors.toList());
    }

    @Override
    public void copySameBizImage(String sourceBizId, String targetBizId, String bizType) {
        List<Images> sourceImageList = imagesService.getFileListByBizId(sourceBizId, bizType);
        if (CollectionUtils.isEmpty(sourceImageList)) {
            return;
        }
        copyImages(sourceImageList, targetBizId, bizType);
    }

    @Override
    public void copySameBizImageList(List<CopyFileDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        // 根据分类类型分组，防止有不同类型时查询不对
        Map<String, List<CopyFileDTO>> listMap = list.stream().collect(Collectors.groupingBy(CopyFileDTO::getBizType));
        for (Map.Entry<String, List<CopyFileDTO>> listEntry : listMap.entrySet()) {
            // 存在图片的资源
            List<Images> imagesList = imagesService.getFileListByBizIds(
                listEntry.getValue().stream().map(CopyFileDTO::getSourceBizId).collect(Collectors.toSet()),
                listEntry.getKey());
            log.info("--------end--------> {}", imagesList.size());
            // 实际有图片的才进行复制保存
            if (!CollectionUtils.isEmpty(imagesList)) {
                // 每个资源对应的图片
                Map<String, List<Images>> imageMap = imagesList.stream()
                    .collect(Collectors.groupingBy(Images::getCategoryId));
                Map<String, CopyFileDTO> copyFileDTOMap = listEntry.getValue().stream()
                    .collect(Collectors.toMap(CopyFileDTO::getSourceBizId, Function.identity(), (k1, k2) -> k1));
                // 有图片的资源
                Set<String> hasImagesCategoryIds = imagesList.stream().map(Images::getCategoryId)
                    .collect(Collectors.toSet());
                hasImagesCategoryIds.forEach(categoryId -> {
                    CopyFileDTO copyFileDTO = copyFileDTOMap.get(categoryId);
                    List<Images> sourceImageList = imageMap.get(categoryId);
                    copyImages(sourceImageList, copyFileDTO.getTargetBizId(), copyFileDTO.getBizType());
                });
            }
        }
    }

    private void copyImages(List<Images> sourceImageList, String targetBizId, String bizType) {
        List<Images> images = new ArrayList<>();
        for (Images sourceImage : sourceImageList) {
            String sourceImagePath = sourceImage.getCurrentPath();
            // 拷贝到原来的文件夹，只需要替换文件名
            String targetImagePath;
            try {
                targetImagePath = sourceImagePath.replace(
                    sourceImagePath
                        .substring(sourceImagePath.lastIndexOf(SEPARATOR) + 1, sourceImagePath.lastIndexOf(".")),
                    newId());
            } catch (Exception e) {
                targetImagePath = sourceImagePath;
            }

            UploadFileResponse uploadFileResponse = uploadFileService
                .copyFile(sourceImagePath, targetImagePath);
            Images targetImage = buildImages(sourceImage.getImageName(), targetBizId,
                bizType,
                uploadFileResponse.getFileUrl(), uploadFileResponse.getFileSize(), 0, 0);
            images.add(targetImage);
        }
        imagesService.saveBatch(images);
    }

    @Override
    public void copySameBizFile(String sourceBizId, String targetBizId, String bizType) {
        List<Files> sourceFileList = filesService.getFileListByBizId(sourceBizId, bizType);
        log.info("{}类型文件引用复制：{} -> {}，", bizType, sourceBizId, targetBizId);
        if (CollectionUtils.isEmpty(sourceFileList)) {
            return;
        }
        ArrayList<Files> files = new ArrayList<>();

        for (Files sourceFile : sourceFileList) {
            Files targetFile = buildFiles(sourceFile.getFileName(), targetBizId, bizType, sourceFile.getCurrentPath(),
                sourceFile.getFileSize());
            targetFile.setIsSource(sourceFile.getIsSource());
            targetFile.setIsAdjunct(sourceFile.getIsAdjunct());
            targetFile.setIsAttachment(sourceFile.getIsAttachment());
            files.add(targetFile);
        }
        filesService.saveBatch(files);

        LambdaQueryWrapper<VideoClarity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VideoClarity::getCategoryId, sourceBizId);
        List<VideoClarity> videoClarityList = videoClarityMapper.selectList(queryWrapper);
        for (VideoClarity videoClarity : videoClarityList) {
            videoClarity.setCategoryId(targetBizId);
            videoClarity.setCreateBy(UserThreadContext.getUserId());
            videoClarity.setCreateTime(new Date());
            videoClarity.setUpdateBy(UserThreadContext.getUserId());
            videoClarity.setUpdateTime(new Date());
            videoClarity.setId(null);
            videoClarityMapper.insert(videoClarity);
        }
    }

    @Override
    public void copySameBizFileList(List<CopyFileDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        // 根据分类类型分组，防止有不同类型时查询不对
        Map<String, List<CopyFileDTO>> listMap = list.stream().collect(Collectors.groupingBy(CopyFileDTO::getBizType));
        for (Map.Entry<String, List<CopyFileDTO>> entry : listMap.entrySet()) {
            // 查询有文件资源的
            List<Files> sourceFileList = filesService.getFileListByBizIds(
                entry.getValue().stream().map(CopyFileDTO::getSourceBizId).collect(Collectors.toSet()), entry.getKey());
            if (CollectionUtils.isEmpty(sourceFileList)) {
                return;
            }
            // 有文件资源的
            Set<String> hasFilesCategoryIds = sourceFileList.stream().map(Files::getCategoryId)
                .collect(Collectors.toSet());

            // 这是需要进行复制的
            List<CopyFileDTO> copyFileDTOS = entry.getValue().stream()
                .filter(copyFileDTO -> hasFilesCategoryIds.contains(copyFileDTO.getSourceBizId())).collect(
                    Collectors.toList());

            Map<String, List<Files>> filesMap = sourceFileList.stream()
                .collect(Collectors.groupingBy(Files::getCategoryId));

            for (CopyFileDTO copyFileDTO : copyFileDTOS) {

                ArrayList<Files> files = new ArrayList<>();

                for (Files sourceFile : filesMap.get(copyFileDTO.getSourceBizId())) {
                    Files targetFile = buildFiles(sourceFile.getFileName(), copyFileDTO.getTargetBizId(),
                        copyFileDTO.getBizType(),
                        sourceFile.getCurrentPath(),
                        sourceFile.getFileSize());
                    targetFile.setIsSource(sourceFile.getIsSource());
                    targetFile.setIsAdjunct(sourceFile.getIsAdjunct());
                    targetFile.setIsAttachment(sourceFile.getIsAttachment());
                    files.add(targetFile);
                }
                filesService.saveBatch(files);

                LambdaQueryWrapper<VideoClarity> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(VideoClarity::getCategoryId, copyFileDTO.getSourceBizId());
                List<VideoClarity> videoClarityList = videoClarityMapper.selectList(queryWrapper);
                for (VideoClarity videoClarity : videoClarityList) {
                    videoClarity.setCategoryId(copyFileDTO.getTargetBizId());
                    videoClarity.setCreateBy(UserThreadContext.getUserId());
                    videoClarity.setCreateTime(new Date());
                    videoClarity.setUpdateBy(UserThreadContext.getUserId());
                    videoClarity.setUpdateTime(new Date());
                    videoClarity.setId(null);
                    videoClarityMapper.insert(videoClarity);
                }
            }
        }
    }

    @Override
    public void copySameFile(String sourceBizId, String sourceType, String targetBizId, String targetType) {
        List<Files> sourceFileList = filesService.getFileListByBizId(sourceBizId, sourceType);
        log.info("文件引用复制：{}/{} -> {}/{}，", sourceType, sourceBizId, targetType, targetBizId);
        if (CollectionUtils.isEmpty(sourceFileList)) {
            return;
        }
        ArrayList<Files> files = new ArrayList<>();

        for (Files sourceFile : sourceFileList) {
            Files targetFile = buildFiles(sourceFile.getFileName(), targetBizId, targetType,
                sourceFile.getCurrentPath(), sourceFile.getFileSize());
            targetFile.setIsSource(sourceFile.getIsSource());
            targetFile.setIsAdjunct(sourceFile.getIsAdjunct());
            targetFile.setIsAttachment(sourceFile.getIsAttachment());
            files.add(targetFile);
        }
        filesService.saveBatch(files);

        LambdaQueryWrapper<VideoClarity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VideoClarity::getCategoryId, sourceBizId);
        List<VideoClarity> videoClarityList = videoClarityMapper.selectList(queryWrapper);
        for (VideoClarity videoClarity : videoClarityList) {
            videoClarity.setCategoryId(targetBizId);
            videoClarity.setCreateBy(UserThreadContext.getUserId());
            videoClarity.setCreateTime(new Date());
            videoClarity.setUpdateBy(UserThreadContext.getUserId());
            videoClarity.setUpdateTime(new Date());
            videoClarity.setId(null);
            videoClarityMapper.insert(videoClarity);
        }
    }

    @Override
    public void copyDifferentBizImage(String sourceBizId, String sourceBizType, String targetBizId,
        String targetBizType) {
        List<Images> sourceImageList = imagesService.getFileListByBizId(sourceBizId, sourceBizType);
        if (CollectionUtils.isEmpty(sourceImageList)) {
            return;
        }
        List<Images> images = new ArrayList<>();
        for (Images sourceImage : sourceImageList) {
            Images image = buildImages(sourceImage.getImageName(), targetBizId, targetBizType,
                sourceImage.getCurrentPath(), sourceImage.getImageSize(), 0, 0);
            images.add(image);
        }
        imagesService.saveBatch(images);
    }

    @Override
    public void copyDifferentBizFile(String sourceBizId, String sourceBizType, String targetBizId,
        String targetBizType) {
        List<Files> sourceFileList = filesService.getFileListByBizId(sourceBizId, sourceBizType);
        if (CollectionUtils.isEmpty(sourceFileList)) {
            return;
        }
        List<Files> files = new ArrayList<>();
        for (Files sourceFile : sourceFileList) {
            Files targetFile = buildFiles(sourceFile.getFileName(), targetBizId, targetBizType,
                sourceFile.getCurrentPath(), sourceFile.getFileSize());
            files.add(targetFile);
        }
        filesService.saveBatch(files);
    }

    @Override
    public NamePath getFileByCategoryTypeAndIsAdjunct(String categoryId, String categoryType, Integer isAdjunct) {
        Files result = filesService.getFileByCategoryTypeAndIsAdjunct(categoryId, categoryType, isAdjunct);
        if (Objects.isNull(result)) {
            return null;
        }
        NamePath namePath = new NamePath();
        namePath.setCategoryId(result.getCategoryId());
        namePath.setName(result.getFileName());
        namePath.setPath(result.getCurrentPath());
        namePath.setUrl(urlHelper.getStaticFullUrl(result.getCurrentPath()));
        return namePath;
    }


    @Override
    public void uploadDir(String source, String target) {
        throw new UnsupportedOperationException();
    }

    @Override
    public NamePath getFileByCategoryId(String categoryId) {
        Files result = filesService.getFileByCategoryId(categoryId);
        NamePath namePath = new NamePath();
        if (result == null) {
            return namePath;
        }
        namePath.setCategoryId(result.getCategoryId());
        namePath.setName(result.getFileName());
        namePath.setPath(result.getCurrentPath());
        namePath.setUrl(urlHelper.getStaticFullUrl(result.getCurrentPath()));
        return namePath;
    }

    @Override
    public NamePath getFileByCategoryIdAndIsSourceAndIsAdjunct(String categoryId, Integer isSource, Integer isAdjunct) {
        Files result = filesService.getFileByCategoryIdAndIsSourceAndIsAdjunct(categoryId, isSource, isAdjunct);
        NamePath namePath = new NamePath();
        if (result == null) {
            return namePath;
        }
        namePath.setCategoryId(result.getCategoryId());
        namePath.setName(result.getFileName());
        namePath.setPath(result.getCurrentPath());
        namePath.setUrl(urlHelper.getStaticFullUrl(result.getCurrentPath()));
        return namePath;
    }

    @Override
    public String getFileViewUrl(String filePath) {
        return urlHelper.getStaticFullUrl((filePath));
    }

    @Override
    public Map<String, NamePath> getFileMapByCategoryIds(Collection<String> categoryIds) {
        List<Files> fileList = filesService.getFileMapByCategoryIds(categoryIds);
        // 附件数据map
        log.info("fileList:" + JsonUtil.objToJson(fileList));
        Map<String, NamePath> adjunctMap = fileList.stream().filter(
                files -> files.getIsAdjunct() != null && files.getIsAdjunct().equals(GeneralJudgeEnum.CONFIRM.getValue()))
            .collect(Collectors.toMap(Files::getCategoryId, dto -> {
                NamePath namePath = new NamePath();
                namePath.setId(dto.getId());
                namePath.setName(dto.getFileName());
                namePath.setPath(dto.getCurrentPath());
                namePath.setUrl(urlHelper.getStaticFullUrl(dto.getCurrentPath()));
                return namePath;
            }));
        // 主数据map
        Map<String, NamePath> collect = new HashMap<>();
        if (!CollectionUtils.isEmpty(fileList)) {
            collect = fileList.stream().filter(files -> files.getIsAdjunct() == null || files.getIsAdjunct()
                .equals(GeneralJudgeEnum.NEGATIVE.getValue())).collect(Collectors.toMap(Files::getCategoryId, dto -> {
                NamePath namePath = new NamePath();
                namePath.setId(dto.getId());
                namePath.setCategoryId(dto.getCategoryId());
                namePath.setName(dto.getFileName());
                namePath.setPath(dto.getCurrentPath());
                namePath.setUrl(urlHelper.getStaticFullUrl(dto.getCurrentPath()));
                // 给主数据添加附件数据
                namePath.setAdjunctResource(adjunctMap.get(namePath.getCategoryId()));
                return namePath;
            }, (key1, key2) -> key2));
        }
        return collect;
    }

    @Override
    public Map<String, NamePath> getSourceFileMapByCategoryIds(Collection<String> categoryIds) {
        List<Files> fileList = filesService.getSourceFileMapByCategoryIds(categoryIds);
        Map<String, NamePath> collect = new HashMap<>();
        if (!CollectionUtils.isEmpty(fileList)) {
            collect = fileList.stream().collect(Collectors.toMap(Files::getCategoryId, dto -> {
                NamePath namePath = new NamePath();
                namePath.setCategoryId(dto.getCategoryId());
                namePath.setName(dto.getFileName());
                namePath.setPath(dto.getCurrentPath());
                namePath.setUrl(urlHelper.getStaticFullUrl(dto.getCurrentPath()));
                return namePath;
            }));
        }
        return collect;
    }

    @Override
    public void editFileCategoryType(String categoryId, String categoryType) {
        filesService.update(new UpdateWrapper<Files>().lambda().set(Files::getCategoryType, categoryType)
            .eq(Files::getCategoryId, categoryId));
    }

    @Override
    public Map<String, String> getFileUrlByIds(Set<String> ids, String bizType) {
        //更新
        if (CollectionUtils.isEmpty(ids)) {
            return new HashMap<>(0);
        }
        List<Files> filesList = filesService.getFileMapByCategoryIds(ids);
        return filesList.stream().collect(
            Collectors.toMap(Files::getCategoryId, f -> urlHelper.getStaticFullUrl(f.getCurrentPath()),
                (key1, key2) -> key1));
    }

    @Override
    public PageInfo<FileListDTO> getFileByFileQuery(FileQuery fileQuery) {
        PageInfo<FileListDTO> pageInfo = filesService.getFileByFileQuery(fileQuery);
        pageInfo.getList().forEach(f -> f.setUrl(urlHelper.getStaticFullUrl(f.getCurrentPath())));
        return pageInfo;
    }

    @Override
    public void updateFileNameById(String id, String fileName) {
        Files files = filesService.getById(id);
        if (Objects.nonNull(files)) {
            files.setFileName(fileName);
            filesDao.updateFiles(files);
        }
    }

    @Override
    public String getTemplateDir() {
        return sysConfig.queryTempPath();
    }

    @Override
    public void compressorFileList(String bizId, String bizType, ZipDownloadDTO zipDownloadDTO) {

        CompletableFuture.runAsync(() -> {
            ExportRecord exportRecord = createExportRecord(bizId, bizType, zipDownloadDTO.getZipName());

            String relativePath = sysConfig.queryTempPath() + SEPARATOR + zipDownloadDTO.getTempPath();
            //临时目录物理地址
            String newTempPath = sysConfig.getPhysicalPath(relativePath);
            FileUtil.mkdir(newTempPath);
            List<String> fileCompressor = new ArrayList<>();
            for (int i = 0; i < zipDownloadDTO.getFileDownloadDTOList().size(); i++) {
                FileDownloadDTO fileDownloadDTO = zipDownloadDTO.getFileDownloadDTOList().get(i);
                LambdaQueryWrapper<Files> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(Files::getCategoryId, fileDownloadDTO.getCategoryId());
                queryWrapper.eq(Files::getCategoryType, fileDownloadDTO.getCategoryType());
                queryWrapper.eq(Files::getIsSource, GeneralJudgeEnum.CONFIRM.getValue());
                log.info(THE_FILE_CATEGORY_INFO_FORMAT, fileDownloadDTO.getCategoryId());
                Files one = filesService.getOne(queryWrapper);
                if (null != one) {
                    try {
                        //将下载文件拷贝到临时目录
                        log.info(COPY_FILE_INFO_FORMAT, one.getFileName(), one.getCurrentPath());
                        File srcFile = uploadFileService.downloadFile(one.getCurrentPath());
                        if (null != srcFile && srcFile.exists()) {
                            String destDir = newTempPath + SEPARATOR + fileDownloadDTO.getLoginName() + UNDERLINE
                                + fileDownloadDTO.getUserName() + UNDERLINE + one.getFileName();
                            FileUtils.copyFile(srcFile, new File(destDir));
                            fileCompressor.add(destDir);
                        }
                    } catch (IOException e) {
                        log.error(COMPRESSOR_FILE_LIST_EXCEPTION, e);
                    }
                }
            }
            // 填充状态并更新
            fillFailStatusAndUpdateIt(exportRecord, fileCompressor);
            // 打包zip,并上传zip文件
            fillSuccessStatusAndUpdateIt(exportRecord, fileCompressor, newTempPath, relativePath,
                zipDownloadDTO.getZipName(), bizId);
        }, commonTaskThreadPool);


    }

    @Override
    public void compressorTrainHomeWorkFileList(String bizId, String bizType,
        TrainHomeWorkZipDownloadDTO zipDownloadDTO) {

        CompletableFuture.runAsync(() -> {
            ExportRecord exportRecord = createExportRecord(bizId, bizType, zipDownloadDTO.getZipName());
            String relativePath =
                sysConfig.queryTempPath() + SEPARATOR + zipDownloadDTO.getTempPath() + SEPARATOR + bizId;

            //临时目录物理地址
            String newTempPath = sysConfig.getPhysicalPath(relativePath);
            FileUtil.mkdir(newTempPath);
            List<String> fileCompressor = new ArrayList<>();
            for (int i = 0; i < zipDownloadDTO.getFileDownloadDTOList().size(); i++) {
                TrainHomeWorkFileDownloadDTO fileDownloadDTO = zipDownloadDTO.getFileDownloadDTOList().get(i);
                LambdaQueryWrapper<Files> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(Files::getCategoryId, fileDownloadDTO.getCategoryId());
                queryWrapper.eq(Files::getCategoryType, fileDownloadDTO.getCategoryType());
                queryWrapper.eq(Files::getIsSource, GeneralJudgeEnum.CONFIRM.getValue());
                log.info(THE_FILE_CATEGORY_INFO_FORMAT, fileDownloadDTO.getCategoryId());
                Files one = filesService.getOne(queryWrapper);
                if (null != one) {
                    try {
                        //将下载文件拷贝到临时目录
                        log.info(COPY_FILE_INFO_FORMAT, one.getFileName(), one.getCurrentPath());
                        File srcFile = uploadFileService.downloadFile(one.getCurrentPath());

                        if (null != srcFile && srcFile.exists()) {

                            // 压缩文件目录处理：班级名称/作业名称（具体的作业文件在对应目录中，后续会递归添加至压缩文件包中）
                            trainHomeWorkFileDownloadMakeDirs(newTempPath, fileDownloadDTO, fileCompressor);

                            // 培训项目作业打包下载：班级名称/作业名称/用户名_姓名_文件名（具体的作业文件在对应目录中，后续会递归添加至压缩文件包中，此处无需添加至压缩文件列表）
                            String destDir = newTempPath + SEPARATOR + fileDownloadDTO.getProName() + SEPARATOR
                                + fileDownloadDTO.getWorkName() + SEPARATOR + fileDownloadDTO.getLoginName() + UNDERLINE
                                + fileDownloadDTO.getUserName() + UNDERLINE + one.getFileName();
                            FileUtils.copyFile(srcFile, new File(destDir));
                        }
                    } catch (IOException e) {
                        log.error(COMPRESSOR_FILE_LIST_EXCEPTION, e);
                    }
                }
            }
            // 填充状态并更新（本方法当下载文件为空时才会执行）
            fillFailStatusAndUpdateIt(exportRecord, fileCompressor);

            // 打包zip,并上传zip文件
            fillSuccessStatusAndUpdateIt(exportRecord, fileCompressor, newTempPath, relativePath,
                zipDownloadDTO.getZipName(), bizId);
        }, commonTaskThreadPool);
    }

    /**
     * 培训项目作业打包下载目录创建：班级名称/作业名称/用户名_姓名_文件名
     *
     * @param newTempPath     临时目录物理地址
     * @param fileDownloadDTO 文件下载信息
     * @param fileCompressor  压缩文件列表
     */
    private static void trainHomeWorkFileDownloadMakeDirs(String newTempPath,
        TrainHomeWorkFileDownloadDTO fileDownloadDTO,
        List<String> fileCompressor) {

        // 创建班级目录
        String classDir = newTempPath + SEPARATOR + fileDownloadDTO.getProName() + SEPARATOR;
        File classDirFile = new File(classDir);
        if (!classDirFile.exists()) {
            FileUtil.mkdir(classDir);
            // 仅对顶级目录添加至压缩文件列表，否则后续递归添加至压缩文件列表时，会重复添加
            fileCompressor.add(classDir);
        }

        // 创建作业目录
        String workDir = newTempPath + SEPARATOR + fileDownloadDTO.getProName() + SEPARATOR
            + fileDownloadDTO.getWorkName() + SEPARATOR;
        File workDirFile = new File(workDir);
        if (!workDirFile.exists()) {
            FileUtil.mkdir(workDir);
        }
    }

    @Override
    public void compressorFilesList(String bizId, String bizType, ZipDownloadDTO zipDownloadDTO) {

        CompletableFuture.runAsync(() -> {
            ExportRecord exportRecord = createExportRecord(bizId, bizType, zipDownloadDTO.getZipName());

            String relativePath = sysConfig.queryTempPath() + SEPARATOR + zipDownloadDTO.getTempPath();
            //临时目录物理地址
            String newTempPath = sysConfig.getPhysicalPath(relativePath);
            FileUtil.mkdir(newTempPath);
            List<String> fileCompressor = new ArrayList<>();
            // 检验是否存在同名文件
            Map<String, Integer> nameMap = new HashMap<>();
            for (int i = 0; i < zipDownloadDTO.getFileDownloadDTOList().size(); i++) {
                FileDownloadDTO fileDownloadDTO = zipDownloadDTO.getFileDownloadDTOList().get(i);
                // 处理每个 FileDownloadDTO
                processFileDownloadDTO(fileDownloadDTO, nameMap, newTempPath, fileCompressor);
            }
            // 导出文件
            if (zipDownloadDTO.getExportFileDownloadDTO() != null) {
                try {
                    //将下载文件拷贝到临时目录
                    log.info(COPY_FILE_INFO_FORMAT, "",
                        zipDownloadDTO.getExportFileDownloadDTO().getPath());
                    File srcFile = uploadFileService.downloadFile(zipDownloadDTO.getExportFileDownloadDTO().getPath());
                    if (null != srcFile && srcFile.exists()) {
                        String destDir =
                            newTempPath + SEPARATOR + zipDownloadDTO.getExportFileDownloadDTO().getName();
                        FileUtils.copyFile(srcFile, new File(destDir));
                        fileCompressor.add(destDir);
                    }
                } catch (IOException e) {
                    log.error(COMPRESSOR_FILE_LIST_EXCEPTION, e);
                }
            }

            // 导出失败填充状态并更新
            fillFailStatusAndUpdateIt(exportRecord, fileCompressor);
            // 打包zip,并上传zip文件
            fillSuccessStatusAndUpdateIt(exportRecord, fileCompressor, newTempPath, relativePath,
                zipDownloadDTO.getZipName(), bizId);
        }, commonTaskThreadPool);


    }

    /**
     * 处理文件下载dto
     *
     * @param fileDownloadDTO 文件下载dto
     * @param nameMap         名称映射
     * @param newTempPath     新临时路径
     * @param fileCompressor  文件压缩器
     */
    private void processFileDownloadDTO(FileDownloadDTO fileDownloadDTO, Map<String, Integer> nameMap,
        String newTempPath, List<String> fileCompressor) {
        LambdaQueryWrapper<Files> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Files::getCategoryId, fileDownloadDTO.getCategoryId());
        queryWrapper.eq(Files::getCategoryType, fileDownloadDTO.getCategoryType());
        log.info(THE_FILE_CATEGORY_INFO_FORMAT, fileDownloadDTO.getCategoryId());
        List<Files> filesList = filesService.list(queryWrapper);
        if (CollectionUtils.isEmpty(filesList)) {
            return;
        }
        for (Files file : filesList) {
            if (Objects.isNull(file)) {
                continue;
            }
            try {
                // 处理单个文件的逻辑
                processFile(file, fileDownloadDTO, nameMap, newTempPath, fileCompressor);
            } catch (IOException e) {
                log.error(COMPRESSOR_FILE_LIST_EXCEPTION, e);
            }
        }
    }

    /**
     * 处理文件
     *
     * @param one             文件
     * @param fileDownloadDTO 文件下载dto
     * @param nameMap         名称映射
     * @param newTempPath     新临时路径
     * @param fileCompressor  文件压缩器
     * @throws IOException IOException
     */
    private void processFile(Files one, FileDownloadDTO fileDownloadDTO, Map<String, Integer> nameMap,
        String newTempPath, List<String> fileCompressor) throws IOException {
        //将下载文件拷贝到临时目录
        log.info(COPY_FILE_INFO_FORMAT, one.getFileName(), one.getCurrentPath());
        File srcFile = uploadFileService.downloadFile(one.getCurrentPath());
        if (null != srcFile && srcFile.exists()) {
            String imageName = fileDownloadDTO.getLoginName() + UNDERLINE
                + fileDownloadDTO.getUserName() + UNDERLINE + one.getFileName();
            Integer n = nameMap.get(imageName);
            if (n == null) {
                nameMap.put(imageName, 1);
            } else {
                // 同名文件添加（_n）
                nameMap.put(imageName, n + 1);
                imageName = fileDownloadDTO.getLoginName() + UNDERLINE
                    + fileDownloadDTO.getUserName() + UNDERLINE + (n + 1) + UNDERLINE + one
                    .getFileName();
            }
            String destDir = newTempPath + SEPARATOR + imageName;
            FileUtils.copyFile(srcFile, new File(destDir));
            fileCompressor.add(destDir);
        }
    }

    @NotNull
    private ExportRecord createExportRecord(String bizId, String bizType, String zipName) {
        Date exportTime = new Date();
        ExportRecord exportRecord = new ExportRecord();
        exportRecord.setBizId(bizId);
        exportRecord.setBizType(bizType);
        exportRecord.setCurrentPath("");
        exportRecord.setFileName(zipName);
        exportRecord.setCreateTime(exportTime);
        exportRecord.setStatus(2);
        exportRecord.setCreateBy(UserThreadContext.getUserId());
        exportRecordService.save(exportRecord);
        return exportRecord;
    }


    /**
     * 填充状态并更新
     *
     * @param exportRecord   导出记录
     * @param fileCompressor 文件压缩器
     * @param newTempPath    新临时路径
     * @param relativePath   相对路径
     * @param zipName        zip名称
     */
    private void fillSuccessStatusAndUpdateIt(ExportRecord exportRecord, List<String> fileCompressor,
        String newTempPath, String relativePath, String zipName, String bizId) {
        // 打包zip
        String zipPath = newTempPath + SEPARATOR + zipName + ".zip";
        // 上传后的目的地 - 相对路径
        relativePath = relativePath + SEPARATOR + zipName + ".zip";

        ZipCompressorUtil zc = new ZipCompressorUtil(zipPath);
        zc.compress(fileCompressor);

        //上传zip文件
        UploadFileRequest uploadFileRequest = new UploadFileRequest();
        uploadFileRequest.setFileName(relativePath);
        uploadFileRequest.setAsyncUpload(false);
        uploadFileRequest.setTempFilePath(zipPath);
        uploadFileRequest.setCopyFolderFlag(false);
        uploadFileRequest.setBizId(bizId);
        uploadFileService.uploadFile(uploadFileRequest);

        exportRecord.setStatus(1);
        exportRecord.setUpdateTime(new Date());
        exportRecord.setCurrentPath(relativePath);
        exportRecordService.updateById(exportRecord);
    }

    /**
     * 填充状态并更新
     *
     * @param exportRecord   导出记录
     * @param fileCompressor 文件压缩器
     */
    private void fillFailStatusAndUpdateIt(ExportRecord exportRecord, List<String> fileCompressor) {
        if (CollectionUtils.isEmpty(fileCompressor)) {
            exportRecord.setStatus(3);
            exportRecord.setUpdateTime(new Date());
            exportRecordService.updateById(exportRecord);
            throw new BusinessException(FileErrorNoEnum.NO_ZIP_FILE);
        }
    }

    @Override
    public MimeInfoDTO getMineInfo(String path) {
        return filesService.getMineInfo(path);
    }

    @Override
    public void saveImagesToDb(String sourceBizId, String bizId, String bizType) {
        List<Images> fileListByBizId = imagesService.getFileListByBizId(sourceBizId, bizType);
        if (CollectionUtils.isEmpty(fileListByBizId)) {
            return;
        }
        List<Images> images = BeanListUtils.copyListProperties(fileListByBizId, Images::new);
        for (Images image : images) {
            image.setId(newId());
            image.setCategoryId(bizId);
        }
        imagesService.saveBatch(images);
    }

    @Override
    public void copyImagesToDb(Collection<CopyFileDTO> copyFileDTO) {
        if (CollectionUtils.isEmpty(copyFileDTO)) {
            return;
        }
        HashMap<String, String> sourceBizIdMap = new HashMap<>();
        for (CopyFileDTO fileDTO : copyFileDTO) {
            sourceBizIdMap.put(fileDTO.getSourceBizId(), fileDTO.getTargetBizId());
        }
        String bizType = copyFileDTO.stream().findFirst().map(CopyFileDTO::getBizType).orElseThrow();
        List<Images> fileListByBizId = imagesService.getFileListByBizIds(sourceBizIdMap.keySet(), bizType);
        if (CollectionUtils.isEmpty(fileListByBizId)) {
            return;
        }
        List<Images> images = BeanListUtils.copyListProperties(fileListByBizId, Images::new);
        for (Images image : images) {
            image.setId(newId());
            image.setCategoryId(sourceBizIdMap.get(image.getCategoryId()));
        }
        imagesService.saveBatch(images);
    }

    @Override
    public void copyFilesToDb(Collection<CopyFileDTO> copyFileDTO) {
        if (CollectionUtils.isEmpty(copyFileDTO)) {
            return;
        }
        HashMap<String, String> sourceBizIdMap = new HashMap<>();
        for (CopyFileDTO fileDTO : copyFileDTO) {
            sourceBizIdMap.put(fileDTO.getSourceBizId(), fileDTO.getTargetBizId());
        }
        String bizType = copyFileDTO.stream().findFirst().map(CopyFileDTO::getBizType).orElseThrow();
        List<Files> fileListByBizId = filesService.getFileByBizIds(sourceBizIdMap.keySet(), bizType, null);
        if (CollectionUtils.isEmpty(fileListByBizId)) {
            return;
        }
        List<Files> filesList = BeanListUtils.copyListProperties(fileListByBizId, Files::new);
        for (Files file : filesList) {
            file.setId(newId());
            file.setCategoryId(sourceBizIdMap.get(file.getCategoryId()));
        }
        filesService.saveBatch(filesList);
    }

    @Override
    public void saveFilesToDb(String sourceBizId, String bizId, String bizType) {
        List<Files> fileListByBizId = filesService.getFileListByBizId(sourceBizId, bizType);
        if (CollectionUtils.isEmpty(fileListByBizId)) {
            return;
        }
        List<Files> filesList = BeanListUtils.copyListProperties(fileListByBizId, Files::new);
        for (Files file : filesList) {
            file.setId(newId());
            file.setCategoryId(bizId);
        }
        filesService.saveBatch(filesList);
    }

    @Override
    public boolean fileExists(String currentPath) {
        try {
            File srcFile = uploadFileService.downloadFile(currentPath);
            return srcFile.exists();
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public File downLoadFile(String url) {
        return uploadFileService.downloadFile(url);
    }

    @Override
    public String getFileText(String url) throws FileNotFoundException {
        File file = uploadFileService.downloadFile(url);
        StringBuilder stringBuffer = new StringBuilder();
        try (BufferedReader bufferedReader = new BufferedReader(new FileReader(file, StandardCharsets.UTF_8))) {
            String line;
            while ((line = bufferedReader.readLine()) != null) {
                stringBuffer.append(line);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return stringBuffer.toString();
    }

    @Override
    public String compressorFiles(Collection<String> filePaths, String zipFileName, boolean isImage) {
        return uploadFileService.compressorFiles(filePaths, zipFileName, isImage);
    }

    @Override
    public void compressorFilesMap(Map<String, String> filePathMap, String zipFileName, String bizId) {
        storageStrategyService.compressorFiles(filePathMap, zipFileName, bizId);
    }

    @Override
    public String uploadImg(MultipartFile multipartFile, String name) {
        return filesService.saveUploadFile(multipartFile, name).getPath();
    }

    @Override
    public void createCertRelateWaterMarkImg(CertRelateWaterMarkImgInfoDTO certRelateWaterMarkImgInfoDTO)
        throws IOException {
        RelateWaterMarkImgDTO dto = new RelateWaterMarkImgDTO();
        dto.setUserName(certRelateWaterMarkImgInfoDTO.getUserName());
        dto.setCertNo(certRelateWaterMarkImgInfoDTO.getCertNo());
        dto.setUserOrgan(certRelateWaterMarkImgInfoDTO.getUserOrgan());
        dto.setCerName(certRelateWaterMarkImgInfoDTO.getCerName());
        dto.setCreateTime(certRelateWaterMarkImgInfoDTO.getCreateTime());
        dto.setCertificationId(certRelateWaterMarkImgInfoDTO.getCertificationId());
        dto.setValidPeriod(certRelateWaterMarkImgInfoDTO.getValidPeriod());
        dto.setRelateId(certRelateWaterMarkImgInfoDTO.getRelateId());
        dto.setEmpNo(certRelateWaterMarkImgInfoDTO.getEmpNo());
        dto.setStartTime(certRelateWaterMarkImgInfoDTO.getStartTime());
        dto.setContentName(certRelateWaterMarkImgInfoDTO.getActivity());
        dto.setSponsor(certRelateWaterMarkImgInfoDTO.getSponsor());

        filesService.createCertRelateWaterMarkImg(dto);

    }

    @Override
    public void copyCoursewareFileRecord(String coursewareId, String copyCoursewareId) {
        List<Files> filesList = filesService.lambdaQuery().eq(Files::getCategoryId, coursewareId).list();
        if (!CollectionUtils.isEmpty(filesList)) {
            filesList.forEach(files -> {
                files.setId(newId());
                files.setCategoryId(copyCoursewareId);
            });
            filesService.saveBatch(filesList);
        }
        // 还要复制清晰度sys_video_clarity
        LambdaQueryWrapper<VideoClarity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VideoClarity::getCategoryId, coursewareId);
        List<VideoClarity> videoClaritieList = videoClarityMapper.selectList(queryWrapper);
        if (!CollectionUtils.isEmpty(videoClaritieList)) {
            videoClaritieList.forEach(videoClarity -> {
                VideoClarity copyVideoClarity = new VideoClarity();
                BeanUtils.copyProperties(videoClarity, copyVideoClarity);
                copyVideoClarity.setId(null);
                copyVideoClarity.setCategoryId(copyCoursewareId);
                videoClarityMapper.insert(copyVideoClarity);
            });
        }
    }

    @Override
    public NamePath getFileNamePathInfo(String bizId, String bizType, Boolean isSource) {
        Files files = filesService.getFileByBizId(bizId, bizType, isSource, Files::getCreateTime, false);
        NamePath namePath = new NamePath();
        if (Objects.isNull(files)) {
            return namePath;
        }
        namePath.setCategoryId(files.getCategoryId());
        namePath.setName(files.getFileName());
        namePath.setPath(files.getCurrentPath());
        namePath.setUrl(urlHelper.getStaticFullUrl(files.getCurrentPath()));
        return namePath;
    }

    @Override
    public Map<String, String> getImageUrlByIds(Collection<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new HashMap<>(0);
        }
        LambdaQueryWrapper<Images> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Images::getCategoryId, ids);
        List<Images> imagesList = imagesService.list(queryWrapper);
        return urlHelper.getStaticFullUrl(imagesList);
    }

    @Override
    public Map<String, String> getMainFileUrlByIds(Set<String> ids, String bizType) {
        if (CollectionUtils.isEmpty(ids)) {
            return new HashMap<>(0);
        }
        List<Files> filesList = filesService.getMainFileMapByCategoryIds(ids);
        filesList = filesList.stream().filter(f -> StringUtils.isNotBlank(f.getCurrentPath()))
            .collect(Collectors.toList());
        return filesList.stream().collect(
            Collectors.toMap(Files::getCategoryId, f -> urlHelper.getStaticFullUrl(f.getCurrentPath()),
                (key1, key2) -> key1));
    }

    /**
     * 重置转码课件，将转码课件的文件地址修改为源文件地址
     */
    @Override
    public void resetTransFile(List<String> ids, String bizType) {
        List<Files> updateFiles = new ArrayList<>(ids.size());
        for (String fileId : ids) {
            Files transFile = filesService.getFileByBizId(fileId, bizType, false);
            Files sourceFile = filesService.getFileByBizId(fileId, bizType, true);
            if (null == sourceFile || null == transFile) {
                continue;
            }
            transFile.setCurrentPath(sourceFile.getCurrentPath());
            updateFiles.add(transFile);
        }
        if (!updateFiles.isEmpty()) {
            filesService.updateBatchById(updateFiles);
        }
    }

    @Override
    public boolean isExistsAttach(Collection<String> categoryIds, String bizType) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return false;
        }
        LambdaQueryWrapper<Files> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Files::getCategoryId, categoryIds);
        queryWrapper.eq(Files::getCategoryType, bizType);
        queryWrapper.eq(Files::getIsSource, GeneralJudgeEnum.CONFIRM.getValue());
        return !CollectionUtils.isEmpty(filesService.list(queryWrapper));
    }

    @Override
    public boolean isExistsImages(Collection<String> categoryIds, String bizType) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return false;
        }
        LambdaQueryWrapper<Images> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Images::getCategoryId, categoryIds);
        queryWrapper.eq(Images::getCategoryType, bizType);
        return !CollectionUtils.isEmpty(imagesService.list(queryWrapper));
    }

    @Override
    public boolean isExistsFile(Collection<String> categoryIds, String bizType) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return false;
        }
        LambdaQueryWrapper<Files> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Files::getId);
        queryWrapper.in(Files::getCategoryId, categoryIds);
        queryWrapper.eq(Files::getCategoryType, bizType);
        return !CollectionUtils.isEmpty(filesService.list(queryWrapper));
    }

    @Override
    public String getReferenceViewUrl(String filePath) {
        return urlHelper.getStaticFullUrl(SEPARATOR + sysConfig.getRoot() + filePath);
    }

    @Override
    public void copySameBizImageNotType(Map<String, String> bizIdMap) {
        List<Images> sourceImageList = imagesService.getFileListByBizIdsNotType(bizIdMap.keySet());
        if (CollectionUtils.isEmpty(sourceImageList)) {
            return;
        }
        List<Images> images = new ArrayList<>();
        for (Images sourceImage : sourceImageList) {
            String newImageId = newId();
            sourceImage.setId(newImageId);
            // 替换 老bizId -> 新的bizId
            sourceImage.setCategoryId(bizIdMap.get(sourceImage.getCategoryId()));
            // 拷贝到原来的文件夹，只需要替换文件名
            images.add(sourceImage);
        }
        imagesService.saveBatch(images);
    }

    //@Override
    public void copySameBizFileNotType(Map<String, String> newPageIdMap) {
        if (CollectionUtils.isEmpty(newPageIdMap)) {
            return;
        }
        List<Files> sourceFileList = filesService.lambdaQuery().in(Files::getCategoryId, newPageIdMap.keySet()).list();
        if (CollectionUtils.isEmpty(sourceFileList)) {
            return;
        }
        List<Files> filesList = new ArrayList<>();
        for (Files sourceFile : sourceFileList) {
            String newImageId = newId();
            sourceFile.setId(newImageId);
            // 替换 老bizId -> 新的bizId
            sourceFile.setCategoryId(newPageIdMap.get(sourceFile.getCategoryId()));
            // 拷贝到原来的文件夹，只需要替换文件名
            filesList.add(sourceFile);
        }
        filesService.saveBatch(filesList);
    }

    @Override
    public List<NamePath> getFilesById(Collection<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        List<Files> filesList = filesService.list(new LambdaQueryWrapper<Files>().in(Files::getId, ids));
        return filesList.stream().map(files -> {
            NamePath namePath = new NamePath();
            namePath.setId(files.getId());
            namePath.setName(files.getFileName());
            namePath.setCategoryId(files.getCategoryId());
            namePath.setPath(files.getCurrentPath());
            namePath.setUrl(urlHelper.getStaticFullUrl(files.getCurrentPath()));
            return namePath;
        }).collect(Collectors.toList());
    }

    @Override
    public void updateIsSourceById(Collection<String> ids, Integer isSource) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        filesService.update(new LambdaUpdateWrapper<Files>().in(Files::getId, ids).set(Files::getIsSource, isSource));
    }

    @Override
    public void saveOrUpdatePicList(SaveOrUpdatePicListDTO saveOrUpdatePicListDTO) {
        Boolean isInsert = saveOrUpdatePicListDTO.getIsInsert();
        List<NamePath> tempFilePath = saveOrUpdatePicListDTO.getTempFilePath();
        String bizId = saveOrUpdatePicListDTO.getBizId();
        String bizType = saveOrUpdatePicListDTO.getBizType();

        if (!CollectionUtils.isEmpty(tempFilePath) && Boolean.TRUE.equals(isInsert)) {
            saveImages(bizId, bizType, tempFilePath);
            return;
        }
        Set<String> tempFilePathSet = tempFilePath.stream().map(NamePath::getPath).collect(Collectors.toSet());

        // 数据库存的
        List<Images> dbImageList = imagesService.getFileListByBizId(bizId, bizType);
        Map<String, Images> pathMap = dbImageList.stream()
            .collect(Collectors.toMap(Images::getCurrentPath, Function.identity()));
        List<Images> saveImagesList = new ArrayList<>();
        // 检查哪些被替换，哪些没有被替换
        for (int i = 0; i < tempFilePath.size(); i++) {
            NamePath namePath = tempFilePath.get(i);
            // 不在数据库内的 path 则是新增的
            if (!pathMap.containsKey(namePath.getPath())) {
                UploadFileResponse uploadFileResponse = saveFile(FileTypeEnum.IMAGE, namePath.getPath(), bizType,
                    bizId);
                Images image = buildImages(namePath.getName(), bizId, bizType, uploadFileResponse.getFileUrl(),
                    uploadFileResponse.getFileSize(), namePath.getWidth(), namePath.getHeight());
                // 这里的图片顺序都是固定的指定替换那个顺序的图就好
                image.setSortNo(i);
                saveImagesList.add(image);
            }
        }

        // 过滤已经需要被删除的
        List<String> deleteImageIdList = dbImageList.stream()
            .filter(item -> !tempFilePathSet.contains(item.getCurrentPath())).map(Images::getId)
            .collect(Collectors.toList());

        imagesService.removeBatchByIds(deleteImageIdList);
        imagesService.saveOrUpdateBatch(saveImagesList);
    }

    @Override
    public List<VideoClarityDTO> getVideoClarity(String categoryId) {
        LambdaQueryWrapper<VideoClarity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VideoClarity::getCategoryId, categoryId);
        List<VideoClarity> videoClaritieList = videoClarityMapper.selectList(queryWrapper);
        return videoClaritieList.stream().map(videoClarity -> {
            VideoClarityDTO dto = new VideoClarityDTO();
            dto.setUrl(urlHelper.getStaticFullUrl(videoClarity.getCurrentPath()));
            dto.setName(videoClarity.getClarityName());
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public void compressorZipList(String bizId, String bizType, String fileName,
        List<ZipDownloadDTO> zipDownloadList) {

        CompletableFuture.runAsync(() -> {
            // 创建导出记录
            ExportRecord exportRecord = createExportRecord(bizId, bizType, fileName);

            // 分批处理文件
            List<String> zipFilePathList = new ArrayList<>();
            for (ZipDownloadDTO zipDownloadDTO : zipDownloadList) {
                String relativePath = sysConfig.queryTempPath() + SEPARATOR + zipDownloadDTO.getTempPath();
                //临时目录物理地址
                String newTempPath = sysConfig.getPhysicalPath(relativePath);
                FileUtil.mkdir(newTempPath);
                List<String> fileCompressor = getCompressorZipFileList(zipDownloadDTO, newTempPath);
                // 填充导出失败状态并更新
                fillFailStatusAndUpdateIt(exportRecord, fileCompressor);
                // 打包zip
                String zipPath = newTempPath + SEPARATOR + zipDownloadDTO.getZipName() + ".zip";
                // 上传后的目的地 - 相对路径
                relativePath = relativePath + SEPARATOR + zipDownloadDTO.getZipName() + ".zip";

                ZipCompressorUtil zc = new ZipCompressorUtil(zipPath);
                zc.compress(fileCompressor);

                //上传zip文件
                UploadFileRequest uploadFileRequest = new UploadFileRequest();
                uploadFileRequest.setFileName(relativePath);
                uploadFileRequest.setAsyncUpload(false);
                uploadFileRequest.setTempFilePath(zipPath);
                uploadFileRequest.setCopyFolderFlag(false);
                uploadFileService.uploadFile(uploadFileRequest);
                zipFilePathList.add(zipPath);
            }

            String relativePath = sysConfig.queryTempPath() + SEPARATOR + bizId;
            //临时目录物理地址
            String newTempPath = sysConfig.getPhysicalPath(relativePath);
            FileUtil.mkdir(newTempPath);
            // 打包zip,并上传zip文件
            fillSuccessStatusAndUpdateIt(exportRecord, zipFilePathList, newTempPath, relativePath, fileName, bizId);
        }, commonTaskThreadPool);


    }

    private List<String> getCompressorZipFileList(ZipDownloadDTO zipDownloadDTO, String newTempPath) {
        List<String> fileCompressor = new ArrayList<>();
        for (int i = 0; i < zipDownloadDTO.getFileDownloadDTOList().size(); i++) {
            FileDownloadDTO fileDownloadDTO = zipDownloadDTO.getFileDownloadDTOList().get(i);
            LambdaQueryWrapper<Files> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Files::getCategoryId, fileDownloadDTO.getCategoryId());
            queryWrapper.eq(Files::getCategoryType, fileDownloadDTO.getCategoryType());
            queryWrapper.eq(Files::getIsSource, GeneralJudgeEnum.CONFIRM.getValue());
            log.info(THE_FILE_CATEGORY_INFO_FORMAT, fileDownloadDTO.getCategoryId());
            Files one = filesService.getOne(queryWrapper);
            if (Objects.isNull(one)) {
                continue;
            }
            try {
                //将下载文件拷贝到临时目录
                log.info(COPY_FILE_INFO_FORMAT, one.getFileName(), one.getCurrentPath());
                File srcFile = uploadFileService.downloadFile(one.getCurrentPath());
                if (null != srcFile && srcFile.exists()) {
                    String destDir = newTempPath + SEPARATOR + fileDownloadDTO.getLoginName() + UNDERLINE
                        + fileDownloadDTO.getUserName() + UNDERLINE + one.getFileName();
                    FileUtils.copyFile(srcFile, new File(destDir));
                    fileCompressor.add(destDir);
                }
            } catch (IOException e) {
                log.error(COMPRESSOR_FILE_LIST_EXCEPTION, e);
            }
        }
        return fileCompressor;
    }

    @Override
    public List<SaveFileDTO> saveOrUpdateFileList(String bizId, String bizType, List<NamePath> list) {
        Set<String> fileIdSet = list.stream().map(NamePath::getId).filter(StringUtils::isNotBlank)
            .collect(Collectors.toSet());
        Map<String, Files> fileMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(fileIdSet)) {
            fileMap.putAll(filesService.listByIds(fileIdSet).stream()
                .collect(Collectors.toMap(Files::getId, Function.identity(), (key1, key2) -> key1)));
        }
        List<SaveFileDTO> resultList = new ArrayList<>();
        List<Files> filesList = new ArrayList<>();
        int beginIndex = 0;
        Set<String> idSet = new HashSet<>();
        for (NamePath namePath : list) {
            if (StringUtils.isEmpty(namePath.getId())) {
                // 新增的文件
                UploadFileResponse uploadFileResponse = saveFile(FileTypeEnum.IMAGE, namePath.getPath(), bizType,
                    bizId);
                Files files = buildFiles(namePath.getName(), bizId, bizType, uploadFileResponse.getFileUrl(),
                    uploadFileResponse.getFileSize());
                files.setSortNo(beginIndex++);
                filesList.add(files);
                resultList.add(
                    buildSaveFileDTO(uploadFileResponse.getFileUrl(), namePath.getPath(), files.getId(), bizId, bizType,
                        namePath.getFileSize()));
            } else {
                // 保留的文件
                idSet.add(namePath.getId());
                Files files = Optional.ofNullable(fileMap.get(namePath.getId())).orElse(new Files().setId(""));
                // 修改排序
                files.setSortNo(beginIndex++);
                filesList.add(files);
                resultList.add(
                    buildSaveFileDTO(namePath.getPath(), namePath.getPath(), namePath.getId(), bizId, bizType,
                        namePath.getFileSize()));
            }
        }

        // 删除不存在的文件
        filesService.remove(
            Wrappers.<Files>lambdaQuery().eq(Files::getCategoryId, bizId).eq(Files::getCategoryType, bizType)
                .notIn(!CollectionUtils.isEmpty(idSet), Files::getId, idSet));

        filesService.saveOrUpdateBatch(filesList);
        return resultList;
    }

    @Override
    public List<NamePath> getVoteImageFileNamePathsByBizIds(Collection<String> bizId, String bizType) {

        List<NamePath> namePaths = new ArrayList<>();
        if (CollectionUtils.isEmpty(bizId)) {
            return namePaths;
        }
        for (String bizId1 : bizId) {
            Images images = imagesService.getFileByBizId(bizId1, bizType);
            if (Objects.nonNull(images)) {
                NamePath namePath = new NamePath();
                namePath.setName(images.getImageName());
                namePath.setPath(images.getCurrentPath());
                namePath.setId(images.getId());
                namePath.setCategoryId(images.getCategoryId());
                namePath.setUrl(urlHelper.getStaticFullUrl((images.getCurrentPath())));
                namePath.setFileSize(images.getImageSize());
                namePath.setWidth(images.getWidth());
                namePath.setHeight(images.getHeight());
                namePaths.add(namePath);
            }
        }
        return namePaths;
    }

    @Override
    public void insertPkMusicImages(String categoryId, String categoryType, String type) {
        if ("play".equals(type)) {
            Images imagesPlay = new Images();
            imagesPlay.setId(newId());
            imagesPlay.setCategoryId(categoryId);
            imagesPlay.setCategoryType(categoryType);
            imagesPlay.setImageName("pkMusicPlay.png");
            imagesPlay.setImageSize(0L);
            imagesPlay.setCurrentPath(SEPARATOR + UserThreadContext.getTenantId()
                + "/file/referenceTopicImage/pkPage/pkMusicPlay.png");
            imagesPlay.setOldPath("");
            imagesPlay.setDpiCode("");
            imagesPlay.setSortNo(1);
            imagesPlay.setWidth(0);
            imagesPlay.setHeight(0);
            imagesPlay.setIsDel(0);
            imagesPlay.setCreateBy(ADMIN);
            imagesPlay.setCreateTime(new Date());
            imagesPlay.setUpdateBy(ADMIN);
            imagesPlay.setUpdateTime(new Date());
            imagesService.save(imagesPlay);
        } else {
            Images imagesStop = new Images();
            imagesStop.setId(newId());
            imagesStop.setCategoryId(categoryId);
            imagesStop.setCategoryType(categoryType);
            imagesStop.setImageName("pkMusicStop.png");
            imagesStop.setImageSize(0L);
            imagesStop.setCurrentPath(SEPARATOR + UserThreadContext.getTenantId()
                + "/file/referenceTopicImage/pkPage/pkMusicStop.png");
            imagesStop.setOldPath("");
            imagesStop.setDpiCode("");
            imagesStop.setSortNo(1);
            imagesStop.setWidth(0);
            imagesStop.setHeight(0);
            imagesStop.setIsDel(0);
            imagesStop.setCreateBy(ADMIN);
            imagesStop.setCreateTime(new Date());
            imagesStop.setUpdateBy(ADMIN);
            imagesStop.setUpdateTime(new Date());
            imagesService.save(imagesStop);
        }
    }

    @Override
    public void deleteOnePic(String idName) {
        LambdaQueryWrapper<Images> imagesLambdaQueryWrapper = new LambdaQueryWrapper<>();
        imagesLambdaQueryWrapper.eq(Images::getCategoryId, idName);
        List<Images> list = imagesService.list(imagesLambdaQueryWrapper);
        list.forEach(images -> imagesService.removeById(images.getId()));
    }

    @Override
    public void onlySaveBizData(String bizId, String bizType, String name, String url) {
        Images images = buildImages(name, bizId, bizType, url, 0L, 0, 0);
        imagesService.save(images);
    }

    @Override
    public SaveFileDTO uploadFile(String tempFilePath) {

        String lastPath = FilenameUtils.getName(tempFilePath);
        String fileType;
        int lastIndexOfDot = lastPath.lastIndexOf(".");
        if (lastIndexOfDot != -1) {
            fileType = lastPath.substring(lastIndexOfDot);
        } else {
            fileType = "错误类型";
        }

        String strBuilder =
            SEPARATOR + sysConfig.getRoot() + SEPARATOR + "file" + SEPARATOR + DateUtil.getYmStr() + SEPARATOR
                + newId() + fileType;
        UploadFileRequest uploadFileRequest = new UploadFileRequest();
        uploadFileRequest.setFileName(strBuilder);
        uploadFileRequest.setAsyncUpload(false);

        uploadFileRequest.setCopyFolderFlag(false);
        uploadFileRequest.setTempFilePath(tempFilePath);
        uploadFileService.uploadFile(uploadFileRequest);

        return new SaveFileDTO().setPath(strBuilder);
    }

    @Override
    public boolean checkFileExists(String currentPath) {
        try {
            File srcFile = uploadFileService.downloadFile(currentPath);
            return srcFile.exists() && srcFile.length() > 0;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public Map<String, String> uploadCwToDify(List<String> cwIdList) {
        Map<String, String> resultMap = new HashMap<>();
        if (CollectionUtils.isEmpty(cwIdList)) {
            return resultMap;
        }

        List<Files> cwFileList = filesService.getSourceFileListByBizIds(cwIdList, FileBizType.CourseWareFile.name());
        if (CollectionUtils.isEmpty(cwFileList)) {
            return resultMap;
        }

        for (Files fileInfo : cwFileList) {
            try {
                processAndUploadFile(fileInfo, resultMap);
            } catch (Exception e) {
                log.error("处理课件文件时发生错误，cwId: {}", fileInfo.getCategoryId(), e);
            }
        }
        return resultMap;
    }

    /**
     * 处理并上传单个文件
     */
    private void processAndUploadFile(Files fileInfo, Map<String, String> resultMap) throws IOException {
        String cwId = fileInfo.getCategoryId();
        String filePath = fileInfo.getCurrentPath();
        String fileName = fileInfo.getFileName();
        String fileExtension = getFileExtension(fileName);

        // 处理文件路径和扩展名
        ProcessedFileInfo processedInfo = processFilePathAndExtension(filePath, fileExtension);
        if (processedInfo == null) {
            return;
        }

        // 上传处理后的文件
        uploadProcessedFile(cwId, processedInfo, resultMap);
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        int lastIndexOfDot = fileName.lastIndexOf(".");
        return lastIndexOfDot != -1 ? fileName.substring(lastIndexOfDot + 1).toLowerCase() : "";
    }

    /**
     * 处理文件路径和扩展名
     */
    private ProcessedFileInfo processFilePathAndExtension(String filePath, String fileExtension) {
        String processedFilePath = filePath;
        String processedExtension = fileExtension;

        // 处理音视频文件
        if (isMediaFile(fileExtension)) {
            processedFilePath = getTranscodedTxtPath(filePath);
            processedExtension = "txt";
        }

        // 下载文件
        File file = downLoadFile(processedFilePath);
        if (file == null) {
            log.error("文件下载失败，filePath: {}", processedFilePath);
            return null;
        }

        // 处理doc文件转换
        File processedFile = processDocFile(file, fileExtension);
        if (processedFile == null) {
            return null;
        }

        return new ProcessedFileInfo(processedFile, processedExtension);
    }

    /**
     * 处理doc文件转换
     */
    private File processDocFile(File file, String originalExtension) {
        if (!"doc".equals(originalExtension)) {
            return file;
        }

        try {
            File docxFile = convertDocToDocx(file);
            if (docxFile != null) {
                java.nio.file.Files.delete(file.toPath());
                return docxFile;
            }
        } catch (Exception e) {
            log.error("doc文件转换失败1", e);
        }
        return null;
    }

    /**
     * 上传处理后的文件
     */
    private void uploadProcessedFile(String cwId, ProcessedFileInfo processedInfo, Map<String, String> resultMap)
        throws IOException {
        File fileToProcess = processedInfo.getFile();
        String fileExtension = processedInfo.getExtension();

        // 重命名文件
        File renamedFile = renameFile(fileToProcess, fileExtension);
        if (renamedFile == null) {
            return;
        }

        try {
            // 上传到Dify
            String fileId = difyApiClient.uploadFile(renamedFile);
            if (fileId != null) {
                resultMap.put(cwId, fileId);
                log.info("课件上传到Dify成功，cwId: {}, fileId: {}", cwId, fileId);
            } else {
                log.error("课件上传到Dify失败，cwId: {}", cwId);
            }
        } finally {
            // 清理临时文件
            if (renamedFile.exists()) {
                java.nio.file.Files.delete(renamedFile.toPath());
            }
        }
    }

    /**
     * 重命名文件
     */
    private File renameFile(File file, String newExtension) {
        String newFileName = file.getName().substring(0, file.getName().lastIndexOf(".") + 1) + newExtension;
        File renamedFile = new File(file.getParent(), newFileName);
        if (!file.renameTo(renamedFile)) {
            log.error("文件重命名失败，filePath: {}", file.getAbsolutePath());
            return null;
        }
        return renamedFile;
    }

    /**
     * 处理后的文件信息类
     */
    private static class ProcessedFileInfo {

        private final File file;
        private final String extension;

        public ProcessedFileInfo(File file, String extension) {
            this.file = file;
            this.extension = extension;
        }

        public File getFile() {
            return file;
        }

        public String getExtension() {
            return extension;
        }
    }

    /**
     * 判断是否为音视频文件
     */
    private boolean isMediaFile(String extension) {
        if (StringUtils.isBlank(extension)) {
            return false;
        }
        String typeLower = extension.toLowerCase();
        return FileType.MP3.getType().equals(typeLower) ||
            FileType.MA.getType().equals(typeLower) ||
            FileType.MV.getType().equals(typeLower) ||
            FileType.MP4.getType().equals(typeLower) ||
            FileType.MPEG.getType().equals(typeLower) ||
            FileType.MPG.getType().equals(typeLower) ||
            FileType.AVI.getType().equals(typeLower) ||
            FileType.NAVI.getType().equals(typeLower) ||
            FileType.ASF.getType().equals(typeLower) ||
            FileType.MOV.getType().equals(typeLower) ||
            FileType.GP.getType().equals(typeLower) ||
            FileType.WMV.getType().equals(typeLower) ||
            FileType.DIVX.getType().equals(typeLower) ||
            FileType.XVID.getType().equals(typeLower) ||
            FileType.RM.getType().equals(typeLower) ||
            FileType.RMVB.getType().equals(typeLower) ||
            FileType.FLV.getType().equals(typeLower) ||
            FileType.F4V.getType().equals(typeLower);
    }

    /**
     * 获取音视频文件转码后的txt文件路径
     */
    private String getTranscodedTxtPath(String originalPath) {
        // 移除文件扩展名
        String basePath = originalPath.substring(0, originalPath.lastIndexOf("."));
        // 添加.txt扩展名
        return basePath + ".txt";
    }

    @Override
    public Map<String, String> getFileUrlByIdsAndType(Set<String> ids, String bizType) {
        if (CollectionUtils.isEmpty(ids)) {
            return new HashMap<>(0);
        }
        List<Files> filesList = filesService.getFileUrlByIdsAndType(ids, bizType);
        return filesList.stream().collect(
            Collectors.toMap(Files::getCategoryId, f -> urlHelper.getStaticFullUrl(f.getCurrentPath()),
                (key1, key2) -> key1));
    }

    /**
     * 将doc文件转换为docx文件
     */
    @SuppressWarnings("java:S5443")
    private File convertDocToDocx(File docFile) throws IOException {
        // 创建临时文件用于存储转换后的docx
        File docxFile = File.createTempFile("converted_", ".docx");

        try (FileInputStream fis = new FileInputStream(docFile);
            FileOutputStream fos = new FileOutputStream(docxFile)) {

            // 使用HWPF读取doc文件
            HWPFDocument doc = new HWPFDocument(fis);

            // 创建XWPF文档
            XWPFDocument docx = new XWPFDocument();

            // 获取doc文档的文本
            WordExtractor extractor = new WordExtractor(doc);
            String[] paragraphs = extractor.getParagraphText();

            // 将文本写入docx文档
            for (String paragraph : paragraphs) {
                if (StringUtils.isNotBlank(paragraph)) {
                    XWPFParagraph xwpfParagraph = docx.createParagraph();
                    XWPFRun run = xwpfParagraph.createRun();
                    run.setText(paragraph.trim());
                }
            }

            // 保存docx文件
            docx.write(fos);

            // 关闭资源
            docx.close();
            doc.close();
            extractor.close();

            return docxFile;
        } catch (Exception e) {
            log.error("doc文件转换失败2", e);
            // 如果转换失败，删除临时文件
            if (docxFile.exists()) {
                java.nio.file.Files.delete(docxFile.toPath());
            }
            throw new IOException("doc文件转换失败3", e);
        }
    }

    @Override
    public Map<String, SaveFileDTO> saveFileBatch(Collection<SaveBatchFileDTO> saveBatchFileCollection) {
        Map<String, SaveFileDTO> resultMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(saveBatchFileCollection)) {
            for (SaveBatchFileDTO saveBatchFileDTO : saveBatchFileCollection) {
                resultMap.put(saveBatchFileDTO.getBizId(),
                    saveSourceFile(saveBatchFileDTO.getBizId(), saveBatchFileDTO.getBizType(),
                        saveBatchFileDTO.getName(),
                        saveBatchFileDTO.getTempPath()));
            }
        }
        return resultMap;
    }

    @Override
    public String saveTextToTempFile(String text, String fileName) {

        log.info("saveTextToTempFile text: {}", text);

        String tempPath = sysConfig.queryTempPath(StringUtil.newId() + ".txt");
        String physicalPath = sysConfig.getPhysicalPath(tempPath);
        File file = new File(physicalPath);
        if (!file.getParentFile().exists()) {
            boolean mkdirs = file.getParentFile().mkdirs();
            log.info("saveTextToTempFile mkdirs: " + mkdirs);
        }

        try (FileWriter writer = new FileWriter(file)) {
            writer.write(text);
            writer.flush();
            log.info("file size: " + new File(sysConfig.getPhysicalPath(tempPath)).length());
        } catch (IOException e) {
            log.error("saveTextToTempFile failedToWrite: {}", file.getAbsolutePath(), e);
            // 处理异常情况，例如记录日志或抛出异常
        }

        UploadFileRequest uploadFileRequest = new UploadFileRequest();
        uploadFileRequest.setFileName(tempPath);
        uploadFileRequest.setAsyncUpload(false);
        uploadFileRequest.setTempFilePath(file.getAbsolutePath());
        uploadFileRequest.setCopyFolderFlag(false);
        UploadFileResponse uploadFileResponse = uploadFileService.uploadFile(uploadFileRequest);
        return storageStrategyService.getUrl(uploadFileResponse.getFileUrl(), true, fileName);
    }

    @Override
    public String getScormPlayUrl() {
        return getPublicReadFileUrl(UserThreadContext.getTenantId() + "/file/scormPlugin/index.html");
    }

    @Override
    public String getPublicReadFileUrl(String path) {
        return urlHelper.getPublicReadFileUrl(path);
    }

    @Override
    public void deletePhysicalFileByIds(Collection<String> bizIds) {
        if (CollectionUtils.isEmpty(bizIds)) {
            return;
        }

        // 查询数据库中的文件记录
        List<Files> filesList = filesService.getFilesCategoryIds(bizIds);
        if (CollectionUtils.isEmpty(filesList)) {
            return;
        }

        // 物理删除对象存储中的文件
        for (Files file : filesList) {
            String currentPath = file.getCurrentPath();
            if (StringUtils.isNotBlank(currentPath)) {
                storageStrategyService.deleteFile(currentPath);
            }
        }

        // 删除数据库记录
        filesService.remove(new LambdaQueryWrapper<Files>().in(Files::getCategoryId, bizIds));
    }

    @Override
    public SaveFileDTO savePublicReadImage(String bizId, String bizType, String name, String tempFilePath) {
        UploadFileResponse uploadFileResponse = savePubLicReadFile(FileTypeEnum.IMAGE, tempFilePath, bizType, bizId);
        Images images = buildImages(name, bizId, bizType, uploadFileResponse.getFileUrl(),
            uploadFileResponse.getFileSize(), 0, 0);
        imagesService.save(images);
        return buildSaveFileDTO(uploadFileResponse.getFileUrl(), tempFilePath, images.getId(), bizId, bizType,
            images.getImageSize());
    }

    @Override
    public void saveOrUpdatePublicReadOnePic(String bizId, String bizType, String name, String tempFilePath,
        Boolean isInsert) {
        if (StringUtils.isNotEmpty(tempFilePath) && Boolean.TRUE.equals(isInsert)) {
            savePublicReadImage(bizId, bizType, name, tempFilePath);
        }
        if (Boolean.FALSE.equals(isInsert)) {
            NamePath dbNamePath = getImageFileNamePath(bizId, bizType);
            // 编辑
            boolean hasPathCondition =
                dbNamePath != null && dbNamePath.getPath() != null && StringUtils.isNotBlank(tempFilePath);
            if (hasPathCondition && !dbNamePath.getPath().equals(tempFilePath)) {
                deleteImageByBizIdAndBizType(bizId, bizType);
                savePublicReadImage(bizId, bizType, name, tempFilePath);
            }
            // 保存
            if (CheckObjAllFieldsIsNullUtil.checkObjAllFieldsIsNull(dbNamePath) && StringUtils.isNotBlank(
                tempFilePath)) {
                savePublicReadImage(bizId, bizType, name, tempFilePath);
            }
            // 删除
            if (!CheckObjAllFieldsIsNullUtil.checkObjAllFieldsIsNull(dbNamePath) && StringUtils.isBlank(tempFilePath)) {
                deleteImageByBizIdAndBizType(bizId, bizType);
            }
        }
    }

    private UploadFileResponse savePubLicReadFile(FileTypeEnum fileType, String sourceFile, String bizType,
        String bizId) {
        return savePublicReadFile(fileType, sourceFile, bizType, true, false, bizId);
    }

    private UploadFileResponse savePublicReadFile(FileTypeEnum fileType, String sourceFile, String bizType,
        boolean unzip,
        boolean isAsync, String bizId) {

        // 定义 File 对象
        File source = new File(sysConfig.getPhysicalPath(sourceFile));

        // 验证对象存储是否存在
        boolean ossExists = storageStrategyService.checkOssIsExists(sourceFile);

        log.info("验证对象存储是否存在。对象：{} 文件类型：{}  bizType：{} unzip：{}  是否存在：{}", sourceFile,
            fileType.getValue(), bizType,
            unzip, ossExists);

        String lastPath = FilenameUtils.getName(sourceFile);

        if (sourceFile.endsWith("html")) {
            source = new File(sysConfig.getPhysicalPath(FilenameUtils.getFullPathNoEndSeparator(sourceFile)));
        }

        if ("index".equalsIgnoreCase(FilenameUtils.getBaseName(sourceFile))) {
            lastPath = sourceFile.substring(
                FilenameUtils.getFullPathNoEndSeparator(sourceFile).lastIndexOf(SEPARATOR) + 1);
        }

        String strBuilder =
            SEPARATOR + sysConfig.getRoot() + SEPARATOR + "publicRead" + SEPARATOR + DateUtil.getYmStr()
                + SEPARATOR + bizType;
        if (sourceFile.contains(NEW_CHECK)) {
            strBuilder += SEPARATOR + NEW;
        }
        strBuilder += SEPARATOR + lastPath;

        UploadFileRequest uploadFileRequest = new UploadFileRequest();
        uploadFileRequest.setFileName(strBuilder);
        uploadFileRequest.setAsyncUpload(isAsync);
        uploadFileRequest.setPublicRead(true);
        if (sourceFile.endsWith("html")) {
            // 在线做课，其他文件要公有读
            uploadFileRequest.setDefaultStrategyFileList(List.of(sysConfig.getPhysicalPath(sourceFile)));
        }

        if (source.isDirectory()) {
            uploadFileRequest.setCopyFolderFlag(true);
            uploadFileRequest.setFolderSrcPath(
                sysConfig.getPhysicalPath(FilenameUtils.getFullPathNoEndSeparator(sourceFile)));
            uploadFileRequest.setFolderTargetPath(FilenameUtils.getFullPathNoEndSeparator(strBuilder));
        } else {
            uploadFileRequest.setCopyFolderFlag(false);
            uploadFileRequest.setTempFilePath(sysConfig.getPhysicalPath(sourceFile));
        }

        UploadFileResponse uploadFileResponse;

        if (ossExists) {
            if (sourceFile.equals(strBuilder)) {
                log.info("目标对象存储已存在，直接返回。目标路径：{}", sourceFile);
                uploadFileResponse = new UploadFileResponse();
                uploadFileResponse.setFileUrl(strBuilder);
                uploadFileResponse.setFilePath(strBuilder);
            } else {
                log.info("执行对象存储Copy。{} 到 {}", sourceFile, strBuilder);
                // 执行对象存储 Copy
                uploadFileResponse = storageStrategyService.copyFile(sourceFile, strBuilder, true);
            }
        } else {
            uploadFileRequest.setBizId(bizId);
            log.info("非对象存储，执行文件上传：{}", uploadFileRequest);
            uploadFileResponse = uploadFileService.uploadFile(uploadFileRequest);
        }

        // 无需解压，直接返回
        if (!unzip) {
            return uploadFileResponse;
        }
        // 需要解压，返回路径
        String path = unZipAndUpload(sourceFile, strBuilder, bizId);
        if (StringUtils.isNotBlank(path)) {
            uploadFileResponse.setFilePath(path);
            uploadFileResponse.setFileUrl(path);
        }

        return uploadFileResponse;
    }

    @Override
    public void dealWithPublicReadPath(List<String> filePathList) {
        LambdaQueryWrapper<Files> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Files::getCategoryType,
            List.of(FileBizType.CourseWareFile.name(), FileBizType.EXAMPLE_FILE.name(),
                FileBizType.APPRAISE_UPLOAD_FILE.name(), FileBizType.APPRAISE_EXAMPLE_FILE.name(),
                FileBizType.JOB_QUALIFICATION_FILE.name(), FileBizType.CourseWarePackages.name(),
                FileBizType.COURSE_WARE_DOCUMENT_FILE.name(), FileBizType.InfoWareFile.name(),
                FileBizType.KnowledgeLibMaterial.name(), FileBizType.APPLY_CERTIFICATION_MATERIAL_EXAMPLE.name(),
                FileBizType.APPLY_OTHER_MATERIAL_EXAMPLE.name(), FileBizType.PROJECT_APP_FILE.name(),
                FileBizType.FORM_TASK_FILE.name(), FileBizType.RECRUITING_RULE_FILE.name(),
                FileBizType.RECRUITING_UPLOAD_FILE.name(), FileBizType.RECRUITING_PUBLICITY_FILE.name(),
                FileBizType.TRAIN_APP_EXAMPLE.name()));
        queryWrapper.eq(Files::getIsSource, 0);
        queryWrapper.eq(Files::getIsAdjunct, 0);
        queryWrapper.notLike(Files::getFileName, ".mp3");
        List<String> pathList = filesService.list(queryWrapper).stream().map(Files::getCurrentPath)
            .filter(StringUtils::isNotBlank).toList();
        storageStrategyService.dealWithOldFile(pathList, filePathList);
    }

    @Override
    public String getCustomPhysicalPath(String fileName, String bizType) {
        return sysConfig.getFilePathByType(fileName, bizType);
    }
}