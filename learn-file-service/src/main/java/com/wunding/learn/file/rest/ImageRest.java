package com.wunding.learn.file.rest;

import com.wunding.learn.common.bean.Result;
import com.wunding.learn.common.constant.file.FileErrorNoEnum;
import com.wunding.learn.common.constant.user.UserErrorNoEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.file.config.SysConfig;
import com.wunding.learn.file.constant.FileTypeEnum;
import com.wunding.learn.file.dto.UploadResultDTO;
import com.wunding.learn.file.service.IFilesService;
import com.wunding.learn.file.util.CropImgUtil;
import com.wunding.learn.user.api.dto.third.UserInfoDTO;
import com.wunding.learn.user.api.service.ThirdFeign;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @date 2022/3/1
 */
@RestController
@RequestMapping("${module.file.contentPath:/}")
@Slf4j
@Tag(description = "图片相关接口", name = "image")
public class ImageRest {

    private static final String[] IMG_SUFFIX = {"png", "jpg", "jpeg", "x-icon", "ico", "icon"};

    @Resource
    private IFilesService filesService;

    @Resource
    private SysConfig sysConfig;
    @Resource
    private ThirdFeign thirdFeign;


    /**
     * 上传图片文件(供小程序使用)
     */
    @PostMapping(value = "/uploadImgForMini", consumes = "multipart/form-data")
    @Operation(operationId = "uploadImgForMini", summary = "上传图片文件(供小程序使用)")
    public Result<UploadResultDTO> uploadImgForMini(
        @Parameter(description = "文件名,当上传方式是Blob时有效") @RequestParam(required = false) String fileName,
        @Parameter(description = "小程序登录校验码") @RequestParam(required = true) String code,
        @Parameter(description = "图片文件", required = true) @RequestPart(name = "file") MultipartFile uploadFile) {

        UserInfoDTO userInfoDTO = thirdFeign
            .getWeChatMiniProgramsUserInfo(UserThreadContext.getTenantId(), code);
        log.info("Login By OAuth2 WeChatMiniPrograms Get User Info DTO：{}", JsonUtil.objToJson(userInfoDTO));

        // 第三方用户授权Code无效
        if (null != userInfoDTO.getErrCode() && userInfoDTO.getErrCode() != 0) {
            log.warn(userInfoDTO.getErrMsg());
            throw new BusinessException(UserErrorNoEnum.ERR_OAUTH2_INVALID_CODE);
        }
        return uploadImg(fileName, uploadFile);
    }

    /**
     * 上传图片文件
     */
    @PostMapping(value = "/uploadImg", consumes = "multipart/form-data")
    @Operation(operationId = "uploadImg", summary = "上传图片文件")
    public Result<UploadResultDTO> uploadImg(
        @Parameter(description = "文件名,当上传方式是Blob时有效") @RequestParam(required = false) String fileName,
        @Parameter(description = "图片文件", required = true) @RequestPart(name = "file") MultipartFile uploadFile) {
        log.info("getOriginalFilename:" + uploadFile.getOriginalFilename());
        log.info("getName:" + uploadFile.getName());
        log.info("isEmpty:" + uploadFile.isEmpty());
        if (uploadFile.isEmpty()) {
            throw new BusinessException(FileErrorNoEnum.ERR_FILE_NOT_NULL);
        }
        if (StringUtils.isNotBlank(fileName) && Objects.equals(uploadFile.getOriginalFilename(), "blob")) {
            filesService.checkFileTypeInLine(fileName, IMG_SUFFIX);
        } else if (!Objects.equals(uploadFile.getOriginalFilename(), "blob")) {
            filesService.checkFileTypeInLine(uploadFile.getOriginalFilename(), IMG_SUFFIX);
        } else {
            throw new BusinessException(FileErrorNoEnum.ERR_PHOTO_FORMAT);
        }
        return Result.success(filesService.saveUploadFile(uploadFile,
            StringUtils.isNotBlank(fileName) ? fileName : uploadFile.getOriginalFilename()));
    }

    /**
     * 批量上传图片
     *
     * @param uploadFiles 上传的文件数组
     */
    @PostMapping(value = "/batchUploadImg", consumes = "multipart/form-data")
    @Operation(operationId = "uploadImages", summary = "上传图片文件")
    public Result<List<UploadResultDTO>> uploadImages(
        @Parameter(description = "图片文件", required = true) @RequestPart(name = "files") MultipartFile[] uploadFiles) {
        List<UploadResultDTO> list = new ArrayList<>();
        for (MultipartFile uploadFile : uploadFiles) {
            filesService.checkFileType(uploadFile.getOriginalFilename(), IMG_SUFFIX);
        }
        for (MultipartFile uploadFile : uploadFiles) {
            list.add(filesService.saveUploadFile(uploadFile, uploadFile.getOriginalFilename()));
        }
        return Result.success(list);
    }

    /**
     * 图片裁切
     *
     * @param srcPath 原图片路径
     * @param rate    缩放比例
     * @param x       起点x
     * @param y       起点y
     * @param width   宽
     * @param height  高
     * @return 裁切后的图片路径
     */
    @GetMapping(value = "/cropImg/{rate}/{x}/{y}/{width}/{height}")
    @Operation(operationId = "cropImg", summary = "图片裁切")
    public Result<UploadResultDTO> cropImg(
        @Parameter(description = "原图片路径", required = true) @RequestParam("srcPath") String srcPath,
        @Parameter(description = "缩放比例", required = true) @PathVariable("rate") Double rate,
        @Parameter(description = "起点x", required = true) @PathVariable("x") Integer x,
        @Parameter(description = "起点y", required = true) @PathVariable("y") Integer y,
        @Parameter(description = "宽", required = true) @PathVariable("width") Integer width,
        @Parameter(description = "高", required = true) @PathVariable("height") Integer height) {

        UploadResultDTO uploadResultDTO = new UploadResultDTO();
        try {
            // 截取框的x轴开始位置
            x = (int) (x * rate);
            // 截取框的y轴开始位置
            y = (int) (y * rate);
            // 截取框的宽度
            int circleWidth = (int) (width * rate);
            // 截取框的高度
            int circleHeight = (int) (height * rate);
            String imagePath = sysConfig.getPhysicalPath(srcPath);
            // 将文件重命名，生成新的文件
            int index = srcPath.lastIndexOf("/");
            String newName = StringUtil.newId() + "." + FilenameUtils.getExtension(srcPath.substring(index + 1));
            String newFilePath = srcPath.substring(0, index + 1) + newName;

            CropImgUtil.operteImage(imagePath, sysConfig.getPhysicalPath(newFilePath), x, y, circleWidth, circleHeight);
            uploadResultDTO.setPath(FilenameUtils.separatorsToUnix(newFilePath));
            return Result.success(uploadResultDTO);
        } catch (Exception e) {
            log.error("图片截取:", e);
            Result.fail();
        }
        return Result.success(uploadResultDTO);
    }

    /**
     * 上传图片文件
     */
    @PostMapping(value = "/uploadImgToFormalPath", consumes = "multipart/form-data")
    @Operation(operationId = "uploadImgToFormalPath", summary = "上传图片文件至公有读正式路径")
    public Result<UploadResultDTO> uploadImgToFormalPath(
        @Parameter(description = "文件名,当上传方式是Blob时有效") @RequestParam(required = false) String fileName,
        @Parameter(description = "图片文件", required = true) @RequestPart(name = "file") MultipartFile uploadFile) {
        log.info("getOriginalFilename:" + uploadFile.getOriginalFilename());
        log.info("getName:" + uploadFile.getName());
        log.info("isEmpty:" + uploadFile.isEmpty());
        if (uploadFile.isEmpty()) {
            throw new BusinessException(FileErrorNoEnum.ERR_FILE_NOT_NULL);
        }
        if (StringUtils.isNotBlank(fileName) && Objects.equals(uploadFile.getOriginalFilename(), "blob")) {
            filesService.checkFileType(fileName, IMG_SUFFIX);
        } else if (!Objects.equals(uploadFile.getOriginalFilename(), "blob")) {
            filesService.checkFileType(uploadFile.getOriginalFilename(), IMG_SUFFIX);
        } else {
            throw new BusinessException(FileErrorNoEnum.ERR_FILE_FORMAT);
        }
        return Result.success(filesService.saveUploadFileToFormalPath(uploadFile,
            StringUtils.isNotBlank(fileName) ? fileName : uploadFile.getOriginalFilename(), FileTypeEnum.IMAGE));
    }
}
