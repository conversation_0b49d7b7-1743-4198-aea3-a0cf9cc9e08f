package com.wunding.learn.file.dto;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class TenantBucketInfo {

    /**
     * 对象存储path模式
     */
    private Integer pathMode;

    /**
     * 内网对象存储接入点
     */
    private String intranetEndPoint;

    /**
     * 对象存储类型 ali tencent aws minio ceph
     */
    private String type;

    /**
     * 接入点
     */
    private String endPoint;

    /**
     * accessKey
     */
    private String accessKey;

    /**
     * secretKey
     */
    private String secretKey;

    /**
     * 区域
     */
    private String region;

    /**
     * 根路径
     */
    private String rootPath;

    /**
     * 静态资源访问URL
     */
    private String staticBaseUrl;


}
