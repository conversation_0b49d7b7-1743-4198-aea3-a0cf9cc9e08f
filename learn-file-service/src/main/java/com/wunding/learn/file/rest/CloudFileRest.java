package com.wunding.learn.file.rest;


import com.wunding.learn.common.bean.Result;
import com.wunding.learn.file.dto.UploadCouldChunkDTO;
import com.wunding.learn.file.dto.UploadCouldComposeChunkDTO;
import com.wunding.learn.file.dto.UploadCouldResultDTO;
import com.wunding.learn.file.dto.UploadCourseResultDTO;
import com.wunding.learn.file.dto.UploadResultDTO;
import com.wunding.learn.file.dto.UploadZipResultDTO;
import com.wunding.learn.file.service.ICloudFileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * <p>  云文件 前端控制器
 *
 * <AUTHOR> href="mailto:<EMAIL>">wgr</a>
 * @since 2024-01-29
 */
@Slf4j
@RestController
@RequestMapping("${module.file.contentPath:/}cloudFile")
@Tag(description = "云端文件上传", name = "CloudFileRest")
public class CloudFileRest {

    @Resource
    private ICloudFileService cloudFileService;


    @Operation(operationId = "checkSuffixes", summary = "根据业务类型检测文件上传类型,如果检测不通过,抛出检测不通过的异常")
    @GetMapping(value = "checkSuffixes")
    public Result<Void> checkSuffixes(
        @Parameter(description = "文件名称", required = true)
        @RequestParam(name = "fileName", required = true) String fileName,
        @Parameter(description = "1=默认所有文件能够上传 2=专题材料类型 3=APP文件类型检测 4=新闻资讯文件类型检测 5=培训计划附件类型检测", required = true)
        @RequestParam(name = "checkType", required = true) Integer checkType) {

        cloudFileService.checkSuffixes(fileName, checkType);
        return Result.success();
    }

    @Operation(operationId = "checkCloudFile", summary = "根据md5检测云文件是否存在")
    @GetMapping(value = "checkCloudFile")
    public Result<UploadCouldResultDTO> checkCloudFile(
        @Parameter(description = "文件MD5", required = true) @RequestParam(name = "md5") String md5,
        @Parameter(description = "文件名") @RequestParam(name = "fileName", required = false) String fileName) {
        return Result.success(cloudFileService.checkCloudFile(md5, fileName));
    }

    @Operation(operationId = "uploadCloudFileChunk", summary = "上传云文件分片")
    @PostMapping(value = "uploadCloudFileChunk", consumes = "multipart/form-data")
    public Result<UploadCouldResultDTO> uploadCloudFileChunk(
        @Parameter(description = "文件", required = true) @RequestPart(name = "chunk") MultipartFile chunk,
        UploadCouldChunkDTO uploadCouldChunkDTO) {
        return Result.success(cloudFileService.uploadCloudFileChunk(chunk, uploadCouldChunkDTO));
    }

    @Operation(operationId = "composeCloudFile", summary = "合并分片成最后的文件")
    @PostMapping(value = "composeCloudFile")
    public Result<UploadCouldResultDTO> composeCloudFile(
        @RequestBody UploadCouldComposeChunkDTO uploadCouldComposeChunkDTO) {
        return Result.success(cloudFileService.composeCloudFile(uploadCouldComposeChunkDTO));
    }


    @Operation(operationId = "uploadCourseWareFile_cloudFile", summary = "上传课件文件,调用合并文件后调用")
    @PostMapping(value = "uploadCourseWareFile")
    public Result<UploadCourseResultDTO> uploadCourseWareFile(@RequestBody UploadResultDTO uploadResultDTO) {
        return Result.success(cloudFileService.uploadCourseWareFile(uploadResultDTO));
    }


    @Operation(operationId = "uploadFileInfo_cloudFile", summary = "新闻资讯上传文件,调用合并文件后调用")
    @PostMapping(value = "uploadFileInfo")
    public Result<UploadCourseResultDTO> uploadFileInfo(@RequestBody UploadResultDTO uploadResultDTO) {
        return Result.success(cloudFileService.uploadFileInfo(uploadResultDTO));
    }

    @Operation(operationId = "uploadZipFileToDecompress_cloudFile", summary = "上传zip文件并解压返回")
    @PostMapping(value = "uploadZipFileToDecompress")
    public Result<UploadZipResultDTO> uploadZipFileToDecompress(@RequestBody UploadResultDTO uploadResultDTO) {
        return Result.success(cloudFileService.uploadZipFileToDecompress(uploadResultDTO));
    }

    @Operation(operationId = "uploadCertRelateZip_cloudFile", summary = "通过外部证书图片添加学员证书")
    @PostMapping(value = "uploadCertRelateZip")
    public Result<UploadZipResultDTO> uploadCertRelateZip(@RequestBody UploadResultDTO uploadResultDTO) {
        return Result.success(cloudFileService.uploadCertRelateZip(uploadResultDTO));
    }

    @Operation(operationId = "uploadCertPhotoZip_cloudFile", summary = "上传zip更新外部证书图片")
    @PostMapping(value = "uploadCertPhotoZip")
    public Result<UploadZipResultDTO> uploadCertPhotoZip(@RequestBody UploadResultDTO uploadResultDTO) {
        return Result.success(cloudFileService.uploadCertPhotoZip(uploadResultDTO));
    }

}
