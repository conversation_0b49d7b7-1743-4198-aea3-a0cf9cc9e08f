package com.wunding.learn.file.mq.consumer;

import com.rabbitmq.client.Channel;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.file.TranscodeStatusEnum;
import com.wunding.learn.common.mq.event.PriorityTransCodeEvent;
import com.wunding.learn.common.mq.event.TransCodeEvent;
import com.wunding.learn.common.mq.event.TransCodeFinishEvent;
import com.wunding.learn.common.mq.service.MqProducer;
import com.wunding.learn.common.mq.util.ConsumerAckUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.file.trans.handler.TransServiceHandler;
import com.wunding.learn.file.trans.pdf.pojo.TransCodeResult;
import jakarta.annotation.Resource;
import java.io.IOException;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

/**
 * 优先队列消费者 ; 逻辑同普通转码 @see TransCodeEventConsumer
 * <AUTHOR>
 */
@Component
@Slf4j
public class PriorityTransCodeEventConsumer {

    @Resource
    private TransServiceHandler transServiceHandler;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Resource
    private MqProducer mqProducer;

    @Value("${app.transRetry:1}")
    private Integer transRetry;

    @Value("${app.maxRetryPeriod:7}")
    private Integer maxRetryPeriod;

    private static final String PRIORITY_TRANS_CODE_EVENT_CONSUMER_QUEUE = "priorityTransCodeEventConsumerQueue";

    private static final String REDIS_KEY_PREFIX = "transcode:processed:";
    private String acquireRedisKey(String businessId) {
        return REDIS_KEY_PREFIX + UserThreadContext.getTenantId() + ":" + businessId;
    }

    /**
     * 课件转码优先处理消息 ： 需要处理 普通转码存在的消息，在优先队列中处理时，二者的排他性
     * 1 、 消息状态检查
     * 2 、 业务幂等性处理【一定时间窗口内已完成的不再额外处理，超过则可以继续转码，如超过一天内的可以继续转码】
     *
     */
    @RabbitListener(
        concurrency = "${app.priorityTransConcurrency:1-2}",
        bindings =
        @QueueBinding(
            value = @Queue(value = PRIORITY_TRANS_CODE_EVENT_CONSUMER_QUEUE),
            exchange = @Exchange(value = TransCodeEvent.CHANGE, type = ExchangeTypes.TOPIC),
            key = PriorityTransCodeEvent.ROUTING_KEY
        ), id = "priorityTransCodeEventConsumer")
    public void priorityTransCodeEventConsumer(
        @Payload PriorityTransCodeEvent transCodeEvent,
        @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
        Channel channel) throws IOException {
        log.info("PriorityTransCodeEventConsumer received transCodeEvent: {}", JsonUtil.objToJson(transCodeEvent));

        UserThreadContext.setTenantId(transCodeEvent.getTenantId());

        try {
            String redisKey = acquireRedisKey(transCodeEvent.getBizId());
            // 因为是优先队列，则直接删除在处理中的key，直接插队转码
            redisTemplate.delete(redisKey);

            // 60   分钟内
            redisTemplate.opsForValue().set(redisKey, "PROCESSING", 15, TimeUnit.DAYS);
            log.info("业务幂等性处理, redisKey:{}, transCodeEvent:{}", redisKey, JsonUtil.objToJson(transCodeEvent));

            noticeBiz(transCodeEvent, TranscodeStatusEnum.TRANSFORMING);
            TransCodeResult transCodeResult = transServiceHandler.trans(transCodeEvent);
            log.info("transCodeResult:{}", JsonUtil.objToJson(transCodeResult));
            if ("RETRY".equals(transCodeResult.getErrorMessage())) {
                log.error("转码异常连接问题 , 重入队列延迟重试.");
                Thread.sleep(30000);
                ConsumerAckUtil.basicNack(transCodeEvent, channel, deliveryTag, false, true);
                UserThreadContext.remove();
                return;
            }
        } catch (InterruptedException e){
            Thread.currentThread().interrupt();
            log.error("转码异常，线程中断--", e);
        }catch (Exception e) {
            log.error("转码异常，转码信息:BizId:{},Path:{},BizType:{},tenantId:{}-->>",
                transCodeEvent.getBizId(), transCodeEvent.getPath(), transCodeEvent.getBizType(), transCodeEvent.getTenantId(),
                e);

            // 获取重试次数
            int retryCount = 0;
            // // 基于 Redis 存储重试次数
            String retryKey = "transcode:retry_count:" + transCodeEvent.getId();
            String retryStr = redisTemplate.opsForValue().get(retryKey);
            if (retryStr != null) {
                retryCount = Integer.parseInt(retryStr);
            }

            if (retryCount < transRetry) {
                try {
                    // 重新入队，改为排队中的状态
                    noticeBiz(transCodeEvent, TranscodeStatusEnum.PENDING);
                    ConsumerAckUtil.basicNack(transCodeEvent, channel, deliveryTag, false, true);
                    redisTemplate.opsForValue().set(retryKey, String.valueOf(retryCount + 1), maxRetryPeriod, TimeUnit.DAYS);
                } catch (IOException ex) {
                    log.error("转码失败，重试发送 basicNack 失败--", ex);
                }
            } else {
                try {
                    // 一次重试的机会，则不再处理，直接失败了。
                    ConsumerAckUtil.basicAck(transCodeEvent, channel, deliveryTag, false);
                } catch (IOException ex) {
                    log.error("转码失败，拒绝消息失败--", ex);
                }
            }
        }
        UserThreadContext.remove();
        //手动ack确认
        ConsumerAckUtil.basicAck(transCodeEvent, channel, deliveryTag, false);
    }

    /**
     * 排队中 、  转码中 区分
     */
    private void noticeBiz(TransCodeEvent transCodeEvent, TranscodeStatusEnum transcodeStatusEnum) {
        TransCodeFinishEvent finishEvent = new TransCodeFinishEvent(
            transCodeEvent.getBizType(),
            transCodeEvent.getBizId(),
            transcodeStatusEnum.value,
            transCodeEvent.getMime(),
            transCodeEvent.getCwLibId()
        );
        mqProducer.sendMsgNoTransaction(finishEvent);
    }
}
