package com.wunding.learn.file.rest;

import com.wunding.learn.common.bean.Result;
import com.wunding.learn.file.constant.FileTypeEnum;
import com.wunding.learn.file.dto.UploadResultDTO;
import com.wunding.learn.file.service.IFilesService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 视频上传
 *
 * <AUTHOR>
 * @date 2022/3/2
 */
@RestController
@RequestMapping("${module.file.contentPath:/}")
@Tag(description = "视频相关接口", name = "video")
public class VideoRest {

    private static final String[] VIDEO_SUFFIX = {"mp4"};

    @Resource
    private IFilesService filesService;

    /**
     * 视频文件上传，上传到临时文件夹
     */
    @PostMapping(value = "/uploadVideoFile")
    @Operation(operationId = "uploadVideoFile", summary = "上传视频文件")
    public Result<UploadResultDTO> uploadVideoFile(
        @Parameter(description = "excel文件", required = true) @RequestPart(name = "file")
        MultipartFile uploadFile) {
        filesService.checkFileType(uploadFile.getOriginalFilename(), VIDEO_SUFFIX);
        return Result.success(filesService.saveUploadFile(uploadFile, uploadFile.getOriginalFilename()));
    }

    /**
     * 视频文件上传，上传到正式文件夹
     */
    @PostMapping(value = "/uploadVideoFileToFormalPath")
    @Operation(operationId = "uploadVideoFileToFormalPath", summary = "上传视频文件至公有读正式路径")
    public Result<UploadResultDTO> uploadVideoFileToFormalPath(
        @Parameter(description = "excel文件", required = true) @RequestPart(name = "file")
        MultipartFile uploadFile) {
        filesService.checkFileType(uploadFile.getOriginalFilename(), VIDEO_SUFFIX);
        return Result.success(filesService.saveUploadFileToFormalPath(uploadFile, uploadFile.getOriginalFilename(),
            FileTypeEnum.FILE));
    }
}
