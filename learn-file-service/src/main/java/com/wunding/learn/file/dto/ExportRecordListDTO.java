package com.wunding.learn.file.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;

/**
 * </p> 导出记录表
 *
 * <AUTHOR> href="mailto:<EMAIL>">ylq</a>
 * @since 2022-09-24
 */
@Data
@Schema(name = "ExportRecordListDTO", description = "导出记录列表对象")
public class ExportRecordListDTO {

    /**
     * 业务id
     */
    @Schema(description = "业务id")
    private String bizId;


    /**
     * 业务类型
     */
    @Schema(description = "业务类型")
    private String bizType;


    /**
     * 文件名称
     */
    @Schema(description = "数据标题")
    private String fileName;

    /**
     * 导出的url
     */
    @Schema(description = "导出的url")
    private String currentUrl;


    /**
     * 导出文件花费的时长
     */
    @Schema(description = "耗时")
    private Integer exportTime;


    /**
     * 导出的状态 1:导出完成，2:导出中，3:导出失败
     */
    @Schema(description = "导出的状态 1:导出完成，2:导出中，3:导出失败")
    private Integer status;


    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    private Integer isDel;


    /**
     * 新增人
     */
    @Schema(description = "新增人")
    private String createBy;


    /**
     * 新增时间
     */
    @Schema(description = "下载时间")
    private Date createTime;


    /**
     * 最后编辑人
     */
    @Schema(description = "最后编辑人")
    private String updateBy;


    /**
     * 最后编辑时间
     */
    @Schema(description = "最后编辑时间")
    private Date updateTime;

    public Integer getExportTime() {
        if (exportTime != null && exportTime < 1) {
            return 1;
        } else {
            return exportTime;
        }
    }
}
