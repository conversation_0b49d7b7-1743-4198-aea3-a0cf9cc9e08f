package com.wunding.learn.file.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

/**
 * 上传文件统一返回对象
 *
 * <AUTHOR>
 * @date 2022/3/1
 */
@Data
@Schema(name = "UploadCouldResultDTO", description = "上传文件统一返回对象")
public class UploadCouldResultDTO {


    @Schema(description = "文件上传后的路径，有这个路径代表已经上传到服务器，如果没有这个路径，则看分片信息，分配信息没有，则从最开始上传分片信息")
    private String path;

    @Schema(description = "文件url")
    private String url;

    @Schema(description = "文件原始名")
    private String fileName;

    @Schema(description = "文件存在磁盘上的名称", hidden = true)
    private String diskFileName;

    @Schema(description = "针对课件上传 文件对应scorm的媒体类型 scorm/mimes.xml")
    private String mime;

    @Schema(description = "宽")
    private Integer width;

    @Schema(description = "高")
    private Integer height;

    @Schema(description = "当前上传到服务的分片列表")
    private List<Integer> chunkList;

    @Schema(description = "合并状态 0-否 1-是", hidden = true)
    private Integer mergeStatus;
}
