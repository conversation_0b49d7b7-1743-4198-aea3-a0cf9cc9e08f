package com.wunding.learn.file.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import com.wunding.learn.file.api.dto.FileListDTO;
import com.wunding.learn.file.api.query.FileQuery;
import com.wunding.learn.file.model.Files;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

/**
 * 文件表 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">xiao8</a>
 * @date 2022-03-11
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")
})
public interface FilesMapper extends BaseMapper<Files> {

    /**
     * 根据关联模块和附件类别筛选文件
     *
     * @param categoryId   关联id
     * @param categoryType 关联模块
     * @param isAdjunct    是否为附件
     * @return 文件
     */
    Files getFileByCategoryTypeAndIsAdjunct(@Param("categoryId") String categoryId,
        @Param("categoryType") String categoryType, @Param("isAdjunct") Integer isAdjunct);

    /**
     * 根据关联模块id获取文件
     *
     * @param categoryId 关联模块Id
     * @return
     */
    Files getFileByCategoryId(@Param("categoryId") String categoryId);

    /**
     * 根据关联模块id获取文件
     *
     * @param categoryId 关联模块Id
     * @param isSource 是否源文件
     * @param isAdjunct 是否附件
     * @return
     */
    Files getFileByCategoryIdAndIsSourceAndIsAdjunct(@Param("categoryId") String categoryId,
        @Param("isSource") Integer isSource, @Param("isAdjunct") Integer isAdjunct);

    /**
     * 根据关联模块ids批量获取文件
     *
     * @param categoryIds
     * @return
     */
    List<Files> getFileMapByCategoryIds(@Param("categoryIds") Collection<String> categoryIds);

    /**
     * 根据关联模块ids和类型精确批量获取文件
     *
     * @param categoryIds
     * @return
     */
    List<Files> getSourceFileMapByCategoryIds(@Param("categoryIds") Collection<String> categoryIds);

    /**
     * 根据id和类型以及文件名模糊分页查询
     *
     * @param fileQuery
     * @return
     */
    List<FileListDTO> getFileByFileQuery(@Param("params") FileQuery fileQuery);

    /**
     * 根据关联模块ids批量获取主文件
     *
     * @param categoryIds
     * @return
     */
    List<Files> getMainFileMapByCategoryIds(@Param("categoryIds") Collection<String> categoryIds);

    Files getFileBybizIdAndbizType(@Param("bizId")String bizId, @Param("bizType")String bizType, @Param("i") Integer i);

    List<Files> getFileUrlByIdsAndType(Set<String> categoryIds, String bizType);

    /**
     * 根据关联模块ids批量获取文件
     *
     * @param categoryIds
     * @return
     */
    List<Files> getFilesCategoryIds(@Param("categoryIds") Collection<String> categoryIds);
}
