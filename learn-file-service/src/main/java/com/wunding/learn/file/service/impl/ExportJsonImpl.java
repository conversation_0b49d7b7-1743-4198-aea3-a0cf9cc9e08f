package com.wunding.learn.file.service.impl;

import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.file.config.SysConfig;
import com.wunding.learn.file.model.UploadFileRequest;
import com.wunding.learn.file.service.ExportJsonService;
import com.wunding.learn.file.service.UploadFileService;
import com.wunding.learn.file.util.FileUtil;
import jakarta.annotation.Resource;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("exportJsonService")
public class ExportJsonImpl implements ExportJsonService {

    public static String SEPARATOR = "/";

    @Resource
    private SysConfig sysConfig;

    @Resource
    private UploadFileService uploadFileService;

    @Override
    public void exportJsonDto(Object object) {
        String filePath = sysConfig.queryTempPath() + SEPARATOR;
        String fileName = "env.json";
        String path = sysConfig.getPhysicalPath(filePath);
        try {
            FileUtil.mkdir(path);
            OutputStreamWriter osw = new OutputStreamWriter(new FileOutputStream(path + fileName),
                StandardCharsets.UTF_8);
            osw.write(JsonUtil.objToJson(object));
            osw.flush();
            osw.close();
        } catch (IOException e) {
            log.error("发生异常", e);
        }
        log.info("输出文件地址：{}", filePath + fileName);
        log.info("json:{}", JsonUtil.objToJson(object));
        UploadFileRequest uploadFileRequest = new UploadFileRequest();
        uploadFileRequest.setFileName(UserThreadContext.getTenantId() + SEPARATOR + fileName);
        uploadFileRequest.setTempFilePath(path + fileName);
        uploadFileRequest.setHeaderMap(Map.of("cache-control", "no-cache"));
        uploadFileRequest.setBizId(StringUtil.newId());
        uploadFileRequest.setPublicRead(true);
        uploadFileService.uploadFile(uploadFileRequest);
    }
}
