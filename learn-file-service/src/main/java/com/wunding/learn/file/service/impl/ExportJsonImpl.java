package com.wunding.learn.file.service.impl;

import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.file.config.SysConfig;
import com.wunding.learn.file.model.UploadFileRequest;
import com.wunding.learn.file.service.ExportJsonService;
import com.wunding.learn.file.service.UploadFileService;
import com.wunding.learn.file.util.FileUtil;
import jakarta.annotation.Resource;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("exportJsonService")
public class ExportJsonImpl implements ExportJsonService {

    public static String SEPARATOR = "/";

    @Resource
    private SysConfig sysConfig;

    @Resource
    private UploadFileService uploadFileService;

    @Override
    public void exportJsonDto(Object object) {
        String filePath = sysConfig.queryTempPath() + SEPARATOR;
        // 外网env.json
        String fileName = "env.json";
        // 内网env-inner.json
        String innerFileName = "env-inner.json";
        String path = sysConfig.getPhysicalPath(filePath);
        String intranetEndPoint = sysConfig.getIntranetEndPoint();
        String staticBaseUrl = sysConfig.getStaticBaseUrl();
        try {
            FileUtil.mkdir(path);
            OutputStreamWriter osw = new OutputStreamWriter(new FileOutputStream(path + fileName),
                StandardCharsets.UTF_8);
            OutputStreamWriter innerOsw = new OutputStreamWriter(new FileOutputStream(path + innerFileName),
                StandardCharsets.UTF_8);
            // 替换objToJson内容的外网地址成为内网域名, 用于外部访问

            String objToJson = JsonUtil.objToJson(object);

            // 如果是外网env写入到外网到env.json
            if (objToJson.contains(staticBaseUrl)) {
                assert objToJson != null;
                osw.write(objToJson);
                osw.flush();
                osw.close();
                // 再替换里面的域名成内网地址再写入到内网env-inner.json
                String intranetJson = objToJson.replace(staticBaseUrl, intranetEndPoint);
                innerOsw.write(intranetJson);
                innerOsw.flush();
                innerOsw.close();
            } else {
                // 如果是在内网环境进行了首页配置的修改
                innerOsw.write(objToJson);
                innerOsw.flush();
                innerOsw.close();
                // 再替换里面的域名成内网地址再写入到外网env.json
                String envJson = objToJson.replace(intranetEndPoint, staticBaseUrl);
                osw.write(envJson);
                osw.flush();
                osw.close();
            }
        } catch (IOException e) {
            log.error("发生异常", e);
        }
        log.info("输出文件地址：{}", filePath + fileName);
        log.info("json:{}", JsonUtil.objToJson(object));
        UploadFileRequest uploadFileRequest = new UploadFileRequest();
        uploadFileRequest.setFileName(UserThreadContext.getTenantId() + SEPARATOR + fileName);
        uploadFileRequest.setTempFilePath(path + fileName);
        uploadFileRequest.setHeaderMap(Map.of("cache-control", "no-cache"));
        uploadFileRequest.setBizId(StringUtil.newId());
        uploadFileRequest.setPublicRead(true);
        uploadFileService.uploadFile(uploadFileRequest);
        UploadFileRequest uploadInnerFileRequest = new UploadFileRequest();
        uploadInnerFileRequest.setFileName(UserThreadContext.getTenantId() + SEPARATOR + innerFileName);
        uploadInnerFileRequest.setTempFilePath(path + fileName);
        uploadInnerFileRequest.setHeaderMap(Map.of("cache-control", "no-cache"));
        uploadInnerFileRequest.setBizId(StringUtil.newId());
        uploadInnerFileRequest.setPublicRead(true);
        uploadFileService.uploadFile(uploadInnerFileRequest);
    }
}
