package com.wunding.learn.file.model;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/2/4 16:15
 */
@Data
public class UploadFileRequest implements Serializable {

    private static final long serialVersionUID = -8436459104570916861L;
    /**
     * 存储的文件名称，需要保持唯一性. 上传后生成的正式目录文件的相对路径(即不带location的正式目录路径)
     */
    private String fileName;

    /**
     * 文件的临时路径，待上传存储的文件
     */
    private String tempFilePath;

    /**
     * 是否copy文件夹
     */
    private boolean copyFolderFlag;

    /**
     * 文件夹源目录
     */
    private String folderSrcPath;

    /**
     * 文件夹目标目录
     */
    private String folderTargetPath;

    /**
     * 是否异步上传 只针对对象存储有用
     */
    private boolean asyncUpload = false;

    /**
     * 对象存储头信息
     */
    private Map<String, String> headerMap;

    /**
     * 是否删除临时文件
     */
    private boolean delTempFile = false;

    /**
     * 对应的业务ID,需要透传,不需要更新状态的传-1，有可能是sys_file中的id或categoryId，也可能是sys_export_record中的ID
     */
    private String bizId;

    /**
     * 搭配文件夹上传时使用的，默认策略的文件列表
     */
    private List<String> defaultStrategyFileList;

    /**
     * 该文件是否公有读
     */
    private boolean publicRead = false;
}
