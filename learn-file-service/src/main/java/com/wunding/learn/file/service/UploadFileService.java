package com.wunding.learn.file.service;

import com.wunding.learn.file.api.constant.FileBizType;
import com.wunding.learn.file.api.constant.ImageBizType;
import com.wunding.learn.file.model.CloudFile;
import com.wunding.learn.file.model.CloudFileChunk;
import com.wunding.learn.file.model.UploadFileRequest;
import com.wunding.learn.file.model.UploadFileResponse;
import java.io.File;
import java.util.Collection;

/**
 * 文件上传
 *
 * <AUTHOR>
 * @since 2021/2/4 15:39
 */
public interface UploadFileService {

    /**
     * 上传文件
     *
     * @param uploadFileRequest
     * @return
     */
    UploadFileResponse uploadFile(UploadFileRequest uploadFileRequest);


    /**
     * 上传文件并解压缩只针对压缩包的上传
     */
    UploadFileResponse uploadFileAndUnzip(UploadFileRequest uploadFileRequest);

    //todo 增加copy文件方法

    /**
     * 复制文件
     *
     * @param source 原文件path
     * @param target 目标文件path
     * @return 复制文件后的目标信息
     */
    UploadFileResponse copyFile(String source, String target);

    /**
     * 复制文件
     *
     * @param source      原文件path
     * @param fileBizType 目标文件类型
     * @return 复制文件后的目标信息
     */
    UploadFileResponse copyFile(String source, FileBizType fileBizType);

    /**
     * 复制文件
     *
     * @param source       原文件path
     * @param imageBizType 目标文件类型
     * @return 复制文件后的目标信息
     */
    UploadFileResponse copyFile(String source, ImageBizType imageBizType);

    /**
     * 复制目录
     *
     * @param source 原目录path
     * @param target 目标目录path
     */
    UploadFileResponse copyDir(String source, String target);

    /**
     * 上传目录
     *
     * @param source 本地目录 绝对路径
     * @param target 上标目录
     * @return 目录主页
     */
    UploadFileResponse uploadDir(String source, String target, String bizId);

    /**
     * 查找文件
     *
     * @param uploadFileRequest 通过fileName来进行查找
     * @return
     */
    UploadFileResponse findFile(UploadFileRequest uploadFileRequest);

    /**
     * 删除文件
     *
     * @param filePath 文件的相对路径
     */
    void deleteFile(String filePath);

    /**
     * 获取文件存储方式
     *
     * @return
     */
    Integer getStorageType();

    /**
     * 下载文件
     *
     * @param uri
     * @return
     */
    File downloadFile(String uri);

    /**
     * 压缩文件
     *
     * @param filePaths   要压缩的文件列表
     * @param zipFileName 压缩包名称
     * @return 压缩后的文件信息
     */
    String compressorFiles(Collection<String> filePaths, String zipFileName, boolean isImage);

    /**
     * 合并文件
     *
     * @param cloudFileChunkList 合并文件的路径
     * @param cloudFile          文件信息
     */
    void composeFiles(Collection<CloudFileChunk> cloudFileChunkList, CloudFile cloudFile);

    /**
     * 获取url
     *
     * @param path       文件path
     * @param isDownload 是否是下载
     * @param fileName   文件名
     * @return 文件URL
     */
    String getUrl(String path, boolean isDownload, String fileName);

    /**
     * 根据path 判断文件存不存在
     *
     * @param path
     * @return
     */
    Boolean checkFileIsExists(String path);


    /**
     * 删除指定时间以前的临时文件
     *
     * @param deleteTime 指定天数
     */
    void deleteTempFile(Integer deleteTime);

    /**
     * 上传分片文件
     *
     * @param index     分片序号
     * @param file      分片文件
     * @param cloudFile 主文件信息
     * @return eTag
     */
    String uploadPartFile(Integer index, File file, CloudFile cloudFile);
}
