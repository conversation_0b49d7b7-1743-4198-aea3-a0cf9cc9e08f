package com.wunding.learn.file.service.impl;

import com.wunding.learn.common.constant.dynamicDataSource.DynamicDataSourceConstant;
import com.wunding.learn.common.constant.tenant.TenantRedisKeyConstant;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.mq.event.I18nConfigEvent;
import com.wunding.learn.common.mq.service.MqProducer;
import com.wunding.learn.common.util.http.JdkHttpUtils;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.file.config.SysConfig;
import com.wunding.learn.file.config.WeblateConfig;
import com.wunding.learn.file.service.UploadFileService;
import com.wunding.learn.file.service.WeblateService;
import jakarta.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.model.ObjectCannedACL;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;

/**
 * <AUTHOR>
 */
@Service("weblateService")
@Slf4j
public class WeblateServiceImpl implements WeblateService {

    @Resource
    private SysConfig sysConfig;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private MqProducer mqProducer;

    @Resource
    private UploadFileService uploadFileService;

    @Resource
    private WeblateConfig weblateConfig;

    @Override
    public void callback() {

        String projectName = weblateConfig.getProject();

        Map<String, String> regionLanguageMap = new HashMap<>();

        regionLanguageMap.put("en", "_en");
        regionLanguageMap.put("zh_Hans", "_zh");
        regionLanguageMap.put("zh_Hans_SG", "");
        regionLanguageMap.put("zh_Hant", "_zh_TW");

        String token = "Token " + weblateConfig.getToken();

        List<String> fileNames = new ArrayList<>();
        fileNames.add("enum_messages");
        fileNames.add("error_messages");
        fileNames.add("export_message");
        fileNames.add("import_messages");
        fileNames.add("messages");
        fileNames.add("templateFileName_message");
        fileNames.add("validation_messages");

        List<String> languageCodeList = getLanguageCode(projectName, token);

        Map<String, String> dataSources = redisTemplate.<String, String>opsForHash()
            .entries(TenantRedisKeyConstant.DB_KEY);
        dataSources.keySet().forEach(key -> {
            UserThreadContext.setTenantId(key.replace(DynamicDataSourceConstant.DB_LOOK_UP_KEY_PREFIX, ""));
            for (String fileName : fileNames) {
                languageCodeList.forEach(languageCode -> {
                    InputStream inputStream = downloadMessageFile(projectName, fileName, languageCode, token);
                    String langCode = regionLanguageMap.get(languageCode);
                    if (langCode == null) {
                        return;
                    }
                    String objectName = "i18n/" + projectName + "/" + fileName + langCode + ".properties";
                    try {
                        byte[] bytes = inputStream.readAllBytes();
                        PutObjectRequest putRequest = PutObjectRequest.builder()
                            .bucket(sysConfig.getBucketName())
                            .key(objectName)
                            .cacheControl("no-cache")
                            .acl(ObjectCannedACL.PUBLIC_READ) // 设置公有读
                            .contentLength((long) bytes.length)
                            .build();

                        // 通过 RequestBody.fromBytes 上传字节数组
                        SysConfig.getS3Client().putObject(putRequest, RequestBody.fromBytes(bytes));
                    } catch (Exception e) {
                        log.error("上传国际化配置文件到对象存储失败", e);
                    }

                    String url = uploadFileService.getUrl(objectName, false, null);
                    mqProducer.sendMsg(new I18nConfigEvent(fileName, projectName, langCode, url));
                });
            }
        });
    }

    private List<String> getLanguageCode(String projectName, String token) {
        List<String> list = new ArrayList<>();
        String json = JdkHttpUtils.doGet(weblateConfig.getBaseUrl() + "/api/projects/" + projectName + "/languages/",
            null,
            Map.of("Authorization", token));
        @SuppressWarnings("squid:S3740")
        List<Map> maps = JsonUtil.json2List(json, Map.class);
        maps.forEach(map -> list.add((String) map.get("code")));
        return list;
    }

    private InputStream downloadMessageFile(String projectName, String filename, String languageName,
        String authorization) {
        String url =
            weblateConfig.getBaseUrl() + "/api/translations/" + projectName + "/" + filename.toLowerCase() + "/"
                + languageName + "/file/";
        String content = JdkHttpUtils.doGet(url, null,
            Map.of("Authorization", authorization));
        return new ByteArrayInputStream(content.getBytes(StandardCharsets.UTF_8));
    }
}
