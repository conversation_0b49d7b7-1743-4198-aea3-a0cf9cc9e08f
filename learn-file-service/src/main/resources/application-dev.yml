# 应用服务 WEB 访问端口
server:
  port: 28003
# 应用名称
spring:

  servlet:
    multipart:
      max-file-size: 2048MB
      max-request-size: 2048MB

  # 数据库设置
  datasource:
    driverClassName: com.mysql.cj.jdbc.Driver
    url: ****************************************************************************************
    username: wdxuexi
    password: learnTest


  #redis
  # Redis服务器地址
  data:
    redis:
      #host: 127.0.0.1
      host: ************
      # Redis服务器连接端口
      #port: 6379
      port: 30079
      # Redis数据库索引（默认为0）
      database: 0
      cache.database: 12
      # Redis服务器连接密码（默认为空）
      #    password: 123456
      password: M0eHdhs9kk4VKLjRAb7J41
      # 连接超时时间（毫秒）
      timeout: 10000

  #rabbitmq 配置
  rabbitmq:
    host: ************
    port: 30672
    virtual-host: /dev
    username: guest
    password: guest


management:
  tracing:
    sampling:
      probability: 1.0
    enabled: true
    propagation:
      type: B3
  otlp:
    metrics:
      export:
        enabled: true
  zipkin:
    tracing:
      endpoint: http://************:30917

opentelemetry:
  traces:
    exporter: otlp
  service:
    name: ${spring.application.name}
  jaeger:
    endpoint: http://************:30915
    protocol: grpc
  instrumentation:
    feign:
      enabled: true
    spring-webmvc:
      enabled: true

xxl:
  job:
    admin:
      addresses: http://************/xxl-job-admin
    executor:
      appName: ${spring.application.name}
      ip:
      port: 9999
      logPath: /data/applogs/xxl-job/jobhandler
      logRetentionDays: -1
    accessToken: vopeqn943epfaspdf

learn:
  service:
    learn-user-service: "http://************:28001"
    ai-maxkb-service: "http://************:8283/api"


#seata:
#  client:
#    undo:
#      log-serialization: kryo
#  config:
#    type: file
#  application-id: ${spring.application.name}
#  enable-auto-data-source-proxy: true
#  registry:
#    type: file
#  service:
#    grouplist:
#      default: ************:8091
#    vgroup-mapping:
#      springboot-seata-group: default
#  # seata 事务组编号 用于TC集群名
#  tx-service-group: springboot-seata-group

app:
  signKey: bladexisapowerfulmicroservicearchitectureupgradedandoptimizedfromacommercialproject
  single:
    - web
    - api111
  # 存储方式 1 对象存储 2 本地存储
  storageType: 1
  intranetEndPoint: https://oss-test.wdxuexi.com
  endPoint: https://oss-test.wdxuexi.com
  accessKey: Rh2BL2nK9RWjqiPX
  secretKey: Xu0uU8xiDtr7bnmjpMVa2N7ZxPgGuLEn
  bucketName: dev
  region: shenzhen
  location: D:/
  root: dev
  ## 做课模板path，必须要保证在upload目录存在
  zuoKeTemplatePath: /template/zuoke
  transCodingUrl: http://wdweike.com/wps2html/uploadServlet
  ffmpegPath: C:/soft/ffmpeg-n4.4.1-2-gcc33e73618-win64-gpl-4.4/bin/ffmpeg.exe
  staticBaseUrl: https://oss-test.wdxuexi.com
  imgProxyUrl: https://imgproxy.oss-test.wdxuexi.com
  # 转码消息者数量
  transConcurrency: 10
  officeTransType: 0
  pathMode: 1
  needContentType: 1

  pdf2html:
    threadNum: 8
    pageTimeout: 60
    officeTimeout: 600
    home: D:\ffmpeg\bin
    bin: D:\pdf2htmlEX\pdf2htmlEX.exe
    #    dataDir: C:\\soft\\pdf2htmlEX\\data
    dataDir: D:\pdf2htmlEX\pdf2htmlEX_data



  weblate:
    baseUrl: http://************:8082
    token: wlu_xeXXHPsT9BDuFMF7hIBYGTB8gVmnr0REBpWK
    project: test


debug: false
############# springDoc配置 #############
springdoc:
  version: '@springdoc.version@'
  api-docs:
    groups:
      enabled: true
    enabled: true
  swagger-ui:
    operationsSorter: function
    tagsSorter: alpha
    docExpansion: none
    path: /swagger-ui.html
  group-configs:
    - group: rest
      packages-to-scan: com.wunding.learn.file.rest
    - group: feign
      packages-to-scan: com.wunding.learn.file.feign
  # 不配置下面 get请求为对象会变为 json
  default-flat-param-object: true
  disable-i18n: true

############# baidu API配置 #############
baidu:
  tokenUrl: https://aip.baidubce.com/oauth/2.0/token
  createUrl: https://aip.baidubce.com/rpc/2.0/aasr/v1/create
  queryUrl: https://aip.baidubce.com/rpc/2.0/aasr/v1/query
  accessKey: c5ZLGbjHz1QUZSnAoKUYx2p0
  secretKey: omt1SGcOafO7CWc01zf5UhykosZpFmUz

dify:
  baseUrl: http://************:8283
  email: <EMAIL>
  password: wunding1996*
  templateId: 4828c598-7d98-433a-bffa-4b3c5180da65