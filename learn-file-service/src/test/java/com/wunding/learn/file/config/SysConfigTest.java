package com.wunding.learn.file.config;

import static org.junit.jupiter.api.Assertions.assertTrue;

import com.alibaba.druid.spring.boot3.autoconfigure.DruidDataSourceAutoConfigure;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.file.service.IImagesService;
import com.wunding.learn.file.util.UrlHelper;
import com.wunding.learn.user.api.service.ParaFeign;
import jakarta.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.file.Paths;
import java.time.Duration;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.TestPropertySource;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.core.sync.ResponseTransformer;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3ClientBuilder;
import software.amazon.awssdk.services.s3.S3Configuration;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.HeadObjectRequest;
import software.amazon.awssdk.services.s3.model.HeadObjectResponse;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.S3Presigner.Builder;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;

//@SpringBootApplication(exclude = {
//    // For AWS S3:
//    InitBucketMapConfig.class, // For newer Spring Cloud AWS
//    // org.springframework.cloud.aws.autoconfigure.s3.S3AutoConfiguration.class // For older Spring Cloud AWS
//    // Add other auto-configuration classes you want to exclude
//})
@SpringBootTest
@SpringBootApplication(
    // Exclude specific auto-configurations for the entire application context
    // This affects both production runs and tests by default
    exclude = {
        DataSourceAutoConfiguration.class,
        DruidDataSourceAutoConfigure.class,
        MybatisAutoConfiguration.class,
        RabbitAutoConfiguration.class,
    }
)
@EnableConfigurationProperties(SysConfig.class)
@TestPropertySource(properties = {
    "app.pathMode=0",
    "app.storageType=1",
    "app.root=dev",
    "app.region=",
    "app.endPoint=https://oss-cn-shenzhen.aliyuncs.com",
    "app.accessKey=LTAI5tMkB6SqkRQfrSXfdEmZ",
    "app.secretKey=******************************",
    "app.bucketName=wd-saas-dev-bucket",
    //没有问题的
    "app.staticBaseUrl=https://saasdev.oss.wdxuexi.com"
    //有问题的
//    "app.staticBaseUrl=https://saassit.oss.wdxuexi.com"
})
//@TestPropertySource(properties = {
//    "app.pathMode=1",
//    "app.root=dev",
//    "app.region=",
//    "app.storageType=1",
//    "app.endPoint=http://192.168.0.72:9000",
//    "app.accessKey=Ie09PmcW7nS2Ff8Qk6vS",
//    "app.secretKey=rYsqF1omxCRp65Uya3aw6NoweQuzJhCucybeqECH",
//    "app.bucketName=wiki",
//    "app.staticBaseUrl=http://192.168.0.72:9000",
//})
class SysConfigTest {

    @Resource
    private SysConfig sysConfig;

    @MockBean
    IImagesService imagesService;
    @MockBean
    private UrlHelper urlHelper;
    @MockBean
    private ParaFeign paraFeign;
    // 忽略掉依赖redis的无用bean
    @MockBean
    private InitBucketMapConfig initBucketMapConfig;

//    @Test
//    void getS3Client() {
//
//        TenantBucketInfo tenantBucketInfo = new TenantBucketInfo();
//        tenantBucketInfo.setAccessKey(sysConfig.getAccessKey());
//        tenantBucketInfo.setRegion(sysConfig.getRegion());
//        tenantBucketInfo.setType(sysConfig.getType());
//        tenantBucketInfo.setEndPoint(sysConfig.getEndPoint());
//        tenantBucketInfo.setStaticBaseUrl(sysConfig.getStaticBaseUrl());
//        tenantBucketInfo.setSecretKey(sysConfig.getSecretKey());
//        tenantBucketInfo.setRootPath("default");
//

    /// /        sysConfig.addTenantBucket("test", "dev");
//        sysConfig.addTenantBucketInfo("test", tenantBucketInfo);
//
//        UserThreadContext.setTenantId("test");
//        S3Client client = sysConfig.getS3Client();
//        System.out.println("Check Bucket '" + sysConfig.getBucketName() + "' exists and S3 connection is healthy.");
//        HeadObjectRequest headObjectRequest = HeadObjectRequest.builder()
//            .bucket(sysConfig.getBucketName())
//            .key("learning/env.json")
//            .build();
//        HeadObjectResponse response = SysConfig.getS3Client().headObject(headObjectRequest);
//    }
    @Test
    void s3PresignGetObjectTest() throws IOException, URISyntaxException {
        String bucketEndPoint = sysConfig.getEndPoint();
        String staticBaseUrl = sysConfig.getStaticBaseUrl();
        String bucketAccessKey = sysConfig.getAccessKey();
        String bucketSecretKey = sysConfig.getSecretKey();
        String bucketRegion = StringUtils.isNotBlank(sysConfig.getRegion()) ? sysConfig.getRegion() : "us-east-1";
        String rootPath = sysConfig.getRoot();

        // 创建认证凭证
        StaticCredentialsProvider credentialsProvider = StaticCredentialsProvider.create(
            AwsBasicCredentials.create(bucketAccessKey, bucketSecretKey));
        // 创建模式配置
        S3Configuration.Builder s3ConfigurationBuilder = S3Configuration.builder().chunkedEncodingEnabled(false);

        // S3Client 构建(用于上传下载等操作)
        S3ClientBuilder s3Builder = S3Client.builder()
            .region(Region.of(bucketRegion))
            .credentialsProvider(credentialsProvider)
            .endpointOverride(URI.create(bucketEndPoint));

        // S3Presigner 构建(用于生成签名地址)
        Builder s3Presignerbuilder = S3Presigner.builder()
            .region(Region.of(bucketRegion))
            .credentialsProvider(credentialsProvider)
            .endpointOverride(URI.create(bucketEndPoint));

        // 是否path模式
        if (GeneralJudgeEnum.CONFIRM.getValue().equals(sysConfig.getPathMode())) {
            s3ConfigurationBuilder.pathStyleAccessEnabled(true);
        }
        S3Configuration s3Configuration = s3ConfigurationBuilder.build();
        s3Builder.serviceConfiguration(s3Configuration);
        s3Presignerbuilder.serviceConfiguration(s3Configuration);

        S3Client s3Client = s3Builder.build();
        S3Presigner s3Presigner = s3Presignerbuilder.build();

        String keyName = "aaaaaaa";
        // 添加一个文件
        PutObjectRequest putRequest = PutObjectRequest.builder()
            .bucket(sysConfig.getBucketName())
            .key(keyName)
            .build();

        File tmpFile = File.createTempFile("myprefix", ".mysuffix");
        s3Client.putObject(putRequest, RequestBody.fromFile(tmpFile));

        // 检查文件
        HeadObjectRequest headObjectRequest = HeadObjectRequest.builder()
            .bucket(sysConfig.getBucketName())
            .key(keyName)
            .build();

        HeadObjectResponse response = s3Client.headObject(headObjectRequest);

        //获取url
        // 过期时间默认2小时
        long expiredSeconds = 7200L;
        // 设置预签名请求
        GetObjectRequest.Builder getObjectRequestBuilder = GetObjectRequest.builder()
            .bucket(sysConfig.getBucketName()).key(keyName);
        GetObjectPresignRequest presignRequest = GetObjectPresignRequest.builder()
            .signatureDuration(Duration.ofSeconds(expiredSeconds))
            .getObjectRequest(getObjectRequestBuilder.build())
            .build();

        String presignedUrl = s3Presigner.presignGetObject(presignRequest).url().toString();

        assertTrue(checkUrlAccessibility(presignedUrl), "presignedUrl:" + presignedUrl + " failed");
        String presignedCDNUrl = replaceDomainWithTargetUrl(presignedUrl, staticBaseUrl);
        assertTrue(checkUrlAccessibility(presignedCDNUrl), "presignedCDNUrl:" + presignedCDNUrl + " failed");

    }

    @Test
    void s3ClientDownloadFileTest() {
        String bucketEndPoint = sysConfig.getEndPoint();
        String bucketAccessKey = sysConfig.getAccessKey();
        String bucketSecretKey = sysConfig.getSecretKey();
        String bucketRegion = StringUtils.isNotBlank(sysConfig.getRegion()) ? sysConfig.getRegion() : "us-east-1";
        String bucketName = sysConfig.getBucketName();
        // 创建认证凭证
        StaticCredentialsProvider credentialsProvider = StaticCredentialsProvider.create(
            AwsBasicCredentials.create(bucketAccessKey, bucketSecretKey));
        // 创建模式配置
        S3Configuration.Builder s3ConfigurationBuilder = S3Configuration.builder().chunkedEncodingEnabled(false);

        // S3Client 构建(用于上传下载等操作)
        S3ClientBuilder s3Builder = S3Client.builder()
            .region(Region.of(bucketRegion))
            .credentialsProvider(credentialsProvider)
            .endpointOverride(URI.create(bucketEndPoint));

        if (GeneralJudgeEnum.CONFIRM.getValue().equals(sysConfig.getPathMode())) {
            s3ConfigurationBuilder.pathStyleAccessEnabled(true);
        }
        S3Configuration s3Configuration = s3ConfigurationBuilder.build();
        s3Builder.serviceConfiguration(s3Configuration);

        try (S3Client s3Client = s3Builder.build()) {
            String objectKey = "default/file/menuIcon/app.png";
            String downloadFilePath = "D:/Downloads/app.png";
            // 构建 GetObject 请求
            GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                .bucket(bucketName)
                .key(objectKey)
                .build();

            // 下载文件到本地
            s3Client.getObject(getObjectRequest, ResponseTransformer.toFile(Paths.get(downloadFilePath)));
            System.out.println("文件下载成功：" + downloadFilePath);
        } catch (Exception e) {
            System.err.println("文件下载失败: " + e.getMessage());
        }
    }

    /**
     * Checks if a given URL is accessible by making an HTTP GET request.
     *
     * @param urlString The URL to check.
     * @return true if the URL returns a success status code (2xx), false otherwise.
     */
    public static boolean checkUrlAccessibility(String urlString) {
        HttpURLConnection connection = null;
        try {
            URI uri = new URI(urlString);
            connection = (HttpURLConnection) uri.toURL().openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000); // 5 seconds
            connection.setReadTimeout(5000);    // 5 seconds

            int responseCode = connection.getResponseCode();
            System.out.println("HTTP Response Code for URL: " + responseCode);

            // A 2xx status code indicates success
            return responseCode >= 200 && responseCode < 300;

        } catch (Exception e) {
            System.err.println("Error checking URL accessibility: " + e.getMessage());
            return false;
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }

    /**
     * Replaces the scheme, host, and port of the originalUrlString with those from the targetUrlString, while retaining
     * the path, query, and fragment from the originalUrlString.
     *
     * @param originalUrlString The original URL whose path, query, and fragment are to be kept.
     * @param targetUrlString   The URL from which the new scheme, host, and port will be taken.
     * @return A new URL string with the combined parts.
     * @throws URISyntaxException If either input string is not a valid URI.
     */
    public static String replaceDomainWithTargetUrl(String originalUrlString, String targetUrlString)
        throws URISyntaxException {

        // 1. Parse the original URL to get its path, query, and fragment.
        URI originalUri = new URI(originalUrlString); // Use new URI() to handle potential URISyntaxException

        // 2. Parse the target URL to get its scheme, host, and port.
        URI targetUri = new URI(targetUrlString);

        String newScheme = targetUri.getScheme();
        String newUserInfo = originalUri.getUserInfo(); // Retain user info from original if any, or target's if original is null
        // More robustly, decide based on specific needs. Here, prefer original.
        // If target has user info and original doesn't, this might be an edge case to define.
        if (newUserInfo == null && targetUri.getUserInfo() != null) {
            // This is a design choice: if original has no userinfo, but target does, do we use target's?
            // For simply replacing domain, usually we don't introduce new userinfo.
            // But if target URL implies a specific user context for the new domain, it might be relevant.
            // For now, let's stick to userinfo from original if present, otherwise null.
        }

        String newHost = targetUri.getHost();
        int newPort = targetUri.getPort(); // This will be -1 if not specified, which is correct for URI constructor

        String originalPath = originalUri.getPath();
        String originalQuery = originalUri.getQuery();
        String originalFragment = originalUri.getFragment();

        // 3. Construct the new URI with:
        //    - Scheme, Host, Port from targetUri
        //    - UserInfo, Path, Query, Fragment from originalUri
        URI resultUri = new URI(
            newScheme,
            newUserInfo, // Typically, if originalUrl had user info, you'd keep it.
            newHost,
            newPort,
            originalPath,
            originalQuery,
            originalFragment
        );

        return resultUri.toString();
    }

}