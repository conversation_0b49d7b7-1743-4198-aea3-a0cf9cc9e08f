package com.wunding.learn.file;

/**
 * <AUTHOR>
 * @date 2022/3/11
 */
public class Test {

    public static void main(String[] args) {
        //        ThreadAbc abc = new ThreadAbc();
        ThreadMain main = new ThreadMain();
        main.start();
    }
}

class ThreadMain extends Thread {

    private ThreadAbc abc = new ThreadAbc();

    //    public ThreadMain(ThreadAbc abcd) {
    //        this.abc = abcd;
    //    }
    @Override
    public void run() {
        System.err.println("main id:" + Thread.currentThread().getId());
        abc.start();
        abc.seeThreadId(
            new Call() {
                @Override
                public void call() {
                    System.err.println("call id:" + Thread.currentThread().getId());
                }
            });
        new Thread(
            new Runnable() {
                @Override
                public void run() {
                    System.err.println("======" + Thread.currentThread().getId());
                    p();
                }
            })
            .start();
    }

    public void p() {
        System.err.println("xxxxxxxxxxx" + Thread.currentThread().getId());
    }
}

class ThreadAbc extends Thread {

    @Override
    public void run() {
        System.err.println("abc id:" + Thread.currentThread().getId());
    }

    public void seeThreadId(Call call) {
        call.call();
        System.err.println("see id:" + Thread.currentThread().getId());
    }
}

interface Call {

    void call();
}
