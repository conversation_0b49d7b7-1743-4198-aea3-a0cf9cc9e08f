package com.wunding.learn.file.api.constant;

import com.wunding.learn.common.annotation.EnumI18nProperty;
import com.wunding.learn.common.enums.file.FileType;
import com.wunding.learn.common.i18n.util.EnumI18n;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @ClassName ExportFileNameEnum
 * @Description 导出 文件名 枚举类
 * @Date 2022/9/30 14:35
 * @Version 1.0
 */
public enum ExportFileNameEnum implements EnumI18n {

    /**
     * 课程模块
     */
    Course("课程列表"),

    CourseCategoryExport("课程分类列表"),

    LecturerCourseDetail("讲师课程明细列表"),

    LecturerCourseWareDetail("讲师课件明细列表"),

    CourseStudyDetail("课程学习明细列表"),

    CoursewareStudyDetail("课件学习明细列表"),

    CourseComment("课程评论列表"),

    SharedLibrary("课程共享库列表"),

    CoursewarePackage("学员上传课件列表"),

    MergeCourse("合并课件到课程列表"),

    CourseWareLearn("学员课件学习明细列表"),

    CourseLearn("课程学习明细列表"),

    CourseCategory("课程分类管理列表"),

    CourseTagManage("课程标签管理列表"),

    CourseSysCategory("课程标签分类管理列表"),

    CourseTagSta("课程标签统计列表"),

    CourseStudyStatistics("课程学习统计-按部门"),

    CourseStudyStatisticsUser("人员明细"),

    CourseNote("课程笔记"),

    /**
     * 审核历史记录列表
     */
    FlowAuditRecords("审核历史记录列表"),

    CourseStudyCourseLearnUser("学习统计-按课程-人员明细"),

    /**
     * 外部课程资源管理列表
     */
    CourseWithout("外部课程列表"),

    /**
     * 考试模块
     */
    Exam("考试管理列表"),

    ExamCorrect("改卷管理列表"),

    ExamCorrectRecord("答题记录列表"),

    SchemaList("组卷方案列表"),

    Exercise("练习管理列表"),

    ExamEmployeeResultsDetail("学员考试成绩明细列表"),

    ExamAnswerOfStatisticalOrg("考试答题统计按部门统计"),

    ExamAnswerOfStatisticalPost("考试答题统计按岗位统计"),

    ExamAnswerRecord("考试成绩明细"),

    ExamAnswerRecordDetail("考试答题记录明细"),

    ExamAnalysisQuestion("答题分析"),

    ExamQuestion("考题题目明细"),

    ExamCompetition("考试竞赛管理列表"),

    ExamCompetitionUserScoreRank("得分排名"),

    ExamCompetitionUserAnswerRank("答题排名"),

    ExamCompetitionSessionUser("场次列表"),

    ExamCompetitionAnswerRecord("答题列表"),

    ExamCompetitionSessionStat("场次明细"),

    CompetitionAnswerRecordStat("答题统计"),

    ExamStatistic("按考试情况统计列表"),

    ExamQuestionUserList("考试答题用户清单"),

    ExamJoinUserByBizRecord("考试情况统计-参加用户-按照"),

    ExamNotJoinUserByBizRecord("考试情况统计-未参加用户-按照"),

    ExamPostUserByBizRecord("考试情况统计-交卷用户-按照"),

    ExamNotPassUserByBizRecord("考试情况统计-未通过用户-按照"),

    ExamPassUserByBizRecord("考试情况统计-通过用户-按照"),

    ExamLimitUserByBizRecord("考试情况统计-下发用户-按照"),

    /**
     * 案例库模块
     */
    ExampleLib("案例库管理列表"),

    ExpertLib("专家库管理列表"),

    ExampleBusiness("案例条线管理列表"),

    ExampleComment("案例评论管理列表"),

    ExampleCategory("案例分类管理列表"),

    ExampleAuditCategory("案例审核分类管理列表"),

    /**
     * 直播模块
     */
    Live("直播管理列表"),

    LiveStatic("直播统计列表"),

    LiveVod("直播回放列表"),

    /**
     * 话题模块
     */
    Post("话题管理帖子列表"),

    PostBanUser("禁言名单管理列表"),

    PostSection("话题板块管理列表"),

    PostSectionExpert("话题板块专家管理列表"),

    PostCountComment("回帖统计"),

    /**
     * 闯关模块
     */
    Emigrated("闯关游戏管理列表"),

    Checkpoint("闯关关卡列表"),

    CheckpointTask("闯关任务列表"),

    StylesTemplate("样式管理列表"),

    Notice("公告管理列表"),

    Team("团队管理列表"),

    Statistical("闯关统计列表"),

    TaskStatistical("查看明细列表"),

    /**
     * 新闻资讯模块
     */
    Info("新闻资讯列表"),

    Category("分类管理列表"),

    Comment("资讯评论管理列表"),

    /**
     * 调研模块
     */
    Survey("调研列表"),

    SurveyAnalysis("调研分析详情"),

    SurveyRecordDetail("全部调研明细"),

    /**
     * 项目模块
     */
    StudyProject("学习项目管理列表"),

    ProjectFixedDate("日期项目管理列表"),

    ProjectFixedCycle("周期项目管理列表"),

    QuickProject("快速培训管理列表"),

    CourseTaskProject("课程任务列表"),

    FormTemplate("辅导模板管理列表"),

    Position("岗位发展列表"),

    Classroom("教室列表"),

    TrainingPlan("培训计划管理列表"),

    ProjectMentor("导师管理列表"),

    ProjectLecturer("讲师管理列表"),

    LecturerProjectStatistic("讲师统计列表"),

    ProjectApplyUser("报名管理用户列表"),

    ProjectSignUser("签到管理用户列表"),

    ProjectPost("话题管理列表"),

    ProjectWork("作业管理列表"),

    EvalReplyUserList("评估人员列表"),

    ProjectEvaluation("评估统计分析结果"),

    PROJECT_EVALUATION_DETAIL("评估统计分析明细"),

    ProjectFormTemplate("辅导结果"),

    ProjectHomeWork("作业详情"),

    ProjectCompletion("结业情况"),

    ProjectStatisticTaskDetail("学习项目明细统计"),

    PROJECT_FIXED_CYCLE_STATISTIC_TASK_DETAIL("周期项目明细统计"),

    QuickProjectStatisticTaskDetail("快速培训明细统计"),

    CourseTaskProjectStatisticTaskDetail("课程任务明细统计"),

    FaceProjectStatisticTaskDetail("面授项目任务明细统计"),

    ProjectStatisticPersonRank("个人学习排名统计"),

    ProjectStatisticOrgCompleteUserDetailIsFinish("部门完成统计用户详情-已完成"),

    ProjectStatisticOrgCompleteUserDetailNotFinish("部门完成统计用户详情-未完成"),

    ProjectStatisticTeamCompleteUserDetailIsFinish("团队完成统计用户详情-已完成"),

    ProjectStatisticTeamCompleteUserDetailNotFinish("团队完成统计用户详情-未完成"),

    ProjectStatisticTeamLearnRank("团队学习排名统计"),

    ProjectStatisticOrgLearnRank("部门学习排名统计"),

    ProjectStatistic("培训班统计"),

    ProjectHoldStatistic("学习项目举办统计"),

    ProjectJoiningStatistic("学习项目参与统计"),

    LecturerTrainStatistic("讲师培训统计"),

    LecturerTrainStatisticByBiz("讲师培训统计按"),

    ProjectStatisticLecturer("培训班讲师统计"),

    ProjectOrgStatistic("培训班组织统计"),

    FormTemplateColumn("任务记录列表"),

    ProjectStatisticByType("按类型-项目统计"),

    ProjectStatisticByTypeUser("按类型-项目统计-"),

    ProjectStatisticJoinUser("项目统计-参加人员详情"),

    ProjectStatisticByProject("项目统计-按项目"),

    ProjectStatisticByOrg("项目统计-按部门"),

    ProjectStatisticByUser("项目统计-按人员"),

    TrainHomeWork("作业完成情况统计"),

    /**
     * 专题模块
     */
    SpecialFixedDate("专题管理列表"),


    SpecialCategory("专题分类管理列表"),

    SpecialLabel("专题标签管理列表"),

    SpecialCompletion("专题结业情况"),

    SpecialStatisticTaskDetail("专题明细统计"),
    /**
     * 讲师模块
     */
    Lecturer("讲师管理列表"),

    LecturerTrainExportDTO("讲师参与培训列表"),

    LecturerCategory("讲师分类设置列表"),

    LecturerLevel("讲师等级设置列表"),

    LecturerModifyRecord("讲师异动记录列表"),

    LecturerExaminationAudit("完课审核列表"),

    LecturerUpgradeConfig("规则配置_晋级规则列表"),

    LecturerDemotionConfig("规则配置_降级规则列表"),

    LecturerRemovalConfig("规则配置_出库规则列表"),

    LecturerExamination("授课课时信息明细列表"),

    LecturerExaminationAssess("授课评估信息明细列表"),

    LecturerTeachDetail("线下授课明细列表"),

    InsideLecturer("讲师统计(按部门)-讲师管理列表"),

    NotWorkday("非工作日列表"),

    /**
     * 认证模块
     */
    Certification("证书管理列表"),

    CertificationSetup("证书体系列表"),

    CertificationLevel("证书等级管理列表"),

    CertificationRule("发证规则列表"),

    CertificationRelate("持证明细列表"),

    CertificationRelateDept("部门持证明细列表"),

    CertificationHoldUser("应持证管理列表"),

    CertificationHoldUserStat("应持证统计管理列表"),

    CertificationDeptTarget("部门持证目标管理列表"),

    CerDeptTargetReport("部门持证目标统计"),

    ASSESS_TOOL_LIST("测评工具列表"),

    ASSESS_EVALUATOR_LIST("测评明细列表"),

    ASSESS_PROJECT_LIST("测评项目列表"),

    ASSESS_USER_LIST2("测评用户列表-导出方式2"),

    ASSESS_USER_LIST1("测评用户列表-导出方式1"),

    ASSESS_DEP_MANAGER_USER_LIST("部门测评人员管理列表"),

    ASSESS_PROJECT_ASSESS_DETAILS("测评项目测评明细列表"),

    ASSESS_QUESTION_DETAIL_LIST("测评题目明细列表"),

    /**
     * 共读模块
     */
    Reading("共读管理列表"),

    ReadingBooksManage("图书管理列表"),

    ReadingSign("打卡列表"),

    ReadingBookExperience("心得列表"),

    ReadingBookExperienceComment("心得评论管理列表"),

    ReadingBookExperienceStar("心得点赞管理列表"),

    ReadingReports("举报列表"),

    ReadingStatistics("图书统计列表"),

    UserRankReadingStatistics("学员共读排行统计"),

    TaskFinishReadingStatistics("任务完成统计"),

    /**
     * 评价模块
     */
    Appraise("评价列表"),
    AppraiseLecturer("讲师评价列表"),

    Meeting("任职资格答辩列表"),

    AppraiseReferee("评委列表"),

    MeetingReferee("评委列表"),

    AppraiseProvider("被评人列表"),

    MeetingProvider("答辩人列表"),

    AppraiseFileType("材料规则列表"),

    AppraiseProviderFile("材料管理列表"),

    AppraiseDetail("评分明细列表"),

    MeetingScoreDetail("评分统计列表"),

    AppraiseShowDetail("查看明细列表"),

    AppraiseHistory("打分历史列表"),
    /**
     * 招募模块
     */
    Recruiting("招募列表"),
    LecturerRecruiting("讲师招募列表"),

    RecruitingAssistant("协办人员列表"),

    RecruitingMaterial("宣传材料列表"),

    RecruitingMaterialRule("材料规则列表"),

    RecruitingAudit("招募审核列表"),

    RecruitingAuditSurveyDetail("招募审核调研明细列表"),
    /**
     * 资源库模块
     */
    CourseWareLib("课件库列表"),

    courseWareLibraryCategory("课件库分类"),

    TestPaperLib("试卷库列表"),

    TestPaperLibraryCategory("试卷库分类"),

    TestPaperQuestion("题目管理"),

    ExamLib("考题库列表"),

    ExamLibQuestion("题目管理"),

    ExamLibCategory("考题库分类"),

    ExerciseLib("练习题库列表"),

    ExerciseLibCategory("练习库分类"),

    SurveyLib("调研库列表"),

    SurveyLibCategory("调研库分类"),

    MaterialLib("知识管理列表"),

    MaterialCategory("知识库标签"),

    KnowledgeBaseType("知识库分类"),

    ResearchField("讲师研究领域"),

    EvaluationLib("评估库列表"),

    EvaluationCategory("评估库分类列表"),

    FormTemplateManage("表单模板管理列表"),

    ExamOrgStatistic("按部门考试情况统计列表"),

    ProjectLecturerTeachDetail("讲师授课明细列表"),

    LecturerTeachStatisticDetail("讲师授课统计明细列表"),

    /**
     * 活动中心
     */
    Award("奖品管理列表"),

    AwardCategory("奖品分类管理"),

    GameLottery("积分中奖列表"),

    MailTemplate("邮箱模板列表"),

    PushManage("推送管理列表"),

    PushMessageManage("消息管理列表"),

    SignStatistics("签到统计"),

    SignStat("签到统计"),

    ProjectSignStat("项目签到统计"),

    TrainSignStat("培训签到统计"),

    SignList("签到列表"),

    ProjectSignList("项目签到列表"),

    AwardRedeemRecord("金币兑换"),

    AwardExchangeRecord("兑换记录"),

    /**
     * 订单列表
     */
    PaymentOrder("订单列表"),

    /**
     * 机构订单会员列表
     */
    PaymentOrgOrderMember("机构订单会员列表"),

    VoteManage("投票管理列表"),

    VoteContent("投票内容管理列表"),

    VoteStatistics("投票统计列表"),

    VoteDetail("投票明细列表"),

    /**
     * 系统管理
     */
    User("用户管理列表"),

    UserExpand("用户管理列表"),

    UserExpandTemplate("用户导入模板"),

    Org("组织列表"),

    Role("角色管理列表"),

    Dict("数据字典管理列表"),

    Version("版本管理"),

    Title("头衔设置列表"),

    HomePageConfig("首页配置列表"),

    Feedback("反馈管理列表"),

    IdentityPostSystemTemplate("岗位体系导入模板"),

    IdentityPost("岗位身份列表"),

    IdentityPostTemplate("岗位身份导入模板"),

    IdentityPostSystemData("岗位体系数据列表"),

    Expert("专家评委库数据列表"),

    IdentityTime("时间身份列表"),

    Identity("身份列表"),

    /**
     * 统计分析模块
     */

    InfoStatAnalysis("资讯访问统计列表"),

    LearnRecordStatAnalysis("学员档案列表"),

    LearnRecordCourseStatAnalysis("学员档案统计-新学课程列表"),

    LearnRecordExamStatAnalysis("学员档案统计-参与考试列表"),

    LearnRecordInfoStatAnalysis("学员档案统计-查看资讯列表"),

    LearnRecordProjectStatAnalysis("学员档案统计-参与学习项目列表"),

    LearnRecordFaceProjectStatAnalysis("学员档案统计-参与面授班级列表"),

    LearnRecordTrainStatAnalysis("学员档案统计-参与培训项目列表"),

    LearnRecordSurveyStatAnalysis("学员档案统计-参与调研列表"),

    ScoreRankStatAnalysis("经验排行榜列表"),

    UserIntegralStatAnalysis("积分统计列表"),

    LecturerIntegral("积分统计列表-按讲师"),

    UserIntegralDetail("积分统计详情"),

    UserIntegralDetailStatAnalysis("积分统计详情列表"),

    CourseLearnState("课程情况统计"),

    CourseLearned("课程学习情况记录"),

    CourseAgreeDetail("课程点赞情况记录"),

    CoursewareLearnStatAnalysis("课件学习统计列表"),

    CoursewareLearnDetailStatAnalysis("课件学习统计详情列表"),

    OrgLearnStatAnalysis("部门学习统计"),

    ExamStateStatAnalysis("考试情况统计"),

    ExamStateStatPartAnalysis("考试情况统计参与人数"),

    ExamStateStatPassAnalysis("考试情况统计及格人数"),

    ExamStateStatPostAnalysis("考试情况统计交卷人数"),

    GldTradeStatAnalysis("金币交易查询"),

    ExchangeRecordStatAnalysis("兑换记录查询"),

    ExcitationCollectStatAnalysis("学员激励汇总"),

    InsideLecturerByDepartment("讲师统计（按部门）"),

    InsideLecturerByCategory("讲师统计（按类别）"),

    UserExcitationRecordStatAnalysis("目标激励明细查询"),

    OnlineUserStatAnalysis("上线用户统计"),

    TrainPlanStatAnalysis("培训计划统计"),
    SysTemTagStatAnalysis("系统标签统计"),

    TimeRegionStatAnalysis("访问时段统计"),

    StudentUploadCoursewareStatAnalysis("学员课件上传统计"),

    SearchKeyStatAnalysis("搜索关键字统计"),

    CertifiedCategory("认证分类列表"),

    Log("操作日志"),

    BizLog("业务操作日志"),

    OrgThirdSyncRecord("部门同步历史"),

    PostThirdSyncRecord("岗位同步历史"),

    UserThirdSyncRecord("人员同步历史"),

    CertificationRelateWatermarkImg("明细图片"),

    Train("培训项目列表"),

    TrainActivity("培训项目活动列表"),

    TrainProject("班级管理列表"),

    TrainActivityPracticeRecord("实操管理列表"),

    MedalUserRelation("勋章列表"),

    FormManage("表单管理清单列表"),

    /**
     * 计划模块
     */
    Plan("培训计划列表"),

    PlanAudit("培训计划管理-审核列表"),

    PlanInventory("培训计划清单列表"),

    PlanInventoryTemplate("培训计划清单模板"),

    PlanTrain("培训项目列表"),

    PlanStatistic("培训计划汇总表"),

    PlanInventoryStatistic("培训计划条目明细"),

    PlanExecuteMonth("培训计划执行统计-按月"),

    PlanExecuteYear("培训计划执行统计-按年"),

    Leaflet("宣传单列表"),

    PlanCategory("培训计划类别"),

    /**
     * 申请模块
     */
    ApplyLecturer("讲师预约审核列表"),

    ApplyCourseDownload("下载申请列表"),

    /**
     * 课程认证讲师-by讲师导出
     */
    LecturerCourseAuthByLecturer("课程认证讲师按照讲师导出"),

    /**
     * 课程认证讲师-by课程导入
     */
    LecturerCourseAuthByLecturerCourse("认证课程列表"),

    /**
     * 课程认证讲师-by课程导出
     */
    LecturerCourseAuthByCourse("课程认证讲师按照课程导出"),

    /**
     * 讲师开发课程列表
     */
    LecturerCourseList("讲师开发课程列表"),

    /**
     * 课程认证讲师-授课明细
     */
    LecturerCourseAuthTeach("课程认证讲师-讲师授课明细"),

    /**
     * 外部培训-外部培训管理列表
     */
    TrainWithOut("外部培训管理列表"),

    /**
     * 外部培训-外部培训协办管理列表
     */
    TrainWithOutAssist("外部培训协办管理列表"),

    /**
     * 外部培训统计
     */
    TrainWithoutStatistics("外部培训统计"),

    /**
     * 外部培训-外部培训报名列表
     */
    TrainWithoutApply("外部培训报名列表"),

    /**
     * 外部培训-外部培训协办报名列表
     */
    TrainWithoutApplyAssist("外部培训协办报名列表"),

    /**
     * 外部培训-外部培训结果列表
     */
    TrainWithoutResult("外部培训结果列表"),

    /**
     * 外部培训-外部培训明细列表
     */
    TrainWithoutApplyDetail("外部培训明细列表"),

    /**
     * 外部培训统计-专业
     */
    TrainWithoutWorkStatistics("外部培训统计-专业"),

    /**
     * 成员管理-学员
     */
    TrainMembers("成员管理-学员"),

    /**
     * 成员管理-协办人员
     */
    TrainCoOrganizer("成员管理-协办人员"),

    /**
     * 活动分
     */
    TaskScore("活动分"),

    /**
     * 供应商管理列表
     */
    Supplier("供应商管理列表"),

    /**
     * 供应商资料管理列表
     */
    SupplierFile("供应商资料管理列表"),

    /**
     * 供应商讲师授课管理列表
     */
    TrainLecturerTeachDetail("供应商讲师授课管理列表"),

    /**
     * 培训班课程列表
     */
    TrainCourse("培训班课程列表"),

    /**
     * 培训项目明细统计
     */
    TrainTaskDetail("培训项目明细统计"),

    /**
     * 培训项目个人学习排名统计
     */
    TrainPersonRank("培训项目个人学习排名统计"),

    /**
     * 培训项目部门学习排名统计
     */
    TrainOrgLearn("培训项目部门学习排名统计"),

    UserWhiteRecord("推送白名单"),

    UserIdentityInfo("用户身份信息"),

    IdentityUserInfo("身份用户管理"),

    UserIdentityList("用户身份管理"),

    VisibleViewLimitUserList("下发人员明细"),

    UserVisitItemRecord("链接访问明细"),

    HomeRouterVisitDetail("后台访问明细"),

    ItemVisitRecord("栏目访问记录"),

    LearnMap("学习地图"),

    /**
     * 学习地图列表
     */
    LearnMapList("学习地图列表"),

    LearnMapExec("学习地图执行列表"),

    LearnMapLearnDetail("学习明细列表"),

    LearnMapLearnUser("学习用户列表"),

    LearnMapActivityHorizontalStat("活动横向统计"),

    LearnMapPhaseHorizontalStat("阶段横向统计"),

    LearnMapActivity("学习地图活动列表"),

    LearnMapProgressStat("学习地图情况"),

    LearnMapActivityProgressStat("学习地图活动情况"),

    AbilityDictionary("能力词典"),

    AbilityMode("能力模型"),
    PermissionConfig("权限目录配置"),

    PermissionRouter("权限目录路由配置"),

    CoursewareQuestionAnswerRecord("课件答题明细"),

    ApplyTemplate("面授项目报名模板"),

    FaceProjectApplyUser("面授项目用户报名列表"),

    FaceProject("面授项目管理列表"),

    FaceProjectStatistic("面授项目培训统计列表"),

    LearnProjectStatistic("学习项目培训统计列表"),

    TrainProjectStatistic("培训项目培训统计列表"),

    ProjectStatisticUserDetail("学员列表"),

    JobQualification("任职资格列表"),

    JobAuthentication("资格认证列表"),

    JobAuthApplyRecord("认证人员列表"),

    PracticalSuperviseUser("监督评价员列表"),

    PracticalOperationUserBySupervise("监督学员列表"),

    PracticalOperationUser("实操记录列表"),

    LearnMapAbilityOrgStatistic("能力项部门初训统计"),

    LearnMapAbilityOrgStatistic2("能力项部门复训统计"),

    LearnMapUserOrgStatistic("胜任力地图部门初训统计"),

    LearnMapUserOrgStatistic2("胜任力地图部门复训统计"),

    LearnMapUserResourcesStatistic("胜任力地图学习明细统计"),

    LearnMapUserStatistic("胜任力地图学习进度明细统计"),

    LearnMapAbilityAsk("学习地图能力学习要求"),

    InvoiceList("开票管理列表"),

    ProjectVacate("请假管理列表"),

    ScheduleStat("日程统计列表"),

    ProjectFormTemplateAnnex("辅导结果附件"),
    /**
     * 游戏模块
     */
    March("游戏管理列表"),

    MarchCheckpoint("游戏关卡列表"),

    MarchCheckpointTask("游戏任务列表"),

    MarchTeam("团队管理列表"),

    MarchPost("话题管理列表"),

    MarchNotice("说明管理列表"),

    MarchPostCountComment("回帖统计"),

    ASSESS_TOOL_QUESTION_TEM("测评工具题目导入模板"),

    ASSESS_PROGRESS("测评项目进度统计"),

    ASSESS_IMPORT_USER("测评用户导入模板"),

    ASSESS_IMPORT_RECORD("测评记录导入模板"),

    ASSESS_IMPORT_DETAILS("测评明细导入模版"),

    RecordDetail("答题明细"),

    ASSESS_REPORT("测评报告"),

    COMPETITION_SESSION_USER_DETAIL("考试竞赛场次人员信息"),

    INTERVIEW_QUANTITY_STATISTICS("访问量统计"),

    USER_POSTER_SHARE("用户海报分享列表"),

    /**
     * 用户金币列表
     */
    UserGoldCoin("用户金币列表"),

    /**
     * 用户金币账单列表
     */
    UserGoldCoinBill("用户金币账单列表"),

    /**
     * 会员卡管理列表
     */
    MemberCard("会员卡管理列表"),

    /**
     * 会员机构管理列表
     */
    MemberOrg("会员机构管理列表"),

    /**
     * 会员管理列表
     */
    Member("会员管理列表"),

    LEARNING_TARGET_MANAGE("学习目标管理"),

    LEARNING_TARGET_MANAGE_USER_PROCESS("学习目标完成明细"),

    USER_GOTTEN_TARGET_VALUE_DETAIL("目标值明细"),

    LEARNING_TARGET_COMPLETION_DETAILS("学习目标完成统计"),

    LEARNING_TARGET_REF_USER_LIST("目标用户列表"),

    TARGET_USER_ACHIEVEMENT_DETAIL("人员学习目标达成明细"),

    TASK_MENTOR_LIST("任务导师列表"),

    TASK_RECORD("任务执行记录"),

    TASK_RECORD_DETAIL("任务执行记录详细"),
    MY_DEAL_RESOURCE_AUDIT_LIST("我的处理列表"),
    MY_APPLY_RESOURCE_AUDIT_LIST("我的申请列表"),
    ALL_RESOURCE_AUDIT_LIST("全部的申请列表"),
    SYS_TAG_LIST("标签列表"),

    ProjectCompletionStatistic("项目完成情况统计"),

    ProjectUserTask("用户项目任务完成情况统计"),

    ProjectUserProjectExcitationRecord("用户项目激励获取记录"),

    ProjectUserProjectCoursewareStudyDetail("用户学习项目课时统计"),

    ;

    @EnumI18nProperty
    private final String type;

    ExportFileNameEnum(String type) {
        this.type = type;
    }

    public static boolean isEffective(String type) {

        if (StringUtils.isBlank(type)) {
            return false;
        }
        String typeLower = type.toLowerCase();

        for (FileType fileType : FileType.values()) {
            if (typeLower.equals(fileType.getType())) {
                return true;
            }
        }
        return false;
    }

    public String getType() {
        return i18n(name(), this.type);
    }


}
