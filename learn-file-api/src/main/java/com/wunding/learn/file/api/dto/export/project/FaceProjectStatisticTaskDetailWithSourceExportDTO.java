package com.wunding.learn.file.api.dto.export.project;

import com.alibaba.excel.annotation.ExcelProperty;
import com.wunding.learn.file.api.dto.export.converter.FinishConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(name = "FaceProjectStatisticTaskDetailWithSourceExportDTO", description = "导出面授项目任务明细统计返回对象")
public class FaceProjectStatisticTaskDetailWithSourceExportDTO {
    @Schema(description = "姓名")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailWithSourceExportDTO.fullName}")
    private String fullName;
    
    @Schema(description = "账号")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailWithSourceExportDTO.loginName}")
    private String loginName;
    
    
    @Schema(description = "部门")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailWithSourceExportDTO.orgPath}")
    private String orgPath;
    
    @Schema(description = "岗位")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailWithSourceExportDTO.postName}")
    private String postName;
    
    @Schema(description = "任务名称")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailWithSourceExportDTO.taskName}")
    private String taskName;
    
    @Schema(description = "团队名称")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailWithSourceExportDTO.teamName}")
    private String teamName;
    
    /**
     * 课时
     */
    @Schema(description = "课时 单位秒")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailWithSourceExportDTO.costTime}")
    private Long costTime;
    
    /**
     * 考试分数
     */
    @Schema(description = "成绩")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailWithSourceExportDTO.userScore}")
    private Float userScore;
    
    /**
     * 任务是否完成
     */
    @Schema(description = "任务状态  -1 未参加 0 未完成 1 已完成 ")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailWithSourceExportDTO.isFinish}",  converter = FinishConverter.class)
    private Integer isFinish;
    
    /**
     * 获取的任务分(-1表示没有完成任务)
     */
    @Schema(description = "任务分")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailWithSourceExportDTO.taskScore}")
    private Integer taskScore;
    
    /**
     * 任务分获取时间
     */
    @Schema(description = "任务分获得时间")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailWithSourceExportDTO.taskFinishedTime}")
    private Date taskFinishedTime;
    
    /**
     * 任务参与时间
     */
    @Schema(description = "实际开始时间")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailWithSourceExportDTO.joinTime}")
    private Date joinTime;
    
    /**
     * 任务完成时间
     */
    @Schema(description = "实际结束时间")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailWithSourceExportDTO.finishedTime}")
    private Date finishedTime;
    
    
    /**
     * 任务开始时间
     */
    @Schema(description = "任务开始时间")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailWithSourceExportDTO.startTime}")
    private Date startTime;
    
    /**
     * 任务结束时间
     */
    @Schema(description = "任务结束时间")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailWithSourceExportDTO.endTime}")
    private Date endTime;
    
    @Schema(description = "来源")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailWithSourceExportDTO.sourceName}")
    private String sourceName;
    
    @Schema(description = "说明")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailWithSourceExportDTO.explanation}")
    private String explanation;
}
