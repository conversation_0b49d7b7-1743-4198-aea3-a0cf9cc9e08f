package com.wunding.learn.file.api.dto.export.certification;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <p>测评题目明细列表导出
 *
 * <AUTHOR> href="mailto:<EMAIL>">liangzm</a>
 * @since 2024/6/24
 */
@Data
@Schema(name = "AssessQuestionDetailExportDTO", description = "测评题目明细列表导出")
public class AssessQuestionDetailExportDTO {


    @Schema(description = "测评人")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.certification.AssessQuestionDetailExportDTO.assessUser}")
    private String assessUser;

    @Schema(description = "测评人账号")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.certification.AssessQuestionDetailExportDTO.assessLoginName}")
    private String assessLoginName;

    @Schema(description = "部门")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.certification.AssessQuestionDetailExportDTO.orgPath}")
    private String orgPath;

    @Schema(description = "测评日期")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.certification.AssessQuestionDetailExportDTO.assessDate}")
    private Date assessDate;

    @Schema(description = "测评编码")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.certification.AssessQuestionDetailExportDTO.proNo}")
    private String proNo;

    @Schema(description = "测评项目")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.certification.AssessQuestionDetailExportDTO.proName}")
    private String proName;

    @Schema(description = "题型")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.certification.AssessQuestionDetailExportDTO.questionTypeName}")
    private String questionTypeName;

    @Schema(description = "题目")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.certification.AssessQuestionDetailExportDTO.questionName}")
    private String questionName;

    @Schema(description = "测评方式")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.certification.AssessQuestionDetailExportDTO.methodTypeName}")
    private String methodTypeName;

    @Schema(description = "被评人")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.certification.AssessQuestionDetailExportDTO.fullName}")
    private String fullName;

    @Schema(description = "被评人账号")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.certification.AssessQuestionDetailExportDTO.loginName}")
    private String loginName;

    @Schema(description = "分值")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.certification.AssessQuestionDetailExportDTO.score}")
    private BigDecimal score;

    @Schema(description = "问答内容")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.certification.AssessQuestionDetailExportDTO.userAnswer}")
    private String userAnswer;

    @Schema(description = "测评时间")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.certification.AssessQuestionDetailExportDTO.answerTime}")
    private Date answerTime;
}
