package com.wunding.learn.file.api.dto.export.recruiting;

import com.alibaba.excel.annotation.ExcelProperty;
import com.wunding.learn.file.api.dto.export.converter.TransformStatusConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(name = "RecruitingMaterialExportDTO", description = "导出宣传材料列表对象")
public class RecruitingMaterialExportDTO {

    /**
     * 材料标题
     */
    @Schema(description = "材料标题")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.recruiting.RecruitingMaterialExportDTO.title}")
    private String title;

    /**
     * 样例文件类型
     */
    @Schema(description = "样例文件类型")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.recruiting.RecruitingMaterialExportDTO.exampleFileType}")
    private String exampleFileType;

    /**
     * 排序
     */
    @Schema(description = "排序")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.recruiting.RecruitingMaterialExportDTO.sort}")
    private Integer sort;

    /**
     * 案例文件转码状态 1-转码中 2-转码成功 3-转码失败 4-排队中
     */
    @Schema(description = "案例文件转码状态 1-转码中 2-转码成功 3-转码失败 4-排队中")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.recruiting.RecruitingMaterialExportDTO.transformStatus}",  converter = TransformStatusConverter.class)
    private Integer transformStatus;


}
