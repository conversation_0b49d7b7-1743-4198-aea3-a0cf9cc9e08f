package com.wunding.learn.file.api.dto.export.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import java.util.Objects;

/**
 * 导出 已完成 未完成 未参加
 *
 * <AUTHOR>
 * @date 2022/11/07
 */
public class FinishConverter implements Converter<Integer> {


    @Override
    public Class<?> supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }


    @Override
    public Integer convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty,
        GlobalConfiguration globalConfiguration) throws Exception {
        Integer res = null;
        String cellText = cellData.getStringValue();
        for (ConvertEnum item : ConvertEnum.values()) {
            if (Objects.equals(cellText, item.getText())) {
                res = item.getValue();
                break;
            }
        }
        return res;
    }

    @Override
    public WriteCellData<?> convertToExcelData(Integer value, ExcelContentProperty contentProperty,
        GlobalConfiguration globalConfiguration) throws Exception {
        String res = "";
        for (ConvertEnum item : ConvertEnum.values()) {
            if (Objects.equals(value, item.getValue())) {
                res = item.getText();
                break;
            }
        }
        return new WriteCellData<>(res);
    }

    enum ConvertEnum {

        /**
         * 未完成
         */
        FINISH(0, "未完成"),

        /**
         * 已完成
         */
        UN_FINISH(1, "已完成"),

        /**
         * 未参加
         */
        UN_JOIN(-1, "未参加");

        /**
         * java中的值
         */
        private final Integer value;
        /**
         * excel中的文本
         */
        private final String text;

        ConvertEnum(Integer value, String text) {
            this.value = value;
            this.text = text;
        }

        public Integer getValue() {
            return value;
        }

        public String getText() {
            return text;
        }
    }
}
