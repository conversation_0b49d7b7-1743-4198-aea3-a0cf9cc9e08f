package com.wunding.learn.file.api.component;

import com.wunding.learn.common.constant.file.FileErrorNoEnum;
import com.wunding.learn.common.enums.file.TranscodeStatusEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.mq.event.PriorityTransCodeEvent;
import com.wunding.learn.common.mq.event.TransCodeEvent;
import com.wunding.learn.common.mq.service.MqProducer;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.file.api.constant.FileBizType;
import com.wunding.learn.file.api.constant.FileResourceEnum;
import com.wunding.learn.file.api.dto.IFileResourceDTO;
import com.wunding.learn.file.api.dto.NamePath;
import com.wunding.learn.file.api.dto.SaveFileDTO;
import com.wunding.learn.file.api.service.FileFeign;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * 文件资源操作公共代码封装
 *
 * <AUTHOR>
 * @date 2022/7/11
 */
@Slf4j
@Component
public class FileResourceOptComponent {

    @Resource
    private FileFeign fileFeign;

    @Resource
    @Lazy
    private MqProducer mqProducer;

    /**
     * 上传文件资源并转码
     *
     * @param bizId           业务主键id
     * @param fileResourceDTO
     * @return
     */
    public Integer dealWithFileResourceUpload(String bizId, IFileResourceDTO fileResourceDTO) {
        String filePath = fileResourceDTO.getFilePath();
        String fileName = fileResourceDTO.getFileName();
        if (!FileBizType.isEffective(fileResourceDTO.getBizType())) {
            throw new BusinessException(FileErrorNoEnum.ERR_NO_FILES_BUSINESS_TYPES);
        }
        // 默认不需要转码
        Integer transformStatus = TranscodeStatusEnum.TRANSFORMED.value;
        log.info("save fileResourceDTO:{}", JsonUtil.objToJson(fileResourceDTO));
        if (StringUtils.isNotBlank(fileResourceDTO.getFileType()) && FileResourceEnum.isNeedTransform(
            fileResourceDTO.getFileType())) {
            // 需要转码
            transformStatus = TranscodeStatusEnum.PENDING.value;
            //将临时目录文件转移至正式目录并进行异步转码
            SaveFileDTO saveFileDTO;
            if (fileResourceDTO.getFileType().equals(FileResourceEnum.PPT.name())) {
                // 上传ppt时可能存在附件
                //保存主文件
                saveFileDTO = fileFeign.saveFile(bizId, fileResourceDTO.getBizType().name(), fileName, filePath);
                // 保存附件
                if (!StringUtil.isEmpty(fileResourceDTO.getAdjunctPath())) {
                    //保存附件
                    fileFeign.saveAttachmentFile(bizId, fileResourceDTO.getBizType().name(),
                        fileResourceDTO.getAdjunctPath(), fileResourceDTO.getAdjunctName());
                }
            } else {
                saveFileDTO = fileFeign.saveFile(bizId, fileResourceDTO.getBizType().name(), fileName, filePath);
            }
            // 发送转码消息
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                log.error("=========dealWithFileResourceUpload error=========", e);
                Thread.currentThread().interrupt();
                throw new IllegalArgumentException(e);

            }
            mqProducer.sendMsg(new TransCodeEvent(saveFileDTO.getId(), bizId, fileResourceDTO.getBizType().name(),
                filePath, fileResourceDTO.getMime(), fileResourceDTO.getFileType(),
                fileResourceDTO.getLibId()));
        } else {
            // 无需转码
            //将临时目录文件转移至正式目录
            fileFeign.saveFile(bizId, fileResourceDTO.getBizType().name(), fileName, filePath);
        }
        return transformStatus;
    }

    /**
     * 文件重新转码
     *
     * @param bizId
     * @param bizType
     */
    public void reTranscoding(String bizId, String bizType, String mime, String fileType, String libId) {
        // 查询文件id、path
        NamePath namePath = fileFeign.getSourceFileInfo(bizId, bizType);
        if (null == namePath) {
            return;
        }
        mqProducer
            .sendMsg(new TransCodeEvent(namePath.getId(), bizId, bizType, namePath.getPath(), mime, fileType, libId));
    }

    /**
     * 文件重新转码
     *
     * @param bizId
     * @param bizType
     */
    public void reTranscoding(String bizId, String bizType, String mime, String fileType, String libId,
        NamePath namePath) {
        if (null == namePath) {
            return;
        }
        mqProducer
            .sendMsg(new TransCodeEvent(namePath.getId(), bizId, bizType, namePath.getPath(), mime, fileType, libId));
    }

    /**
     * 资源优先转码
     */
    public void priorityTranscoding(String bizId, String bizType, String mime, String fileType, String libId) {
        // 查询文件id、path
        NamePath namePath = fileFeign.getSourceFileInfo(bizId, bizType);
        if (null == namePath) {
            return;
        }
        mqProducer
            .sendMsg(new PriorityTransCodeEvent(namePath.getId(), bizId, bizType, namePath.getPath(), mime, fileType, libId));
    }
}
