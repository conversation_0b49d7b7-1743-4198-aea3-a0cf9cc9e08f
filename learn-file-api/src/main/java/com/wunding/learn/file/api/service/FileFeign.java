package com.wunding.learn.file.api.service;

import com.github.pagehelper.PageInfo;
import com.wunding.learn.file.api.dto.CertRelateWaterMarkImgInfoDTO;
import com.wunding.learn.file.api.dto.CopyFileDTO;
import com.wunding.learn.file.api.dto.FileListDTO;
import com.wunding.learn.file.api.dto.MimeInfoDTO;
import com.wunding.learn.file.api.dto.NamePath;
import com.wunding.learn.file.api.dto.SaveBatchFileDTO;
import com.wunding.learn.file.api.dto.SaveFileDTO;
import com.wunding.learn.file.api.dto.SaveOrUpdatePicListDTO;
import com.wunding.learn.file.api.dto.UserAvatarsDTO;
import com.wunding.learn.file.api.dto.VideoClarityDTO;
import com.wunding.learn.file.api.dto.ZipDownloadDTO;
import com.wunding.learn.file.api.dto.export.project.TrainHomeWorkZipDownloadDTO;
import com.wunding.learn.file.api.query.FileQuery;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件处理接口
 *
 * <AUTHOR>
 * @date 2022/3/2
 */
@FeignClient(url = "${learn.service.learn-file-service}", name = "learn-file-service", path = "/file"
    // fallback = FileFeignFallbackImpl.class,
    // fallbackFactory = FileFeignClientFallbackFactory.class
)
public interface FileFeign {

    /**
     * 保存文件并记录日志
     *
     * @param bizId        业务id
     * @param bizType      业务类型
     * @param name         文件名
     * @param tempFilePath 临时文件路径
     * @return 正式文件路径
     */
    @PostMapping("/file/saveFileWithLog")
    SaveFileDTO saveFileWithLog(@RequestParam("bizId") String bizId, @RequestParam("bizType") String bizType,
        @RequestParam("name") String name, @RequestParam("tempFilePath") String tempFilePath);

    /**
     * 保存文件
     *
     * @param bizId        业务id
     * @param bizType      业务类型
     * @param name         文件名
     * @param tempFilePath 临时文件路径
     * @return 正式文件路径
     */
    @PostMapping("/file/saveFile")
    SaveFileDTO saveFile(@RequestParam("bizId") String bizId, @RequestParam("bizType") String bizType,
        @RequestParam("name") String name, @RequestParam("tempFilePath") String tempFilePath);

    /**
     * 编辑保存删除单个文件一条龙
     *
     * @param bizId            业务id
     * @param bizType          业务类型
     * @param name             文件名
     * @param tempFilePath     临时文件路径
     * @param deleteAttachment 表示该文件是否为附件 0:否  1是
     * @param isInsert         true 新增单个文件， false 新增更新删除单个文件
     */
    @PostMapping("/file/saveOrUpdateOneFile")
    void saveOrUpdateOneFile(@RequestParam("bizId") String bizId, @RequestParam("bizType") String bizType,
        @RequestParam("name") String name, @RequestParam("tempFilePath") String tempFilePath,
        @RequestParam("isInsert") Boolean isInsert, @RequestParam("deleteAttachment") Integer deleteAttachment);

    /**
     * 获得文件大小
     *
     * @param tempFilePath 临时文件路径
     * @return 正式文件路径
     */
    @PostMapping("/file/getFileSize")
    long getFileSize(@RequestParam("tempFilePath") String tempFilePath);

    /**
     * 保存原文件
     *
     * @param bizId        业务id
     * @param bizType      业务类型
     * @param name         文件名
     * @param tempFilePath 临时文件路径
     * @return 正式文件路径
     */
    @PostMapping("/file/saveSourceFile")
    SaveFileDTO saveSourceFile(@RequestParam("bizId") String bizId, @RequestParam("bizType") String bizType,
        @RequestParam("name") String name, @RequestParam("tempFilePath") String tempFilePath);

    /**
     * 保存文件夹
     *
     * @param bizId       业务id
     * @param bizType     业务类型
     * @param name        文件名
     * @param tempDirPath 临时文件路径
     * @return 正式文件路径
     */
    @PostMapping("/file/saveDir")
    SaveFileDTO saveDir(@RequestParam("bizId") String bizId, @RequestParam("bizType") String bizType,
        @RequestParam("name") String name, @RequestParam("tempDirPath") String tempDirPath);

    /**
     * 保存附件
     *
     * @param bizId                  业务id
     * @param bizType                业务类型
     * @param attachmentFileTempName 附件文件名
     * @param attachmentFileTempPath 临时附件文件路径
     * @return 正式文件路径
     */
    @PostMapping("/file/saveAttachmentFile")
    SaveFileDTO saveAttachmentFile(@RequestParam("bizId") String bizId, @RequestParam("bizType") String bizType,
        @RequestParam("attachmentFileTempPath") String attachmentFileTempPath,
        @RequestParam("attachmentFileTempName") String attachmentFileTempName);


    /**
     * 保存多个文件
     *
     * @param bizId        业务id
     * @param bizType      业务类型
     * @param tempFileList 临时文件路径列表
     * @return 正式文件路径列表
     */
    @PostMapping("/file/saveFiles")
    List<SaveFileDTO> saveFiles(@RequestParam("bizId") String bizId, @RequestParam("bizType") String bizType,
        @RequestBody List<NamePath> tempFileList);

    /**
     * 根据文件ID删除文件
     *
     * @param fileId
     */
    @PostMapping("/file/deleteFileByFileId")
    void deleteFileByFileId(@RequestParam("fileId") String fileId);


    /**
     * 根据业务id 删除文件 deleteAttachment = 1 代表删除所有的文件 deleteAttachment = 0 代表仅删除主文件 deleteAttachment = -1 代表仅删除附件文件
     *
     * @param bizId            业务id
     * @param bizType          业务类型
     * @param deleteAttachment 删除附件方式
     * @return 正式文件路径
     */
    @PostMapping("/file/deleteFileByBizIdAndBizType")
    void deleteFileByBizIdAndBizType(@RequestParam("bizId") String bizId, @RequestParam("bizType") String bizType,
        @RequestParam("deleteAttachment") Integer deleteAttachment);

    /**
     * 根据业务id 删除文件 deleteAttachment = 1 代表删除所有的文件 deleteAttachment = 0 代表仅删除主文件 deleteAttachment = -1 代表仅删除附件文件
     *
     * @param bizIds           业务id集合
     * @param bizType          业务类型
     * @param deleteAttachment 删除附件方式
     * @return 正式文件路径
     */
    @PostMapping("/file/deleteFileByBizIdsAndBizType")
    void deleteFileByBizIdListAndBizType(@RequestParam("bizIds") String bizIds, @RequestParam("bizType") String bizType,
        @RequestParam("deleteAttachment") Integer deleteAttachment);

    /**
     * 根据文件Ids删除文件
     *
     * @param fileIds
     */
    @PostMapping("/file/deleteFileByIds")
    void deleteFileByFileIds(@RequestBody Collection<String> fileIds);

    /**
     * 保存文件
     *
     * @param bizId        业务id
     * @param bizType      业务类型
     * @param name         文件名
     * @param tempFilePath 临时文件路径
     * @return 正式文件路径
     */
    @PostMapping("/file/saveImage")
    SaveFileDTO saveImage(@RequestParam("bizId") String bizId, @RequestParam("bizType") String bizType,
        @RequestParam("name") String name, @RequestParam("tempFilePath") String tempFilePath);

    /**
     * 保存默认图片业务关系
     *
     * @param bizId       业务id
     * @param bizType     业务类型
     * @param name        文件名
     * @param oosFilePath 文件路径
     * @return 正式文件路径
     */
    @PostMapping("/file/saveDefultImage")
    void saveDefaultImage(@RequestParam("bizId") String bizId, @RequestParam("bizType") String bizType,
        @RequestParam("name") String name, @RequestParam("oosFilePath") String oosFilePath);

    /**
     * 保存文件
     *
     * @param bizId        业务id
     * @param bizType      业务类型
     * @param name         文件名
     * @param tempFilePath 临时文件路径
     * @param width        宽
     * @param height       高
     * @return 正式文件路径
     */
    @PostMapping("/file/saveImage2")
    SaveFileDTO saveImage2(@RequestParam("bizId") String bizId, @RequestParam("bizType") String bizType,
        @RequestParam("name") String name, @RequestParam("tempFilePath") String tempFilePath,
        @RequestParam("width") Integer width, @RequestParam("height") Integer height);

    /**
     * 根据业务id和业务类型获取文件删除图片
     *
     * @param bizId   业务id
     * @param bizType 业务类型
     */
    @PostMapping("/file/deleteImageByBizIdAndBizType")
    void deleteImageByBizIdAndBizType(@RequestParam("bizId") String bizId, @RequestParam("bizType") String bizType);

    /**
     * 根据业务id和业务类型获取文件删除图片
     *
     * @param bizIdList 业务id集合
     * @param bizType   业务类型
     */
    @PostMapping("/file/deleteImageByBizIdListAndBizType")
    void deleteImageByBizIdListAndBizType(@RequestParam("bizIdList") List<String> bizIdList,
        @RequestParam("bizType") String bizType);

    /**
     * 编辑保存删除单张照片一条龙
     *
     * @param bizId        业务id
     * @param bizType      业务类型
     * @param name         文件名
     * @param tempFilePath 临时文件路径
     * @param isInsert     true 新增单张照片， false 新增更新删除单张照片
     */
    @PostMapping("/file/saveOrUpdateOnePic")
    void saveOrUpdateOnePic(@RequestParam("bizId") String bizId, @RequestParam("bizType") String bizType,
        @RequestParam("name") String name, @RequestParam("tempFilePath") String tempFilePath,
        @RequestParam("isInsert") Boolean isInsert);

    /**
     * 根据图片ID删除文件
     */
    @PostMapping("/file/deleteImageByImageId")
    void deleteImageByImageId(@RequestParam("imageId") String imageId);

    /**
     * 根据图片IDs删除文件
     */
    @PostMapping("/file/deleteImageByImagesIds")
    void deleteImageByImagesIds(@RequestBody List<String> imagesIds);

    /**
     * 保存多个文件
     *
     * @param bizId        业务id
     * @param bizType      业务类型
     * @param tempFileList 临时文件路径列表
     * @return 正式文件路径列表
     */
    @PostMapping("/file/saveImages")
    List<SaveFileDTO> saveImages(@RequestParam("bizId") String bizId, @RequestParam("bizType") String bizType,
        @RequestBody List<NamePath> tempFileList);


    /**
     * 批量保存图像
     *
     * @param saveFileDTOList 保存文件dtolist
     * @return {@link List }<{@link SaveFileDTO }>
     */
    @PostMapping("/file/saveImageBatch")
    List<SaveFileDTO> saveImageBatch(@RequestBody Collection<SaveFileDTO> saveFileDTOList);

    /**
     * 保存、更新多个文件(删除不存在的，保留有ID的)
     *
     * @param bizId   业务id
     * @param bizType 业务类型
     * @return
     */
    @PostMapping("/file/saveOrUpdateImages")
    List<SaveFileDTO> saveOrUpdateImages(@RequestParam("bizId") String bizId, @RequestParam("bizType") String bizType,
        @RequestBody List<NamePath> list);

    /**
     * 获取文件URL地址
     *
     * @param path 文件路径
     * @return URL地址
     */
    @GetMapping("/file/getFileUrlByPath")
    String getFileUrl(@RequestParam("path") String path);

    /**
     * 获取文件URL地址
     *
     * @param paths 文件路径集合
     * @return
     */
    @GetMapping("/file/getFileUrlByPaths")
    Map<String, String> getFileUrl(@RequestParam("paths") Collection<String> paths);

    /**
     * 按业务id和业务类型获取文件URL, 获取转码后文件/index.html
     *
     * @param bizId   业务id
     * @param bizType 业务类型
     * @return 文件URL
     */
    @GetMapping("/file/getFileUrl")
    String getFileUrl(@RequestParam("bizId") String bizId, @RequestParam("bizType") String bizType);

    /**
     * 按业务id和业务类型获取文件URL，获取原文件/.zip
     *
     * @param bizId   业务id
     * @param bizType 业务类型
     * @return 文件URL
     */
    @GetMapping("/file/getSourceFileUrl")
    String getSourceFileUrl(@RequestParam("bizId") String bizId, @RequestParam("bizType") String bizType);

    /**
     * 按业务id和业务类型获取文件URL，获取原文件/.zip
     *
     * @param bizIds  业务id
     * @param bizType 业务类型
     * @return 文件URL
     */
    @PostMapping("/file/getSourceFileUrls")
    Map<String, String> getSourceFileUrls(@RequestParam("bizIds") List<String> bizIds,
        @RequestParam("bizType") String bizType);

    /**
     * 按业务id和业务类型获取文件URL，获取原文件/.zip
     *
     * @param bizId   业务id
     * @param bizType 业务类型
     * @return 文件信息
     */
    @GetMapping("/file/getSourceFileInfo")
    NamePath getSourceFileInfo(@RequestParam("bizId") String bizId, @RequestParam("bizType") String bizType);

    /**
     * 按业务id和业务类型获取文件URL，获取原文件/.zip
     *
     * @param bizId   业务id
     * @param bizType 业务类型
     * @return 文件信息
     */
    @GetMapping("/file/getSourceFileInfoOfPhysicalPath")
    NamePath getSourceFileInfoOfPhysicalPath(@RequestParam("bizId") String bizId,
        @RequestParam("bizType") String bizType);

    /**
     * 按业务id和业务类型获取文件URL，获取原文件/.zip
     *
     * @param bizIds  业务id
     * @param bizType 业务类型
     * @return 文件信息
     */
    @PostMapping("/file/getSourceFileInfos")
    List<NamePath> getSourceFileInfos(@RequestBody List<String> bizIds, @RequestParam("bizType") String bizType,
        @RequestParam("isSource") boolean isSource);

    /**
     * 按业务id和业务类型获取视频
     *
     * @param bizId   业务id
     * @param bizType 业务类型
     * @return 文件URL
     */
    @GetMapping("/file/getVideoNamePath")
    NamePath getVideoNamePath(@RequestParam("bizId") String bizId, @RequestParam("bizType") String bizType);

    /**
     * 按业务id和业务类型获取图片NamePath
     *
     * @param bizId   业务id
     * @param bizType 业务类型
     * @return 文件URL
     */
    @GetMapping("/file/getImageFileNamePath")
    NamePath getImageFileNamePath(@RequestParam("bizId") String bizId, @RequestParam("bizType") String bizType);

    /**
     * 按业务id和业务类型获取文件NamePath
     *
     * @param bizId   业务id
     * @param bizType 业务类型
     * @return 文件信息
     */
    @GetMapping("/file/getFileNamePath")
    NamePath getFileNamePath(@RequestParam("bizId") String bizId, @RequestParam("bizType") String bizType);

    /**
     * 按业务id和业务类型获取文件NamePath
     *
     * @param bizId   业务id
     * @param bizType 业务类型
     * @return 文件信息列表
     */
    @GetMapping("/file/getFileNamePaths")
    List<NamePath> getFileNamePaths(@RequestParam("bizId") String bizId, @RequestParam("bizType") String bizType);

    /**
     * 按业务id和业务类型获取图片List<NamePath>
     *
     * @param bizId   业务id
     * @param bizType 业务类型
     * @return 文件URL
     */
    @GetMapping("/file/getImageFileNamePaths")
    List<NamePath> getImageFileNamePaths(@RequestParam("bizId") String bizId, @RequestParam("bizType") String bizType);

    /**
     * 按业务ids和业务类型获取图片List<NamePath>
     *
     * @param bizId   业务id
     * @param bizType 业务类型
     * @return 文件URL
     */
    @PostMapping("/file/getImageFileNamePathsByBizIds")
    List<NamePath> getImageFileNamePathsByBizIds(@RequestBody Collection<String> bizId,
        @RequestParam("bizType") String bizType);

    /**
     * 按业务ids和业务类型获取图片List<NamePath>
     *
     * @param bizId   业务id
     * @param bizType 业务类型
     * @return 文件URL
     */
    @PostMapping("/file/getImageFileNamePathsByBizIdsOfMap")
    Map<String, List<NamePath>> getImageFileNamePathsByBizIdsOfMap(@RequestBody Collection<String> bizId,
        @RequestParam("bizType") String bizType);

    /**
     * 按业务ids和业务类型获取图片Map
     * <p>
     * 一个业务id对应一张图片，多张图片得自行再实现一个 feign 接口
     *
     * @param bizId   业务id
     * @param bizType 业务类型
     * @return 文件URL
     */
    @PostMapping("/file/getImageFileNamePathMapByBizIds")
    Map<String, NamePath> getImageFileNamePathMapByBizIds(@RequestBody Collection<String> bizId,
        @RequestParam("bizType") String bizType);

    /**
     * 按业务id和业务类型获取文件URL
     *
     * @param bizIds  业务id
     * @param bizType 业务类型
     * @return 文件URL列表
     */
    @GetMapping("/file/getFileUrls")
    Map<String, String> getFileUrls(@RequestBody List<String> bizIds,
        @RequestParam("bizType") String bizType);

    /**
     * 按业务id和业务类型获取图片URL，如果多条返回第一条
     *
     * @param bizId   业务id
     * @param bizType 业务类型
     * @return 文件URL
     */
    @GetMapping("/file/getImageUrl")
    String getImageUrl(@RequestParam("bizId") String bizId, @RequestParam("bizType") String bizType);

    @GetMapping("/file/getImagePath")
    String getImagePath(@RequestParam("bizId") String bizId, @RequestParam("bizType") String bizType);

    /**
     * 批量获取用户头像
     *
     * @param userIds
     * @return
     */
    @PostMapping("/file/getUserAvatar")
    Map<String, String> getUserAvatar(@RequestBody Collection<String> userIds);

    /**
     * 按业务id和业务类型获取图片URL列表
     *
     * @param bizId   业务id
     * @param bizType 业务类型
     * @return 文件URL列表
     */
    @GetMapping("/file/getImageUrls")
    List<String> getImageUrls(@RequestParam("bizId") String bizId, @RequestParam("bizType") String bizType);

    /**
     * 按业务id和业务类型获取对应图片URL，限定1个业务id1张图片
     *
     * @param ids     业务id
     * @param bizType 业务类型
     * @return 业务id对应文件URL
     */
    @PostMapping("/file/getImageUrlsByIds")
    Map<String, String> getImageUrlsByIds(@RequestBody Collection<String> ids, @RequestParam("bizType") String bizType);

    @PostMapping("/file/getImagePathsByIds")
    Map<String, String> getImagePathsByIds(@RequestBody Collection<String> ids,
        @RequestParam("bizType") String bizType);

    /**
     * 按业务id和业务类型获取对应图片URL，限定1个业务id1张图片
     *
     * @param ids     业务id
     * @param bizType 业务类型
     * @return 业务id对应文件URL
     */
    @PostMapping("/file/getImagesUrlsByIds")
    Map<String, List<String>> getImagesUrlsByIds(@RequestBody Collection<String> ids,
        @RequestParam("bizType") String bizType);

    /**
     * 获取用户头像列表
     */
    @GetMapping("/file/getUserAvatars")
    List<UserAvatarsDTO> getUserAvatars(@RequestParam("userIds") List<String> userIds);


    /**
     * 用于引入资源时防止图片找不到，复制一份
     *
     * @param sourceBizId 资源库id 如：试卷库id
     * @param targetBizId 引入资源库的考试、练习或其他的id
     * @param bizType     图片类型 如题目选项图片
     */
    @GetMapping("/file/copySameBizImage")
    void copySameBizImage(@RequestParam("sourceBizId") String sourceBizId,
        @RequestParam("targetBizId") String targetBizId, @RequestParam("bizType") String bizType);

    /**
     * 用于引入资源时防止图片找不到，复制一份
     *
     * @param list 要复制的数据
     */
    @PostMapping("/file/copySameBizImageList")
    void copySameBizImageList(@RequestBody List<CopyFileDTO> list);

    /**
     * 理论我们的图片表根据bizId就可以查询出对应的数据
     * <p>
     * 这里直接根据 bizId 查询并复制文件引用
     * <p>
     * 该方法不会在对象存储中新生成一份文件,只是生成一条数据库数据(不同 id 引用同一个文件 path)
     *
     * @param bizIdMap 以源id (sourceBizId) 作为 key,目标id (targetBizId) 作为 value
     */
    @PostMapping("/file/copySameBizImageNotType")
    void copySameBizImageNotType(@RequestBody Map<String, String> bizIdMap);

    /**
     * 用于引入资源时防止文件找不到，复制一份
     *
     * @param sourceBizId 资源库id 如：试卷库id
     * @param targetBizId 引入资源库的考试、练习或其他的id
     * @param bizType     文件类型 如题目选项视频
     */
    @GetMapping("/file/copySameBizFile")
    void copySameBizFile(@RequestParam("sourceBizId") String sourceBizId,
        @RequestParam("targetBizId") String targetBizId, @RequestParam("bizType") String bizType);

    /**
     * 用于引入资源时防止文件找不到，复制一份
     *
     * @param list 要复制的数据
     */
    @GetMapping("/file/copySameBizFileList")
    void copySameBizFileList(@RequestBody List<CopyFileDTO> list);


    /**
     * 用于引入资源时防止文件找不到，复制一份
     *
     * @param sourceBizId 资源库id 如：试卷库id
     * @param sourceType  原文件类型
     * @param targetBizId 入资源库的考试、练习或其他的id
     * @param targetType  新文件类型
     */
    @GetMapping("/file/copySameFile")
    void copySameFile(@RequestParam("sourceBizId") String sourceBizId, @RequestParam("sourceType") String sourceType,
        @RequestParam("targetBizId") String targetBizId, @RequestParam("targetType") String targetType);

    /**
     * 不同业务之间复制图片，如：考试题目的图片复制到课程中
     *
     * @param sourceBizId   源业务id 如：考试题目id
     * @param sourceBizType 源业务类型 如： 考试题目图片
     * @param targetBizId   目标业务id 如：课程id
     * @param targetBizType 目标业务类型 如：课程封面图片
     */
    @GetMapping("/file/copyDifferentBizImage")
    void copyDifferentBizImage(@RequestParam("sourceBizId") String sourceBizId,
        @RequestParam("sourceBizType") String sourceBizType, @RequestParam("targetBizId") String targetBizId,
        @RequestParam("targetBizType") String targetBizType);


    /**
     * 根据关联模块和附件类别筛选文件
     *
     * @param categoryId   关联模块Id
     * @param categoryType 关联模块
     * @param isAdjunct    是否为附件
     * @return 文件
     */
    @GetMapping("/file/getFileByCategoryTypeAndIsAdjunct")
    NamePath getFileByCategoryTypeAndIsAdjunct(@RequestParam("categoryId") String categoryId,
        @RequestParam("categoryType") String categoryType, @RequestParam("isAdjunct") Integer isAdjunct);


    /**
     * 不同业务之间复制文件，如：考试题目的文件复制到课程中
     *
     * @param sourceBizId   源业务id 如：考试题目id
     * @param sourceBizType 源业务类型 如： 考试题目文件
     * @param targetBizId   目标业务id 如：课程id
     * @param targetBizType 目标业务类型 如：课程封面文件
     */
    @GetMapping("/file/copyDifferentBizFile")
    void copyDifferentBizFile(@RequestParam("sourceBizId") String sourceBizId,
        @RequestParam("sourceBizType") String sourceBizType, @RequestParam("targetBizId") String targetBizId,
        @RequestParam("targetBizType") String targetBizType);

    /**
     * 上传目录
     *
     * @param source 本地目录
     * @param target 目标目录
     */
    @PostMapping("/file/uploadDir")
    void uploadDir(@RequestParam("source") String source, @RequestParam("target") String target);

    /**
     * 根据关联模块id获取文件
     *
     * @param categoryId 关联模块Id
     * @return
     */
    @GetMapping("/file/getFileByCategoryId")
    NamePath getFileByCategoryId(@RequestParam("categoryId") String categoryId);

    /**
     * 根据关联模块id获取文件
     *
     * @param categoryId 关联模块Id
     * @param isSource   是否源文件
     * @param isAdjunct  是否附件
     * @return
     */
    @GetMapping("/file/getFileByCategoryIdAndIsSourceAndIsAdjunct")
    NamePath getFileByCategoryIdAndIsSourceAndIsAdjunct(@RequestParam("categoryId") String categoryId,
        @RequestParam("isSource") Integer isSource, @RequestParam("isAdjunct") Integer isAdjunct);


    /**
     * 根据文件path获取分享访问地址
     */
    @GetMapping("/file/getFileViewUrl")
    String getFileViewUrl(@RequestParam("filePath") String filePath);

    /**
     * 根据关联模块ids批量获取文件
     *
     * @param categoryIds
     * @return
     */
    @PostMapping("/file/getFileMapByCategoryIds")
    Map<String, NamePath> getFileMapByCategoryIds(@RequestBody Collection<String> categoryIds);

    /**
     * 根据资源id修改资源所属类型
     *
     * @param categoryId
     * @param categoryType
     */
    @GetMapping("/file/editFileCategoryType")
    void editFileCategoryType(@RequestParam("categoryId") String categoryId,
        @RequestParam("categoryType") String categoryType);

    /**
     * 根据关联模块ids和类型精确批量获取源文件
     *
     * @param categoryIds
     * @return
     */
    @PostMapping("/file/getSourceFileMapByCategoryIds")
    Map<String, NamePath> getSourceFileMapByCategoryIds(@RequestBody Collection<String> categoryIds);

    /**
     * 根据id和类型以及文件名模糊分页查询
     *
     * @param fileQuery
     * @return
     */
    @PostMapping("/file/getFileByFileQuery")
    PageInfo<FileListDTO> getFileByFileQuery(@RequestBody FileQuery fileQuery);

    /**
     * 修改文件名
     *
     * @param id
     * @param fileName
     */
    @PostMapping("/file/updateFileNameById")
    void updateFileNameById(@RequestParam("id") String id, @RequestParam("fileName") String fileName);

    /**
     * 按业务id和业务类型获取对应图片URL，限定1个业务id1张图片
     *
     * @param ids     业务id
     * @param bizType 业务类型
     * @return 业务id对应文件URL
     */
    @PostMapping("/file/getFileUrlByIds")
    Map<String, String> getFileUrlByIds(@RequestBody Set<String> ids, @RequestParam("bizType") String bizType);

    /**
     * 自定义压缩文件
     *
     * @param zipDownloadDTO 压缩文件下载对象
     */
    @PostMapping("/file/compressorFileList")
    void compressorFileList(@RequestParam("bizId") String bizId, @RequestParam("bizType") String bizType,
        @RequestBody ZipDownloadDTO zipDownloadDTO);

    /**
     * 培训项目作业压缩打包下载
     *
     * @param zipDownloadDTO 培训项目作业压缩文件下载对象
     */
    @PostMapping("/file/compressorTrainHomeWorkFileList")
    void compressorTrainHomeWorkFileList(@RequestParam("bizId") String bizId, @RequestParam("bizType") String bizType,
        @RequestBody TrainHomeWorkZipDownloadDTO zipDownloadDTO);

    /**
     * 自定义压缩文件，包含多文件情况
     *
     * @param bizId
     * @param bizType
     * @param zipDownloadDTO
     */
    @PostMapping("/file/compressorFilesList")
    void compressorFilesList(@RequestParam("bizId") String bizId, @RequestParam("bizType") String bizType,
        @RequestBody ZipDownloadDTO zipDownloadDTO);

    /**
     * 获取文件媒体信息
     *
     * @param path
     * @return
     */
    @GetMapping("/file/getMineInfo")
    MimeInfoDTO getMineInfo(@RequestParam("path") String path);

    /**
     * 复制数据库数据，不复制文件
     *
     * @param sourceBizId 源BizId
     * @param bizId
     * @param bizType
     */
    @PostMapping("/file/saveImagesToDb")
    void saveImagesToDb(@RequestParam("sourceBizId") String sourceBizId, @RequestParam("bizId") String bizId,
        @RequestParam("bizType") String bizType);

    /**
     * 复制数据库数据，不复制文件
     *
     * @param copyFileDTO 复制文件dto
     */
    @PostMapping("/file/copyImagesToDb")
    void copyImagesToDb(@RequestBody Collection<CopyFileDTO> copyFileDTO);


    /**
     * 复制数据库数据，不复制文件
     *
     * @param copyFileDTO 复制文件dto
     */
    @PostMapping("/file/copyFilesToDb")
    void copyFilesToDb(@RequestBody Collection<CopyFileDTO> copyFileDTO);

    /**
     * 复制数据库数据，不复制文件
     *
     * @param sourceBizId
     * @param bizId
     * @param bizType
     */
    @PostMapping("/file/saveFilesToDb")
    void saveFilesToDb(@RequestParam("sourceBizId") String sourceBizId, @RequestParam("bizId") String bizId,
        @RequestParam("bizType") String bizType);

    /**
     * 判断文件是否存在
     *
     * @param currentPath
     * @return
     */
    @GetMapping("/file/fileExists")
    boolean fileExists(@RequestParam("currentPath") String currentPath);

    /**
     * 文件下载
     *
     * @param url 下载地址
     * @return 文件
     */
    @GetMapping("/file/downLoad")
    File downLoadFile(@RequestParam("url") String url);

    @GetMapping("/file/getFileText")
    String getFileText(@RequestParam("url") String url) throws FileNotFoundException;

    /**
     * 压缩文件
     *
     * @param filePaths   要压缩的文件列表
     * @param zipFileName 压缩包名称
     * @return 压缩后的文件信息
     */
    @PostMapping("/file/compressorFiles")
    String compressorFiles(@RequestBody Collection<String> filePaths, @RequestParam("zipFileName") String zipFileName,
        @RequestParam("isImage") boolean isImage);

    /**
     * 压缩文件
     *
     * @param filePathMap 要压缩的文件列表Map(key为上传路径，value为下载路径)
     * @param zipFileName 压缩包名称
     * @return 压缩后的文件信息
     */
    @PostMapping("/file/compressorFilesMap")
    void compressorFilesMap(@RequestBody Map<String, String> filePathMap,
        @RequestParam("zipFileName") String zipFileName, @RequestParam("bizId") String bizId);

    /**
     * 上传图片
     *
     * @param multipartFile 图片
     * @param name          图片名
     * @return 图片路径
     */
    @PostMapping("/file/uploadImg")
    String uploadImg(@RequestPart(name = "file") MultipartFile multipartFile, @RequestParam("name") String name);

    @GetMapping("/file/certRelate/waterMark")
    void createCertRelateWaterMarkImg(@RequestBody CertRelateWaterMarkImgInfoDTO certRelateWaterMarkImgInfoDTO)
        throws IOException;


    /**
     * 根据关联模块id,业务类型,是否原文件获取文件信息
     *
     * @param bizId    业务id
     * @param bizType  业务类型
     * @param isSource 是否是原文件
     * @return
     */
    @GetMapping("/file/getFileNamePathInfo")
    NamePath getFileNamePathInfo(@RequestParam("bizId") String bizId, @RequestParam("bizType") String bizType,
        @RequestParam(value = "isSource", required = false) Boolean isSource);

    /**
     * 用新的课件id复制课件记录
     *
     * @param coursewareId     源课件id
     * @param copyCoursewareId 复制课件id
     * @return
     */
    @GetMapping("/file/copyCoursewareFileRecord")
    void copyCoursewareFileRecord(@RequestParam("coursewareId") String coursewareId,
        @RequestParam("copyCoursewareId") String copyCoursewareId);

    /**
     * 按业务id和业务类型获取对应图片URL，限定1个业务id1张图片
     *
     * @param ids 业务id
     * @return 业务id对应文件URL
     */
    @PostMapping("/file/getImageUrlByIds")
    Map<String, String> getImageUrlByIds(@RequestBody Collection<String> ids);

    @GetMapping("/file/getTemplateDir")
    String getTemplateDir();

    /**
     * 按业务id和业务类型获取对应主文件
     *
     * @param ids     业务id
     * @param bizType 业务类型
     * @return 业务id对应文件URL
     */
    @PostMapping("/file/getMainFileUrlByIds")
    Map<String, String> getMainFileUrlByIds(@RequestBody Set<String> ids, @RequestParam("bizType") String bizType);

    /**
     * 重置转码课件
     *
     * @desc 将转码课件的文件地址修改为源文件地址
     */
    @PostMapping("/file/resetTransFile")
    void resetTransFile(@RequestBody List<String> ids, @RequestParam("bizType") String bizType);

    /**
     * 是否存在附件
     *
     * @param categoryIds 业务id
     * @param bizType     业务类型
     * @return
     */
    @GetMapping("/file/isExistsAttach")
    boolean isExistsAttach(@RequestBody Collection<String> categoryIds, @RequestParam("bizType") String bizType);

    /**
     * 是否存在图片
     *
     * @param categoryIds 业务id
     * @param bizType     业务类型
     * @return
     */
    @GetMapping("/file/isExistsImages")
    boolean isExistsImages(@RequestBody Collection<String> categoryIds, @RequestParam("bizType") String bizType);

    /**
     * 根据主题引用访问地址
     */
    @GetMapping("/file/getReferenceViewUrl")
    String getReferenceViewUrl(@RequestParam("filePath") String filePath);

    /**
     * 编辑保存删除单张照片一条龙
     */
    @PostMapping("/file/saveOrUpdatePicList")
    void saveOrUpdatePicList(@RequestBody SaveOrUpdatePicListDTO saveOrUpdatePicListDTO);

    @PostMapping("/file/copySameBizFileNotType")
    void copySameBizFileNotType(@RequestBody Map<String, String> newPageIdMap);

    @PostMapping("/file/getFilesById")
    List<NamePath> getFilesById(@RequestBody Collection<String> ids);

    /**
     * 更新files isSource
     *
     * @param ids
     * @param isSource
     */
    @PostMapping("/file/updateIsSourceById")
    void updateIsSourceById(@RequestBody Collection<String> ids, @RequestParam("isSource") Integer isSource);

    /**
     * 获取视频支持的清晰度
     *
     * @param categoryId 业务id
     * @return 清晰度 url 列表
     */
    @GetMapping("/file/getVideoClarity")
    List<VideoClarityDTO> getVideoClarity(@RequestParam("categoryId") String categoryId);

    /**
     * 压缩批量zip文件
     *
     * @param zipDownloadList
     * @return
     */
    @PostMapping("/file/compressorZipList")
    void compressorZipList(@RequestParam("bizId") String bizId, @RequestParam("bizType") String bizType,
        @RequestParam("fileName") String fileName, @RequestBody List<ZipDownloadDTO> zipDownloadList);

    /**
     * 是否存在文件
     *
     * @param categoryIds 业务id
     * @param bizType     业务类型
     * @return
     */
    @GetMapping("/file/isExistsFile")
    boolean isExistsFile(@RequestBody Collection<String> categoryIds, @RequestParam("bizType") String bizType);

    /**
     * 保存、更新多个文件(删除不存在的，保留有ID的)
     *
     * @param bizId   业务id
     * @param bizType 业务类型
     * @return {@link SaveFileDTO}
     */
    @PostMapping("/file/saveOrUpdateFileList")
    List<SaveFileDTO> saveOrUpdateFileList(@RequestParam("bizId") String bizId, @RequestParam("bizType") String bizType,
        @RequestBody List<NamePath> list);

    /**
     * 按业务ids和业务类型获取图片List<NamePath> 客户端投票功能
     *
     * @param bizId   业务id
     * @param bizType 业务类型
     * @return 文件URL
     */
    @PostMapping("/file/getVoteImageFileNamePathsByBizIds")
    List<NamePath> getVoteImageFileNamePathsByBizIds(@RequestBody Collection<String> bizId,
        @RequestParam("bizType") String bizType);

    /**
     * 添加PK赛音乐图片
     */
    @PostMapping("/file/insertPkMusicImages")
    void insertPkMusicImages(@RequestParam("categoryId") String categoryId,
        @RequestParam("categoryType") String categoryType, @RequestParam("type") String type);

    /**
     * 删除单张图片
     *
     * @param idName 图片名称
     */
    @PutMapping("/file/deleteOnePic")
    void deleteOnePic(@RequestParam("idName") String idName);

    /**
     * 只保存业务数据，没有上传文件处理。
     *
     * @param bizId   业务id
     * @param bizType 业务类型
     * @param name    图片名称
     * @param url     注意最后是url不是path，把url直接存path里了，取了直接用
     */
    @GetMapping("/file/onlySaveBizData")
    void onlySaveBizData(@RequestParam("bizId") String bizId, @RequestParam("bizType") String bizType,
        @RequestParam("name") String name, @RequestParam("url") String url);

    /**
     * 上传文件
     *
     * @param tempFilePath 临时文件路径
     * @return 正式文件路径
     */
    @PostMapping("/file/uploadFile")
    SaveFileDTO uploadFile(@RequestParam("tempFilePath") String tempFilePath);


    /**
     * 判断文件是否存在 (0KB也属于文件不存在)
     *
     * @param currentPath 文件相对路径
     * @return 是否存在
     */
    @GetMapping("/file/checkFileExists")
    boolean checkFileExists(@RequestParam("currentPath") String currentPath);

    /**
     * 按业务id和业务类型获取对应图片URL，限定1个业务id1张图片
     *
     * @param ids     业务id
     * @param bizType 业务类型
     * @return 业务id对应文件URL
     */
    @PostMapping("/file/getFileUrlByIdsAndType")
    Map<String, String> getFileUrlByIdsAndType(@RequestBody Set<String> ids, @RequestParam("bizType") String bizType);

    /**
     * 上传课件到dify知识库
     *
     * @param cwIdList 课件id列表
     * @return dify返回的fileId
     */
    @PostMapping("/file/uploadCwToDify")
    Map<String, String> uploadCwToDify(@RequestBody List<String> cwIdList);

    /**
     * 批量保存文件
     *
     * @param saveBatchFileCollection 文件集合
     * @return 正式文件路径
     */
    @PostMapping("/file/saveFileBatch")
    Map<String, SaveFileDTO> saveFileBatch(@RequestBody Collection<SaveBatchFileDTO> saveBatchFileCollection);

    /**
     * 保存文本到临时文件
     *
     * @param text 文本
     * @return url
     */
    @PostMapping("/file/saveTextToTempFile")
    String saveTextToTempFile(@RequestBody String text, @RequestParam("fileName") String fileName);

    /**
     * 获取scorm播放地址
     *
     * @return url
     */
    @GetMapping("/file/getScormPlayUrl")
    String getScormPlayUrl();


    /**
     * 获取自定义物理路径
     *
     * @return {@link String }
     */
    @GetMapping("/file/getCustomPhysicalPath")
    String getCustomPhysicalPath(@RequestParam("fileName") String fileName, @RequestParam("bizType") String bizType);

    /**
     * 获取公有读文件URL地址
     *
     * @param path 文件路径
     * @return URL地址
     */
    @GetMapping("/file/getPublicReadFileUrl")
    String getPublicReadFileUrl(@RequestParam("path") String path);

    /**
     * 根据文件Ids物理删除文件（数据库记录 + 对象存储）
     *
     * @param bizIds 资源id集合
     */
    @PostMapping("/file/deletePhysicalByBizIds")
    void deletePhysicalFileByIds(@RequestBody Collection<String> bizIds);

    /**
     * 保存公有读文件
     *
     * @param bizId        业务id
     * @param bizType      业务类型
     * @param name         文件名
     * @param tempFilePath 临时文件路径
     * @return 正式文件路径
     */
    @PostMapping("/file/savePublicReadImage")
    SaveFileDTO savePublicReadImage(@RequestParam("bizId") String bizId, @RequestParam("bizType") String bizType,
        @RequestParam("name") String name, @RequestParam("tempFilePath") String tempFilePath);

    /**
     * 编辑保存删除单张公有读照片一条龙
     *
     * @param bizId        业务id
     * @param bizType      业务类型
     * @param name         文件名
     * @param tempFilePath 临时文件路径
     * @param isInsert     true 新增单张照片， false 新增更新删除单张照片
     */
    @PostMapping("/file/saveOrUpdatePublicReadOnePic")
    void saveOrUpdatePublicReadOnePic(@RequestParam("bizId") String bizId, @RequestParam("bizType") String bizType,
        @RequestParam("name") String name, @RequestParam("tempFilePath") String tempFilePath,
        @RequestParam("isInsert") Boolean isInsert);

    /**
     * 处理旧公有读时转码文件
     */
    @PostMapping("/file/dealWithPublicReadPath")
    void dealWithPublicReadPath(@RequestBody List<String> filePathList);
}
