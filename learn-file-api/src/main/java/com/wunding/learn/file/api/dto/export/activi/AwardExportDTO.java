package com.wunding.learn.file.api.dto.export.activi;

import com.alibaba.excel.annotation.ExcelProperty;
import com.wunding.learn.file.api.dto.export.converter.WhetherConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/11/01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(name = "AwardPageDTO", description = "奖品列表数据对象")
public class AwardExportDTO {

    @Schema(description = "奖品名称")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.activi.AwardExportDTO.name}")
    private String name;

    @Schema(description = "奖品分类")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.activi.AwardExportDTO.category}")
    private String category;

    @Schema(description = "消耗金币数量")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.activi.AwardExportDTO.consumeNum}")
    private BigDecimal consumeNum;

    @Schema(description = "总数量")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.activi.AwardExportDTO.total}")
    private BigDecimal total;

    @Schema(description = "已兑换数量")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.activi.AwardExportDTO.exchangedNum}")
    private BigDecimal exchangedNum;

    @Schema(description = "剩余数量")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.activi.AwardExportDTO.availableNum}")
    private BigDecimal availableNum;

    @Schema(description = "每人限兑数量")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.activi.AwardExportDTO.limitCount}")
    private BigDecimal limitCount;

    @Schema(description = "有效开始时间")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.activi.AwardExportDTO.beginTime}")
    private Date beginTime;

    @Schema(description = "有效结束时间")
    @ExcelProperty("${com.wunding.learn.file.api.dto.export.activi.AwardExportDTO.endTime}")
    private Date endTime;

    @Schema(description = "是否上架 0-否 1-是")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.activi.AwardExportDTO.isOnSale}",  converter = WhetherConverter.class)
    private Integer isOnSale;

}
