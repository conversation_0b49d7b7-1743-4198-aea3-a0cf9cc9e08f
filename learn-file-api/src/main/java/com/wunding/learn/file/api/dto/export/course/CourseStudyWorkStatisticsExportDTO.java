package com.wunding.learn.file.api.dto.export.course;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/5/12 9:53
 */
@Data
@Schema(name = "CourseStudyStatisticsExportDTO", description = "课程学习统计对象")
public class CourseStudyWorkStatisticsExportDTO implements Serializable {

    @Schema(description = "组织名称")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.course.CourseStudyWorkStatisticsExportDTO.orgName}")
    private String orgName;

    @Schema(description = "下发人数")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.course.CourseStudyWorkStatisticsExportDTO.viewLimitUserCount}")
    private Long viewLimitUserCount;

    @Schema(description = "未学人数")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.course.CourseStudyWorkStatisticsExportDTO.unLearnUserCount}")
    private Long unLearnUserCount;

    @Schema(description = "学习中人数")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.course.CourseStudyWorkStatisticsExportDTO.learningUserCount}")
    private Long learningUserCount;

    @Schema(description = "已学完人数")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.course.CourseStudyWorkStatisticsExportDTO.learnedUserCount}")
    private Long learnedUserCount;

}
