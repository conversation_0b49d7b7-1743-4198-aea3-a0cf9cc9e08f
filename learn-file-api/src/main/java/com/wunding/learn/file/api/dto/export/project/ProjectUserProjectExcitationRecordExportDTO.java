package com.wunding.learn.file.api.dto.export.project;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(name = "ProjectUserProjectExcitationRecordExportDTO", description = "导出用户项目激励获取记录对象")
public class ProjectUserProjectExcitationRecordExportDTO {

    /**
     * 序号
     */
    @Schema(description = "序号")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.ProjectUserProjectExcitationRecordExportDTO.no}")
    private Integer no;

    /**
     * 事件名称
     */
    @Schema(description = "事件名称")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.ProjectUserProjectExcitationRecordExportDTO.name}")
    private String name;

    /**
     * 数值
     */
    @Schema(description = "值")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.ProjectUserProjectExcitationRecordExportDTO.value}")
    private BigDecimal value;

}
