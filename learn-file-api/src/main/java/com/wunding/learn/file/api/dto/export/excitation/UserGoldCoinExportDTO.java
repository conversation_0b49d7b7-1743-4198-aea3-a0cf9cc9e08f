package com.wunding.learn.file.api.dto.export.excitation;

import com.alibaba.excel.annotation.ExcelProperty;
import com.wunding.learn.file.api.dto.export.ExportDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <p>
 * 外部课程资源导出对象
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">z<PERSON><PERSON><PERSON><PERSON></a>
 * @date 2023/8/7 15:02
 */
@Data
@Schema(name = "UserGoldCoinExportDTO", description = "用户金币导出数据对象")
public class UserGoldCoinExportDTO extends ExportDTO {

    @Schema(description = "用户类别[0:内部用户/格学院会员 1:外部用户 2:机构用户]")
    @ExcelProperty("用户类别")
    private String type;

    @Schema(description = "用户姓名")
    @ExcelProperty("用户姓名")
    private String name;

    @Schema(description = "用户手机号")
    @ExcelProperty("用户手机号")
    private String phone;

    @Schema(description = "账号")
    @ExcelProperty("账号")
    private String loginName;

    @Schema(description = "机构名称")
    @ExcelProperty("机构名称")
    private String memberOrgName;

    @Schema(description = "部门名称")
    @ExcelProperty("部门名称")
    private String orgName;

    @Schema(description = "金币数量")
    @ExcelProperty("金币数量")
    private BigDecimal num;

    @Schema(description = "可兑换金币数量")
    @ExcelProperty("可兑换金币数量")
    private BigDecimal convertibleNum;
}
