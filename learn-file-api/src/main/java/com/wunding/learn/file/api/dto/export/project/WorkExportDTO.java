package com.wunding.learn.file.api.dto.export.project;

import com.alibaba.excel.annotation.ExcelProperty;
import com.wunding.learn.file.api.dto.export.converter.PublishConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;

/**
 * @Author: yanglequn
 * @Date: 2022/11/07 10:45
 */
@Data
@Schema(name = "WorkExportDTO", description = "导出作业管理列表对象")
public class WorkExportDTO {

    @Schema(description = "作业名称")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.WorkExportDTO.workName}")
    private String workName;

    @Schema(description = "作业说明")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.WorkExportDTO.workExplain}")
    private String workExplain;

    @Schema(description = "完成时间")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.WorkExportDTO.finishTime}")
    private Date finishTime;

    @Schema(description = "是否发布：0-未发布，1-发布")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.WorkExportDTO.isPublish}",  converter = PublishConverter.class)
    private Integer isPublish;

    @Schema(description = "发布时间")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.WorkExportDTO.publishTime}")
    private Date publishTime;
}
