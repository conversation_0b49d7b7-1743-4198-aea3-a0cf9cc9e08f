package com.wunding.learn.file.api.dto.export.project;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(name = "ProjectUserProjectCoursewareStudyDetailExportDTO", description = "导出用户学习项目课时统计对象")
public class ProjectUserProjectCoursewareStudyDetailExportDTO {

    /**
     * 序号
     */
    @Schema(description = "序号")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.ProjectUserProjectCoursewareStudyDetailExportDTO.no}")
    private Integer no;


    /**
     * 课程任务名称
     */
    @Schema(description = "课程任务名称")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.ProjectUserProjectCoursewareStudyDetailExportDTO.taskName}")
    private String taskName;

    /**
     * 课程已学时长
     */
    @Schema(description = "已学课时")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.ProjectUserProjectCoursewareStudyDetailExportDTO.duration}")
    private BigDecimal duration;

    /**
     * 课程总计时长
     */
    @Schema(description = "总计课时")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.ProjectUserProjectCoursewareStudyDetailExportDTO.totalDuration}")
    private BigDecimal totalDuration;

}
