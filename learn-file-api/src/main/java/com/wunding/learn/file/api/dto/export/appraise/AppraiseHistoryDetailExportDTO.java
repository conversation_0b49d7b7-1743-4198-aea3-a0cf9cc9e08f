package com.wunding.learn.file.api.dto.export.appraise;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.NumberFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * @tiele AppraiseHistoryDetailVo
 * @projectName devlop-learn
 * @Date 2021/12/7  AppraiseHistoryDetailVo
 */
@Data
@Schema(name = "AppraiseHistoryDetailExportDTO", description = "导出打分历史DTO")
public class AppraiseHistoryDetailExportDTO {

    /**
     * 评委姓名
     */
    @Schema(description = "评委姓名")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.appraise.AppraiseHistoryDetailExportDTO.refereeName}")
    private String refereeName;

    /**
     * 评委账号
     */
    @Schema(description = "账户")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.appraise.AppraiseHistoryDetailExportDTO.loginName}")
    private String loginName;

    /**
     * 评委权重
     */
    @NumberFormat("#.##%")
    @Schema(description = "权重")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.appraise.AppraiseHistoryDetailExportDTO.weight}")
    private Integer weight;

    /**
     * 评委部门
     */
    @Schema(description = "部门")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.appraise.AppraiseHistoryDetailExportDTO.levelPathName}")
    private String levelPathName;

    /**
     * 评委评分 取出 保留两位小数
     */
    @Schema(description = "评委评分 取出 保留两位小数")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.appraise.AppraiseHistoryDetailExportDTO.scored}")
    private String scored;

    /**
     * 评委加权评分 取出 保留两位小数
     */
    @Schema(description = "评委加权评分 取出 保留两位小数")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.appraise.AppraiseHistoryDetailExportDTO.weightedScored}")
    private String weightedScored;

    /**
     * 评分时间
     */
    @Schema(description = "评价时间")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.appraise.AppraiseHistoryDetailExportDTO.createTime}")
    private String createTime;

}
