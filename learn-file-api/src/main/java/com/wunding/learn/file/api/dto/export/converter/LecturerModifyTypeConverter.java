package com.wunding.learn.file.api.dto.export.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.wunding.learn.common.i18n.util.I18nUtil;
import java.util.Objects;

/**
 * 导出 在途/出库 转换
 *
 * <AUTHOR>
 * @date 2022/10/24
 */
public class LecturerModifyTypeConverter implements Converter<Integer> {


    @Override
    public Class<?> supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }


    @Override
    public Integer convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty,
        GlobalConfiguration globalConfiguration) throws Exception {
        Integer res = null;
        String cellText = cellData.getStringValue();
        for (ModifyType lecturerModifyType : ModifyType.values()) {
            if (Objects.equals(cellText, lecturerModifyType.getText())) {
                res = lecturerModifyType.getValue();
                break;
            }
        }
        return res;
    }

    @Override
    public WriteCellData<?> convertToExcelData(Integer value, ExcelContentProperty contentProperty,
        GlobalConfiguration globalConfiguration) throws Exception {
        String res = "";
        for (ModifyType lecturerModifyType : ModifyType.values()) {
            if (Objects.equals(value, lecturerModifyType.getValue())) {
                res = I18nUtil.getMessage(lecturerModifyType.getText());
                break;
            }
        }
        return new WriteCellData<>(res);
    }

    enum ModifyType {

        /**
         * 待晋级
         */
        FOR_PROMOTION(1, "待晋级"),

        /**
         * 待降级
         */
        TO_DOWNGRADE(2, "待降级"),
        /**
         * 待出库
         */
        FOR_OUTBOUND(3, "待出库"),

        /**
         * 手工调整
         */
        ADJUST_MANUALLY(4, "手工调整"),

        /**
         * 已晋级
         */
        HAVE_TO_QUALIFY(5, "已晋级"),

        /**
         * 已降级
         */
        HAVE_THE_DROP(6, "已降级"),

        /**
         * 已出库
         */
        HAVE_OUTBOUND(7, "已出库"),

        /**
         * 入库
         */
        PUT_IN_STORAGE(8, "入库"),

        /**
         * 再入库
         */
        THEN_PUT_IN_STORAGE(9, "再入库"),

        /**
         * 晋级失败
         */
        QUALIFY_FAIL(10, "晋级失败"),

        /**
         * 降级失败
         */
        DROP_FAIL(11, "降级失败"),

        /**
         * 出库失败
         */
        OUTBOUND_FAIL(12, "出库失败"),
        ;

        /**
         * java中的值
         */
        private final Integer value;
        /**
         * excel中的文本
         */
        private final String text;

        ModifyType(Integer value, String text) {
            this.value = value;
            this.text = text;
        }

        public Integer getValue() {
            return value;
        }

        public String getText() {
            return text;
        }
    }
}
