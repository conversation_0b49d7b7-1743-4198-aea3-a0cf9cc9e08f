package com.wunding.learn.file.api.dto.export.emigrated;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: TeamExportDTO
 * @projectName: mlearn
 * @description：
 * @date 2022/11/10 14:42
 */
@Data
@Schema(name = "TeamExportDTO", description = "导出团队列表对象")
public class TeamExportDTO {

    /**
     * 团队名称
     */
    @Schema(description = "团队名称")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.emigrated.TeamExportDTO.name}")
    private String name;

    /**
     * 人数
     */
    @Schema(description = "人数")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.emigrated.TeamExportDTO.memberCount}")
    private Integer memberCount;

}
