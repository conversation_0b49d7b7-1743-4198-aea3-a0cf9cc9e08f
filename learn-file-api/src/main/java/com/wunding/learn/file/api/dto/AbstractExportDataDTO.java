package com.wunding.learn.file.api.dto;

import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.bean.BaseEntity;
import com.wunding.learn.common.bean.BasePageQuery;
import com.wunding.learn.common.util.json.JsonUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public abstract class AbstractExportDataDTO<T, E> implements IExportDataDTO {

    /**
     * 查询对象存在两个分页查询参数的基类  其实现中根据具体的分页查询对象的基类 选择其中一个
     */
    protected BaseEntity queryDTO;
    protected BasePageQuery pageQueryDTO;

    protected AbstractExportDataDTO(BaseEntity queryDTO) {
        this.queryDTO = queryDTO;
    }

    protected AbstractExportDataDTO(BasePageQuery pageQueryDTO) {
        this.pageQueryDTO = pageQueryDTO;
    }

    protected AbstractExportDataDTO() {

    }

    @Override
    public List<Map<String, Object>> getData(Integer pageNo, Integer pageSize) {
        T bean = getBean();
        initQueryDto(pageNo, pageSize);
        assert bean != null;
        PageInfo<E> pageInfo = getPageInfo();
        List<Map<String, Object>> res = new ArrayList<>();
        for (E dto : pageInfo.getList()) {
            beforeCreateBeanMap(dto);
            Map<String, Object> map = JsonUtil.parseObjectToMap(dto);
            afterCreateBeanMap(map);
            res.add(map);
        }
        return res;
    }

    private void initQueryDto(Integer pageNo, Integer pageSize) {
        if (Objects.nonNull(queryDTO)) {
            queryDTO.setPageNo(pageNo);
            queryDTO.setPageSize(pageSize);
            queryDTO.setExport(true);
        }

        if (Objects.nonNull(pageQueryDTO)) {
            pageQueryDTO.setPageNo(pageNo);
            pageQueryDTO.setPageSize(pageSize);
            pageQueryDTO.setExport(true);
        }

    }

    /**
     * 获取查询的bean
     *
     * @return
     */
    protected abstract T getBean();

    /**
     * 获取查询的pageInfo
     *
     * @return
     */
    protected abstract PageInfo<E> getPageInfo();

    /**
     * 便于扩展， 在转beanMap前处理
     *
     * @param dto
     * @return
     */
    protected void beforeCreateBeanMap(E dto) {
    }

    /**
     * 转beanMap后处理
     *
     * @param map
     * @return
     */
    protected void afterCreateBeanMap(Map<String, Object> map) {
    }

}
