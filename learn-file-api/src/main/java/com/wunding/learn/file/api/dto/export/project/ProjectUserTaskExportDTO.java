package com.wunding.learn.file.api.dto.export.project;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(name = "ProjectUserTaskExportDTO", description = "导出用户项目任务完成情况统计对象")
public class ProjectUserTaskExportDTO {

    /**
     * 序号
     */
    @Schema(description = "序号")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.ProjectUserTaskExportDTO.no}")
    private Integer no;


    /**
     * 项目阶段名称
     */
    @Schema(description = "阶段")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.ProjectUserTaskExportDTO.phaseName}")
    private String phaseName;

    /**
     * 任务类型
     */
    @Schema(description = "任务类型")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.ProjectUserTaskExportDTO.taskType}")
    private String taskType;

    /**
     * 任务名称
     */
    @Schema(description = "任务名称")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.ProjectUserTaskExportDTO.taskName}")
    private String taskName;

    /**
     * 用户参与的状态: 尚未进行 0, 正在进行 1, 已经完成 2 （1 这种中间状态，如果某项活动没有这种逻辑，则只存在0 和 2）
     */
    @Schema(description = "完成状态")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.ProjectUserTaskExportDTO.userStatus}")
    private String userStatus;

    /**
     * 任务执行结果（仅针对 实操 和 考试显示对应的结果） 如：80分，通过
     */
    @Schema(description = "结果")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.ProjectUserTaskExportDTO.taskResult}")
    private String taskResult;





}
