package com.wunding.learn.file.api.constant;

import com.wunding.learn.file.api.dto.export.ExampleBusinessExportDTO;
import com.wunding.learn.file.api.dto.export.ExampleCategoryExportDTO;
import com.wunding.learn.file.api.dto.export.ExampleCommentExportDTO;
import com.wunding.learn.file.api.dto.export.ExampleLibExportDTO;
import com.wunding.learn.file.api.dto.export.ExpertLibExportDTO;
import com.wunding.learn.file.api.dto.export.ExportDTO;
import com.wunding.learn.file.api.dto.export.LibraryOperationRecordExportDTO;
import com.wunding.learn.file.api.dto.export.PostBanUserExportDTO;
import com.wunding.learn.file.api.dto.export.PostExportDTO;
import com.wunding.learn.file.api.dto.export.PostSectionExpertExportDTO;
import com.wunding.learn.file.api.dto.export.PostSectionExportDTO;
import com.wunding.learn.file.api.dto.export.SharedLibraryExportDTO;
import com.wunding.learn.file.api.dto.export.UserPosterShareExportDTO;
import com.wunding.learn.file.api.dto.export.ability.AbilityDictionaryExportDTO;
import com.wunding.learn.file.api.dto.export.ability.AbilityModeExportDTO;
import com.wunding.learn.file.api.dto.export.activi.AwardCategoryExportDTO;
import com.wunding.learn.file.api.dto.export.activi.AwardExchangeRecordExportDTO;
import com.wunding.learn.file.api.dto.export.activi.AwardExportDTO;
import com.wunding.learn.file.api.dto.export.activi.AwardRedeemRecordExportDTO;
import com.wunding.learn.file.api.dto.export.activi.LotteryRecordExportDTO;
import com.wunding.learn.file.api.dto.export.activi.MailTemplateExportDTO;
import com.wunding.learn.file.api.dto.export.activi.ProjectSignListExportDTO;
import com.wunding.learn.file.api.dto.export.activi.PushManageExportDTO;
import com.wunding.learn.file.api.dto.export.activi.SignListExportDTO;
import com.wunding.learn.file.api.dto.export.activi.SignStatExportDTO;
import com.wunding.learn.file.api.dto.export.activi.TrainSignStatExportDTO;
import com.wunding.learn.file.api.dto.export.activi.VoteContentExportDTO;
import com.wunding.learn.file.api.dto.export.activi.VoteDetailExportDTO;
import com.wunding.learn.file.api.dto.export.activi.VoteExportDTO;
import com.wunding.learn.file.api.dto.export.activi.VoteStatisticsExportDTO;
import com.wunding.learn.file.api.dto.export.analysis.AccessStatDayDTO;
import com.wunding.learn.file.api.dto.export.analysis.CompetitionAnswerRecordStatExportDTO;
import com.wunding.learn.file.api.dto.export.analysis.CourseAgreeDetailStatAnalysisExportDTO;
import com.wunding.learn.file.api.dto.export.analysis.CourseLearnedStatAnalysisExportDTO;
import com.wunding.learn.file.api.dto.export.analysis.CourseStateStatAnalysisExportDTO;
import com.wunding.learn.file.api.dto.export.analysis.CourseStudyCourseLearnUserExportDTO;
import com.wunding.learn.file.api.dto.export.analysis.CoursewareLearnDetailStatAnalysisExportDTO;
import com.wunding.learn.file.api.dto.export.analysis.CoursewareLearnStatAnalysisExportDTO;
import com.wunding.learn.file.api.dto.export.analysis.ExamStateStatAnalysisExportDTO;
import com.wunding.learn.file.api.dto.export.analysis.ExamStateStatPartAnalysisExportDTO;
import com.wunding.learn.file.api.dto.export.analysis.ExchangeRecordStatAnalysisExportDTO;
import com.wunding.learn.file.api.dto.export.analysis.ExcitationCollectStatAnalysisExportDTO;
import com.wunding.learn.file.api.dto.export.analysis.GldTradeStatAnalysisExportDTO;
import com.wunding.learn.file.api.dto.export.analysis.InfoStatAnalysisExportDTO;
import com.wunding.learn.file.api.dto.export.analysis.InsideLecturerCategoryStatAnalysisExportDTO;
import com.wunding.learn.file.api.dto.export.analysis.InsideLecturerDepartmentStatAnalysisExportDTO;
import com.wunding.learn.file.api.dto.export.analysis.ItemVisitRecordExportDTO;
import com.wunding.learn.file.api.dto.export.analysis.LearnRecordCourseStatAnalysisExportDTO;
import com.wunding.learn.file.api.dto.export.analysis.LearnRecordExamStatAnalysisExportDTO;
import com.wunding.learn.file.api.dto.export.analysis.LearnRecordFaceProjectStatAnalysisExportDTO;
import com.wunding.learn.file.api.dto.export.analysis.LearnRecordInfoStatAnalysisExportDTO;
import com.wunding.learn.file.api.dto.export.analysis.LearnRecordProjectStatAnalysisExportDTO;
import com.wunding.learn.file.api.dto.export.analysis.LearnRecordStatAnalysisExportDTO;
import com.wunding.learn.file.api.dto.export.analysis.LearnRecordSurveyStatAnalysisExportDTO;
import com.wunding.learn.file.api.dto.export.analysis.LearnRecordTrainStatAnalysisExportDTO;
import com.wunding.learn.file.api.dto.export.analysis.LecturerIntegralExportDTO;
import com.wunding.learn.file.api.dto.export.analysis.OnlineUserDayStatAnalysisExportDTO;
import com.wunding.learn.file.api.dto.export.analysis.OnlineUserDeptStatAnalysisExportDTO;
import com.wunding.learn.file.api.dto.export.analysis.OnlineUserMonthStatAnalysisExportDTO;
import com.wunding.learn.file.api.dto.export.analysis.OrgLearnStatExportDTO;
import com.wunding.learn.file.api.dto.export.analysis.ScoreRankStatAnalysisExportDTO;
import com.wunding.learn.file.api.dto.export.analysis.StatSearchKeyExportDTO;
import com.wunding.learn.file.api.dto.export.analysis.StudentUploadCoursewareStatAnalysisExportDTO;
import com.wunding.learn.file.api.dto.export.analysis.SysTemTagStatAnalysisExportDTO;
import com.wunding.learn.file.api.dto.export.analysis.TimeRegionStatAnalysisExportDTO;
import com.wunding.learn.file.api.dto.export.analysis.TrainPlanStatAnalysisExportDTO;
import com.wunding.learn.file.api.dto.export.analysis.UserExcitationRecordStatAnalysisExportDTO;
import com.wunding.learn.file.api.dto.export.analysis.UserIntegralDetailExportDTO;
import com.wunding.learn.file.api.dto.export.analysis.UserIntegralDetailStatAnalysisExportDTO;
import com.wunding.learn.file.api.dto.export.analysis.UserIntegralStatAnalysisExportDTO;
import com.wunding.learn.file.api.dto.export.apply.ApplyExportDTO;
import com.wunding.learn.file.api.dto.export.appraise.AppraiseExportDTO;
import com.wunding.learn.file.api.dto.export.appraise.AppraiseFileType2ExportDTO;
import com.wunding.learn.file.api.dto.export.appraise.AppraiseFileTypeExportDTO;
import com.wunding.learn.file.api.dto.export.appraise.AppraiseHistoryDetailExportDTO;
import com.wunding.learn.file.api.dto.export.appraise.AppraiseProviderExportDTO;
import com.wunding.learn.file.api.dto.export.appraise.AppraiseProviderFileExportDTO;
import com.wunding.learn.file.api.dto.export.appraise.AppraiseRefereeExportDTO;
import com.wunding.learn.file.api.dto.export.appraise.LecturerAppraiseExportDTO;
import com.wunding.learn.file.api.dto.export.appraise.MeetingDetailExportDTO;
import com.wunding.learn.file.api.dto.export.appraise.MeetingProviderExportDTO;
import com.wunding.learn.file.api.dto.export.certification.AssessDepManagerExportDTO;
import com.wunding.learn.file.api.dto.export.certification.AssessDetailsExportDTO;
import com.wunding.learn.file.api.dto.export.certification.AssessEvaluatorListExportDTO;
import com.wunding.learn.file.api.dto.export.certification.AssessProjectExportDTO;
import com.wunding.learn.file.api.dto.export.certification.AssessQuestionDetailExportDTO;
import com.wunding.learn.file.api.dto.export.certification.AssessToolExportDTO;
import com.wunding.learn.file.api.dto.export.certification.AssessUserExportDTO1;
import com.wunding.learn.file.api.dto.export.certification.AssessUserExportDTO2;
import com.wunding.learn.file.api.dto.export.certification.CerDeptTargetReportExportDTO;
import com.wunding.learn.file.api.dto.export.certification.CertWorkTypeDTO;
import com.wunding.learn.file.api.dto.export.certification.CertificationDeptTargetExportDTO;
import com.wunding.learn.file.api.dto.export.certification.CertificationExportDTO;
import com.wunding.learn.file.api.dto.export.certification.CertificationHoldUserExportDTO;
import com.wunding.learn.file.api.dto.export.certification.CertificationLevelExportDTO;
import com.wunding.learn.file.api.dto.export.certification.CertificationRelateDeptExportDTO;
import com.wunding.learn.file.api.dto.export.certification.CertificationRelateExportDTO;
import com.wunding.learn.file.api.dto.export.certification.CertificationRuleExportDTO;
import com.wunding.learn.file.api.dto.export.certification.CertificationSetupExportDTO;
import com.wunding.learn.file.api.dto.export.certification.CertifiedCategoryExportDTO;
import com.wunding.learn.file.api.dto.export.course.AllCourseAuditDTO;
import com.wunding.learn.file.api.dto.export.course.CategoryExportDTO;
import com.wunding.learn.file.api.dto.export.course.CourseCategoryExportDTO;
import com.wunding.learn.file.api.dto.export.course.CourseCommentExportDTO;
import com.wunding.learn.file.api.dto.export.course.CourseExportDTO;
import com.wunding.learn.file.api.dto.export.course.CourseLearnExportDTO;
import com.wunding.learn.file.api.dto.export.course.CourseNoteExportDTO;
import com.wunding.learn.file.api.dto.export.course.CourseStudyCourseStatisticsExportDTO;
import com.wunding.learn.file.api.dto.export.course.CourseStudyCourseUserExportDTO;
import com.wunding.learn.file.api.dto.export.course.CourseStudyDetailExportDTO;
import com.wunding.learn.file.api.dto.export.course.CourseStudyStatisticsExportDTO;
import com.wunding.learn.file.api.dto.export.course.CourseStudyStatisticsUserExportDTO;
import com.wunding.learn.file.api.dto.export.course.CourseSysCategoryExportDTO;
import com.wunding.learn.file.api.dto.export.course.CourseTagCategoryExportDTO;
import com.wunding.learn.file.api.dto.export.course.CourseTagManageExportDTO;
import com.wunding.learn.file.api.dto.export.course.CourseTagStaExportDTO;
import com.wunding.learn.file.api.dto.export.course.CourseWareLearnExportDTO;
import com.wunding.learn.file.api.dto.export.course.CourseWithoutExportDTO;
import com.wunding.learn.file.api.dto.export.course.CoursewarePackageExportDTO;
import com.wunding.learn.file.api.dto.export.course.CoursewareQuestionAnswerRecordExportDTO;
import com.wunding.learn.file.api.dto.export.course.CoursewareStudyDetail2ExportDTO;
import com.wunding.learn.file.api.dto.export.course.CoursewareStudyDetailExportDTO;
import com.wunding.learn.file.api.dto.export.course.LecturerCourseExportDTO;
import com.wunding.learn.file.api.dto.export.course.LecturerCoursewareExportDTO;
import com.wunding.learn.file.api.dto.export.course.MergeCourseExportDTO;
import com.wunding.learn.file.api.dto.export.course.MyApplyCourseAuditDTO;
import com.wunding.learn.file.api.dto.export.course.TrainCourseExportDTO;
import com.wunding.learn.file.api.dto.export.emigrated.CheckpointExportDTO;
import com.wunding.learn.file.api.dto.export.emigrated.EmigratedExportDTO;
import com.wunding.learn.file.api.dto.export.emigrated.NoticeExportDTO;
import com.wunding.learn.file.api.dto.export.emigrated.StatisticalExportDTO;
import com.wunding.learn.file.api.dto.export.emigrated.StylesTemplateExportDTO;
import com.wunding.learn.file.api.dto.export.emigrated.TaskExportDTO;
import com.wunding.learn.file.api.dto.export.emigrated.TaskStatisticalExportDTO;
import com.wunding.learn.file.api.dto.export.emigrated.TeamExportDTO;
import com.wunding.learn.file.api.dto.export.evluation.EvalReplyUser;
import com.wunding.learn.file.api.dto.export.exam.CompetitionSessionUserDetailExportDTO;
import com.wunding.learn.file.api.dto.export.exam.ExamAnalysisQuestionExportDTO;
import com.wunding.learn.file.api.dto.export.exam.ExamAnswerOfStatisticalOrgExportDTO;
import com.wunding.learn.file.api.dto.export.exam.ExamAnswerOfStatisticalPostExportDTO;
import com.wunding.learn.file.api.dto.export.exam.ExamAnswerRecordDetailListExportDTO;
import com.wunding.learn.file.api.dto.export.exam.ExamAnswerUserExportDTO;
import com.wunding.learn.file.api.dto.export.exam.ExamCategoryExportDTO;
import com.wunding.learn.file.api.dto.export.exam.ExamCompetitionExportDTO;
import com.wunding.learn.file.api.dto.export.exam.ExamCompetitionSessionAnswerRecordeExportDTO;
import com.wunding.learn.file.api.dto.export.exam.ExamCompetitionSessionStatExportDTO;
import com.wunding.learn.file.api.dto.export.exam.ExamCompetitionSessionUserExportDTO;
import com.wunding.learn.file.api.dto.export.exam.ExamCompetitionUserAnswerRankExportDTO;
import com.wunding.learn.file.api.dto.export.exam.ExamCompetitionUserScoreRankExportDTO;
import com.wunding.learn.file.api.dto.export.exam.ExamCorrectExamListExportDTO;
import com.wunding.learn.file.api.dto.export.exam.ExamCorrectRecordExportDTO;
import com.wunding.learn.file.api.dto.export.exam.ExamEmployeeResultsDetailExportDTO;
import com.wunding.learn.file.api.dto.export.exam.ExamListExportDTO;
import com.wunding.learn.file.api.dto.export.exam.ExamOrgStatisticExportDTO;
import com.wunding.learn.file.api.dto.export.exam.ExamQuestionExportDTO;
import com.wunding.learn.file.api.dto.export.exam.ExamStatisticExportDTO;
import com.wunding.learn.file.api.dto.export.exam.ExamUserByBizRecordExportDTO;
import com.wunding.learn.file.api.dto.export.exam.ExamUserByStatisticsExportDTO;
import com.wunding.learn.file.api.dto.export.exam.ExerciseExportDTO;
import com.wunding.learn.file.api.dto.export.exam.SchemaExportDTO;
import com.wunding.learn.file.api.dto.export.excitation.UserGoldCoinBillExportDTO;
import com.wunding.learn.file.api.dto.export.excitation.UserGoldCoinExportDTO;
import com.wunding.learn.file.api.dto.export.excitation.UserGottenTargetValueDetailExportDTO;
import com.wunding.learn.file.api.dto.export.flow.AllResourceAuditDTO;
import com.wunding.learn.file.api.dto.export.flow.FlowAuditRecordsExportDTO;
import com.wunding.learn.file.api.dto.export.flow.MyApplyResourceAuditDTO;
import com.wunding.learn.file.api.dto.export.info.InfoExportDTO;
import com.wunding.learn.file.api.dto.export.info.ResourceCommentExportDTO;
import com.wunding.learn.file.api.dto.export.leaflet.LeafletExportDTO;
import com.wunding.learn.file.api.dto.export.lecturer.InsideLecturerExportDTO;
import com.wunding.learn.file.api.dto.export.lecturer.LecturerCategoryExportDTO;
import com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthByCourseExportDTO;
import com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthByLecturerCourseExportDTO;
import com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthByLecturerExportDTO;
import com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthTeachExportDTO;
import com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseListExportDTO;
import com.wunding.learn.file.api.dto.export.lecturer.LecturerDemotionConfigExportDTO;
import com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAssessExportDTO;
import com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAuditExportDTO;
import com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationExportDTO;
import com.wunding.learn.file.api.dto.export.lecturer.LecturerExportDTO;
import com.wunding.learn.file.api.dto.export.lecturer.LecturerLevelExportDTO;
import com.wunding.learn.file.api.dto.export.lecturer.LecturerModifyRecordExportDTO;
import com.wunding.learn.file.api.dto.export.lecturer.LecturerRemovalConfigExportDTO;
import com.wunding.learn.file.api.dto.export.lecturer.LecturerTeachDetailExportDTO;
import com.wunding.learn.file.api.dto.export.lecturer.LecturerTeachStatisticDetailPageDTO;
import com.wunding.learn.file.api.dto.export.lecturer.LecturerTrainExportDTO;
import com.wunding.learn.file.api.dto.export.lecturer.LecturerTrainStatisticExportDTO;
import com.wunding.learn.file.api.dto.export.lecturer.LecturerUpgradeConfigExportDTO;
import com.wunding.learn.file.api.dto.export.lecturer.NotWorkdayExportDTO;
import com.wunding.learn.file.api.dto.export.library.CourseWareLibExportDTO;
import com.wunding.learn.file.api.dto.export.library.EvaluationLibExportDTO;
import com.wunding.learn.file.api.dto.export.library.ExamLibExportDTO;
import com.wunding.learn.file.api.dto.export.library.ExerciseLibExportDTO;
import com.wunding.learn.file.api.dto.export.library.ExerciseQuestionExportDTO;
import com.wunding.learn.file.api.dto.export.library.KnowledgeBaseCategoryExportDTO;
import com.wunding.learn.file.api.dto.export.library.LibQuestionExportDTO;
import com.wunding.learn.file.api.dto.export.library.MaterialLibExportDTO;
import com.wunding.learn.file.api.dto.export.library.ResearchFieldExportDTO;
import com.wunding.learn.file.api.dto.export.library.SurveyCategoryExportDTO;
import com.wunding.learn.file.api.dto.export.library.SurveyLibExportDTO;
import com.wunding.learn.file.api.dto.export.library.SysCategoryExportDTO;
import com.wunding.learn.file.api.dto.export.library.TagCategoryExportDTO;
import com.wunding.learn.file.api.dto.export.library.TestPaperLibExportDTO;
import com.wunding.learn.file.api.dto.export.library.TestPaperQuestionExportDTO;
import com.wunding.learn.file.api.dto.export.live.LiveExportDTO;
import com.wunding.learn.file.api.dto.export.live.LiveStaticExportDTO;
import com.wunding.learn.file.api.dto.export.live.LiveVodExportDTO;
import com.wunding.learn.file.api.dto.export.march.MarchCheckpointExportDTO;
import com.wunding.learn.file.api.dto.export.march.MarchCountCommentExportDTO;
import com.wunding.learn.file.api.dto.export.march.MarchExportDTO;
import com.wunding.learn.file.api.dto.export.march.MarchNoticeExportDTO;
import com.wunding.learn.file.api.dto.export.march.MarchPostExportDTO;
import com.wunding.learn.file.api.dto.export.march.MarchTaskExportDTO;
import com.wunding.learn.file.api.dto.export.march.MarchTeamExportDTO;
import com.wunding.learn.file.api.dto.export.medal.MedalUserRelationExportDTO;
import com.wunding.learn.file.api.dto.export.members.TrainCoOrganizerExportDTO;
import com.wunding.learn.file.api.dto.export.members.TrainMembersExportDTO;
import com.wunding.learn.file.api.dto.export.payment.PaymentOrderExportDTO;
import com.wunding.learn.file.api.dto.export.payment.PaymentOrgOrderMemberExportDTO;
import com.wunding.learn.file.api.dto.export.plan.FormTemplateManageDTO;
import com.wunding.learn.file.api.dto.export.plan.PlanCategoryExportDTO;
import com.wunding.learn.file.api.dto.export.plan.PlanExportDTO;
import com.wunding.learn.file.api.dto.export.plan.PlanStatisticExportDTO;
import com.wunding.learn.file.api.dto.export.plan.StatAnalysisPlanExecuteMonthExportDTO;
import com.wunding.learn.file.api.dto.export.plan.StatAnalysisPlanExecuteYearExportDTO;
import com.wunding.learn.file.api.dto.export.plan.TrainProjectStatisticExportDTO;
import com.wunding.learn.file.api.dto.export.post.CountCommentExportDTO;
import com.wunding.learn.file.api.dto.export.project.ApplyUserExportDTO;
import com.wunding.learn.file.api.dto.export.project.ClassroomExportDTO;
import com.wunding.learn.file.api.dto.export.project.CourseTaskProjectExportDTO;
import com.wunding.learn.file.api.dto.export.project.CourseTaskProjectStatisticTaskDetailExportDTO;
import com.wunding.learn.file.api.dto.export.project.DefaultApplyExportDTO;
import com.wunding.learn.file.api.dto.export.project.FaceProjectExportDTO;
import com.wunding.learn.file.api.dto.export.project.FaceProjectHomeWorkExportDTO;
import com.wunding.learn.file.api.dto.export.project.FaceProjectHomeWorkWithSourceExportDTO;
import com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticExportDTO;
import com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailExportDTO;
import com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailWithSourceExportDTO;
import com.wunding.learn.file.api.dto.export.project.FormTemplateExportDTO;
import com.wunding.learn.file.api.dto.export.project.HomeWorkExportDTO;
import com.wunding.learn.file.api.dto.export.project.InvoiceExportDTO;
import com.wunding.learn.file.api.dto.export.project.LearnMapUserResourcesStatisticExportDTO;
import com.wunding.learn.file.api.dto.export.project.LearnProjectStatisticExportDTO;
import com.wunding.learn.file.api.dto.export.project.MentorExportDTO;
import com.wunding.learn.file.api.dto.export.project.PositionExportDTO;
import com.wunding.learn.file.api.dto.export.project.PracticalOperationUserBySuperviseExportDTO;
import com.wunding.learn.file.api.dto.export.project.PracticalOperationUserExportDTO;
import com.wunding.learn.file.api.dto.export.project.PracticalSuperviseUserExportDTO;
import com.wunding.learn.file.api.dto.export.project.ProjectCompletionExportDTO;
import com.wunding.learn.file.api.dto.export.project.ProjectCompletionStatisticExportDTO;
import com.wunding.learn.file.api.dto.export.project.ProjectEvaluationExportDTO;
import com.wunding.learn.file.api.dto.export.project.ProjectFixedCycleExportDTO;
import com.wunding.learn.file.api.dto.export.project.ProjectFixedCycleStatisticTaskDetailExportDTO;
import com.wunding.learn.file.api.dto.export.project.ProjectFixedDateExportDTO;
import com.wunding.learn.file.api.dto.export.project.ProjectHoldStatisticExportDTO;
import com.wunding.learn.file.api.dto.export.project.ProjectJoiningStaticExportDTO;
import com.wunding.learn.file.api.dto.export.project.ProjectLecturerTeachDetailExportDTO;
import com.wunding.learn.file.api.dto.export.project.ProjectStatisticByOrgExportDTO;
import com.wunding.learn.file.api.dto.export.project.ProjectStatisticByProjectExportDTO;
import com.wunding.learn.file.api.dto.export.project.ProjectStatisticByTypeUserExportDTO;
import com.wunding.learn.file.api.dto.export.project.ProjectStatisticByTypeUserLimitExportDTO;
import com.wunding.learn.file.api.dto.export.project.ProjectStatisticByUserExportDTO;
import com.wunding.learn.file.api.dto.export.project.ProjectStatisticExportDTO;
import com.wunding.learn.file.api.dto.export.project.ProjectStatisticJoinUserExportDTO;
import com.wunding.learn.file.api.dto.export.project.ProjectStatisticLecturerExportDTO;
import com.wunding.learn.file.api.dto.export.project.ProjectStatisticTaskDetailExportDTO;
import com.wunding.learn.file.api.dto.export.project.ProjectStatisticUserExportDTO;
import com.wunding.learn.file.api.dto.export.project.ProjectUserProjectCoursewareStudyDetailExportDTO;
import com.wunding.learn.file.api.dto.export.project.ProjectUserProjectExcitationRecordExportDTO;
import com.wunding.learn.file.api.dto.export.project.ProjectUserTaskExportDTO;
import com.wunding.learn.file.api.dto.export.project.QuickProjectExportDTO;
import com.wunding.learn.file.api.dto.export.project.QuickProjectStatisticTaskDetailExportDTO;
import com.wunding.learn.file.api.dto.export.project.SignUserExportDTO;
import com.wunding.learn.file.api.dto.export.project.StatisticOrgCompleteUserDetailIsFinishExportDTO;
import com.wunding.learn.file.api.dto.export.project.StatisticOrgCompleteUserDetailNotFinishExportDTO;
import com.wunding.learn.file.api.dto.export.project.StatisticOrgLearnRankExportDTO;
import com.wunding.learn.file.api.dto.export.project.StatisticPersonRankExportDTO;
import com.wunding.learn.file.api.dto.export.project.StatisticTeamLearnRankExportDTO;
import com.wunding.learn.file.api.dto.export.project.TrainHomeWorkExportDTO;
import com.wunding.learn.file.api.dto.export.project.TrainingPlanExportDTO;
import com.wunding.learn.file.api.dto.export.project.WorkExportDTO;
import com.wunding.learn.file.api.dto.export.reading.BookExperienceCommentExportDTO;
import com.wunding.learn.file.api.dto.export.reading.BookExperienceExportDTO;
import com.wunding.learn.file.api.dto.export.reading.BookExperienceReportExportDTO;
import com.wunding.learn.file.api.dto.export.reading.BookExperienceStarExportDTO;
import com.wunding.learn.file.api.dto.export.reading.BookManageExportDTO;
import com.wunding.learn.file.api.dto.export.reading.BookStudyProgressExportDTO;
import com.wunding.learn.file.api.dto.export.reading.ReadingExportDTO;
import com.wunding.learn.file.api.dto.export.reading.ReadingSignExportDTO;
import com.wunding.learn.file.api.dto.export.reading.TaskFinishExportDTO;
import com.wunding.learn.file.api.dto.export.reading.UserRankReadingExportDTO;
import com.wunding.learn.file.api.dto.export.recruiting.LecturerRecruitingExportDTO;
import com.wunding.learn.file.api.dto.export.recruiting.RecruitingAssistantExportDTO;
import com.wunding.learn.file.api.dto.export.recruiting.RecruitingAuditSurveyDetailExportDTO;
import com.wunding.learn.file.api.dto.export.recruiting.RecruitingExportDTO;
import com.wunding.learn.file.api.dto.export.recruiting.RecruitingMaterialExportDTO;
import com.wunding.learn.file.api.dto.export.recruiting.RecruitingMaterialRule2ExportDTO;
import com.wunding.learn.file.api.dto.export.recruiting.RecruitingMaterialRuleExportDTO;
import com.wunding.learn.file.api.dto.export.recruiting.RecruitingParticipationRecordExportDTO;
import com.wunding.learn.file.api.dto.export.special.SpecialCategoryExportDTO;
import com.wunding.learn.file.api.dto.export.special.SpecialCompletionExportDTO;
import com.wunding.learn.file.api.dto.export.special.SpecialFixedCycleExportDTO;
import com.wunding.learn.file.api.dto.export.special.SpecialFixedDateExportDTO;
import com.wunding.learn.file.api.dto.export.special.SpecialLabelExportDTO;
import com.wunding.learn.file.api.dto.export.special.StatisticFixedCycleTaskDetailExportDTO;
import com.wunding.learn.file.api.dto.export.special.StatisticTaskDetailExportDTO;
import com.wunding.learn.file.api.dto.export.survey.SurveyAnalysisQuestionExportDTO;
import com.wunding.learn.file.api.dto.export.survey.SurveyExportDTO;
import com.wunding.learn.file.api.dto.export.survey.SurveyRecordDetailExportDTO;
import com.wunding.learn.file.api.dto.export.sync.TaskRecordDetailExportDTO;
import com.wunding.learn.file.api.dto.export.sync.TaskRecordExportDTO;
import com.wunding.learn.file.api.dto.export.sys.BizLogExportDTO;
import com.wunding.learn.file.api.dto.export.sys.ClientVersionExportDTO;
import com.wunding.learn.file.api.dto.export.sys.DictExportDTO;
import com.wunding.learn.file.api.dto.export.sys.FeedbackExportDTO;
import com.wunding.learn.file.api.dto.export.sys.HomePageConfigExportDTO;
import com.wunding.learn.file.api.dto.export.sys.HomeRouterVisitDetailExportDTO;
import com.wunding.learn.file.api.dto.export.sys.LogExportDTO;
import com.wunding.learn.file.api.dto.export.sys.OrgExportDTO;
import com.wunding.learn.file.api.dto.export.sys.OrgThirdSyncRecordExportDTO;
import com.wunding.learn.file.api.dto.export.sys.PostThirdSyncRecordExportDTO;
import com.wunding.learn.file.api.dto.export.sys.PushMessageExportDTO;
import com.wunding.learn.file.api.dto.export.sys.RoleExportDTO;
import com.wunding.learn.file.api.dto.export.sys.TitleExportDTO;
import com.wunding.learn.file.api.dto.export.sys.UserExportDTO;
import com.wunding.learn.file.api.dto.export.sys.UserThirdSyncRecordExportDTO;
import com.wunding.learn.file.api.dto.export.sys.UserVisitItemRecordExportDTO;
import com.wunding.learn.file.api.dto.export.sys.UserWhiteExportDTO;
import com.wunding.learn.file.api.dto.export.taskscore.TaskScoreExportDTO;
import com.wunding.learn.file.api.dto.export.train.ActivityPracticeRecordExportDTO;
import com.wunding.learn.file.api.dto.export.train.LearnMapActivityProgressExportDTO;
import com.wunding.learn.file.api.dto.export.train.LearnMapExecExportDTO;
import com.wunding.learn.file.api.dto.export.train.LearnMapExportDTO;
import com.wunding.learn.file.api.dto.export.train.LearnMapLearnDetailExportDTO;
import com.wunding.learn.file.api.dto.export.train.LearnMapLearnDetailNonePhaseExportDTO;
import com.wunding.learn.file.api.dto.export.train.LearnMapLearnUserFinishExportDTO;
import com.wunding.learn.file.api.dto.export.train.LearnMapLearnUserPartExportDTO;
import com.wunding.learn.file.api.dto.export.train.LearnMapProgressExportDTO;
import com.wunding.learn.file.api.dto.export.train.LearningTargetCompletionDetailsIdentityExportDTO;
import com.wunding.learn.file.api.dto.export.train.LearningTargetCompletionDetailsOrgExportDTO;
import com.wunding.learn.file.api.dto.export.train.LearningTargetManageIdentityExportDTO;
import com.wunding.learn.file.api.dto.export.train.LearningTargetManageOrgExportDTO;
import com.wunding.learn.file.api.dto.export.train.LearningTargetManageUserProcessExportDTO;
import com.wunding.learn.file.api.dto.export.train.LearningTargetRefUserListExportDTO;
import com.wunding.learn.file.api.dto.export.train.SupplierExportDTO;
import com.wunding.learn.file.api.dto.export.train.SupplierFileExportDTO;
import com.wunding.learn.file.api.dto.export.train.TargetUserAchievementDetailExportDTO;
import com.wunding.learn.file.api.dto.export.train.TrainActivityExportDTO;
import com.wunding.learn.file.api.dto.export.train.TrainExportDTO;
import com.wunding.learn.file.api.dto.export.train.TrainLecturerTeachExportDTO;
import com.wunding.learn.file.api.dto.export.train.TrainOrgLearnExportDTO;
import com.wunding.learn.file.api.dto.export.train.TrainPersonRankExportDTO;
import com.wunding.learn.file.api.dto.export.train.TrainProjectExportDTO;
import com.wunding.learn.file.api.dto.export.train.TrainTaskDetailExportDTO;
import com.wunding.learn.file.api.dto.export.train.TrainWithOutAssistExportDTO;
import com.wunding.learn.file.api.dto.export.train.TrainWithOutExportDTO;
import com.wunding.learn.file.api.dto.export.train.TrainWithoutApplyDetailExportDTO;
import com.wunding.learn.file.api.dto.export.train.TrainWithoutApplyExportDTO;
import com.wunding.learn.file.api.dto.export.train.TrainWithoutResultExportDTO;
import com.wunding.learn.file.api.dto.export.train.TrainWithoutStatisticsExportDTO;
import com.wunding.learn.file.api.dto.export.user.IdentityPostSystemDataExportDTO;
import com.wunding.learn.file.api.dto.export.user.IdentityUserInfoExportDTO;
import com.wunding.learn.file.api.dto.export.user.MemberCardExportDTO;
import com.wunding.learn.file.api.dto.export.user.MemberExportDTO;
import com.wunding.learn.file.api.dto.export.user.MemberOrgExportDTO;
import com.wunding.learn.file.api.dto.export.user.PermissionConfigExportDTO;
import com.wunding.learn.file.api.dto.export.user.PermissionRouterExportDTO;
import com.wunding.learn.file.api.dto.export.user.SysTagListExportDTO;
import com.wunding.learn.file.api.dto.export.user.UserIdentityInfoExportDTO;
import com.wunding.learn.file.api.dto.export.user.UserIdentityListExportDTO;
import com.wunding.learn.file.api.dto.export.user.VisibleViewLimitUserExportDTO;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/03/15
 */
public enum ExportBizType {

    /**
     * 课程管理列表
     */
    Course(CourseExportDTO.class),

    /**
     * 讲师课程明细列表
     */
    LecturerCourseDetail(LecturerCourseExportDTO.class),

    /**
     * 讲师课件明细列表
     */
    LecturerCourseWareDetail(LecturerCoursewareExportDTO.class),

    /**
     * 学习明细列表
     */
    CourseStudyDetail(CourseStudyDetailExportDTO.class),
    /**
     * 课件学习明细列表
     */
    CoursewareStudyDetail(CoursewareStudyDetailExportDTO.class),
    /**
     * 课件学习明细列表
     */
    CoursewareStudyDetail2(CoursewareStudyDetail2ExportDTO.class),

    /**
     * 课程评论管理列表
     */
    CourseComment(CourseCommentExportDTO.class),
    /**
     * 班级团队列表
     */
    FLOW_AUDIT_RECORDS(FlowAuditRecordsExportDTO.class),
    /**
     * 课程共享库管理列表
     */
    SharedLibrary(SharedLibraryExportDTO.class),
    /**
     * 学员上传课件列表
     */
    CoursewarePackage(CoursewarePackageExportDTO.class),
    /**
     * 合并课件到课程列表
     */
    MergeCourse(MergeCourseExportDTO.class),
    /**
     * 学员课件学习明细列表
     */
    CourseWareLearn(CourseWareLearnExportDTO.class),
    /**
     * 学员课程学习明细列表
     */
    CourseLearn(CourseLearnExportDTO.class),
    /**
     * 课程分类管理列表
     */
    CourseCategory(CourseCategoryExportDTO.class),
    /**
     * 课程标签管理列表
     */
    CourseTagManage(CourseTagManageExportDTO.class),
    /**
     * 课程标签分类管理列表
     */
    CourseSysCategory(CourseTagCategoryExportDTO.class),
    /**
     * 课程标签统计列表
     */
    CourseTagSta(CourseTagStaExportDTO.class),

    /**
     * 课程管理-学习统计-按部门
     */
    CourseStudyStatistics(CourseStudyStatisticsExportDTO.class),

    /**
     * 课程管理-学习统计-人员明细
     */
    CourseStudyStatisticsUser(CourseStudyStatisticsUserExportDTO.class),

    /**
     * 课程管理-学习统计-按课程
     */
    CourseStudyCourseStatistics(CourseStudyCourseStatisticsExportDTO.class),

    /**
     * 课程管理-学习统计-按课程-人员明细学习
     */
    CourseStudyCourseLearnUser(CourseStudyCourseLearnUserExportDTO.class),

    /**
     * 外部课程列表
     */
    CourseWithout(CourseWithoutExportDTO.class),

    /**
     * 课程管理-学习统计-按课程-人员明细
     */
    CourseStudyCourseUser(CourseStudyCourseUserExportDTO.class),
    /**
     * 考试管理列表
     */
    Exam(ExamListExportDTO.class),
    /**
     * 考题题目明细
     */
    ExamQuestion(ExamQuestionExportDTO.class),
    /**
     * 改卷管理列表
     */
    ExamCorrect(ExamCorrectExamListExportDTO.class),
    /**
     * 答题记录列表
     */
    ExamCorrectRecord(ExamCorrectRecordExportDTO.class),

    /**
     * 考试情况统计
     */
    ExamUserByBizRecord(ExamUserByBizRecordExportDTO.class),

    /**
     * 考试按部门统计
     */
    ExamOrgStatistic(ExamOrgStatisticExportDTO.class),

    /**
     * 考试按考试统计
     */
    ExamStatistic(ExamStatisticExportDTO.class),

    /**
     * 考试情况统计-用户
     */
    ExamUserByStatistics(ExamUserByStatisticsExportDTO.class),

    /**
     * 讲师授课明细数据
     */
    ProjectLecturerTeachDetail(ProjectLecturerTeachDetailExportDTO.class),

    /**
     * 讲师培训统计明细数据
     */
    LecturerTeachStatisticDetail(ProjectLecturerTeachDetailExportDTO.class),

    /**
     * 组卷方案列表
     */
    SchemaList(SchemaExportDTO.class),
    /**
     * 练习管理列表
     */
    Exercise(ExerciseExportDTO.class),
    /**
     * 员工考试成绩明细列表
     */
    ExamEmployeeResultsDetail(ExamEmployeeResultsDetailExportDTO.class),
    /**
     * 考试答题统计按部门统计
     */
    ExamAnswerOfStatisticalOrg(ExamAnswerOfStatisticalOrgExportDTO.class),
    /**
     * 考试答题统计按岗位统计
     */
    ExamAnswerOfStatisticalPost(ExamAnswerOfStatisticalPostExportDTO.class),
    /**
     * 考试答题记录
     */
    ExamAnswerRecord(ExportDTO.class),
    /**
     * 考试答题记录明细
     */
    ExamAnswerRecordDetail(ExamAnswerRecordDetailListExportDTO.class),
    /**
     * 答题分析
     */
    ExamAnalysisQuestion(ExamAnalysisQuestionExportDTO.class),
    /**
     * 案例库管理列表
     */
    ExampleLib(ExampleLibExportDTO.class),
    /**
     * 专家库管理列表
     */
    ExpertLib(ExpertLibExportDTO.class),
    /**
     * 案例条线管理列表
     */
    ExampleBusiness(ExampleBusinessExportDTO.class),
    /**
     * 案例评论管理列表
     */
    ExampleComment(ExampleCommentExportDTO.class),
    /**
     * 案例分类管理列表
     */
    ExampleCategory(ExampleCategoryExportDTO.class),
    /**
     * 案例审核分类管理列表
     */
    ExampleAuditCategory(ExampleCategoryExportDTO.class),

    /**
     * 考试竞赛管理列表
     */
    ExamCompetition(ExamCompetitionExportDTO.class),

    /**
     * 考试竞赛答题排名列表
     */
    ExamCompetitionUserAnswerRank(ExamCompetitionUserAnswerRankExportDTO.class),

    /**
     * 场次列表
     */
    ExamCompetitionSessionUser(ExamCompetitionSessionUserExportDTO.class),

    /**
     * 答题列表
     */
    ExamCompetitionAnswerRecord(ExamCompetitionSessionAnswerRecordeExportDTO.class),

    /**
     * 场次明细
     */
    ExamCompetitionSessionStat(ExamCompetitionSessionStatExportDTO.class),

    /**
     * 考试竞赛得分排名列表
     */
    ExamCompetitionUserScoreRank(ExamCompetitionUserScoreRankExportDTO.class),

    /**
     * 直播管理列表
     */
    Live(LiveExportDTO.class),

    /**
     * 统计列表
     */
    LiveStatic(LiveStaticExportDTO.class),

    ProjectCompletionStatistic(ProjectCompletionStatisticExportDTO.class),

    ProjectUserTask(ProjectUserTaskExportDTO.class),

    ProjectUserProjectExcitationRecord(ProjectUserProjectExcitationRecordExportDTO.class),

    ProjectUserProjectCoursewareStudyDetail(ProjectUserProjectCoursewareStudyDetailExportDTO.class),

    /**
     * 直播回放列表
     */
    LiveVod(LiveVodExportDTO.class),

    /**
     * 话题管理帖子列表
     */
    Post(PostExportDTO.class),
    /**
     * 禁言名单管理列表
     */
    PostBanUser(PostBanUserExportDTO.class),
    /**
     * 话题板块管理列表
     */
    PostSection(PostSectionExportDTO.class),
    /**
     * 话题板块专家管理列表
     */
    PostSectionExpert(PostSectionExpertExportDTO.class),
    /**
     * 话题回帖统计
     */
    PostCountComment(CountCommentExportDTO.class),
    /**
     * 闯关游戏管理列表
     */
    Emigrated(EmigratedExportDTO.class),
    /**
     * 关卡列表
     */
    Checkpoint(CheckpointExportDTO.class),
    /**
     * 闯关任务列表
     */
    CheckpointTask(TaskExportDTO.class),

    /**
     * 样式管理列表
     */
    StylesTemplate(StylesTemplateExportDTO.class),

    /**
     * 闯关应用-公告管理列表
     */
    Notice(NoticeExportDTO.class),

    /**
     * 闯关应用-团队管理列表
     */
    Team(TeamExportDTO.class),

    /**
     * 闯关应用-闯关统计列表
     */
    Statistical(StatisticalExportDTO.class),

    /**
     * 闯关应用-闯关统计-查看明细列表
     */
    TaskStatistical(TaskStatisticalExportDTO.class),

    /**
     * 调研列表
     */
    Info(InfoExportDTO.class),

    /**
     * 评论管理列表
     */
    Comment(ResourceCommentExportDTO.class),

    /**
     * 调研分析
     */
    SurveyAnalysis(SurveyAnalysisQuestionExportDTO.class),

    /**
     * 调研明细
     */
    SurveyRecordDetail(SurveyRecordDetailExportDTO.class),

    /**
     * 分类列表
     */
    Category(CategoryExportDTO.class),

    /**
     * 调研列表
     */
    Survey(SurveyExportDTO.class),

    /**
     * 日期项目列表
     */
    ProjectFixedDate(ProjectFixedDateExportDTO.class),

    /**
     * 周期项目列表
     */
    ProjectFixedCycle(ProjectFixedCycleExportDTO.class),

    /**
     * 快速培训项目列表
     */
    QuickProject(QuickProjectExportDTO.class),

    /**
     * 线上课程学习
     */
    CourseTaskProject(CourseTaskProjectExportDTO.class),

    /**
     * 面授项目
     */
    FaceProject(FaceProjectExportDTO.class),

    /**
     * 面授项目培训统计
     */
    FaceProjectStatistic(FaceProjectStatisticExportDTO.class),

    /**
     * 学习项目培训统计
     */
    LearnProjectStatistic(LearnProjectStatisticExportDTO.class),

    /**
     * 培训项目培训统计
     */
    TrainProjectStatistic(com.wunding.learn.file.api.dto.export.train.TrainProjectStatisticExportDTO.class),

    /**
     * 学员详情列表
     */
    ProjectStatisticUserDetail(ProjectStatisticUserExportDTO.class),

    /**
     * 辅导模板管理列表
     */
    FormTemplate(FormTemplateExportDTO.class),

    /**
     * 岗位发展列表
     */
    Position(PositionExportDTO.class),

    /**
     * 教室列表
     */
    Classroom(ClassroomExportDTO.class),

    /**
     * 培训计划管理列表
     */
    TrainingPlan(TrainingPlanExportDTO.class),

    /**
     * 导师管理列表
     */
    ProjectMentor(MentorExportDTO.class),

    /**
     * 讲师管理列表
     */
    ProjectLecturer(com.wunding.learn.file.api.dto.export.project.LecturerExportDTO.class),

    /**
     * 讲师管理列表
     */
    LecturerProjectStatistic(com.wunding.learn.file.api.dto.export.project.LecturerProjectStatisticExportDTO.class),

    /**
     * 报名管理用户列表
     */
    ProjectApplyUser(ApplyUserExportDTO.class),

    /**
     * 签到管理用户列表
     */
    ProjectSignUser(SignUserExportDTO.class),

    /**
     * 话题管理列表
     */
    ProjectPost(PostExportDTO.class),

    /**
     * 作业管理列表
     */
    ProjectWork(WorkExportDTO.class),

    /**
     * 培训项目作业完成情况统计列表
     */
    TrainHomeWork(TrainHomeWorkExportDTO.class),
    /**
     * 评估统计分析结果
     */
    ProjectEvaluation(WorkExportDTO.class),

    /**
     * 评估统计分析结果
     */
    NEW_PROJECT_EVALUATION(ProjectEvaluationExportDTO.class),

    /**
     * 评估人员列表
     */
    EvalReplyUserList(EvalReplyUser.class),

    /**
     * 辅导结果预览doc下载
     */
    FormTemplateColumn(WorkExportDTO.class),

    /**
     * 学习项目作业
     */
    ProjectHomeWork(HomeWorkExportDTO.class),

    /**
     * 面授项目作业
     */
    FaceProjectHomeWork(FaceProjectHomeWorkExportDTO.class),

    /**
     * 面授项目作业(带来源)
     */
    FaceProjectHomeWorkWithSource(FaceProjectHomeWorkWithSourceExportDTO.class),

    /**
     * 学习项目结业情况
     */
    ProjectCompletion(ProjectCompletionExportDTO.class),

    /**
     * 日期学习项目明细统计
     */
    ProjectStatisticTaskDetail(ProjectStatisticTaskDetailExportDTO.class),

    /**
     * 导出面授项目任务明细统计 (带来源字段)
     */
    ProjectStatisticTaskDetailWithSource(FaceProjectStatisticTaskDetailWithSourceExportDTO.class),

    /**
     * 导出面授项目任务明细统计
     */
    FaceProjectStatisticTaskDetail(FaceProjectStatisticTaskDetailExportDTO.class),

    /**
     * 周期学习项目明细统计
     */
    ProjectFixedCycleStatisticTaskDetail(ProjectFixedCycleStatisticTaskDetailExportDTO.class),

    /**
     * 快速培训明细统计
     */
    QuickProjectStatisticTaskDetail(QuickProjectStatisticTaskDetailExportDTO.class),

    /**
     * 课程学习任务明细统计
     */
    CourseTaskProjectStatisticTaskDetail(CourseTaskProjectStatisticTaskDetailExportDTO.class),

    /**
     * 学习项目个人学习排名统计
     */
    ProjectStatisticPersonRank(StatisticPersonRankExportDTO.class),

    /**
     * 学习项目-部门完成统计用户详情-已完成
     */
    ProjectStatisticOrgCompleteUserDetailIsFinish(StatisticOrgCompleteUserDetailIsFinishExportDTO.class),

    /**
     * 学习项目-部门完成统计用户详情-未完成
     */
    ProjectStatisticOrgCompleteUserDetailNotFinish(StatisticOrgCompleteUserDetailNotFinishExportDTO.class),

    /**
     * 学习项目团队学习排名统计
     */
    ProjectStatisticTeamLearnRank(StatisticTeamLearnRankExportDTO.class),

    /**
     * 学习项目部门学习排名统计
     */
    ProjectStatisticOrgLearnRank(StatisticOrgLearnRankExportDTO.class),

    /**
     * 培训班统计
     */
    ProjectStatistic(ProjectStatisticExportDTO.class),

    /**
     * 项目统计-按类型
     */
    ProjectStatisticByType(ExportDTO.class),

    /**
     * 项目统计-按类型-用户
     */
    ProjectStatisticByTypeUser(ProjectStatisticByTypeUserExportDTO.class),

    /**
     * 项目统计-按类型-下发用户
     */
    ProjectStatisticByTypeUserLimit(ProjectStatisticByTypeUserLimitExportDTO.class),

    /**
     * 项目统计-按组织-参加用户
     */
    ProjectStatisticByOrgJoinUser(ProjectStatisticJoinUserExportDTO.class),

    /**
     * 项目统计-按项目-参加用户
     */
    ProjectStatisticByProjectJoinUser(ProjectStatisticJoinUserExportDTO.class),

    /**
     * 项目统计-按项目
     */
    ProjectStatisticByProject(ProjectStatisticByProjectExportDTO.class),

    /**
     * 项目统计-按部门
     */
    ProjectStatisticByOrg(ProjectStatisticByOrgExportDTO.class),

    /**
     * 项目统计-按人员
     */
    ProjectStatisticByUser(ProjectStatisticByUserExportDTO.class),

    /**
     * 学习项目举办统计
     */
    ProjectHoldStatistic(ProjectHoldStatisticExportDTO.class),

    /**
     * 学习项目参与统计
     */
    ProjectJoiningStatistic(ProjectJoiningStaticExportDTO.class),

    /**
     * 讲师培训统计
     */
    LecturerTrainStatistic(LecturerTrainStatisticExportDTO.class),

    /**
     * 讲师培训统计ByBiz
     */
    LecturerTrainStatisticByBiz(ExportDTO.class),

    /**
     * 培训班讲师统计
     */
    ProjectStatisticLecturer(ProjectStatisticLecturerExportDTO.class),

    /**
     * 培训班组织统计
     */
    ProjectOrgStatistic(ExportDTO.class),

    /**
     * 日期专题列表
     */
    SpecialFixedDate(SpecialFixedDateExportDTO.class),

    /**
     * 周期专题列表
     */
    SpecialFixedCycle(SpecialFixedCycleExportDTO.class),

    /**
     * 专题标签列表
     */
    SpecialLabel(SpecialLabelExportDTO.class),

    /**
     * 专题分类列表
     */
    SpecialCategory(SpecialCategoryExportDTO.class),

    /**
     * 专题结业情况
     */
    SpecialCompletion(SpecialCompletionExportDTO.class),

    /**
     * 专题明细统计
     */
    SpecialStatisticTaskDetail(StatisticTaskDetailExportDTO.class),

    StatisticFixedCycleTaskDetail(StatisticFixedCycleTaskDetailExportDTO.class),

    /**
     * 讲师管理列表
     */
    Lecturer(LecturerExportDTO.class),

    /**
     * 讲师参与培训列表
     */
    LecturerTrainExportDTO(LecturerTrainExportDTO.class),

    /**
     * 讲师分类设置列表
     */
    LecturerCategory(LecturerCategoryExportDTO.class),

    /**
     * 讲师等级设置列表
     */
    LecturerLevel(LecturerLevelExportDTO.class),

    /**
     * 讲师异动记录列表
     */
    LecturerModifyRecord(LecturerModifyRecordExportDTO.class),

    /**
     * 非工作日列表
     */
    NotWorkday(NotWorkdayExportDTO.class),

    /**
     * 授课记录审核列表
     */
    LecturerExaminationAudit(LecturerExaminationAuditExportDTO.class),

    /**
     * 规则配置_晋级规则列表
     */
    LecturerUpgradeConfig(LecturerUpgradeConfigExportDTO.class),

    /**
     * 规则配置_降级规则列表
     */
    LecturerDemotionConfig(LecturerDemotionConfigExportDTO.class),

    /**
     * 规则配置_出库规则列表
     */
    LecturerRemovalConfig(LecturerRemovalConfigExportDTO.class),

    /**
     * 授课课时信息明细列表
     */
    LecturerExamination(LecturerExaminationExportDTO.class),

    /**
     * 授课评估信息明细列表
     */
    LecturerExaminationAssess(LecturerExaminationAssessExportDTO.class),

    /**
     * 线下授课明细列表
     */
    LecturerTeachDetail(LecturerTeachDetailExportDTO.class),

    /**
     * 讲师授课明细表
     */
    LecturerTeachStaticDetail(LecturerTeachStatisticDetailPageDTO.class),

    /**
     * 讲师统计(按部门)-讲师管理列表
     */
    InsideLecturer(InsideLecturerExportDTO.class),

    /**
     * 证书管理列表
     */
    Certification(CertificationExportDTO.class),

    /**
     * 认证体系管理列表
     */
    CertificationSetup(CertificationSetupExportDTO.class),

    /**
     * 证书等级管理列表
     */
    CertificationLevel(CertificationLevelExportDTO.class),

    /**
     * 发证规则列表
     */
    CertificationRule(CertificationRuleExportDTO.class),

    /**
     * 持证明细列表
     */
    CertificationRelate(CertificationRelateExportDTO.class),

    /**
     * 部门持证明细列表
     */
    CertificationRelateDept(CertificationRelateDeptExportDTO.class),

    /**
     * 应持证人员管理
     */
    CertificationHoldUser(CertificationHoldUserExportDTO.class),

    /**
     * 应持证统计管理
     */
    CertificationHoldUserStat(ExportDTO.class),

    /**
     * 认证工种分类
     */
    CertifiedWorkType(CertWorkTypeDTO.class),

    /**
     * 共读管理列表
     */
    Reading(ReadingExportDTO.class),

    /**
     * 图书管理列表
     */
    ReadingBooksManage(BookManageExportDTO.class),

    /**
     * 打卡列表
     */
    ReadingSign(ReadingSignExportDTO.class),

    /**
     * 心得列表
     */
    ReadingBookExperience(BookExperienceExportDTO.class),

    /**
     * 心得评论管理列表
     */
    ReadingBookExperienceComment(BookExperienceCommentExportDTO.class),

    /**
     * 图书心得点赞列表
     */
    ReadingBookExperienceStar(BookExperienceStarExportDTO.class),

    /**
     * 举报列表
     */
    ReadingReports(BookExperienceReportExportDTO.class),

    /**
     * 图书统计列表
     */
    ReadingStatistics(BookStudyProgressExportDTO.class),

    /**
     * 图书统计列表-任务完成
     */
    TaskFinishReadingStatistics(TaskFinishExportDTO.class),

    /**
     * 图书统计列表-学员排行
     */
    UserRankReadingStatistics(UserRankReadingExportDTO.class),

    /**
     * 评价管理列表
     */
    Appraise(AppraiseExportDTO.class),

    /**
     * 讲师评价管理列表
     */
    LecturerAppraise(LecturerAppraiseExportDTO.class),

    /**
     * 答辩管理列表
     */
    Meeting(ExportDTO.class),

    /**
     * 评委列表
     */
    AppraiseReferee(AppraiseRefereeExportDTO.class),

    /**
     * 答辩会议评委列表
     */
    MeetingReferee(AppraiseRefereeExportDTO.class),

    /**
     * 被评人列表
     */
    AppraiseProvider(AppraiseProviderExportDTO.class),

    /**
     * 被评人列表
     */
    MeetingProvider(MeetingProviderExportDTO.class),

    /**
     * 材料规则列表
     */
    AppraiseFileType(AppraiseFileTypeExportDTO.class),

    /**
     * 材料规则列表
     */
    AppraiseFileType2(AppraiseFileType2ExportDTO.class),

    /**
     * 材料管理列表
     */
    AppraiseProviderFile(AppraiseProviderFileExportDTO.class),

    /**
     * 评分明细列表
     */
    AppraiseDetail(ExportDTO.class),

    /**
     * 评分统计
     */
    MeetingScoreDetail(MeetingDetailExportDTO.class),

    /**
     * 查看明细列表
     */
    AppraiseShowDetail(ExportDTO.class),

    /**
     * 打分历史列表
     */
    AppraiseHistory(AppraiseHistoryDetailExportDTO.class),

    /**
     * 招募管理列表
     */
    Recruiting(RecruitingExportDTO.class),

    /**
     * 讲师招募管理列表
     */
    LecturerRecruiting(LecturerRecruitingExportDTO.class),

    /**
     * 招募管理协办人员列表
     */
    RecruitingAssistant(RecruitingAssistantExportDTO.class),

    /**
     * 招募管理宣传材料列表
     */
    RecruitingMaterial(RecruitingMaterialExportDTO.class),

    /**
     * 招募管理材料规则列表
     */
    RecruitingMaterialRule(RecruitingMaterialRuleExportDTO.class),

    RecruitingMaterialRule2(RecruitingMaterialRule2ExportDTO.class),

    /**
     * 招募管理招募审核列表
     */
    RecruitingAudit(RecruitingParticipationRecordExportDTO.class),

    /**
     * 招募管理招募审核调研明细列表
     */
    RecruitingAuditSurveyDetail(RecruitingAuditSurveyDetailExportDTO.class),

    /**
     * 课件库列表
     */
    CourseWareLib(CourseWareLibExportDTO.class),
    /**
     * 课程库标签分类管理列表
     */
    courseWareLibraryCategory(CourseSysCategoryExportDTO.class),

    /**
     * 试卷库列表
     */
    TestPaperLib(TestPaperLibExportDTO.class),

    /**
     * 试卷库-题目管理列表
     */
    TestPaperQuestion(TestPaperQuestionExportDTO.class),

    /**
     * 试卷库分类
     */
    TestPaperLibraryCategory(ExamCategoryExportDTO.class),

    /**
     * 考题库列表
     */
    ExamLib(ExamLibExportDTO.class),

    /**
     * 考题库-题目管理列表
     */
    ExamLibQuestion(LibQuestionExportDTO.class),

    /**
     * 考题库分类
     */
    ExamLibCategory(LibQuestionExportDTO.class),

    /**
     * 考试题目答题用户列表
     */
    ExamQuestionAnswerUser(ExamAnswerUserExportDTO.class),

    /**
     * 练习库列表
     */
    ExerciseLib(ExerciseLibExportDTO.class),

    /**
     * 练习库-题目管理列表
     */
    ExerciseLibQuestion(ExerciseQuestionExportDTO.class),

    /**
     * 练习库分类
     */
    ExerciseLibCategory(CourseSysCategoryExportDTO.class),

    /**
     * 调研库列表
     */
    SurveyLib(SurveyLibExportDTO.class),

    /**
     * 调研库分类
     */
    SurveyLibCategory(SurveyCategoryExportDTO.class),

    /**
     * 知识库列表
     */
    MaterialLib(MaterialLibExportDTO.class),

    /**
     * 知识库标签列表
     */
    MaterialCategory(TagCategoryExportDTO.class),

    /**
     * 研究领域列表
     */
    ResearchFieldCategory(ResearchFieldExportDTO.class),

    /**
     * 知识库分类
     */
    KnowledgeBaseType(KnowledgeBaseCategoryExportDTO.class),

    /**
     * 评估库列表
     */
    EvaluationLib(EvaluationLibExportDTO.class),

    /**
     * 评估库分类列表
     */
    EvaluationCategory(SysCategoryExportDTO.class),

    /**
     * 奖品管理列表
     */
    Award(AwardExportDTO.class),

    /**
     * 奖品分类管理
     */
    AwardCategory(AwardCategoryExportDTO.class),

    /**
     * 积分中奖列表
     */
    GameLottery(LotteryRecordExportDTO.class),

    /**
     * 邮箱模板列表
     */
    MailTemplate(MailTemplateExportDTO.class),

    /**
     * 推送管理列表
     */
    PushManage(PushManageExportDTO.class),

    /**
     * 投票管理列表
     */
    VoteManage(VoteExportDTO.class),

    /**
     * 投票内容管理列表
     */
    VoteContent(VoteContentExportDTO.class),

    /**
     * 投票统计列表
     */
    VoteStatistics(VoteStatisticsExportDTO.class),

    /**
     * 投票明细列表
     */
    VoteDetail(VoteDetailExportDTO.class),

    /**
     * 签到统计
     */
    SignStat(SignStatExportDTO.class),

    /**
     * 培训签到统计
     */
    TrainSignStat(TrainSignStatExportDTO.class),

    /**
     * 项目签到统计
     */
    ProjectSignStat(ExportDTO.class),

    /**
     * 签到列表
     */
    SignList(SignListExportDTO.class),

    /**
     * 项目签到列表
     */
    ProjectSignList(ProjectSignListExportDTO.class),

    /**
     * 奖品兑换记录
     */
    AwardRedeemRecord(AwardRedeemRecordExportDTO.class),

    /**
     * 金币兑换记录
     */
    AwardExchangeRecord(AwardExchangeRecordExportDTO.class),

    /**
     * 订单列表
     */
    PaymentOrder(PaymentOrderExportDTO.class),

    /**
     * 机构订单会员列表
     */
    PaymentOrgOrderMember(PaymentOrgOrderMemberExportDTO.class),

    /**
     * 用户管理列表
     */
    User(UserExportDTO.class),

    /**
     * 用户管理列表-包含自定义字段
     */
    UserExpand(ExportDTO.class),

    /**
     * 组织列表
     */
    Org(OrgExportDTO.class),

    /**
     * 角色管理列表
     */
    Role(RoleExportDTO.class),

    /**
     * 数据字典管理列表
     */
    Dict(DictExportDTO.class),

    /**
     * 版本管理列表
     */
    Version(ClientVersionExportDTO.class),

    /**
     * 头衔设置列表
     */
    Title(TitleExportDTO.class),

    /**
     * 首页配置列表
     */
    HomePageConfig(HomePageConfigExportDTO.class),

    /**
     * 反馈管理列表
     */
    Feedback(FeedbackExportDTO.class),

    /**
     * 课程情况统计
     */
    CourseLearnState(CourseStateStatAnalysisExportDTO.class),

    /**
     * 部门学习导出
     */
    OrgLearnState(OrgLearnStatExportDTO.class),

    /**
     * 课程已学未学导出
     */
    CourseLearned(CourseLearnedStatAnalysisExportDTO.class),

    /**
     * 课程点赞数据导出
     */
    CourseAgreeDetail(CourseAgreeDetailStatAnalysisExportDTO.class),

    ExamStateStatAnalysis(ExamStateStatAnalysisExportDTO.class),

    ExamStateStatPartAnalysis(ExamStateStatPartAnalysisExportDTO.class),

    ExamStateStatPassAnalysis(ExamStateStatPartAnalysisExportDTO.class),

    ExamStateStatPostAnalysis(ExamStateStatPartAnalysisExportDTO.class),

    CompetitionAnswerRecordStat(CompetitionAnswerRecordStatExportDTO.class),

    GldTradeStatAnalysis(GldTradeStatAnalysisExportDTO.class),

    ExchangeRecordStatAnalysis(ExchangeRecordStatAnalysisExportDTO.class),

    ExcitationCollectStatAnalysis(ExcitationCollectStatAnalysisExportDTO.class),

    InsideLecturerCategoryStatAnalysis(InsideLecturerCategoryStatAnalysisExportDTO.class),

    InsideLecturerDepartmentStatAnalysis(InsideLecturerDepartmentStatAnalysisExportDTO.class),

    UserExcitationRecordStatAnalysis(UserExcitationRecordStatAnalysisExportDTO.class),

    TrainPlanStatAnalysis(TrainPlanStatAnalysisExportDTO.class),

    SysTemTagStatAnalysis(SysTemTagStatAnalysisExportDTO.class),

    TimeRegionStatAnalysis(TimeRegionStatAnalysisExportDTO.class),

    /**
     * 积分统计列表
     */
    UserIntegralStatAnalysis(UserIntegralStatAnalysisExportDTO.class),

    /**
     * 积分统计列表-按讲师
     */
    LecturerIntegral(LecturerIntegralExportDTO.class),

    /**
     * 查看积分记录
     */
    UserIntegralDetail(UserIntegralDetailExportDTO.class),

    /**
     * 积分统计详情列表
     */
    UserIntegralDetailStatAnalysis(UserIntegralDetailStatAnalysisExportDTO.class),

    /**
     * 课件学习统计
     */
    CoursewareLearnStatAnalysis(CoursewareLearnStatAnalysisExportDTO.class),

    /**
     * 课件学习统计详情
     */
    CoursewareLearnDetailStatAnalysis(CoursewareLearnDetailStatAnalysisExportDTO.class),

    /**
     * 资讯访问统计
     */
    InfoStatAnalysis(InfoStatAnalysisExportDTO.class),

    /**
     * 学员档案
     */
    LearnRecordStatAnalysis(LearnRecordStatAnalysisExportDTO.class),

    /**
     * 栏目访问
     */
    ItemVisitRecord(ItemVisitRecordExportDTO.class),

    /**
     * 学习记录课程列表
     */
    LearnRecordCourseStatAnalysis(LearnRecordCourseStatAnalysisExportDTO.class),

    /**
     * 学习记录考试列表
     */
    LearnRecordExamStatAnalysis(LearnRecordExamStatAnalysisExportDTO.class),

    /**
     * 学习记录资讯列表
     */
    LearnRecordInfoStatAnalysis(LearnRecordInfoStatAnalysisExportDTO.class),

    /**
     * 学习记录项目列表
     */
    LearnRecordProjectStatAnalysis(LearnRecordProjectStatAnalysisExportDTO.class),

    /**
     * 学习记录项目列表
     */
    LearnRecordFaceProjectStatAnalysis(LearnRecordFaceProjectStatAnalysisExportDTO.class),

    /**
     * 学习记录项目列表
     */
    LearnRecordTrainStatAnalysis(LearnRecordTrainStatAnalysisExportDTO.class),

    /**
     * 学习记录调研列表
     */
    LearnRecordSurveyStatAnalysis(LearnRecordSurveyStatAnalysisExportDTO.class),

    /**
     * 经验排行榜
     */
    ScoreRankStatAnalysis(ScoreRankStatAnalysisExportDTO.class),

    /**
     * 上线用户统计-按天
     */
    OnlineUserDayStatAnalysis(OnlineUserDayStatAnalysisExportDTO.class),

    /**
     * 上线用户统计-按月
     */
    OnlineUserMonthStatAnalysis(OnlineUserMonthStatAnalysisExportDTO.class),

    /**
     * 上线用户统计-按部门
     */
    OnlineUserDeptStatAnalysis(OnlineUserDeptStatAnalysisExportDTO.class),

    /**
     * 按天访问量统计
     */
    AccessStatDayDTO(AccessStatDayDTO.class),

    /**
     * 按月访问量统计
     */
    AccessStatMonthDTO(com.wunding.learn.file.api.dto.export.analysis.AccessStatMonthDTO.class),

    /**
     * 按部门访问量统计
     */
    AccessStatOrgDTO(com.wunding.learn.file.api.dto.export.analysis.AccessStatOrgDTO.class),

    /**
     * 学员上传课件统计
     */
    StudentUploadCoursewareStatAnalysis(StudentUploadCoursewareStatAnalysisExportDTO.class),

    /**
     * 搜索关键字统计
     */
    SearchKeyStatDTO(StatSearchKeyExportDTO.class),

    /**
     * 认证分类列表
     */
    CertifiedCategory(CertifiedCategoryExportDTO.class),

    /**
     * 操作日志
     */
    Log(LogExportDTO.class),

    /**
     * 业务操作日志
     */
    BizLog(BizLogExportDTO.class),

    /**
     * 部门同步历史
     */
    OrgThirdSyncRecord(OrgThirdSyncRecordExportDTO.class),

    /**
     * 岗位同步历史
     */
    PostThirdSyncRecord(PostThirdSyncRecordExportDTO.class),

    /**
     * 人员同步历史
     */
    UserThirdSyncRecord(UserThirdSyncRecordExportDTO.class),

    /**
     * 培训班列表
     */
    Train(TrainExportDTO.class),

    /**
     * 培训列表活动导出
     */
    TrainActivity(TrainActivityExportDTO.class),

    /**
     * 班级管理
     */
    TrainProject(TrainProjectExportDTO.class),

    /**
     * 实操管理导出
     */
    TrainActivityPracticeRecord(ActivityPracticeRecordExportDTO.class),

    /**
     * 勋章用户关系列表
     */
    MedalUserRelation(MedalUserRelationExportDTO.class),

    /**
     * 培训计划列表
     */
    Plan(PlanExportDTO.class),

    /**
     * 培训计划清单列表
     */
    PlanInventory(ExportDTO.class),

    /**
     * 培训计划类别管理列表
     */
    PlanCategory(PlanCategoryExportDTO.class),

    /**
     * 培训计划汇总表
     */
    PlanStatistic(PlanStatisticExportDTO.class),

    /**
     * 培训计划条目明细
     */
    PlanInventoryStatistic(ExportDTO.class),

    /**
     * 培训项目列表
     */
    PlanTrain(TrainProjectStatisticExportDTO.class),

    /**
     * 培训计划执行统计-按月
     */
    PlanExecuteMonth(StatAnalysisPlanExecuteMonthExportDTO.class),

    /**
     * 培训计划执行统计-按年
     */
    PlanExecuteYear(StatAnalysisPlanExecuteYearExportDTO.class),

    /**
     * 表单管理清单列表
     */
    FormManage(ExportDTO.class),

    /**
     * 证书管理列表
     */
    Leaflet(LeafletExportDTO.class),

    /**
     * 申请管理列表
     */
    Apply(ApplyExportDTO.class),

    /**
     * 课程认证讲师-by讲师
     */
    LecturerCourseAuthByLecturer(LecturerCourseAuthByLecturerExportDTO.class),

    /**
     * 讲师库-认证课程导出数据对象
     */
    LecturerCourseAuthByLecturerCourse(LecturerCourseAuthByLecturerCourseExportDTO.class),

    /**
     * 课程认证讲师-by课程
     */
    LecturerCourseAuthByCourse(LecturerCourseAuthByCourseExportDTO.class),

    /**
     * 讲师开发课程数据
     */
    LecturerCourseList(LecturerCourseListExportDTO.class),

    /**
     * 课程认证讲师-授课讲师明细
     */
    LecturerCourseAuthTeach(LecturerCourseAuthTeachExportDTO.class),

    /**
     * 外部培训管理列表
     */
    TrainWithOut(TrainWithOutExportDTO.class),

    /**
     * 外部培训协办管理列表
     */
    TrainWithOutAssist(TrainWithOutAssistExportDTO.class),

    /**
     * 外部培训管理统计-汇总
     */
    TrainWithoutStatistics(TrainWithoutStatisticsExportDTO.class),

    /**
     * 外部培训报名列表
     */
    TrainWithoutApply(TrainWithoutApplyExportDTO.class),

    /**
     * 外部培训结果列表
     */
    TrainWithoutResult(TrainWithoutResultExportDTO.class),

    /**
     * 外部培训明细列表
     */
    TrainWithoutApplyDetail(TrainWithoutApplyDetailExportDTO.class),

    /**
     * 外部培训统计列表-专业
     */
    TrainWithoutWorkStatistics(ExportDTO.class),

    /**
     * 成员管理-学员列表
     */
    TrainMembers(TrainMembersExportDTO.class),

    /**
     * 成员管理-协办人员列表
     */
    TrainCoOrganizer(TrainCoOrganizerExportDTO.class),

    /**
     * 部门持证目标管理列表
     */
    CertificationDeptTarget(CertificationDeptTargetExportDTO.class),

    /**
     * 部门持证目标统计
     */
    CerDeptTargetReport(CerDeptTargetReportExportDTO.class),

    /**
     * 活动分
     */
    TaskScore(TaskScoreExportDTO.class),

    /**
     * 供应商管理列表
     */
    Supplier(SupplierExportDTO.class),

    /**
     * 供应商资料管理列表
     */
    SupplierFile(SupplierFileExportDTO.class),

    /**
     * 供应商讲师授课管理列表
     */
    TrainLecturerTeachDetail(TrainLecturerTeachExportDTO.class),

    /**
     * 培训项目课程
     */
    TrainCourse(TrainCourseExportDTO.class),

    /**
     * 培训项目明细统计
     */
    TrainTaskDetail(TrainTaskDetailExportDTO.class),

    /**
     * 培训项目个人学习排名统计
     */
    TrainPersonRank(TrainPersonRankExportDTO.class),

    /**
     * 培训项目部门学习排名统计
     */
    TrainOrgLearn(TrainOrgLearnExportDTO.class),

    /**
     * 表单模板管理列表
     */
    FormTemplateManage(FormTemplateManageDTO.class),

    /**
     * 推送白名单
     */
    UserWhiteRecord(UserWhiteExportDTO.class),

    /**
     * 用户访问栏目明细
     */
    UserVisitItemRecord(UserVisitItemRecordExportDTO.class),

    /**
     * 后台路由访问明细
     */
    HomeRouterVisitDetail(HomeRouterVisitDetailExportDTO.class),

    /**
     * 推送消息
     */
    PushMessageRecord(PushMessageExportDTO.class),

    /**
     * 用户身份信息
     */
    UserIdentityInfo(UserIdentityInfoExportDTO.class),

    /**
     * 身份用户管理
     */
    IdentityUserInfo(IdentityUserInfoExportDTO.class),

    /**
     * 用户身份列表
     */
    UserIdentityList(UserIdentityListExportDTO.class),

    /**
     * 下发人员明细列表
     */
    VisibleViewLimitUserList(VisibleViewLimitUserExportDTO.class),

    /**
     * 学习地图
     */
    LearnMap(LearnMapExportDTO.class),

    /**
     * 学习地图执行
     */
    LearnMapExec(LearnMapExecExportDTO.class),

    /**
     * 学习地图/学习地图执行学习明细列表
     */
    LearnMapLearnDetail(LearnMapLearnDetailExportDTO.class),

    /**
     * 学习地图/学习地图执行学习明细无阶段列表
     */
    LearnMapLearnDetailNonePhase(LearnMapLearnDetailNonePhaseExportDTO.class),

    /**
     * 学习地图/学习地图执行学习用户列表
     */
    LearnMapLearnUserPart(LearnMapLearnUserPartExportDTO.class),

    /**
     * 学习地图/学习地图执行学习用户列表
     */
    LearnMapLearnUserFinish(LearnMapLearnUserFinishExportDTO.class),

    /**
     * 学习地图执行横向统计
     */
    LearnMapHorizontalStat(ExportDTO.class),

    /**
     * 学习地图活动列表
     */
    LearnMapActivity(ExportDTO.class),

    /**
     * 学习地图情况列表
     */
    LearnMapProgressStat(LearnMapProgressExportDTO.class),

    /**
     * 学习地图活动情况列表
     */
    LearnMapActivityProgressStat(LearnMapActivityProgressExportDTO.class),

    /**
     * 课程笔记
     */
    CourseNote(CourseNoteExportDTO.class),

    /**
     * 能力词典
     */
    AbilityDictionary(AbilityDictionaryExportDTO.class),

    /**
     * 能力模型
     */
    AbilityMode(AbilityModeExportDTO.class),

    /**
     * 权限目录配置
     */
    PermissionConfig(PermissionConfigExportDTO.class),

    /**
     * 权限目录路由配置
     */
    PermissionRouter(PermissionRouterExportDTO.class),

    /**
     * 课件答题明细列表
     */
    CoursewareQuestionAnswerRecord(CoursewareQuestionAnswerRecordExportDTO.class),

    /**
     * 岗位体系导入模板
     */
    IdentityPostSystemTemplate(ExportDTO.class),

    /**
     * 岗位身份导入模板
     */
    IdentityPostTemplate(ExportDTO.class),

    /**
     * 岗位身份列表
     */
    IdentityPost(ExportDTO.class),

    /**
     * 身份列表
     */
    Identity(ExportDTO.class),

    /**
     * 岗位体系数据列表
     */
    IdentityPostSystemData(IdentityPostSystemDataExportDTO.class),

    /**
     * 评委专家数据列表
     */
    Expert(ExportDTO.class),

    /**
     * 任职资格
     */
    JobQualification(ExportDTO.class),

    /**
     * 任职资格
     */
    JobAuthentication(ExportDTO.class),

    /**
     * 认证申请人员
     */
    JobAuthApplyRecord(ExportDTO.class),

    /**
     * 监督评价员管理
     */
    PracticalSuperviseUser(PracticalSuperviseUserExportDTO.class),

    /**
     * 监督学员实操记录列表
     */
    PracticalOperationUserBySupervise(PracticalOperationUserBySuperviseExportDTO.class),

    /**
     * 监督学员实操记录列表
     */
    PracticalOperationUser(PracticalOperationUserExportDTO.class),

    /**
     * 能力项部门统计
     */
    LearnMapAbilityOrgStatistic(ExportDTO.class),

    /**
     * 胜任力地图部门统计
     */
    LearnMapUserOrgStatistic(ExportDTO.class),

    /**
     * 胜任力地图学习明细统计
     */
    LearnMapUserResourcesStatistic(LearnMapUserResourcesStatisticExportDTO.class),

    /**
     * 胜任力地图学习进度明细统计
     */
    LearnMapUserStatistic(ExportDTO.class),

    /**
     * 学习地图能力学习要求记录
     */
    LearnMapAbilityAsk(ExportDTO.class),

    /**
     * 面授项目报名模板
     */
    ApplyTemplate(ExportDTO.class),

    /**
     * 面授项目用户报名列表
     */
    FaceProjectApplyUser(ExportDTO.class),

    /**
     * 面授项目用户默认报名列表
     */
    FaceProjectDefaultApplyUser(DefaultApplyExportDTO.class),

    /**
     * 面授项目用户默认报名列表
     */
    InvoiceList(InvoiceExportDTO.class),

    /**
     * 请假管理列表
     */
    ProjectVacate(ExportDTO.class),

    /**
     * 日程统计
     */
    ScheduleStat(ExportDTO.class),

    /**
     * 游戏管理列表
     */
    March(MarchExportDTO.class),
    /**
     * 关卡列表
     */
    MarchCheckpoint(MarchCheckpointExportDTO.class),

    /**
     * 关卡列表
     */
    MarchTask(MarchTaskExportDTO.class),

    /**
     * 游戏-团队管理列表
     */
    MarchTeam(MarchTeamExportDTO.class),

    /**
     * 游戏-话题管理列表
     */
    MarchPost(MarchPostExportDTO.class),

    /**
     * 游戏-说明管理列表
     */
    MarchNotice(MarchNoticeExportDTO.class),

    /**
     * 话题回帖统计
     */
    MarchPostCountComment(MarchCountCommentExportDTO.class),

    /**
     * 测评工具
     */
    ASSESS_TOOL(AssessToolExportDTO.class),

    ASSESS_EVALUATOR_LIST(AssessEvaluatorListExportDTO.class),

    ASSESS_PROJECT(AssessProjectExportDTO.class),

    ASSESS_TOOL_QUESTION(ExportDTO.class),

    /**
     * 测评用户导出方式2
     */
    ASSESS_USER_LIST2(AssessUserExportDTO2.class),

    /**
     * 测评用户导出方式1
     */
    ASSESS_USER_LIST1(AssessUserExportDTO1.class),

    /**
     * 部门测评配置管理列表导出
     */
    ASSESS_DEP_MANAGER_USER_LIST(AssessDepManagerExportDTO.class),

    /**
     * 测评项目测评明细列表导出
     */
    ASSESS_PROJECT_ASSESS_DETAILS(AssessDetailsExportDTO.class),

    /**
     * 测评题目明细列表导出
     */
    ASSESS_QUESTION_DETAIL(AssessQuestionDetailExportDTO.class),

    /**
     * 测评进度列表导出
     */
    ASSESS_PROGRESS(ExportDTO.class),

    /**
     * 测评用户导入
     */
    ASSESS_USER(ExportDTO.class),

    /**
     * 测评报告
     */
    ASSESS_REPORT(ExportDTO.class),

    /**
     * 测评记录
     */
    ASSESS_RECORD(ExportDTO.class),

    /**
     * 测评明细
     */
    ASSESS_DETAILS(ExportDTO.class),

    answerRecordDetailData(ExportDTO.class),

    /**
     * 中台通用导出枚举
     */
    OLEARN_EXPORT(ExportDTO.class),

    /**
     * 海报分享记录
     */
    USER_POSTER_SHARE_RECORD(UserPosterShareExportDTO.class),

    /**
     * 考试竞赛场次参与人员详情信息
     */
    COMPETITION_SESSION_USER_DETAIL(CompetitionSessionUserDetailExportDTO.class),

    /**
     * 用户金币列表
     */
    UserGoldCoin(UserGoldCoinExportDTO.class),

    /**
     * 用户金币账单列表
     */
    UserGoldCoinBill(UserGoldCoinBillExportDTO.class),

    /**
     * 会员卡管理列表
     */
    MemberCard(MemberCardExportDTO.class),

    /**
     * 会员机构管理列表
     */
    MemberOrg(MemberOrgExportDTO.class),

    /**
     * 会员管理列表
     */
    Member(MemberExportDTO.class),

    /**
     * 课程分类导出
     */
    CourseCategoryExport(ExportDTO.class),

    LEARNING_TARGET_MANAGE_ORG(LearningTargetManageOrgExportDTO.class),

    LEARNING_TARGET_MANAGE_IDENTITY(LearningTargetManageIdentityExportDTO.class),

    LEARNING_TARGET_MANAGE_USER_PROCESS(LearningTargetManageUserProcessExportDTO.class),

    USER_GOTTEN_TARGET_VALUE_DETAIL(UserGottenTargetValueDetailExportDTO.class),

    LEARNING_TARGET_COMPLETION_DETAILS_ORG(LearningTargetCompletionDetailsOrgExportDTO.class),

    LEARNING_TARGET_COMPLETION_DETAILS_IDENTITY(LearningTargetCompletionDetailsIdentityExportDTO.class),

    LEARNING_TARGET_REF_USER_LIST(LearningTargetRefUserListExportDTO.class),

    TARGET_USER_ACHIEVEMENT_DETAIL(TargetUserAchievementDetailExportDTO.class),

    TASK_MENTOR_LIST(ExportDTO.class),

    /**
     * 课程审核我的申请列表
     */
    @Deprecated
    MY_APPLY_COURSE_AUDIT_LIST(MyApplyCourseAuditDTO.class),

    /**
     * 审核-我的申请列表
     */
    MY_APPLY_RESOURCE_AUDIT_LIST(MyApplyResourceAuditDTO.class),

    /**
     * 课程审核我的处理列表
     */
    @Deprecated
    MY_DEAL_COURSE_AUDIT_LIST(AllCourseAuditDTO.class),

    /**
     * 审核-我的处理列表
     */
    MY_DEAL_RESOURCE_AUDIT_LIST(AllResourceAuditDTO.class),

    /**
     * 课程审核全部的申请列表
     */
    @Deprecated
    ALL_COURSE_AUDIT_LIST(AllCourseAuditDTO.class),

    /**
     * 审核-全部的申请列表
     */
    ALL_RESOURCE_AUDIT_LIST(MyApplyResourceAuditDTO.class),

    LIBRARY_OPERATION_RECORD(LibraryOperationRecordExportDTO.class),

    TASK_RECORD(TaskRecordExportDTO.class),

    /**
     * 标签管理列表
     */
    SYS_TAG_LIST(SysTagListExportDTO.class),

    TASK_RECORD_DETAIL(TaskRecordDetailExportDTO.class);

    private final Class<?> type;

    public static ExportBizType get(String bizType) {
        for (ExportBizType exportBizType : ExportBizType.values()) {
            if (Objects.equals(exportBizType.name(), bizType)) {
                return exportBizType;
            }
        }
        return null;
    }

    ExportBizType(Class<?> type) {
        this.type = type;
    }

    public Class<?> getType() {
        return type;
    }
}
