package com.wunding.learn.file.api.dto.export.project;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(name = "ProjectCompletionStatisticExportDTO", description = "导出项目完成情况统计对象")
public class ProjectCompletionStatisticExportDTO {


    /**
     * 姓名
     */
    @Schema(description = "姓名")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.ProjectCompletionStatisticExportDTO.fullName}")
    private String fullName;

    /**
     * 账号
     */
    @Schema(description = "账号")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.ProjectCompletionStatisticExportDTO.loginName}")
    private String loginName;

    /**
     * 部门全称
     */
    @Schema(description = "部门")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.ProjectCompletionStatisticExportDTO.orgPath}")
    private String orgPath;

    /**
     * 团队名称
     */
    @Schema(description = "团队")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.ProjectCompletionStatisticExportDTO.teamName}")
    private String teamName;


    /**
     * 项目完成率（所有任务完成率）
     */
    @Schema(description = "项目完成率")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.ProjectCompletionStatisticExportDTO.finishRatio}")
    private String finishRatioStr;

    /**
     * 项目完成率（所有任务完成率，课程任务中计算必修）
     */
    @Schema(description = "项目完成率1")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.ProjectCompletionStatisticExportDTO.finishOnlyMustRatio}")
    private String finishOnlyMustRatioStr;

    /**
     * 必修课程完成率
     */
    @Schema(description = "课程完成率")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.ProjectCompletionStatisticExportDTO.courseMustFinishRatio}")
    private String courseMustFinishRatioStr;

    /**
     * 课时（小时） 不足0.01小时按0计算，否则四舍五入
     */
    @Schema(description = "课时（小时）")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.ProjectCompletionStatisticExportDTO.courseTime}")
    private BigDecimal courseTime;

    /**
     * 学时
     */
    @Schema(description = "学时")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.ProjectCompletionStatisticExportDTO.learnTime}")
    private BigDecimal learnTime;

    /**
     * 积分
     */
    @Schema(description = "积分")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.ProjectCompletionStatisticExportDTO.integral}")
    private BigDecimal integral;

    /**
     * 金币
     */
    @Schema(description = "金币")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.ProjectCompletionStatisticExportDTO.goldCoin}")
    private BigDecimal goldCoin;

    /**
     * 学分
     */
    @Schema(description = "学分")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.project.ProjectCompletionStatisticExportDTO.credit}")
    private BigDecimal credit;

}
