package com.wunding.learn.file.api.dto.export.plan;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 表单模板管理导出对象
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">wgr</a>
 * @date 2023/4/23 17:59
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(name = "FormTemplateManageDTO", description = "表单模板管理导出对象")
public class FormTemplateManageDTO {

    /**
     * 表单编号
     */
    @Schema(description = "表单编号")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.plan.FormTemplateManageDTO.formNo}")
    private String formNo;

    /**
     * 表单名称
     */
    @Schema(description = "表单名称")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.plan.FormTemplateManageDTO.formName}")
    private String formName;

    @Schema(description = "是否发布 0-否 1-是 默认0")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.plan.FormTemplateManageDTO.isPublish}")
    private String isPublish;

    /**
     * 分类
     */
    @Schema(description = "分类")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.plan.FormTemplateManageDTO.categoryName}")
    private String categoryName;

    /**
     * 用途说明
     */
    @Schema(description = "用途说明")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.plan.FormTemplateManageDTO.description}")
    private String description;


    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.plan.FormTemplateManageDTO.createName}")
    private String createName;

    /**
     * 创建人账号
     */
    @Schema(description = "账号")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.plan.FormTemplateManageDTO.createLoginName}")
    private String createLoginName;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.plan.FormTemplateManageDTO.createTime}")
    private Date createTime;

    /**
     * 记录条数
     */
    @Schema(description = "记录条数")
    @ExcelProperty(value = "${com.wunding.learn.file.api.dto.export.plan.FormTemplateManageDTO.dateNumber}")
    private Integer dateNumber;


}
