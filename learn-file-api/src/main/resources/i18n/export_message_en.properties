#Wed May 08 15:08:12 CST 2024
com.wunding.learn.file.api.dto.export.ExampleBusinessExportDTO.code=bar code
com.wunding.learn.file.api.dto.export.ExampleBusinessExportDTO.isAvailable=Whether to enable
com.wunding.learn.file.api.dto.export.ExampleBusinessExportDTO.name=line name
com.wunding.learn.file.api.dto.export.ExampleCategoryExportDTO.categoryName=Category Name
com.wunding.learn.file.api.dto.export.ExampleCategoryExportDTO.isAvailable=Whether to enable
com.wunding.learn.file.api.dto.export.ExampleCategoryExportDTO.sortNo=display order
com.wunding.learn.file.api.dto.export.ExampleCommentExportDTO.content=content
com.wunding.learn.file.api.dto.export.ExampleCommentExportDTO.createTime=release time
com.wunding.learn.file.api.dto.export.ExampleCommentExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.ExampleCommentExportDTO.isEfficient=efficient
com.wunding.learn.file.api.dto.export.ExampleCommentExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.ExampleCommentExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.ExampleCommentExportDTO.resourceName=Case title
com.wunding.learn.file.api.dto.export.ExampleLibExportDTO.authStatus=Certification status
com.wunding.learn.file.api.dto.export.ExampleLibExportDTO.authorName=Author's full name
com.wunding.learn.file.api.dto.export.ExampleLibExportDTO.commentCount=Number of comments
com.wunding.learn.file.api.dto.export.ExampleLibExportDTO.transformStatus=Transform Status
com.wunding.learn.file.api.dto.export.ExampleLibExportDTO.createTime=creation time
com.wunding.learn.file.api.dto.export.ExampleLibExportDTO.exampleCateName=Case classification name
com.wunding.learn.file.api.dto.export.ExampleLibExportDTO.exampleCode=Case number
com.wunding.learn.file.api.dto.export.ExampleLibExportDTO.exampleName=Case name
com.wunding.learn.file.api.dto.export.ExampleLibExportDTO.extra=Premium type
com.wunding.learn.file.api.dto.export.ExampleLibExportDTO.orgPath=Department name
com.wunding.learn.file.api.dto.export.ExampleLibExportDTO.publishTime=Storage time
com.wunding.learn.file.api.dto.export.ExampleLibExportDTO.recommend=Is it recommended?
com.wunding.learn.file.api.dto.export.ExampleLibExportDTO.status=Approval Status
com.wunding.learn.file.api.dto.export.ExpertLibExportDTO.auditBusinessName=Review line
com.wunding.learn.file.api.dto.export.ExpertLibExportDTO.auditNumber=Total number of reviews
com.wunding.learn.file.api.dto.export.ExpertLibExportDTO.auditOrgName=Scope of review department
com.wunding.learn.file.api.dto.export.ExpertLibExportDTO.lastAuditTime=Last review date
com.wunding.learn.file.api.dto.export.ExpertLibExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.ExpertLibExportDTO.userLoginName=Account
com.wunding.learn.file.api.dto.export.ExpertLibExportDTO.userName=Name
com.wunding.learn.file.api.dto.export.PostBanUserExportDTO.endTime=end muting time
com.wunding.learn.file.api.dto.export.PostBanUserExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.PostBanUserExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.PostBanUserExportDTO.startTime=Start muting time
com.wunding.learn.file.api.dto.export.PostExportDTO.commentNum=Number of replies
com.wunding.learn.file.api.dto.export.PostExportDTO.createFullName=Posted by
com.wunding.learn.file.api.dto.export.PostExportDTO.createTime=Posting time
com.wunding.learn.file.api.dto.export.PostExportDTO.isClient=source
com.wunding.learn.file.api.dto.export.PostExportDTO.isDel=delete or not
com.wunding.learn.file.api.dto.export.PostExportDTO.orgPath=Belonging Department
com.wunding.learn.file.api.dto.export.PostExportDTO.postType=Post type
com.wunding.learn.file.api.dto.export.PostExportDTO.sectionName=Topic section
com.wunding.learn.file.api.dto.export.PostExportDTO.state=state
com.wunding.learn.file.api.dto.export.PostExportDTO.teamName=visible team
com.wunding.learn.file.api.dto.export.PostExportDTO.title=theme
com.wunding.learn.file.api.dto.export.PostExportDTO.viewNum=viewing count
com.wunding.learn.file.api.dto.export.PostSectionExpertExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.PostSectionExpertExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.PostSectionExpertExportDTO.userName=account
com.wunding.learn.file.api.dto.export.PostSectionExportDTO.auditStatus=Approval Status
com.wunding.learn.file.api.dto.export.PostSectionExportDTO.createTime=creation time
com.wunding.learn.file.api.dto.export.PostSectionExportDTO.dataSource=source
com.wunding.learn.file.api.dto.export.PostSectionExportDTO.isAnonymous=Anonymous or not
com.wunding.learn.file.api.dto.export.PostSectionExportDTO.isEnable=Whether to enable
com.wunding.learn.file.api.dto.export.PostSectionExportDTO.isPublic=Is it public?
com.wunding.learn.file.api.dto.export.PostSectionExportDTO.orgPath=Create department
com.wunding.learn.file.api.dto.export.PostSectionExportDTO.sectionName=Section name
com.wunding.learn.file.api.dto.export.PostSectionExportDTO.userName=founder
com.wunding.learn.file.api.dto.export.SharedLibraryExportDTO.courseCateTypeName=course sorts
com.wunding.learn.file.api.dto.export.SharedLibraryExportDTO.courseName=Course Title
com.wunding.learn.file.api.dto.export.SharedLibraryExportDTO.cwCount=Number of courseware
com.wunding.learn.file.api.dto.export.SharedLibraryExportDTO.isPublish=Post status
com.wunding.learn.file.api.dto.export.SharedLibraryExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.SharedLibraryExportDTO.publishBy=publisher
com.wunding.learn.file.api.dto.export.SharedLibraryExportDTO.publishTime=release time
com.wunding.learn.file.api.dto.export.ability.AbilityDictionaryExportDTO.abilityCategoryName=Ability classification
com.wunding.learn.file.api.dto.export.ability.AbilityDictionaryExportDTO.code=Ability coding
com.wunding.learn.file.api.dto.export.ability.AbilityDictionaryExportDTO.courseNum=course
com.wunding.learn.file.api.dto.export.ability.AbilityDictionaryExportDTO.isAvailable=state
com.wunding.learn.file.api.dto.export.ability.AbilityDictionaryExportDTO.name=Ability name
com.wunding.learn.file.api.dto.export.ability.AbilityModeExportDTO.applicationLevel=application level
com.wunding.learn.file.api.dto.export.ability.AbilityModeExportDTO.categoryName=Model classification
com.wunding.learn.file.api.dto.export.ability.AbilityModeExportDTO.code=model encoding
com.wunding.learn.file.api.dto.export.ability.AbilityModeExportDTO.courseNum=course
com.wunding.learn.file.api.dto.export.ability.AbilityModeExportDTO.isAvailable=state
com.wunding.learn.file.api.dto.export.ability.AbilityModeExportDTO.name=Model name
com.wunding.learn.file.api.dto.export.ability.AbilityModeExportDTO.taskNum=task item
com.wunding.learn.file.api.dto.export.activi.AwardCategoryExportDTO.categoryName=Category Name
com.wunding.learn.file.api.dto.export.activi.AwardCategoryExportDTO.isAvailable=Whether to enable
com.wunding.learn.file.api.dto.export.activi.AwardCategoryExportDTO.parentCategoryName=Sub-headings
com.wunding.learn.file.api.dto.export.activi.AwardCategoryExportDTO.sortNo=display order
com.wunding.learn.file.api.dto.export.activi.AwardExchangeRecordExportDTO.address=address
com.wunding.learn.file.api.dto.export.activi.AwardExchangeRecordExportDTO.createTime=Redemption time
com.wunding.learn.file.api.dto.export.activi.AwardExchangeRecordExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.activi.AwardExchangeRecordExportDTO.orgName=department
com.wunding.learn.file.api.dto.export.activi.AwardExchangeRecordExportDTO.phone=Phone number
com.wunding.learn.file.api.dto.export.activi.AwardExchangeRecordExportDTO.redeemNum=Exchange quantity
com.wunding.learn.file.api.dto.export.activi.AwardExchangeRecordExportDTO.status=Redemption status
com.wunding.learn.file.api.dto.export.activi.AwardExchangeRecordExportDTO.userName=Name
com.wunding.learn.file.api.dto.export.activi.AwardExportDTO.availableNum=The remaining amount
com.wunding.learn.file.api.dto.export.activi.AwardExportDTO.beginTime=Valid start time
com.wunding.learn.file.api.dto.export.activi.AwardExportDTO.category=Prize categories
com.wunding.learn.file.api.dto.export.activi.AwardExportDTO.consumeNum=Consume gold coins
com.wunding.learn.file.api.dto.export.activi.AwardExportDTO.endTime=Valid end time
com.wunding.learn.file.api.dto.export.activi.AwardExportDTO.exchangedNum=Quantity redeemed
com.wunding.learn.file.api.dto.export.activi.AwardExportDTO.isOnSale=Is it on the shelves?
com.wunding.learn.file.api.dto.export.activi.AwardExportDTO.limitCount=Limited redemption quantity per person
com.wunding.learn.file.api.dto.export.activi.AwardExportDTO.name=Prize name
com.wunding.learn.file.api.dto.export.activi.AwardExportDTO.total=The total amount
com.wunding.learn.file.api.dto.export.activi.AwardRedeemRecordExportDTO.awardName=Prize name
com.wunding.learn.file.api.dto.export.activi.AwardRedeemRecordExportDTO.createTime=Redemption time
com.wunding.learn.file.api.dto.export.activi.AwardRedeemRecordExportDTO.drawUserName=Processor
com.wunding.learn.file.api.dto.export.activi.AwardRedeemRecordExportDTO.id=order number
com.wunding.learn.file.api.dto.export.activi.AwardRedeemRecordExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.activi.AwardRedeemRecordExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.activi.AwardRedeemRecordExportDTO.redeemNum=Exchange quantity
com.wunding.learn.file.api.dto.export.activi.AwardRedeemRecordExportDTO.status=Redemption status
com.wunding.learn.file.api.dto.export.activi.AwardRedeemRecordExportDTO.userName=Name
com.wunding.learn.file.api.dto.export.activi.LotteryRecordExportDTO.awardName=Prize name
com.wunding.learn.file.api.dto.export.activi.LotteryRecordExportDTO.loginName=Account
com.wunding.learn.file.api.dto.export.activi.LotteryRecordExportDTO.lotteryTime=Draw time
com.wunding.learn.file.api.dto.export.activi.LotteryRecordExportDTO.optionName=Award name
com.wunding.learn.file.api.dto.export.activi.LotteryRecordExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.activi.LotteryRecordExportDTO.userName=Name
com.wunding.learn.file.api.dto.export.activi.LotteryRecordExportDTO.status=Distribution status
com.wunding.learn.file.api.dto.export.activi.LotteryRecordExportDTO.remark=Distribution remarks
com.wunding.learn.file.api.dto.export.activi.LotteryRecordExportDTO.dealTime=Distribution time
com.wunding.learn.file.api.dto.export.activi.LotteryRecordExportDTO.dealUserName=Distribution By
com.wunding.learn.file.api.dto.export.activi.LotteryRecordExportDTO.dealLoginName=Distribution Account
com.wunding.learn.file.api.dto.export.activi.LotteryRecordExportDTO.address=Address
com.wunding.learn.file.api.dto.export.activi.LotteryRecordExportDTO.contactPhone=Contact Number
com.wunding.learn.file.api.dto.export.activi.LotteryRecordExportDTO.contactName=Contact Name
com.wunding.learn.file.api.dto.export.activi.MailTemplateExportDTO.addBy=publisher
com.wunding.learn.file.api.dto.export.activi.MailTemplateExportDTO.addDate=release time
com.wunding.learn.file.api.dto.export.activi.MailTemplateExportDTO.isAvailabl=Whether to enable
com.wunding.learn.file.api.dto.export.activi.MailTemplateExportDTO.name=Template name
com.wunding.learn.file.api.dto.export.activi.MailTemplateExportDTO.sortNo=display order
com.wunding.learn.file.api.dto.export.activi.MailTemplateExportDTO.type=template type
com.wunding.learn.file.api.dto.export.activi.ProjectSignListExportDTO.address=Check-in location
com.wunding.learn.file.api.dto.export.activi.ProjectSignListExportDTO.endTime=Check-in end time
com.wunding.learn.file.api.dto.export.activi.ProjectSignListExportDTO.isPublish=Post status
com.wunding.learn.file.api.dto.export.activi.ProjectSignListExportDTO.phaseName=Stage of project
com.wunding.learn.file.api.dto.export.activi.ProjectSignListExportDTO.publishTime=release time
com.wunding.learn.file.api.dto.export.activi.ProjectSignListExportDTO.resourceName=Check-in object
com.wunding.learn.file.api.dto.export.activi.ProjectSignListExportDTO.scheduleName=Schedule name
com.wunding.learn.file.api.dto.export.activi.ProjectSignListExportDTO.signName=Sign-in name
com.wunding.learn.file.api.dto.export.activi.ProjectSignListExportDTO.startTime=Check-in start time
com.wunding.learn.file.api.dto.export.activi.ProjectSignListExportDTO.trainDuration=Training duration
com.wunding.learn.file.api.dto.export.activi.ProjectSignListExportDTO.type=Check-in type
com.wunding.learn.file.api.dto.export.activi.PushManageExportDTO.pushArea=User scope
com.wunding.learn.file.api.dto.export.activi.PushManageExportDTO.pushClientArea=Push method
com.wunding.learn.file.api.dto.export.activi.PushManageExportDTO.pushContentType=Push type
com.wunding.learn.file.api.dto.export.activi.PushManageExportDTO.pushTime=Push time
com.wunding.learn.file.api.dto.export.activi.PushManageExportDTO.title=push content
com.wunding.learn.file.api.dto.export.activi.SignListExportDTO.actualAndShouldSignNum=Actual signature/required signature
com.wunding.learn.file.api.dto.export.activi.SignListExportDTO.address=Check-in location
com.wunding.learn.file.api.dto.export.activi.SignListExportDTO.isPublish=Post status
com.wunding.learn.file.api.dto.export.activi.SignListExportDTO.publishTime=release time
com.wunding.learn.file.api.dto.export.activi.SignListExportDTO.signName=Sign-in name
com.wunding.learn.file.api.dto.export.activi.SignListExportDTO.startToEndTime=Check-in time
com.wunding.learn.file.api.dto.export.activi.SignStatExportDTO.orgName=department
com.wunding.learn.file.api.dto.export.activi.SignStatExportDTO.postName=post
com.wunding.learn.file.api.dto.export.activi.SignStatExportDTO.signInTime=Check-in time
com.wunding.learn.file.api.dto.export.activi.SignStatExportDTO.userLoginName=account
com.wunding.learn.file.api.dto.export.activi.SignStatExportDTO.userName=Name
com.wunding.learn.file.api.dto.export.activi.TrainSignStatExportDTO.orgName=department
com.wunding.learn.file.api.dto.export.activi.TrainSignStatExportDTO.postName=post
com.wunding.learn.file.api.dto.export.activi.TrainSignStatExportDTO.signInTime=Check-in time
com.wunding.learn.file.api.dto.export.activi.TrainSignStatExportDTO.userLoginName=account
com.wunding.learn.file.api.dto.export.activi.TrainSignStatExportDTO.userName=Name
com.wunding.learn.file.api.dto.export.activi.VoteContentExportDTO.courseware=Courseware materials
com.wunding.learn.file.api.dto.export.activi.VoteContentExportDTO.imageExist=picture
com.wunding.learn.file.api.dto.export.activi.VoteContentExportDTO.isStorage=Whether to store
com.wunding.learn.file.api.dto.export.activi.VoteContentExportDTO.optionCodeStr=serial number
com.wunding.learn.file.api.dto.export.activi.VoteContentExportDTO.rank=Current total ranking
com.wunding.learn.file.api.dto.export.activi.VoteContentExportDTO.ticketNum=Number of votes
com.wunding.learn.file.api.dto.export.activi.VoteContentExportDTO.title=title
com.wunding.learn.file.api.dto.export.activi.VoteDetailExportDTO.isExistCourseWare=Is there any courseware?
com.wunding.learn.file.api.dto.export.activi.VoteDetailExportDTO.orgPath=Voter organization
com.wunding.learn.file.api.dto.export.activi.VoteDetailExportDTO.ticketNum=Number of votes
com.wunding.learn.file.api.dto.export.activi.VoteDetailExportDTO.title=Content title
com.wunding.learn.file.api.dto.export.activi.VoteDetailExportDTO.voteCode=voting code
com.wunding.learn.file.api.dto.export.activi.VoteDetailExportDTO.voteName=voting name
com.wunding.learn.file.api.dto.export.activi.VoteDetailExportDTO.voter=voter
com.wunding.learn.file.api.dto.export.activi.VoteExportDTO.isPublish=Post status
com.wunding.learn.file.api.dto.export.activi.VoteExportDTO.organ=Create an organization
com.wunding.learn.file.api.dto.export.activi.VoteExportDTO.startToEndTime=Start and end dates
com.wunding.learn.file.api.dto.export.activi.VoteExportDTO.voteCode=voting code
com.wunding.learn.file.api.dto.export.activi.VoteExportDTO.voteName=voting name
com.wunding.learn.file.api.dto.export.activi.VoteExportDTO.voteNum=Number of votes
com.wunding.learn.file.api.dto.export.activi.VoteExportDTO.votePeopleNum=Number of voters
com.wunding.learn.file.api.dto.export.activi.VoteStatisticsExportDTO.allTicketNum=Total votes
com.wunding.learn.file.api.dto.export.activi.VoteStatisticsExportDTO.isExistCourseWare=Is there any courseware?
com.wunding.learn.file.api.dto.export.activi.VoteStatisticsExportDTO.title=Content title
com.wunding.learn.file.api.dto.export.activi.VoteStatisticsExportDTO.voteCode=voting code
com.wunding.learn.file.api.dto.export.activi.VoteStatisticsExportDTO.voteName=voting name
com.wunding.learn.file.api.dto.export.analysis.AccessStatDayDTO.key=date
com.wunding.learn.file.api.dto.export.analysis.AccessStatDayDTO.rate=Proportion
com.wunding.learn.file.api.dto.export.analysis.AccessStatDayDTO.value=Views
com.wunding.learn.file.api.dto.export.analysis.AccessStatMonthDTO.key=month
com.wunding.learn.file.api.dto.export.analysis.AccessStatMonthDTO.value=Views
com.wunding.learn.file.api.dto.export.analysis.AccessStatOrgDTO.orgName=department
com.wunding.learn.file.api.dto.export.analysis.AccessStatOrgDTO.rate=Proportion
com.wunding.learn.file.api.dto.export.analysis.AccessStatOrgDTO.value=Views
com.wunding.learn.file.api.dto.export.analysis.CompetitionAnswerRecordStatExportDTO.answerNumber=Number of answers
com.wunding.learn.file.api.dto.export.analysis.CompetitionAnswerRecordStatExportDTO.championNumber=Number of championships
com.wunding.learn.file.api.dto.export.analysis.CompetitionAnswerRecordStatExportDTO.correctNumber=Number of correct answers
com.wunding.learn.file.api.dto.export.analysis.CompetitionAnswerRecordStatExportDTO.correctRate=Correct rate
com.wunding.learn.file.api.dto.export.analysis.CompetitionAnswerRecordStatExportDTO.firstParticipationTime=First participation time
com.wunding.learn.file.api.dto.export.analysis.CompetitionAnswerRecordStatExportDTO.fullName=username
com.wunding.learn.file.api.dto.export.analysis.CompetitionAnswerRecordStatExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.analysis.CompetitionAnswerRecordStatExportDTO.orgName=department
com.wunding.learn.file.api.dto.export.analysis.CompetitionAnswerRecordStatExportDTO.participationCount=Participate in sessions
com.wunding.learn.file.api.dto.export.analysis.CompetitionAnswerRecordStatExportDTO.runnerUpNumber=Runner-up times
com.wunding.learn.file.api.dto.export.analysis.CompetitionAnswerRecordStatExportDTO.thirdPlaceNumber=Number of third place
com.wunding.learn.file.api.dto.export.analysis.CompetitionAnswerRecordStatExportDTO.winsNumber=Number of wins
com.wunding.learn.file.api.dto.export.analysis.CourseAgreeDetailStatAnalysisExportDTO.createTime=Start learning time
com.wunding.learn.file.api.dto.export.analysis.CourseAgreeDetailStatAnalysisExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.analysis.CourseAgreeDetailStatAnalysisExportDTO.orgPath=Department
com.wunding.learn.file.api.dto.export.analysis.CourseLearnedStatAnalysisExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.analysis.CourseLearnedStatAnalysisExportDTO.maxViewTime=last study time
com.wunding.learn.file.api.dto.export.analysis.CourseLearnedStatAnalysisExportDTO.minViewTime=Start learning time
com.wunding.learn.file.api.dto.export.analysis.CourseLearnedStatAnalysisExportDTO.orgPath=Department
com.wunding.learn.file.api.dto.export.analysis.CourseLearnedStatAnalysisExportDTO.totalDuration=Effective learning time (seconds)
com.wunding.learn.file.api.dto.export.analysis.CourseStateStatAnalysisExportDTO.categoryName=classification name
com.wunding.learn.file.api.dto.export.analysis.CourseStateStatAnalysisExportDTO.courseName=Course Title
com.wunding.learn.file.api.dto.export.analysis.CourseStateStatAnalysisExportDTO.finishNum=Completed
com.wunding.learn.file.api.dto.export.analysis.CourseStateStatAnalysisExportDTO.publishTime=release time
com.wunding.learn.file.api.dto.export.analysis.CourseStateStatAnalysisExportDTO.starUserCount=Number of reviewers
com.wunding.learn.file.api.dto.export.analysis.CourseStateStatAnalysisExportDTO.synthesizeStar=Comprehensive star rating
com.wunding.learn.file.api.dto.export.analysis.CourseStateStatAnalysisExportDTO.totalAgree=Number of likes
com.wunding.learn.file.api.dto.export.analysis.CourseStateStatAnalysisExportDTO.totalCommPerson=Number of comments
com.wunding.learn.file.api.dto.export.analysis.CourseStateStatAnalysisExportDTO.totalComment=Number of comments
com.wunding.learn.file.api.dto.export.analysis.CourseStateStatAnalysisExportDTO.totalCw=Number of courseware
com.wunding.learn.file.api.dto.export.analysis.CourseStateStatAnalysisExportDTO.underwayNum=Not finished yet
com.wunding.learn.file.api.dto.export.analysis.CoursewareLearnDetailStatAnalysisExportDTO.durationStr=Valid time
com.wunding.learn.file.api.dto.export.analysis.CoursewareLearnDetailStatAnalysisExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.analysis.CoursewareLearnDetailStatAnalysisExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.analysis.CoursewareLearnDetailStatAnalysisExportDTO.orgPath=organize
com.wunding.learn.file.api.dto.export.analysis.CoursewareLearnDetailStatAnalysisExportDTO.postName=post
com.wunding.learn.file.api.dto.export.analysis.CoursewareLearnStatAnalysisExportDTO.courseName=Courses
com.wunding.learn.file.api.dto.export.analysis.CoursewareLearnStatAnalysisExportDTO.cwName=Courseware name
com.wunding.learn.file.api.dto.export.analysis.CoursewareLearnStatAnalysisExportDTO.learnCount=Number of students
com.wunding.learn.file.api.dto.export.analysis.CoursewareLearnStatAnalysisExportDTO.learnNoteCount=Number of notes
com.wunding.learn.file.api.dto.export.analysis.ExamStateStatAnalysisExportDTO.examName=Exam name
com.wunding.learn.file.api.dto.export.analysis.ExamStateStatAnalysisExportDTO.examNo=Exam number
com.wunding.learn.file.api.dto.export.analysis.ExamStateStatAnalysisExportDTO.firstPassRate=first time pass rate
com.wunding.learn.file.api.dto.export.analysis.ExamStateStatAnalysisExportDTO.partCount=The number of participants
com.wunding.learn.file.api.dto.export.analysis.ExamStateStatAnalysisExportDTO.passCount=Number of people who passed
com.wunding.learn.file.api.dto.export.analysis.ExamStateStatAnalysisExportDTO.passRate=passing rate
com.wunding.learn.file.api.dto.export.analysis.ExamStateStatAnalysisExportDTO.postCount=Number of people submitting papers
com.wunding.learn.file.api.dto.export.analysis.ExamStateStatAnalysisExportDTO.questionCount=Number of questions
com.wunding.learn.file.api.dto.export.analysis.ExamStateStatPartAnalysisExportDTO.examScore=test scores
com.wunding.learn.file.api.dto.export.analysis.ExamStateStatPartAnalysisExportDTO.loginName=Login Name
com.wunding.learn.file.api.dto.export.analysis.ExamStateStatPartAnalysisExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.analysis.ExamStateStatPartAnalysisExportDTO.isPost=Whether to submit the paper
com.wunding.learn.file.api.dto.export.analysis.ExamStateStatPartAnalysisExportDTO.orgPath=Department
com.wunding.learn.file.api.dto.export.analysis.ExamStateStatPartAnalysisExportDTO.answerTime=Answer Time
com.wunding.learn.file.api.dto.export.analysis.ExchangeRecordStatAnalysisExportDTO.consumeNum=Number of chips
com.wunding.learn.file.api.dto.export.analysis.ExchangeRecordStatAnalysisExportDTO.createTime=Redemption time
com.wunding.learn.file.api.dto.export.analysis.ExchangeRecordStatAnalysisExportDTO.excitationTypeName=Chip type
com.wunding.learn.file.api.dto.export.analysis.ExchangeRecordStatAnalysisExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.analysis.ExchangeRecordStatAnalysisExportDTO.loginName=Account
com.wunding.learn.file.api.dto.export.analysis.ExchangeRecordStatAnalysisExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.analysis.ExchangeRecordStatAnalysisExportDTO.restNum=exchange balance
com.wunding.learn.file.api.dto.export.analysis.ExchangeRecordStatAnalysisExportDTO.rewardNum=Exchange quantity
com.wunding.learn.file.api.dto.export.analysis.ExchangeRecordStatAnalysisExportDTO.type=Redemption method
com.wunding.learn.file.api.dto.export.analysis.ExcitationCollectStatAnalysisExportDTO.creditSurplus=Credit balance (cumulative)
com.wunding.learn.file.api.dto.export.analysis.ExcitationCollectStatAnalysisExportDTO.creditTotal=Total credits
com.wunding.learn.file.api.dto.export.analysis.ExcitationCollectStatAnalysisExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.analysis.ExcitationCollectStatAnalysisExportDTO.goldCoinSurplus=Gold coin balance (cumulative)
com.wunding.learn.file.api.dto.export.analysis.ExcitationCollectStatAnalysisExportDTO.goldCoinTotal=total gold coins
com.wunding.learn.file.api.dto.export.analysis.ExcitationCollectStatAnalysisExportDTO.integralSurplus=Points balance (cumulative)
com.wunding.learn.file.api.dto.export.analysis.ExcitationCollectStatAnalysisExportDTO.integralTotal=total points
com.wunding.learn.file.api.dto.export.analysis.ExcitationCollectStatAnalysisExportDTO.learnTimeSurplus=Credit hours balance (cumulative)
com.wunding.learn.file.api.dto.export.analysis.ExcitationCollectStatAnalysisExportDTO.learnTimeTotal=total period
com.wunding.learn.file.api.dto.export.analysis.ExcitationCollectStatAnalysisExportDTO.loginName=Account
com.wunding.learn.file.api.dto.export.analysis.ExcitationCollectStatAnalysisExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.analysis.GldTradeStatAnalysisExportDTO.createTime=transaction hour
com.wunding.learn.file.api.dto.export.analysis.GldTradeStatAnalysisExportDTO.currentNum=current balance
com.wunding.learn.file.api.dto.export.analysis.GldTradeStatAnalysisExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.analysis.GldTradeStatAnalysisExportDTO.loginName=Account
com.wunding.learn.file.api.dto.export.analysis.GldTradeStatAnalysisExportDTO.operateNum=quantity
com.wunding.learn.file.api.dto.export.analysis.GldTradeStatAnalysisExportDTO.operateType=Income and expenditure type
com.wunding.learn.file.api.dto.export.analysis.GldTradeStatAnalysisExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.analysis.GldTradeStatAnalysisExportDTO.summary=Summary
com.wunding.learn.file.api.dto.export.analysis.InfoStatAnalysisExportDTO.infoCateName=Information category name
com.wunding.learn.file.api.dto.export.analysis.InfoStatAnalysisExportDTO.infoName=Information name
com.wunding.learn.file.api.dto.export.analysis.InfoStatAnalysisExportDTO.publishTime=release time
com.wunding.learn.file.api.dto.export.analysis.InfoStatAnalysisExportDTO.totalComment=Number of comments
com.wunding.learn.file.api.dto.export.analysis.InfoStatAnalysisExportDTO.totalCommentPerson=Number of comments
com.wunding.learn.file.api.dto.export.analysis.InfoStatAnalysisExportDTO.totalView=viewing count
com.wunding.learn.file.api.dto.export.analysis.InfoStatAnalysisExportDTO.totalViewPerson=Number of visitors
com.wunding.learn.file.api.dto.export.analysis.InsideLecturerCategoryStatAnalysisExportDTO.age=age
com.wunding.learn.file.api.dto.export.analysis.InsideLecturerCategoryStatAnalysisExportDTO.education=Educational qualifications
com.wunding.learn.file.api.dto.export.analysis.InsideLecturerCategoryStatAnalysisExportDTO.instructionTime=class
com.wunding.learn.file.api.dto.export.analysis.InsideLecturerCategoryStatAnalysisExportDTO.jobLevel=Rank
com.wunding.learn.file.api.dto.export.analysis.InsideLecturerCategoryStatAnalysisExportDTO.name=Lecturer Category
com.wunding.learn.file.api.dto.export.analysis.InsideLecturerCategoryStatAnalysisExportDTO.overallScore=Satisfaction
com.wunding.learn.file.api.dto.export.analysis.InsideLecturerCategoryStatAnalysisExportDTO.peopleNum=total people
com.wunding.learn.file.api.dto.export.analysis.InsideLecturerDepartmentStatAnalysisExportDTO.code=serial number
com.wunding.learn.file.api.dto.export.analysis.InsideLecturerDepartmentStatAnalysisExportDTO.name=department
com.wunding.learn.file.api.dto.export.analysis.InsideLecturerDepartmentStatAnalysisExportDTO.peopleNum=quantity
com.wunding.learn.file.api.dto.export.analysis.ItemVisitRecordExportDTO.itemName=program name
com.wunding.learn.file.api.dto.export.analysis.ItemVisitRecordExportDTO.statisticDate=date
com.wunding.learn.file.api.dto.export.analysis.ItemVisitRecordExportDTO.userNum=Number of people
com.wunding.learn.file.api.dto.export.analysis.ItemVisitRecordExportDTO.visitNum=Visitors
com.wunding.learn.file.api.dto.export.analysis.LearnRecordCourseStatAnalysisExportDTO.courseName=Course Title
com.wunding.learn.file.api.dto.export.analysis.LearnRecordCourseStatAnalysisExportDTO.maxViewTime=Recent study time
com.wunding.learn.file.api.dto.export.analysis.LearnRecordCourseStatAnalysisExportDTO.minViewTime=Start learning time
com.wunding.learn.file.api.dto.export.analysis.LearnRecordCourseStatAnalysisExportDTO.totalDuration=Effective study time
com.wunding.learn.file.api.dto.export.analysis.LearnRecordExamStatAnalysisExportDTO.answerTime=Submission time
com.wunding.learn.file.api.dto.export.analysis.LearnRecordExamStatAnalysisExportDTO.endTime=Exam end time
com.wunding.learn.file.api.dto.export.analysis.LearnRecordExamStatAnalysisExportDTO.examName=Exam name
com.wunding.learn.file.api.dto.export.analysis.LearnRecordExamStatAnalysisExportDTO.passScore=passing score
com.wunding.learn.file.api.dto.export.analysis.LearnRecordExamStatAnalysisExportDTO.startTime=Exam start time
com.wunding.learn.file.api.dto.export.analysis.LearnRecordExamStatAnalysisExportDTO.totalScore=total score
com.wunding.learn.file.api.dto.export.analysis.LearnRecordExamStatAnalysisExportDTO.userScore=User score
com.wunding.learn.file.api.dto.export.analysis.LearnRecordInfoStatAnalysisExportDTO.authorName=author
com.wunding.learn.file.api.dto.export.analysis.LearnRecordInfoStatAnalysisExportDTO.categoryName=Information category
com.wunding.learn.file.api.dto.export.analysis.LearnRecordInfoStatAnalysisExportDTO.infoName=Information name
com.wunding.learn.file.api.dto.export.analysis.LearnRecordInfoStatAnalysisExportDTO.keyWord=Key words
com.wunding.learn.file.api.dto.export.analysis.LearnRecordInfoStatAnalysisExportDTO.viewTime=View time
com.wunding.learn.file.api.dto.export.analysis.LearnRecordProjectStatAnalysisExportDTO.address=Place
com.wunding.learn.file.api.dto.export.analysis.LearnRecordProjectStatAnalysisExportDTO.endTime=Project end time
com.wunding.learn.file.api.dto.export.analysis.LearnRecordProjectStatAnalysisExportDTO.projectMethod=Training type
com.wunding.learn.file.api.dto.export.analysis.LearnRecordProjectStatAnalysisExportDTO.projectName=project name
com.wunding.learn.file.api.dto.export.analysis.LearnRecordProjectStatAnalysisExportDTO.projectNo=Study item number
com.wunding.learn.file.api.dto.export.analysis.LearnRecordProjectStatAnalysisExportDTO.projectType=plan type
com.wunding.learn.file.api.dto.export.analysis.LearnRecordProjectStatAnalysisExportDTO.startTime=Project start time
com.wunding.learn.file.api.dto.export.analysis.LearnRecordFaceProjectStatAnalysisExportDTO.address=Place
com.wunding.learn.file.api.dto.export.analysis.LearnRecordFaceProjectStatAnalysisExportDTO.endTime=End time of face-to-face classes
com.wunding.learn.file.api.dto.export.analysis.LearnRecordFaceProjectStatAnalysisExportDTO.projectMethod=Type of training
com.wunding.learn.file.api.dto.export.analysis.LearnRecordFaceProjectStatAnalysisExportDTO.projectName=The name of the face-to-face class
com.wunding.learn.file.api.dto.export.analysis.LearnRecordFaceProjectStatAnalysisExportDTO.projectNo=In-person class number
com.wunding.learn.file.api.dto.export.analysis.LearnRecordFaceProjectStatAnalysisExportDTO.projectType=The type of plan
com.wunding.learn.file.api.dto.export.analysis.LearnRecordFaceProjectStatAnalysisExportDTO.startTime=Face-to-face class start time
com.wunding.learn.file.api.dto.export.analysis.LearnRecordTrainStatAnalysisExportDTO.endTime=The end time of the training program
com.wunding.learn.file.api.dto.export.analysis.LearnRecordTrainStatAnalysisExportDTO.trainName=The name of the training program
com.wunding.learn.file.api.dto.export.analysis.LearnRecordTrainStatAnalysisExportDTO.trainNo=Training item number
com.wunding.learn.file.api.dto.export.analysis.LearnRecordTrainStatAnalysisExportDTO.startTime=The start time of the training program
com.wunding.learn.file.api.dto.export.analysis.LearnRecordStatAnalysisExportDTO.birthday=Birthday
com.wunding.learn.file.api.dto.export.analysis.LearnRecordStatAnalysisExportDTO.email=Mail
com.wunding.learn.file.api.dto.export.analysis.LearnRecordStatAnalysisExportDTO.examNum=Take the test
com.wunding.learn.file.api.dto.export.analysis.LearnRecordStatAnalysisExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.analysis.LearnRecordStatAnalysisExportDTO.joinDate=Joining date
com.wunding.learn.file.api.dto.export.analysis.LearnRecordStatAnalysisExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.analysis.LearnRecordStatAnalysisExportDTO.newsNum=Browse information
com.wunding.learn.file.api.dto.export.analysis.LearnRecordStatAnalysisExportDTO.orgPath=Department
com.wunding.learn.file.api.dto.export.analysis.LearnRecordStatAnalysisExportDTO.postName=Position
com.wunding.learn.file.api.dto.export.analysis.LearnRecordStatAnalysisExportDTO.projectNum=Number of study projects participated in
com.wunding.learn.file.api.dto.export.analysis.LearnRecordStatAnalysisExportDTO.faceProjectNum=Number of face-to-face classes attended
com.wunding.learn.file.api.dto.export.analysis.LearnRecordStatAnalysisExportDTO.trainNum=Number of training programs attended
com.wunding.learn.file.api.dto.export.analysis.LearnRecordStatAnalysisExportDTO.subtotal=Subtotal
com.wunding.learn.file.api.dto.export.analysis.LearnRecordStatAnalysisExportDTO.sex=gender
com.wunding.learn.file.api.dto.export.analysis.LearnRecordStatAnalysisExportDTO.surveyNum=Take the survey
com.wunding.learn.file.api.dto.export.analysis.LearnRecordStatAnalysisExportDTO.telephone=Telephone
com.wunding.learn.file.api.dto.export.analysis.LearnRecordStatAnalysisExportDTO.totalAgree=Number of likes
com.wunding.learn.file.api.dto.export.analysis.LearnRecordStatAnalysisExportDTO.totalComment=Number of comments
com.wunding.learn.file.api.dto.export.analysis.LearnRecordStatAnalysisExportDTO.totalCourse=Number of new courses
com.wunding.learn.file.api.dto.export.analysis.LearnRecordStatAnalysisExportDTO.totalLearn=Effective study time
com.wunding.learn.file.api.dto.export.analysis.LearnRecordSurveyStatAnalysisExportDTO.endTime=Survey end time
com.wunding.learn.file.api.dto.export.analysis.LearnRecordSurveyStatAnalysisExportDTO.finishTime=User submission time
com.wunding.learn.file.api.dto.export.analysis.LearnRecordSurveyStatAnalysisExportDTO.questionNum=Number of questions
com.wunding.learn.file.api.dto.export.analysis.LearnRecordSurveyStatAnalysisExportDTO.startTime=Survey start time
com.wunding.learn.file.api.dto.export.analysis.LearnRecordSurveyStatAnalysisExportDTO.surveyName=Survey name
#LecturerIntegralExportDTO
com.wunding.learn.file.api.dto.export.analysis.LecturerIntegralExportDTO.categoryCode=Lecturer classification
com.wunding.learn.file.api.dto.export.analysis.LecturerIntegralExportDTO.currentTime=Get time
com.wunding.learn.file.api.dto.export.analysis.LecturerIntegralExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.analysis.LecturerIntegralExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.analysis.LecturerIntegralExportDTO.num=Earn points
com.wunding.learn.file.api.dto.export.analysis.LecturerIntegralExportDTO.orgName=Department
com.wunding.learn.file.api.dto.export.analysis.LecturerIntegralExportDTO.orgPath=Department Full Path
com.wunding.learn.file.api.dto.export.analysis.LecturerIntegralExportDTO.relateLevelPathName=Full Path Of Related Department
#OnlineUserDayStatAnalysisExportDTO
com.wunding.learn.file.api.dto.export.analysis.OnlineUserDayStatAnalysisExportDTO.accessDate=date
com.wunding.learn.file.api.dto.export.analysis.OnlineUserDayStatAnalysisExportDTO.newLoginNum=First time online user
com.wunding.learn.file.api.dto.export.analysis.OnlineUserDayStatAnalysisExportDTO.onlineNum=Number of online users
com.wunding.learn.file.api.dto.export.analysis.OnlineUserDayStatAnalysisExportDTO.proportionOfOnlineUsers=Proportion of online users
com.wunding.learn.file.api.dto.export.analysis.OnlineUserDeptStatAnalysisExportDTO.newLoginNum=First time online user
com.wunding.learn.file.api.dto.export.analysis.OnlineUserDeptStatAnalysisExportDTO.onlineNum=Number of online users
com.wunding.learn.file.api.dto.export.analysis.OnlineUserDeptStatAnalysisExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.analysis.OnlineUserDeptStatAnalysisExportDTO.proportionOfOnlineUsers=Proportion of online users
com.wunding.learn.file.api.dto.export.analysis.OnlineUserMonthStatAnalysisExportDTO.accessMonth=date
com.wunding.learn.file.api.dto.export.analysis.OnlineUserMonthStatAnalysisExportDTO.newLoginNum=First time online user
com.wunding.learn.file.api.dto.export.analysis.OnlineUserMonthStatAnalysisExportDTO.onlineNum=Number of online users
com.wunding.learn.file.api.dto.export.analysis.OnlineUserMonthStatAnalysisExportDTO.proportionOfOnlineUsers=Proportion of online users
com.wunding.learn.file.api.dto.export.analysis.OrgLearnStatExportDTO.agreePerson=Number of likes
com.wunding.learn.file.api.dto.export.analysis.OrgLearnStatExportDTO.orgName=Department name
com.wunding.learn.file.api.dto.export.analysis.OrgLearnStatExportDTO.totalAgree=Number of likes
com.wunding.learn.file.api.dto.export.analysis.OrgLearnStatExportDTO.totalComment=Number of comments
com.wunding.learn.file.api.dto.export.analysis.OrgLearnStatExportDTO.totalCommentPerson=Number of comments
com.wunding.learn.file.api.dto.export.analysis.OrgLearnStatExportDTO.totalPerson=Number of students
com.wunding.learn.file.api.dto.export.analysis.OrgLearnStatExportDTO.totalShare=Number of shares
com.wunding.learn.file.api.dto.export.analysis.OrgLearnStatExportDTO.totalSharePerson=Number of shares
com.wunding.learn.file.api.dto.export.analysis.ScoreRankStatAnalysisExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.analysis.ScoreRankStatAnalysisExportDTO.orgPath=Department
com.wunding.learn.file.api.dto.export.analysis.ScoreRankStatAnalysisExportDTO.rankNum=Ranking
com.wunding.learn.file.api.dto.export.analysis.ScoreRankStatAnalysisExportDTO.title=title
com.wunding.learn.file.api.dto.export.analysis.ScoreRankStatAnalysisExportDTO.totalScore=Experience
com.wunding.learn.file.api.dto.export.analysis.StatSearchKeyExportDTO.keyword=Key words
com.wunding.learn.file.api.dto.export.analysis.StatSearchKeyExportDTO.searchCount=Searches
com.wunding.learn.file.api.dto.export.analysis.StatSearchKeyExportDTO.type=type
com.wunding.learn.file.api.dto.export.analysis.StudentUploadCoursewareStatAnalysisExportDTO.commentCount=Number of comments
com.wunding.learn.file.api.dto.export.analysis.StudentUploadCoursewareStatAnalysisExportDTO.createTime=Creation date
com.wunding.learn.file.api.dto.export.analysis.StudentUploadCoursewareStatAnalysisExportDTO.cwName=courseware
com.wunding.learn.file.api.dto.export.analysis.StudentUploadCoursewareStatAnalysisExportDTO.fullName=author
com.wunding.learn.file.api.dto.export.analysis.StudentUploadCoursewareStatAnalysisExportDTO.learnCount=Number of students
com.wunding.learn.file.api.dto.export.analysis.StudentUploadCoursewareStatAnalysisExportDTO.learnedCount=Number of people who have completed the course
com.wunding.learn.file.api.dto.export.analysis.StudentUploadCoursewareStatAnalysisExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.analysis.StudentUploadCoursewareStatAnalysisExportDTO.overallScore=Overall rating
com.wunding.learn.file.api.dto.export.analysis.TimeRegionStatAnalysisExportDTO.accessPoint=Views
com.wunding.learn.file.api.dto.export.analysis.TimeRegionStatAnalysisExportDTO.accessPointRatio=Visit proportion
com.wunding.learn.file.api.dto.export.analysis.TimeRegionStatAnalysisExportDTO.onlinePoint=Online users
com.wunding.learn.file.api.dto.export.analysis.TimeRegionStatAnalysisExportDTO.onlinePointRatio=Proportion of online users
com.wunding.learn.file.api.dto.export.analysis.TimeRegionStatAnalysisExportDTO.periodTime=period
com.wunding.learn.file.api.dto.export.analysis.TrainPlanStatAnalysisExportDTO.actualCost=Actual cost (yuan)
com.wunding.learn.file.api.dto.export.analysis.TrainPlanStatAnalysisExportDTO.actualPeriod=Issues completed
com.wunding.learn.file.api.dto.export.analysis.TrainPlanStatAnalysisExportDTO.actualPerson=actual number of visitors
com.wunding.learn.file.api.dto.export.analysis.TrainPlanStatAnalysisExportDTO.orgPath=organizer
com.wunding.learn.file.api.dto.export.analysis.TrainPlanStatAnalysisExportDTO.particularYear=years
com.wunding.learn.file.api.dto.export.analysis.TrainPlanStatAnalysisExportDTO.pay=expenditure
com.wunding.learn.file.api.dto.export.analysis.TrainPlanStatAnalysisExportDTO.progress=schedule
com.wunding.learn.file.api.dto.export.analysis.TrainPlanStatAnalysisExportDTO.trainFee=Cost budget (yuan)
com.wunding.learn.file.api.dto.export.analysis.TrainPlanStatAnalysisExportDTO.trainPeriod=Number of planning periods
com.wunding.learn.file.api.dto.export.analysis.TrainPlanStatAnalysisExportDTO.trainPlanName=Plan name
com.wunding.learn.file.api.dto.export.analysis.TrainPlanStatAnalysisExportDTO.trainRatio=training ratio
com.wunding.learn.file.api.dto.export.analysis.TrainPlanStatAnalysisExportDTO.trainTrips=Planned visits
com.wunding.learn.file.api.dto.export.analysis.UserExcitationRecordStatAnalysisExportDTO.category=learning activities
com.wunding.learn.file.api.dto.export.analysis.UserExcitationRecordStatAnalysisExportDTO.createTime=Get Time
com.wunding.learn.file.api.dto.export.analysis.UserExcitationRecordStatAnalysisExportDTO.eventName=Inspiring event name
com.wunding.learn.file.api.dto.export.analysis.UserExcitationRecordStatAnalysisExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.analysis.UserExcitationRecordStatAnalysisExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.analysis.UserExcitationRecordStatAnalysisExportDTO.operateNum=Get value
com.wunding.learn.file.api.dto.export.analysis.UserExcitationRecordStatAnalysisExportDTO.targetName=Motivational Notes
com.wunding.learn.file.api.dto.export.analysis.UserExcitationRecordStatAnalysisExportDTO.typeName=Incentive type name
com.wunding.learn.file.api.dto.export.analysis.UserIntegralDetailExportDTO.createTime=add time
com.wunding.learn.file.api.dto.export.analysis.UserIntegralDetailExportDTO.description=Remark
com.wunding.learn.file.api.dto.export.analysis.UserIntegralDetailExportDTO.score=Earn points
com.wunding.learn.file.api.dto.export.analysis.UserIntegralDetailStatAnalysisExportDTO.createTime=time
com.wunding.learn.file.api.dto.export.analysis.UserIntegralDetailStatAnalysisExportDTO.description=Remark
com.wunding.learn.file.api.dto.export.analysis.UserIntegralDetailStatAnalysisExportDTO.score=Earn points
com.wunding.learn.file.api.dto.export.analysis.UserIntegralStatAnalysisExportDTO.availableNum=Earn points
com.wunding.learn.file.api.dto.export.analysis.UserIntegralStatAnalysisExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.analysis.UserIntegralStatAnalysisExportDTO.loginName=Account
com.wunding.learn.file.api.dto.export.analysis.UserIntegralStatAnalysisExportDTO.orgName=Department
com.wunding.learn.file.api.dto.export.analysis.UserIntegralStatAnalysisExportDTO.levelPathName=Department Full Path
com.wunding.learn.file.api.dto.export.analysis.UserIntegralStatAnalysisExportDTO.relateLevelPathName=Full Path Of Related Department
com.wunding.learn.file.api.dto.export.apply.ApplyExportDTO.applyNo=serial number
com.wunding.learn.file.api.dto.export.apply.ApplyExportDTO.applyStatus=application status
com.wunding.learn.file.api.dto.export.apply.ApplyExportDTO.applySubject=Application topic
com.wunding.learn.file.api.dto.export.apply.ApplyExportDTO.applyTime=Date of Application
com.wunding.learn.file.api.dto.export.apply.ApplyExportDTO.applyUser=applicant
com.wunding.learn.file.api.dto.export.apply.ApplyExportDTO.applyUserNo=Applicant account
com.wunding.learn.file.api.dto.export.apply.ApplyExportDTO.endTime=Use end time
com.wunding.learn.file.api.dto.export.apply.ApplyExportDTO.orgPath=Apply for an organization
com.wunding.learn.file.api.dto.export.apply.ApplyExportDTO.resourcesType=Resource Type
com.wunding.learn.file.api.dto.export.apply.ApplyExportDTO.startTime=Use start time
com.wunding.learn.file.api.dto.export.appraise.AppraiseDetailExportDTO.goDownNum=Number of people who have rated
com.wunding.learn.file.api.dto.export.appraise.AppraiseDetailExportDTO.providerName=assessee
com.wunding.learn.file.api.dto.export.appraise.AppraiseDetailExportDTO.title=title
com.wunding.learn.file.api.dto.export.appraise.AppraiseDetailExportDTO.totalScore=total score
com.wunding.learn.file.api.dto.export.appraise.AppraiseExportDTO.address=Place
com.wunding.learn.file.api.dto.export.appraise.AppraiseExportDTO.code=serial number
com.wunding.learn.file.api.dto.export.appraise.AppraiseExportDTO.isPublish=Post status
com.wunding.learn.file.api.dto.export.appraise.AppraiseExportDTO.judgeBeginTime=Review start time
com.wunding.learn.file.api.dto.export.appraise.AppraiseExportDTO.materialBeginTime=Material submission start time
com.wunding.learn.file.api.dto.export.appraise.AppraiseExportDTO.providerUserCount=Number of people being evaluated
com.wunding.learn.file.api.dto.export.appraise.AppraiseExportDTO.refereeUserCount=Number of judges
com.wunding.learn.file.api.dto.export.appraise.AppraiseExportDTO.title=title
com.wunding.learn.file.api.dto.export.appraise.AppraiseExportDTO.type=Review type
com.wunding.learn.file.api.dto.export.appraise.AppraiseFileType2ExportDTO.description=Material provision instructions
com.wunding.learn.file.api.dto.export.appraise.AppraiseFileType2ExportDTO.fileMaxSize=File size limit (M)
com.wunding.learn.file.api.dto.export.appraise.AppraiseFileType2ExportDTO.fileType=Material support type
com.wunding.learn.file.api.dto.export.appraise.AppraiseFileType2ExportDTO.isSaveLib=Whether to be put into storage
com.wunding.learn.file.api.dto.export.appraise.AppraiseFileType2ExportDTO.required=Is it required?
com.wunding.learn.file.api.dto.export.appraise.AppraiseFileType2ExportDTO.title=Material title
com.wunding.learn.file.api.dto.export.appraise.AppraiseFileTypeExportDTO.description=Material provision instructions
com.wunding.learn.file.api.dto.export.appraise.AppraiseFileTypeExportDTO.fileMaxSize=File size limit (M)
com.wunding.learn.file.api.dto.export.appraise.AppraiseFileTypeExportDTO.fileType=Material support type
com.wunding.learn.file.api.dto.export.appraise.AppraiseFileTypeExportDTO.required=Is it required?
com.wunding.learn.file.api.dto.export.appraise.AppraiseFileTypeExportDTO.title=Material title
com.wunding.learn.file.api.dto.export.appraise.AppraiseHistoryDetailExportDTO.createTime=Evaluation time
com.wunding.learn.file.api.dto.export.appraise.AppraiseHistoryDetailExportDTO.levelPathName=department
com.wunding.learn.file.api.dto.export.appraise.AppraiseHistoryDetailExportDTO.loginName=Account
com.wunding.learn.file.api.dto.export.appraise.AppraiseHistoryDetailExportDTO.refereeName=Judge's name
com.wunding.learn.file.api.dto.export.appraise.AppraiseHistoryDetailExportDTO.scored=Score
com.wunding.learn.file.api.dto.export.appraise.AppraiseHistoryDetailExportDTO.weight=Weights
com.wunding.learn.file.api.dto.export.appraise.AppraiseHistoryDetailExportDTO.weightedScored=weighted score
com.wunding.learn.file.api.dto.export.appraise.AppraiseProviderExportDTO.appraisedCount=Number of reviews
com.wunding.learn.file.api.dto.export.appraise.AppraiseProviderExportDTO.avgScore=The average score
com.wunding.learn.file.api.dto.export.appraise.AppraiseProviderExportDTO.avgWeightScore=Weighted average
com.wunding.learn.file.api.dto.export.appraise.AppraiseProviderExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.appraise.AppraiseProviderExportDTO.isFinishAppraise=Review status
com.wunding.learn.file.api.dto.export.appraise.AppraiseProviderExportDTO.isSubmitAppraiseFile=Material
com.wunding.learn.file.api.dto.export.appraise.AppraiseProviderExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.appraise.AppraiseProviderExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.appraise.AppraiseProviderExportDTO.source=source
com.wunding.learn.file.api.dto.export.appraise.AppraiseProviderFileExportDTO.addDate=Upload time
com.wunding.learn.file.api.dto.export.appraise.AppraiseProviderFileExportDTO.fileName=file name
com.wunding.learn.file.api.dto.export.appraise.AppraiseProviderFileExportDTO.fileSize=File size
com.wunding.learn.file.api.dto.export.appraise.AppraiseProviderFileExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.appraise.AppraiseProviderFileExportDTO.isSaveLib=Whether materials are to be put into storage
com.wunding.learn.file.api.dto.export.appraise.AppraiseProviderFileExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.appraise.AppraiseProviderFileExportDTO.score=latest score
com.wunding.learn.file.api.dto.export.appraise.AppraiseProviderFileExportDTO.status=Review status
com.wunding.learn.file.api.dto.export.appraise.AppraiseProviderFileExportDTO.title=Material title
com.wunding.learn.file.api.dto.export.appraise.AppraiseRefereeExportDTO.appraisedUserCount=Number of people who have rated
com.wunding.learn.file.api.dto.export.appraise.AppraiseRefereeExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.appraise.AppraiseRefereeExportDTO.isAnonymous=Anonymous or not
com.wunding.learn.file.api.dto.export.appraise.AppraiseRefereeExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.appraise.AppraiseRefereeExportDTO.notAppraisedUserCount=Number of people who have not rated
com.wunding.learn.file.api.dto.export.appraise.AppraiseRefereeExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.appraise.AppraiseRefereeExportDTO.weight=Weights
com.wunding.learn.file.api.dto.export.appraise.MeetingDetailExportDTO.avgScore=The average score
com.wunding.learn.file.api.dto.export.appraise.MeetingDetailExportDTO.cPostName=Certification positions
com.wunding.learn.file.api.dto.export.appraise.MeetingDetailExportDTO.curRefereeNum=Number of votes
com.wunding.learn.file.api.dto.export.appraise.MeetingDetailExportDTO.isPass=Opinion
com.wunding.learn.file.api.dto.export.appraise.MeetingDetailExportDTO.levelPathName=department
com.wunding.learn.file.api.dto.export.appraise.MeetingDetailExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.appraise.MeetingDetailExportDTO.noPassCount=Fail
com.wunding.learn.file.api.dto.export.appraise.MeetingDetailExportDTO.passCount=pass
com.wunding.learn.file.api.dto.export.appraise.MeetingDetailExportDTO.postName=post
com.wunding.learn.file.api.dto.export.appraise.MeetingDetailExportDTO.providerName=Name
com.wunding.learn.file.api.dto.export.appraise.MeetingDetailExportDTO.sortNo=Order of defense
com.wunding.learn.file.api.dto.export.appraise.MeetingProviderExportDTO.endTime=End Time
com.wunding.learn.file.api.dto.export.appraise.MeetingProviderExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.appraise.MeetingProviderExportDTO.isException=Is it an exception?
com.wunding.learn.file.api.dto.export.appraise.MeetingProviderExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.appraise.MeetingProviderExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.appraise.MeetingProviderExportDTO.postName=post
com.wunding.learn.file.api.dto.export.appraise.MeetingProviderExportDTO.sortNo=Order of defense
com.wunding.learn.file.api.dto.export.appraise.MeetingProviderExportDTO.startTime=Starting time
com.wunding.learn.file.api.dto.export.certification.CerDeptTargetReportExportDTO.cerName=Certificate name
com.wunding.learn.file.api.dto.export.certification.CerDeptTargetReportExportDTO.cerType=Certificate classification
com.wunding.learn.file.api.dto.export.certification.CerDeptTargetReportExportDTO.expireCount=Expiration number
com.wunding.learn.file.api.dto.export.certification.CerDeptTargetReportExportDTO.holdCount=Current holding quantity
com.wunding.learn.file.api.dto.export.certification.CerDeptTargetReportExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.certification.CerDeptTargetReportExportDTO.reserveHoldCount=Current effective holding quantity
com.wunding.learn.file.api.dto.export.certification.CerDeptTargetReportExportDTO.reserveTargetCoverage=target effective coverage
com.wunding.learn.file.api.dto.export.certification.CerDeptTargetReportExportDTO.setupName=Certification System
com.wunding.learn.file.api.dto.export.certification.CerDeptTargetReportExportDTO.targetCount=Target holding quantity
com.wunding.learn.file.api.dto.export.certification.CerDeptTargetReportExportDTO.targetCoverage=target coverage
com.wunding.learn.file.api.dto.export.certification.CertWorkTypeDTO.certifiedName=Certificate name
com.wunding.learn.file.api.dto.export.certification.CertWorkTypeDTO.workTypeBig=Major categories of work
com.wunding.learn.file.api.dto.export.certification.CertWorkTypeDTO.workTypeName=Job name
com.wunding.learn.file.api.dto.export.certification.CertWorkTypeDTO.workTypeSmall=Subcategories of work
com.wunding.learn.file.api.dto.export.certification.CertificationDeptTargetExportDTO.cerName=Certificate name
com.wunding.learn.file.api.dto.export.certification.CertificationDeptTargetExportDTO.cerType=Certificate classification
com.wunding.learn.file.api.dto.export.certification.CertificationDeptTargetExportDTO.orgCode=organization code
com.wunding.learn.file.api.dto.export.certification.CertificationDeptTargetExportDTO.orgName=name of association
com.wunding.learn.file.api.dto.export.certification.CertificationDeptTargetExportDTO.orgPathName=Organization full path name
com.wunding.learn.file.api.dto.export.certification.CertificationDeptTargetExportDTO.setupName=Certification System
com.wunding.learn.file.api.dto.export.certification.CertificationDeptTargetExportDTO.targetCount=target quantity
com.wunding.learn.file.api.dto.export.certification.CertificationExportDTO.certCategoryName=Certificate classification
com.wunding.learn.file.api.dto.export.certification.CertificationExportDTO.certificationSetupName=Certification system
com.wunding.learn.file.api.dto.export.certification.CertificationExportDTO.isAvailable=Enabled status
com.wunding.learn.file.api.dto.export.certification.CertificationExportDTO.name=Certification name
com.wunding.learn.file.api.dto.export.certification.CertificationExportDTO.orgName=Issuing organization
com.wunding.learn.file.api.dto.export.certification.CertificationExportDTO.validPeriod=Certificate validity period
com.wunding.learn.file.api.dto.export.certification.CertificationHoldUserExportDTO.cerCategoryName=Certificate classification
com.wunding.learn.file.api.dto.export.certification.CertificationHoldUserExportDTO.cerName=Certificate name
com.wunding.learn.file.api.dto.export.certification.CertificationHoldUserExportDTO.cerSetupName=Certification System
com.wunding.learn.file.api.dto.export.certification.CertificationHoldUserExportDTO.certificateEndTime=Date of Expiry
com.wunding.learn.file.api.dto.export.certification.CertificationHoldUserExportDTO.certificateTime=Certificate holding date
com.wunding.learn.file.api.dto.export.certification.CertificationHoldUserExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.certification.CertificationHoldUserExportDTO.isCertificate=Whether to hold a certificate
com.wunding.learn.file.api.dto.export.certification.CertificationHoldUserExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.certification.CertificationHoldUserExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.certification.CertificationLevelExportDTO.isPublic=Is it public level?
com.wunding.learn.file.api.dto.export.certification.CertificationLevelExportDTO.levelName=Level name
com.wunding.learn.file.api.dto.export.certification.CertificationLevelExportDTO.setupName=Belonging system
com.wunding.learn.file.api.dto.export.certification.CertificationRelateDeptExportDTO.acquireType=Channels for obtaining certification
com.wunding.learn.file.api.dto.export.certification.CertificationRelateDeptExportDTO.cerName=Certificate name
com.wunding.learn.file.api.dto.export.certification.CertificationRelateDeptExportDTO.certNo=Certificate encoding
com.wunding.learn.file.api.dto.export.certification.CertificationRelateDeptExportDTO.certificationSetupName=Certification System
com.wunding.learn.file.api.dto.export.certification.CertificationRelateDeptExportDTO.createTime=Certification date
com.wunding.learn.file.api.dto.export.certification.CertificationRelateDeptExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.certification.CertificationRelateDeptExportDTO.lastRecertificationTime=Latest renewal date
com.wunding.learn.file.api.dto.export.certification.CertificationRelateDeptExportDTO.levelPathName=Certificate classification
com.wunding.learn.file.api.dto.export.certification.CertificationRelateDeptExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.certification.CertificationRelateDeptExportDTO.reserve=reserve effectiveness
com.wunding.learn.file.api.dto.export.certification.CertificationRelateDeptExportDTO.userName=account
com.wunding.learn.file.api.dto.export.certification.CertificationRelateDeptExportDTO.userScore=score
com.wunding.learn.file.api.dto.export.certification.CertificationRelateDeptExportDTO.validPeriod=Validity period (months)
com.wunding.learn.file.api.dto.export.certification.CertificationRelateExportDTO.acquireType=Channels for obtaining certification
com.wunding.learn.file.api.dto.export.certification.CertificationRelateExportDTO.cerName=Certificate name
com.wunding.learn.file.api.dto.export.certification.CertificationRelateExportDTO.certNo=Certificate encoding
com.wunding.learn.file.api.dto.export.certification.CertificationRelateExportDTO.certificateEndTime=Date of Expiry
com.wunding.learn.file.api.dto.export.certification.CertificationRelateExportDTO.certificationSetupName=Certificate system
com.wunding.learn.file.api.dto.export.certification.CertificationRelateExportDTO.createTime=Certification date
com.wunding.learn.file.api.dto.export.certification.CertificationRelateExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.certification.CertificationRelateExportDTO.lastRecertificationTime=Latest renewal date
com.wunding.learn.file.api.dto.export.certification.CertificationRelateExportDTO.levelPathName=Certificate classification
com.wunding.learn.file.api.dto.export.certification.CertificationRelateExportDTO.orgName=department
com.wunding.learn.file.api.dto.export.certification.CertificationRelateExportDTO.reserve=reserve effectiveness
com.wunding.learn.file.api.dto.export.certification.CertificationRelateExportDTO.reviewDate=Review date
com.wunding.learn.file.api.dto.export.certification.CertificationRelateExportDTO.userName=account
com.wunding.learn.file.api.dto.export.certification.CertificationRelateExportDTO.userScore=score
com.wunding.learn.file.api.dto.export.certification.CertificationRelateExportDTO.validPeriod=Validity period (months)
com.wunding.learn.file.api.dto.export.certification.CertificationRuleExportDTO.certCategoryName=Certificate classification
com.wunding.learn.file.api.dto.export.certification.CertificationRuleExportDTO.certificationName=Certificate name
com.wunding.learn.file.api.dto.export.certification.CertificationRuleExportDTO.certificationSetupName=Certification System
com.wunding.learn.file.api.dto.export.certification.CertificationRuleExportDTO.contentName=Related content
com.wunding.learn.file.api.dto.export.certification.CertificationRuleExportDTO.contentType=Associated content types
com.wunding.learn.file.api.dto.export.certification.CertificationRuleExportDTO.isAvailable=Whether to enable
com.wunding.learn.file.api.dto.export.certification.CertificationRuleExportDTO.isDefault=Is it the default
com.wunding.learn.file.api.dto.export.certification.CertificationRuleExportDTO.isRenewal=Whether to renew
com.wunding.learn.file.api.dto.export.certification.CertificationRuleExportDTO.name=Rule name
com.wunding.learn.file.api.dto.export.certification.CertificationRuleExportDTO.validPeriod=Validity period
com.wunding.learn.file.api.dto.export.certification.CertificationSetupExportDTO.description=System description
com.wunding.learn.file.api.dto.export.certification.CertificationSetupExportDTO.organization=certification organization
com.wunding.learn.file.api.dto.export.certification.CertificationSetupExportDTO.setupName=System name
com.wunding.learn.file.api.dto.export.certification.CertifiedCategoryExportDTO.categoryCode=Classification coding
com.wunding.learn.file.api.dto.export.certification.CertifiedCategoryExportDTO.categoryName=Category Name
com.wunding.learn.file.api.dto.export.certification.CertifiedCategoryExportDTO.parentCategoryName=Sub-headings
com.wunding.learn.file.api.dto.export.certification.CertifiedCategoryExportDTO.setupName=Certification System
com.wunding.learn.file.api.dto.export.certification.CertifiedCategoryExportDTO.status=state
com.wunding.learn.file.api.dto.export.course.CategoryExportDTO.categoryName=Category Name
com.wunding.learn.file.api.dto.export.course.CategoryExportDTO.categoryType=Classification categories
com.wunding.learn.file.api.dto.export.course.CategoryExportDTO.isAvailable=Whether to enable
com.wunding.learn.file.api.dto.export.course.CategoryExportDTO.parentName=Sub-headings
com.wunding.learn.file.api.dto.export.course.CategoryExportDTO.sortNo=display order
com.wunding.learn.file.api.dto.export.course.CourseCategoryExportDTO.chineseName=Chinese name
com.wunding.learn.file.api.dto.export.course.CourseCategoryExportDTO.englishName=English name
com.wunding.learn.file.api.dto.export.course.CourseCategoryExportDTO.isAvailable=Whether to enable
com.wunding.learn.file.api.dto.export.course.CourseCategoryExportDTO.name=default name
com.wunding.learn.file.api.dto.export.course.CourseCategoryExportDTO.pName=Sub-headings
com.wunding.learn.file.api.dto.export.course.CourseCategoryExportDTO.sortNo=display order
com.wunding.learn.file.api.dto.export.course.CourseCommentExportDTO.content=comments
com.wunding.learn.file.api.dto.export.course.CourseCommentExportDTO.createTime=Comment time
com.wunding.learn.file.api.dto.export.course.CourseCommentExportDTO.fullName=Commentator
com.wunding.learn.file.api.dto.export.course.CourseCommentExportDTO.isDel=state
com.wunding.learn.file.api.dto.export.course.CourseCommentExportDTO.loginName=Commenter account
com.wunding.learn.file.api.dto.export.course.CourseCommentExportDTO.orgName=Commenter organization
com.wunding.learn.file.api.dto.export.course.CourseCommentExportDTO.resourceName=Course Title
com.wunding.learn.file.api.dto.export.course.CourseCommentExportDTO.source=source
com.wunding.learn.file.api.dto.export.course.CourseExportDTO.author=author
com.wunding.learn.file.api.dto.export.course.CourseExportDTO.courseCateName=course sorts
com.wunding.learn.file.api.dto.export.course.CourseExportDTO.courseName=Course Title
com.wunding.learn.file.api.dto.export.course.CourseExportDTO.courseNo=Course No
com.wunding.learn.file.api.dto.export.course.CourseExportDTO.courseWareNum=Number of courseware
com.wunding.learn.file.api.dto.export.course.CourseExportDTO.isPublish=Post status
com.wunding.learn.file.api.dto.export.course.CourseExportDTO.auditStatusStr=Audit status
com.wunding.learn.file.api.dto.export.course.CourseExportDTO.isShare=Share or not
com.wunding.learn.file.api.dto.export.course.CourseExportDTO.keywordStr=Key words
com.wunding.learn.file.api.dto.export.course.CourseExportDTO.orgPath=Create organization name
com.wunding.learn.file.api.dto.export.course.CourseExportDTO.pv=pv
com.wunding.learn.file.api.dto.export.course.CourseExportDTO.publishName=Publisher name
com.wunding.learn.file.api.dto.export.course.CourseExportDTO.publishTime=release time
com.wunding.learn.file.api.dto.export.course.CourseExportDTO.viewCount=Number of students
com.wunding.learn.file.api.dto.export.course.CourseLearnExportDTO.beginLearnTime=Starting time
com.wunding.learn.file.api.dto.export.course.CourseLearnExportDTO.courseName=Course Title
com.wunding.learn.file.api.dto.export.course.CourseLearnExportDTO.courseNo=Course code
com.wunding.learn.file.api.dto.export.course.CourseLearnExportDTO.courseWareCount=Total number of courseware
com.wunding.learn.file.api.dto.export.course.CourseLearnExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.course.CourseLearnExportDTO.jobName=Position
com.wunding.learn.file.api.dto.export.course.CourseLearnExportDTO.lastLearnTime=Complete time
com.wunding.learn.file.api.dto.export.course.CourseLearnExportDTO.learnState=learning progress
com.wunding.learn.file.api.dto.export.course.CourseLearnExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.course.CourseLearnExportDTO.orgName=Course Management Unit
com.wunding.learn.file.api.dto.export.course.CourseLearnExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.course.CourseLearnExportDTO.remainderCourseWareCount=Number of remaining courseware
com.wunding.learn.file.api.dto.export.course.CourseNoteExportDTO.auditBy=reviewer
com.wunding.learn.file.api.dto.export.course.CourseNoteExportDTO.auditStatus=Approval Status
com.wunding.learn.file.api.dto.export.course.CourseNoteExportDTO.auditTime=review time
com.wunding.learn.file.api.dto.export.course.CourseNoteExportDTO.content=Note content
com.wunding.learn.file.api.dto.export.course.CourseNoteExportDTO.createTime=Record time
com.wunding.learn.file.api.dto.export.course.CourseNoteExportDTO.cwName=Courseware name
com.wunding.learn.file.api.dto.export.course.CourseNoteExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.course.CourseNoteExportDTO.markPosition=mark location
com.wunding.learn.file.api.dto.export.course.CourseNoteExportDTO.orgName=department
com.wunding.learn.file.api.dto.export.course.CourseNoteExportDTO.screenshotPath=Attached pictures
com.wunding.learn.file.api.dto.export.course.CourseStudyStatExportDTO.learnedUserCount=Number of people who have completed the course
com.wunding.learn.file.api.dto.export.course.CourseStudyStatExportDTO.learningUserCount=Number of people studying
com.wunding.learn.file.api.dto.export.course.CourseStudyStatExportDTO.orgName=name of association
com.wunding.learn.file.api.dto.export.course.CourseStudyStatExportDTO.unLearnUserCount=Number of people who have not studied
com.wunding.learn.file.api.dto.export.course.CourseStudyStatExportDTO.viewLimitUserCount=Number of people issued
com.wunding.learn.file.api.dto.export.course.CourseStudyStatUserRecordExportDTO.fullName=username
com.wunding.learn.file.api.dto.export.course.CourseStudyStatUserRecordExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.course.CourseStudyStatUserRecordExportDTO.orgName=Department name
com.wunding.learn.file.api.dto.export.course.CourseStudyWorkStatisticsExportDTO.learnedUserCount=Number of people who have completed the course
com.wunding.learn.file.api.dto.export.course.CourseStudyWorkStatisticsExportDTO.learningUserCount=Number of people studying
com.wunding.learn.file.api.dto.export.course.CourseStudyWorkStatisticsExportDTO.orgName=name of association
com.wunding.learn.file.api.dto.export.course.CourseStudyWorkStatisticsExportDTO.unLearnUserCount=Number of people who have not studied
com.wunding.learn.file.api.dto.export.course.CourseStudyWorkStatisticsExportDTO.viewLimitUserCount=Number of people issued
com.wunding.learn.file.api.dto.export.course.CourseSysCategoryExportDTO.isAvailable=Whether to enable
com.wunding.learn.file.api.dto.export.course.CourseSysCategoryExportDTO.categoryName=Category Name
com.wunding.learn.file.api.dto.export.course.CourseSysCategoryExportDTO.parentName=Parent Name
com.wunding.learn.file.api.dto.export.course.CourseSysCategoryExportDTO.sortNo=display order
com.wunding.learn.file.api.dto.export.course.CourseTagCategoryExportDTO.categoryName=Category Name
com.wunding.learn.file.api.dto.export.course.CourseTagCategoryExportDTO.isAvailable=Whether to enable
com.wunding.learn.file.api.dto.export.course.CourseTagCategoryExportDTO.parentName=Sub-headings
com.wunding.learn.file.api.dto.export.course.CourseTagCategoryExportDTO.sortNo=display order
com.wunding.learn.file.api.dto.export.course.CourseTagManageExportDTO.defaultType=default
com.wunding.learn.file.api.dto.export.course.CourseTagManageExportDTO.isAvailable=Whether to enable
com.wunding.learn.file.api.dto.export.course.CourseTagManageExportDTO.isOptional=User choice
com.wunding.learn.file.api.dto.export.course.CourseTagManageExportDTO.isShow=Front-end display
com.wunding.learn.file.api.dto.export.course.CourseTagManageExportDTO.levelPathName=full path name
com.wunding.learn.file.api.dto.export.course.CourseTagManageExportDTO.sortNo=display order
com.wunding.learn.file.api.dto.export.course.CourseTagManageExportDTO.tagClassifyName=Tag classification
com.wunding.learn.file.api.dto.export.course.CourseTagManageExportDTO.tagName=Tag name
com.wunding.learn.file.api.dto.export.course.CourseTagStaExportDTO.levelPathName=full path name
com.wunding.learn.file.api.dto.export.course.CourseTagStaExportDTO.tagClassifyName=Tag classification
com.wunding.learn.file.api.dto.export.course.CourseTagStaExportDTO.tagCollectNum=Number of collectors
com.wunding.learn.file.api.dto.export.course.CourseTagStaExportDTO.tagHoldNum=Number of tag holders
com.wunding.learn.file.api.dto.export.course.CourseTagStaExportDTO.tagName=Tag name
com.wunding.learn.file.api.dto.export.course.CourseWareLearnExportDTO.courseCategoryName=course sorts
com.wunding.learn.file.api.dto.export.course.CourseWareLearnExportDTO.courseName=Course Title
com.wunding.learn.file.api.dto.export.course.CourseWareLearnExportDTO.courseNo=Course code
com.wunding.learn.file.api.dto.export.course.CourseWareLearnExportDTO.cwName=Courseware name
com.wunding.learn.file.api.dto.export.course.CourseWareLearnExportDTO.duration=Learning time (seconds)
com.wunding.learn.file.api.dto.export.course.CourseWareLearnExportDTO.endTime=end study time
com.wunding.learn.file.api.dto.export.course.CourseWareLearnExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.course.CourseWareLearnExportDTO.jobName=Position
com.wunding.learn.file.api.dto.export.course.CourseWareLearnExportDTO.learnState=learning progress
com.wunding.learn.file.api.dto.export.course.CourseWareLearnExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.course.CourseWareLearnExportDTO.orgName=Course Management Unit
com.wunding.learn.file.api.dto.export.course.CourseWareLearnExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.course.CourseWareLearnExportDTO.playTime=Courseware duration (seconds)
com.wunding.learn.file.api.dto.export.course.CourseWareLearnExportDTO.startTime=Start learning time
com.wunding.learn.file.api.dto.export.course.CourseWareLearnExportDTO.userScore=test scores
com.wunding.learn.file.api.dto.export.course.CoursewarePackageExportDTO.createBy=Uploaded by
com.wunding.learn.file.api.dto.export.course.CoursewarePackageExportDTO.createTime=Upload time
com.wunding.learn.file.api.dto.export.course.CoursewarePackageExportDTO.cwName=Courseware name
com.wunding.learn.file.api.dto.export.course.CoursewarePackageExportDTO.isAudit=state
com.wunding.learn.file.api.dto.export.course.CoursewarePackageExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.course.CoursewarePackageExportDTO.transFormStatus=Transition state
com.wunding.learn.file.api.dto.export.course.CoursewareStudyDetail2ExportDTO.beginLearnTime=Starting time
com.wunding.learn.file.api.dto.export.course.CoursewareStudyDetail2ExportDTO.courseWareName=Courseware name
com.wunding.learn.file.api.dto.export.course.CoursewareStudyDetail2ExportDTO.duration=Accumulated time
com.wunding.learn.file.api.dto.export.course.CoursewareStudyDetail2ExportDTO.finishLearnTime=Complete time
com.wunding.learn.file.api.dto.export.course.CoursewareStudyDetail2ExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.course.CoursewareStudyDetail2ExportDTO.jobName=post
com.wunding.learn.file.api.dto.export.course.CoursewareStudyDetail2ExportDTO.lastLearnTime=recent studies
com.wunding.learn.file.api.dto.export.course.CoursewareStudyDetail2ExportDTO.learnState=Learning Status
com.wunding.learn.file.api.dto.export.course.CoursewareStudyDetail2ExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.course.CoursewareStudyDetail2ExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.course.CoursewareStudyDetail2ExportDTO.passState=pass status
com.wunding.learn.file.api.dto.export.course.CoursewareStudyDetail2ExportDTO.userScore=score
com.wunding.learn.file.api.dto.export.course.CoursewareStudyDetailExportDTO.beginLearnTime=Starting time
com.wunding.learn.file.api.dto.export.course.CoursewareStudyDetailExportDTO.courseWareName=Courseware name
com.wunding.learn.file.api.dto.export.course.CoursewareStudyDetailExportDTO.duration=Accumulated time
com.wunding.learn.file.api.dto.export.course.CoursewareStudyDetailExportDTO.finishLearnTime=Complete time
com.wunding.learn.file.api.dto.export.course.CoursewareStudyDetailExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.course.CoursewareStudyDetailExportDTO.jobName=post
com.wunding.learn.file.api.dto.export.course.CoursewareStudyDetailExportDTO.lastLearnTime=recent studies
com.wunding.learn.file.api.dto.export.course.CoursewareStudyDetailExportDTO.learnState=Learning Status
com.wunding.learn.file.api.dto.export.course.CoursewareStudyDetailExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.course.CoursewareStudyDetailExportDTO.orgName=department
com.wunding.learn.file.api.dto.export.course.CoursewareStudyDetailExportDTO.orgPath=department path
com.wunding.learn.file.api.dto.export.course.CoursewareStudyDetailExportDTO.passState=pass status
com.wunding.learn.file.api.dto.export.course.CoursewareStudyDetailExportDTO.userScore=score
com.wunding.learn.file.api.dto.export.course.MergeCourseExportDTO.createBy=Uploader
com.wunding.learn.file.api.dto.export.course.MergeCourseExportDTO.createTime=Upload time
com.wunding.learn.file.api.dto.export.course.MergeCourseExportDTO.cwName=Courseware name
com.wunding.learn.file.api.dto.export.course.MergeCourseExportDTO.isAudit=state
com.wunding.learn.file.api.dto.export.course.MergeCourseExportDTO.orgName=department
com.wunding.learn.file.api.dto.export.course.TrainCourseExportDTO.courseName=Course Title
com.wunding.learn.file.api.dto.export.course.TrainCourseExportDTO.createLoginName=account
com.wunding.learn.file.api.dto.export.course.TrainCourseExportDTO.createName=founder
com.wunding.learn.file.api.dto.export.course.TrainCourseExportDTO.createTime=creation time
com.wunding.learn.file.api.dto.export.course.TrainCourseExportDTO.isPublish=release
com.wunding.learn.file.api.dto.export.course.TrainCourseExportDTO.publishLoginName=account
com.wunding.learn.file.api.dto.export.course.TrainCourseExportDTO.publishName=publisher
com.wunding.learn.file.api.dto.export.course.TrainCourseExportDTO.publishTime=release time
com.wunding.learn.file.api.dto.export.emigrated.CheckpointExportDTO.name=Level name
com.wunding.learn.file.api.dto.export.emigrated.CheckpointExportDTO.sort=serial number
com.wunding.learn.file.api.dto.export.emigrated.EmigratedExportDTO.beginTime=Breakthrough start time
com.wunding.learn.file.api.dto.export.emigrated.EmigratedExportDTO.code=Passage number
com.wunding.learn.file.api.dto.export.emigrated.EmigratedExportDTO.endTime=Pass end time
com.wunding.learn.file.api.dto.export.emigrated.EmigratedExportDTO.isPublish=Post status
com.wunding.learn.file.api.dto.export.emigrated.EmigratedExportDTO.name=Level name
com.wunding.learn.file.api.dto.export.emigrated.EmigratedExportDTO.publishBy=publisher
com.wunding.learn.file.api.dto.export.emigrated.EmigratedExportDTO.publishTime=release time
com.wunding.learn.file.api.dto.export.emigrated.NoticeExportDTO.content=Announcement content
com.wunding.learn.file.api.dto.export.emigrated.NoticeExportDTO.isPublish=Post status
com.wunding.learn.file.api.dto.export.emigrated.NoticeExportDTO.name=Announcement name
com.wunding.learn.file.api.dto.export.emigrated.NoticeExportDTO.publishTime=release time
com.wunding.learn.file.api.dto.export.emigrated.StatisticalExportDTO.checkpointName=Current level
com.wunding.learn.file.api.dto.export.emigrated.StatisticalExportDTO.checkpointSort=Current level number
com.wunding.learn.file.api.dto.export.emigrated.StatisticalExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.emigrated.StatisticalExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.emigrated.StatisticalExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.emigrated.StatisticalExportDTO.ranking=Ranking
com.wunding.learn.file.api.dto.export.emigrated.StatisticalExportDTO.score=Total Score
com.wunding.learn.file.api.dto.export.emigrated.StatisticalExportDTO.status=state
com.wunding.learn.file.api.dto.export.emigrated.StatisticalExportDTO.taskName=current task
com.wunding.learn.file.api.dto.export.emigrated.StylesTemplateExportDTO.description=illustrate
com.wunding.learn.file.api.dto.export.emigrated.StylesTemplateExportDTO.name=style module
com.wunding.learn.file.api.dto.export.emigrated.TaskExportDTO.checkpointName=Level name
com.wunding.learn.file.api.dto.export.emigrated.TaskExportDTO.name=mission name
com.wunding.learn.file.api.dto.export.emigrated.TaskExportDTO.taskStatus=mission status
com.wunding.learn.file.api.dto.export.emigrated.TaskExportDTO.resourceStatus=resource status
com.wunding.learn.file.api.dto.export.emigrated.TaskExportDTO.createType=create type
com.wunding.learn.file.api.dto.export.emigrated.TaskExportDTO.preTaskName=Predecessor task name
com.wunding.learn.file.api.dto.export.emigrated.TaskExportDTO.score=Task points
com.wunding.learn.file.api.dto.export.emigrated.TaskExportDTO.sort=Level serial number
com.wunding.learn.file.api.dto.export.emigrated.TaskExportDTO.type=type
com.wunding.learn.file.api.dto.export.emigrated.TaskStatisticalExportDTO.createBy=founder
com.wunding.learn.file.api.dto.export.emigrated.TaskStatisticalExportDTO.createTime=creation time
com.wunding.learn.file.api.dto.export.emigrated.TaskStatisticalExportDTO.happenTime=Occurrence date
com.wunding.learn.file.api.dto.export.emigrated.TaskStatisticalExportDTO.score=Task points
com.wunding.learn.file.api.dto.export.emigrated.TaskStatisticalExportDTO.summary=Task summary
com.wunding.learn.file.api.dto.export.emigrated.TaskStatisticalExportDTO.type=type
com.wunding.learn.file.api.dto.export.emigrated.TeamExportDTO.memberCount=Number of people
com.wunding.learn.file.api.dto.export.emigrated.TeamExportDTO.name=Team Name
com.wunding.learn.file.api.dto.export.exam.ExamAnalysisQuestionExportDTO.name=Exam question analysis
com.wunding.learn.file.api.dto.export.exam.ExamAnalysisQuestionExportDTO.passScore=\ 
com.wunding.learn.file.api.dto.export.exam.ExamAnalysisQuestionExportDTO.question=\ 
com.wunding.learn.file.api.dto.export.exam.ExamAnalysisQuestionExportDTO.ratio=\ 
com.wunding.learn.file.api.dto.export.exam.ExamAnalysisQuestionExportDTO.totalScore=\ 
com.wunding.learn.file.api.dto.export.exam.ExamAnswerOfStatisticalOrgExportDTO.avgScore=average score
com.wunding.learn.file.api.dto.export.exam.ExamAnswerOfStatisticalOrgExportDTO.orgName=department
com.wunding.learn.file.api.dto.export.exam.ExamAnswerOfStatisticalOrgExportDTO.passRate=passing rate
com.wunding.learn.file.api.dto.export.exam.ExamAnswerOfStatisticalPostExportDTO.avgScore=average score
com.wunding.learn.file.api.dto.export.exam.ExamAnswerOfStatisticalPostExportDTO.name=post
com.wunding.learn.file.api.dto.export.exam.ExamAnswerOfStatisticalPostExportDTO.passRate=passing rate
com.wunding.learn.file.api.dto.export.exam.ExamAnswerRecordDetailListExportDTO.answer=Reference answer
com.wunding.learn.file.api.dto.export.exam.ExamAnswerRecordDetailListExportDTO.examName=Exam name
com.wunding.learn.file.api.dto.export.exam.ExamAnswerRecordDetailListExportDTO.examNo=Exam number
com.wunding.learn.file.api.dto.export.exam.ExamAnswerRecordDetailListExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.exam.ExamAnswerRecordDetailListExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.exam.ExamAnswerRecordDetailListExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.exam.ExamAnswerRecordDetailListExportDTO.postName=post
com.wunding.learn.file.api.dto.export.exam.ExamAnswerRecordDetailListExportDTO.questionName=topic
com.wunding.learn.file.api.dto.export.exam.ExamAnswerRecordDetailListExportDTO.questionType=question type
com.wunding.learn.file.api.dto.export.exam.ExamAnswerRecordDetailListExportDTO.score=question score
com.wunding.learn.file.api.dto.export.exam.ExamAnswerRecordDetailListExportDTO.userAnswer=Answer status
com.wunding.learn.file.api.dto.export.exam.ExamAnswerRecordListExportDTO.answerCount=Number of exams remaining
com.wunding.learn.file.api.dto.export.exam.ExamAnswerRecordListExportDTO.answerTime=Submission time
com.wunding.learn.file.api.dto.export.exam.ExamAnswerRecordListExportDTO.examStatus=exam status
com.wunding.learn.file.api.dto.export.exam.ExamAnswerRecordListExportDTO.finalScore=final grade
com.wunding.learn.file.api.dto.export.exam.ExamAnswerRecordListExportDTO.firstScore=First time results
com.wunding.learn.file.api.dto.export.exam.ExamAnswerRecordListExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.exam.ExamAnswerRecordListExportDTO.isPass=Pass
com.wunding.learn.file.api.dto.export.exam.ExamAnswerRecordListExportDTO.isRetake=Do you want to retake the exam?
com.wunding.learn.file.api.dto.export.exam.ExamAnswerRecordListExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.exam.ExamAnswerRecordListExportDTO.orgName=department
com.wunding.learn.file.api.dto.export.exam.ExamAnswerRecordListExportDTO.postCount=Number of submissions
com.wunding.learn.file.api.dto.export.exam.ExamAnswerRecordListExportDTO.postName=post
com.wunding.learn.file.api.dto.export.exam.ExamCategoryExportDTO.isAvailable=Whether to enable
com.wunding.learn.file.api.dto.export.exam.ExamCategoryExportDTO.name=Category Name
com.wunding.learn.file.api.dto.export.exam.ExamCategoryExportDTO.sortNo=display order
com.wunding.learn.file.api.dto.export.exam.ExamCompetitionExportDTO.answerModel=Answer mode
com.wunding.learn.file.api.dto.export.exam.ExamCompetitionExportDTO.code=competition coding
com.wunding.learn.file.api.dto.export.exam.ExamCompetitionExportDTO.createFullName=founder
com.wunding.learn.file.api.dto.export.exam.ExamCompetitionExportDTO.createUserName=account
com.wunding.learn.file.api.dto.export.exam.ExamCompetitionExportDTO.endTime=End Time
com.wunding.learn.file.api.dto.export.exam.ExamCompetitionExportDTO.isPublish=Post status
com.wunding.learn.file.api.dto.export.exam.ExamCompetitionExportDTO.name=Competition name
com.wunding.learn.file.api.dto.export.exam.ExamCompetitionExportDTO.orgName=Belonging department
com.wunding.learn.file.api.dto.export.exam.ExamCompetitionExportDTO.peopleModel=Personnel mode
com.wunding.learn.file.api.dto.export.exam.ExamCompetitionExportDTO.publishTime=release time
com.wunding.learn.file.api.dto.export.exam.ExamCompetitionExportDTO.startTime=Starting time
com.wunding.learn.file.api.dto.export.exam.ExamCompetitionSessionAnswerRecordeExportDTO.isCorrect=is it right or not
com.wunding.learn.file.api.dto.export.exam.ExamCompetitionSessionAnswerRecordeExportDTO.questionName=Question name
com.wunding.learn.file.api.dto.export.exam.ExamCompetitionSessionAnswerRecordeExportDTO.sortNo=serial number
com.wunding.learn.file.api.dto.export.exam.ExamCompetitionSessionStatExportDTO.code=Session coding
com.wunding.learn.file.api.dto.export.exam.ExamCompetitionSessionStatExportDTO.createTime=Starting time
com.wunding.learn.file.api.dto.export.exam.ExamCompetitionSessionStatExportDTO.hasRobot=robot
com.wunding.learn.file.api.dto.export.exam.ExamCompetitionSessionStatExportDTO.participantsNumber=The number of participants
com.wunding.learn.file.api.dto.export.exam.ExamCompetitionSessionUserExportDTO.answerNumber=Number of answers
com.wunding.learn.file.api.dto.export.exam.ExamCompetitionSessionUserExportDTO.champion=champion
com.wunding.learn.file.api.dto.export.exam.ExamCompetitionSessionUserExportDTO.code=Session coding
com.wunding.learn.file.api.dto.export.exam.ExamCompetitionSessionUserExportDTO.correctNumber=Number of correct answers
com.wunding.learn.file.api.dto.export.exam.ExamCompetitionSessionUserExportDTO.correctRate=Correct rate
com.wunding.learn.file.api.dto.export.exam.ExamCompetitionSessionUserExportDTO.createTime=Participation time
com.wunding.learn.file.api.dto.export.exam.ExamCompetitionSessionUserExportDTO.groupName=Group
com.wunding.learn.file.api.dto.export.exam.ExamCompetitionSessionUserExportDTO.opponent=opponent
com.wunding.learn.file.api.dto.export.exam.ExamCompetitionSessionUserExportDTO.runnerUp=runner up
com.wunding.learn.file.api.dto.export.exam.ExamCompetitionSessionUserExportDTO.thirdPlace=Second runner-up
com.wunding.learn.file.api.dto.export.exam.ExamCompetitionSessionUserExportDTO.win=win
com.wunding.learn.file.api.dto.export.exam.ExamCompetitionUserAnswerRankExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.exam.ExamCompetitionUserAnswerRankExportDTO.loginName=Login Name
com.wunding.learn.file.api.dto.export.exam.ExamCompetitionUserAnswerRankExportDTO.num=Number of answers
com.wunding.learn.file.api.dto.export.exam.ExamCompetitionUserAnswerRankExportDTO.orgName=department
com.wunding.learn.file.api.dto.export.exam.ExamCompetitionUserAnswerRankExportDTO.ranking=Ranking
com.wunding.learn.file.api.dto.export.exam.ExamCompetitionUserScoreRankExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.exam.ExamCompetitionUserScoreRankExportDTO.loginName=Login Name
com.wunding.learn.file.api.dto.export.exam.ExamCompetitionUserScoreRankExportDTO.num=Score
com.wunding.learn.file.api.dto.export.exam.ExamCompetitionUserScoreRankExportDTO.orgName=department
com.wunding.learn.file.api.dto.export.exam.ExamCompetitionUserScoreRankExportDTO.ranking=Ranking
com.wunding.learn.file.api.dto.export.exam.ExamCorrectExamListExportDTO.examName=Exam name
com.wunding.learn.file.api.dto.export.exam.ExamCorrectExamListExportDTO.passScore=passing score
com.wunding.learn.file.api.dto.export.exam.ExamCorrectExamListExportDTO.questionCount=Number of questions
com.wunding.learn.file.api.dto.export.exam.ExamCorrectExamListExportDTO.reviewedCount=The volume has been changed
com.wunding.learn.file.api.dto.export.exam.ExamCorrectExamListExportDTO.submitCount=Number of submitted papers
com.wunding.learn.file.api.dto.export.exam.ExamCorrectExamListExportDTO.totalScore=total score
com.wunding.learn.file.api.dto.export.exam.ExamCorrectExamListExportDTO.unReviewedCount=Uncorrected
com.wunding.learn.file.api.dto.export.exam.ExamCorrectRecordExportDTO.checkFinished=Has the volume been changed?
com.wunding.learn.file.api.dto.export.exam.ExamCorrectRecordExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.exam.ExamCorrectRecordExportDTO.loginName=user account
com.wunding.learn.file.api.dto.export.exam.ExamCorrectRecordExportDTO.orgPath=Department
com.wunding.learn.file.api.dto.export.exam.ExamCorrectRecordExportDTO.submitTime=Submission time
com.wunding.learn.file.api.dto.export.exam.ExamCorrectRecordExportDTO.userScore=exam score
com.wunding.learn.file.api.dto.export.exam.ExamEmployeeResultsDetailExportDTO.examName=Exam name
com.wunding.learn.file.api.dto.export.exam.ExamEmployeeResultsDetailExportDTO.examNo=serial number
com.wunding.learn.file.api.dto.export.exam.ExamEmployeeResultsDetailExportDTO.examScore=total exam score
com.wunding.learn.file.api.dto.export.exam.ExamEmployeeResultsDetailExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.exam.ExamEmployeeResultsDetailExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.exam.ExamEmployeeResultsDetailExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.exam.ExamEmployeeResultsDetailExportDTO.postName=post
com.wunding.learn.file.api.dto.export.exam.ExamEmployeeResultsDetailExportDTO.score=actual score
com.wunding.learn.file.api.dto.export.exam.ExamEmployeeResultsDetailExportDTO.postCount=Number of exam submissions
com.wunding.learn.file.api.dto.export.exam.ExamEmployeeResultsDetailExportDTO.examStatus=Exam Status
com.wunding.learn.file.api.dto.export.exam.ExamListExportDTO.checkPaperMethod=Grading method
com.wunding.learn.file.api.dto.export.exam.ExamListExportDTO.createTime=creation time
com.wunding.learn.file.api.dto.export.exam.ExamListExportDTO.examName=Exam name
com.wunding.learn.file.api.dto.export.exam.ExamListExportDTO.examNo=Exam number
com.wunding.learn.file.api.dto.export.exam.ExamListExportDTO.examTimeCount=Exam duration
com.wunding.learn.file.api.dto.export.exam.ExamListExportDTO.isPublish=state
com.wunding.learn.file.api.dto.export.exam.ExamListExportDTO.passScore=passing score
com.wunding.learn.file.api.dto.export.exam.ExamListExportDTO.publishBy=publisher
com.wunding.learn.file.api.dto.export.exam.ExamListExportDTO.publishTime=release time
com.wunding.learn.file.api.dto.export.exam.ExamListExportDTO.questionCount=Number of questions
com.wunding.learn.file.api.dto.export.exam.ExamListExportDTO.sourceType=Question source
com.wunding.learn.file.api.dto.export.exam.ExamListExportDTO.totalScore=total score
com.wunding.learn.file.api.dto.export.exam.ExamOrgStatisticExportDTO.firstPassRateStr=first time pass rate
com.wunding.learn.file.api.dto.export.exam.ExamOrgStatisticExportDTO.notJoinNum=Did not participate
com.wunding.learn.file.api.dto.export.exam.ExamOrgStatisticExportDTO.notPassNum=failed
com.wunding.learn.file.api.dto.export.exam.ExamOrgStatisticExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.exam.ExamOrgStatisticExportDTO.passNum=Number of people who passed
com.wunding.learn.file.api.dto.export.exam.ExamOrgStatisticExportDTO.passRateStr=passing rate
com.wunding.learn.file.api.dto.export.exam.ExamQuestionExportDTO.answer=standard answer
com.wunding.learn.file.api.dto.export.exam.ExamQuestionExportDTO.difficulty=Degree of difficulty
com.wunding.learn.file.api.dto.export.exam.ExamQuestionExportDTO.end=Set up to 8 options
com.wunding.learn.file.api.dto.export.exam.ExamQuestionExportDTO.mark=question score
com.wunding.learn.file.api.dto.export.exam.ExamQuestionExportDTO.optionA=option A
com.wunding.learn.file.api.dto.export.exam.ExamQuestionExportDTO.optionB=option B
com.wunding.learn.file.api.dto.export.exam.ExamQuestionExportDTO.optionC=option C
com.wunding.learn.file.api.dto.export.exam.ExamQuestionExportDTO.optionD=option D
com.wunding.learn.file.api.dto.export.exam.ExamQuestionExportDTO.optionE=option E
com.wunding.learn.file.api.dto.export.exam.ExamQuestionExportDTO.optionF=option F
com.wunding.learn.file.api.dto.export.exam.ExamQuestionExportDTO.optionG=option G
com.wunding.learn.file.api.dto.export.exam.ExamQuestionExportDTO.optionH=option H
com.wunding.learn.file.api.dto.export.exam.ExamQuestionExportDTO.pointDesc=Score point description
com.wunding.learn.file.api.dto.export.exam.ExamQuestionExportDTO.questionDesc=Analysis of test questions
com.wunding.learn.file.api.dto.export.exam.ExamQuestionExportDTO.questionName=topic
com.wunding.learn.file.api.dto.export.exam.ExamQuestionExportDTO.questionType=type
com.wunding.learn.file.api.dto.export.exam.ExamUserByBizRecordExportDTO.examScore=test scores
com.wunding.learn.file.api.dto.export.exam.ExamUserByBizRecordExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.exam.ExamUserByBizRecordExportDTO.isPost=Whether to submit the paper
com.wunding.learn.file.api.dto.export.exam.ExamUserByBizRecordExportDTO.loginName=user account
com.wunding.learn.file.api.dto.export.exam.ExamUserByBizRecordExportDTO.orgPath=Department
com.wunding.learn.file.api.dto.export.exam.ExamUserByBizRecordExportDTO.postName=post
com.wunding.learn.file.api.dto.export.exam.ExamUserByBizRecordExportDTO.score=total exam score
com.wunding.learn.file.api.dto.export.exam.ExerciseExportDTO.createTime=Upload time
com.wunding.learn.file.api.dto.export.exam.ExerciseExportDTO.exerciseName=Exercise name
com.wunding.learn.file.api.dto.export.exam.ExerciseExportDTO.isPublish=state
com.wunding.learn.file.api.dto.export.exam.ExerciseExportDTO.publishBy=publisher
com.wunding.learn.file.api.dto.export.exam.ExerciseExportDTO.publishTime=release time
com.wunding.learn.file.api.dto.export.exam.ExerciseExportDTO.questionCount=Number of questions
com.wunding.learn.file.api.dto.export.exam.SchemaExportDTO.available=Whether to enable
com.wunding.learn.file.api.dto.export.exam.SchemaExportDTO.createTime=add time
com.wunding.learn.file.api.dto.export.exam.SchemaExportDTO.schemaDescription=Remark
com.wunding.learn.file.api.dto.export.exam.SchemaExportDTO.schemaName=Scheme name
com.wunding.learn.file.api.dto.export.exam.SchemaExportDTO.schemaTotalScore=total score
com.wunding.learn.file.api.dto.export.info.InfoExportDTO.authorFullName=author
com.wunding.learn.file.api.dto.export.info.InfoExportDTO.infoCateName=Information classification
com.wunding.learn.file.api.dto.export.info.InfoExportDTO.isPublish=Publish State
com.wunding.learn.file.api.dto.export.info.InfoExportDTO.transformStatus=Transform Status
com.wunding.learn.file.api.dto.export.info.InfoExportDTO.keyWord=Key words
com.wunding.learn.file.api.dto.export.info.InfoExportDTO.publishByFullName=publisher
com.wunding.learn.file.api.dto.export.info.InfoExportDTO.publishTime=release time
com.wunding.learn.file.api.dto.export.info.InfoExportDTO.title=Information name
com.wunding.learn.file.api.dto.export.info.ResourceCommentExportDTO.content=comments
com.wunding.learn.file.api.dto.export.info.ResourceCommentExportDTO.createTime=Comment time
com.wunding.learn.file.api.dto.export.info.ResourceCommentExportDTO.fullName=Commentator
com.wunding.learn.file.api.dto.export.info.ResourceCommentExportDTO.isDel=state
com.wunding.learn.file.api.dto.export.info.ResourceCommentExportDTO.resourceName=Information name
com.wunding.learn.file.api.dto.export.leaflet.LeafletExportDTO.contentForm=Content form
com.wunding.learn.file.api.dto.export.leaflet.LeafletExportDTO.isPublish=state
com.wunding.learn.file.api.dto.export.leaflet.LeafletExportDTO.sortNo=serial number
com.wunding.learn.file.api.dto.export.leaflet.LeafletExportDTO.title=title
com.wunding.learn.file.api.dto.export.lecturer.CategoryExportDTO.isAvailable=Whether to enable
com.wunding.learn.file.api.dto.export.lecturer.CategoryExportDTO.name=Tag name
com.wunding.learn.file.api.dto.export.lecturer.CategoryExportDTO.parentName=Parent label
com.wunding.learn.file.api.dto.export.lecturer.CategoryExportDTO.sortNo=display order
com.wunding.learn.file.api.dto.export.lecturer.LecturerCategoryExportDTO.categoryName=Category Name
com.wunding.learn.file.api.dto.export.lecturer.LecturerCategoryExportDTO.isAvailable=Whether to enable
com.wunding.learn.file.api.dto.export.lecturer.LecturerCategoryExportDTO.serialNumber=coding
com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthByCourseExportDTO.courseCategory=course sorts
com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthByCourseExportDTO.courseCode=Course code
com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthByCourseExportDTO.courseName=Course Title
com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthByCourseExportDTO.lecturerNum=Number of lecturers
com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthByLecturerExportDTO.authenticationOrg=certification organization
com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthByLecturerExportDTO.authenticationTime=Certification date
com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthByLecturerExportDTO.courseCode=Course code
com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthByLecturerExportDTO.courseName=Course Title
com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthByLecturerExportDTO.employeeNo=Lecturer account
com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthByLecturerExportDTO.evalScore=Evaluation points
com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthByLecturerExportDTO.lectureNum=Number of lectures
com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthByLecturerExportDTO.lecturerCategory=Lecturer Category
com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthByLecturerExportDTO.lecturerCode=Instructor coding
com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthByLecturerExportDTO.lecturerLevel=Lecturer level
com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthByLecturerExportDTO.lecturerName=Lecturer's name
com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthByLecturerExportDTO.score=Certification Rating
com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthTeachExportDTO.courseNum=Number of courses developed
com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthTeachExportDTO.employeeNo=Lecturer account
com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthTeachExportDTO.evalScore=Evaluation points
com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthTeachExportDTO.lecturerCategory=Lecturer classification
com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthTeachExportDTO.lecturerCode=Lecturer number
com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthTeachExportDTO.lecturerLevel=Lecturer level
com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthTeachExportDTO.lecturerName=Lecturer name
com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthTeachExportDTO.teachNum=Number of lectures
com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseListExportDTO.courseName=Course Title
com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseListExportDTO.courseNo=Course code
com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseListExportDTO.coursewareNum=Number of courseware
com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseListExportDTO.publishTime=release date
com.wunding.learn.file.api.dto.export.lecturer.LecturerDemotionConfigExportDTO.afterDemotionLevelName=Lecturer level (after downgrade)
com.wunding.learn.file.api.dto.export.lecturer.LecturerDemotionConfigExportDTO.afterDemotionTypeName=Lecturer type (after downgrade)
com.wunding.learn.file.api.dto.export.lecturer.LecturerDemotionConfigExportDTO.autoDemotion=Automatic downgrade
com.wunding.learn.file.api.dto.export.lecturer.LecturerDemotionConfigExportDTO.currentLevelName=Lecturer level
com.wunding.learn.file.api.dto.export.lecturer.LecturerDemotionConfigExportDTO.currentTypeName=Lecturer type
com.wunding.learn.file.api.dto.export.lecturer.LecturerDemotionConfigExportDTO.isAvailable=Whether to enable
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAssessExportDTO.assessBeginTime=Assessment start time
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAssessExportDTO.assessEndTime=Assessment end time
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAssessExportDTO.assessName=Assessment name
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAssessExportDTO.lecturerCategory=Lecturer Category
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAssessExportDTO.lecturerCode=Lecturer number
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAssessExportDTO.lecturerLevel=Lecturer level
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAssessExportDTO.lecturerLoginName=account
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAssessExportDTO.lecturerName=Name
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAssessExportDTO.memberNumber=The number of participants
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAssessExportDTO.orgPath=Organizing
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAssessExportDTO.projectCode=Project/Class Number
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAssessExportDTO.projectName=Project/Class Name
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAssessExportDTO.projectStartTime=Project/class start time
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAssessExportDTO.score=Score
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAuditExportDTO.acceptanceType=Acceptance method
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAuditExportDTO.accessScore=Evaluation points
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAuditExportDTO.assessBeginTime=Start evaluation time
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAuditExportDTO.assessCount=Quantity should be assessed
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAuditExportDTO.assessEndTime=end evaluation time
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAuditExportDTO.assessedCount=Assessment quantity
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAuditExportDTO.classFee=Teaching fees
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAuditExportDTO.courseName=Course Title
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAuditExportDTO.coursewareNameCollection=Course Contents
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAuditExportDTO.evalType=Evaluate
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAuditExportDTO.examNum=Number of people taking the exam
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAuditExportDTO.examPassRate=exam pass rate
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAuditExportDTO.instructionTime=Teaching hours
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAuditExportDTO.joinNum=The number of participants
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAuditExportDTO.leader=head teacher
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAuditExportDTO.leaderEmployeeNo=Class teacher account
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAuditExportDTO.leaderOrgPath=Head teacher department
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAuditExportDTO.lecturerBookCategory=Accounting Instructor Category
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAuditExportDTO.lecturerBookLevel=Accounting instructor level
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAuditExportDTO.lecturerCategory=Lecturer Category
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAuditExportDTO.isImport=Import
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAuditExportDTO.lecturerCode=Lecturer number
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAuditExportDTO.lecturerLevel=Lecturer level
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAuditExportDTO.lecturerLoginName=Lecturer Account
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAuditExportDTO.lecturerName=Lecturer's name
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAuditExportDTO.teachingTime=Teaching Time
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAuditExportDTO.lecturerOrgPath=Lecturer Department
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAuditExportDTO.projectCode=Item Number
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAuditExportDTO.projectName=project name
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAuditExportDTO.referencedType=type
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAuditExportDTO.refuseReason=Reason for failure
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAuditExportDTO.signNum=Number of people signing in
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAuditExportDTO.signRate=Check-in rate
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAuditExportDTO.status=Approval Status
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAuditExportDTO.teachTime=training time period
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAuditExportDTO.trainCategory=Training Category
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAuditExportDTO.virtualClassFee=Virtual tuition fee/hour
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationAuditExportDTO.virtualFeeType=Virtual Tuition Fee Category
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationExportDTO.auditStatus=Expense review
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationExportDTO.courseFee=Lesson fee/hour
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationExportDTO.courseFeeTotal=Total course fee
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationExportDTO.coursewareNameCollection=Course Contents
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationExportDTO.instructionTime=class(hour)
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationExportDTO.lecturerCategory=Lecturer classification
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationExportDTO.lecturerCode=Lecturer number
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationExportDTO.lecturerLevel=Lecturer level
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationExportDTO.lecturerLoginName=account
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationExportDTO.lecturerName=Name
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationExportDTO.orgPath=Organizing
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationExportDTO.projectCode=Project/Class Number
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationExportDTO.projectName=Project/Class Name
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationExportDTO.projectStartTime=Project/class start time
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationExportDTO.virtualFee=Virtual tuition fee/hour
com.wunding.learn.file.api.dto.export.lecturer.LecturerExaminationExportDTO.virtualFeeType=Virtual Tuition Fee Category
com.wunding.learn.file.api.dto.export.lecturer.LecturerExportDTO.avgScore=Overall rating
com.wunding.learn.file.api.dto.export.lecturer.LecturerExportDTO.categoryName=Classification
com.wunding.learn.file.api.dto.export.lecturer.LecturerExportDTO.createTime=Storage time
com.wunding.learn.file.api.dto.export.lecturer.LecturerExportDTO.isApplication=Employers need to apply
com.wunding.learn.file.api.dto.export.lecturer.LecturerExportDTO.lecturerNumber=serial number
com.wunding.learn.file.api.dto.export.lecturer.LecturerExportDTO.levelName=grade
com.wunding.learn.file.api.dto.export.lecturer.LecturerExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.lecturer.LecturerExportDTO.name=Name
com.wunding.learn.file.api.dto.export.lecturer.LecturerExportDTO.orgPath=Unit (department)
com.wunding.learn.file.api.dto.export.lecturer.LecturerExportDTO.sex=gender
com.wunding.learn.file.api.dto.export.lecturer.LecturerExportDTO.status=state
com.wunding.learn.file.api.dto.export.lecturer.LecturerExportDTO.sumInstructionTime=Total class time (minutes)
com.wunding.learn.file.api.dto.export.lecturer.LecturerLevelExportDTO.classFees=Lesson fee/hour
com.wunding.learn.file.api.dto.export.lecturer.LecturerLevelExportDTO.isAvailable=Whether to enable
com.wunding.learn.file.api.dto.export.lecturer.LecturerLevelExportDTO.isInterior=Is it internal
com.wunding.learn.file.api.dto.export.lecturer.LecturerLevelExportDTO.lecturerCategoryName=Classification
com.wunding.learn.file.api.dto.export.lecturer.LecturerLevelExportDTO.levelName=grade
com.wunding.learn.file.api.dto.export.lecturer.LecturerLevelExportDTO.serialNumber=level coding
com.wunding.learn.file.api.dto.export.lecturer.LecturerLevelExportDTO.sortNo=Level number
com.wunding.learn.file.api.dto.export.lecturer.LecturerLevelExportDTO.virtualClassFees=Virtual tuition fee/hour
com.wunding.learn.file.api.dto.export.lecturer.LecturerLevelExportDTO.virtualClassFeesType=Virtual Tuition Classification
com.wunding.learn.file.api.dto.export.lecturer.LecturerModifyRecordExportDTO.lecturerNumber=coding
com.wunding.learn.file.api.dto.export.lecturer.LecturerModifyRecordExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.lecturer.LecturerModifyRecordExportDTO.modifyType=Abnormal movement classification
com.wunding.learn.file.api.dto.export.lecturer.LecturerModifyRecordExportDTO.name=Name
com.wunding.learn.file.api.dto.export.lecturer.LecturerModifyRecordExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.lecturer.LecturerModifyRecordExportDTO.postLevelName=Lecturer level after the change
com.wunding.learn.file.api.dto.export.lecturer.LecturerModifyRecordExportDTO.postTypeName=Classification of lecturers after changes
com.wunding.learn.file.api.dto.export.lecturer.LecturerModifyRecordExportDTO.preLevelName=Lecturer level
com.wunding.learn.file.api.dto.export.lecturer.LecturerModifyRecordExportDTO.preTypeName=Lecturer classification
com.wunding.learn.file.api.dto.export.lecturer.LecturerModifyRecordExportDTO.updateBy=Operator
com.wunding.learn.file.api.dto.export.lecturer.LecturerModifyRecordExportDTO.updateTime=Change time
com.wunding.learn.file.api.dto.export.lecturer.LecturerRemovalConfigExportDTO.autoRemoval=Automatic delivery
com.wunding.learn.file.api.dto.export.lecturer.LecturerRemovalConfigExportDTO.currentLevelName=Lecturer level
com.wunding.learn.file.api.dto.export.lecturer.LecturerRemovalConfigExportDTO.currentTypeName=Lecturer type
com.wunding.learn.file.api.dto.export.lecturer.LecturerRemovalConfigExportDTO.isAvailable=Whether to enable
com.wunding.learn.file.api.dto.export.lecturer.LecturerRemovalConfigExportDTO.serialNumber=serial number
com.wunding.learn.file.api.dto.export.lecturer.LecturerTeachDetailExportDTO.cwName=Courseware
com.wunding.learn.file.api.dto.export.lecturer.LecturerTeachDetailExportDTO.lecturerCategory=Lecturer type
com.wunding.learn.file.api.dto.export.lecturer.LecturerTeachDetailExportDTO.lecturerLevel=Lecturer level
com.wunding.learn.file.api.dto.export.lecturer.LecturerTeachDetailExportDTO.lecturerName=Lecturer's name
com.wunding.learn.file.api.dto.export.lecturer.LecturerTeachDetailExportDTO.loginName=Lecturer account
com.wunding.learn.file.api.dto.export.lecturer.LecturerTeachDetailExportDTO.orgPath=Current unit
com.wunding.learn.file.api.dto.export.lecturer.LecturerTeachDetailExportDTO.projectName=Teaching projects
com.wunding.learn.file.api.dto.export.lecturer.LecturerTeachDetailExportDTO.studentCount=Number of students
com.wunding.learn.file.api.dto.export.lecturer.LecturerTeachDetailExportDTO.teachTime=Teaching time
com.wunding.learn.file.api.dto.export.lecturer.LecturerTeachDetailExportDTO.timeLength=Teaching hours
com.wunding.learn.file.api.dto.export.lecturer.LecturerTeachDetailExportDTO.totalScore=score
com.wunding.learn.file.api.dto.export.lecturer.LecturerTeachStatisticDetailPageDTO.courseTaskName=Course Title
com.wunding.learn.file.api.dto.export.lecturer.LecturerTeachStatisticDetailPageDTO.evalScore=Evaluation points
com.wunding.learn.file.api.dto.export.lecturer.LecturerTeachStatisticDetailPageDTO.instructionTime=Teaching (hours)
com.wunding.learn.file.api.dto.export.lecturer.LecturerTeachStatisticDetailPageDTO.joinNum=Number of participants
com.wunding.learn.file.api.dto.export.lecturer.LecturerTeachStatisticDetailPageDTO.planNo=Plan number
com.wunding.learn.file.api.dto.export.lecturer.LecturerTeachStatisticDetailPageDTO.planTrainCategory=Plan Category
com.wunding.learn.file.api.dto.export.lecturer.LecturerTeachStatisticDetailPageDTO.proName=Project/Class Name
com.wunding.learn.file.api.dto.export.lecturer.LecturerTeachStatisticDetailPageDTO.proNo=Item Number
com.wunding.learn.file.api.dto.export.lecturer.LecturerTeachStatisticDetailPageDTO.proObject=Participants
com.wunding.learn.file.api.dto.export.lecturer.LecturerTeachStatisticDetailPageDTO.taskEndTime=End Time
com.wunding.learn.file.api.dto.export.lecturer.LecturerTeachStatisticDetailPageDTO.taskStartTime=Starting time
com.wunding.learn.file.api.dto.export.lecturer.LecturerTrainStatisticExportDTO.accessScore=Evaluation points
com.wunding.learn.file.api.dto.export.lecturer.LecturerTrainStatisticExportDTO.badRateStr=defective rate
com.wunding.learn.file.api.dto.export.lecturer.LecturerTrainStatisticExportDTO.courseNum=Number of courses developed
com.wunding.learn.file.api.dto.export.lecturer.LecturerTrainStatisticExportDTO.employeeNo=account
com.wunding.learn.file.api.dto.export.lecturer.LecturerTrainStatisticExportDTO.entryPeriod=Years of entry
com.wunding.learn.file.api.dto.export.lecturer.LecturerTrainStatisticExportDTO.lecturerCategory=Lecturer classification
com.wunding.learn.file.api.dto.export.lecturer.LecturerTrainStatisticExportDTO.lecturerCode=serial number
com.wunding.learn.file.api.dto.export.lecturer.LecturerTrainStatisticExportDTO.lecturerLevel=Lecturer level
com.wunding.learn.file.api.dto.export.lecturer.LecturerTrainStatisticExportDTO.lecturerName=Name
com.wunding.learn.file.api.dto.export.lecturer.LecturerTrainStatisticExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.lecturer.LecturerTrainStatisticExportDTO.queryDateStr=time
com.wunding.learn.file.api.dto.export.lecturer.LecturerTrainStatisticExportDTO.reward=Tuition fee
com.wunding.learn.file.api.dto.export.lecturer.LecturerTrainStatisticExportDTO.teachHour=Teaching (hours)
com.wunding.learn.file.api.dto.export.lecturer.LecturerUpgradeConfigExportDTO.afterPromotionLevelName=Lecturer level (after promotion)
com.wunding.learn.file.api.dto.export.lecturer.LecturerUpgradeConfigExportDTO.afterPromotionTypeName=Lecturer classification (after promotion)
com.wunding.learn.file.api.dto.export.lecturer.LecturerUpgradeConfigExportDTO.autoUpgrade=Automatic promotion
com.wunding.learn.file.api.dto.export.lecturer.LecturerUpgradeConfigExportDTO.currentLevelName=Lecturer level
com.wunding.learn.file.api.dto.export.lecturer.LecturerUpgradeConfigExportDTO.currentTypeName=Lecturer classification
com.wunding.learn.file.api.dto.export.lecturer.LecturerUpgradeConfigExportDTO.isAvailable=Whether to enable
com.wunding.learn.file.api.dto.export.lecturer.LecturerUpgradeConfigExportDTO.serialNumber=serial number
com.wunding.learn.file.api.dto.export.lecturer.NotWorkdayExportDTO.createTime=creation time
com.wunding.learn.file.api.dto.export.lecturer.NotWorkdayExportDTO.fillDate=date
com.wunding.learn.file.api.dto.export.lecturer.NotWorkdayExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.lecturer.NotWorkdayExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.lecturer.NotWorkdayExportDTO.note=Remark
com.wunding.learn.file.api.dto.export.lecturer.NotWorkdayExportDTO.type=type
com.wunding.learn.file.api.dto.export.lecturer.NotWorkdayExportDTO.year=years
com.wunding.learn.file.api.dto.export.library.CourseWareLibExportDTO.createByName=warehousing person
com.wunding.learn.file.api.dto.export.library.CourseWareLibExportDTO.createTime=Storage time
com.wunding.learn.file.api.dto.export.library.CourseWareLibExportDTO.cwName=Courseware name
com.wunding.learn.file.api.dto.export.library.CourseWareLibExportDTO.cwType=Courseware type
com.wunding.learn.file.api.dto.export.library.CourseWareLibExportDTO.isAvailable=Whether to enable
com.wunding.learn.file.api.dto.export.library.CourseWareLibExportDTO.libraryName=Courseware library classification
com.wunding.learn.file.api.dto.export.library.CourseWareLibExportDTO.transformStatus=Transition state
com.wunding.learn.file.api.dto.export.library.CourseWareLibExportDTO.updateByName=editor
com.wunding.learn.file.api.dto.export.library.CourseWareLibExportDTO.updateTime=Edit time
com.wunding.learn.file.api.dto.export.library.EvaluationLibExportDTO.createTime=add time
com.wunding.learn.file.api.dto.export.library.EvaluationLibExportDTO.createUserName=Added by
com.wunding.learn.file.api.dto.export.library.EvaluationLibExportDTO.evalName=Assessment name
com.wunding.learn.file.api.dto.export.library.EvaluationLibExportDTO.isAvailable=Whether to enable
com.wunding.learn.file.api.dto.export.library.EvaluationLibExportDTO.libCategoryName=Evaluation library classification name
com.wunding.learn.file.api.dto.export.library.EvaluationLibExportDTO.updateTime=Edit time
com.wunding.learn.file.api.dto.export.library.EvaluationLibExportDTO.updateUserName=editor
com.wunding.learn.file.api.dto.export.library.ExamLibExportDTO.answerNum=Questions and Answers
com.wunding.learn.file.api.dto.export.library.ExamLibExportDTO.categoryName=Test question bank classification
com.wunding.learn.file.api.dto.export.library.ExamLibExportDTO.clozeNum=Fill in the blanks
com.wunding.learn.file.api.dto.export.library.ExamLibExportDTO.compulsoryNum=Required questions
com.wunding.learn.file.api.dto.export.library.ExamLibExportDTO.createByName=creator
com.wunding.learn.file.api.dto.export.library.ExamLibExportDTO.createTime=Creation date
com.wunding.learn.file.api.dto.export.library.ExamLibExportDTO.highLevelNum=High difficulty
com.wunding.learn.file.api.dto.export.library.ExamLibExportDTO.judgeNum=True or False
com.wunding.learn.file.api.dto.export.library.ExamLibExportDTO.libraryName=Question group name
com.wunding.learn.file.api.dto.export.library.ExamLibExportDTO.lowLevelNum=low difficulty
com.wunding.learn.file.api.dto.export.library.ExamLibExportDTO.mediumLevelNum=Medium difficulty
com.wunding.learn.file.api.dto.export.library.ExamLibExportDTO.multipleChoiceNum=Multiple choice questions
com.wunding.learn.file.api.dto.export.library.ExamLibExportDTO.questionCount=total quantity
com.wunding.learn.file.api.dto.export.library.ExamLibExportDTO.radioNum=Multiple choice questions
com.wunding.learn.file.api.dto.export.library.ExamLibExportDTO.updateByName=Last updated by
com.wunding.learn.file.api.dto.export.library.ExamLibExportDTO.updateTime=last updated date
com.wunding.learn.file.api.dto.export.library.ExerciseLibExportDTO.categoryName=Exercise library classification
com.wunding.learn.file.api.dto.export.library.ExerciseLibExportDTO.createByName=warehousing person
com.wunding.learn.file.api.dto.export.library.ExerciseLibExportDTO.createTime=Storage time
com.wunding.learn.file.api.dto.export.library.ExerciseLibExportDTO.isAvailable=Whether to enable
com.wunding.learn.file.api.dto.export.library.ExerciseLibExportDTO.libraryName=Question group name
com.wunding.learn.file.api.dto.export.library.ExerciseLibExportDTO.updateByName=editor
com.wunding.learn.file.api.dto.export.library.ExerciseLibExportDTO.updateTime=Edit time
com.wunding.learn.file.api.dto.export.library.ExerciseLibExportDTO.used=Whether to quote
com.wunding.learn.file.api.dto.export.library.ExerciseQuestionExportDTO.answer=Reference answer
com.wunding.learn.file.api.dto.export.library.ExerciseQuestionExportDTO.questionName=Question name
com.wunding.learn.file.api.dto.export.library.ExerciseQuestionExportDTO.questionType=question type
com.wunding.learn.file.api.dto.export.library.KnowledgeBaseCategoryExportDTO.isAvailable=Whether to enable
com.wunding.learn.file.api.dto.export.library.KnowledgeBaseCategoryExportDTO.name=Category Name
com.wunding.learn.file.api.dto.export.library.KnowledgeBaseCategoryExportDTO.parentName=Sub-headings
com.wunding.learn.file.api.dto.export.library.KnowledgeBaseCategoryExportDTO.sortNo=display order
com.wunding.learn.file.api.dto.export.library.LibQuestionExportDTO.answer=Reference answer
com.wunding.learn.file.api.dto.export.library.LibQuestionExportDTO.difficulty=Degree of difficulty
com.wunding.learn.file.api.dto.export.library.LibQuestionExportDTO.knowledgePoints=knowledge points
com.wunding.learn.file.api.dto.export.library.LibQuestionExportDTO.isCompulsory=Required questions
com.wunding.learn.file.api.dto.export.library.LibQuestionExportDTO.questionName=Question name
com.wunding.learn.file.api.dto.export.library.LibQuestionExportDTO.questionType=question type
com.wunding.learn.file.api.dto.export.library.MaterialLibExportDTO.createTime=creation time
com.wunding.learn.file.api.dto.export.library.MaterialLibExportDTO.isAvailable=Whether to enable
com.wunding.learn.file.api.dto.export.library.MaterialLibExportDTO.knowledgeType=Knowledge base classification
com.wunding.learn.file.api.dto.export.library.MaterialLibExportDTO.name=Material name
com.wunding.learn.file.api.dto.export.library.MaterialLibExportDTO.sortNo=display order
com.wunding.learn.file.api.dto.export.library.MaterialLibExportDTO.transformStatus=Transition state
com.wunding.learn.file.api.dto.export.library.MaterialLibExportDTO.type=material type
com.wunding.learn.file.api.dto.export.library.MaterialLibExportDTO.updateTime=Edit time
com.wunding.learn.file.api.dto.export.library.ResearchFieldExportDTO.isAvailable=Whether to enable
com.wunding.learn.file.api.dto.export.library.ResearchFieldExportDTO.name=Category Name
com.wunding.learn.file.api.dto.export.library.ResearchFieldExportDTO.parentName=Sub-headings
com.wunding.learn.file.api.dto.export.library.ResearchFieldExportDTO.sortNo=display order
com.wunding.learn.file.api.dto.export.library.SurveyCategoryExportDTO.available=Whether to enable
com.wunding.learn.file.api.dto.export.library.SurveyCategoryExportDTO.categoryName=Category Name
com.wunding.learn.file.api.dto.export.library.SurveyCategoryExportDTO.sortNo=display order
com.wunding.learn.file.api.dto.export.library.SurveyLibExportDTO.available=Whether to enable
com.wunding.learn.file.api.dto.export.library.SurveyLibExportDTO.categoryName=Research database classification
com.wunding.learn.file.api.dto.export.library.SurveyLibExportDTO.createBy=Added by
com.wunding.learn.file.api.dto.export.library.SurveyLibExportDTO.createTime=add time
com.wunding.learn.file.api.dto.export.library.SurveyLibExportDTO.surveyName=Survey name
com.wunding.learn.file.api.dto.export.library.SurveyLibExportDTO.updateBy=editor
com.wunding.learn.file.api.dto.export.library.SurveyLibExportDTO.updateTime=Edit time
com.wunding.learn.file.api.dto.export.library.SysCategoryExportDTO.categoryType=Category Type
com.wunding.learn.file.api.dto.export.library.SysCategoryExportDTO.categoryName=Category Name
com.wunding.learn.file.api.dto.export.library.SysCategoryExportDTO.isAvailable=Whether to enable
com.wunding.learn.file.api.dto.export.library.SysCategoryExportDTO.parentName=upper level
com.wunding.learn.file.api.dto.export.library.SysCategoryExportDTO.sortNo=display order
com.wunding.learn.file.api.dto.export.library.TagCategoryExportDTO.isAvailable=Whether to enable
com.wunding.learn.file.api.dto.export.library.TagCategoryExportDTO.name=Tag name
com.wunding.learn.file.api.dto.export.library.TagCategoryExportDTO.parentName=Parent label
com.wunding.learn.file.api.dto.export.library.TagCategoryExportDTO.sortNo=display order
com.wunding.learn.file.api.dto.export.library.TestPaperLibExportDTO.answerNum=Questions and Answers
com.wunding.learn.file.api.dto.export.library.TestPaperLibExportDTO.categoryName=Test paper library classification
com.wunding.learn.file.api.dto.export.library.TestPaperLibExportDTO.clozeNum=Fill in the blanks
com.wunding.learn.file.api.dto.export.library.TestPaperLibExportDTO.compulsoryNum=Required questions
com.wunding.learn.file.api.dto.export.library.TestPaperLibExportDTO.createBy=creator
com.wunding.learn.file.api.dto.export.library.TestPaperLibExportDTO.createTime=Creation date
com.wunding.learn.file.api.dto.export.library.TestPaperLibExportDTO.highLevelNum=High difficulty
com.wunding.learn.file.api.dto.export.library.TestPaperLibExportDTO.judgeNum=True or False
com.wunding.learn.file.api.dto.export.library.TestPaperLibExportDTO.lowLevelNum=low difficulty
com.wunding.learn.file.api.dto.export.library.TestPaperLibExportDTO.mediumLevelNum=Medium difficulty
com.wunding.learn.file.api.dto.export.library.TestPaperLibExportDTO.multipleChoiceNum=Multiple choice questions
com.wunding.learn.file.api.dto.export.library.TestPaperLibExportDTO.questionCount=total quantity
com.wunding.learn.file.api.dto.export.library.TestPaperLibExportDTO.radioNum=Multiple choice questions
com.wunding.learn.file.api.dto.export.library.TestPaperLibExportDTO.testPaperName=Exam paper name
com.wunding.learn.file.api.dto.export.library.TestPaperLibExportDTO.updateBy=Last updated by
com.wunding.learn.file.api.dto.export.library.TestPaperLibExportDTO.updateTime=last updated date
com.wunding.learn.file.api.dto.export.library.TestPaperQuestionExportDTO.answer=Reference answer
com.wunding.learn.file.api.dto.export.library.TestPaperQuestionExportDTO.difficulty=Degree of difficulty
com.wunding.learn.file.api.dto.export.library.TestPaperQuestionExportDTO.mark=question score
com.wunding.learn.file.api.dto.export.library.TestPaperQuestionExportDTO.questionName=Question name
com.wunding.learn.file.api.dto.export.library.TestPaperQuestionExportDTO.questionType=question type
com.wunding.learn.file.api.dto.export.library.TestPaperQuestionExportDTO.sortNo=sort
com.wunding.learn.file.api.dto.export.library.TestPaperQuestionExportDTO.tag=Label
com.wunding.learn.file.api.dto.export.live.LiveExportDTO.createBy=Added by
com.wunding.learn.file.api.dto.export.live.LiveExportDTO.endTime=End Time
com.wunding.learn.file.api.dto.export.live.LiveExportDTO.intro=Introduction
com.wunding.learn.file.api.dto.export.live.LiveExportDTO.isPublish=Whether to publish
com.wunding.learn.file.api.dto.export.live.LiveExportDTO.liveName=Live broadcast name
com.wunding.learn.file.api.dto.export.live.LiveExportDTO.liveScene=Live broadcast template
com.wunding.learn.file.api.dto.export.live.LiveExportDTO.startTime=Starting time
com.wunding.learn.file.api.dto.export.live.LiveExportDTO.status=state
com.wunding.learn.file.api.dto.export.live.LiveStaticExportDTO.endTime=departure time
com.wunding.learn.file.api.dto.export.live.LiveStaticExportDTO.orgPath=organize
com.wunding.learn.file.api.dto.export.live.LiveStaticExportDTO.startTime=Join time
com.wunding.learn.file.api.dto.export.live.LiveStaticExportDTO.terminalType=terminal type
com.wunding.learn.file.api.dto.export.live.LiveStaticExportDTO.userIp=IP address
com.wunding.learn.file.api.dto.export.live.LiveStaticExportDTO.userName=Name
com.wunding.learn.file.api.dto.export.live.LiveVodExportDTO.duration=duration
com.wunding.learn.file.api.dto.export.live.LiveVodExportDTO.endTime=End Time
com.wunding.learn.file.api.dto.export.live.LiveVodExportDTO.liveName=Live broadcast name
com.wunding.learn.file.api.dto.export.live.LiveVodExportDTO.startTime=Starting time
com.wunding.learn.file.api.dto.export.medal.MedalUserRelationExportDTO.awardTime=Issue date
com.wunding.learn.file.api.dto.export.medal.MedalUserRelationExportDTO.empNo=account
com.wunding.learn.file.api.dto.export.medal.MedalUserRelationExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.medal.MedalUserRelationExportDTO.medalName=Medal name
com.wunding.learn.file.api.dto.export.medal.MedalUserRelationExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.members.TrainCoOrganizerExportDTO.authItemZn=Responsibilities
com.wunding.learn.file.api.dto.export.members.TrainCoOrganizerExportDTO.empNo=account
com.wunding.learn.file.api.dto.export.members.TrainCoOrganizerExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.members.TrainCoOrganizerExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.members.TrainMembersExportDTO.empNo=account
com.wunding.learn.file.api.dto.export.members.TrainMembersExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.members.TrainMembersExportDTO.joinTime=Joining date
com.wunding.learn.file.api.dto.export.members.TrainMembersExportDTO.joinType=How to join
com.wunding.learn.file.api.dto.export.members.TrainMembersExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.members.TrainMembersExportDTO.reviewStatus=state
com.wunding.learn.file.api.dto.export.plan.FormTemplateManageDTO.categoryName=Classification
com.wunding.learn.file.api.dto.export.plan.FormTemplateManageDTO.createLoginName=account
com.wunding.learn.file.api.dto.export.plan.FormTemplateManageDTO.createName=founder
com.wunding.learn.file.api.dto.export.plan.FormTemplateManageDTO.createTime=creation time
com.wunding.learn.file.api.dto.export.plan.FormTemplateManageDTO.dateNumber=Number of records
com.wunding.learn.file.api.dto.export.plan.FormTemplateManageDTO.description=Instructions for use
com.wunding.learn.file.api.dto.export.plan.FormTemplateManageDTO.formName=form name
com.wunding.learn.file.api.dto.export.plan.FormTemplateManageDTO.formNo=Form Number
com.wunding.learn.file.api.dto.export.plan.FormTemplateManageDTO.isPublish=Post status
com.wunding.learn.file.api.dto.export.plan.PlanCategoryExportDTO.categoryName=Category Name
com.wunding.learn.file.api.dto.export.plan.PlanCategoryExportDTO.isAvailable=Whether to enable
com.wunding.learn.file.api.dto.export.plan.PlanCategoryExportDTO.sortNo=display order
com.wunding.learn.file.api.dto.export.plan.PlanExportDTO.auditUserName=Submitter
com.wunding.learn.file.api.dto.export.plan.PlanExportDTO.createByName=founder
com.wunding.learn.file.api.dto.export.plan.PlanExportDTO.createTime=creation time
com.wunding.learn.file.api.dto.export.plan.PlanExportDTO.lastAuditTime=Last review time
com.wunding.learn.file.api.dto.export.plan.PlanExportDTO.orgPath=executive organization
com.wunding.learn.file.api.dto.export.plan.PlanExportDTO.planCategoryName=Plan Category
com.wunding.learn.file.api.dto.export.plan.PlanExportDTO.planName=Plan name
com.wunding.learn.file.api.dto.export.plan.PlanExportDTO.planNo=Plan number
com.wunding.learn.file.api.dto.export.plan.PlanExportDTO.planQuarter=planning quarter
com.wunding.learn.file.api.dto.export.plan.PlanExportDTO.planYear=planning year
com.wunding.learn.file.api.dto.export.plan.PlanExportDTO.status=state
com.wunding.learn.file.api.dto.export.plan.PlanExportDTO.totalNumber=Total number of items
com.wunding.learn.file.api.dto.export.plan.PlanStatisticExportDTO.avgDuration=Average academic hours per student
com.wunding.learn.file.api.dto.export.plan.PlanStatisticExportDTO.completionRate=plan completion rate
com.wunding.learn.file.api.dto.export.plan.PlanStatisticExportDTO.courseNumber=Plan class hours
com.wunding.learn.file.api.dto.export.plan.PlanStatisticExportDTO.orgPath=executive organization
com.wunding.learn.file.api.dto.export.plan.PlanStatisticExportDTO.personNumber=Planned visits
com.wunding.learn.file.api.dto.export.plan.PlanStatisticExportDTO.planCategoryName=Plan Category
com.wunding.learn.file.api.dto.export.plan.PlanStatisticExportDTO.planName=Plan name
com.wunding.learn.file.api.dto.export.plan.PlanStatisticExportDTO.planNo=Plan number
com.wunding.learn.file.api.dto.export.plan.PlanStatisticExportDTO.planQuarter=Training plan quarterly
com.wunding.learn.file.api.dto.export.plan.PlanStatisticExportDTO.planYear=planning year
com.wunding.learn.file.api.dto.export.plan.PlanStatisticExportDTO.satisfaction=program satisfaction
com.wunding.learn.file.api.dto.export.plan.PlanStatisticExportDTO.status=plan status
com.wunding.learn.file.api.dto.export.plan.PlanStatisticExportDTO.totalNumber=Total number of items
com.wunding.learn.file.api.dto.export.plan.StatAnalysisPlanExecuteMonthExportDTO.avgDuration=Average academic hours per student
com.wunding.learn.file.api.dto.export.plan.StatAnalysisPlanExecuteMonthExportDTO.avgEvaluation=Satisfaction
com.wunding.learn.file.api.dto.export.plan.StatAnalysisPlanExecuteMonthExportDTO.coverageRate=Coverage
com.wunding.learn.file.api.dto.export.plan.StatAnalysisPlanExecuteMonthExportDTO.mouthOrYear=month
com.wunding.learn.file.api.dto.export.plan.StatAnalysisPlanExecuteMonthExportDTO.orgPath=name of association
com.wunding.learn.file.api.dto.export.plan.StatAnalysisPlanExecuteMonthExportDTO.totalNumber=Total number of people in the department
com.wunding.learn.file.api.dto.export.plan.StatAnalysisPlanExecuteMonthExportDTO.userNumber=Total number of participants in the training program
com.wunding.learn.file.api.dto.export.plan.StatAnalysisPlanExecuteYearExportDTO.avgDuration=Average academic hours per student
com.wunding.learn.file.api.dto.export.plan.StatAnalysisPlanExecuteYearExportDTO.avgEvaluation=Satisfaction
com.wunding.learn.file.api.dto.export.plan.StatAnalysisPlanExecuteYearExportDTO.coverageRate=Coverage
com.wunding.learn.file.api.dto.export.plan.StatAnalysisPlanExecuteYearExportDTO.mouthOrYear=years
com.wunding.learn.file.api.dto.export.plan.StatAnalysisPlanExecuteYearExportDTO.orgPath=name of association
com.wunding.learn.file.api.dto.export.plan.StatAnalysisPlanExecuteYearExportDTO.totalNumber=Total number of people in the department
com.wunding.learn.file.api.dto.export.plan.StatAnalysisPlanExecuteYearExportDTO.userNumber=Total number of participants in the training program
com.wunding.learn.file.api.dto.export.plan.TrainProjectStatisticExportDTO.endTime=Project end time
com.wunding.learn.file.api.dto.export.plan.TrainProjectStatisticExportDTO.evaluationScore=Evaluation points
com.wunding.learn.file.api.dto.export.plan.TrainProjectStatisticExportDTO.orgPath=Creator department name
com.wunding.learn.file.api.dto.export.plan.TrainProjectStatisticExportDTO.startTime=Project start time
com.wunding.learn.file.api.dto.export.plan.TrainProjectStatisticExportDTO.trainName=project name
com.wunding.learn.file.api.dto.export.plan.TrainProjectStatisticExportDTO.trainNo=Project code
com.wunding.learn.file.api.dto.export.plan.TrainProjectStatisticExportDTO.userNumber=Number of people
com.wunding.learn.file.api.dto.export.post.CountCommentExportDTO.commentTime=reply time
com.wunding.learn.file.api.dto.export.post.CountCommentExportDTO.content=Reply content
com.wunding.learn.file.api.dto.export.post.CountCommentExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.post.CountCommentExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.post.CountCommentExportDTO.starNum=Number of likes
com.wunding.learn.file.api.dto.export.post.CountCommentExportDTO.userName=account
com.wunding.learn.file.api.dto.export.project.ApplyUserExportDTO.applyState=Approval Status
com.wunding.learn.file.api.dto.export.project.ApplyUserExportDTO.applyTime=registration time
com.wunding.learn.file.api.dto.export.project.ApplyUserExportDTO.auditTime=review time
com.wunding.learn.file.api.dto.export.project.ApplyUserExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.project.ApplyUserExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.project.ApplyUserExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.project.ApplyUserExportDTO.postName=post
com.wunding.learn.file.api.dto.export.project.ClassroomExportDTO.address=Place
com.wunding.learn.file.api.dto.export.project.ClassroomExportDTO.createBy=add a person
com.wunding.learn.file.api.dto.export.project.ClassroomExportDTO.createTime=add time
com.wunding.learn.file.api.dto.export.project.ClassroomExportDTO.description=Remark
com.wunding.learn.file.api.dto.export.project.ClassroomExportDTO.roomName=name
com.wunding.learn.file.api.dto.export.project.CompletionExportDTO.comProject=Study project completed
com.wunding.learn.file.api.dto.export.project.CompletionExportDTO.comSurvey=Research completed
com.wunding.learn.file.api.dto.export.project.CompletionExportDTO.evaNum=Number of evaluations
com.wunding.learn.file.api.dto.export.project.CompletionExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.project.CompletionExportDTO.isCompletion=Whether to graduate
com.wunding.learn.file.api.dto.export.project.CompletionExportDTO.isExam=passed the exam
com.wunding.learn.file.api.dto.export.project.CompletionExportDTO.isHomework=Assignment submission
com.wunding.learn.file.api.dto.export.project.CompletionExportDTO.isLearned=Course learned
com.wunding.learn.file.api.dto.export.project.CompletionExportDTO.isPreCompletion=Meet graduation requirements
com.wunding.learn.file.api.dto.export.project.CompletionExportDTO.jobName=post
com.wunding.learn.file.api.dto.export.project.CompletionExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.project.CompletionExportDTO.orgName=department
com.wunding.learn.file.api.dto.export.project.CompletionExportDTO.signNum=Number of check-ins
com.wunding.learn.file.api.dto.export.project.ProjectCompletionStatisticExportDTO.fullName=name
com.wunding.learn.file.api.dto.export.project.ProjectCompletionStatisticExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.project.ProjectCompletionStatisticExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.project.ProjectCompletionStatisticExportDTO.teamName=team
com.wunding.learn.file.api.dto.export.project.ProjectCompletionStatisticExportDTO.finishRatio=Project completion rate
com.wunding.learn.file.api.dto.export.project.ProjectCompletionStatisticExportDTO.finishOnlyMustRatio=Project completion rate 1
com.wunding.learn.file.api.dto.export.project.ProjectCompletionStatisticExportDTO.courseMustFinishRatio=Course completion rate
com.wunding.learn.file.api.dto.export.project.ProjectCompletionStatisticExportDTO.courseTime=Duration (hours)
com.wunding.learn.file.api.dto.export.project.ProjectCompletionStatisticExportDTO.learnTime=hours
com.wunding.learn.file.api.dto.export.project.ProjectCompletionStatisticExportDTO.integral=integral
com.wunding.learn.file.api.dto.export.project.ProjectCompletionStatisticExportDTO.goldCoin=gold
com.wunding.learn.file.api.dto.export.project.ProjectCompletionStatisticExportDTO.credit=credits
com.wunding.learn.file.api.dto.export.project.ProjectUserTaskExportDTO.no=serial number
com.wunding.learn.file.api.dto.export.project.ProjectUserTaskExportDTO.phaseName=stage
com.wunding.learn.file.api.dto.export.project.ProjectUserTaskExportDTO.taskType=Task type
com.wunding.learn.file.api.dto.export.project.ProjectUserTaskExportDTO.taskName=The name of the task
com.wunding.learn.file.api.dto.export.project.ProjectUserTaskExportDTO.userStatus=Completion status
com.wunding.learn.file.api.dto.export.project.ProjectUserTaskExportDTO.taskResult=results
com.wunding.learn.file.api.dto.export.project.ProjectUserProjectExcitationRecordExportDTO.no=serial number
com.wunding.learn.file.api.dto.export.project.ProjectUserProjectExcitationRecordExportDTO.name=The name of the event
com.wunding.learn.file.api.dto.export.project.ProjectUserProjectExcitationRecordExportDTO.value=value
com.wunding.learn.file.api.dto.export.project.ProjectUserProjectCoursewareStudyDetailExportDTO.no=serial number
com.wunding.learn.file.api.dto.export.project.ProjectUserProjectCoursewareStudyDetailExportDTO.taskName=The name of the course task
com.wunding.learn.file.api.dto.export.project.ProjectUserProjectCoursewareStudyDetailExportDTO.duration=Lessons learned
com.wunding.learn.file.api.dto.export.project.ProjectUserProjectCoursewareStudyDetailExportDTO.totalDuration=Total number of lessons
com.wunding.learn.file.api.dto.export.project.CourseTaskProjectExportDTO.createTime=creation time
com.wunding.learn.file.api.dto.export.project.CourseTaskProjectExportDTO.endTime=End Time
com.wunding.learn.file.api.dto.export.project.CourseTaskProjectExportDTO.isPublish=state
com.wunding.learn.file.api.dto.export.project.CourseTaskProjectExportDTO.proName=mission name
com.wunding.learn.file.api.dto.export.project.CourseTaskProjectExportDTO.proNo=Task number
com.wunding.learn.file.api.dto.export.project.CourseTaskProjectExportDTO.publishBy=publisher
com.wunding.learn.file.api.dto.export.project.CourseTaskProjectExportDTO.startTime=Starting time
com.wunding.learn.file.api.dto.export.project.CourseTaskProjectExportDTO.taskNum=Number of tasks
com.wunding.learn.file.api.dto.export.project.CourseTaskProjectStatisticTaskDetailExportDTO.costTime=class
com.wunding.learn.file.api.dto.export.project.CourseTaskProjectStatisticTaskDetailExportDTO.endTime=Task end time
com.wunding.learn.file.api.dto.export.project.CourseTaskProjectStatisticTaskDetailExportDTO.finishedTime=actual end time
com.wunding.learn.file.api.dto.export.project.CourseTaskProjectStatisticTaskDetailExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.project.CourseTaskProjectStatisticTaskDetailExportDTO.isFinish=Task status
com.wunding.learn.file.api.dto.export.project.CourseTaskProjectStatisticTaskDetailExportDTO.joinTime=actual start time
com.wunding.learn.file.api.dto.export.project.CourseTaskProjectStatisticTaskDetailExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.project.CourseTaskProjectStatisticTaskDetailExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.project.CourseTaskProjectStatisticTaskDetailExportDTO.postName=post
com.wunding.learn.file.api.dto.export.project.CourseTaskProjectStatisticTaskDetailExportDTO.startTime=Task start time
com.wunding.learn.file.api.dto.export.project.CourseTaskProjectStatisticTaskDetailExportDTO.taskName=mission name
com.wunding.learn.file.api.dto.export.project.CourseTaskProjectStatisticTaskDetailExportDTO.userScore=score
com.wunding.learn.file.api.dto.export.project.DefaultApplyExportDTO.applyState=Audit status
com.wunding.learn.file.api.dto.export.project.DefaultApplyExportDTO.applyTime=Registration time
com.wunding.learn.file.api.dto.export.project.DefaultApplyExportDTO.applyType=Registration type
com.wunding.learn.file.api.dto.export.project.DefaultApplyExportDTO.auditTime=Audit time
com.wunding.learn.file.api.dto.export.project.DefaultApplyExportDTO.certificateNo=Certificate number
com.wunding.learn.file.api.dto.export.project.DefaultApplyExportDTO.companyAddress=company communication address
com.wunding.learn.file.api.dto.export.project.DefaultApplyExportDTO.companyContact=company contact
com.wunding.learn.file.api.dto.export.project.DefaultApplyExportDTO.companyPhone=company contact phone number
com.wunding.learn.file.api.dto.export.project.DefaultApplyExportDTO.fullName=name
com.wunding.learn.file.api.dto.export.project.DefaultApplyExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.project.DefaultApplyExportDTO.name=Name
com.wunding.learn.file.api.dto.export.project.DefaultApplyExportDTO.orgPath=Department
com.wunding.learn.file.api.dto.export.project.DefaultApplyExportDTO.phone=Mobile phone number
com.wunding.learn.file.api.dto.export.project.DefaultApplyExportDTO.position=Current position
com.wunding.learn.file.api.dto.export.project.DefaultApplyExportDTO.postName=Position
com.wunding.learn.file.api.dto.export.project.DefaultApplyExportDTO.postalCode =Postal code
com.wunding.learn.file.api.dto.export.project.DefaultApplyExportDTO.professionName=Industry of the unit
com.wunding.learn.file.api.dto.export.project.DefaultApplyExportDTO.provinceName=Place of origin
com.wunding.learn.file.api.dto.export.project.DefaultApplyExportDTO.remainderCourseWareCount=Organization name
com.wunding.learn.file.api.dto.export.project.DefaultApplyExportDTO.sex=Gender
com.wunding.learn.file.api.dto.export.project.DefaultApplyExportDTO.shenzhenWorkTime=Year in Shenzhen
com.wunding.learn.file.api.dto.export.project.EvalStatisticalAnalysisExportDTO.finishTime=submission time submission time submission time
com.wunding.learn.file.api.dto.export.project.EvalStatisticalAnalysisExportDTO.fullName=name name name
com.wunding.learn.file.api.dto.export.project.EvalStatisticalAnalysisExportDTO.loginName=account account account
com.wunding.learn.file.api.dto.export.project.EvalStatisticalAnalysisExportDTO.orgName=Organization Department Organization Department Organization Department
com.wunding.learn.file.api.dto.export.project.EvalStatisticalAnalysisExportDTO.questionName=serial number
com.wunding.learn.file.api.dto.export.project.EvalStatisticalAnalysisExportDTO.questionNo=serial number
com.wunding.learn.file.api.dto.export.project.EvalStatisticalAnalysisExportDTO.questionType=serial number
com.wunding.learn.file.api.dto.export.project.EvalStatisticalAnalysisExportDTO.serialNumber=serial number serial number serial number
com.wunding.learn.file.api.dto.export.project.FaceProjectHomeWorkExportDTO.createTime=Upload time
com.wunding.learn.file.api.dto.export.project.FaceProjectHomeWorkExportDTO.explanation=illustrate
com.wunding.learn.file.api.dto.export.project.FaceProjectHomeWorkExportDTO.isSubmitHomework=Completion status
com.wunding.learn.file.api.dto.export.project.FaceProjectHomeWorkExportDTO.fileName=Homework file
com.wunding.learn.file.api.dto.export.project.FaceProjectHomeWorkExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.project.FaceProjectHomeWorkExportDTO.grade=score
com.wunding.learn.file.api.dto.export.project.FaceProjectHomeWorkExportDTO.isJudge=Has it been rated?
com.wunding.learn.file.api.dto.export.project.FaceProjectHomeWorkExportDTO.orgName=department
com.wunding.learn.file.api.dto.export.project.FaceProjectHomeWorkExportDTO.postName=post
com.wunding.learn.file.api.dto.export.project.FaceProjectHomeWorkExportDTO.remarks=Comments
com.wunding.learn.file.api.dto.export.project.FaceProjectHomeWorkWithSourceExportDTO.createTime=Upload time
com.wunding.learn.file.api.dto.export.project.FaceProjectHomeWorkWithSourceExportDTO.explanation=illustrate
com.wunding.learn.file.api.dto.export.project.FaceProjectHomeWorkWithSourceExportDTO.isSubmitHomework=Completion status
com.wunding.learn.file.api.dto.export.project.FaceProjectHomeWorkWithSourceExportDTO.fileName=Homework files
com.wunding.learn.file.api.dto.export.project.FaceProjectHomeWorkWithSourceExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.project.FaceProjectHomeWorkWithSourceExportDTO.grade=score
com.wunding.learn.file.api.dto.export.project.FaceProjectHomeWorkWithSourceExportDTO.isJudge=Has it been rated?
com.wunding.learn.file.api.dto.export.project.FaceProjectHomeWorkWithSourceExportDTO.orgName=department
com.wunding.learn.file.api.dto.export.project.FaceProjectHomeWorkWithSourceExportDTO.postName=post
com.wunding.learn.file.api.dto.export.project.FaceProjectHomeWorkWithSourceExportDTO.remarks=Comments
com.wunding.learn.file.api.dto.export.project.FaceProjectHomeWorkWithSourceExportDTO.sourceName=source
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailExportDTO.costTime=class(s)
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailExportDTO.endTime=Task end time
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailExportDTO.explanation=illustrate
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailExportDTO.finishedTime=actual end time
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailExportDTO.isFinish=Task status
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailExportDTO.joinTime=actual start time
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailExportDTO.postName=post
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailExportDTO.startTime=Task start time
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailExportDTO.taskFinishedTime=Time to get task points
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailExportDTO.taskName=mission name
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailExportDTO.taskScore=Task points
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailExportDTO.teamName=team
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailExportDTO.userScore=score
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailWithSourceExportDTO.costTime=class(s)
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailWithSourceExportDTO.endTime=Task end time
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailWithSourceExportDTO.explanation=illustrate
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailWithSourceExportDTO.finishedTime=actual end time
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailWithSourceExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailWithSourceExportDTO.isFinish=Task status
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailWithSourceExportDTO.joinTime=actual start time
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailWithSourceExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailWithSourceExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailWithSourceExportDTO.postName=post
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailWithSourceExportDTO.sourceName=source
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailWithSourceExportDTO.startTime=Task start time
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailWithSourceExportDTO.taskFinishedTime=Time to get task points
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailWithSourceExportDTO.taskName=mission name
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailWithSourceExportDTO.taskScore=Task points
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailWithSourceExportDTO.teamName=team
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticTaskDetailWithSourceExportDTO.userScore=score
com.wunding.learn.file.api.dto.export.project.FormTemplateExportDTO.coachPlan=Counseling program
com.wunding.learn.file.api.dto.export.project.FormTemplateExportDTO.coachWay=Counseling methods
com.wunding.learn.file.api.dto.export.project.FormTemplateExportDTO.code=serial number
com.wunding.learn.file.api.dto.export.project.FormTemplateExportDTO.isPublish=Post status
com.wunding.learn.file.api.dto.export.project.FormTemplateExportDTO.name=Tutoring name
com.wunding.learn.file.api.dto.export.project.FormTemplateExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.project.FormTemplateExportDTO.publishBy=publisher
com.wunding.learn.file.api.dto.export.project.FormTemplateExportDTO.publishTime=release time
com.wunding.learn.file.api.dto.export.project.HomeWorkExportDTO.createTime=Upload time
com.wunding.learn.file.api.dto.export.project.HomeWorkExportDTO.fileName=Homework file
com.wunding.learn.file.api.dto.export.project.HomeWorkExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.project.HomeWorkExportDTO.grade=score
com.wunding.learn.file.api.dto.export.project.HomeWorkExportDTO.isJudge=Has it been rated?
com.wunding.learn.file.api.dto.export.project.HomeWorkExportDTO.orgName=department
com.wunding.learn.file.api.dto.export.project.HomeWorkExportDTO.postName=post
com.wunding.learn.file.api.dto.export.project.HomeWorkExportDTO.remarks=Comments
com.wunding.learn.file.api.dto.export.project.HomeWorkExportDTO.isSubmitHomework=Completion status
com.wunding.learn.file.api.dto.export.project.TrainHomeWorkExportDTO.proName=class name
com.wunding.learn.file.api.dto.export.project.TrainHomeWorkExportDTO.workName=Job name
com.wunding.learn.file.api.dto.export.project.TrainHomeWorkExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.project.TrainHomeWorkExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.project.TrainHomeWorkExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.project.TrainHomeWorkExportDTO.finishedStatus=finished condition
com.wunding.learn.file.api.dto.export.project.TrainHomeWorkExportDTO.content=content
com.wunding.learn.file.api.dto.export.project.TrainHomeWorkExportDTO.fileName=file name
com.wunding.learn.file.api.dto.export.project.TrainHomeWorkExportDTO.createTime=Submission time
com.wunding.learn.file.api.dto.export.project.InvoiceExportDTO.auditStateName=Approval Status
com.wunding.learn.file.api.dto.export.project.InvoiceExportDTO.bankAccount=Bank Account
com.wunding.learn.file.api.dto.export.project.InvoiceExportDTO.bankOfDeposit=Bank of deposit
com.wunding.learn.file.api.dto.export.project.InvoiceExportDTO.companyAddress=address
com.wunding.learn.file.api.dto.export.project.InvoiceExportDTO.companyName=company name
com.wunding.learn.file.api.dto.export.project.InvoiceExportDTO.companyPhone=Unit contact number
com.wunding.learn.file.api.dto.export.project.InvoiceExportDTO.contacts=Invoice Contact
com.wunding.learn.file.api.dto.export.project.InvoiceExportDTO.contactsConsigneeAddress=Invoice contact recipient address
com.wunding.learn.file.api.dto.export.project.InvoiceExportDTO.contactsEmail=Invoice contact email address
com.wunding.learn.file.api.dto.export.project.InvoiceExportDTO.contactsPhone=Invoice contact phone number
com.wunding.learn.file.api.dto.export.project.InvoiceExportDTO.fullName=username
com.wunding.learn.file.api.dto.export.project.InvoiceExportDTO.invoiceTypeName=invoice type
com.wunding.learn.file.api.dto.export.project.InvoiceExportDTO.invoiceValue=invoice amount
com.wunding.learn.file.api.dto.export.project.InvoiceExportDTO.phone=User mobile phone number
com.wunding.learn.file.api.dto.export.project.InvoiceExportDTO.taxpayerId=Taxpayer Identification Number
com.wunding.learn.file.api.dto.export.project.LecturerExportDTO.bookkeepingLecturer=Accounting instructor type
com.wunding.learn.file.api.dto.export.project.LecturerExportDTO.bookkeepingLecturerLevel=Accounting instructor level
com.wunding.learn.file.api.dto.export.project.LecturerExportDTO.classRepay=Teaching fees
com.wunding.learn.file.api.dto.export.project.LecturerExportDTO.costApprolve=Expense review
com.wunding.learn.file.api.dto.export.project.LecturerExportDTO.coursewareNamesStr=Courseware
com.wunding.learn.file.api.dto.export.project.LecturerExportDTO.endTime=Class end time
com.wunding.learn.file.api.dto.export.project.LecturerExportDTO.eval=Evaluate
com.wunding.learn.file.api.dto.export.project.LecturerExportDTO.isImport=Import
com.wunding.learn.file.api.dto.export.project.LecturerExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.project.LecturerExportDTO.instructionTime=Teaching hours
com.wunding.learn.file.api.dto.export.project.LecturerExportDTO.lecturerCode=Lecturer number
com.wunding.learn.file.api.dto.export.project.LecturerExportDTO.levelRepay=Level tuition fee
com.wunding.learn.file.api.dto.export.project.LecturerExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.project.LecturerExportDTO.startTime=Lesson start time
com.wunding.learn.file.api.dto.export.project.LecturerTeachStatisticDetailExportDTO.courseTaskName=Course task name
com.wunding.learn.file.api.dto.export.project.LecturerTeachStatisticDetailExportDTO.evalScore=Evaluation score (decimal, keep two decimal places)
com.wunding.learn.file.api.dto.export.project.LecturerTeachStatisticDetailExportDTO.instructionTime=Teaching (hours)
com.wunding.learn.file.api.dto.export.project.LecturerTeachStatisticDetailExportDTO.joinNum=Number of participants
com.wunding.learn.file.api.dto.export.project.LecturerTeachStatisticDetailExportDTO.planNo=Plan number
com.wunding.learn.file.api.dto.export.project.LecturerTeachStatisticDetailExportDTO.planTrainCategory=Program training type
com.wunding.learn.file.api.dto.export.project.LecturerTeachStatisticDetailExportDTO.proName=project name
com.wunding.learn.file.api.dto.export.project.LecturerTeachStatisticDetailExportDTO.proNo=Item Number
com.wunding.learn.file.api.dto.export.project.LecturerTeachStatisticDetailExportDTO.proObject=Project object
com.wunding.learn.file.api.dto.export.project.LecturerTeachStatisticDetailExportDTO.taskEndTime=Course task end time
com.wunding.learn.file.api.dto.export.project.LecturerTeachStatisticDetailExportDTO.taskStartTime=Course task start time
com.wunding.learn.file.api.dto.export.project.MentorExportDTO.mentorName=tutor's name
com.wunding.learn.file.api.dto.export.project.MentorExportDTO.mentorNo=Tutor account
com.wunding.learn.file.api.dto.export.project.MentorExportDTO.mentorOrgPath=mentor organization
com.wunding.learn.file.api.dto.export.project.MentorExportDTO.mentorType=Mentor type
com.wunding.learn.file.api.dto.export.project.MentorExportDTO.studentName=Student name
com.wunding.learn.file.api.dto.export.project.MentorExportDTO.studentNo=Student account
com.wunding.learn.file.api.dto.export.project.MentorExportDTO.studentOrgPath=student organization
com.wunding.learn.file.api.dto.export.project.PositionExportDTO.activityCount=Number of associated activities
com.wunding.learn.file.api.dto.export.project.PositionExportDTO.isPublish=Post status
com.wunding.learn.file.api.dto.export.project.PositionExportDTO.positionType=development type
com.wunding.learn.file.api.dto.export.project.PositionExportDTO.sourcePositionName=post
com.wunding.learn.file.api.dto.export.project.PositionExportDTO.targetPositionName=Target development positions
com.wunding.learn.file.api.dto.export.project.PostExportDTO.commentNum=Number of replies
com.wunding.learn.file.api.dto.export.project.PostExportDTO.createBy=Posted by
com.wunding.learn.file.api.dto.export.project.PostExportDTO.createTime=Posting time
com.wunding.learn.file.api.dto.export.project.PostExportDTO.isClient=source
com.wunding.learn.file.api.dto.export.project.PostExportDTO.isDel=delete or not
com.wunding.learn.file.api.dto.export.project.PostExportDTO.orgLevelPathName=department
com.wunding.learn.file.api.dto.export.project.PostExportDTO.postType=Post type
com.wunding.learn.file.api.dto.export.project.PostExportDTO.sectionName=Topic section
com.wunding.learn.file.api.dto.export.project.PostExportDTO.status=state
com.wunding.learn.file.api.dto.export.project.PostExportDTO.teamName=visible team
com.wunding.learn.file.api.dto.export.project.PostExportDTO.title=theme
com.wunding.learn.file.api.dto.export.project.PostExportDTO.viewNum=viewing count
com.wunding.learn.file.api.dto.export.project.ProjectCompletionExportDTO.comProject=Study project completed
com.wunding.learn.file.api.dto.export.project.ProjectCompletionExportDTO.comSurvey=Research completed
com.wunding.learn.file.api.dto.export.project.ProjectCompletionExportDTO.evaNum=Number of evaluations
com.wunding.learn.file.api.dto.export.project.ProjectCompletionExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.project.ProjectCompletionExportDTO.isCompletion=Whether to graduate
com.wunding.learn.file.api.dto.export.project.ProjectCompletionExportDTO.isExam=passed the exam
com.wunding.learn.file.api.dto.export.project.ProjectCompletionExportDTO.isHomework=Assignment submission
com.wunding.learn.file.api.dto.export.project.ProjectCompletionExportDTO.isLearned=Course learned
com.wunding.learn.file.api.dto.export.project.ProjectCompletionExportDTO.isPreCompletion=Meet graduation requirements
com.wunding.learn.file.api.dto.export.project.ProjectCompletionExportDTO.jobName=post
com.wunding.learn.file.api.dto.export.project.ProjectCompletionExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.project.ProjectCompletionExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.project.ProjectCompletionExportDTO.signNum=Number of check-ins
com.wunding.learn.file.api.dto.export.project.ProjectFixedCycleExportDTO.createTime=creation time
com.wunding.learn.file.api.dto.export.project.ProjectFixedCycleExportDTO.cycleDay=Cycle days
com.wunding.learn.file.api.dto.export.project.ProjectFixedCycleExportDTO.isPublish=state
com.wunding.learn.file.api.dto.export.project.ProjectFixedCycleExportDTO.peopleNum=The number of participants
com.wunding.learn.file.api.dto.export.project.ProjectFixedCycleExportDTO.proName=project name
com.wunding.learn.file.api.dto.export.project.ProjectFixedCycleExportDTO.proNo=Item Number
com.wunding.learn.file.api.dto.export.project.ProjectFixedCycleExportDTO.publishBy=publisher
com.wunding.learn.file.api.dto.export.project.ProjectFixedCycleExportDTO.relatedProjectNum=Number of associated items
com.wunding.learn.file.api.dto.export.project.ProjectFixedCycleExportDTO.taskNum=Number of tasks
com.wunding.learn.file.api.dto.export.project.ProjectFixedCycleStatisticTaskDetailExportDTO.costTime=class(Second)
com.wunding.learn.file.api.dto.export.project.ProjectFixedCycleStatisticTaskDetailExportDTO.finishedTime=actual end time
com.wunding.learn.file.api.dto.export.project.ProjectFixedCycleStatisticTaskDetailExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.project.ProjectFixedCycleStatisticTaskDetailExportDTO.isFinish=Task status
com.wunding.learn.file.api.dto.export.project.ProjectFixedCycleStatisticTaskDetailExportDTO.joinTime=actual start time
com.wunding.learn.file.api.dto.export.project.ProjectFixedCycleStatisticTaskDetailExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.project.ProjectFixedCycleStatisticTaskDetailExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.project.ProjectFixedCycleStatisticTaskDetailExportDTO.postName=post
com.wunding.learn.file.api.dto.export.project.ProjectFixedCycleStatisticTaskDetailExportDTO.taskFinishedTime=Time to get task points
com.wunding.learn.file.api.dto.export.project.ProjectFixedCycleStatisticTaskDetailExportDTO.taskName=mission name
com.wunding.learn.file.api.dto.export.project.ProjectFixedCycleStatisticTaskDetailExportDTO.taskScore=Task points
com.wunding.learn.file.api.dto.export.project.ProjectFixedCycleStatisticTaskDetailExportDTO.teamName=team
com.wunding.learn.file.api.dto.export.project.ProjectFixedCycleStatisticTaskDetailExportDTO.userScore=score
com.wunding.learn.file.api.dto.export.project.ProjectFixedDateExportDTO.createTime=creation time
com.wunding.learn.file.api.dto.export.project.ProjectFixedDateExportDTO.endTime=End Time
com.wunding.learn.file.api.dto.export.project.ProjectFixedDateExportDTO.isPublish=state
com.wunding.learn.file.api.dto.export.project.ProjectFixedDateExportDTO.peopleNum=The number of participants
com.wunding.learn.file.api.dto.export.project.ProjectFixedDateExportDTO.proName=project name
com.wunding.learn.file.api.dto.export.project.ProjectFixedDateExportDTO.proNo=Item Number
com.wunding.learn.file.api.dto.export.project.ProjectFixedDateExportDTO.publishBy=publisher
com.wunding.learn.file.api.dto.export.project.ProjectFixedDateExportDTO.relatedProjectNum=Number of associated items
com.wunding.learn.file.api.dto.export.project.ProjectFixedDateExportDTO.referencedName=Train Name
com.wunding.learn.file.api.dto.export.project.ProjectFixedDateExportDTO.startTime=Starting time
com.wunding.learn.file.api.dto.export.project.ProjectFixedDateExportDTO.taskNum=Number of tasks
com.wunding.learn.file.api.dto.export.project.ProjectHoldStatisticExportDTO.coverNum=Number of people covered
com.wunding.learn.file.api.dto.export.project.ProjectHoldStatisticExportDTO.holdNum=Events held
com.wunding.learn.file.api.dto.export.project.ProjectHoldStatisticExportDTO.joinNum=Number of training people
com.wunding.learn.file.api.dto.export.project.ProjectHoldStatisticExportDTO.lecturerJoinNum=Number of lecturers
com.wunding.learn.file.api.dto.export.project.ProjectHoldStatisticExportDTO.lecturerNum=Number of lecturers
com.wunding.learn.file.api.dto.export.project.ProjectHoldStatisticExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.project.ProjectHoldStatisticExportDTO.projectSatisfaction=Satisfaction
com.wunding.learn.file.api.dto.export.project.ProjectHoldStatisticExportDTO.totalInstructionTime=Total training hours
com.wunding.learn.file.api.dto.export.project.ProjectJoiningStaticExportDTO.coverMemberNum=Number of people covered
com.wunding.learn.file.api.dto.export.project.ProjectJoiningStaticExportDTO.coverMemberRatePercent=Coverage
com.wunding.learn.file.api.dto.export.project.ProjectJoiningStaticExportDTO.joinTrainNum=Participate in (training) sessions
com.wunding.learn.file.api.dto.export.project.ProjectJoiningStaticExportDTO.memberAvgHours=Class hours per student
com.wunding.learn.file.api.dto.export.project.ProjectJoiningStaticExportDTO.orgPath=Department name
com.wunding.learn.file.api.dto.export.project.ProjectJoiningStaticExportDTO.satisfaction=(average) satisfaction
com.wunding.learn.file.api.dto.export.project.ProjectJoiningStaticExportDTO.trainMemberNum=Number of training people
com.wunding.learn.file.api.dto.export.project.ProjectLecturerTeachDetailExportDTO.courseName=Course Title
com.wunding.learn.file.api.dto.export.project.ProjectLecturerTeachDetailExportDTO.endTime=End Time
com.wunding.learn.file.api.dto.export.project.ProjectLecturerTeachDetailExportDTO.evalScore=Evaluation points
com.wunding.learn.file.api.dto.export.project.ProjectLecturerTeachDetailExportDTO.planNo=Plan number
com.wunding.learn.file.api.dto.export.project.ProjectLecturerTeachDetailExportDTO.projectName=project name
com.wunding.learn.file.api.dto.export.project.ProjectLecturerTeachDetailExportDTO.projectNo=Item Number
com.wunding.learn.file.api.dto.export.project.ProjectLecturerTeachDetailExportDTO.startTime=Starting time
com.wunding.learn.file.api.dto.export.project.ProjectLecturerTeachDetailExportDTO.teachHours=Teaching (hours)
com.wunding.learn.file.api.dto.export.project.ProjectLecturerTeachDetailExportDTO.trainCategory=Training category (full path)
com.wunding.learn.file.api.dto.export.project.ProjectStatisticExportDTO.courseTaskCompleteRate=course completion rate
com.wunding.learn.file.api.dto.export.project.ProjectStatisticExportDTO.courseTaskNum=Number of courses
com.wunding.learn.file.api.dto.export.project.ProjectStatisticExportDTO.endTime=End Time
com.wunding.learn.file.api.dto.export.project.ProjectStatisticExportDTO.leaderFullName=head teacher
com.wunding.learn.file.api.dto.export.project.ProjectStatisticExportDTO.leaderUserName=account
com.wunding.learn.file.api.dto.export.project.ProjectStatisticExportDTO.lecturerSatisfaction=Lecturer satisfaction
com.wunding.learn.file.api.dto.export.project.ProjectStatisticExportDTO.peopleNum=Planned number of people
com.wunding.learn.file.api.dto.export.project.ProjectStatisticExportDTO.projectName=class name
com.wunding.learn.file.api.dto.export.project.ProjectStatisticExportDTO.projectSatisfaction=organizational satisfaction
com.wunding.learn.file.api.dto.export.project.ProjectStatisticExportDTO.signCompleteNum=Number of full attendance sign-ins
com.wunding.learn.file.api.dto.export.project.ProjectStatisticExportDTO.signNum=Number of people signing in
com.wunding.learn.file.api.dto.export.project.ProjectStatisticExportDTO.startTime=Starting time
com.wunding.learn.file.api.dto.export.project.ProjectStatisticLecturerExportDTO.avgScore=Evaluation points
com.wunding.learn.file.api.dto.export.project.ProjectStatisticLecturerExportDTO.courseName=course
com.wunding.learn.file.api.dto.export.project.ProjectStatisticLecturerExportDTO.endTime=End Time
com.wunding.learn.file.api.dto.export.project.ProjectStatisticLecturerExportDTO.lecturerFullName=lecturer
com.wunding.learn.file.api.dto.export.project.ProjectStatisticLecturerExportDTO.lecturerLoginName=account
com.wunding.learn.file.api.dto.export.project.ProjectStatisticLecturerExportDTO.lecturerNumber=Lecturer number
com.wunding.learn.file.api.dto.export.project.ProjectStatisticLecturerExportDTO.projectName=class name
com.wunding.learn.file.api.dto.export.project.ProjectStatisticLecturerExportDTO.startTime=Starting time
com.wunding.learn.file.api.dto.export.project.ProjectStatisticTaskDetailExportDTO.costTime=class(Second)
com.wunding.learn.file.api.dto.export.project.ProjectStatisticTaskDetailExportDTO.endTime=Task end time
com.wunding.learn.file.api.dto.export.project.ProjectStatisticTaskDetailExportDTO.finishedTime=actual end time
com.wunding.learn.file.api.dto.export.project.ProjectStatisticTaskDetailExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.project.ProjectStatisticTaskDetailExportDTO.isFinish=Task status
com.wunding.learn.file.api.dto.export.project.ProjectStatisticTaskDetailExportDTO.joinTime=actual start time
com.wunding.learn.file.api.dto.export.project.ProjectStatisticTaskDetailExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.project.ProjectStatisticTaskDetailExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.project.ProjectStatisticTaskDetailExportDTO.postName=post
com.wunding.learn.file.api.dto.export.project.ProjectStatisticTaskDetailExportDTO.startTime=Task start time
com.wunding.learn.file.api.dto.export.project.ProjectStatisticTaskDetailExportDTO.taskFinishedTime=Time to get task points
com.wunding.learn.file.api.dto.export.project.ProjectStatisticTaskDetailExportDTO.taskName=mission name
com.wunding.learn.file.api.dto.export.project.ProjectStatisticTaskDetailExportDTO.taskScore=Task points
com.wunding.learn.file.api.dto.export.project.ProjectStatisticTaskDetailExportDTO.teamName=team
com.wunding.learn.file.api.dto.export.project.ProjectStatisticTaskDetailExportDTO.userScore=score
com.wunding.learn.file.api.dto.export.project.QuickProjectExportDTO.createTime=creation time
com.wunding.learn.file.api.dto.export.project.QuickProjectExportDTO.endTime=End Time
com.wunding.learn.file.api.dto.export.project.QuickProjectExportDTO.isPublish=state
com.wunding.learn.file.api.dto.export.project.QuickProjectExportDTO.peopleNum=The number of participants
com.wunding.learn.file.api.dto.export.project.QuickProjectExportDTO.proName=Training name
com.wunding.learn.file.api.dto.export.project.QuickProjectExportDTO.proNo=Training number
com.wunding.learn.file.api.dto.export.project.QuickProjectExportDTO.publishBy=publisher
com.wunding.learn.file.api.dto.export.project.QuickProjectExportDTO.startTime=Starting time
com.wunding.learn.file.api.dto.export.project.QuickProjectStatisticTaskDetailExportDTO.costTime=class(s)
com.wunding.learn.file.api.dto.export.project.QuickProjectStatisticTaskDetailExportDTO.endTime=Task end time
com.wunding.learn.file.api.dto.export.project.QuickProjectStatisticTaskDetailExportDTO.finishedTime=actual end time
com.wunding.learn.file.api.dto.export.project.QuickProjectStatisticTaskDetailExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.project.QuickProjectStatisticTaskDetailExportDTO.isFinish=Task status
com.wunding.learn.file.api.dto.export.project.QuickProjectStatisticTaskDetailExportDTO.joinTime=actual start time
com.wunding.learn.file.api.dto.export.project.QuickProjectStatisticTaskDetailExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.project.QuickProjectStatisticTaskDetailExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.project.QuickProjectStatisticTaskDetailExportDTO.postName=post
com.wunding.learn.file.api.dto.export.project.QuickProjectStatisticTaskDetailExportDTO.startTime=Task start time
com.wunding.learn.file.api.dto.export.project.QuickProjectStatisticTaskDetailExportDTO.taskFinishedTime=Time to get task points
com.wunding.learn.file.api.dto.export.project.QuickProjectStatisticTaskDetailExportDTO.taskName=mission name
com.wunding.learn.file.api.dto.export.project.QuickProjectStatisticTaskDetailExportDTO.taskScore=Task points
com.wunding.learn.file.api.dto.export.project.QuickProjectStatisticTaskDetailExportDTO.userScore=score
com.wunding.learn.file.api.dto.export.project.SignUserExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.project.SignUserExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.project.SignUserExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.project.SignUserExportDTO.postName=post
com.wunding.learn.file.api.dto.export.project.SignUserExportDTO.signInTime=Check-in time
com.wunding.learn.file.api.dto.export.project.StatisticOrgLearnRankExportDTO.orgPath=Department name
com.wunding.learn.file.api.dto.export.project.StatisticOrgLearnRankExportDTO.rank=Ranking
com.wunding.learn.file.api.dto.export.project.StatisticOrgLearnRankExportDTO.score=Task points
com.wunding.learn.file.api.dto.export.project.StatisticPersonRankExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.project.StatisticPersonRankExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.project.StatisticPersonRankExportDTO.ranking=Ranking
com.wunding.learn.file.api.dto.export.project.StatisticPersonRankExportDTO.score=Task points
com.wunding.learn.file.api.dto.export.project.StatisticTeamLearnRankExportDTO.rank=Ranking
com.wunding.learn.file.api.dto.export.project.StatisticTeamLearnRankExportDTO.score=Task points
com.wunding.learn.file.api.dto.export.project.StatisticTeamLearnRankExportDTO.teamName=Team Name
com.wunding.learn.file.api.dto.export.project.StatisticOrgCompleteUserDetailNotFinishExportDTO.isImport=Import
com.wunding.learn.file.api.dto.export.project.StatisticOrgCompleteUserDetailNotFinishExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.project.StatisticOrgCompleteUserDetailNotFinishExportDTO.loginName=Account
com.wunding.learn.file.api.dto.export.project.StatisticOrgCompleteUserDetailNotFinishExportDTO.levelPathName=Department
com.wunding.learn.file.api.dto.export.project.StatisticOrgCompleteUserDetailNotFinishExportDTO.postName=Post
com.wunding.learn.file.api.dto.export.project.StatisticOrgCompleteUserDetailNotFinishExportDTO.createTime=Start Time
com.wunding.learn.file.api.dto.export.project.StatisticOrgCompleteUserDetailIsFinishExportDTO.isImport=Import
com.wunding.learn.file.api.dto.export.project.StatisticOrgCompleteUserDetailIsFinishExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.project.StatisticOrgCompleteUserDetailIsFinishExportDTO.loginName=Account
com.wunding.learn.file.api.dto.export.project.StatisticOrgCompleteUserDetailIsFinishExportDTO.levelPathName=Department
com.wunding.learn.file.api.dto.export.project.StatisticOrgCompleteUserDetailIsFinishExportDTO.postName=Post
com.wunding.learn.file.api.dto.export.project.StatisticOrgCompleteUserDetailIsFinishExportDTO.finishTime=Completion Time
com.wunding.learn.file.api.dto.export.project.TrainingPlanExportDTO.createBy=founder
com.wunding.learn.file.api.dto.export.project.TrainingPlanExportDTO.isPublish=Post status
com.wunding.learn.file.api.dto.export.project.TrainingPlanExportDTO.orgPath=organizer
com.wunding.learn.file.api.dto.export.project.TrainingPlanExportDTO.particularYear=years
com.wunding.learn.file.api.dto.export.project.TrainingPlanExportDTO.relationTrainNum=Number of related learning projects
com.wunding.learn.file.api.dto.export.project.TrainingPlanExportDTO.trainFee=Cost budget (yuan)
com.wunding.learn.file.api.dto.export.project.TrainingPlanExportDTO.trainPeriod=Number of planning periods
com.wunding.learn.file.api.dto.export.project.TrainingPlanExportDTO.trainPlanName=Plan name
com.wunding.learn.file.api.dto.export.project.WorkExportDTO.finishTime=Complete time
com.wunding.learn.file.api.dto.export.project.WorkExportDTO.isPublish=state
com.wunding.learn.file.api.dto.export.project.WorkExportDTO.publishTime=release time
com.wunding.learn.file.api.dto.export.project.WorkExportDTO.workExplain=Homework instructions
com.wunding.learn.file.api.dto.export.project.WorkExportDTO.workName=Job name
com.wunding.learn.file.api.dto.export.reading.BookExperienceCommentExportDTO.content=content
com.wunding.learn.file.api.dto.export.reading.BookExperienceCommentExportDTO.createTime=Review date
com.wunding.learn.file.api.dto.export.reading.BookExperienceCommentExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.reading.BookExperienceCommentExportDTO.loginName=Account
com.wunding.learn.file.api.dto.export.reading.BookExperienceCommentExportDTO.orgPath=organize
com.wunding.learn.file.api.dto.export.reading.BookExperienceExportDTO.bookName=book title
com.wunding.learn.file.api.dto.export.reading.BookExperienceExportDTO.commentNum=Number of comments
com.wunding.learn.file.api.dto.export.reading.BookExperienceExportDTO.content=Experience content
com.wunding.learn.file.api.dto.export.reading.BookExperienceExportDTO.createTime=Fill in the date
com.wunding.learn.file.api.dto.export.reading.BookExperienceExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.reading.BookExperienceExportDTO.likeNum=Number of likes
com.wunding.learn.file.api.dto.export.reading.BookExperienceExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.reading.BookExperienceExportDTO.orgPath=organize
com.wunding.learn.file.api.dto.export.reading.BookExperienceReportExportDTO.createTime=Report time
com.wunding.learn.file.api.dto.export.reading.BookExperienceReportExportDTO.fullName=whistleblower
com.wunding.learn.file.api.dto.export.reading.BookExperienceReportExportDTO.loginName=Report account
com.wunding.learn.file.api.dto.export.reading.BookExperienceReportExportDTO.orgPath=Whistleblower Unit
com.wunding.learn.file.api.dto.export.reading.BookExperienceReportExportDTO.reason=Report reason
com.wunding.learn.file.api.dto.export.reading.BookExperienceReportExportDTO.status=Processing status
com.wunding.learn.file.api.dto.export.reading.BookExperienceReportExportDTO.type=type
com.wunding.learn.file.api.dto.export.reading.BookExperienceReportExportDTO.updateTime=processing time
com.wunding.learn.file.api.dto.export.reading.BookExperienceStarExportDTO.createTime=Like date
com.wunding.learn.file.api.dto.export.reading.BookExperienceStarExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.reading.BookExperienceStarExportDTO.loginName=Account
com.wunding.learn.file.api.dto.export.reading.BookExperienceStarExportDTO.orgPath=organize
com.wunding.learn.file.api.dto.export.reading.BookManageExportDTO.courseCode=Course code
com.wunding.learn.file.api.dto.export.reading.BookManageExportDTO.courseName=Course Title
com.wunding.learn.file.api.dto.export.reading.BookManageExportDTO.createTime=add time
com.wunding.learn.file.api.dto.export.reading.BookManageExportDTO.name=book title
com.wunding.learn.file.api.dto.export.reading.BookManageExportDTO.sortNo=sort
com.wunding.learn.file.api.dto.export.reading.BookStudyProgressExportDTO.bookName=book title
com.wunding.learn.file.api.dto.export.reading.BookStudyProgressExportDTO.createTime=Study start time
com.wunding.learn.file.api.dto.export.reading.BookStudyProgressExportDTO.finishTime=Learning completion time
com.wunding.learn.file.api.dto.export.reading.BookStudyProgressExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.reading.BookStudyProgressExportDTO.status=state
com.wunding.learn.file.api.dto.export.reading.BookStudyProgressExportDTO.userLoginName=account
com.wunding.learn.file.api.dto.export.reading.BookStudyProgressExportDTO.userName=Name
com.wunding.learn.file.api.dto.export.reading.ReadingExportDTO.endTime=End Time
com.wunding.learn.file.api.dto.export.reading.ReadingExportDTO.isPublish=Post status
com.wunding.learn.file.api.dto.export.reading.ReadingExportDTO.no=Reading number
com.wunding.learn.file.api.dto.export.reading.ReadingExportDTO.orgPath=Create an organization
com.wunding.learn.file.api.dto.export.reading.ReadingExportDTO.publishBy=publisher
com.wunding.learn.file.api.dto.export.reading.ReadingExportDTO.publishTime=release time
com.wunding.learn.file.api.dto.export.reading.ReadingExportDTO.startTime=Starting time
com.wunding.learn.file.api.dto.export.reading.ReadingExportDTO.subject=Common reading name
com.wunding.learn.file.api.dto.export.reading.ReadingSignExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.reading.ReadingSignExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.reading.ReadingSignExportDTO.orgPath=organize
com.wunding.learn.file.api.dto.export.reading.ReadingSignExportDTO.signCount=frequency
com.wunding.learn.file.api.dto.export.reading.TaskFinishExportDTO.createTime=Complete time
com.wunding.learn.file.api.dto.export.reading.TaskFinishExportDTO.orgPath=organize
com.wunding.learn.file.api.dto.export.reading.TaskFinishExportDTO.taskName=mission name
com.wunding.learn.file.api.dto.export.reading.TaskFinishExportDTO.userLoginName=account
com.wunding.learn.file.api.dto.export.reading.TaskFinishExportDTO.userName=student
com.wunding.learn.file.api.dto.export.reading.UserRankReadingExportDTO.orgPath=organize
com.wunding.learn.file.api.dto.export.reading.UserRankReadingExportDTO.ranking=Ranking
com.wunding.learn.file.api.dto.export.reading.UserRankReadingExportDTO.score=total score
com.wunding.learn.file.api.dto.export.reading.UserRankReadingExportDTO.userLoginName=account
com.wunding.learn.file.api.dto.export.reading.UserRankReadingExportDTO.userName=student
com.wunding.learn.file.api.dto.export.recruiting.RecruitingAssistantExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.recruiting.RecruitingAssistantExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.recruiting.RecruitingAssistantExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.recruiting.RecruitingAssistantExportDTO.participationNum=Number of people reviewed
com.wunding.learn.file.api.dto.export.recruiting.RecruitingAssistantExportDTO.viewLimit=Audit scope
com.wunding.learn.file.api.dto.export.recruiting.RecruitingAuditSurveyDetailExportDTO.answerContent=Answer content
com.wunding.learn.file.api.dto.export.recruiting.RecruitingAuditSurveyDetailExportDTO.finishTime=Submission time
com.wunding.learn.file.api.dto.export.recruiting.RecruitingAuditSurveyDetailExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.recruiting.RecruitingAuditSurveyDetailExportDTO.levelPathName=department
com.wunding.learn.file.api.dto.export.recruiting.RecruitingAuditSurveyDetailExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.recruiting.RecruitingAuditSurveyDetailExportDTO.questionName=topic
com.wunding.learn.file.api.dto.export.recruiting.RecruitingAuditSurveyDetailExportDTO.questionType=question type
com.wunding.learn.file.api.dto.export.recruiting.RecruitingAuditSurveyDetailExportDTO.surveyName=Survey name
com.wunding.learn.file.api.dto.export.recruiting.RecruitingExportDTO.address=Place
com.wunding.learn.file.api.dto.export.recruiting.RecruitingExportDTO.isPublish=Post status
com.wunding.learn.file.api.dto.export.recruiting.RecruitingExportDTO.limitNum=Planned number of people
com.wunding.learn.file.api.dto.export.recruiting.RecruitingExportDTO.longTimeOpen=Whether to recruit for a long time
com.wunding.learn.file.api.dto.export.recruiting.RecruitingExportDTO.openBeginTime=Opening hours
com.wunding.learn.file.api.dto.export.recruiting.RecruitingExportDTO.recruitingCode=Recruitment number
com.wunding.learn.file.api.dto.export.recruiting.RecruitingExportDTO.signUpCount=registered
com.wunding.learn.file.api.dto.export.recruiting.RecruitingExportDTO.title=title
com.wunding.learn.file.api.dto.export.recruiting.RecruitingExportDTO.type=Recruitment type
com.wunding.learn.file.api.dto.export.recruiting.RecruitingMaterialExportDTO.exampleFileType=material type
com.wunding.learn.file.api.dto.export.recruiting.RecruitingMaterialExportDTO.sort=display order
com.wunding.learn.file.api.dto.export.recruiting.RecruitingMaterialExportDTO.title=Material title
com.wunding.learn.file.api.dto.export.recruiting.RecruitingMaterialExportDTO.transformStatus=Case file transcoding status
com.wunding.learn.file.api.dto.export.recruiting.RecruitingMaterialRule2ExportDTO.description=Material provision instructions
com.wunding.learn.file.api.dto.export.recruiting.RecruitingMaterialRule2ExportDTO.fileMaxSize=File size limit (M)
com.wunding.learn.file.api.dto.export.recruiting.RecruitingMaterialRule2ExportDTO.fileType=Material support type
com.wunding.learn.file.api.dto.export.recruiting.RecruitingMaterialRule2ExportDTO.isSaveLib=Whether to be put into storage
com.wunding.learn.file.api.dto.export.recruiting.RecruitingMaterialRule2ExportDTO.materialType=Material category
com.wunding.learn.file.api.dto.export.recruiting.RecruitingMaterialRule2ExportDTO.required=Is it required?
com.wunding.learn.file.api.dto.export.recruiting.RecruitingMaterialRule2ExportDTO.sort=sort
com.wunding.learn.file.api.dto.export.recruiting.RecruitingMaterialRule2ExportDTO.title=Material title
com.wunding.learn.file.api.dto.export.recruiting.RecruitingMaterialRuleExportDTO.description=Material provision instructions
com.wunding.learn.file.api.dto.export.recruiting.RecruitingMaterialRuleExportDTO.fileMaxSize=File size limit (M)
com.wunding.learn.file.api.dto.export.recruiting.RecruitingMaterialRuleExportDTO.fileType=Material support type
com.wunding.learn.file.api.dto.export.recruiting.RecruitingMaterialRuleExportDTO.materialType=Material category
com.wunding.learn.file.api.dto.export.recruiting.RecruitingMaterialRuleExportDTO.required=Is it required?
com.wunding.learn.file.api.dto.export.recruiting.RecruitingMaterialRuleExportDTO.sort=sort
com.wunding.learn.file.api.dto.export.recruiting.RecruitingMaterialRuleExportDTO.title=Material title
com.wunding.learn.file.api.dto.export.recruiting.RecruitingParticipationRecordExportDTO.auditName=Administrative staff
com.wunding.learn.file.api.dto.export.recruiting.RecruitingParticipationRecordExportDTO.createTime=Registration date
com.wunding.learn.file.api.dto.export.recruiting.RecruitingParticipationRecordExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.recruiting.RecruitingParticipationRecordExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.recruiting.RecruitingParticipationRecordExportDTO.score=review score
com.wunding.learn.file.api.dto.export.recruiting.RecruitingParticipationRecordExportDTO.status=Approval Status
com.wunding.learn.file.api.dto.export.recruiting.RecruitingParticipationRecordExportDTO.userName=Name
com.wunding.learn.file.api.dto.export.special.SpecialCategoryExportDTO.categoryName=Category Name
com.wunding.learn.file.api.dto.export.special.SpecialCategoryExportDTO.isAvailable=Whether to enable
com.wunding.learn.file.api.dto.export.special.SpecialCategoryExportDTO.parentName=upper level
com.wunding.learn.file.api.dto.export.special.SpecialCategoryExportDTO.sort=display order
com.wunding.learn.file.api.dto.export.special.SpecialCompletionExportDTO.comSurvey=Research completed
com.wunding.learn.file.api.dto.export.special.SpecialCompletionExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.special.SpecialCompletionExportDTO.isCompletion=Whether to graduate
com.wunding.learn.file.api.dto.export.special.SpecialCompletionExportDTO.isExam=passed the exam
com.wunding.learn.file.api.dto.export.special.SpecialCompletionExportDTO.isLearned=Course learned
com.wunding.learn.file.api.dto.export.special.SpecialCompletionExportDTO.isPreCompletion=Meet graduation requirements
com.wunding.learn.file.api.dto.export.special.SpecialCompletionExportDTO.jobName=post
com.wunding.learn.file.api.dto.export.special.SpecialCompletionExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.special.SpecialCompletionExportDTO.orgName=department
com.wunding.learn.file.api.dto.export.special.SpecialFixedCycleExportDTO.createTime=creation time
com.wunding.learn.file.api.dto.export.special.SpecialFixedCycleExportDTO.isPublish=state
com.wunding.learn.file.api.dto.export.special.SpecialFixedCycleExportDTO.joinNumber=The number of participants
com.wunding.learn.file.api.dto.export.special.SpecialFixedCycleExportDTO.publishBy=publisher
com.wunding.learn.file.api.dto.export.special.SpecialFixedCycleExportDTO.specialName=Topic name
com.wunding.learn.file.api.dto.export.special.SpecialFixedCycleExportDTO.specialNo=Topic number
com.wunding.learn.file.api.dto.export.special.SpecialFixedCycleExportDTO.taskNumber=Number of tasks
com.wunding.learn.file.api.dto.export.special.SpecialFixedDateExportDTO.createTime=creation time
com.wunding.learn.file.api.dto.export.special.SpecialFixedDateExportDTO.endTime=End Time
com.wunding.learn.file.api.dto.export.special.SpecialFixedDateExportDTO.isPublish=state
com.wunding.learn.file.api.dto.export.special.SpecialFixedDateExportDTO.joinNumber=The number of participants
com.wunding.learn.file.api.dto.export.special.SpecialFixedDateExportDTO.publishBy=publisher
com.wunding.learn.file.api.dto.export.special.SpecialFixedDateExportDTO.specialName=Topic name
com.wunding.learn.file.api.dto.export.special.SpecialFixedDateExportDTO.specialNo=Topic number
com.wunding.learn.file.api.dto.export.special.SpecialFixedDateExportDTO.startTime=Starting time
com.wunding.learn.file.api.dto.export.special.SpecialFixedDateExportDTO.taskNumber=Number of tasks
com.wunding.learn.file.api.dto.export.special.SpecialLabelExportDTO.categoryName=Tag name
com.wunding.learn.file.api.dto.export.special.SpecialLabelExportDTO.isAvailable=Whether to enable
com.wunding.learn.file.api.dto.export.special.SpecialLabelExportDTO.parentName=upper level
com.wunding.learn.file.api.dto.export.special.SpecialLabelExportDTO.sort=display order
com.wunding.learn.file.api.dto.export.special.StatisticFixedCycleTaskDetailExportDTO.courseHour=class
com.wunding.learn.file.api.dto.export.special.StatisticFixedCycleTaskDetailExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.special.StatisticFixedCycleTaskDetailExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.special.StatisticFixedCycleTaskDetailExportDTO.postName=post
com.wunding.learn.file.api.dto.export.special.StatisticFixedCycleTaskDetailExportDTO.taskName=mission name
com.wunding.learn.file.api.dto.export.special.StatisticFixedCycleTaskDetailExportDTO.userName=Name
com.wunding.learn.file.api.dto.export.special.StatisticFixedCycleTaskDetailExportDTO.userScore=score
com.wunding.learn.file.api.dto.export.special.StatisticTaskDetailExportDTO.courseHour=class
com.wunding.learn.file.api.dto.export.special.StatisticTaskDetailExportDTO.endTime=Task end time
com.wunding.learn.file.api.dto.export.special.StatisticTaskDetailExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.special.StatisticTaskDetailExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.special.StatisticTaskDetailExportDTO.postName=post
com.wunding.learn.file.api.dto.export.special.StatisticTaskDetailExportDTO.startTime=Task start time
com.wunding.learn.file.api.dto.export.special.StatisticTaskDetailExportDTO.taskName=mission name
com.wunding.learn.file.api.dto.export.special.StatisticTaskDetailExportDTO.userName=Name
com.wunding.learn.file.api.dto.export.special.StatisticTaskDetailExportDTO.userScore=score
com.wunding.learn.file.api.dto.export.survey.SurveyAnalysisQuestionExportDTO.questionOption=choice or answer
com.wunding.learn.file.api.dto.export.survey.SurveyAnalysisQuestionExportDTO.questionType=question type
com.wunding.learn.file.api.dto.export.survey.SurveyAnalysisQuestionExportDTO.ratio=Proportion
com.wunding.learn.file.api.dto.export.survey.SurveyAnalysisQuestionExportDTO.sortNo=serial number
com.wunding.learn.file.api.dto.export.survey.SurveyAnalysisQuestionExportDTO.title=title
com.wunding.learn.file.api.dto.export.survey.SurveyAnalysisQuestionExportDTO.totalCount=total
com.wunding.learn.file.api.dto.export.survey.SurveyExportDTO.description=Research introduction
com.wunding.learn.file.api.dto.export.survey.SurveyExportDTO.publishBy=announcer
com.wunding.learn.file.api.dto.export.survey.SurveyExportDTO.publishTime=release time
com.wunding.learn.file.api.dto.export.survey.SurveyExportDTO.published=state
com.wunding.learn.file.api.dto.export.survey.SurveyExportDTO.questionCount=Total number of questions
com.wunding.learn.file.api.dto.export.survey.SurveyExportDTO.startAndEndTime=Starting and ending time
com.wunding.learn.file.api.dto.export.survey.SurveyExportDTO.surveyName=Survey name
com.wunding.learn.file.api.dto.export.survey.SurveyExportDTO.surveyNo=Survey number
com.wunding.learn.file.api.dto.export.survey.SurveyRecordDetailExportDTO.answer=Answer content
com.wunding.learn.file.api.dto.export.survey.SurveyRecordDetailExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.survey.SurveyRecordDetailExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.survey.SurveyRecordDetailExportDTO.orgName=department
com.wunding.learn.file.api.dto.export.survey.SurveyRecordDetailExportDTO.questionName=topic
com.wunding.learn.file.api.dto.export.survey.SurveyRecordDetailExportDTO.questionType=question type
com.wunding.learn.file.api.dto.export.survey.SurveyRecordDetailExportDTO.surveyName=Survey name
com.wunding.learn.file.api.dto.export.survey.SurveyRecordDetailExportDTO.updateTime=Submission time
com.wunding.learn.file.api.dto.export.sys.BizLogExportDTO.bizAction=Operation type
com.wunding.learn.file.api.dto.export.sys.BizLogExportDTO.bizDiff=Change log
com.wunding.learn.file.api.dto.export.sys.BizLogExportDTO.bizId=Primary key ID
com.wunding.learn.file.api.dto.export.sys.BizLogExportDTO.bizLogTime=Operation date
com.wunding.learn.file.api.dto.export.sys.BizLogExportDTO.bizLoginName=Operation account
com.wunding.learn.file.api.dto.export.sys.BizLogExportDTO.bizName=object name
com.wunding.learn.file.api.dto.export.sys.BizLogExportDTO.bizTime=time consuming
com.wunding.learn.file.api.dto.export.sys.BizLogExportDTO.bizType=Operation object
com.wunding.learn.file.api.dto.export.sys.BizLogExportDTO.bizUserId=Operation user id
com.wunding.learn.file.api.dto.export.sys.ClientVersionExportDTO.pid=product name
com.wunding.learn.file.api.dto.export.sys.ClientVersionExportDTO.publishDate=release date
com.wunding.learn.file.api.dto.export.sys.ClientVersionExportDTO.verCaption=Release Notes
com.wunding.learn.file.api.dto.export.sys.ClientVersionExportDTO.verName=release version
com.wunding.learn.file.api.dto.export.sys.ClientVersionExportDTO.verNo=Build number
com.wunding.learn.file.api.dto.export.sys.DictExportDTO.description=display name
com.wunding.learn.file.api.dto.export.sys.DictExportDTO.dictLevel=Hierarchy
com.wunding.learn.file.api.dto.export.sys.DictExportDTO.dictName=Dictionary name
com.wunding.learn.file.api.dto.export.sys.DictExportDTO.dictValue=Dictionary value
com.wunding.learn.file.api.dto.export.sys.DictExportDTO.isAvailable=it's usable or not
com.wunding.learn.file.api.dto.export.sys.DictExportDTO.parentName=Superior name
com.wunding.learn.file.api.dto.export.sys.DictExportDTO.sortNo=sort
com.wunding.learn.file.api.dto.export.sys.DictExportDTO.sysDefined=Is it system defined?
com.wunding.learn.file.api.dto.export.sys.FeedbackExportDTO.createTime=feedback time
com.wunding.learn.file.api.dto.export.sys.FeedbackExportDTO.isProcess=state
com.wunding.learn.file.api.dto.export.sys.FeedbackExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.sys.FeedbackExportDTO.processMemo=Processing Instructions
com.wunding.learn.file.api.dto.export.sys.FeedbackExportDTO.processTime=processing time
com.wunding.learn.file.api.dto.export.sys.FeedbackExportDTO.source=source
com.wunding.learn.file.api.dto.export.sys.FeedbackExportDTO.suggestion=Feedback content
com.wunding.learn.file.api.dto.export.sys.FeedbackExportDTO.telephone=contact number
com.wunding.learn.file.api.dto.export.sys.FeedbackExportDTO.userName=Name
com.wunding.learn.file.api.dto.export.sys.HomePageConfigExportDTO.isAvailable=state
com.wunding.learn.file.api.dto.export.sys.HomePageConfigExportDTO.isGlobalDefault=global default
com.wunding.learn.file.api.dto.export.sys.HomePageConfigExportDTO.name=Scheme name
com.wunding.learn.file.api.dto.export.sys.HomePageConfigExportDTO.orgFullPath=organize
com.wunding.learn.file.api.dto.export.sys.HomeRouterVisitDetailExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.sys.HomeRouterVisitDetailExportDTO.itemName=module name
com.wunding.learn.file.api.dto.export.sys.HomeRouterVisitDetailExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.sys.HomeRouterVisitDetailExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.sys.HomeRouterVisitDetailExportDTO.visitTime=interview time
com.wunding.learn.file.api.dto.export.sys.LogExportDTO.createTime=Operation date
com.wunding.learn.file.api.dto.export.sys.LogExportDTO.loginName=Operation account
com.wunding.learn.file.api.dto.export.sys.LogExportDTO.targetId=Primary key ID
com.wunding.learn.file.api.dto.export.sys.LogExportDTO.targetName=object name
com.wunding.learn.file.api.dto.export.sys.LogExportDTO.targetTypeName=Operation object
com.wunding.learn.file.api.dto.export.sys.LogExportDTO.type=Operation code
com.wunding.learn.file.api.dto.export.sys.OrgExportDTO.createBy=Creator name
com.wunding.learn.file.api.dto.export.sys.OrgExportDTO.createTime=creation time
com.wunding.learn.file.api.dto.export.sys.OrgExportDTO.levelCode=hierarchical coding
com.wunding.learn.file.api.dto.export.sys.OrgExportDTO.levelPathName=Level name
com.wunding.learn.file.api.dto.export.sys.OrgExportDTO.orgUserCount=Number Of People
com.wunding.learn.file.api.dto.export.sys.OrgExportDTO.orgCode=organizational coding
com.wunding.learn.file.api.dto.export.sys.OrgExportDTO.orgName=name of association
com.wunding.learn.file.api.dto.export.sys.OrgExportDTO.parentCode=Parent organization code
com.wunding.learn.file.api.dto.export.sys.OrgThirdSyncRecordExportDTO.createTime=Update time
com.wunding.learn.file.api.dto.export.sys.OrgThirdSyncRecordExportDTO.deptId=department id
com.wunding.learn.file.api.dto.export.sys.OrgThirdSyncRecordExportDTO.deptName=Department name
com.wunding.learn.file.api.dto.export.sys.OrgThirdSyncRecordExportDTO.deptParentId=Parent department id
com.wunding.learn.file.api.dto.export.sys.OrgThirdSyncRecordExportDTO.deptParentName=Parent department name
com.wunding.learn.file.api.dto.export.sys.OrgThirdSyncRecordExportDTO.operateType=Operation type
com.wunding.learn.file.api.dto.export.sys.OrgThirdSyncRecordExportDTO.order=serial number
com.wunding.learn.file.api.dto.export.sys.PostThirdSyncRecordExportDTO.createTime=Update time
com.wunding.learn.file.api.dto.export.sys.PostThirdSyncRecordExportDTO.operateType=Operation type
com.wunding.learn.file.api.dto.export.sys.PostThirdSyncRecordExportDTO.postName=Position Title
com.wunding.learn.file.api.dto.export.sys.PushMessageExportDTO.channelName=notification channel
com.wunding.learn.file.api.dto.export.sys.PushMessageExportDTO.eventName=message event
com.wunding.learn.file.api.dto.export.sys.PushMessageExportDTO.functionModule=Business module
com.wunding.learn.file.api.dto.export.sys.PushMessageExportDTO.pushAppointTime=Schedule delivery time
com.wunding.learn.file.api.dto.export.sys.PushMessageExportDTO.pushMethod=sending method
com.wunding.learn.file.api.dto.export.sys.PushMessageExportDTO.pushStatus=send status
com.wunding.learn.file.api.dto.export.sys.PushMessageExportDTO.pushTime=Send time
com.wunding.learn.file.api.dto.export.sys.PushMessageExportDTO.resourceName=business name
com.wunding.learn.file.api.dto.export.sys.PushMessageExportDTO.viewLimit=send the object
com.wunding.learn.file.api.dto.export.sys.RoleExportDTO.id=role number
com.wunding.learn.file.api.dto.export.sys.RoleExportDTO.isAvailable=Whether to enable
com.wunding.learn.file.api.dto.export.sys.RoleExportDTO.parentName=Superior role name
com.wunding.learn.file.api.dto.export.sys.RoleExportDTO.userCount=Number of people
com.wunding.learn.file.api.dto.export.sys.RoleExportDTO.roleName=Role Name
com.wunding.learn.file.api.dto.export.sys.RoleExportDTO.sortNo=display order
com.wunding.learn.file.api.dto.export.sys.RoleExportDTO.sysDefined=Is it system defined?
com.wunding.learn.file.api.dto.export.sys.TitleExportDTO.minToMaxValue=Experience range
com.wunding.learn.file.api.dto.export.sys.TitleExportDTO.name=Title name
com.wunding.learn.file.api.dto.export.sys.UserExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.sys.UserExportDTO.isAvailable=it's usable or not
com.wunding.learn.file.api.dto.export.sys.UserExportDTO.isLive=Activate now
com.wunding.learn.file.api.dto.export.sys.UserExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.sys.UserExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.sys.UserExportDTO.postName=post
com.wunding.learn.file.api.dto.export.sys.UserExportDTO.roleName=Role
com.wunding.learn.file.api.dto.export.sys.UserExportDTO.sex=gender
com.wunding.learn.file.api.dto.export.sys.UserExportDTO.timeIdentity=temporal identity
com.wunding.learn.file.api.dto.export.sys.UserThirdSyncRecordExportDTO.createTime=Update time
com.wunding.learn.file.api.dto.export.sys.UserThirdSyncRecordExportDTO.deptId=department id
com.wunding.learn.file.api.dto.export.sys.UserThirdSyncRecordExportDTO.deptName=Department name
com.wunding.learn.file.api.dto.export.sys.UserThirdSyncRecordExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.sys.UserThirdSyncRecordExportDTO.operateType=Operation type
com.wunding.learn.file.api.dto.export.sys.UserThirdSyncRecordExportDTO.order=Department serial number
com.wunding.learn.file.api.dto.export.sys.UserThirdSyncRecordExportDTO.position=Position
com.wunding.learn.file.api.dto.export.sys.UserThirdSyncRecordExportDTO.thirdUserId=Qiwei id
com.wunding.learn.file.api.dto.export.sys.UserThirdSyncRecordExportDTO.userName=Name
com.wunding.learn.file.api.dto.export.sys.UserVisitItemRecordExportDTO.fullName=user
com.wunding.learn.file.api.dto.export.sys.UserVisitItemRecordExportDTO.itemName=program name
com.wunding.learn.file.api.dto.export.sys.UserVisitItemRecordExportDTO.loginName=Account
com.wunding.learn.file.api.dto.export.sys.UserVisitItemRecordExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.sys.UserVisitItemRecordExportDTO.visitTime=interview time
com.wunding.learn.file.api.dto.export.sys.UserWhiteExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.sys.UserWhiteExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.sys.UserWhiteExportDTO.orgPath=Department (optional)
com.wunding.learn.file.api.dto.export.taskscore.TaskScoreExportDTO.accessType=method of obtaining
com.wunding.learn.file.api.dto.export.taskscore.TaskScoreExportDTO.activityName=Event name
com.wunding.learn.file.api.dto.export.taskscore.TaskScoreExportDTO.activityResourceType=type of activity
com.wunding.learn.file.api.dto.export.taskscore.TaskScoreExportDTO.createTime=Get Time
com.wunding.learn.file.api.dto.export.taskscore.TaskScoreExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.taskscore.TaskScoreExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.taskscore.TaskScoreExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.taskscore.TaskScoreExportDTO.remark=Remark
com.wunding.learn.file.api.dto.export.taskscore.TaskScoreExportDTO.score=activity score
com.wunding.learn.file.api.dto.export.taskscore.TaskScoreExportDTO.stageName=stage
com.wunding.learn.file.api.dto.export.train.ActivityPracticeRecordExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.train.ActivityPracticeRecordExportDTO.isPass=pass or not
com.wunding.learn.file.api.dto.export.train.ActivityPracticeRecordExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.train.ActivityPracticeRecordExportDTO.practiceTime=Practical operation date
com.wunding.learn.file.api.dto.export.train.ActivityPracticeRecordExportDTO.remark=Remark
com.wunding.learn.file.api.dto.export.train.ActivityPracticeRecordExportDTO.score=Fraction
com.wunding.learn.file.api.dto.export.train.ActivityPracticeRecordExportDTO.userName=account
com.wunding.learn.file.api.dto.export.train.LearnMapActivityExportDTO.activityName=Event name
com.wunding.learn.file.api.dto.export.train.LearnMapActivityExportDTO.createLoginName=account
com.wunding.learn.file.api.dto.export.train.LearnMapActivityExportDTO.createTime=Add date
com.wunding.learn.file.api.dto.export.train.LearnMapActivityExportDTO.createUserName=add a person
com.wunding.learn.file.api.dto.export.train.LearnMapActivityExportDTO.isCompulsory=Select compulsory courses
com.wunding.learn.file.api.dto.export.train.LearnMapActivityExportDTO.orgName=department
com.wunding.learn.file.api.dto.export.train.LearnMapActivityExportDTO.phaseName=Stage name
com.wunding.learn.file.api.dto.export.train.LearnMapActivityExportDTO.resourceNo=Activity resource number
com.wunding.learn.file.api.dto.export.train.LearnMapActivityExportDTO.resourceTypeName=type of activity
com.wunding.learn.file.api.dto.export.train.LearnMapActivityProgressExportDTO.activityName=Event name
com.wunding.learn.file.api.dto.export.train.LearnMapActivityProgressExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.train.LearnMapActivityProgressExportDTO.isCompulsory=Select compulsory courses
com.wunding.learn.file.api.dto.export.train.LearnMapActivityProgressExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.train.LearnMapActivityProgressExportDTO.mapName=Learn map names
com.wunding.learn.file.api.dto.export.train.LearnMapActivityProgressExportDTO.orgName=department
com.wunding.learn.file.api.dto.export.train.LearnMapActivityProgressExportDTO.phaseName=Stage name
com.wunding.learn.file.api.dto.export.train.LearnMapActivityProgressExportDTO.resourceType=type of activity
com.wunding.learn.file.api.dto.export.train.LearnMapActivityProgressExportDTO.status=finished condition
com.wunding.learn.file.api.dto.export.train.LearnMapActivityProgressExportDTO.targetIdentityName=developable identity
com.wunding.learn.file.api.dto.export.train.LearnMapExecExportDTO.createByFullName=founder
com.wunding.learn.file.api.dto.export.train.LearnMapExecExportDTO.createByLoginName=account
com.wunding.learn.file.api.dto.export.train.LearnMapExecExportDTO.endTime=End Time
com.wunding.learn.file.api.dto.export.train.LearnMapExecExportDTO.execName=mission name
com.wunding.learn.file.api.dto.export.train.LearnMapExecExportDTO.execNo=task coding
com.wunding.learn.file.api.dto.export.train.LearnMapExecExportDTO.isPublish=Post status
com.wunding.learn.file.api.dto.export.train.LearnMapExecExportDTO.mapName=study map
com.wunding.learn.file.api.dto.export.train.LearnMapExecExportDTO.orgName=department
com.wunding.learn.file.api.dto.export.train.LearnMapExecExportDTO.publishByFullName=publisher
com.wunding.learn.file.api.dto.export.train.LearnMapExecExportDTO.publishTime=release time
com.wunding.learn.file.api.dto.export.train.LearnMapExecExportDTO.startTime=Starting time
com.wunding.learn.file.api.dto.export.train.LearnMapExportDTO.activityCount=Number of activities
com.wunding.learn.file.api.dto.export.train.LearnMapExportDTO.courseCount=Number of courses
com.wunding.learn.file.api.dto.export.train.LearnMapExportDTO.createLoginName=account
com.wunding.learn.file.api.dto.export.train.LearnMapExportDTO.createTime=creation time
com.wunding.learn.file.api.dto.export.train.LearnMapExportDTO.createUserName=founder
com.wunding.learn.file.api.dto.export.train.LearnMapExportDTO.isPublish=Post status
com.wunding.learn.file.api.dto.export.train.LearnMapExportDTO.mapName=Learn map names
com.wunding.learn.file.api.dto.export.train.LearnMapExportDTO.mapNo=Learn map numbers
com.wunding.learn.file.api.dto.export.train.LearnMapExportDTO.mapType=category
com.wunding.learn.file.api.dto.export.train.LearnMapExportDTO.orgName=department
com.wunding.learn.file.api.dto.export.train.LearnMapExportDTO.publishTime=release time
com.wunding.learn.file.api.dto.export.train.LearnMapExportDTO.publishUserName=publisher
com.wunding.learn.file.api.dto.export.train.LearnMapExportDTO.sourceIdentityName=Identity name
com.wunding.learn.file.api.dto.export.train.LearnMapExportDTO.targetIdentityName=developable identity
com.wunding.learn.file.api.dto.export.train.LearnMapExportDTO.totalCoursewareDuration=Lesson time (minutes)
com.wunding.learn.file.api.dto.export.train.LearnMapLearnDetailExportDTO.activityName=Event name
com.wunding.learn.file.api.dto.export.train.LearnMapLearnDetailExportDTO.courseTime=Lesson time (seconds)
com.wunding.learn.file.api.dto.export.train.LearnMapLearnDetailExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.train.LearnMapLearnDetailExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.train.LearnMapLearnDetailExportDTO.orgName=department
com.wunding.learn.file.api.dto.export.train.LearnMapLearnDetailExportDTO.phaseName=Stage name
com.wunding.learn.file.api.dto.export.train.LearnMapLearnDetailExportDTO.postName=post
com.wunding.learn.file.api.dto.export.train.LearnMapLearnDetailExportDTO.resourceType=type of activity
com.wunding.learn.file.api.dto.export.train.LearnMapLearnDetailExportDTO.status=finished condition
com.wunding.learn.file.api.dto.export.train.LearnMapLearnDetailExportDTO.testScore=score
com.wunding.learn.file.api.dto.export.train.LearnMapLearnDetailNonePhaseExportDTO.activityName=Event name
com.wunding.learn.file.api.dto.export.train.LearnMapLearnDetailNonePhaseExportDTO.courseTime=Lesson time (seconds)
com.wunding.learn.file.api.dto.export.train.LearnMapLearnDetailNonePhaseExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.train.LearnMapLearnDetailNonePhaseExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.train.LearnMapLearnDetailNonePhaseExportDTO.orgName=department
com.wunding.learn.file.api.dto.export.train.LearnMapLearnDetailNonePhaseExportDTO.postName=post
com.wunding.learn.file.api.dto.export.train.LearnMapLearnDetailNonePhaseExportDTO.resourceType=type of activity
com.wunding.learn.file.api.dto.export.train.LearnMapLearnDetailNonePhaseExportDTO.status=finished condition
com.wunding.learn.file.api.dto.export.train.LearnMapLearnDetailNonePhaseExportDTO.testScore=score
com.wunding.learn.file.api.dto.export.train.LearnMapLearnUserFinishExportDTO.finishTime=Complete time
com.wunding.learn.file.api.dto.export.train.LearnMapLearnUserFinishExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.train.LearnMapLearnUserFinishExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.train.LearnMapLearnUserFinishExportDTO.orgName=department
com.wunding.learn.file.api.dto.export.train.LearnMapLearnUserPartExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.train.LearnMapLearnUserPartExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.train.LearnMapLearnUserPartExportDTO.orgName=department
com.wunding.learn.file.api.dto.export.train.LearnMapLearnUserPartExportDTO.startTime=participation time
com.wunding.learn.file.api.dto.export.train.LearnMapProgressExportDTO.completionCompulsoryRate=Completion rate of required courses
com.wunding.learn.file.api.dto.export.train.LearnMapProgressExportDTO.completionRate=overall completion rate
com.wunding.learn.file.api.dto.export.train.LearnMapProgressExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.train.LearnMapProgressExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.train.LearnMapProgressExportDTO.mapName=Learn map names
com.wunding.learn.file.api.dto.export.train.LearnMapProgressExportDTO.orgName=department
com.wunding.learn.file.api.dto.export.train.LearnMapProgressExportDTO.status=finished condition
com.wunding.learn.file.api.dto.export.train.LearnMapProgressExportDTO.targetIdentityName=developable identity
com.wunding.learn.file.api.dto.export.train.SupplierExportDTO.abbreviation=abbreviation
com.wunding.learn.file.api.dto.export.train.SupplierExportDTO.createLoginName=account
com.wunding.learn.file.api.dto.export.train.SupplierExportDTO.createName=add a person
com.wunding.learn.file.api.dto.export.train.SupplierExportDTO.createTime=Add date
com.wunding.learn.file.api.dto.export.train.SupplierExportDTO.isAvailable=state
com.wunding.learn.file.api.dto.export.train.SupplierExportDTO.lecturerCount=Number of lecturers
com.wunding.learn.file.api.dto.export.train.SupplierExportDTO.supplierName=Supplier name
com.wunding.learn.file.api.dto.export.train.SupplierExportDTO.supplierNo=supplier code
com.wunding.learn.file.api.dto.export.train.SupplierExportDTO.territory=Areas of cooperation
com.wunding.learn.file.api.dto.export.train.SupplierFileExportDTO.categoryName=Data classification
com.wunding.learn.file.api.dto.export.train.SupplierFileExportDTO.createLoginName=account
com.wunding.learn.file.api.dto.export.train.SupplierFileExportDTO.createName=Uploader
com.wunding.learn.file.api.dto.export.train.SupplierFileExportDTO.fileSize=File size
com.wunding.learn.file.api.dto.export.train.SupplierFileExportDTO.rowNo=serial number
com.wunding.learn.file.api.dto.export.train.SupplierFileExportDTO.title=title
com.wunding.learn.file.api.dto.export.train.TrainActivityExportDTO.activityName=Event name
com.wunding.learn.file.api.dto.export.train.TrainActivityExportDTO.activityTypeName=Activities
com.wunding.learn.file.api.dto.export.train.TrainActivityExportDTO.endTime=End Time
com.wunding.learn.file.api.dto.export.train.TrainActivityExportDTO.resourceTypeName=type of activity
com.wunding.learn.file.api.dto.export.train.TrainActivityExportDTO.secondStageName=Second stage name
com.wunding.learn.file.api.dto.export.train.TrainActivityExportDTO.stageName=Stage name
com.wunding.learn.file.api.dto.export.train.TrainActivityExportDTO.startTime=Starting time
com.wunding.learn.file.api.dto.export.train.TrainExportDTO.endTime=end date
com.wunding.learn.file.api.dto.export.train.TrainExportDTO.publishBy=announcer
com.wunding.learn.file.api.dto.export.train.TrainExportDTO.createTime=Create Time
com.wunding.learn.file.api.dto.export.train.TrainExportDTO.holdOrgName=Organizing
com.wunding.learn.file.api.dto.export.train.TrainExportDTO.isPublish=Post status
com.wunding.learn.file.api.dto.export.train.TrainExportDTO.manageUserName=principal
com.wunding.learn.file.api.dto.export.train.TrainExportDTO.manageUserNo=account
com.wunding.learn.file.api.dto.export.train.TrainExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.train.TrainExportDTO.startTime=start date
com.wunding.learn.file.api.dto.export.train.TrainExportDTO.trainName=project name
com.wunding.learn.file.api.dto.export.train.TrainExportDTO.trainNo=Project code
com.wunding.learn.file.api.dto.export.train.TrainExportDTO.trainTypeName=Training Category
com.wunding.learn.file.api.dto.export.train.TrainLecturerTeachExportDTO.cwName=Courseware
com.wunding.learn.file.api.dto.export.train.TrainLecturerTeachExportDTO.lecturerCategory=Lecturer classification
com.wunding.learn.file.api.dto.export.train.TrainLecturerTeachExportDTO.lecturerLevel=Lecturer level
com.wunding.learn.file.api.dto.export.train.TrainLecturerTeachExportDTO.lecturerName=Lecturer's name
com.wunding.learn.file.api.dto.export.train.TrainLecturerTeachExportDTO.loginName=Lecturer account
com.wunding.learn.file.api.dto.export.train.TrainLecturerTeachExportDTO.orgPath=Lecturer's unit
com.wunding.learn.file.api.dto.export.train.TrainLecturerTeachExportDTO.projectName=Teaching projects
com.wunding.learn.file.api.dto.export.train.TrainLecturerTeachExportDTO.studentCount=Number of students
com.wunding.learn.file.api.dto.export.train.TrainLecturerTeachExportDTO.teachTime=Teaching time
com.wunding.learn.file.api.dto.export.train.TrainLecturerTeachExportDTO.timeLength=Teaching hours
com.wunding.learn.file.api.dto.export.train.TrainLecturerTeachExportDTO.totalScore=score
com.wunding.learn.file.api.dto.export.train.TrainOrgLearnExportDTO.orgPath=Department name
com.wunding.learn.file.api.dto.export.train.TrainOrgLearnExportDTO.rank=Ranking
com.wunding.learn.file.api.dto.export.train.TrainOrgLearnExportDTO.score=activity points
com.wunding.learn.file.api.dto.export.train.TrainPersonRankExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.train.TrainPersonRankExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.train.TrainPersonRankExportDTO.ranking=Ranking
com.wunding.learn.file.api.dto.export.train.TrainPersonRankExportDTO.score=activity points
com.wunding.learn.file.api.dto.export.train.TrainProjectExportDTO.createTime=creation time
com.wunding.learn.file.api.dto.export.train.TrainProjectExportDTO.endTime=End Time
com.wunding.learn.file.api.dto.export.train.TrainProjectExportDTO.proName=class name
com.wunding.learn.file.api.dto.export.train.TrainProjectExportDTO.proNo=class number
com.wunding.learn.file.api.dto.export.train.TrainProjectExportDTO.publishStatus=state
com.wunding.learn.file.api.dto.export.train.TrainProjectExportDTO.referencedName=project name
com.wunding.learn.file.api.dto.export.train.TrainProjectExportDTO.startTime=Starting time
com.wunding.learn.file.api.dto.export.train.TrainTaskDetailExportDTO.costTime=class
com.wunding.learn.file.api.dto.export.train.TrainTaskDetailExportDTO.endTime=Event end time
com.wunding.learn.file.api.dto.export.train.TrainTaskDetailExportDTO.finishedTime=actual end time
com.wunding.learn.file.api.dto.export.train.TrainTaskDetailExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.train.TrainTaskDetailExportDTO.isFinish=Active status
com.wunding.learn.file.api.dto.export.train.TrainTaskDetailExportDTO.joinTime=actual start time
com.wunding.learn.file.api.dto.export.train.TrainTaskDetailExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.train.TrainTaskDetailExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.train.TrainTaskDetailExportDTO.postName=post
com.wunding.learn.file.api.dto.export.train.TrainTaskDetailExportDTO.startTime=Event start time
com.wunding.learn.file.api.dto.export.train.TrainTaskDetailExportDTO.taskFinishedTime=Time to earn event points
com.wunding.learn.file.api.dto.export.train.TrainTaskDetailExportDTO.taskName=Event name
com.wunding.learn.file.api.dto.export.train.TrainTaskDetailExportDTO.taskScore=activity points
com.wunding.learn.file.api.dto.export.train.TrainTaskDetailExportDTO.userScore=score
com.wunding.learn.file.api.dto.export.train.TrainWithOutAssistExportDTO.deptName=Organizing
com.wunding.learn.file.api.dto.export.train.TrainWithOutAssistExportDTO.endTime=Registration end time
com.wunding.learn.file.api.dto.export.train.TrainWithOutAssistExportDTO.name=Batch name
com.wunding.learn.file.api.dto.export.train.TrainWithOutAssistExportDTO.no=Training number
com.wunding.learn.file.api.dto.export.train.TrainWithOutAssistExportDTO.startTime=Registration start time
com.wunding.learn.file.api.dto.export.train.TrainWithOutAssistExportDTO.statusName=Post status
com.wunding.learn.file.api.dto.export.train.TrainWithOutExportDTO.deptName=Organizing
com.wunding.learn.file.api.dto.export.train.TrainWithOutExportDTO.endTime=Registration end time
com.wunding.learn.file.api.dto.export.train.TrainWithOutExportDTO.name=Batch name
com.wunding.learn.file.api.dto.export.train.TrainWithOutExportDTO.no=Training number
com.wunding.learn.file.api.dto.export.train.TrainWithOutExportDTO.passRate=Passing rate
com.wunding.learn.file.api.dto.export.train.TrainWithOutExportDTO.startTime=Registration start time
com.wunding.learn.file.api.dto.export.train.TrainWithOutExportDTO.statusName=Post status
com.wunding.learn.file.api.dto.export.train.TrainWithoutApplyDetailExportDTO.agencyConfirmStatus=Institutional registration status
com.wunding.learn.file.api.dto.export.train.TrainWithoutApplyDetailExportDTO.agencyTrainStatus=Institutional training status
com.wunding.learn.file.api.dto.export.train.TrainWithoutApplyDetailExportDTO.applyTime=registration time
com.wunding.learn.file.api.dto.export.train.TrainWithoutApplyDetailExportDTO.applyUserName=Name
com.wunding.learn.file.api.dto.export.train.TrainWithoutApplyDetailExportDTO.applyUserNo=account
com.wunding.learn.file.api.dto.export.train.TrainWithoutApplyDetailExportDTO.cerInside=Internal certification
com.wunding.learn.file.api.dto.export.train.TrainWithoutApplyDetailExportDTO.certificationStatus=External certificate image
com.wunding.learn.file.api.dto.export.train.TrainWithoutApplyDetailExportDTO.education=Educational qualifications
com.wunding.learn.file.api.dto.export.train.TrainWithoutApplyDetailExportDTO.examScore=Theoretical scores
com.wunding.learn.file.api.dto.export.train.TrainWithoutApplyDetailExportDTO.examScoreTime=Theory result date
com.wunding.learn.file.api.dto.export.train.TrainWithoutApplyDetailExportDTO.examType=Exam type
com.wunding.learn.file.api.dto.export.train.TrainWithoutApplyDetailExportDTO.idNumber=identification number
com.wunding.learn.file.api.dto.export.train.TrainWithoutApplyDetailExportDTO.insideConfirmStatus=Internal confirmation status
com.wunding.learn.file.api.dto.export.train.TrainWithoutApplyDetailExportDTO.insideConfirmTime=Internal confirmation date
com.wunding.learn.file.api.dto.export.train.TrainWithoutApplyDetailExportDTO.isPass=pass or not
com.wunding.learn.file.api.dto.export.train.TrainWithoutApplyDetailExportDTO.isWork=On-the-job status
com.wunding.learn.file.api.dto.export.train.TrainWithoutApplyDetailExportDTO.jobName=major
com.wunding.learn.file.api.dto.export.train.TrainWithoutApplyDetailExportDTO.operateScore=Practical results
com.wunding.learn.file.api.dto.export.train.TrainWithoutApplyDetailExportDTO.operateScoreTime=Practical result date
com.wunding.learn.file.api.dto.export.train.TrainWithoutApplyDetailExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.train.TrainWithoutApplyDetailExportDTO.phone=phone number
com.wunding.learn.file.api.dto.export.train.TrainWithoutApplyDetailExportDTO.sex=gender
com.wunding.learn.file.api.dto.export.train.TrainWithoutApplyDetailExportDTO.trainWithoutName=training batch
com.wunding.learn.file.api.dto.export.train.TrainWithoutApplyExportDTO.applyTime=registration time
com.wunding.learn.file.api.dto.export.train.TrainWithoutApplyExportDTO.applyType=ways of registration
com.wunding.learn.file.api.dto.export.train.TrainWithoutApplyExportDTO.applyUserName=Name
com.wunding.learn.file.api.dto.export.train.TrainWithoutApplyExportDTO.applyUserNo=account
com.wunding.learn.file.api.dto.export.train.TrainWithoutApplyExportDTO.education=Educational qualifications
com.wunding.learn.file.api.dto.export.train.TrainWithoutApplyExportDTO.examType=Exam type
com.wunding.learn.file.api.dto.export.train.TrainWithoutApplyExportDTO.idNumber=ID number
com.wunding.learn.file.api.dto.export.train.TrainWithoutApplyExportDTO.insideConfirmStatus=Confirm status
com.wunding.learn.file.api.dto.export.train.TrainWithoutApplyExportDTO.isWork=On-the-job status
com.wunding.learn.file.api.dto.export.train.TrainWithoutApplyExportDTO.jobName=Training professional
com.wunding.learn.file.api.dto.export.train.TrainWithoutApplyExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.train.TrainWithoutApplyExportDTO.phone=Phone number
com.wunding.learn.file.api.dto.export.train.TrainWithoutApplyExportDTO.salaryPay=Deduction method
com.wunding.learn.file.api.dto.export.train.TrainWithoutApplyExportDTO.sex=gender
com.wunding.learn.file.api.dto.export.train.TrainWithoutApplyExportDTO.trainWithoutName=Batch name
com.wunding.learn.file.api.dto.export.train.TrainWithoutResultExportDTO.agencyConfirmStatus=Institutional registration status
com.wunding.learn.file.api.dto.export.train.TrainWithoutResultExportDTO.agencyTrainStatus=Institutional training status
com.wunding.learn.file.api.dto.export.train.TrainWithoutResultExportDTO.applyTime=registration time
com.wunding.learn.file.api.dto.export.train.TrainWithoutResultExportDTO.applyUserName=Name
com.wunding.learn.file.api.dto.export.train.TrainWithoutResultExportDTO.applyUserNo=account
com.wunding.learn.file.api.dto.export.train.TrainWithoutResultExportDTO.education=Educational qualifications
com.wunding.learn.file.api.dto.export.train.TrainWithoutResultExportDTO.examScore=Theoretical scores
com.wunding.learn.file.api.dto.export.train.TrainWithoutResultExportDTO.examScoreTime=Theory result date
com.wunding.learn.file.api.dto.export.train.TrainWithoutResultExportDTO.examType=Exam type
com.wunding.learn.file.api.dto.export.train.TrainWithoutResultExportDTO.idNumber=ID number
com.wunding.learn.file.api.dto.export.train.TrainWithoutResultExportDTO.isWork=On-the-job status
com.wunding.learn.file.api.dto.export.train.TrainWithoutResultExportDTO.jobName=major
com.wunding.learn.file.api.dto.export.train.TrainWithoutResultExportDTO.operateScore=Practical results
com.wunding.learn.file.api.dto.export.train.TrainWithoutResultExportDTO.operateScoreTime=Practical result date
com.wunding.learn.file.api.dto.export.train.TrainWithoutResultExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.train.TrainWithoutResultExportDTO.phone=phone number
com.wunding.learn.file.api.dto.export.train.TrainWithoutResultExportDTO.sex=gender
com.wunding.learn.file.api.dto.export.train.TrainWithoutResultExportDTO.trainWithoutName=training batch
com.wunding.learn.file.api.dto.export.train.TrainWithoutResultExportDTO.insideConfirmTime=Internal confirmation date
com.wunding.learn.file.api.dto.export.train.TrainWithoutResultExportDTO.insideConfirmStatus=Internal confirmation status
com.wunding.learn.file.api.dto.export.train.TrainWithoutResultExportDTO.isPass=Passes or not
com.wunding.learn.file.api.dto.export.train.TrainWithoutResultExportDTO.cerInside=Internal issuance
com.wunding.learn.file.api.dto.export.train.TrainWithoutResultExportDTO.certificationStatus=Picture of the external certificate
com.wunding.learn.file.api.dto.export.train.TrainWithoutStatisticsExportDTO.absenteeismNum=Number of absences
com.wunding.learn.file.api.dto.export.train.TrainWithoutStatisticsExportDTO.agencyConfirmNum=Number of people confirmed by the organization
com.wunding.learn.file.api.dto.export.train.TrainWithoutStatisticsExportDTO.avgExamScore=exam average score
com.wunding.learn.file.api.dto.export.train.TrainWithoutStatisticsExportDTO.avgOperateScore=Practical average score
com.wunding.learn.file.api.dto.export.train.TrainWithoutStatisticsExportDTO.haveScoreNum=Number of people with grades
com.wunding.learn.file.api.dto.export.train.TrainWithoutStatisticsExportDTO.maxExamScore=highest exam score
com.wunding.learn.file.api.dto.export.train.TrainWithoutStatisticsExportDTO.maxOperateScore=Highest score in practice
com.wunding.learn.file.api.dto.export.train.TrainWithoutStatisticsExportDTO.missExamNum=Number of absentees
com.wunding.learn.file.api.dto.export.train.TrainWithoutStatisticsExportDTO.orgPath=department
com.wunding.learn.file.api.dto.export.train.TrainWithoutStatisticsExportDTO.passRate=Pass rate
com.wunding.learn.file.api.dto.export.train.TrainWithoutStatisticsExportDTO.sumNum=Total number of applicants
com.wunding.learn.file.api.dto.export.user.IdentityPostSystemDataExportDTO.code=coding
com.wunding.learn.file.api.dto.export.user.IdentityPostSystemDataExportDTO.levelPathName=Full path of the system
com.wunding.learn.file.api.dto.export.user.IdentityPostSystemDataExportDTO.name=name
com.wunding.learn.file.api.dto.export.user.IdentityPostSystemDataExportDTO.sortNo=sort
com.wunding.learn.file.api.dto.export.user.IdentityUserInfoExportDTO.createTime=add time
com.wunding.learn.file.api.dto.export.user.IdentityUserInfoExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.user.IdentityUserInfoExportDTO.orgName=department
com.wunding.learn.file.api.dto.export.user.IdentityUserInfoExportDTO.userName=Name
com.wunding.learn.file.api.dto.export.user.PermissionConfigExportDTO.code=coding
com.wunding.learn.file.api.dto.export.user.PermissionConfigExportDTO.createBy=founder
com.wunding.learn.file.api.dto.export.user.PermissionConfigExportDTO.createTime=creation time
com.wunding.learn.file.api.dto.export.user.PermissionConfigExportDTO.id=primary key
com.wunding.learn.file.api.dto.export.user.PermissionConfigExportDTO.isDel=delete or not
com.wunding.learn.file.api.dto.export.user.PermissionConfigExportDTO.level=Hierarchy
com.wunding.learn.file.api.dto.export.user.PermissionConfigExportDTO.levelPath=Full path
com.wunding.learn.file.api.dto.export.user.PermissionConfigExportDTO.levelPathName=full pathname
com.wunding.learn.file.api.dto.export.user.PermissionConfigExportDTO.parentId=superior id
com.wunding.learn.file.api.dto.export.user.PermissionConfigExportDTO.sortNo=sort
com.wunding.learn.file.api.dto.export.user.PermissionConfigExportDTO.title=title
com.wunding.learn.file.api.dto.export.user.PermissionConfigExportDTO.type=Type 1-column, 2-authority point
com.wunding.learn.file.api.dto.export.user.PermissionConfigExportDTO.updateBy=updater
com.wunding.learn.file.api.dto.export.user.PermissionConfigExportDTO.updateTime=Update time
com.wunding.learn.file.api.dto.export.user.PermissionRouterExportDTO.createBy=founder
com.wunding.learn.file.api.dto.export.user.PermissionRouterExportDTO.createTime=creation time
com.wunding.learn.file.api.dto.export.user.PermissionRouterExportDTO.id=primary key
com.wunding.learn.file.api.dto.export.user.PermissionRouterExportDTO.isDel=delete or not
com.wunding.learn.file.api.dto.export.user.PermissionRouterExportDTO.parentId=Permission directory configuration ID
com.wunding.learn.file.api.dto.export.user.PermissionRouterExportDTO.routerId=Route ID
com.wunding.learn.file.api.dto.export.user.PermissionRouterExportDTO.sortNo=sort
com.wunding.learn.file.api.dto.export.user.PermissionRouterExportDTO.updateBy=updater
com.wunding.learn.file.api.dto.export.user.PermissionRouterExportDTO.updateTime=Update time
com.wunding.learn.file.api.dto.export.user.UserIdentityInfoExportDTO.identityCategoryName=Identity Category
com.wunding.learn.file.api.dto.export.user.UserIdentityInfoExportDTO.identityDirectoryName=identity directory
com.wunding.learn.file.api.dto.export.user.UserIdentityInfoExportDTO.identityName=Identity name
com.wunding.learn.file.api.dto.export.user.UserIdentityListExportDTO.createTime=add time
com.wunding.learn.file.api.dto.export.user.UserIdentityListExportDTO.identityCategoryName=Identity Category
com.wunding.learn.file.api.dto.export.user.UserIdentityListExportDTO.identityDirectoryName=identity directory
com.wunding.learn.file.api.dto.export.user.UserIdentityListExportDTO.identityName=Identity name
com.wunding.learn.file.api.dto.export.user.UserIdentityListExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.user.UserIdentityListExportDTO.ogrName=department
com.wunding.learn.file.api.dto.export.user.UserIdentityListExportDTO.userName=Name
com.wunding.learn.file.api.dto.export.user.VisibleViewLimitUserExportDTO.loginName=account
com.wunding.learn.file.api.dto.export.user.VisibleViewLimitUserExportDTO.orgName=department
com.wunding.learn.file.api.dto.export.user.VisibleViewLimitUserExportDTO.postName=post
com.wunding.learn.file.api.dto.export.user.VisibleViewLimitUserExportDTO.userName=Name
com.wunding.learn.file.api.dto.export.march.MarchExportDTO.beginTime=start time
com.wunding.learn.file.api.dto.export.march.MarchExportDTO.code=coding
com.wunding.learn.file.api.dto.export.march.MarchExportDTO.endTime=end time
com.wunding.learn.file.api.dto.export.march.MarchExportDTO.isPublish=state
com.wunding.learn.file.api.dto.export.march.MarchExportDTO.name=name
com.wunding.learn.file.api.dto.export.march.MarchExportDTO.publishBy=publisher
com.wunding.learn.file.api.dto.export.march.MarchExportDTO.publishTime=release time
com.wunding.learn.file.api.dto.export.march.MarchExportDTO.orgName=department
com.wunding.learn.file.api.dto.export.march.MarchExportDTO.createTime=creation time
com.wunding.learn.file.api.dto.export.march.MarchCheckpointExportDTO.name=name
com.wunding.learn.file.api.dto.export.march.MarchCheckpointExportDTO.sort=sort
com.wunding.learn.file.api.dto.export.march.MarchCheckpointExportDTO.mileage=mileage
com.wunding.learn.file.api.dto.export.march.MarchCheckpointExportDTO.story=story
com.wunding.learn.file.api.dto.export.march.MarchCheckpointExportDTO.title=title
com.wunding.learn.file.api.dto.export.march.TaskExportDTO.name=name
com.wunding.learn.file.api.dto.export.march.TaskExportDTO.sort=sort
com.wunding.learn.file.api.dto.export.march.TaskExportDTO.mileage=mileage
com.wunding.learn.file.api.dto.export.march.TeamExportDTO.memberCount=Number of people
com.wunding.learn.file.api.dto.export.march.TeamExportDTO.name=Team Name
com.wunding.learn.file.api.dto.export.march.MarchPostExportDTO.title=title
com.wunding.learn.file.api.dto.export.march.MarchPostExportDTO.isDel=delete or not
com.wunding.learn.file.api.dto.export.march.MarchPostExportDTO.commentNum=Number of comments
com.wunding.learn.file.api.dto.export.march.MarchPostExportDTO.createFullName=Posted by
com.wunding.learn.file.api.dto.export.march.MarchPostExportDTO.createTime=Posting time
com.wunding.learn.file.api.dto.export.march.MarchPostExportDTO.sectionName=Topic section
com.wunding.learn.file.api.dto.export.march.MarchPostExportDTO.teamName=Team Name
com.wunding.learn.file.api.dto.export.march.MarchNoticeExportDTO.content=Explanation content
com.wunding.learn.file.api.dto.export.march.MarchNoticeExportDTO.isPublish=Post status
com.wunding.learn.file.api.dto.export.march.MarchNoticeExportDTO.name=Explanation name
com.wunding.learn.file.api.dto.export.march.MarchNoticeExportDTO.publishTime=release time
com.wunding.learn.file.api.dto.export.march.MarchCountCommentExportDTO.commentTime=Reply Time
com.wunding.learn.file.api.dto.export.march.MarchCountCommentExportDTO.content=Reply content
com.wunding.learn.file.api.dto.export.march.MarchCountCommentExportDTO.fullName=Name
com.wunding.learn.file.api.dto.export.march.MarchCountCommentExportDTO.teamName=Team Name
com.wunding.learn.file.api.dto.export.march.MarchCountCommentExportDTO.starNum=Number of likes
com.wunding.learn.file.api.dto.export.march.MarchCountCommentExportDTO.userName=Account
com.wunding.learn.file.api.dto.export.certification.AssessToolExportDTO.code=Tool code
com.wunding.learn.file.api.dto.export.certification.AssessToolExportDTO.name=Tool name
com.wunding.learn.file.api.dto.export.certification.AssessToolExportDTO.applicableHierarchyNames=Applicable hierarchy
com.wunding.learn.file.api.dto.export.certification.AssessToolExportDTO.usageClassificationNames=Usage classification
com.wunding.learn.file.api.dto.export.certification.AssessToolExportDTO.relationType=Relation type
com.wunding.learn.file.api.dto.export.certification.AssessToolExportDTO.relationObjectName=Relation name
com.wunding.learn.file.api.dto.export.certification.AssessToolExportDTO.isPublish=Status
com.wunding.learn.file.api.dto.export.certification.AssessToolExportDTO.publishTime=Publish time
com.wunding.learn.file.api.dto.export.certification.AssessToolExportDTO.createTime=Create time
com.wunding.learn.file.api.dto.export.certification.AssessToolExportDTO.publishBy=Publish by
com.wunding.learn.file.api.dto.export.certification.AssessToolExportDTO.createBy=Create by
com.wunding.learn.file.api.dto.export.certification.AssessToolExportDTO.creatorLoginName=Creator account
com.wunding.learn.file.api.dto.export.certification.AssessToolExportDTO.sortNo=Sort
com.wunding.learn.file.api.dto.export.certification.AssessProjectExportDTO.proNo=Assess no
com.wunding.learn.file.api.dto.export.certification.AssessProjectExportDTO.name=Project name
com.wunding.learn.file.api.dto.export.certification.AssessProjectExportDTO.toolName=Tool name
com.wunding.learn.file.api.dto.export.certification.AssessProjectExportDTO.categoryName=Category
com.wunding.learn.file.api.dto.export.certification.AssessProjectExportDTO.startTime=Start time
com.wunding.learn.file.api.dto.export.certification.AssessProjectExportDTO.endTime=End time
com.wunding.learn.file.api.dto.export.certification.AssessProjectExportDTO.isPublish=Status
com.wunding.learn.file.api.dto.export.certification.AssessProjectExportDTO.joinCount=Total people
com.wunding.learn.file.api.dto.export.certification.AssessProjectExportDTO.organizationName=Organization
com.wunding.learn.file.api.dto.export.certification.AssessProjectExportDTO.completeRate=Complete rate
com.wunding.learn.file.api.dto.export.certification.AssessProjectExportDTO.createBy=Create by
com.wunding.learn.file.api.dto.export.certification.AssessProjectExportDTO.creatorLoginName=Account
com.wunding.learn.file.api.dto.export.certification.AssessProjectExportDTO.creatorOrganization=Department
com.wunding.learn.file.api.dto.export.certification.AssessProjectExportDTO.createTime=Create time
com.wunding.learn.file.api.dto.export.certification.AssessProjectExportDTO.publishTime=Publish time
com.wunding.learn.file.api.dto.export.certification.AssessUserExportDTO2.fullName=Username
com.wunding.learn.file.api.dto.export.certification.AssessUserExportDTO2.loginName=Account
com.wunding.learn.file.api.dto.export.certification.AssessUserExportDTO2.orgPath=Department
com.wunding.learn.file.api.dto.export.certification.AssessUserExportDTO2.upperEvaluatorNames=Superior
com.wunding.learn.file.api.dto.export.certification.AssessUserExportDTO2.peersEvaluatorNames=Peers
com.wunding.learn.file.api.dto.export.certification.AssessUserExportDTO2.lowerEvaluatorNames=Subordinate
com.wunding.learn.file.api.dto.export.certification.AssessUserExportDTO1.fullName=Username
com.wunding.learn.file.api.dto.export.certification.AssessUserExportDTO1.loginName=Account
com.wunding.learn.file.api.dto.export.certification.AssessUserExportDTO1.orgPath=Department
com.wunding.learn.file.api.dto.export.certification.AssessUserExportDTO1.methodType=Assess type
com.wunding.learn.file.api.dto.export.certification.AssessUserExportDTO1.evaluatorFullName=Assessor username
com.wunding.learn.file.api.dto.export.certification.AssessUserExportDTO1.evaluatorLoginName=Assessor account
com.wunding.learn.file.api.dto.export.certification.AssessUserExportDTO1.evaluatorOrgPath=Department
com.wunding.learn.file.api.dto.export.certification.AssessDepManagerExportDTO.projectNo=Assess no
com.wunding.learn.file.api.dto.export.certification.AssessDepManagerExportDTO.projectName=Project name
com.wunding.learn.file.api.dto.export.certification.AssessDepManagerExportDTO.startTime=Start time
com.wunding.learn.file.api.dto.export.certification.AssessDepManagerExportDTO.endTime=End time
com.wunding.learn.file.api.dto.export.certification.AssessDepManagerExportDTO.fullName=Evaluated person
com.wunding.learn.file.api.dto.export.certification.AssessDepManagerExportDTO.loginName=Evaluated account
com.wunding.learn.file.api.dto.export.certification.AssessDepManagerExportDTO.orgName=Evaluated department
com.wunding.learn.file.api.dto.export.certification.AssessDepManagerExportDTO.upperEvaluatorNames=Superior
com.wunding.learn.file.api.dto.export.certification.AssessDepManagerExportDTO.peersEvaluatorNames=Peers
com.wunding.learn.file.api.dto.export.certification.AssessDepManagerExportDTO.lowerEvaluatorNames=Subordinate
com.wunding.learn.file.api.dto.export.certification.AssessDetailsExportDTO.fullName=Username
com.wunding.learn.file.api.dto.export.certification.AssessDetailsExportDTO.loginName=Account
com.wunding.learn.file.api.dto.export.certification.AssessDetailsExportDTO.orgPath=Department
com.wunding.learn.file.api.dto.export.certification.AssessDetailsExportDTO.methodType=Assess type
com.wunding.learn.file.api.dto.export.certification.AssessDetailsExportDTO.evaluatorFullName=Assessor username
com.wunding.learn.file.api.dto.export.certification.AssessDetailsExportDTO.evaluatorLoginName=Account
com.wunding.learn.file.api.dto.export.certification.AssessDetailsExportDTO.evaluatorOrgPath=Department
com.wunding.learn.file.api.dto.export.certification.AssessDetailsExportDTO.abilityTypeName=Ability type
com.wunding.learn.file.api.dto.export.certification.AssessDetailsExportDTO.abilityCode=Ability code
com.wunding.learn.file.api.dto.export.certification.AssessDetailsExportDTO.abilityName=Ability name
com.wunding.learn.file.api.dto.export.certification.AssessDetailsExportDTO.questionType=Question type
com.wunding.learn.file.api.dto.export.certification.AssessDetailsExportDTO.questionName=Question name
com.wunding.learn.file.api.dto.export.certification.AssessDetailsExportDTO.score=Assess score
com.wunding.learn.file.api.dto.export.certification.AssessDetailsExportDTO.questionAndAnswer=Answer content
com.wunding.learn.file.api.dto.export.certification.AssessDetailsExportDTO.completeStatus=Complete status
com.wunding.learn.file.api.dto.export.certification.AssessDetailsExportDTO.completeTime=Complete time
com.wunding.learn.file.api.dto.export.certification.AssessQuestionDetailExportDTO.fullName=The evaluated person
com.wunding.learn.file.api.dto.export.certification.AssessQuestionDetailExportDTO.loginName=The evaluated person account
com.wunding.learn.file.api.dto.export.certification.AssessQuestionDetailExportDTO.orgPath=Department
com.wunding.learn.file.api.dto.export.certification.AssessQuestionDetailExportDTO.assessDate=AssessDate
com.wunding.learn.file.api.dto.export.certification.AssessQuestionDetailExportDTO.proNo=Assess no
com.wunding.learn.file.api.dto.export.certification.AssessQuestionDetailExportDTO.proName=Assess project
com.wunding.learn.file.api.dto.export.certification.AssessQuestionDetailExportDTO.questionTypeName=Question type
com.wunding.learn.file.api.dto.export.certification.AssessQuestionDetailExportDTO.questionName=Question
com.wunding.learn.file.api.dto.export.certification.AssessQuestionDetailExportDTO.methodTypeName=Assess type
com.wunding.learn.file.api.dto.export.certification.AssessQuestionDetailExportDTO.assessUser=Assess user
com.wunding.learn.file.api.dto.export.certification.AssessQuestionDetailExportDTO.assessLoginName=Assess account
com.wunding.learn.file.api.dto.export.certification.AssessQuestionDetailExportDTO.score=Assess score
com.wunding.learn.file.api.dto.export.certification.AssessQuestionDetailExportDTO.userAnswer=Answer content
com.wunding.learn.file.api.dto.export.certification.AssessQuestionDetailExportDTO.answerTime=AssessTime
com.wunding.learn.file.api.dto.export.exam.CompetitionSessionUserDetailExportDTO.fullName=fullName
com.wunding.learn.file.api.dto.export.exam.CompetitionSessionUserDetailExportDTO.loginName=loginName
com.wunding.learn.file.api.dto.export.exam.CompetitionSessionUserDetailExportDTO.orgPath=orgPath
com.wunding.learn.file.api.dto.export.exam.CompetitionSessionUserDetailExportDTO.joinTime=joinTime
com.wunding.learn.file.api.dto.export.evluation.EvalReplyUser.loginName=Login name
com.wunding.learn.file.api.dto.export.evluation.EvalReplyUser.fullName=Full name
com.wunding.learn.file.api.dto.export.evluation.EvalReplyUser.finishTime=Evaluate Time
com.wunding.learn.file.api.dto.export.evluation.EvalReplyUser.orgName=Org name
com.wunding.learn.file.api.dto.export.project.ProjectEvaluationExportDTO.questionCategory=Question category
com.wunding.learn.file.api.dto.export.project.ProjectEvaluationExportDTO.questionName=Question name
com.wunding.learn.file.api.dto.export.project.ProjectEvaluationExportDTO.questionType=Question type
com.wunding.learn.file.api.dto.export.project.ProjectEvaluationExportDTO.totalScore=Total score
com.wunding.learn.file.api.dto.export.project.ProjectEvaluationExportDTO.avgScore=Composite score
#CourseStudyCourseStatisticsExportDTO
com.wunding.learn.file.api.dto.export.course.CourseStudyCourseStatisticsExportDTO.courseName=course name
com.wunding.learn.file.api.dto.export.course.CourseStudyCourseStatisticsExportDTO.categoryName=course type 
com.wunding.learn.file.api.dto.export.course.CourseStudyCourseStatisticsExportDTO.categoryLevelName=type path
com.wunding.learn.file.api.dto.export.course.CourseStudyCourseStatisticsExportDTO.learnFinishNeedTime=need time(s)
com.wunding.learn.file.api.dto.export.course.CourseStudyCourseStatisticsExportDTO.viewLimitUserCount=limit user count
com.wunding.learn.file.api.dto.export.course.CourseStudyCourseStatisticsExportDTO.finishNum=finishNum
com.wunding.learn.file.api.dto.export.course.CourseStudyCourseStatisticsExportDTO.unLearnUserNum=unLearnUserNum
com.wunding.learn.file.api.dto.export.course.CourseStudyCourseStatisticsExportDTO.underwayNum=underwayNum
com.wunding.learn.file.api.dto.export.course.CourseStudyCourseStatisticsExportDTO.totalCw=totalCw
com.wunding.learn.file.api.dto.export.course.CourseStudyCourseStatisticsExportDTO.totalCommPerson=totalCommPerson
com.wunding.learn.file.api.dto.export.course.CourseStudyCourseStatisticsExportDTO.synthesizeStar=synthesizeStar
com.wunding.learn.file.api.dto.export.course.CourseStudyCourseStatisticsExportDTO.finishRate=finishRate
#ExamUserByStatisticsExportDTO
com.wunding.learn.file.api.dto.export.exam.ExamUserByStatisticsExportDTO.fullName=Full name
com.wunding.learn.file.api.dto.export.exam.ExamUserByStatisticsExportDTO.loginName=Login name
com.wunding.learn.file.api.dto.export.exam.ExamUserByStatisticsExportDTO.postName=Post name
com.wunding.learn.file.api.dto.export.exam.ExamUserByStatisticsExportDTO.orgName=Department name
com.wunding.learn.file.api.dto.export.exam.ExamUserByStatisticsExportDTO.levelPathName=Department path
com.wunding.learn.file.api.dto.export.exam.ExamUserByStatisticsExportDTO.relateLevelPathName=Relate Department path
com.wunding.learn.file.api.dto.export.exam.ExamUserByStatisticsExportDTO.examScore=Exam score
com.wunding.learn.file.api.dto.export.exam.ExamUserByStatisticsExportDTO.score=Score
com.wunding.learn.file.api.dto.export.exam.ExamUserByStatisticsExportDTO.examStatus=Exam tatus
#CourseStudyCourseUserExportDTO
com.wunding.learn.file.api.dto.export.course.CourseStudyCourseUserExportDTO.userNo=Login name
com.wunding.learn.file.api.dto.export.course.CourseStudyCourseUserExportDTO.fullName=Full name
com.wunding.learn.file.api.dto.export.course.CourseStudyCourseUserExportDTO.orgName=Department name
com.wunding.learn.file.api.dto.export.course.CourseStudyCourseUserExportDTO.levelPathName=Department path
com.wunding.learn.file.api.dto.export.course.CourseStudyCourseUserExportDTO.relateLevelPathName=Relate Department path
#CourseStudyStatisticsExportDTO
com.wunding.learn.file.api.dto.export.course.CourseStudyStatisticsExportDTO.orgName=Department name
com.wunding.learn.file.api.dto.export.course.CourseStudyStatisticsExportDTO.levelPathName=Department path
com.wunding.learn.file.api.dto.export.course.CourseStudyStatisticsExportDTO.viewLimitUserCount=Number of people issued
com.wunding.learn.file.api.dto.export.course.CourseStudyStatisticsExportDTO.unLearnUserCount=Unlearn user count
com.wunding.learn.file.api.dto.export.course.CourseStudyStatisticsExportDTO.learningUserCount=Learning user count
com.wunding.learn.file.api.dto.export.course.CourseStudyStatisticsExportDTO.learnedUserCount=Learned user count
com.wunding.learn.file.api.dto.export.course.CourseStudyStatisticsExportDTO.finishRate=Finish rate
#CourseStudyStatisticsUserExportDTO
com.wunding.learn.file.api.dto.export.course.CourseStudyStatisticsUserExportDTO.fullName=User name
com.wunding.learn.file.api.dto.export.course.CourseStudyStatisticsUserExportDTO.loginName=Account
com.wunding.learn.file.api.dto.export.course.CourseStudyStatisticsUserExportDTO.orgName=Department name
com.wunding.learn.file.api.dto.export.course.CourseStudyStatisticsUserExportDTO.levelPathName=Department path
com.wunding.learn.file.api.dto.export.course.CourseStudyStatisticsUserExportDTO.relateLevelPathName=Relate Department path
#ProjectStatisticByProjectExportDTO
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByUserExportDTO.projectName=Project name
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByUserExportDTO.fullName=User name
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByUserExportDTO.loginName=Account
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByUserExportDTO.orgName=Department name
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByUserExportDTO.orgLevelPathName=Department path
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByUserExportDTO.relateLevelPathName=Relate Department path
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByUserExportDTO.taskProcess=Task progress
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByUserExportDTO.finishRate=Finish rate
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByUserExportDTO.taskScore=Task score
#ProjectStatisticByTypeUserLimitExportDTO
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByTypeUserLimitExportDTO.orgName=Department name
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByTypeUserLimitExportDTO.levelPathName=Department path
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByTypeUserLimitExportDTO.relateLevelPathName=Relate Department path
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByTypeUserLimitExportDTO.loginName=Account
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByTypeUserLimitExportDTO.fullName=User name
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByTypeUserLimitExportDTO.taskNum=Task num
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByTypeUserLimitExportDTO.isJoin=Join or not
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByTypeUserLimitExportDTO.joinTime=Join time
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByTypeUserLimitExportDTO.progress=Progress
#ProjectStatisticByTypeUserExportDTO
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByTypeUserExportDTO.orgName=Department name
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByTypeUserExportDTO.levelPathName=Department path
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByTypeUserExportDTO.relateLevelPathName=Relate Department path
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByTypeUserExportDTO.loginName=Account
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByTypeUserExportDTO.fullName=User name
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByTypeUserExportDTO.taskNum=Task num
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByTypeUserExportDTO.isJoin=Join or not
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByTypeUserExportDTO.joinTime=Join time
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByTypeUserExportDTO.progress=Progress
#ProjectStatisticByProjectExportDTO
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByProjectExportDTO.projectName=Project name
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByProjectExportDTO.taskNum=Task num
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByProjectExportDTO.viewLimitNum=Number of people issued
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByProjectExportDTO.joinNum=Join num
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByProjectExportDTO.notJoinNum=Not join num
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByProjectExportDTO.finishNum=Finish num
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByProjectExportDTO.joinRate=Join rate
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByProjectExportDTO.finishRate=Finish rate
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByProjectExportDTO.totalFinishRate=Total finish rate
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByProjectExportDTO.isExpire=Is expire
#ProjectStatisticByOrgExportDTO
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByOrgExportDTO.orgName=Department name
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByOrgExportDTO.orgLevelPathName=Department path
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByOrgExportDTO.taskScore=Task score
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByOrgExportDTO.viewLimitNum=Number of people issued
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByOrgExportDTO.joinNum=Join num
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByOrgExportDTO.notJoinNum=Not join num
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByOrgExportDTO.finishNum=Finish num
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByOrgExportDTO.joinRate=Join rate
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByOrgExportDTO.finishRate=Finish rate
com.wunding.learn.file.api.dto.export.project.ProjectStatisticByOrgExportDTO.totalFinishRate=Total finish rate
#CourseStudyCourseLearnUserExportDTO
com.wunding.learn.file.api.dto.export.analysis.CourseStudyCourseLearnUserExportDTO.userNo=Account
com.wunding.learn.file.api.dto.export.analysis.CourseStudyCourseLearnUserExportDTO.fullName=User name
com.wunding.learn.file.api.dto.export.analysis.CourseStudyCourseLearnUserExportDTO.orgName=Department name
com.wunding.learn.file.api.dto.export.analysis.CourseStudyCourseLearnUserExportDTO.levelPathName=Department path
com.wunding.learn.file.api.dto.export.analysis.CourseStudyCourseLearnUserExportDTO.relateLevelPathName=Relate Department path
com.wunding.learn.file.api.dto.export.analysis.CourseStudyCourseLearnUserExportDTO.minViewTime=Start learn time
com.wunding.learn.file.api.dto.export.analysis.CourseStudyCourseLearnUserExportDTO.maxViewTime=End learn time
com.wunding.learn.file.api.dto.export.analysis.CourseStudyCourseLearnUserExportDTO.totalDuration=Total duration(second)
#CourseStudyDetailExportDTO
com.wunding.learn.file.api.dto.export.course.CourseStudyDetailExportDTO.fullName=User name
com.wunding.learn.file.api.dto.export.course.CourseStudyDetailExportDTO.loginName=Account
com.wunding.learn.file.api.dto.export.course.CourseStudyDetailExportDTO.orgName=Department name
com.wunding.learn.file.api.dto.export.course.CourseStudyDetailExportDTO.orgPath=Department path
com.wunding.learn.file.api.dto.export.course.CourseStudyDetailExportDTO.relateLevelPathName=Relate Department path
com.wunding.learn.file.api.dto.export.course.CourseStudyDetailExportDTO.jobName=Post
com.wunding.learn.file.api.dto.export.course.CourseStudyDetailExportDTO.beginLearnTime=Start learn time
com.wunding.learn.file.api.dto.export.course.CourseStudyDetailExportDTO.lastLearnTime=Last learn time
com.wunding.learn.file.api.dto.export.course.CourseStudyDetailExportDTO.finishLearnTime=Finish learn time
com.wunding.learn.file.api.dto.export.course.CourseStudyDetailExportDTO.duration=Total duration(second)
com.wunding.learn.file.api.dto.export.course.CourseStudyDetailExportDTO.learnState=Learn state
com.wunding.learn.file.api.dto.export.course.CourseStudyDetailExportDTO.remainderCourseWareCount=Remainder CourseWare Count
#LecturerTrainExportDTO
com.wunding.learn.file.api.dto.export.lecturer.LecturerTrainExportDTO.lecturerCode=Lecturer Code
com.wunding.learn.file.api.dto.export.lecturer.LecturerTrainExportDTO.lecturerName=Lecturer Name
com.wunding.learn.file.api.dto.export.lecturer.LecturerTrainExportDTO.employeeNo=Employee No
com.wunding.learn.file.api.dto.export.lecturer.LecturerTrainExportDTO.trainProCode=Train Project Code
com.wunding.learn.file.api.dto.export.lecturer.LecturerTrainExportDTO.trainProName=Train Project Name
com.wunding.learn.file.api.dto.export.lecturer.LecturerTrainExportDTO.trainProStartTime=Train Project Start Time
com.wunding.learn.file.api.dto.export.lecturer.LecturerTrainExportDTO.orgName=Organize The Event
com.wunding.learn.file.api.dto.export.lecturer.LecturerTrainExportDTO.instructionNumber=Number Of Lectures
com.wunding.learn.file.api.dto.export.lecturer.LecturerTrainExportDTO.instructionTime=Teaching Duration(hour)
com.wunding.learn.file.api.dto.export.lecturer.LecturerTrainExportDTO.partCount=Number Of Participants
com.wunding.learn.file.api.dto.export.lecturer.LecturerTrainExportDTO.score=Score
#LecturerCourseExportDTO
com.wunding.learn.file.api.dto.export.course.LecturerCourseExportDTO.uploadTime=Upload Time
com.wunding.learn.file.api.dto.export.course.LecturerCourseExportDTO.courseName=Course Name
com.wunding.learn.file.api.dto.export.course.LecturerCourseExportDTO.totalCw=Number Of Courseware
com.wunding.learn.file.api.dto.export.course.LecturerCourseExportDTO.uploadType=Upload Type
com.wunding.learn.file.api.dto.export.course.LecturerCourseExportDTO.learnCount=Number Of Learners
com.wunding.learn.file.api.dto.export.course.LecturerCourseExportDTO.totalAgree=Number Of Likes
#LecturerCoursewareExportDTO
com.wunding.learn.file.api.dto.export.course.LecturerCoursewareExportDTO.uploadTime=Upload Time
com.wunding.learn.file.api.dto.export.course.LecturerCoursewareExportDTO.courseName=Course Name
com.wunding.learn.file.api.dto.export.course.LecturerCoursewareExportDTO.cwName=CourseWare Name
com.wunding.learn.file.api.dto.export.course.LecturerCoursewareExportDTO.uploadType=Upload Type
com.wunding.learn.file.api.dto.export.course.LecturerCoursewareExportDTO.totalComment=Number Of Comments
com.wunding.learn.file.api.dto.export.course.LecturerCoursewareExportDTO.noteCount=Number Of Notes
#FaceProjectExportDTO
com.wunding.learn.file.api.dto.export.project.FaceProjectExportDTO.createTime=Create time
com.wunding.learn.file.api.dto.export.project.FaceProjectExportDTO.isPublish=Status
com.wunding.learn.file.api.dto.export.project.FaceProjectExportDTO.peopleNum=Number Of Participants
com.wunding.learn.file.api.dto.export.project.FaceProjectExportDTO.proName=Project Name
com.wunding.learn.file.api.dto.export.project.FaceProjectExportDTO.proNo=Project No
com.wunding.learn.file.api.dto.export.project.FaceProjectExportDTO.publishBy=Publisher
com.wunding.learn.file.api.dto.export.project.FaceProjectExportDTO.taskNum=Number Of Tasks

#下面表头用业务路径而不是实体类路径
com.wunding.learn.certification.service.biz.impl.AssessProjectBizImpl.getAssessProgressExportHeads.username=Username
com.wunding.learn.certification.service.biz.impl.AssessProjectBizImpl.getAssessProgressExportHeads.loginName=Login name
com.wunding.learn.certification.service.biz.impl.AssessProjectBizImpl.getAssessProgressExportHeads.selfAssess=Self assess
com.wunding.learn.certification.service.biz.impl.AssessProjectBizImpl.getAssessProgressExportHeads.upperAssess=Upper assess
com.wunding.learn.certification.service.biz.impl.AssessProjectBizImpl.getAssessProgressExportHeads.peersAssess=Peers assess
com.wunding.learn.certification.service.biz.impl.AssessProjectBizImpl.getAssessProgressExportHeads.lowerAssess=Lower assess
com.wunding.learn.file.api.dto.export.certification.AssessEvaluatorListExportDTO.evaluatorFullName=Assess user
com.wunding.learn.file.api.dto.export.certification.AssessEvaluatorListExportDTO.completeTime=AssessDate
com.wunding.learn.file.api.dto.export.certification.AssessEvaluatorListExportDTO.evaluatorLoginName=Assess account
com.wunding.learn.file.api.dto.export.certification.AssessEvaluatorListExportDTO.methodTypeName=Assess type
com.wunding.learn.file.api.dto.export.certification.AssessEvaluatorListExportDTO.userFullName=The evaluated person
com.wunding.learn.file.api.dto.export.certification.AssessEvaluatorListExportDTO.userLoginName=The evaluated person account
com.wunding.learn.file.api.dto.export.certification.AssessEvaluatorListExportDTO.proName=Assess project
com.wunding.learn.file.api.dto.export.certification.AssessEvaluatorListExportDTO.orgPath=Evaluated department
com.wunding.learn.file.api.dto.export.UserPosterShareExportDTO.shareTime=Share time
com.wunding.learn.file.api.dto.export.UserPosterShareExportDTO.userName=Sharer
com.wunding.learn.file.api.dto.export.UserPosterShareExportDTO.loginName=account number
com.wunding.learn.file.api.dto.export.UserPosterShareExportDTO.orgPath=Department
com.wunding.learn.file.api.dto.export.UserPosterShareExportDTO.resourceType=Content type
com.wunding.learn.file.api.dto.export.UserPosterShareExportDTO.shareWay=Sharing channels

#课程分类导出
com.wunding.learn.file.api.dto.export.Course.CourseCategoryExport.First=First classification
com.wunding.learn.file.api.dto.export.Course.CourseCategoryExport.Second=Secondary classification
com.wunding.learn.file.api.dto.export.Course.CourseCategoryExport.Third=Third classification
com.wunding.learn.file.api.dto.export.Course.CourseCategoryExport.Four=Four classification
com.wunding.learn.file.api.dto.export.Course.CourseCategoryExport.Five=Five classification
com.wunding.learn.file.api.dto.export.Course.CourseCategoryExport.SortNo=Display order
com.wunding.learn.file.api.dto.export.Course.CourseCategoryExport.Enable=Enabled

com.wunding.learn.file.api.dto.export.train.LearningTargetManageOrgExportDTO.targetUserTypeName=Department
com.wunding.learn.file.api.dto.export.train.LearningTargetManageOrgExportDTO.startTime=Start Date
com.wunding.learn.file.api.dto.export.train.LearningTargetManageOrgExportDTO.endTime=End Date
com.wunding.learn.file.api.dto.export.train.LearningTargetManageOrgExportDTO.targetValueType=Target Value Type
com.wunding.learn.file.api.dto.export.train.LearningTargetManageOrgExportDTO.targetValue=Target Value
com.wunding.learn.file.api.dto.export.train.LearningTargetManageOrgExportDTO.orgName=Belonging Department
com.wunding.learn.file.api.dto.export.train.LearningTargetManageOrgExportDTO.isPublish=Publish Status
com.wunding.learn.file.api.dto.export.train.LearningTargetManageOrgExportDTO.activeType=Activity Type
com.wunding.learn.file.api.dto.export.train.LearningTargetManageOrgExportDTO.createTime=Added Time
com.wunding.learn.file.api.dto.export.train.LearningTargetManageOrgExportDTO.createBy=Added By
com.wunding.learn.file.api.dto.export.train.LearningTargetManageOrgExportDTO.loginName=Account
com.wunding.learn.file.api.dto.export.train.LearningTargetManageOrgExportDTO.creatorOrg=Department

com.wunding.learn.file.api.dto.export.train.LearningTargetManageIdentityExportDTO.targetUserTypeName=Identity
com.wunding.learn.file.api.dto.export.train.LearningTargetManageIdentityExportDTO.startTime=Start Date
com.wunding.learn.file.api.dto.export.train.LearningTargetManageIdentityExportDTO.endTime=End Date
com.wunding.learn.file.api.dto.export.train.LearningTargetManageIdentityExportDTO.targetValueType=Target Value Type
com.wunding.learn.file.api.dto.export.train.LearningTargetManageIdentityExportDTO.targetValue=Target Value
com.wunding.learn.file.api.dto.export.train.LearningTargetManageIdentityExportDTO.orgName=Belonging Department
com.wunding.learn.file.api.dto.export.train.LearningTargetManageIdentityExportDTO.isPublish=Publish Status
com.wunding.learn.file.api.dto.export.train.LearningTargetManageIdentityExportDTO.activeType=Activity Type
com.wunding.learn.file.api.dto.export.train.LearningTargetManageIdentityExportDTO.createTime=Added Time
com.wunding.learn.file.api.dto.export.train.LearningTargetManageIdentityExportDTO.createBy=Added By
com.wunding.learn.file.api.dto.export.train.LearningTargetManageIdentityExportDTO.loginName=Account
com.wunding.learn.file.api.dto.export.train.LearningTargetManageIdentityExportDTO.creatorOrg=Department

com.wunding.learn.file.api.dto.export.train.LearningTargetManageUserProcessExportDTO.fullName=Full Name
com.wunding.learn.file.api.dto.export.train.LearningTargetManageUserProcessExportDTO.loginName=Account
com.wunding.learn.file.api.dto.export.train.LearningTargetManageUserProcessExportDTO.orgName=Department
com.wunding.learn.file.api.dto.export.train.LearningTargetManageUserProcessExportDTO.finishNum=Completed Number
com.wunding.learn.file.api.dto.export.train.LearningTargetManageUserProcessExportDTO.unFinishNum=Unfinished Number
com.wunding.learn.file.api.dto.export.train.LearningTargetManageUserProcessExportDTO.processStr=Progress

com.wunding.learn.file.api.dto.export.train.LearningTargetCompletionDetailsOrgExportDTO.orgName=Organization Department
com.wunding.learn.file.api.dto.export.train.LearningTargetCompletionDetailsOrgExportDTO.targetUserTypeName=Department
com.wunding.learn.file.api.dto.export.train.LearningTargetCompletionDetailsOrgExportDTO.startTime=Start Date
com.wunding.learn.file.api.dto.export.train.LearningTargetCompletionDetailsOrgExportDTO.endTime=End Date
com.wunding.learn.file.api.dto.export.train.LearningTargetCompletionDetailsOrgExportDTO.activeType=Activity Type
com.wunding.learn.file.api.dto.export.train.LearningTargetCompletionDetailsOrgExportDTO.targetValueType=Target Value Type
com.wunding.learn.file.api.dto.export.train.LearningTargetCompletionDetailsOrgExportDTO.targetValue=Target Value
com.wunding.learn.file.api.dto.export.train.LearningTargetCompletionDetailsOrgExportDTO.targetUserNum=Involved Users
com.wunding.learn.file.api.dto.export.train.LearningTargetCompletionDetailsOrgExportDTO.finishUserNum=Completed Users
com.wunding.learn.file.api.dto.export.train.LearningTargetCompletionDetailsOrgExportDTO.unFinishUserNum=Unfinished Users
com.wunding.learn.file.api.dto.export.train.LearningTargetCompletionDetailsOrgExportDTO.finishingRate=Completion Rate

com.wunding.learn.file.api.dto.export.train.LearningTargetCompletionDetailsIdentityExportDTO.orgName=Organization Department
com.wunding.learn.file.api.dto.export.train.LearningTargetCompletionDetailsIdentityExportDTO.targetUserTypeName=Identity
com.wunding.learn.file.api.dto.export.train.LearningTargetCompletionDetailsIdentityExportDTO.startTime=Start Date
com.wunding.learn.file.api.dto.export.train.LearningTargetCompletionDetailsIdentityExportDTO.endTime=End Date
com.wunding.learn.file.api.dto.export.train.LearningTargetCompletionDetailsIdentityExportDTO.activeType=Activity Type
com.wunding.learn.file.api.dto.export.train.LearningTargetCompletionDetailsIdentityExportDTO.targetValueType=Target Value Type
com.wunding.learn.file.api.dto.export.train.LearningTargetCompletionDetailsIdentityExportDTO.targetValue=Target Value
com.wunding.learn.file.api.dto.export.train.LearningTargetCompletionDetailsIdentityExportDTO.targetUserNum=Involved Users
com.wunding.learn.file.api.dto.export.train.LearningTargetCompletionDetailsIdentityExportDTO.finishUserNum=Completed Users
com.wunding.learn.file.api.dto.export.train.LearningTargetCompletionDetailsIdentityExportDTO.unFinishUserNum=Unfinished Users
com.wunding.learn.file.api.dto.export.train.LearningTargetCompletionDetailsIdentityExportDTO.finishingRate=Completion Rate

com.wunding.learn.file.api.dto.export.train.LearningTargetRefUserListExportDTO.fullName=Full Name
com.wunding.learn.file.api.dto.export.train.LearningTargetRefUserListExportDTO.loginName=Account
com.wunding.learn.file.api.dto.export.train.LearningTargetRefUserListExportDTO.orgName=Department
com.wunding.learn.file.api.dto.export.train.LearningTargetRefUserListExportDTO.finishNum=Completed Number
com.wunding.learn.file.api.dto.export.train.LearningTargetRefUserListExportDTO.unFinishNum=Unfinished Number
com.wunding.learn.file.api.dto.export.train.LearningTargetRefUserListExportDTO.finishingRate=Completion Rate

com.wunding.learn.file.api.dto.export.train.TargetUserAchievementDetailExportDTO.fullName=Full Name
com.wunding.learn.file.api.dto.export.train.TargetUserAchievementDetailExportDTO.loginName=Account
com.wunding.learn.file.api.dto.export.train.TargetUserAchievementDetailExportDTO.orgName=Department
com.wunding.learn.file.api.dto.export.train.TargetUserAchievementDetailExportDTO.targetUserTypeName=Department
com.wunding.learn.file.api.dto.export.train.TargetUserAchievementDetailExportDTO.activeType=Activity Type
com.wunding.learn.file.api.dto.export.train.TargetUserAchievementDetailExportDTO.startTime=Start Date
com.wunding.learn.file.api.dto.export.train.TargetUserAchievementDetailExportDTO.endTime=End Date
com.wunding.learn.file.api.dto.export.train.TargetUserAchievementDetailExportDTO.targetValueType=Target Value Type
com.wunding.learn.file.api.dto.export.train.TargetUserAchievementDetailExportDTO.targetValue=Target Value
com.wunding.learn.file.api.dto.export.train.TargetUserAchievementDetailExportDTO.belongOrgName=Belonging Department
com.wunding.learn.file.api.dto.export.train.TargetUserAchievementDetailExportDTO.finishNum=Completed Value
com.wunding.learn.file.api.dto.export.train.TargetUserAchievementDetailExportDTO.finishingRate=Completion Rate

com.wunding.learn.file.api.dto.export.excitation.UserGottenTargetValueDetailExportDTO.activeContent=Study Type
com.wunding.learn.file.api.dto.export.excitation.UserGottenTargetValueDetailExportDTO.activeName=Study Content
com.wunding.learn.file.api.dto.export.excitation.UserGottenTargetValueDetailExportDTO.obtainNum=Obtained Value
com.wunding.learn.file.api.dto.export.excitation.UserGottenTargetValueDetailExportDTO.obtainTime=Obtained Time

com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticExportDTO.projectName=Class & Project Name
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticExportDTO.proNo=Project No
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticExportDTO.startTime=Start Time
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticExportDTO.endTime=End Time
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticExportDTO.orgPath=Department
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticExportDTO.leaderFullName=Leader
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticExportDTO.trainDays=Train Day
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticExportDTO.peopleNum=Student Num
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticExportDTO.lecturerNum=Lecturer Num
com.wunding.learn.file.api.dto.export.project.FaceProjectStatisticExportDTO.projectScore=Project Score

com.wunding.learn.file.api.dto.export.train.TrainProjectStatisticExportDTO.projectName=Class & Project Name
com.wunding.learn.file.api.dto.export.train.TrainProjectStatisticExportDTO.proNo=Project No
com.wunding.learn.file.api.dto.export.train.TrainProjectStatisticExportDTO.startTime=Start Time
com.wunding.learn.file.api.dto.export.train.TrainProjectStatisticExportDTO.endTime=End Time
com.wunding.learn.file.api.dto.export.train.TrainProjectStatisticExportDTO.orgPath=Department
com.wunding.learn.file.api.dto.export.train.TrainProjectStatisticExportDTO.leaderFullName=Leader
com.wunding.learn.file.api.dto.export.train.TrainProjectStatisticExportDTO.trainDays=Train Day
com.wunding.learn.file.api.dto.export.train.TrainProjectStatisticExportDTO.peopleNum=Student Num
com.wunding.learn.file.api.dto.export.train.TrainProjectStatisticExportDTO.lecturerNum=Lecturer Num
com.wunding.learn.file.api.dto.export.train.TrainProjectStatisticExportDTO.projectScore=Project Score
com.wunding.learn.file.api.dto.export.train.TrainProjectStatisticExportDTO.trainName=Train Name

com.wunding.learn.file.api.dto.export.project.ProjectStatisticUserExportDTO.isImport=Is Import
com.wunding.learn.file.api.dto.export.project.ProjectStatisticUserExportDTO.userName=User Name
com.wunding.learn.file.api.dto.export.project.ProjectStatisticUserExportDTO.loginName=Login Name
com.wunding.learn.file.api.dto.export.project.ProjectStatisticUserExportDTO.orgName=Department
com.wunding.learn.file.api.dto.export.project.ProjectStatisticUserExportDTO.postName=Post Name
com.wunding.learn.file.api.dto.export.project.ProjectStatisticUserExportDTO.progressPercentStr=Progress
com.wunding.learn.file.api.dto.export.project.ProjectStatisticUserExportDTO.statusStr=Status

com.wunding.learn.file.api.dto.export.project.LecturerProjectStatisticExportDTO.isImport=Is Import
com.wunding.learn.file.api.dto.export.project.LecturerProjectStatisticExportDTO.fullName=Lecturer Name
com.wunding.learn.file.api.dto.export.project.LecturerProjectStatisticExportDTO.lecturerCode=Lecturer Code
com.wunding.learn.file.api.dto.export.project.LecturerProjectStatisticExportDTO.categoryName=Category Name
com.wunding.learn.file.api.dto.export.project.LecturerProjectStatisticExportDTO.bookkeepingLecturerLevel=LecturerLevel
com.wunding.learn.file.api.dto.export.project.LecturerProjectStatisticExportDTO.startTime=Teaching time
com.wunding.learn.file.api.dto.export.project.LecturerProjectStatisticExportDTO.instructionTime=Instruction Time
com.wunding.learn.file.api.dto.export.project.LecturerProjectStatisticExportDTO.courseName=Teaching content
com.wunding.learn.file.api.dto.export.project.LecturerProjectStatisticExportDTO.score=Score
com.wunding.learn.file.api.dto.export.LibraryOperationRecordExportDTO.serialNumber=serial number
com.wunding.learn.file.api.dto.export.LibraryOperationRecordExportDTO.operateTime=operating time
com.wunding.learn.file.api.dto.export.LibraryOperationRecordExportDTO.fullName=operator
com.wunding.learn.file.api.dto.export.LibraryOperationRecordExportDTO.roleName=role
com.wunding.learn.file.api.dto.export.LibraryOperationRecordExportDTO.operateDetail=operate detail

# MyApplyCourseAuditDTO
com.wunding.learn.file.api.dto.export.course.MyApplyCourseAuditDTO.processCode=Apply number
com.wunding.learn.file.api.dto.export.course.MyApplyCourseAuditDTO.auditStatus=Apply status
com.wunding.learn.file.api.dto.export.course.MyApplyCourseAuditDTO.applyType=Apply type
com.wunding.learn.file.api.dto.export.course.MyApplyCourseAuditDTO.courseName=Course name
com.wunding.learn.file.api.dto.export.course.MyApplyCourseAuditDTO.courseNo=Course number
com.wunding.learn.file.api.dto.export.course.MyApplyCourseAuditDTO.coursewareNum=Number of courseware
com.wunding.learn.file.api.dto.export.course.MyApplyCourseAuditDTO.categoryName=Course category
com.wunding.learn.file.api.dto.export.course.MyApplyCourseAuditDTO.authorName=Author

# AllCourseAuditDTO
com.wunding.learn.file.api.dto.export.course.AllCourseAuditDTO.processCode=Apply number
com.wunding.learn.file.api.dto.export.course.AllCourseAuditDTO.applyUserName=Applicant
com.wunding.learn.file.api.dto.export.course.AllCourseAuditDTO.applyUserLoginName=Applicant's account
com.wunding.learn.file.api.dto.export.course.AllCourseAuditDTO.applyTime=Application date
com.wunding.learn.file.api.dto.export.course.AllCourseAuditDTO.auditStatus=Apply status
com.wunding.learn.file.api.dto.export.course.AllCourseAuditDTO.applyType=Apply type
com.wunding.learn.file.api.dto.export.course.AllCourseAuditDTO.courseName=Course name
com.wunding.learn.file.api.dto.export.course.AllCourseAuditDTO.courseNo=Course number
com.wunding.learn.file.api.dto.export.course.AllCourseAuditDTO.coursewareNum=Number of courseware
com.wunding.learn.file.api.dto.export.course.AllCourseAuditDTO.categoryName=Course category
com.wunding.learn.file.api.dto.export.course.AllCourseAuditDTO.authorName=Author

# SysTagListExportDTO
com.wunding.learn.file.api.dto.export.user.SysTagListExportDTO.tagClassifyName=Tag classification
com.wunding.learn.file.api.dto.export.user.SysTagListExportDTO.tagName=Tag name
com.wunding.learn.file.api.dto.export.user.SysTagListExportDTO.isRecommend=Recommend
com.wunding.learn.file.api.dto.export.user.SysTagListExportDTO.isOptional=Optional
com.wunding.learn.file.api.dto.export.user.SysTagListExportDTO.isShow=Show
com.wunding.learn.file.api.dto.export.user.SysTagListExportDTO.clientDisplay=Filter
com.wunding.learn.file.api.dto.export.user.SysTagListExportDTO.defaultType=Default
com.wunding.learn.file.api.dto.export.user.SysTagListExportDTO.sortNo=Order
com.wunding.learn.file.api.dto.export.user.SysTagListExportDTO.isAvailable=Available

# SysTemTagStatAnalysisExportDTO
com.wunding.learn.file.api.dto.export.analysis.SysTemTagStatAnalysisExportDTO.tagClassifyName=Label classification
com.wunding.learn.file.api.dto.export.analysis.SysTemTagStatAnalysisExportDTO.tagName=Label name
com.wunding.learn.file.api.dto.export.analysis.SysTemTagStatAnalysisExportDTO.relateCourseNum=Number of associated courses
com.wunding.learn.file.api.dto.export.analysis.SysTemTagStatAnalysisExportDTO.relateSpecialNum=Number of associated special
com.wunding.learn.file.api.dto.export.analysis.SysTemTagStatAnalysisExportDTO.relateKnowledgeNum=Number of associated knowledge
com.wunding.learn.file.api.dto.export.analysis.SysTemTagStatAnalysisExportDTO.isAvailable=Available
com.wunding.learn.file.api.dto.export.analysis.SysTemTagStatAnalysisExportDTO.tagCollectNum=Number of collectors
com.wunding.learn.file.api.dto.export.analysis.SysTemTagStatAnalysisExportDTO.tagHoldNum=Number of Holders

#ExamAnswerUserExportDTO
com.wunding.learn.file.api.dto.export.exam.ExamAnswerUserExportDTO.userName=user name
com.wunding.learn.file.api.dto.export.exam.ExamAnswerUserExportDTO.loginName=user account
com.wunding.learn.file.api.dto.export.exam.ExamAnswerUserExportDTO.orgName=organization name
com.wunding.learn.file.api.dto.export.exam.ExamAnswerUserExportDTO.postName=position
com.wunding.learn.file.api.dto.export.exam.ExamAnswerUserExportDTO.userAnswer=user answer

#LecturerCourseAuthByLecturerCourseExportDTO
com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthByLecturerCourseExportDTO.courseCode=Course code
com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthByLecturerCourseExportDTO.courseName=Course name
com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthByLecturerCourseExportDTO.authenticationTime=Certification date
com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthByLecturerCourseExportDTO.score=Certification score
com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthByLecturerCourseExportDTO.authenticationOrg=Certification organization
com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthByLecturerCourseExportDTO.lectureNum=Number of lectures
com.wunding.learn.file.api.dto.export.lecturer.LecturerCourseAuthByLecturerCourseExportDTO.evalScore=Evaluation points

# MyApplyResourceAuditDTO field internationalization
com.wunding.learn.file.api.dto.export.flow.MyApplyResourceAuditDTO.processCode=Application Number
com.wunding.learn.file.api.dto.export.flow.MyApplyResourceAuditDTO.auditStatusName=Audit Status
com.wunding.learn.file.api.dto.export.flow.MyApplyResourceAuditDTO.resourceTypeName=Resource Type
com.wunding.learn.file.api.dto.export.flow.MyApplyResourceAuditDTO.applyTypeName=Application Type
com.wunding.learn.file.api.dto.export.flow.MyApplyResourceAuditDTO.resourceName=Resource Name
com.wunding.learn.file.api.dto.export.flow.MyApplyResourceAuditDTO.resourceCode=Resource Code
com.wunding.learn.file.api.dto.export.flow.MyApplyResourceAuditDTO.registerTime=Application Time
com.wunding.learn.file.api.dto.export.flow.MyApplyResourceAuditDTO.summary=Summary

# AllResourceAuditDTO field internationalization
com.wunding.learn.file.api.dto.export.flow.AllResourceAuditDTO.processCode=Application Number
com.wunding.learn.file.api.dto.export.flow.AllResourceAuditDTO.applicantUserName=Applicant
com.wunding.learn.file.api.dto.export.flow.AllResourceAuditDTO.applicantUserLoginName=Account
com.wunding.learn.file.api.dto.export.flow.AllResourceAuditDTO.auditStatusName=Audit Status
com.wunding.learn.file.api.dto.export.flow.AllResourceAuditDTO.resourceTypeName=Resource Type
com.wunding.learn.file.api.dto.export.flow.AllResourceAuditDTO.applyTypeName=Application Type
com.wunding.learn.file.api.dto.export.flow.AllResourceAuditDTO.resourceName=Resource Name
com.wunding.learn.file.api.dto.export.flow.AllResourceAuditDTO.resourceCode=Resource Code
com.wunding.learn.file.api.dto.export.flow.AllResourceAuditDTO.registerTime=Application Time
com.wunding.learn.file.api.dto.export.flow.AllResourceAuditDTO.summary=Summary
