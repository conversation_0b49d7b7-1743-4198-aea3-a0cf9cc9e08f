dept=部门
post=岗位
username=姓名
account=账号
question=题目
questionType=题目类型
referenceAnswer=参考答案
examName=考试名称
questionCount=题目数
totalScore=总分
userOrgName=所属部门
code=编号
status=状态
publishBy=发布人
type=类型
uploadTime=上传时间
publishTime=发布时间
createTime=创建时间
addTime=添加时间
enabled=是否启用
categoryType=分类类别
categoryName=分类名称
superiorCategory=上级分类
displayOrder=显示顺序
upperLevel=上一级
courseName=课程名称
source=来源
来源=来源
阶段=阶段
活动类型=活动类型
选必修=选必修
活动名称=活动名称
活动资源编号=活动资源编号
必选条目=必选条目
锁定条目=锁定条目
添加日期=添加时间
添加人=添加人
账号=账号
部门=部门
必修=必修
选修=选修
是=是
否=否
有=有
无=无
能力=能力
自建=自建
startTime=开始时间
completeTime=完成时间
labelName=标签名称
coursewareName=课件名称
userScore=成绩
editTime=编辑时间
editor=编辑者
order=排序
isPublish=发布状态
sharedLibrary.courseCateTypeName=课程分类
sharedLibrary.cwCount=课件数量
examAnswerOfStatistical.passRate=及格率
examAnswerOfStatistical.avgScore=平均成绩
examAnswerRecordDetailList.userAnswer=作答情况
examAnswerRecordDetailList.score=题目得分
examAnswerRecordDetailList.examNo=考试编号
com.wunding.learn.file.api.dto.export.exam.ExamAnswerRecordDetailListExportDTO.examSortNo=考试次序
com.wunding.learn.file.api.dto.export.exam.ExamAnswerRecordDetailListExportDTO.userScore=考试成绩
com.wunding.learn.file.api.dto.export.exam.ExamAnswerRecordDetailListExportDTO.answerTime=交卷时间
examAnswerRecordList.answerTime=交卷时间
examAnswerRecordList.postCount=交卷次数
examAnswerRecordList.answerCount=剩余考试次数
examAnswerRecordList.firstScore=首次成绩
examAnswerRecordList.finalScore=最终成绩
examAnswerRecordList.isPass=通过
examAnswerRecordList.isRetake=是否重考
examAnswerRecordList.examStatus=考试状态
examCorrectExamList.passScore=及格分
examCorrectExamList.submitCount=已交卷
examCorrectExamList.reviewedCount=已改卷
examCorrectExamList.unReviewedCount=未改卷
examCorrectRecord.loginName=用户账号
examCorrectRecord.submitTime=交卷时间
examCorrectRecord.checkFinished=是否已改卷
examCorrectRecord.userScore=考试得分
examEmployeeResultsDetail.examStatus=考试状态
examEmployeeResultsDetail.postCount=交卷次数
examEmployeeResultsDetail.examScore=考试总分
examEmployeeResultsDetail.score=实际得分
examList.examNo=考试编号
examList.passScore=及格分
examList.examTimeCount=考试时长
examList.checkPaperMethod=阅卷方式
examList.sourceType=题目来源
examQuestion.difficulty=难易度
examQuestion.answer=标准答案
examQuestion.mark=题目分值
examQuestion.questionDesc=试题解析
examQuestion.pointDesc=得分点说明
examQuestion.option=选项
examQuestion.end=最多设置8个选项
exercise.exerciseName=练习名称
schema.schemaName=方案名称
schema.schemaDescription=备注
courseComment.fullName=评论人
courseComment.loginName=评论人账号
courseComment.createTime=评论时间
courseComment.content=评论内容
course.courseNo=课程编号
course.courseCateName=课程分类
course.courseWareNum=课件数
course.authorName=作者
course.keywordStr=关键词
course.viewCount=学习人数
course.orgName=创建组织名称
course.isShare=是否共享
course.publishName=发布人名称
courseLearn.jobName=所属岗位
courseLearn.courseNo=课程编码
courseLearn.orgName=课程管理单位
courseLearn.courseWareCount=课件总数
courseLearn.learnState=学习进度
courseLearn.remainderCourseWareCount=剩余课件数
courseStudyDetail.lastLearnTime=最近学习时间
courseStudyDetail.duration=累计时长
courseStudyDetail.learnState=学习状态
courseStudyDetail.remainderCourseWareCount=剩余课件数
courseTagManage.tagClassifyName=标签分类
courseTagManage.levelPathName=全路径名称
courseTagManage.isOptional=用户自选
courseTagManage.isShow=前端展示
courseTagManage.defaultType=默认
courseTagSta.tagClassifyName=标签分类
courseTagSta.levelPathName=全路径名称
courseTagSta.tagCollectNum=收藏人数
courseTagSta.tagHoldNum=标签持有人数
courseWareLearn.jobName=所属岗位
courseWareLearn.courseNo=课程编码
courseWareLearn.courseCategoryName=课程分类
courseWareLearn.orgName=课程管理单位
courseWareLearn.playTime=课件时长（秒）
courseWareLearn.startTime=开始学习时间
courseWareLearn.endTime=结束学习时间
courseWareLearn.duration=学习时长（秒）
courseWareLearn.learnState=学习进度
courseWareLearn.userScore=考试成绩
coursewarePackage.transFormStatus=转换状态
coursewarePackage.createBy=上传者
coursewareStudyDetail2.lastLearnTime=最近学习
coursewareStudyDetail2.duration=累计时长
coursewareStudyDetail2.learnState=学习状态
coursewareStudyDetail.lastLearnTime=最近学习
coursewareStudyDetail.duration=累计时长
coursewareStudyDetail.learnState=学习状态
coursewareStudyDetail.passState=通过状态
mergeCourse.createBy=上传人
courseWareLib.cwType=课件类型
courseWareLib.transformStatus=转换状态
courseWareLib.createTime=入库时间
courseWareLib.createByName=入库者
courseWareLib.libraryName=课件库分类
evaluationLib.evalName=评估名称
evaluationLib.createUserName=添加者
evaluationLib.libCategoryName=评估库分类名称
examLib.categoryName=考题库分类
examLib.libraryName=题组名称
examLib.questionCount=总计数量
examLib.radioNum=单选题
examLib.multipleChoiceNum=多选题
examLib.judgeNum=判断题
examLib.clozeNum=填空题
examLib.answerNum=问答题
examLib.highLevelNum=难度高
examLib.mediumLevelNum=难度中
examLib.lowLevelNum=难度低
examLib.compulsoryNum=必选题
examLib.questionCompulsory=必考题
examLib.createTime=创建日期
examLib.createByName=创建者
examLib.updateTime=最后更新日期
examLib.updateByName=最后更新者
exerciseLib.libraryName=题组名称
exerciseLib.used=是否引用
exerciseLib.createTime=入库时间
exerciseLib.createByName=入库者
exerciseLib.categoryName=练习库分类
libQuestion.questionName=题目名称
materialLib.name=材料名称
materialLib.type=材料类型
materialLib.transformStatus=转换状态
materialLib.knowledgeType=知识库分类
surveyLib.createBy=添加者
surveyLib.categoryName=调研库分类
tagCategory.parentName=上级标签
testPaperLib.categoryName=试卷库分类
testPaperLib.testPaperName=试卷名称
testPaperLib.questionCount=总计数量
testPaperLib.radioNum=单选题
testPaperLib.multipleChoiceNum=多选题
testPaperLib.judgeNum=判断题
testPaperLib.clozeNum=填空题
testPaperLib.answerNum=问答题
testPaperLib.highLevelNum=难度高
testPaperLib.mediumLevelNum=难度中
testPaperLib.lowLevelNum=难度低
testPaperLib.compulsoryNum=必选题
testPaperLib.questionCompulsory=必考题
testPaperLib.createTime=创建日期
testPaperLib.createBy=创建者
testPaperLib.updateTime=最后更新日期
testPaperLib.updateBy=最后更新者
testPaperQuestion.questionName=题目名称
testPaperQuestion.mark=题目分值
testPaperQuestion.tag=标签
setAs=设为
selfAssessMethod=自评
otherAssessMethod=他评
其他=其他
全部分类=全部分类
审核通过=审核通过
审核不通过=审核不通过
审核人=审核人
在途=在途
出库=出库
保存材料=保存材料
提交报名=提交报名
一般=一般
满意=满意
非常满意=非常满意
活动数=活动数
培训人次=培训人次
培训人数=培训人数
评估分=评估分
课程任务=课程任务

课程=课程
资讯=资讯
学习项目=学习项目
专题=专题
讲师=讲师
直播=直播
话题=话题
案例库=案例库
培训项目=培训项目
组织信息=组织信息
签到=签到
我的任务=我的任务
考试竞赛=考试竞赛
中部菜单=中部菜单
宣传单=宣传单
我的卡片=我的卡片
头条广告=头条广告
积分排名=积分排名
闯关=闯关
数据正在导入，请稍后查看=数据正在导入，请稍后查看
项目编码对应的项目不存在=项目编码对应的项目不存在
项目编码对应的项目与项目名称不匹配=项目编码对应的项目与项目名称不匹配
进入项目日期需要大于项目开始日期=进入项目日期需要大于项目开始日期
完成项目日期需要小于等于项目结束日期=完成项目日期需要小于等于项目结束日期
用户账号对应的用户不存在=用户账号对应的用户不存在
用户账号对应的用户与用户名称不匹配=用户账号对应的用户与用户名称不匹配
第=第
行=行
授课开始时间需要大于项目开始日期=授课开始时间需要大于项目开始日期
授课结束时间需要小于等于项目结束日期=授课结束时间需要小于等于项目结束日期
讲师编号对应的讲师不存在=讲师编号对应的讲师不存在
讲师编号对应的讲师与讲师姓名不匹配=讲师编号对应的讲师与讲师姓名不匹配
内部讲师对应的用户账号不能为空！=内部讲师对应的用户账号不能为空！
用户账号对应的用户不存在！=用户账号对应的用户不存在！
讲师对应的用户不一致！=讲师对应的用户不一致！

猜你喜欢=猜你喜欢
推荐课程=推荐课程
热门课程1=热门课程1
热门课程2=热门课程2
岗位课程=岗位课程
我正在学=我正在学
我参加的专题=我参加的专题
讲师风采1=讲师风采1
讲师风采2=讲师风采2
讲师风采3=讲师风采3
直播中=直播中
当前直播=当前直播
直播回看=直播回看
最新话题=最新话题
热门话题1=热门话题1
热门话题2=热门话题2
置顶话题1=置顶话题1
本月热门案例1=本月热门案例1
本月热门案例2=本月热门案例2
本月热门案例3=本月热门案例3
热门案例1=热门案例1
热门案例2=热门案例2
热门案例3=热门案例3
案例质量等级1=案例质量等级1
案例质量等级2=案例质量等级2
案例质量等级3=案例质量等级3
指定类别=指定类别
每日点击签到打卡=每日点击签到打卡
每日课程学习10分钟=每日课程学习10分钟
非长期任务=非长期任务
全部任务=全部任务
公司排名=公司排名
组织排名=组织排名
单图=单图
基本条件=基本条件
关键能力=关键能力
知识内容=知识内容
关键任务=关键任务
组织回馈=组织回馈
知识学习=知识学习
培训=培训
授课=授课
评价=评价
实操=实操
AI识别=AI识别
共计=共计
个知识=个知识
个课件=个课件
个题目=个题目
个考题=个考题
个试卷=个试卷
个练习=个练习
个调研=个调研
个评估=个评估
个部门=个部门
考试=考试
的使用范围，修改后共计=的使用范围，修改后共计
试卷创建了=试卷创建了
考题创建了=考题创建了
操作记录=操作记录
学分=学分
金币=金币
积分=积分
学时=学时
新建申请=新建申请
修改申请=修改申请
不涉及=不涉及
驳回=驳回
审批不通过=审批不通过
审批通过=审批通过
审批中=审批中
草稿=草稿
练习=练习
案例=案例
知识=知识
题库题目=题库题目
试卷库=试卷库
面授培训=面授培训
快速培训=快速培训
调研=调研
投票=投票
PK赛=PK赛
招募=招募
共读=共读
证书=证书
学习地图=学习地图
任职资格=任职资格
人才测评=人才测评
未分类=未分类

首次激活打卡=首次激活打卡
每日连续登录打卡（打卡次数）=每日连续登录打卡（打卡次数）
学完课件（按课件时长）=学完课件（按课件时长）
分享直播=分享直播
学完课件（固定）=学完课件（固定）
提交案例=提交案例
案例被标识优质=案例被标识优质
回帖=回帖
发表话题=发表话题
最佳话题回帖=最佳话题回帖

分享课程=分享课程
闯关关卡完成=闯关关卡完成
共读心得提交次数=共读心得提交次数
共读评论心得数量=共读评论心得数量
共读打卡次数=共读打卡次数
共读阅读图书数量=共读阅读图书数量
参与获取（双人）=参与获取（双人）
个人获胜利（单人）=个人获胜利（单人）
个人获胜利（双人）=个人获胜利（双人）
个人获季军（多人）=个人获季军（多人）
个人获亚军（多人）=个人获亚军（多人）
个人获冠军（多人）=个人获冠军（多人）
参与获取（单人）=参与获取（单人）
参与获取（多人）=参与获取（多人）
参与获取（组队）=参与获取（组队）
团队获胜（组队）=团队获胜（组队）
点击课件学习=点击课件学习
点击课程学习=点击课程学习
课件评星=课件评星
点赞资讯评论=点赞资讯评论
点击观看直播=点击观看直播
点击查看资讯=点击查看资讯
点赞课程评论=点赞课程评论
学员点赞讲师=学员点赞讲师
收藏课程=收藏课程
回复课件评论=回复课件评论
回复课程评论=回复课程评论
学完课程=学完课程
点赞课程=点赞课程
课程评星=课程评星
评论资讯=评论资讯
评论课件=评论课件
评论课程=评论课程
提交考试=提交考试
考试成绩区间激励=考试成绩区间激励
完成练习=完成练习
提交调研=提交调研
提交作业=提交作业
完成项目=完成项目
完成项目报名=完成项目报名
任务完成率=任务完成率
分享项目=分享项目
提交评估=提交评估
关注话题=关注话题
参与授课项目=参与授课项目
完成签到=完成签到
反馈意见受理=反馈意见受理
提交反馈意见=提交反馈意见
参与审核案例=参与审核案例
案例评审通过后，评审得分激励=案例评审通过后，评审得分激励
案例被设置为公司级=案例被设置为公司级
案例被设置为集团级=案例被设置为集团级
待晋级=待晋级
待降级=待降级
待出库=待出库
手工调整=手工调整
已晋级=已晋级
已降级=已降级
已出库=已出库
入库=入库
再入库=再入库
晋级失败=晋级失败
降级失败=降级失败
出库失败=出库失败
课件=课件
评估=评估
商学院=商学院
话题版块=话题版块
反馈=反馈
兑换=兑换
抽奖=抽奖
系统=系统
积分清零=积分清零
关卡-进行中=关卡-进行中
关卡-已完成=关卡-已完成
关卡-未解锁=关卡-未解锁
移动版底部地图=移动版底部地图
移动版地图=移动版地图
移动版地图-无数据时=移动版地图-无数据时
PC版底部地图=PC版底部地图
PC版地图=PC版地图
PC版地图-无数据时=PC版地图-无数据时
进行中的关卡样式图=进行中的关卡样式图
已完成的关卡样式图=已完成的关卡样式图
未解锁的关卡样式图=未解锁的关卡样式图
移动版闯关底部样式图=移动版闯关底部样式图
移动版闯关整体地图（如关卡较多时会重复此部分循环使用）=移动版闯关整体地图（如关卡较多时会重复此部分循环使用）
移动版闯关无关卡数据时默认样式=移动版闯关无关卡数据时默认样式
PC版闯关底部样式图=PC版闯关底部样式图
PC版闯关整体地图（如关卡较多时会重复此部分循环使用）=PC版闯关整体地图（如关卡较多时会重复此部分循环使用）
已完成=已完成
未完成=未完成
未参加=未参加
全流程行动学习=全流程行动学习
标准化行动学习=标准化行动学习
被动微行动学习（测评）=被动微行动学习（测评）
被动微行动学习（无测评）=被动微行动学习（无测评）
无领导行动学习（测评）=无领导行动学习（测评）
无领导行动学习（无测评）=无领导行动学习（无测评）
被动无领导行动学习（测评）=被动无领导行动学习（测评）
主动无领导行动学习（测评）=主动无领导行动学习（测评）
主动无领导行动学习（无测评）=主动无领导行动学习（无测评）
自评通知=自评通知
他评通知=他评通知
发布通知=发布通知
催办通知=催办通知
协办待审通知=协办待审通知
审核结果通知=审核结果通知
发布报名通知=发布报名通知
评价结果通知=评价结果通知
开班信息通知=开班信息通知
审核未通过不可修改审核结果通知=审核未通过不可修改审核结果通知
审核未通过可修改结果通知=审核未通过可修改结果通知
审核通过结果通知=审核通过结果通知
答辩结果通知=答辩结果通知
开课信息通知=开课信息通知
自评=自评
上级=上级
平级=平级
下级=下级
未开始=未开始
单选题=单选题
多选题=多选题
判断题=判断题
量表题=量表题
问答题=问答题
场景说明=场景说明
能力模型=能力模型
胜任力地图=胜任力地图
题目平均分=题目平均分
题目合计分=题目合计分
题目最低分=题目最低分
题目最高分=题目最高分
选项结果对应分值=选项结果对应分值
选项结果总分分值=选项结果总分分值
选项结果平均分值=选项结果平均分值
选项结果中最低分值=选项结果中最低分值
选项结果中最高分值=选项结果中最高分值
满意程度=满意程度
符合程度=符合程度
非常不满意=非常不满意
不满意=不满意
比较满意=比较满意
很不符合=很不符合
不太符合=不太符合
比较符合=比较符合
非常符合=非常符合
竖排有标题=竖排有标题
竖排无标题=竖排无标题
横排滑动有标题=横排滑动有标题
横排滑动无标题=横排滑动无标题
竖排有详情=竖排有详情
竖排无详情=竖排无详情
横排滑动有详情=横排滑动有详情
横排滑动无详情=横排滑动无详情
上下布局=上下布局
单行=单行
矩阵样式=矩阵样式
样式1=样式1
左右滑动=左右滑动
横排大=横排大
横排小=横排小
单个有标题=单个有标题
滑动有标题1=滑动有标题1
滑动有标题2=滑动有标题2
竖排=竖排
竖排（类型版）=竖排（类型版）
横排（类型版）=横排（类型版）
竖排无简介=竖排无简介
竖排有简介=竖排有简介
无分类=无分类
横排=横排
竖版（封面在左）=竖版（封面在左）
竖版（封面在右）=竖版（封面在右）
滑动有标题=滑动有标题
PC-1/1-前后布局=PC-1/1-前后布局
PC-1/1-双列=PC-1/1-双列
PC-1/1-多行=PC-1/1-多行
PC-1/1-多行（类型版）=PC-1/1-多行（类型版）
PC-1/1-左右布局（封面在左）=PC-1/1-左右布局（封面在左）
PC-1/1-左右布局=PC-1/1-左右布局
PC-1/1-左右布局（无详情）=PC-1/1-左右布局（无详情）
PC-1/1-左右布局（有详情）=PC-1/1-左右布局（有详情）
PC-1/1-无分类=PC-1/1-无分类
PC-1/1-无标题=PC-1/1-无标题
PC-1/1-单行=PC-1/1-单行
PC-1/1-单列=PC-1/1-单列
PC-1/1-单行（类型版）=PC-1/1-单行（类型版）
PC-1/1-单列（强调前三）=PC-1/1-单列（强调前三）
PC-1/1-左右布局（封面在右）=PC-1/1-左右布局（封面在右）
PC-1/1-样式1=PC-1/1-样式1
PC-1/1-样式2=PC-1/1-样式2
PC-1/1-上下布局（无详情）=PC-1/1-上下布局（无详情）
PC-1/1-上下布局=PC-1/1-上下布局
PC-1/1-上下布局（有详情）=PC-1/1-上下布局（有详情）
PC-1/1-上下布局（有评论）=PC-1/1-上下布局（有评论）
PC-1/1-有分类=PC-1/1-有分类
PC-1/2-四宫格=PC-1/2-四宫格
PC-1/2-多行=PC-1/2-多行
PC-1/2-多行（类型版）=PC-1/2-多行（类型版）
PC-1/2-左右布局（封面在左）=PC-1/2-左右布局（封面在左）
PC-1/2-左右布局=PC-1/2-左右布局
PC-1/2-左右布局（有详情）=PC-1/2-左右布局（有详情）
PC-1/2-单列=PC-1/2-单列
PC-1/2-单列（强调前三）=PC-1/2-单列（强调前三）
PC-1/2-上下布局（封面在右）=PC-1/2-上下布局（封面在右）
PC-1/2-左右布局（封面在右）=PC-1/2-左右布局（封面在右）
PC-1/2-独栏（有按钮）=PC-1/2-独栏（有按钮）
PC-1/2样式1=PC-1/2样式1
PC-1/2-样式1=PC-1/2-样式1
PC-1/2-样式2=PC-1/2-样式2
PC-1/2-上下布局（有按钮）=PC-1/2-上下布局（有按钮）
PC-1/2-上下布局（封面在左）=PC-1/2-上下布局（封面在左）
PC-1/2上下布局=PC-1/2上下布局
PC-1/2-上下布局=PC-1/2-上下布局
PC-1/2-上下左右布局（有按钮）=PC-1/2-上下左右布局（有按钮）
PC-1/4-有详情=PC-1/4-有详情
PC-1/4-无详情=PC-1/4-无详情
PC-1/4-前后布局=PC-1/4-前后布局
PC-头条1/4-多行=PC-头条1/4-多行
PC-1/4-多行=PC-1/4-多行
PC-头条1/4-多行（类型版）=PC-头条1/4-多行（类型版）
PC-1/4-多行（类型版）=PC-1/4-多行（类型版）
PC-头条1/4-封面在左=PC-头条1/4-封面在左
PC-1/4-左右布局（封面在左）=PC-1/4-左右布局（封面在左）
PC-1/4-左右布局=PC-1/4-左右布局
PC-头条1/4-左右布局=PC-头条1/4-左右布局
PC1/4-矩阵样式=PC1/4-矩阵样式
PC-头条1/4-矩阵样式=PC-头条1/4-矩阵样式
PC-头条1/4-默认=PC-头条1/4-默认
PC-头条1/4-无详情=PC-头条1/4-无详情
PC-头条1/4-无封面=PC-头条1/4-无封面
PC-1/4-无封面=PC-1/4-无封面
PC-1/4-无标题=PC-1/4-无标题
PC-头条1/4-单列=PC-头条1/4-单列
PC-1/4-单列=PC-1/4-单列
PC-1/4-单列（强调前三）=PC-1/4-单列（强调前三）
PC-头条1/4-封面在右=PC-头条1/4-封面在右
PC-1/4-左右布局（封面在右）=PC-1/4-左右布局（封面在右）
PC-头条1/4-样式1=PC-头条1/4-样式1
PC-1/4-样式1=PC-1/4-样式1
PC-头条1/4-样式2=PC-头条1/4-样式2
PC-1/4-样式2=PC-1/4-样式2
PC-1/4-上下布局=PC-1/4-上下布局
PC-头条1/4-上下布局=PC-头条1/4-上下布局
PC-1/4-上下布局(有标题)=PC-1/4-上下布局(有标题)
PC-1/4-上下布局（无标题）=PC-1/4-上下布局（无标题）
PC-3/4-前后布局=PC-3/4-前后布局
PC-3/4-双列=PC-3/4-双列
PC-3/4-多行=PC-3/4-多行
PC-3/4-多行（类型版）=PC-3/4-多行（类型版）
PC-3/4-左右布局（封面在左）=PC-3/4-左右布局（封面在左）
PC-3/4-左右布局（无详情）=PC-3/4-左右布局（无详情）
PC-3/4-左右布局=PC-3/4-左右布局
PC-3/4-左右布局（有详情）=PC-3/4-左右布局（有详情）
PC-3/4-无标题=PC-3/4-无标题
PC-3/4-无分类=PC-3/4-无分类
PC-3/4-单列=PC-3/4-单列
PC-3/4-单列（强调前三）=PC-3/4-单列（强调前三）
PC-3/4-左右布局（封面在右）=PC-3/4-左右布局（封面在右）
PC-3/4-样式1=PC-3/4-样式1
PC-3/4-样式2=PC-3/4-样式2
PC-3/4-上下布局=PC-3/4-上下布局
PC-3/4-上下布局标题=PC-3/4-上下布局标题
PC-3/4-上下布局（有详情）=PC-3/4-上下布局（有详情）
PC-3/4-上下布局（无详情）=PC-3/4-上下布局（无详情）
PC-3/4-有分类=PC-3/4-有分类
滑动无标题=滑动无标题
竖排（强调前三）=竖排（强调前三）
样式2=样式2
单个无标题=单个无标题
默认样式=默认样式
AI问答课程=AI问答课程
最新课程（创建时间）=最新课程（创建时间）
最新课程（发布时间）=最新课程（发布时间）

猜你喜欢单独逻辑=猜你喜欢单独逻辑
下发范围标识为精选好课的课程=下发范围标识为精选好课的课程
下发范围-最近一个月内发布的-评论数+评星次数大于{0}的=下发范围-最近一个月内发布的-评论数+评星次数大于{0}的
下发范围-评论数+评星次数-大于{0}的=下发范围-评论数+评星次数-大于{0}的
与自己岗位匹配的课程=与自己岗位匹配的课程
我学习的，未完成的课程=我学习的，未完成的课程
下发范围=下发范围
下发范围包含我未参与或者未完成的学习项目=下发范围包含我未参与或者未完成的学习项目
所有内部讲师=所有内部讲师
下发范围-直播结束时间大于当前时间并且直播开始时间小于当前时间=下发范围-直播结束时间大于当前时间并且直播开始时间小于当前时间
下发范围直播结束时间大于当前时间=下发范围直播结束时间大于当前时间
下发范围直播结束时间小于当前间，且有回看文件的直播=下发范围直播结束时间小于当前间，且有回看文件的直播
版块下发范围=版块下发范围
版块下发范围发表时间在{0}天内回帖数量大于{1}个=版块下发范围发表时间在{0}天内回帖数量大于{1}个
版块下发范围回帖数量大于{0}个=版块下发范围回帖数量大于{0}个
版块下发范围设置为置顶的帖子=版块下发范围设置为置顶的帖子
所有的范围本月发表的，且本月发生了点赞数大于{2}=所有的范围本月发表的，且本月发生了点赞数大于{2}
公司范围内公开本月发表的，且本月发生了点赞数大于{2}=公司范围内公开本月发表的，且本月发生了点赞数大于{2}
集团范围内公开本月发表的，且本月发生了点赞数大于{2}=集团范围内公开本月发表的，且本月发生了点赞数大于{2}
所有的范围点赞数大于2=所有的范围点赞数大于2
公司范围内公开点赞数大于2=公司范围内公开点赞数大于2
集团范围内公开点赞数大于2=集团范围内公开点赞数大于2
标记为案例质量等级1的=标记为案例质量等级1的
标记为案例质量等级2的=标记为案例质量等级2的
标记为案例质量等级3的=标记为案例质量等级3的
下发范围包含我的培训项目=下发范围包含我的培训项目
每日点击签到，完成打卡=每日点击签到，完成打卡
每日学习课程达到10分钟，则视为完成打卡=每日学习课程达到10分钟，则视为完成打卡
所有带有开始和结束日期的学习活动=所有带有开始和结束日期的学习活动
所有学习活动=所有学习活动
微信公众号=微信公众号
企业微信=企业微信
微信小程序=微信小程序
邮箱=邮箱
APP=APP
钉钉=钉钉
短信=短信
学时上报=学时上报
辅导方案=辅导方案
培训周期类型=培训周期类型
喜欢类型=喜欢类型
外部培训=外部培训
测评=测评
推送=推送
DIY首页=DIY首页
面授项目=面授项目
第三方=第三方
登录相关=登录相关
表单模板=表单模板
总体运营统计=总体运营统计
培训计划=培训计划
个人中心=个人中心
海报分享=海报分享
用户=用户
激励=激励
周期学习项目=周期学习项目
快速培训项目=快速培训项目
新闻资讯=新闻资讯
面授班级=面授班级
答辩=答辩
测评项目=测评项目
分=分


培训班=培训班
项目=项目
报名=报名
表单=表单
尚未进行=尚未进行
正在进行=正在进行
已经完成=已经完成

未通过=未通过
通过=通过
请输入题目=请输入题目
选项1=选项1
选项2=选项2

状态=状态
启用=启用
停用=停用