登录验证码=Login verification code
首页-总评论数=Home page-total number of comments
首页-浏览人次=Home page-number of visitors
首页-话题总数=Home page-total number of topics
首页-主题数=Home page-number of topics
首页-投票数=Home page-number of votes
首页-发布学习项目=Home page-Published projects
首页-发布报名数=Home page-Published sign-up
首页-发布签到数=Home page-Published sign-in
首页-发布调研=Home page-Published survey
首页-发布考试=Home page-Published exam
登录短信验证码=Login SMS verification code
首页-发布练习=Home page-Published exercise
是否集团模式=Group mode
话题回帖是否需要审核=Review topic replies
是否播放防挂机=Playback anti-hang-up
防挂机时长（分）=Anti-hang-up duration (mins)
防挂机倒计时长（秒）=Anti-hang-up countdown duration (s)
首次播放是否倍数=Play in multiple for the first time
首次播放是否可拖拽=Drag and drop the first playback?
首页-总用户数=Home page-total number of users
直播供应商=Live broadcast provider
个人中心-收藏=Personal Center-Collection
个人中心-下载=Personal Center-download
个人中心-签到=Personal Center-sign in
个人中心-话题=Personal Center-topic
个人中心-证书=Personal Centre-Certificate
个人中心-课时=Personal center-class hours
个人中心-积分=Personal Center-points
首页-已激活用户=Home page-activated user
个人中心-学时=Personal center-class hours
个人中心-学分=Individual Center-credits
个人中心-金币=Personal Center-Gold Coin
首页-热门课程1-天数=Home page-popular courses 1-days
首页-课程-评论数+评星次数=Home page-course-number of comments + number of star reviews
首页-热门话题1-天数=Home page-hot topics 1-days
首页-首页-话题-回帖数量=Home page-home page-topics-number of replies
首页-未激活用户=Home page-inactive user
猜你喜欢的课程加塞设置=Guess your hobby course plug setting.
首页-发布课程=Home page-published course
是否默认入库=Put into storage by default
首页-总课件数=Home page-total number of courses
首页-浏览人数=Home page-number of visitors
首页-发布资讯=Home page-published news
0-禁用,1-启用=0-disabled, 1-enabled
0-隐藏,1-显示=0-Hidden, 1-Show
0-否,1-是=0-No, 1-Yes
1-展示互动,2-保利威=1-show interaction, 2-Polyway
当收到的评估样本数大于等于【规则参数1】时，当前等级持有的【规则参数2】\ 个月内，所有项目评估平均分高于(含)【规则参数3】分。=When the number of evaluation samples received is greater than or equal to [rule parameter 1], the average evaluation score of all projects is higher than (including) [rule parameter 3] within the [rule parameter 2] months held by the current level.
当收到的评估样本数大于等于【规则参数1】时当前等级持有的【规则参数2】\ 个月内，所有项目评估平均分低于(含)【规则参数3】分=When the number of evaluation samples received is greater than or equal to [rule parameter 1], the average evaluation score of all projects  is lower than (including) [rule parameter 3] within the [rule parameter 2] months held by the current level.
针对成为讲师超过【规则参数1】的讲师，最近【规则参数2】月内，授课时间少于(含)【规则参数3】小时=For lecturers who become lecturers more than [rule parameter 1], the teaching time is less than (including) [rule parameter 3] hours in the last [rule parameter 2] month.
当前等级持有的【规则参数1】\ 个月内，项目内评估最低分\ 高于(含）【规则参数2】分\ （含授课材料及讲师授课情况评估）=Within the period of [rule parameter 1] held by the current level, the minimum score of evaluation in the project is higher than (including) the score of [rule parameter 2] (including teaching materials and lecturer teaching evaluation).
当前等级持有的【规则参数1】\ 个月内，项目内评估最低分\ 低于(含)【规则参数2】分=Within the period of [rule parameter 1] held by the current level, the lowest evaluation score within the project is lower than (including) [rule parameter 2].
针对成为讲师超过【规则参数1】天的讲师，最近【规则参数2】月内，授课时间超过(含)【规则参数3】小时=For lecturers who have been lecturers for more than [rule parameter 1] days, the teaching time has exceeded (including) [rule parameter 3] hours in the last [rule parameter 2] month.
当前等级持有的时间大于【规则参数1】\ 个月=The current level holds for more than [rule parameter 1] months.
中间菜单=Middle menu
底部菜单=Bottom menu
我的学习=My study
我的应用=My application
全局=Overall situation
课程=Course
课件=Courseware
考试=Examination
练习=Exercise
直播=Live
调研=Survey
学习项目=Project
资讯=News
评估=Evaluation
话题版块=Topic section
签到=Sign in
讲师=Lecturer
反馈=Feedback
案例库=Case base
组织信息=Organization
我的任务=My task
头条广告=Notice
积分排名=Rank
考试竞赛=Examination contest
中部菜单=Home
宣传单=Leaflet
我的卡片=My card
学分=Credit
金币=Gold coin
积分=Points
学时=Class hours
反馈意见受理=Acceptance of feedback
管理员审核通过学员上传课件=The administrator approved the courseware uploaded by trainees
最佳话题回帖=Best topic reply
收藏课程=Collect course
评论课程=Review course
评论课件=Review courseware
评论资讯=Review news
提交评估=Submit evaluation
提交考试=Submit exam
完成练习=Complete exercise
提交反馈意见=Submit feedback
提交作业=Submit assignment
完成签到=Complete sign-in
课程评星=Course star rating
课程测试计数事件=Course test count events
课程测试其他事件=Course Test other events
点赞课程=Like course
课件被评星=Courseware star rating
课件被浏览数=Courseware views
课件被点赞数=Courseware liked
连续每日学完一个课程（打卡次数）=Finish one course every day (punch in times)
连续每日学完一个课件（打卡次数）=Finish one courseware every day (punch in times)
每日连续登录打卡（打卡次数)=Sign in every day (punch in times)
提交案例=Submit case
参与审核案例=Participate in reviewing cases
案例评审通过后，评审得分激励=The case will be incenntived after passing the review
案例被设置为公司级=The case is set to the company level
案例被标识优质=The case is marked with high quality.
案例被设置为集团级=The case is set to the group level
考试成绩区间激励=Test score interval incentive
学完课程=Complete course
学完课件=Complete courseware
完成学习项目=Complete project
完成学习项目报名=Complete sign-up of the project
提交调研=Submit a survey
首次激活打卡=First activation punch card
关注话题=Follow topics
积分清零=Points zeroing
点击课程学习=Click to learn
点击课件学习=Click to learn
参与授课项目=Teaching project
参与授课课件个数=No. of lectures taught
通过发起直播贡献知识获得贡献学分=Get credits by initiating live to contribute knowledge
在直播间与主播进行互动给分=Get points by interacting with the anchor
发起的直播达到一定观看互动量给分=Get points when the initiated live reaches a certain amount of views
主播可在直播间内进行金币悬赏提问，学员在直播间内回答问题可获得金币=Trainees get gold coins for answering questions with rewards in the live room
发起直播给分=Get points by initiating lives
发表话题=Publish topic
任务完成率=Task completion rate
回复课程评论=Reply to course comments
回复课件评论=Reply to the courseware comments
回帖=Reply post
分享课程=Share course
分享课件=Share courseware
课件评星=Courseware star rating
话题回帖数量=Number of topic replies
所有课件的浏览时长进度(百分比)=Progress of all courseware viewing hours (%)
课件浏览时长进度(百分比)=Progress of courseware viewing hours (%)
点击观看直播=Click to watch live
累计观看直播时长达到指定时长=Cumulative live viewing time reaches the specified length
点击查看资讯=Click to view news
点赞课程评论=Like the course comment
学员点赞讲师=Students like the lecturer.
点赞资讯评论=Like news comment
上传课件并审核通过=Upload the courseware and approve it
测试数据=Test data
用户每日学完一个课程算1次打卡=Users complete one course per day count as 1 punch
用户每日学完一个课件算1次打卡=UUsers complete one courseware per day count as 1 punch
用户每日登录平台即打卡1次，不连续则清零重新计算=Users login platform once a day count as 1 punch(Clear and recalculate if not consecutive)
每个学员提交一份案例时奖励=Reward when each student submits a case
评委进行评分评分时，自己获得的奖励=Reward that judges received when they scored.
案例评审时，评审结束后获得的分数，达到一定程度时的奖励=Awarded when obtaining a certain score after the case review
案例被调整为公司级时奖励，降级不再取消=Rewarded when the case is set to company level, and the demotion is no longer cancelled
案例被管理员标识优质，取消优质不再取消=Rewarded when the case is marked with high quality, and the quality will not be cancelled.
案例被调整为集团级时奖励，降级不再取消=Rewarded when the case is set to group level, and the demotion is no longer cancelled
根据考试成绩区间进行激励=Motivate according to the test score range
完成学习项目中所有学习任务获得激励=Rewarded when completing all tasks in the project
用户首次访问系统时获得，仅1次=Rewarded only when the user accesses the system for the first time
积分清零(仅为积分统计使用，实际积分配置规则不使用)=Integral zeroing (only for integral statistics, Not valid for actual points configuration rules)
任意入口点击打开课程时计算=Calculated when open the course from any entry
任意入口点击打开课件时计算=Calculate when open the courseware from any entry
仅在学习项目结束后7天获得=Only available 7 days after the project
仅在学习项目结束后7天，根据任务完成率区间激励=Only rewarded 7 days after the project based on the task completion rate interval
对话题进行回帖=Reply to topic
根据话题回帖数量区间激励版主=Reward the moderator according to the number of topic replies
课程下所有的课件时长进度百分比=Progress of all lecture hours under the course(%)
公告=Announcement
报名=Sign up
话题=Topic
团队=Team
作业=Assignment
评论=Comment
结业=Gradutation
项目=Project
导师=Mentor
关卡-进行中=Level-in progress
关卡-已完成=Level-Completed
关卡-未解锁=Level-unlocked
移动版底部地图=Mobile version bottom map
移动版地图-无数据时=Mobile Map - When No Data is Available
PC版底部地图=Map at the bottom of PC
PC版地图=PC version map
PC版地图-无数据时=PC version map - without data
移动版地图=Mobile map
已完成的关卡样式图=Completed level style diagram
移动版闯关无关卡数据时默认样式=Default style for mobile quiz without levels data
PC版闯关整体地图（如关卡较多时会重复此部分循环使用）=Overall map for PC quiz (if there are many levels, this part will be recycled)
移动版闯关底部样式图=Bottom style for mobile quiz
进行中的关卡样式图=Level style diagram in progress
未解锁的关卡样式图=Unlocked level style diagram
PC版闯关底部样式图=Bottom style for PC quiz
移动版闯关整体地图（如关卡较多时会重复此部分循环使用）=Overall map for mobile quiz (if there are many levels, this part will be recycled)
资源不存在=Resource does not exist
无权访问=No right to access
不可重复提交=No repeat submissions
必传文件未上传=Required files are not uploaded
上传内容为空=Upload content is empty
请上传材料后再保存=Please upload the material before saving it.
不在允许时间内操作=The operation is not within the allowed time
相关参数错误=Related parameter error
保存文件出错=Error saving file
不支持的操作=Unsupported operation
包含已发布的资源,无法删除=Contains published resources and cannot be deleted
包含已启用的资源,无法删除=Contains enabled resources and cannot be deleted
初始化资源无法删除=Initialization resource cannot be deleted
初始化资源无法禁用=Initialization resource cannot be disabled
暂无数据=No data available for now
只能喜欢一次=Can only like it once
该评论不存在！=The comment does not exist!
层级必须小于4级=The level must be less than 4 levels
分类已被引用不能删除=Referenced categories cannot be deleted
标签已被使用=The label is already in use
该课程不存在=The course does not exist
无权限查看该课程=Have no right to view the course
同级下该分类已存在=The category already exists at the same level
该分类不存在=The category does not exist.
分类已被引用不能修改状态=Referenced category cannot be modified.
该课件不存在=The courseware does not exist.
课程评论不存在=Course review does not exist.
已选的下发范围超过上级分类=The selected distribution range exceeds the superior category
课件数为0，请添加课件后再发布！=There is 0 courseware. Please add the courseware before publishing!
删除标签中含有含有随课程分类创建的标签，该部分无法删除=The labels created with course categories cannnot be deleted
标签不存在或不支持选择=The label does not exist or not supproting to select
已保存成功，无需重复提交哦!=Saved successfully, no need to resubmit!
已保存成功，资源不存在!=Saved successfully, resource does not exist!
您未选择该标签，不能删除!=The label cannot be deleted without being selected!
包含已启用的分类，请取消后重试！=Contains enabled categories, please cancel and try again!
包含已启用的课件课数据，请取消后重试！=Contains the enabled courseware, please cancel and try again!
包含已启用的课程标签，请取消后重试！=Contains the enabled course label, please cancel and try again!
课程章节必须为正整数！=Course chapters must be positive integers!
章节名称不可为空=Chapter name cannot be empty
该课程未发布=The course has not been published
该课件未转码完成=The courseware was not transcoded.
无有效课件可关联创建=No valid courseware can be associated to create
该资源在资料库中不存在=The resource does not exist in the database
该资源中无可引用题目=There are no referenced questions in this resource
已发布不允许删除=Published resources cannot be deleted
闯关不存在=Quiz does not exist.
关卡存在任务不能删除=Levels with tasks cannot be deleted
已发布的公告不能修改=Published announcements cannot be modified
公告不存在=Announcement does not exist
任务不存在=Task does not exist
该闯关没有任务，不能发布=Unable to publish a quiz without a task
已有参与记录不能删除=Tasks with participation records cannot be deleted
团队名称已存在=Team name already exists
关卡不存在=The level does not exist.
团队不存在=The team does not exist
任务分记录不存在=Task points record does not exist
无法删除系统产生的任务分记录=Unable to delete task points record generated by the system
序号重复=Duplicate serial number
选中催办用户id不能为空=Selected reminder user id cannot be empty
该闯关样式模板不存在=The quiz style template does not exist.
闯关未开通该应用=The quiz does not activate the application.
只允许作业类型的任务=Only tasks of assignment are allowed
该闯关没有此任务或作业=The quiz does not have this task or assignment.
公共参数缺失=Missing common parameters
作业字数不符合要求=The number of words in the assignment does not meet the requirements.
作业已经审批，无法修改或审批作业=Unable to modify or approve assignment that has already been approved
参数错误=Parameter error
作业未提交状态不可审批=Unsubmitted assignment cannot be approved
评分不能为空=The score cannot be empty
评分不符合要求=The score does not meet the requirements.
作业不支持评分=Grading is not supported for assignments.
闯关介绍不得为空=The introduction of quiz cannot be empty.
操作成功=The operation is successful
服务器内部错误=Server internal error
客户端版本太低=Client version is too low
公共参数错误=Common parameter error
不允许重复提交=Duplicate submission is not allowed
存在敏感词=Existence of sensitive words
不允许被修改=Not allowed to be modified
不允许重复操作=Repetitive operations are not allowed
二维码已失效=The QR code is invalid
状态已经改变=The state has changed
无权限查看=No right to view
加群失败=Failed to add group
创建群失败=Failed to create group
不在允许时间内=Not within the allowed time
不存在=Doesn't exist
含有下级分类，无法删除=Contains subordinate categories and cannot be deleted
请求未授权=The request is not authorized
远程服务错误=Remote service error
不支持该类型的文件导入=This type of file import is not supported
导入错误，请下载正确的导入模板=Import error, please download the correct import template
日期参数错误=Date parameter error
请导入正确的下发范围文件=Please import the correct distribution scope file
用户不是管理员=The user is not an administrator
部门不在管辖范围中=The department is not under the jurisdiction.
管辖范围不能为空=Jurisdiction cannot be empty
开始时间不能大于结束时间=The start time cannot be later than the end time
导入的用户在用户列表中不存在=The imported user does not exist in the user list
帐号与姓名不匹配=The account number does not match the name
资源不存在或已删除=Resource does not exist or has been deleted
资源未启用=Unenabled resources
文件不存在=File does not exist
评估已完成，不可以重复提交=The evaluation has been completed and cannot be submitted repeatedly
评估不存在=Evaluation does not exist
评估记录不存在=Evaluation record does not exist
参数documentId不能为空=Parameter documentId cannot be empty
下发范围或者导入文件为空=The distribution scope or the import file is empty
保存失败，没有导入题目！=Failed to save, no questions imported!
保存失败，没有试卷信息！=Failed to save, no test paper information!
练习不存在=Exercise does not exist
练习未发布=Unpublished exercise
考试不存在或未发布=Exam does not exist or is not published
无权限访问该考试/练习=Have no right to access to the exam / activity
考试还未开始=The exam hasn't started yet.
考试次数已用完=No more exam times
已有另一个考试正在进行,请交卷后再来考试。=There is already another exam going on. Please hand in your paper and come back.
考试已结束,自动交卷=The exam is over and the papers will be handed in automatically.
不在考试时间范围内=Not within the exam time
非法提交=Illegal submission
未交卷，无法查看答案和解析=Answers and explanations cannot be viewed until the paper is handed in
考试时间不得为空=Exam time must not be empty
考试时长不得为零=The length of the exam must not be zero.
及格分有误,请修改\ 区间\:\ 0<及格分<总分=The passing score is incorrect, please modify the interval: 0 < passing score < total score
导入的用户不存在=The imported user does not exist
该练习/考试为组卷方式导入，无法预览=The exercise / exam is composed randomly and cannot be previewed
每种题型只能有一个=There can only be one question type
组卷方案已不存在=The test paper generating scheme no longer exists.
题目不存在=The problem does not exist.
试卷库中没有此试卷=This paper is not in the test paper library.
包含被引用的试卷，请取消后重试！=Contains the referenced paper, please cancel and try again!
包含被启用的试卷，请取消后重试！=Contains enabled papers, please cancel and try again!
请先选择资源！=Please select the resource first!
考题库/练习库已不存在=The question pool / exercise library no longer exists
包含被引用的练习库，请取消后重试！=Contains the referenced exercise library, please cancel and try again!
包含被引用的考题库，请取消后重试！=Contains the referenced question pool, please cancel and try again!
包含被启用的练习库，请取消后重试！=Contains the enabled exercise library, please cancel and try again!
包含被启用的考题库，请取消后重试！=Contains the enabled question pool, please cancel and try again!
类型与资源不匹配=Type does not match resource
请下载正确的文件进行导入=Please download the correct file for import
上一次参加的考试已不存在=The last exam taken no longer exists
分类不存在=Category does not exist
启用状态的分类无法删除，请取消后重试！=The enabled category cannot be deleted, please cancel and try again!
考试题目来源为组卷方案不可查看=Exam questions are not allowed to be viewed when the source is test paper scheme
未交卷,请尽快完成考试=No answers have been submitted yet, please complete the exam as soon as possible
资源未启用，无法引用=Unenabled resource is not allowed to reference
版本号一致，不需要重新获取题目=The version number is the same, and there is no need to retrieve the question
当前已改卷,考生已重新进入考试，请等待考生交卷=The paper has been marked and the examinee has re-entered the exam. Please wait for the examinee to hand in the paper.
上一次考试正在阅卷中，请等待老师阅卷后再次参加!=The last exam is in the process of marking, please wait for the teacher to mark the paper and take it again!
无法满足组卷要求,请重新选择=Unable to meet the requirements of generating test papers, please re-select
分类不可用,请重新选择=Categoty is not available, please re-select
组卷方案已启用，不能删除=Enabled composition scheme cannot be deleted
添加题目/更新题目选项无参考答案=Add question / update question option no reference answer
开始时间不得晚于结束时间=The start time must not be later than the end time
视频文件大小超出两百M限制,请重新上传=The video file size exceeds the limit of 200 MB. Please upload it again.
包含阅卷中/已通过的考生，请重新选择=Include examinees who are marking / have passed, please re-select
组卷方案已被使用，不能删除或修改=The test paper scheme has been used and cannot be deleted or modified
您不是当前考试的阅卷人，无法进行改卷操作=You are not the reader of the current exam, so you cannot change the test paper.
设置重考时，重考次数需设置大于0=When setting a retest, the number of reexams should be greater than 0
有考试引用了该组卷方案，不能编辑=This test paper scheme is quoted in some exams and cannot be edited.
该题不是主观题，无法获取答题详情列表=This question is not subjective and a list of answer details cannot be obtained
题目标准答案不得为空=The standard answer to the question must not be empty
题目选项格式有误=The format of the topic option is incorrect.
题目顺序不可重复=The order of questions can not be repeated.
已被引用不能删除=Has been referenced and cannot be deleted
已被引用不能修改状态=Has been referenced and cannot modify the state
案例条线已经被使用！=The case line has been used!
该用户账号已被删除！=The user account has been deleted!
该用户已被添加为专家！=The user has been added as an expert!
该案例不存在！=The case does not exist!
案例上传文件不可为空！=Case upload file cannot be empty!
不可选自己为上级父类！=Do not choose yourself as the superior parent class!
该专家不存在！=The expert does not exist!
启用状态的无法删除，请取消后重试！=The enabled status cannot be deleted, please cancel and try again!
条线已被引用不能删除=The line is already referenced and cannot be deleted
条线编码必须由数字和字母组成=The stripe code must consist of numbers and letters
条线编码长度应为2-36位=The length of the strip code should be 2-36 bits
案例提交必须全部字段填写=All fields must be filled in for case submission
案例暂存必须至少填写一个字段=At least one field must be filled in for the temporary storage of the case
头衔经验最小值不可大于最大值!=The minimum value of title experience must not be greater than the maximum value.
头衔名称已存在!=Title name already exists
输入的经验值范围已有其他头衔占用，请调整输入的经验值范围!=The range of experience values entered is already occupied by another title. Please adjust the range of experience values entered
奖品已经被积分游戏选项引用=The prize has been quoted by the points game option
奖品不存在=The prize doesn't exist
奖品库存不足=Insufficient stock of prizes
积分游戏选项奖品ID不存在=Points game options prize ID does not exist
该分类已经被奖品引用=The category has been quoted by the prize
该分类已经被下级分类引用=The category has been referenced by the subordinate category
该分类是系统初始化数据不可删除=The category which is the system initialization data cannot be deleted
该兑换订单不存在=The exchange order does not exist
该兑换订单已处理=The exchange order has been processed
激励配置被锁定无法删除=Incentive configuration is locked and cannot be deleted
资源类型不正确=Incorrect resource type
资源数据id不可为空=Resource data id cannot be empty
激励不足=Lack of incentive
积分不足=Insufficient points
学分不足=Insufficient credits
学时不足=Insufficient class hours
金币不足=Insufficient gold coins
激励类型不正确=Incorrect incentive type
人数已满=Full capacity
今日已经没有票了=There are no tickets left today
积分游戏为空或者版本号为空=The points game is empty or the version number is empty
抽奖次数为零=Number of raffles is zero
文件格式错误=Wrong file format
解压失败=Decompression failed
文件打包失败=File packaging failed
内容有误=The content is wrong
格式不正确=Incorrect format
课件格式不正确=Incorrect courseware format
操作失败!=Operation failed
导入的数据为空=The imported data is empty
请下载正确的模版进行导入=Please download the correct template for import
导出数据超出范围，请修改导出格式=The exported data is out of range, please modify the export format
导出失败=Export failed
复制出错=Copy error
无数据导出=Innumerable data export
文件资源已损坏=File resource is corrupted
文件不得为空=File must not be empty
达到空间大小限制，请与管理员联系=The space size limit is reached, please contact the administrator
板块名称已存在=Session name already exists
讲师不存在=Lecturer does not exist
讲师分类不存在=Lecturer category does not exist
讲师级别不存在=Lecturer level does not exist
同一分类下等级序号重复=The grade serial number is repeated under the same category
该用户已经是讲师，不可重复添加或申请！=The user is already be a  lecturer and cannot be added or applied repeatedly!
选择的讲师状态无需改变！=The selected instructor status does not need to be changed!
启用的配置请输入参数=Please enter parameters for the enabled configuration
请至少启用一条规则=Please enable at least one rule
规则配置信息不存在=Rule configuration information does not exist
不允许给自己点赞=Not allowed to like yourself
选择的讲师分类下存在等级，不能删除=There is a level under the selected lecturer category and cannot be deleted
该用户已经申请讲师，不可重复申请！=The user has applied for a lecturer and cannot repeat the application!
讲师详细资料不存在=Lecturer details do not exist
规则信息不存在=Rule information does not exist
选择的分类存在已启用等级，不能禁用=The selected category has an enabled level and cannot be disabled
选择的等级已被讲师关联，不能删除=The selected level has been associated by the lecturer and cannot be deleted
选择的等级被入库讲师引用，无法禁用=The selected level is referenced by the repository lecturer and cannot be disabled
选择的讲师\ 讲师等级已禁用=The selected lecturer level is disabled
选择的等级\ 的讲师分类被禁用=Lecturer category for the selected level is disabled
项目讲师记录已存在，不能重复添加=Project lecturer record already exists and cannot be added repeatedly
包含未出库讲师,不能删除=Contains unreleased instructors and cannot be deleted
分类已经被使用，不能禁用=Category is already in use and cannot be disabled
分类已经启用，不能删除=Category is enabled and cannot be deleted
知识库材料已启用，无法删除=Knowledge library material is enabled and cannot be deleted
讲师已经被预约，无法取消=The lecturer has been booked and cannot be cancelled
导入的讲师登录账号重复，请修改后重新导入=The login account of the imported lecturer is duplicated. Please modify it and import it again.
导入的讲师登录账号无效，请修改后重新导入=The imported lecturer login account is invalid. Please modify it and import it again.
导入的电话号码无效，请修改后重新导入=The imported phone number is invalid. Please modify it and import it again.
导入的邮箱无效，请修改后重新导入=The imported mailbox is invalid. Please modify it and import it again.
导入的姓名有误，请修改后重新导入=The imported name is incorrect. Please modify it and import it again.
讲师授课记录不存在=Lecturer lecture record does not exist
频道已满,添加失败=The channel is full, failed to add
直播不存在=Live does not exist
获取直播回放列表失败=Failed to get live playback list
直播未开始=The live has not started.
直播已结束=The live is over.
生成直播管理后台跳转链接失败=Failed to generate live management backend jump link
创建直播失败=Failed to create live
至少发布一个=Publish at least one
广告图片不可为空=Advertisement picture cannot be empty
该广告不存在=The ad does not exist
该签到不存在=The sign-in does not exist
该签到未发布=The sign-in has not been published
头条资源id不可为空=Headline resource id cannot be empty
头条类型错误=Wrong headline type
该头条不存在=The headline does not exist
外部链接url不可为空=External link url cannot be empty
头条图片(PC)、头条图片(APP)必须都上传图片或者都为空=Headline pictures (PC) and headline pictures (APP) must be uploaded or empty
签到未发布或不可用=Sign-in is not published or not available
未在签到时间范围=Not in the sign-in time range
已经签到过了=Already signed in
包含已被删除或未发布资源,请检查后重试=Contains deleted or unpublished resources, please check and try again
头条图片(含PC和移动H5)和头条通知至少上传一项=At least one headline picture (including PC and Mobile H5) and headline notification is uploaded
通知模式不可为空=Notification mode cannot be empty
添加投票失败=Failed to add vote
投票已存在=The vote already exists
修改投票失败=Failed to modify the vote
投票不存在=Voting does not exist
删除投票失败=Failed to delete vote
改变发布状态失败=Failed to change publishing status
添加投票内容失败=Failed to add voting content
修改投票内容失败=Failed to modify the voting content
删除投票内容失败=Failed to delete voting content
投票编号已经超过最大999，不能添加投票选项了=The voting number has exceeded the maximum of 999, so voting options cannot be added
投票内容不存在=The voting content does not exist
投票已结束=The vote is over
今日已投票=Voted today
没有投票次数了=No more votes
教室不存在=Classroom does not exist
时间段不存在=Time period does not exist
教室存在未来时间的预约,不能删除=The reserved classroom cannot be deleted
岗位发展计划包含未删除的活动,不能删除=The post development plan contains undeleted activities and cannot be deleted
存在已发布的岗位发展计划,不能删除=There is a published job development plan and cannot be deleted
岗位不存在=The post does not exist
失败！请勿选择同样的团队进行更换=Failure! Do not select the same team for replacement
所选部分资源无辅导栏目无法发布，请重新选择=Some of the selected resources cannot be published without a tutoring section, please re-select
所选部分资源包含非草稿状态的培训计划,仅支持删除草稿状态=Some of the selected resources contain training plans that are not in draft status. Only draft status deletion is supported
所选部分资源包含非草稿状态的培训计划,仅支持审批草稿状态=Some of the selected resources contain training plans that are not in draft status, and only draft status approval is supported
考试不存在=Exam does not exist
学习项目不存在=Project does not exist
活动类型错误=Activity type error
活动不存在=Activity does not exist
活动更新失败,请检查岗位发展计划和活动是否存在=Failed to update the activity. Please check whether the post development plan and activity exist
培训计划不存在或状态不支持=Training pkan does not exist or status does not support
审核配置不存在=Audit configuration does not exist
作业完成时间不能超过项目起止日期，请重新填写=The completion time of the assignment cannot exceed the start and end date of the project. Please fill in again
当前未添加结业条件，无法生成结业名单=No graduation condition has been added currently, unable to generate graduation list
作业不存在=Assignment does not exist
作业提交记录不存在=Assignment submission record does not exist
学习项目没有任务不允许发布=Projects are not allowed to be published without tasks.
用户团队已锁定,不能操作=The user team is locked and cannot operate
当前未添加结业条件，无法结业=No graduation conditions have been added at present, so graduation is not allowed
学习项目任务不存在=
任务排序已存在=Task sorting already exists
已过期评估不可再发布=Expired evaluation can no longer be published
直播发布失败=Failed to publish live
项目下已存在报名列表,无法创建!=The sign-up list already exists under the project and cannot be created!
学习项目任务类型错误=Project task type error
评估未发布不能分析=Evaluation cannot be analyzed without publihsing
项目应用未开启或不可用=The project application is not open or unavailable
签到还未开始=Sign-in hasn't started yet
签到已经结束=Sign-in is over.
评估已发布不能编辑=The evaluation has been published and cannot be edited
没有权限访问=No permission to access
学习项目还未开始=The learning project hasn't started yet.
学习项目已结束=The learning project has been completed
报名未开始=Sign-up has not started.
报名已结束=Sign-up is over.
报名不存在=Sign-up does not exist
该学习项目不允许取消报名=Canceling sign-up is not allowed for this project
您没有权限查看该项目的评价信息=You do not have permission to view evaluation information for this project
已完成评估，不能再次评估=The evaluation has been completed and cannot be evaluated again
项目类型为固定周期的时候，周期天数不能小于1=When the project type is a fixed cycle, the number of cycle days cannot be less than 1
学习项目任务创建类型错误=Project task creation type error
第一个栏目操作人类型必须是学员=The first column operator type must be a trainee
您已经报过名了=You have already signed up
班主任不需要报名=The head teacher does not need to register
没有作业附件可下载=No homework attachment to download
请使用管理员账号查看=Please use the administrator account to view
请勿重复签到=Please do not repeat check-in
报名人数超过计划人数=The number of applicants exceeds that of the plan
催办对象id不能为空=Reminder id cannot be empty
辅导不存在=Tutoring does not exist
查询参数为空=Query parameter is empty
评估类型为上传评估时\ 必须上传文件=Files must be uploaded when the evaluation type is upload
辅导任务不存在=Tutoring task does not exist
请上传评估文件=Please upload the evaluation file
岗位与目标发展岗位不允许相同!=Posts and target development posts are not allowed to be the same!
辅导模板栏目不存在=Tutoring template column does not exist
序号不能为空!=Sequence number cannot be empty!
不在报名时间内=Not within the sign-up time
报名人数已满=The number of applicants is full
该用户已报名=The user has signed up
序号重复,请重新输入!=Repeat the serial number, please re-enter it!
辅导模板不存在!=Tutoring template does not exist!
任务时间不能为空=Task time cannot be empty
内容长度必须在1-10000之间=Content length must be between 1 and 10000
学习项目已发布，取消发布后才能删除=The project has been published and cannot be deleted until canceling publishing
该学习项目下不存在本应用=This application does not exist under this project
该操作类型对应的值不存在=The value corresponding to this operation type does not exist
讲师状态异常=The lecturer is in abnormal condition
生成评估时必须要选择课件=Courseware must be selected when generating evaluation
该共读不存在!=The co-reading does not exist!
删除图书id不能为空=Delete book id cannot be empty
任务数量为零不能启用=The number of tasks is zero and cannot be enabled
删除失败！=Delete failed!
该心得不存在！=The experience does not exist!
该心得评论不存在=The comment does not exist
参数不正确=The parameter is incorrect
调研材料未通过=The research materials failed
报名人数达到上限=The number of applicants reached the upper limit
招募中不存在协办人=There is no co-sponsor in the recruitment
文件大小限制不可为空=File size limit cannot be empty
不支持的类型=Unsupported type
专题没有任务不允许发布=The subject is not allowed to be published without a task
专题不存在=The subject does not exist.
专题标签不存在=Subject label does not exist
专题任务并不存在=Subject tasks do not exist
专题任务创建类型错误=Error in subject task creation type
无权限查看该专题=Have no right to view the subject
专题还未开始=The subject hasn't started yet
专题已结束=The subject has been completed
已发布的调研不能删除，请重新选择=Published survey cannot be deleted, please re-select
调研不存在=Survey does not exist
被引入的调研库不能禁用，请重新选择=The introduced survey library cannot be disabled, please re-select
启用的调研库分类不能删除，请重新选择=The enabled survey library category cannot be deleted, please re-select
请先上传调研问卷=Please upload questionnaire first
调研库分类不存在=The survey library category does not exist
已有该分类调研库,不能禁用，请重新选择=The classified survey library already exists and cannot be disabled. Please select again.
无权限查看此调研=No right to view this survey
此调研不允许查看结果=This survey is not allowed to view the results
您已参加过调研,不能重复参加=You have already participated in the survey, so you cannot repeat it
需要刷新token,请获取token重新调用=Token needs to be refreshed. Get token and call again.
客户端未登录，需要自动重登录后才能进行相关访问操作=The client is not logged in. Automatic re-login is required before the relevant access operation can be carried out.
你的账号已在另一处登陆=Your account has been logged in somewhere else
jwt内容错误或已过期=Jwt content error or expired
短信验证码不正确=Incorrect SMS verification code
当前用户暂无管理权限，请联系管理员开通=The current user does not have administrative permissions. Please contact the administrator to activate
学员账户不能登录=Student account cannot be logged in
登录端类型为空，不允许登录！=Login side type is empty, login is not allowed!
用户名或密码错误=Wrong user name or password
用户名/账号不存在=User name / account does not exist
用户不存在=User does not exist
该用户已被禁用=The user has been disabled
组织编号重复=Duplicate organization number
有下一级组织=There is a next level of organization
组织已有用户=The organization already has users
身份编号重复=Duplicate identity number
身份不存在=Identity does not exist
岗位编号重复=Duplicate post number
已关联岗位的岗位族无法删除，请取消后重试=The post family of the associated position cannot be deleted. Please cancel and try again.
查询出错=An query error
同时只能一个导入用户,请等待其它导入完成=Only one user can be imported at the same time. Please wait for other imports to complete.
任务已提交=Task submitted
角色id已存在=Role id already exists
角色的上级不能是自已=The superior of the role cannot be self
已选组织有部分组织已不存在,请重新选=Some of the selected organizations no longer exist, please reselect
底部菜单最多5个=Up to 5 menus at the bottom
该反馈不存在=The feedback does not exist
该反馈已被处理过=The feedback has been processed
接口未授权，请与管理与联系=The interface is not authorized, please contact the management
学员下不能有子角色=Trainees cannot have sub-roles
当前角色下已有用户=Existing users under current role
当前角色下已有子级角色=Existing sub-level roles under current role
当前角色下已有子级角色并已有用户=Existing sub-level roles and users under current role
禁止给学员角色后端授权=Prohibit back-end authorization for trainee roles
禁止修改admin权限=Prohibit modification of admin permissions
用户已登出=The user has logged out
json格式返回结果转换错误=Json format returned result conversion error
鉴权失败=Authentication failed
帖子不存在！=The post does not exist!
帖子评论不存在！=Post comments do not exist!
当前登录人和发帖人不一致！=The current login person is inconsistent with the poster!
帖子已经设置了最佳回帖！=The post has set up the best reply!
不能设置帖子创建人的回帖为最佳回帖！=Cannot set the reply of the creator of the post as the best reply!
匿名帖子不能设置为最佳回帖！=Anonymous posts cannot be set as the best responses!
目前不在投票时间内！=It's not voting time right now!
请不要重复投票！=Please don't repeat the vote!
帖子不允许非创建人修改删除！=Posts are not allowed to be modified and deleted by non-creators!
帖子含有敏感内容！=The post contains sensitive content!
不允许非创建人修改=Non-creators are not allowed to modify
评论存在敏感词=There are sensitive words in comments
您已经被禁言，请联系管理员！=You have been banned, please contact the administrator!
当前用户不在该版块可见范围内=The current user is not within the visible range of this section
版块内含有帖子不能删除！=Posts in the section cannot be deleted!
该用户已是版块专家！=The user is already a forum expert!
投票项有重复，请重新填写=The voting items are duplicated, please fill in again
金币余额不足=Insufficient balance of gold coins
该分类名称已存在，请重新输入！=The category name already exists, please re-enter it!
分类已被引用不能禁用=Category has been referenced and cannot be disabled
已发布资讯不能删除=Published news cannot be deleted
正文内容不能为空=The content of the body cannot be empty
资讯评论不存在！=News comments do not exist!
资讯转码不是完成状态=News transcoding is not completed
样例文件为空=Sample file is empty
选择的用户不能是被评人或者已经是评委=The selected user cannot be participants or already a judge
未获取到样例文件扩展类型=The sample file extension type was not obtained
选择的用户不能是评委或者已经是被评人！=The selected user cannot be a judge or already a participant!
按天查\ -\ 日期不能为空=Check by day-date cannot be empty
按月查\ -\ 日期不能为空=Check by month-date cannot be empty
数据已被引用,不能删除=The data has been referenced and cannot be deleted
证书体系已被引用,不能删除=Certificate system has been referenced and cannot be deleted
证书体系已认证分类被引用,不能删除=Referenced certificate system certified category cannot be deleted
logo图片不能为空=Logo picture cannot be empty
体系不能为空=System cannot be empty
认证规则不存在=Authentication rules do not exist
参数集合为空=Parameter set is empty
认证已被启用,不能删除=Enabled authentication cannot be deleted
不存在该证书=The certificate does not exist
证书名称已存在，不能创建！=The certificate name already exists and cannot be created!
有效期不能为空！=The period of validity cannot be empty!
证书存在关联的发证规则，无法删除=There is an associated issuing rule for the certificate and cannot be deleted
证书预览错误=Certificate preview error
名称重复=Duplicate name
认证分类已被引用,不能删除=Referenced authentication category cannot be deleted
认证分类已被引用,不能禁用=Referenced authentication category cannot be disabled
资源不存在！=The resource does not exist!
资源没发布！=The resource is not published!
资源不开启评论=Resources do not allow to comment
不支持课件类型\ \:=Courseware types not supported:
暂未开发=Not developed yet
关联考试已结束=The associated exam has been completed
课件类型不支持=Courseware types not supported
课件未完成不能查看考试信息=Unable to view exam information when the courseware is not completed
模版文件不可为空=Template file cannot be empty
操作失败，请核对数据重试!=The operation failed, please check the data and try again!
操作成功!=The operation is successful!
题目选项最多允许8个选项，请修改后重试导入！=Topic options allow up to 8 options, please modify and try to import again!
导入的文件出现问题,请重试!=There is a problem with the imported file, please try again!
上架状态无效=Invalid status on shelves
该激励事件不存在，请刷新重试=The incentive event does not exist. Please refresh and try again.
计数类型的激励事件阈值上限和阈值下限不能为空=The upper limit and lower limit of the incentive event threshold for the counting type cannot be empty
该激励时间的阈值下线不能大于阈值上限=The threshold of the excitation time cannot be greater than the upper limit of the threshold.
激励事件类型无效=Invalid incentive event type
激励事件分类Code无效=Incentive event category code is invalid
启用禁用状态数据无效=Enabled / disabled status data is invalid
锁定/解除锁定状态数据无效=Invalid lock / unlock status data
缺失用户id=Missing user id
业务类型无效=Invalid business type
主文件不存在=Master file does not exist
解压课件失败=Failed to decompress the courseware
没有可以压缩下载的文件=There are no files that can be compressed and downloaded
文件格式不正确=Incorrect file format
保存上传文件失败=Failed to save uploaded file
证书背景图片不存在=Certificate background image does not exist
文件格式不支持=File format is not supported
执行转码失败=Failed to perform transcoding
pdf文件复制失败=Failed to copy pdf file
pdf\ 执行转码失败=Pdf failed to perform transcoding
模板做课,\ 模板文件不存在=use template to make lessons, template file does not exist
不支持的媒体类型=Unsupported media type
操作的数据主键字符串不能为空=The data primary key string for the operation cannot be empty
启用/禁用状态参数错误=Enabled / disabled status parameter error
分类类别无效=Invalid category
分类名称不能为空=Category name cannot be empty
审核数据主键不能为空=Audit data primary key cannot be empty
审核状态无效=Invalid audit status
审核拒绝时拒绝理由不能为空=The reason for rejection cannot be empty when the audit is rejected
知识库材料主键集合不能为空=Knowledge base material primary key collection cannot be empty
禁用/启用状态值无效=Invalid disabled / enabled status value
授课信息主键不能为空=The primary key of lecture information cannot be empty
删除数据主键字符串不能为空=Delete data primary key string cannot be empty
材料主键不能为空=Material primary key cannot be empty
参数：内容规则无效=Parameter: invalid content rule
实际课酬计算方式参数错误=The parameters of the calculation method of actual class remuneration are wrong
已存在此编码=This code already exists
回放不存在!=Playback does not exist!
直播回放不存在=Live playback does not exist
账号已存在。=The account already exists.
请选择人员!=Please select a person!
栏目不存在=Column does not exist
只能发布创建的、未发布、未删除的任务！=Only created, unpublished, undeleted tasks can be published!
只能取消发布创建的、已发布、未删除的任务！=Only publish created, published, undeleted tasks can be cancelled!
审核配置列表不得为空=Audit configuration list must not be empty
最后成员的权限只允许为【终审】=In the end, the permissions of members are only allowed to be [final review].
所有成员的权限只允许有一个【终审】=Only one [final review] is allowed for all members.
结束时间不能比开始时间早=The end time cannot be earlier than the start time
开始时间或者结束时间不得为空=Start time or end time must not be empty
闯关学员异常=Trainees are abnormal
任务数为0不能启用！=The number of tasks is 0 and cannot be enabled!
调研表已有用户填写不能更改=The survey form has been filled out by users and cannot be changed
材料规则-调研表最多只能添加一个=Material rules-only one can be added to the survey table
评审已有用户参与不能更改=The review already has user participation and cannot be changed
宣传文件为空=The Promotional document is empty
请添加调研模板=Please add a survey template
选择的材料规则中已有被评审人上传材料，不可删除=The material has been uploaded by the reviewer in the selected material rules and cannot be deleted
选择的调研表中已有被评审人参与，不可删除=The selected research table has already participated by the reviewer and cannot be deleted
未获取到宣传文件扩展类型=Did not get the extension type of promotional file
未获取到宣传文件类型=Did not get the promotional file type
操作失败，请昨选择该用户审核状态=The operation failed. Please select the user's audit status.
操作失败，请入正确的评分=The operation failed, please enter the correct score
操作失败，该用户已有审核记录=The operation failed. The user already has an audit record
操作失败，报名记录不存在或审核状态不正确=The operation failed, the registration record does not exist or the audit status is incorrect.
本次招募没有设置调研=There is no research set up for this recruitment.
暂时没有被招募人参加调研=No one has been recruited to participate in the research for now
下载出错，该用户未上传材料！=Download error, the user did not upload material!
招募不存在=Recruitment does not exist
已有用户报名，不可修改招募类型/协办处理类型=Users have signed up, and the recruitment type / co-processing type cannot be modified.
人数限制不得小于当前报名人数=The number limit shall not be less than the current number of applicants
招募结束时间不能比招募开始时间早=The end of recruitment cannot be earlier than the start of recruitment
开始时间或者结束时间为空=Start time or end time is empty
请输入限制人数=Please enter a limit
招募封面未上传=The recruitment cover was not uploaded
宣传封面未上传=The promotional cover was not uploaded
请选择下发范围或者导入用户文件=Please select the scope of distribution or import user files
专题任务开始时间不能小于专题计划开始时间！=The start time of the project task cannot be less than the start time of the project plan!
专题任务结束时间不能大于专题计划结束时间！=The end time of the project task cannot be greater than the end time of the project plan!
该组织已存在方案=The organization already has a scheme
上传的Excel中数据为空=The data in the uploaded Excel is empty
生日异常=Birthday exception
账号不能修改=The account cannot be modified
请输入密码=Enter your password
邮箱不能为空！=Mailbox cannot be empty!
邮箱地址不存在对应的用户！=There is no corresponding user for the email address!
该用户已被禁用！=The user has been disabled!
加密数据异常！=Encrypted data exception!
链接已失效,超出了2小时！=The link has expired for more than 2 hours!
链接已失效,你已经修改过密码！=The link has expired, you have changed the password!
如果启用，登录时需要输入验证码，否则不需要。=Enabled status means a verification code is required for login, otherwise it is not.
如果显示，首页会显示统计字段，隐藏则不显示。=Show means that the statistics field will be shown on the front page, otherwise it will not.
如果为是，讲师和课程分类根据用户组织显示相应数据=Yes means lecturer and course categories will display data according to user organization.
如果为是，话题回帖内容需要审核通过后才显示，否则不显示。=Yes means replies to topics need to be approved before they are visable.
客户端观看视频课件根据此参数判断是否防挂机=The parameter determines whether to set anti-hang-up for watching video courseware.
自定义防挂机时长（分）=Customized anti-hang-up hours (mins)
自定义防挂机倒计时长（秒）=Customized anti-hang-up countdown length (s)
首次视频课件播放是否倍数=Allow to play the video lesson for the first time in multiple speed or not
首次视频课件播放是否可拖拽=Allow to drag the video lesson for the first time or not
选择直播供应商=Select a  live provider
如果显示，个人中心会显示收藏字段，隐藏则不显示。=Show means that the collect field will be shown on the personal center, otherwise it will not.
如果显示，个人中心会显示下载字段，隐藏则不显示。=Show means that the download field will be shown on the personal center, otherwise it will not.
如果显示，个人中心会显示签到字段，隐藏则不显示。=Show means that the sign-in field will be shown on the personal center, otherwise it will not.
如果显示，个人中心会显示话题字段，隐藏则不显示。=Show means that the topic field will be shown on the personal center, otherwise it will not.
如果显示，个人中心会显示证书字段，隐藏则不显示。=Show means that the certificate field will be shown on the personal center, otherwise it will not.
如果显示，个人中心会显示课时字段，隐藏则不显示。=Show means that the class hours field will be shown on the personal center, otherwise it will not.
如果显示，个人中心会显示积分字段，隐藏则不显示。=Show means that the point field will be shown on the personal center, otherwise it will not.
如果显示，个人中心会显示学时字段，隐藏则不显示。=Show means that the class hours field will be shown on the personal center, otherwise it will not.
如果显示，个人中心会显示学分字段，隐藏则不显示。=Show means that the credit field will be shown on the personal center, otherwise it will not.
如果显示，个人中心会显示金币字段，隐藏则不显示。=Show means that the gold coin field will be shown on the personal center, otherwise it will not.
指定时间内发布的课程才能展示=Only show lcourses published at specified time
大于此配置的首页-话题\ 评论数+评星次数才能显示=Minimum required parameter values for popular courses
指定时间内发布的话题才能展示=Only show topics published at specified time
大于此配置的首页-话题-回帖数量才能显示=Minimum required parameter values for popular topics
0：表示不可加塞\ 。大于0\ 则表示加塞推荐课程的数量=0 means no plugging. A number greater than 0 means the max number of recommended courses allowed to plug into Your favourite.
控制课件是否默认勾选课件入库=Decide whether or not to set the courseware entry as the default checkbox
考试-重考次数=Exam - Retakes quantity
个人上传=Upload
审核=Review
入库评审=Storage review
学员=Trainee
调研已结束=Survey has ended
已删除=Delete
正常=Normal
转换完成=Conversion successful
未审核=To be audited
拒绝=Unapproved
系统渠道=System channel
手动添加=Add manually
未通过=Failed to pass
通过=Pass
未交卷=Unhanded in paper
已交卷=Number of submitted papers
阅卷中=Marking the paper
其它=Others
待审核=To be audited
审核通过=Approved
审核拒绝=Unapproved
已启用=Enabled
未启用=Disabled
未领取=Not received
已领取=Received
已返还=Returned
未持证=Unlicensed
已持证=Licensed
闯关游戏=Quiz
招募=Recruit
讲师等级=Lecturer grade
系统阅卷=System marking
人工阅卷=Manual marking
单次=Single
循环=Loop
低=Low
中=Medium
高=High
草稿=Draft
已撤回=Withdrawn
已驳回=Rejected
已提交=Submitted
已审核，审核通过=Audited, approved
已审核，审核不通过=Audited, failed.
已发布=Published
评审中=Reviewing
已评审，认证通过=Reviewed, certified.
已评审，认证不通过=Reviewed, not certified.
单选=Single choice
多选=Multiple choices
填空=Fill-in
判断=True or False
问答=Question and Answer
自动兑换=Automatic exchange
手动兑换=Manual exchange
未完成=Unfinished
已完成=Done
未参加=Un-participate
不需要评估=No evaluation
查看评估=View evaluation
需要评估=Evaluation
待晋级=To be promoted
待降级=To be demoted
待出库=To be released from library
手工调整=Manual adjustment
已晋级=Promoted
已降级=Downgraded
已出库=Out of the library
入库=Put into storage
再入库=Re-store
晋级失败=Failed to advance
降级失败=Downgrading failed
出库失败=Outbound failed
在途=On the way
出库=Out of the warehouse
收入=Income
支出=Expenditure
同岗位=Same post
跨岗位=Cross post
未发布=Unpublished
共享库课程=Shared course
未考催办=Call for exam
专题=Subject
专题课程任务=Subject - course task
专题考试任务=Subject - examination task
专题练习任务=Subject - exercise task
专题调研任务=Subject - research task
专题直播任务=Subject - live task
培训班=Training course
培训报名=Training sign-up
活动签到=Activity sign-in
奖品=Prize
学习项目报名审核=Project - audit sign-up
学习项目课程任务=Project - course task
学习项目考试任务=Project - examination task
学习项目练习任务=Project - exercise task
学习项目调研任务=Project - research task
学习项目直播任务=Project - live task
学习项目培训班任务=Project - training course task
学习项目辅导任务=Project - tutoring task
学习项目学习项目任务=Project task
招募活动=Recruitment
共读=Co-reading
闯关关卡任务=Quiz level task
讲师预警=Lecturer warning
未知=Unknown
男=Male
女=Female
导入试卷=Import test paper
引入试卷=Cite test paper
题库组卷=Random test paper
AI识别=AI recognize
转换中=In conversion
转换成功=Conversion successful
转换失败=Conversion failed
是=Yes
否=No
能力=Ability
自建=Self-built
已学习=Complete
学习中=Learning
完成=Complete
学员课件=Student courseware
学员课件学习明细=Courseware learning
学员课程学习明细=Course learning
首页=Home
任务管理=Task management
课程标签统计=Course lable statistics
外链访问明细=External chain access details
外链统计=Outer chain statistics
新闻资讯=News
闯关=Quiz
周期项目=Periodic project
课程学习任务=Course learning task
辅导模板管理=Tutoring template
辅导栏目管理=Tutoring column management
辅导分类=Tutoring classification
导师分类=Tutor classification
岗位发展=Post development
教室管理=Classroom management
学习项目配置=Learning project configuration
专题学习=Special subject learning
专题标签=Subject label
专题分类=Thematic classification
讲师异动管理=Lecturer change management
授课记录审核=Audit lecture records
规则配置=Rule configuration
线下授课明细=Offline teaching details
用户管理=User management
组织管理=Organization and management
身份管理=Identity management
角色管理=Role management
同步配置=Synchronous configuration
数据字典=Data dictionary
头衔设置=Title setting
参数配置=Parameter configuration
DIY首页=DIY Home Page
栏位管理=Field management
激励配置=Incentive allocation
反馈管理=Feedback management
操作日志=Operation log
ios下载码=ios download code
版本管理=Version management
界面配置=Interface configuration
分类管理=Category management
学习任务中心=Learning task center
总体运营统计=Overall operation statistics
上线用户统计=Online user statistics
课程情况统计=Curriculum statistics
部门学习统计=Departmental learning statistics
积分统计=Integral statistics
经验排行榜=Experience ranking
学员档案=Student file
资讯访问统计=News access statistics
访问量统计=Visit statistics
访问时段统计=Visit period statistics
课件学习统计=Courseware learning statistics
学员课件上传统计=Courseware from trainee 
搜索关键词统计=Search keyword statistics
内部讲师统计=Internal instructor statistics
目标激励明细查询=Target incentive details query
学员激励汇总表=Summary of student incentives
兑换记录查询=Exchange record inquiry
金币交易查询=Gold coin trading inquiry
考试情况统计=Examination statistics
课件库=Course library
试卷库=Test paper library
考题库=Exam question pool
练习库=Exercise library
调研库=Survey library
知识=Knowledge
评估库=Evaluation library
图片库=Picture library
快速培训=Rapid training
改卷列表=Mark list
学员考试成绩明细=Details of students examination results
运营中心=Operation center
培训计划管理=Training scheme
报表中心=Report center
窄屏广告=Narrow screen advertisement
头条通知=Headline notice
积分游戏=Points game
金币兑换=Coins exchange
积分中奖=Points winning
奖品管理=Prize management
邮箱模板=Email template
资源中心=Resource center
推送管理=Pushing management
推送白名单=Push whitelist
消息管理=Message management
投票=Voting
系统设置=System setup
案例=Case
培训中心=Training center
证书=Certification
持证明细=Certification details
持证统计=Certification statistics
管理培训项目=Manage Training Projects
班级管理=Class Management
表单模板库=Form Template Library
讲师及课程下载申请=Instructor and Course Download Application
举办统计=Event Holding Statistics
参与统计=Participation Statistics
讲师培训统计=Instructor Training Statistics
讲师授课统计明细=Instructor Teaching Statistics Details
课程认证讲师=Course Certified Instructor
课酬规则=Lesson Compensation Rules
讲师培训类别统计=Instructor Training Category Statistics
讲师计划类型统计=Instructor Plan Type Statistics
非工作日维护=Non-Working Day Maintenance
培训计划类别管理=Training Plan Category Management
培训计划汇总表=Training Plan Summary Table
培训计划条目明细=Training Plan Item Details
培训计划执行统计=Training Plan Execution Statistics
培训计划审核列表=Training Plan Review List
供应商管理=Supplier Management
外部培训管理=External Training Management
外部培训协办管理=External Training Co-Organizer Management
外部培训统计=External Training Statistics
外部培训明细=External Training Details
部门持证目标管理=Department Certification Target Management
持证目标统计=Certification Target Statistics
部门持证明细=Department Certification Details
发布通知=Publish Notification
催办通知=Urge Notification
协办待审通知=Co-organizing Pending Review Notification
审核结果通知=Audit Result Notification
发布报名通知=Publish Registration Notification
评价结果通知=Evaluation Result Notification
消息发送默认语言=DefaultLanguageForMessageSending
外部培训的培训专业认证体系=TrainingSpecialtyCertificationSystemForExternalTraining
外部培训报名时确认状态默认值=DefaultConfirmationStatusForExternalTrainingRegistration
外部培训机构确认状态默认值=DefaultConfirmationStatusForExternalTrainingInstitution
签到开始默认提前时间(分钟)=DefaultAdvanceTimeForCheckInStart(inminutes)
签到结束默认延后时间(分钟)=DefaultDelayTimeForCheckInEnd(inminutes)
具体活动的积分兑换配置=PointsRedemptionConfigurationForSpecificActivities
学员端-讲师课酬=StudentPortal-InstructorCompensation
后台-讲师积分推送=Backend-InstructorPointsPush
后台-培训班开班申请表单=Backend-TrainingClassOpeningApplicationForm
后台-专题开班申请表单=Backend-SpecialTopicOpeningApplicationForm
系统特权角色=SystemPrivilegedRoles
考试推送的课程是否按权限进行查看=WhetherCoursesPushedForExamsAreViewedAccordingToPermissions
评估结果优良分值=EvaluationResultExcellenceScoreValue
评估默认开始时间(分钟)=DefaultEvaluationStartTime(inminutes)
评估默认结束时间(分钟)=DefaultEvaluationEndTime(inminutes)
内训默认评估模板=DefaultEvaluationTemplateForInternalTraining
售后默认评估模板=DefaultEvaluationTemplateForAfterSales
培训计划汇总统计-项目完成标记=TrainingPlanSummaryStatistics-ProjectCompletionMark
培训计划执行统计-部门名称显示=TrainingPlanExecutionStatistics-DepartmentNameDisplay
课程-课程搜索是否启用ES=Course-WhetherESIsEnabledForCourseSearch
课程-课程关键词标签解析=Course-CourseKeywordTagParsing
讲师资源申请特权角色=PrivilegedRolesForInstructorResourceApplications
正常上午上班时间=RegularMorningWorkStartTime
正常上午下班时间=RegularMorningWorkEndTime
正常下午上班时间=RegularAfternoonWorkStartTime
正常下班时间=RegularWorkEndTime
考试防切屏录屏截图=ExamAntiScreenSwitchingScreenRecordingAndScreenshotPrevention
允许考试人脸识别=AllowFacialRecognitionForExams
自动保存考试时间=AutoSaveExamTime
三方链接加密私钥=PrivateKeyForThirdPartyLinkEncryption
三方链接加密公钥=PublicKeyForThirdPartyLinkEncryption
在线人数刷新时间（分）=OnlineUserCountRefreshTime(inminutes)
根据接口去请求认证体系的数据作为下拉数据，用于外部培训的培训专业=Request data from the authentication system via the API to use as dropdown options for training specialties in external training.
如果配置为否，则表示外部报名时，需要进行名单确认=If configured as "No," it means that a list confirmation is required during external registration.
如果配置为否，则表示外部报名时，需要外部培训机构进行确认=If configured as "No," it means that external training institutions need to confirm during external registration.
配置上该分值，签到(签退)默认开始时间为任务开始(结束)前N分钟=Configure this score value; the default start time for check-in (or check-out) will be N minutes before the task starts (or ends).
配置上该分值，签到(签退)默认开始时间为任务开始(结束)后N分钟=Configure this score value; the default start time for check-in (or check-out) will be N minutes after the task starts (or ends).
如果选择了"是"则，在课程积分配置中可修改是否可兑换的开关=If "Yes" is selected, the toggle for whether course points can be redeemed can be modified in the course points configuration.
如果是，则讲师在客户端能看到自己的课酬信息。=If enabled, instructors can view their own lesson compensation information in the client application.
如果是，则每月1日给老师邮箱推送积分总计， 及积分明细=If enabled, a summary of points and detailed point records will be sent to the teacher's email on the 1st of each month.
配置所诉需要开班的申请表单=Configure the application form required for opening a new class (as mentioned).
配置所诉需要开专题的申请表单=Configure the application form required for opening a new special topic (as mentioned).
用户系统所有的兜底管理操作=All fallback management operations in the user system.
如果选择否，则学员在考试入口进入的课程不检验权限=If "No" is selected, courses accessed via the exam entrance will not verify permissions for students.
配置上该分值，评估结果大于等于该分值则为优良，否则不良=Configure this score value; if the evaluation result is greater than or equal to this value, it is considered "Excellent"; otherwise, it is "Poor".
配置上该分值，评估默认开始时间为任务结束后N分钟=Configure this score value; the default start time for evaluation will be N minutes after the task ends.
配置上该分值，评估默认结束时间为任务结束后N分钟=Configure this score value; the default end time for evaluation will be N minutes after the task ends.
用于内训老师默认的评估模板=Default evaluation template for internal training instructors.
用于售后老师默认的评估模板=Default evaluation template for after-sales instructors.
如果启用，培训计划汇总表中的计划完成率字段会按此标识统计=If enabled, the "Plan Completion Rate" field in the training plan summary table will be calculated based on this identifier.
如果启用，培训计划执行统计-部门名称会按配置显示=If enabled, the "Department Name" in the training plan execution statistics will be displayed according to the configuration.
如果启用，搜索框会从ES查询数据。=If enabled, the search box will query data from ES.
如果启用，课件内容会关键词标签解析。=If enabled, courseware content will be parsed for keyword tags.
配置上的角色使用讲师时，可以不通过申请直接进行使用=When a role configured to use instructors is selected, it can be used directly without applying.
如果显示，管理端会显示设置入口和统计字段，隐藏则不显示。=If displayed, the management interface will show the settings entrance and statistical fields; if hidden, they will not be shown.
开启人脸识别，请与供应商联系进行开通=To enable facial recognition, please contact the supplier for activation.
单位：秒\ ，建议最小输入10秒=Unit: seconds, the minimum recommended input is 10 seconds.
配置第三方链接加密信息的私钥=Configure the private key for encrypting third-party link information.
配置第三方链接加密信息的公钥=Configure the public key for encrypting third-party link information.
配置系统登录统计在线人数刷新时间=Configure the refresh time for online user count statistics in the system login.
有=Yes
无=No
集团级=Group level
公司级=Company level
部门级=Departmental level
1-跟随系统,2-简体中文,3-英文=1-FollowSystem,2-SimplifiedChinese,3-English
0-请选择=0-PleaseSelect
1-展示互动,2-保利威,3-小鱼易连=1-Display interaction,2-Polyv,3-XYLink
0-发布,1-结业=0-Publish,1-Complete
0-部门名称,1-部门全路径名=0-DepartmentName,1-FullDepartmentPathName
学习账单=Learning Statement
外部链接=External Link
专题制度=Special Topic System
周期学习项目=Periodic project
快速培训课程任务=Quick Training Course Tasks
快速培训考试任务=Quick Training Exam Tasks
培训项目=Training programs
培训项目活动=Training Program Activities
培训项目班级=Training Program Classes
辅导=Tutor
活动期间打卡次数=The number of check-ins during the activity period
活动期间提交心得次数=Number of times insights were submitted during the activity
评论心得的数量=Number of insights commented on
每日只能打卡一次，每次都可以获得相应的运营分，超过要求次数后不再获得。=You can only check in once a day, and you'll receive corresponding operation points each time. You won't get them anymore after exceeding the required number of check-ins.
每本图书的心得算一次，第一次对图书提交心得可获得运营分，超过次数后不再获得。=Each book's review submission counts as one instance. You can earn operation points for the first time you submit a review for a book, and you won't receive them anymore after exceeding the allowed number of submissions.
每读完一本书即可获得运营分，超过规定数量后不再获取。=You can earn operation points each time you finish reading a book, and you will no longer receive them after exceeding the specified quantity.
对心得每进行一次评论即可获得运营分，超过规定数量后不再获得。=You can earn operation points each time you leave a comment on a review, and you will no longer receive them after exceeding the specified number of comments.
未认证=Not certified
集团级认证=Group certification
公司级认证=Company certification
部门级认证=Departmental certification
等级=Level 
其他=Other
全部分类=All Categorys
dept=Department
post=Post
username=Name
account=Account
question=Question
questionType=Question type
referenceAnswer=Reference answer
examName=Exam name
questionCount=Questions quantity
totalScore=Total score
userOrgName=Subordinate department
code=Code
status=Status
publishBy=Publisher
type=Type
uploadTime=Upload time
publishTime=Publish time
createTime=Create time
addTime=Add time
enabled=Enabled
categoryType=Category type
categoryName=Category name
superiorCategory=Superior category
displayOrder=Display order
upperLevel=Upper level
courseName=Course name
source=Source
startTime=Start time
completeTime=Completion time
labelName=Label name
coursewareName=Courseware name
userScore=Exam results
editTime=Edit time
editor=Editor
order=Order
isPublish=Published
sharedLibrary.courseCateTypeName=Course category
sharedLibrary.cwCount=Courseware quantity
examAnswerOfStatistical.passRate=Passing rate
examAnswerOfStatistical.avgScore=Average score
examAnswerRecordDetailList.userAnswer=Answer situation
examAnswerRecordDetailList.score=Question score
examAnswerRecordDetailList.examNo=Exam code
com.wunding.learn.file.api.dto.export.exam.ExamAnswerRecordDetailListExportDTO.examSortNo=Exam No
com.wunding.learn.file.api.dto.export.exam.ExamAnswerRecordDetailListExportDTO.userScore=Exam score
com.wunding.learn.file.api.dto.export.exam.ExamAnswerRecordDetailListExportDTO.answerTime=Submission time
examAnswerRecordList.answerTime=Submission time
examAnswerRecordList.postCount=Number of submit
examAnswerRecordList.answerCount=Remaining exams
examAnswerRecordList.firstScore=First score
examAnswerRecordList.finalScore=Final score
examAnswerRecordList.isPass=Pass
examAnswerRecordList.isRetake=Retake the exam
examAnswerRecordList.examStatus=Exam status
examCorrectExamList.passScore=Passing score
examCorrectExamList.submitCount=Number of submitted papers
examCorrectExamList.reviewedCount=Marked quantity
examCorrectExamList.unReviewedCount=Unmarked quantity
examCorrectRecord.loginName=Account
examCorrectRecord.submitTime=Submission time
examCorrectRecord.checkFinished=Marked
examCorrectRecord.userScore=Exam score
examEmployeeResultsDetail.examStatus=Exam Status
examEmployeeResultsDetail.postCount=Post Count
examEmployeeResultsDetail.examScore=Exam points
examEmployeeResultsDetail.score=Exam score
examList.examNo=Exam code
examList.passScore=Passing score
examList.examTimeCount=Exam duration
examList.checkPaperMethod=Marking mode
examList.sourceType=Question source
examQuestion.difficulty=difficulty
examQuestion.answer=Correct answer
examQuestion.mark=Question score
examQuestion.questionDesc=Question analysis
examQuestion.pointDesc=Score description
examQuestion.option=Option 
examQuestion.end=Set up to 8 options
exercise.exerciseName=Exercise name
schema.schemaName=Scheme name
schema.schemaDescription=Remarks
courseComment.fullName=Commentator
courseComment.loginName=Commentator account
courseComment.createTime=Comment time
courseComment.content=Comment content
course.courseNo=Course code
course.courseCateName=Course category
course.courseWareNum=Courseware quantity
course.authorName=Author
course.keywordStr=Keywords
course.viewCount=Number of visitors
course.orgName=Organizer
course.isShare=Shared course
course.publishName=Publisher
courseLearn.jobName=Post
courseLearn.courseNo=Course Code
courseLearn.orgName=Organization
courseLearn.courseWareCount=Total quantity of courseware
courseLearn.learnState=Learning progress
courseLearn.remainderCourseWareCount=Remaining courses quantity
courseStudyDetail.lastLearnTime=Recent study time
courseStudyDetail.duration=Cumulative duration
courseStudyDetail.learnState=Learning state
courseStudyDetail.remainderCourseWareCount=Remaining courses quantity
courseTagManage.tagClassifyName=Label category
courseTagManage.levelPathName=Full path name
courseTagManage.isOptional=Optional
courseTagManage.isShow=Front-end display
courseTagManage.defaultType=Default
courseTagSta.tagClassifyName=Label category
courseTagSta.levelPathName=Full path name
courseTagSta.tagCollectNum=Tag collect num
courseTagSta.tagHoldNum=Tag hold num
courseWareLearn.jobName=Post
courseWareLearn.courseNo=Course code
courseWareLearn.courseCategoryName=Course category
courseWareLearn.orgName=Organization
courseWareLearn.playTime=Duration
courseWareLearn.startTime=Start learning time
courseWareLearn.endTime=End learning time
courseWareLearn.duration=Learning duration
courseWareLearn.learnState=Learning progress
courseWareLearn.userScore=Exam score
coursewarePackage.transFormStatus=Transition state
coursewarePackage.createBy=Uploader
coursewareStudyDetail2.lastLearnTime=Recent study
coursewareStudyDetail2.duration=Cumulative duration
coursewareStudyDetail2.learnState=Learning state
coursewareStudyDetail.lastLearnTime=Recent study
coursewareStudyDetail.duration=Cumulative duration
coursewareStudyDetail.learnState=Learning state
coursewareStudyDetail.passState=Pass status
mergeCourse.createBy=Uploader
courseWareLib.cwType=Courseware type
courseWareLib.transformStatus=Transition state
courseWareLib.createTime=Storage time
courseWareLib.createByName=Depositor
courseWareLib.libraryName=Course library category
evaluationLib.evalName=Evaluation name
evaluationLib.createUserName=Creator
evaluationLib.libCategoryName=Evaluation library category
examLib.categoryName=Question pool category
examLib.libraryName=Question pool name
examLib.questionCount=Total
examLib.radioNum=Single choice
examLib.multipleChoiceNum=Multiple choice
examLib.judgeNum=True or False
examLib.clozeNum=Fill-in
examLib.answerNum=Question and answer
examLib.highLevelNum=High level
examLib.mediumLevelNum=Medium level
examLib.lowLevelNum=Low level
examLib.compulsoryNum=Compulsory
examLib.createTime=Creation date
examLib.createByName=Creator
examLib.updateTime=Edit time
examLib.updateByName=Editor
exerciseLib.libraryName=Question pool name
exerciseLib.used=Quoted
exerciseLib.createTime=Storage time
exerciseLib.createByName=Depositor
exerciseLib.categoryName=Exercise library category
libQuestion.questionName=Question
materialLib.name=Material name
materialLib.type=Material type
materialLib.transformStatus=Transition state
materialLib.knowledgeType=Knowledge base category
surveyLib.createBy=Creator
surveyLib.categoryName=Survey library category
tagCategory.parentName=Superior label
testPaperLib.categoryName=Exam paper library category
testPaperLib.testPaperName=Test paper name
testPaperLib.questionCount=Total
testPaperLib.radioNum=Single choice
testPaperLib.multipleChoiceNum=Multiple choice
testPaperLib.judgeNum=True or False
testPaperLib.clozeNum=Fill-in
testPaperLib.answerNum=Question and answer
testPaperLib.highLevelNum=High level
testPaperLib.mediumLevelNum=Medium level
testPaperLib.lowLevelNum=Low level
testPaperLib.compulsoryNum=Compulsory
testPaperLib.createTime=Creation date
testPaperLib.createBy=Creator
testPaperLib.updateTime=Edit time
testPaperLib.updateBy=Editor
testPaperQuestion.questionName=Question
testPaperQuestion.mark=Question score
testPaperQuestion.tag=Label
setAs=set as 
selfAssessMethod=self-evaluation
otherAssessMethod=evaluation
审核不通过=Not approved
审核未通过=Not approved
审核人=Reviewer
启用=Enabled
停用=Disable
培训计划=training plan
状态=state
学习地图=Learning map
测评项目=Assessment project
面授班级=Face-to-face class
日程安排=Schedule
答辩=Reply
活动数=activity number
培训人次=number of trainees
培训人数=number of trainees
评估分=evaluation score
不涉及=not involved
评估验收=evaluation and acceptance
考试验收=exam acceptance
保存材料=Save materials
提交报名=Submit registration
撤回报名=Withdraw registration
导入报名=Import registration
来源=source
阶段=stage
活动类型=activity type
选必修=Choose required
活动名称=activity name
活动资源编号=Activity Resource Number
必选条目=Required entry
锁定条目=Lock entry
添加日期=addition time
添加人=addition of human
账号=account
部门=department
必修=compulsory
选修=elective
一般=normal
满意=satisfaction
非常满意=very satisfied
课程任务=Course task
数据正在导入，请稍后查看=The data is being imported, please check back later
项目编码对应的项目不存在=The project that corresponds to the project code does not exist
项目编码对应的项目与项目名称不匹配=The project corresponding to the project code does not match the project name
进入项目日期需要大于项目开始日期=The entry date into the project needs to be greater than the project start date
完成项目日期需要小于等于项目结束日期=The project completion date needs to be less than or equal to the project end date
用户账号对应的用户不存在=The user for the user account does not exist
用户账号对应的用户与用户名称不匹配=The username of the user account does not match
第=The
行=line
授课开始时间需要大于项目开始日期=The class start time needs to be greater than the program start date
授课结束时间需要小于等于项目结束日期=The end time of the course must be less than or equal to the end date of the project
讲师编号对应的讲师不存在=The instructor for the instructor number does not exist
讲师编号对应的讲师与讲师姓名不匹配=The instructor name for the instructor number does not match
内部讲师对应的用户账号不能为空！=The user account corresponding to the internal instructor cannot be empty!
用户账号对应的用户不存在！=The user corresponding to the user account does not exist!
讲师对应的用户不一致！=The instructor corresponds to the user inconsistently!
任务=task
猜你喜欢=lovely
推荐课程=Recommended courses
热门课程1=Popular Course 1
热门课程2=Popular Course 2
岗位课程=Job courses
我正在学=learning
我参加的专题=joined subject
讲师风采1=Lecturer Style 1
讲师风采2=Lecturer Style 2
讲师风采3=Lecturer Style 3
直播中=living
当前直播=Current live
直播回看=Live replay
最新话题=Latest topics
热门话题1=Hot Topic 1
热门话题2=Hot Topic 2
置顶话题1=Top Topic 1
本月热门案例1=This month's popular case 1
本月热门案例2=This month's popular case 2
本月热门案例3=This month's popular case 3
热门案例1=Hot Case 1
热门案例2=Hot Case 2
热门案例3=Hot Case 3
案例质量等级1=Case Quality Level 1
案例质量等级2=Case Quality Level 2
案例质量等级3=Case Quality Level 3
指定类别=Specify Category
每日点击签到打卡=Click to check-in and clock in daily
每日课程学习10分钟=10 minutes of daily course learning
非长期任务=Non long-term tasks
全部任务=All Tasks
公司排名=Company Ranking
组织排名=Organizational ranking
单图=Single image
基本条件=Basic Conditions
关键能力=Key Competencies
知识内容=Knowledge Content
关键任务=Key Tasks
组织回馈=Organizational Feedback
知识学习=Knowledge Learning
培训=Training
授课=Teaching
评价=Appraise
实操=practicalOperation
课程问答=course Q&A
请先将课件入问答=Please initialize the course Q&A first
请等待课件向量化完成=Please wait for course vectorization to complete
没有可纳入问答的课件=No courseware available for Q&A
AI问答课程的数量已达上限=The number of AI Q&A courses has reached its limit
AI返回数据格式异常，请重试=AI returned data format is incorrect, please try again
聊天机器人使用数量已达到上限=The number of chatbots has reached its maximum limit
创建AI应用失败=Failed to create AI application
创建AI应用密钥失败=Failed to create AI application key
创建知识库失败=Failed to create knowledge base
关联知识库失败=Failed to associate knowledge base
上传文件到知识库失败=Failed to upload file to knowledge base
AI对话失败=AI conversation failed
设置AI配置失败=Failed to configure AI settings
删除AI资源失败=Failed to delete AI resource
纳入问答失败=Failed to include courseware in Q&A
存在未完成转码课件=There are unfinished transcoded coursewares
一个用户只能同时开启一个智能问答窗口=A user can only have one AI Q&A window open at a time
共计=total of
个知识=material
个课件=courseWare
个题目=question
个考题=examination question
个试卷=test paper
个练习=exercise
个调研=survey
个评估=evaluation
个部门=department
的使用范围，修改后共计=scope of use ,Modified total of 
试卷创建了=test paper create
考题创建了=examination question create
操作记录= operation record
新建申请=New application
修改申请=Modify application
驳回=Reject
审批不通过=Approval not approved
审批通过=Approved
审批中=In approval
题库题目=exam lib question
面授培训=face project
PK赛=pk competition
任职资格=job qualification
人才测评=assess project
未分类=Unclassified
分享直播=Share live
学完课件（固定）=Completed courseware (fixed)
闯关关卡完成=Complete the levels and challenges
共读心得提交次数=Number of submissions for shared reading experience
共读评论心得数量=Number of shared comments and insights
共读打卡次数=Read and clock in times together
共读阅读图书数量=Number of books read together
参与获取（双人）=Participate in obtaining (for two people)
个人获胜利（单人）=Individual Victory (Single)
个人获胜利（双人）=Individual Victory (Double)
个人获季军（多人）=Individual third place winner (multiple individuals)
个人获亚军（多人）=Individual runner up (multiple individuals)
个人获冠军（多人）=Individual champion (multiple people)
参与获取（单人）=Participate in obtaining (single person)
参与获取（多人）=Participate in obtaining (multiple people)
参与获取（组队）=Participate in obtaining (team)
团队获胜（组队）=Team wins (team)
完成项目=Complete a project
完成项目报名=Complete project registration
分享项目=Share project
商学院=business_school
兑换=exchangeCommodity
抽奖=integralDraw
系统=system
学完课件（按课件时长）=Complete the courseware (according to the duration of the courseware)
全流程行动学习=Full process action learning
标准化行动学习=Standardized Action Learning
被动微行动学习（测评）=Passive Micro Action Learning (Assessment)
被动微行动学习（无测评）=Passive micro action learning (no assessment)
无领导行动学习（测评）=Leaderless action learning (assessment)
无领导行动学习（无测评）=No leadership action learning (no assessment)
被动无领导行动学习（测评）=Passive leaderless action learning (assessment)
主动无领导行动学习（测评）=Proactive leaderless action learning (assessment)
主动无领导行动学习（无测评）=Proactive leaderless action learning (no assessment)
自评通知=Self evaluation Notice
他评通知=He commented on the notification
开班信息通知=Notice of Class Opening Information
审核未通过不可修改审核结果通知=Notification of non modifiable audit results if the audit has not been approved
审核未通过可修改结果通知=Notification of modifiable results if the review fails
审核通过结果通知=Notification of Approval Results
答辩结果通知=Notice of defense results
开课信息通知=Course information notification
自评=self-assessment
上级=manager
平级=lateral
下级=subordinate
未开始=未开始
单选题=Single Choice Question
多选题=Multiple choice questions
判断题=True or false
量表题=Scale questions
问答题=Essay question
场景说明=Scenario description
能力模型=Competency Model
胜任力地图=Competency Map
题目平均分=Average Score of Questions
题目合计分=Total Score of Questions
题目最低分=Minimum Score of Questions
题目最高分=Maximum Score of Questions
选项结果对应分值=Corresponding score for option results
选项结果总分分值=Total score of option results
选项结果平均分值=Average score of option results
选项结果中最低分值=The lowest score in the option results
选项结果中最高分值=The highest score in the option results
满意程度=Satisfaction level
符合程度=Compliance level
非常不满意=Very dissatisfied
不满意=Dissatisfied
比较满意=Quite satisfied
很不符合=Very inconsistent
不太符合=Not quite suitable
比较符合=Compared consistent
非常符合=Very consistent
竖排有标题=Vertical layout with title
竖排无标题=Vertical layout without title
横排滑动有标题=Horizontal sliding layout with title
横排滑动无标题=Horizontal sliding layout without title
竖排有详情=Vertical layout with details
竖排无详情=Vertical layout without details
横排滑动有详情=Horizontal sliding layout with details
横排滑动无详情=Horizontal sliding layout without details
上下布局=Top-bottom layout
单行=Single row
矩阵样式=Matrix style
样式1=Style 1
左右滑动=Left-right sliding
横排大=Large horizontal layout
横排小=Small horizontal layout
单个有标题=Single item with title
滑动有标题1=Sliding layout with title 1
滑动有标题2=Sliding layout with title 2
竖排=Vertical layout
竖排（类型版）=Vertical layout (type version)
横排（类型版）=Horizontal layout (type version)
竖排无简介=Vertical layout without introduction
竖排有简介=Vertical layout with introduction
无分类=Uncategorized
横排=Horizontal layout
竖版（封面在左）=Vertical layout (cover on left)
竖版（封面在右）=Vertical layout (cover on right)
滑动有标题=Sliding layout with title
PC-1/1-前后布局=PC-1/1 Front-back layout
PC-1/1-双列=PC-1/1 Double columns
PC-1/1-多行=PC-1/1 Multiple rows
PC-1/1-多行（类型版）=PC-1/1 Multiple rows (type version)
PC-1/1-左右布局（封面在左）=PC-1/1 Left-right layout (cover on left)
PC-1/1-左右布局=PC-1/1 Left-right layout
PC-1/1-左右布局（无详情）=PC-1/1 Left-right layout (without details)
PC-1/1-左右布局（有详情）=PC-1/1 Left-right layout (with details)
PC-1/1-无分类=PC-1/1 Uncategorized
PC-1/1-无标题=PC-1/1 Without title
PC-1/1-单行=PC-1/1 Single row
PC-1/1-单列=PC-1/1 Single column
PC-1/1-单行（类型版）=PC-1/1 Single row (type version)
PC-1/1-单列（强调前三）=PC-1/1 Single column (emphasizing top three)
PC-1/1-左右布局（封面在右）=PC-1/1 Left-right layout (cover on right)
PC-1/1-样式1=PC-1/1 Style 1
PC-1/1-样式2=PC-1/1 Style 2
PC-1/1-上下布局（无详情）=PC-1/1 Top-bottom layout (without details)
PC-1/1-上下布局=PC-1/1 Top-bottom layout
PC-1/1-上下布局（有详情）=PC-1/1 Top-bottom layout (with details)
PC-1/1-上下布局（有评论）=PC-1/1 Top-bottom layout (with comments)
PC-1/1-有分类=PC-1/1 Categorized
PC-1/2-四宫格=PC-1/2 Four-grid layout
PC-1/2-多行=PC-1/2 Multiple rows
PC-1/2-多行（类型版）=PC-1/2 Multiple rows (type version)
PC-1/2-左右布局（封面在左）=PC-1/2 Left-right layout (cover on left)
PC-1/2-左右布局=PC-1/2 Left-right layout
PC-1/2-左右布局（有详情）=PC-1/2 Left-right layout (with details)
PC-1/2-单列=PC-1/2 Single column
PC-1/2-单列（强调前三）=PC-1/2 Single column (emphasizing top three)
PC-1/2-上下布局（封面在右）=PC-1/2 Top-bottom layout (cover on right)
PC-1/2-左右布局（封面在右）=PC-1/2 Left-right layout (cover on right)
PC-1/2-独栏（有按钮）=PC-1/2 Single column (with button)
PC-1/2样式1=PC-1/2 Style 1
PC-1/2-样式1=PC-1/2 Style 1
PC-1/2-样式2=PC-1/2 Style 2
PC-1/2-上下布局（有按钮）=PC-1/2 Top-bottom layout (with button)
PC-1/2-上下布局（封面在左）=PC-1/2 Top-bottom layout (cover on left)
PC-1/2上下布局=PC-1/2 Top-bottom layout
PC-1/2-上下布局=PC-1/2 Top-bottom layout
PC-1/2-上下左右布局（有按钮）=PC-1/2 Top-bottom-left-right layout (with button)
PC-1/4-有详情=PC-1/4 With details
PC-1/4-无详情=PC-1/4 Without details
PC-1/4-前后布局=PC-1/4 Front-back layout
PC-头条1/4-多行=PC-Headline 1/4 Multiple rows
PC-1/4-多行=PC-1/4 Multiple rows
PC-头条1/4-多行（类型版）=PC-Headline 1/4 Multiple rows (type version)
PC-1/4-多行（类型版）=PC-1/4 Multiple rows (type version)
PC-头条1/4-封面在左=PC-Headline 1/4 Cover on left
PC-1/4-左右布局（封面在左）=PC-1/4 Left-right layout (cover on left)
PC-1/4-左右布局=PC-1/4 Left-right layout
PC-头条1/4-左右布局=PC-Headline 1/4 Left-right layout
PC1/4-矩阵样式=PC1/4 Matrix style
PC-头条1/4-矩阵样式=PC-Headline 1/4 Matrix style
PC-头条1/4-默认=PC-Headline 1/4 Default
PC-头条1/4-无详情=PC-Headline 1/4 Without details
PC-头条1/4-无封面=PC-Headline 1/4 Without cover
PC-1/4-无封面=PC-1/4 Without cover
PC-1/4-无标题=PC-1/4 Without title
PC-头条1/4-单列=PC-Headline 1/4 Single column
PC-1/4-单列=PC-1/4 Single column
PC-1/4-单列（强调前三）=PC-1/4 Single column (emphasizing top three)
PC-头条1/4-封面在右=PC-Headline 1/4 Cover on right
PC-1/4-左右布局（封面在右）=PC-1/4 Left-right layout (cover on right)
PC-头条1/4-样式1=PC-Headline 1/4 Style 1
PC-1/4-样式1=PC-1/4 Style 1
PC-头条1/4-样式2=PC-Headline 1/4 Style 2
PC-1/4-样式2=PC-1/4 Style 2
PC-1/4-上下布局=PC-1/4 Top-bottom layout
PC-头条1/4-上下布局=PC-Headline 1/4 Top-bottom layout
PC-1/4-上下布局(有标题)=PC-1/4 Top-bottom layout (with title)
PC-1/4-上下布局（无标题）=PC-1/4 Top-bottom layout (without title)
PC-3/4-前后布局=PC-3/4 Front-back layout
PC-3/4-双列=PC-3/4 Double columns
PC-3/4-多行=PC-3/4 Multiple rows
PC-3/4-多行（类型版）=PC-3/4 Multiple rows (type version)
PC-3/4-左右布局（封面在左）=PC-3/4 Left-right layout (cover on left)
PC-3/4-左右布局（无详情）=PC-3/4 Left-right layout (without details)
PC-3/4-左右布局=PC-3/4 Left-right layout
PC-3/4-左右布局（有详情）=PC-3/4 Left-right layout (with details)
PC-3/4-无标题=PC-3/4 Without title
PC-3/4-无分类=PC-3/4 Uncategorized
PC-3/4-单列=PC-3/4 Single column
PC-3/4-单列（强调前三）=PC-3/4 Single column (emphasizing top three)
PC-3/4-左右布局（封面在右）=PC-3/4 Left-right layout (cover on right)
PC-3/4-样式1=PC-3/4 Style 1
PC-3/4-样式2=PC-3/4 Style 2
PC-3/4-上下布局=PC-3/4 Top-bottom layout
PC-3/4-上下布局标题=PC-3/4 Top-bottom layout with title
PC-3/4-上下布局（有详情）=PC-3/4 Top-bottom layout (with details)
PC-3/4-上下布局（无详情）=PC-3/4 Top-bottom layout (without details)
PC-3/4-有分类=PC-3/4 Categorized
滑动无标题=Sliding layout without title
竖排（强调前三）=Vertical layout (emphasizing top three)
样式2=Style 2
单个无标题=Single item without title
默认样式=Default style
AI问答课程=AI questions and answers course
最新课程（创建时间）=Latest Course (Create Time)
最新课程（发布时间）=Latest Course (Publish Time)
猜你喜欢单独逻辑=Guess You Like Logic (Independent)
下发范围标识为精选好课的课程=Courses with "Featured Courses" label in distribution scope
下发范围-最近一个月内发布的-评论数+评星次数大于{0}的=Distribution scope - Courses released in the last month with total comments + ratings count greater than {0}
下发范围-评论数+评星次数-大于{0}的=Distribution scope - Total comments + ratings count greater than {0}
与自己岗位匹配的课程=Courses matching your position
我学习的，未完成的课程=Courses I'm learning but haven't completed
下发范围=Distribution scope
下发范围包含我未参与或者未完成的学习项目=Distribution scope includes learning projects I haven't participated in or completed
所有内部讲师=All internal instructors
下发范围-直播结束时间大于当前时间并且直播开始时间小于当前时间=Distribution scope - Live sessions with end time greater than current time and start time less than current time
下发范围直播结束时间大于当前时间=Distribution scope - Live sessions with end time greater than current time
下发范围直播结束时间小于当前间，且有回看文件的直播=Distribution scope - Live sessions with end time less than current time and available replay files
版块下发范围=Section distribution scope
版块下发范围发表时间在{0}天内回帖数量大于{1}个=Section distribution scope - Posts published within {0} days with reply count greater than {1}
版块下发范围回帖数量大于{0}个=Section distribution scope - Posts with reply count greater than {0}
版块下发范围设置为置顶的帖子=Section distribution scope - Posts set as pinned
所有的范围本月发表的，且本月发生了点赞数大于{2}=All distribution scope - Posts published this month with likes count greater than {2} this month
公司范围内公开本月发表的，且本月发生了点赞数大于{2}=Company-wide public - Posts published this month with likes count greater than {2} this month
集团范围内公开本月发表的，且本月发生了点赞数大于{2}=Group-wide public - Posts published this month with likes count greater than {2} this month
所有的范围点赞数大于2=All distribution scope - Posts with likes count greater than 2
公司范围内公开点赞数大于2=Company-wide public - Posts with likes count greater than 2
集团范围内公开点赞数大于2=Group-wide public - Posts with likes count greater than 2
标记为案例质量等级1的=Marked as Case Quality Level 1
标记为案例质量等级2的=Marked as Case Quality Level 2
标记为案例质量等级3的=Marked as Case Quality Level 3
下发范围包含我的培训项目=Distribution scope includes my training projects
每日点击签到，完成打卡=Click to check in daily and complete check-in
每日学习课程达到10分钟，则视为完成打卡=Learning courses for 10 minutes daily is considered as completing the打卡 (clock-in)
所有带有开始和结束日期的学习活动=All learning activities with start and end dates
所有学习活动=All learning activities
微信公众号=WeChatOfficialAccounts
企业微信=WeCom
微信小程序=WeChat Mini Program
邮箱=Email
APP=APP
钉钉=DingTalk
短信=SMS
学时上报=AcademicHoursReporting
辅导方案=TutoringPlan
培训周期类型=TrainingCycleType
喜欢类型=PreferredType (or FavoriteType, depending on context)
外部培训=ExternalTraining
测评=Assessment (or Evaluation, depending on the specific context)
推送=Push (or NotificationPush, if referring to sending notifications)
面授项目=FaceToFaceTrainingProject
第三方=ThirdParty
登录相关=LoginRelated
表单模板=FormTemplate
个人中心=PersonalCenter
海报分享=PosterSharing
用户=User
激励=Excitation
快速培训项目=Quick Training Project
分=points

表单=forms
尚未进行=Not yet conducted
正在进行=ongoing
已经完成=It's done
请输入题目=Please enter the question
选项1=Option 1
选项2=Option 2