package com.wunding.learn.promotedgame.service.client.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(name = "PersonalRankDTO", description = "个人排行榜")
public class PersonalRankDTO {


    /**
     * 排名
     */
    @Schema(description = "排名")
    private Integer no;

    /**
     * 统计id
     */
    @Schema(description = "统计id")
    private String sid;

    /**
     * 闯关主表id
     */
    @Schema(description = "闯关主表id")
    private String emigratedId;

    /**
     * 用户id
     */
    @Schema(description = "用户id")
    private String userId;

    /**
     * 状态(0:未开始,1:进行中,2:已完成)
     */
    @Schema(description = "状态(0:未开始,1:进行中,2:已完成)")
    private Integer status;

    /**
     * 得分
     */
    @Schema(description = "得分")
    private Integer score;

    /**
     * 用户名
     */
    @Schema(description = "用户名")
    private String userName;

    /**
     * 部门
     */
    @Schema(description = "部门")
    private String departmentName;

    /**
     * 头像
     */
    @Schema(description = "头像")
    private String headImg;

}
