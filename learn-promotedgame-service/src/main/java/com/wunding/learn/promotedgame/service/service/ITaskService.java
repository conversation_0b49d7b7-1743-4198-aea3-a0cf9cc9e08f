package com.wunding.learn.promotedgame.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.dto.PublishDTO;
import com.wunding.learn.promotedgame.service.admin.dto.CheckpointQueryDTO;
import com.wunding.learn.promotedgame.service.admin.dto.SaveTaskContentDTO;
import com.wunding.learn.promotedgame.service.admin.dto.TaskDTO;
import com.wunding.learn.promotedgame.service.admin.dto.TaskListDTO;
import com.wunding.learn.promotedgame.service.admin.dto.TaskSaveDTO;
import com.wunding.learn.promotedgame.service.model.Task;
import java.util.List;
import org.springframework.scheduling.annotation.Async;

/**
 * <p> 闯关任务表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">liyihui</a>
 * @since 2022-08-19
 */
public interface ITaskService extends IService<Task> {


    /**
     * 获取任务列表
     *
     * @param checkpointQueryDTO 闯关查询类
     * @return
     */
    PageInfo<TaskListDTO> list(CheckpointQueryDTO checkpointQueryDTO);

    /**
     * 获取关卡任务列表
     *
     * @param checkpointId 关卡id
     * @return
     */
    List<TaskListDTO> checkpointTaskList(String checkpointId, String taskId);

    /**
     * 创建任务
     *
     * @param taskSaveDTO 任务信息
     * @return 任务id
     */
    String createTask(TaskSaveDTO taskSaveDTO);

    /**
     * 更新任务
     *
     * @param taskSaveDTO 任务信息
     * @return 更新结果
     */
    Boolean updateTask(TaskSaveDTO taskSaveDTO);

    /**
     * 删除任务
     *
     * @param ids 任务id
     * @return 删除结果
     */
    void removeTask(String emigratedId, String ids);

    /**
     * 获取任务详情
     *
     * @param id 任务id
     * @return 任务详情
     */
    TaskDTO getTaskDetail(String id);

    /**
     * 用户开始任务
     *
     * @param taskId       任务id
     * @param isIgnoreView 是否忽略可见范围：0-否 1-是
     */
    void startTask(String taskId, Integer isIgnoreView);

    /**
     * 提交任务完成
     *
     * @param taskId
     */
    void submitTaskResult(String taskId);

    /**
     * 保存用户闯关记录
     *
     * @param taskId
     * @param isByUser 是不是用户主动触发的
     */
    void saveTaskRecord(String taskId, boolean isByUser, String currentUserId, boolean fromConsumer);


    /**
     * 导出闯关任务列表
     */
    @Async
    void exportData(CheckpointQueryDTO checkpointQueryDTO);

    /**
     * 根据目标ID返回任务ID
     *
     * @param tarId
     * @return
     */
    String getTaskIdByTarId(String tarId);

    /**
     * 根据任务id获取任务的类型
     *
     * @param id
     * @return
     */
    Integer getTaskTypeById(String id);

    /**
     * <p>  创建引用添加任务
     *
     * <AUTHOR>
     * @since 2024/8/8
     */
    String createQuoteTask(TaskSaveDTO taskSaveDTO);

    /**
     * <p>  创建直接添加的任务，返回任务id
     *
     * <AUTHOR>
     * @since 2024/8/9
     */
    String createDirectTask(TaskSaveDTO taskSaveDTO);

    /**
     * <p>  保存闯关任务内容id
     *
     * <AUTHOR>
     * @since 2024/8/9
     */
    void saveTaskContent(SaveTaskContentDTO saveTaskContentDTO);

    /**
     * <p>  发布/取消发布闯关任务
     *
     * <AUTHOR>
     * @since 2024/8/9
     */
    void publishTask(PublishDTO publishDTO);

    /**
     * <p>  同步闯关直接创建任务的下发范围
     *
     * <AUTHOR>
     * @since 2024/8/19
     */
    void syncSaveViewLimit(String emigratedId, List<Task> taskList);
}
