package com.wunding.learn.promotedgame.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @title: TeamSaveDTO
 * @projectName: mlearn
 * @description：
 * @date 2022/8/22 10:09
 */
@Data
@Schema(name = "TeamSaveDTO", description = "团队添加对象")
public class TeamSaveDTO {

    @Schema(description = "团队名称", required = true)
    @NotBlank(message = "团队名称不能为空")
    @Length(max = 50, message = "团队名称长度不能超过50")
    private String name;

    @Schema(description = "闯关id", hidden = true)
    private String emigratedId;

}
