package com.wunding.learn.promotedgame.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.promotedgame.service.admin.dto.CheckpointQueryDTO;
import com.wunding.learn.promotedgame.service.admin.dto.StylesTemplateListDTO;
import com.wunding.learn.promotedgame.service.admin.dto.StylesTemplateSaveDTO;
import com.wunding.learn.promotedgame.service.model.StylesTemplate;
import org.springframework.scheduling.annotation.Async;

/**
 * <p> 闯关样式模板表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">liyihui</a>
 * @since 2022-08-19
 */
public interface IStylesTemplateService extends IService<StylesTemplate> {

    /**
     * 获取模板列表
     *
     * @param checkpointQueryDTO 闯关
     * @return 模板列表
     */
    PageInfo<StylesTemplateListDTO> list(CheckpointQueryDTO checkpointQueryDTO);

    /**
     * 添加初始化模板
     *
     * @param emigratedId  闯关id
     * @param templateCode 样式模板ID
     * @return 模板列表
     */
    void create(String emigratedId, int templateCode);

    /**
     * 更新模板样式图片
     *
     * @param stylesTemplateSaveDTO
     * @return
     */
    Boolean update(StylesTemplateSaveDTO stylesTemplateSaveDTO);

    /**
     * 整体更换模板
     *
     * @param emigratedId  闯关id
     * @param templateCode 样式模板ID
     * @return
     */
    void updateStyleTemplate(String emigratedId, Integer templateCode);

    /**
     * 导出样式管理列表
     */
    @Async
    void exportData(CheckpointQueryDTO checkpointQueryDTO);
}
