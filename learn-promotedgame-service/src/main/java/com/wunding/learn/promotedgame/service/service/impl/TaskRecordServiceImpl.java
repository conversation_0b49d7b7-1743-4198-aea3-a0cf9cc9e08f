package com.wunding.learn.promotedgame.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.common.constant.other.PublishEnum;
import com.wunding.learn.common.dto.ResourceRecordSyncDTO;
import com.wunding.learn.common.enums.ResourceTypeEnum;
import com.wunding.learn.common.enums.other.OperationEnum;
import com.wunding.learn.common.enums.other.PublishStatusEnum;
import com.wunding.learn.common.mq.event.ResourceRecordSyncEvent;
import com.wunding.learn.common.mq.event.promotedGame.PromotedGameFinishEvent;
import com.wunding.learn.common.mq.service.MqProducer;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.promotedgame.service.constsnts.EmigratedConstants;
import com.wunding.learn.promotedgame.service.enums.CheckpointEnum;
import com.wunding.learn.promotedgame.service.enums.EmigratedStatusEnum;
import com.wunding.learn.promotedgame.service.event.EmigratedPartEvent;
import com.wunding.learn.promotedgame.service.event.EmigratedPassEvent;
import com.wunding.learn.promotedgame.service.mapper.CheckpointRecordMapper;
import com.wunding.learn.promotedgame.service.mapper.TaskRecordMapper;
import com.wunding.learn.promotedgame.service.mapper.TaskStatisticsMapper;
import com.wunding.learn.promotedgame.service.model.*;
import com.wunding.learn.promotedgame.service.service.*;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.service.UserFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p> 闯关任务记录表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">liyihui</a>
 * @since 2022-08-19
 */
@Slf4j
@Service("promotedGameTaskRecordService")
public class TaskRecordServiceImpl extends ServiceImpl<TaskRecordMapper, TaskRecord> implements ITaskRecordService {

    @Resource(name = "promotedGameTaskService")
    @Lazy
    private ITaskService taskService;
    @Resource
    private TaskStatisticsMapper taskStatisticsMapper;
    @Resource
    private ICheckpointRecordService checkpointRecordService;
    @Resource
    private ICheckpointService checkpointService;
    @Resource
    private CheckpointRecordMapper checkpointRecordMapper;
    @Resource
    private IStatisticalService statisticalService;
    @Resource
    private MqProducer mqProducer;

    @Resource
    private UserFeign userFeign;

    @Resource
    @Lazy
    private IEmigratedService emigratedService;

    @Override
    public Long getSumTime(String emigratedId, String userId) {
        return baseMapper.getSumTime(emigratedId, userId);
    }


    @Override
    public void saveUserTaskRecord(String currentUserId, Task task, boolean fromConsumer) {

        //检查关卡是否完成
        Map<String, Integer> checkpointStatusMap = checkpointStatus(currentUserId, task.getCheckpointId());
        Integer checkpointStatus = checkpointStatusMap.get(EmigratedConstants.RESULT);
        if (-1 == checkpointStatus) {
            //这种是不正常的情况
            return;
        }
        log.error("saveUserTaskRecord 走到了这里，currentUserId:{},checkpointStatus:{}", currentUserId, JsonUtil.objToJson(checkpointStatus));
        //保存关卡记录表
        this.insertOrUpdateCheckpointRecord(checkpointStatus, currentUserId, task);

        //检查闯关是否完成
        int emigratedPass = this.checkPromotedGameFinish(currentUserId, task.getEmigratedId());

        //保存闯关记录
        this.insertOrUpdateEmigratedStatistical(emigratedPass, currentUserId, task, fromConsumer);
    }

    @Override
    public void saveUserTaskRecordV1(String currentUserId, Task task, boolean fromConsumer) {
        // 检查关卡是否完成
        Map<String, Integer> checkpointStatusMap = checkpointStatus(currentUserId, task.getCheckpointId());
        Integer checkpointStatus = checkpointStatusMap.get(EmigratedConstants.RESULT);
        if (-1 == checkpointStatus) {
            //这种是不正常的情况
            return;
        }

        Integer finishTaskCount = checkpointStatusMap.get(EmigratedConstants.FINISH_TASK_COUNT);
        if (0 == finishTaskCount) {
            //这种是不正常的情况
            return;
        }
        log.error("saveUserTaskRecordV1 走到了这里1，currentUserId：{}", currentUserId);
        //保存关卡记录表
        this.insertOrUpdateCheckpointRecord(checkpointStatus, currentUserId, task);

        //检查闯关是否完成
        int emigratedPass = checkPromotedGameFinish(currentUserId, task.getEmigratedId());

        //保存闯关记录
        this.insertOrUpdateEmigratedStatistical(emigratedPass, currentUserId, task, fromConsumer);
    }

    /**
     * 查询关卡id
     *
     * @param currentUserId 当前用户id
     * @param checkpointId  关卡id
     * @return 0-进行中 1-已完成
     */
    private Map<String, Integer> checkpointStatus(String currentUserId, String checkpointId) {
        Map<String, Integer> result = new HashMap<>(4);

        LambdaQueryWrapper<Task> promotedgamerecordLambdaQueryWrapper = new LambdaQueryWrapper<>();
        promotedgamerecordLambdaQueryWrapper.eq(Task::getCheckpointId, checkpointId);
        promotedgamerecordLambdaQueryWrapper.eq(Task::getIsPublish, PublishStatusEnum.IS_PUBLISH.getValue());
        promotedgamerecordLambdaQueryWrapper.orderByAsc(Task::getSort);
        List<Task> promotedGameItems = taskService.list(promotedgamerecordLambdaQueryWrapper);
        if (CollectionUtils.isEmpty(promotedGameItems)) {
            //闯关中不存在已发布的任务
            result.put(EmigratedConstants.RESULT, -1);
            result.put(EmigratedConstants.FINISH_TASK_COUNT, 0);
            return result;
        }

        List<String> promotedGameItemIds = promotedGameItems.stream().map(Task::getId).collect(Collectors.toList());

        LambdaQueryWrapper<TaskRecord> countPromotedGameRecord = new LambdaQueryWrapper<>();
        countPromotedGameRecord.in(TaskRecord::getTaskId, promotedGameItemIds);
        countPromotedGameRecord.eq(TaskRecord::getResult, EmigratedConstants.EMIGRATED_PASS);
        countPromotedGameRecord.eq(TaskRecord::getUserId, currentUserId);
        long finishTaskCount = count(countPromotedGameRecord);

        int checkpointStatus;
        if (finishTaskCount >= promotedGameItemIds.size()) {
            // 闯关关卡完成
            checkpointStatus = CheckpointEnum.PASS.getState();
        } else {
            // 闯关关卡未完成
            checkpointStatus = CheckpointEnum.UNDERWAY.getState();
        }
        result.put(EmigratedConstants.RESULT, checkpointStatus);
        result.put(EmigratedConstants.FINISH_TASK_COUNT, (int) finishTaskCount);
        return result;
    }

    /**
     * 保存或更新关卡记录
     *
     * @param checkpointStatus 关卡状态
     * @param currentUserId    当前用户id
     * @param task             任务
     */
    private void insertOrUpdateCheckpointRecord(int checkpointStatus, String currentUserId,
                                                Task task) {
        log.error("insertOrUpdateCheckpointRecord 走到了这里9，currentUserId：{}", currentUserId);
        Date currentTime = new Date();
        //保存关卡记录表
        long sumTaskScore = taskStatisticsMapper.sumTaskScore(currentUserId, task.getCheckpointId());
        CheckpointRecord checkpointRecord = new CheckpointRecord();
        checkpointRecord.setStatus(checkpointStatus);
        checkpointRecord.setScore(sumTaskScore);

        LambdaQueryWrapper<CheckpointRecord> recordLambdaQueryWrapper = new LambdaQueryWrapper<>();
        recordLambdaQueryWrapper.eq(CheckpointRecord::getEmigratedId, task.getEmigratedId());
        recordLambdaQueryWrapper.eq(CheckpointRecord::getCheckpointId, task.getCheckpointId());
        recordLambdaQueryWrapper.eq(CheckpointRecord::getUserId, currentUserId);

        List<CheckpointRecord> checkpointRecordList = checkpointRecordService.getBaseMapper().selectList(recordLambdaQueryWrapper);
        if (checkpointRecordList.size() > 1) {
            log.error("checkpointRecordList插入了两行，害死人," + JsonUtil.objToJson(checkpointRecordList));
            //在这里删除一行
            for (int i = 0; i < checkpointRecordList.size(); i++) {
                if (i == 0) {
                    continue;
                }
                checkpointRecordService.getBaseMapper().deleteById(checkpointRecordList.get(i).getId());
            }
            log.error("checkpointRecordList插入了两行," + JsonUtil.objToJson(checkpointRecordList.get(0)));
        }

        CheckpointRecord existCheckpointRecord = checkpointRecordService.getOne(recordLambdaQueryWrapper);

        // 保存关卡记录表
        if (null == existCheckpointRecord) {
            checkpointRecord.setId(StringUtil.newId());
            checkpointRecord.setEmigratedId(task.getEmigratedId());
            checkpointRecord.setUserId(currentUserId);
            checkpointRecord.setStatus(checkpointStatus);
            checkpointRecord.setCheckpointId(task.getCheckpointId());
            checkpointRecord.setCreateTime(currentTime);
            if (Objects.equals(CheckpointEnum.PASS.getState(), checkpointStatus)) {
                checkpointRecord.setFinishTime(currentTime);
            }
            log.error("insertOrUpdateCheckpointRecord save," + JsonUtil.objToJson(checkpointRecord));

            checkpointRecordService.save(checkpointRecord);
        } else {
            // 完成时间不再重复改变
            if (Objects.equals(CheckpointEnum.PASS.getState(), checkpointStatus)
                    && Objects.isNull(existCheckpointRecord.getFinishTime())) {
                checkpointRecord.setFinishTime(currentTime);
            }
            //更新关卡记录表
            checkpointRecord.setId(existCheckpointRecord.getId());
            log.error("insertOrUpdateCheckpointRecord updateById," + JsonUtil.objToJson(checkpointRecord));
            checkpointRecordService.updateById(checkpointRecord);
        }
    }

    /**
     * 查询闯关状态
     *
     * @param currentUserId  当前用户id
     * @param promotedGameId 闯关id
     * @return 闯关是否完成  1-进行中 2-已完成
     */
    public int checkPromotedGameFinish(String currentUserId, String promotedGameId) {
        //检查闯关是否完成
        LambdaQueryWrapper<Checkpoint> checkpointLambdaQueryWrapper = new LambdaQueryWrapper<>();
        checkpointLambdaQueryWrapper.select(Checkpoint::getId);
        checkpointLambdaQueryWrapper.eq(Checkpoint::getEmigratedId, promotedGameId);
        checkpointLambdaQueryWrapper.eq(Checkpoint::getIsPublish, PublishEnum.PUBLISHED.getValue());
        checkpointLambdaQueryWrapper.orderByAsc(Checkpoint::getSort);
        List<Checkpoint> emigratedCheckpoints = checkpointService.list(checkpointLambdaQueryWrapper);
        if (CollectionUtils.isEmpty(emigratedCheckpoints)) {
            //闯关中不存在已发布的关卡
            return -1;
        }

        //已经发布的关卡id
        List<String> emigratedCheckpointIds = emigratedCheckpoints.stream().map(Checkpoint::getId)
                .collect(Collectors.toList());
        //TODO 这里有问题。
        LambdaQueryWrapper<CheckpointRecord> checkpointRecordLambdaQueryWrapper = new LambdaQueryWrapper<>();
        checkpointRecordLambdaQueryWrapper.eq(CheckpointRecord::getStatus, CheckpointEnum.PASS.getState());
        checkpointRecordLambdaQueryWrapper.eq(CheckpointRecord::getUserId, currentUserId);
        checkpointRecordLambdaQueryWrapper.in(CheckpointRecord::getCheckpointId, emigratedCheckpointIds);
        long passCheckpointCount = checkpointRecordService.count(checkpointRecordLambdaQueryWrapper);
        int emigratedPass = EmigratedStatusEnum.UNDERWAY.getStatus();
        if (passCheckpointCount >= emigratedCheckpointIds.size()) {
            //通过的关卡数等与已发布关卡数，代表闯关完成
            emigratedPass = EmigratedStatusEnum.FINISH.getStatus();
        }
        return emigratedPass;
    }

    /**
     * 保存或者更新闯关记录
     *
     * @param emigratedPass 闯关状态
     * @param currentUserId 当前用户id
     * @param task          任务
     */
    public void insertOrUpdateEmigratedStatistical(int emigratedPass, String currentUserId, Task task,
                                                   boolean fromConsumer) {
        log.error("insertOrUpdateEmigratedStatistical 闯关到了这里，userId:{}，task:{}，fromConsumer:{}", currentUserId, JsonUtil.objToJson(task), fromConsumer);
        Statistical insertOrUpdateStatistical = new Statistical();
        insertOrUpdateStatistical.setCheckpointCurrentId(task.getCheckpointId());
        insertOrUpdateStatistical.setTaskCurrentId(task.getId());
        insertOrUpdateStatistical.setStatus(emigratedPass);

        // 闯关总得分
        long sumCheckpointScore = checkpointRecordMapper.sumCheckpointScore(currentUserId, task.getEmigratedId());
        sumCheckpointScore += taskStatisticsMapper.sumImportScore(currentUserId, task.getEmigratedId());
        insertOrUpdateStatistical.setScore(sumCheckpointScore);

        LambdaQueryWrapper<Statistical> statisticalLambdaQueryWrapper = new LambdaQueryWrapper<>();
        statisticalLambdaQueryWrapper.eq(Statistical::getUserId, currentUserId);
        statisticalLambdaQueryWrapper.eq(Statistical::getEmigratedId, task.getEmigratedId());
        statisticalLambdaQueryWrapper.orderByDesc(Statistical::getCreateTime);
        List<Statistical> statisticalList = statisticalService.list(statisticalLambdaQueryWrapper);

        if (!CollectionUtils.isEmpty(statisticalList)) {
            //这里又多了些东西
            log.error("insertOrUpdateEmigratedStatistical 闯关到了这里，userId:{}，statisticalList:{}，statisticalList:{}",
                    currentUserId, JsonUtil.objToJson(statisticalList), statisticalList.size());
            for (int i = 0; i < statisticalList.size(); i++) {
                if (i == 0) {
                    continue;
                }
                log.error("insertOrUpdateEmigratedStatistical 害死人，userId:{},id:{}", currentUserId, statisticalList.get(i).getId());
                statisticalService.getBaseMapper().deleteById(statisticalList.get(i).getId());
            }
        }
        Statistical statistical = statisticalService.getOne(statisticalLambdaQueryWrapper);
        if (null != statistical && statistical.getStatus() == EmigratedStatusEnum.FINISH.getStatus()) {
            //闯关已经完成不执行
            return;
        }

        UserDTO userById = userFeign.getUserById(currentUserId);
        // 不存在，并且不是消息队列调用过来的
        if (null == statistical && !fromConsumer) {
            insertOrUpdateStatistical.setId(StringUtil.newId());
            insertOrUpdateStatistical.setUserId(currentUserId);
            insertOrUpdateStatistical.setCreateBy(currentUserId);
            insertOrUpdateStatistical.setEmigratedId(task.getEmigratedId());
            insertOrUpdateStatistical.setCreateTime(new Date());
            insertOrUpdateStatistical.setLevelPath(userById.getLevelPath());
            statisticalService.save(insertOrUpdateStatistical);
            log.error("insertOrUpdateEmigratedStatistical  save，insertOrUpdateStatistical:{}", insertOrUpdateStatistical);
            // 刷新参加人数
            mqProducer.send(new EmigratedPartEvent(task.getEmigratedId()));
            mqProducer.sendMsg(new ResourceRecordSyncEvent(
                    new ResourceRecordSyncDTO(OperationEnum.CREATE, ResourceTypeEnum.QUIZ.name(),
                            insertOrUpdateStatistical.getId(), task.getEmigratedId(), currentUserId, 0, currentUserId,
                            new Date(), currentUserId, new Date())));

        } else {
            //闯关用户记录表
            insertOrUpdateStatistical.setId(statistical.getId());
            insertOrUpdateStatistical.setUpdateTime(new Date());
            insertOrUpdateStatistical.setUpdateBy(currentUserId);
            insertOrUpdateStatistical.setLevelPath(userById.getLevelPath());
            log.error("insertOrUpdateEmigratedStatistical  updateById，insertOrUpdateStatistical:{}", insertOrUpdateStatistical);
            statisticalService.updateById(insertOrUpdateStatistical);
        }

        if (EmigratedStatusEnum.FINISH.getStatus() == emigratedPass) {
            insertOrUpdateStatistical.setFinishTime(new Date());
            // 发送[闯关完成事件]
            mqProducer.send(
                    new PromotedGameFinishEvent(task.getEmigratedId(), currentUserId, currentUserId, sumCheckpointScore));
            // 刷新完成人数
            mqProducer.send(new EmigratedPassEvent(task.getEmigratedId()));
            // 发送资源记录同步事件
            mqProducer.sendMsg(new ResourceRecordSyncEvent(
                    new ResourceRecordSyncDTO(OperationEnum.UPDATE, ResourceTypeEnum.QUIZ.name(),
                            insertOrUpdateStatistical.getId(), task.getEmigratedId(), currentUserId, 1, currentUserId,
                            new Date(), currentUserId, new Date())));
        }
    }
}
