package com.wunding.learn.promotedgame.service.client.dto;

import com.wunding.learn.common.bean.BasePageQuery;
import io.swagger.v3.oas.annotations.Parameter;
import java.io.Serializable;
import java.util.Date;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.format.annotation.DateTimeFormat.ISO;

/**
 * 班主任获取批阅列表
 *
 * <AUTHOR> href="mailto:<EMAIL>">yuxueyi</a>
 * @title EmigratedHomeWorkListAo
 * @date 2022/2/24 11:17
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EmigratedHomeWorkListQueryDTO extends BasePageQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @Parameter(description = "开始时间", required = true)
    @DateTimeFormat(iso = ISO.DATE_TIME)
    @NotNull(message = "开始时间不能为空")
    private Date startTime;


    @Parameter(description = "结束时间", required = true)
    @DateTimeFormat(iso = ISO.DATE_TIME)
    @NotNull(message = "结束时间不能为空")
    private Date endTime;

    @Parameter(description = "作业名称搜索关键字")
    private String keyWord;

}
