package com.wunding.learn.forum.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.forum.service.admin.dto.PostReportDTO;
import com.wunding.learn.forum.service.admin.query.PostReportQuery;
import com.wunding.learn.forum.service.client.dto.PostReportApiDTO;
import com.wunding.learn.forum.service.mapper.PostReportMapper;
import com.wunding.learn.forum.service.model.PostReport;
import com.wunding.learn.forum.service.service.IPostReportService;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.service.UserFeign;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p> 帖子/回帖举报表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">吴光荣</a>
 * @since 2022-06-13
 */
@Slf4j
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Service("postReportService")
public class PostReportServiceImpl extends ServiceImpl<PostReportMapper, PostReport> implements IPostReportService {

    private final UserFeign userFeign;

    @Override
    public PageInfo<PostReportDTO> getPostReport(PostReportQuery postReportQuery) {
        // 按举报人查询
        String createByName = postReportQuery.getCreateByName();
        if (StringUtils.isNotEmpty(createByName)) {
            String[] userIdArray = createByName.split(",");
            postReportQuery.setUserIdList(Arrays.asList(userIdArray));
        }
        PageInfo<PostReportDTO> pageData = PageMethod.startPage(postReportQuery.getPageNo(),
                postReportQuery.getPageSize())
            .doSelectPageInfo(() -> getBaseMapper().selectPostReport(postReportQuery));

        List<PostReportDTO> pageDataList = pageData.getList();
        if (pageDataList.isEmpty()) {
            return pageData;
        }

        List<String> userIdList = pageDataList.stream().map(PostReportDTO::getReportBy)
            .collect(Collectors.toList());

        Map<String, String> fullNameMap = userFeign.getUseListByIds(userIdList).stream()
            .collect(Collectors.toMap(UserDTO::getId, UserDTO::getFullName));
        pageDataList.forEach(item -> item.setFullName(fullNameMap.get(item.getReportBy())));
        return pageData;
    }


    @Override
    public void savePostReport(PostReportApiDTO postReportApiDTO) {
        PostReport postReport = new PostReport();
        BeanUtils.copyProperties(postReportApiDTO, postReport);
        postReport.setPostReportId(StringUtil.newId());
        save(postReport);
    }
}
