package com.wunding.learn.forum.service.service.impl;

import static com.wunding.learn.common.util.string.StringUtil.newId;
import static com.wunding.learn.forum.service.model.Post.POST_TYPE_VOTE;
import com.alibaba.ttl.threadpool.TtlExecutors;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.wunding.learn.common.bean.Result;
import com.wunding.learn.common.constant.other.CommonConstants;
import com.wunding.learn.common.constant.redis.CacheRedisDbConst;
import com.wunding.learn.common.context.user.LoginInfo;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.ResourceTypeEnum;
import com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum;
import com.wunding.learn.common.enums.excitation.ExcitationEventEnum;
import com.wunding.learn.common.enums.excitation.ExcitationTypeEnum;
import com.wunding.learn.common.enums.excitation.OtherEventCategoryEnum;
import com.wunding.learn.common.enums.market.HeadContentRuleEnum;
import com.wunding.learn.common.enums.other.ContentRuleEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.other.RouterVisitEnum;
import com.wunding.learn.common.enums.other.SystemConfigCodeEnum;
import com.wunding.learn.common.enums.viewlimit.NewViewLimitTypeEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.mq.dto.ExcitationMQEventDTO;
import com.wunding.learn.common.mq.dto.ExcitationTradeDTO;
import com.wunding.learn.common.mq.dto.ResourceConfigInitDTO;
import com.wunding.learn.common.mq.dto.SearchKeyDetailDTO;
import com.wunding.learn.common.mq.event.HomeRouterVisitEvent;
import com.wunding.learn.common.mq.event.ResourceInteractEvent.ResourceInteractEventRoutingKeyConstants;
import com.wunding.learn.common.mq.event.SearchKeyDetailEvent;
import com.wunding.learn.common.mq.event.excitation.ExcitationInitMqEvent;
import com.wunding.learn.common.mq.event.excitation.ExcitationMQEvent;
import com.wunding.learn.common.mq.event.excitation.ExcitationTradeEvent;
import com.wunding.learn.common.mq.service.MqProducer;
import com.wunding.learn.common.mybatis.service.impl.BaseServiceImpl;
import com.wunding.learn.common.util.bean.CopyNotNullObjectUtil;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.excel.ExcelCheckMessage;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.util.redis.RedisDBUtil;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.excitation.api.service.ExcitationFeign;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.constant.ImageBizType;
import com.wunding.learn.file.api.dto.AbstractExportDataDTO;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.file.api.dto.ImportDataDTO;
import com.wunding.learn.file.api.dto.NamePath;
import com.wunding.learn.file.api.dto.SaveOrUpdatePicListDTO;
import com.wunding.learn.file.api.service.FileFeign;
import com.wunding.learn.file.api.service.ImportDataFeign;
import com.wunding.learn.forum.api.dto.MarchPostInfoDTO.PostNamePath;
import com.wunding.learn.forum.api.dto.MarchSavePostDTO;
import com.wunding.learn.forum.api.dto.PostDelStatusDTO;
import com.wunding.learn.forum.api.dto.PostListDTO;
import com.wunding.learn.forum.api.query.PostQuery;
import com.wunding.learn.forum.service.admin.dto.ImportResultDTO;
import com.wunding.learn.forum.service.admin.dto.PostDTO;
import com.wunding.learn.forum.service.admin.dto.PostEssenceStatusDTO;
import com.wunding.learn.forum.service.admin.dto.PostInfoDTO;
import com.wunding.learn.forum.service.admin.dto.PostTopStatusDTO;
import com.wunding.learn.forum.service.admin.dto.PostVoteOptionDTO;
import com.wunding.learn.forum.service.admin.dto.SavePostDTO;
import com.wunding.learn.forum.service.client.dto.BestPostCommentDTO;
import com.wunding.learn.forum.service.client.dto.ForumPostApiDTO;
import com.wunding.learn.forum.service.client.dto.ForumPostDetailDTO;
import com.wunding.learn.forum.service.client.dto.PostAttentionResultDTO;
import com.wunding.learn.forum.service.client.dto.PostEssenceDTO;
import com.wunding.learn.forum.service.client.dto.PostOptionApiDTO;
import com.wunding.learn.forum.service.client.dto.PostPollDto;
import com.wunding.learn.forum.service.client.dto.PostTopDTO;
import com.wunding.learn.forum.service.client.dto.PostVoteResultDto;
import com.wunding.learn.forum.service.client.dto.SavePostApiDTO;
import com.wunding.learn.forum.service.client.dto.UpdatePostDTO;
import com.wunding.learn.forum.service.client.query.ForumMyPostApiQuery;
import com.wunding.learn.forum.service.client.query.ForumPostApiQuery;
import com.wunding.learn.forum.service.client.query.ForumPostSearchQuery;
import com.wunding.learn.forum.service.client.query.ForumSearchWordApiQuery;
import com.wunding.learn.forum.service.client.query.PostHomePageQuery;
import com.wunding.learn.forum.service.client.query.TrainForumPostApiQuery;
import com.wunding.learn.forum.service.dao.PostDao;
import com.wunding.learn.forum.service.enums.BusinessExceptionEnum;
import com.wunding.learn.forum.service.enums.PushEventEnum;
import com.wunding.learn.forum.service.event.ForumAttentionEvent;
import com.wunding.learn.forum.service.event.ForumLikeEvent;
import com.wunding.learn.forum.service.event.ForumPollEvent;
import com.wunding.learn.forum.service.event.ForumPostEvent;
import com.wunding.learn.forum.service.event.ForumViewEvent;
import com.wunding.learn.forum.service.imports.ForumPostExcelTemplate;
import com.wunding.learn.forum.service.mapper.PostMapper;
import com.wunding.learn.forum.service.model.ForumSection;
import com.wunding.learn.forum.service.model.Post;
import com.wunding.learn.forum.service.model.PostAttention;
import com.wunding.learn.forum.service.model.PostBanUser;
import com.wunding.learn.forum.service.model.PostComment;
import com.wunding.learn.forum.service.model.PostPoll;
import com.wunding.learn.forum.service.model.PostSpecialist;
import com.wunding.learn.forum.service.model.PostView;
import com.wunding.learn.forum.service.model.PostVote;
import com.wunding.learn.forum.service.model.PostVoteOption;
import com.wunding.learn.forum.service.service.IForumSectionService;
import com.wunding.learn.forum.service.service.IPostAttentionService;
import com.wunding.learn.forum.service.service.IPostBanUserService;
import com.wunding.learn.forum.service.service.IPostCashService;
import com.wunding.learn.forum.service.service.IPostCommentService;
import com.wunding.learn.forum.service.service.IPostPollService;
import com.wunding.learn.forum.service.service.IPostService;
import com.wunding.learn.forum.service.service.IPostSpecialistService;
import com.wunding.learn.forum.service.service.IPostViewService;
import com.wunding.learn.forum.service.service.IPostVoteOptionService;
import com.wunding.learn.forum.service.service.IPostVoteService;
import com.wunding.learn.lecturer.api.dto.LecturerDTO;
import com.wunding.learn.lecturer.api.service.LecturerFeign;
import com.wunding.learn.push.api.dto.SavePushManageDTO;
import com.wunding.learn.push.api.service.PushFeign;
import com.wunding.learn.user.api.dto.OrgDTO;
import com.wunding.learn.user.api.dto.OrgShowDTO;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.dto.UserRankBaseInfoDTO;
import com.wunding.learn.user.api.dto.viewlimit.ViewLimitMainSaveDTO;
import com.wunding.learn.user.api.dto.viewlimit.ViewLimitProgrammeInfoDTO;
import com.wunding.learn.user.api.enums.DictEnum;
import com.wunding.learn.user.api.service.OrgFeign;
import com.wunding.learn.user.api.service.ParaFeign;
import com.wunding.learn.user.api.service.RouterFeign;
import com.wunding.learn.user.api.service.SysSensitiveWordFeign;
import com.wunding.learn.user.api.service.UserFeign;
import com.wunding.learn.user.api.service.ViewLimitFeign;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

/**
 * <p> 帖子表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">吴光荣</a>
 * @since 2022-06-13
 */
@Slf4j
@Service("postService")
public class PostServiceImpl extends BaseServiceImpl<PostMapper, Post> implements IPostService {

    public static final String FORUM_PUSH_TYPE = "forum";
    public static final String PROJECT_FORUM_PUSH_TYPE = "project_forum";
    private static final String POST_TYPE = "postType";
    private static final String IS_CLIENT = "isClient";
    private static final String IS_DEL = "isDel";
    private static final Integer MAX_SIZE = 100000;
    private static final Integer PAGE_SIZE = 1000;
    /**
     * 投票贴
     */
    private static final Integer IS_VOTE = 2;
    /**
     * 主题贴
     */
    private static final Integer TOPIC_POST = 1;
    /**
     * 客户端
     */
    private static final Integer CLIENT = 1;
    /**
     * 管理端
     */
    private static final Integer ADMIN = 2;
    /**
     * 百分之百
     */
    private static final Integer ONE_HUNDRED_PERCENT = 100;

    private static final ExecutorService IMPORT_DATA_THREAD_POOL;

    static {
        IMPORT_DATA_THREAD_POOL = TtlExecutors.getTtlExecutorService(
            new ThreadPoolExecutor(2, 2, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(10),
                new CustomizableThreadFactory("import-forum-post-exec-pool-")));
    }

    @Resource
    private PostMapper postMapper;
    @Resource
    private OrgFeign orgFeign;
    @Resource
    private UserFeign userFeign;
    @Resource
    private FileFeign fileFeign;
    @Resource
    private ImportDataFeign importDataFeign;
    @Resource
    private IForumSectionService forumSectionService;
    @Resource
    @Lazy
    private IPostCommentService postCommentService;
    @Resource
    private IPostVoteOptionService postVoteOptionService;
    @Resource
    private IPostViewService postViewService;
    @Resource
    private IPostAttentionService postAttentionService;
    @Resource
    private LecturerFeign lecturerFeign;
    @Resource
    private IPostVoteService postVoteService;
    @Resource
    private IPostPollService postPollService;
    @Resource
    private IPostSpecialistService postSpecialistService;
    @Resource
    private IPostBanUserService postBanUserService;
    @Resource
    private ExportComponent exportComponent;
    @Resource
    private ParaFeign paraFeign;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private PushFeign pushFeign;
    @Resource
    private ExcitationFeign excitationFeign;
    @Resource
    private IPostCashService postCashService;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private ViewLimitFeign viewLimitFeign;
    @Resource(name = "postDao")
    private PostDao postDao;
    @Resource
    private Executor commonTaskThreadPool;
    @Resource
    SysSensitiveWordFeign sysSensitiveWordFeign;
    @Resource
    private RouterFeign routerFeign;


    @Override
    public void batchUpdatePostFavourite() {
        String dictId = DictEnum.ForumFavorite.toString();
        String paraValue = paraFeign.getParaValue(dictId);
        if (StringUtils.isNotEmpty(paraValue)) {
            Integer dictValue = Integer.parseInt(paraValue);
            for (int i = 0; i < MAX_SIZE; i++) {
                //每次最多处理1000条
                List<PostDTO> postDTOList = postMapper.selectPostCommentNumList(i * PAGE_SIZE, PAGE_SIZE);
                if (CollectionUtils.isEmpty(postDTOList)) {
                    break;
                }
                List<Post> updatePostList = new ArrayList<>();

                setIsFavourite(dictValue, postDTOList, updatePostList);

                if (!CollectionUtils.isEmpty(updatePostList)) {
                    String msg = String.format("【刷新帖子热门状态 - 更新 %s 个帖子】", updatePostList.size());
                    postDao.batchUpdatePostFavourite(updatePostList, msg);
                }
            }
        }

    }

    private void setIsFavourite(Integer dictValue, List<PostDTO> postDTOList,
        List<Post> updatePostList) {
        for (PostDTO postDTO : postDTOList) {
            // 回复数超过且热门值为启用转态设置的数就设置热门
            Post post = new Post();
            if (postDTO.getCommentNum() >= dictValue) {
                // 如果是非热门，回复数达到阈值，则将其置为热门
                if (postDTO.getIsFavourite() == 0) {
                    postDTO.setIsFavourite(1);
                    BeanUtils.copyProperties(postDTO, post);
                    updatePostList.add(post);
                }
            } else {
                // 如果已经是热门，但是回复数不足，则将其置为非热门
                if (postDTO.getIsFavourite() == 1) {
                    postDTO.setIsFavourite(0);
                    BeanUtils.copyProperties(postDTO, post);
                    updatePostList.add(post);
                }
            }
        }
    }

    @Override
    public PageInfo<PostListDTO> findPostListByPage(PostQuery postQuery) {
        //查询用户的管辖范围，根据用户的管辖范围和输入条件来查询数据
        Set<String> managerAreaOrgIds = orgFeign.findUserManageAreaLevelPath(UserThreadContext.getUserId());
        postQuery.setManagerAreaOrgIds(managerAreaOrgIds);
        postQuery.setCurrentOrgId(UserThreadContext.getOrgId());
        postQuery.setCurrentUserId(UserThreadContext.getUserId());

        if (StringUtils.isNotEmpty(postQuery.getOrgId())) {
            // 查询用户服务查询该组织及其以下所有的组织id
            Set<String> childrenId = orgFeign.getChildrenId(postQuery.getOrgId());
            postQuery.setChildOrgIdList(childrenId);
        }

        PageInfo<PostListDTO> objectPageInfo = PageMethod.startPage(postQuery.getPageNo(), postQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.selectPostListByPage(postQuery));

        List<PostListDTO> postList = objectPageInfo.getList();
        Set<String> orgIdSet = postList.stream().map(PostListDTO::getOrgId).collect(Collectors.toSet());
        Map<String, OrgShowDTO> orgShowDTOMap = orgFeign.getOrgShowDTO(orgIdSet);
        postList.forEach(postListDTO -> {

            if (StringUtils.isNotEmpty(postListDTO.getCreateBy())) {
                //查询作者名称
                UserDTO userDTO = userFeign.getUserById(postListDTO.getCreateBy());
                postListDTO.setCreateFullName(null != userDTO ? userDTO.getFullName() : null);
            }

            if (StringUtils.isNotEmpty(postListDTO.getOrgId())) {
                //查询创建组织名称
                Optional.ofNullable(orgShowDTOMap.get(postListDTO.getOrgId())).ifPresent(orgShowDTO -> {
                    postListDTO.setOrgLevelPathName(orgShowDTO.getLevelPathName());
                    postListDTO.setOrgName(orgShowDTO.getOrgShortName());
                    postListDTO.setOrgPath(orgShowDTO.getLevelPathName());
                });
            }

        });

        //路由访问埋点
        mqProducer.sendMsg(
            new HomeRouterVisitEvent(RouterVisitEnum.TopicManagement.getRouterId(), UserThreadContext.getUserId(),
                RouterVisitEnum.TopicManagement.getName()));

        return objectPageInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdatePost(SavePostDTO savePostDTO) {
        log.info("----saveOrUpdatePost---savePostDTO:" + savePostDTO.toString());
        Post post = new Post();
        String postId = savePostDTO.getPostId();
        BeanUtils.copyProperties(savePostDTO, post);

        // 学习项目兼容设置公共话题还是非公共
        if (StringUtils.isNotEmpty(savePostDTO.getProjectId())) {
            post.setIsPublic(StringUtils.isEmpty(savePostDTO.getTeamId()) ? 1 : 0);
        }

        if (StringUtils.isEmpty(postId)) {
            //创建操作
            postId = newId();
            post.setPostIntro(StringUtil.getBriefs(post.getPostContent()));
            post.setPostId(postId);
            post.setOrgId(UserThreadContext.getOrgId());
            post.setIsClient(ADMIN);
            post.setIsAvailable(1);
            postDao.savePost(post);

            // 发表话题激励
            ForumSection section = forumSectionService.getById(post.getSectionId());
            if (Objects.nonNull(section)) {
                mqProducer.sendMsg(new ExcitationMQEvent(
                    new ExcitationMQEventDTO().setEventId(ExcitationEventEnum.makeTopic.name())
                        .setTargetId(section.getSectionId())
                        .setTargetName(section.getSectionName() + "-" + post.getSectionId())
                        .setUserId(UserThreadContext.getUserId())));
            }
        } else {
            postDao.updatePost(post);
        }

        // fileFeign调用
        //处理图片
        List<NamePath> namePathList = fileFeign.getImageFileNamePaths(postId, ImageBizType.POST_IMAGE.name());

        List<NamePath> imgPathList = savePostDTO.getImgPathList();
        if (!CollectionUtils.isEmpty(imgPathList)) {
            // 删除原来的图片
            fileFeign.deleteImageByBizIdAndBizType(postId, ImageBizType.POST_IMAGE.name());
            // 保存图片
            fileFeign.saveImages(postId, ImageBizType.POST_IMAGE.name(), imgPathList);
            post.setIsImg(1);
        } else {
            if (!CollectionUtils.isEmpty(namePathList)) {
                //如果更新保存没有图片，但是有老图片，则完全删除旧图片
                List<String> imageViewIdList = new ArrayList<>();
                for (NamePath namePath : namePathList) {
                    imageViewIdList.add(namePath.getId());
                }
                fileFeign.deleteImageByImagesIds(imageViewIdList);
            }
            post.setIsImg(0);
        }
        updateById(post);

        //处理投票选项
        handlePostVoteOption(post, savePostDTO.getVoteOptionList());
        postCashService.removeForumDetailById(savePostDTO.getPostId());
    }

    /**
     * 处理帖子投票选项
     *
     * @param post           帖子
     * @param voteOptionList 投票选项
     */
    private void handlePostVoteOption(Post post, List<String> voteOptionList) {
        if (IS_VOTE.equals(post.getPostType())) {
            // 投票贴选项是否有重复 校验
            long count = voteOptionList.stream().distinct().count();
            if (count < voteOptionList.size()) {
                throw new BusinessException(BusinessExceptionEnum.VOTE_OPTION_HAS_REPEAT);
            }
            //增量添加投票选项,先删再加会清空投票记录
            String postId = post.getPostId();

            if (!CollectionUtils.isEmpty(voteOptionList)) {
                // 查出已经存在的投票选项
                List<PostVoteOption> existPostVoteOption = postVoteOptionService.lambdaQuery()
                    .select(PostVoteOption::getPostVoteOptionId, PostVoteOption::getOptionName,
                        PostVoteOption::getSortNo).eq(PostVoteOption::getPostId, postId).list();
                Set<String> optionNameSet = existPostVoteOption.stream().map(PostVoteOption::getOptionName)
                    .collect(Collectors.toSet());

                // 取出最大排序,逐渐往上加
                int sortNo = existPostVoteOption.stream().map(PostVoteOption::getSortNo).max(Integer::compareTo)
                    .orElse(0);

                // 需要添加的选项
                List<String> addOptionNameList = voteOptionList.stream()
                    .filter(optionName -> !optionNameSet.contains(optionName)).collect(Collectors.toList());
                // 收集添加的选项
                List<PostVoteOption> postVoteOptionList = new ArrayList<>();
                for (String s : addOptionNameList) {
                    PostVoteOption voteOption = new PostVoteOption();
                    voteOption.setPostVoteOptionId(newId());
                    voteOption.setPostId(postId);
                    voteOption.setOptionName(s);
                    voteOption.setSortNo(sortNo++);
                    postVoteOptionList.add(voteOption);
                }

                // 现在选项只有增没有减逻辑，这里逻辑不会走
                // 收集删除的选项
                List<String> removeIds = existPostVoteOption.stream()
                    .filter(item -> !voteOptionList.contains(item.getOptionName()))
                    .map(PostVoteOption::getPostVoteOptionId).collect(Collectors.toList());

                if (!CollectionUtils.isEmpty(removeIds)) {
                    postVoteOptionService.removeBatchByIds(removeIds);
                }

                if (!CollectionUtils.isEmpty(postVoteOptionList)) {
                    postVoteOptionService.saveBatch(postVoteOptionList);
                }
            }
        }
    }

    @Override
    public SavePostDTO findPostDetailByPostId(String postId) {
        SavePostDTO savePostDTO = new SavePostDTO();
        // is_del的也查出来
        Post post = baseMapper.selectPostById(postId);
        BeanUtils.copyProperties(post, savePostDTO);
        // 返回归属部门名称
        savePostDTO.setOrgName(orgFeign.getById(savePostDTO.getOrgId()).getOrgName());
        if (post.getPostType().equals(POST_TYPE_VOTE)) {
            List<PostVoteOption> postVoteOptionList = postVoteOptionService.lambdaQuery()
                .eq(PostVoteOption::getPostId, postId).orderByAsc(PostVoteOption::getSortNo).list();

            List<String> voteOptionList = postVoteOptionList.stream().map(PostVoteOption::getOptionName)
                .collect(Collectors.toList());
            savePostDTO.setVoteOptionList(voteOptionList);
        }

        List<NamePath> namePathList = fileFeign.getImageFileNamePaths(postId, ImageBizType.POST_IMAGE.name());
        if (!CollectionUtils.isEmpty(namePathList)) {
            savePostDTO.setImgPathList(namePathList);
        }

        return savePostDTO;
    }

    @Override
    public PostInfoDTO getPostDetailInfo(String id) {
        // 非投票贴逻辑
        PostInfoDTO postInfoDTO = getBaseMapper().selectPostInfo(id);
        if (postInfoDTO == null) {
            return null;
        }
        String userId = UserThreadContext.getUserId();
        // 获取用户头像
        postInfoDTO.setCurrentUserImg(fileFeign.getImageUrl(userId, ImageBizType.Avatar.name()));
        // 获取帖子图片
        postInfoDTO.setImgPathList(fileFeign.getImageFileNamePaths(id, ImageBizType.POST_IMAGE.name()));
        // 获取发帖用户名称
        postInfoDTO.setUserName(userFeign.getUserFullNameById(postInfoDTO.getCreateBy()));
        // 有人投票且是投票贴则走投票逻辑
        if (IS_VOTE.equals(postInfoDTO.getPostType())) {
            List<PostVoteOptionDTO> postVoteOptionDTO = postVoteOptionService.getPostVoteOptionDTO(id);
            // 最大取余法计算百分比
            calculatePercent(postInfoDTO.getParticipant(), postVoteOptionDTO);
            postInfoDTO.setPostVoteOption(postVoteOptionDTO);
        }
        return postInfoDTO;
    }

    /**
     * 最大余额法，精度为整数
     */
    private <T extends PostVoteOptionDTO> void calculatePercent(Integer sum, List<T> list) {
        // 没有人投过票直接返回
        if (sum == null || sum == 0) {
            // 给百分比赋初始值
            list.forEach(postVoteOption -> postVoteOption.setPercent(0));
            return;
        }
        for (PostVoteOptionDTO postVoteOptionDTO : list) {
            // 乘以 100 转换成百分号表示一样
            BigDecimal value = BigDecimal.valueOf(postVoteOptionDTO.getOptionNumber() * 100 / sum.doubleValue());
            postVoteOptionDTO.setPercent(value.intValue());
            postVoteOptionDTO.setPoint(value.remainder(BigDecimal.ONE).doubleValue());
        }
        //求和：当前各项百分比合计。由于我们舍弃了小数位，所以该合计只会小于等于100
        int curSum = list.stream().mapToInt(PostVoteOptionDTO::getPercent).sum();
        while (curSum < ONE_HUNDRED_PERCENT) {
            //找出小数余额最大的组，对其进行加1
            Optional<T> postVoteOptionDTO = list.stream().max(Comparator.comparingDouble(PostVoteOptionDTO::getPoint));
            postVoteOptionDTO.ifPresent(max -> {
                max.setPercent(max.getPercent() + 1);
                //当前这个数已经加1了，不应该参与下一轮的竞选
                max.setPoint(0.0);
            });
            curSum++;
        }
    }

    @Override
    public boolean delOrRestorePost(PostDelStatusDTO postDelStatusDTO) {
        postDelStatusDTO.setUpdateUser(UserThreadContext.getUserId());
        postCashService.removeForumDetailById(
            org.springframework.util.StringUtils.collectionToDelimitedString(postDelStatusDTO.getPostIdList(), ","));

        // 遍历执行成功次数（需要记录每个帖子的业务日志）
        Integer result = 0;
        for (String postId : postDelStatusDTO.getPostIdList()) {
            // 查询帖子（无视删除状态），用于日志记录
            Post post = baseMapper.selectPostById(postId);

            // 重新封装参数，改批量更新列表为单一对象
            PostDelStatusDTO dto = new PostDelStatusDTO();
            BeanUtils.copyProperties(postDelStatusDTO, dto);
            dto.setPostIdList(Collections.singletonList(postId));

            // 执行帖子删除或恢复，同时记录业务日志
            if (1 == dto.getDelStatus()) {
                result += postDao.delPost(post, dto);
            } else {
                result += postDao.restorePost(post, dto);
            }
        }
        return result > 0;
    }

    @Override
    public boolean topOrCancelPost(PostTopStatusDTO postTopStatusDTO) {
        postCashService.removeForumDetailById(
            org.springframework.util.StringUtils.collectionToDelimitedString(postTopStatusDTO.getPostIdList(), ","));

        List<Post> posts = listByIds(postTopStatusDTO.getPostIdList());
        posts.forEach(e -> {
            e.setIsTop(postTopStatusDTO.getTopStatus());
            postDao.updatePost(e);
        });
        return true;
    }

    @Override
    public boolean presenceOrNot(PostEssenceStatusDTO postEssenceStatusDTO) {
        postCashService.removeForumDetailById(
            org.springframework.util.StringUtils.collectionToDelimitedString(postEssenceStatusDTO.getPostIdList(),
                ","));

        List<Post> posts = listByIds(postEssenceStatusDTO.getPostIdList());

        posts.forEach(e -> {
            e.setIsEssence(postEssenceStatusDTO.getEssenceStatus());
            postDao.updatePost(e);
        });

        return true;
    }

    @Override
    @SuppressWarnings("unchecked")
    public Future<ImportResultDTO> createExcel(String excelFilePath, String projectId, Integer isTrain) {
        LoginInfo loginInfo = UserThreadContext.getLoginInfo();
        assert IMPORT_DATA_THREAD_POOL != null;
        return IMPORT_DATA_THREAD_POOL.submit(() -> {
            ImportResultDTO importResultDTO = new ImportResultDTO();
            importResultDTO.setIsSuccess(false);
            long beginTime = System.currentTimeMillis();
            ImportDataDTO importData = importDataFeign.getImportData(excelFilePath);
            log.info("获取导入的数据耗时：{}，共计{}条数据", System.currentTimeMillis() - beginTime,
                importData.getRowCount());
            String[][] excel = importData.getExcel();
            beginTime = System.currentTimeMillis();

            //查询用户的管辖范围，根据用户的管辖范围和输入条件来查询数据
            log.info("用户信息: " + loginInfo.getUserId());
            Set<String> managerAreaOrgIds = orgFeign.getUserManageAreaOrgId(loginInfo.getUserId());
            log.info("管辖范围: " + managerAreaOrgIds);

            ExcelCheckMessage excelCheckMessage = new ForumPostExcelTemplate(forumSectionService, projectId,
                isTrain, managerAreaOrgIds).check(excel);
            log.info("ForumDetailExcelTemplate check 耗时{}", System.currentTimeMillis() - beginTime);

            if (CollectionUtils.isEmpty(excelCheckMessage.getMessage())) {
                beginTime = System.currentTimeMillis();
                List<Post> postList = (List<Post>) excelCheckMessage.getObjects();
                for (Post item : postList) {
                    item.setPostId(newId());
                    item.setOrgId(loginInfo.getOrgId());
                    item.setPostType(TOPIC_POST);
                    item.setIsClient(ADMIN);
                    item.setCreateBy(loginInfo.getUserId());
                    item.setUpdateBy(loginInfo.getUserId());
                }
                // 遍历插入Post
                for (Post post : postList) {
                    // 添加帖子
                    postDao.savePost(post);
                    // 初始化帖子的激励配置
                    mqProducer.sendMsg(new ExcitationInitMqEvent(
                        new ResourceConfigInitDTO().setResourceId(post.getPostId())
                            .setResourceType(ExcitationEventCategoryEnum.TOPIC.getCode())));
                }
                log.info("savePostDetailDataList 耗时{}", System.currentTimeMillis() - beginTime);
                importResultDTO.setIsSuccess(true);
            }
            importResultDTO.setMsg(JsonUtil.objToJson(excelCheckMessage.getMessage()));
            return importResultDTO;
        });
    }

    @Override
    public PageInfo<ForumPostApiDTO> getPostPageBySectionId(ForumPostApiQuery forumPostApiQuery) {
        return getListByOrderType(forumPostApiQuery);
    }

    @Override
    public PageInfo<ForumPostApiDTO> getTrainHomePageList(TrainForumPostApiQuery trainForumPostApiQuery) {
        String contentRule = trainForumPostApiQuery.getContentRule();
        if (StringUtils.isNotBlank(contentRule)) {
            if (ContentRuleEnum.NEW_TOPIC_NOT_TOP.name().equals(contentRule)) {
                trainForumPostApiQuery.setOrderBy(" p.create_time desc");
            } else if (ContentRuleEnum.NEW_TOPIC_TOP.name().equals(contentRule)) {
                trainForumPostApiQuery.setOrderBy(" p.is_top desc,p.create_time desc");
            } else if (ContentRuleEnum.HOT_TOPIC_COMMENT.name().equals(contentRule)) {
                trainForumPostApiQuery.setOrderBy(" commentNum desc");
            } else if (ContentRuleEnum.HOT_TOPIC_VIEW.name().equals(contentRule)) {
                trainForumPostApiQuery.setOrderBy(" viewNum desc");
            }
        }
        PageInfo<ForumPostApiDTO> result = PageMethod.startPage(trainForumPostApiQuery.getPageNo(),
                trainForumPostApiQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.getTrainHomePageList(trainForumPostApiQuery));
        List<ForumPostApiDTO> forumPostApiDTOList = result.getList();
        if (forumPostApiDTOList.isEmpty()) {
            return result;
        }
        //处理返回的帖子列表
        handlePostResult(forumPostApiDTOList);
        return result;
    }

    @Override
    public PageInfo<ForumPostApiDTO> getMyPostPageBySectionId(ForumMyPostApiQuery forumPostApiQuery) {
        ForumPostApiQuery forumMyPostApiQuery = new ForumPostApiQuery();
        BeanUtils.copyProperties(forumPostApiQuery, forumMyPostApiQuery);
        forumMyPostApiQuery.setCurrentUserId(UserThreadContext.getUserId());
        forumMyPostApiQuery.setCount(true);
        return getListByOrderType(forumMyPostApiQuery);
    }

    @Override
    public ForumPostDetailDTO getPostDetailByPostId(String postId) {
        String curUserId = UserThreadContext.getUserId();
        String key = RedisDBUtil.getFullKey(CacheRedisDbConst.CACHE, "getPostDetailByPostId", postId, curUserId);
        ForumPostDetailDTO redisCash = (ForumPostDetailDTO) redisTemplate.opsForValue().get(key);
        if (redisCash != null) {
            return redisCash;
        }
        ForumPostDetailDTO forumPostDetailDTO = postCashService.getForumDetailById(postId);
        // 提前返回,防止空指针
        if (forumPostDetailDTO == null) {
            return null;
        }
        String postOwner = postCashService.getIsPostOwnerByUserId(postId, curUserId);
        forumPostDetailDTO.setIsPostOwner(postOwner);
        String sectionId = forumPostDetailDTO.getSectionId();
        ForumSection forumSection = forumSectionService.getById(sectionId);
        forumPostDetailDTO.setSectionName(forumSection.getSectionName());
        forumPostDetailDTO.setEnableAnonymous(forumSection.getIsAnonymous());
        Integer currentCommentVoteByPOstIdAndUserId = postVoteService.getCurrentCommentVoteByPOstIdAndUserId(postId,
            curUserId);
        forumPostDetailDTO.setIsStar(
            currentCommentVoteByPOstIdAndUserId != null ? currentCommentVoteByPOstIdAndUserId : 0);
        Integer isAt = postAttentionService.getIsAtByPostIdAndUserId(postId, curUserId);
        forumPostDetailDTO.setIsAttention(isAt != null ? isAt : 0);
        String userId = forumPostDetailDTO.getCreateBy();
        // 匿名贴检测
        if (checkPostAuthor(forumPostDetailDTO)) {
            UserDTO userDTO = userFeign.getUserById(userId);
            forumPostDetailDTO.setCreateFullName(userDTO.getFullName()).setUserAvatar(userDTO.getAvatar());
        }
        OrgDTO orgDTO = orgFeign.getOrgByUserId(userId);
        forumPostDetailDTO.setOrgLevelPathName(Optional.ofNullable(orgDTO).map(OrgDTO::getOrgName).orElse(null));
        // 是投票贴则获取选项
        if (IS_VOTE.equals(forumPostDetailDTO.getPostType())) {
            Integer personCount = postPollService.getPersonCountByPostId(postId);
            List<PostOptionApiDTO> postVoteOptionList = postVoteOptionService.getPostVoteOptionList(postId);
            List<String> optionIds = postVoteOptionList.stream().map(PostOptionApiDTO::getPostVoteOptionId)
                .collect(Collectors.toList());
            Map<String, Integer> postOptionPollMapByPostId = postPollService.getPollByPostId(optionIds);
            postVoteOptionList.forEach(dto -> dto.setOptionNumber(
                postOptionPollMapByPostId.get(dto.getPostVoteOptionId()) != null ? postOptionPollMapByPostId.get(
                    dto.getPostVoteOptionId()) : 0));
            // 计算投票
            calculatePercent(personCount, postVoteOptionList);
            forumPostDetailDTO.setPersonCount(personCount);
            forumPostDetailDTO.setPollList(postVoteOptionList);
            // 是否已经投过票
            boolean isPolled = postVoteOptionList.stream()
                .anyMatch(postOptionApiDTO -> postOptionApiDTO.getIsSelected() == 1);
            forumPostDetailDTO.setIsPolled(isPolled ? 1 : 0);
        }
        // 获取帖子图片
        forumPostDetailDTO.setImgPath(fileFeign.getImageFileNamePaths(postId, ImageBizType.POST_IMAGE.name()));

        // 清除帖子未读状态
        if (curUserId.equals(forumPostDetailDTO.getCreateBy())) {
            postCashService.updateIsUnreadByPostId(postId);
        }
        // 增加帖子浏览记录
        boolean exists = postViewService.lambdaQuery().eq(PostView::getPostId, postId)
            .eq(PostView::getViewBy, curUserId).exists();
        if (exists) {
            postCashService.updatePostViewInfo(postId);
        } else {
            PostView postView = new PostView();
            postView.setViewId(newId());
            postView.setPostId(postId);
            postView.setViewBy(curUserId);
            postView.setOrgId(UserThreadContext.getOrgId());
            postView.setViewTime(new Date());
            postCashService.savePostView(postView);
        }
        // 刷新浏览数,浏览数量只要该用户进一次就刷新累加,同一个用户可以多次
        mqProducer.sendMsg(new ForumViewEvent(postId));
        // 讲师表信息
        LecturerDTO lecturerByUserId = lecturerFeign.getLecturerByUserId(forumPostDetailDTO.getCreateBy());
        if (lecturerByUserId != null) {
            forumPostDetailDTO.setGrade(lecturerByUserId.getLevelName())
                .setCategory(lecturerByUserId.getCategoryName());
        }
        redisTemplate.opsForValue().set(key, forumPostDetailDTO, 300, TimeUnit.SECONDS);
        redisTemplate.expire(key, 300, TimeUnit.SECONDS);
        return forumPostDetailDTO;

    }

    @Override
    public PageInfo<ForumPostApiDTO> searchKeyWordPostPageBySectionId(ForumSearchWordApiQuery forumPostApiQuery) {
        // 关键字搜索一下用户Id
        forumPostApiQuery.setCurrentUserId(UserThreadContext.getUserId());
        ForumPostApiQuery postApiQuery = new ForumPostApiQuery();
        BeanUtils.copyProperties(forumPostApiQuery, postApiQuery);
        String searchKeyWord = forumPostApiQuery.getSearchKeyWord();
        if (StringUtils.isNotEmpty(searchKeyWord)) {
            List<String> userIdList = userFeign.getUserIdByLoginNameOrFullName(searchKeyWord, 0);
            if (!userIdList.isEmpty()) {
                postApiQuery.setUserIdList(userIdList).setSectionId(forumPostApiQuery.getSectionId());
            }
        }
        postApiQuery.setCount(true);
        return getPostPageBySectionId(postApiQuery);
    }

    /**
     * 置顶排序查询话题信息
     *
     * @param forumPostApiQuery 论坛帖子api查询
     * @return {@link PageInfo}<{@link ForumPostApiDTO}>
     */

    private PageInfo<ForumPostApiDTO> getListByOrderType(ForumPostApiQuery forumPostApiQuery) {
        //默认校验下发
        if(null == forumPostApiQuery.getIsCheckLimit()){
            forumPostApiQuery.setIsCheckLimit(true);
        }
        PageInfo<ForumPostApiDTO> pageInfo = PageMethod.startPage(forumPostApiQuery.getPageNo(),
                forumPostApiQuery.getPageSize(), forumPostApiQuery.isCount())
            .doSelectPageInfo(() -> baseMapper.selectPostListBySectionId(forumPostApiQuery));
        List<ForumPostApiDTO> unCashList = new ArrayList<>();
        List<ForumPostApiDTO> list = pageInfo.getList();
        if (CollectionUtils.isEmpty(list)) {
            return pageInfo;
        }
        handleList(forumPostApiQuery, unCashList, list);
        if (CollectionUtils.isEmpty(unCashList)) {
            return pageInfo;
        }
        List<String> postIds = unCashList.stream().map(ForumPostApiDTO::getPostId).collect(Collectors.toList());
        Map<String, Integer> map = postVoteService.getCurrentCommentVoteMapByPostIdAndUserId(postIds,
            forumPostApiQuery.getCurrentUserId());
        Map<String, Integer> isAtMap = postAttentionService.getIsAtMapByPostIdAndUserId(postIds,
            forumPostApiQuery.getCurrentUserId());
        Map<String, ForumPostApiDTO> forumPostApiDTOMap = postCashService.getCashMapById(postIds);
        List<String> userIdList = new ArrayList<>();
        for (Map.Entry<String, ForumPostApiDTO> entry : forumPostApiDTOMap.entrySet()) {
            ForumPostApiDTO forumPostApiDTO = entry.getValue();
            userIdList.add(forumPostApiDTO.getCreateBy());
        }
        Map<String, UserDTO> userNameMapByIds = userFeign.getUserNameMapByIds(userIdList);
        Map<String, LecturerDTO> lecturerMapByUserIds = lecturerFeign.getLecturerMapByUserIds(userIdList);
        List<NamePath> imageFileNamePathsByBizIds = fileFeign.getImageFileNamePathsByBizIds(postIds,
            ImageBizType.POST_IMAGE.name());
        Map<String, List<NamePath>> finalNamePathListMap = Collections.emptyMap();
        if (!CollectionUtils.isEmpty(postIds)) {
            finalNamePathListMap = imageFileNamePathsByBizIds.stream()
                .collect(Collectors.groupingBy(NamePath::getCategoryId));
        }
        Map<String, List<NamePath>> finalNamePathListMap1 = finalNamePathListMap;
        unCashList.forEach(item -> {
            String key = RedisDBUtil.getFullKey(CacheRedisDbConst.CACHE, "handlePostResult", item.getPostId(),
                forumPostApiQuery.getCurrentUserId());
            ForumPostApiDTO cashById = forumPostApiDTOMap.get(item.getPostId());
            CopyNotNullObjectUtil.copyProperties(cashById, item);
            item.setIsStar(map.get(item.getPostId()) != null ? map.get(item.getPostId()) : 0);
            item.setIsAttention(isAtMap.get(item.getPostId()) != null ? isAtMap.get(item.getPostId()) : 0);
            if (checkPostAuthor(item)) {
                UserDTO userDTO = userNameMapByIds.get(item.getCreateBy());
                if (userDTO != null) {
                    item.setCreateFullName(userDTO.getFullName());
                    item.setUserAvatar(userDTO.getAvatar());
                }
            }
            handleItem(lecturerMapByUserIds, finalNamePathListMap1, item);
            redisTemplate.opsForValue().set(key, item);
            redisTemplate.expire(key, 300, TimeUnit.SECONDS);
        });
        Map<String, ForumPostApiDTO> collect = unCashList.stream()
            .collect(Collectors.toMap(ForumPostApiDTO::getPostId, dto -> dto, (key1, key2) -> key1));
        setList(list, collect);
        pageInfo.setIsLastPage(forumPostApiQuery.getPageSize() != list.size());
        return pageInfo;
    }

    private void handleItem(Map<String, LecturerDTO> lecturerMapByUserIds,
        Map<String, List<NamePath>> finalNamePathListMap1, ForumPostApiDTO item) {
        // (简介为空)截取简介
        if (StringUtils.isNotBlank(item.getPostContent())) {
            item.setPostIntro(StringUtil.getBriefs(item.getPostContent()));
        }
        // 图片
        List<NamePath> namePathList = finalNamePathListMap1.getOrDefault(item.getPostId(), Collections.emptyList());
        if (!CollectionUtils.isEmpty(namePathList)) {
            item.setImgPath(namePathList);
        }
        // 讲师表信息
        LecturerDTO lecturerByUserId = lecturerMapByUserIds.get(item.getCreateBy());
        if (lecturerByUserId != null) {
            item.setGrade(lecturerByUserId.getLevelName()).setCategory(lecturerByUserId.getCategoryName());
        }
    }

    private void handleList(ForumPostApiQuery forumPostApiQuery, List<ForumPostApiDTO> unCashlist,
        List<ForumPostApiDTO> list) {
        list.forEach(item -> {
            String key = RedisDBUtil.getFullKey(CacheRedisDbConst.CACHE, "handlePostResult", item.getPostId(),
                forumPostApiQuery.getCurrentUserId());
            ForumPostApiDTO forumPostApiDTO = (ForumPostApiDTO) redisTemplate.opsForValue().get(key);
            if (forumPostApiDTO != null) {
                CopyNotNullObjectUtil.copyProperties(forumPostApiDTO, item);
            } else {
                unCashlist.add(item);
            }
        });
    }

    private void setList(List<ForumPostApiDTO> list, Map<String, ForumPostApiDTO> collect) {
        for (ForumPostApiDTO item : list) {
            ForumPostApiDTO forumPostApiDTO = collect.get(item.getPostId());
            if (forumPostApiDTO != null) {
                BeanUtils.copyProperties(forumPostApiDTO, item);
            }
        }
    }

    @SneakyThrows
    private void handlePostResult(List<ForumPostApiDTO> forumPostApiDTOList) {
        List<String> userIdList = new ArrayList<>(forumPostApiDTOList.size());
        Set<String> postIds = new HashSet<>(forumPostApiDTOList.size());
        for (ForumPostApiDTO forumPostApiDTO : forumPostApiDTOList) {
            userIdList.add(forumPostApiDTO.getCreateBy());
            postIds.add(forumPostApiDTO.getPostId());
        }

        @SuppressWarnings("rawtypes") List<CompletableFuture> completableFutureList = new ArrayList<>();
        CompletableFuture<Map<String, List<NamePath>>> namePathListMapFuture = null;

        if (!CollectionUtils.isEmpty(postIds)) {
            namePathListMapFuture = CompletableFuture.supplyAsync(
                    () -> fileFeign.getImageFileNamePathsByBizIds(postIds, ImageBizType.POST_IMAGE.name()),
                    commonTaskThreadPool
                )
                .thenApply(
                    imageFileNamePathsByBizIds ->
                        imageFileNamePathsByBizIds.stream().collect(Collectors.groupingBy(NamePath::getCategoryId))
                );

            completableFutureList.add(namePathListMapFuture);
        }

        CompletableFuture<Map<String, UserDTO>> userNameMapByIdsFuture = CompletableFuture.supplyAsync(() ->
            userFeign.getUserNameAndAvatarByIds(userIdList), commonTaskThreadPool);
        CompletableFuture<Map<String, LecturerDTO>> lecturerMapByUserIdsFuture = CompletableFuture.supplyAsync(() ->
            lecturerFeign.getLecturerMapByUserIds(userIdList), commonTaskThreadPool);

        completableFutureList.add(userNameMapByIdsFuture);
        completableFutureList.add(lecturerMapByUserIdsFuture);

        //线程阻塞至全部完成
        CompletableFuture.allOf(completableFutureList.toArray(CompletableFuture[]::new)).get(60, TimeUnit.SECONDS);

        Map<String, UserDTO> userNameMapByIds = userNameMapByIdsFuture.get();
        Map<String, LecturerDTO> lecturerMapByUserIds = lecturerMapByUserIdsFuture.get();
        Map<String, List<NamePath>> namePathListMap = new HashMap<>();
        if (Objects.nonNull(namePathListMapFuture)) {
            namePathListMap = namePathListMapFuture.get();
        }

        final Map<String, List<NamePath>> finalNamePathListMap = namePathListMap;

        forumPostApiDTOList.forEach(item -> {
            String userId = item.getCreateBy();
            if (checkPostAuthor(item)) {
                UserDTO userDTO = userNameMapByIds.get(userId);
                if (userDTO != null) {
                    item.setCreateFullName(userDTO.getFullName());
                    item.setUserAvatar(userDTO.getAvatar());
                }
            }
            // (简介为空)截取简介
            if (StringUtils.isNotBlank(item.getPostContent())) {
                item.setPostIntro(StringUtil.getBriefs(item.getPostContent()));
            }
            // 图片
            item.setImgPath(finalNamePathListMap.get(item.getPostId()));
            // 讲师表信息
            LecturerDTO lecturerByUserId = lecturerMapByUserIds.get(userId);
            if (lecturerByUserId != null) {
                item.setGrade(lecturerByUserId.getLevelName()).setCategory(lecturerByUserId.getCategoryName());
            }
        });
    }

    private boolean checkPostAuthor(ForumPostApiDTO forumPostApiDTO) {
        String userId = UserThreadContext.getUserId();
        if (forumPostApiDTO.getIsAnonymous() == 0 || StringUtils.equals(userId, forumPostApiDTO.getCreateBy())) {
            return true;
        }
        // 匿名贴且当前用户不是发帖人
        forumPostApiDTO.setCreateFullName("匿名").setCreateBy("anon").setUserAvatar(null);
        return false;
    }

    @Override
    public void updatePostEssenceStatus(PostEssenceDTO postEssenceDTO) {
        log.info("-----updatePostEssenceStatus---postEssenceDTO:" + postEssenceDTO.toString());
        lambdaUpdate().set(Post::getIsEssence, postEssenceDTO.getIsEssence())
            .in(Post::getPostId, postEssenceDTO.getPostIdList()).update();
        postCashService.removeForumDetailById(
            org.springframework.util.StringUtils.collectionToDelimitedString(postEssenceDTO.getPostIdList(), ","));
    }

    @Override
    public void updatePostTopStatus(PostTopDTO postTopDTO) {
        log.info("-----updatePostTopStatus---postTopDTO:" + postTopDTO.toString());
        lambdaUpdate().set(Post::getIsTop, postTopDTO.getIsTop()).in(Post::getPostId, postTopDTO.getPostIdList())
            .update();
        postCashService.removeForumDetailById(
            org.springframework.util.StringUtils.collectionToDelimitedString(postTopDTO.getPostIdList(), ","));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBestPostCommentSet(BestPostCommentDTO bestPostCommentDTO) {
        log.info("save_best_post_comment_arg:{}", bestPostCommentDTO);
        String postId = bestPostCommentDTO.getPostId();
        String commentId = bestPostCommentDTO.getCommentId();
        String userId = UserThreadContext.getUserId();

        Post post = getById(postId);
        PostComment postComment = postCommentService.getById(commentId);
        if (post == null) {
            throw new BusinessException(BusinessExceptionEnum.POST_NOT_EXIST_EXCEPTION);
        }

        if (postComment == null) {
            throw new BusinessException(BusinessExceptionEnum.POST_COMMENT_NOT_EXIST_EXCEPTION);
        }

        if (!userId.equals(post.getCreateBy())) {
            throw new BusinessException(BusinessExceptionEnum.POST_CREAT_NOT_CUR_USERID_EXCEPTION);
        }

        if (StringUtils.isNotEmpty(post.getBestReplyId()) || postComment.getIsAccept() == 1) {
            throw new BusinessException(BusinessExceptionEnum.POST_HAS_BEST_REPLY_EXCEPTION);
        }

        if (post.getCreateBy().equals(postComment.getCommentBy())) {
            throw new BusinessException(BusinessExceptionEnum.POST_NOT_SET_CREATE_BEST_REPLY_EXCEPTION);
        }
        // 提问者和回答者不能为同一人

        Integer isAnonymous = postComment.getIsAnonymous();
        if (isAnonymous == 1) {
            throw new BusinessException(
                BusinessExceptionEnum.POST_IS_ANONYMOUS_CAN_NOT_SET_BEST_REPLY_EXCEPTION);
        }

        //只有正常帖子才能被设置最佳回复
        if (isAnonymous == 0) {

            post.setBestReplyId(commentId);
            updateById(post);

            postComment.setIsAccept(1);
            postCommentService.updateById(postComment);

            if (post.getRewardIntegral() > 0) {
                mqProducer.sendMsg(new ExcitationTradeEvent(
                    new ExcitationTradeDTO().setPayerId(post.getCreateBy()).setPayeeId(postComment.getCommentBy())
                        .setAmount(new BigDecimal(post.getRewardIntegral()))
                        .setEventId(OtherEventCategoryEnum.topicReward.name())
                        .setExcitationTypeEnum(ExcitationTypeEnum.GOLD_COIN)
                        .setSummary("话题悬赏(" + post.getTitle() + ")")));
            }

            // 发送消息[最佳话题回帖]
            ForumSection section = forumSectionService.getById(post.getSectionId());
            if (Objects.nonNull(section)) {
                mqProducer.sendMsg(new ExcitationMQEvent(
                    new ExcitationMQEventDTO().setUserId(postComment.getCommentBy())
                        .setEventId(ExcitationEventEnum.bestReplyTopic.name()).setTargetId(section.getSectionId())
                        .setTargetName(section.getSectionName() + "-" + post.getTitle())));
            }
            // 发送推送消息给回答人
            handlerPushMsg(List.of(postComment.getCommentBy()), PushEventEnum.SET_BEST_ANSWER, postId, FORUM_PUSH_TYPE,
                post.getTitle());
        }
        postCashService.removeForumDetailById(postId);
    }

    @Override
    public PostAttentionResultDTO addOrUpdatePostAttention(String postId) {
        String curUserId = UserThreadContext.getUserId();
        String orgId = UserThreadContext.getOrgId();

        Post post = getById(postId);
        if (post == null) {
            throw new BusinessException(BusinessExceptionEnum.POST_NOT_EXIST_EXCEPTION);
        }

        PostAttentionResultDTO postAttentionResultDTO = new PostAttentionResultDTO();
        postAttentionResultDTO.setPostId(postId);
        PostAttention postAttention = postAttentionService.lambdaQuery().eq(PostAttention::getPostId, postId)
            .eq(PostAttention::getAttentionBy, curUserId).one();
        if (postAttention == null) {
            postAttention = new PostAttention();
            postAttention.setPostAttentionId(StringUtil.newId());
            postAttention.setAttentionBy(curUserId);
            postAttention.setAttentionTime(new Date());
            postAttention.setOrgId(orgId);
            postAttention.setPostId(postId);
            postAttentionService.save(postAttention);
            // 关注话题MQ激励锚点
            ForumSection section = forumSectionService.getById(post.getSectionId());
            if (Objects.nonNull(section)) {
                mqProducer.sendMsg(new ExcitationMQEvent(
                    new ExcitationMQEventDTO().setEventId(ExcitationEventEnum.focusTopic.name()).setUserId(curUserId)
                        .setTargetId(section.getSectionId())
                        .setTargetName(section.getSectionName() + "-" + post.getTitle())));
            }

            postAttentionResultDTO.setIsAttention(1);
        } else {
            postAttentionService.removeById(postAttention);
            postAttentionResultDTO.setIsAttention(0);
        }
        long count = postAttentionService.lambdaQuery().eq(PostAttention::getPostId, postId).count();
        postAttentionResultDTO.setAttentionNum(count);
        // 刷新关注数
        mqProducer.sendMsg(new ForumAttentionEvent(postId));
        return postAttentionResultDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PostVoteResultDto addOrUpdatePostVote(String postId) {
        String curUserId = UserThreadContext.getUserId();

        Post post = getById(postId);
        if (post == null) {
            throw new BusinessException(BusinessExceptionEnum.POST_NOT_EXIST_EXCEPTION);
        }
        PostVoteResultDto postVoteResultDto = new PostVoteResultDto();
        PostVote postVote = postVoteService.lambdaQuery().eq(PostVote::getPostId, postId)
            .eq(PostVote::getVoteBy, curUserId).one();
        if (postVote == null) {
            postVote = new PostVote();
            postVote.setPostVoteId(StringUtil.newId());
            postVote.setPostId(postId);
            postVote.setVoteCount(1);
            postVote.setVoteBy(curUserId);
            postVote.setVoteTime(new Date());
            postVote.setCategoryType(post.getPostType());
            postVoteService.save(postVote);
        } else {
            Integer voteCount = postVote.getVoteCount();
            voteCount = voteCount == 1 ? 2 : 1;
            postVote.setVoteCount(voteCount);
            postVoteService.updateById(postVote);
        }
        long likeNum = postVoteService.lambdaQuery().eq(PostVote::getPostId, postId).eq(PostVote::getVoteCount, 1)
            .count();
        postVoteResultDto.setLikeNum(likeNum);
        // 刷新点赞数
        mqProducer.sendMsg(new ForumLikeEvent(postId));
        return postVoteResultDto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePostPoll(PostPollDto postPollDto) {
        log.info("------savePostPoll----postPollDto:" + postPollDto.toString());
        String curUserId = UserThreadContext.getUserId();
        String postId = postPollDto.getPostId();
        Post post = getById(postId);
        if (post == null) {
            throw new BusinessException(BusinessExceptionEnum.POST_NOT_EXIST_EXCEPTION);
        }
        Date now = new Date();
        if (!(now.after(post.getStartTime()) && now.before(post.getEndTime()))) {
            throw new BusinessException(BusinessExceptionEnum.POST_IS_NOT_BETWEEN_VOTE_TIME_EXCEPTION);
        }
        List<String> postVoteIdList = postPollDto.getPostVoteIdList();
        List<PostPoll> postPollList = new ArrayList<>();
        for (String postVoteId : postVoteIdList) {
            PostPoll postPoll = postPollService.lambdaQuery().eq(PostPoll::getPostId, postId)
                .eq(PostPoll::getOptionId, postVoteId).eq(PostPoll::getCreateBy, curUserId).one();
            if (postPoll != null) {
                throw new BusinessException(BusinessExceptionEnum.POST_HAS_VOTE_EXCEPTION);
            }
            PostPoll tmpPostPoll = new PostPoll();
            tmpPostPoll.setPostPollId(StringUtil.newId());
            tmpPostPoll.setPostId(postId);
            tmpPostPoll.setOptionId(postVoteId);
            postPollList.add(tmpPostPoll);
        }
        if (!CollectionUtils.isEmpty(postPollList)) {
            postPollService.saveBatch(postPollList);
            // 刷新投票数
            mqProducer.sendMsg(new ForumPollEvent(postId));
        }
    }

    @Override
    public void deletePostByPostId(List<String> postIdList) {
        log.info("------deletePostByPostId----postId:" + postIdList);
        String curUserId = UserThreadContext.getUserId();
        List<String> deleteIdList = new ArrayList<>();
        // 非发帖人不允许删除
        List<Post> postList = listByIds(postIdList);
        Set<String> sectionIds = postList.stream().map(Post::getSectionId).collect(Collectors.toSet());
        List<ForumSection> forumSections = forumSectionService.listByIds(sectionIds);
        Map<String, String> sectionMap = forumSections.stream()
            .collect(Collectors.toMap(ForumSection::getSectionId, ForumSection::getCreateBy));
        for (Post post : postList) {
            // 非版主和非发帖人不能删除
            if (!StringUtils.equals(curUserId, sectionMap.get(post.getSectionId())) && !StringUtils.equals(curUserId,
                post.getCreateBy())) {
                throw new BusinessException(BusinessExceptionEnum.POST_NOT_DELETE_BY_OTHER_EXCEPTION);
            }
            deleteIdList.add(post.getPostId());
        }
        removeBatchByIds2(deleteIdList);
        // 刷新版块发帖数
        sectionIds.forEach(sectionId -> mqProducer.sendMsg(new ForumPostEvent(sectionId)));
        postCashService.removeForumDetailById(
            org.springframework.util.StringUtils.collectionToDelimitedString(postIdList, ","));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePost(UpdatePostDTO updatePostDTO) {
        log.info("------updatePost----updatePostDTO:" + updatePostDTO.toString());
        String curUserId = UserThreadContext.getUserId();
        String postId = updatePostDTO.getPostId();
        Post post = getById(postId);
        // 校验话题
        checkPost(updatePostDTO.getPostContent(), curUserId, post);

        Integer oldIntegral = post.getRewardIntegral();
        Integer newIntegral = updatePostDTO.getIntegral();
        if (newIntegral != null) {
            int lessFortune = newIntegral - oldIntegral;
            // 用户积分差额检验
            if (lessFortune > 0 && excitationFeign.getUserGold(curUserId) < lessFortune) {
                throw new BusinessException(BusinessExceptionEnum.USER_ERR_SCORE_LACK);
            }
        }

        //更新帖子内容
        post.setPostContent(updatePostDTO.getPostContent());
        post.setRewardIntegral(updatePostDTO.getIntegral());
        // 兼容学习项目部分
        if (updatePostDTO.getIsPublic() != null) {
            post.setIsPublic(updatePostDTO.getIsPublic());
        }

        //更新延迟时间
        if (updatePostDTO.getDelayDay() != null && post.getEndTime() != null) {
            // 修改投票时间，是在已有的投票时间上延长投票时间
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(post.getEndTime());
            // 当前帖子投票结束时间加上延长的投票时间
            calendar.add(Calendar.DAY_OF_MONTH, +updatePostDTO.getDelayDay());
            post.setEndTime(calendar.getTime());
        }

        //设置是否有专家
        List<String> specialistUserIdList = updatePostDTO.getSpecialistUserIdList();
        // 处理@专家
        handleSpecialistList(post, specialistUserIdList);

        //如果是投票帖，更新选项
        List<String> postVoteOptionNameList = updatePostDTO.getPostVoteOptionNameList();
        if (!CollectionUtils.isEmpty(postVoteOptionNameList)) {
            handlePostVoteOption(post, postVoteOptionNameList);
        }

        updateById(post);

        postCashService.removeForumDetailById(updatePostDTO.getPostId());
        //TODO 金币差额悬赏减 俩种情况

    }

    /**
     * 处理推送消息
     *
     * @param pushUser      需要推送的用户
     * @param pushEventEnum 推送事件类型
     */
    @Override
    public void handlerPushMsg(List<String> pushUser, PushEventEnum pushEventEnum, String pushAbout, String contentType,
        String contentTitle) {
        log.info("handler_push_arg:[{}-{}-{}],push_event_enum:[{}],push_user:[{}]", pushAbout, contentType,
            contentTitle, pushEventEnum.getType(), pushUser);
        SavePushManageDTO push = new SavePushManageDTO();
        // 先处理需要发送的用户,后续对 "收到回帖" 这种情况再将 发帖人id添加进去
        TreeSet<ViewLimitMainSaveDTO> userLimit = new TreeSet<>();
        pushUser.forEach(item -> {
            ViewLimitMainSaveDTO viewLimitDTO = new ViewLimitMainSaveDTO();
            viewLimitDTO.setViewId(item);
            viewLimitDTO.setViewType(NewViewLimitTypeEnum.UserLimit.getViewType());
            viewLimitDTO.setLimitType(GeneralJudgeEnum.NEGATIVE.getValue());
            userLimit.add(viewLimitDTO);
        });
        push.setUserLimit(userLimit);
        // 处理用户area
        List<UserRankBaseInfoDTO> userBaseInfo = userFeign.getUserRankBaseInfo(pushUser);
        StringBuilder pushArea = new StringBuilder();
        userBaseInfo.forEach(user -> pushArea.append(user.getUserName()).append(","));
        push.setPushArea(pushArea.deleteCharAt(pushArea.length() - 1).toString());
        // 非学习项目处理
        push.setPushContentType(contentType);
        push.setPushAbout(pushAbout);
        push.setMessages(contentTitle);
        // 不同类型设置不同的消息
        Arrays.stream(PushEventEnum.values()).forEach(eventEnum -> {
            if (eventEnum.getType().equals(pushEventEnum.getType())) {
                eventEnum.getMsgHandler().accept(push);
            }
        });
        ViewLimitProgrammeInfoDTO viewLimitProgrammeInfoDTO = viewLimitFeign.saveViewLimit(push.getUserLimit());
        push.setProgrammeId(viewLimitProgrammeInfoDTO.getId());
        push.setPushClientArea(3);
        push.setIsTemp(GeneralJudgeEnum.NEGATIVE.getValue());
        push.setContentTitle(contentTitle);

        if (log.isDebugEnabled()) {
            log.debug("current_push_dto：[{}]", push);
        }
        pushFeign.savePush(push);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Object> savePost(SavePostApiDTO savePostApiDTO) {
        log.info("------savePost----savePostDTO:{}", savePostApiDTO);
        String curUserId = UserThreadContext.getUserId();
        // 检查标题
        List<String> routerIds = routerFeign.getRouterNames();
        // 检验系统版本权限是否包含要校验的内容；
        if(routerIds.contains(ResourceTypeEnum.SENSITIVE_CONFIG.getRouter())){
            // 敏感词校验
            String sensitiveWord = sysSensitiveWordFeign.checkWordsReturnStr(savePostApiDTO.getPostContent());
            String titleSensitiveWord = sysSensitiveWordFeign.checkWordsReturnStr(savePostApiDTO.getTitle());
            if (!StringUtils.isNotEmpty(sensitiveWord) || StringUtils.isNotEmpty(titleSensitiveWord)) {
                throw new BusinessException(BusinessExceptionEnum.POST_HAS_SENSITIVE_WORD_EXCEPTION);
            }
        }
        // 禁言处理
        Optional<PostBanUser> postBanUser = postBanUserService.lambdaQuery().eq(PostBanUser::getUserId, curUserId)
            .oneOpt();
        if (postBanUser.isPresent()) {
            PostBanUser banUser = postBanUser.get();
            // 判断当前时间是否是结束时间之后了
            if (new Date().before(banUser.getEndTime())) {
                Result<Object> fail = Result.fail(BusinessExceptionEnum.CUR_USER_HAS_BEAN_BAN.getValue(),
                    BusinessExceptionEnum.CUR_USER_HAS_BEAN_BAN.getName());
                fail.setData(banUser.getEndTime());
                return fail;
            }
        }

        Post post = new Post();
        String postId = newId();
        BeanUtils.copyProperties(savePostApiDTO, post);
        post.setOrgId(UserThreadContext.getOrgId());
        post.setPostId(postId);
        post.setIsClient(CLIENT);
        post.setIsAvailable(1);
        post.setPostIntro(StringUtil.getBriefs(savePostApiDTO.getPostContent()));

        // 兼容学习项目部分
        handleProject(savePostApiDTO, post);
        // 用户积分差额检验
        Integer lessFortune = savePostApiDTO.getRewardIntegral();
        // 用户积分差额检验
        if (lessFortune != null && (lessFortune > 0 && excitationFeign.getUserGold(curUserId) < lessFortune)) {
            throw new BusinessException(BusinessExceptionEnum.USER_ERR_SCORE_LACK);
        }
        // 设置是否有专家
        List<String> specialistUserIdList = savePostApiDTO.getExpertIdList();
        handleSpecialistList(post, specialistUserIdList);
        // 如果是投票帖，添加选项,设置更新时间
        if (IS_VOTE.equals(savePostApiDTO.getPostType())) {
            // 更新延迟时间
            Optional.ofNullable(savePostApiDTO.getValidDay()).ifPresent(validDay -> {
                Date beginTime = new Date();
                // 修改投票时间，是在已有的投票时间上延长投票时间
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(beginTime);
                // 当前帖子投票结束时间加上延长的投票时间
                calendar.add(Calendar.DAY_OF_MONTH, +validDay);
                post.setStartTime(beginTime);
                post.setEndTime(calendar.getTime());
            });
            List<String> postVoteOptionNameList = savePostApiDTO.getVoteOptionList();
            // 处理投票贴选项
            handlePostVoteOption(post, postVoteOptionNameList);
        }
        // 图片的插入
        List<NamePath> imgNamePathList = savePostApiDTO.getImgNamePathList();
        if (!CollectionUtils.isEmpty(imgNamePathList)) {
            post.setIsImg(1);
            fileFeign.saveImages(postId, ImageBizType.POST_IMAGE.name(), imgNamePathList);
        }
        save(post);
        // 发表帖子积分锚点
        ForumSection section = forumSectionService.getById(post.getSectionId());
        if (Objects.nonNull(section)) {
            mqProducer.sendMsg(new ExcitationMQEvent(
                new ExcitationMQEventDTO().setEventId(ExcitationEventEnum.makeTopic.name())
                    .setTargetId(section.getSectionId()).setTargetName(section.getSectionName() + "-" + post.getTitle())
                    .setUserId(curUserId)));
        }

        // 发送推送消息给@专家
        handlePushMsg(post, postId, specialistUserIdList);
        // 刷新版块发帖数
        mqProducer.sendMsg(new ForumPostEvent(post.getSectionId()));
        // TODO 金币差额悬赏减
        return Result.success();
    }

    private void handlePushMsg(Post post, String postId, List<String> specialistUserIdList) {
        if (!CollectionUtils.isEmpty(specialistUserIdList)) {
            String pushAbout = postId;
            String pushType = FORUM_PUSH_TYPE;
            if (StringUtils.isNotEmpty(post.getProjectId())) {
                pushType = PROJECT_FORUM_PUSH_TYPE;
                pushAbout = post.getProjectId() + "_" + postId;
            }
            // feign接口调用推送
            handlerPushMsg(specialistUserIdList, PushEventEnum.EXPERT_ASSISTANCE, pushAbout, pushType, post.getTitle());
        }
    }

    private void handleProject(SavePostApiDTO savePostApiDTO, Post post) {
        if (StringUtils.isNotEmpty(savePostApiDTO.getProjectId())) {
            // 如果没有团队的情况 默认帖子公开
            if (StringUtils.isEmpty(savePostApiDTO.getTeamId())) {
                post.setIsPublic(1);
            } else {
                // 如果非公开则加入团队id,反之不添加
                Optional.ofNullable(savePostApiDTO.getIsPublic()).filter(value -> value == 0).ifPresentOrElse(value -> {
                    post.setIsPublic(value);
                    post.setTeamId(savePostApiDTO.getTeamId());
                    post.setTeamName(savePostApiDTO.getTeamName());
                }, () -> post.setIsPublic(1));
            }
        }
    }

    private void handleSpecialistList(Post post, List<String> specialistList) {
        if (!CollectionUtils.isEmpty(specialistList)) {
            post.setIsExpert(1);
            List<PostSpecialist> postSpecialistList = new ArrayList<>();
            int i = 0;
            for (String userId : specialistList) {
                PostSpecialist postSpecialist = new PostSpecialist();
                postSpecialist.setPostSpecialistId(StringUtil.newId());
                postSpecialist.setPostId(post.getPostId());
                postSpecialist.setUserId(userId);
                postSpecialist.setSortNo(++i);
                postSpecialistList.add(postSpecialist);
            }
            postSpecialistService.saveBatch(postSpecialistList);
        }
    }

    @Override
    public Integer getMyPostUnreadCount(String projectId) {
        return getBaseMapper().selectMyPostsUnreadCommentCount(UserThreadContext.getUserId(), projectId);
    }

    @Override
    @Async
    public void exportPost(PostQuery postQuery) {
        // 构建并实现接口类的方法 返回导出的接口类对象
        IExportDataDTO exportDataDTO = buildExportPostDataDTO(postQuery);
        exportComponent.exportRecord(exportDataDTO);
    }

    /**
     * 构建并实现接口类的方法 返回导出的接口类对象
     *
     * @param postQuery {@link PostQuery}
     * @return {@link IExportDataDTO}
     */
    public IExportDataDTO buildExportPostDataDTO(PostQuery postQuery) {
        return new AbstractExportDataDTO<IPostService, PostListDTO>(postQuery) {

            @Override
            protected IPostService getBean() {
                return SpringUtil.getBean("postService", IPostService.class);
            }

            @Override
            protected PageInfo<PostListDTO> getPageInfo() {
                return getBean().findPostListByPage(postQuery);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.Post;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.Post.getType();
            }

            @Override
            protected void afterCreateBeanMap(Map<String, Object> map) {
                Object postType = map.get(POST_TYPE);
                if (Objects.equals(postType, 2)) {
                    map.put(POST_TYPE, "投票帖");
                } else {
                    map.put(POST_TYPE, "主题帖");
                }
                Object isClient = map.get(IS_CLIENT);
                if (Objects.equals(isClient, 2)) {
                    map.put(IS_CLIENT, "管理端");
                } else {
                    map.put(IS_CLIENT, "客户端");
                }
                Object isDel = map.get(IS_DEL);
                if (Objects.equals(isDel, 1)) {
                    map.put(IS_DEL, "已删除");
                } else {
                    map.put(IS_DEL, "未删除");
                }
            }

        };
    }

    @Override
    public PageInfo<ForumPostApiDTO> searchPost(ForumPostSearchQuery forumPostSearchQuery) {
        ForumSearchWordApiQuery forumSearchWordApiQuery = new ForumSearchWordApiQuery();
        BeanUtils.copyProperties(forumPostSearchQuery, forumSearchWordApiQuery);
        forumSearchWordApiQuery.setCount(true);

        SearchKeyDetailDTO searchKeyDetailDTO = new SearchKeyDetailDTO();
        searchKeyDetailDTO.setKeyword(forumPostSearchQuery.getSearchKeyWord());
        searchKeyDetailDTO.setSearchBy(UserThreadContext.getUserId());
        searchKeyDetailDTO.setType(3);
        searchKeyDetailDTO.setOrgId(UserThreadContext.getOrgId());
        mqProducer.sendMsg(new SearchKeyDetailEvent(searchKeyDetailDTO));
        return searchKeyWordPostPageBySectionId(forumSearchWordApiQuery);
    }

    @Override
    public PageInfo<ForumPostApiDTO> findPostHomePageList(PostHomePageQuery postHomePageQuery) {
        PageInfo<ForumPostApiDTO> result = getPostsByRule(postHomePageQuery);
        List<ForumPostApiDTO> forumPostApiDTOList = result.getList();
        if (forumPostApiDTOList.isEmpty()) {
            return result;
        }
        //处理返回的帖子列表
        handlePostResult(forumPostApiDTOList);

        return result;
    }

    private PageInfo<ForumPostApiDTO> getPostsByRule(PostHomePageQuery postHomePageQuery) {
        Integer pageNo = postHomePageQuery.getPageNo();
        Integer pageSize = postHomePageQuery.getPageSize();
        postHomePageQuery.setUserId(UserThreadContext.getUserId());
        String contentRule = postHomePageQuery.getContentRule();

        if (HeadContentRuleEnum.HOTTOPICS1.getRuleType().equals(contentRule)) {
            String publishDays = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_471.getCode());
            postHomePageQuery.setPublishDays(Integer.valueOf(publishDays));
            String commentNum = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_481.getCode());
            postHomePageQuery.setCommentNum(Integer.valueOf(commentNum));
            postHomePageQuery.setOrderType(CommonConstants.TOPICS_ORDERTYPE_COMMENTNUM);
            return PageMethod.startPage(pageNo, pageSize)
                .doSelectPageInfo(() -> baseMapper.selectPostHomePageList(postHomePageQuery));
        }

        if (HeadContentRuleEnum.HOTTOPICS2.getRuleType().equals(contentRule)) {
            String commentNum = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_481.getCode());
            postHomePageQuery.setCommentNum(Integer.valueOf(commentNum));
            postHomePageQuery.setOrderType(CommonConstants.TOPICS_ORDERTYPE_COMMENTNUM);
            return PageMethod.startPage(pageNo, pageSize)
                .doSelectPageInfo(() -> baseMapper.selectPostHomePageList(postHomePageQuery));
        }

        if (HeadContentRuleEnum.ELITEPOSTS1.getRuleType().equals(contentRule)) {
            postHomePageQuery.setOrderType(CommonConstants.TOPICS_ORDERTYPE_ESSENCEDATE);
            postHomePageQuery.setIsTop(1);
            return PageMethod.startPage(pageNo, pageSize)
                .doSelectPageInfo(() -> baseMapper.selectPostHomePageList(postHomePageQuery));
        }

        // 不设置内容规则时，返回最新话题列表
        if (HeadContentRuleEnum.LATESTTOPICS.getRuleType().equals(contentRule) || StringUtils.isEmpty(contentRule)) {
            postHomePageQuery.setOrderType(CommonConstants.TOPICS_ORDERTYPE_ADDDATE);
            return PageMethod.startPage(pageNo, pageSize)
                .doSelectPageInfo(() -> baseMapper.selectPostHomePageList(postHomePageQuery));
        }

        return new PageInfo<>();
    }

    @Override
    @SuppressWarnings("java:S3776")
    public void exportData(PostQuery queryDTO) {
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IPostService, PostListDTO>(queryDTO) {

            @Override
            protected IPostService getBean() {
                return SpringUtil.getBean("postService", IPostService.class);
            }

            @Override
            protected PageInfo<PostListDTO> getPageInfo() {
                return getBean().findPostListByPage((PostQuery) queryDTO);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.ProjectPost;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.ProjectPost.getType();
            }

            @Override
            protected void afterCreateBeanMap(Map<String, Object> map) {
                String teamNameStr = "teamName";

                Object isTop = map.get("isTop");
                Object isEssence = map.get("isEssence");
                Object isFavourite = map.get("isFavourite");
                StringBuilder status = new StringBuilder();
                StringBuilder state = new StringBuilder();

                if (isTop.equals(1)) {
                    status.append(" [置顶]");
                    state.append(" 置顶");
                }
                if (isEssence.equals(1)) {
                    status.append(" [推荐]");
                    state.append(" 推荐");
                }
                if (isFavourite.equals(1)) {
                    status.append(" [热门]");
                    state.append(" 热门");
                }

                Object title = map.get("title");
                map.put("title", title.toString() + status);

                Object teamName = map.get(teamNameStr);
                map.put(teamNameStr, ObjectUtils.isEmpty(teamName) ? "全部" : teamName);
                map.put("state", state);
                Object postType = map.get(POST_TYPE);
                if (postType.equals(1)) {
                    map.put(POST_TYPE, "主题帖");
                } else if (postType.equals(2)) {
                    map.put(POST_TYPE, "投票帖");
                }

                Object isClient = map.get(IS_CLIENT);
                if (isClient.equals(1)) {
                    map.put(IS_CLIENT, "客户端");
                } else {
                    map.put(IS_CLIENT, "管理端");
                }
                if (Objects.isNull(teamName) || StringUtils.isEmpty(teamName.toString())) {
                    map.put(teamNameStr, "全部");
                }

            }
        };

        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public void updatePostInteractNum(String id, String event) {
        switch (event) {
            case ResourceInteractEventRoutingKeyConstants.FORUM_VIEW_EVENT:
                baseMapper.updateViewNum(id);
                break;
            case ResourceInteractEventRoutingKeyConstants.FORUM_COMMENT_EVENT:
                baseMapper.updateCommentNum(id);
                break;
            case ResourceInteractEventRoutingKeyConstants.FORUM_LIKE_EVENT:
                baseMapper.updateStarNum(id);
                break;
            case ResourceInteractEventRoutingKeyConstants.FORUM_POLL_EVENT:
                baseMapper.updatePollNum(id);
                break;
            case ResourceInteractEventRoutingKeyConstants.FORUM_ATTENTION_EVENT:
                baseMapper.updateAttentionNum(id);
                break;
            default:
        }
        postCashService.removeForumDetailById(id);
    }

    @Override
    public Integer getCurrentPosterByPostIdAndUserId(String postId, String userId) {
        return baseMapper.getCurrentPosterByPostIdAndUserId(postId, userId);
    }

    @Override
    public List<ForumPostApiDTO> selectPostListBySectionId(ForumPostApiQuery forumPostApiQuery) {
        return baseMapper.selectPostListBySectionId(forumPostApiQuery);
    }

    @Override
    public Long queryCountBySectionId(String sectionId) {
        return count(new LambdaQueryWrapper<Post>().eq(Post::getSectionId, sectionId));
    }

    @Override
    public PageInfo<PostListDTO> findMarchPostListByPage(PostQuery postQuery) {
        postQuery.setCurrentOrgId(UserThreadContext.getOrgId());
        postQuery.setCurrentUserId(UserThreadContext.getUserId());

        // section和关卡id有一层关联关系,通过section的projectId做处理
        return PageMethod.startPage(postQuery.getPageNo(), postQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.selectMarchPostListByPage(postQuery));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveMarchPost(MarchSavePostDTO marchSavePostDTO) {
        log.info("------savePost----savePostDTO:{}", marchSavePostDTO);
        ForumSection forumSection = forumSectionService.lambdaQuery()
            .eq(ForumSection::getProjectId, marchSavePostDTO.getCheckpointId()).one();
        // 检查标题
        List<String> routerIds = routerFeign.getRouterNames();
        // 检验系统版本权限是否包含要校验的内容；
        if(routerIds.contains(ResourceTypeEnum.SENSITIVE_CONFIG.getRouter())){
            // 敏感词校验
            String sensitiveWord = sysSensitiveWordFeign.checkWordsReturnStr(marchSavePostDTO.getPostContent());
            String titleSensitiveWord = sysSensitiveWordFeign.checkWordsReturnStr(marchSavePostDTO.getTitle());
            if (StringUtils.isNotEmpty(sensitiveWord) || StringUtils.isNotEmpty(titleSensitiveWord)) {
                throw new BusinessException(BusinessExceptionEnum.POST_HAS_SENSITIVE_WORD_EXCEPTION);
            }
        }

        Post post = new Post();
        String postId = newId();
        BeanUtils.copyProperties(marchSavePostDTO, post);
        post.setSectionId(forumSection.getSectionId());
        post.setProjectId(marchSavePostDTO.getMarchId());
        post.setOrgId(UserThreadContext.getOrgId());
        post.setPostId(postId);
        post.setIsClient(CLIENT);
        post.setIsAvailable(1);
        post.setPostIntro(StringUtil.getBriefs(marchSavePostDTO.getPostContent()));
        post.setIsPublic(1);
        post.setTeamId(marchSavePostDTO.getTeamId());
        post.setTeamName(marchSavePostDTO.getTeamName());

        // 图片的插入
        List<PostNamePath> imgNamePathList = marchSavePostDTO.getImgNamePathList();
        if (!CollectionUtils.isEmpty(imgNamePathList)) {
            List<NamePath> namePathList = imgNamePathList.stream().map(item -> {
                NamePath namePath = new NamePath();
                BeanUtils.copyProperties(item, namePath);
                return namePath;
            }).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(imgNamePathList)) {
                post.setIsImg(1);
                fileFeign.saveImages(postId, ImageBizType.POST_IMAGE.name(), namePathList);
            }
        }

        save(post);

        // 刷新版块发帖数
        mqProducer.sendMsg(new ForumPostEvent(post.getSectionId()));
    }

    @Override
    public void updateMarchPost(MarchSavePostDTO updatePostDTO) {
        log.info("------updatePost----updatePostDTO:" + updatePostDTO.toString());
        String curUserId = UserThreadContext.getUserId();
        String postId = updatePostDTO.getPostId();
        Post post = getById(postId);
        checkPost(updatePostDTO.getPostContent(), curUserId, post);

        // 图片的插入
        List<PostNamePath> imgNamePathList = updatePostDTO.getImgNamePathList();
        if (!CollectionUtils.isEmpty(imgNamePathList)) {
            List<NamePath> namePathList = imgNamePathList.stream().map(item -> {
                NamePath namePath = new NamePath();
                BeanUtils.copyProperties(item, namePath);
                return namePath;
            }).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(imgNamePathList)) {
                post.setIsImg(1);
                SaveOrUpdatePicListDTO updatePicListDTO = new SaveOrUpdatePicListDTO();
                updatePicListDTO.setBizId(postId);
                updatePicListDTO.setBizType(ImageBizType.POST_IMAGE.name());
                updatePicListDTO.setIsInsert(false);
                updatePicListDTO.setTempFilePath(namePathList);
                fileFeign.saveOrUpdatePicList(updatePicListDTO);
            }
        } else {
            fileFeign.deleteImageByBizIdAndBizType(postId, ImageBizType.POST_IMAGE.name());
        }

        //更新帖子内容
        post.setTitle(updatePostDTO.getTitle());
        post.setPostContent(updatePostDTO.getPostContent());
        post.setPostIntro(StringUtil.getBriefs(updatePostDTO.getPostContent()));
        updateById(post);

        postCashService.removeForumDetailById(postId);
    }

    /**
     * 检查话题帖子
     *
     * @param content   帖子内容
     * @param curUserId 添加用户id
     * @param post      话题帖子
     */
    private void checkPost(String content, String curUserId, Post post) {
        if (post == null) {
            throw new BusinessException(BusinessExceptionEnum.POST_NOT_EXIST_EXCEPTION);
        }

        if (!curUserId.equals(post.getCreateBy())) {
            throw new BusinessException(BusinessExceptionEnum.POST_NOT_DELETE_BY_OTHER_EXCEPTION);
        }

        List<String> routerIds = routerFeign.getRouterNames();
        // 检验系统版本权限是否包含要校验的内容；
        if(routerIds.contains(ResourceTypeEnum.SENSITIVE_CONFIG.getRouter())){
            // 敏感词校验
            String sensitiveWord = sysSensitiveWordFeign.checkWordsReturnStr(content);
            if (StringUtils.isNotEmpty(sensitiveWord)) {
                throw new BusinessException(BusinessExceptionEnum.POST_HAS_SENSITIVE_WORD_EXCEPTION);
            }
        }
    }
}
