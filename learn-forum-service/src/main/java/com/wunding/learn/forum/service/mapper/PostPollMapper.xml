<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.forum.service.mapper.PostPollMapper">
    <!-- 开启二级缓存 -->
    <!--
<cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.forum.service.mapper.PostPollMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.forum.service.model.PostPoll">
        <!--@Table post_poll-->
        <id column="post_poll_id" jdbcType="VARCHAR" property="postPollId"/>
        <result column="post_id" jdbcType="VARCHAR"
          property="postId"/>
        <result column="option_id" jdbcType="VARCHAR"
          property="optionId"/>
        <result column="create_by" jdbcType="VARCHAR"
          property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP"
          property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR"
          property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP"
          property="updateTime"/>
        <result column="is_del" jdbcType="TINYINT"
          property="isDel"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        post_poll_id
        , post_id, option_id, create_by, create_time, update_by, update_time, is_del
    </sql>

    <select id="getPollByPostId" resultType="com.wunding.learn.common.dto.IdNumberInt">
        select option_id id, count(option_id) number
        from post_poll
        <if test="list!=null and list.size()>0">
            where option_id in
            <foreach collection="list" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        group by option_id
    </select>
</mapper>
