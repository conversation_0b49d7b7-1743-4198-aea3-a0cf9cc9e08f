package com.wunding.learn.forum.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.common.dto.IdNumberInt;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import com.wunding.learn.forum.service.model.PostAttention;
import java.util.List;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 帖子关注记录表 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">吴光荣</a>
 * @since 2022-06-13
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")
})
public interface PostAttentionMapper extends BaseMapper<PostAttention> {

    /**
     * @param postId
     * @param curUserId
     * @return
     */
    Integer getIsAtByPostIdAndUserId(@Param("postId") String postId, @Param("userId") String curUserId);

    /**
     * @param postIds
     * @param currentUserId
     * @return
     */
    List<IdNumberInt> getIsAtMapByPostIdAndUserId(@Param("list") List<String> postIds,
        @Param("userId") String currentUserId);
}
