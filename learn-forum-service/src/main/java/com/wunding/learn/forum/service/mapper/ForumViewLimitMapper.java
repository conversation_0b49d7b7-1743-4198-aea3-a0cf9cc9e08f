package com.wunding.learn.forum.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import com.wunding.learn.forum.service.model.ForumViewLimit;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 论坛下发范围表 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">chh</a>
 * @since 2022-06-15
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")
})
public interface ForumViewLimitMapper extends BaseMapper<ForumViewLimit> {

}
