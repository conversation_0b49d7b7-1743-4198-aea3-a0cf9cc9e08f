package com.wunding.learn.forum.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.forum.service.admin.dto.PostVoteOptionDTO;
import com.wunding.learn.forum.service.client.dto.PostOptionApiDTO;
import com.wunding.learn.forum.service.mapper.PostVoteOptionMapper;
import com.wunding.learn.forum.service.model.PostVoteOption;
import com.wunding.learn.forum.service.service.IPostPollService;
import com.wunding.learn.forum.service.service.IPostVoteOptionService;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p> 投票贴选项表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">吴光荣</a>
 * @since 2022-06-13
 */
@Slf4j
@Service("postVoteOptionService")
public class PostVoteOptionServiceImpl extends ServiceImpl<PostVoteOptionMapper, PostVoteOption> implements
    IPostVoteOptionService {

    @Resource
    private IPostPollService postPollService;
    @Override
    public List<PostVoteOptionDTO> getPostVoteOptionDTO(String id) {
        List<PostOptionApiDTO> postOptionApiDTOList = baseMapper.selectOptionVoteList(id, null);
        List<String> optionIds = postOptionApiDTOList.stream().map(PostVoteOptionDTO::getPostVoteOptionId)
            .collect(Collectors.toList());
        Map<String, Integer> postOptionPollMapByPostId = postPollService.getPollByPostId(optionIds);
        return postOptionApiDTOList.stream().map(item -> new PostVoteOptionDTO()
            .setOptionName(item.getOptionName())
            .setOptionNumber(
                postOptionPollMapByPostId.get(item.getPostVoteOptionId()) != null ? postOptionPollMapByPostId.get(
                    item.getPostVoteOptionId()) : 0)
            .setPostVoteOptionId(item.getPostVoteOptionId())).collect(Collectors.toList());
    }

    @Override
    public List<PostOptionApiDTO> getPostVoteOptionList(String postId) {
        String userId = UserThreadContext.getUserId();
        return baseMapper.selectOptionVoteList(postId, userId);
    }


}
