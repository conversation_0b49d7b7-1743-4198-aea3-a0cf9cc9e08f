package com.wunding.learn.forum.service.service.impl;

import static com.wunding.learn.common.util.string.StringUtil.newId;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import com.wunding.learn.common.bean.Result;
import com.wunding.learn.common.constant.other.AvailableEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.ResourceTypeEnum;
import com.wunding.learn.common.enums.excitation.ExcitationEventEnum;
import com.wunding.learn.common.enums.other.SystemConfigCodeEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.mq.dto.ExcitationMQEventDTO;
import com.wunding.learn.common.mq.event.ResourceInteractEvent.ResourceInteractEventRoutingKeyConstants;
import com.wunding.learn.common.mq.event.excitation.ExcitationMQEvent;
import com.wunding.learn.common.mq.service.MqProducer;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.string.StringPool;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.excitation.api.service.ExcitationFeign;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.constant.ImageBizType;
import com.wunding.learn.file.api.dto.AbstractExportDataDTO;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.file.api.dto.NamePath;
import com.wunding.learn.file.api.service.FileFeign;
import com.wunding.learn.forum.api.dto.MarchPostInfoDTO.PostNamePath;
import com.wunding.learn.forum.api.dto.MarchPostReplyDTO;
import com.wunding.learn.forum.service.admin.dto.CountCommentDTO;
import com.wunding.learn.forum.service.admin.dto.PostCommentBaseDTO;
import com.wunding.learn.forum.service.admin.dto.PostCommentDTO;
import com.wunding.learn.forum.service.admin.dto.PostSubCommentDTO;
import com.wunding.learn.forum.service.admin.query.CountPostCommentQuery;
import com.wunding.learn.forum.service.admin.query.PostCommentQuery;
import com.wunding.learn.forum.service.admin.query.PostSubCommentQuery;
import com.wunding.learn.forum.service.client.dto.ForumPostCommentApiDTO;
import com.wunding.learn.forum.service.client.dto.PostCommentDetailDTO;
import com.wunding.learn.forum.service.client.dto.PostCommentReplyDTO;
import com.wunding.learn.forum.service.client.dto.PostReplyDTO;
import com.wunding.learn.forum.service.client.dto.PostSubCommentApiDTO;
import com.wunding.learn.forum.service.client.dto.PostVoteResultDto;
import com.wunding.learn.forum.service.client.query.PostCommentApiQuery;
import com.wunding.learn.forum.service.dao.PostCommentDao;
import com.wunding.learn.forum.service.enums.BusinessExceptionEnum;
import com.wunding.learn.forum.service.enums.PushEventEnum;
import com.wunding.learn.forum.service.event.ForumCommentEvent;
import com.wunding.learn.forum.service.event.ForumCommentLikeEvent;
import com.wunding.learn.forum.service.event.ForumCommentReplyEvent;
import com.wunding.learn.forum.service.mapper.ForumSectionMapper;
import com.wunding.learn.forum.service.mapper.PostCommentMapper;
import com.wunding.learn.forum.service.model.ForumSection;
import com.wunding.learn.forum.service.model.Post;
import com.wunding.learn.forum.service.model.PostBanUser;
import com.wunding.learn.forum.service.model.PostComment;
import com.wunding.learn.forum.service.model.PostVote;
import com.wunding.learn.forum.service.service.IPostBanUserService;
import com.wunding.learn.forum.service.service.IPostCommentService;
import com.wunding.learn.forum.service.service.IPostService;
import com.wunding.learn.forum.service.service.IPostVoteService;
import com.wunding.learn.lecturer.api.dto.LecturerDTO;
import com.wunding.learn.lecturer.api.service.LecturerFeign;
import com.wunding.learn.user.api.dto.OrgShowDTO;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.dto.UserOrgDTO;
import com.wunding.learn.user.api.dto.UserRankBaseInfoDTO;
import com.wunding.learn.user.api.service.OrgFeign;
import com.wunding.learn.user.api.service.ParaFeign;
import com.wunding.learn.user.api.service.RouterFeign;
import com.wunding.learn.user.api.service.SysSensitiveWordFeign;
import com.wunding.learn.user.api.service.UserFeign;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * <p> 帖子评论表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">吴光荣</a>
 * @since 2022-06-13
 */
@Slf4j
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Service("postCommentService")
public class PostCommentServiceImpl extends ServiceImpl<PostCommentMapper, PostComment> implements IPostCommentService {

    /**
     * 一级评论
     */
    private static final String COMMENT_PARENT = "0";
    private final UserFeign userFeign;
    private final FileFeign fileFeign;
    private final OrgFeign orgFeign;
    private final ParaFeign paraFeign;


    private final IPostVoteService postVoteService;
    private final IPostBanUserService postBanUserService;
    private final ForumSectionMapper forumSectionMapper;
    @Resource
    @Lazy
    private IPostService postService;
    @Resource
    private ExportComponent exportComponent;
    @Resource
    private MqProducer mqProducer;
    @Resource(name = "postCommentDao")
    private PostCommentDao postCommentDao;

    @Lazy
    @Resource
    private final LecturerFeign lecturerFeign;

    @Lazy
    @Resource
    private final ExcitationFeign excitationFeign;

    @Resource
    SysSensitiveWordFeign sysSensitiveWordFeign;

    @Resource
    private RouterFeign routerFeign;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePostCommentCheckStatus(Integer status, String postCommentId, String postId) {
        // 更新回帖审核状态
        PostComment byId = getById(postCommentId);
        byId.setCheckStatus(status);
        postCommentDao.updatePostComment(byId);
        // 刷新回帖数
        mqProducer.sendMsg(new ForumCommentEvent(postId));
    }

    @Override
    public PageInfo<PostCommentDTO> getPostComment(PostCommentQuery postCommentQuery) {
        // 查询出一级评论
        PageInfo<PostCommentDTO> data = PageMethod.startPage(postCommentQuery.getPageNo(),
            postCommentQuery.getPageSize()).doSelectPageInfo(() -> getBaseMapper().selectPostComment(postCommentQuery));
        List<PostCommentDTO> postCommentDTOList = data.getList();
        if (postCommentDTOList.isEmpty()) {
            return data;
        }

        List<String> userIdList = postCommentDTOList.stream().map(PostCommentDTO::getCommentBy)
            .collect(Collectors.toList());
        // 用户基本信息映射
        Map<String, UserRankBaseInfoDTO> userBaseInfoMap = userFeign.getUserRankBaseInfo(userIdList).stream().collect(
            Collectors.toMap(UserRankBaseInfoDTO::getUserId, userRankBaseInfoDTO -> userRankBaseInfoDTO,
                (key1, key2) -> key1));

        // 获取二级评论
        Set<String> commentIds = postCommentDTOList.stream().map(PostCommentDTO::getCommentId)
            .collect(Collectors.toSet());

        // 二级评论映射
        Map<String, List<PostSubCommentDTO>> replyPostCommentMap = getReplyPostComment(
            () -> getBaseMapper().selectPostCommentMap(commentIds));

        Map<String, OrgShowDTO> orgShowDTOMap = orgFeign.getOrgShowDTO(userFeign.getUserOrdIdByUserIds(userIdList));

        // 用户积分模块查询
        Map<String, BigDecimal> userIntegralMap = excitationFeign.getUserIntegral(userIdList);
        postCommentDTOList.forEach(item -> {
            // 有图片查询图片
            String userId = item.getCommentBy();
            item.setImgPathList(
                fileFeign.getImageFileNamePaths(item.getCommentId(), ImageBizType.PostAnswerImg.name()));
            UserRankBaseInfoDTO userRankBaseInfoDTO = userBaseInfoMap.get(userId);
            item.setFullName(userRankBaseInfoDTO.getUserName());
            Optional.ofNullable(orgShowDTOMap.get(userRankBaseInfoDTO.getOrgId())).ifPresent(orgShowDTO -> {
                item.setOrgName(orgShowDTO.getOrgShortName());
                item.setOrgPath(orgShowDTO.getLevelPathName());
            });
            item.setPostComments(replyPostCommentMap.getOrDefault(item.getCommentId(), Collections.emptyList()));
            item.setUserAvatar(userRankBaseInfoDTO.getUserAvatar());
            item.setUserFortune(Optional.ofNullable(userIntegralMap.get(userId)).orElse(BigDecimal.ZERO).intValue());
        });
        return data;
    }

    @Override
    public PageInfo<ForumPostCommentApiDTO> searchKeyWord(PostCommentApiQuery postCommentApiQuery) {
        String userId = UserThreadContext.getUserId();
        PageInfo<ForumPostCommentApiDTO> pageInfo = new PageInfo<>();
        List<ForumPostCommentApiDTO> forumPostCommentApiDTOList = getBaseMapper().selectPostCommentApi(
            postCommentApiQuery.getPostId(), userId, postCommentApiQuery.getSearchKeyWord());
        if (forumPostCommentApiDTOList.isEmpty()) {
            pageInfo.setIsLastPage(true);
            return pageInfo;
        }
        // 二级评论也搜索
        Set<String> commentIdList = forumPostCommentApiDTOList.stream().map(postComment -> {
            // 判断是不是二级评论，一级评论直接返回Id,二级评论返回它的父类Id
            if (StringUtils.equals(postComment.getParentId(), COMMENT_PARENT)) {
                return postComment.getCommentId();
            }
            return postComment.getParentId();
        }).collect(Collectors.toSet());
        pageInfo = PageMethod.startPage(postCommentApiQuery.getPageNo(),
                postCommentApiQuery.getPageSize())
            .doSelectPageInfo(() -> getBaseMapper().selectPostCommentBatch(commentIdList, userId));
        List<ForumPostCommentApiDTO> result = pageInfo.getList();
        if (result.isEmpty()) {
            return pageInfo;
        }
        for (ForumPostCommentApiDTO forumPostCommentApiDTO : pageInfo.getList()) {
            Integer isStar = postVoteService.getCurrentCommentVoteByPOstIdAndUserId(
                forumPostCommentApiDTO.getCommentId(), userId);
            forumPostCommentApiDTO.setIsStar(isStar);
        }
        commonGetReplyComment(result);
        return pageInfo;
    }

    @Override
    public PageInfo<ForumPostCommentApiDTO> getApiPostComment(PostCommentQuery postCommentQuery) {
        String userId = UserThreadContext.getUserId();
        // 查询出一级评论
        PageInfo<ForumPostCommentApiDTO> pageInfo = PageMethod.startPage(postCommentQuery.getPageNo(),
            postCommentQuery.getPageSize(), false).doSelectPageInfo(
            () -> getBaseMapper().selectPostCommentAndChirdenApi(postCommentQuery.getPostId(), userId));
        List<ForumPostCommentApiDTO> list = pageInfo.getList();
        pageInfo.setIsLastPage(postCommentQuery.getPageSize() != list.size());
        if (list.isEmpty()) {
            return pageInfo;
        }
        List<String> userIdList = pageInfo.getList().stream().map(PostCommentBaseDTO::getCommentBy)
            .collect(Collectors.toList());
        List<ForumPostCommentApiDTO> firstCommentsList = pageInfo.getList();
        firstCommentsList.forEach(item -> {
            List<String> secondUserIdList = item.getPostComments().stream().map(PostSubCommentApiDTO::getCommentBy)
                .collect(Collectors.toList());
            userIdList.addAll(secondUserIdList);
        });
        // 用户基本信息映射
        Map<String, UserDTO> userNameMapByIds = userFeign.getUserNameMapByIds(userIdList);
        // 获取一级评论id集合
        Set<String> commentIds = pageInfo.getList().stream().map(ForumPostCommentApiDTO::getCommentId)
            .collect(Collectors.toSet());
        Map<String, List<NamePath>> commentNamepathMap = fileFeign.getImageFileNamePathsByBizIds(commentIds,
            ImageBizType.PostAnswerImg.name()).stream().collect(Collectors.groupingBy(NamePath::getCategoryId));
        Map<String, LecturerDTO> lecturerMapByUserIds = lecturerFeign.getLecturerMapByUserIds(userIdList);
        for (ForumPostCommentApiDTO item : pageInfo.getList()) {
            Long count = lambdaQuery().eq(PostComment::getParentId, item.getCommentId())
                .eq(PostComment::getCheckStatus, 1).eq(PostComment::getIsAvailable, 1).count();
            item.setReplyCount(count.intValue());
            Integer isStar = postVoteService.getCurrentCommentVoteByPOstIdAndUserId(item.getCommentId(), userId);
            item.setIsStar(isStar);
            Integer isPoster = postService.getCurrentPosterByPostIdAndUserId(item.getCommentId(), userId);
            item.setIsPoster(isPoster);
            if (checkPostCommentAuthor(item)) {
                // 非匿名用户信息设置
                String curUserId = item.getCommentBy();
                UserDTO userDTO = userNameMapByIds.get(curUserId);
                item.setFullName(userDTO.getFullName());
                item.setUserAvatar(userDTO.getAvatar());
                // 非匿名讲师表信息设置
                LecturerDTO lecturerByUserId = lecturerMapByUserIds.get(item.getCommentBy());
                Optional.ofNullable(lecturerByUserId).ifPresent(
                    lecturer -> item.setGrade(lecturer.getLevelName()).setCategory(lecturer.getCategoryName()));
            }
            item.setImgPathList(commentNamepathMap.get(item.getCommentId()));
            if (!CollectionUtils.isEmpty(item.getPostComments())) {
                for (PostSubCommentApiDTO subItem : item.getPostComments()) {
                    if (checkPostCommentAuthor(subItem)) {
                        // 非匿名用户信息设置
                        UserDTO userDTO = userNameMapByIds.get(subItem.getCommentBy());
                        subItem.setFullName(userDTO.getFullName());
                        subItem.setUserAvatar(userDTO.getAvatar());
                    }
                    Integer subIsStar = postVoteService.getCurrentCommentVoteByPOstIdAndUserId(subItem.getCommentId(),
                        userId);
                    subItem.setIsStar(subIsStar);
                    Integer subIsPoster = postService.getCurrentPosterByPostIdAndUserId(subItem.getPostId(),
                        subItem.getCommentBy());
                    subItem.setIsPoster(subIsPoster);
                }
            }
        }
        return pageInfo;
    }

    @Override
    public PageInfo<PostSubCommentApiDTO> getSubComment(PostSubCommentQuery postSubCommentQuery) {
        String userId = UserThreadContext.getUserId();
        PageInfo<PostSubCommentApiDTO> pageInfo = PageMethod.startPage(postSubCommentQuery.getPageNo(),
            postSubCommentQuery.getPageSize()).doSelectPageInfo(
            () -> getBaseMapper().selectPostSubCommentApi(postSubCommentQuery.getCommentId(), userId));
        List<PostSubCommentApiDTO> list = pageInfo.getList();
        if (list.isEmpty()) {
            return pageInfo;
        }
        // 处理二级评论部分
        handleSubPostComment(list);
        return pageInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Object> savePostCommentReply(PostCommentReplyDTO postCommentReplyDTO) {
        log.info("save_post_comment_reply_arg:{}", postCommentReplyDTO);
        // 保存回复回帖
        // 禁言处理
        String curUserId = UserThreadContext.getUserId();
        Optional<PostBanUser> postBanUser = postBanUserService.lambdaQuery().eq(PostBanUser::getUserId, curUserId)
            .oneOpt();
        if (commentCheck(postCommentReplyDTO.getContent(), postBanUser)) {
            Result<Object> fail = Result.fail(BusinessExceptionEnum.POST_HAS_SENSITIVE_WORD_EXCEPTION.getValue(),
                BusinessExceptionEnum.CUR_USER_HAS_BEAN_BAN.getName());
            postBanUser.map(PostBanUser::getEndTime).ifPresent(fail::setData);
            return fail;
        }
        String replyCommentId = newId();
        PostComment postComment = new PostComment();
        BeanUtils.copyProperties(postCommentReplyDTO, postComment);
        postComment.setCommentId(replyCommentId);
        postComment.setCommentBy(curUserId);
        postComment.setCommentTime(new Date());
        // 话题审核机制
        String paraValue = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_201.getCode());
        if (paraValue != null && StringUtils.equals(paraValue, "1")) {
            postComment.setCheckStatus(0);
        } else {
            postComment.setCheckStatus(1);
        }
        // 有@专家列表保存用逗号分割
        List<String> specialistUserIdList = postCommentReplyDTO.getSpecialistUserIdList();
        if (!CollectionUtils.isEmpty(specialistUserIdList)) {
            postComment.setPostTo(String.join(",", specialistUserIdList));
            // @专家消息推送
            postService.handlerPushMsg(specialistUserIdList, PushEventEnum.RECEIVED_REPLY, postComment.getPostId(),
                PostServiceImpl.FORUM_PUSH_TYPE, postComment.getContent());
        }
        save(postComment);
        // 刷新回帖回复数
        mqProducer.sendMsg(new ForumCommentReplyEvent(postComment.getParentId()));
        return Result.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Object> savePostReply(PostReplyDTO postReplyDTO) {
        // 评论校验
        String curUserId = UserThreadContext.getUserId();
        // 禁言处理
        Optional<PostBanUser> postBanUser = postBanUserService.lambdaQuery().eq(PostBanUser::getUserId, curUserId)
            .oneOpt();
        if (commentCheck(postReplyDTO.getContent(), postBanUser)) {
            log.info("cur_user_has_bean_ban_user_id:{}", curUserId);
            Result<Object> fail = Result.fail(BusinessExceptionEnum.CUR_USER_HAS_BEAN_BAN.getValue(),
                BusinessExceptionEnum.CUR_USER_HAS_BEAN_BAN.getName());
            postBanUser.map(PostBanUser::getEndTime).ifPresent(fail::setData);
            return fail;
        }
        String commentId = newId();
        PostComment postComment = new PostComment();
        BeanUtils.copyProperties(postReplyDTO, postComment);
        postComment.setCommentId(commentId);
        postComment.setCommentBy(curUserId);
        postComment.setCommentTime(new Date());
        String paraValue = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_201.getCode());
        if (paraValue != null && StringUtils.equals(paraValue, "1")) {
            postComment.setCheckStatus(0);
        } else {
            postComment.setCheckStatus(1);
        }
        // 图片处理
        List<NamePath> imgList = postReplyDTO.getImgList();
        if (!CollectionUtils.isEmpty(imgList)) {
            fileFeign.saveImages(commentId, ImageBizType.PostAnswerImg.name(), imgList);
        }
        // 有@专家列表保存用逗号分割
        List<String> specialistUserIdList = postReplyDTO.getSpecialistUserIdList();
        if (!CollectionUtils.isEmpty(specialistUserIdList)) {
            postComment.setPostTo(String.join(",", specialistUserIdList));
        }
        //  添加话题回复激励事件
        Post post = postService.getById(postReplyDTO.getPostId());
        Optional.ofNullable(post).ifPresent(postItem -> {
            ForumSection section = forumSectionMapper.selectById(postItem.getSectionId());
            Optional.ofNullable(section).ifPresent(sectionItem -> mqProducer.sendMsg(new ExcitationMQEvent(
                new ExcitationMQEventDTO().setEventId(ExcitationEventEnum.replyTopic.name()).setUserId(curUserId)
                    .setTargetId(sectionItem.getSectionId())
                    .setTargetName(sectionItem.getSectionName() + "-" + postItem.getTitle()))));
        });

        // @专家消息推送
        if (!CollectionUtils.isEmpty(specialistUserIdList)) {
            postService.handlerPushMsg(specialistUserIdList, PushEventEnum.RECEIVED_REPLY, postComment.getPostId(),
                PostServiceImpl.FORUM_PUSH_TYPE, postComment.getContent());
        }
        postCommentDao.savePostComment(postComment);
        // 刷新回帖数
        mqProducer.sendMsg(new ForumCommentEvent(postReplyDTO.getPostId()));
        return Result.success();
    }

    private boolean commentCheck(String content, Optional<PostBanUser> postBanUser) {
        List<String> routerIds = routerFeign.getRouterNames();
        // 检验系统版本权限是否包含要校验的内容；
        if(routerIds.contains(ResourceTypeEnum.SENSITIVE_CONFIG.getRouter())){
            // 敏感词校验
            String descStr = sysSensitiveWordFeign.checkWordsReturnStr(content);
            if (!StringUtils.isEmpty(descStr)) {
                throw new BusinessException(BusinessExceptionEnum.COMMENT_HAS_SENSITIVE_WORD_EXCEPTION);
            }
        }
        if (postBanUser.isPresent()) {
            PostBanUser banUser = postBanUser.get();
            // 判断当前时间是否是结束时间之后了
            return new Date().before(banUser.getEndTime());
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePostCommentByCommentId(String commentId) {
        log.info("------deletePostCommentByCommentId----commentId:{}", commentId);
        String curUserId = UserThreadContext.getUserId();
        PostComment postComment = getById(commentId);
        if (postComment != null) {
            Post post = postService.getById(postComment.getPostId());
            if (post == null) {
                return;
            }
            if (!curUserId.equals(postComment.getCommentBy()) && !StringUtils.equals(curUserId, post.getCreateBy())) {
                throw new BusinessException(BusinessExceptionEnum.POST_NOT_DELETE_BY_OTHER_EXCEPTION);
            }
            removeById(postComment);
            // 刷新回帖数
            mqProducer.sendMsg(new ForumCommentEvent(post.getPostId()));
        }
    }

    @Override
    public PostCommentDetailDTO detailComment(String commentId) {
        String userId = UserThreadContext.getUserId();
        // 查询出一级评论
        PostCommentDetailDTO postComment = getBaseMapper().selectPostCommentDetail(commentId, userId);
        if (postComment == null) {
            return null;
        }
        // 先判断是否匿名，减少feign调用
        if (checkPostCommentAuthor(postComment)) {
            UserDTO userInfo = userFeign.getUserById(postComment.getCommentBy());
            postComment.setFullName(userInfo.getFullName()).setUserAvatar(userInfo.getAvatar());
        }
        // 非匿名讲师表信息设置
        LecturerDTO lecturerByUserId = lecturerFeign.getLecturerByUserId(postComment.getCommentBy());
        Optional.ofNullable(lecturerByUserId).ifPresent(
            lecturer -> postComment.setGrade(lecturer.getLevelName()).setCategory(lecturer.getCategoryName()));
        postComment.setImgPathList(fileFeign.getImageFileNamePaths(commentId, ImageBizType.PostAnswerImg.name()));
        return postComment;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteComment(String commentId, Integer status) {
        PostComment postComment = baseMapper.getComment(commentId);

        if (1 == status) {
            postCommentDao.delPostComment(postComment, commentId, status);
        } else {
            postCommentDao.restorePostComment(postComment, commentId, status);
        }

        // 刷新回帖数
        mqProducer.sendMsg(new ForumCommentEvent(postComment.getPostId()));
    }

    @Override
    public PageInfo<CountCommentDTO> countComment(CountPostCommentQuery countPostCommentQuery) {
        log.info("count_comment_arg:{}", countPostCommentQuery);
        String userIdStr = countPostCommentQuery.getUserIdStr();
        Set<String> orgIds = null;
        boolean orgIdEmpty = StringUtils.isEmpty(countPostCommentQuery.getOrgId());
        if (orgIdEmpty) {
            orgIds = orgFeign.getUserManageAreaOrgId(UserThreadContext.getUserId());
        }

        if (StringUtils.isNotEmpty(userIdStr)) {
            String[] userIdArray = userIdStr.split(",");
            countPostCommentQuery.setUserIdList(Arrays.asList(userIdArray));
        }

        // 这个帖子的一级评论全量数据，再去过滤出用户的组织统计
        List<CountCommentDTO> commentDTOList = getBaseMapper().selectCountPostComment(countPostCommentQuery);
        if (commentDTOList.isEmpty()) {
            return new PageInfo<>();
        }

        Set<String> userIds = commentDTOList.stream().map(CountCommentDTO::getCommentBy).collect(Collectors.toSet());
        Map<String, UserOrgDTO> userMap = orgFeign.getUserAndOrgMapByUserIds(userIds);

        Map<String, OrgShowDTO> orgShowDTOMap = orgFeign.getOrgShowDTO(userFeign.getUserOrdIdByUserIds(userIds));
        // 筛选出用户id,没有默认当前用户组织id
        final Set<String> finalOrgIds = orgIds;
        List<String> existUser = userMap.values().stream()
            // 查询条件用户名为空则短路过滤条件 (过滤管辖范围)
            .filter(userDTO -> {
                if (!orgIdEmpty) {
                    return StringUtils.equals(userDTO.getOrgId(), countPostCommentQuery.getOrgId());
                }
                return finalOrgIds.contains(userDTO.getOrgId());
            }).map(UserOrgDTO::getUserId).collect(Collectors.toList());
        if (existUser.isEmpty()) {
            return new PageInfo<>();
        }
        List<CountCommentDTO> result = commentDTOList.stream()
            .filter(comment -> existUser.contains(comment.getCommentBy())).collect(Collectors.toList());
        result.forEach(countCommentDTO -> {
            String commentBy = countCommentDTO.getCommentBy();
            UserOrgDTO userOrgDTO = userMap.get(commentBy);
            countCommentDTO.setUserName(userOrgDTO.getLoginName()).setFullName(userOrgDTO.getFullName());
            Optional.ofNullable(orgShowDTOMap.get(userOrgDTO.getOrgId())).ifPresent(orgShowDTO -> {
                countCommentDTO.setOrgName(orgShowDTO.getOrgShortName()).setOrgPath(orgShowDTO.getLevelPathName());
            });
        });
        List<List<CountCommentDTO>> partition = Lists.partition(result, countPostCommentQuery.getPageSize());
        PageInfo<CountCommentDTO> resultPageInfo = new PageInfo<>();
        // try-resource关闭这个分页资源
        try (Page<CountCommentDTO> resultPage = new Page<>(countPostCommentQuery.getPageNo(),
            countPostCommentQuery.getPageSize())) {
            resultPage.setTotal(result.size());
            resultPage.setPageNum(countPostCommentQuery.getPageNo());
            resultPage.setPageSize(countPostCommentQuery.getPageSize());
            resultPage.addAll(partition.get(countPostCommentQuery.getPageNo() - 1));
            resultPageInfo = resultPage.toPageInfo();
        } catch (Exception e) {
            log.error("count_comment_error", e);
        }
        return resultPageInfo;
    }

    @Override
    public PostVoteResultDto addOrUpdateCommentVote(String commentId) {
        String curUserId = UserThreadContext.getUserId();

        PostComment postComment = getById(commentId);
        if (postComment == null) {
            throw new BusinessException(BusinessExceptionEnum.POST_COMMENT_NOT_EXIST_EXCEPTION);
        }
        Post post = postService.getById(postComment.getPostId());
        PostVoteResultDto postVoteResultDto = new PostVoteResultDto();
        PostVote postVote = postVoteService.lambdaQuery().eq(PostVote::getPostId, commentId)
            .eq(PostVote::getVoteBy, curUserId).one();
        if (postVote == null) {
            postVote = new PostVote();
            postVote.setPostVoteId(StringUtil.newId());
            postVote.setPostId(commentId);
            postVote.setVoteCount(1);
            postVote.setVoteBy(curUserId);
            postVote.setVoteTime(new Date());
            postVote.setCategoryType(post.getPostType());
            postVoteService.save(postVote);
        } else {
            Integer voteCount = postVote.getVoteCount();
            voteCount = voteCount == 1 ? 2 : 1;
            postVote.setVoteCount(voteCount);
            postVoteService.updateById(postVote);
        }

        long likeNum = postVoteService.lambdaQuery().eq(PostVote::getPostId, commentId).eq(PostVote::getVoteCount, 1)
            .count();
        postVoteResultDto.setLikeNum(likeNum);
        // 刷新回帖点赞数
        mqProducer.sendMsg(new ForumCommentLikeEvent(commentId));
        return postVoteResultDto;
    }


    /**
     * 异步执行 二级评论的公共处理部分（即方法getReplyPostComment）
     *
     * @param forumPostCommentApiDTOList 帖子评论对象
     */
    private void commonGetReplyComment(List<ForumPostCommentApiDTO> forumPostCommentApiDTOList) {
        String userId = UserThreadContext.getUserId();
        List<String> userIdList = forumPostCommentApiDTOList.stream().map(PostCommentBaseDTO::getCommentBy)
            .collect(Collectors.toList());
        // 用户基本信息映射
        Map<String, UserDTO> userNameMapByIds = userFeign.getUserNameMapByIds(userIdList);
        // 获取一级评论id集合
        Set<String> commentIds = forumPostCommentApiDTOList.stream().map(ForumPostCommentApiDTO::getCommentId)
            .collect(Collectors.toSet());
        Map<String, List<NamePath>> commentNamepathMap = fileFeign.getImageFileNamePathsByBizIds(commentIds,
            ImageBizType.PostAnswerImg.name()).stream().collect(Collectors.groupingBy(NamePath::getCategoryId));
        Map<String, LecturerDTO> lecturerMapByUserIds = lecturerFeign.getLecturerMapByUserIds(userIdList);
        // 查询出二级评论和一级评论映射
        Map<String, List<PostSubCommentApiDTO>> replyPostCommentMap = getReplyPostComment(() -> {
            ArrayList<PostSubCommentApiDTO> result = new ArrayList<>();
            List<PostSubCommentApiDTO> commentByList = getBaseMapper().selectPostSubCommentByList(commentIds, userId);
            for (PostSubCommentApiDTO postSubCommentApiDTO : commentByList) {
                Integer isStar = postVoteService.getCurrentCommentVoteByPOstIdAndUserId(
                    postSubCommentApiDTO.getCommentId(), userId);
                postSubCommentApiDTO.setIsStar(isStar);
                Integer isPoster = postService.getCurrentPosterByPostIdAndUserId(postSubCommentApiDTO.getPostId(),
                    postSubCommentApiDTO.getCommentBy());
                postSubCommentApiDTO.setIsPoster(isPoster);
            }
            Map<String, List<PostSubCommentApiDTO>> postMap = commentByList.stream()
                .collect(Collectors.groupingBy(PostCommentBaseDTO::getParentId));
            for (List<PostSubCommentApiDTO> value : postMap.values()) {
                // 限制评论3条
                List<PostSubCommentApiDTO> collect = value.stream().limit(3).collect(Collectors.toList());
                result.addAll(collect);
            }
            return result;
        });

        // 这一步feign操作理论也能放入异步处理,只要feign接口能抗住并发
        forumPostCommentApiDTOList.forEach(item -> {
            // 检查匿名情况，替换成匿名信息
            if (checkPostCommentAuthor(item)) {
                // 非匿名用户信息设置
                String curUserId = item.getCommentBy();
                UserDTO userDTO = userNameMapByIds.get(curUserId);
                item.setFullName(userDTO.getFullName());
                item.setUserAvatar(userDTO.getAvatar());
                // 非匿名讲师表信息设置
                LecturerDTO lecturerByUserId = lecturerMapByUserIds.get(item.getCommentBy());
                Optional.ofNullable(lecturerByUserId).ifPresent(
                    lecturer -> item.setGrade(lecturer.getLevelName()).setCategory(lecturer.getCategoryName()));
            }
            item.setImgPathList(commentNamepathMap.get(item.getCommentId()));
            item.setPostComments(replyPostCommentMap.get(item.getCommentId()));
        });
    }

    /**
     * 二级回复客户端和管理公共处理部分
     *
     * @param supplier 函数式接口
     * @return {@link Map}<{@link String}, {@link List}<{@link T}>>
     */
    private <T extends PostCommentBaseDTO> Map<String, List<T>> getReplyPostComment(Supplier<List<T>> supplier) {
        log.info("into_get_reply_post_comment");
        if (supplier == null) {
            return Collections.emptyMap();
        }
        List<T> postCommentDTOList = supplier.get();
        if (postCommentDTOList.isEmpty()) {
            // 不直接返回null,而返回一个空map,防止空指针
            return Collections.emptyMap();
        }
        Map<String, List<T>> postCommentMap = postCommentDTOList.stream()
            .collect(Collectors.groupingBy(PostCommentBaseDTO::getParentId));
        postCommentMap.values().forEach(this::handleSubPostComment);
        return postCommentMap;
    }

    /**
     * 处理子评论的头像和用户名等信息
     *
     * @param postSubComment 二级评论
     */
    private <T extends PostCommentBaseDTO> void handleSubPostComment(List<T> postSubComment) {
        log.info("handle_sub_post_comment:{}", postSubComment);
        List<String> userIds = postSubComment.stream().map(PostCommentBaseDTO::getCommentBy)
            .collect(Collectors.toList());
        Map<String, UserDTO> userMap = userFeign.getUseListByIds(userIds).stream()
            .collect(Collectors.toMap(UserDTO::getId, userDTO -> userDTO));

        postSubComment.forEach(postCommentDTO -> {
            if (checkPostCommentAuthor(postCommentDTO)) {
                Optional.ofNullable(userMap.get(postCommentDTO.getCommentBy())).ifPresent(userDTO -> {
                    postCommentDTO.setUserAvatar(userDTO.getAvatar());
                    postCommentDTO.setFullName(userDTO.getFullName());
                });
            }
        });
    }

    /**
     * 匿名贴检查文章评论是否是作者
     *
     * @param postCommentDTO 文章评论dto
     * @return boolean
     */
    private boolean checkPostCommentAuthor(PostCommentBaseDTO postCommentDTO) {
        String userId = UserThreadContext.getUserId();
        if (postCommentDTO.getIsAnonymous() == 0 || StringUtils.equals(userId, postCommentDTO.getCommentBy())) {
            return true;
        }
        // 匿名贴且当前用户不是发帖人
        postCommentDTO.setFullName("匿名").setCommentBy("anon").setUserAvatar(null);
        return false;
    }

    @Override
    public void exportData(CountPostCommentQuery queryDTO) {
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IPostCommentService, CountCommentDTO>(queryDTO) {

            @Override
            protected IPostCommentService getBean() {
                return SpringUtil.getBean("postCommentService", IPostCommentService.class);
            }

            @Override
            protected PageInfo<CountCommentDTO> getPageInfo() {
                return getBean().countComment((CountPostCommentQuery) queryDTO);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.PostCountComment;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.PostCountComment.getType();
            }

        };
        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public void updatePostCommentInteractNum(String id, String event) {
        switch (event) {
            case ResourceInteractEventRoutingKeyConstants.FORUM_COMMENT_REPLY_EVENT:
                baseMapper.updateReplyNum(id);
                break;
            case ResourceInteractEventRoutingKeyConstants.FORUM_COMMENT_LIKE_EVENT:
                baseMapper.updateStarNum(id);
                break;
            default:
        }
    }

    @Override
    public void exportCountComment(CountPostCommentQuery countPostCommentQuery) {
        // 构建并实现接口类的方法 返回导出的接口类对象
        IExportDataDTO exportDataDTO = buildExportCountCommentDataDTO(countPostCommentQuery);
        exportComponent.exportRecord(exportDataDTO);
    }

    public IExportDataDTO buildExportCountCommentDataDTO(CountPostCommentQuery countPostCommentQuery) {
        return new AbstractExportDataDTO<IPostCommentService, CountCommentDTO>(countPostCommentQuery) {

            @Override
            protected IPostCommentService getBean() {
                return SpringUtil.getBean("postCommentService", IPostCommentService.class);
            }

            @Override
            protected PageInfo<CountCommentDTO> getPageInfo() {
                return getBean().countComment(countPostCommentQuery);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.PostCountComment;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.PostCountComment.getType();
            }

        };
    }

    @Override
    public void saveMarchPostReply(MarchPostReplyDTO postReplyDTO) {
        List<String> routerIds = routerFeign.getRouterNames();
        // 检验系统版本权限是否包含要校验的内容；
        if(routerIds.contains(ResourceTypeEnum.SENSITIVE_CONFIG.getRouter())){
            // 敏感词校验
            String descStr = sysSensitiveWordFeign.checkWordsReturnStr(postReplyDTO.getContent());
            if (!StringUtils.isEmpty(descStr)) {
                throw new BusinessException(BusinessExceptionEnum.COMMENT_HAS_SENSITIVE_WORD_EXCEPTION);
            }
        }
        // 评论校验
        String curUserId = UserThreadContext.getUserId();
        String commentId = newId();
        PostComment postComment = new PostComment();
        BeanUtils.copyProperties(postReplyDTO, postComment);
        postComment.setCommentId(commentId);
        postComment.setCommentBy(curUserId);
        postComment.setCommentTime(new Date());
        postComment.setCheckStatus(1);
        postComment.setIsAnonymous(0);
        postComment.setIsAvailable(AvailableEnum.AVAILABLE.getValue());
        // 图片处理
        List<PostNamePath> postImgList = postReplyDTO.getImgList();
        if (!CollectionUtils.isEmpty(postImgList)) {
            List<NamePath> imgList = postImgList.stream().map(item -> {
                NamePath namePath = new NamePath();
                BeanUtils.copyProperties(item, namePath);
                return namePath;
            }).collect(Collectors.toList());
            fileFeign.saveImages(commentId, ImageBizType.PostAnswerImg.name(), imgList);
        }

        postCommentDao.savePostComment(postComment);
        if (StringUtils.equals(StringPool.ZERO, postComment.getParentId())) {
            // 刷新回帖数
            mqProducer.sendMsg(new ForumCommentEvent(postReplyDTO.getPostId()));
        } else {
            // 刷新回帖回复数
            mqProducer.sendMsg(new ForumCommentReplyEvent(postComment.getParentId()));
        }
    }
}
