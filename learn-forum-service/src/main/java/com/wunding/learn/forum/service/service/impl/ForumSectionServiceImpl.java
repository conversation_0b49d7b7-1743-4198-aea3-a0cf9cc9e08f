package com.wunding.learn.forum.service.service.impl;

import static com.wunding.learn.common.util.string.StringUtil.newId;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.wunding.learn.common.bean.Result;
import com.wunding.learn.common.constant.forum.ForumErrorNoEnum;
import com.wunding.learn.common.constant.other.AvailableEnum;
import com.wunding.learn.common.constant.other.JudgeEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.dto.FileDTO;
import com.wunding.learn.common.enums.exam.IsTrainEnum;
import com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.i18n.util.I18nUtil;
import com.wunding.learn.common.i18n.util.ImportTemplateI18nEnum;
import com.wunding.learn.common.mq.dto.ResourceConfigInitDTO;
import com.wunding.learn.common.mq.event.ResourceInteractEvent.ResourceInteractEventRoutingKeyConstants;
import com.wunding.learn.common.mq.event.excitation.ExcitationInitMqEvent;
import com.wunding.learn.common.mq.service.MqProducer;
import com.wunding.learn.common.mybatis.service.impl.BaseServiceImpl;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.constant.ImageBizType;
import com.wunding.learn.file.api.dto.AbstractExportDataDTO;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.file.api.dto.NamePath;
import com.wunding.learn.file.api.service.FileFeign;
import com.wunding.learn.forum.service.admin.dto.ForumSectionDTO;
import com.wunding.learn.forum.service.admin.dto.SaveForumSectionDTO;
import com.wunding.learn.forum.service.admin.dto.SectionAuditDTO;
import com.wunding.learn.forum.service.admin.dto.SimpleForumSectionDTO;
import com.wunding.learn.forum.service.admin.query.ForumSectionQuery;
import com.wunding.learn.forum.service.admin.query.SimpleSectionQuery;
import com.wunding.learn.forum.service.client.dto.ForumSectionApiDTO;
import com.wunding.learn.forum.service.client.dto.ForumSectionDetailDTO;
import com.wunding.learn.forum.service.client.dto.SaveSectionApiDTO;
import com.wunding.learn.forum.service.client.dto.UpdateSectionPicDTO;
import com.wunding.learn.forum.service.client.query.ForumMyCreateSectionQuery;
import com.wunding.learn.forum.service.client.query.ForumSectionApiQuery;
import com.wunding.learn.forum.service.component.ForumViewLimitComponent;
import com.wunding.learn.forum.service.dao.ForumSectionDao;
import com.wunding.learn.forum.service.enums.BusinessExceptionEnum;
import com.wunding.learn.forum.service.mapper.ForumSectionMapper;
import com.wunding.learn.forum.service.model.ForumSection;
import com.wunding.learn.forum.service.model.Post;
import com.wunding.learn.forum.service.service.IForumSectionService;
import com.wunding.learn.forum.service.service.IPostCashService;
import com.wunding.learn.forum.service.service.IPostService;
import com.wunding.learn.user.api.dto.OrgDTO;
import com.wunding.learn.user.api.dto.OrgShowDTO;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.dto.viewlimit.ViewLimitMainSaveDTO;
import com.wunding.learn.user.api.dto.viewlimit.ViewLimitProgrammeInfoDTO;
import com.wunding.learn.user.api.service.OrgFeign;
import com.wunding.learn.user.api.service.UserFeign;
import com.wunding.learn.user.api.service.ViewLimitFeign;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p> 版块表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">chh</a>
 * @since 2022-06-15
 */
@Slf4j
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Service("forumSectionService")
public class ForumSectionServiceImpl extends BaseServiceImpl<ForumSectionMapper, ForumSection> implements
    IForumSectionService {

    /**
     * 最顶一级组织
     */
    private static final String MASTER_ORG = "0";
    /**
     * 后台添加
     */
    private static final Integer IS_BACKGROUND = 1;
    /**
     * 已审核
     */
    private static final Integer IS_AUDIT = 2;
    /**
     * 客户端
     */
    private static final Integer CLIENT = 2;
    /**
     * 是否是公共模块
     */
    private static final Integer IS_PUBLIC = 1;

    @Resource
    private ForumViewLimitComponent forumViewLimitComponent;
    @Resource
    private FileFeign fileFeign;
    @Resource
    private OrgFeign orgFeign;
    @Resource
    private UserFeign userFeign;
    @Resource
    private ExportComponent exportComponent;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private IPostCashService postCashService;
    @Resource
    private ViewLimitFeign viewLimitFeign;
    @Resource(name = "forumSectionDao")
    private ForumSectionDao forumSectionDao;
    @Resource
    @Lazy
    private IPostService postService;

    @Override
    public void markSection(SectionAuditDTO sectionAuditDTO) {
        List<ForumSection> forumSections = listByIds(sectionAuditDTO.getSectionIds());
        forumSections.forEach(e -> {
            e.setAuditStatus(sectionAuditDTO.getIsAudit());
            forumSectionDao.updateForumSection(e);
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> saveForumSection(SaveForumSectionDTO saveForumSectionDTO) {
        log.info("saveForumSection-----SaveForumSectionDTO:{}", saveForumSectionDTO);
        // 非管理员同一组织id下的板块只能有一个名字
        String orgId = "";
        String userId = UserThreadContext.getUserId();
        OrgDTO orgByUserId = orgFeign.getOrgByUserId(userId);
        if (orgByUserId != null) {
            orgId = orgByUserId.getId();
        }
        // 如果是系统内置角色，则不按orgId查询,反之用户是系统内置角色则只筛选orgId为 0 的情况
        boolean exists = lambdaQuery().eq(ForumSection::getSectionName, saveForumSectionDTO.getSectionName())
            .eq(ForumSection::getOrgId, orgId)
            .eq(ForumSection::getIsTrain, Optional.ofNullable(saveForumSectionDTO.getIsTrain()).orElse(0))
            .eq(StringUtils.isNotEmpty(saveForumSectionDTO.getProjectId()), ForumSection::getProjectId,
                saveForumSectionDTO.getProjectId()).exists();
        if (exists) {
            log.info("forum_section_name_already_existed:{}", saveForumSectionDTO);
            throw new BusinessException(ForumErrorNoEnum.ERR_FORUM_SECTION_EXISTS);
        }
        String sectionId = newId();
        ForumSection forumSection = new ForumSection();
        BeanUtils.copyProperties(saveForumSectionDTO, forumSection);
        forumSection.setSectionId(sectionId);
        forumSection.setOrgId(orgId);
        forumSection.setDataSource(IS_BACKGROUND);
        // 后台创建直接是已审核状态
        forumSection.setAuditStatus(IS_AUDIT);
        // 公共处理模块
        commonSaveOrUpdateOperation(saveForumSectionDTO, sectionId);
        log.info("forumSection:{}", forumSection);
        forumSectionDao.saveForumSection(forumSection);
        // 初始化话题板块激励规则
        mqProducer.sendMsg(new ExcitationInitMqEvent(
            new ResourceConfigInitDTO().setResourceId(forumSection.getSectionId())
                .setResourceType(ExcitationEventCategoryEnum.TOPIC.getCode())));
        return Result.success(sectionId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> updateForumSection(SaveForumSectionDTO saveForumSectionDTO) {
        String orgId = UserThreadContext.getOrgId();
        String sectionId = saveForumSectionDTO.getSectionId();
        // 如果是系统内置角色，则不按orgId查询
        boolean exists = lambdaQuery().eq(ForumSection::getSectionName, saveForumSectionDTO.getSectionName())
            .eq(ForumSection::getOrgId, orgId)
            .eq(ForumSection::getIsTrain, Optional.ofNullable(saveForumSectionDTO.getIsTrain()).orElse(0))
            .eq(StringUtils.isNotEmpty(saveForumSectionDTO.getProjectId()), ForumSection::getProjectId,
                saveForumSectionDTO.getProjectId()).ne(ForumSection::getSectionId, sectionId).exists();
        if (exists) {
            log.info("forum_section_name_already_existed:{}", saveForumSectionDTO);
            throw new BusinessException(ForumErrorNoEnum.ERR_FORUM_SECTION_EXISTS);
        }
        // 校验是否关闭启用
        if (Objects.equals(AvailableEnum.NOT_AVAILABLE.getValue(), saveForumSectionDTO.getIsEnable())
            && postService.queryCountBySectionId(saveForumSectionDTO.getSectionId()) > 0) {
            throw new BusinessException(ForumErrorNoEnum.ERR_FORUM_SECTION_NOT_DISABLE);
        }
        ForumSection forumSection = new ForumSection();
        BeanUtils.copyProperties(saveForumSectionDTO, forumSection);
        if (Objects.nonNull(saveForumSectionDTO.getPicUrl())) {
            NamePath namePath = fileFeign.getImageFileNamePath(sectionId, ImageBizType.SectionPicImage.name());
            Optional.ofNullable(namePath).map(NamePath::getPath).ifPresent(path -> {
                // 图片发生变更，删除图片
                if (!path.equals(saveForumSectionDTO.getPicUrl().getPath())) {
                    fileFeign.deleteImageByBizIdAndBizType(sectionId, ImageBizType.SectionPicImage.name());
                }
            });
        }
        if (Objects.nonNull(saveForumSectionDTO.getBackPicUrl())) {
            NamePath namePath = fileFeign.getImageFileNamePath(sectionId, ImageBizType.SectionBackPicImage.name());
            Optional.ofNullable(namePath).map(NamePath::getPath).ifPresent(path -> {
                // 图片发生变更，删除图片
                if (!path.equals(saveForumSectionDTO.getBackPicUrl().getPath())) {
                    fileFeign.deleteImageByBizIdAndBizType(sectionId, ImageBizType.SectionBackPicImage.name());
                }
            });
        }
        // 公共处理模块
        commonSaveOrUpdateOperation(saveForumSectionDTO, sectionId);
        return Result.success(forumSectionDao.updateForumSection(forumSection));
    }

    @Override
    public PageInfo<ForumSectionDTO> getForumSection(ForumSectionQuery forumSectionQuery) {
        String userId = UserThreadContext.getUserId();

        Set<String> manageAreaOrgId = orgFeign.findUserManageAreaLevelPath(userId);
        forumSectionQuery.setManagerAreaOrgIds(manageAreaOrgId);
        forumSectionQuery.setCurrentOrgId(UserThreadContext.getOrgId());
        forumSectionQuery.setCurrentUserId(UserThreadContext.getUserId());

        PageInfo<ForumSection> pageData = PageMethod.startPage(forumSectionQuery.getPageNo(),
            forumSectionQuery.getPageSize()).doSelectPageInfo(() -> baseMapper.getForumSection(forumSectionQuery));

        List<ForumSection> sectionList = pageData.getList();
        if (sectionList.isEmpty()) {
            PageInfo<ForumSectionDTO> result = new PageInfo<>();
            BeanUtils.copyProperties(pageData, result);
            return result;
        }

        Set<String> orgIdList = sectionList.stream().map(ForumSection::getOrgId).collect(Collectors.toSet());
        Map<String, OrgShowDTO> orgShowDTOMap = orgFeign.getOrgShowDTO(orgIdList);

        List<String> userIds = sectionList.stream().map(ForumSection::getCreateBy).collect(Collectors.toList());
        Map<String, UserDTO> userMap = userFeign.getUseListByIds(userIds).stream()
            .collect(Collectors.toMap(UserDTO::getId, userDTO -> userDTO));

        List<ForumSectionDTO> resultList = sectionList.stream().map(forumSection -> {
            ForumSectionDTO forumSectionDTO = new ForumSectionDTO();
            BeanUtils.copyProperties(forumSection, forumSectionDTO);
            Optional.ofNullable(orgShowDTOMap.get(forumSection.getOrgId())).ifPresent(orgShowDTO -> {
                forumSectionDTO.setOrgName(orgShowDTO.getOrgShortName());
                forumSectionDTO.setOrgPath(orgShowDTO.getLevelPathName());
            });
            Optional.ofNullable(userMap.get(forumSection.getCreateBy()))
                .ifPresent(user -> forumSectionDTO.setUserName(user.getFullName()));
            return forumSectionDTO;
        }).collect(Collectors.toList());

        PageInfo<ForumSectionDTO> result = new PageInfo<>();
        BeanUtils.copyProperties(pageData, result);
        result.setList(resultList);
        return result;
    }

    @Override
    public ForumSectionDTO getDetailForumSection(String sectionId) {
        return getDetailInfo(sectionId, forumSection -> {
            log.info("admin_get_detail_info_into:{}", forumSection);
            ForumSectionDTO forumSectionDTO = new ForumSectionDTO();
            if (null == forumSection) {
                return forumSectionDTO;
            }
            BeanUtils.copyProperties(forumSection, forumSectionDTO);
            forumSectionDTO.setPicUrl(fileFeign.getImageFileNamePath(sectionId, ImageBizType.SectionPicImage.name()));
            forumSectionDTO.setBackPicUrl(
                fileFeign.getImageFileNamePath(sectionId, ImageBizType.SectionBackPicImage.name()));
            // 获取下发范围
            forumSectionDTO.setLimit(forumViewLimitComponent.getViewLimitBaseInfo(sectionId));
            return forumSectionDTO;
        });
    }

    @Override
    public ForumSectionDetailDTO getDetailForumSectionForApi(String sectionId) {
        return getDetailInfo(sectionId, forumSection -> {
            log.info("into_client_get_detail_info:{}", forumSection);
            ForumSectionDetailDTO forumSectionDetailDTO = new ForumSectionDetailDTO();
            if (null == forumSection) {
                return forumSectionDetailDTO;
            }
            String userId = UserThreadContext.getUserId();
            BeanUtils.copyProperties(forumSection, forumSectionDetailDTO);
            forumSectionDetailDTO.setIsOpen(forumSection.getIsEnable());
            forumSectionDetailDTO.setPicUrl(
                fileFeign.getImageFileNamePath(sectionId, ImageBizType.SectionPicImage.name()));
            forumSectionDetailDTO.setBackPicUrl(
                fileFeign.getImageFileNamePath(sectionId, ImageBizType.SectionBackPicImage.name()));
            // 当前用户是否是版主
            if (StringUtils.equals(userId, forumSection.getCreateBy())) {
                forumSectionDetailDTO.setIsModerator(1);
            } else {
                userId = forumSection.getCreateBy();
            }
            forumSectionDetailDTO.setModerator(userFeign.getUserFullNameById(userId));
            forumSectionDetailDTO.setModeratorId(userId);
            return forumSectionDetailDTO;
        });
    }


    @Override
    public List<Post> checkSectionName(String projectId, List<Post> postList, List<String> message,
        Integer isTrain, Collection<String> managerAreaOrgIds) {
        ArrayList<Post> result = new ArrayList<>();
        List<String> sectionNameList = postList.stream().map(Post::getSectionId).collect(Collectors.toList());
        // 用户非最顶级组织则通过自己的组织Id去查询
        List<ForumSection> sectionList = lambdaQuery()
            .select(ForumSection::getSectionId, ForumSection::getSectionName, ForumSection::getIsEnable)
            .in(ForumSection::getSectionName, sectionNameList)
            .in(StringUtils.isEmpty(projectId), ForumSection::getOrgId, managerAreaOrgIds)
            .eq(StringUtils.isEmpty(projectId), ForumSection::getIsTrain, IsTrainEnum.ITSELF.getValue())
            .eq(StringUtils.isNotEmpty(projectId), ForumSection::getProjectId, projectId).or()
            .eq(StringUtils.isEmpty(projectId), ForumSection::getIsPublic, IS_PUBLIC).list();
        Map<String, ForumSection> sectionMap = sectionList.stream()
            .collect(Collectors.toMap(ForumSection::getSectionName, Function.identity(), (k1, k2) -> k1));
        //根据板块名称查询板块，板块真的没有查到才算是不存在
        Set<String> sectionNameSet = lambdaQuery().select(ForumSection::getSectionId, ForumSection::getSectionName,
                ForumSection::getIsEnable).in(ForumSection::getSectionName, sectionNameList)
            .eq(StringUtils.isEmpty(projectId), ForumSection::getIsTrain, IsTrainEnum.ITSELF.getValue())
            .eq(StringUtils.isNotEmpty(projectId), ForumSection::getProjectId, projectId)
            .list().stream()
            .map(ForumSection::getSectionName).collect(Collectors.toSet());

        AtomicInteger i = new AtomicInteger(0);
        postList.forEach(post -> {
            i.getAndIncrement();
            if (!sectionNameSet.contains(post.getSectionId())) {
                message.add(
                    I18nUtil.getImportMessage(ImportTemplateI18nEnum.ForumSectionServiceImplForumSectionNotExist,
                        i.get()));
                return;
            }
            // 所属话题版块信息
            ForumSection section = sectionMap.get(post.getSectionId());
            // 校验话题版块
            if (Objects.isNull(section)) {
                message.add(I18nUtil.getImportMessage(
                    ImportTemplateI18nEnum.ForumSectionServiceImplNoPermissionToImportForumOfThisForumSection,
                    i.get()));
                return;
            }
            if (Objects.equals(AvailableEnum.NOT_AVAILABLE.getValue(), section.getIsEnable())) {
                message.add(I18nUtil.getImportMessage(ImportTemplateI18nEnum.ForumSectionServiceImplForumSectionDisable,
                    i.get()));
                return;
            }
            post.setSectionId(section.getSectionId());
            post.setProjectId(projectId);
            post.setIsPublic(1);
            // 0-非项目。1-学习项目 2-周期项目 3-面授班级
            post.setIsTrain(isTrain);
            post.setIsAvailable(1);
            result.add(post);
        });
        return result;
    }

    @Override
    public List<ForumSectionApiDTO> getAllPageSection(ForumSectionApiQuery forumSectionApiQuery) {
        // 设置查询默认参数值
        forumSectionApiQuery.setUserId(UserThreadContext.getUserId());

        forumSectionApiQuery.setIsPublic(1);
        forumSectionApiQuery.setIsEnable(1);
        // 查询版块
        List<ForumSectionApiDTO> list = getBaseMapper().pageSection(forumSectionApiQuery);

        if (!list.isEmpty()) {
            // 查询版块封面图片
            Set<String> sectionIds = list.stream().map(ForumSectionApiDTO::getSectionId).collect(Collectors.toSet());
            Map<String, NamePath> picUrlMap = fileFeign.getImageFileNamePathsByBizIds(sectionIds,
                    ImageBizType.SectionPicImage.name()).stream()
                .collect(Collectors.toMap(NamePath::getCategoryId, namePath -> namePath, (key1, key2) -> key1));
            // 查询版主用户信息
            Set<String> userIds = list.stream().map(ForumSectionApiDTO::getModeratorId).collect(Collectors.toSet());
            Map<String, UserDTO> userMap = userFeign.getUserNameByUserIds(userIds);
            // 响应数据处理
            list.forEach(dto -> {
                dto.setIsModerator(Objects.equals(dto.getModeratorId(), forumSectionApiQuery.getUserId()) ? 1 : 0);
                dto.setPicUrl(picUrlMap.get(dto.getSectionId()));
                dto.setModerator(userMap.get(dto.getModeratorId()).getFullName());
            });
        }
        return list;
    }

    @Override
    public PageInfo<ForumSectionApiDTO> myCreateSection(ForumMyCreateSectionQuery forumSectionApiQuery) {
        String userId = UserThreadContext.getUserId();
        ForumSectionApiQuery sectionApiQuery = new ForumSectionApiQuery();
        BeanUtils.copyProperties(forumSectionApiQuery, sectionApiQuery);
        sectionApiQuery.setUserId(userId);
        PageInfo<ForumSectionApiDTO> pageInfo = PageMethod.startPage(forumSectionApiQuery.getPageNo(),
            forumSectionApiQuery.getPageSize()).doSelectPageInfo(() -> getBaseMapper().pageSection(sectionApiQuery));

        List<ForumSectionApiDTO> forumSectionApiDTOList = pageInfo.getList();
        if (forumSectionApiDTOList.isEmpty()) {
            return pageInfo;
        }

        forumSectionApiDTOList.forEach(item -> item.setModerator(userFeign.getUserFullNameById(userId))
            .setPicUrl(fileFeign.getImageFileNamePath(item.getSectionId(), ImageBizType.SectionPicImage.name())));

        return pageInfo;
    }

    @Override
    public void deleteSection(String sectionId) {
        String userId = UserThreadContext.getUserId();
        // 版主才能删除板块,板块Id的版主和当前登录用户匹配上才会删除掉
        lambdaUpdate().set(ForumSection::getIsDel, 1).eq(ForumSection::getSectionId, sectionId)
            .eq(ForumSection::getCreateBy, userId).update();
        postCashService.removeSectionById(sectionId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createSection(SaveSectionApiDTO saveSectionApiDTO) {
        log.info("forum_section_create:{}", saveSectionApiDTO);
        String orgId = UserThreadContext.getOrgId();
        boolean condition = (saveSectionApiDTO.getIsPublic() != null && saveSectionApiDTO.getIsPublic() == 0);
        boolean exists = lambdaQuery().eq(ForumSection::getSectionName, saveSectionApiDTO.getSectionName())
            .eq(condition, ForumSection::getOrgId, orgId).eq(!condition, ForumSection::getOrgId, MASTER_ORG)
            .eq(ForumSection::getIsTrain, 0)
            .ne(StringUtils.isNotEmpty(saveSectionApiDTO.getSectionId()), ForumSection::getSectionId,
                saveSectionApiDTO.getSectionId()).exists();
        if (exists) {
            throw new BusinessException(BusinessExceptionEnum.ALREADY_A_SECTOR_EXPERT);
        }
        ForumSection forumSection = new ForumSection();
        BeanUtils.copyProperties(saveSectionApiDTO, forumSection);
        // 已驳回的板块修改状态
        if (StringUtils.isEmpty(saveSectionApiDTO.getSectionId())) {
            forumSection.setSectionId(newId());
        } else {
            forumSection.setAuditStatus(IS_BACKGROUND);
        }
        forumSection.setOrgId(orgId);
        forumSection.setDataSource(CLIENT);
        boolean result = saveOrUpdate2(forumSection);
        if (!result) {
            return false;
        }
        mqProducer.sendMsg(new ExcitationInitMqEvent(
            new ResourceConfigInitDTO().setResourceId(forumSection.getSectionId())
                .setResourceType(ExcitationEventCategoryEnum.TOPIC.getCode())));
        String sectionId = forumSection.getSectionId();

        // 保存推送范围
        TreeSet<ViewLimitMainSaveDTO> viewLimitMainSaveDTOList = new TreeSet<>();
        ViewLimitMainSaveDTO viewLimitMainSaveDTO = new ViewLimitMainSaveDTO();
        // 判断是否是公共板块
        if (condition) {
            // 选择部门下发范围
            viewLimitMainSaveDTO.setViewId(orgId).setViewType(0).setLimitType(0);
        } else {
            // 默认下范围（全公司）
            viewLimitMainSaveDTO.setViewId(MASTER_ORG).setViewType(0).setLimitType(0);
        }
        viewLimitMainSaveDTOList.add(viewLimitMainSaveDTO);
        ViewLimitProgrammeInfoDTO viewLimitProgrammeInfoDTO = viewLimitFeign.saveViewLimit(viewLimitMainSaveDTOList);
        forumViewLimitComponent.handleNewViewLimit(viewLimitProgrammeInfoDTO.getId(), sectionId);

        // 板块封面图片保存
        FileDTO sectionImg = saveSectionApiDTO.getSectionImg();
        if (sectionImg != null && StringUtils.isNotEmpty(sectionImg.getPath())) {
            // 删除图片再添加
            fileFeign.deleteImageByBizIdAndBizType(sectionId, ImageBizType.SectionPicImage.name());
            // 拷贝临时目录至正式目录
            fileFeign.saveImage(sectionId, ImageBizType.SectionPicImage.name(), sectionImg.getName(),
                sectionImg.getPath());
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSection(SaveSectionApiDTO updateSectionApiDTO) {
        log.info("forum_section_update:{}", updateSectionApiDTO);
        String orgId = UserThreadContext.getOrgId();
        String userId = UserThreadContext.getUserId();
        String sectionId = updateSectionApiDTO.getSectionId();
        Integer isPublic = updateSectionApiDTO.getIsPublic();

        ForumSection forumSection = new ForumSection();
        BeanUtils.copyProperties(updateSectionApiDTO, forumSection);
        forumSection.setOrgId(orgId);
        boolean result = lambdaUpdate().eq(ForumSection::getCreateBy, userId).eq(ForumSection::getSectionId, sectionId)
            .update(forumSection);
        if (!result) {
            return false;
        }

        // 非公共且组织Id非最顶级组织则设置下发范围
        TreeSet<ViewLimitMainSaveDTO> viewLimitMainSaveDTOList = new TreeSet<>();
        ViewLimitMainSaveDTO viewLimitMainSaveDTO = new ViewLimitMainSaveDTO();
        if (isPublic != null && isPublic == 0) {
            // 选择部门下发范围
            viewLimitMainSaveDTO.setViewId(orgId).setViewType(0).setLimitType(0);
        } else {
            // 默认下范围（全公司）
            viewLimitMainSaveDTO.setViewId(MASTER_ORG).setViewType(0).setLimitType(0);
        }
        viewLimitMainSaveDTOList.add(viewLimitMainSaveDTO);
        ViewLimitProgrammeInfoDTO viewLimitProgrammeInfoDTO = viewLimitFeign.saveViewLimit(viewLimitMainSaveDTOList);
        forumViewLimitComponent.handleNewViewLimit(viewLimitProgrammeInfoDTO.getId(), sectionId);

        // 板块封面图片保存
        FileDTO sectionImg = updateSectionApiDTO.getSectionImg();
        if (sectionImg != null) {
            // 查一遍看有没有变化
            NamePath namePath = fileFeign.getImageFileNamePath(sectionId, ImageBizType.SectionPicImage.name());
            Optional.ofNullable(namePath).map(NamePath::getPath).ifPresent(path -> {
                // 图片发生变更，删除图片
                if (!path.equals(sectionImg.getPath())) {
                    fileFeign.deleteImageByBizIdAndBizType(sectionId, ImageBizType.SectionPicImage.name());
                    // 拷贝临时目录至正式目录
                    fileFeign.saveImage(sectionId, ImageBizType.SectionPicImage.name(), sectionImg.getName(),
                        sectionImg.getPath());
                }
            });

        }
        postCashService.removeSectionById(updateSectionApiDTO.getSectionId());
        return true;
    }

    @Override
    public void updateSectionPic(UpdateSectionPicDTO updateSectionPicDTO) {
        log.info("update_section_pic_arg:{}", updateSectionPicDTO);
        String sectionId = updateSectionPicDTO.getSectionId();
        String userId = UserThreadContext.getUserId();
        ForumSection section = getById(sectionId);
        // 非版主不能修改
        if (!StringUtils.equals(section.getCreateBy(), userId)) {
            throw new BusinessException(BusinessExceptionEnum.SECTION_NOT_DELETE_BY_OTHER_EXCEPTION);
        }

        FileDTO sectionImg = updateSectionPicDTO.getSectionImg();
        if (sectionImg != null) {
            // 先删除图片
            fileFeign.deleteImageByBizIdAndBizType(sectionId, ImageBizType.SectionPicImage.name());
            // 拷贝临时目录至正式目录
            fileFeign.saveImage(sectionId, ImageBizType.SectionPicImage.name(), sectionImg.getName(),
                sectionImg.getPath());
        }
        FileDTO sectionBackImg = updateSectionPicDTO.getSectionBackImg();
        if (sectionBackImg != null) {
            // 先删除图片
            fileFeign.deleteImageByBizIdAndBizType(sectionId, ImageBizType.SectionBackPicImage.name());
            // 拷贝临时目录至正式目录
            fileFeign.saveImage(sectionId, ImageBizType.SectionBackPicImage.name(), sectionBackImg.getName(),
                sectionBackImg.getPath());
        }
        postCashService.removeSectionById(updateSectionPicDTO.getSectionId());
    }


    /**
     * 添加和更新的一些公共操作
     *
     * @param saveForumSectionDTO 板块信息dto
     * @param sectionId           板块id
     */
    private void commonSaveOrUpdateOperation(SaveForumSectionDTO saveForumSectionDTO, String sectionId) {
        log.info("comment_save_or_update_operation_arg:{}", saveForumSectionDTO);
        // 话题板块公开的情况下默认下发范围是所有人可见
        if (Objects.equals(saveForumSectionDTO.getIsPublic(), JudgeEnum.CONFIRM.getValue())){
            TreeSet<ViewLimitMainSaveDTO> viewLimitMainSaveDTOList = new TreeSet<>();
            ViewLimitMainSaveDTO viewLimitMainSaveDTO = new ViewLimitMainSaveDTO();
            // 获取下发范围的id
            viewLimitMainSaveDTO.setViewId("0").setViewType(0).setLimitType(0);
            viewLimitMainSaveDTOList.add(viewLimitMainSaveDTO);
            ViewLimitProgrammeInfoDTO viewLimitProgrammeInfoDTO = viewLimitFeign.saveViewLimit(viewLimitMainSaveDTOList);
            saveForumSectionDTO.setProgrammeId(viewLimitProgrammeInfoDTO.getId());
        }
        // 存在下发范围
        boolean hasViewLimitList =
            saveForumSectionDTO.getViewType() != null && saveForumSectionDTO.getProgrammeId() != null;
        if (hasViewLimitList) {
            log.info("exec_forum_view_limit:{},section_id:{}", saveForumSectionDTO.getProgrammeId(), sectionId);
            forumViewLimitComponent.handleNewViewLimit(saveForumSectionDTO.getProgrammeId(), sectionId);
        } else if (StringUtils.isNotEmpty(saveForumSectionDTO.getProjectId())
            && saveForumSectionDTO.getProgrammeId() != null) {
            forumViewLimitComponent.handleNewViewLimit(saveForumSectionDTO.getProgrammeId(), sectionId);
        }

        if (Objects.nonNull(saveForumSectionDTO.getPicUrl()) && StringUtils.isNotEmpty(
            saveForumSectionDTO.getPicUrl().getPath())) {
            // 拷贝临时目录至正式目录
            fileFeign.saveImage(sectionId, ImageBizType.SectionPicImage.name(),
                saveForumSectionDTO.getPicUrl().getName(), saveForumSectionDTO.getPicUrl().getPath());
        }
        // 保存背景图片
        if (Objects.nonNull(saveForumSectionDTO.getBackPicUrl()) && StringUtils.isNotEmpty(
            saveForumSectionDTO.getBackPicUrl().getPath())) {
            // 拷贝临时目录至正式目录
            fileFeign.saveImage(sectionId, ImageBizType.SectionBackPicImage.name(),
                saveForumSectionDTO.getBackPicUrl().getName(), saveForumSectionDTO.getBackPicUrl().getPath());
        }
        postCashService.removeSectionById(sectionId);
    }

    @Override
    public List<SimpleForumSectionDTO> findSimpleForumSectionList(SimpleSectionQuery simpleSectionQuery) {
        String userId = UserThreadContext.getUserId();
        String orgId = UserThreadContext.getOrgId();

        ForumSectionQuery forumSectionQuery = new ForumSectionQuery().setIsTrain(
                Optional.ofNullable(simpleSectionQuery.getIsTrain()).orElse(0)).setAuditStatus(IS_AUDIT)
            .setIsEnable(GeneralJudgeEnum.CONFIRM.getValue());
        forumSectionQuery.setCurrentOrgId(orgId);
        forumSectionQuery.setCurrentUserId(userId);

        //查询用户的管辖范围，根据用户的管辖范围和输入条件来查询数据
        Set<String> managerAreaOrgIds = orgFeign.findUserManageAreaLevelPath(UserThreadContext.getUserId());
        forumSectionQuery.setManagerAreaOrgIds(managerAreaOrgIds);
        if (StringUtils.isNotEmpty(simpleSectionQuery.getProjectId())) {
            forumSectionQuery.setProjectId(simpleSectionQuery.getProjectId());
        }
        List<ForumSection> forumSectionList = baseMapper.getForumSection(forumSectionQuery);
        return forumSectionList.stream().map(forumSection -> {
            SimpleForumSectionDTO simpleForumSectionDTO = new SimpleForumSectionDTO();
            BeanUtils.copyProperties(forumSection, simpleForumSectionDTO);
            return simpleForumSectionDTO;
        }).collect(Collectors.toList());
    }

    /**
     * 客户端和服务端对获取详细信息的不同部分的操作
     *
     * @param sectionId 部分id
     * @param function  函数
     * @return {@link T}
     */
    private <T> T getDetailInfo(String sectionId, Function<ForumSection, T> function) {
        ForumSection forumSection = getById(sectionId);
        return forumSection == null ? null : function.apply(forumSection);
    }

    @Override
    @Async
    public void exportPostSection(ForumSectionQuery forumSectionQuery) {
        // 构建并实现接口类的方法 返回导出的接口类对象
        IExportDataDTO exportDataDTO = buildExportPostSectionDataDTO(forumSectionQuery);
        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public void updateSectionInteractNum(String id, String event) {
        if (ResourceInteractEventRoutingKeyConstants.FORUM_POST_EVENT.equals(event)) {
            baseMapper.updatePostNum(id);
        }
    }

    /**
     * 构建并实现接口类的方法 返回导出的接口类对象
     *
     * @param forumSectionQuery 查询参数
     */
    public IExportDataDTO buildExportPostSectionDataDTO(ForumSectionQuery forumSectionQuery) {
        return new AbstractExportDataDTO<IForumSectionService, ForumSectionDTO>(forumSectionQuery) {

            private static final String DATA_SOURCE = "dataSource";
            private static final String AUDIT_STATUS = "auditStatus";

            @Override
            protected IForumSectionService getBean() {
                return SpringUtil.getBean("forumSectionService", IForumSectionService.class);
            }

            @Override
            protected PageInfo<ForumSectionDTO> getPageInfo() {
                return getBean().getForumSection(forumSectionQuery);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.PostSection;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.PostSection.getType();
            }

            @Override
            protected void afterCreateBeanMap(Map<String, Object> map) {
                Object dataSource = map.get(DATA_SOURCE);
                if (Objects.equals(dataSource, 1)) {
                    map.put(DATA_SOURCE, "管理端");
                } else {
                    map.put(DATA_SOURCE, "客户端");
                }
                Object auditStatus = map.get(AUDIT_STATUS);
                if (Objects.equals(auditStatus, 2)) {
                    map.put(AUDIT_STATUS, "已审核");
                } else if (Objects.equals(auditStatus, 3)) {
                    map.put(AUDIT_STATUS, "已驳回");
                } else {
                    map.put(AUDIT_STATUS, "未审核");
                }
            }

        };
    }
}
