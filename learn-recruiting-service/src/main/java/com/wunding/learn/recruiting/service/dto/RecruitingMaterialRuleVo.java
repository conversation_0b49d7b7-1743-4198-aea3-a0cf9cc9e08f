package com.wunding.learn.recruiting.service.dto;


import com.wunding.learn.recruiting.service.model.RecruitingMaterialRule;
import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * @tiele RecruitingMaterialRuleVo
 * @projectName devlop-learn
 * @description: TODO 招募宣传材料表 VO
 * @Date 2021/12/29  RecruitingMaterialRuleVo
 */
@Data
public class RecruitingMaterialRuleVo extends RecruitingMaterialRule {

    /**
     * 材料规则 示例文件名称
     */
    private String exampleFileName;

    /**
     * 材料规则  示例文件大小
     */
    private String exampleFileSize;

    /**
     * 材料规则  被评审人上传文件名称
     */
    private String participantFileName;

    /**
     * 材料规则  被评审人上传文件路径
     */
    private String participantFileUrl;

    /**
     * 材料规则  被评审人上传文件转码状态 1-转码中 2-转码成功 3-转码失败 4-排队中
     */
    private String participantTransformStatus;

    /**
     * 上传的excel路径
     */
    private String excelFilePath;

    /**
     * 是否有参与记录生产 1 -无 2-有
     */
    private Integer participation;

}
