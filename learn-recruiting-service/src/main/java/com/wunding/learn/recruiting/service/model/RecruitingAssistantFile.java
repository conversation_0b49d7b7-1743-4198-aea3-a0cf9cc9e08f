package com.wunding.learn.recruiting.service.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 学员材料表
 *
 * <AUTHOR> href="mailto:<EMAIL>">xiao8</a>
 * @since 2022-08-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("recruiting_assistant_file")
@Schema(name = "RecruitingAssistantFile", description = "学员材料表")
public class RecruitingAssistantFile implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;


    /**
     * 招募id
     */
    @Schema(description = "招募id")
    @TableField("recruiting_id")
    private String recruitingId;


    /**
     * 参与记录id
     */
    @Schema(description = "参与记录id")
    @TableField("participation_record_id")
    private String participationRecordId;


    /**
     * 材料id
     */
    @Schema(description = "材料id")
    @TableField("material_id")
    private String materialId;


    /**
     * 当材料规则是文本时为文本内容，其他类型时为空
     */
    @Schema(description = "当材料规则是文本时为文本内容，其他类型时为空")
    @TableField("text_content")
    private String textContent;


    /**
     * 课件真实时长（视频/mp3）
     */
    @Schema(description = "课件真实时长（视频/mp3）")
    @TableField("play_time")
    private Integer playTime;


    /**
     * 文件媒体类型
     */
    @Schema(description = "文件媒体类型")
    @TableField("mime")
    private String mime;


    /**
     * 是否已经入库课件 0-否 1-是;课程评估时有用到
     */
    @Schema(description = "是否已经入库课件 0-否 1-是;课程评估时有用到")
    @TableField("is_enter_storage")
    private Integer isEnterStorage;


    /**
     * 1-转码中 2-转码成功 3-转码失败 4-排队中
     */
    @Schema(description = "1-转码中 2-转码成功 3-转码失败 4-排队中")
    @TableField("transform_status")
    private Integer transformStatus;


    /**
     * 课件类型  与courseware 表cwType保持一致
     */
    @Schema(description = "课件类型  与courseware 表cwType保持一致")
    @TableField("cw_type")
    private String cwType;


    /**
     * 材料排序
     */
    @Schema(description = "材料排序")
    @TableField("sort")
    private Integer sort;


    /**
     * 是否删除(1:已删除,0未删除)
     */
    @Schema(description = "是否删除(1:已删除,0未删除)")
    @TableField("is_del")
    @TableLogic(value = "0", delval = "1")
    private Integer isDel;


    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;


    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 修改人
     */
    @Schema(description = "修改人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;


    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


}
