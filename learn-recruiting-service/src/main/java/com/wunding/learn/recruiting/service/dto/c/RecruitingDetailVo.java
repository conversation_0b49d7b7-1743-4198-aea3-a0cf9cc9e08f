package com.wunding.learn.recruiting.service.dto.c;

import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR> href="mailto:<EMAIL>">yuxueyi</a>
 * @title RecruitingDetailVo
 * @desc 招募详情
 * @date 2021/12/29 15:42
 */
@Data
@Schema
public class RecruitingDetailVo {

    @Schema(description = "招募id")
    private String id;

    @Schema(description = "招募标题")
    private String title;

    @Schema(description = "招募地点")
    private String address;

    @Schema(description = "举办组织")
    private String sponsorOrg;

    @Schema(description = "招募宣传封面")
    private String coverImage;

    /**
     * 招募宣传图片
     */
    @Schema(description = "招募宣传图片")
    private String advertiseImage;

    @Schema(description = "是否限制人数 0-否 1-是")
    private int isLimit;

    @Schema(description = "招募限制人数 isLimit 为1 时有值")
    private Integer limitNum;

    @Schema(description = "招募活动说明")
    private String activityDescription;

    @Schema(description = "当前人的角色 1-被招募人，2-协办人")
    private Integer currentRole;

    @Schema(description = "招募活动是否开始  0-否 1-是 ")
    private Integer isStart;

    @Schema(description = "是否开放报名  0-否 1-是 , currentRole = 1 时起作用")
    private Integer isOpenApply;

    @Schema(description = "是否已经参加招募 0-否 1-是 , currentRole = 1 时起作用")
    private Integer isJoin;

    @Schema(description = "调研是否通过 0-否 1-是")
    private Integer isPass;

    @Schema(description = "招募类型(1:讲师认证,2:课程认证,3:其它)")
    private Integer type;

    @Schema(description = "调研材料id，不为空时说明有调研材料，有且最多有一个")
    private String surveyMaterialRuleId;

    @Schema(description = "是否包含申请第二步材料 0-不包含，1-包含")
    private Integer hasMaterialFile;

    @Schema(description = "招募开始时间")
    private Date beginTime;

    @Schema(description = "招募结束时间")
    private Date endTime;

    @Schema(description = "招募宣传材料")
    private List<RecruitingMaterialVo> recruitingMaterialVos = Lists.newArrayList();

    @Data
    @Schema
    public static class RecruitingMaterialVo {

        @Schema(description = "招募宣传材料id")
        private String id;

        @Schema(description = "文件名")
        private String fileName;

        @Schema(description = "播放地址")
        private String playUrl;

        @Schema(description = "文件类型")
        private String mime;

        @Schema(description = "转码状态 1-转码中 2-转码成功 3-转码失败 4-排队中")
        private Integer transformStatus;

    }
}
