package com.wunding.learn.recruiting.service.dto.c;

import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR> href="mailto:<EMAIL>">yuxueyi</a>
 * @title RecruitingMaterialVo
 * @desc 被招募人审核页详情
 * @date 2021/12/29 20:38
 */
@Data
@Schema
public class RecruitingAssistantAuditDetailVo {

    @Schema(description = "招募标题")
    private String title;

    @Schema(description = "审核详情")
    private AssistantAudit assistantAudit;

    @Schema(description = "调研题目 ")
    private RecruitingParticipantSurveyDetail recruitingParticipantSurveyDetail;

    @Schema(description = "招募材料")
    private List<RecruitingAssistantFile> materialRulesVos = Lists.newArrayList();

    @Data
    @Schema
    public static class AssistantAudit {

        @Schema(description = "审核结果 2-审核拒绝 3-审核通过 ")
        private int status;

        @Schema(description = "评分")
        private BigDecimal score;

        @Schema(description = "评语")
        private String remark;

        @Schema(description = "审核时间")
        private Date auditTime;
    }

    @Data
    @Schema
    public static class RecruitingParticipantSurveyDetail {

        @Schema(description = "调研材料规则标题")
        private String title;

        @Schema(description = "调研题目 ")
        private List<RecruitingSurveyQuestion> recruitingSurveyQuestions =
            Lists.newArrayList();

        @Schema(description = "被评人调研答案 ")
        private List<RecruitingSurveyQuestionAnswer>
            recruitingSurveyQuestionAnswers = Lists.newArrayList();

    }

    @Data
    @Schema
    public static class RecruitingAssistantFile {

        @Schema(description = "被招募人上传文件id")
        private String id;

        @Schema(description = "材料规则标题")
        private String title;

        @Schema(description = "materialType=2时不为空，是否待入库课件 0-否 1-是")
        private Integer isSaveLib;

        @Schema(description = "材料类型 2-文件录入 3-文本材料")
        private Integer materialType;

        @Schema(description = "材料规则提供说明")
        private String description;

        @Schema(description = "被招募人提交的文本内容")
        private String participantTextContent;

        @Schema(description = "被招募人上传的材料id")
        private String participantFileId;

        @Schema(description = "被招募人上传文件名")
        private String participantFileName;

        @Schema(description = "被招募人上传文件预览地址")
        private String participantFileUrl;

        @Schema(description = "被招募人上传文件 mine")
        private String participantFileMime;

        @Schema(description = "被招募人上传的文件转码状态 1-转码中 2-转码成功 3-转码失败 4-排队中")
        private Integer participantTransformStatus;
    }
}
