package com.wunding.learn.recruiting.service.client.dto;

import com.wunding.learn.file.api.constant.FileBizType;
import com.wunding.learn.file.api.dto.IFileResourceDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: AppraiseMaterial
 * @projectName devlop-learn
 * @description: 被评人上传材料
 * @date 2021/12/316:31
 */
@Data
@Schema(name = "RecruitingAssistantFileTranslateDTO", description = "被评人上传材料")
public class RecruitingAssistantFileTranslateDTO implements Serializable, IFileResourceDTO {

    private static final long serialVersionUID = 3286842360394607547L;

    @Schema(description = "被评人上传的材料id, 添加时为null，编辑时修改需要置null,编辑时没修改需要带入值")
    private String recruitingAssistantFileId;

    @Schema(description = "材料类型id", required = true)
    private String fileTypeId;

    @Schema(description = "文件路径", required = true)
    private String path;

    @Schema(description = "文件名", required = true)
    private String fileName;

    @Schema(description = "文件类型", required = true)
    private String mime;

    @Schema(description = "文件类型", required = true)
    private String fileType;

    @Schema(description = "1-转码中 2-转码成功 3-转码失败 4-排队中 ", required = true)
    private String transformStatus;


    @Override
    public String getFileType() {
        return fileType;
    }

    @Override
    public String getFilePath() {
        return path;
    }

    @Override
    public String getAdjunctName() {
        return fileName;
    }

    @Override
    public String getAdjunctType() {
        return null;
    }

    @Override
    public String getAdjunctPath() {
        return null;
    }

    @Override
    public FileBizType getBizType() {
        return FileBizType.RECRUITING_UPLOAD_FILE;
    }

    @Override
    public String getMime() {
        return mime;
    }

    @Override
    public String getLibId() {
        return fileTypeId;
    }
}
