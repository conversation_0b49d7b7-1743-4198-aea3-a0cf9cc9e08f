package com.wunding.learn.recruiting.service.admin.dto;

import com.wunding.learn.file.api.dto.NamePath;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(name = "RecruitingAssistantFileDTO", description = "招募人参与记录返回对象")
public class RecruitingAssistantFileDTO {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private String id;

    /**
     * 材料标题
     */
    @Schema(description = "材料标题")
    private String title;

    /**
     * 材料类型
     */
    @Schema(description = "材料类型")
    private String fileType;

    /**
     * 招募id
     */
    @Schema(description = "招募id")
    private String recruitingId;

    /**
     * 当材料规则是文本时为文本内容，其他类型时为空
     */
    @Schema(description = "当材料规则是文本时为文本内容，其他类型时为空")
    private String textContent;

    /**
     * 材料文件信息
     */
    @Schema(description = "材料文件信息")
    private NamePath file;

    /**
     * 1-转码中 2-转码成功 3-转码失败 4-排队中
     */
    @Schema(description = "1-转码中 2-转码成功 3-转码失败 4-排队中")
    private Integer transformStatus;
}
