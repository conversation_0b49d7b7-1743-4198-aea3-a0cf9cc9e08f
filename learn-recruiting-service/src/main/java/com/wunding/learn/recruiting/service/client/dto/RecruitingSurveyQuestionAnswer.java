package com.wunding.learn.recruiting.service.client.dto;

import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR> href="mailto:<EMAIL>">yuxueyi</a>
 * @title RecruitingSurveyQuestionAnswer
 * @desc 被评人选择的调研选项
 * @date 2021/12/30 19:00
 */
@Data
@Schema
public class RecruitingSurveyQuestionAnswer {

    @Schema(description = "调研题目id")
    private String id;

    @Schema(description = "调研题目选项")
    private List<String> chooseQuestionIds = Lists.newArrayList();

}