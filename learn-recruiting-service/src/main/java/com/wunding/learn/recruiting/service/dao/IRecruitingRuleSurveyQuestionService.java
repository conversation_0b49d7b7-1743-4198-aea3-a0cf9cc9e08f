package com.wunding.learn.recruiting.service.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.recruiting.service.model.RecruitingRuleSurveyQuestion;


/**
 * <p> 招募规则调研题目表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">chen<PERSON><PERSON><PERSON></a>
 * @since 2022-09-01
 */
public interface IRecruitingRuleSurveyQuestionService extends IService<RecruitingRuleSurveyQuestion> {

    /**
     * 删除题目和选项
     *
     * @param recruitingId 招募id
     * @param ruleId       规则id
     */
    void delQuestionAndItemByRuleId(String recruitingId, String ruleId);

}
