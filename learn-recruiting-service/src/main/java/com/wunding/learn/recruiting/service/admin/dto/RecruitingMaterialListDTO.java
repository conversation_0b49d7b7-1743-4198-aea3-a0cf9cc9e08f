package com.wunding.learn.recruiting.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(name = "RecruitingMaterialListDTO", description = "宣传材料列表返回对象")
public class RecruitingMaterialListDTO {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private String id;

    /**
     * 招募id
     */
    @Schema(description = "招募id")
    private String recruitingId;

    /**
     * 材料标题
     */
    @Schema(description = "材料标题")
    private String title;

    /**
     * 样例文件类型
     */
    @Schema(description = "样例文件类型")
    private String exampleFileType;

    /**
     * 案例文件转码状态 1-转码中 2-转码成功 3-转码失败 4-排队中
     */
    @Schema(description = "案例文件转码状态 1-转码中 2-转码成功 3-转码失败 4-排队中")
    private Integer transformStatus;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sort;


}
