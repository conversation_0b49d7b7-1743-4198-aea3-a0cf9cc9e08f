package com.wunding.learn.recruiting.service.dto.c;

import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR> href="mailto:<EMAIL>">yuxueyi</a>
 * @title RecruitingMaterialVo
 * @desc TODO
 * @date 2021/12/29 20:38
 */
@Data
@Schema
public class RecruitingMaterialRuleClientVo {

    @Schema(description = "招募材料")
    private List<MaterialRulesVo> materialRulesVos = Lists.newArrayList();

    @Data
    @Schema
    public static class PermitFileType {

        @Schema(description = "video-视频,audio-音频,image-图片,pdf-PDF,word-WORD文档")
        private String type;

        @Schema(description = "材料支持类型")
        private String permitFileType;
    }

    @Data
    @Schema
    public static class MaterialRulesVo {

        @Schema(description = "材料规则id")
        private String id;

        @Schema(description = "材料类型 2-文件录入 3-文本材料")
        private Integer materialType;

        @Schema(description = "材料规标题")
        private String title;

        @Schema(description = "是否必填 0-否 1-是")
        private Integer required;

        @Schema(description = "materialType=2时不为空，是否待入库课件 0-否 1-是")
        private Integer isSaveLib;

        @Schema(description = "materialType=2时不为空，材料支持类型")
        private List<PermitFileType> permitFileTypes;

        @Schema(description = "materialType=2时不为空，文件大小限制 单位 M")
        private Integer fileMaxSize;

        @Schema(description = "materialType=2时不为空，示例文件Url")
        private String exampleFileUrl;

        @Schema(description = "示例文件 mine")
        private String exampleFileMine;

        @Schema(description = "materialType=2时不为空，案例文件 转码状态 1-转码中 2-转码成功 3-转码失败 4-排队中")
        private Integer transformStatus;

        @Schema(description = "materialType=2时不为空，示例文件名")
        private String exampleFileName;

        @Schema(description = "materialType=2时不为空，示例文件类型")
        private String exampleFileType;

        @Schema(description = "材料提供说明")
        private String description;

        @Schema(description = "被招募人提交的文本内容")
        private String participantTextContent;

        @Schema(description = "被招募人上传的材料id")
        private String participantFileId;

        @Schema(description = "被招募人上传文件名")
        private String participantFileName;

        @Schema(description = "被招募人上传文件预览地址")
        private String participantFileUrl;

        @Schema(description = "被招募人上传文件 mine")
        private String participantFileMime;

        @Schema(description = "被招募人上传的文件转码状态 1-转码中 2-转码成功 3-转码失败 4-排队中")
        private Integer participantTransformStatus;
    }

}
