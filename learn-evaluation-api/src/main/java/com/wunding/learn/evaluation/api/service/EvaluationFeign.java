package com.wunding.learn.evaluation.api.service;

import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.dto.LearningCalendarTaskDTO;
import com.wunding.learn.common.dto.LearningCalendarTaskQuery;
import com.wunding.learn.common.dto.PublishDTO;
import com.wunding.learn.common.dto.TaskAppResourceDTO;
import com.wunding.learn.common.dto.lecturerworkbench.AppDTO;
import com.wunding.learn.common.dto.lecturerworkbench.WorkbenchEvaluationPageDTO;
import com.wunding.learn.evaluation.api.dto.EvalDTO;
import com.wunding.learn.evaluation.api.dto.EvaluationExcelSaveDTO;
import com.wunding.learn.evaluation.api.dto.EvaluationFeignDTO;
import com.wunding.learn.evaluation.api.dto.EvaluationFeignSaveDTO;
import com.wunding.learn.evaluation.api.dto.EvaluationInfoDTO;
import com.wunding.learn.evaluation.api.dto.EvaluationLimitViewUpdateDTO;
import com.wunding.learn.evaluation.api.dto.EvaluationListDTO;
import com.wunding.learn.evaluation.api.dto.ExaminationEvalFeignSaveDTO;
import com.wunding.learn.evaluation.api.dto.LearnerEvaluateInfoDTO;
import com.wunding.learn.evaluation.api.dto.LecturerEvaluateInfoDTO;
import com.wunding.learn.evaluation.api.dto.LecturerExaminationEvalDTO;
import com.wunding.learn.evaluation.api.dto.ProjectEvalAvgScore;
import com.wunding.learn.evaluation.api.dto.ProjectEvalInfoDTO;
import com.wunding.learn.evaluation.api.dto.ProjectJoinEvalDTO;
import com.wunding.learn.evaluation.api.dto.ProjectSimpleEvalOrgDTO;
import com.wunding.learn.evaluation.api.dto.UserEvalStatusInfoDTO;
import com.wunding.learn.evaluation.api.query.EvaluationProjectQuery;
import com.wunding.learn.evaluation.api.query.EvaluationQueryDTO;
import com.wunding.learn.evaluation.api.query.LearningCalendarTaskFeignQuery;
import com.wunding.learn.evaluation.api.query.LecturerMinScoreQueryDTO;
import com.wunding.learn.evaluation.api.query.ProjectJoinEvalQuery;
import com.wunding.learn.evaluation.api.query.UserEvalStatusInfoQuery;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 评估服务代理
 *
 * <AUTHOR>
 * @since 2022/12/7
 */
@FeignClient(url = "${learn.service.learn-evaluation-service}", path = "/evaluation", name = "learn-evaluation-service")
public interface EvaluationFeign {

    /**
     * 创建/更新评估
     *
     * @param saveDTO 评估信息
     */
    @PostMapping("/saveOrUpdate")
    String saveOrUpdate(@RequestBody EvaluationFeignSaveDTO saveDTO);

    /**
     * 获取有效评估id列表（未删除，结束时间大于当前时间）
     *
     * @return 返回有效评估ID列表
     */
    @GetMapping("/getEffectiveEvaluationIds")
    List<String> getEffectiveEvaluationIds();

    /**
     * 保存/更新讲师授课评估
     *
     * @param saveDTO 讲师授课评估对象
     */
    @PostMapping("/saveOrUpdateExaminationEval")
    void saveOrUpdateExaminationEval(@RequestBody ExaminationEvalFeignSaveDTO saveDTO);

    /**
     * 删除评估
     *
     * @param idsList 评估ID列表
     */
    @PostMapping("/delete")
    void delete(@RequestBody Collection<String> idsList);

    /**
     * 发布评估
     *
     * @param publishDTO 资源发布参数对象
     */
    @PostMapping("/published")
    void published(@RequestBody PublishDTO publishDTO);

    /**
     * 获取评估信息
     *
     * @param id 评估ID
     * @return 评估编辑预览对象
     */
    @GetMapping("/info")
    EvaluationInfoDTO getInfo(@RequestParam("id") String id);


    /**
     * 根据学习项目ID 查询学习的评估
     *
     * @param projectIdList 学习项目ID
     * @return 评估编辑预览对象
     */
    @PostMapping("/getProjectEvaluationInfoFromProjectIdList")
    EvaluationInfoDTO getProjectEvaluationInfoFromProjectIdList(@RequestBody Collection<String> projectIdList);

    /**
     * 根据评估对象获取评估
     *
     * @param evaluationObject 评估对象
     * @param evaluationType   评估类型
     * @return 评估实体DTO
     */
    @GetMapping("/getByEvaluationObj")
    EvalDTO getByEvaluationObj(@RequestParam("evaluationObject") String evaluationObject,
        @RequestParam("evaluationType") Integer evaluationType);

    /**
     * 获取学习项目评估列表
     *
     * @param query 查询条件
     * @return 评估分页列表
     */
    @PostMapping("/listPageProject")
    PageInfo<EvaluationListDTO> listPageProject(@RequestBody EvaluationProjectQuery query);

    /**
     * 原来没有注释
     *
     * @param query 查询条件
     * @return 不清楚
     */
    @PostMapping("/getOldEvaluation")
    String getOldEvaluation(@RequestBody EvaluationProjectQuery query);

    /**
     * 获取学习项目评估列表
     *
     * @param query 查询条件
     * @return 评估分页列表
     */
    @PostMapping("/listPageProjectLecturer")
    PageInfo<EvaluationListDTO> listPageProjectLecturer(@RequestBody EvaluationProjectQuery query);

    /**
     * 获取讲师授课明细评估的平均分数
     *
     * @param lecturerExaminationIdSet 查询参数
     * @return 讲师授课明细评估的平均分数
     */
    @PostMapping("/getLecturerAvgScore")
    BigDecimal getLecturerAvgScore(@RequestBody Set<String> lecturerExaminationIdSet);

    /**
     * 获取学习项目对讲师在一定时间内所有评估最低分数
     *
     * @param lecturerMinScoreQueryDTO 查询参数
     * @return 学习项目对讲师在一定时间内所有评估最低分数
     */
    @PostMapping("/getLecturerMinScore")
    Float getLecturerMinScore(@RequestBody LecturerMinScoreQueryDTO lecturerMinScoreQueryDTO);

    /**
     * 获取学习项目已经评估的数量
     *
     * @param evaluationObjectList 项目评估对象idList
     * @return 学习项目已经评估的数量
     */
    @PostMapping("/getEvaluatedObjectList")
    Integer getEvaluatedObjectList(@RequestBody List<String> evaluationObjectList);

    /**
     * 评估主键set
     *
     * @param evalIdSet 评估ID
     * @return 评估主键set
     */
    @PostMapping("/getEvaluationInfoByIdList")
    List<EvalDTO> getEvalInfoListByIdList(@RequestBody Set<String> evalIdSet);

    /**
     * 评估对象set
     *
     * @param evalObjectSet 评估对象set
     * @return 评估对象set
     */
    @PostMapping("/getEvalInfoListByEvalObjects")
    Map<String, EvalDTO> getEvalInfoListByEvalObjects(@RequestBody Set<String> evalObjectSet);


    /**
     * 获取评估的 平均分， 所有问题项的评估平均分
     *
     * @param evalIdList 评估ID列表
     * @return 评估的 平均分， 所有问题项的评估平均分
     */
    @PostMapping("/findEvalAvgScoreAndQuestionItemAvgScore")
    List<ProjectSimpleEvalOrgDTO> findEvalAvgScoreAndQuestionItemAvgScore(@RequestBody Collection<String> evalIdList);


    /**
     * 查询学习项目的讲师 和 组织平均分
     *
     * @param projectIdList 学习项目ID列表
     * @return 学习项目的讲师 和 组织平均分
     */
    @PostMapping("/findProjectEvalAvgScore")
    List<ProjectEvalAvgScore> findProjectEvalAvgScore(@RequestBody Collection<String> projectIdList);


    /**
     * 查询学习项目参与评估信息
     *
     * @param projectJoinEvalQuery
     * @return
     */
    @PostMapping("/findProjectJoinEvalInfo")
    List<ProjectJoinEvalDTO> findProjectJoinEvalInfo(@RequestBody List<ProjectJoinEvalQuery> projectJoinEvalQuery);

    /**
     * 通过评估对象获取评估id集合
     *
     * @param evalObjectSet 评估对象
     * @return 评估id集合
     */
    @PostMapping("/getEvalIdsByEvalObjects")
    Set<String> getEvalIdsByEvalObjects(@RequestBody Set<String> evalObjectSet);

    /**
     * 获取已评估人数
     *
     * @param evalId 评估主键
     * @return 已评估人数
     */
    @GetMapping("/getEvaluatedCount")
    Long getCountEvalFinishNum(@RequestParam("evalId") String evalId);

    /**
     * 获取评估列表
     *
     * @param evaluationQueryDTO 查询条件
     * @return 评估分页列表
     */
    @GetMapping("/queryPage")
    PageInfo<EvalDTO> queryPage(@RequestBody EvaluationQueryDTO evaluationQueryDTO);

    /**
     * 获取评估列表
     *
     * @param evaluationQueryDTO 查询条件
     * @return 评估分页列表
     */
    @PostMapping("/queryPageByMapper")
    PageInfo<EvalDTO> queryPageByMapper(@RequestBody EvaluationQueryDTO evaluationQueryDTO);


    /**
     * 查询执行类型的评估的平均分
     *
     * @param evalId 评价ID
     * @param type   类型
     * @return 执行类型的评估的平均分
     */
    @GetMapping("/selectAvgScoreByType")
    Float selectAvgScoreByType(@RequestParam("evalId") String evalId, @RequestParam("type") String type);

    /**
     * 查询指定评估记录的数量
     *
     * @param evalId 指定评估
     * @return 指定评估记录的数量
     */
    @GetMapping("/countEvaluationUser")
    Long countEvaluationUser(@RequestParam("evalId") String evalId);

    /**
     * 评估是否存在
     *
     * @param id 评估ID
     * @return 是否存在
     */
    @GetMapping("/isExist")
    Boolean isExist(@RequestParam("id") String id);

    /**
     * 根据ID获取评估信息
     *
     * @param id 评估ID
     * @return 评估信息
     */
    @GetMapping("/getById")
    EvaluationFeignDTO getById(@RequestParam("id") String id);

    /**
     * 获取讲师/管理员获取项目评估列表
     *
     * @param ids 查询参数
     * @return 评估MAP
     */
    @PostMapping("/listLecturerEvaluateInfo")
    Map<String, LecturerEvaluateInfoDTO> listLecturerEvaluateInfo(@RequestBody Collection<String> ids);

    /**
     * 查询一段时间内的评估列表
     *
     * @param query 查询条件
     * @return 评估MAP
     */
    @PostMapping("/findLearningCalenderEvalTaskList")
    Map<String, LearningCalendarTaskDTO> findLearningCalenderEvalTaskList(
        @RequestBody LearningCalendarTaskFeignQuery query);

    /**
     * 获取用户评估状态信息
     *
     * @param query 查询条件
     * @return 评估MAP
     */
    @PostMapping("/getUserEvalStatusInfo")
    Map<String, UserEvalStatusInfoDTO> getUserEvalStatusInfo(@RequestBody UserEvalStatusInfoQuery query);

    /**
     * 获取指定日期内的评估id
     *
     * @param learningCalendarTaskQuery 查询条件
     * @return 评估id列表
     */
    @PostMapping("/getEvalIdsByCalendar")
    List<String> getEvalIdsByCalendar(@RequestBody LearningCalendarTaskQuery learningCalendarTaskQuery);

    /**
     * 保存辅导评估
     *
     * @param dto 辅导评估
     */
    @PostMapping("/saveHandEvaluation")
    void saveHandEvaluation(@RequestBody EvaluationExcelSaveDTO dto);

    /**
     * 获取学员评估参与信息Map
     *
     * @param projectId 项目ID
     * @param pageNo    分页页码
     * @param pageSize  分页大小
     * @return 学员评估参与信息列表
     */
    @GetMapping("/listLearnerEvaluateInfo")
    PageInfo<LearnerEvaluateInfoDTO> listLearnerEvaluateInfo(@RequestParam("projectId") String projectId,
        @RequestParam("pageNo") Integer pageNo, @RequestParam("pageSize") Integer pageSize);

    /**
     * 获取一次讲师授课评估中的平均分
     *
     * @param evalId 评估ID
     * @return 讲师授课评估中的平均分
     */
    @GetMapping("/getAvgScore")
    BigDecimal getAvgScore(@RequestParam("evalId") String evalId);

    /**
     * 计算授课明细所在评估的综合平均评分
     *
     * @param examinationIdList 评估列表
     * @return 授课明细所在评估的综合平均评分
     */
    @PostMapping("/calculateLecturerScore")
    BigDecimal calculateLecturerScore(@RequestBody Collection<String> examinationIdList);

    /**
     * 获取讲师创建的学习项目培训分页数据
     *
     * @param pageNo    页码
     * @param pageSize  页大小
     * @param projectId 项目ID
     * @return 分页数据列表
     */
    @GetMapping("/getLecturerEvaluationPageInfo")
    PageInfo<WorkbenchEvaluationPageDTO> getLecturerEvaluationPageInfo(@RequestParam("pageNo") Integer pageNo,
        @RequestParam("pageSize") Integer pageSize, @RequestParam("projectId") String projectId);

    /**
     * 获取讲师授课的评估信息
     *
     * @param evalObjectIds 查询参数
     * @return 讲师授课的评估信息
     */
    @GetMapping("/getLecturerExaminationEvalDTO")
    Map<String, LecturerExaminationEvalDTO> getLecturerExaminationEvalDTO(Collection<String> evalObjectIds);

    /**
     * 获取讲师工作台评估Map
     *
     * @param ids 评估id集合
     * @return {@link Map}<{@link String},{@link AppDTO}>
     */
    @PostMapping("/lecturerWorkbench/getEvaluationMap")
    Map<String, AppDTO> getLecturerWorkbenchEvaluationMap(@RequestBody Collection<String> ids);

    /**
     * 同步更新快速培训下评估的开始-结束时间
     *
     * @param evalDTO 评估dto
     */
    @PostMapping("/updateTimeByQuickProjectId")
    void updateTimeByQuickProjectId(@RequestBody EvalDTO evalDTO);

    /**
     * 获取评估信息Map
     *
     * @param evalIds 评估ID列表
     * @return 评估Map
     */
    @PostMapping("/getProjectEvalInfoMap")
    Map<String, ProjectEvalInfoDTO> getProjectEvalInfoMap(@RequestBody Collection<String> evalIds);

    /**
     * 根据学习项目ID获取任务评估信息
     *
     * @param projectId 学习项目ID
     * @return 任务评估信息
     */
    @PostMapping("/getTaskEvaluationByProjectId")
    List<TaskAppResourceDTO> getTaskEvaluationByProjectId(@RequestParam("projectId") String projectId);

    /**
     * 根据学习项目ID获取已发布的任务评估信息
     *
     * @param projectId 学习项目ID
     * @return 任务评估信息
     */
    @PostMapping("/getPublishTaskEvaluationByProjectId")
    List<TaskAppResourceDTO> getPublishTaskEvaluationByProjectId(@RequestParam("projectId") String projectId);


    /**
     * 更新可见范围
     *
     * @param updateDTO 评估下发范围更新对象
     */
    @PostMapping("/updateViewLimitByProjectId")
    void updateViewLimitByProjectId(@RequestBody EvaluationLimitViewUpdateDTO updateDTO);


    /**
     * 更新快速培训可见范围
     *
     * @param updateDTO 评估下发范围更新对象
     */
    @PostMapping("/updateViewLimitForRapidTrain")
    void updateViewLimitForRapidTrain(@RequestBody EvaluationLimitViewUpdateDTO updateDTO);

    /**
     * 更新任务评估时间
     *
     * @param updateList 参数
     */
    @PostMapping("/updateEvalTime")
    void updateEvalTime(@RequestBody Collection<EvalDTO> updateList);


    @PostMapping("/getProjectAvgScoreByProjectIds")
    BigDecimal getProjectAvgScoreByProjectIds(@RequestBody Collection<String> projectIds);

    /**
     * 该对象是否被评估过
     *
     * @param evalObject 评估对象ID
     * @return 是否被评估过
     */
    @GetMapping("/evalObjectIsAssess")
    Boolean evalObjectIsAssess(@RequestParam("evalObject") String evalObject);

    /**
     * 校验评估对象
     *
     * @param excel 模板数据
     */
    @PostMapping("/checkProjectEvaluationExcelTemplate")
    void checkProjectEvaluationExcelTemplate(@RequestBody String[][] excel);

    /**
     * 检查用户状态
     *
     * @param id     评估id
     * @param userId 用户id
     * @return {@link Integer}
     */
    @PostMapping("/checkUserStatus")
    Integer checkUserStatus(@RequestParam("id") String id, @RequestParam("userId") String userId);

    /**
     * 检查用户完成状态
     *
     * @param userId 用户id
     * @param ids    资源id集合
     * @return {@link Integer}
     */
    @PostMapping("/evaluationFeign/listUserResourcesProgress")
    Map<String, Integer> listUserResourcesProgress(@RequestParam("userId") String userId,
        @RequestParam("resourceIds") Collection<String> ids);

    /**
     * 根据用户管理范围的组织信息和资源ID校验是否具有资源管理权限
     *
     * @param userManageAreaOrgId 户管理范围的组织信息ID列表
     * @param id                  评估ID
     * @return 0 无权限，1 有权限
     */
    @PostMapping("/checkEvaluationManagePermissions")
    Integer checkEvaluationManagePermissions(
        @RequestParam("userManageAreaOrgId") Collection<String> userManageAreaOrgId, @RequestParam("id") String id);

    /**
     * 获取评估关联的项目Id(调用前,确认评估关联的类型)
     *
     * @param id
     * @return
     */
    @GetMapping("getEvaluationProjectIdById")
    String getEvaluationProjectIdById(@RequestParam("id") String id);


    /**
     * 检查下发范围
     *
     * @param userId     用户id
     * @param resourceId 资源id
     * @return {@link Boolean}
     */
    @GetMapping("/checkViewLimit")
    Boolean checkViewLimit(@RequestParam("userId") String userId, @RequestParam("resourceId") String resourceId);

    /**
     * 根据评估对象 获取评估 (讲师参与培训, 一个培训下可能有多个评估,需要计算评估分和参与人数)
     *
     * @param collect 评估对象id
     * @return
     */
    @PostMapping("/getEvalInfoByEvaluationObjects")
    EvalDTO getEvalInfoByEvaluationObjects(@RequestParam("collect") Set<String> collect);

    /**
     * 保存评估下发
     *
     * @param evaluationId 评估id
     * @param viewLimitId  下发id
     */
    @GetMapping("/handleViewLimit")
    void handleViewLimit(@RequestParam("evaluationId") String evaluationId,
        @RequestParam("viewLimitId") Long viewLimitId);
}


