package com.wunding.learn.evaluation.api.query;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/3/31
 */
@Data
@Accessors(chain = true)
public class EvaluationListWithLimitQuery {

    /**
     * 学习项目ID
     */
    private String projectId;

    /**
     * 当前用户id
     */
    private String currentUserId;

    /**
     * 是否发布
     */
    private Integer isPublish;

    /**
     * 是否下发范围过滤
     */
    private Integer isViewLimit;

}
