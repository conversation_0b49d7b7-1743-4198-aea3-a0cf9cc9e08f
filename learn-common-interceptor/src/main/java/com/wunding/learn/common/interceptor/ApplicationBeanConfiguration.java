package com.wunding.learn.common.interceptor;

import java.nio.charset.StandardCharsets;
import java.util.List;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 系统配置
 *
 * <AUTHOR>
 * @date 2022/02/24
 */
@Configuration
@Slf4j
public class ApplicationBeanConfiguration implements WebMvcConfigurer {

    //各模块URI前缀，微服务默认是 / , 单体是 /模块名/

    @Value("${module.user.contentPath:/}")
    private String user;
    @Value("${module.login.contentPath:/}")
    private String login;
    @Value("${module.appraise.contentPath:/}")
    private String appraise;
    @Value("${module.businessView.contentPath:/}")
    private String businessView;
    @Value("${module.certification.contentPath:/}")
    private String certification;
    @Value("${module.comment.contentPath:/}")
    private String comment;
    @Value("${module.course.contentPath:/}")
    private String course;
    @Value("${module.evaluate.contentPath:/}")
    private String evaluate;
    @Value("${module.exam.contentPath:/}")
    private String exam;
    @Value("${module.example.contentPath:/}")
    private String example;
    @Value("${module.excitation.contentPath:/}")
    private String excitation;
    @Value("${module.file.contentPath:/}")
    private String file;
    @Value("${module.forum.contentPath:/}")
    private String forum;
    @Value("${module.info.contentPath:/}")
    private String info;
    @Value("${module.lecturer.contentPath:/}")
    private String lecturer;
    @Value("${module.live.contentPath:/}")
    private String live;
    @Value("${module.market.contentPath:/}")
    private String market;
    @Value("${module.project.contentPath:/}")
    private String project;
    @Value("${module.promotedGame.contentPath:/}")
    private String promotedGame;
    @Value("${module.push.contentPath:/}")
    private String push;
    @Value("${module.reading.contentPath:/}")
    private String reading;
    @Value("${module.recruiting.contentPath:/}")
    private String recruiting;
    @Value("${module.special.contentPath:/}")
    private String special;
    @Value("${module.survey.contentPath:/}")
    private String survey;
    @Value("${module.websocket.contentPath:/}")
    private String websocket;
    @Value("${module.march.contentPath:/}")
    private String march;
    @Value("${module.payment.contentPath:/}")
    private String payment;
    @Value("${module.operation.contentPath:/}")
    private String operation;
    @Value("${module.sync.contentPath:/}")
    private String sync;

    private static final String PATH_PATTERNS_ALL = "/**";


    /**
     * 处理jwt，用户信息保存的拦截器
     */
    @Resource
    private ValidateUserInterceptor validateUserInterceptor;

    @Resource
    private TenantInfoInterceptor tenantInfoInterceptor;

    @Resource
    private TraceInterceptor traceInterceptor;

    @Resource
    private RepeatSubmitIntercept repeatSubmitIntercept;

    /**
     * 配置系统拦截器
     *
     * @param registry 拦截器注册
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        /**
         * 用户身份信息拦截放行URI列表，URI不带contentPath
         */
        String[] pathPatternsIgnore = {
            // 各种模块放行URI，注意前缀，这里的URL应该是Rest文件里的Mapping保持一至
            file + "trans/syncTransFile",
            file + "url/getFilePathConfigByIp",
            login + "login",
            login + "refreshToken",
            login + "captcha/**",
            login + "jsapiTicket",
            login + "appInfo",
            login + "bindSysUser",
            login + "loginSkip",
            login + "sso/authCode",
            login + "sso/training/ip",
            login + "sso/afterSales/login",
            login + "sso/training/login",
            login + "token",
            login + "enroll/api/getWxUserPhone",
            login + "enroll/api/register",
            login + "weChatGetPhone",
            login + "weChatGetOpenId",
            login + "weChatLogin",
            login + "getUserByPhone",
            login + "cas/**",
            login + "casLogin",
            user + "findPassword/**",
            user + "privacy/url",
            user + "api/version/list",
            user + "api/version/check",
            user + "/saveUserAndOrg",
            user + "/user/saveUserAndOrg",
            user + "/SL",
            // 单体的feign没有前缀,因为单体版本没有@feignClient
            "/saveUserAndOrg",
            user + "api/behavior/**",
            live + "api/live/auth",
            live + "api/live/playBack/*",
//            course + "show!play.do",
//            course + "lms!readInit.do",
            course + "/page/**",
            exam + "api/v1/syncAllExams",
            march + "/syncStep",
            payment + "wechat/notify",
            payment + "wechat/refundNotify",
            file + "/uploadImgForMini",
            file + "weblate/callback",
            operation + "posterShareClient/reportPsMini/**",
            // 通过放行URI，
            "/swagger-resources/**",
            "/swagger-ui/**",
            "/swagger-ui*/**",
            "/api/v1/*",
            "/v2/**",
            "/v3/**",
            "/actuator/**",
            "/druid/**",
            "/common/**",
            "/error",
            "/page/**",
            //同步数据接口
            sync+"api/receive/**"
        };
        registry.addInterceptor(tenantInfoInterceptor)
            .addPathPatterns(PATH_PATTERNS_ALL)
            .order(10);
        registry
            .addInterceptor(validateUserInterceptor)
            .addPathPatterns(PATH_PATTERNS_ALL)
            .excludePathPatterns(pathPatternsIgnore)
            .order(100);

        registry
            .addInterceptor(repeatSubmitIntercept)
            .addPathPatterns(PATH_PATTERNS_ALL)
            .excludePathPatterns(pathPatternsIgnore)
            .order(110);

        registry.addInterceptor(traceInterceptor)
            .addPathPatterns(PATH_PATTERNS_ALL)
            .order(1000);
    }


    @Override
    public void extendMessageConverters(List<HttpMessageConverter<?>> converters) {
        for (HttpMessageConverter<?> converter : converters) {
            if (converter instanceof StringHttpMessageConverter) {
                ((StringHttpMessageConverter) converter).setDefaultCharset(StandardCharsets.UTF_8);
            }
        }
        WebMvcConfigurer.super.extendMessageConverters(converters);
    }
}
