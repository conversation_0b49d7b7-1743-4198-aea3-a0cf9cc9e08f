package com.wunding.learn.info.service.admin.query;

import io.swagger.v3.oas.annotations.Parameter;
import java.io.Serializable;
import lombok.Data;

/**
 * 分类数据列表查询对象
 *
 * <AUTHOR>
 * @date 2022/7/11
 */
@Data
public class CategoryBaseQuery implements Serializable {

    public static final long serialVersionUID = 1L;

    @Parameter(description = "需要排除的数据主键,可以为空")
    private String excludeId;

    @Parameter(description = "分类(InfoCate：资讯分类)", required = true)
    private String type;

    @Parameter(description = "分类名")
    private String categoryName;

}
