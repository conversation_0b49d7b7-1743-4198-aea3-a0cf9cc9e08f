package com.wunding.learn.info.service.admin.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.wunding.learn.flowable.api.dto.BaseAuditStatusDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 资讯列表dto
 *
 * <AUTHOR>
 * @date 2022/08/05
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Schema(name = "InfoDTO", description = "资讯列表对象")
public class InfoDTO extends BaseAuditStatusDTO {

    /**
     * 资讯ID
     */
    @Schema(description = "资讯ID")
    private String id;


    /**
     * 资讯标题
     */
    @Schema(description = "资讯标题")
    private String title;

    /**
     * 资讯分类ID
     */
    @Schema(description = "资讯分类ID")
    private String infoCateName;

    /**
     * 资讯关键词
     */
    @Schema(description = "资讯关键词")
    private String keyWord;

    /**
     * 是否置顶
     */
    @Schema(description = "是否置顶")
    private Integer isTop;

    /**
     * 资讯作者
     */
    @Schema(description = "资讯作者", hidden = true)
    @JsonIgnore
    private String author;

    /**
     * 作者全名
     */
    @Schema(description = "资讯作者名称")
    private String authorFullName;

    /**
     * 发布者
     */
    @Schema(description = "发布者", hidden = true)
    @JsonIgnore
    private String publishBy;

    /**
     * 发布者全称
     */
    @Schema(description = "发布者姓名")
    private String publishByFullName;

    /**
     * 发布时间
     */
    @Schema(description = "发布时间")
    private Date publishTime;

    /**
     * 是否发布
     */
    @Schema(description = "是否发布")
    private Integer isPublish;

    /**
     * 资讯格式 0在线编辑 1附件上传
     */
    @Schema(description = "资讯格式 0在线编辑 1附件上传")
    private Integer infomationSource;

    /**
     * html的地址
     */
    @Schema(description = "在线做课或文件转码index.html地址")
    private String htmlUrl;

    /**
     * 视频转换状态  1 转换中  2 转换完成 3 转换失败
     */
    @Schema(description = "视频转换状态  1-转码中 2-转码成功 3-转码失败 4-排队中")
    private Integer transformStatus;

    @Schema(description = "创建、归属部门Id")
    private String orgId;

    @Schema(description = "创建、归属部门")
    private String orgName;
}





























