package com.wunding.learn.info.service.admin.rest;

import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.bean.BaseEntity;
import com.wunding.learn.common.bean.Result;
import com.wunding.learn.common.dto.ProcessAuditDTO;
import com.wunding.learn.flowable.api.dto.ProcessDefinitionDetailDTO;
import com.wunding.learn.flowable.api.dto.ProcessListBaseDTO;
import com.wunding.learn.info.service.admin.dto.InfoProcessInfoDTO;
import com.wunding.learn.info.service.biz.InfoProcessBiz;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("${module.info.contentPath:/}infoProcess")
@Tag(description = "资讯审核管理", name = "InfoProcessRest")
public class InfoProcessRest {


    @Resource
    private InfoProcessBiz infoProcessBiz;

    @PostMapping("/getInfoProcessList")
    @Operation(operationId = "getInfoProcessList_InfoProcessRest", summary = "获取课程审核流程列表", description = "获取课程审核流程列表")
    public Result<PageInfo<ProcessListBaseDTO>> getInfoProcessList(@RequestBody BaseEntity query) {
        return Result.success(infoProcessBiz.getInfoProcessList(query));
    }

    @PostMapping("/auditProcess/info")
    @Operation(operationId = "auditProcess_info", summary = "课程申请处理", description = "课程申请处理")
    public Result<Void> auditProcess(@RequestBody @Validated ProcessAuditDTO processAuditDTO) {
        infoProcessBiz.auditProcess(processAuditDTO);
        return Result.success();
    }

    @GetMapping("/getInfoProcessInfo")
    @Operation(operationId = "getInfoProcessInfo", summary = "资讯审核信息", description = "资讯审核信息")
    public Result<InfoProcessInfoDTO> getCourseProcessInfo(
        @Parameter(description = "课程审核的流程实例id") @RequestParam("id") String id) {
        return Result.success(infoProcessBiz.getInfoProcessInfo(id));
    }

    @PutMapping("/applyAudit")
    @Operation(operationId = "applyAudit_InfoRest", summary = "资讯提交审核", description = "资讯提交审核")
    public Result<Void> applyAudit(@RequestParam("infoId") String infoId) {
        infoProcessBiz.applyAudit(infoId);
        return Result.success();
    }


    @PutMapping("/revokeInfoProcess")
    @Operation(operationId = "revokeInfoProcess_InfoRest", summary = "撤销资讯审核", description = "撤销资讯审核")
    public Result<Void> revokeInfoProcess(@RequestParam("infoId") String infoId) {
        infoProcessBiz.revokeInfoProcess(infoId);
        return Result.success();
    }


    @GetMapping("/getInfoAuditStatus")
    @Operation(deprecated = true, operationId = "getInfoAuditStatus", summary = "获取资讯审核状态", description = "获取资讯审核状态")
    public Result<Integer> getInfoAuditStatus(
        @Parameter(description = "资讯id") @RequestParam("resourceId") String resourceId) {
        return Result.success(infoProcessBiz.getInfoAuditStatus(resourceId));
    }

    @GetMapping("/detail")
    @Operation(operationId = "detail_InfoProcessRest", summary = "审核流程定义详情", description = "审核流程定义详情")
    public Result<ProcessDefinitionDetailDTO> detail(@RequestParam("definitionId") String definitionId) {
        // 审核流程定义详情
        return Result.success(infoProcessBiz.detail(definitionId));
    }
}
