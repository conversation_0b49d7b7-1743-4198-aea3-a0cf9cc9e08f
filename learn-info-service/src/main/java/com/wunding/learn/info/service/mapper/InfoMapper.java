package com.wunding.learn.info.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import com.wunding.learn.info.service.admin.dto.InfoDTO;
import com.wunding.learn.info.service.admin.dto.InfoDetailDTO;
import com.wunding.learn.info.service.admin.dto.InfoPreviewDTO;
import com.wunding.learn.info.service.admin.query.InfoQuery;
import com.wunding.learn.info.service.client.dto.InfoClientDTO;
import com.wunding.learn.info.service.client.query.InfoClientQuery;
import com.wunding.learn.info.service.client.query.InfoHomePageQuery;
import com.wunding.learn.info.service.model.Info;
import java.util.Collection;
import java.util.List;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 资讯表 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2022-08-02
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")
})
public interface InfoMapper extends BaseMapper<Info> {

    /**
     * 预览
     *
     * @param infoId 资讯id
     * @return {@link InfoPreviewDTO}
     */
    InfoPreviewDTO preview(String infoId);

    /**
     * 查询资讯信息
     *
     * @param infoQuery 信息查询
     * @return {@link List}<{@link InfoDTO}>
     */
    List<InfoDTO> selectInfoByPage(@Param("params") InfoQuery infoQuery);

    /**
     * 查询资讯客户端
     *
     * @param infoClientQuery 资讯查询对象
     * @return {@link List}<{@link InfoClientDTO}>
     */
    List<InfoClientDTO> selectInfoClientPage(@Param("params") InfoClientQuery infoClientQuery);

    /**
     * 管理端获取资讯详细
     *
     * @param infoId 资讯id
     * @return {@link InfoDetailDTO}
     */
    InfoDetailDTO getInfoDetail(String infoId);

    /**
     * 过滤获取已被删除的资源idl
     *
     * @param infoIdList
     * @return
     */
    List<String> getInvalidInfoId(@Param("infoIdList") Collection<String> infoIdList);

    /**
     * 查询首页资讯列表
     *
     * @param infoHomePageQuery
     * @return
     */
    List<InfoClientDTO> selectInfoPageHomeList(InfoHomePageQuery infoHomePageQuery);

    /**
     * 更新资讯浏览数
     *
     * @param id 资讯id
     */
    void updateViewNum(String id);

    /**
     * 是否存在资讯需要审核
     *
     * @param infoIdList 信息id列表
     * @return boolean
     */
    boolean isExistInfoNeedAudit(List<String> infoIdList);

}
