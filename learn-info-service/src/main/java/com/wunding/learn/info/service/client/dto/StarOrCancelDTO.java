package com.wunding.learn.info.service.client.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

/**
 * 点赞或取消dto
 *
 * <AUTHOR>
 * @date 2022/08/12
 */
@Data
@Accessors(chain = true)
@Schema(name = "StarOrCancelDTO", description = "点赞或取消对象")
public class StarOrCancelDTO {

    /**
     * id
     */
    @Schema(description = "资讯或评论id")
    @NotBlank(message = "资讯或评论id不能为空")
    @Length(max = 36, message = "资讯或评论id长度不能超过36")
    private String id;

    /**
     * 点赞类别 1 资讯点赞,2 资讯评论点赞
     */
    @Schema(description = "点赞类别 1 资讯点赞,2 资讯评论点赞")
    @NotNull(message = "点赞类型不能为空")
    @Min(value = 1, message = "点赞类型为非定义数字")
    @Min(value = 2, message = "点赞类型为非定义数字")
    private Integer categoryType;

}
