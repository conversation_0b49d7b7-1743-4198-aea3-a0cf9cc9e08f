package com.wunding.learn.plan.api.service.dto;

import com.wunding.learn.common.util.date.DateHelper;
import com.wunding.learn.file.api.dto.NamePath;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 报名用户资料api对象
 *
 * <AUTHOR>
 * @date 2022/07/13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "EnrollMemberResourceAo", description = "报名用户资料api对象")
public class EnrollMemberResourceAo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "用户id", hidden = true)
    private String userId;

    /**
     * 主键id
     */
    @Schema(description = "用户资料Id,修改时携带")
    private String memberResourceId;

    @Schema(description = "报名类型： 0 自报名 1 代报名", required = true)
    private Integer applyType;

    /**
     * 姓名
     */
    @Schema(description = "姓名", required = true)
    @NotBlank(message = "姓名必填")
    private String name;

    /**
     * 性别 0 未知 1 男 2 女
     */
    @Schema(description = "性别 0 未知 1 男 2 女", required = true)
    @NotNull(message = "性别必选")
    private Integer sex;


    /**
     * 报名记录id
     */
    @Schema(description = "报名记录id", hidden = true)
    private String applyRecordId;

    /**
     * 省份ID
     */
    @Schema(description = "省份ID", required = true)
    @NotBlank(message = "省份信息不能为空")
    private String provinceId;

    /**
     * 城市ID
     */
    @Schema(description = "城市ID", required = true)
    @NotBlank(message = "城市信息不能为空")
    private String cityId;

    /**
     * 职务
     */
    @Schema(description = "职务", required = true)
    @NotBlank(message = "职务信息不能为空")
    private String position;

    /**
     * 证件类型 1:身份证 2：护照
     */
    @Schema(description = "证件类型 1:身份证 2：护照 3：港澳通行证 4：港澳居民来往内地通行证(回乡证)", required = true)
    @NotNull(message = "证件类型不能为空")
    private Integer certificateType;

    /**
     * 证件号
     */
    @Schema(description = "证件号", required = true)
    @NotBlank(message = "证件号码不能为空")
    private String certificateNo;

    /**
     * 手机号码
     */
    @Schema(description = "手机号码", required = true)
    @NotBlank(message = "手机号码不能为空")
    @Length(max = 11, message = "手机号码不能超过11位")
    private String phone;

    /**
     * 来深工作时间
     */
    @Schema(description = "来深工作时间", required = true)
    @NotNull(message = "来深工作时间不能为空")
    @DateTimeFormat(pattern = DateHelper.YYYYMMDD)
    private Date shenzhenWorkTime;

    /**
     * 在深缴纳社保时间（月）
     */
    @Schema(description = "在深缴纳社保时间（月）")
    private Integer paySocialSecurity;

    /**
     * 公司/机构名称
     */
    @Schema(description = "公司/机构名称", required = true)
    @NotBlank(message = "公司/机构名称不能为空")
    private String companyName;

    /**
     * 单位联系方式
     */
    @Schema(description = "单位联系人手机号码", required = true)
    @NotBlank(message = "单位联系人手机号码不能为空")
    private String companyPhone;

    /**
     * 联系人
     */
    @Schema(description = "联系人", required = true)
    @NotBlank(message = "单位联系人不能为空")
    private String companyContact;

    /**
     * 单位通信地址
     */
    @Schema(description = "单位通信地址", required = true)
    @NotBlank(message = "单位通信地址不能为空")
    private String companyAddress;

    /**
     * 邮政编码
     */
    @Schema(description = "邮政编码", required = true)
    @NotBlank(message = "邮政编码不能为空")
    private String postalCode;


    /**
     * 所属行业id
     */
    @Schema(description = "所属行业id", required = true)
    @NotBlank(message = "所属杨业不能为空")
    private String professionId;

    /**
     * 其他机构
     */
    @Schema(description = "其他机构名称")
    private String otherProfessionName;

    /**
     * 公司规模id
     */
    @Schema(description = "公司规模id", required = true)
    @NotBlank(message = "公司规模不能为空")
    private String scaleId;

    /**
     * 报名id
     */
    @Schema(description = "报名id", hidden = true)
    private String enrollId;

    /**
     * 材料机构名称/扫描件机构名称
     */
    @Schema(description = "材料机构名称/扫描件机构名称", hidden = true)
    private String materialCompanyName;

    /**
     * 扫描文件图片路径
     */
    @Schema(description = "证明材料/扫描文件图片路径")
    @Size(max = 9, message = "证明材料最多支持9份")
    private List<NamePath> certificationImagePathList = new ArrayList<>();

    /**
     * 其他图片路径列表
     */
    @Schema(description = "其他材料图片路径")
    private List<NamePath> otherImagePathList = new ArrayList<>();

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 是否接受调剂 0否 1是
     */
    @Schema(description = "是否接受调剂 0否 1是")
    private Integer isAcceptTransfer;

    /**
     * 状态 1草稿 2审核中  5审核不通过(允许编辑) 6审核不通过(不允许编辑)  7审核通过/未分班/待分班 9分班/已分班
     */
    @Schema(description = "状态 1草稿 2审核中  5审核不通过(允许编辑) 6审核不通过(不允许编辑)  7审核通过/未分班/待分班 9分班/已分班", hidden = true)
    private Integer status;

    /**
     * 来自于保存 还是提交   1 保存 2 提交
     */
    @Schema(description = "提交方式 1 保存 2 提交", hidden = true)
    private Integer fromWhichOpt;

    /**
     * 数据来源 1小程序添加 2手动录入 3批量导入
     */
    @Schema(description = "数据来源 1小程序添加 2手动录入 3批量导入", hidden = true)
    private Integer sourceType;

    /**
     * 资源简历
     */
    @Schema(description = "资源简历")
    private List<ResourceResume> resourceResume = new ArrayList<>();

    @Data
    @Schema(name = "ResourceResume", description = "资源简历对象")
    public static class ResourceResume implements Serializable {

        private static final long serialVersionUID = -645731592561313611L;

        /**
         * 主键id
         */
        @Schema(description = "资源简历Id,添加不携带,修改携带")
        private String resumeId;

        /**
         * 工作开始时间
         */
        @Schema(description = "工作开始时间", required = true)
        private Date beginTime;

        /**
         * 工作结束时间
         */
        @Schema(description = "工作结束时间", required = true)
        private Date endTime;

        /**
         * 工作单位
         */
        @Schema(description = "工作单位", required = true)
        private String company;

        /**
         * 职务
         */
        @Schema(description = "职务", required = true)
        private String position;

    }

    /**
     * 资源志愿
     */
    @Schema(description = "资源志愿")
    private List<ResourceVolunteer> resourceVolunteers = new ArrayList<>();

    @Data
    @Schema(name = "ResourceVolunteer", description = "资源志愿对象")
    public static class ResourceVolunteer implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 主键id
         */
        @Schema(description = "资源志愿Id,添加不携带,修改携带")
        private String volunteerId;

        /**
         * 组织（机构）id
         */
        @Schema(description = "组织（机构）id")
        private String orgId;

        /**
         * 组织（机构）名称
         */
        @Schema(description = "组织（机构）名称")
        private String orgName;

        /**
         * 培训课题分类id
         */
        @Schema(description = "培训课题分类id")
        private String subjectCategoryId;

        /**
         * 培训课题分类名称
         */
        @Schema(description = "培训课题分类名称")
        private String subjectCategoryName;

        /**
         * 排序
         */
        @Schema(description = "排序，第一志愿传1，第二志愿传2，依次递增")
        private Integer sortNo;

    }

}
