package com.wunding.learn.plan.api.service;

import com.wunding.learn.common.dto.PlanStatisticSourceDataSaveDTO;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <p>
 * 计划相关统计feign
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">wgr</a>
 * @date 2023/3/22 16:09
 */
@FeignClient(url = "${learn.service.learn-plan-service}", name = "learn-plan-service", path = "/planStatistic")
public interface PlanStatisticFeign {

    /**
     * 保存计划和资源的关联数据信息
     *
     * @param saveDTO
     * @return
     */
    @GetMapping("/planStatistic/savePlanStatistic")
    void savePlanStatistic(@RequestBody PlanStatisticSourceDataSaveDTO saveDTO);

    /**
     * 更新资源的发布状态
     *
     * @param sourceIds
     * @param isPublish
     * @return
     */
    @GetMapping("/planStatistic/updateStatisticPublish")
    void updateStatisticPublish(@RequestBody List<String> sourceIds,
        @RequestParam("isPublish") Integer isPublish);


}
