server:
# 应用服务 WEB 访问端口
  port: 8080
  servlet:
    context-path: /
  tomcat:
    accept-count: 2000
    max-connections: 81920
    threads:
      max: 2000
      min-spare: 100

# 应用名称
spring:
  application:
    name: jacob-office

  #redis
  # Redis服务器地址
  redis:

    # Lettuce
    # 连接池最大连接数（使用负值表示没有限制）
    lettuce:
      # 关闭超时时间
      shutdown-timeout: 100
      pool:
        max-active: 1000
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: 10000
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池中的最小空闲连接
        min-idle: 0
  #rabbitmq 配置
  rabbitmq:
    publisher-returns: true
    publisher-confirm-type: correlated
    host: ************
    port: 30672
    virtual-host: /
    username: guest
    password: guest


    connection-timeout: 15000
    listener:
      simple:
        # 消费端有几个纯种
        concurrency: 10
        # 手动ACK
        acknowledge-mode: manual
        # 消费端的监听最大个数
        max-concurrency: 10
        # 每次拉取消息数量
        prefetch: 1
    template:
      exchange: file_exchange
      default-receive-queue: file_consumer
      routing-key: file_routing



#开启acutor端点
management:
  jmx:
    metrics:
      export:
        enabled: false
  endpoints:
# 禁用所有JMX端点暴露
    jmx:
      exposure:
        exclude: '*'
    web:
      exposure:
        include: 'info,health,metrics,prometheus'
  endpoint:
    health:
      show-details: always
      probes:
        enabled: true
  metrics:
    tags:
      application: ${spring.application.name}

app:
  storageType: 1
  endPoint: https://oss-test.wdxuexi.com
  accessKey: Rh2BL2nK9RWjqiPX
  secretKey: Xu0uU8xiDtr7bnmjpMVa2N7ZxPgGuLEn
  bucketName: dev
  region: shenzhen
  location: D:/
  pathMode : 1