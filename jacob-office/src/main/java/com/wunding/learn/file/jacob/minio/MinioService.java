package com.wunding.learn.file.jacob.minio;

import com.wunding.learn.common.constant.file.FileErrorNoEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.file.jacob.config.MinioConfig;
import jakarta.annotation.Resource;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.bouncycastle.util.Arrays;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.core.sync.ResponseTransformer;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MinioService {

    @Resource(name = "minioConfig")
    private MinioConfig minioConfig;

    @Resource(name = "s3Client")
    private S3Client s3Client;

    /**
     * 下载文件
     *
     * @param objectKey 对象存储key
     * @return File对象
     */
    public File downloadFile(String objectKey) {
        log.info("downloadFile objectKey:{}", objectKey);
        try {
            File file = new File(minioConfig.getPhysicalPath(objectKey));
            if (file.exists()) {
                // 如果文件大小是0，等文件下载完成，最长等60秒
                long startTime = System.currentTimeMillis();
                while (file.length() == 0 && (System.currentTimeMillis() - startTime) < (60 * 1000)) {
                    Thread.sleep(1000);
                    file = new File(minioConfig.getPhysicalPath(objectKey));
                    log.info("file size:{}", file.length());
                }
                return file;
            }

            // 清理 objectKey 前缀
            String objectName = objectKey;
            if (objectName.startsWith(MinioConfig.SEPARATOR)) {
                objectName = objectName.substring(1);
            }
            if (objectName.startsWith(minioConfig.getBucketName())) {
                objectName = objectName.substring(minioConfig.getBucketName().length() + 1);
            }

            log.info("downloadFile objectName:{}  bucketName:{}", objectName, minioConfig.getBucketName());
            Path targetPath = file.toPath();
            Files.createDirectories(targetPath.getParent());

            // 构造 S3 下载请求
            GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                .bucket(minioConfig.getBucketName())
                .key(objectName)
                .build();

            // 下载文件并写入本地
            s3Client.getObject(getObjectRequest, ResponseTransformer.toFile(targetPath));

            return file;
        } catch (Exception e) {
            log.error("下载文件发生异常：", e);
            Thread.currentThread().interrupt();
            throw new BusinessException(FileErrorNoEnum.DOWNLOAD_FILE_ERROR);
        }
    }

    /**
     * 文件上传到minio
     *
     * @param filePath  原文件绝对路径
     * @param objectKey 对象存储 key
     */
    public void uploadFile(String filePath, String objectKey) {
        if (objectKey.startsWith("/" + minioConfig.getBucketName())) {
            objectKey = objectKey.substring(minioConfig.getBucketName().length() + 1);
        }

        try {
            File tmpFile = new File(FilenameUtils.separatorsToUnix(filePath));
            String contentType = Files.probeContentType(tmpFile.toPath());
            if (filePath.endsWith("html")) {
                contentType = "text/html";
            }
            PutObjectRequest.Builder requestBuilder = PutObjectRequest.builder()
                .bucket(minioConfig.getBucketName())
                .key(objectKey);
            if (contentType != null) {
                requestBuilder.contentType(contentType);
            }
            PutObjectRequest putRequest = requestBuilder.build();

            long start = System.currentTimeMillis();
            s3Client.putObject(putRequest, RequestBody.fromFile(tmpFile));
            log.info("putObject time: {}ms", System.currentTimeMillis() - start);
        } catch (Exception e) {
            log.error("上传到对象服务器出错", e);
        }
    }

    /**
     * 上传目录
     *
     * @param srcPath    目录绝对路径
     * @param targetPath 对象存储路径
     */
    public void uploadDir(String srcPath, String targetPath) {
        try {
            // 如果文件夹不存在 则建立新文件夹
            File a = new File(FilenameUtils.separatorsToUnix((srcPath)));
            if (Arrays.isNullOrEmpty(a.list())) {
                return;
            }
            String[] file = a.list();
            File temp;
            long startTime = System.currentTimeMillis();
            for (int i = 0; i < Objects.requireNonNull(file).length; i++) {
                // 获取临时文件
                temp = getTempFile(srcPath, file, i);
                // temp是一个文件则直接上传
                tempIsFileUpload(targetPath, temp);
                // temp 如果是一个文件上传
                tempIsDirUpload(srcPath, targetPath, file, temp, i);
                log.info("耗时：" + (System.currentTimeMillis() - startTime));
            }
        } catch (Exception e) {
            log.error("复制目录出错误", e);
        }
    }

    /**
     * temp是目录上传
     *
     * @param srcPath    src路径
     * @param targetPath 目标路径
     * @param file       文件
     * @param temp       临时雇员
     * @param i          我
     */
    private void tempIsDirUpload(String srcPath, String targetPath, String[] file, File temp, int i) {
        if (temp.isDirectory()) {
            // 如果是子文件夹
            String filePath = file[i];
            uploadDir(srcPath + MinioConfig.SEPARATOR + filePath, targetPath + MinioConfig.SEPARATOR + filePath);
        }
    }

    /**
     * temp是文件上传
     *
     * @param targetPath 目标路径
     * @param temp       临时雇员
     */
    private void tempIsFileUpload(String targetPath, File temp) {
        if (temp.isFile()) {
            String objectName =
                FilenameUtils.separatorsToUnix(targetPath + MinioConfig.SEPARATOR + temp.getName());

            if (objectName.startsWith(MinioConfig.SEPARATOR)) {
                objectName = objectName.substring(1);
            }
            if (objectName.startsWith(minioConfig.getBucketName())) {
                objectName = objectName.substring(minioConfig.getBucketName().length() + 1);
            }
            uploadFile(temp.getAbsolutePath(), objectName);

        }
    }

    @NotNull
    private File getTempFile(String srcPath, String[] file, int i) {
        File temp;
        if (srcPath.endsWith(MinioConfig.SEPARATOR)) {
            temp = new File(srcPath + file[i]);
        } else {
            temp = new File(srcPath + MinioConfig.SEPARATOR + file[i]);
        }
        return temp;
    }
}
