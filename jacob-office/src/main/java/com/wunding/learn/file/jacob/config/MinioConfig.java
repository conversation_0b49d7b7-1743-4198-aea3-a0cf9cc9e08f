package com.wunding.learn.file.jacob.config;

import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import java.net.URI;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3ClientBuilder;
import software.amazon.awssdk.services.s3.S3Configuration;

/**
 * <AUTHOR>
 */
@ConfigurationProperties("app")
@Configuration("minioConfig")
@Data
public class MinioConfig {

    private String endPoint;

    private String accessKey;

    private String secretKey;

    private String region;

    private String storageType;

    private String bucketName;

    private String location;

    /**
     * 对象存储path模式
     */
    private Integer pathMode;

    public static final String SEPARATOR = "/";

    @Bean("s3Client")
    public S3Client S3Client() {
        String bucketRegion = StringUtils.isNotBlank(region) ? region : "us-east-1";
        StaticCredentialsProvider credentialsProvider = StaticCredentialsProvider.create(
            AwsBasicCredentials.create(accessKey, secretKey));

        // S3Client 构建(用于上传下载等操作)
        S3ClientBuilder s3Builder = S3Client.builder()
            .region(Region.of(bucketRegion))
            .credentialsProvider(credentialsProvider)
            .endpointOverride(URI.create(endPoint));

        // 是否path模式
        if (GeneralJudgeEnum.CONFIRM.getValue().equals(pathMode)) {
            s3Builder.serviceConfiguration(S3Configuration.builder().pathStyleAccessEnabled(true).build());
        }
        return s3Builder.build();
    }

    public String getPhysicalPath(String path) {
        String physicalPath = location;
        if (StringUtils.endsWith(physicalPath, SEPARATOR)) {
            physicalPath = physicalPath.substring(0, physicalPath.length() - 1);
        }
        if (!StringUtils.startsWith(path, SEPARATOR)) {
            physicalPath = physicalPath + SEPARATOR + path;
        } else {
            physicalPath = physicalPath + path;
        }
        return physicalPath;
    }
}
