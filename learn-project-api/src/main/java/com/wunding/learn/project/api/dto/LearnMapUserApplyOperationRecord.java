package com.wunding.learn.project.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 学习项目创建-用户对象
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @since 2023-12-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "LearnMapUserApplyOperationRecord", description = "用户能力培训申请对象")
public class LearnMapUserApplyOperationRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "学习形式id")
    private String categoryType;

    @Schema(description = "申请值")
    private Integer price;

    @Schema(description = "申请时间")
    private Date applyDate;

    @Schema(description = "申请值单位表示，天：1，月：30，年：365")
    private Integer priceType;
}
