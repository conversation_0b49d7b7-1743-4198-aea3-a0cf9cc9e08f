package com.wunding.learn.project.api.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p> 项目统计返回对象
 *
 * <AUTHOR> href="mailto:<EMAIL>">liangzm</a>
 * @since 2025/2/12
 */
@Data
@Schema(name = "ProjectStatisticDTO", description = "项目统计返回对象")
@Accessors(chain = true)
public class ProjectStatisticDTO implements Serializable {

    private static final long serialVersionUID = -1308937565435536585L;

    @Schema(description = "项目id")
    private String id;

    @Schema(description = "项目编号")
    private String proNo;

    @Schema(description = "项目名称")
    private String proName;

    @Schema(description = "开始时间")
    private Date startTime;

    @Schema(description = "结束时间")
    private Date endTime;

    @JsonIgnore
    @Schema(description = "创建、归属部门Id", hidden = true)
    private String orgId;

    @Schema(description = "归属部门")
    private String orgName;

    @Schema(description = "归属部门全路径")
    private String orgPath;

    @JsonIgnore
    @Schema(description = "班主任id", hidden = true)
    private String leader;

    @Schema(description = "班主任")
    private String leaderName;

    @Schema(description = "培训天数")
    private Integer trainDays;

    @Schema(description = "学员人数")
    private Long peopleNum;

    @Schema(description = "讲师人数")
    private Long lecturerNum;

    @Schema(description = "项目评价")
    private String projectScore;
}
