package com.wunding.learn.project.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <p>
 * 学习项目DTO
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">wgr</a>
 * @date 2023/3/16 14:17
 */
@Data
@Schema(name = "ProjectSaveDTO", description = "学习项目创建对象")
public class ProjectBaseDTO {

    @Schema(description = "项目类型 0普通项目 1快速培训项目 2线上课程学习")
    private Integer projectType;

    @Schema(description = "是否发布 0不 1是")
    private Integer isPublish;

    @Schema(description = "阶段ID")
    private String stageId;

    @Schema(description = "引用类型  0=默认值  1=培训项目", hidden = true)
    private Integer referencedType;

    @Schema(description = "引用类型名称", hidden = true)
    private String referencedName;

    @Schema(description = "引用资源的ID,如培训项目ID")
    private String referencedId;

    @Schema(description = "项目名称", required = true)
    @NotBlank(message = "项目名称不能为空")
    @Length(max = 80, message = "项目名称长度不能超过80")
    private String proName;

    @Schema(description = "关联计划Id")
    @Length(max = 36, message = "Id长度不能超过36")
    private String trainPlanId;

    @Schema(description = "项目描述 说明")
    @Length(max = 500, message = "项目描述长度不能超过500")
    private String proDesc;

    @Schema(description = "封面地址", required = true)
    @NotBlank(message = "封面地址不能为空")
    private String coverImagePath;

    @Schema(description = "封面图片名称", required = true)
    @Length(max = 50, message = "封面图片名称长度不能超过50")
    private String coverImageName;


    @Schema(description = "班主任用户id")
    @Length(max = 36, message = "Id长度不能超过36")
    private String leaderId;

    @Schema(description = "班主任不参与统计 0-否，班主任参与统计 1是，班主任不参与统计")
    private Integer leaderDontStat;


    @Schema(description = "项目类型 0-面授 1-远程 2-面授加远程")
    private String proMethod;

    @Schema(description = "培训分类id", hidden = true)
    private String trainCategoryId;

    @Schema(description = "计划类型 0固定日期，1固定周期", required = true)
    @Max(1)
    @Min(0)
    @NotNull(message = "计划类型不能为空")
    private Integer type;

    @Schema(description = "固定日期开始时间")
    private Date startTime;

    @Schema(description = "固定日期结束时间")
    private Date endTime;

    @NotEmpty(message = "学习项目栏目 开通应用不能为空")
    @Schema(description = "学习项目栏目 开通应用")
    private List<String> projectItem;

    @Schema(description = "下发方式：0 部分可见 1仅创建者可见 2所有人可见", required = true)
    @NotNull(message = "下发可见方式不可为空")
    private Integer viewType;

    @Schema(description = "可见范围方案id")
    private Long programmeId;

    @Schema(description = "学习项目阶段")
    @Valid
    private List<ProphaseSaveDTO> prophaseSaveDTOS;

    @Data
    @Schema(name = "ProphaseSaveDTO", description = "学习项目阶段对象")
    public static class ProphaseSaveDTO {

        @Schema(description = "项目阶段id")
        @Length(max = 36, message = "项目阶段id长度不能超过36")
        private String id;

        @Schema(description = "项目阶段名称")
        @NotBlank(message = "项目阶段名称")
        @Length(max = 20, message = "项目阶段名称长度不能超过20")
        private String name;

        @Schema(description = "项目阶段排序")
        @NotNull(message = "排序值不能为空")
        @Min(value = 0, message = "项目阶段排序不能是负数")
        private Long sort;
    }

}
