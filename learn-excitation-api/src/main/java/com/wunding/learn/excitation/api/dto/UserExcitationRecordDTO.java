package com.wunding.learn.excitation.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户项目激励获取记录
 *
 * <AUTHOR>
 * @since 2025-06-14
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(name = "UserExcitationRecordDTO", description = "用户项目激励获取记录")
public class UserExcitationRecordDTO implements Serializable {


    private static final long serialVersionUID = -6112636989379933053L;

    /**
     * 序号
     */
    @Schema(description = "序号")
    private Integer no;

    /**
     * 事件名称
     */
    @Schema(description = "事件名称")
    private String name;

    /**
     * 数值
     */
    @Schema(description = "数值")
    private BigDecimal value;


}
