package com.wunding.learn.excitation.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 用户 excitation 统计数据
 * <AUTHOR>
 * @date 2025/02/07
 */
@Data
public class UserExcitationStatisticDTO implements Serializable {

    public static final long serialVersionUID = 2196025447642079733L;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private String userId;

    /**
     * 学时
     */
    @Schema(description = "学时")
    private BigDecimal learnTime;

    /**
     * 积分
     */
    @Schema(description = "积分")
    private BigDecimal integral;

    /**
     * 金币
     */
    @Schema(description = "金币")
    private BigDecimal goldCoin;

    /**
     * 学分
     */
    @Schema(description = "学分")
    private BigDecimal credit;
}
