package com.wunding.learn.excitation.api.query;

import com.wunding.learn.common.bean.BasePageQuery;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 用户学习项目激励获取记录查询对象
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
public class UserProjectExcitationRecordQuery extends BasePageQuery implements Serializable {


    private static final long serialVersionUID = 5554569620009110909L;
    /**
     * 学习项目ID
     */
    @Parameter(description = "学习项目ID")
    @NotBlank(message = "学习项目ID不可为空")
    private String projectId;

    /**
     * 学习项目相关资源ID集合（从学习项目服务中获取）
     */
    @Parameter(description = "学习项目相关资源ID集合（从学习项目服务中获取）", hidden = true)
    private List<String> targetIds;

    /**
     * 用户ID
     */
    @Parameter(description = "用户ID")
    @NotBlank(message = "用户ID不可为空")
    private String userId;

    /**
     * 查询类型
     */
    @Parameter(description = "查询类型：0-学时，1-积分，2-金币，3-学分")
    @Schema(allowableValues = {"0", "1", "2", "3"}, example = "0")
    @NotBlank(message = "查询类型不可为空")
    private Integer type;
}
