package com.wunding.learn.excitation.api.service;

import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.dto.MyStatisticListDTO;
import com.wunding.learn.excitation.api.dto.TargetValueGottenDetailClientDTO;
import com.wunding.learn.excitation.api.dto.UserExcitationDataDTO;
import com.wunding.learn.excitation.api.dto.UserExcitationInitDTO;
import com.wunding.learn.excitation.api.dto.UserExcitationRecordDTO;
import com.wunding.learn.excitation.api.dto.UserExcitationReduceDTO;
import com.wunding.learn.excitation.api.dto.UserExcitationStatisticDTO;
import com.wunding.learn.excitation.api.dto.UserExcitationTradeBaseDTO;
import com.wunding.learn.excitation.api.dto.UserGottenTargetValueDetailDTO;
import com.wunding.learn.excitation.api.query.UserProjectExcitationRecordQuery;
import com.wunding.learn.excitation.api.query.UserTargetValueQuery;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2022/11/9
 */
@FeignClient(url = "${learn.service.learn-excitation-service}", name = "learn-excitation-service", path = "/excitation")
public interface ExcitationFeign {

    /**
     * 初始化用户激励配置信息
     *
     * @param dto
     */
    @PostMapping("/initUserExcitationInfo")
    void initUserExcitationInfo(@RequestBody UserExcitationInitDTO dto);

    /**
     * 获取用户积分
     *
     * @param userIdList 用户id列表
     * @return {@link Map}<{@link String}, {@link BigDecimal}>
     */
    @GetMapping("/getUserIntegral")
    Map<String, BigDecimal> getUserIntegral(@RequestParam("userIdList") Collection<String> userIdList);

    /**
     * 获取用户金币数量
     *
     * @param curUserId 添加用户id
     * @return {@link Integer}
     */
    @GetMapping("/getUserGold")
    Integer getUserGold(@RequestParam("curUserId") String curUserId);

    /**
     * 获取用户称号
     *
     * @param userIds 用户ID
     * @return {@link Map}<{@link String}, {@link String}>
     */
    @GetMapping("/getUsersTitle")
    Map<String, String> getUsersTitle(@RequestParam("userIds") Collection<String> userIds);

    /**
     * 是否已经完成了激励交易
     *
     * @param dto
     */
    @PostMapping("/isFinishExcitationTrade")
    <T extends UserExcitationTradeBaseDTO> Boolean isFinishExcitationTrade(@RequestBody T dto);

    /**
     * 扣除用户激励
     *
     * @param reduceDTO
     */
    @PostMapping("/reduceUserExcitation")
    void reduceUserExcitationWithCheck(@RequestBody UserExcitationReduceDTO reduceDTO);

    /**
     * 查询用户 激励记录数
     *
     * @param userId
     * @param sourceId
     * @param eventId
     * @param operateType
     */
    @GetMapping("/getUserExcitationRecordCount")
    Integer getUserExcitationRecordCount(@RequestParam("userId") String userId,
        @RequestParam("sourceId") String sourceId, @RequestParam("eventId") String eventId,
        @RequestParam("operateType") Integer operateType);

    /**
     * 返还用户兑换学习消费
     *
     * @param projectId
     * @param userId
     */
    @GetMapping("/returnUserProjectExchange")
    void returnUserProjectExchange(@RequestParam("projectId") String projectId, @RequestParam("userId") String userId);

    /**
     * 获取个人可兑换激励数
     * @param excitationType 激励类型
     * @return
     */
    @GetMapping("/getCourseExchangeable")
    Object getCourseExchangeable(@RequestParam("excitationType") String excitationType);

    /**
     * 获取个人可兑换激励数
     * @param excitationType 激励类型
     * @return
     */
    @GetMapping("/getExcitation")
    Object getExcitation(@RequestParam("excitationType") String excitationType);

    /**
     * 查用户在目标管理中获取的目标值
     *
     * @param query {@link UserTargetValueQuery}
     * @return key->userId,value->用户获取的目标值
     */
    @PostMapping("/getUserHasBeenGotTargetValue")
    Map<String, Integer> getUserHasBeenGotTargetValue(@RequestBody UserTargetValueQuery query);

    /**
     * 查用户在目标管理中获取目标值的详情
     *
     * @param query {@link UserTargetValueQuery}
     * @return key->userId,value->用户获取的目标值
     */
    @PostMapping("/queryUserGottenTargetValueDetailClientList")
    PageInfo<TargetValueGottenDetailClientDTO> queryUserGottenTargetValueDetailClientList(@RequestBody UserTargetValueQuery query);

    /**
     * 查用户在目标管理中获取目标值的详情
     *
     * @param query {@link UserTargetValueQuery}
     * @return key->userId,value->用户获取的目标值
     */
    @PostMapping("/queryUserGottenTargetValueDetailList")
    PageInfo<UserGottenTargetValueDetailDTO> queryUserGottenTargetValueDetailList(@RequestBody UserTargetValueQuery query);

    /**
     *
     * @param checkSysConfig
     * @return
     */
    @GetMapping("/getPersonalInfo")
    List<MyStatisticListDTO> getPersonalInfo(boolean checkSysConfig) throws IllegalAccessException;

    /**
     * 查用户的学时学分积分
     *
     * @param userId 用户id
     * @return
     */
    @GetMapping("/getUserExcitationData")
    UserExcitationDataDTO getUserExcitationData(@RequestParam("userId") String userId);

    /**
     * 根据用户ID列表获取用户激励统计数据
     *
     * @param userIds   用户id集合
     * @param targetIds 目标id集合
     * @return 用户激励统计数据
     */
    @PostMapping("/getUserExcitationData")
    Map<String, UserExcitationStatisticDTO> getUserExcitationStatistic(
        @RequestParam("userIds") Collection<String> userIds,
        @RequestParam("targetIds") Collection<String> targetIds);

    /**
     * 获取用户学习项目激励记录
     *
     * @param query 用户学习项目激励获取记录查询对象
     * @return 用户项目激励记录分页列表
     */
    @PostMapping("/getUserProjectExcitationRecord")
    PageInfo<UserExcitationRecordDTO> getUserProjectExcitationRecord(
        @RequestBody UserProjectExcitationRecordQuery query);
}
