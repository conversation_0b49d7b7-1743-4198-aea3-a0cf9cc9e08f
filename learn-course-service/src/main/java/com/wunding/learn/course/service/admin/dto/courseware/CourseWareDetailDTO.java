package com.wunding.learn.course.service.admin.dto.courseware;

import com.wunding.learn.user.api.dto.AiCwSaveConfig;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 课件详情
 *
 * <AUTHOR> href="mailto:<EMAIL>">yuxueyi</a>
 * @version V7.0.0
 * @since 2022/6/8  11:14
 */
@Data
@Accessors(chain = true)
@Schema(name = "CourseWareDetailDTO", description = "课件详情对象")
public class CourseWareDetailDTO {

    @Schema(description = "课件Id")
    private String id;

    @Schema(description = "课件作者")
    private String cwAuthor;

    @Schema(description = "课件名称")
    private String cwName;

    @Schema(description = "课程章节ID")
    private String chapterId;

    @Schema(description = "课件创建来源: 0-在线做课 1-上传课件 2-课件库 5-客户端上传课件 6-PC上传课件 10-课件模板")
    private Integer isSource;

    @Schema(description = "模板类型 1：模板1,2：模板2, 3：模板3, 4：模板4")
    private Integer modelType;

    @Schema(description = "课件类型，上传文件时必填，从课件文件上传接口获取")
    private String cwType;

    @Schema(description = "课件内容")
    private String cwContent;

    @Schema(description = "课件时长,单位秒，管理员输入的时长")
    private Integer playTime;


    @Schema(description = "能否倍速[0:否，1:是]")
    private Integer canSpeed;

    @Schema(description = "能否拖拽进度条[0:否，1:是]")
    private Integer canDrag;

    /**
     * 完成类型[0:按时长，1:按进度]
     */
    @Schema(description = "完成类型[0:按时长，1:按进度]")
    private Integer finishType;

    @Schema(description = "当完成类型为按进度时的课件学习进度百分比要求(0-100)，仅在finish_type=1时有效")
    private Integer requiredProgress;

    @Schema(description = "课件真实时长（视频/mp3）,即接口返回的时长")
    private Integer realPlayTime;

    @Schema(description = "是否播放防挂机（1：是，0：否）")
    private Integer isHangup;

    @Schema(description = "防挂机时长（分）")
    private Integer hangupDurationMinute;

    @Schema(description = "防挂机倒计时长（秒）")
    private Integer hangupDurationSecond;

    @Schema(description = "显示顺序")
    private Integer sortNo;

    @Schema(description = "考试Id")
    private String examId;

    @Schema(description = "课件关联考试时的完成条件（-1:不涉及考试 0:提交考试即完成 1:通过考试才完成，仅在 exam_id 不为空时生效）")
    private Integer examFinishType;

    @Schema(description = "考试名称")
    private String examName;

    @Schema(description = "关联资源名称")
    private String cwFileName;

    @Schema(description = "关联mp3名称")
    private String cwMp3Name;

    @Schema(description = "是否更新文件 0-否 1-是  更新时，重新上传了文件,传1")
    private Integer isUpdateFile;

    @Schema(description = "是否更新mp3附件文件 0-否 1-是  有更新mp3附件文件时,传1")
    private Integer isUpdateMp3;

    /**
     * 课件AI开关
     */
    @Schema(description = "ai开关 关键词：aiCwKeyword,摘要:aiCwDesc,试题:aiCwQuestion,大纲:aiCwOutline")
    private List<AiCwSaveConfig> aiSwitch = new ArrayList<>();


}
