package com.wunding.learn.course.service.service.impl;

import static com.wunding.learn.common.util.string.StringUtil.newId;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import com.wunding.learn.apply.api.query.AuthQuery;
import com.wunding.learn.apply.api.service.ApplyFeign;
import com.wunding.learn.comment.api.service.CommentFeign;
import com.wunding.learn.common.bean.BaseEntity;
import com.wunding.learn.common.category.model.Categorys;
import com.wunding.learn.common.category.service.ICategorysService;
import com.wunding.learn.common.constant.course.CourseErrorNoEnum;
import com.wunding.learn.common.constant.dynamicDataSource.DynamicDataSourceConstant;
import com.wunding.learn.common.constant.other.AvailableEnum;
import com.wunding.learn.common.constant.other.BaseErrorNoEnum;
import com.wunding.learn.common.constant.other.ClientTypeEnum;
import com.wunding.learn.common.constant.other.CommonConstants;
import com.wunding.learn.common.constant.other.DelEnum;
import com.wunding.learn.common.constant.other.ErrorNoEnum;
import com.wunding.learn.common.constant.tenant.TenantRedisKeyConstant;
import com.wunding.learn.common.constant.user.UserErrorNoEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.directory.dto.SaveOrUpdateResourceDirectoryDTO;
import com.wunding.learn.common.directory.service.IDirectoryService;
import com.wunding.learn.common.dto.CerDitchDTO;
import com.wunding.learn.common.dto.LecturerCourseDetailDTO;
import com.wunding.learn.common.dto.PublishDTO;
import com.wunding.learn.common.dto.ReduceExcitationDTO;
import com.wunding.learn.common.dto.ResourceBaseDTO;
import com.wunding.learn.common.dto.ResourceMemberCardDTO;
import com.wunding.learn.common.dto.SysTagBaseDTO;
import com.wunding.learn.common.enums.ResourceTypeEnum;
import com.wunding.learn.common.enums.category.CategoryType;
import com.wunding.learn.common.enums.comment.CommentTypeEnum;
import com.wunding.learn.common.enums.course.CourseAuditStatusEnum;
import com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum;
import com.wunding.learn.common.enums.excitation.ExcitationEventEnum;
import com.wunding.learn.common.enums.excitation.ExcitationTypeEnum;
import com.wunding.learn.common.enums.file.CWTypeEnum;
import com.wunding.learn.common.enums.file.TranscodeStatusEnum;
import com.wunding.learn.common.enums.market.FirstInfoContentEnum;
import com.wunding.learn.common.enums.market.HeadContentRuleEnum;
import com.wunding.learn.common.enums.other.ContentRuleEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.other.OperationEnum;
import com.wunding.learn.common.enums.other.PublishStatusEnum;
import com.wunding.learn.common.enums.other.ResourceChangeEnum;
import com.wunding.learn.common.enums.other.ResourceTypeCodeEnum;
import com.wunding.learn.common.enums.other.ResourcesTypeEnum;
import com.wunding.learn.common.enums.other.RouterVisitEnum;
import com.wunding.learn.common.enums.other.SynUpdateCopyDataEventEnum;
import com.wunding.learn.common.enums.other.SystemConfigCodeEnum;
import com.wunding.learn.common.enums.other.TradeTypeEnum;
import com.wunding.learn.common.enums.other.VoteType;
import com.wunding.learn.common.enums.process.ProcessDefinitionTypeEnum;
import com.wunding.learn.common.enums.process.ProcessStatusEnum;
import com.wunding.learn.common.enums.push.PushType;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.exception.handler.ApiAssert;
import com.wunding.learn.common.i18n.util.I18nUtil;
import com.wunding.learn.common.library.record.enums.HandleTypeEnum;
import com.wunding.learn.common.library.record.enums.LibraryTypeEnum;
import com.wunding.learn.common.library.record.service.BaseLibraryRecordService;
import com.wunding.learn.common.mq.dto.ExcitationMQEventDTO;
import com.wunding.learn.common.mq.dto.ResourceConfigInitDTO;
import com.wunding.learn.common.mq.dto.ResourceStatusDTO;
import com.wunding.learn.common.mq.dto.SearchKeyDetailDTO;
import com.wunding.learn.common.mq.event.HomeRouterVisitEvent;
import com.wunding.learn.common.mq.event.ResourceChangeEvent;
import com.wunding.learn.common.mq.event.ResourceInteractEvent;
import com.wunding.learn.common.mq.event.ResourceInteractEvent.ResourceInteractEventRoutingKeyConstants;
import com.wunding.learn.common.mq.event.ResourceOperateEvent;
import com.wunding.learn.common.mq.event.SearchKeyDetailEvent;
import com.wunding.learn.common.mq.event.SynUpdateCopyCourseEvent;
import com.wunding.learn.common.mq.event.course.CourseCategoryChangeEvent;
import com.wunding.learn.common.mq.event.course.CourseFinishEvent;
import com.wunding.learn.common.mq.event.course.CourseTrainCategoryChangeEvent;
import com.wunding.learn.common.mq.event.course.DeleteAbilityCourseRelateEvent;
import com.wunding.learn.common.mq.event.excitation.ExcitationInitMqEvent;
import com.wunding.learn.common.mq.event.excitation.ExcitationMQEvent;
import com.wunding.learn.common.mq.event.market.FirstInfoViewLimitChangeEvent;
import com.wunding.learn.common.mq.event.project.ActivityStatusChangeEvent;
import com.wunding.learn.common.mq.service.MqProducer;
import com.wunding.learn.common.multi.language.dto.MultiLangMessageDTO;
import com.wunding.learn.common.multi.language.model.MultiLangMessage;
import com.wunding.learn.common.multi.language.service.IMultiLangMessageService;
import com.wunding.learn.common.mybatis.service.impl.BaseServiceImpl;
import com.wunding.learn.common.query.ResourceBaseQuery;
import com.wunding.learn.common.query.SysTagBaseQuery;
import com.wunding.learn.common.redis.util.RedisLockUtil;
import com.wunding.learn.common.share.dto.PosterShareClientDTO;
import com.wunding.learn.common.share.dto.PosterShareResourceDTO;
import com.wunding.learn.common.share.service.IPosterShareService;
import com.wunding.learn.common.util.bean.BeanListUtils;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.date.DateUtil;
import com.wunding.learn.common.util.http.DifyApiClient;
import com.wunding.learn.common.util.http.WebUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.util.string.StringPool;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.common.util.string.TranslateUtil;
import com.wunding.learn.common.viewlimit.service.IResourceViewLimitService;
import com.wunding.learn.course.api.constants.CWCreateSourceEnum;
import com.wunding.learn.course.api.dto.CourseInfoDTO;
import com.wunding.learn.course.api.dto.CourseSimpleInfoDTO;
import com.wunding.learn.course.api.dto.CourseViewLimitCheckResultDTO;
import com.wunding.learn.course.api.query.CourseSummaryListQuery;
import com.wunding.learn.course.service.admin.dto.BatchMoveCategoryDTO;
import com.wunding.learn.course.service.admin.dto.CourseCategoryStatDTO;
import com.wunding.learn.course.service.admin.dto.CourseDetailDTO;
import com.wunding.learn.course.service.admin.dto.CourseLearnDetailDTO;
import com.wunding.learn.course.service.admin.dto.CourseListDTO;
import com.wunding.learn.course.service.admin.dto.CourseProcessListDTO;
import com.wunding.learn.course.service.admin.dto.CourseStudyDetailDTO;
import com.wunding.learn.course.service.admin.dto.CourseWithoutLinkDTO;
import com.wunding.learn.course.service.admin.dto.CoursewareStudyDetailDTO;
import com.wunding.learn.course.service.admin.dto.KeyWordResultDTO;
import com.wunding.learn.course.service.admin.dto.RecommendDTO;
import com.wunding.learn.course.service.admin.dto.SaveCourseDTO;
import com.wunding.learn.course.service.admin.dto.SaveTrainCourseDTO;
import com.wunding.learn.course.service.admin.dto.ShareDTO;
import com.wunding.learn.course.service.admin.dto.TrainCourseDetailDTO;
import com.wunding.learn.course.service.admin.dto.TrainCourseListDTO;
import com.wunding.learn.course.service.admin.dto.courseware.SaveCourseWareDTO;
import com.wunding.learn.course.service.admin.query.CourseLearnQuery;
import com.wunding.learn.course.service.admin.query.CourseProcessQuery;
import com.wunding.learn.course.service.admin.query.CourseQuery;
import com.wunding.learn.course.service.admin.query.CourseStudyQuery;
import com.wunding.learn.course.service.admin.query.CoursewareStudyQuery;
import com.wunding.learn.course.service.admin.query.TrainCourseQuery;
import com.wunding.learn.course.service.client.dto.BaseCourseDTO;
import com.wunding.learn.course.service.client.dto.BusinessCourseListDTO;
import com.wunding.learn.course.service.client.dto.CategoryCourseDTO;
import com.wunding.learn.course.service.client.dto.CourseCollectionDTO;
import com.wunding.learn.course.service.client.dto.CourseDetailClientDTO;
import com.wunding.learn.course.service.client.dto.CourseItemNumberDTO;
import com.wunding.learn.course.service.client.dto.CourseTagDTO;
import com.wunding.learn.course.service.client.dto.CourseValueDTO;
import com.wunding.learn.course.service.client.dto.CoursewareDetailClientDTO;
import com.wunding.learn.course.service.client.dto.CoursewareMp3DTO;
import com.wunding.learn.course.service.client.dto.CoursewareStarDTO;
import com.wunding.learn.course.service.client.dto.GradeCourseStarDTO;
import com.wunding.learn.course.service.client.dto.LikeDTO;
import com.wunding.learn.course.service.client.dto.MarkInfoDTO;
import com.wunding.learn.course.service.client.dto.MyCourseDTO;
import com.wunding.learn.course.service.client.dto.PostCourseClientDTO;
import com.wunding.learn.course.service.client.dto.ProjectTaskCourseInteractDTO;
import com.wunding.learn.course.service.client.dto.RatingCourseDTO;
import com.wunding.learn.course.service.client.dto.RecommendCourseDTO;
import com.wunding.learn.course.service.client.dto.RelatedCourseDTO;
import com.wunding.learn.course.service.client.dto.StudyRecordDTO;
import com.wunding.learn.course.service.client.dto.SubordinateCourseDTO;
import com.wunding.learn.course.service.client.factory.CoursewareContentFactory;
import com.wunding.learn.course.service.client.query.CategoryCourseQueryDTO;
import com.wunding.learn.course.service.client.query.CourseCollectionQuery;
import com.wunding.learn.course.service.client.query.CourseDetailClientQuery;
import com.wunding.learn.course.service.client.query.CourseHomePageQuery;
import com.wunding.learn.course.service.client.query.CourseListQuery;
import com.wunding.learn.course.service.client.query.DownloadShiroQuery;
import com.wunding.learn.course.service.client.query.FavoriteCourseQuery;
import com.wunding.learn.course.service.client.query.MyCourseQuery;
import com.wunding.learn.course.service.client.query.PostCourseClientQuery;
import com.wunding.learn.course.service.client.query.RecommendCourseQuery;
import com.wunding.learn.course.service.client.query.RelatedCourseQuery;
import com.wunding.learn.course.service.client.query.SaveGradeCourseStarDTO;
import com.wunding.learn.course.service.client.query.SearchCourseQuery;
import com.wunding.learn.course.service.client.query.SearchCourseWareQuery;
import com.wunding.learn.course.service.client.query.SubordinateCourseQuery;
import com.wunding.learn.course.service.component.CourseViewLimitComponent;
import com.wunding.learn.course.service.component.CourseWithoutLinkFactory;
import com.wunding.learn.course.service.component.CoursewareLibViewLimitComponent;
import com.wunding.learn.course.service.constant.CourseConstant;
import com.wunding.learn.course.service.constant.CourseRedisKeyEnum;
import com.wunding.learn.course.service.constant.CourseTagCreateTypeEnum;
import com.wunding.learn.course.service.dao.CourseDao;
import com.wunding.learn.course.service.mapper.CourseCategoryMapper;
import com.wunding.learn.course.service.mapper.CourseFavorateMapper;
import com.wunding.learn.course.service.mapper.CourseMapper;
import com.wunding.learn.course.service.mapper.CourseTagMapper;
import com.wunding.learn.course.service.mapper.CourseViewMapper;
import com.wunding.learn.course.service.mapper.CoursewareMapper;
import com.wunding.learn.course.service.mapper.CoursewarePackageMapper;
import com.wunding.learn.course.service.mapper.TagMapper;
import com.wunding.learn.course.service.mapper.UserCourseRecordMapper;
import com.wunding.learn.course.service.model.Course;
import com.wunding.learn.course.service.model.CourseCategory;
import com.wunding.learn.course.service.model.CourseChapter;
import com.wunding.learn.course.service.model.CourseFavorate;
import com.wunding.learn.course.service.model.CourseRefDify;
import com.wunding.learn.course.service.model.CourseShareRecord;
import com.wunding.learn.course.service.model.CourseStar;
import com.wunding.learn.course.service.model.CourseStudyPlan;
import com.wunding.learn.course.service.model.CourseTag;
import com.wunding.learn.course.service.model.CourseView;
import com.wunding.learn.course.service.model.CourseVote;
import com.wunding.learn.course.service.model.CourseWithout;
import com.wunding.learn.course.service.model.Courseware;
import com.wunding.learn.course.service.model.CoursewarePackage;
import com.wunding.learn.course.service.model.CoursewarePackageMergeRecord;
import com.wunding.learn.course.service.model.CoursewareQuestion;
import com.wunding.learn.course.service.model.CoursewareStar;
import com.wunding.learn.course.service.model.CoursewareUserRecord;
import com.wunding.learn.course.service.model.UserCourseRecord;
import com.wunding.learn.course.service.service.ICourseCategoryService;
import com.wunding.learn.course.service.service.ICourseChapterService;
import com.wunding.learn.course.service.service.ICourseFavorateService;
import com.wunding.learn.course.service.service.ICourseKeywordService;
import com.wunding.learn.course.service.service.ICourseNoteService;
import com.wunding.learn.course.service.service.ICourseQaQuestionInfoService;
import com.wunding.learn.course.service.service.ICourseRefDifyService;
import com.wunding.learn.course.service.service.ICourseService;
import com.wunding.learn.course.service.service.ICourseShareRecordService;
import com.wunding.learn.course.service.service.ICourseStarService;
import com.wunding.learn.course.service.service.ICourseStudyPlanService;
import com.wunding.learn.course.service.service.ICourseTagService;
import com.wunding.learn.course.service.service.ICourseViewLimitService;
import com.wunding.learn.course.service.service.ICourseVoteService;
import com.wunding.learn.course.service.service.ICourseWithoutLinkProduct;
import com.wunding.learn.course.service.service.ICourseWithoutService;
import com.wunding.learn.course.service.service.ICoursewarePackageMergeRecordService;
import com.wunding.learn.course.service.service.ICoursewareQuestionService;
import com.wunding.learn.course.service.service.ICoursewareService;
import com.wunding.learn.course.service.service.ICoursewareStarService;
import com.wunding.learn.course.service.service.ICoursewareUserRecordService;
import com.wunding.learn.course.service.service.ITagService;
import com.wunding.learn.course.service.service.IUserCourseRecordService;
import com.wunding.learn.exam.api.dto.ViewExamFeignDTO;
import com.wunding.learn.exam.api.query.ExamQuery;
import com.wunding.learn.exam.api.service.AnswerRecordFeign;
import com.wunding.learn.exam.api.service.ExamFeign;
import com.wunding.learn.excitation.api.dto.UserExcitationReduceDTO;
import com.wunding.learn.excitation.api.service.ExcitationFeign;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.constant.FileBizType;
import com.wunding.learn.file.api.constant.ImageBizType;
import com.wunding.learn.file.api.dto.AbstractExportDataDTO;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.file.api.dto.NamePath;
import com.wunding.learn.file.api.dto.SaveFileDTO;
import com.wunding.learn.file.api.service.FileFeign;
import com.wunding.learn.flowable.api.constant.ProcessRedisKeyEnum;
import com.wunding.learn.flowable.api.dto.CompleteResultDTO;
import com.wunding.learn.flowable.api.dto.ProcessDefinitionFeignDTO;
import com.wunding.learn.flowable.api.dto.ProcessDefinitionTaskDTO;
import com.wunding.learn.flowable.api.dto.StartProcessInstanceDTO;
import com.wunding.learn.flowable.api.feign.ProcessFeign;
import com.wunding.learn.flowable.api.model.ProcessInstanceResource;
import com.wunding.learn.flowable.api.model.ProcessInstanceTaskResource;
import com.wunding.learn.flowable.api.service.IProcessInstanceResourceService;
import com.wunding.learn.flowable.api.service.IProcessInstanceTaskResourceService;
import com.wunding.learn.lecturer.api.dto.LecturerCourseAuthFeignDTO;
import com.wunding.learn.lecturer.api.dto.LecturerDetailDTO;
import com.wunding.learn.lecturer.api.service.LecturerExaminationFeign;
import com.wunding.learn.lecturer.api.service.LecturerFeign;
import com.wunding.learn.operation.api.service.LayoutFeign;
import com.wunding.learn.push.api.dto.PushAttributeDTO;
import com.wunding.learn.push.api.dto.PushNoticeSetDTO;
import com.wunding.learn.push.api.dto.PushResourceDTO;
import com.wunding.learn.push.api.dto.SendPushDTO;
import com.wunding.learn.push.api.service.PushFeign;
import com.wunding.learn.user.api.dto.AiBaseConfigDTO;
import com.wunding.learn.user.api.dto.IdentityDTO;
import com.wunding.learn.user.api.dto.OrgDTO;
import com.wunding.learn.user.api.dto.OrgShowDTO;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.dto.UserIdentityFeignDTO;
import com.wunding.learn.user.api.dto.UserOrgDTO;
import com.wunding.learn.user.api.enums.IdentityCategoryEnum;
import com.wunding.learn.user.api.query.ProgrammedIdQuery;
import com.wunding.learn.user.api.service.AiBaseConfigFeign;
import com.wunding.learn.user.api.service.CategoryFeign;
import com.wunding.learn.user.api.service.IdentityFeign;
import com.wunding.learn.user.api.service.MemberCardFeign;
import com.wunding.learn.user.api.service.OrgFeign;
import com.wunding.learn.user.api.service.ParaFeign;
import com.wunding.learn.user.api.service.RouterFeign;
import com.wunding.learn.user.api.service.SysTagFeign;
import com.wunding.learn.user.api.service.UserFeign;
import com.wunding.learn.user.api.service.UserIdentityFeign;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

/**
 * <p> 课程表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">余学益</a>
 * @date 2022-05-10
 */
@Slf4j
@Service("courseService")
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class CourseServiceImpl extends BaseServiceImpl<CourseMapper, Course> implements ICourseService,
    CommandLineRunner {

    public static final int FIVE_STARS = 5;
    public static final int ONE_STARS = 1;
    private static final String TAG_ID_SEPARATOR = ",";
    private static final String COURSE_ORDER_FAVORITE_NUMBER = "voteNumber";
    private static final String COURSE_ORDER_PUBLISH_TIME = "publish_time";
    private static final String LIKE = "like";
    private static final String VIEW = "view";
    private static final String TIME_DOWN = "timeDown";
    private static final String TIME_UP = "timeUp";
    private static final String CLICK_NUMBER = "clickNumber";
    private static final String DESC = " desc";
    private static final String ASC = " asc";
    private static final String COURSE_TYPE_E = "E";
    private static final String COURSE_TYPE_M = "M";
    private static final String COURSE_IMG_ICON = "CourseImgIcon";
    private static final String POSITION_CATE = "PositionCate";
    private static final String TYPE_COURSE = "course";
    private static final String TYPE_COURSEWARE = "courseware";

    private static final String COURSE_SERVICE = "courseService";

    private static final String LEARN_STATE = "learnState";

    private static final String UPLOAD_TYPE = "uploadType";

    private static final String PASS_STATE = "passState";
    private static final String REGEX = "(^/)|(/$)";

    private static final int ONE_HUNDRED = 100;

    @Resource
    private RouterFeign routerFeign;

    @Resource(name = "courseDao")
    private CourseDao courseDao;
    @Resource
    private ITagService tagService;
    @Resource
    private OrgFeign orgFeign;
    @Resource
    private UserFeign userFeign;
    @Resource
    private CommentFeign commentFeign;
    @Resource
    private CoursewareMapper coursewareMapper;
    @Resource
    private ICourseTagService courseTagService;
    @Resource
    private ICourseChapterService courseChapterService;
    @Resource
    private UserCourseRecordMapper userCourseRecordMapper;
    @Resource
    private CourseViewLimitComponent courseViewLimitComponent;
    @Resource
    private BaseLibraryRecordService baseLibraryRecordService;
    @Resource
    private CoursewareLibViewLimitComponent coursewareLibViewLimitComponent;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private IUserCourseRecordService userCourseRecordService;
    @Resource
    private FileFeign fileFeign;
    @Resource
    private IdentityFeign identityFeign;
    @Resource
    private CourseFavorateMapper courseFavorateMapper;
    @Resource
    private CourseTagMapper courseTagMapper;
    @Resource
    private TagMapper tagMapper;
    @Resource
    private ICourseCategoryService courseCategoryService;
    @Resource
    private ExamFeign examFeign;
    @Resource
    private AnswerRecordFeign answerRecordFeign;
    @Resource
    private ICoursewareUserRecordService coursewareUserRecordService;
    @Resource
    private MqProducer mqProducer;
    @Resource
    @Lazy
    private LecturerExaminationFeign lecturerExaminationFeign;
    @Resource
    private ICoursewareStarService coursewareStarService;
    @Resource
    private ICourseVoteService courseVoteService;
    @Resource
    private ICourseStarService courseStarService;
    @Resource
    private ICoursewareService coursewareService;
    @Resource
    private CourseViewMapper courseViewMapper;
    @Resource
    private CourseMapper courseMapper;
    @Resource
    private CourseCategoryMapper courseCategoryMapper;
    @Resource
    private CoursewareContentFactory coursewareContentFactory;
    @Resource
    private ICategorysService categorysService;
    @Resource
    private ExportComponent exportComponent;
    @Resource
    private ParaFeign paraFeign;
    @Resource
    private PushFeign pushFeign;
    @Resource
    private ICourseKeywordService courseKeywordService;
    @Resource
    private IDirectoryService directoryService;
    @Resource
    private CategoryFeign categoryFeign;
    @Lazy
    @Resource
    private ExcitationFeign excitationFeign;
    @Resource
    private LayoutFeign layoutFeign;
    @Resource
    @Lazy
    private ApplyFeign applyFeign;
    @Resource
    private ICourseNoteService courseNoteService;
    @Resource
    @Lazy
    private ICoursewareQuestionService coursewareQuestionService;

    @Resource
    private CoursewarePackageMapper coursewarePackageMapper;

    @Resource
    private ICourseStudyPlanService courseStudyPlanService;

    @Resource
    private IMultiLangMessageService multiLangMessageService;

    @Resource
    private IResourceViewLimitService resourceViewLimitService;

    @Resource
    private AiBaseConfigFeign aiBaseConfigFeign;

    @Resource
    private ICourseWithoutService courseWithoutService;

    @Resource
    private CourseWithoutLinkFactory courseWithoutLinkFactory;

    @Resource
    private MemberCardFeign memberCardFeign;

    @Resource
    private UserIdentityFeign userIdentityFeign;

    @Resource
    private ICourseViewLimitService courseViewLimitService;

    @Resource
    private ICoursewarePackageMergeRecordService coursewarePackageMergeRecordService;

    @Resource
    @Lazy
    private LecturerFeign lecturerFeign;

    @Resource
    private IPosterShareService posterShareService;

    @Resource
    private ICourseShareRecordService courseShareRecordService;

    @Resource
    private ICourseFavorateService courseFavorateService;

    private final TransactionTemplate transactionTemplate;

    @Resource
    private ICourseQaQuestionInfoService courseQaQuestionInfoService;

    @Resource
    private ICourseRefDifyService courseRefDifyService;

    @Resource
    private DifyApiClient difyApiClient;

    @Resource
    @Lazy
    private ProcessFeign processFeign;

    @Resource
    private IProcessInstanceResourceService processInstanceResourceService;
    @Resource
    private IProcessInstanceTaskResourceService processInstanceTaskResourceService;
    @Resource
    private SysTagFeign sysTagFeign;

    @Override
    public void run(String... args) throws Exception {
        for (Object tenantId : redisTemplate.opsForHash().keys(TenantRedisKeyConstant.DB_KEY)) {
            String tid = ((String) tenantId).replace(DynamicDataSourceConstant.DB_LOOK_UP_KEY_PREFIX, "");
            UserThreadContext.setTenantId(tid);
            initCourseNo();
        }
        UserThreadContext.remove();
    }

    //@PostConstruct
    private void initCourseNo() {
        // 默认的起始课程编号
        long defaultCourseNumber = 1000000L;

        QueryWrapper<Course> query = new QueryWrapper<>();
        query.select(" concat( 'C', max(  cast(  replace(course_no,'C','') as SIGNED     ) ))  course_no");
        // 应对赠送课程，兼容WD前缀，本地租户依然使用C开头的编号自增
        query.likeRight("course_no", "C");
        Course course = baseMapper.selectOne(query);

        long currentCourseNumber = defaultCourseNumber;

        if (course != null && StringUtils.isNotBlank(course.getCourseNo())) {
            String oldNo = course.getCourseNo();

            try {
                if (oldNo != null && !"null".equals(oldNo.substring(1))) {
                    currentCourseNumber = Long.parseLong(oldNo.substring(1));
                }
            } catch (NumberFormatException e) {
                log.error("类型转换异常", e);
                currentCourseNumber = defaultCourseNumber;
            }
        }

        // 获取Redis中存储的课程编号
        Long redisNumber = null;
        Object o = redisTemplate.opsForValue().get(CourseRedisKeyEnum.COURSE_CODE_NUM.getKey());

        if (o instanceof Integer) {
            redisNumber = ((Integer) o).longValue();
        } else if (o instanceof Long) {
            redisNumber = (Long) o;
        }

        // 如果Redis中存储的课程编号为空或小于当前的课程编号，则更新Redis中的课程编号
        if (redisNumber == null || redisNumber < currentCourseNumber) {
            redisTemplate.opsForValue().set(CourseRedisKeyEnum.COURSE_CODE_NUM.getKey(), currentCourseNumber);
        }
    }

    private void initProcessCode() {
        // 查询当前最大的审批流程编号
        String processCodeMax = processFeign.getProcessCodeMax();
        long currNum = 1000000L;
        if (StringUtils.isEmpty(processCodeMax)) {
            redisTemplate.opsForValue().set(ProcessRedisKeyEnum.PROCESS_CODE_NUM.getKey(), currNum);
        } else {
            String oldNo = processCodeMax;
            Long redisNum = null;
            try {
                currNum = Long.parseLong(oldNo.substring(1));
                Object v = redisTemplate.opsForValue().get(ProcessRedisKeyEnum.PROCESS_CODE_NUM.getKey());
                if (v instanceof Integer) {
                    redisNum = ((Integer) v).longValue();
                } else if (v instanceof Long) {
                    redisNum = (Long) v;
                }
            } catch (NumberFormatException e) {
                log.error("发生异常", e);
                currNum = 1000000L;
            }
            if (redisNum == null || redisNum < currNum) {
                redisTemplate.opsForValue().set(ProcessRedisKeyEnum.PROCESS_CODE_NUM.getKey(), currNum);
            }
        }
    }

    private String generateCode() {
        Object v = redisTemplate.opsForValue().get(CourseRedisKeyEnum.COURSE_CODE_NUM.getKey());
        if (v == null) {
            initCourseNo();
        }
        return "C" + redisTemplate.opsForValue().increment(CourseRedisKeyEnum.COURSE_CODE_NUM.getKey(), 1);
    }

    /**
     * 处理课程分类名称
     *
     * @param defaultCategoryName 默认课程分类名称
     * @param defaultCategoryId   默认课程分类id
     * @param multiLangMessageMap 课程分类多语言map
     * @return
     */
    private String dealCourseCategoryName(String defaultCategoryName, String defaultCategoryId,
        Map<String, List<MultiLangMessage>> multiLangMessageMap) {
        if (StringUtils.isBlank(defaultCategoryName) || StringUtils.isBlank(defaultCategoryId)) {
            return StringUtils.EMPTY;
        }
        String language = UserThreadContext.getAcceptLanguage();
        if (!CollectionUtils.isEmpty(multiLangMessageMap.get(defaultCategoryId))) {
            List<MultiLangMessageDTO> multiLangMessageDTOS = BeanListUtils.copyList(
                multiLangMessageMap.get(defaultCategoryId), MultiLangMessageDTO.class);
            Optional<MultiLangMessageDTO> optional = multiLangMessageDTOS.stream()
                .filter(m -> m.getLang().equals(language)).findFirst();
            if (optional.isPresent() && StringUtils.isNotBlank(optional.get().getContent())) {
                return optional.get().getContent();
            }
        }
        return defaultCategoryName;
    }

    @Override
    public PageInfo<CourseListDTO> findCourseListByPage(CourseQuery courseQuery) {
        //是否查询所有数据(不校验任何下发，管辖权限)
        if (null == courseQuery.getIsAll() || !courseQuery.getIsAll()) {
            Set<String> managerAreaOrgIds = orgFeign.findUserManageAreaLevelPath(UserThreadContext.getUserId());
            courseQuery.setManagerAreaOrgIds(managerAreaOrgIds);
            courseQuery.setCurrentOrgId(UserThreadContext.getOrgId());
            courseQuery.setCurrentUserId(UserThreadContext.getUserId());

            if (StringUtils.isNotEmpty(courseQuery.getCreateOrgId())) {
                // 查询用户服务查询该组织及其以下所有的组织id
                Set<String> childrenId = orgFeign.getChildrenId(courseQuery.getCreateOrgId());
                courseQuery.setCreateAndUnderOrgIds(childrenId);
            }
        }

        PageInfo<CourseListDTO> objectPageInfo = PageMethod.startPage(courseQuery.getPageNo(),
            courseQuery.getPageSize()).doSelectPageInfo(() -> baseMapper.findCourseListByPage(courseQuery));

        Set<String> userIds = new HashSet<>();
        List<CourseListDTO> list = objectPageInfo.getList();
        if (CollectionUtils.isEmpty(list)) {
            return objectPageInfo;
        }
        list.forEach(courseInfo -> {
            userIds.add(courseInfo.getCreateBy());
            userIds.add(courseInfo.getPublishBy());
        });
        Map<String, UserOrgDTO> userMap = orgFeign.getUserAndOrgMapByUserIds(userIds);
        List<String> courseIds = list.stream().map(CourseListDTO::getId)
            .collect(Collectors.toList());
        Map<String, List<KeyWordResultDTO>> keywordMap = courseTagService.getKeywordMapByCourseIds(courseIds, 3);
        Map<String, OrgShowDTO> orgShowDTOMap = orgFeign.getOrgShowDTO(userFeign.getUserOrdIdByUserIds(userIds));
        Map<String, List<KeyWordResultDTO>> finalKeywordMap = keywordMap;
        Map<String, List<MultiLangMessage>> messageMap = multiLangMessageService.getMultiLangMessageMap(
            list.stream().map(CourseListDTO::getCourseCateId).collect(Collectors.toList()),
            CourseConstant.COURSE_CATEGORY);

        List<String> refDifyCourseIdList = getRefDifyCourseIdList(courseIds);
        list.forEach(courseListDTO -> {

            //课程分类名称
            courseListDTO.setCourseCateName(
                dealCourseCategoryName(courseListDTO.getCourseCateName(), courseListDTO.getCourseCateId(), messageMap));

            if (StringUtils.isNotEmpty(courseListDTO.getOrgId())) {
                //查询创建组织名称
                Optional.ofNullable(orgShowDTOMap.get(courseListDTO.getOrgId())).ifPresent(orgShowDTO -> {
                    courseListDTO.setOrgName(orgShowDTO.getOrgShortName());
                    courseListDTO.setOrgPath(orgShowDTO.getLevelPathName());
                });
            }

            //查询浏览人数
            LambdaQueryWrapper<UserCourseRecord> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(UserCourseRecord::getCourseId, courseListDTO.getId());
            courseListDTO.setViewCount(userCourseRecordMapper.selectCount(queryWrapper));

            //课件数
            LambdaQueryWrapper<Courseware> countCourseware = new LambdaQueryWrapper<>();
            countCourseware.eq(Courseware::getCourseId, courseListDTO.getId());
            countCourseware.eq(Courseware::getIsAvailable, AvailableEnum.AVAILABLE.getValue());
            courseListDTO.setCourseWareNum(coursewareMapper.selectCount(countCourseware));

            //发布者和添加者
            Optional.ofNullable(userMap.get(courseListDTO.getPublishBy())).ifPresent(userInfo -> {
                courseListDTO.setPublishLoginName(userInfo.getLoginName());
                courseListDTO.setPublishName(userInfo.getFullName());
            });
            Optional.ofNullable(userMap.get(courseListDTO.getCreateBy())).ifPresent(userInfo -> {
                courseListDTO.setCreateLoginName(userInfo.getLoginName());
                courseListDTO.setCreateName(userInfo.getFullName());
            });
            List<KeyWordResultDTO> keywordList = finalKeywordMap.get(courseListDTO.getId());
            if (!CollectionUtils.isEmpty(keywordList)) {
                courseListDTO.setKeyword(keywordList);
                // 关键词
                List<String> words = keywordList.stream().map(KeyWordResultDTO::getWord).collect(Collectors.toList());
                courseListDTO.setKeywordStr(String.join("，", words));
            }

            courseListDTO.setIsQaCourse(refDifyCourseIdList.contains(courseListDTO.getId()) ? 1 : 0);

            courseListDTO.setAuditStatusStr(
                CourseAuditStatusEnum.fromCode(courseListDTO.getAuditStatus()).getDescription());

        });
        //路由访问埋点
        mqProducer.sendMsg(
            new HomeRouterVisitEvent(RouterVisitEnum.CourseManagement.getRouterId(), UserThreadContext.getUserId(),
                RouterVisitEnum.CourseManagement.getName()));

        return objectPageInfo;
    }

    /**
     * 获取引用 aigc 课程的课程id
     *
     * @param courseIds 课程id列表
     * @return 引用 aigc 课程的课程id
     */
    private List<String> getRefDifyCourseIdList(List<String> courseIds) {

        //  获取AI开关
        AiBaseConfigDTO aiConfig = aiBaseConfigFeign.getAiConfig();

        // 判断AI开关开启 且 AI问答开关开启
        if (aiConfig != null && aiConfig.getAiSwitch().equals(GeneralJudgeEnum.CONFIRM.getValue())
            && aiConfig.getAiCourseAsk()
            .equals(GeneralJudgeEnum.CONFIRM.getValue())) {
            List<CourseRefDify> courseRefDifyList = courseRefDifyService.lambdaQuery()
                .in(CourseRefDify::getCourseId, courseIds).list();
            return courseRefDifyList.stream().map(CourseRefDify::getCourseId).toList();
        } else {
            return new ArrayList<>();
        }

    }

    @Override
    public PageInfo<LecturerCourseDetailDTO> findCourseSummaryListByPage(
        CourseSummaryListQuery courseSummaryListQuery) {
        // 查讲师下符合查询条件的课程
        PageInfo<LecturerCourseDetailDTO> coursePageInfo = PageMethod.startPage(courseSummaryListQuery.getPageNo(),
                courseSummaryListQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.findCourseSummaryListByPage(courseSummaryListQuery));

        Set<String> courseIdSet = new HashSet<>();

        coursePageInfo.getList().forEach(courseDTO -> {
            courseIdSet.add(courseDTO.getId());
            //查询浏览人数
            LambdaQueryWrapper<UserCourseRecord> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(UserCourseRecord::getCourseId, courseDTO.getId());
            courseDTO.setLearnCount(userCourseRecordMapper.selectCount(queryWrapper).intValue());

            //课件数
            LambdaQueryWrapper<Courseware> countCourseware = new LambdaQueryWrapper<>();
            countCourseware.eq(Courseware::getCourseId, courseDTO.getId());
            countCourseware.eq(Courseware::getIsAvailable, AvailableEnum.AVAILABLE.getValue());
            courseDTO.setTotalCw(coursewareMapper.selectCount(countCourseware).intValue());
        });

        return coursePageInfo;
    }

    @Override
    public void exportCourseData(CourseSummaryListQuery courseSummaryListQuery) {
        Locale locale = new Locale(
            StringUtils.isNotBlank(UserThreadContext.getAcceptLanguage()) ? UserThreadContext.getAcceptLanguage()
                : "zh");
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<ICourseService, LecturerCourseDetailDTO>(
            courseSummaryListQuery) {

            @Override
            protected ICourseService getBean() {
                return SpringUtil.getBean(COURSE_SERVICE, ICourseService.class);
            }

            @Override
            protected PageInfo<LecturerCourseDetailDTO> getPageInfo() {
                if (StringUtils.isNotBlank(UserThreadContext.getAcceptLanguage())) {
                    LocaleContextHolder.setLocale(locale);
                }
                return getBean().findCourseSummaryListByPage((CourseSummaryListQuery) pageQueryDTO);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.LecturerCourseDetail;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.LecturerCourseDetail.getType();
            }

            @Override
            protected void afterCreateBeanMap(Map<String, Object> map) {
                Object uploadType = map.get(UPLOAD_TYPE);
                if (Objects.equals(uploadType, 1)) {
                    map.put(UPLOAD_TYPE, I18nUtil.getDefaultMessage("课程上传"));
                } else if (Objects.equals(uploadType, 2)) {
                    map.put(UPLOAD_TYPE, I18nUtil.getDefaultMessage("工作台上传"));
                } else {
                    map.put(UPLOAD_TYPE, I18nUtil.getDefaultMessage("项目上传"));
                }
            }

        };
        exportComponent.exportRecord(exportDataDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String saveOrUpdateCourse(SaveCourseDTO saveCourseDTO) {

        if (StringUtils.isNotBlank(saveCourseDTO.getId())) {
            this.updateCourse(saveCourseDTO);
            return saveCourseDTO.getId();
        }
        ICourseService courseService = SpringUtil.getBean(COURSE_SERVICE, ICourseService.class);
        assert courseService != null;
        return courseService.saveCourse(saveCourseDTO);
    }

    /**
     * 新增逻辑
     */

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveCourse(SaveCourseDTO saveCourseDTO) {
        String userId = UserThreadContext.getUserId();
        //当isTrain为空时,添加默认值
        Integer isTrain = Optional.ofNullable(saveCourseDTO.getIsTrain()).orElse(0);
        saveCourseDTO.setIsTrain(isTrain);
        checkNewCourseVisitArea(saveCourseDTO);
        String courseId = newId();
        Course course = new Course();
        saveCourseDTO.setId(courseId);
        BeanUtils.copyProperties(saveCourseDTO, course);

        // 保存图片，拷贝临时目录至正式目录
        saveCourseImage(saveCourseDTO, courseId, course);

        // 验证课程编码
        String courseNo = generateCode();
        verifyAndSetCourseNo(courseNo, course);

        course.setOrgId(UserThreadContext.getOrgId());

        // 校验海报
        posterShareService.checkPosterShare(saveCourseDTO.getPosterShareDTO());

        setPublishForSave(saveCourseDTO, course, userId);

        setCourseUploadWay(saveCourseDTO, course);
        ProcessDefinitionFeignDTO processDefinitionFeignDTO;
        if (!StringUtils.isEmpty(saveCourseDTO.getCourseCateId())) {
            processDefinitionFeignDTO = getProcessDefinitionFeignDTO(saveCourseDTO.getCourseCateId());
        } else {
            processDefinitionFeignDTO = null;
        }

        // 设置审核状态
        setAuditStatusForSave(isTrain, course, processDefinitionFeignDTO, userId);

        // 执行数据新增
        courseDao.saveCourse(course);

        // 保存下发范围
        courseViewLimitComponent.handleNewViewLimit(saveCourseDTO.getProgrammeId(), courseId);

        //课程类型为第三方课程资源时，自动生成默认课件，以供跳转到外部课程
        if (null != course.getSourceType() && Objects.equals(1, course.getSourceType())) {
            initCourseWithoutWare(courseId);
        }

        // 初始化课程资源激励配置
        mqProducer.sendMsg(new ExcitationInitMqEvent(new ResourceConfigInitDTO().setResourceId(courseId)
            .setResourceType(ExcitationEventCategoryEnum.COURSE.getCode())));

        // 保存标签关联关系
        this.saveOrUpdateCourseTag(courseId, saveCourseDTO);

        List<String> positionIds = saveCourseDTO.getPositionIds();
        if (!CollectionUtils.isEmpty(positionIds)) {
            this.saveOrUpdateCourseCategory(courseId, saveCourseDTO.getPositionIds());
        }

        // 不为空时说明是合并课件到课程
        if (StringUtils.isNotEmpty(saveCourseDTO.getCourseWareIdList())) {
            saveCourseDTO.setId(courseId);
            saveMergeCourseWare(saveCourseDTO);
        }

        // 启用才给资源配置推送
        if (GeneralJudgeEnum.CONFIRM.getValue().equals(saveCourseDTO.getIsPublish()) && Optional.ofNullable(
            saveCourseDTO.getPushNoticeSetDTO()).isPresent()) {
            sendPushFeign(saveCourseDTO, 0);
        }

        // 海报分享
        posterShareService.savePosterShare(courseId, ResourceTypeEnum.COURSE, saveCourseDTO.getPosterShareDTO());

        // 课程相关数据变动，同步变动课程数据
        mqProducer.sendMsg(new SynUpdateCopyCourseEvent(courseId, SynUpdateCopyDataEventEnum.COURSE.getKey()));
        handleCategoryCanDel();
        return courseId;
    }

    private ProcessDefinitionFeignDTO getProcessDefinitionFeignDTO(String courseCateId) {
        ProcessDefinitionFeignDTO processDefinitionFeignDTO;
        Categorys category = categorysService.getById(courseCateId);
        String levelPath = category.getLevelPath();
        String[] parts = levelPath.replaceAll(REGEX, "").split("/");
        List<String> categoryIdList = new ArrayList<>();
        for (int i = parts.length - 1; i >= 0; i--) {
            categoryIdList.add(parts[i]);
        }
        processDefinitionFeignDTO = processFeign.getNearestProcessDefinitionByProcessContentId(categoryIdList);
        return processDefinitionFeignDTO;
    }

    private void setPublishForSave(SaveCourseDTO saveCourseDTO, Course course, String userId) {
        // 默认发布时
        Integer isPublish = saveCourseDTO.getIsPublish();

        // 快速培训的默认发布
        if (saveCourseDTO.getIsAutoPublish() != null && saveCourseDTO.getIsAutoPublish() == 1) {
            course.setIsPublish(saveCourseDTO.getIsAutoPublish());
            course.setPublishBy(userId);
            course.setPublishTime(new Date());
        }
        // 开启了配置才能在添加时直接发布
        String paraValue = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_916.getCode());
        if (isPublish != null && isPublish == PublishStatusEnum.IS_PUBLISH.getValue()) {
            if (StringUtils.isNotBlank(paraValue) && GeneralJudgeEnum.CONFIRM.getValue()
                .equals(Integer.valueOf(paraValue))) {
                course.setIsPublish(isPublish);
                course.setPublishBy(userId);
                course.setPublishTime(new Date());
            } else {
                throw new BusinessException(CourseErrorNoEnum.ERR_COURSEWARE_NUM_NUL, null,
                    saveCourseDTO.getCourseName());
            }
        }
    }

    private void setAuditStatusForSave(Integer isTrain, Course course,
        ProcessDefinitionFeignDTO processDefinitionFeignDTO,
        String userId) {
        if (isTrain != 0) {
            course.setAuditStatus(5);
        } else if (processDefinitionFeignDTO == null) {
            course.setAuditStatus(5);
        } else if (!processDefinitionFeignDTO.getProcessScene().contains("insert")) {
            course.setAuditStatus(5);
        } else {
            String exemptRoles = processDefinitionFeignDTO.getExemptRoles();
            List<String> exemptRolesList = JsonUtil.json2List(exemptRoles, String.class);
            List<String> roleIdByUserId = userFeign.getRoleIdByUserId(userId);
            boolean isExemptRoleUser = roleIdByUserId.stream().noneMatch(exemptRolesList::contains);
            if (!isExemptRoleUser) {
                course.setAuditStatus(2);
            } else {
                course.setAuditStatus(0);
                addCourseProcessInstanceResource(course, 0, processDefinitionFeignDTO);
            }
        }
    }

    private CompleteResultDTO startTransferProcess(Course course, ProcessInstanceResource instanceResource) {
        String processCode = instanceResource.getProcessCode();
        String definitionId = instanceResource.getProcessDefinitionId();
        Integer processApplyType = instanceResource.getProcessApplyType();
        //走审核流程
        StartProcessInstanceDTO startProcessInstanceDTO = new StartProcessInstanceDTO();
        startProcessInstanceDTO.setProcessCode(processCode);
        startProcessInstanceDTO.setDefinitionType(ProcessDefinitionTypeEnum.COURSE_AUDIT.getType());
        startProcessInstanceDTO.setResourceId(course.getId());
        startProcessInstanceDTO.setResourceName(course.getCourseName());
        startProcessInstanceDTO.setResourceType(ResourceTypeEnum.COURSE_AUDIT.getType());
        startProcessInstanceDTO.setApplicantUserId(UserThreadContext.getUserId());
        startProcessInstanceDTO.setDefinitionId(definitionId);
        startProcessInstanceDTO.setProcessApplyType(processApplyType);
        return processFeign.startProcessInstance(startProcessInstanceDTO);
    }

    private String generateProcessCode() {
        Object v = redisTemplate.opsForValue().get(ProcessRedisKeyEnum.PROCESS_CODE_NUM.getKey());
        if (v == null) {
            initProcessCode();
        }
        return "P" + redisTemplate.opsForValue().increment(ProcessRedisKeyEnum.PROCESS_CODE_NUM.getKey(), 1);
    }

    private void setCourseUploadWay(SaveCourseDTO saveCourseDTO, Course course) {
        if (saveCourseDTO.getIsTrain() == 0) {
            course.setUploadWay(1);
        } else {
            Integer uploadWay = Optional.ofNullable(saveCourseDTO.getUploadWay()).orElse(3);
            course.setUploadWay(uploadWay);
        }
    }

    private void verifyAndSetCourseNo(String courseNo, Course course) {
        LambdaQueryWrapper<Course> query = new LambdaQueryWrapper<>();
        query.eq(Course::getCourseNo, courseNo);
        List<Course> list = this.getBaseMapper().selectList(query);
        if (CollectionUtils.isEmpty(list)) {
            course.setCourseNo(courseNo);
        } else {
            throw new BusinessException(CourseErrorNoEnum.ERR_CW_CODE_REPEAT, null, courseNo);
        }
    }

    private void saveCourseImage(SaveCourseDTO saveCourseDTO, String courseId, Course course) {
        if (StringUtils.isNotBlank(saveCourseDTO.getCoverImagePath())) {
            SaveFileDTO image = fileFeign.saveImage(courseId, ImageBizType.CourseImgIcon.name(),
                saveCourseDTO.getCoverImageName(), saveCourseDTO.getCoverImagePath());
            if (null != image) {
                course.setCoverImageUrl(image.getPath());
            }
        } else if (saveCourseDTO.getIsUseDefaultImg() != null && saveCourseDTO.getIsUseDefaultImg() == 1) {
            fileFeign.saveDefaultImage(courseId, ImageBizType.CourseImgIcon.name(), "courseDefaultImage.png",
                "/file/quickProjectDefaultImage/courseDefaultImage.png");
        }
    }

    private void checkNewCourseVisitArea(SaveCourseDTO saveCourseDTO) {
        // 当下发范围为不是创建人可见,并且是课程管理时进行鉴权
        if (saveCourseDTO.getViewType() != 1 && saveCourseDTO.getIsTrain() == 0) {
            //对下发范围进行鉴权
            ProgrammedIdQuery programmedIdQuery = new ProgrammedIdQuery();
            programmedIdQuery.setNewProgrammeId(saveCourseDTO.getProgrammeId());
            boolean checkViewLimit = userFeign.checkUserManageArea(programmedIdQuery);
            if (!checkViewLimit) {
                throw new BusinessException(ErrorNoEnum.ERR_VIEW_LIMIT);
            }
        }
    }

    /**
     * 调用推送feign
     *
     * @param saveCourseDTO 保存实时dto
     * @param operateState  操作状态
     */
    private void sendPushFeign(SaveCourseDTO saveCourseDTO, Integer operateState) {
        SendPushDTO sendPushDTO = new SendPushDTO();
        sendPushDTO.setLang(LocaleContextHolder.getLocale().getLanguage());
        sendPushDTO.setServerName(Objects.requireNonNull(WebUtil.getRequest()).getServerName());
        PushNoticeSetDTO pushNoticeSetDTO = saveCourseDTO.getPushNoticeSetDTO();
        sendPushDTO.setPushNoticeSetDTO(pushNoticeSetDTO);

        // 手动推送时采用内容封面图片处理
        if (Objects.equals(pushNoticeSetDTO.getPushMethod(), 1)) {
            pushNoticeSetDTO.getManualPushSet().getCustomPushContents().forEach(customPushContent -> {
                if (Objects.equals(customPushContent.getPushImage(), 1)) {
                    customPushContent.setImagePath(
                        fileFeign.getImageFileNamePath(saveCourseDTO.getId(), ImageBizType.CourseImgIcon.name()));
                }
            });
        }

        // 为空默认是0
        Integer isTrain = Optional.ofNullable(saveCourseDTO.getIsTrain()).orElse(0);
        String resourceName = saveCourseDTO.getCourseName();
        PushResourceDTO pushResourceDTO = new PushResourceDTO().setResourceName(resourceName).setIsTrain(isTrain)
            .setOperateState(operateState).setProgrammeId(saveCourseDTO.getProgrammeId());

        sendPushDTO.setPushResourceDTO(pushResourceDTO);

        PushAttributeDTO pushAttributeDTO = new PushAttributeDTO();

        boolean condition = isTrain != 0;
        if (condition) {
            // 非 0 证明是属于二级内容过来
            String[] name = pushNoticeSetDTO.getResourceName().split(StringPool.UNDERSCORE);
            String[] resourceId = pushNoticeSetDTO.getResourceId().split(StringPool.UNDERSCORE);
            pushAttributeDTO.setName(name[0]);
            pushAttributeDTO.setSecondaryName(name[1]);

            // 此时中 pushNoticeSetDTO 前端只会传 projectId_ 所以拼接好id
            pushResourceDTO.setResourceId(resourceId[0] + StringPool.UNDERSCORE + saveCourseDTO.getId());
        } else {
            pushAttributeDTO.setName(pushNoticeSetDTO.getResourceName());
            pushResourceDTO.setResourceId(saveCourseDTO.getId());
        }

        pushResourceDTO.setResourceName(pushNoticeSetDTO.getResourceName());
        pushResourceDTO.setResourceType(pushNoticeSetDTO.getResourceType());
        pushAttributeDTO.setExistSecondary(condition);
        pushAttributeDTO.setIntro(saveCourseDTO.getDescriptions());

        @SuppressWarnings("unchecked") String systemName = (String) redisTemplate.opsForHash()
            .get(TenantRedisKeyConstant.SYSTEM_NAME_KEY, UserThreadContext.getTenantId());
        pushAttributeDTO.setSystemName(systemName);

        sendPushDTO.setPushAttributeDTO(pushAttributeDTO);

        pushFeign.sendPush(sendPushDTO);
    }

    /**
     * 保存课程岗位关联数据
     */
    private void saveOrUpdateCourseCategory(String courseId, List<String> positionIds) {

        LambdaUpdateWrapper<CourseCategory> lambda = new UpdateWrapper<CourseCategory>().lambda();
        lambda.eq(CourseCategory::getCourseId, courseId);
        lambda.eq(CourseCategory::getCategoryType, POSITION_CATE);
        lambda.notIn(!CollectionUtils.isEmpty(positionIds), CourseCategory::getCategoryId, positionIds);
        courseCategoryService.remove(lambda);

        CourseCategory courseCategory;
        if (!CollectionUtils.isEmpty(positionIds)) {
            for (String positionId : positionIds) {
                LambdaQueryWrapper<CourseCategory> courseCategoryLambdaQueryWrapper = new LambdaQueryWrapper<>();
                courseCategoryLambdaQueryWrapper.eq(CourseCategory::getCourseId, courseId);
                courseCategoryLambdaQueryWrapper.eq(CourseCategory::getCategoryId, positionId);
                courseCategoryLambdaQueryWrapper.eq(CourseCategory::getCategoryType, POSITION_CATE);
                long exist = courseCategoryService.count(courseCategoryLambdaQueryWrapper);
                if (exist != 0) {
                    continue;
                }

                courseCategory = new CourseCategory();
                courseCategory.setId(StringUtil.newId());
                courseCategory.setCourseId(courseId);
                courseCategory.setCategoryId(positionId);
                courseCategory.setCategoryType(POSITION_CATE);
                courseCategoryService.save(courseCategory);
            }
        }
    }

    /**
     * 保存标签关联关系 只能关联叶子标签
     *
     * @param courseId      课程id
     * @param saveCourseDTO 课程
     */
    @Override
    public void saveOrUpdateCourseTag(String courseId, SaveCourseDTO saveCourseDTO) {
        //保存标签关联关系
        List<CourseTag> courseTagArrayList = Lists.newArrayList();

        Set<String> tagIds = saveCourseDTO.getTagIds();
        if (!CollectionUtils.isEmpty(tagIds)) {
            for (String tagId : tagIds) {
                CourseTag courseTag = createCourseTag(courseId, tagId,
                    CourseTagCreateTypeEnum.COURSE_CREATE_ADMIN.getValue());
                Optional.ofNullable(saveCourseDTO.getCreateType())
                    .ifPresent(createType -> courseTag.setCreateType(createType));
                courseTagArrayList.add(courseTag);
            }
            // 保存标签和课程资源的关系
            sysTagFeign.saveSysTagResourceRelation(tagIds, courseId, ResourceTypeEnum.COURSE.getCode());

        }
        if (!CollectionUtils.isEmpty(courseTagArrayList)) {
            log.info("新增标签关联 courseId{} , tagIdList:{}", courseId,
                JsonUtil.objToJson(courseTagArrayList.stream().map(CourseTag::getTagId).collect(Collectors.toList())));
            courseTagService.saveBatch(courseTagArrayList);
        }
    }

    // 创建课程标签对象
    private CourseTag createCourseTag(String courseId, String tagId, Integer createType) {
        CourseTag courseTag = new CourseTag();
        courseTag.setId(newId());
        courseTag.setCourseId(courseId);
        courseTag.setTagId(tagId);
        courseTag.setCreateType(createType);
        return courseTag;
    }

    /**
     * 更新课程标签
     *
     * @param courseId 课程id
     */
    private void updateCourseTags(String courseId, SaveCourseDTO saveCourseDTO) {
        //判断标签是否更新
        LambdaQueryWrapper<CourseTag> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CourseTag::getCourseId, courseId);
        queryWrapper.eq(CourseTag::getCreateType, CourseTagCreateTypeEnum.COURSE_CREATE_ADMIN.getValue());
        List<CourseTag> courseTags = courseTagService.list(queryWrapper);
        Set<String> tagIds = courseTags.stream().map(CourseTag::getTagId).collect(Collectors.toSet());

        //新选择的标签id
        Set<String> newTagIds = new HashSet<>();
        if (null != saveCourseDTO.getTagIds() && !saveCourseDTO.getTagIds().isEmpty()) {
            newTagIds.addAll(saveCourseDTO.getTagIds());
        }

        if (CollectionUtils.isEmpty(newTagIds)) {
            //这里为空说明是前端把标签给删除了，移除旧的未关联数据
            LambdaQueryWrapper<CourseTag> deleteCourseTag = new LambdaQueryWrapper<>();
            deleteCourseTag.eq(CourseTag::getCourseId, courseId);
            courseTagService.remove(deleteCourseTag);
        }
        //课程分类id，因为创建课程分类的时候同时会创建一个课程标签
        String courseCateId = saveCourseDTO.getCourseCateId();
        if (!tagIds.equals(newTagIds) || StringUtils.isNotEmpty(courseCateId)) {
            //这里不一样说明需要变更数据，先移除旧的未关联数据,再进行添加
            LambdaQueryWrapper<CourseTag> deleteCourseTag = new LambdaQueryWrapper<>();
            deleteCourseTag.eq(CourseTag::getCourseId, courseId);
            courseTagService.remove(deleteCourseTag);
            //传入的标签与库里的标签不同,或者课程分类不为空
            this.saveOrUpdateCourseTag(courseId, saveCourseDTO);
        }

    }

    /**
     * 更新逻辑
     */
    @Override
    @SuppressWarnings("java:S3776")
    public void updateCourse(SaveCourseDTO updateCourse) {
        //更新课程不会传入isTrain参数,需要手动获取
        updateCourse.setIsTrain(baseMapper.selectById(updateCourse.getId()).getIsTrain());
        // 当下发范围为仅创建人可见,不用鉴权
        checkCourseVisitArea(updateCourse);
        String courseId = updateCourse.getId();
        Course existCourse = baseMapper.selectById(courseId);
        if (existCourse.getAuditStatus() == 3) {
            throw new BusinessException(CourseErrorNoEnum.ERR_COURSE_AUDIT_REJECT_FINISH);
        }
        ApiAssert.notNullResource(existCourse);
        // 校验海报分享
        posterShareService.checkPosterShare(updateCourse.getPosterShareDTO());

        //注意更新图片
        Course course = new Course();
        BeanUtils.copyProperties(updateCourse, course);

        //设置封面图片
        setCoverImageUrl(updateCourse, course, courseId);

        //更新标签关联关系
        this.updateCourseTags(courseId, updateCourse);

        //更新课程岗位关联关系
        this.saveOrUpdateCourseCategory(courseId, updateCourse.getPositionIds());

        //更新课件关键词
        courseKeywordService.updateKeywordScore(courseId);
        Integer isPublish = updateCourse.getIsPublish();

        checkPublish(updateCourse, isPublish);

        // 谁能分得清这courseCateId和courseCategoryId的区别啊，这命名无敌了
        if (isNeedToReAudit(existCourse.getCourseCateId(), updateCourse.getCourseCateId())) {

            Categorys category = categorysService.getById(updateCourse.getCourseCateId());
            String levelPath = category.getLevelPath();
            String[] parts = levelPath.replaceAll(REGEX, "").split("/");
            List<String> categoryIdList = new ArrayList<>();
            for (int i = parts.length - 1; i >= 0; i--) {
                categoryIdList.add(parts[i]);
            }
            ProcessDefinitionFeignDTO processDefinitionFeignDTO = processFeign.getNearestProcessDefinitionByProcessContentId(
                categoryIdList);
            if (Objects.nonNull(processDefinitionFeignDTO)) {
                if (processDefinitionFeignDTO.getProcessScene().contains("update")) {
                    course.setIsPublish(0);
                    course.setAuditStatus(0);
                    processInstanceResourceService.remove(
                        new LambdaQueryWrapper<ProcessInstanceResource>().eq(ProcessInstanceResource::getResourceId,
                            courseId));
                    processFeign.deleteProcessInstanceByResourceId(courseId);
                    addCourseProcessInstanceResource(course, 1, processDefinitionFeignDTO);
                } else {
                    processInstanceResourceService.remove(
                        new LambdaQueryWrapper<ProcessInstanceResource>().eq(ProcessInstanceResource::getResourceId,
                            courseId));
                    processFeign.deleteProcessInstanceByResourceId(courseId);
                    course.setAuditStatus(5);
                }
            }
            // 变更的分类不存在审核时，课程审核设置为不涉及
            if (processDefinitionFeignDTO == null) {
                processInstanceResourceService.remove(
                    new LambdaQueryWrapper<ProcessInstanceResource>().eq(ProcessInstanceResource::getResourceId,
                        courseId));
                processFeign.deleteProcessInstanceByResourceId(courseId);
                course.setAuditStatus(5);
            }
        }

        //更新课程信息
        course.setId(courseId);
        course.setIsPublic(isPublish);
        course.setAutoPublishTime(updateCourse.getAutoPublishTime());
        courseDao.updateCourseOfWrapper(course);

        //更新下发范围
        courseViewLimitComponent.handleNewViewLimit(updateCourse.getProgrammeId(), courseId);

        // 更新头条下发范围
        mqProducer.sendMsg(new FirstInfoViewLimitChangeEvent(courseId, FirstInfoContentEnum.course.name(),
            updateCourse.getProgrammeId()));

        // 发送资源操作事件消息
        mqProducer.sendMsg(new ResourceOperateEvent(
            isPublish == PublishStatusEnum.IS_PUBLISH.getValue() ? OperationEnum.PUBLISH : OperationEnum.PUBLISH_CANCEL,
            PushType.COURSE.getKey(), courseId));

        Course resource = getById(course.getId());
        if (resource != null) {
            // 更新资源评论状态
            List<ResourceStatusDTO> list = new ArrayList<>();
            ResourceStatusDTO dto = new ResourceStatusDTO();
            BeanUtils.copyProperties(resource, dto);
            dto.setResourceId(resource.getId());
            dto.setResourceType(ResourceChangeEnum.course.getValue());
            list.add(dto);
            // 发送资源修改信息
            ResourceChangeEvent event = new ResourceChangeEvent(null, null, null, null);
            event.setResourceStatusList(list);
            mqProducer.sendMsg(event);
        }

        // 与添加逻辑类似
        if (GeneralJudgeEnum.CONFIRM.getValue().equals(updateCourse.getIsPublish()) && Optional.ofNullable(
            updateCourse.getPushNoticeSetDTO()).isPresent()) {
            sendPushFeign(updateCourse, 1);
        }

        // 课程相关数据变动，同步变动课程数据
        mqProducer.sendMsg(new SynUpdateCopyCourseEvent(courseId, SynUpdateCopyDataEventEnum.COURSE.getKey()));

        // 课程类别(培训分类)是否发生变更，同步变更授课信息冗余属性(已审核不变更)
        if (!Objects.equals(existCourse.getCourseCategoryId(), updateCourse.getCourseCategoryId())) {
            mqProducer.sendMsg(
                new CourseTrainCategoryChangeEvent(updateCourse.getId(), updateCourse.getCourseCategoryId()));
        }
        if (!Objects.equals(existCourse.getCourseCateId(), updateCourse.getCourseCateId())) {
            mqProducer.sendMsg(new CourseCategoryChangeEvent(updateCourse.getId(), updateCourse.getCourseCateId()));
        }
        mqProducer.sendMsg(new ResourceOperateEvent(OperationEnum.UPDATE, PushType.COURSE.getKey(), courseId));
        handleCategoryCanDel();

        //如果开启了审核，有学员已经提交了审核，
        //再关闭笔记审核的话，后续笔记不需要审
        //核，已经进入审核状态的全部通过。

        courseNoteService.updateCourseNoteStatus(courseId, updateCourse.getIsAuditNote());
        // 更新海报分享
        posterShareService.updatePosterShare(courseId, ResourceTypeEnum.COURSE, updateCourse.getPosterShareDTO());

    }

    private void setCoverImageUrl(SaveCourseDTO updateCourse, Course course, String courseId) {
        course.setCoverImageUrl(null);

        // 默认每次图片都做了修改
        fileFeign.deleteImageByBizIdAndBizType(courseId, ImageBizType.CourseImgIcon.name());
        if (StringUtils.isNotBlank(updateCourse.getCoverImagePath())) {
            // 保存图片，拷贝临时目录至正式目录
            SaveFileDTO saveFileDTO = fileFeign.saveImage(courseId, ImageBizType.CourseImgIcon.name(),
                StringUtils.isNotBlank(updateCourse.getCoverImageName()) ? updateCourse.getCoverImageName() : courseId,
                updateCourse.getCoverImagePath());
            if (null != saveFileDTO) {
                course.setCoverImageUrl(saveFileDTO.getPath());
            }
        } else if (updateCourse.getIsUseDefaultImg() != null && updateCourse.getIsUseDefaultImg() == 1) {
            fileFeign.saveDefaultImage(courseId, ImageBizType.CourseImgIcon.name(), "courseDefaultImage.png",
                "/file/quickProjectDefaultImage/courseDefaultImage.png");
        } else {
            course.setCoverImageUrl(StringUtils.EMPTY);
        }
    }

    private boolean isNeedToReAudit(String oldCategoryId, String thisCategoryId) {
        // 分类不修改时，前端不传分类id过来
        if (StringUtils.isEmpty(thisCategoryId) || oldCategoryId.equals(thisCategoryId)) {
            return false;
        }
        // true-需要重新审核
        return true;
    }

    private void checkPublish(SaveCourseDTO updateCourse, Integer isPublish) {
        //发布时需校验
        // 开启了配置才能在添加时直接发布
        String paraValue = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_916.getCode());
        if (StringUtils.isNotBlank(paraValue) && GeneralJudgeEnum.NEGATIVE.getValue().equals(Integer.valueOf(paraValue))
            && PublishStatusEnum.IS_PUBLISH.getValue() == isPublish) {
            List<String> ids = new ArrayList<>();
            ids.add(updateCourse.getId());
            List<CourseListDTO> result = baseMapper.getCWNumByCourseIds(ids);
            result.stream().filter(dto -> dto.getCourseWareNum() == 0).findFirst().ifPresent(dto -> {
                throw new BusinessException(CourseErrorNoEnum.ERR_COURSEWARE_NUM_NUL, null, dto.getCourseName());
            });
        }
    }

    private void checkCourseVisitArea(SaveCourseDTO updateCourse) {
        if (updateCourse.getViewType() != 1 && updateCourse.getIsTrain() == 0) {
            //查询课程的历史下发方案
            Long programmeId = resourceViewLimitService.getViewLimitIdByResourceId(updateCourse.getId());
            //下发范围方案调整时,对调整的下发范围进行鉴权
            if (!programmeId.equals(updateCourse.getProgrammeId())) {
                ProgrammedIdQuery programmedIdQuery = new ProgrammedIdQuery();
                programmedIdQuery.setNewProgrammeId(updateCourse.getProgrammeId());
                programmedIdQuery.setOldProgrammeId(programmeId);
                boolean checkViewLimit = userFeign.checkUserManageArea(programmedIdQuery);
                if (!checkViewLimit) {
                    throw new BusinessException(ErrorNoEnum.ERR_VIEW_LIMIT);
                }
            }
        }
    }

    private void handleCategoryCanDel() {
        List<Course> courseList = list();
        if (CollectionUtils.isEmpty(courseList)) {
            // 取消课程类别
            // categoryFeign.deleteCategoryByType(CategoryTypeEnum.CourseCategory.getType())

            // 维护课程分类是否可删除 - 未使用，均可删除
            handelCourseCategoryCanDel(new ArrayList<>());
            return;
        }
        List<String> collect = courseList.stream().map(Course::getCourseCategoryId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            // 取消课程类别
            //categoryFeign.deleteCategoryByType(CategoryTypeEnum.CourseCategory.getType())
            return;
        }
        // 取消课程类别
        // categoryFeign.updateCategoryByListCanDel(collect, CategoryTypeEnum.CourseCategory.getType())

        // 维护课程分类是否可删除 - 根据使用情况进行处理
        List<String> cateList = courseList.stream().map(Course::getCourseCateId).distinct()
            .collect(Collectors.toList());
        handelCourseCategoryCanDel(cateList);
    }

    // 维护课程分类是否可删除
    private void handelCourseCategoryCanDel(List<String> usingCateIds) {

        // 无课程，均可删除
        if (CollectionUtils.isEmpty(usingCateIds)) {
            LambdaUpdateWrapper<Categorys> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(Categorys::getIsCanDelete, GeneralJudgeEnum.CONFIRM.getValue());
            categorysService.update(updateWrapper);
        } else {
            // 获取所有未删除课程分类 仅处理内部课程分类
            List<Categorys> list = categorysService.list(
                new LambdaQueryWrapper<Categorys>()
                    .eq(Categorys::getCategoryType, CategoryType.CourseCate.name()));
            if (!CollectionUtils.isEmpty(list)) {
                list.forEach(cate -> cate.setIsCanDelete(
                    usingCateIds.contains(cate.getId())
                        // 已使用，不可删除
                        ? GeneralJudgeEnum.NEGATIVE.getValue()
                        // 未使用，可以删除
                        : GeneralJudgeEnum.CONFIRM.getValue()));
                categorysService.updateBatchById(list);
            }
        }
    }

    @Override
    public CourseDetailDTO getCourseById(String id) {
        Course selectById = Optional.ofNullable(baseMapper.selectById(id))
            .orElseThrow(() -> new BusinessException(CourseErrorNoEnum.ERR_COURSE_NULL));

        CourseDetailDTO courseDetailDTO = new CourseDetailDTO();
        BeanUtils.copyProperties(selectById, courseDetailDTO);

        courseDetailDTO.setCreateOrgId(selectById.getOrgId());
        Optional.ofNullable(orgFeign.getById(selectById.getOrgId())).ifPresent(orgFeignById -> {
            courseDetailDTO.setCreateOrgName(orgFeignById.getOrgName());
            courseDetailDTO.setOrgName(orgFeignById.getOrgName());
        });

        LambdaQueryWrapper<Categorys> categoryQueryWrapper = new LambdaQueryWrapper<>();
        categoryQueryWrapper.eq(Categorys::getId, courseDetailDTO.getCourseCateId());
        Categorys category = Optional.ofNullable(categorysService.getOne(categoryQueryWrapper))
            .orElseGet(() -> new Categorys().setCategoryName(StringUtils.EMPTY).setId(StringUtils.EMPTY));
        courseDetailDTO.setCourseCateName(category.getCategoryName());
        Map<String, List<MultiLangMessage>> multiLangMessageMap = multiLangMessageService.getMultiLangMessageMap(
            List.of(courseDetailDTO.getCourseCateId()), CourseConstant.COURSE_CATEGORY);
        courseDetailDTO.setCourseCateName(
            dealCourseCategoryName(category.getCategoryName(), courseDetailDTO.getCourseCateId(), multiLangMessageMap));
        courseDetailDTO.setCourseCateId(category.getId());
        courseDetailDTO.setCourseCateLevelPath(category.getLevelPath());
        courseDetailDTO.setCourseChapterDTOList(
            courseChapterService.getCourseChapterByCourseId(courseDetailDTO.getId()));

        dealWithPosition(id, courseDetailDTO);
        dealWithTag(id, courseDetailDTO);

        // 设置下发范围
        courseDetailDTO.setLimit(courseViewLimitComponent.getViewLimitBaseInfo(id));

        Optional.ofNullable(fileFeign.getImageFileNamePath(courseDetailDTO.getId(), ImageBizType.CourseImgIcon.name()))
            .ifPresent(img -> {
                courseDetailDTO.setCoverImagePath(img.getPath());
                courseDetailDTO.setCoverImageUrl(img.getUrl());
            });

        courseDetailDTO.setDownloadOrgId(selectById.getDownloadOrgId());
        Optional.ofNullable(orgFeign.getById(selectById.getDownloadOrgId()))
            .ifPresent(downloadOrg -> courseDetailDTO.setDownloadOrgName(downloadOrg.getOrgName()));

        dealWithCourseCategory(selectById, courseDetailDTO);
        //获取第三方课程资源名称
        if (StringUtils.isNotEmpty(courseDetailDTO.getSourceId())) {
            CourseWithout courseWithout = courseWithoutService.getById(courseDetailDTO.getSourceId());
            if (null != courseWithout) {
                courseDetailDTO.setSourceName(courseWithout.getCourseName());
            }
        }

        LambdaQueryWrapper<Courseware> coursewareLambdaQueryWrapper = new LambdaQueryWrapper<>();
        coursewareLambdaQueryWrapper.eq(Courseware::getCourseId, id);
        courseDetailDTO.setCoursewareCount(coursewareService.count(coursewareLambdaQueryWrapper));

        CourseRefDify courseRefDify = courseRefDifyService.lambdaQuery().eq(CourseRefDify::getCourseId, id).one();
        if (courseRefDify != null) {
            courseDetailDTO.setChatUrl(difyApiClient.getChatUrl(courseRefDify.getDifyAppId()));
        }
        return courseDetailDTO;
    }

    private void dealWithCourseCategory(Course selectById, CourseDetailDTO courseDetailDTO) {
        //填充 课程类别名称
        if (StringUtils.isNotBlank(selectById.getCourseCateId())) {
            Categorys categorys = categorysService.getById(selectById.getCourseCateId());
            courseDetailDTO.setCourseCategoryName(categorys.getCategoryName());
            courseDetailDTO.setCourseCategoryLevelPathName("/全部分类"
                + generateLevelPath(categorys.getParentId()) + "/" + categorys.getCategoryName());
        }
        LambdaQueryWrapper<Courseware> coursewareLambdaQueryWrapper = new LambdaQueryWrapper<>();
        coursewareLambdaQueryWrapper.eq(Courseware::getCourseId, courseDetailDTO.getId());
        courseDetailDTO.setCoursewareCount(coursewareService.count(coursewareLambdaQueryWrapper));
        // 海报分享信息
        courseDetailDTO.setPosterShareIsOpen(posterShareService.isOpenShare());
        courseDetailDTO.setPosterShareDTO(posterShareService
            .getPosterShare(courseDetailDTO.getId(), ResourceTypeEnum.COURSE));
    }

    private String generateLevelPath(String parentCateId) {
        if (StringUtils.isNotBlank(parentCateId)) {
            Categorys categorys = categorysService.getById(parentCateId);
            return generateLevelPath(categorys.getParentId()) + "/" + categorys.getCategoryName();
        }
        return "";
    }

    private void dealWithTag(String id, CourseDetailDTO courseDetailDTO) {
        LambdaQueryWrapper<CourseTag> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CourseTag::getCourseId, id);
        queryWrapper.eq(CourseTag::getCreateType, CourseTagCreateTypeEnum.COURSE_CREATE_ADMIN.getValue());
        List<CourseTag> courseTags = courseTagService.list(queryWrapper);
        if (!CollectionUtils.isEmpty(courseTags)) {
            courseDetailDTO.setTagIds(courseTags.stream().map(CourseTag::getTagId).collect(Collectors.toSet()));
        }
    }

    private void dealWithPosition(String id, CourseDetailDTO courseDetailDTO) {
        LambdaQueryWrapper<CourseCategory> courseCategoryQueryWrapper = new LambdaQueryWrapper<>();
        courseCategoryQueryWrapper.eq(CourseCategory::getCourseId, id);
        courseCategoryQueryWrapper.eq(CourseCategory::getCategoryType, POSITION_CATE);
        courseCategoryQueryWrapper.select(CourseCategory::getCategoryId);
        List<CourseCategory> courseCategoryList = courseCategoryService.list(courseCategoryQueryWrapper);
        if (!CollectionUtils.isEmpty(courseCategoryList)) {
            List<String> identityIds = courseCategoryList.stream().map(CourseCategory::getCategoryId)
                .collect(Collectors.toList());
            courseDetailDTO.setPositionIds(identityIds);
            Set<IdentityDTO> identityList = identityFeign.getByIds(identityIds);
            if (!CollectionUtils.isEmpty(identityList)) {
                courseDetailDTO.setPositionNames(
                    identityList.stream().map(IdentityDTO::getName).collect(Collectors.toList()));
            }
        }
    }

    @Override
    public void delCourseById(String ids) {
        String[] strIds = ids.split(",");
        List<String> idsList = Arrays.asList(strIds);
        List<Course> delCourseList = listByIds(idsList);
        if (!CollectionUtils.isEmpty(
            delCourseList.stream().filter(c -> GeneralJudgeEnum.CONFIRM.getValue().equals(c.getIsPublish()))
                .collect(Collectors.toSet()))) {
            throw new BusinessException(BaseErrorNoEnum.INCLUDING_IS_PUBLISH);
        }

        // 循环执行删除操作，同时记录删除日志
        delCourseList.forEach(e -> {
            courseDao.delCourse(e);
            // 删除标签关联关系
            sysTagFeign.saveSysTagResourceRelation(new HashSet<String>(), e.getId(), "");
        });

        // 发送消息，同步状态到岗位发展管理
        ActivityStatusChangeEvent.sendMsg(idsList, ResourceTypeCodeEnum.COURSE, null, 1, mqProducer);
        // 发送资源修改信息
        mqProducer.sendMsg(
            new ResourceChangeEvent(FirstInfoContentEnum.course.name(), idsList, GeneralJudgeEnum.CONFIRM.getValue(),
                GeneralJudgeEnum.NEGATIVE.getValue()));

        // 发送资源操作事件消息
        idsList.forEach(
            id -> mqProducer.sendMsg(new ResourceOperateEvent(OperationEnum.DELETE, PushType.COURSE.getKey(), id)));

        // 课程相关数据变动，同步变动课程数据
        idsList.forEach(
            id -> mqProducer.sendMsg(new SynUpdateCopyCourseEvent(id, SynUpdateCopyDataEventEnum.COURSE.getKey())));

        // 同步能力词典和能力模型关联课程
        mqProducer.sendMsg(new DeleteAbilityCourseRelateEvent(ids));
        // 删除下发范围
        idsList.forEach(courseId -> courseViewLimitComponent.delViewLimit(courseId));
        // 删除相应的学习计划
        LambdaQueryWrapper<CourseStudyPlan> courseStudyPlanQuery = new LambdaQueryWrapper<>();
        courseStudyPlanQuery.in(CourseStudyPlan::getCourseId, idsList);
        courseStudyPlanService.remove(courseStudyPlanQuery);
        handleCategoryCanDel();

        courseQaQuestionInfoService.deleteQACourseByCourseIdList(idsList);
        // 删除审核流程实例
        processFeign.deleteProcessInstanceAndTaskByResourceId(idsList);
    }

    @Override
    public void publish(PublishDTO publishDTO) {
        // 是否有课程存在审核流程或者审核不通过
        if (PublishStatusEnum.IS_PUBLISH.getValue() == publishDTO.getIsPublish() && baseMapper.isExistCourseNeedAudit(
            publishDTO.getIds())) {
            throw new BusinessException(CourseErrorNoEnum.EXIST_COURSE_NEED_AUDIT);
        }

        //发布时需校验
        // 开启了配置才能在添加时直接发布
        String paraValue = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_916.getCode());
        if (StringUtils.isNotBlank(paraValue) && GeneralJudgeEnum.NEGATIVE.getValue().equals(Integer.valueOf(paraValue))
            && PublishStatusEnum.IS_PUBLISH.getValue() == publishDTO.getIsPublish()) {
            List<CourseListDTO> result = baseMapper.getCWNumByCourseIds(publishDTO.getIds());
            result.stream().filter(dto -> dto.getCourseWareNum() == 0).findFirst().ifPresent(dto -> {
                throw new BusinessException(CourseErrorNoEnum.ERR_COURSEWARE_NUM_NUL, null, dto.getCourseName());
            });
        }

        //根据传参查询对应课程资源列表
        List<Course> courseList = listByIds(publishDTO.getIds());
        Map<String, Course> courseMap = courseList.stream()
            .collect(Collectors.toMap(Course::getId, Function.identity()));

        //校验课程资源状态，如果已被删除（空值），则抛出业务异常
        for (String id : publishDTO.getIds()) {
            if (null == courseMap.get(id)) {
                throw new BusinessException(CourseErrorNoEnum.ERR_COURSE_NULL);
            }
        }

        for (String id : publishDTO.getIds()) {
            Course course = new Course();
            course.setId(id);
            course.setIsPublish(publishDTO.getIsPublish());
            Course byId = courseMap.get(id);
            course.setCourseName(byId.getCourseName());
            course.setConsumeExcitationType(byId.getConsumeExcitationType());
            course.setConsumeExcitationNum(byId.getConsumeExcitationNum());
            if (PublishStatusEnum.IS_PUBLISH.getValue() == publishDTO.getIsPublish()) {
                // 发布课程
                courseDao.publish(course);
            } else {
                // 取消发布
                courseDao.unPublish(course);
            }
            // 发送资源操作事件消息
            mqProducer.sendMsg(new ResourceOperateEvent(
                publishDTO.getIsPublish() == PublishStatusEnum.IS_PUBLISH.getValue() ? OperationEnum.PUBLISH
                    : OperationEnum.PUBLISH_CANCEL, PushType.COURSE.getKey(), id));
        }

        // 发送消息，同步状态到岗位发展管理
        ActivityStatusChangeEvent.sendMsg(publishDTO.getIds(), ResourceTypeCodeEnum.COURSE, publishDTO.getIsPublish(),
            0, mqProducer);

        List<ResourceStatusDTO> list = new ArrayList<>();
        ResourceStatusDTO dto;
        for (Course course : courseList) {
            dto = new ResourceStatusDTO();
            BeanUtils.copyProperties(course, dto);
            dto.setResourceId(course.getId());
            dto.setResourceType(ResourceChangeEnum.course.getValue());
            dto.setIsPublish(publishDTO.getIsPublish());
            list.add(dto);
        }
        // 发送资源修改信息
        ResourceChangeEvent event = new ResourceChangeEvent(FirstInfoContentEnum.course.name(), publishDTO.getIds(),
            GeneralJudgeEnum.NEGATIVE.getValue(), publishDTO.getIsPublish());
        event.setResourceStatusList(list);
        mqProducer.sendMsg(event);

    }


    @Override
    public void recommend(RecommendDTO recommendDTO) {
        List<Course> list = listByIds(recommendDTO.getIds());
        list.stream().forEach(e -> {
            Course course = new Course();
            course.setId(e.getId());
            course.setCourseName(e.getCourseName());
            course.setIsRecommend(recommendDTO.getIsRecommend());
            if (recommendDTO.getIsRecommend().equals(GeneralJudgeEnum.CONFIRM.getValue())) {
                course.setRecommendTime(new Date());
            }
            courseDao.updateCourse(course);
        });
    }

    @Override
    public void share(ShareDTO shareDTO) {
        LambdaQueryWrapper<Course> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Course::getId, shareDTO.getIds());
        // 去共享则查未共享的课程，取消共享则查已共享的课程
        queryWrapper.eq(Course::getIsShare,
            GeneralJudgeEnum.CONFIRM.getValue().equals(shareDTO.getIsShare()) ? GeneralJudgeEnum.NEGATIVE.getValue()
                : GeneralJudgeEnum.CONFIRM.getValue());
        List<Course> list = list(queryWrapper);
        list.forEach(e -> {
            Course course = new Course();
            BeanUtils.copyProperties(e, course);
            course.setUpdateBy(UserThreadContext.getUserId());
            course.setUpdateTime(new Date());
            course.setId(e.getId());
            course.setIsShare(shareDTO.getIsShare());

            if (GeneralJudgeEnum.CONFIRM.getValue().equals(shareDTO.getIsShare())) {
                // 登记原下发
                CourseShareRecord courseShareRecord = new CourseShareRecord();
                courseShareRecord.setCourseId(e.getId());
                courseShareRecord.setViewType(e.getViewType());
                courseShareRecord.setViewLimitId(
                    courseViewLimitComponent.getViewLimitBaseInfo(e.getId()).getProgrammeId());
                courseShareRecordService.save(courseShareRecord);
                // 修改下发
                course.setViewType(2);
                courseViewLimitComponent.handleNewAllViewLimit(e.getId());
            } else {
                // 回滚原下发
                LambdaQueryWrapper<CourseShareRecord> recordLambdaQueryWrapper = new LambdaQueryWrapper<>();
                recordLambdaQueryWrapper.eq(CourseShareRecord::getCourseId, e.getId());
                recordLambdaQueryWrapper.orderByAsc(CourseShareRecord::getId);
                recordLambdaQueryWrapper.last("limit 1");
                Optional.ofNullable(courseShareRecordService.getOne(recordLambdaQueryWrapper))
                    .ifPresent(courseShareRecord -> {
                        course.setViewType(courseShareRecord.getViewType());
                        courseViewLimitComponent.handleNewViewLimit(courseShareRecord.getViewLimitId(), e.getId());
                    });
                // 删除登记记录
                recordLambdaQueryWrapper.clear();
                recordLambdaQueryWrapper.eq(CourseShareRecord::getCourseId, e.getId());
                courseShareRecordService.remove(recordLambdaQueryWrapper);
            }
            courseDao.updateCourse(course);
        });
    }

    @Override
    public PageInfo<CourseLearnDetailDTO> getUserDetailData(CourseLearnQuery courseLearnQuery) {
        Set<String> managerAreaOrgIds = orgFeign.findUserManageAreaLevelPath(UserThreadContext.getUserId());
        if (!managerAreaOrgIds.contains("/0/")) {
            courseLearnQuery.setManagerAreaOrgIds(managerAreaOrgIds);
        }
        courseLearnQuery.setCurrentOrgId(UserThreadContext.getOrgId());
        courseLearnQuery.setCurrentUserId(UserThreadContext.getUserId());
        //用户id
        if (StringUtils.isNotBlank(courseLearnQuery.getUserIds())) {
            courseLearnQuery.setUserIdsVo(Arrays.asList(courseLearnQuery.getUserIds().split(TAG_ID_SEPARATOR)));
        }

        if (StringUtils.isNotBlank(courseLearnQuery.getCreateOrgId())) {
            Optional.ofNullable(orgFeign.getById(courseLearnQuery.getCreateOrgId()))
                .ifPresent(orgDTO -> courseLearnQuery.setLevelPath(orgDTO.getLevelPath()));
        }

        PageInfo<CourseLearnDetailDTO> pageInfo = PageMethod.startPage(courseLearnQuery.getPageNo(),
                courseLearnQuery.getPageSize())
            .doSelectPageInfo(() -> userCourseRecordService.getCourseLearnDetail(courseLearnQuery));
        List<CourseLearnDetailDTO> list = pageInfo.getList();
        if (CollectionUtils.isEmpty(list)) {
            return pageInfo;
        }
        List<String> userIds = list.stream().map(CourseLearnDetailDTO::getUserId).collect(Collectors.toList());
        Set<String> orgIds = list.stream().map(CourseLearnDetailDTO::getOrgId).collect(Collectors.toSet());
        Map<String, UserOrgDTO> userMap = orgFeign.getUserAndOrgMapByUserIds(userIds);
        Map<String, OrgShowDTO> orgShowDTOMap = orgFeign.getOrgShowDTO(orgIds);

        Set<String> manageCourseOrgIds = list.stream().map(CourseLearnDetailDTO::getManageCourseOrgId)
            .collect(Collectors.toSet());
        Map<String, OrgShowDTO> manageCourseOrgShowDTOMap = orgFeign.getOrgShowDTO(manageCourseOrgIds);
        list.forEach(cld -> {
            //未学课件数
            if (cld.getCourseWareCount() != null && cld.getLearnedCourseWareCount() != null) {
                cld.setRemainderCourseWareCount(cld.getCourseWareCount() - cld.getLearnedCourseWareCount());
            }
            Optional.ofNullable(userMap.get(cld.getUserId())).ifPresent(userOrgDTO -> {
                cld.setLoginName(userOrgDTO.getLoginName());
                cld.setFullName(userOrgDTO.getFullName());
                cld.setJobName(userOrgDTO.getPostName());
            });
            Optional.ofNullable(orgShowDTOMap.get(cld.getOrgId())).ifPresent(orgShowDTO -> {
                cld.setOrgName(orgShowDTO.getOrgShortName());
                cld.setOrgPath(orgShowDTO.getLevelPathName());
            });
            Optional.ofNullable(manageCourseOrgShowDTOMap.get(cld.getManageCourseOrgId())).ifPresent(orgShowDTO -> {
                cld.setManageCourseOrgName(orgShowDTO.getOrgShortName());
                cld.setManageCourseOrgLevelPath(orgShowDTO.getLevelPathName());
            });
            BigDecimal userScore = examFeign.getUserScore(UserThreadContext.getUserId(), cld.getExamId());
            if (userScore != null) {
                cld.setScore(userScore);
            }

        });
        return pageInfo;
    }

    @Override
    public PageInfo<CourseCollectionDTO> getCourseCollectList(CourseCollectionQuery collectionQuery) {
        collectionQuery.setCurrentUserId(UserThreadContext.getUserId());
        PageInfo<CourseCollectionDTO> pageInfo = PageMethod.startPage(collectionQuery.getPageNo(),
                collectionQuery.getPageSize())
            .doSelectPageInfo(() -> courseFavorateMapper.courseCollectionList(collectionQuery));
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return pageInfo;
        }
        // 获取课程主键
        Set<String> courseIdSet = pageInfo.getList().stream().map(CourseCollectionDTO::getId)
            .filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        // 获取课程与标签关联
        List<CourseTag> courseTagList = courseTagMapper.selectList(
            new LambdaQueryWrapper<CourseTag>().in(CourseTag::getCourseId, courseIdSet));
        Map<String, List<CourseTag>> courseTagMap = courseTagList.stream()
            .collect(Collectors.groupingBy(CourseTag::getCourseId));
        // 获取标签
        Set<String> tagIdSet = courseTagList.stream().map(CourseTag::getTagId).collect(Collectors.toSet());
        SysTagBaseQuery sysTagBaseQuery = new SysTagBaseQuery();
        sysTagBaseQuery.setTagIdSet(tagIdSet);
        Map<String, SysTagBaseDTO> tagMap;
        if (!CollectionUtils.isEmpty(tagIdSet)) {
            tagMap = sysTagFeign.selectSysTagBaseByIds(sysTagBaseQuery).stream().collect(Collectors.toMap(SysTagBaseDTO::getId, sysTagBaseDTO -> sysTagBaseDTO));
        } else {
            tagMap = new HashMap<>();
        }
        // 获取课程封面信息
        Map<String, String> imageMap = fileFeign.getImageUrlsByIds(courseIdSet, ImageBizType.CourseImgIcon.name());
        Map<String, Integer> commentMap = commentFeign.getDiscussCount(courseIdSet, CommentTypeEnum.COURSE);
        pageInfo.getList().forEach(dto -> {
            Optional.ofNullable(courseTagMap.get(dto.getId())).ifPresent(courseTags -> courseTags.forEach(
                courseTag -> Optional.ofNullable(tagMap.get(courseTag.getTagId())).ifPresent(SysTagBaseDTO -> {
                    MarkInfoDTO tagDTO = new MarkInfoDTO();
                    tagDTO.setMarkId(SysTagBaseDTO.getId());
                    tagDTO.setMarkContent(SysTagBaseDTO.getTagName());
                    List<MarkInfoDTO> tageDTOList = dto.getMarkInfoList();
                    if (CollectionUtils.isEmpty(tageDTOList)) {
                        tageDTOList = new ArrayList<>();
                    }
                    tageDTOList.add(tagDTO);
                    dto.setMarkInfoList(tageDTOList);
                })
            ));
            Optional.ofNullable(imageMap.get(dto.getId())).ifPresent(dto::setImage);
            Optional.ofNullable(commentMap.get(dto.getId())).ifPresent(dto::setCommentNumber);
            //填充 兑换学习消耗激励类型
            if (StringUtils.isNotBlank(dto.getConsumeExcitationType())) {
                dto.setConsumeExcitationTypeName(ExcitationTypeEnum.getNameByCode(dto.getConsumeExcitationType()));
            }
        });
        return pageInfo;
    }

    @Override
    public CourseItemNumberDTO getItemNumber() {
        CourseItemNumberDTO courseItemNumberDTO = new CourseItemNumberDTO();
        List<String> jobLevelPathList = identityFeign.getJobLevelPath(UserThreadContext.getUserId());
        // 1.获取已学数量
        LambdaQueryWrapper<CourseView> courseViewWrapper = new LambdaQueryWrapper<>();
        courseViewWrapper.eq(CourseView::getViewBy, UserThreadContext.getUserId());
        List<CourseView> courseViewList = courseViewMapper.selectList(courseViewWrapper);
        if (CollectionUtils.isEmpty(courseViewList)) {
            courseViewList = new ArrayList<>();
        }
        Set<String> cwIdSet = courseViewList.stream().map(CourseView::getCwId).collect(Collectors.toSet());
        List<Courseware> coursewareList = coursewareMapper.selectBatchIds(cwIdSet);
        if (CollectionUtils.isEmpty(coursewareList)) {
            coursewareList = new ArrayList<>();
        }
        Set<String> courseIdSet = coursewareList.stream().map(Courseware::getCourseId).collect(Collectors.toSet());
        String courseType = "";
        if (ClientTypeEnum.CLIECNT_TYPE_PC.getName().equals(UserThreadContext.getOs())) {
            // h5和pc端
            courseType = COURSE_TYPE_E;
        } else {
            // 课程类别 客户端APP课程（M和EM）
            courseType = COURSE_TYPE_M;
        }
        LambdaQueryWrapper<Course> courseWrapper = new LambdaQueryWrapper<>();
        courseWrapper.in(Course::getId).eq(Course::getCourseType, courseType);
        List<Course> courseList = courseMapper.selectBatchIds(courseIdSet);
        int studyRecordCount = courseList.size();
        courseItemNumberDTO.setStudyRecordCount(studyRecordCount);
        // 2.获取已学未完成数量
        // 获取已学课程的所有课件
        LambdaQueryWrapper<Courseware> coursewareWrapper = new LambdaQueryWrapper<>();
        coursewareWrapper.in(Courseware::getCourseId, courseIdSet);
        List<Courseware> coursewareAll = coursewareMapper.selectList(coursewareWrapper);
        final Set<String> coursewareIdAllSet = coursewareAll.stream().map(Courseware::getId)
            .collect(Collectors.toSet());
        // 课程下的所有课件与已学课件取差集 剩余为未学习课件
        coursewareIdAllSet.removeAll(cwIdSet);
        int studyNoFinishCount = coursewareIdAllSet.size();
        courseItemNumberDTO.setStudyNoFinishCount(studyNoFinishCount);
        // 3.获取岗位课程数量
        LambdaQueryWrapper<CourseCategory> courseCategoryWrapper = new LambdaQueryWrapper<>();
        courseCategoryWrapper.eq(CourseCategory::getCategoryType, CategoryType.PositionCate.name());
        List<CourseCategory> courseCategoryList = courseCategoryMapper.selectList(courseCategoryWrapper);
        courseIdSet = courseCategoryList.stream().map(CourseCategory::getCourseId).collect(Collectors.toSet());
        courseWrapper.clear();
        courseWrapper.in(Course::getId, courseIdSet).eq(Course::getIsAvailable, AvailableEnum.AVAILABLE.getValue())
            .eq(Course::getIsPublish, PublishStatusEnum.IS_PUBLISH.getValue());
        if (!CollectionUtils.isEmpty(jobLevelPathList)) {
            for (String jobLevelPath : jobLevelPathList) {
                courseWrapper.or().likeLeft(Course::getCourseType, jobLevelPath);
            }
        }

        courseList = courseMapper.selectList(courseWrapper);
        final Map<String, Course> courseMap = courseList.stream()
            .collect(Collectors.toMap(Course::getId, course -> course));
        courseCategoryList = courseCategoryList.stream()
            .filter(courseCategory -> null != courseMap.get(courseCategory.getCourseId())).filter(courseCategory -> {
                IdentityDTO identityDTO = identityFeign.getByCode(courseCategory.getCategoryId(),
                    IdentityCategoryEnum.POST.getCategoryId());
                return null != identityDTO;
            }).collect(Collectors.toList());
        int positionCourseCount = courseCategoryList.size();
        courseItemNumberDTO.setPositionCourseCount(positionCourseCount);
        // 4.获取当前用户的课程收藏数量
        List<CourseFavorate> courseFavorateList = courseFavorateMapper.selectList(
            new LambdaQueryWrapper<CourseFavorate>().eq(CourseFavorate::getCreateBy, UserThreadContext.getUserId()));
        courseIdSet = courseFavorateList.stream().map(CourseFavorate::getCourseId).collect(Collectors.toSet());
        courseList = courseMapper.selectBatchIds(courseIdSet);
        Map<String, Course> favorateCourseMap = courseList.stream()
            .collect(Collectors.toMap(Course::getId, course -> course));
        courseFavorateList = courseFavorateList.stream().filter(courseFavorate -> {
            Course course = favorateCourseMap.get(courseFavorate.getCourseId());
            return course != null;
        }).collect(Collectors.toList());
        int collectionCount = courseFavorateList.size();
        courseItemNumberDTO.setCollectionCount(collectionCount);
        return courseItemNumberDTO;
    }

    @Override
    public List<CourseTagDTO> getCourseTagList() {
        List<CourseTagDTO> courseTagDTOList = new ArrayList<>();
        QueryWrapper<CourseTag> courseTagWrapper = new QueryWrapper<>();
        courseTagWrapper.select("DISTINCT tag_id");
        List<CourseTag> courseTagList = courseTagMapper.selectList(courseTagWrapper);
        if (CollectionUtils.isEmpty(courseTagList)) {
            return courseTagDTOList;
        }

        final Set<String> tagIdSet = courseTagList.stream().map(CourseTag::getTagId).collect(Collectors.toSet());
        SysTagBaseQuery sysTagBaseQuery = new SysTagBaseQuery();
        sysTagBaseQuery.setTagIdSet(tagIdSet);
        sysTagBaseQuery.setIsShow(CommonConstants.IS_SHOW_YES);
        sysTagBaseQuery.setIsAvailable(AvailableEnum.AVAILABLE.getValue());
        List<SysTagBaseDTO> sysTagBaseDTOList = sysTagFeign.selectSysTagBaseByIds(sysTagBaseQuery);

        if (CollectionUtils.isEmpty(sysTagBaseDTOList)) {
            return courseTagDTOList;
        }

        courseTagDTOList = sysTagBaseDTOList.stream().map(sysTagBaseDTO -> {
            CourseTagDTO courseTagDTO = new CourseTagDTO();
            courseTagDTO.setId(sysTagBaseDTO.getId());
            courseTagDTO.setTitle(sysTagBaseDTO.getTagName());
            return courseTagDTO;
        }).collect(Collectors.toList());
        return courseTagDTOList;
    }

    @Override
    public PageInfo<PostCourseClientDTO> getPostCourseList(PostCourseClientQuery postCourseClientQuery) {
        postCourseClientQuery.setImgIcon(COURSE_IMG_ICON);
        postCourseClientQuery.setCType(POSITION_CATE);
        postCourseClientQuery.setCurrentUserId(UserThreadContext.getUserId());
        if (ClientTypeEnum.CLIECNT_TYPE_PC.getName().equals(UserThreadContext.getOs())) {
            // h5和pc端
            postCourseClientQuery.setCourseType(COURSE_TYPE_E);
        } else {
            // 课程类别 客户端APP课程（M和EM）
            postCourseClientQuery.setCourseType(COURSE_TYPE_M);
        }
        postCourseClientQuery.setIdentityId(identityFeign.getIdentityId(postCourseClientQuery.getCurrentUserId()));
        PageInfo<PostCourseClientDTO> pageInfo = PageMethod.startPage(postCourseClientQuery.getPageNo(),
                postCourseClientQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.getPostCourseList(postCourseClientQuery));
        List<String> idList = pageInfo.getList().stream().map(PostCourseClientDTO::getId).collect(Collectors.toList());
        Map<String, String> imgMap = fileFeign.getImageUrlsByIds(idList, postCourseClientQuery.getImgIcon());
        Map<String, Integer> commentMap = commentFeign.getDiscussCount(idList, CommentTypeEnum.COURSE);
        Map<String, List<MarkInfoDTO>> courseMarkInfoMap = getCourseMarkInfo(idList);
        pageInfo.getList().forEach(p -> {
            String id = p.getId();
            p.setMarkInfoList(
                CollectionUtils.isEmpty(courseMarkInfoMap.get(id)) ? new ArrayList<>() : courseMarkInfoMap.get(id));
            Optional.ofNullable(imgMap.get(id)).ifPresent(p::setImage);
            p.setCommentNumber(Optional.ofNullable(commentMap.get(id)).orElse(0));
            //填充 兑换学习消耗激励类型
            if (StringUtils.isNotBlank(p.getConsumeExcitationType())) {
                p.setConsumeExcitationTypeName(ExcitationTypeEnum.getNameByCode(p.getConsumeExcitationType()));
            }
        });
        return pageInfo;
    }

    @Override
    public CourseDetailClientDTO getCourseDetail(CourseDetailClientQuery courseDetailClientQuery) {
        String userId = UserThreadContext.getUserId();
        courseDetailClientQuery.setCurrentUserId(userId);
        CourseDetailClientDTO courseDetail = Optional.ofNullable(baseMapper.getCourseDetail(courseDetailClientQuery))
            .orElseThrow(() -> new BusinessException(CourseErrorNoEnum.ERR_COURSE_NULL));
        if (Objects.equals(courseDetail.getIsPublish(), PublishStatusEnum.IS_NO_PUBLISH.getValue())) {
            throw new BusinessException(CourseErrorNoEnum.ERR_COURSE_NOT_PUBLISH);
        }
        //课程PC/APP端类型校验
        verifyCourseType(courseDetailClientQuery, courseDetail);

        //是否跳过校验下发标志
        boolean notCheckLimit = false;

        if (GeneralJudgeEnum.CONFIRM.getValue().equals(courseDetailClientQuery.getIsIgnoreView()) || checkPostCourse(
            courseDetailClientQuery)) {
            // 前端传来不需要校验下发
            // 当前用户角色符合岗位课程的岗位角色,不需要校验下发
            notCheckLimit = true;
        }
        notCheckLimit = checkTrainCourse(courseDetailClientQuery, notCheckLimit);
        // 完善数据各字段
        replenishParam(courseDetail, userId);

        // 学习计划 - 这里需要校验系统路由权限,学习计划归属于人才测评模块
        List<String> routerIds = routerFeign.getRouterNames();
        courseDetail.setHasPlan(0);
        if (routerIds.contains(ResourceTypeEnum.TALENT_ASSESS.getRouter())) {
            courseDetail.setHasPlan(1);
            Optional.ofNullable(
                    courseStudyPlanService.getPlanByUserAndCourseId(userId, courseDetail.getId()))
                .ifPresent(courseStudyPlan -> courseDetail.setCourseStudyPlanId(courseStudyPlan.getId()));
        }

        // 用户是否是上级
        courseDetail.setIsSuperiors(userFeign.getIsSuperiors(userId));
        courseDetail.setNeedQueryRelatedCourse(Integer.valueOf(paraFeign.getParaValue("313")));
        courseDetail.setPosterShareIsOpen(posterShareService.resourceIsOpenShare(courseDetail.getId()));

        // 共享课程不需要校验下发
        if (GeneralJudgeEnum.CONFIRM.getValue().equals(courseDetail.getIsShare())) {
            notCheckLimit = true;
        }
        // pv使用对象
        ICourseService courseService = SpringUtil.getBean(COURSE_SERVICE, ICourseService.class);
        if (notCheckLimit) {
            //不需要进行下发校验
            courseDetailClientQuery.setBizId(
                Optional.ofNullable(courseDetailClientQuery.getBizId()).orElse(courseDetailClientQuery.getId()));
            mqProducer.sendMsg(new ExcitationMQEvent(
                new ExcitationMQEventDTO(UserThreadContext.getUserId(), ExcitationEventEnum.learnCourse.name(),
                    courseDetail.getId(), ExcitationEventCategoryEnum.COURSE.getCode()).setTargetName(
                        courseDetail.getCourseName()).setBizId(courseDetailClientQuery.getBizId())
                    .setBizType(courseDetailClientQuery.getBizType())
                    .setIsExchange(courseDetailClientQuery.getIsExchange())));

            // 校验是否存在激励扣除项配置，避免无激励配置的课程
            if (StringUtils.isNotBlank(courseDetail.getConsumeExcitationType()) && (
                courseDetail.getConsumeExcitationNum() != null)
                && (BigDecimal.ZERO.compareTo(courseDetail.getConsumeExcitationNum()) != 0)) {
                // 判断是否有激励扣除项配置，如果有，则进行相应扣除
                extracted(courseDetailClientQuery, courseDetail);
            }

            // 异步累增pv
            courseService.addCoursePV(courseDetailClientQuery.getId());
            return courseDetail;
        } else {
            //需要进行下发权限校验
            CourseViewLimitCheckResultDTO resultDTO = courseViewLimitService.checkViewPermision(
                UserThreadContext.getUserId(), courseDetailClientQuery.getId(), courseDetailClientQuery.getBizId(),
                courseDetailClientQuery.getActivityId());
            if (Objects.equals(resultDTO.getCheckResult(), GeneralJudgeEnum.CONFIRM.getValue())) {
                //校验下发通过，发送消息[点击学习]
                courseDetailClientQuery.setBizId(
                    Optional.ofNullable(courseDetailClientQuery.getBizId()).orElse(courseDetailClientQuery.getId()));
                mqProducer.sendMsg(new ExcitationMQEvent(
                    new ExcitationMQEventDTO(UserThreadContext.getUserId(), ExcitationEventEnum.learnCourse.name(),
                        courseDetail.getId(), ExcitationEventCategoryEnum.COURSE.getCode()).setTargetName(
                            courseDetail.getCourseName()).setBizId(courseDetailClientQuery.getBizId())
                        .setBizType(courseDetailClientQuery.getBizType())
                        .setIsExchange(courseDetailClientQuery.getIsExchange())));
                // 异步累增pv
                courseService.addCoursePV(courseDetailClientQuery.getId());
                return courseDetail;

            } else {
                //校验下发不通过，判断是否有激励扣除配置，有扣除激励配置则校验是否通过激励配置，没有下发，没有激励，两项都不通过，无权访问

                extracted(courseDetailClientQuery, courseDetail);
                // 异步累增pv
                courseService.addCoursePV(courseDetailClientQuery.getId());
                return courseDetail;
            }
        }
    }

    /**
     * 判断来自培训项目的课程
     *
     * @param courseDetailClientQuery 外面权限
     * @param notCheckLimit           已经判断过的是否需要校验权限的结果
     * @return
     */
    private boolean checkTrainCourse(CourseDetailClientQuery courseDetailClientQuery, boolean notCheckLimit) {

        if (courseDetailClientQuery.getBizType().equals(ExcitationEventCategoryEnum.TRAIN.getCode())) {
            // 来自培训项目的活动，对课程不作权限校验
            if (StringUtils.isNotBlank(courseDetailClientQuery.getActivityId()) && StringUtils.isNotBlank(
                courseDetailClientQuery.getBizId())) {
                notCheckLimit = true;
            }
            //来自培训项目的课程，要作权限校验
            if (StringUtils.isBlank(courseDetailClientQuery.getActivityId()) && StringUtils.isNotBlank(
                courseDetailClientQuery.getBizId())) {
                notCheckLimit = false;
            }
        }
        return notCheckLimit;
    }

    private void extracted(CourseDetailClientQuery courseDetailClientQuery,
        CourseDetailClientDTO courseDetail) {
        if (StringUtils.isNotBlank(courseDetail.getConsumeExcitationType()) && (
            courseDetail.getConsumeExcitationNum() != null)
            && (BigDecimal.ZERO.compareTo(courseDetail.getConsumeExcitationNum()) != 0)) {
            //存在扣除激励配置，进行激励扣除验证
            Boolean reduceExcitation = this.getReduceExcitation(courseDetailClientQuery.getId(),
                courseDetailClientQuery.getBizId()).getReduce();
            if (Boolean.FALSE.equals(reduceExcitation)) {
                //没有扣除过激励，进行扣除激励
                excitationFeign.reduceUserExcitationWithCheck(
                    (UserExcitationReduceDTO) new UserExcitationReduceDTO().setEnableRepeatReduce(false)
                        .setTargetId(courseDetailClientQuery.getId())
                        .setUserId(UserThreadContext.getUserId())
                        .setExcitationNum(courseDetail.getConsumeExcitationNum())
                        .setTradeType(TradeTypeEnum.COURSE.name())
                        .setTargetName(courseDetail.getCourseName())
                        .setExcitationType(courseDetail.getConsumeExcitationType())
                        .setEventEnum(ExcitationEventEnum.COURSE_CONSUME_STUDY));
            }
            //扣除激励校验通过，发送消息[点击学习]
            courseDetailClientQuery.setBizId(
                Optional.ofNullable(courseDetailClientQuery.getBizId())
                    .orElse(courseDetailClientQuery.getId()));
            mqProducer.sendMsg(new ExcitationMQEvent(
                new ExcitationMQEventDTO(UserThreadContext.getUserId(), ExcitationEventEnum.learnCourse.name(),
                    courseDetail.getId(), ExcitationEventCategoryEnum.COURSE.getCode()).setTargetName(
                        courseDetail.getCourseName()).setBizId(courseDetailClientQuery.getBizId())
                    .setBizType(courseDetailClientQuery.getBizType())
                    .setIsExchange(courseDetailClientQuery.getIsExchange())));
        } else {
            //没有则下发，不存在扣除激励配置，无权访问
            throw new BusinessException(ErrorNoEnum.ERR_NO_POWER);
        }
    }

    /**
     * 获取课程认证讲师
     *
     * @param courseId
     */
    private List<LecturerCourseAuthFeignDTO> getAuthenticationLecturer(String courseId) {
        return lecturerFeign.getLecturerCourseAuthByCourseId(courseId);
    }

    @Override
//    @Async
    public void reduceUserExcitation(CourseDetailClientQuery courseDetailClientQuery,
        CourseDetailClientDTO courseDetail) {
        Boolean reduceExcitation = this.getReduceExcitation(courseDetailClientQuery.getId(),
            courseDetailClientQuery.getBizId()).getReduce();
        if (Boolean.FALSE.equals(reduceExcitation) && StringUtils.isNotBlank(courseDetail.getConsumeExcitationType())
            && (courseDetail.getConsumeExcitationNum() != null) && (
            BigDecimal.ZERO.compareTo(courseDetail.getConsumeExcitationNum())
                != 0)) {
            excitationFeign.reduceUserExcitationWithCheck(
                (UserExcitationReduceDTO) new UserExcitationReduceDTO().setEnableRepeatReduce(false)
                    .setTargetId(courseDetailClientQuery.getId()).setUserId(UserThreadContext.getUserId())
                    .setExcitationNum(courseDetail.getConsumeExcitationNum()).setTradeType(TradeTypeEnum.COURSE.name())
                    .setTargetName(courseDetail.getCourseName())
                    .setExcitationType(courseDetail.getConsumeExcitationType())
                    .setEventEnum(ExcitationEventEnum.COURSE_CONSUME_STUDY));
        }
    }

    @Override
    public List<CoursewareDetailClientDTO> getCwListByCourseId(CourseDetailClientQuery query) {
        // 通过课程id获取课程信息
        Course course = get(query.getId());
        // 查询指定课程课件列表
        List<Courseware> list = coursewareMapper.selectList(
            new LambdaQueryWrapper<Courseware>().eq(Courseware::getCourseId, course.getId())
                .eq(Courseware::getIsAvailable, AvailableEnum.AVAILABLE.getValue())
                .eq(Courseware::getTransformStatus, TranscodeStatusEnum.TRANSFORMED.value)
                .like(StringUtils.isNotBlank(query.getSearchKey()), Courseware::getCwName, query.getSearchKey())
                .orderByAsc(Courseware::getSortNo).orderByDesc(Courseware::getCreateTime));
        // 响应数据处理
        List<CoursewareDetailClientDTO> dtoList = list.stream().map(cw -> {
            CoursewareDetailClientDTO dto = new CoursewareDetailClientDTO();
            BeanUtils.copyProperties(cw, dto);
            dto.setType(cw.getCwType());
            dto.setPlayTime(cw.getRealPlayTime());
            dto.setSetPlayTime(cw.getPlayTime());
            return dto;
        }).collect(Collectors.toList());

        handCwList(query, dtoList);
        return dtoList;
    }

    /**
     * 处理组装课件数据
     *
     * @param query
     * @param courseWareList
     */
    private void handCwList(CourseDetailClientQuery query, List<CoursewareDetailClientDTO> courseWareList) {
        if (CollectionUtils.isEmpty(courseWareList)) {
            return;
        }
        String userId = UserThreadContext.getUserId();

        List<String> userIds = courseWareList.stream().map(CoursewareDetailClientDTO::getCreateBy)
            .collect(Collectors.toList());
        List<String> cwIds = courseWareList.stream().map(CoursewareDetailClientDTO::getId).collect(Collectors.toList());
        // 这里进一步优化空间就是牺牲实时性，不走feign调用，直接查课程库的user表
        Map<String, String> userNameMapByIds = userFeign.getFullNameByUserIdList(userIds);
        Map<String, Integer> commentMap = commentFeign.getDiscussCount(cwIds, CommentTypeEnum.COURSE);
        Map<String, LecturerDetailDTO> lecturerByCwIds = lecturerExaminationFeign.getLecturerByCwIds(cwIds);
        Map<String, ViewExamFeignDTO> examInfoMapByIds = new HashMap<>();

        List<String> examIds = courseWareList.stream().map(CoursewareDetailClientDTO::getExamId)
            .filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(examIds)) {
            ExamQuery examQuery = new ExamQuery();
            examQuery.setExamIds(examIds);
            examQuery.setIsPublish(1);
            examQuery.setIsAvailable(1);
            examInfoMapByIds = examFeign.getExamInfoMapByIds(examQuery);
        }
        Map<String, CoursewareQuestion> courseQuestionMap = coursewareQuestionService.getCourseQuestionMap(cwIds);
        // 查询课件进度
        Map<String, Long> cwIdToProgressMap = coursewareUserRecordService.getCoursewareProgressByUserIdAndCwId(userId,
            cwIds);
        // 查询课件评星
        Map<String, CoursewareStarDTO> coursewareStarMap = coursewareStarService.queryCoursewareStarByCwIdList(cwIds);
        // 查询自我评星
        Map<String, Integer> oneselfStarMap = coursewareStarService.getOneselfStarByCwIdList(
            UserThreadContext.getUserId(), cwIds);
        for (CoursewareDetailClientDTO c : courseWareList) {
            String cwId = c.getId();
            ViewExamFeignDTO examInfo = examInfoMapByIds.get(c.getExamId());
            checkExamInfo(c, examInfo, userId);
            // 获取课件列表之后，获取讲师名称，用竖线进行拼接
            LecturerDetailDTO lecturer = lecturerByCwIds.get(cwId);
            if (lecturer != null) {
                c.setLecturerId(lecturer.getId());
                c.setLecturerName(lecturer.getName());
            }
            c.setProgress(Optional.ofNullable(cwIdToProgressMap.get(cwId)).orElse(0L).intValue());

            //完善数据各字段
            replenishParam(c, coursewareStarMap, oneselfStarMap);

            String fullName = userNameMapByIds.get(c.getCreateBy());
            c.setCreateBy(fullName);
            Integer commentNumber = commentMap.get(cwId);
            if (commentNumber != null) {
                c.setCommentNumber(commentNumber);
            }

            c.setCanDrag(c.getProgress() == ONE_HUNDRED ? 1 : 0);
            //课件是否包含题目
            c.setIsContainQuestion(courseQuestionMap.get(cwId) == null ? 0 : 1);
        }
        coursewareContentFactory.fillCoursewareContents(courseWareList);
        List<String> pptType = Arrays.asList(CWTypeEnum.PPT.getFileType());
        courseWareList.forEach(c -> {
            if (pptType.contains(c.getType().toLowerCase())) {
                Optional.ofNullable(
                        fileFeign.getFileByCategoryTypeAndIsAdjunct(c.getId(), FileBizType.CourseWareFile.name(), 1))
                    .ifPresent(mp3File -> c.setCoursewareMp3DTO(new CoursewareMp3DTO().setUrl(mp3File.getUrl())));
            }
        });
        dealWithChapter(query, courseWareList);
        // 关联考试课件已学处理
        courseWareWithExamLearnedHandler(userId, query.getId(), courseWareList);

        setCwDescriptions(courseWareList);
    }

    private void checkExamInfo(CoursewareDetailClientDTO c, ViewExamFeignDTO examInfo, String userId) {
        if (Optional.ofNullable(examInfo).isPresent()) {
            c.setExamName(examInfo.getExamName());
            c.setIsFinishExam(examFeign.getExamFinishStatus(userId, c.getExamId()));
        } else {
            // 配合前端展示，未查到考试则将id置空
            c.setExamId(null);
        }
    }

    private void setCwDescriptions(List<CoursewareDetailClientDTO> courseWareList) {
        AiBaseConfigDTO aiConfig = aiBaseConfigFeign.getAiConfig();
        int aiParaDescSwitch = Integer.parseInt(
            paraFeign.getParaValue(SystemConfigCodeEnum.AI_MAX_KB_SWITCH_DESC.getCode()));
        for (CoursewareDetailClientDTO coursewareDetailClientDTO : courseWareList) {
            if (!Objects.equals(aiConfig.getAiSwitch(), 1) || !Objects.equals(aiParaDescSwitch, 1)) {
                coursewareDetailClientDTO.setDescriptions("");
            }
        }
    }

    @Override
    public PageInfo<CourseStudyDetailDTO> courseStudyDetail(CourseStudyQuery courseStudyQuery) {
        dealWithQuery(courseStudyQuery);
        PageInfo<CourseStudyDetailDTO> pageInfo = PageMethod.startPage(courseStudyQuery.getPageNo(),
                courseStudyQuery.getPageSize())
            .doSelectPageInfo(() -> userCourseRecordService.getCourseStudyDetail(courseStudyQuery));
        List<String> userIds = pageInfo.getList().stream().map(CourseStudyDetailDTO::getUserId)
            .collect(Collectors.toList());
        Map<String, UserOrgDTO> userMap = orgFeign.getUserAndOrgMapByUserIds(userIds);
        Map<String, List<UserOrgDTO>> userOrgMap = userFeign.getUserOrgByParams(userIds, 0);
        //处理部门简称
        Set<String> orgIds = userMap.values().stream().map(UserOrgDTO::getOrgId)
            .collect(Collectors.toSet());
        Map<String, OrgShowDTO> orgShowDTOMap = orgFeign.getOrgShowDTO(orgIds);
        pageInfo.getList().forEach(csd -> {
            //未学课件数
            if (csd.getCourseWareCount() != null && csd.getLearnedCourseWareCount() != null) {
                csd.setRemainderCourseWareCount(csd.getCourseWareCount() - csd.getLearnedCourseWareCount());
            }
            UserOrgDTO userOrgDTO = userMap.get(csd.getUserId());
            if (null != userOrgDTO) {
                csd.setLoginName(userOrgDTO.getLoginName());
                csd.setFullName(userOrgDTO.getFullName());
                csd.setOrgName(userOrgDTO.getOrgName());
                csd.setOrgLevelPathName(userOrgDTO.getLevelPathName());
                csd.setJobName(userOrgDTO.getPostName());
                //处理部门简称
                OrgShowDTO orgShowDTO = orgShowDTOMap.get(userOrgDTO.getOrgId());
                if (null != orgShowDTO) {
                    csd.setOrgPath(orgShowDTO.getLevelPathName());
                }
            }
            dealWithOrg(csd, userOrgMap);
        });
        return pageInfo;
    }

    private static void dealWithOrg(CourseStudyDetailDTO csd, Map<String, List<UserOrgDTO>> userOrgMap) {
        //处理关联部门
        List<UserOrgDTO> userOrgs = userOrgMap.get(csd.getUserId());
        if (!CollectionUtils.isEmpty(userOrgs)) {
            String relateLevelPathName;
            Set<String> relateLevelPathNames = new HashSet<>();
            for (UserOrgDTO item : userOrgs) {
                String levelPathName = StringUtil.isEmpty(item.getEmployeeNo()) ? item.getLevelPathName()
                    : item.getLevelPathName() + "(" + item.getEmployeeNo() + ")";
                relateLevelPathNames.add(levelPathName);
            }
            relateLevelPathName = String.join(",", relateLevelPathNames);
            csd.setRelateLevelPathName(relateLevelPathName);
        }
    }

    private void dealWithQuery(CourseStudyQuery courseStudyQuery) {
        //用户id
        if (StringUtils.isNotBlank(courseStudyQuery.getUserIds())) {
            courseStudyQuery.setUserIdsVo(Arrays.asList(courseStudyQuery.getUserIds().split(TAG_ID_SEPARATOR)));
        }
        // 管理单位
        // TODO 根据组织搜索未实现
        String orgId = courseStudyQuery.getOrgId();
        if (StringUtils.isNotBlank(orgId)) {
            OrgDTO orgDTO = orgFeign.getById(orgId);
            if (orgDTO != null) {
                courseStudyQuery.setLevelPath(orgDTO.getLevelPath());
            }
        }
    }

    @Override
    public PageInfo<CoursewareStudyDetailDTO> coursewareStudyDetail(CoursewareStudyQuery coursewareStudyQuery) {
        //用户id
        if (StringUtils.isNotBlank(coursewareStudyQuery.getUserIds())) {
            coursewareStudyQuery.setUserIdsVo(Arrays.asList(coursewareStudyQuery.getUserIds().split(TAG_ID_SEPARATOR)));
        }
        // 管理单位
        // TODO 根据组织搜索未实现
        String orgId = coursewareStudyQuery.getOrgId();
        if (StringUtils.isNotBlank(orgId)) {
            OrgDTO orgDTO = orgFeign.getById(orgId);
            if (orgDTO != null) {
                coursewareStudyQuery.setLevelPath(orgDTO.getLevelPath());
            }
        }
        PageInfo<CoursewareStudyDetailDTO> pageInfo = PageMethod.startPage(coursewareStudyQuery.getPageNo(),
                coursewareStudyQuery.getPageSize())
            .doSelectPageInfo(() -> coursewareUserRecordService.getCoursewareStudyDetail(coursewareStudyQuery));
        List<String> userIds = pageInfo.getList().stream().map(CoursewareStudyDetailDTO::getUserId)
            .collect(Collectors.toList());
        Map<String, UserOrgDTO> userMap = orgFeign.getUserAndOrgMapByUserIds(userIds);
        List<String> examIds = pageInfo.getList().stream().map(CoursewareStudyDetailDTO::getExamId)
            .filter(StringUtils::isNotBlank).collect(Collectors.toList());
        Map<String, ViewExamFeignDTO> examMap = new HashMap<>();
        Map<String, OrgShowDTO> orgShowDTOMap = orgFeign.getOrgShowDTO(userFeign.getUserOrdIdByUserIds(userIds));
        if (!CollectionUtils.isEmpty(examIds)) {
            examMap = examFeign.getExamInfoMapByExamIds(examIds);
        }
        Map<String, ViewExamFeignDTO> finalExamMap = examMap;
        pageInfo.getList().forEach(csd -> {
            UserOrgDTO userOrgDTO = userMap.get(csd.getUserId());
            if (null != userOrgDTO) {
                csd.setLoginName(userOrgDTO.getLoginName());
                csd.setFullName(userOrgDTO.getFullName());
                csd.setOrgLevelPathName(userOrgDTO.getLevelPathName());
                csd.setJobName(userOrgDTO.getPostName());
                csd.setOrgName(userOrgDTO.getOrgName());
                Optional.ofNullable(orgShowDTOMap.get(userOrgDTO.getOrgId()))
                    .ifPresent(orgShowDTO -> csd.setOrgPath(orgShowDTO.getLevelPathName()));

            }
            // 及格分
            if (StringUtils.isNotBlank(csd.getExamId())) {
                Optional.ofNullable(finalExamMap.get(csd.getExamId())).ifPresent(exam -> {
                    csd.setPassScore(exam.getPassScore());
                    Optional.ofNullable(answerRecordFeign.getScoreByExamIdAndUserId(csd.getExamId(), csd.getUserId()))
                        .ifPresent(userScore -> {
                            csd.setUserScore(userScore);
                            csd.setPassState(csd.getUserScore().compareTo(csd.getPassScore()) >= 0
                                ? GeneralJudgeEnum.CONFIRM.getValue() : GeneralJudgeEnum.NEGATIVE.getValue());
                        });
                });
            }
        });
        return pageInfo;
    }

    private void dealWithChapter(CourseDetailClientQuery courseDetailClientQuery,
        List<CoursewareDetailClientDTO> courseWareList) {
        // 获取所有课程章节
        LambdaQueryWrapper<CourseChapter> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CourseChapter::getCourseId, courseDetailClientQuery.getId());
        List<CourseChapter> chapters = courseChapterService.list(queryWrapper);
        // 设置课程课件章节字段
        // 1、课程无章节 课件章节顺序chapterSortNo = -1；2、课程有章节 该课件没有选择章节 = 0
        setChapterParam(chapters, courseWareList);
    }

    private void setChapterParam(List<CourseChapter> chapters, List<CoursewareDetailClientDTO> courseWareList) {
        if (CollectionUtils.isEmpty(chapters)) {
            for (CoursewareDetailClientDTO courseware : courseWareList) {
                courseware.setChapterName("");
                courseware.setChapterId("");
                courseware.setChapterSortNo(-1);
            }
            return;
        }
        for (CourseChapter chapter : chapters) {
            for (CoursewareDetailClientDTO courseware : courseWareList) {
                // 课件管理章节ID
                String chapterId = courseware.getChapterId();
                if (StringUtils.isNotEmpty(chapterId) && chapterId.equals(chapter.getId())) {
                    courseware.setChapterName(chapter.getChapterName());
                    courseware.setChapterSortNo(chapter.getSortNo());
                }
            }
        }
    }

    private void replenishParam(CoursewareDetailClientDTO coursewareDetailClientDTO,
        Map<String, CoursewareStarDTO> coursewareStarMap, Map<String, Integer> oneselfStarMap) {

        coursewareDetailClientDTO.setIsComment(1);
        coursewareDetailClientDTO.setModel("1");
        coursewareDetailClientDTO.setOrientation("P");
        // 查询课件评星
        CoursewareStarDTO coursewareStar = coursewareStarMap.get(coursewareDetailClientDTO.getId());
        coursewareDetailClientDTO.setCommonStar(
            Optional.ofNullable(coursewareStar).map(CoursewareStarDTO::getCommonStar).orElse(new BigDecimal("0.0"))
                .toString());
        coursewareDetailClientDTO.setStarCount(
            Optional.ofNullable(coursewareStar).map(CoursewareStarDTO::getStarUserCount).orElse(0));
        // 查询自我评星
        Integer oneselfStar = oneselfStarMap.get(coursewareDetailClientDTO.getId());
        coursewareDetailClientDTO.setMyStar(oneselfStar == null ? 0 : oneselfStar);
        if (coursewareDetailClientDTO.getProgress() == null) {
            return;
        }
        if (coursewareDetailClientDTO.getProgress() == ONE_HUNDRED) {
            coursewareDetailClientDTO.setHistoryRecord(0);
        } else {
            coursewareDetailClientDTO.setHistoryRecord(
                coursewareDetailClientDTO.getProgress() * coursewareDetailClientDTO.getPlayTime() / ONE_HUNDRED);
        }
    }

    private void courseWareWithExamLearnedHandler(String userId, String courseId,
        List<CoursewareDetailClientDTO> courseWareList) {
        if (CollectionUtils.isEmpty(courseWareList)) {
            return;
        }
        // 符合条件课件
        List<CoursewareDetailClientDTO> finishCourseWares = new ArrayList<>();

        List<String> courseIds = courseWareList.stream().map(CoursewareDetailClientDTO::getId)
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(courseIds)) {
            return;
        }
        LambdaQueryWrapper<CoursewareUserRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CoursewareUserRecord::getCoursewareId, courseIds);
        queryWrapper.eq(CoursewareUserRecord::getUserId, userId);
        List<CoursewareUserRecord> list = coursewareUserRecordService.list(queryWrapper);
        Map<String, CoursewareUserRecord> recordMaps = list.stream()
            .collect(Collectors.toMap(CoursewareUserRecord::getCoursewareId, dto -> dto));
        dealWithCourseWare(courseWareList, recordMaps, finishCourseWares);

        // 课件已学处理
        if (!finishCourseWares.isEmpty()) {
            List<String> cwIds = finishCourseWares.stream().map(CoursewareDetailClientDTO::getId)
                .collect(Collectors.toList());
            Map<String, CourseInfoDTO> courseWareIdAndCourseMap = coursewareService.getCoursesByCourseWareIds(cwIds);
            for (CoursewareDetailClientDTO courseWare : finishCourseWares) {
                // 更新用户课件学习记录
                LambdaUpdateWrapper<CoursewareUserRecord> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(CoursewareUserRecord::getUserId, userId);
                updateWrapper.eq(CoursewareUserRecord::getCoursewareId, courseWare.getId());
                updateWrapper.eq(CoursewareUserRecord::getCourseId, courseWare.getCourseId());
                updateWrapper.set(CoursewareUserRecord::getIsLearned, courseWare.getIsLearned());
                updateWrapper.set(Objects.nonNull(courseWare.getFinishTime()), CoursewareUserRecord::getFinishTime,
                    courseWare.getFinishTime());
                coursewareUserRecordService.update(updateWrapper);

                // 修改已完成课件的积分情况
                Integer isTrain = Optional.ofNullable(courseWareIdAndCourseMap.get(courseWare.getId()))
                    .map(CourseInfoDTO::getIsTrain).orElse(null);

                mqProducer.sendMsg(new ExcitationMQEvent(
                    new ExcitationMQEventDTO(userId, ExcitationEventEnum.finishCourseWare.name(), courseWare.getId(),
                        ExcitationEventCategoryEnum.COURSE_WARE.getCode()).setTargetName(courseWare.getCwName())
                        .setIsTrain(isTrain)));

                mqProducer.sendMsg(new ExcitationMQEvent(
                    new ExcitationMQEventDTO(userId, ExcitationEventEnum.finishCourseWareByTime.name(),
                        courseWare.getId(),
                        ExcitationEventCategoryEnum.COURSE_WARE.getCode()).setTargetName(courseWare.getCwName())
                        .setIsTrain(isTrain)));
            }
        }
        // 课程已学处理
        LambdaQueryWrapper<CoursewareUserRecord> queryWrapper2 = new LambdaQueryWrapper<>();
        queryWrapper2.eq(CoursewareUserRecord::getUserId, userId);
        queryWrapper2.eq(CoursewareUserRecord::getCourseId, courseId);
        queryWrapper2.eq(CoursewareUserRecord::getIsLearned, 1);
        long learnedCount = coursewareUserRecordService.count(queryWrapper2);
        LambdaQueryWrapper<UserCourseRecord> userCourseRecordLambdaQuery = new LambdaQueryWrapper<>();
        userCourseRecordLambdaQuery.select(UserCourseRecord::getIsLearned);
        userCourseRecordLambdaQuery.eq(UserCourseRecord::getUserId, userId);
        userCourseRecordLambdaQuery.eq(UserCourseRecord::getCourseId, courseId);
        UserCourseRecord userCourseRecord = userCourseRecordService.getOne(userCourseRecordLambdaQuery);
        if (learnedCount >= courseWareList.size() && userCourseRecord.getIsLearned() == 0) {
            // 课程已学状态更新
            LambdaUpdateWrapper<UserCourseRecord> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(UserCourseRecord::getIsLearned, 1);
            updateWrapper.set(UserCourseRecord::getFinishTime, new Date());
            updateWrapper.eq(UserCourseRecord::getUserId, userId);
            updateWrapper.eq(UserCourseRecord::getCourseId, courseId);
            updateWrapper.eq(UserCourseRecord::getIsLearned, 0);
            userCourseRecordService.update(updateWrapper);
            // 课程学完更新积分
            // 发送消息[课程完成]
            String courseName = null;
            Integer isTrain = null;
            Course courseInfo = baseMapper.selectById(courseId);
            if (null != courseInfo) {
                courseName = courseInfo.getCourseName();
                isTrain = courseInfo.getIsTrain();
            }
            mqProducer.sendMsg(new ExcitationMQEvent(
                new ExcitationMQEventDTO(UserThreadContext.getUserId(), ExcitationEventEnum.finishCourse.name(),
                    courseId, ExcitationEventCategoryEnum.COURSE.getCode()).setTargetName(courseName)
                    .setIsTrain(isTrain)));
            //发送完成课程事件
            mqProducer.sendMsg(
                new CourseFinishEvent(courseId, UserThreadContext.getUserId(), UserThreadContext.getUserId()));
        }
    }

    private static void dealWithCourseWare(List<CoursewareDetailClientDTO> courseWareList,
        Map<String, CoursewareUserRecord> recordMaps, List<CoursewareDetailClientDTO> finishCourseWares) {
        for (CoursewareDetailClientDTO courseware : courseWareList) {
            CoursewareUserRecord coursewareUserRecord = recordMaps.get(courseware.getId());
            if (Optional.ofNullable(coursewareUserRecord).isPresent()) {
                courseware.setIsLearned(coursewareUserRecord.getIsLearned());
                courseware.setLearnedTime(coursewareUserRecord.getDuration().intValue());
            } else {
                courseware.setIsLearned(0);
                courseware.setLearnedTime(0);
            }
            if (StringUtils.isNotEmpty(courseware.getExamId()) && courseware.getIsFinishExam() > 0
                && courseware.getIsLearned() < 1 && courseware.getLearnedTime() >= courseware.getSetPlayTime()) {
                courseware.setIsLearned(1);
                if (coursewareUserRecord != null) {
                    courseware.setFinishTime(coursewareUserRecord.getEndTime());
                }
                finishCourseWares.add(courseware);
            }
        }
    }

    /**
     * 完善课程信息
     *
     * @param courseDetail
     */
    private void replenishParam(CourseDetailClientDTO courseDetail, String userId) {
        String courseId = courseDetail.getId();
        courseDetail.setImage(fileFeign.getImageUrl(courseId, COURSE_IMG_ICON));

        courseDetail.setModel("0");

        List<MarkInfoDTO> courseMarkInfoByCourseId = getCourseMarkInfoByCourseId(courseId);
        courseDetail.setMarkInfoList(courseMarkInfoByCourseId);

        if (null != courseDetail.getPublishBy()) {
            courseDetail.setPublishBy(userFeign.getUserFullNameById(courseDetail.getPublishBy()));
        }
        if (null == courseDetail.getIsLearned()) {
            courseDetail.setIsLearned(0);
        }
        courseDetail.setIsShowExcitation(0);

        Long vote = courseVoteService.lambdaQuery()
            .eq(CourseVote::getCourseId, courseId)
            .eq(CourseVote::getCreateBy, userId)
            .ge(CourseVote::getVoteCount, 0)
            .count();
        courseDetail.setIsVote(vote.intValue());

        Long favorite = courseFavorateService.lambdaQuery()
            .eq(CourseFavorate::getCourseId, courseId)
            .eq(CourseFavorate::getCreateBy, userId)
            .count();
        courseDetail.setIsFavorite(favorite.intValue());

        UserCourseRecord userCourseRecord = userCourseRecordService.lambdaQuery()
            .select(UserCourseRecord::getIsLearned)
            .eq(UserCourseRecord::getCourseId, courseId)
            .eq(UserCourseRecord::getUserId, userId)
            .eq(UserCourseRecord::getIsDel, 0)
            .one();
        courseDetail.setIsLearned(userCourseRecord == null ? 0 : userCourseRecord.getIsLearned());

        // 学习计划
        Optional.ofNullable(
                courseStudyPlanService.getPlanByUserAndCourseId(UserThreadContext.getUserId(),
                    courseDetail.getId()))
            .ifPresent(courseStudyPlan -> courseDetail.setCourseStudyPlanId(courseStudyPlan.getId()));
        // 用户是否是上级
        courseDetail.setIsSuperiors(userFeign.getIsSuperiors(UserThreadContext.getUserId()));

        //课程认证讲师信息
        List<LecturerCourseAuthFeignDTO> authLecturerList = getAuthenticationLecturer(
            courseDetail.getId());
        courseDetail.setAuthLecturerList(authLecturerList);

        CourseRefDify courseRefDify = courseRefDifyService.lambdaQuery()
            .eq(CourseRefDify::getCourseId, courseDetail.getId()).one();
        if (courseRefDify != null) {
            courseDetail.setChatUrl(difyApiClient.getChatUrl(courseRefDify.getDifyAppId()));
        }
    }

    private Map<String, List<MarkInfoDTO>> getCourseMarkInfo(List<String> courseIdList) {
        if (CollectionUtils.isEmpty(courseIdList)) {
            return new HashMap<>();
        }
        LambdaQueryWrapper<CourseTag> courseTagQuery = new LambdaQueryWrapper<>();
        courseTagQuery.select(CourseTag::getTagId, CourseTag::getCourseId);
        courseTagQuery.in(CourseTag::getCourseId, courseIdList);
        List<CourseTag> courseTags = courseTagService.list(courseTagQuery);
        if (CollectionUtils.isEmpty(courseTags)) {
            return new HashMap<>();
        }
        List<MarkInfoDTO> list = tagService.getTagInfoByIds(courseTags);
        if (CollectionUtils.isEmpty(list)) {
            return new HashMap<>();
        }
        return list.stream().collect(Collectors.groupingBy(MarkInfoDTO::getCourseId));
    }

    private List<MarkInfoDTO> getCourseMarkInfoByCourseId(String courseId) {
        String language = UserThreadContext.getAcceptLanguage();
        List<CourseTag> courseTagList = courseTagService.lambdaQuery()
            .select(CourseTag::getCourseId, CourseTag::getCreateType, CourseTag::getTagId)
            .eq(CourseTag::getCourseId, courseId).list();
        if (CollectionUtils.isEmpty(courseTagList)) {
            return new ArrayList<>();
        }
        Set<String> tagIdList = courseTagList.stream().map(CourseTag::getTagId).collect(Collectors.toSet());
        SysTagBaseQuery sysTagBaseQuery = new SysTagBaseQuery();
        sysTagBaseQuery.setTagIdSet(tagIdList);
        sysTagBaseQuery.setIsAvailable(AvailableEnum.AVAILABLE.getValue());
        sysTagBaseQuery.setIsShow(CommonConstants.IS_SHOW_YES);
        sysTagBaseQuery.setIsDel(DelEnum.NOT_DELETE.getValue());
        List<SysTagBaseDTO> sysTagBaseDTOs = sysTagFeign.selectSysTagBaseByIds(sysTagBaseQuery);
        List<MultiLangMessage> multiLangMessageList = new ArrayList<>();
        if (StringUtils.isNotEmpty(language)) {
            multiLangMessageList = multiLangMessageService.lambdaQuery()
                .select(MultiLangMessage::getContent, MultiLangMessage::getResourceId)
                .eq(MultiLangMessage::getLang, language)
                .eq(MultiLangMessage::getResourceType, "courseTag")
                .eq(MultiLangMessage::getProperty, "tagName")
                .in(MultiLangMessage::getResourceId, tagIdList)
                .list();
        }
        List<MarkInfoDTO> markInfoDTOList = new ArrayList<>();
        makeMarkInfo(courseTagList, sysTagBaseDTOs, multiLangMessageList, markInfoDTOList);
        return markInfoDTOList;
    }

    private static void makeMarkInfo(List<CourseTag> courseTagList, List<SysTagBaseDTO> sysTagBaseDTOs,
        List<MultiLangMessage> multiLangMessageList, List<MarkInfoDTO> markInfoDTOList) {
        for (CourseTag courseTag : courseTagList) {
            MarkInfoDTO result = new MarkInfoDTO();
            result.setCourseId(courseTag.getCourseId());
            result.setCreateType(courseTag.getCreateType());
            result.setMarkId(courseTag.getTagId());
            if (CollectionUtils.isEmpty(multiLangMessageList)) {
                for (SysTagBaseDTO sysTagBaseDTO : sysTagBaseDTOs) {
                    if (sysTagBaseDTO.getId().equals(courseTag.getTagId())) {
                        result.setMarkContent(sysTagBaseDTO.getTagName());
                    }
                }
                markInfoDTOList.add(result);
                continue;
            }
            for (MultiLangMessage multiLangMessage : multiLangMessageList) {
                if (multiLangMessage.getResourceId().equals(courseTag.getTagId())) {
                    result.setMarkContent(multiLangMessage.getContent());
                }
            }
            markInfoDTOList.add(result);
        }
    }

    private Map<String, List<MarkInfoDTO>> getCourseMarkInfoByCourseIdList(Collection<String> courseIds, boolean flag) {
        String language = UserThreadContext.getAcceptLanguage();

        List<MarkInfoDTO> markInfoDTOList = courseTagMapper.getCourseMarkInfoByCourseId(courseIds, flag, language);
        if (CollectionUtils.isEmpty(markInfoDTOList)) {
            return new HashMap<>();
        }
        return markInfoDTOList.stream().collect(Collectors.groupingBy(MarkInfoDTO::getCourseId));
    }

    private void verifyCourseType(CourseDetailClientQuery courseDetailClientQuery, CourseDetailClientDTO courseDetail) {
        String courseType = courseDetail.getCourseType();
        String type = courseDetailClientQuery.getType();
        if (StringUtils.isNotEmpty(courseType) && StringUtils.isNotEmpty(type)) {
            // 网页端
            if (COURSE_TYPE_E.equals(courseType) && !ClientTypeEnum.CLIECNT_TYPE_PC.getName()
                .equals(UserThreadContext.getOs())) {
                throw new BusinessException(ErrorNoEnum.ERR_NO_POWER);
            }
            // 手机端
            if (COURSE_TYPE_M.equals(courseType) && ClientTypeEnum.CLIECNT_TYPE_PC.getName()
                .equals(UserThreadContext.getOs())) {
                throw new BusinessException(ErrorNoEnum.ERR_NO_POWER);
            }
        }
    }

    /**
     * 验证该课程是否是岗位课程，拥有该岗位角色的用户可以直接访问课程，跳过下发校验
     *
     * @param courseDetailClientQuery
     * @return
     */
    private boolean checkPostCourse(CourseDetailClientQuery courseDetailClientQuery) {
        courseDetailClientQuery.setCategoryType(POSITION_CATE);
        List<UserIdentityFeignDTO> userIdentityList = userIdentityFeign
            .getUserIdentityByCategory(courseDetailClientQuery.getCurrentUserId(),
                IdentityCategoryEnum.POST.getCategoryId());
        Set<String> identityIds = userIdentityList.stream().map(UserIdentityFeignDTO::getIdentityId)
            .collect(Collectors.toSet());

        LambdaQueryWrapper<CourseCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CourseCategory::getCourseId, courseDetailClientQuery.getId());
        queryWrapper.eq(CourseCategory::getCategoryType, courseDetailClientQuery.getCategoryType());
        queryWrapper.in(CourseCategory::getCategoryId, identityIds);
        long isPositionCate = courseCategoryService.count(queryWrapper);
        return isPositionCate > 0;
    }


    /**
     * 转换order order --> new（最新发布，默认）、like（最多喜欢）、view（最多浏览）
     *
     * @param order 排序方式
     * @return 排序方式
     */
    private String translateOrderColumn(String order) {
        if (order == null || "".equals(order.trim())) {
            return COURSE_ORDER_PUBLISH_TIME + DESC;
        }

        if (LIKE.equals(order.trim())) {
            return COURSE_ORDER_FAVORITE_NUMBER + DESC;
        }

        if (VIEW.equals(order.trim())) {
            return CLICK_NUMBER + DESC;
        }

        if (TIME_DOWN.equals(order.trim())) {
            return COURSE_ORDER_PUBLISH_TIME + DESC;
        }

        if (TIME_UP.equals(order.trim())) {
            return COURSE_ORDER_PUBLISH_TIME + ASC;
        }

        return COURSE_ORDER_PUBLISH_TIME + DESC;
    }


    /**
     * 合并课件到课程
     *
     * @param course
     */
    private void saveMergeCourseWare(SaveCourseDTO course) {
        String[] idArray = course.getCourseWareIdList().split(",");
        List<String> idList = Arrays.asList(idArray);
        Courseware courseware;
        StringBuilder libName = new StringBuilder();
        String userId = UserThreadContext.getUserId();
        for (String courseWareId : idList) {
            // 修改课件文件关联类型
            fileFeign.editFileCategoryType(courseWareId, FileBizType.CourseWareFile.name());
            String cwId = newId();
            //入库资源库
            courseware = coursewareMapper.selectById(courseWareId);
            String authorId = Optional.ofNullable(coursewarePackageMapper.selectById(courseWareId))
                .map(CoursewarePackage::getCreateBy).orElse("");
            libName.append(courseware.getCwName());
            courseware.setId(cwId);
            courseware.setCreateBy(userId);
            courseware.setUploadBy(courseWareId);
            courseware.setIsSource(9);
            courseware.setCourseId(course.getId());
            courseware.setIsAvailable(1);
            courseware.setOrgId(UserThreadContext.getOrgId());
            //设置课件的作者为上传课件的学员
            String authorName = userFeign.getUserFullNameById(authorId);
            courseware.setCwAuthor(authorName);
            coursewareMapper.insert(courseware);
            //复制课程的下发范围给新加入的课件资源库
            coursewareLibViewLimitComponent.handleNewViewLimit(course.getProgrammeId(), cwId);
            //为入库的课件复制文件
            fileFeign.copySameBizFile(courseWareId, cwId, FileBizType.CourseWareFile.name());
            // 登记合并记录
            coursewarePackageMergeRecordService.save(
                new CoursewarePackageMergeRecord().setId(newId()).setPackageId(courseWareId).setCwId(cwId));
        }

        // 保存入库操作记录
        baseLibraryRecordService.saveOperationRecord(libName.toString(), idList.size(), HandleTypeEnum.TP_ADD_RECORD,
            LibraryTypeEnum.LIB_COURSE);
    }

    @Override
    @Transactional
    public LikeDTO ratingCourse(RatingCourseDTO dto) {
        String courseId = dto.getCourseId();
        if (StringUtils.isBlank(courseId)) {
            throw new BusinessException(ErrorNoEnum.ERR_PARAMS);
        }
        String userId = UserThreadContext.getUserId();
        CourseVote previousVote = courseVoteService.getOne(
            new LambdaQueryWrapper<CourseVote>().eq(CourseVote::getCategoryType, VoteType.CourseVote.getKey())
                .eq(CourseVote::getCreateBy, userId).eq(CourseVote::getCourseId, courseId));
        if (previousVote == null) {
            //投票记录为空，需要触发激励
            CourseVote cvt = new CourseVote();
            cvt.setId(newId());
            cvt.setCourseId(courseId);
            cvt.setCategoryType(VoteType.CourseVote.getKey());
            cvt.setVoteCount(1);
            courseVoteService.save(cvt);

            //累加点赞数
            courseMapper.addCourseVoteNumber(courseId);

            // 发送消息[点赞课程]
            Course course = courseMapper.selectById(courseId);
            mqProducer.sendMsg(new ExcitationMQEvent(
                new ExcitationMQEventDTO(userId, ExcitationEventEnum.courseVote.name(), courseId,
                    ExcitationEventCategoryEnum.COURSE.getCode()).setTargetName(course.getCourseName())
                    .setBizType(dto.getBizType()).setBizId(dto.getBizId()).setIsExchange(dto.getIsExchange())));
        } else {
            //存在投票记录，判断是否投过票
            if (previousVote.getVoteCount() > 0) {
                previousVote.setVoteCount(0);
                //减少总点赞数
                courseMapper.reduceCourseVoteNumber(courseId);
            } else {
                previousVote.setVoteCount(1);
                //增加总点赞数
                courseMapper.addCourseVoteNumber(courseId);
            }
            courseVoteService.updateById(previousVote);
        }

        long courseVoteCount = courseMapper.selectById(courseId).getVoteNumber();

        return new LikeDTO((int) courseVoteCount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public GradeCourseStarDTO gradeCourseStar(SaveGradeCourseStarDTO starDTO) {
        if (starDTO.getMyStar() > FIVE_STARS || starDTO.getMyStar() < ONE_STARS) {
            throw new BusinessException(ErrorNoEnum.ERR_PARAMS);
        }
        String userId = UserThreadContext.getUserId();

        //课程评星
        if (TYPE_COURSE.equals(starDTO.getFlag())) {
            long starCount = courseStarService.count(
                new LambdaQueryWrapper<CourseStar>().eq(CourseStar::getCreateBy, userId)
                    .eq(CourseStar::getCourseId, starDTO.getId()));
            if (starCount > 0) {
                throw new BusinessException(ErrorNoEnum.ERR_REPEAT_OPERATE);
            }

            CourseStar star = new CourseStar();
            star.setId(newId());
            star.setCourseId(starDTO.getId());
            star.setStarCount(starDTO.getMyStar());
            courseStarService.save(star);

            // 发送消息[进行课程评星]
            Course course = courseMapper.selectById(starDTO.getId());
            mqProducer.sendMsg(new ExcitationMQEvent(
                new ExcitationMQEventDTO(userId, ExcitationEventEnum.courseStar.name(), starDTO.getId(),
                    ExcitationEventCategoryEnum.COURSE.getCode()).setTargetName(course.getCourseName())
                    .setBizId(starDTO.getBizId()).setBizType(starDTO.getBizType())
                    .setIsExchange(starDTO.getIsExchange())));

        } else if (TYPE_COURSEWARE.equals(starDTO.getFlag())) {

            long starCount = coursewareStarService.count(
                new LambdaQueryWrapper<CoursewareStar>().eq(CoursewareStar::getCreateBy, userId)
                    .eq(CoursewareStar::getCwId, starDTO.getId()));
            if (starCount > 0) {
                throw new BusinessException(ErrorNoEnum.ERR_REPEAT_OPERATE);
            }

            CoursewareStar star = new CoursewareStar();
            star.setId(newId());
            star.setCwId(starDTO.getId());
            star.setStarCount(starDTO.getMyStar());
            coursewareStarService.save(star);
            // 冗余更新课件综合星级
            mqProducer.sendMsg(
                new ResourceInteractEvent(ResourceInteractEventRoutingKeyConstants.COURSE_WARE_COMMON_STAR_EVENT,
                    starDTO.getId()));
            // 课件评星更新课件活跃时间
            coursewareService.update(new LambdaUpdateWrapper<Courseware>().set(Courseware::getActiveTime, new Date())
                .eq(Courseware::getId, starDTO.getId()));

            // 发送消息[进行课件评星]
            Courseware courseware = coursewareMapper.selectById(starDTO.getId());
            mqProducer.sendMsg(new ExcitationMQEvent(
                new ExcitationMQEventDTO(userId, ExcitationEventEnum.starCourseWare.name(), starDTO.getId(),
                    ExcitationEventCategoryEnum.COURSE_WARE.getCode()).setTargetName(courseware.getCwName())
                    .setBizId(starDTO.getBizId()).setBizType(starDTO.getBizType())
                    .setIsExchange(starDTO.getIsExchange())));
        } else {
            throw new BusinessException(ErrorNoEnum.ERR_PARAMS);
        }
        BigDecimal commonStar = baseMapper.getCourseCommonStar(starDTO);
        GradeCourseStarDTO gradeCourseStarDTO = new GradeCourseStarDTO();
        gradeCourseStarDTO.setCommonStar(commonStar.toString());
        return gradeCourseStarDTO;
    }

    @Override
    public PageInfo<StudyRecordDTO> getViewCourseList(BaseEntity baseEntity) {
        baseEntity.setCurrentUserId(UserThreadContext.getUserId());
        PageInfo<StudyRecordDTO> pageInfo = PageMethod.startPage(baseEntity.getPageNo(), baseEntity.getPageSize())
            .doSelectPageInfo(() -> baseMapper.getViewCourseList(baseEntity));
        List<StudyRecordDTO> list = pageInfo.getList();
        if (CollectionUtils.isEmpty(list)) {
            return pageInfo;
        }
        List<String> idList = list.stream().map(StudyRecordDTO::getId).collect(Collectors.toList());
        Map<String, List<MarkInfoDTO>> courseMarkInfoMap = getCourseMarkInfo(idList);
        list.forEach(s -> {
            String id = s.getId();
            s.setModel("0");
            s.setMarkInfoList(
                CollectionUtils.isEmpty(courseMarkInfoMap.get(id)) ? new ArrayList<>() : courseMarkInfoMap.get(id));
        });
        return pageInfo;
    }

    @Override
    public PageInfo<com.wunding.learn.course.service.client.dto.CourseListDTO> getCourseList(
        CourseListQuery courseListQuery) {
        if (ClientTypeEnum.CLIECNT_TYPE_PC.getName().equals(UserThreadContext.getOs())) {
            // h5和pc端
            courseListQuery.setCourseType(COURSE_TYPE_E);
        } else {
            // 课程类别 客户端APP课程（M和EM）
            courseListQuery.setCourseType(COURSE_TYPE_M);
        }
        log.info("用户操作系统：{}", UserThreadContext.getOs());
        courseListQuery.setOrder(this.translateOrderColumn(courseListQuery.getOrder()));
        courseListQuery.setLearnStatusList(
            TranslateUtil.translateBySplit(courseListQuery.getLearnStatus(), String.class));

        courseListQuery.setCurrentUserId(UserThreadContext.getUserId());

        PageInfo<com.wunding.learn.course.service.client.dto.CourseListDTO> pageInfo = PageMethod.startPage(
                courseListQuery.getPageNo(), courseListQuery.getPageSize(), courseListQuery.isCount())
            .doSelectPageInfo(() -> baseMapper.getCourseList(courseListQuery));

        //处理课程返回列表
        handleCourseListDTO(pageInfo);

        return pageInfo;
    }


    @Override
    public void handleCourseListDTO(PageInfo<com.wunding.learn.course.service.client.dto.CourseListDTO> pageInfo) {
        pageInfo.setIsLastPage(pageInfo.getPageSize() != pageInfo.getList().size());
        List<com.wunding.learn.course.service.client.dto.CourseListDTO> list = pageInfo.getList();
//        // 优化 clickNumber  voteNumber isVote learnStatus
        if (!CollectionUtils.isEmpty(list)) {
            List<String> courseIds = list.stream().map(com.wunding.learn.course.service.client.dto.CourseListDTO::getId)
                .collect(Collectors.toList());
            String userId = UserThreadContext.getUserId();
            Map<String, Integer> isVoteByCourseIdsAndCurrentUserId = getIsVoteByCourseIdsAndCurrentUserId(courseIds,
                userId);
            Map<String, String> imgMap = fileFeign.getImageUrlsByIds(courseIds, COURSE_IMG_ICON);
            for (com.wunding.learn.course.service.client.dto.CourseListDTO dto : list) {
                Integer isVote = isVoteByCourseIdsAndCurrentUserId.get(dto.getId());
                if (isVote != null) {
                    dto.setIsVote(isVote);
                }
                String s = imgMap.get(dto.getId());
                if (StringUtils.isNotBlank(s)) {
                    dto.setImage(s);
                }
                //填充 兑换学习消耗激励类型
                if (StringUtils.isNotBlank(dto.getConsumeExcitationType())) {
                    dto.setConsumeExcitationTypeName(ExcitationTypeEnum.getNameByCode(dto.getConsumeExcitationType()));
                }
            }
            // 分批获取 clickNumber  voteNumber isVote learnStatus
        }
    }

    @Override
    public PageInfo<RelatedCourseDTO> getRelatedCourse(RelatedCourseQuery relatedCourseQuery) {
        LambdaQueryWrapper<CourseTag> courseTagQuery = new LambdaQueryWrapper<>();
        courseTagQuery.select(CourseTag::getTagId);
        courseTagQuery.eq(CourseTag::getCourseId, relatedCourseQuery.getId());
        List<CourseTag> courseTagList = courseTagService.list(courseTagQuery);
        // 无标签则无可推荐课件
        if (CollectionUtils.isEmpty(courseTagList)) {
            return new PageInfo<>();
        }
        relatedCourseQuery.setTagIds(courseTagList.stream().map(CourseTag::getTagId).collect(Collectors.toList()));
        relatedCourseQuery.setCurrentUserId(UserThreadContext.getUserId());
        if (ClientTypeEnum.CLIECNT_TYPE_PC.getName().equals(UserThreadContext.getOs())) {
            // h5和pc端
            relatedCourseQuery.setCourseType(COURSE_TYPE_E);
        } else {
            // 课程类别 客户端APP课程（M和EM）
            relatedCourseQuery.setCourseType(COURSE_TYPE_M);
        }
        PageInfo<RelatedCourseDTO> pageInfo = PageMethod.startPage(relatedCourseQuery.getPageNo(),
            relatedCourseQuery.getPageSize()).doSelectPageInfo(() -> baseMapper.getRelatedCourse(relatedCourseQuery));
        List<RelatedCourseDTO> list = pageInfo.getList();
        if (!CollectionUtils.isEmpty(list)) {
            Set<String> courseIdSet = list.stream().map(RelatedCourseDTO::getId).collect(Collectors.toSet());
            Map<String, NamePath> imgMap = fileFeign.getImageFileNamePathMapByBizIds(courseIdSet,
                COURSE_IMG_ICON);
            pageInfo.getList()
                .forEach(r -> Optional.ofNullable(imgMap.get(r.getId())).ifPresent(img -> r.setImage(img.getUrl())));
        }
        return pageInfo;
    }

    @Override
    public PageInfo<MyCourseDTO> getMyCourse(MyCourseQuery myCourseQuery) {
        if (ClientTypeEnum.CLIECNT_TYPE_PC.getName().equals(UserThreadContext.getOs())) {
            // h5和pc端
            myCourseQuery.setCourseType(COURSE_TYPE_E);
        } else {
            // 课程类别 客户端APP课程（M和EM）
            myCourseQuery.setCourseType(COURSE_TYPE_M);
        }
        myCourseQuery.setCurrentUserId(UserThreadContext.getUserId());
        PageInfo<MyCourseDTO> pageInfo;
        if (null != myCourseQuery.getStudyStatus() && myCourseQuery.getStudyStatus().equals(1)) {
            pageInfo = PageMethod.startPage(myCourseQuery.getPageNo(), myCourseQuery.getPageSize())
                .doSelectPageInfo(() -> baseMapper.getMyFinishCourse(myCourseQuery));
        } else {
            pageInfo = PageMethod.startPage(myCourseQuery.getPageNo(), myCourseQuery.getPageSize())
                .doSelectPageInfo(() -> baseMapper.getMyNoFinishCourse(myCourseQuery));
        }
        List<String> idList = pageInfo.getList().stream().map(MyCourseDTO::getId).collect(Collectors.toList());

        Map<String, String> imgMap = fileFeign.getImageUrlsByIds(idList, COURSE_IMG_ICON);

        Map<String, List<MarkInfoDTO>> courseMarkInfoMap = getCourseMarkInfo(idList);

        pageInfo.getList().forEach(c -> {
            String id = c.getId();
            Optional.ofNullable(imgMap.get(id)).ifPresent(c::setImage);
            c.setMarkInfoList(
                CollectionUtils.isEmpty(courseMarkInfoMap.get(id)) ? new ArrayList<>() : courseMarkInfoMap.get(id));

            //填充 兑换学习消耗激励类型
            if (StringUtils.isNotBlank(c.getConsumeExcitationType())) {
                c.setConsumeExcitationTypeName(ExcitationTypeEnum.getNameByCode(c.getConsumeExcitationType()));
            }
        });
        return pageInfo;
    }

    @Override
    public PageInfo<RecommendCourseDTO> getRecommendCourse(RecommendCourseQuery recommendCourseQuery) {
        if (ClientTypeEnum.CLIECNT_TYPE_PC.getName().equals(UserThreadContext.getOs())) {
            // h5和pc端
            recommendCourseQuery.setCourseType(COURSE_TYPE_E);
        } else {
            // 课程类别 客户端APP课程（M和EM）
            recommendCourseQuery.setCourseType(COURSE_TYPE_M);
        }
        recommendCourseQuery.setCurrentUserId(UserThreadContext.getUserId());
        PageInfo<RecommendCourseDTO> pageInfo = PageMethod.startPage(recommendCourseQuery.getPageNo(),
                recommendCourseQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.getRecommendCourse(recommendCourseQuery));
        List<String> idList = pageInfo.getList().stream().map(RecommendCourseDTO::getId).collect(Collectors.toList());
        Map<String, String> imgMap = fileFeign.getImageUrlsByIds(idList, COURSE_IMG_ICON);
        Map<String, List<MarkInfoDTO>> courseMarkInfoMap = getCourseMarkInfo(idList);
        pageInfo.getList().forEach(c -> {
            String id = c.getId();
            Optional.ofNullable(imgMap.get(id)).ifPresent(c::setImage);
            c.setMarkInfoList(
                CollectionUtils.isEmpty(courseMarkInfoMap.get(id)) ? new ArrayList<>() : courseMarkInfoMap.get(id));
            //填充 兑换学习消耗激励类型
            if (StringUtils.isNotBlank(c.getConsumeExcitationType())) {
                c.setConsumeExcitationTypeName(ExcitationTypeEnum.getNameByCode(c.getConsumeExcitationType()));
            }
        });
        return pageInfo;
    }

    @Override
    public Integer getCourseLearnNum(String userId, Collection<String> courseIds) {
        return baseMapper.getCourseLearnNum(userId, courseIds);
    }

    @Override
    public CerDitchDTO getDitch(String contentId) {
        return baseMapper.getDitch(contentId);
    }

    @Override
    public PageInfo<SubordinateCourseDTO> subordinateCourseList(SubordinateCourseQuery subordinateCourseQuery) {
        UserDTO user = userFeign.getUserById(subordinateCourseQuery.getUserId());
        if (Optional.ofNullable(user).isEmpty()) {
            throw new BusinessException(UserErrorNoEnum.USER_NOT_EXISTS);
        }

        PageInfo<SubordinateCourseDTO> pageInfo = PageMethod.startPage(subordinateCourseQuery.getPageNo(),
                subordinateCourseQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.subordinateCourseList(subordinateCourseQuery));
        Set<String> courseIdSet = pageInfo.getList().stream().map(SubordinateCourseDTO::getId)
            .filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        Map<String, String> imageMap = fileFeign.getImageUrlsByIds(courseIdSet, ImageBizType.CourseImgIcon.name());
        for (SubordinateCourseDTO dto : pageInfo.getList()) {
            Optional.ofNullable(imageMap.get(dto.getId())).ifPresent(dto::setImage);
            dto.setLearningTime(DateUtil.convertTimeRules2(dto.getLearnedTime()));
            //填充 兑换学习消耗激励类型
            if (StringUtils.isNotBlank(dto.getConsumeExcitationType())) {
                dto.setConsumeExcitationTypeName(ExcitationTypeEnum.getNameByCode(dto.getConsumeExcitationType()));
            }
            if (dto.getIsLearned() == 1) {
                dto.setProgress("100%");
            } else if (dto.getCwCount() > 0 && dto.getIsLearnedCwCount() > 0) {
                NumberFormat numberFormat = NumberFormat.getInstance();
                numberFormat.setMaximumFractionDigits(0);
                numberFormat.setRoundingMode(RoundingMode.DOWN);
                String progress = numberFormat.format(
                    (float) dto.getIsLearnedCwCount() / (float) dto.getCwCount() * 100);
                dto.setProgress(progress.concat("%"));
            } else {
                dto.setProgress("0%");
            }
        }
        return pageInfo;
    }

    @Override
    public void autoPublishCourse() {
        try {
            log.info("---定时发布课程开始----");
            List<Course> autoPublishCourseList = baseMapper.getAutoPublishCourseList();
            List<ResourceStatusDTO> list = new ArrayList<>();
            ResourceStatusDTO dto;
            for (Course course : autoPublishCourseList) {
                dto = new ResourceStatusDTO();
                BeanUtils.copyProperties(course, dto);
                dto.setResourceId(course.getId());
                dto.setResourceType(ResourceChangeEnum.course.getValue());
                dto.setIsPublish(PublishStatusEnum.IS_PUBLISH.getValue());
                list.add(dto);
            }
            // 发送资源修改信息
            ResourceChangeEvent event = new ResourceChangeEvent(null, null, null, null);
            event.setResourceStatusList(list);
            mqProducer.sendMsg(event);
            baseMapper.autoPublishCourse();
            log.info("---定时发布课程结束----");
        } catch (Exception e) {
            log.error("---定时发布课程失败----");
        }
    }

    @Override
    public void exportData(CourseQuery courseQuery) {

        IExportDataDTO exportDataDTO = new IExportDataDTO() {
            @Override
            public List getData(Integer pageNo, Integer pageSize) {
                ICourseService courseService = SpringUtil.getBean(COURSE_SERVICE, ICourseService.class);
                courseQuery.setExport(true);
                courseQuery.setPageNo(pageNo);
                courseQuery.setPageSize(pageSize);
                PageInfo<CourseListDTO> courseListByPage = courseService.findCourseListByPage(courseQuery);
                List<Map<String, Object>> courseListExportDTOS = new ArrayList<>();
                for (CourseListDTO courseListDTO : courseListByPage.getList()) {
                    Map<String, Object> beanMap = JsonUtil.parseObjectToMap(courseListDTO);
                    courseListExportDTOS.add(beanMap);
                }
                return courseListExportDTOS;
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.Course;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.Course.getType();
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public List<String> getInvalidCourseId(Collection<String> courseIdList) {
        return baseMapper.getInvalidCourseId(courseIdList);
    }

    @Override
    public void courseLearnExportData(CourseLearnQuery courseLearnQuery) {
        Locale locale = new Locale(
            StringUtils.isNotBlank(UserThreadContext.getAcceptLanguage()) ? UserThreadContext.getAcceptLanguage()
                : "zh");
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<ICourseService, CourseLearnDetailDTO>(
            courseLearnQuery) {

            @Override
            protected ICourseService getBean() {
                return SpringUtil.getBean(COURSE_SERVICE, ICourseService.class);
            }

            @Override
            protected PageInfo<CourseLearnDetailDTO> getPageInfo() {
                if (StringUtils.isNotBlank(UserThreadContext.getAcceptLanguage())) {
                    LocaleContextHolder.setLocale(locale);
                }
                return getBean().getUserDetailData((CourseLearnQuery) queryDTO);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.CourseLearn;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.CourseLearn.getType();
            }

            @Override
            protected void afterCreateBeanMap(Map<String, Object> map) {
                Object learnState = map.get(LEARN_STATE);
                if (Objects.equals(learnState, 1)) {
                    map.put(LEARN_STATE, I18nUtil.getDefaultMessage("完成"));
                } else {
                    map.put(LEARN_STATE, I18nUtil.getDefaultMessage("学习中"));
                }
                Object orgName = map.get("orgName");
                map.put("manageOrgName", orgName);
            }
        };
        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    @Async
    public void exportStudyData(CourseStudyQuery courseStudyQuery) {
        Locale locale = new Locale(
            StringUtils.isNotBlank(UserThreadContext.getAcceptLanguage()) ? UserThreadContext.getAcceptLanguage()
                : "zh");
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<ICourseService, CourseStudyDetailDTO>(
            courseStudyQuery) {

            @Override
            protected ICourseService getBean() {
                return SpringUtil.getBean(COURSE_SERVICE, ICourseService.class);
            }

            @Override
            protected PageInfo<CourseStudyDetailDTO> getPageInfo() {
                if (StringUtils.isNotBlank(UserThreadContext.getAcceptLanguage())) {
                    LocaleContextHolder.setLocale(locale);
                }
                return getBean().courseStudyDetail((CourseStudyQuery) queryDTO);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.CourseStudyDetail;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.CourseStudyDetail.getType();
            }

            @Override
            protected void afterCreateBeanMap(Map<String, Object> map) {
                Object learnState = map.get(LEARN_STATE);
                if (Objects.equals(learnState, 1)) {
                    map.put(LEARN_STATE, I18nUtil.getDefaultMessage("已学习"));
                } else if (Objects.equals(learnState, 0)) {
                    map.put(LEARN_STATE, I18nUtil.getDefaultMessage("学习中"));
                } else {
                    map.put(LEARN_STATE, I18nUtil.getDefaultMessage("未开始"));
                }
            }

        };
        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    @Async
    public void exportCWStudyDetailData(CoursewareStudyQuery query) {
        Locale locale = new Locale(
            StringUtils.isNotBlank(UserThreadContext.getAcceptLanguage()) ? UserThreadContext.getAcceptLanguage()
                : "zh");

        Course tempCourse;
        String courseId = query.getCourseId();
        if (StringUtils.isNotBlank(courseId)) {
            tempCourse = baseMapper.selectById(courseId);
        } else {
            Courseware courseware = coursewareMapper.selectById(query.getCwId());
            tempCourse = baseMapper.selectById(courseware.getCourseId());
        }
        Course course = Optional.ofNullable(tempCourse)
            .orElseThrow(() -> new BusinessException(CourseErrorNoEnum.ERR_COURSE_NULL));
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<ICourseService, CoursewareStudyDetailDTO>(query) {

            @Override
            protected ICourseService getBean() {
                return SpringUtil.getBean(COURSE_SERVICE, ICourseService.class);
            }

            @Override
            protected PageInfo<CoursewareStudyDetailDTO> getPageInfo() {
                if (StringUtils.isNotBlank(UserThreadContext.getAcceptLanguage())) {
                    LocaleContextHolder.setLocale(locale);
                }
                return getBean().coursewareStudyDetail((CoursewareStudyQuery) queryDTO);
            }

            @Override
            public ExportBizType getType() {
                if (0 == course.getIsTrain()) {
                    return ExportBizType.CoursewareStudyDetail;
                }
                return ExportBizType.CoursewareStudyDetail2;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.CoursewareStudyDetail.getType();
            }

            @Override
            protected void afterCreateBeanMap(Map<String, Object> map) {
                Object learnState = map.get(LEARN_STATE);
                if (Objects.equals(learnState, 1)) {
                    map.put(LEARN_STATE, I18nUtil.getDefaultMessage("已学习"));
                } else if (Objects.equals(learnState, 0)) {
                    map.put(LEARN_STATE, I18nUtil.getDefaultMessage("学习中"));
                } else {
                    map.put(LEARN_STATE, I18nUtil.getDefaultMessage("未开始"));
                }

                // 通过状态，空值不展示 0未通过 1通过
                Object passState = map.get(PASS_STATE);
                if (Objects.isNull(passState)) {
                    map.put(PASS_STATE, "");
                } else if (Objects.equals(passState, 0)) {
                    map.put(PASS_STATE, I18nUtil.getDefaultMessage("未通过"));
                } else if (Objects.equals(passState, 1)) {
                    map.put(PASS_STATE, I18nUtil.getDefaultMessage("通过"));
                }
            }

        };
        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public PageInfo<com.wunding.learn.course.service.client.dto.CourseListDTO> searchCourse(
        SearchCourseQuery searchCourseQuery) {
        CourseListQuery courseListQuery = new CourseListQuery();
        BeanUtils.copyProperties(searchCourseQuery, courseListQuery);
        courseListQuery.setCount(true);

        //课程搜索关键字 存
        SearchKeyDetailDTO searchKeyDetailDTO = new SearchKeyDetailDTO();
        searchKeyDetailDTO.setKeyword(searchCourseQuery.getSearchKey());
        searchKeyDetailDTO.setSearchBy(UserThreadContext.getUserId());
        searchKeyDetailDTO.setType(1);
        searchKeyDetailDTO.setOrgId(UserThreadContext.getOrgId());

        mqProducer.sendMsg(new SearchKeyDetailEvent(searchKeyDetailDTO));

        return getCourseList(courseListQuery);
    }


    @Override
    public PageInfo<CoursewareDetailClientDTO> searchCourseWare(SearchCourseWareQuery searchCourseWareQuery) {
        CourseDetailClientQuery courseDetailClientQuery = new CourseDetailClientQuery();
        BeanUtils.copyProperties(searchCourseWareQuery, courseDetailClientQuery);
        courseDetailClientQuery.setCurrentOrgId(UserThreadContext.getOrgId());
        courseDetailClientQuery.setCurrentUserId(UserThreadContext.getUserId());
        courseDetailClientQuery.setIsIgnoreView(0);
        PageInfo<CoursewareDetailClientDTO> pageInfo = PageMethod.startPage(courseDetailClientQuery.getPageNo(),
                courseDetailClientQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.getCourseWareList(courseDetailClientQuery));
        //处理课件的其他字段
        handCwList(courseDetailClientQuery, pageInfo.getList());

        //课件关键字 存数据库
        SearchKeyDetailDTO searchKeyDetailDTO = new SearchKeyDetailDTO();
        searchKeyDetailDTO.setKeyword(searchCourseWareQuery.getSearchKey());
        searchKeyDetailDTO.setSearchBy(UserThreadContext.getUserId());
        searchKeyDetailDTO.setType(2);
        searchKeyDetailDTO.setOrgId(UserThreadContext.getOrgId());

        mqProducer.sendMsg(new SearchKeyDetailEvent(searchKeyDetailDTO));
        return pageInfo;
    }

    @Override
    public PageInfo<com.wunding.learn.course.service.client.dto.CourseListDTO> findFavoriteCourseList(
        FavoriteCourseQuery favoriteCourseQuery) {
        favoriteCourseQuery.setUserId(UserThreadContext.getUserId());
        PageInfo<com.wunding.learn.course.service.client.dto.CourseListDTO> pageInfo = PageMethod.startPage(
                favoriteCourseQuery.getPageNo(), favoriteCourseQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.selectFavoriteCourseList(favoriteCourseQuery));
        queryCommentNumAndImage(pageInfo);
        return pageInfo;
    }


    /**
     * 查询评论数和图像
     *
     * @param pageInfo
     */
    private void queryCommentNumAndImage(PageInfo<com.wunding.learn.course.service.client.dto.CourseListDTO> pageInfo) {

        List<com.wunding.learn.course.service.client.dto.CourseListDTO> courseListDTOList = pageInfo.getList();
        if (!CollectionUtils.isEmpty(courseListDTOList)) {
            List<String> courseIdList = courseListDTOList.stream()
                .map(com.wunding.learn.course.service.client.dto.CourseListDTO::getId).collect(Collectors.toList());

            Map<String, Integer> commentMap = commentFeign.getDiscussCount(courseIdList, CommentTypeEnum.COURSE);
            Map<String, String> imageUrlMap = fileFeign.getImageUrlsByIds(courseIdList, COURSE_IMG_ICON);
            Map<String, List<MarkInfoDTO>> courseMarkInfoByCourseId = getCourseMarkInfoByCourseIdList(courseIdList,
                false);
            pageInfo.getList().forEach(c -> {
                String courseId = c.getId();
                c.setImage(imageUrlMap.get(courseId) != null ? imageUrlMap.get(courseId) : "");
                c.setCommentNumber(commentMap.get(courseId) != null ? commentMap.get(courseId) : 0);
                c.setMarkInfoList(
                    courseMarkInfoByCourseId.get(courseId) != null ? courseMarkInfoByCourseId.get(courseId) : null);
            });
        }
    }

    @Override
    public PageInfo<com.wunding.learn.course.service.client.dto.CourseListDTO> findCourseHomePageList(
        CourseHomePageQuery courseHomePageQuery) {
        PageInfo<com.wunding.learn.course.service.client.dto.CourseListDTO> pageInfo = getCoursesByRule(
            courseHomePageQuery);
        log.info("pageInfo: " + pageInfo.getList());
        queryCommentNumAndImage(pageInfo);
        return pageInfo;
    }

    @Override
    public Map<String, Integer> getClickNumberByCourseIds(Collection<String> ids) {
        List<CourseValueDTO> clickNumBer = baseMapper.getClickNumberByCourseIds(ids);
        if (!CollectionUtils.isEmpty(clickNumBer)) {
            return clickNumBer.stream().collect(Collectors.toMap(CourseValueDTO::getId, CourseValueDTO::getValue));
        }
        return new HashMap<>();
    }

    @Override
    public Map<String, Integer> getVoteNumberByCourseIds(Collection<String> ids) {
        List<CourseValueDTO> voteNumBer = baseMapper.getVoteNumberByCourseIds(ids);
        if (!CollectionUtils.isEmpty(voteNumBer)) {
            return voteNumBer.stream().collect(Collectors.toMap(CourseValueDTO::getId, CourseValueDTO::getValue));
        }
        return new HashMap<>();
    }

    @Override
    public Map<String, Integer> getIsVoteByCourseIdsAndCurrentUserId(Collection<String> ids, String userId) {
        List<CourseValueDTO> isVote = baseMapper.getIsVoteByCourseIdsAndCurrentUserId(ids, userId);
        if (!CollectionUtils.isEmpty(isVote)) {
            return isVote.stream().collect(Collectors.toMap(CourseValueDTO::getId, CourseValueDTO::getValue));
        }
        return new HashMap<>();
    }

    @Override
    public CourseInfoDTO getRealityById(String id) {
        return baseMapper.getRealityById(id);
    }

    @Override
    public ProjectTaskCourseInteractDTO getCourseInteract(String courseId) {
        return baseMapper.getCourseInteract(courseId);
    }

    @Override
    public void updateCourseInteractNum(String id, String event) {
        switch (event) {
            case ResourceInteractEventRoutingKeyConstants.COURSE_COMMENT_EVENT:
                Map<String, Integer> commentNumMap = commentFeign.getDiscussCount(Collections.singleton(id),
                    CommentTypeEnum.COURSE);
                lambdaUpdate().set(Course::getCommentNumber, commentNumMap.get(id)).eq(Course::getId, id).update();
                break;
            case ResourceInteractEventRoutingKeyConstants.COURSE_LIKE_EVENT:
                baseMapper.updateLikeNum(id);
                break;
            case ResourceInteractEventRoutingKeyConstants.COURSE_VIEW_EVENT:
                baseMapper.updateViewNum(id);
                break;
            case ResourceInteractEventRoutingKeyConstants.COURSEWARE_COMMENT_EVENT:
                Map<String, Integer> coursewareCommentNumMap = commentFeign.getDiscussCount(Collections.singleton(id),
                    CommentTypeEnum.COURSEWARE);
                baseMapper.updateCoursewareComment(id,
                    Optional.ofNullable(coursewareCommentNumMap.get(id)).orElse(GeneralJudgeEnum.NEGATIVE.getValue()));
                break;
            default:
        }
    }

    @Override
    public Map<String, Integer> getLikeCount(Collection<String> courseIds) {
        LambdaQueryWrapper<Course> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Course::getId, Course::getVoteNumber);
        queryWrapper.in(Course::getId, courseIds);
        List<Course> list = list(queryWrapper);
        if (!CollectionUtils.isEmpty(list)) {
            return list.stream().collect(Collectors.toMap(Course::getId, Course::getVoteNumber));
        }
        return CollectionUtils.newHashMap(0);
    }

    @Override
    public Map<String, Long> getViewCount(Collection<String> resourceId) {
        LambdaQueryWrapper<Course> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Course::getId, Course::getClickNumber);
        queryWrapper.in(Course::getId, resourceId);
        List<Course> list = list(queryWrapper);
        if (!CollectionUtils.isEmpty(list)) {
            return list.stream().collect(Collectors.toMap(Course::getId, Course::getClickNumber));
        }
        return CollectionUtils.newHashMap(0);
    }

    @Override
    public Map<String, CourseInfoDTO> getCommentCount(Set<String> courseIds) {
        LambdaQueryWrapper<Course> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Course::getId, Course::getCommentNumber, Course::getVoteNumber, Course::getClickNumber);
        queryWrapper.in(Course::getId, courseIds);
        List<Course> list = list(queryWrapper);
        if (!CollectionUtils.isEmpty(list)) {
            List<CourseInfoDTO> courseInfoDTOS = BeanListUtils.copyListProperties(list, CourseInfoDTO::new);
            return courseInfoDTOS.stream().collect(Collectors.toMap(CourseInfoDTO::getId, dto -> dto));
        }
        return CollectionUtils.newHashMap(0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createTrainCourse(SaveTrainCourseDTO saveTrainCourseDTO) {
        SaveCourseDTO saveCourseDTO = new SaveCourseDTO();
        BeanUtils.copyProperties(saveTrainCourseDTO, saveCourseDTO);
        ICourseService courseService = SpringUtil.getBean(COURSE_SERVICE, ICourseService.class);
        assert courseService != null;
        String courseId = courseService.saveCourse(saveCourseDTO);
        // 处理目录关联
        directoryService.saveOrUpdateResourceDirectory(
            new SaveOrUpdateResourceDirectoryDTO().setDirectoryId(saveTrainCourseDTO.getDirectoryId())
                .setResourceIdList(Collections.singletonList(courseId)));

        return courseId;
    }

    @Override
    public void editTrainCourse(SaveTrainCourseDTO saveTrainCourseDTO) {
        SaveCourseDTO saveCourseDTO = new SaveCourseDTO();
        BeanUtils.copyProperties(saveTrainCourseDTO, saveCourseDTO);
        // 不允许修改关联id
        saveCourseDTO.setRelevanceId(null);
        this.updateCourse(saveCourseDTO);
        // 处理目录关联
        directoryService.saveOrUpdateResourceDirectory(
            new SaveOrUpdateResourceDirectoryDTO().setDirectoryId(saveTrainCourseDTO.getDirectoryId())
                .setResourceIdList(Collections.singletonList(saveTrainCourseDTO.getId())));
    }

    @Override
    public TrainCourseDetailDTO getTrainCourse(String id) {
        CourseDetailDTO courseDetailDTO = this.getCourseById(id);
        TrainCourseDetailDTO trainCourseDetailDTO = new TrainCourseDetailDTO();
        BeanUtils.copyProperties(courseDetailDTO, trainCourseDetailDTO);
        Optional.ofNullable(directoryService.getDirectoryInfoByResourceId(id)).ifPresent(directoryInfo -> {
            trainCourseDetailDTO.setDirectoryId(directoryInfo.getId());
            trainCourseDetailDTO.setDirectoryName(directoryInfo.getDirectoryName());
        });
        return trainCourseDetailDTO;
    }

    @Override
    public PageInfo<TrainCourseListDTO> trainCourseList(TrainCourseQuery trainCourseQuery) {

        Set<String> managerAreaOrgIds = orgFeign.findUserManageAreaLevelPath(UserThreadContext.getUserId());
        trainCourseQuery.setManagerAreaOrgIds(managerAreaOrgIds);
        trainCourseQuery.setCurrentOrgId(UserThreadContext.getOrgId());
        trainCourseQuery.setCurrentUserId(UserThreadContext.getUserId());

        if (StringUtils.isNotEmpty(trainCourseQuery.getCreateOrgId())) {
            // 查询用户服务查询该组织及其以下所有的组织id
            Set<String> childrenId = orgFeign.getChildrenId(trainCourseQuery.getCreateOrgId());
            trainCourseQuery.setCreateAndUnderOrgIds(childrenId);
        }

        PageInfo<TrainCourseListDTO> objectPageInfo = PageMethod.startPage(trainCourseQuery.getPageNo(),
            trainCourseQuery.getPageSize()).doSelectPageInfo(() -> baseMapper.trainCourseList(trainCourseQuery));

        Set<String> userIds = new HashSet<>();
        objectPageInfo.getList().forEach(courseInfo -> {
            userIds.add(courseInfo.getCreateBy());
            userIds.add(courseInfo.getPublishBy());
        });
        Map<String, UserOrgDTO> userMap = orgFeign.getUserAndOrgMapByUserIds(userIds);

        objectPageInfo.getList().forEach(courseListDTO -> {

            if (StringUtils.isNotEmpty(courseListDTO.getOrgId())) {
                //查询创建组织名称
                OrgDTO orgFeignById = orgFeign.getById(courseListDTO.getOrgId());
                courseListDTO.setOrgName(null != orgFeignById ? orgFeignById.getOrgName() : null);
            }

            //查询浏览人数
            LambdaQueryWrapper<UserCourseRecord> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(UserCourseRecord::getCourseId, courseListDTO.getId());
            courseListDTO.setViewCount(userCourseRecordMapper.selectCount(queryWrapper));

            //课件数
            LambdaQueryWrapper<Courseware> countCourseware = new LambdaQueryWrapper<>();
            countCourseware.eq(Courseware::getCourseId, courseListDTO.getId());
            countCourseware.eq(Courseware::getIsAvailable, AvailableEnum.AVAILABLE.getValue());
            courseListDTO.setCourseWareNum(coursewareMapper.selectCount(countCourseware));

            //发布者和添加者
            Optional.ofNullable(userMap.get(courseListDTO.getPublishBy())).ifPresent(userInfo -> {
                courseListDTO.setPublishLoginName(userInfo.getLoginName());
                courseListDTO.setPublishName(userInfo.getFullName());
            });
            Optional.ofNullable(userMap.get(courseListDTO.getCreateBy())).ifPresent(userInfo -> {
                courseListDTO.setCreateLoginName(userInfo.getLoginName());
                courseListDTO.setCreateName(userInfo.getFullName());
            });

        });
        return objectPageInfo;
    }

    @Override
    public void exportTrainCourseData(TrainCourseQuery query) {
        query.setExport(true);
        IExportDataDTO exportDataDTO = new IExportDataDTO() {
            @Override
            public List getData(Integer pageNo, Integer pageSize) {
                ICourseService courseService = SpringUtil.getBean(COURSE_SERVICE, ICourseService.class);
                query.setExport(true);
                query.setPageNo(pageNo);
                query.setPageSize(pageSize);
                PageInfo<TrainCourseListDTO> trainCourseList = courseService.trainCourseList(query);
                List<Map<String, Object>> trainCourseExportDTOS = new ArrayList<>();
                for (TrainCourseListDTO trainCourseListDTO : trainCourseList.getList()) {
                    Map<String, Object> beanMap = JsonUtil.parseObjectToMap(trainCourseListDTO);
                    trainCourseExportDTOS.add(beanMap);
                }
                return trainCourseExportDTOS;
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.TrainCourse;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.TrainCourse.getType();
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public PageInfo<BusinessCourseListDTO> getClientTrainCourseList(String trainId,
        String directoryId, String layoutConfigId, Integer pageNo, Integer pageSize) {
        CourseQuery courseQuery = new CourseQuery();
        courseQuery.setRelevanceId(trainId);
        courseQuery.setDirectoryId(directoryId);
        courseQuery.setPageNo(pageNo);
        courseQuery.setPageSize(pageSize);

        Set<String> managerAreaOrgIds = orgFeign.findUserManageAreaLevelPath(UserThreadContext.getUserId());
        courseQuery.setManagerAreaOrgIds(managerAreaOrgIds);
        courseQuery.setCurrentOrgId(UserThreadContext.getOrgId());
        courseQuery.setCurrentUserId(UserThreadContext.getUserId());

        if (StringUtils.isNotEmpty(courseQuery.getCreateOrgId())) {
            // 查询用户服务查询该组织及其以下所有的组织id
            Set<String> childrenId = orgFeign.getChildrenId(courseQuery.getCreateOrgId());
            courseQuery.setCreateAndUnderOrgIds(childrenId);
        }

        PageInfo<BusinessCourseListDTO> pageInfo = PageMethod.startPage(courseQuery.getPageNo(),
            courseQuery.getPageSize()).doSelectPageInfo(() -> baseMapper.getTrainCourseList(courseQuery));

        List<BusinessCourseListDTO> list = pageInfo.getList();
        if (CollectionUtils.isEmpty(list)) {
            return pageInfo;
        }

        List<String> courseIds = list.stream().map(BusinessCourseListDTO::getId).collect(Collectors.toList());
        String userId = UserThreadContext.getUserId();
        Map<String, Integer> isVoteByCourseIdsAndCurrentUserId = getIsVoteByCourseIdsAndCurrentUserId(courseIds,
            userId);
        Map<String, String> imgMap = fileFeign.getImageUrlsByIds(courseIds, COURSE_IMG_ICON);
        Map<String, Integer> commentMap = commentFeign.getDiscussCount(courseIds, CommentTypeEnum.COURSE);
        for (BusinessCourseListDTO dto : list) {
            Integer isVote = isVoteByCourseIdsAndCurrentUserId.get(dto.getId());
            List<String> resourceIds = new ArrayList<>();
            resourceIds.add(dto.getId());
            dto.setMemberIconUrls(memberCardFeign.getMemberIconMapByResourceIds(resourceIds).entrySet().stream()
                .flatMap(entry -> entry.getValue().stream()).collect(Collectors.toSet()));

            if (isVote != null) {
                dto.setIsVote(isVote);
            }
            String s = imgMap.get(dto.getId());
            if (StringUtils.isNotBlank(s)) {
                dto.setImage(s);
            }
            dto.setCommentNumber(Optional.ofNullable(commentMap.get(dto.getId())).orElse(0));

            //填充 兑换学习消耗激励类型
            if (StringUtils.isNotBlank(dto.getConsumeExcitationType())) {
                dto.setConsumeExcitationTypeName(ExcitationTypeEnum.getNameByCode(dto.getConsumeExcitationType()));
            }
        }

        String contentStyle = layoutFeign.getContentStyleByLayoutConfigId(layoutConfigId);
        if (ContentRuleEnum.HOT_COURSE.getRuleType().equals(contentStyle)) {
            // 热门课程，根据浏览人数倒序
            List<BusinessCourseListDTO> sortList = list.stream()
                .sorted(Comparator.comparing(BusinessCourseListDTO::getClickNumber)
                    .reversed()).collect(Collectors.toList());
            pageInfo.setList(sortList);
            return pageInfo;
        } else if (Objects.equals(ContentRuleEnum.LEARNING_COURSE.getRuleType(), contentStyle)) {
            // 正在学的课程
            List<String> courseIdList = list.stream()
                .map(BusinessCourseListDTO::getId).collect(Collectors.toList());
            List<String> learningCourseIdList = userCourseRecordMapper.getLearningCourseIdByCourseIdList(courseIdList);
            List<BusinessCourseListDTO> learningCourseList = list.stream()
                .filter(trainCourseListDTO -> learningCourseIdList.contains(trainCourseListDTO.getId())).collect(
                    Collectors.toList());
            pageInfo.setList(learningCourseList);
            return pageInfo;
        }
        return pageInfo;
    }


    private PageInfo<com.wunding.learn.course.service.client.dto.CourseListDTO> getCoursesByRule(
        CourseHomePageQuery courseHomePageQuery) {
        String contentRule = courseHomePageQuery.getContentRule();
        if (StringUtils.isEmpty(contentRule)) {
            contentRule = "recommendCourse";
        }
        //50：客户端； 51：微信 52：h5 pc
        String userId = UserThreadContext.getUserId();
        Integer pageSize = courseHomePageQuery.getPageSize();
        Integer pageNo = courseHomePageQuery.getPageNo();
        courseHomePageQuery.setUserId(userId);
        String os = UserThreadContext.getOs();
        courseHomePageQuery.setCourseType("52".equals(os) ? "E" : "M");

        switch (HeadContentRuleEnum.getCoursesRule(contentRule)) {
            case GUESSYOULIKE:
                String recommendLimit = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_501.getCode());
                //加塞数量上限
                Integer recommendNumLimit = Integer.valueOf(recommendLimit);

                if (StringUtils.isNotEmpty(courseHomePageQuery.getViewForm())
                    && CourseHomePageQuery.VIEW_FORM_HOMEPAGE.equals(courseHomePageQuery.getViewForm())) {

                    //已经设置为加塞的数量
                    int recommendCourseCount = baseMapper.getRecommendCourseCount(courseHomePageQuery);

                    //加塞数
                    int recommendNum =
                        recommendCourseCount >= recommendNumLimit ? recommendNumLimit : recommendCourseCount;
                    courseHomePageQuery.setRecommendNum(recommendNum);
                    //标签关联的课程数量
                    int courseTagCourseNum = pageSize <= recommendNum ? 0 : pageSize - recommendNum;
                    courseHomePageQuery.setCourseTagCourseNum(courseTagCourseNum);

                    return PageMethod.startPage(courseHomePageQuery.getPageNo(), courseHomePageQuery.getPageSize())
                        .doSelectPageInfo(() -> baseMapper.guessYouLikeForHomePage(courseHomePageQuery));
                } else {
                    //加塞数
                    courseHomePageQuery.setRecommendNum(recommendNumLimit);
                    //用户每次登录后，展示排序都不同
                    courseHomePageQuery.setGuessLikeOrder(userId.hashCode() % 10);
                    return PageMethod.startPage(courseHomePageQuery.getPageNo(), courseHomePageQuery.getPageSize())
                        .doSelectPageInfo(() -> baseMapper.guessYouLike(courseHomePageQuery));
                }
            case RECOMMENDCOURSE:
                courseHomePageQuery.setOrderType(CommonConstants.COURSE_ORDERTYPE_RECOMMENDDATE);
                return PageMethod.startPage(pageNo, pageSize)
                    .doSelectPageInfo(() -> baseMapper.selectRecommendCourse(courseHomePageQuery));
            case POPULARCOURSE1:
                courseHomePageQuery.setOrderType(CommonConstants.COURSE_ORDERTYPE_COMMENTNUMBERANDLIKENUM);
                String publishDays = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_451.getCode());
                courseHomePageQuery.setPublishDays(Integer.valueOf(publishDays));
                String commentNumAndLikeNum = paraFeign.getParaValue(
                    SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_461.getCode());
                courseHomePageQuery.setCommentNumAndLikeNum(Integer.valueOf(commentNumAndLikeNum));
                return PageMethod.startPage(pageNo, pageSize)
                    .doSelectPageInfo(() -> baseMapper.selectPopularCourse(courseHomePageQuery));
            case POPULARCOURSE2:
                courseHomePageQuery.setOrderType(CommonConstants.COURSE_ORDERTYPE_COMMENTNUMBERANDLIKENUM);
                String commentNumAndLikeNum1 = paraFeign.getParaValue(
                    SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_461.getCode());
                courseHomePageQuery.setCommentNumAndLikeNum(Integer.valueOf(commentNumAndLikeNum1));
                return PageMethod.startPage(pageNo, pageSize)
                    .doSelectPageInfo(() -> baseMapper.selectPopularCourse(courseHomePageQuery));
            case POSTCOURSE:
                courseHomePageQuery.setIdentityLevelPathId(identityFeign.getIdentityId(userId));
                return PageMethod.startPage(pageNo, pageSize)
                    .doSelectPageInfo(() -> baseMapper.getPostCourse(courseHomePageQuery));
            case LEARNINGCOURSE:
                return PageMethod.startPage(pageNo, pageSize)
                    .doSelectPageInfo(() -> baseMapper.getLearningCourse(courseHomePageQuery));
            case AICOURSE:
                PageInfo<com.wunding.learn.course.service.client.dto.CourseListDTO> pageInfo = PageMethod.startPage(
                        pageNo, pageSize)
                    .doSelectPageInfo(() -> baseMapper.getAICourse(courseHomePageQuery));
                List<com.wunding.learn.course.service.client.dto.CourseListDTO> list = pageInfo.getList();
                if (CollectionUtils.isEmpty(list)) {
                    return pageInfo;
                }
                List<String> courseIdList = list.stream().map(BaseCourseDTO::getId).toList();
                List<CourseRefDify> courseRefDifyList = courseRefDifyService.lambdaQuery()
                    .in(CourseRefDify::getCourseId, courseIdList).list();
                Map<String, String> courseRefDifyMap = courseRefDifyList.stream().collect(Collectors.toMap(
                    CourseRefDify::getCourseId, CourseRefDify::getDifyAppId, (existing, replacement) -> existing));
                list.forEach(a -> a.setChatUrl(difyApiClient.getChatUrl(courseRefDifyMap.get(a.getId()))));
                return pageInfo;
            case NEWCOURSECREATETIME:
            case NEWCOURSEPUBLISHTIME:
                return PageMethod.startPage(pageNo, pageSize)
                    .doSelectPageInfo(() -> baseMapper.getNewCourse(courseHomePageQuery));
            default:
                return new PageInfo<>();
        }
    }

    @Override
    public void viewLimitChange(Integer viewType, Map<String, Long> viewLimitChangeMap) {
        log.info("viewLimitChangeByUserId:" + JsonUtil.objToJson(viewLimitChangeMap));
        //下发范围类型
        for (Entry<String, Long> entry : viewLimitChangeMap.entrySet()) {
            String key = entry.getKey();
            Long value = viewLimitChangeMap.get(key);
            // 更新下发范围
            courseViewLimitComponent.handleNewViewLimit(value, key);
            if (null != viewType) {
                //更新下发范围类型
                Course saveInfo = new Course();
                saveInfo.setId(key);
                saveInfo.setViewType(viewType);
                baseMapper.updateById(saveInfo);
            }

        }
    }

    @Override
    public List<CategoryCourseDTO> getCategoryCourseList(CategoryCourseQueryDTO categoryCourseQueryDTO) {
        return baseMapper.getCategoryCourseList(categoryCourseQueryDTO);
    }

    @Override
    public int getOrgIdList(Collection<String> levelPathList, Collection<String> childOrgIds) {
        return baseMapper.getOrgIdList(levelPathList, childOrgIds);
    }

    @Override
    public List<String> getParentLevelPath(String parentId) {
        return baseMapper.getParentLevelPath(parentId);
    }

    @Override
    public Integer getCourseDownloadShiro(String courseId) {
        String currUserId = UserThreadContext.getUserId();
        if (Objects.equals(currUserId, "admin")) {
            return 1;
        }
        DownloadShiroQuery query = new DownloadShiroQuery();
        query.setCourseId(courseId);
        UserDTO userById = userFeign.getUserById(currUserId);
        if (userById != null) {
            query.setLevelPath(userById.getLevelPath());
        }
        query.setCurrentUserId(currUserId);
        AuthQuery authQuery = new AuthQuery();
        authQuery.setApplyUserId(currUserId);
        authQuery.setResourcesId(courseId);
        authQuery.setResourcesType(ResourcesTypeEnum.COURSE_DOWNLOAD.getCode());
        Boolean aBoolean = applyFeign.queryAuth(authQuery);
        Integer courseDownloadShiro = baseMapper.getCourseDownloadShiro(query);
        if (Boolean.TRUE.equals(aBoolean) || courseDownloadShiro > 0) {
            return 1;
        } else {
            return 0;
        }

    }

    /**
     * 课程付费校验 1,课程是非收费的，直接通过 2,课程是收费的，入口是公共课程入口，则需要进行付费校验 3，课程是收费的，入口是学习项目，或者培训项目，或者专题等内部入口，课程类型不是培训项目内部课程，不需要付费校验,直接通过
     * 4，课程是收费的，入口是学习项目，或者培训项目，或者专题等内部入口，课程类型是培训项目内部课程，需要进行付费校验,校验规则如下： 若是课程的可见性为公开的，则都需要校验是否已经支付过
     * 若是课程的可见性为部分可见的，则判断是否有权限查看，有权限查看直接通过，没有权限查看校验是否已经支付过 若是课程的可见性为仅创建人可见的，直接通过
     *
     * @param courseId
     * @param bizId
     * @return
     */
    @Override
    public ReduceExcitationDTO getReduceExcitation(String courseId, String bizId) {
        return getReduceExcitation(courseId, bizId, StringUtils.EMPTY);
    }

    @Override
    public ReduceExcitationDTO getReduceExcitation(String courseId, String bizId, String activityId) {

        // 如果有活动id，则不扣除金币
        if (StringUtils.isNotBlank(activityId)) {
            return createReduceExcitationDTO(true);
        }

        log.info("======getReduceExcitation courseId:{},bizId:{} ========", courseId, bizId);

        // 校验课程是否存在
        Course course = validateCourse(courseId);
        log.info("======getReduceExcitation course:{} ========", course);

        // 课程是非收费的，直接通过
        if (isFreeCourse(course)) {
            log.info("======getReduceExcitation 课程是非收费的，直接通过 course:{}========", course);
            return createReduceExcitationDTO(true);
        }

        // 根据业务ID判断入口类型
        if (StringUtils.isBlank(bizId)) {
            // 公共课程入口，进行付费校验
            ReduceExcitationDTO reduceExcitationDTO = checkResourcePay(courseId,
                ExcitationEventEnum.COURSE_CONSUME_STUDY.name(),
                course.getConsumeExcitationNum(), course.getConsumeExcitationType());

            log.info("======getReduceExcitation 公共课程入口，进行付费校验 reduceExcitationDTO:{}========",
                reduceExcitationDTO);

            return reduceExcitationDTO;
        } else {
            // 业务id不为空，内部入口，判断课程类型
            return handleInternalEntry(course);
        }
    }

    private ReduceExcitationDTO handleInternalEntry(Course course) {
        //课程来源： 0-课程管理 1-学习项目 2-专题 3-培训班
        Integer isTrain = course.getIsTrain();

        //课程类型是非培训项目课程,不需要进行付费校验
        if (isTrain == null || isTrain != 3) {
            ReduceExcitationDTO reduceExcitationDTO = createReduceExcitationDTO(true);
            log.info(
                "======getReduceExcitation 内部入口，课程类型是非培训项目课程,直接通过 reduceExcitationDTO:{}========",
                reduceExcitationDTO);
            return createReduceExcitationDTO(true);
        }

        // 下发方式：0 部分可见 1仅创建者可见 2所有人可见
        Integer viewType = course.getViewType();

        if (null != viewType && (0 == viewType || 1 == viewType)) {
            //若是课程的可见性为部分可见，则判断是否有权限查看，有权限查看直接通过，没有权限查看校验是否已经支付过
            if (Boolean.TRUE.equals(
                courseViewLimitComponent.checkViewLimit(course.getId(), UserThreadContext.getUserId()))) {
                //判断是否有权限查看
                ReduceExcitationDTO reduceExcitationDTO = createReduceExcitationDTO(true);
                log.info(
                    "======getReduceExcitation 内部入口，课程的可见性为部分可见,有权限查看直接通过 reduceExcitationDTO:{}========",
                    reduceExcitationDTO);
                return reduceExcitationDTO;
            }
            ReduceExcitationDTO reduceExcitationDTO = checkResourcePay(course.getId(),
                ExcitationEventEnum.COURSE_CONSUME_STUDY.name(),
                course.getConsumeExcitationNum(), course.getConsumeExcitationType());
            log.info(
                "======getReduceExcitation 内部入口，课程的可见性为部分可见,校验是否已经支付过 reduceExcitationDTO:{}========",
                reduceExcitationDTO);
            return reduceExcitationDTO;
        } else if (null != viewType && viewType == 2) {
            ReduceExcitationDTO reduceExcitationDTO = checkResourcePay(course.getId(),
                ExcitationEventEnum.COURSE_CONSUME_STUDY.name(),
                course.getConsumeExcitationNum(), course.getConsumeExcitationType());
            log.info(
                "======getReduceExcitation 内部入口，课程的可见性为公开的,校验是否已经支付过 reduceExcitationDTO:{}========",
                reduceExcitationDTO);
            return reduceExcitationDTO;
        } else {
            //仅创建人可见,不需要进行付费校验,直接通过
            ReduceExcitationDTO reduceExcitationDTO = createReduceExcitationDTO(true);
            log.info(
                "======getReduceExcitation 内部入口，课程的可见性为仅创建人可见,直接通过 reduceExcitationDTO:{}========",
                reduceExcitationDTO);
            return reduceExcitationDTO;
        }
    }

    private Course validateCourse(String courseId) {
        Course course = baseMapper.selectById(courseId);
        if (course == null) {
            log.warn("Course not found for ID: {}", courseId);
            throw new BusinessException(CourseErrorNoEnum.ERR_COURSE_NULL);
        }
        return course;
    }

    private boolean isFreeCourse(Course course) {
        return Objects.equals(0, course.getConsumeExcitationNum()) || course.getConsumeExcitationNum() == null;
    }

    private ReduceExcitationDTO createReduceExcitationDTO(boolean reduce) {
        ReduceExcitationDTO dto = new ReduceExcitationDTO();
        dto.setReduce(reduce);
        return dto;
    }

    @Async
    @Override
    public void addCourseClickNumber(String courseId) {
        courseMapper.addCourseClickNumber(courseId);
    }

    @Override
    public List<TrainCourseListDTO> getTrainCourseByDirectoryId(TrainCourseQuery query) {
        return baseMapper.trainCourseList(query);
    }

    @Override
    public int checkChildrenOrg(Set<String> parentViewOrgIdList, Set<String> childrenViewOrgIdList) {
        if (CollectionUtils.isEmpty(parentViewOrgIdList) || CollectionUtils.isEmpty(childrenViewOrgIdList)) {
            return 0;
        }
        return baseMapper.checkChildrenOrg(parentViewOrgIdList, childrenViewOrgIdList);
    }

    @Override
    public List<CourseSimpleInfoDTO> getCourseByUserId(String userId) {
        return baseMapper.getCourseByUserId(userId);
    }

    @Override
    public List<Course> getRealityCourseList(Collection<String> courseIdList) {
        if (CollectionUtils.isEmpty(courseIdList)) {
            return new ArrayList<>();
        }
        return baseMapper.getRealityCourseList(courseIdList);
    }

    @Override
    public List<ResourceBaseDTO> getCourseBaseList(ResourceBaseQuery resourceBaseQuery) {
        return baseMapper.getCourseBaseList(resourceBaseQuery);
    }

    @Override
    public Boolean checkCourseName(String courseName, String courseId) {
        Boolean isHave = false;
        if (StringUtils.isEmpty(courseName)) {
            return isHave;
        }
        //只检测课程列表可以看到的所有数据,使用相等
        List<Course> list = baseMapper.selectList(
            new LambdaQueryWrapper<Course>().eq(Course::getIsCopy, 0).eq(Course::getIsTrain, 0)
                .eq(Course::getCourseName, courseName).ne(!StringUtils.isEmpty(courseId), Course::getId, courseId));
        if (!CollectionUtils.isEmpty(list)) {
            isHave = true;
        }
        return isHave;
    }

    @Override
    public String getCourseWithoutLink(String courseId, String os) {
        String link = null;
        //获取课程对应的外部资源id
        Course course = baseMapper.selectById(courseId);
        if (null == course) {
            throw new BusinessException(CourseErrorNoEnum.ERR_COURSE_NULL);
        }
        //获取关联的外部课程资源
        String courseWithoutId = course.getSourceId();
        if (StringUtils.isEmpty(courseWithoutId)) {
            throw new BusinessException(CourseErrorNoEnum.ERR_COURSE_WITHOUT_NOT_FOUND);
        }
        CourseWithout courseWithout = courseWithoutService.getById(courseWithoutId);
        if (null == courseWithout) {
            throw new BusinessException(CourseErrorNoEnum.ERR_COURSE_WITHOUT_NOT_FOUND);
        }
        //通过分类获取对应的平台配置
        String sourceType = courseWithout.getSourceType();
        log.info("sourceType======================={}", sourceType);
        if (StringUtils.isEmpty(sourceType)) {
            throw new BusinessException(CourseErrorNoEnum.ERR_COURSE_WITHOUT_SOURCE_IS_NULL);
        }
        String paraCode = paraFeign.getCodeByValue(sourceType);
        log.info("paraCode======================={}", paraCode);
        if (StringUtils.isEmpty(paraCode)) {
            throw new BusinessException(CourseErrorNoEnum.ERR_COURSE_WITHOUT_CODE_NOT_FOUND);
        }

        //调用工厂方法生成链接
        ICourseWithoutLinkProduct linkProduct = courseWithoutLinkFactory.getProduct(paraCode);
        linkProduct.initConfig(paraFeign);
        CourseWithoutLinkDTO courseWithoutLinkDTO = new CourseWithoutLinkDTO();
        courseWithoutLinkDTO.setOs(os);
        courseWithoutLinkDTO.setCourseId(courseWithout.getCourseNo());
        link = linkProduct.productLink(courseWithoutLinkDTO);

        //TODO 由于环境不通，先返回一个百度链接
        if (null == link) {
            link = "https://www.baidu.com/";
        }

        return link;
    }

    @Override
    public List<ResourceMemberCardDTO> getBusinessCourseMemberCardList(String courseId) {
        List<String> resourceIds = new ArrayList<>();
        resourceIds.add(courseId);
        return memberCardFeign.getMemberIconMapByResourceIds(resourceIds).values().stream().flatMap(
                Collection::stream)
            .map(memberCard -> {
                ResourceMemberCardDTO dto = new ResourceMemberCardDTO();
                BeanUtils.copyProperties(memberCard, dto);
                return dto;
            })
            .distinct().sorted((dto1, dto2) -> dto1.getSort().compareTo(dto2.getSort())).collect(Collectors.toList());

    }

    @Override
    public Map<String, Course> getCourseMap(Collection<String> courseIdList) {
        if (courseIdList.isEmpty()) {
            return new HashMap<>();
        }
        List<Course> courseList = lambdaQuery().in(Course::getId, courseIdList).list();
        return courseList.stream().collect(Collectors.toMap(Course::getId, Function.identity()));
    }


    /**
     * 校验资源支付记录
     *
     * @param resourceId            资源id
     * @param eventId               激励事件id
     * @param consumeExcitationNum  消耗激励数量
     * @param consumeExcitationType 消耗激励类型
     * @return
     */
    private ReduceExcitationDTO checkResourcePay(String resourceId, String eventId, BigDecimal consumeExcitationNum,
        String consumeExcitationType) {
        ReduceExcitationDTO reduceExcitationDTO = new ReduceExcitationDTO();
        Integer userExcitationRecordCount = excitationFeign.getUserExcitationRecordCount(
            UserThreadContext.getUserId(),
            resourceId,
            eventId, 1);

        reduceExcitationDTO.setReduce(userExcitationRecordCount >= 1);
        reduceExcitationDTO.setConsumeExcitationNum(consumeExcitationNum);
        reduceExcitationDTO.setConsumeExcitationType(consumeExcitationType);
        reduceExcitationDTO.setConsumeExcitationTypeName(
            ExcitationTypeEnum.getNameByCode(consumeExcitationType));
        Object excitation = excitationFeign.getExcitation(consumeExcitationType);
        reduceExcitationDTO.setExchangeable(excitation);
        return reduceExcitationDTO;
    }

    /**
     * 自动生成第三方资源课程课件
     *
     * @param courseId
     * @return
     */
    private void initCourseWithoutWare(String courseId) {
        if (StringUtils.isEmpty(courseId)) {
            return;
        }
        SaveCourseWareDTO saveCourseWareDTO = new SaveCourseWareDTO();
        saveCourseWareDTO.setCourseId(courseId);
        saveCourseWareDTO.setCwName("第三方资源课程课件");
        saveCourseWareDTO.setVersion("without");
        //这里不能用id
        String userName = userFeign.getUserFullNameById(UserThreadContext.getUserId());
        saveCourseWareDTO.setCwAuthor(userName);
        saveCourseWareDTO.setIsSource(CWCreateSourceEnum.ON_LINE.getSource());
        saveCourseWareDTO.setCwContent("<p>第三方资源课程</p>");
        saveCourseWareDTO.setRealPlayTime(15);
        saveCourseWareDTO.setPlayTime(15);
        saveCourseWareDTO.setSortNo(0);
        saveCourseWareDTO.setIsHangup(0);
        saveCourseWareDTO.setIsRepository(0);
        coursewareService.saveCourseWare(saveCourseWareDTO);
    }


    @Override
    public PosterShareClientDTO getPosterShare(String courseId) {
        return posterShareService
            .getPosterShareInfo(courseId, ResourceTypeEnum.COURSE, ImageBizType.CourseImgIcon, resourceId -> {
                Course course = lambdaQuery().select(Course::getCourseName, Course::getClickNumber, Course::getAuthor)
                    .eq(Course::getId, courseId).one();
                if (Objects.isNull(course)) {
                    throw new BusinessException(CourseErrorNoEnum.ERR_COURSE_NULL);
                }
                String shareUserId = UserThreadContext.getUserId();
                String fullName = userFeign.getUserFullNameById(shareUserId);
                PosterShareResourceDTO resourceDTO = new PosterShareResourceDTO();
                // 课程名称
                resourceDTO.setResourceTitle(course.getCourseName());
                // 参与人数
                resourceDTO.setJoinNum(course.getClickNumber());
                // 作者
                resourceDTO.setAuthor(course.getAuthor());
                // 分享人
                resourceDTO.setShareUserName(fullName != null ? fullName : StringUtils.EMPTY);
                return resourceDTO;
            });
    }


    @Override
    public Course get(String id) {
        Course course = baseMapper.selectById(id);
        if (course == null) {
            throw new BusinessException(CourseErrorNoEnum.ERR_COURSE_NULL);
        }
        return course;
    }


    @Override
    public void dealWithShareCourse() {
        // 查询全部共享课程-不用管新旧数据重叠  取消共享时会取最早登记的那条数据
        LambdaQueryWrapper<Course> courseLambdaQueryWrapper = new LambdaQueryWrapper<>();
        courseLambdaQueryWrapper.eq(Course::getIsShare, GeneralJudgeEnum.CONFIRM.getValue());
        for (Course course : list(courseLambdaQueryWrapper)) {
            // 登记原下发
            CourseShareRecord courseShareRecord = new CourseShareRecord();
            courseShareRecord.setCourseId(course.getId());
            courseShareRecord.setViewType(course.getViewType());
            courseShareRecord.setViewLimitId(
                courseViewLimitComponent.getViewLimitBaseInfo(course.getId()).getProgrammeId());
            courseShareRecordService.save(courseShareRecord);
            // 修改下发
            course.setViewType(2);
            courseViewLimitComponent.handleNewAllViewLimit(course.getId());
        }

        // 处理课件评论数冗余
        List<Courseware> coursewareList = coursewareService.list();
        if (!CollectionUtils.isEmpty(coursewareList)) {
            Set<String> coursewareIdSet = coursewareList.stream().map(Courseware::getId).collect(Collectors.toSet());
            Map<String, Integer> commentCountMap = commentFeign.getDiscussCount(coursewareIdSet,
                CommentTypeEnum.COURSEWARE);
            for (Courseware courseware : coursewareList) {
                Optional.ofNullable(commentCountMap.get(courseware.getId()))
                    .ifPresent(commentCount -> baseMapper.updateCoursewareComment(courseware.getId(), commentCount));
            }
        }
    }

    @Async("courseAsyncThreadPool")
    @Override
    public void addCoursePV(String courseId) {
        courseMapper.addCoursePV(courseId);
    }

    @Override
    public void batchMoveCategoryDTO(BatchMoveCategoryDTO batchMoveCategoryDTO) {
        // 1、 分类id校验
        Categorys categorys = categorysService.get(batchMoveCategoryDTO.getCategoryId());
        // 2、 课程id校验
        List<String> courseIdList = batchMoveCategoryDTO.getIds();
        // 根据课程id校验课程是否可用
        List<Course> courseList = baseMapper.selectByIds(courseIdList);
        if (CollectionUtils.isEmpty(courseList)) {
            throw new BusinessException(ErrorNoEnum.ERR_RESOURCE_NOT_EXIST);
        }
        String lockName = CourseRedisKeyEnum.BATCH_MOVE_COURSE_CATEORY_KEY.getKey();

        try {
            RedisLockUtil.acquire(lockName, 30, 60);
            transactionTemplate.execute(status -> {
                // 3、 更新课程分类
                for (Course course : courseList) {
                    course.setCourseCateId(categorys.getId());
                    baseMapper.updateById(course);
                }
                // 4、 更新课程标签
                // 4.1 如存在，则删除旧的课程、分类标签的关系数据
                LambdaQueryWrapper<CourseTag> courseTagLambdaQueryWrapper = new LambdaQueryWrapper<>();
                courseTagLambdaQueryWrapper.select(CourseTag::getId);
                courseTagLambdaQueryWrapper.eq(CourseTag::getCreateType, GeneralJudgeEnum.NEGATIVE.getValue());
                courseTagLambdaQueryWrapper.eq(CourseTag::getIsCopy, GeneralJudgeEnum.NEGATIVE.getValue());
                courseTagLambdaQueryWrapper.in(CourseTag::getCourseId, courseIdList);
                List<CourseTag> courseTagList = courseTagService.list(courseTagLambdaQueryWrapper);
                if (!CollectionUtils.isEmpty(courseTagList)) {
                    courseTagService.removeBatchByIds(courseTagList.stream().map(CourseTag::getId).toList());
                }
                // 4.2 如存在分类标签（分类对应的标签），有则建立课程对应的标签关系数据，则插入新的课程、分类标签的关系数据
                SysTagBaseQuery  tagBaseQuery = new SysTagBaseQuery();
                tagBaseQuery.setTagClassifyId(categorys.getId());
                List<SysTagBaseDTO> sysTagBaseList = sysTagFeign.selectSysTagBaseByIds(tagBaseQuery);
                if (!CollectionUtils.isEmpty(sysTagBaseList)) {
                    // 一个分类对应标签一个
                    SysTagBaseDTO tagsFirst = sysTagBaseList.getFirst();
                    for (String courseId : courseIdList) {
                        CourseTag courseTag = new CourseTag();
                        courseTag.setId(newId());
                        courseTag.setCourseId(courseId);
                        courseTag.setTagId(tagsFirst.getId());
                        courseTag.setCreateType(GeneralJudgeEnum.NEGATIVE.getValue());
                        courseTag.setIsCopy(GeneralJudgeEnum.NEGATIVE.getValue());
                        courseTag.setScore(BigDecimal.ZERO);
                        courseTagList.add(courseTag);
                    }
                    courseTagService.saveBatch(courseTagList);
                }
                return true;
            });
        } finally {
            RedisLockUtil.release(lockName);
        }
    }

    @Override
    public PageInfo<CourseProcessListDTO> selectMyApplyCourseAudit(CourseProcessQuery query) {
        return PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> baseMapper.selectMyApplyCourseAudit(query));
    }

    @Override
    public PageInfo<CourseProcessListDTO> selectMyDealCourseAudit(CourseProcessQuery query) {
        return PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> baseMapper.selectMyDealCourseAudit(query));
    }

    @Override
    public PageInfo<CourseProcessListDTO> selectAllCourseAudit(CourseProcessQuery query) {
        return PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> baseMapper.selectAllCourseAudit(query));
    }

    @Override
    public void applyAudit(String courseId) {
        Course course = get(courseId);
        if (course.getAuditStatus() == 3) {
            throw new BusinessException(CourseErrorNoEnum.ERR_COURSE_AUDIT_REJECT_FINISH);
        }
        if (course.getAuditStatus() != 0) {
            throw new BusinessException(CourseErrorNoEnum.EXIST_COURSE_AUDIT_APPLY);
        }
        ProcessInstanceResource instanceResource = processInstanceResourceService.getProcessInstanceResourceByResourceId(
            courseId);

        // 删除原有的user库中审批流程及任务
        processFeign.deleteProcessInstanceAndTaskByResourceId(List.of(courseId));

        log.info("resource: " + instanceResource);
        // 生成审批流程任务
        ProcessInstanceTaskResource processInstanceTaskResource = new ProcessInstanceTaskResource().setId(newId());
        //开启审核 - 同步审核实例到user库
        CompleteResultDTO completeResultDTO = startTransferProcess(course, instanceResource);
        if (Boolean.TRUE.equals(completeResultDTO.getOperateResult()) && Boolean.FALSE.equals(
            completeResultDTO.getHasNextTask())) {
            course.setAuditStatus(2);
            updateById(course);
        } else {
            //保存流程实例任务
            processInstanceTaskResource.setProcessInstanceId(instanceResource.getId());
            processInstanceTaskResource.setAssignee(completeResultDTO.getAssignee());
            processInstanceTaskResource.setAssigneeRole(completeResultDTO.getAssigneeRole());
            processInstanceTaskResource.setStatus(ProcessStatusEnum.RUNNING.getStatus());
            processInstanceTaskResource.setIsDel(GeneralJudgeEnum.NEGATIVE.getValue());
            processInstanceTaskResource.setIsAvailable(GeneralJudgeEnum.CONFIRM.getValue());
            processInstanceTaskResourceService.saveOrUpdate(processInstanceTaskResource);
            course.setAuditStatus(1);
            updateById(course);
        }
    }

    @Override
    public void revokeCourseProcess(String courseId) {
        // 1: 查询课程信息
        Course course = baseMapper.selectById(courseId);
        if (StringUtils.isEmpty(course.getCourseCateId())) {
            throw new BusinessException(CourseErrorNoEnum.ERR_COURSE_HAS_NOT_CATEGORY);
        }
        if (course.getAuditStatus() != 1) {
            throw new BusinessException(CourseErrorNoEnum.ERR_HAS_COURSE_AUDIT_FINISH);
        }
        // 2: 撤销审核流程实例
        processInstanceResourceService.remove(
            new LambdaQueryWrapper<ProcessInstanceResource>().eq(ProcessInstanceResource::getResourceId,
                courseId));
        processFeign.deleteProcessInstanceByResourceId(courseId);
        // 3: 查询当前课程分类，判断是否需要审核
        Categorys category = categorysService.getById(course.getCourseCateId());
        if (Objects.isNull(category)) {
            // 分类不存在,课程不涉及审核
            course.setAuditStatus(5);
        } else {
            String levelPath = category.getLevelPath();
            String[] parts = levelPath.replaceAll(REGEX, "").split("/");
            List<String> categoryIdList = new ArrayList<>();
            for (int i = parts.length - 1; i >= 0; i--) {
                categoryIdList.add(parts[i]);
            }
            ProcessDefinitionFeignDTO processDefinitionFeignDTO = processFeign.getNearestProcessDefinitionByProcessContentId(
                categoryIdList);
            // 5: 生成新的流程案例
            if (Objects.nonNull(processDefinitionFeignDTO)) {
                ProcessDefinitionTaskDTO taskDTO = processFeign.getProcessDefinitionTaskByProcessDefinitionId(
                    processDefinitionFeignDTO.getId());
                course.setAuditStatus(0);
                course.setIsPublish(0);
                processInstanceResourceService.remove(
                    new LambdaQueryWrapper<ProcessInstanceResource>().eq(ProcessInstanceResource::getResourceId,
                        courseId));
                ProcessInstanceResource dto = new ProcessInstanceResource();
                dto.setId(newId());
                dto.setProcessCode(generateProcessCode());
                dto.setProcessDefinitionId(processDefinitionFeignDTO.getId());
                dto.setResourceId(courseId);
                dto.setApplicantUserId(UserThreadContext.getUserId());
                dto.setProcessApplyType(1);
                dto.setCurrentAssigneeRole(taskDTO.getAssigneeRole());
                dto.setCurrentAssignee(taskDTO.getAssignee());
                dto.setStatus(ProcessStatusEnum.RUNNING.getStatus());
                dto.setIsAvailable(1);
                processInstanceResourceService.save(dto);
            }
        }
        course.setId(courseId);
        courseDao.updateCourseOfWrapper(course);

    }

    @Override
    public void addProcessInstanceResource(Course course, ProcessInstanceResource processInstanceResource) {
        ProcessDefinitionFeignDTO processDefinitionFeignDTO;
        if (!StringUtils.isEmpty(course.getCourseCateId())) {
            processDefinitionFeignDTO = getProcessDefinitionFeignDTO(course.getCourseCateId());
        } else {
            processDefinitionFeignDTO = null;
        }
        if (Objects.isNull(processDefinitionFeignDTO)) {
            // 更新课程申请状态
            baseMapper.update(Wrappers.<Course>lambdaUpdate()
                .eq(Course::getId, course.getId())
                .set(Course::getAuditStatus, 5));
        } else {
            processInstanceResourceService.remove(
                new LambdaQueryWrapper<ProcessInstanceResource>().eq(ProcessInstanceResource::getResourceId,
                    course.getId()));
            processFeign.deleteProcessInstanceByResourceId(course.getId());

            // 获取流程任务信息
            ProcessDefinitionTaskDTO taskDTO = processFeign.getProcessDefinitionTaskByProcessDefinitionId(
                processDefinitionFeignDTO.getId());

            // 生成新的申请实例
            ProcessInstanceResource newInstance = new ProcessInstanceResource();
            newInstance.setId(newId());
            newInstance.setProcessCode(generateProcessCode());
            newInstance.setProcessDefinitionId(processDefinitionFeignDTO.getId());
            newInstance.setResourceId(course.getId());
            newInstance.setApplicantUserId(processInstanceResource.getApplicantUserId());
            newInstance.setProcessApplyType(processInstanceResource.getProcessApplyType());
            newInstance.setCurrentAssigneeRole(taskDTO.getAssigneeRole());
            newInstance.setCurrentAssignee(taskDTO.getAssignee());
            newInstance.setStatus(ProcessStatusEnum.RUNNING.getStatus());
            newInstance.setIsAvailable(1);
            processInstanceResourceService.save(newInstance);
            // 更新课程申请状态
            baseMapper.update(Wrappers.<Course>lambdaUpdate()
                .eq(Course::getId, course.getId())
                .set(Course::getAuditStatus, 0));
        }
    }


    @Override
    public List<CourseCategoryStatDTO> getCourseCategoryStat() {
        // 先获取可见课程总数
        String currentUserId = UserThreadContext.getUserId();
        String currentOrgId = UserThreadContext.getOrgId();
        Set<String> managerAreaOrgIds = orgFeign.findUserManageAreaLevelPath(UserThreadContext.getUserId());
        Integer courseTotal = baseMapper.getCourseTotal(currentUserId, currentOrgId, managerAreaOrgIds);
        if (courseTotal != 0) {
            List<CourseCategoryStatDTO> courseCategoryStatDTOList = baseMapper.getCourseCategoryStat(currentUserId,
                currentOrgId, managerAreaOrgIds);
            if (!CollectionUtils.isEmpty(courseCategoryStatDTOList)) {
                BigDecimal bigCourseTotal = BigDecimal.valueOf(courseTotal);
                int courseCategoryTotal = courseCategoryStatDTOList.stream().map(CourseCategoryStatDTO::getCourseCount)
                    .mapToInt(Integer::intValue)
                    .sum();
                courseCategoryStatDTOList.forEach(dto -> dto.setProportion(
                    BigDecimal.valueOf(dto.getCourseCount()).divide(bigCourseTotal, 3, RoundingMode.HALF_UP)));
                if (courseCategoryTotal != courseTotal) {
                    int remainderCourseCount = courseTotal - courseCategoryTotal;
                    courseCategoryStatDTOList.add(
                        new CourseCategoryStatDTO().setCategoryName("其他").setCourseCount(remainderCourseCount)
                            .setProportion(BigDecimal.valueOf(remainderCourseCount)
                                .divide(bigCourseTotal, 3, RoundingMode.HALF_UP)));
                }
                return courseCategoryStatDTOList;
            }
        }
        return List.of();
    }

    public void addCourseProcessInstanceResource(Course course, Integer processApplyType,
        ProcessDefinitionFeignDTO processDefinitionFeignDTO) {
        log.info("course: " + course);
        if (Objects.nonNull(processDefinitionFeignDTO)) {
            // 获取流程任务信息
            ProcessDefinitionTaskDTO taskDTO = processFeign.getProcessDefinitionTaskByProcessDefinitionId(
                processDefinitionFeignDTO.getId());

            // 生成新的申请实例
            ProcessInstanceResource newInstance = new ProcessInstanceResource();
            newInstance.setId(newId());
            newInstance.setProcessCode(generateProcessCode());
            newInstance.setProcessDefinitionId(processDefinitionFeignDTO.getId());
            newInstance.setResourceId(course.getId());
            newInstance.setApplicantUserId(UserThreadContext.getUserId());
            newInstance.setProcessApplyType(processApplyType);
            newInstance.setCurrentAssigneeRole(taskDTO.getAssigneeRole());
            newInstance.setCurrentAssignee(taskDTO.getAssignee());
            newInstance.setStatus(ProcessStatusEnum.RUNNING.getStatus());
            newInstance.setIsAvailable(1);
            processInstanceResourceService.save(newInstance);
        }
    }
}
