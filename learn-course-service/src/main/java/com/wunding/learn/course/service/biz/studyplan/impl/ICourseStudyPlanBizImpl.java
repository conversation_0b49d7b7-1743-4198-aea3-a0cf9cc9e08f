package com.wunding.learn.course.service.biz.studyplan.impl;

import static com.wunding.learn.common.util.string.StringUtil.newId;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.constant.course.CourseErrorNoEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.dto.ChangeSortNoDTO;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.other.PublishStatusEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.util.bean.BeanListUtils;
import com.wunding.learn.course.service.biz.studyplan.ICourseStudyPlanBiz;
import com.wunding.learn.course.service.client.dto.AddCourseStudyPlanDTO;
import com.wunding.learn.course.service.client.dto.AssessCourseDTO;
import com.wunding.learn.course.service.client.dto.CourseStudyPlanCourseDTO;
import com.wunding.learn.course.service.client.dto.CourseStudyPlanCourseDetailDTO;
import com.wunding.learn.course.service.client.dto.CourseStudyPlanMonthDTO;
import com.wunding.learn.course.service.client.dto.CourseSuggestDTO;
import com.wunding.learn.course.service.client.dto.CourseSuggestRecordDTO;
import com.wunding.learn.course.service.client.dto.EditCourseStudyPlanDTO;
import com.wunding.learn.course.service.client.dto.LastLearnCourseDTO;
import com.wunding.learn.course.service.client.query.CoursePlanQuery;
import com.wunding.learn.course.service.client.query.CourseSuggestQuery;
import com.wunding.learn.course.service.model.Course;
import com.wunding.learn.course.service.model.CourseStudyPlan;
import com.wunding.learn.course.service.model.CourseSuggestRecord;
import com.wunding.learn.course.service.model.Courseware;
import com.wunding.learn.course.service.model.CoursewareUserRecord;
import com.wunding.learn.course.service.model.UserCourseRecord;
import com.wunding.learn.course.service.service.ICourseService;
import com.wunding.learn.course.service.service.ICourseStudyPlanService;
import com.wunding.learn.course.service.service.ICourseSuggestRecordService;
import com.wunding.learn.course.service.service.ICoursewareService;
import com.wunding.learn.course.service.service.ICoursewareUserRecordService;
import com.wunding.learn.course.service.service.IUserCourseRecordService;
import com.wunding.learn.file.api.constant.ImageBizType;
import com.wunding.learn.file.api.service.FileFeign;
import com.wunding.learn.user.api.dto.UserOrgDTO;
import com.wunding.learn.user.api.service.OrgFeign;
import jakarta.annotation.Resource;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * <p> 课程学习计划业务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">aixinrong</a>
 * @since 2024/6/11
 */
@Slf4j
@Service("courseStudyPlanBiz")
public class ICourseStudyPlanBizImpl implements ICourseStudyPlanBiz {

    @Resource
    private ICourseStudyPlanService courseStudyPlanService;

    @Resource
    private ICourseSuggestRecordService courseSuggestRecordService;

    @Resource
    private OrgFeign orgFeign;

    @Resource
    private IUserCourseRecordService userCourseRecordService;

    @Resource
    private ICourseService courseService;

    @Resource
    private ICoursewareService coursewareService;

    @Resource
    private ICoursewareUserRecordService coursewareUserRecordService;

    @Resource
    private FileFeign fileFeign;

    @Override
    public String addToPlan(AddCourseStudyPlanDTO addCourseStudyPlanDTO) {
        // 校验是否在计划中
        LambdaQueryWrapper<CourseStudyPlan> courseStudyPlanQuery = new LambdaQueryWrapper<>();
        courseStudyPlanQuery.eq(CourseStudyPlan::getUserId, UserThreadContext.getUserId());
        courseStudyPlanQuery.eq(CourseStudyPlan::getCourseId, addCourseStudyPlanDTO.getCourseId());
        courseStudyPlanQuery.select(CourseStudyPlan::getId);
        if (!CollectionUtils.isEmpty(courseStudyPlanService.list(courseStudyPlanQuery))) {
            throw new BusinessException(CourseErrorNoEnum.ERR_ADD_COURSE_PLAN_FAIL);
        }
        String courseStudyPlanId = newId();
        CourseStudyPlan courseStudyPlan = new CourseStudyPlan();
        courseStudyPlan.setId(courseStudyPlanId);
        courseStudyPlan.setUserId(UserThreadContext.getUserId());
        courseStudyPlan.setCourseId(addCourseStudyPlanDTO.getCourseId());
        courseStudyPlan.setPlanTime(addCourseStudyPlanDTO.getPlanTime());
        courseStudyPlan.setSortNo(
            courseStudyPlanService.getMaxSortNo(UserThreadContext.getUserId(), addCourseStudyPlanDTO.getPlanTime()));
        courseStudyPlanService.save(courseStudyPlan);
        return courseStudyPlanId;
    }

    @Override
    public void editPlan(EditCourseStudyPlanDTO editCourseStudyPlanDTO) {
        CourseStudyPlan courseStudyPlan = new CourseStudyPlan();
        courseStudyPlan.setId(editCourseStudyPlanDTO.getId());
        courseStudyPlan.setPlanTime(editCourseStudyPlanDTO.getPlanTime());
        courseStudyPlanService.updateById(courseStudyPlan);
    }

    @Override
    public void cancelPlan(String id) {
        LambdaUpdateWrapper<CourseStudyPlan> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CourseStudyPlan::getId, id);
        updateWrapper.set(CourseStudyPlan::getPlanTime, null);
        courseStudyPlanService.update(updateWrapper);
    }

    @Override
    public void delFromPlan(String id) {
        CourseStudyPlan courseStudyPlan = Optional.ofNullable(courseStudyPlanService.getById(id))
            .orElseThrow(() -> new BusinessException(CourseErrorNoEnum.ERR_COURSE_PLAN_IS_NULL));
        Course course = Optional.ofNullable(courseService.getById(courseStudyPlan.getCourseId()))
            .orElseThrow(() -> new BusinessException(CourseErrorNoEnum.ERR_COURSE_NULL));
        LambdaQueryWrapper<CourseSuggestRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(CourseSuggestRecord::getId);
        queryWrapper.in(CourseSuggestRecord::getCourseId, courseStudyPlan.getCourseId());
        queryWrapper.eq(CourseSuggestRecord::getUserId, UserThreadContext.getUserId());
        queryWrapper.orderByDesc(CourseSuggestRecord::getCreateTime);
        if (PublishStatusEnum.IS_PUBLISH.getValue() == course.getIsPublish() && !CollectionUtils.isEmpty(
            courseSuggestRecordService.list(queryWrapper))) {
            throw new BusinessException(CourseErrorNoEnum.ERR_DEL_COURSE_PLAN_FAIL);
        }
        courseStudyPlanService.removeById(id);
    }

    @Override
    public void courseSuggest(CourseSuggestDTO courseSuggestDTO) {
        if (UserThreadContext.getUserId().equals(courseSuggestDTO.getUserId())) {
            throw new BusinessException(CourseErrorNoEnum.ERR_COURSE_COURSE_TO_ONESELF_FAIL);
        }
        Course course = Optional.ofNullable(courseService.getById(courseSuggestDTO.getCourseId()))
            .orElseThrow(() -> new BusinessException(CourseErrorNoEnum.ERR_COURSE_NULL));
        if (!GeneralJudgeEnum.NEGATIVE.getValue().equals(course.getIsTrain())) {
            throw new BusinessException(CourseErrorNoEnum.ERR_SUGGEST_COURSE_FAIL);
        }
        CourseSuggestRecord courseSuggestRecord = new CourseSuggestRecord();
        courseSuggestRecord.setId(newId());
        courseSuggestRecord.setReferrerId(UserThreadContext.getUserId());
        courseSuggestRecord.setUserId(courseSuggestDTO.getUserId());
        courseSuggestRecord.setCourseId(courseSuggestDTO.getCourseId());
        courseSuggestRecord.setRequiredTime(courseSuggestDTO.getRequiredTime());
        courseSuggestRecordService.save(courseSuggestRecord);
    }

    @Override
    public PageInfo<CourseSuggestRecordDTO> getCourseSuggestList(CourseSuggestQuery courseSuggestQuery) {
        PageInfo<CourseSuggestRecordDTO> pageInfo = courseSuggestRecordService.getCourseSuggestList(courseSuggestQuery);
        List<CourseSuggestRecordDTO> list = pageInfo.getList();
        if (!CollectionUtils.isEmpty(list)) {
            Set<String> userIdSet = new HashSet<>();
            list.forEach(l -> {
                userIdSet.add(l.getReferrerId());
                userIdSet.add(l.getUserId());
            });
            Set<String> courseIds = list.stream().map(CourseSuggestRecordDTO::getCourseId).collect(Collectors.toSet());
            Map<String, String> imgMap = fileFeign.getImageUrlsByIds(courseIds, ImageBizType.CourseImgIcon.name());
            Map<String, UserOrgDTO> userMap = orgFeign.getUserAndOrgMapByUserIds(userIdSet);
            list.forEach(l -> {
                Optional.ofNullable(userMap.get(l.getReferrerId()))
                    .ifPresent(userInfo -> {
                        l.setReferrerName(userInfo.getFullName());
                        l.setReferrerAvatar(userInfo.getAvatar());
                    });
                Optional.ofNullable(userMap.get(l.getUserId()))
                    .ifPresent(userInfo -> {
                        l.setUserName(userInfo.getFullName());
                        l.setUserAvatar(userInfo.getAvatar());
                    });
                l.setImage(imgMap.get(l.getCourseId()));
            });
        }
        return pageInfo;
    }

    @Override
    public void delCourseSuggest(String id) {
        courseSuggestRecordService.removeById(id);
    }

    @Override
    public List<CourseStudyPlanMonthDTO> getMonthPlanList() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        String currentYearMonth = YearMonth.now().format(formatter);
        List<String> yearMonthList = getYearMonthList(formatter);
        List<CourseStudyPlanMonthDTO> planMonthList = new ArrayList<>();
        yearMonthList.forEach(yearMonth -> {
            CourseStudyPlanMonthDTO dto = new CourseStudyPlanMonthDTO();
            dto.setYearMonth(yearMonth);
            dto.setIsCurrentYearMonth(currentYearMonth.equals(yearMonth) ? GeneralJudgeEnum.CONFIRM.getValue()
                : GeneralJudgeEnum.NEGATIVE.getValue());
            List<CourseStudyPlan> yearMonthPlanCourseList = courseStudyPlanService.getYearMonthPlanCourseList(
                UserThreadContext.getUserId(), yearMonth);
            dto.setPlanCourseTotal(yearMonthPlanCourseList.size());
            dto.setHaveLearnCourseTotal(
                courseStudyPlanService.getYearMonthPlanHaveLearnCourseTotal(UserThreadContext.getUserId(), yearMonth));
            planMonthList.add(dto);
        });
        return planMonthList;
    }

    @Override
    public PageInfo<CourseStudyPlanCourseDTO> getPlanCourseList(CoursePlanQuery coursePlanQuery) {
        PageInfo<CourseStudyPlanCourseDTO> pageInfo = courseStudyPlanService.getPlanCourseList(coursePlanQuery);
        List<CourseStudyPlanCourseDTO> list = pageInfo.getList();
        if (!CollectionUtils.isEmpty(list)) {
            Set<String> courseIdSet = list.stream().map(CourseStudyPlanCourseDTO::getCourseId)
                .collect(Collectors.toSet());
            Map<String, String> imgMap = fileFeign.getImageUrlsByIds(courseIdSet, ImageBizType.CourseImgIcon.name());
            Map<String, CourseSuggestRecord> suggestMap = courseSuggestRecordService.getSuggestToMe(courseIdSet)
                .stream()
                .collect(Collectors.toMap(CourseSuggestRecord::getCourseId, Function.identity(), (key1, key2) -> key1));
            list.forEach(l -> {
                Optional.ofNullable(imgMap.get(l.getCourseId())).ifPresent(l::setImage);
                l.setIsSuggest(Optional.ofNullable(suggestMap.get(l.getCourseId())).isPresent()
                    ? GeneralJudgeEnum.CONFIRM.getValue() : GeneralJudgeEnum.NEGATIVE.getValue());
            });
        }
        return pageInfo;
    }

    @Override
    public List<CourseStudyPlanCourseDetailDTO> getPlanCourseAll(String yearMonth) {
        List<CourseStudyPlanCourseDTO> planCourseList = courseStudyPlanService.getPlanCourseAll(yearMonth);
        if (CollectionUtils.isEmpty(planCourseList)) {
            return new ArrayList<>();
        }
        List<CourseStudyPlanCourseDetailDTO> detailList = BeanListUtils.copyList(planCourseList,
            CourseStudyPlanCourseDetailDTO.class);
        Set<String> courseIdSet = detailList.stream().map(CourseStudyPlanCourseDetailDTO::getCourseId)
            .collect(Collectors.toSet());
        Map<String, String> imgMap = fileFeign.getImageUrlsByIds(courseIdSet, ImageBizType.CourseImgIcon.name());
        List<CourseSuggestRecord> suggestToMeList = courseSuggestRecordService.getSuggestToMe(courseIdSet);
        Set<String> referrerIdSet = suggestToMeList.stream().map(CourseSuggestRecord::getReferrerId)
            .collect(Collectors.toSet());
        Map<String, UserOrgDTO> userMap = orgFeign.getUserAndOrgMapByUserIds(referrerIdSet);
        Map<String, CourseSuggestRecord> suggestMap = suggestToMeList.stream()
            .collect(Collectors.toMap(CourseSuggestRecord::getCourseId, Function.identity(), (key1, key2) -> key1));
        detailList.forEach(d -> {
            Optional.ofNullable(imgMap.get(d.getCourseId())).ifPresent(d::setImage);
            CourseSuggestRecord courseSuggestRecord = suggestMap.get(d.getCourseId());
            boolean isSuggest = Optional.ofNullable(courseSuggestRecord).isPresent();
            d.setIsSuggest(isSuggest ? GeneralJudgeEnum.CONFIRM.getValue() : GeneralJudgeEnum.NEGATIVE.getValue());
            if (isSuggest) {
                Optional.ofNullable(userMap.get(courseSuggestRecord.getReferrerId())).ifPresent(userInfo -> {
                    d.setReferrerLoginName(userInfo.getLoginName());
                    d.setReferrerName(userInfo.getFullName());
                    d.setReferrerTime(courseSuggestRecord.getCreateTime());
                });
            }
        });
        return detailList;
    }

    @Override
    public void batchChangePlanSortNo(ChangeSortNoDTO changeSortNoDTO) {
        List<CourseStudyPlan> updateBatch = changeSortNoDTO.getList().stream().map(dto -> {
            CourseStudyPlan courseStudyPlan = new CourseStudyPlan();
            courseStudyPlan.setId(dto.getId());
            courseStudyPlan.setSortNo(dto.getSortNo());
            return courseStudyPlan;
        }).collect(Collectors.toList());
        courseStudyPlanService.updateBatchById(updateBatch);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void assessCourse(AssessCourseDTO assessCourseDTO) {
        Set<String> courseIdSet = assessCourseDTO.getCourseIdSet();
        // 是否加入计划
        boolean addPlan = GeneralJudgeEnum.CONFIRM.getValue().equals(assessCourseDTO.getAddPlan());
        // 是否去推荐
        boolean toSuggest = GeneralJudgeEnum.CONFIRM.getValue().equals(assessCourseDTO.getToSuggest());
        String userId = assessCourseDTO.getUserId();
        Date commonTime = assessCourseDTO.getCommonTime();
        String currentUserId = UserThreadContext.getUserId();
        courseIdSet.forEach(courseId -> {
            if (addPlan) {
                // 查询是否已有计划
                CourseStudyPlan courseStudyPlan = Optional.ofNullable(
                        courseStudyPlanService.getPlanByUserAndCourseId(userId, courseId))
                    .orElse(new CourseStudyPlan()
                        .setId(newId())
                        .setCourseId(courseId)
                        .setUserId(userId)
                        .setSortNo(courseStudyPlanService.getMaxSortNo(userId, commonTime)));

                // 更新原数据时，只有当参数提供了计划时间时，才执行更新计划完成时间。为空则保持原计划时间不变。
                Optional.ofNullable(commonTime).ifPresent(courseStudyPlan::setPlanTime);
                courseStudyPlanService.saveOrUpdate(courseStudyPlan);
            }
            if (toSuggest && !currentUserId.equals(userId)) {
                CourseSuggestRecord courseSuggestRecord = new CourseSuggestRecord();
                courseSuggestRecord.setId(newId());
                courseSuggestRecord.setCourseId(courseId);
                courseSuggestRecord.setReferrerId(currentUserId);
                courseSuggestRecord.setUserId(userId);
                courseSuggestRecord.setRequiredTime(commonTime);
                courseSuggestRecordService.save(courseSuggestRecord);
            }
        });
    }

    @Override
    public LastLearnCourseDTO getLastLearnCourse() {
        LastLearnCourseDTO lastLearnCourseDTO = new LastLearnCourseDTO();
        // 加入计划的课程
        LambdaQueryWrapper<CourseStudyPlan> courseStudyPlanQuery = new LambdaQueryWrapper<>();
        courseStudyPlanQuery.eq(CourseStudyPlan::getUserId, UserThreadContext.getUserId());
        courseStudyPlanQuery.select(CourseStudyPlan::getCourseId, CourseStudyPlan::getId);
        List<CourseStudyPlan> courseStudyPlanList = courseStudyPlanService.list(courseStudyPlanQuery);
        if (!CollectionUtils.isEmpty(courseStudyPlanList)) {
            Set<String> courseIdSet = courseStudyPlanList.stream().map(CourseStudyPlan::getCourseId)
                .collect(Collectors.toSet());
            Map<String, String> planMap = courseStudyPlanList.stream()
                .collect(Collectors.toMap(CourseStudyPlan::getCourseId, CourseStudyPlan::getId, (key1, key2) -> key1));
            // 最近学习的课程
            LambdaQueryWrapper<UserCourseRecord> userCourseRecordQuery = new LambdaQueryWrapper<>();
            userCourseRecordQuery.in(UserCourseRecord::getCourseId, courseIdSet);
            userCourseRecordQuery.eq(UserCourseRecord::getUserId, UserThreadContext.getUserId());
            userCourseRecordQuery.select(UserCourseRecord::getCourseId);
            userCourseRecordQuery.orderByDesc(UserCourseRecord::getUpdateTime);
            userCourseRecordQuery.last("limit 1");
            Optional.ofNullable(userCourseRecordService.getOne(userCourseRecordQuery))
                .flatMap(userCourseRecord -> Optional.ofNullable(courseService.getById(userCourseRecord.getCourseId())))
                .ifPresent(course -> {
                    Optional.ofNullable(planMap.get(course.getId())).ifPresent(lastLearnCourseDTO::setId);
                    lastLearnCourseDTO.setCourseId(course.getId());
                    lastLearnCourseDTO.setCourseName(course.getCourseName());
                    lastLearnCourseDTO.setIsPublish(course.getIsPublish());
                    LambdaQueryWrapper<Courseware> coursewareQuery = new LambdaQueryWrapper<>();
                    coursewareQuery.eq(Courseware::getCourseId, course.getId());
                    coursewareQuery.select(Courseware::getId);
                    Set<String> coursewareIdSet = coursewareService.list(coursewareQuery).stream()
                        .map(Courseware::getId)
                        .collect(Collectors.toSet());
                    if (!CollectionUtils.isEmpty(coursewareIdSet)) {
                        LambdaQueryWrapper<CoursewareUserRecord> coursewareUserRecordQuery = new LambdaQueryWrapper<>();
                        coursewareUserRecordQuery.in(CoursewareUserRecord::getCoursewareId, coursewareIdSet);
                        coursewareUserRecordQuery.eq(CoursewareUserRecord::getCourseId, course.getId());
                        coursewareUserRecordQuery.eq(CoursewareUserRecord::getUserId, UserThreadContext.getUserId());
                        coursewareUserRecordQuery.orderByDesc(CoursewareUserRecord::getEndTime);
                        coursewareUserRecordQuery.last("limit 1");
                        Optional.ofNullable(coursewareUserRecordService.getOne(coursewareUserRecordQuery))
                            .ifPresent(coursewareUserRecord -> lastLearnCourseDTO.setCwId(
                                coursewareUserRecord.getCoursewareId()));
                    }
                });
        }
        return lastLearnCourseDTO;
    }

    private List<String> getYearMonthList(DateTimeFormatter formatter) {
        // 获取当前年月
        YearMonth currentYearMonth = YearMonth.now();

        // 创建一个列表来存储结果
        List<String> yearMonthList = new ArrayList<>();

        // 获取前五个月
        for (int i = 8; i > 0; i--) {
            YearMonth previousYearMonth = currentYearMonth.minusMonths(i);
            yearMonthList.add(previousYearMonth.format(formatter));
        }

        // 添加当前月
        yearMonthList.add(currentYearMonth.format(formatter));

        // 获取后六个月
        for (int i = 1; i <= 9; i++) {
            YearMonth nextYearMonth = currentYearMonth.plusMonths(i);
            yearMonthList.add(nextYearMonth.format(formatter));
        }
        return yearMonthList;
    }
}
