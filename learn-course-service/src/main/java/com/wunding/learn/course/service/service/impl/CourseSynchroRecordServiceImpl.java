package com.wunding.learn.course.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.course.service.mapper.CourseSynchroRecordMapper;
import com.wunding.learn.course.service.model.CourseSynchroRecord;
import com.wunding.learn.course.service.service.ICourseSynchroRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p> 课程同步记录表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">余学益</a>
 * @date 2022-05-10
 */
@Slf4j
@Service("courseSynchroRecordService")
public class CourseSynchroRecordServiceImpl extends
    ServiceImpl<CourseSynchroRecordMapper, CourseSynchroRecord> implements ICourseSynchroRecordService {

}
