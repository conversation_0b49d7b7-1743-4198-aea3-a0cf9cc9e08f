package com.wunding.learn.course.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.course.service.client.dto.CourseViewDurationClientDTO;
import com.wunding.learn.course.service.client.dto.CourseViewDurationDTO;
import com.wunding.learn.course.service.client.dto.LearnTimeDTO;
import com.wunding.learn.course.service.client.dto.LearnTimeReportResultDTO;
import com.wunding.learn.course.service.client.dto.UserCourseDurationDTO;
import com.wunding.learn.course.service.client.query.CourseViewDurationClientQuery;
import com.wunding.learn.course.service.client.query.UserCourseDurationQuery;
import com.wunding.learn.course.service.client.query.UserCourseDurationSimpleQuery;
import com.wunding.learn.course.service.model.CourseViewDuration;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <p> 课件学时上报表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">余学益</a>
 * @date 2022-05-10
 */
public interface ICourseViewDurationService extends IService<CourseViewDuration> {

    /**
     * 查询该课件的学习总时间
     *
     * @param courseView 查询对象
     * @return 学习总时间
     */
    CourseViewDurationClientDTO sumCourseStudyTime(CourseViewDurationClientQuery courseView);

    /**
     * 查询该课件的学习总时间
     *
     * @param courseViewList 课程视图列表
     * @return 学习总时间
     */
    Map<String, CourseViewDurationDTO> sumCourseStudyTime(List<CourseViewDurationClientQuery> courseViewList);

    /**
     * 查询该课件的最新上报的时长
     *
     * @param courseView 查询对象
     * @return 最新上报的时长
     */
    Integer getNewProgress(CourseViewDurationClientQuery courseView);

    /**
     * 学时上报
     *
     * @param learnTimeDTOList 学时上报学时对象列表
     */
    CompletableFuture<LearnTimeReportResultDTO> reportLearnTime(List<LearnTimeDTO> learnTimeDTOList);

    /**
     * 获取用户av
     *
     * @param userId
     * @return
     */
    Double getUserCourseDruation(String userId);

    /**
     * 获取用户的课时记录
     *
     * @param userCourseDurationQuery
     * @return
     */
    PageInfo<UserCourseDurationDTO> getUserCourseDurationByPage(UserCourseDurationQuery userCourseDurationQuery);

    /**
     * 查询
     *
     * @param userCourseDurationSimpleQuery
     * @return
     */
    String findUserCourseDuration(UserCourseDurationSimpleQuery userCourseDurationSimpleQuery);

    /**
     * 上报学时
     *
     * @param learnTimeDTOList 学时上报学时对象列表
     * @return {@link LearnTimeReportResultDTO}
     */
    CompletableFuture<LearnTimeReportResultDTO> controlsReportLearnTime(List<LearnTimeDTO> learnTimeDTOList);

    /**
     * 获取用户日期范围内学习时长（秒）
     *
     * @param userCourseDurationSimpleQuery
     * @return
     */
    Long findUserCourseDurationByDate(UserCourseDurationSimpleQuery userCourseDurationSimpleQuery);

    /*
     * 根据用户ID集合查询指定课程的学习时长
     * @param userIds 用户ID集合
     * @param courseIds 课程ID集合
     * @return 用户累计学时集合
     */
    Map<String, Long> getCourseDurationMap(Collection<String> userIds, Collection<String> courseIds);
}
