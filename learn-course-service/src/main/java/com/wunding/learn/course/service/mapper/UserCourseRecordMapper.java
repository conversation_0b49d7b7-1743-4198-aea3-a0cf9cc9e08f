package com.wunding.learn.course.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.common.dto.ResourceUserDTO;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import com.wunding.learn.common.query.ResourceUserQuery;
import com.wunding.learn.course.api.dto.UserCourseStudyInfoDTO;
import com.wunding.learn.course.api.dto.UserCoursewareStudyInfoDTO;
import com.wunding.learn.course.api.query.UserCourseStudyInfoQuery;
import com.wunding.learn.course.service.admin.dto.CourseLearnDetailDTO;
import com.wunding.learn.course.service.admin.dto.CourseStudyDetailDTO;
import com.wunding.learn.course.service.admin.query.CourseLearnQuery;
import com.wunding.learn.course.service.admin.query.CourseStudyQuery;
import com.wunding.learn.course.service.admin.query.CourseStudyStatisticsQuery;
import com.wunding.learn.course.service.client.dto.CourseClickNumberDTO;
import com.wunding.learn.course.service.model.UserCourseRecord;
import com.wunding.learn.exam.api.query.UserIdpStatisticQuery;
import java.util.List;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 用户课程学习记录 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">余学益</a>
 * @date 2022-05-10
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")
})
public interface UserCourseRecordMapper extends BaseMapper<UserCourseRecord> {

    /**
     * 查询课程学习详情
     *
     * @param courseLearnQuery
     * @return
     */
    List<CourseLearnDetailDTO> getCourseLearnDetail(@Param("params") CourseLearnQuery courseLearnQuery);

    /**
     * 课程管理-课程学习明细
     *
     * @param courseStudyQuery
     * @return
     */
    List<CourseStudyDetailDTO> getCourseStudyDetail(@Param("params") CourseStudyQuery courseStudyQuery);

    /**
     * 课程管理-课程学习明细（根据下发范围）
     *
     * @param courseStudyQuery
     * @return {@link List}<{@link CourseStudyDetailDTO}>
     */
    List<CourseStudyDetailDTO> getCourseStudyDetailByViewLimit(@Param("params") CourseStudyQuery courseStudyQuery);

    /**
     * 课程统计-课程学习明细
     *
     * @param query
     * @return
     */
    List<CourseStudyDetailDTO> courseStudyStatisticsDetail(@Param("params") CourseStudyStatisticsQuery query);



    /**
     * 获取用户在某个课程学习的总学时
     *
     * @param userId
     * @param courseId
     * @return
     */
    Integer getUserCourseHour(@Param("userId") String userId, @Param("courseId") String courseId);

    /**
     * 获取用户某们课程是否完成
     *
     * @param userId
     * @param courseId
     * @return
     */
    Integer getUserCourseStatus(@Param("userId") String userId, @Param("courseId") String courseId);

    /**
     * * 获取已完成课程数量
     *
     * @param userIdpStatisticQuery
     * @return
     */
    int getIdpCourseStatistic(@Param("params") UserIdpStatisticQuery userIdpStatisticQuery);

    /**
     * 查询正在学的课程
     *
     * @param courseIdList 课程id集合
     * @return
     */
    List<String> getLearningCourseIdByCourseIdList(@Param("courseIdList") List<String> courseIdList);

    /**
     * 批量获取用户课程学习记录
     *
     * @param resourceUserQuery
     * @return
     */
    List<ResourceUserDTO> getUserCourseStatusList(@Param("params") ResourceUserQuery resourceUserQuery);

    List<CourseClickNumberDTO> selectCountByCourseIdList(@Param("courseIdList") List<String> courseIdList);

    /**
     * 查询用户是否已学完课程 0否 1是
     *
     * @param courseId
     * @return 课程评星信息
     */
    Integer getMyLearnedByCourseId(@Param("courseId") String courseId, @Param("userId") String userId);

    /**
     * 查询用户指定课程学习时长统计数据（已学时长、课程总计时长）
     */
    List<UserCourseStudyInfoDTO> getUserCourseStudyInfo(UserCourseStudyInfoQuery query);

    /**
     * 查询用户指定课程下课件学习时长统计数据（已学时长、课程总计时长）
     */
    List<UserCoursewareStudyInfoDTO> getUserCoursewareStudyInfo(@Param("courseId") String courseId,
        @Param("userId") String userId);
}
