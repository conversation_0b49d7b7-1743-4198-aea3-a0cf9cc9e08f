<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.course.service.mapper.UserCourseRecordMapper">
    <!-- 开启二级缓存 -->
    <!--
<cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.course.service.mapper.UserCourseRecordMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.course.service.model.UserCourseRecord">
        <!--@Table user_course_record-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="user_id" jdbcType="VARCHAR"
          property="userId"/>
        <result column="course_id" jdbcType="VARCHAR"
          property="courseId"/>
        <result column="is_learned" jdbcType="TINYINT"
          property="isLearned"/>
        <result column="duration" jdbcType="BIGINT"
          property="duration"/>
        <result column="finish_time" jdbcType="TIMESTAMP"
          property="finishTime"/>
        <result column="create_by" jdbcType="VARCHAR"
          property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP"
          property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR"
          property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP"
          property="updateTime"/>
        <result column="is_del" jdbcType="TINYINT"
          property="isDel"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
             , user_id
             , course_id
             , is_learned
             , duration
             , finish_time
             , create_by
             , create_time
             , update_by
             , update_time
             , is_del
    </sql>

    <!--嵌套子查询-待优化-->
    <select id="getCourseLearnDetail" parameterType="com.wunding.learn.course.service.admin.query.CourseLearnQuery"
      resultType="com.wunding.learn.course.service.admin.dto.CourseLearnDetailDTO" useCache="false">
        select
        ucr.id,
        c.id course_id,
        c.course_no,
        c.course_name,
        c.org_id manageCourseOrgId,
        su.org_id,
        (select count(1) from courseware cw where cw.course_id = c.id and cw.is_del = 0) courseWareCount,
        ucr.user_id,
        ucr.create_time beginLearnTime,
        ucr.update_time lastLearnTime,
        ucr.is_learned learnState,
        (select count(1)
        from courseware_user_record cwr
        inner join courseware cw on cwr.courseware_id = cw.id
        where cwr.user_id = ucr.user_id
        and cwr.course_id = c.id
        and cwr.is_learned = 1
        and cw.is_del = 0) learnedCourseWareCount,
        c.exam_id,
        ucr.duration,
        ifnull(( select sum(cw.play_time) from courseware cw where cw.is_del=0 and cw.course_id = c.id),0) learn_finish_need_time,
        ucr.create_time createTime,
        ucr.update_time updateTime
        from user_course_record ucr
        <if test="params.updateRecordTimeStart != null">
            force index (user_course_record_update_time_IDX)
        </if>
        inner join course c on ucr.course_id = c.id
        left join sys_org so on c.org_id = so.id
        left join sys_user su on ucr.user_id = su.id
        <where>
            c.is_train = 0
              and c.is_del = 0
            <if test="params.managerAreaOrgIds != null and params.managerAreaOrgIds.size() > 0">
                and (
                <foreach collection="params.managerAreaOrgIds" item="levelPath" open="(" close=")" separator="or">
                    so.level_path like concat(#{levelPath}, '%')
                </foreach>
                or (c.org_id = #{params.currentOrgId} and c.create_by = #{params.currentUserId})
                    )
            </if>
            <if test="params.courseId != null and params.courseId != ''">
                and c.id = #{params.courseId,jdbcType=VARBINARY}
            </if>
            <if test="params.courseCode != null and params.courseCode != ''">
                and instr(c.course_no, #{params.courseCode,jdbcType=VARCHAR}) > 0
            </if>
            <if test="params.courseName != null and params.courseName != ''">
                and instr(c.course_name, #{params.courseName,jdbcType=VARCHAR}) > 0
            </if>
            <if test="params.userIdsVo != null and params.userIdsVo.size() > 0">
                <foreach collection="params.userIdsVo" item="uid" open="and ucr.user_id in (" close=")"
                  separator=",">
                    #{uid}
                </foreach>
            </if>
            <if test="params.levelPath != null and params.levelPath != ''">
                and ucr.level_path like concat(#{params.levelPath}, '%')
            </if>
            <if test="params.updateRecordTimeStart != null">
                and ucr.update_time >= #{params.updateRecordTimeStart}
            </if>
            <if test="params.updateRecordTimeEnd != null">
                and #{params.updateRecordTimeEnd} >= ucr.update_time
            </if>
            <if test="params.updateRecordTimeStart != null">
                order by ucr.update_time desc
            </if>
        </where>
    </select>

    <select id="getCourseStudyDetail" parameterType="com.wunding.learn.course.service.admin.query.CourseStudyQuery"
      resultType="com.wunding.learn.course.service.admin.dto.CourseStudyDetailDTO" useCache="false">
        select ucr.user_id,
               ucr.create_time             beginLearnTime,
               ucr.update_time             lastLearnTime,
               ucr.finish_time             finishLearnTime,
               ucr.is_learned              learnState,
               ucr.duration,
               (select count(1)
                from courseware_user_record cwr,
                     courseware cw
                where cwr.user_id = ucr.user_id
                  and cwr.course_id = ucr.course_id
                  and cwr.courseware_id = cw.id
                  and cw.is_del = 0
                  and cwr.is_learned = 1
                  and cw.is_available = 1) learnedCourseWareCount,
               (select count(1)
                from courseware cw
                where cw.course_id = ucr.course_id
                  and cw.is_del = 0
                  and cw.is_available = 1) courseWareCount
        from user_course_record ucr
        where ucr.course_id = #{params.courseId,jdbcType=VARCHAR}
        <if test="params.userIdsVo != null and params.userIdsVo.size() > 0">
            <foreach collection="params.userIdsVo" item="uid" open="and ucr.user_id in (" close=")"
              separator=",">
                #{uid}
            </foreach>
        </if>
        <if test="params.levelPath != null and params.levelPath != ''">
            and ucr.level_path like concat(#{params.levelPath}, '%')
        </if>
        order by ucr.user_id desc
    </select>

    <select id="getCourseStudyDetailByViewLimit" parameterType="com.wunding.learn.course.service.admin.query.CourseStudyQuery"
      resultType="com.wunding.learn.course.service.admin.dto.CourseStudyDetailDTO" useCache="false">
      select * from
      (
        select wvlu.user_id,
               ucr.create_time             beginLearnTime,
               ucr.update_time             lastLearnTime,
               ucr.finish_time             finishLearnTime,
               ifnull(ucr.is_learned,-1)              learnState,
               ucr.duration,
               (select count(1)
                from courseware_user_record cwr,
                     courseware cw
                where cwr.user_id = ucr.user_id
                  and cwr.course_id = ucr.course_id
                  and cwr.courseware_id = cw.id
                  and cw.is_del = 0
                  and cwr.is_learned = 1
                  and cw.is_available = 1) learnedCourseWareCount,
               (select count(1)
                from courseware cw
                where cw.course_id = #{params.courseId}
                  and cw.is_del = 0
                  and cw.is_available = 1) courseWareCount
        from w_resource_view_limit wrvl
        left join w_view_limit_user wvlu on wvlu.view_limit_id = wrvl.view_limit_id
        left join user_course_record ucr on ucr.course_id = wrvl.resource_id and ucr.user_id = wvlu.user_id
        <if test="params.levelPath != null and params.levelPath != ''">
            left join sys_user su on su.id = wvlu.user_id
            left join sys_org so on so.id = su.org_id
        </if>
        where wrvl.resource_id = #{params.courseId}
          and resource_type = 'CourseViewLimit'
        <if test="params.userIdsVo != null and params.userIdsVo.size() > 0">
            <foreach collection="params.userIdsVo" item="uid" open="and wvlu.user_id in (" close=")"
              separator=",">
                #{uid}
            </foreach>
        </if>
        <if test="params.levelPath != null and params.levelPath != ''">
            and so.level_path like concat(#{params.levelPath}, '%')
        </if>
        )t
        <where>
            <if test="params.learnState != null">
                learnState = #{params.learnState}
            </if>
        </where>
        order by learnState, user_id asc
      </select>


    <select id="courseStudyStatisticsDetail" parameterType="com.wunding.learn.course.service.admin.query.CourseStudyStatisticsQuery"
      resultType="com.wunding.learn.course.service.admin.dto.CourseStudyDetailDTO" useCache="false">
        select ucr.user_id,
        su.org_id,
        ucr.create_time beginLearnTime,
        ucr.update_time lastLearnTime,
        ucr.finish_time finishLearnTime,
        ucr.is_learned learnState,
        ucr.duration,
        (select count(1)
        from ${params.coursewareUserRecordPartition} cwr, courseware_bak cw
        where cwr.user_id = ucr.user_id
        and cwr.course_id = ucr.course_id
        and cwr.courseware_id = cw.id
        and cw.is_del = 0
        and cwr.is_learned = 1
        and cw.is_available = 1) learnedCourseWareCount,
        (select count(1)
        from courseware_bak cw
        where cw.course_id = ucr.course_id
        and cw.is_del = 0
        and cw.is_available = 1) courseWareCount
        from ${params.userCourseRecordPartition} ucr
        left join sys_user su on su.id = ucr.user_id
        where ucr.course_id = #{params.courseId,jdbcType=VARCHAR}
        and exists (select 1  from ${params.viewLimitUserPartition} a where  ucr.user_id = a.user_id and a.view_limit_id = #{params.viewLimitId} )
        <if test="params.studyStatus !=null ">
            and ucr.is_learned = #{params.studyStatus}
        </if>
        <if test="params.userIdsVo != null and params.userIdsVo.size() > 0">
            <foreach collection="params.userIdsVo" item="uid" open="and ucr.user_id in (" close=")"
              separator=",">
                #{uid}
            </foreach>
        </if>
        <if test="params.underOrgIds != null and params.underOrgIds.size() > 0">
            and su.org_id in
            <foreach collection="params.underOrgIds" item="item" open="(" close=")"
              separator=",">
                #{item}
            </foreach>
        </if>
    </select>


    <select id="getUserCourseHour" resultType="java.lang.Integer" parameterType="java.lang.String">
        select ifnull(sum(duration), 0)
        from user_course_record
        where user_id = #{userId}
          and course_id = #{courseId}
          and is_del = 0
    </select>

    <select id="getIdpCourseStatistic" parameterType="com.wunding.learn.course.api.query.UserIdpStatisticQuery"
      resultType="int">
        select count(1)
        from user_course_record lus
        where lus.user_id = #{params.userId}
          and lus.is_del = 0
          and lus.is_learned = 1
          and date_format(lus.create_time, '%Y') = #{params.year}
    </select>

    <select id="getUserCourseStatus" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(1)
        from user_course_record lus
        where lus.user_id = #{userId}
          and course_id = #{courseId}
          and lus.is_del = 0
          and lus.is_learned = 1
    </select>
    <select id="getLearningCourseIdByCourseIdList" resultType="java.lang.String">
        select course_id
        from user_course_record where is_learned = 0
                                  and course_id in
        <foreach collection="courseIdList" open="(" item="courseIdList" separator="," close=")">
            #{courseIdList}
        </foreach>
    </select>

    <select id="getUserCourseStatusList" resultType="com.wunding.learn.common.dto.ResourceUserDTO">
        select user_id userId, is_learned isFinish, duration durationOfCoursesTaken
        from user_course_record lus
        where lus.is_del = 0
          and course_id = #{params.resourceId}
          and lus.user_id in
        <foreach collection="params.userIdList" open=" (" close=")" separator="," item="userId">
            #{userId}
        </foreach>
    </select>

    <select id="selectCountByCourseIdList"
      resultType="com.wunding.learn.course.service.client.dto.CourseClickNumberDTO">
        select course_id, count(0) as clickNumber
        from user_course_record
        where is_del = 0
        and course_id in
        <foreach collection="courseIdList" open=" (" close=")" separator="," item="courseId">
            #{courseId}
        </foreach>
        group by course_id
    </select>

    <select id="getMyLearnedByCourseId" resultType="java.lang.Integer" useCache="false">
        select ur.is_learned
        from user_course_record ur
        where ur.user_id = #{userId}
          and ur.course_id = #{courseId}
          and ur.is_del= 0
    </select>
    <select id="getUserCourseStudyInfo" resultType="com.wunding.learn.course.api.dto.UserCourseStudyInfoDTO" useCache="false">
        select
            c.id,
            (select ifnull(sum(ucr.duration),0) from user_course_record ucr where ucr.is_del = 0 and ucr.user_id = #{userId} and ucr.course_id = c.id) as duration,
            (select ifnull(sum(cw.play_time),0) from courseware cw where cw.is_del = 0 and cw.course_id = c.id) as totalDuration
        from
            course c
        WHERE
            c.is_del = 0
          and c.id in <foreach collection="idList" open=" (" close=")" separator="," item="courseId">#{courseId}</foreach>
    </select>
    <select id="getUserCoursewareStudyInfo" resultType="com.wunding.learn.course.api.dto.UserCoursewareStudyInfoDTO" useCache="false">
        select
            row_number() over (order by cw.sort_no,cw.create_time desc ) as no,
            cw.id,
            cw.cw_name as cwName,
            (select ifnull(sum(ucr.duration),0) from courseware_user_record ucr where ucr.user_id = #{userId} and ucr.courseware_id = cw.id)/3600 as duration,
            ifnull(cw.play_time,0)/3600 as totalduration
        from
            courseware cw
        where
            cw.is_del = 0
          and cw.course_id = #{courseId}
        order by
            cw.sort_no,
            cw.create_time desc
    </select>
</mapper>
