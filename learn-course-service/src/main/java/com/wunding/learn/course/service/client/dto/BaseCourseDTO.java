package com.wunding.learn.course.service.client.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * @Author: aixinrong
 * @Date: 2022/6/29 13:35
 */
@Data
@Schema(name = "BaseCourseDTO", description = "基础课程对象")
public class BaseCourseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "课程id")
    private String id;

    @Schema(description = "课程名称")
    private String courseName;

    @Schema(description = "浏览量,cv")
    private Integer clickNumber;

    @Schema(description = "点赞数,pv")
    private Integer voteNumber;

    @Schema(description = "本人是否点赞,大于0为已点赞")
    private Integer isVote;

    @Schema(description = "评论数")
    private Integer commentNumber;

    @Schema(description = "评论人集合", hidden = true)
    private List<String> commentBy;

    @Schema(description = "课程图片")
    private String image;

    @Schema(description = "所属标签信息")
    private List<MarkInfoDTO> markInfoList;

    @Schema(description = "兑换学习消耗激励数量")
    private BigDecimal consumeExcitationNum;

    @Schema(description = "兑换学习消耗激励类型")
    private String consumeExcitationType;

    @Schema(description = "兑换学习消耗激励类型名称")
    private String consumeExcitationTypeName;

    @Schema(description = "没有权限是否展示，0为不展示，1为展示，默认0")
    private Integer limitShow;

    @Schema(description = "来源方式[0-自有课程，1-第三方课程资源，默认0]")
    private Integer sourceType;

    @Schema(description = "第三方课程资源id[course_without表的id]")
    private String sourceId;
}
