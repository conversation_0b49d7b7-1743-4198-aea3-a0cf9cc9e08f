package com.wunding.learn.course.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(name = "AiQuestionOptionDTO", description = "课件AI题目选项")
public class AiQuestionOptionDTO {

    @Schema(description = "选项id")
    private Long id;

    @Schema(description = "选项对应的题目id")
    private Long questionId;

    /**
     * 选项序号
     */
    @Schema(description = "选项序号")
    @NotNull(message = "选项序号不可为空")
    private Integer optionIndex;

    /**
     * 选项编号
     */
    @Schema(description = "选项编号")
    @NotBlank(message = "选项编号不可为空")
    private String optionCode;

    /**
     * 选项内容
     */
    @Schema(description = "选项内容")
    @NotBlank(message = "选项内容不可为空")
    private String optionName;
}
