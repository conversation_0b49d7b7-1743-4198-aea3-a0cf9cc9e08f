package com.wunding.learn.course.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.course.service.mapper.CourseViewMapper;
import com.wunding.learn.course.service.model.CourseView;
import com.wunding.learn.course.service.service.ICourseViewService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p> 课程访问记录 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">余学益</a>
 * @date 2022-05-10
 */
@Slf4j
@Service("courseViewService")
public class CourseViewServiceImpl extends ServiceImpl<CourseViewMapper, CourseView> implements ICourseViewService {

}
