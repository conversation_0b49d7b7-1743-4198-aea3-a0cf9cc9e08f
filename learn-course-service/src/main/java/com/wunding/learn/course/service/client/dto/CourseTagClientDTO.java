package com.wunding.learn.course.service.client.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/7/11 13:30
 */
@Data
@Schema(name = "CourseTagClientDTO", description = "课程标签对象")
public class CourseTagClientDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "标签id")
    private String id;

    @Schema(description = "父级标签id")
    private String parentId;

    @Schema(description = "创建方式 0-随课程分类创建  1-手动创建")
    private Integer createType;

    @Schema(description = "标签分类id")
    private String tagClassifyId;

    @Schema(description = "标签名称")
    private String tagName;

    @Schema(description = "自建的标签分类名称")
    private String tagClassifyName;

    @Schema(description = "全路径id")
    private String levelPath;

    @Schema(description = "全路径名")
    private String levelPathName;

    @Schema(description = "用户是否自选 0-否 1-是")
    private Integer isOptional;

    @Schema(description = "是否前端展示 0-否 1-是")
    private Integer isShow;

    @Schema(description = "显示顺序")
    private Integer sortNo;

    @Schema(description = "是否启用 0-否 1-是")
    private Integer isAvailable;

    @Schema(description = "是否刪除 0-否 1-是")
    private Integer isDel;

    @Schema(description = "默认方式 0-否 1-初始化默认 2-追加默认")
    private Integer defaultType;

}
