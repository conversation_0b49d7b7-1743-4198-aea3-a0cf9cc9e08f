package com.wunding.learn.course.service.consumer;

import com.rabbitmq.client.Channel;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.mq.event.ResourceFinishEvent;
import com.wunding.learn.common.mq.util.ConsumerAckUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.course.service.service.ICoursewareUserRecordService;
import java.io.IOException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

/**
 * 资源修改事件 采用topic 模式   市场运营项目-头条专用
 *
 * <AUTHOR>
 * @date 2022/6/6
 */
@Component
@Slf4j
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class ExamPassToUpdateCourseWareEventConsumer {

    private final ICoursewareUserRecordService coursewareUserRecordService;

    /**
     * 课件关联考试，考试完成事件的消息队列
     */
    public static final String EXAM_PASS_TO_UPDATE_COURSEWARE_EVENT_CONSUMER_QUEUE = "examPassToUpdateCourseWareEventConsumerQueue";

    @RabbitListener(
        bindings = @QueueBinding(
            value = @Queue(value = EXAM_PASS_TO_UPDATE_COURSEWARE_EVENT_CONSUMER_QUEUE),
            exchange = @Exchange(value = ResourceFinishEvent.SYSTEM_RESOURCE_FINISH_EXCHANGE, type = ResourceFinishEvent.SYSTEM_RESOURCE_FINISH_EXCHANGE_TYPE),
            key = {ResourceFinishEvent.ResourceEventRoutingKeyConstants.EXAM_PASS_EVENT}),
        id = "examPassToUpdateCourseWareEventConsumer")
    public void inventoryToSourceUpdateEventConsumer(
        @Payload ResourceFinishEvent resourceFinishEvent
        , @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag
        , Channel channel) throws IOException {

        UserThreadContext.setTenantId(resourceFinishEvent.getTenantId());

        log.info("examPassToUpdateCourseWareEventConsumer receive event :{} ", JsonUtil.objToJson(resourceFinishEvent));

        // 更新用户课件学习记录的学习状态及完成时间
        try {
            coursewareUserRecordService.updateUserRecord(
                1,
                resourceFinishEvent.getContentId(),
                resourceFinishEvent.getUserId());
            ConsumerAckUtil.basicAck(resourceFinishEvent,channel,deliveryTag, false);
        } catch (Exception e) {
            ConsumerAckUtil.basicNack(resourceFinishEvent,channel,deliveryTag, false,true);
            log.error("examPassToUpdateCourseWareEventConsumer error", e);
        } finally {
            UserThreadContext.remove();
        }
    }

}
