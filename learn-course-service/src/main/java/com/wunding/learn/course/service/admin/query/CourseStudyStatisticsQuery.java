package com.wunding.learn.course.service.admin.query;

import com.wunding.learn.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.Parameter;
import java.io.Serializable;
import java.util.List;
import java.util.Set;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: yanglequn
 * @Date: 2023/4/14 9:49
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CourseStudyStatisticsQuery extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Parameter(description = "课程id", required = true)
    @NotBlank(message = "课程id不可为空")
    private String courseId;

    @Parameter(description = "用户id集合,多个用逗号分割")
    private String userIds;

    @Parameter(hidden = true)
    private List<String> userIdsVo;

    @Parameter(description = "组织id集合,多个用逗号分割")
    private String orgId;

    @Parameter(description = "下级所有组织id", hidden = true)
    private Set<String> underOrgIds;

    @Parameter(description = "学习状态 0-学习中 1-已学 2-未学")
    private Integer studyStatus;


    @Parameter(description = "组织id列表", hidden = true)
    private Set<String> orgIdList;

    @Parameter(description = "是否只查询当前部门,true/false")
    private Boolean onlyCurrentLevel;

    @Parameter(description = "分表存储位置", hidden = true)
    private String tablePartition;

    @Parameter(description = "课件学习记录分表存储位置", hidden = true)
    private String coursewareUserRecordPartition;

    @Parameter(description = "课程学习记录分表存储位置", hidden = true)
    private String userCourseRecordPartition;

    @Parameter(description = "用户可见范围分表存储位置", hidden = true)
    private String viewLimitUserPartition;

    @Parameter(description = "下发策略id", hidden = true)
    private Long viewLimitId;
}



