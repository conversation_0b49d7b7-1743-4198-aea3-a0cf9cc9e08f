package com.wunding.learn.course.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * @Author: aixinrong
 * @Date: 2022/6/13 14:33
 */
@Data
@Schema(name = "CourseWareLibraryDTO", description = "课件库列表数据对象")
public class CourseWareLibraryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "课件id")
    private String id;

    @Schema(description = "课件名称")
    private String cwName;

    @Schema(description = "课件类型")
    private String cwType;

    @Schema(description = "视频转换状态 1-转码中 2-转码成功 3-转码失败 4-排队中")
    private Integer transformStatus;

    @Schema(description = "课件创建方式0.在线做课1.上传课件2.资源库的课件3.引用课件8.待入库课件9.学员上传课件10.课件模板")
    private Integer isSource;

    @Schema(description = "是否启用 0-否 1-是")
    private Integer isAvailable;

    @Schema(description = "入库者id")
    private String createBy;

    @Schema(description = "入库者名称")
    private String createByName;

    @Schema(description = "入库时间")
    private Date createTime;

    @Schema(description = "编辑者id")
    private String updateBy;

    @Schema(description = "编辑者名称")
    private String updateByName;

    @Schema(description = "编辑时间")
    private Date updateTime;

    @Schema(description = "课件评论数")
    private Integer cwComment;

    @Schema(description = "课件播放数")
    private Integer cwView;

    @Schema(description = "课件点赞数")
    private Integer cwAgree;

    @Schema(description = "课件库分类id")
    private String libraryCateId;

    @Schema(description = "课件库分类名称")
    private String libraryName;

    @Schema(description = "上传文件名称")
    private String cwPath;

    @Schema(description = "文件下载地址")
    private String cwFileUrl;

    @Schema(description = "课件时长")
    private Integer playTime;

    @Schema(description = "创建、归属部门Id")
    private String orgId;

    @Schema(description = "创建、归属部门")
    private String orgName;

    @Schema(description = "课件真实时长（视频/mp3）")
    private Integer realPlayTime;
}
