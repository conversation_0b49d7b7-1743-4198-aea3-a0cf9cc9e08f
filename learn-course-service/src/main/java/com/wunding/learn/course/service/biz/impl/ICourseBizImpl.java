package com.wunding.learn.course.service.biz.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wunding.learn.common.ai.outline.dto.AiOutlineItemDTO;
import com.wunding.learn.common.ai.outline.enums.AiOutlineItemResourceTypeEnums;
import com.wunding.learn.common.ai.outline.service.IAiOutlineItemService;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum;
import com.wunding.learn.common.enums.excitation.ExcitationEventEnum;
import com.wunding.learn.common.enums.file.CWTypeEnum;
import com.wunding.learn.common.mq.dto.ExcitationMQEventDTO;
import com.wunding.learn.common.mq.event.excitation.ExcitationMQEvent;
import com.wunding.learn.common.mq.service.MqProducer;
import com.wunding.learn.common.util.bean.BeanListUtils;
import com.wunding.learn.course.service.admin.dto.CourseStarDTO;
import com.wunding.learn.course.service.biz.ICourseBiz;
import com.wunding.learn.course.service.client.dto.CourseChapterClientDTO;
import com.wunding.learn.course.service.client.dto.CoursewareInfoClientDTO;
import com.wunding.learn.course.service.client.dto.CoursewareListClientDTO;
import com.wunding.learn.course.service.client.dto.CoursewareMp3DTO;
import com.wunding.learn.course.service.client.query.CourseChapterListClientQuery;
import com.wunding.learn.course.service.client.query.CourseViewDurationClientQuery;
import com.wunding.learn.course.service.client.query.CoursewareInfoClientQuery;
import com.wunding.learn.course.service.client.query.CoursewareListClientQuery;
import com.wunding.learn.course.service.model.Course;
import com.wunding.learn.course.service.model.CourseChapter;
import com.wunding.learn.course.service.model.Courseware;
import com.wunding.learn.course.service.model.CoursewareUserRecord;
import com.wunding.learn.course.service.service.ICourseChapterService;
import com.wunding.learn.course.service.service.ICourseService;
import com.wunding.learn.course.service.service.ICourseStarService;
import com.wunding.learn.course.service.service.ICourseViewDurationService;
import com.wunding.learn.course.service.service.ICoursewareService;
import com.wunding.learn.course.service.service.ICoursewareStarService;
import com.wunding.learn.course.service.service.ICoursewareUserRecordService;
import com.wunding.learn.exam.api.service.ExamFeign;
import com.wunding.learn.file.api.constant.FileBizType;
import com.wunding.learn.file.api.service.FileFeign;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * <p> 课程 业务服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @since 2024-08-06
 */
@Slf4j
@Service("courseBiz")
public class ICourseBizImpl implements ICourseBiz {

    public static final int HUNDRED = 100;
    @Resource
    private ICourseService courseService;
    @Resource
    private ICourseChapterService courseChapterService;
    @Resource
    private ICoursewareService coursewareService;
    @Resource
    private ICourseViewDurationService courseViewDurationService;
    @Resource
    private ICoursewareUserRecordService coursewareUserRecordService;
    @Resource
    private ICourseStarService courseStarService;
    @Resource
    private ICoursewareStarService coursewareStarService;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private ExamFeign examFeign;
    @Resource
    private FileFeign fileFeign;
    @Resource
    private IAiOutlineItemService aiOutlineItemService;

    @Override
    public List<CourseChapterClientDTO> getCourseChapterList(CourseChapterListClientQuery query) {
        // 通过课程id获取课程信息并校验是否存在
        Course course = courseService.get(query.getId());
        // 查询课程章节列表
        List<CourseChapter> list = courseChapterService.list(
            new LambdaQueryWrapper<CourseChapter>().eq(CourseChapter::getCourseId, course.getId()));
        return BeanListUtils.copyList(list, CourseChapterClientDTO.class);
    }

    @Override
    public List<CoursewareListClientDTO> getCoursewareList(CoursewareListClientQuery query) {
        List<CoursewareListClientDTO> dtoList = new ArrayList<>();
        // 通过课程id获取课程信息并校验是否存在
        Course course = courseService.get(query.getCourseId());
        // 查询课件列表
        List<Courseware> list = coursewareService.getBaseMapper().selectList(
            new LambdaQueryWrapper<Courseware>().select(
                    Courseware::getId,
                    Courseware::getChapterId,
                    Courseware::getCwName,
                    Courseware::getCwType,
                    Courseware::getMime,
                    Courseware::getPlayTime,
                    Courseware::getRealPlayTime,
                    Courseware::getFinishType,
                    Courseware::getRequiredProgress,
                    Courseware::getExamId,
                    Courseware::getExamFinishType,
                    Courseware::getDescriptions)
                .eq(Courseware::getCourseId, course.getId())
                .eq(Courseware::getTransformStatus, 2)
                .eq(StringUtils.isNotBlank(query.getChapterId()), Courseware::getChapterId, query.getChapterId())
                .eq(StringUtils.isNotBlank(query.getCoursewareId()), Courseware::getId, query.getCoursewareId())
                .orderByAsc(Courseware::getSortNo).orderByAsc(Courseware::getId));
        // 响应数据处理
        if (!list.isEmpty()) {
            // 课件学习记录Map
            List<String> cwIds = list.stream().map(Courseware::getId).toList();
            Map<String, CoursewareUserRecord> coursewareUserRecordMap = coursewareUserRecordService.queryListByUserIdAndCwIds(
                    UserThreadContext.getUserId(), cwIds).stream()
                .collect(Collectors.toMap(CoursewareUserRecord::getCoursewareId, Function.identity(), (k1, k2) -> k1));

            // 考试信息Map
            List<String> examIds = list.stream().map(Courseware::getExamId).filter(StringUtils::isNotEmpty).toList();
            log.info("examIds: {}", examIds);
            Map<String, String> examMap = examFeign.getNameBatchIds(examIds);

            // 课件大纲信息Map
            Map<String, List<AiOutlineItemDTO>> aiOutlineItemListMap = aiOutlineItemService.getAiOutlineItemListMap(
                cwIds, AiOutlineItemResourceTypeEnums.COURSEWARE.getCode());

          list.forEach(courseware -> {
                CoursewareListClientDTO dto = new CoursewareListClientDTO();
                BeanUtils.copyProperties(courseware, dto);
                // 学习记录处理
                dto.setIsLearned(0);
                dto.setLearnedTime(0L);
                Optional.ofNullable(coursewareUserRecordMap.get(courseware.getId())).ifPresent(coursewareUserRecord -> {
                    dto.setIsLearned(coursewareUserRecord.getIsLearned());
                    dto.setLearnedTime(coursewareUserRecord.getDuration());
                });
                // 关联考试处理
                Optional.ofNullable(examMap.get(courseware.getExamId())).ifPresent(dto::setExamName);
                // 关联课件大纲信息
                Optional.ofNullable(aiOutlineItemListMap.get(courseware.getId())).ifPresent(dto::setAiOutlineItemDTOList);
                dtoList.add(dto);
            });
        }
        return dtoList;
    }

    @Override
    public CourseStarDTO getCourseStarByCourseId(String courseId) {
        return courseStarService.getCourseStarByCourseId(courseId);
    }

    @Override
    public CoursewareInfoClientDTO getCoursewareInfo(CoursewareInfoClientQuery query) {
        CoursewareInfoClientDTO dto = new CoursewareInfoClientDTO();
        String userId = UserThreadContext.getUserId();

        // 查询课件信息
        Courseware courseware = coursewareService.get(query.getId());
        BeanUtils.copyProperties(courseware, dto);
        dto.setDescription(courseware.getDescriptions());
        dto.setType(courseware.getCwType());
        dto.setSetPlayTime(courseware.getPlayTime());

        // 激励业务属性数据对象处理
        query.setBizId(Optional.ofNullable(query.getBizId()).orElse(query.getId()));
        query.setBizType(
            Optional.ofNullable(query.getBizType()).orElse(ExcitationEventCategoryEnum.COURSE_WARE.getCode()));
        // 发送消息[点击学习]
        mqProducer.sendMsg(new ExcitationMQEvent(
            new ExcitationMQEventDTO(userId, ExcitationEventEnum.learnCourseWare.name(), courseware.getId(),
                ExcitationEventCategoryEnum.COURSE_WARE.getCode()).setTargetName(courseware.getCwName())
                .setBizType(query.getBizType()).setBizId(query.getBizId()).setIsExchange(query.getIsExchange())));

        dto.setProgress(0);
        // 兼容投票课件详情查看
        if (StringUtils.isNotEmpty(dto.getCourseId())) {
            // 获取课件上次观看占比
            CourseViewDurationClientQuery dbClientQuery = new CourseViewDurationClientQuery();
            dbClientQuery.setCourseId(dto.getCourseId());
            dbClientQuery.setCwId(dto.getId());
            dbClientQuery.setViewBy(userId);
            Optional.ofNullable(courseViewDurationService.getNewProgress(dbClientQuery)).ifPresent(dto::setProgress);
            //设置播放进度
            if (Objects.equals(dto.getProgress(), HUNDRED)) {
                dto.setProgress(0);
            } else {
                dto.setProgress(dto.getProgress() * dto.getPlayTime() / 100);
            }
        }
        // 查询课件学习记录
        dto.setIsLearned(0);
        dto.setLearnedTime(0L);
        Optional.ofNullable(coursewareUserRecordService.getByUserIdAndCwId(userId, courseware.getId()))
            .ifPresent(coursewareUserRecord -> {
                dto.setIsLearned(coursewareUserRecord.getIsLearned());
                dto.setLearnedTime(coursewareUserRecord.getDuration());
            });

        // 播放控制需求调整：原处理逻辑根据全局课件播放参数配置进行控制，目前调整为：全局配置仅在管理端课件编辑时进行默认引导操作，学员端播放以课件自身配置为准【涉及功能点：视频课件进度条能否拖拽、能否倍速、是否播放防挂机】
        // 视频课件进度条能否拖拽。 0 不能拖拽、1 能拖拽
        // 首次能拖   并且 没学 = 能拖
        // 首次能拖   并且 学了 = 能拖
        // 首次不能拖 并且 没学 = 不能拖
        // 首次不能拖 并且 学了 = 能拖
        if (Objects.equals(0, dto.getCanDrag()) && dto.getIsLearned() == 1) {
            dto.setCanDrag(1);
        }

        if (Objects.equals(0, dto.getCanSpeed()) && dto.getIsLearned() == 1) {
            dto.setCanSpeed(1);
        }

        // 设置默认参数值
        dto.setOrientation("P");
        dto.setIsComment(1);
        dto.setIsDownload(1);
        dto.setEnableRating(1);
        dto.setMyCompany(1);
        dto.setIsShowExcitation(0);

        // 查询自我评星
        Integer oneselfStar = coursewareStarService.getOneselfStar(userId, courseware.getId());
        dto.setMyStar(oneselfStar == null ? 0 : oneselfStar);

        // 查询课件URL相关
        // 设置课件资源地址
        String currentPath = fileFeign.getFileUrl(courseware.getId(), FileBizType.CourseWareFile.name());
        dto.setUrl(currentPath);
        // PPT-附件音频资源
        List<String> pptType = Arrays.asList(CWTypeEnum.PPT.getFileType());
        if (pptType.contains(courseware.getCwType().toLowerCase())) {
            Optional.ofNullable(
                    fileFeign.getFileByCategoryTypeAndIsAdjunct(courseware.getId(), FileBizType.CourseWareFile.name(), 1))
                .ifPresent(mp3File -> dto.setCoursewareMp3DTO(new CoursewareMp3DTO().setUrl(mp3File.getUrl())));
        }
        // 设置视频清晰度URL
        dto.setVideoClarityList(fileFeign.getVideoClarity(courseware.getId()));
        if(Objects.equals(dto.getType(), CWTypeEnum.Scorm.name())){
            dto.setScormPlayUrl(fileFeign.getScormPlayUrl());
        }
        return dto;
    }

}
