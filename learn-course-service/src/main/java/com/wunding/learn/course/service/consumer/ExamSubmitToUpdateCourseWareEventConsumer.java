package com.wunding.learn.course.service.consumer;

import com.rabbitmq.client.Channel;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.mq.event.ResourceFinishEvent;
import com.wunding.learn.common.mq.util.ConsumerAckUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.course.service.service.ICoursewareUserRecordService;
import java.io.IOException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

/**
 * 课件关联考试，考试提交（答题完成）事件 采用topic 模式
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Component
@Slf4j
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class ExamSubmitToUpdateCourseWareEventConsumer {

    private final ICoursewareUserRecordService coursewareUserRecordService;

    /**
     * 课件关联考试，考试提交（答题完成）事件的消息队列
     */
    public static final String EXAM_SUBMIT_TO_UPDATE_COURSEWARE_EVENT_CONSUMER_QUEUE = "examSubmitToUpdateCourseWareEventConsumerQueue";

    @RabbitListener(
        bindings = @QueueBinding(
            value = @Queue(value = EXAM_SUBMIT_TO_UPDATE_COURSEWARE_EVENT_CONSUMER_QUEUE),
            exchange = @Exchange(
                value = ResourceFinishEvent.SYSTEM_RESOURCE_FINISH_EXCHANGE,
                type = ResourceFinishEvent.SYSTEM_RESOURCE_FINISH_EXCHANGE_TYPE),
            key = {ResourceFinishEvent.ResourceEventRoutingKeyConstants.EXAM_SUBMIT_EVENT}),
        id = "examSubmitToUpdateCourseWareEventConsumer")
    public void inventoryToSourceUpdateEventConsumer(
        @Payload ResourceFinishEvent resourceFinishEvent,
        @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
        Channel channel) throws IOException {

        UserThreadContext.setTenantId(resourceFinishEvent.getTenantId());

        log.info("examSubmitToUpdateCourseWareEventConsumer receive event :{} ",
            JsonUtil.objToJson(resourceFinishEvent));

        // 更新用户课件学习记录的学习状态及完成时间
        try {
            coursewareUserRecordService.updateUserRecord(
                0,
                resourceFinishEvent.getContentId(),
                resourceFinishEvent.getUserId());
            ConsumerAckUtil.basicAck(resourceFinishEvent, channel, deliveryTag, false);
        } catch (Exception e) {
            ConsumerAckUtil.basicNack(resourceFinishEvent, channel, deliveryTag, false, true);
            log.error("examSubmitToUpdateCourseWareEventConsumer error", e);
        } finally {
            UserThreadContext.remove();
        }
    }

}
