package com.wunding.learn.course.service.admin.dto.courseware;

import com.wunding.learn.common.aop.valid.constraints.RichTextLength;
import com.wunding.learn.common.enums.file.FileType;
import com.wunding.learn.file.api.constant.FileBizType;
import com.wunding.learn.file.api.dto.IFileResourceDTO;
import com.wunding.learn.user.api.dto.AiCwSaveConfig;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

/**
 * 课件新增和保存时的公共参数
 *
 * <AUTHOR> href="mailto:<EMAIL>">yuxueyi</a>
 * @version V7.0.0
 * @since 2022/6/6  14:40
 */
@Data
@ToString(callSuper = true)
@Schema(name = "CourseWareBaseDTO", description = "课件保存基础对象")
public class CourseWareBaseDTO implements IFileResourceDTO {

    @Schema(description = "课件作者，可手动输入，不输入则为当前操作人")
    private String cwAuthor;

    @Schema(description = "课件名称", required = true)
    @NotBlank(message = "课件名称不可为空")
    @Length(max = 80, message = "课件名称不可超过80个字符")
    private String cwName;

    @Schema(description = "课程章节ID")
    private String chapterId;

    @Schema(description = "课件创建来源: 0-在线做课，1-上传课件，2-课件库，10-课件模板，11-案例库", required = true)
    private Integer isSource;

    @Schema(description = "模板类型 1：模板1,2：模板2, 3：模板3, 4：模板4")
    private Integer modelType;

    /**
     * 实际上也是courseware表id
     */
    @Schema(description = "引用的资源库id，isSource = 2时，必填")
    private String libraryId;

    @Schema(description = "课件类型，上传文件时必填，从课件文件上传接口获取")
    private String cwType;

    @Schema(description = "课件内容")
    @RichTextLength(max = 20000)
    private String cwContent;

    @Schema(description = "课件MIME")
    private String mime;

    @Schema(description = "文件名，上传课件时必填")
    private String fileName;

    @Schema(description = "文件临时目录地址，上传课件时必填")
    private String filePath;

    @Schema(description = "版本号，上传课件时必填")
    private String version;

    @Schema(description = "mp3文件名，上传课件类型时ppt时可填")
    private String mp3fileName;

    @Schema(description = "mp3文件临时目录地址，上传课件类型时ppt时可填")
    private String mp3filePath;

    @Schema(description = "课件时长,单位秒，管理员输入的时长", required = true)
    @Min(value = 1, message = "课件时长必须为正整数")
    @Max(value = 99999, message = "课件时长不可大于99999")
    @NotNull(message = "课件时长不可为空")
    private Integer playTime;


    @Schema(description = "能否倍速[0:否，1:是]")
    private Integer canSpeed;

    @Schema(description = "能否拖拽进度条[0:否，1:是]")
    private Integer canDrag;

    @Schema(description = "完成类型[0:按时长，1:按进度]")
    private Integer finishType;

    @Schema(description = "当完成类型为按进度时的课件学习进度百分比要求(0-100)，仅在finish_type=1时有效")
    private Integer requiredProgress;

    @Schema(description = "课件真实时长（视频/mp3）,即接口返回的时长", required = true)
    @NotNull(message = "课件真实时长不可为空")
    private Integer realPlayTime;

    @Schema(description = "是否播放防挂机（1：是，0：否）")
    private Integer isHangup;

    @Schema(description = "防挂机时长（分）")
    @Min(value = 1, message = "防挂机时长必须为正整数")
    @Max(value = 99999, message = "防挂机时长不可大于99999")
    private Integer hangupDurationMinute;

    @Schema(description = "防挂机倒计时长（秒）")
    @Min(value = 1, message = "防挂机倒计时长必须为正整数")
    @Max(value = 99999, message = "防挂机倒计时长不可大于99999")
    private Integer hangupDurationSecond;

    @Schema(description = "显示顺序")
    private Integer sortNo;

    @Schema(description = "考试Id")
    private String examId;

    @Schema(description = "课件关联考试时的完成条件（-1:不涉及考试 0:提交考试即完成 1:通过考试才完成，仅在 exam_id 不为空时生效）")
    private Integer examFinishType;

    @Schema(description = "课件库id", hidden = true)
    private String courseLibId;

    @Schema(description = "关联文件相对路径：目前针对scorm课件index.html位置")
    private String href;

    /**
     * 课件AI关键词开关
     */
    @Schema(description = "ai开关 关键词：{id:aiCwKeyword},摘要:{id:aiCwDesc{,试题:{id:aiCwQuestion,questionCount:10,questionType:[1,2,3]},大纲:{id:aiCwOutline}")
    private List<AiCwSaveConfig> aiSwitch = new ArrayList<>();


    @Override
    public String getFileType() {
        return cwType;
    }


    @Override
    public FileBizType getBizType() {
        return FileBizType.CourseWareFile;
    }

    @Override
    public String getLibId() {
        return courseLibId;
    }

    @Override
    public String getAdjunctName() {
        return mp3fileName;
    }

    @Override
    public String getAdjunctType() {
        return FileType.MP3.getType();
    }

    @Override
    public String getAdjunctPath() {
        return mp3filePath;
    }

}
