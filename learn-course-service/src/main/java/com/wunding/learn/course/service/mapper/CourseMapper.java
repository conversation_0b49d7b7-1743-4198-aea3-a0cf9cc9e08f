package com.wunding.learn.course.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.common.bean.BaseEntity;
import com.wunding.learn.common.dto.CerDitchDTO;
import com.wunding.learn.common.dto.CertificationContentDTO;
import com.wunding.learn.common.dto.LecturerCourseDetailDTO;
import com.wunding.learn.common.dto.ResourceBaseDTO;
import com.wunding.learn.common.dto.ResourceDeleteInfoDTO;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import com.wunding.learn.common.query.ResourceBaseQuery;
import com.wunding.learn.course.api.dto.CourseInfoDTO;
import com.wunding.learn.course.api.dto.CourseLearnDetailDTO;
import com.wunding.learn.course.api.dto.CourseSimpleInfoDTO;
import com.wunding.learn.course.api.dto.CourseWareLearnDetailDTO;
import com.wunding.learn.course.api.query.CourseLearnQuery;
import com.wunding.learn.course.api.query.CourseSummaryListQuery;
import com.wunding.learn.course.service.admin.dto.AbilityChooseCourseDTO;
import com.wunding.learn.course.service.admin.dto.CourseCashDTO;
import com.wunding.learn.course.service.admin.dto.CourseCategoryStatDTO;
import com.wunding.learn.course.service.admin.dto.CourseListDTO;
import com.wunding.learn.course.service.admin.dto.CourseProcessListDTO;
import com.wunding.learn.course.service.admin.dto.TrainCourseListDTO;
import com.wunding.learn.course.service.admin.query.AbilityChooseCourseQuery;
import com.wunding.learn.course.service.admin.query.CourseProcessQuery;
import com.wunding.learn.course.service.admin.query.CourseQuery;
import com.wunding.learn.course.service.admin.query.TrainCourseQuery;
import com.wunding.learn.course.service.client.dto.BusinessCourseListDTO;
import com.wunding.learn.course.service.client.dto.CategoryCourseDTO;
import com.wunding.learn.course.service.client.dto.CourseDetailClientDTO;
import com.wunding.learn.course.service.client.dto.CourseValueDTO;
import com.wunding.learn.course.service.client.dto.CoursewareDetailClientDTO;
import com.wunding.learn.course.service.client.dto.MyCourseDTO;
import com.wunding.learn.course.service.client.dto.PostCourseClientDTO;
import com.wunding.learn.course.service.client.dto.ProjectTaskCourseInteractDTO;
import com.wunding.learn.course.service.client.dto.RecommendCourseDTO;
import com.wunding.learn.course.service.client.dto.RelatedCourseDTO;
import com.wunding.learn.course.service.client.dto.StudyRecordDTO;
import com.wunding.learn.course.service.client.dto.SubordinateCourseDTO;
import com.wunding.learn.course.service.client.query.CategoryCourseQueryDTO;
import com.wunding.learn.course.service.client.query.CourseDetailClientQuery;
import com.wunding.learn.course.service.client.query.CourseHomePageQuery;
import com.wunding.learn.course.service.client.query.CourseListQuery;
import com.wunding.learn.course.service.client.query.DownloadShiroQuery;
import com.wunding.learn.course.service.client.query.FavoriteCourseQuery;
import com.wunding.learn.course.service.client.query.MyCourseQuery;
import com.wunding.learn.course.service.client.query.PostCourseClientQuery;
import com.wunding.learn.course.service.client.query.RecommendCourseQuery;
import com.wunding.learn.course.service.client.query.RelatedCourseQuery;
import com.wunding.learn.course.service.client.query.SaveGradeCourseStarDTO;
import com.wunding.learn.course.service.client.query.SubordinateCourseQuery;
import com.wunding.learn.course.service.model.Course;
import com.wunding.learn.user.api.dto.AbilityRelateCourseDTO;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 课程表 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">余学益</a>
 * @date 2022-05-10
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")})
public interface CourseMapper extends BaseMapper<Course> {

    /**
     * 得到 课程 或者课件的综合评星
     *
     * @param saveGradeCourseStarDTO
     * @return
     */
    BigDecimal getCourseCommonStar(@Param("saveGradeCourseStarDTO") SaveGradeCourseStarDTO saveGradeCourseStarDTO);

    /**
     * 获取课程列表
     *
     * @param courseQuery
     * @return
     */
    List<CourseListDTO> findCourseListByPage(@Param("params") CourseQuery courseQuery);


    /**
     * 根据开始结束时间、课程名称查询用户创建的课程
     *
     * @param courseSummaryListQuery
     * @return
     */
    List<LecturerCourseDetailDTO> findCourseSummaryListByPage(
        @Param("params") CourseSummaryListQuery courseSummaryListQuery);

    /**
     * 获取培训项目课程列表
     *
     * @param courseQuery
     * @return
     */
    List<TrainCourseListDTO> trainCourseList(@Param("params") TrainCourseQuery courseQuery);

    /**
     * 获取岗位课程列表
     *
     * @param postCourseClientQuery 查询对象
     * @return 岗位课程列表
     */
    List<PostCourseClientDTO> getPostCourseList(@Param("params") PostCourseClientQuery postCourseClientQuery);

    /**
     * 获取课程详细信息
     *
     * @param courseDetailClientQuery 查询对象
     * @return 课程详细信息
     */
    CourseDetailClientDTO getCourseDetail(@Param("params") CourseDetailClientQuery courseDetailClientQuery);

    /**
     * 获取所属课件
     *
     * @param courseDetailClientQuery 查询对象
     * @return 所属课件
     */
    List<CoursewareDetailClientDTO> getCourseWareList(@Param("params") CourseDetailClientQuery courseDetailClientQuery);

    /**
     * 获取学习记录
     *
     * @param baseEntity 分页对象
     * @return 学习记录
     */
    List<StudyRecordDTO> getViewCourseList(@Param("params") BaseEntity baseEntity);

    /**
     * 获取课程列表
     *
     * @param courseListQuery 查询对象
     * @return
     */
    List<com.wunding.learn.course.service.client.dto.CourseListDTO> getCourseList(
        @Param("params") CourseListQuery courseListQuery);

    /**
     * 获取相关课程
     *
     * @param relatedCourseQuery 查询对象
     * @return
     */
    List<RelatedCourseDTO> getRelatedCourse(@Param("params") RelatedCourseQuery relatedCourseQuery);

    /**
     * 获取我的未学完课程
     *
     * @param myCourseQuery
     * @return
     */
    List<MyCourseDTO> getMyNoFinishCourse(@Param("params") MyCourseQuery myCourseQuery);

    /**
     * 获取我的已学完课程
     *
     * @param myCourseQuery
     * @return
     */
    List<MyCourseDTO> getMyFinishCourse(@Param("params") MyCourseQuery myCourseQuery);

    /**
     * 获取推荐课程
     *
     * @param recommendCourseQuery
     * @return
     */
    List<RecommendCourseDTO> getRecommendCourse(@Param("params") RecommendCourseQuery recommendCourseQuery);

    /**
     * 获取课程下已发布课件数
     *
     * @param ids
     * @return
     */
    List<CourseListDTO> getCWNumByCourseIds(@Param("ids") List<String> ids);

    /**
     * 获取用户指定课程中的已学数
     *
     * @param userId
     * @param courseIds
     * @return
     */
    Integer getCourseLearnNum(@Param("userId") String userId, @Param("courseIds") Collection<String> courseIds);

    /**
     * 根据渠道id 获取一个渠道信息
     *
     * @param contentId 内容识别
     * @return {@link CerDitchDTO}
     */
    CerDitchDTO getDitch(String contentId);

    /**
     * 根据主键查询课程，包含已删除
     *
     * @param id
     * @return
     */
    Course selectOneCourseIncludeDel(@Param("id") String id);

    /**
     * 235 获取下属课程列表
     *
     * @param subordinateCourseQuery
     * @return
     */
    List<SubordinateCourseDTO> subordinateCourseList(@Param("params") SubordinateCourseQuery subordinateCourseQuery);

    /**
     * 自动发布课程列表
     *
     * @return
     */
    List<Course> getAutoPublishCourseList();

    /**
     * 自动发布课程
     */
    void autoPublishCourse();

    /**
     * 过滤获取已被删除的资源id
     *
     * @param courseIdList
     * @return
     */
    List<String> getInvalidCourseId(@Param("courseIdList") Collection<String> courseIdList);


    /**
     * 查询喜欢的课程
     *
     * @param favoriteCourseQuery
     * @return
     */
    List<com.wunding.learn.course.service.client.dto.CourseListDTO> selectFavoriteCourseList(
        FavoriteCourseQuery favoriteCourseQuery);


    /**
     * 获取推荐课程数量
     *
     * @param courseHomePageQuery
     * @return
     */
    int getRecommendCourseCount(CourseHomePageQuery courseHomePageQuery);


    /**
     * 首页-猜你喜欢课程
     *
     * @param courseHomePageQuery
     * @return
     */
    List<com.wunding.learn.course.service.client.dto.CourseListDTO> guessYouLikeForHomePage(
        CourseHomePageQuery courseHomePageQuery);


    /**
     * 猜你喜欢课程
     *
     * @param courseHomePageQuery
     * @return
     */
    List<com.wunding.learn.course.service.client.dto.CourseListDTO> guessYouLike(
        CourseHomePageQuery courseHomePageQuery);


    /**
     * 热门课程
     *
     * @param courseHomePageQuery
     * @return
     */
    List<com.wunding.learn.course.service.client.dto.CourseListDTO> selectPopularCourse(
        CourseHomePageQuery courseHomePageQuery);


    /**
     * 获取岗位课程
     *
     * @param courseHomePageQuery
     * @return
     */
    List<com.wunding.learn.course.service.client.dto.CourseListDTO> getPostCourse(
        CourseHomePageQuery courseHomePageQuery);


    /**
     * 获取正在学习的课程且 未完成
     *
     * @param courseHomePageQuery
     * @return
     */
    List<com.wunding.learn.course.service.client.dto.CourseListDTO> getLearningCourse(
        CourseHomePageQuery courseHomePageQuery);

    /**
     * 根据分类获取课程列表
     *
     * @param categoryCourseQueryDTO
     * @return
     */
    List<CategoryCourseDTO> getCategoryCourseList(CategoryCourseQueryDTO categoryCourseQueryDTO);

    /**
     * 根据课程Id获取返回的点击数量
     *
     * @param ids
     * @return
     */
    List<CourseValueDTO> getClickNumberByCourseIds(@Param("ids") Collection<String> ids);

    /**
     * 根据课程Id获取返回的投票数
     *
     * @param ids
     * @return
     */
    List<CourseValueDTO> getVoteNumberByCourseIds(@Param("ids") Collection<String> ids);

    /**
     * @param ids
     * @param userId
     * @return
     */
    List<CourseValueDTO> getIsVoteByCourseIdsAndCurrentUserId(@Param("ids") Collection<String> ids,
        @Param("userId") String userId);

    /**
     * 自增课程浏览量
     *
     * @param courseId
     */
    void addCourseClickNumber(@Param("courseId") String courseId);

    /**
     * 自增课程投票点赞
     *
     * @param courseId
     */
    void addCourseVoteNumber(String courseId);

    /**
     * 获取单个课程信息(不过滤已被删除课程)
     *
     * @param id
     * @return
     */
    CourseInfoDTO getRealityById(@Param("id") String id);

    /**
     * 获取子组织id列表
     *
     * @param levelPathList 组织path ID
     * @return {@link List}<{@link String}>
     */
    int getOrgIdList(@Param("list") Collection<String> levelPathList, @Param("orgList") Collection<String> childOrgIds);

    /**
     * 获取父级路径
     *
     * @param parentId 父id
     * @return {@link List}<{@link String}>
     */
    List<String> getParentLevelPath(String parentId);

    /**
     * 获取该课程互动详情
     *
     * @param courseId
     * @return
     */
    ProjectTaskCourseInteractDTO getCourseInteract(@Param("courseId") String courseId);

    /**
     * 根据课件ID获取课程
     *
     * @param cwId
     * @return
     */
    Course getCourseByCwId(@Param("cwId") String cwId);

    /**
     * 获取课程缓存
     *
     * @param id 课程id
     * @return {@link CourseCashDTO}
     */
    CourseCashDTO getCourseCashById(@Param("id") String id);

    /**
     * 批量获取课程缓存
     *
     * @param cIds 课程id集合
     * @return {@link List}<{@link CourseCashDTO}>
     */
    List<CourseCashDTO> getCourseCashByIds(@Param("cIds") Set<String> cIds);

    /**
     * @param id
     */
    void updateLikeNum(String id);

    /**
     * @param id
     */
    void updateViewNum(String id);

    /**
     * 推荐课程
     *
     * @param courseHomePageQuery
     * @return
     */
    List<com.wunding.learn.course.service.client.dto.CourseListDTO> selectRecommendCourse(
        CourseHomePageQuery courseHomePageQuery);

    /**
     * 查询当前登录人是否有下载课程课件的权限
     *
     * @param query
     * @return
     */
    Integer getCourseDownloadShiro(DownloadShiroQuery query);

    List<BusinessCourseListDTO> getTrainCourseList(
        @Param("params") CourseQuery courseQuery);

    /**
     * 校验子级是否都在父级下
     *
     * @param parentViewOrgIdList
     * @param childrenViewOrgIdList
     * @return
     */
    int checkChildrenOrg(@Param("parentViewOrgIdList") Set<String> parentViewOrgIdList,
        @Param("childrenViewOrgIdList") Set<String> childrenViewOrgIdList);

    /**
     * 获取用户创建的课程
     *
     * @param userId
     * @return
     */
    List<CourseSimpleInfoDTO> getCourseByUserId(String userId);

    /**
     * 批量获取课程（无视删除状态）
     *
     * @param courseIdList
     * @return
     */
    List<Course> getRealityCourseList(@Param("courseIdList") Collection<String> courseIdList);

    /**
     * 获取课程基本信息
     *
     * @param resourceBaseQuery
     * @return
     */
    List<ResourceBaseDTO> getCourseBaseList(@Param("params") ResourceBaseQuery resourceBaseQuery);

    List<AbilityChooseCourseDTO> getAbilityChooseRelateCourseList(@Param("params") AbilityChooseCourseQuery query,
        @Param("courseIds") List<String> courseIds);

    Course getCourseInfoByCourseNo(@Param("courseNo") String courseNo);

    List<AbilityChooseCourseDTO> getCourseTimeByCourseId(@Param("courseIdList") List<String> courseIdList);

    List<AbilityRelateCourseDTO> selectCourseListByCourseId(@Param("courseIdList") Set<String> courseIdList);

    /**
     * 批量获取课程学学习详情
     *
     * @param courseLearnQuery
     * @return
     */
    List<CourseLearnDetailDTO> getCourseLearnDetail(@Param("params") CourseLearnQuery courseLearnQuery);

    /**
     * 获取创建的课程的简单信息
     *
     * @param userId
     * @return
     */
    List<CourseInfoDTO> getCreateCourse(String userId);

    /**
     * 获取课程是否删除
     *
     * @param id
     * @return
     */
    int getCourseIsDelById(String id);

    List<CertificationContentDTO> getCertificationContentList(@Param("batchIds") Collection<String> batchIds);


    /**
     * 根据用户管理范围的组织信息和资源ID校验是否具有资源管理权限
     *
     * @param userManageAreaOrgId 用户管辖范围ID列表
     * @param id                  课程ID
     * @return 0 无权限，1 有权限
     */
    Integer checkCourseManagePermissions(@Param("userManageAreaOrgId") Collection<String> userManageAreaOrgId,
        @Param("id") String id);

    /**
     * 获取所属课件
     *
     * @param courseDetailClientQuery 查询对象
     * @return 所属课件
     */
    List<CoursewareDetailClientDTO> getCwListInfoByCourseId(
        @Param("params") CourseDetailClientQuery courseDetailClientQuery);

    /**
     * <p>  减少课程总点赞数
     *
     * <AUTHOR>
     * @since 2024/5/20
     */
    void reduceCourseVoteNumber(String courseId);

    /**
     * 查询剩余待学时长
     *
     * @param courseId      课程id
     * @param currentUserId 用户id
     * @return {@link CourseWareLearnDetailDTO}
     */
    List<CourseWareLearnDetailDTO> getCoursewareAwaitLearnDurationList(@Param("courseId") String courseId,
        @Param("currentUserId") String currentUserId);

    /**
     * 修改课件评论数
     *
     * @param coursewareId  课件id
     * @param commentNumber 评论数
     */
    void updateCoursewareComment(@Param("coursewareId") String coursewareId,
        @Param("commentNumber") Integer commentNumber);

    /**
     * 更新课程的总时长
     * @param courseSumPlayTime 课件总时长
     * @param id 课程id
     */
    void updateCourseSumPlayTime(@Param("courseSumPlayTime") Integer courseSumPlayTime, @Param("id") String id);

    /**
     * 获取课程的删除信息
     *
     * @param resourceId
     * @return ResourceDeleteInfoDTO
     */
    ResourceDeleteInfoDTO getCourseDeleteInfoById(String resourceId);

    /**
     * 获取课程删除状态
     *
     * @param courseIds
     * @return
     */
    List<CourseInfoDTO> getCourseIsDel(Collection<String> courseIds);

    /**
     * 自增课程PV
     *
     * @param courseId
     */
    void addCoursePV(@Param("courseId") String courseId);

    /**
     * 获取AI问答课程
     *
     * @param courseHomePageQuery
     * @return
     */
    List<com.wunding.learn.course.service.client.dto.CourseListDTO> getAICourse(
        CourseHomePageQuery courseHomePageQuery);

    boolean isExistCourseNeedAudit(@Param("courseIds") List<String> courseIds);

    @Deprecated
    List<CourseProcessListDTO> selectMyApplyCourseAudit(@Param("params") CourseProcessQuery query);

    @Deprecated
    List<CourseProcessListDTO> selectMyDealCourseAudit(@Param("params") CourseProcessQuery query);

    @Deprecated
    List<CourseProcessListDTO> selectAllCourseAudit(@Param("params") CourseProcessQuery query);

    /**
     * 获取最新课程（按创建时间/发布时间倒序）
     *
     * @param courseHomePageQuery
     * @return
     */
    List<com.wunding.learn.course.service.client.dto.CourseListDTO> getNewCourse(
        CourseHomePageQuery courseHomePageQuery);

    /**
     * 获取可见课程总数
     *
     * @param currentUserId     用户id
     * @param currentOrgId      组织id
     * @param managerAreaOrgIds 管辖范围
     * @return 总数
     */
    Integer getCourseTotal(@Param("currentUserId") String currentUserId, @Param("currentOrgId") String currentOrgId,
        @Param("managerAreaOrgIds") Set<String> managerAreaOrgIds);

    /**
     * 获取各分类总数
     *
     * @param currentUserId     用户id
     * @param currentOrgId      组织id
     * @param managerAreaOrgIds 管辖范围
     * @return {@link CourseCategoryStatDTO}
     */
    List<CourseCategoryStatDTO> getCourseCategoryStat(@Param("currentUserId") String currentUserId,
        @Param("currentOrgId") String currentOrgId,
        @Param("managerAreaOrgIds") Set<String> managerAreaOrgIds);

    /**
     * 查询已发布的课程id
     * @param courseIds
     * @return
     */
    List<String> listPublishedCourseIds(@Param("courseIds") List<String> courseIds);

    Integer getCourseSumPlayTime(@Param("courseId")String courseId);
}
