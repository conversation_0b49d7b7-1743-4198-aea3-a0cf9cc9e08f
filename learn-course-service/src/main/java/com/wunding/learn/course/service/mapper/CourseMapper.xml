<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.course.service.mapper.CourseMapper">
    <!-- 开启二级缓存 -->
    <!--
<cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.course.service.mapper.CourseMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.course.service.model.Course">
        <!--@Table course-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="course_no" jdbcType="VARCHAR"
          property="courseNo"/>
        <result column="course_name" jdbcType="VARCHAR"
          property="courseName"/>
        <result column="course_cate_id" jdbcType="VARCHAR"
          property="courseCateId"/>
        <result column="item_id" jdbcType="VARCHAR"
          property="itemId"/>
        <result column="relevance_id" jdbcType="VARCHAR"
          property="relevanceId"/>
        <result column="course_type" jdbcType="VARCHAR"
          property="courseType"/>
        <result column="study_type" jdbcType="VARCHAR"
          property="studyType"/>
        <result column="cover_image_url" jdbcType="VARCHAR"
          property="coverImageUrl"/>
        <result column="descriptions" jdbcType="LONGVARCHAR"
          property="descriptions"/>
        <result column="credit" jdbcType="DECIMAL"
          property="credit"/>
        <result column="score" jdbcType="DECIMAL"
          property="score"/>
        <result column="org_id" jdbcType="VARCHAR"
          property="orgId"/>
        <result column="author" jdbcType="VARCHAR"
          property="author"/>
        <result column="is_required" jdbcType="TINYINT"
          property="isRequired"/>
        <result column="is_sign" jdbcType="TINYINT"
          property="isSign"/>
        <result column="is_audit" jdbcType="TINYINT"
          property="isAudit"/>
        <result column="is_download" jdbcType="TINYINT"
          property="isDownload"/>
        <result column="is_comment" jdbcType="TINYINT"
          property="isComment"/>
        <result column="is_vote" jdbcType="TINYINT"
          property="isVote"/>
        <result column="is_first" jdbcType="TINYINT"
          property="isFirst"/>
        <result column="is_recommend" jdbcType="TINYINT"
          property="isRecommend"/>
        <result column="is_share" jdbcType="TINYINT"
          property="isShare"/>
        <result column="is_favorite" jdbcType="TINYINT"
          property="isFavorite"/>
        <result column="is_allow_share" jdbcType="TINYINT"
          property="isAllowShare"/>
        <result column="is_available" jdbcType="TINYINT"
          property="isAvailable"/>
        <result column="is_del" jdbcType="TINYINT"
          property="isDel"/>
        <result column="view_type" jdbcType="INTEGER"
          property="viewType"/>
        <result column="is_publish" jdbcType="TINYINT"
          property="isPublish"/>
        <result column="publish_by" jdbcType="VARCHAR"
          property="publishBy"/>
        <result column="publish_time" jdbcType="TIMESTAMP"
          property="publishTime"/>
        <result column="publish_path" jdbcType="VARCHAR"
          property="publishPath"/>
        <result column="sort_no" jdbcType="INTEGER"
          property="sortNo"/>
        <result column="click_number" jdbcType="BIGINT"
          property="clickNumber"/>
        <result column="comment_number" jdbcType="INTEGER"
          property="commentNumber"/>
        <result column="download_number" jdbcType="INTEGER"
          property="downloadNumber"/>
        <result column="vote_number" jdbcType="INTEGER"
          property="voteNumber"/>
        <result column="favorite_number" jdbcType="INTEGER"
          property="favoriteNumber"/>
        <result column="share_in_number" jdbcType="INTEGER"
          property="shareInNumber"/>
        <result column="source" jdbcType="VARCHAR"
          property="source"/>
        <result column="exam_id" jdbcType="VARCHAR"
          property="examId"/>
        <result column="create_by" jdbcType="VARCHAR"
          property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP"
          property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR"
          property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP"
          property="updateTime"/>
        <result column="dev_time" jdbcType="VARCHAR"
          property="devTime"/>
        <result column="language" jdbcType="VARCHAR"
          property="language"/>
        <result column="version" jdbcType="VARCHAR"
          property="version"/>
        <result column="course_level" jdbcType="VARCHAR"
          property="courseLevel"/>
        <result column="share_by" jdbcType="VARCHAR"
          property="shareBy"/>
        <result column="share_time" jdbcType="TIMESTAMP"
          property="shareTime"/>
        <result column="is_train" jdbcType="TINYINT"
          property="isTrain"/>
        <result column="csort_no" jdbcType="INTEGER"
          property="csortNo"/>
        <result column="auto_publish_time" jdbcType="TIMESTAMP"
          property="autoPublishTime"/>
        <result column="is_public" jdbcType="TINYINT"
          property="isPublic"/>
        <result column="is_create_sign" jdbcType="TINYINT"
          property="isCreateSign"/>
        <result column="is_create_assess" jdbcType="TINYINT"
          property="isCreateAssess"/>
        <result column="is_hang_up" jdbcType="TINYINT"
          property="isHangUp"/>
        <result column="hang_up_duration_minute" jdbcType="VARCHAR"
          property="hangUpDurationMinute"/>
        <result column="hang_up_duration_second" jdbcType="VARCHAR"
          property="hangUpDurationSecond"/>
        <result column="recommend_time" jdbcType="TIMESTAMP"
          property="recommendTime"/>
        <result column="is_use_default_img" jdbcType="TINYINT"
          property="isUseDefaultImg"/>
        <result column="consume_excitation_num" jdbcType="DECIMAL"
          property="consumeExcitationNum"/>
        <result column="consume_excitation_type" jdbcType="VARCHAR"
          property="consumeExcitationType"/>
        <result column="download_org_id" jdbcType="VARCHAR"
          property="downloadOrgId"/>
        <result column="course_category_id" jdbcType="VARCHAR"
          property="courseCategoryId"/>
        <result column="is_allow_note" jdbcType="TINYINT"
          property="isAllowNote"/>
        <result column="is_audit_note" jdbcType="TINYINT"
          property="isAuditNote"/>
        <result column="limit_show" jdbcType="TINYINT"
          property="limitShow"/>
        <result column="source_type" jdbcType="TINYINT"
          property="sourceType"/>
        <result column="source_id" jdbcType="VARCHAR"
          property="sourceId"/>
        <result column="upload_way" jdbcType="TINYINT"
          property="uploadWay"/>
        <result column="sum_play_time" jdbcType="INTEGER"
          property="sumPlayTime"/>
        <result column="audit_status" jdbcType="VARCHAR"
          property="auditStatus"/>
        <result column="register_time" jdbcType="TIMESTAMP"
          property="registerTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <select id="getParentCate" resultType="com.wunding.learn.course.service.admin.dto.CourseCategoryTreeDTO"
      useCache="false">
        select c.id,
               c.sort_no,
               c.category_name as                                           name,
               c.category_level,
               c.parent_id     as                                           pid,
               (select category_name from categorys where id = c.parent_id) pName
        from categorys c
        where c.category_level = 1
          and c.category_type = 'CourseTagNewCate'
          and c.is_available = 1
          and c.is_del = 0
        order by c.sort_no
    </select>

    <select id="findCourseListByPage" resultType="com.wunding.learn.course.service.admin.dto.CourseListDTO"
      useCache="false">
        select c.id,
               c.course_no,
               c.course_name,
               ct.category_name  courseCateName,
               c.course_cate_id,
               c.author,
               c.is_publish,
               c.org_id,
               c.is_share,
               c.is_recommend,
               c.publish_by,
               c.create_by,
               c.publish_time,
               c.create_time,
               c.cover_image_url coverImageUrl,
               c.is_train,
               c.org_id,
               g.org_name,
               c.pv,
               c.buildin_flag,
               c.update_time,
               round((select sum(cw.play_time) from courseware cw where cw.course_id = c.id and cw.is_del = 0) / 60,
                     1)          courseTime,
               c.audit_status
        from course c
                 inner join sys_org g on g.id = c.org_id
                 left join categorys ct on ct.id = c.course_cate_id
                 left join resource_directory rd on c.id = rd.resource_id
        where c.is_del = 0
          and c.is_copy = 0
        <if test="null != params.isPublish">
            and c.is_publish = #{params.isPublish}
        </if>
        <if test="null != params.isShare">
            and c.is_share = #{params.isShare}
        </if>
        <if test="null != params.isTrain">
            and c.is_train in
            <foreach collection="params.isTrain.split(',')" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="null != params.isRecommend">
            and c.is_recommend = #{params.isRecommend}
        </if>
        <if test="null != params.courseCateId and params.courseCateId != ''">
            and ct.category_type = 'CourseCate'
            and ct.level_path like concat(ifnull((select level_path
                                                  from categorys
                                                  where id =
                                                        #{params.courseCateId}), ''), '%')
            and ct.is_del = 0
            and ct.is_available = 1
        </if>
        <if test="null != params.publishBeginTime">
            and date_format(c.publish_time, '%Y-%m-%d') >= date_format(#{params.publishBeginTime}, '%Y-%m-%d')
        </if>
        <if test="null != params.publishEndTime">
            and date_format(#{params.publishEndTime}, '%Y-%m-%d') >= date_format(c.publish_time, '%Y-%m-%d')
        </if>
        <if test="null != params.courseTags and params.courseTags != ''">
            and exists (select 1 from course_tag b where b.course_id = c.id
                                                     and b.tag_id in
            <foreach collection="params.courseTags.split(',')" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>)
        </if>
        <if test="null != params.courseName and params.courseName != ''">
            and instr(c.course_name, #{params.courseName}) > 0
        </if>
        <if test="null != params.author and params.author != ''">
            and instr(c.author, #{params.author}) > 0
        </if>
        <if test="null != params.courseNo and params.courseNo != ''">
            and instr(c.course_no, #{params.courseNo}) > 0
        </if>
        <if test="null != params.createAndUnderOrgIds and params.createAndUnderOrgIds.size() > 0">
            <foreach close=")" collection="params.createAndUnderOrgIds" item="item" open="and c.org_id in("
              separator=",">
                #{item}
            </foreach>
        </if>
        <if test="params.managerAreaOrgIds != null and params.managerAreaOrgIds.size() > 0">
            and (
            <foreach collection="params.managerAreaOrgIds" item="levelPath" open="(" close=")" separator="or">
                g.level_path like concat(#{levelPath}, '%')
            </foreach>
            or c.org_id = #{params.currentOrgId} or c.create_by = #{params.currentUserId}
                )
        </if>
        <if test="params.relevanceId != null and params.relevanceId != ''">
            and c.relevance_id = #{params.relevanceId}
        </if>
        <if test="params.directoryId != null and params.directoryId != ''">
            and rd.directory_id = #{params.directoryId}
        </if>
        <if test="params.updateRecordTimeStart != null">
            and c.update_time >= #{params.updateRecordTimeStart}
        </if>
        <if test="params.updateRecordTimeEnd != null">
            and #{params.updateRecordTimeEnd} >= c.update_time
        </if>
        <if test="null != params.sourceType and params.sourceType == 0">
            and c.source_type = 0
        </if>
        <if test="null != params.sourceType and params.sourceType == 1">
            and c.source_type = 1
        </if>
        <if test="null != params.sourceType and params.sourceType == 2">
            and c.buildin_flag = 1
        </if>
        <if test="null != params.buildinFlag and params.buildinFlag == 1">
            and c.buildin_flag = 1
        </if>
        <if test="params.createBy != null and params.createBy != ''">
            and c.create_by = #{params.createBy}
        </if>
        <choose>
            <when test="params.createBy != null and params.createBy != ''">
                order by c.create_time desc, c.id desc
            </when>
            <otherwise>
                order by c.update_time desc, c.is_publish,c.publish_time desc,c.create_time desc
            </otherwise>
        </choose>
    </select>

    <select id="findCourseSummaryListByPage" resultType="com.wunding.learn.common.dto.LecturerCourseDetailDTO"
      useCache="false">
        SELECT c.id,
               c.course_name courseName,
               c.create_time uploadTime,
               c.upload_way  uploadType,
               c.vote_number totalAgree
        FROM course c
        where c.create_by = #{params.userId}
        <if test="null != params.uploadBeginDate">
            and DATE_FORMAT(c.create_time, '%Y-%m-%d') >= DATE_FORMAT(#{params.uploadBeginDate}, '%Y-%m-%d')
        </if>
        <if test="null != params.uploadEndDate">
            and DATE_FORMAT(#{params.uploadEndDate}, '%Y-%m-%d') >= DATE_FORMAT(c.create_time, '%Y-%m-%d')
        </if>
        <if test="null != params.courseName and params.courseName != ''">
            and INSTR(c.course_name, #{params.courseName}) > 0
        </if>
        ORDER BY c.create_time DESC
    </select>


    <select id="trainCourseList" resultType="com.wunding.learn.course.service.admin.dto.TrainCourseListDTO"
      useCache="false">
        select distinct c.id,
                        c.course_no,
                        c.course_name,
                        ct.category_name  courseCateName,
                        c.course_cate_id,
                        c.author,
                        c.is_publish,
                        c.org_id,
                        c.is_share,
                        c.is_recommend,
                        c.publish_by,
                        c.create_by,
                        c.publish_time,
                        c.create_time,
                        c.cover_image_url coverImageUrl,
                        c.update_time,
                        c.is_train
        from course c
                 inner join sys_org g on g.id = c.org_id
                 left join categorys ct on ct.id = c.course_cate_id
                 left join resource_directory rd on c.id = rd.resource_id
        where c.is_del = 0
          and c.is_copy = 0
        <if test="null != params.courseId">
            and c.id = #{params.courseId}
        </if>
        <if test="null != params.isPublish">
            and c.is_publish = #{params.isPublish}
        </if>
        <if test="null != params.isShare">
            and c.is_share = #{params.isShare}
        </if>
        <!-- 根据课程资源类型去查对应的课程 并上 对应关联资源的课程 -->
        <if test="null != params.sourceType">
            <choose>
                <when test="params.sourceType == 0 and null != params.relevanceId">
                    and (c.is_train = '0' or (c.relevance_id = #{params.relevanceId} and c.is_train = '3'))
                </when>
                <when test="params.sourceType == 3">
                    and (c.relevance_id = #{params.relevanceId} and c.is_train = '3')
                </when>
            </choose>
        </if>
        <if test="null != params.isRecommend">
            and c.is_recommend = #{params.isRecommend}
        </if>
        <if test="null != params.courseCateId and params.courseCateId != ''">
            and ct.category_type = 'CourseCate'
            and ct.level_path like concat(ifnull((select level_path
                                                  from categorys
                                                  where id =
                                                        #{params.courseCateId}), ''), '%')
            and ct.is_del = 0
            and ct.is_available = 1
        </if>
        <if test="null != params.publishBeginTime">
            and date_format(c.publish_time, '%Y-%m-%d') >= date_format(#{params.publishBeginTime}, '%Y-%m-%d')
        </if>
        <if test="null != params.publishEndTime">
            and date_format(#{params.publishEndTime}, '%Y-%m-%d') >= date_format(c.publish_time, '%Y-%m-%d')
        </if>
        <if test="null != params.courseTag and params.courseTag != ''">
            and exists (select 1 from course_tag b where b.course_id = c.id and b.tag_id = #{params.courseTag})
        </if>
        <if test="null != params.courseName and params.courseName != ''">
            and instr(c.course_name, #{params.courseName}) > 0
        </if>
        <if test="null != params.author and params.author != ''">
            and instr(c.author, #{params.author}) > 0
        </if>
        <if test="null != params.courseNo and params.courseNo != ''">
            and instr(c.course_no, #{params.courseNo}) > 0
        </if>
        <if test="null != params.createAndUnderOrgIds and params.createAndUnderOrgIds.size() > 0">
            <foreach collection="params.createAndUnderOrgIds" open="and c.org_id in(" close=")" item="item"
              separator=",">
                #{item}
            </foreach>
        </if>
        <if test="params.managerAreaOrgIds != null and params.managerAreaOrgIds.size() > 0">
            and (
            <foreach collection="params.managerAreaOrgIds" item="levelPath" open="(" close=")" separator="or">
                g.level_path like concat(#{levelPath}, '%')
            </foreach>
            or (c.org_id = #{params.currentOrgId} and c.create_by = #{params.currentUserId})
                or c.limit_show = 1
                )
        </if>
        <if test="params.directoryId != null and params.directoryId != ''">
            and rd.directory_id in
            <foreach collection="params.directoryId.split(',')" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        order by c.update_time desc, c.is_publish, c.publish_time desc, c.create_time desc
    </select>


    <select id="getPostCourseList" parameterType="com.wunding.learn.course.service.client.query.PostCourseClientQuery"
      resultType="com.wunding.learn.course.service.client.dto.PostCourseClientDTO" useCache="false">
        select distinct c.id,
                        c.course_name,
                        (select t.category_name from categorys t where t.id = c.course_cate_id)          courseCateName,
                        (select count(a.id) from courseware a where a.course_id = c.id and a.is_del = 0) cwCount,
                        c.publish_time,
                        c.click_number                                                                   clickNumber,
                        c.vote_number                                                                    voteNumber,
                        (select count(cvt.id)
                         from course_vote cvt
                         where cvt.course_id = c.id
                           and cvt.create_by = #{params.currentUserId})
                                                                                                         isVote,
                        (select count(cs.id) from course_star cs where cs.course_id = c.id)              starCount,
                        (select ifnull(round(avg(star_count), 1), 0)
                         from course_star cs
                         where cs.course_id = c.id)                                                      commonStar,
                        (select ifnull(max(star_count), 0)
                         from course_star cs
                         where cs.course_id = c.id
                           and create_by = #{params.currentUserId})                                      myStar,
                        c.is_comment,
                        c.is_download,
                        c.is_favorite,
                        c.is_share,
                        c.credit,
                        c.descriptions,
                        c.consume_excitation_num,
                        c.consume_excitation_type
        from course c,
             course_category cc
        where c.id = cc.course_id
          and cc.category_type = #{params.cType}
          and c.is_del = 0
          and c.is_publish = 1
        <if test="params.courseType != null and params.courseType != ''">
            and c.course_type like concat(concat('%', #{params.courseType}), '%')
        </if>
        <if test="params.identityId != null">
            and cc.category_id = #{params.identityId}
        </if>
        order by c.publish_time desc
    </select>

    <select id="getCourseDetail" parameterType="com.wunding.learn.course.service.client.query.CourseDetailClientQuery"
      resultType="com.wunding.learn.course.service.client.dto.CourseDetailClientDTO" useCache="false">
        select c.id,
               c.course_name,
               c.author,
               c.publish_by,
               c.is_publish,
               c.course_no       code,
               c.sum_play_time   sumPlayTime,
               c.course_cate_id  cateId,
               c.publish_time,
               c.vote_number     voteNumber,
               c.favorite_number favoriteNumber,
               c.is_comment,
               c.is_download,
               c.is_share,
               c.descriptions,
               c.course_type,
               c.is_publish,
               c.is_train,
               c.consume_excitation_type,
               c.consume_excitation_num,
               c.is_allow_note,
               c.click_number    clickNumber,
               c.comment_number  commentNumber,
               c.source_type,
               c.source_id,
               c.pv + 1 as       pv
        <if test="params.itemId == null or params.itemId == ''">
            ,
                c.is_hang_up,
                c.hang_up_duration_minute,
                c.hang_up_duration_second
        </if>
        from course c
        where c.is_del = 0
          and c.id = #{params.id}
    </select>

    <select id="getCourseWareList"
      parameterType="com.wunding.learn.course.service.client.query.CourseDetailClientQuery"
      resultType="com.wunding.learn.course.service.client.dto.CoursewareDetailClientDTO" useCache="false">
        select cw.id,
               cw.cw_name,
               cw.chapter_id,
               cw.mime,
               cw.cw_type        as                                                                type,
               cw.descriptions,
               cw.class_hour,
               cw.course_id,
               cw.real_play_time as                                                                playTime,
               cw.play_time      as                                                                setPlayTime,
               (select c.publish_time from course c where c.id = cw.course_id)                     publishTime,
               (select count(cvw.id) from course_view cvw where cvw.course_id = cw.course_id and cw.id = cvw.cw_id)
                                                                                                   clickNumber,
               cw.sort_no,
               cw.create_time,
               cw.exam_id,
               cw.create_by,
               cw.version,
               cw.is_re_upload,
               c.is_download     as                                                                isDownload,
               (select count(1) from courseware_user_record cwur where cwur.courseware_id = cw.id) learnedCount
        from courseware cw
            left join course c on c.id = cw.course_id
        <if
          test="params.isIgnoreView != null and params.isIgnoreView == 0 and params.currentUserId != null and params.currentUserId != ''">
            left join w_resource_view_limit rv on c.id = rv.resource_id and rv.resource_type = 'CourseViewLimit'
        </if>
        <where>
            cw.is_del = 0
              and cw.is_available = 1
              and cw.transform_status = 2
              and c.is_del = 0
              and c.is_publish = 1
            <if test="params.id != null and params.id != ''">
                and cw.course_id = #{params.id}
            </if>
            <if test="params.searchKey != null and params.searchKey != ''">
                and instr(cw.cw_name, #{params.searchKey}) > 0
            </if>
            <if
              test="params.isIgnoreView != null and params.isIgnoreView == 0 and params.currentUserId != null and params.currentUserId != ''">
                and (exists(select 1
                            from w_view_limit_user v
                            where rv.view_limit_id = v.view_limit_id
                              and v.user_id =
                                  #{params.currentUserId}))
            </if>
        </where>
        order by cw.sort_no, cw.id
    </select>

    <select id="getCourseCommonStar" resultType="java.math.BigDecimal" useCache="false">
        select ifnull(round(avg(star_count), 1), 0.0) as commonStar from
        <if test="saveGradeCourseStarDTO.flag == 'course'">
            course_star where course_id = #{saveGradeCourseStarDTO.id}
        </if>
        <if test="saveGradeCourseStarDTO.flag == 'courseware'">
            courseware_star
            where cw_id = #{saveGradeCourseStarDTO.id}
        </if>
    </select>


    <select id="getViewCourseList" resultType="com.wunding.learn.course.service.client.dto.StudyRecordDTO"
      useCache="false">
        select tbl0.id,
               tbl0.course_name,
               tbl2.lastStudyTime
        from course tbl0,
             (select t1.course_id id, ymd, max(t1.view_time) lastStudyTime
              from (select cv.course_id, cv.view_time, date_format(date(cv.view_time), '%Y-%m-%d') ymd
                    from course_view cv
                             inner join course c on cv.course_id = c.id and cv.view_by = #{params.currentUserId}) t1
              group by course_id, ymd) tbl2
        where tbl0.id = tbl2.id
          and tbl0.is_train <![CDATA[ <> ]]> 3
        order by tbl2.lastStudyTime desc, tbl2.id desc
    </select>

    <select id="getCourseList" parameterType="com.wunding.learn.course.service.client.query.CourseListQuery"
      resultType="com.wunding.learn.course.service.client.dto.CourseListDTO" useCache="false">
        select *,
               0 isVote
        from (
        select c.id,
               c.course_name,
               c.publish_time,
               c.click_number clickNumber,
               c.vote_number  voteNumber,
               c.consume_excitation_num,
               c.consume_excitation_type,
               c.comment_number
        <if test="null != params.learnStatusList and params.learnStatusList.size() > 0">
            ,
                ifnull((select is_learned
                        from user_course_record tur
                        where tur.course_id = c.id
                          and tur.user_id
                            = #{params.currentUserId}), -1)
                    learnStatus
        </if>
        from course c
            inner join w_resource_view_limit rv on c.id = rv.resource_id and rv.resource_type = 'CourseViewLimit'
            inner join categorys tg on tg.id = c.course_cate_id
        <if test="null != params.tagIds and params.tagIds != ''">
            left join course_tag ct on ct.course_id = c.id
            left join tag t on ct.tag_id = t.id
        </if>
        <if test="params.categoryId != null and params.categoryId != ''">
            and tg.level_path like concat((select level_path from categorys where id = #{params.categoryId}), '%')
        </if>
        where c.is_del = 0
          and c.is_publish = 1
          and c.is_copy = 0
          and c.is_train = 0
        <if test="params.currentUserId != null and params.currentUserId != ''">
            and (exists(select 1
                        from w_view_limit_user v
                        where rv.view_limit_id = v.view_limit_id
                          and v.user_id =
                              #{params.currentUserId}))
        </if>
        <if test="null != params.tagIds and params.tagIds != ''">
            <foreach close=")" collection="params.tagIds.split(',')" item="item" open=" and (" separator=" or ">
                instr(t.level_path, concat('/', #{item})) > 0
            </foreach>
        </if>
        and(
        <if test='params.courseType != null and params.courseType != ""'>
            c.course_type = concat(#{params.courseType,jdbcType=VARCHAR})
                or c.course_type = 'EM'
        </if>
        )
        ) t
        <where>
            <if test="params.searchKey != null and params.searchKey != ''">
                and (instr(t.course_name, #{params.searchKey}) > 0
                or t.id in (select tct.course_id
                            from course_tag tct
                                     inner join tag tt on tt.id = tct.tag_id
                                and instr(lower(tt.tag_name), #{params.searchKey}) > 0)
                )
            </if>
            <if test="null != params.learnStatusList and params.learnStatusList.size() > 0">
                and t.learnStatus in
                <foreach collection="params.learnStatusList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        order by t.${params.order}, t.id desc
    </select>

    <select id="getRelatedCourse" parameterType="com.wunding.learn.course.service.client.query.RelatedCourseQuery"
      resultType="com.wunding.learn.course.service.client.dto.RelatedCourseDTO" useCache="false">
        select c.id,
               c.course_name,
               c.click_number
        from course c
                 inner join w_resource_view_limit rv on c.id = rv.resource_id and rv.resource_type = 'CourseViewLimit'
        where c.is_del = 0
          and c.is_publish = 1
          and c.is_train = 0
          and c.is_copy = 0
          and c.id != #{params.id}
        <if test="params.currentUserId != null and params.currentUserId != ''">
            and (exists(select 1
                        from w_view_limit_user v
                        where rv.view_limit_id = v.view_limit_id
                          and v.user_id =
                              #{params.currentUserId}))
        </if>
        <if test="params.courseType != null and params.courseType != ''">
            and c.course_type like concat('%', #{params.courseType}, '%')
        </if>
        <if test="null != params.tagIds and params.tagIds.size() > 0">
            and ( exists(select 1 from course_tag ct where ct.course_id = c.id
            <foreach collection="params.tagIds" item="item" open=" and ct.tag_id in(" separator="," close=")">
                #{item}
            </foreach>
            ))
        </if>
        order by c.publish_time, c.id desc
    </select>

    <select id="getMyNoFinishCourse" parameterType="com.wunding.learn.course.service.client.query.MyCourseQuery"
      resultType="com.wunding.learn.course.service.client.dto.MyCourseDTO" useCache="false">
        with temp_courseids as (select c.id course_id
                                from course c
                                         left join (select tb1.course_id, tb1.is_learned
                                                    from user_course_record tb1
                                                    where tb1.is_learned = 1
                                                      and tb1.user_id = #{params.currentUserId}) ur
                                                   on c.id = ur.course_id
                                where c.is_publish = 1
                                  and c.is_del = 0
                                  and c.is_copy = 0
                                  and c.is_available = 1
                                  and ur.is_learned is null)
        select c.id,
               c.course_name,
               c.publish_time,
               c.click_number,
               c.comment_number,
               c.vote_number voteNumber,
               (select count(cvt.id)
                from course_vote cvt
                where cvt.course_id = c.id
                  and cvt.create_by = #{params.currentUserId})
                             isVote,
               c.consume_excitation_num,
               c.consume_excitation_type
        from course c
                 inner join temp_courseids on course_id = c.id
                 inner join w_resource_view_limit rv
                            on c.id = rv.resource_id and rv.resource_type = 'CourseViewLimit'
        where c.is_del = 0
          and c.is_copy = 0
          and c.is_publish = 1
          and c.is_train = 0
          and c.course_type like concat('%', #{params.courseType}, '%')
          and (exists(select 1
                      from w_view_limit_user v
                      where rv.view_limit_id = v.view_limit_id
                        and v.user_id =
                            #{params.currentUserId}))
        order by c.publish_time desc, c.id desc
    </select>

    <select id="getMyFinishCourse" parameterType="com.wunding.learn.course.service.client.query.MyCourseQuery"
      resultType="com.wunding.learn.course.service.client.dto.MyCourseDTO" useCache="false">
        with temp_courseids as (select tb1.course_id
                                from user_course_record tb1,
                                     course c
                                where c.id = tb1.course_id
                                  and c.is_publish = 1
                                  and c.is_del = 0
                                  and c.is_copy = 0
                                  and c.is_available = 1
                                  and tb1.is_learned = 1
                                  and tb1.user_id = #{params.currentUserId})
        select c.id,
               c.course_name,
               c.publish_time,
               c.click_number,
               c.comment_number,
               c.vote_number voteNumber,
               (select count(cvt.id)
                from course_vote cvt
                where cvt.course_id = c.id
                  and cvt.create_by = #{params.currentUserId})
                             isVote,
               c.consume_excitation_num,
               c.consume_excitation_type
        from course c
                 inner join temp_courseids on course_id = c.id
                 inner join w_resource_view_limit rv
                            on c.id = rv.resource_id and rv.resource_type = 'CourseViewLimit'
        where c.is_del = 0
          and c.is_copy = 0
          and c.is_publish = 1
          and c.is_train = 0
          and c.course_type like concat('%', #{params.courseType}, '%')
          and (exists(select 1
                      from w_view_limit_user v
                      where rv.view_limit_id = v.view_limit_id
                        and v.user_id =
                            #{params.currentUserId}))
        order by c.publish_time desc, c.id desc
    </select>

    <select id="getRecommendCourse" parameterType="com.wunding.learn.course.service.client.query.RecommendCourseQuery"
      resultType="com.wunding.learn.course.service.client.dto.RecommendCourseDTO" useCache="false">
        select c.id,
               c.course_name,
               c.click_number,
               c.comment_number,
               c.vote_number,
               c.consume_excitation_num,
               c.consume_excitation_type
        from course c
                 inner join w_resource_view_limit rv
                            on c.id = rv.resource_id and rv.resource_type = 'CourseViewLimit'
        where c.is_del = 0
          and c.is_publish = 1
          and c.is_recommend = 1
          and c.is_train = 0
          and c.is_copy = 0
          and c.course_type like concat(concat('%', #{params.courseType}), '%')
          and (exists(select 1
                      from w_view_limit_user v
                      where rv.view_limit_id = v.view_limit_id
                        and v.user_id =
                            #{params.currentUserId}))
        order by c.publish_time desc, c.id desc
    </select>

    <select id="getCWNumByCourseIds" resultType="com.wunding.learn.course.service.admin.dto.CourseListDTO"
      useCache="false">
        select c.course_name,
               (select count(cw.id)
                from courseware cw
                where cw.course_id = c.id
                  and cw.is_del = 0) courseWareNum
        from course c
        <where>
            c.source_type != '1'
              and c.id in
            <foreach close=")" collection="ids" index="" item="id" open="(" separator=",">
                #{id}
            </foreach>
        </where>
    </select>

    <select id="getCourseLearnNum" resultType="java.lang.Integer" useCache="false">
        select count(ur.course_id)
        from user_course_record ur
        where ur.course_id in
        <foreach collection="courseIds" item="courseId" open="(" close=")" separator=",">
            #{courseId}
        </foreach>
        and ur.is_learned = 1
        and ur.user_id = #{userId}
    </select>

    <select id="getDitch" resultType="com.wunding.learn.common.dto.CerDitchDTO">
        select id, course_name name, 1 state
        from course
        where is_del = 0
          and is_publish = 1
          and is_available = 1
          and id = #{contentId}
    </select>

    <select id="selectOneCourseIncludeDel" resultType="com.wunding.learn.course.service.model.Course">
        select
        <include refid="Base_Column_List"/>
        from course
        where id = #{id,jdbcType=VARCHAR}
    </select>

    <select id="subordinateCourseList"
      parameterType="com.wunding.learn.course.service.client.query.SubordinateCourseQuery"
      resultType="com.wunding.learn.course.service.client.dto.SubordinateCourseDTO" useCache="false">
        select b.id,
               b.course_name,
               a.is_learned,
               a.duration                     learnedTime,
               (select count(cw.id)
                from courseware cw
                where cw.course_id = b.id
                  and cw.is_del = 0
                  and cw.is_available = 1) as cwCount,
               (select count(cw.id)
                from courseware_user_record cwur,
                     courseware cw
                where cwur.courseware_id = cw.id
                  and cwur.course_id = b.id
                  and cwur.user_id = #{params.userId}
                  and cwur.is_learned = 1
                  and cw.is_del = 0
                  and cw.is_available = 1) as isLearnedCwCount,
               b.consume_excitation_num,
               b.consume_excitation_type
        from user_course_record a
                 inner join course b on a.course_id = b.id
        where a.create_time >= #{params.startTime}
          and a.create_time <![CDATA[ <= ]]> #{params.endTime}
          and a.is_del = 0
          and a.user_id = #{params.userId}
    </select>

    <select id="getAutoPublishCourseList" resultType="com.wunding.learn.course.service.model.Course">
        select
        <include refid="Base_Column_List"/>
        from course co
        where co.is_publish = 0
          and co.auto_publish_time <![CDATA[ <= ]]> now()
          and co.is_del = 0
          and co.is_publish = 0
          and (select count(cw.id) from courseware cw where cw.course_id = co.id and cw.is_del = 0) > 0
    </select>

    <select id="getInvalidCourseId" resultType="java.lang.String">
        select id
        from course
        where id in (
        <foreach collection="courseIdList" item="item" separator=",">
            #{item}
        </foreach>
        )
          and (is_del = 1 or is_publish = 0)
    </select>

    <select id="selectFavoriteCourseList"
      resultType="com.wunding.learn.course.service.client.dto.CourseListDTO" useCache="false">
        select cf.course_id   id,
               c.course_name  courseName,
               c.click_number clickNumber,
               c.vote_number  voteNumber
        from course_favorate cf
                 inner join course c on cf.course_id = c.id and c.is_del = 0 and c.is_publish = 1
        where cf.create_by = #{userId}
        order by cf.create_time desc
    </select>

    <select id="getRecommendCourseCount" resultType="java.lang.Integer" useCache="false">
        select count(1)
        from (select c.id,
                     c.course_name
              from course c
                       inner join w_resource_view_limit rv on c.id = rv.resource_id
                  and rv.resource_type = 'CourseViewLimit' and (
                                                                  exists(select 1
                                                                         from w_view_limit_user v
                                                                         where rv.view_limit_id = v.view_limit_id
                                                                           and v.user_id = #{userId}))
              where c.is_del = 0
                and c.is_publish = 1
                and c.is_recommend = 1
                and c.is_train = 0
                and c.course_type like concat(concat('%', #{courseType}), '%')) a
    </select>


    <select id="guessYouLikeForHomePage"
      resultType="com.wunding.learn.course.service.client.dto.CourseListDTO" useCache="false">
        (
            -- 加塞的课程
            select c.id,
                   c.course_name,
                   c.click_number            clickNumber,
                   c.vote_number             voteNumber,
                   c.consume_excitation_num  consumeExcitationNum,
                   c.consume_excitation_type consumeExcitationType
            from course c
                     inner join w_resource_view_limit rv on c.id = rv.resource_id
                and rv.resource_type = 'CourseViewLimit'
                and exists(select 1
                           from w_view_limit_user v
                           where rv.view_limit_id = v.view_limit_id
                             and v.user_id = #{userId})
            <if test="contentRange != null and contentRange == 2 and orgId != null and orgId != ''">
                left join sys_org so on c.org_id = so.id
            </if>
            <if test="categoryId != null and categoryId != ''">
                left join categorys ct on ct.id = c.course_cate_id
            </if>
            where c.is_del = 0
              and c.is_publish = 1
              and c.is_train = 0
              and c.is_recommend = 1
              and c.is_copy = 0
              and c.course_type like concat(concat('%', #{courseType}), '%')
              <if test="contentRange != null and contentRange == 2 and orgId != null and orgId != ''">
                and instr(so.level_path,concat('/', #{orgId}, '/')) > 0
              </if>
              <if test="categoryId != null and categoryId != ''">
                and ct.category_type = 'CourseCate'
                and ct.level_path like concat(ifnull((select level_path from categorys where id = #{categoryId}), ''), '%')
                and ct.is_del = 0
                and ct.is_available = 1
              </if>
            order by c.recommend_time desc, c.id
            limit #{recommendNum})
        union
        (
            -- 我的标签关联的课程
            select c.id,
                   c.course_name,
                   c.click_number            clickNumber,
                   c.vote_number             voteNumber,
                   c.consume_excitation_num  consumeExcitationNum,
                   c.consume_excitation_type consumeExcitationType
            from course c
                     inner join w_resource_view_limit rv on c.id = rv.resource_id
                and rv.resource_type = 'CourseViewLimit'
                and exists(select 1
                           from w_view_limit_user v
                           where rv.view_limit_id = v.view_limit_id
                             and v.user_id = #{userId})
                     inner join course_tag tct on tct.course_id = c.id
                     inner join user_course_tag tuct on tuct.tag_id = tct.tag_id and tuct.user_id = #{userId}
                <if test="contentRange != null and contentRange == 2 and orgId != null and orgId != ''">
                    left join sys_org so on c.org_id = so.id
                </if>
                <if test="categoryId != null and categoryId != ''">
                    left join categorys ct on ct.id = c.course_cate_id
                </if>
            where c.is_del = 0
              and c.is_publish = 1
              and c.is_train = 0
              and c.is_recommend = 0
              and c.is_copy = 0
              and c.course_type like concat(concat('%', #{courseType}), '%')
              <if test="contentRange != null and contentRange == 2 and orgId != null and orgId != ''">
                and instr(so.level_path,concat('/', #{orgId}, '/')) > 0
              </if>
              <if test="categoryId != null and categoryId != ''">
                and ct.category_type = 'CourseCate'
                and ct.level_path like concat(ifnull((select level_path from categorys where id = #{categoryId}), ''), '%')
                and ct.is_del = 0
                and ct.is_available = 1
              </if>
            order by c.publish_time desc
            limit #{courseTagCourseNum})
    </select>

    <select id="guessYouLike" resultType="com.wunding.learn.course.service.client.dto.CourseListDTO" useCache="false">
        select row_number() over (order by a.id desc) rownum, a.*
        from (
                 -- 加塞的课程
                 (select c.id,
                         c.course_name,
                         c.click_number            clickNumber,
                         c.vote_number             voteNumber,
                         c.consume_excitation_num  consumeExcitationNum,
                         c.consume_excitation_type consumeExcitationType
                  from course c
                           inner join w_resource_view_limit rv on c.id = rv.resource_id
                      and rv.resource_type = 'CourseViewLimit'
                      and exists(select 1
                                 from w_view_limit_user v
                                 where rv.view_limit_id = v.view_limit_id
                                   and v.user_id = #{userId})
                    <if test="contentRange != null and contentRange == 2 and orgId != null and orgId != ''">
                        left join sys_org so on c.org_id = so.id
                    </if>
                    <if test="categoryId != null and categoryId != ''">
                        left join categorys ct on ct.id = c.course_cate_id
                    </if>
                  where c.is_del = 0
                    and c.is_publish = 1
                    and c.is_train = 0
                    and c.is_recommend = 1
                    and c.is_copy = 0
                    and c.course_type like concat(concat('%', #{courseType}), '%')
                    <if test="contentRange != null and contentRange == 2 and orgId != null and orgId != ''">
                        and instr(so.level_path, concat('/', #{orgId}, '/')) > 0
                    </if>
                    <if test="categoryId != null and categoryId != ''">
                        and ct.category_type = 'CourseCate'
                        and ct.level_path like concat(ifnull((select level_path from categorys where id = #{categoryId}), ''), '%')
                        and ct.is_del = 0
                        and ct.is_available = 1
                    </if>
                      limit #{recommendNum})
                 union
                 -- 我的标签关联的课程
                 select distinct c.id,
                                 c.course_name,
                                 c.click_number            clickNumber,
                                 c.vote_number             voteNumber,
                                 c.consume_excitation_num  consumeExcitationNum,
                                 c.consume_excitation_type consumeExcitationType
                 from course c
                          inner join w_resource_view_limit rv on c.id = rv.resource_id
                     and rv.resource_type = 'CourseViewLimit'
                     and exists(select 1
                                from w_view_limit_user v
                                where rv.view_limit_id = v.view_limit_id
                                  and v.user_id = #{userId})
                          inner join course_tag tct on tct.course_id = c.id
                          inner join user_course_tag tuct on tuct.tag_id = tct.tag_id and tuct.user_id = #{userId}
                    <if test="contentRange != null and contentRange == 2 and orgId != null and orgId != ''">
                        left join sys_org so on c.org_id = so.id
                    </if>
                 where c.is_del = 0
                   and c.is_publish = 1
                   and c.is_train = 0
                   and c.is_recommend = 0
                   and c.is_copy = 0
                   and c.course_type like concat(concat('%', #{courseType}), '%')
                   <if test="contentRange != null and contentRange == 2 and orgId != null and orgId != ''">
                        and instr(so.level_path,concat('/', #{orgId}, '/')) > 0
                    </if>
                    <if test="categoryId != null and categoryId != ''">
                        and c.course_cate_id = #{categoryId}
                    </if>
                   ) a
    </select>

    <select id="selectPopularCourse" resultType="com.wunding.learn.course.service.client.dto.CourseListDTO"
      useCache="false">
        select *
        from (select c.id,
                     c.course_name,
                     c.recommend_time,
                     c.publish_time,
                     c.click_number                                                      clickNumber,
                     c.vote_number                                                       voteNumber,
                     c.comment_number                                                    commentNumber,
                     (select count(cs.id) from course_star cs where cs.course_id = c.id) starNumber,
                     c.consume_excitation_num,
                     c.consume_excitation_type
              from course c
                       inner join w_resource_view_limit rv on c.id = rv.resource_id
                  and rv.resource_type = 'CourseViewLimit' and (
                                                                  exists (select 1
                                                                          from w_view_limit_user v
                                                                          where rv.view_limit_id = v.view_limit_id
                                                                            and v.user_id = #{userId}))
                <if test="contentRange != null and contentRange == 2 and orgId != null and orgId != ''">
                    left join sys_org so on c.org_id = so.id
                </if>
                <if test="categoryId != null and categoryId != ''">
                    left join categorys ct on ct.id = c.course_cate_id
                </if>
              where c.is_del = 0
                and c.is_publish = 1
                and c.is_train = 0
                and c.course_type like concat(concat('%', #{courseType}), '%')
                and c.is_copy = 0
                <if test="contentRange != null and contentRange == 2 and orgId != null and orgId != ''">
                    and instr(so.level_path, concat('/', #{orgId}, '/')) > 0
                </if>
                <if test="categoryId != null and categoryId != ''">
                    and ct.category_type = 'CourseCate'
                    and ct.level_path like concat(ifnull((select level_path from categorys where id = #{categoryId}), ''), '%')
                    and ct.is_del = 0
                    and ct.is_available = 1
                </if>
                ) a
        <where>
            <if test="publishDays != null">
                and date_sub(curdate(), interval #{publishDays} day) &lt;= date(a.publish_time)
            </if>
            <if test="commentNumAndLikeNum != null">
                and (a.starNumber + a.commentNumber) >= #{commentNumAndLikeNum}
            </if>
        </where>
        order by
        <if test="orderType == 'recommendDate'">
            a.recommend_time desc,
            a.id desc
        </if>
        <if test="orderType == 'commentNumberAndLikeNum'">
            a.voteNumber desc,
            a.id desc
        </if>
    </select>

    <select id="getPostCourse" resultType="com.wunding.learn.course.service.client.dto.CourseListDTO" useCache="false">
        select * from (
        select distinct c.id,
                        c.course_name,
                        c.click_number clickNumber,
                        c.vote_number  voteNumber,
                        c.publish_time
        from course c
                 inner join course_category cc on c.id = cc.course_id and cc.category_type = 'PositionCate'
        <if test="contentRange != null and contentRange == 2 and orgId != null and orgId != ''">
            left join sys_org so on c.org_id = so.id
        </if>
        <if test="categoryId != null and categoryId != ''">
            left join categorys ct on ct.id = c.course_cate_id
        </if>
        where c.is_del = 0
          and c.is_publish = 1
          and c.is_train = 0
          and c.is_copy = 0
          and c.course_type like concat(concat('%', #{courseType}), '%')
        <if test="null != identityLevelPathId and identityLevelPathId != ''">
            and cc.category_id = #{identityLevelPathId}
        </if>
        <if test="contentRange != null and contentRange == 2 and orgId != null and orgId != ''">
            and instr(so.level_path, concat('/', #{orgId}, '/')) > 0
        </if>
        <if test="categoryId != null and categoryId != ''">
            and ct.category_type = 'CourseCate'
            and ct.level_path like concat(ifnull((select level_path from categorys where id = #{categoryId}), ''), '%')
            and ct.is_del = 0
            and ct.is_available = 1
        </if>
        ) a
        order by a.publish_time desc
    </select>


    <select id="getLearningCourse" resultType="com.wunding.learn.course.service.client.dto.CourseListDTO"
      useCache="false">
        select *
        from (select c.id,
                     c.course_name,
                     c.update_time,
                     c.click_number clickNumber,
                     c.vote_number  voteNumber
              from course c
                       inner join w_resource_view_limit rv on c.id = rv.resource_id
                  and rv.resource_type = 'CourseViewLimit'
                  and exists(select 1
                             from w_view_limit_user v
                             where rv.view_limit_id = v.view_limit_id
                               and v.user_id = #{userId})
                       inner join user_course_record tur on tur.course_id = c.id
                  and tur.user_id = #{userId}
                  and tur.is_learned = 0
                    <if test="contentRange != null and contentRange == 2 and orgId != null and orgId != ''">
                        left join sys_org so on c.org_id = so.id
                    </if>
                    <if test="categoryId != null and categoryId != ''">
                        left join categorys ct on ct.id = c.course_cate_id
                    </if>
              where c.is_del = 0
                and c.is_publish = 1
                and c.is_train = 0
                and c.is_copy = 0
                and c.course_type like concat(concat('%', #{courseType}), '%')
                <if test="contentRange != null and contentRange == 2 and orgId != null and orgId != ''">
                    and instr(so.level_path, concat('/', #{orgId}, '/')) > 0
                </if>
                <if test="categoryId != null and categoryId != ''">
                    and ct.category_type = 'CourseCate'
                    and ct.level_path like concat(ifnull((select level_path from categorys where id = #{categoryId}), ''), '%')
                    and ct.is_del = 0
                    and ct.is_available = 1
                </if>
                ) a
        order by a.update_time desc
    </select>


    <select id="getCategoryCourseList" resultType="com.wunding.learn.course.service.client.dto.CategoryCourseDTO"
      useCache="false">
        select distinct c.id,
                        c.course_name,
                        (select t.category_name from categorys t where t.id = c.course_cate_id) course_cate_name,
                        c.click_number                                                          clickNumber,
                        c.vote_number                                                           voteNumber,
                        c.comment_number,
                        c.course_type,
                        c.course_cate_id,
                        (select group_concat(tct.tag_id separator '|')
                         from course_tag tct
                         where tct.course_id = c.id
                         group by tct.course_id)                                                markids,
                        c.consume_excitation_num,
                        c.consume_excitation_type,
                        c.sort_no,
                        c.publish_time
        from course c
                 inner join w_resource_view_limit rv on c.id = rv.resource_id and rv.resource_type = 'CourseViewLimit'
        where c.is_del = 0
          and c.is_publish = 1
          and c.is_copy = 0
          and (exists(select 1
                      from w_view_limit_user v
                      where rv.view_limit_id = v.view_limit_id
                        and v.user_id =
                            #{userId}))
        <if test="categoryId != null and categoryId != ''">
            and exists (select 1
                        from course_tag b
                        where b.course_id = c.id
                          and b.tag_id = #{categoryId})
        </if>
        <if test="imgFirst != null and imgFirst != ''">
            and c.is_first = #{imgFirst}
        </if>
        <if test="courseType != null and courseType != ''">
            and c.course_type like concat('%', #{courseType}, '%')
        </if>
        order by c.sort_no desc, c.publish_time desc
    </select>

    <select id="getClickNumberByCourseIds" resultType="com.wunding.learn.course.service.client.dto.CourseValueDTO">
        select c.id id, c.click_number value
        from course c where
        <foreach collection="ids" open="c.id in (" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <select id="getVoteNumberByCourseIds" resultType="com.wunding.learn.course.service.client.dto.CourseValueDTO">
        select c.id id, c.vote_number value
        from course c where
        <foreach collection="ids" open="c.id in (" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>


    <select id="getIsVoteByCourseIdsAndCurrentUserId"
      resultType="com.wunding.learn.course.service.client.dto.CourseValueDTO" useCache="false">
        select cvt.course_id id, count(cvt.course_id) value
        from course_vote cvt where cvt.create_by = #{userId}
        <foreach collection="ids" open="and cvt.course_id in (" close=")" separator="," item="item">
            #{item}
        </foreach>
        group by cvt.course_id
    </select>


    <select id="getRealityById" resultType="com.wunding.learn.course.api.dto.CourseInfoDTO">
        select
        <include refid="Base_Column_List">
        </include>
        from course
        where id = #{id}
    </select>


    <select id="getParentLevelPath" resultType="java.lang.String" useCache="false">
        select b.level_path
        from sys_org b,
             categorys_view_limit c
        where c.category_id = b.id
          and c.content_id = #{parentId}
    </select>

    <select id="getOrgIdList" resultType="java.lang.Integer" useCache="false">
        select count(id)
        from
        (
        select id
        from sys_org a
        <foreach close=")" collection="orgList" item="id" open="(" separator=",">
            #{id}
        </foreach>
        ) temp
        where temp.id in
        <where>
            <foreach collection="list" item="item" separator="or">
                a.level_path like concat(#{item}, '%')
            </foreach>
        </where>
    </select>


    <select id="getCourseInteract"
      resultType="com.wunding.learn.course.service.client.dto.ProjectTaskCourseInteractDTO" useCache="false">
        select c.vote_number                                                                               voteNumber,
               (select count(cs.id) from course_star cs where cs.course_id = c.id)                         starCount,
               (select ifnull(round(avg(star_count), 1), 0) from course_star cs where cs.course_id = c.id) commonStar
        from course c
        where c.is_del = 0
          and c.id = #{courseId}
    </select>

    <select id="getCourseByCwId" parameterType="java.lang.String"
      resultType="com.wunding.learn.course.service.model.Course" useCache="false">
        select c.*
        from course c
                 inner join courseware cw on cw.course_id = c.id
        where cw.id = #{cwId}
    </select>

    <select id="getCourseCashById" resultType="com.wunding.learn.course.service.admin.dto.CourseCashDTO"
      useCache="true">
        select id
             , course_no
             , course_name
             , org_id
        from course
        where id = #{id}
    </select>


    <select id="getCourseCashByIds" resultType="com.wunding.learn.course.service.admin.dto.CourseCashDTO"
      useCache="true">
        select id
             , course_no
             , course_name
             , org_id
        from course
        <where>
            <if test="cIds != null and cIds.size() > 0">
                <foreach close=")" collection="cIds" item="item" open=" id in (" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectRecommendCourse" resultType="com.wunding.learn.course.service.client.dto.CourseListDTO"
      useCache="false">
        select *
        from (select c.id,
                     c.course_name,
                     c.recommend_time,
                     c.publish_time,
                     c.click_number                                                      clickNumber,
                     c.vote_number                                                       voteNumber,
                     c.comment_number                                                    commentNumber,
                     (select count(cs.id) from course_star cs where cs.course_id = c.id) starNumber
              from course c
                       inner join w_resource_view_limit rv on c.id = rv.resource_id
                  and rv.resource_type = 'CourseViewLimit' and (
                                                                  exists (select 1
                                                                          from w_view_limit_user v
                                                                          where rv.view_limit_id = v.view_limit_id
                                                                            and v.user_id = #{userId}))
                 <if test="contentRange != null and contentRange == 2 and orgId != null and orgId != ''">
                    left join sys_org so on c.org_id = so.id
                 </if>
                 <if test="categoryId != null and categoryId != ''">
                        left join categorys ct on ct.id = c.course_cate_id
                    </if>
              where c.is_del = 0
                and c.is_publish = 1
                and c.is_recommend = 1
                and c.is_train = 0
                and c.is_copy = 0
                and c.course_type like concat(concat('%', #{courseType}), '%')
                <if test="contentRange != null and contentRange == 2 and orgId != null and orgId != ''">
                    and instr(so.level_path, concat('/', #{orgId}, '/')) > 0
                </if>
                <if test="categoryId != null and categoryId != ''">
                    and ct.category_type = 'CourseCate'
                    and ct.level_path like concat(ifnull((select level_path from categorys where id = #{categoryId}), ''), '%')
                    and ct.is_del = 0
                    and ct.is_available = 1
                </if>
                ) a
        <where>
            <if test="publishDays != null">
                and date_sub(curdate(), interval #{publishDays} day) &lt;= date(a.publish_time)
            </if>
            <if test="commentNumAndLikeNum != null">
                and (a.starNumber + a.commentNumber) >= #{commentNumAndLikeNum}
            </if>
        </where>
        order by
        <if test="orderType == 'recommendDate'">
            a.recommend_time desc, a.id desc
        </if>
        <if test="orderType == 'commentNumberAndLikeNum'">
            a.voteNumber desc,a.id desc
        </if>
    </select>

    <select id="getCourseDownloadShiro" resultType="java.lang.Integer" useCache="false">
        select count(1)
        from course c
                 left join sys_org so on so.id = c.download_org_id
        where c.id = #{courseId}
          and (
            #{levelPath} like concat(so.level_path, '%')
                or c.create_by = #{currentUserId}
            )
    </select>

    <select id="getTrainCourseList" resultType="com.wunding.learn.course.service.client.dto.BusinessCourseListDTO">
        select c.id,
               c.course_name,
               c.publish_time,
               c.click_number clickNumber,
               c.vote_number  voteNumber,
               c.consume_excitation_num,
               c.consume_excitation_type,
               c.is_vote      isVote,
               c.limit_show,
               c.source_type,
               c.source_id
        from course c
                 inner join sys_org g on g.id = c.org_id
                 left join categorys ct on ct.id = c.course_cate_id
                 left join resource_directory rd on c.id = rd.resource_id
                 inner join w_resource_view_limit rv on c.id = rv.resource_id and rv.resource_type = 'CourseViewLimit'
        where c.is_del = 0
          and c.is_copy = 0
          and c.is_train = 3
          and c.is_publish = 1
        <if test="params.relevanceId != null and params.relevanceId != ''">
            and c.relevance_id = #{params.relevanceId}
        </if>
        <if test="params.directoryId != null and params.directoryId != ''">
            and rd.directory_id = #{params.directoryId}
        </if>
        <!-- 没有权限也展示 + 有权限展示 -->
        and (
            limit_show = 1
                or
            exists(select 1
                   from w_view_limit_user v
                   where rv.view_limit_id = v.view_limit_id
                     and v.user_id = #{params.currentUserId})
            )
        order by c.is_publish, c.publish_time desc, c.create_time desc
    </select>

    <select id="checkChildrenOrg" resultType="int" useCache="false">
        select count(1)
        from sys_org
        where
        <foreach collection="parentViewOrgIdList" open="(" close=")" separator="or" item="parentOrgId">
            instr(level_path, #{parentOrgId})
        </foreach>
        <foreach collection="childrenViewOrgIdList" open="and id in (" close=")" separator="," item="childrenOrgId">
            #{childrenOrgId}
        </foreach>
    </select>

    <select id="getCourseByUserId" parameterType="java.lang.String"
      resultType="com.wunding.learn.course.api.dto.CourseSimpleInfoDTO">
        select id
             , course_name
             , create_time
        from course
        where create_by = #{userId}
        order by create_time desc
    </select>


    <select id="getRealityCourseList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List">
        </include>
        from course where id in
        <foreach collection="courseIdList" separator="," open="(" close=")" item="courseId">
            #{courseId}
        </foreach>
    </select>

    <select id="getCourseBaseList" resultType="com.wunding.learn.common.dto.ResourceBaseDTO" useCache="false">
        select c.id,
               c.course_name as title,
               c.course_no      resourceNo,
               c.is_publish,
               c.is_del,
               o.level_path
        from course c
                 inner join sys_org o on c.org_id = o.id
        where c.id in
        <foreach close=")" collection="params.resourceIdList" item="resourceId" open="(" separator=",">
            #{resourceId}
        </foreach>
    </select>

    <select id="getAbilityChooseRelateCourseList"
      resultType="com.wunding.learn.course.service.admin.dto.AbilityChooseCourseDTO" useCache="false">
        select c.id                                                                                 courseId,
               c.course_no                                                                          courseNo,
               c.course_name                                                                        courseName,
               c.is_publish                                                                         isPublish,
               c.publish_by,
               c.publish_time                                                                       publishTime,
               su.full_name                                                                         createBy,
               c.create_time,
               (select count(cw.id) from courseware cw where cw.course_id = c.id and cw.is_del = 0) coursewareNum,
               round((select sum(cw.play_time) from courseware cw where cw.course_id = c.id and cw.is_del = 0) / 60,
                     1)                                                                             courseTime
        from course c
                 left join sys_user su on su.id = c.create_by
                 left join sys_org g on g.id = c.org_id
        where c.is_del = 0
          and c.is_train = 0
          and c.is_copy = 0
        <if test="params.startTime != null">
            and c.publish_time >= #{params.startTime}
        </if>
        <if test="params.endTime != null">
            and #{params.endTime} >= c.publish_time
        </if>
        <if test="params.isPublish != null">
            and c.is_publish = #{params.isPublish}
        </if>
        <if test="params.name != null and params.name != ''">
            and instr(c.course_name, #{params.name}) > 0
        </if>
        <if test="courseIds != null and courseIds.size() > 0">
            and c.id in
            <foreach close=")" collection="courseIds" item="courseId" open="(" separator=",">
                #{courseId}
            </foreach>
        </if>
        <if test="params.managerAreaOrgIds != null and params.managerAreaOrgIds.size() > 0">
            and (
            <foreach collection="params.managerAreaOrgIds" item="levelPath" open="(" close=")" separator="or">
                g.level_path like concat(#{levelPath}, '%')
            </foreach>
            or c.create_by = #{params.currentUserId}
                )
        </if>
    </select>

    <select id="getCourseInfoByCourseNo" resultType="com.wunding.learn.course.service.model.Course" useCache="false">
        select c.id id, c.course_no courseNo, c.course_name courseName
        from course c
        where c.is_del = 0
          and is_copy = 0
          and is_train = 0
          and c.course_no = #{courseNo}
    </select>

    <select id="getCourseTimeByCourseId" resultType="com.wunding.learn.course.service.admin.dto.AbilityChooseCourseDTO">
        select course_id, (round(sum(play_time) / 60, 1)) courseTime
        from courseware where is_del = 0
        <if test="courseIdList != null and courseIdList.size() > 0">
            and course_id in
            <foreach close=")" collection="courseIdList" item="courseId" open="(" separator=",">
                #{courseId}
            </foreach>
        </if>
        group by course_id
    </select>

    <select id="selectCourseListByCourseId" resultType="com.wunding.learn.user.api.dto.AbilityRelateCourseDTO"
      useCache="false">
        select c.id          courseId,
               c.course_no   courseNo,
               c.course_name courseName,
               round((select sum(cw.play_time) from courseware cw where cw.course_id = c.id and cw.is_del = 0) / 60,
                     1)      courseTime
        from course c where c.is_del = 0
        <if test="courseIdList != null and courseIdList.size() > 0">
            and c.id in
            <foreach close=")" collection="courseIdList" item="courseId" open="(" separator=",">
                #{courseId}
            </foreach>
        </if>
    </select>

    <select id="getCourseLearnDetail" resultType="com.wunding.learn.course.api.dto.CourseLearnDetailDTO"
      useCache="false">
        select c.id                    courseId,
               c.course_name,
               c.is_del,
               c.is_publish,
               ucr.is_learned          isLearned,
               ifnull(ucr.duration, 0) learnDuration,
               ifnull((select sum(cw.play_time) from courseware cw where cw.course_id = c.id and cw.is_del = 0),
                      0)               courseDuration
        from course c
                 left join user_course_record ucr on c.id = ucr.course_id and ucr.user_id = #{params.currentUserId}
        <foreach close=")" collection="params.courseIdList" item="courseId" open="where c.id in (" separator=",">
            #{courseId}
        </foreach>
        <if test="params.sortType != null and params.sortType == 0">
            order by case
                         when ucr.is_learned is null then 1
                         when ucr.is_learned = 0 then 2
                         when ucr.is_learned = 1 then 3
                         end
        </if>
        <if test="params.sortType != null and params.sortType == 1">
            order by case
            when  ucr.is_learned = 1 then 1
            when ucr.is_learned = 0 then 2
            when ucr.is_learned is null then 3
            end
        </if>
    </select>

    <select id="getCreateCourse" resultType="com.wunding.learn.course.api.dto.CourseInfoDTO">
        select id,
               course_no,
               course_name,
               course_cate_id,
               item_id,
               course_type,
               study_type,
               cover_image_url,
               descriptions,
               credit,
               score,
               org_id,
               author,
               is_required,
               is_sign,
               is_audit,
               is_download,
               is_comment,
               is_vote,
               is_first,
               is_recommend,
               is_share,
               is_favorite,
               is_allow_share,
               is_available,
               is_del,
               is_publish,
               publish_by,
               publish_time,
               publish_path,
               sort_no,
               click_number,
               comment_number,
               download_number,
               vote_number,
               favorite_number,
               share_in_number,
               source,
               exam_id,
               create_by,
               create_time,
               update_by,
               update_time,
               dev_time,
               language,
               version,
               course_level,
               share_by,
               share_time,
               is_train,
               csort_no,
               auto_publish_time,
               is_public,
               is_create_sign,
               is_create_assess,
               is_hang_up,
               hang_up_duration_minute,
               hang_up_duration_second,
               recommend_time
        from course
        where is_del = 0
          and create_by = #{userId}
    </select>

    <select id="getCourseIsDelById" parameterType="java.lang.String" resultType="java.lang.Integer">
        select is_del
        from course
        where id = #{id}
    </select>
    <select id="getCertificationContentList" resultType="com.wunding.learn.common.dto.CertificationContentDTO">
        select c.id,
               c.course_name as name,
               c.is_del
        from course c
        <where>
            c.id in
            <foreach close=")" collection="batchIds" index="" item="id" open="(" separator=",">
                #{id}
            </foreach>
        </where>
    </select>
    <select id="checkCourseManagePermissions" resultType="java.lang.Integer" useCache="false">
        select count(1)
        from course tp
                 inner join sys_org g on g.id = tp.org_id
        where tp.id = #{id,jdbcType=VARCHAR}
        <if test="userManageAreaOrgId != null and userManageAreaOrgId.size > 0">
            and
            <foreach close=")" collection="userManageAreaOrgId" item="levelPath" open="(" separator="or">
                g.level_path like concat(#{levelPath}, '%')
            </foreach>
        </if>
    </select>
    <select id="getCoursewareAwaitLearnDurationList"
      resultType="com.wunding.learn.course.api.dto.CourseWareLearnDetailDTO" useCache="false">
        select ifnull(c.play_time, 0) as playTime, ifnull(cur.duration, 0) as `duration`
        from courseware c
                 left join courseware_user_record cur on c.id = cur.courseware_id and cur.user_id = #{currentUserId}
        where c.is_del = 0
          and c.course_id = #{courseId}
          and (cur.is_learned = 0 or cur.id is null)
    </select>

    <sql id="Base_Column_List">
        id
          ,
        course_no,
        course_name,
        course_cate_id,
        item_id,
        relevance_id,
        course_type,
        study_type,
        cover_image_url,
        descriptions,
        credit,
        score,
        org_id,
        author,
        is_required,
        is_sign,
        is_audit,
        is_download,
        is_comment,
        is_vote,
        is_first,
        is_recommend,
        is_share,
        is_favorite,
        is_allow_share,
        is_available,
        is_del,
        view_type,
        is_publish,
        publish_by,
        publish_time,
        publish_path,
        sort_no,
        click_number,
        comment_number,
        download_number,
        vote_number,
        favorite_number,
        share_in_number,
        source,
        exam_id,
        create_by,
        create_time,
        update_by,
        update_time,
        dev_time,
        language,
        version,
        course_level,
        share_by,
        share_time,
        is_train,
        csort_no,
        auto_publish_time,
        is_public,
        is_create_sign,
        is_create_assess,
        is_hang_up,
        hang_up_duration_minute,
        hang_up_duration_second,
        recommend_time,
        consume_excitation_num
          ,
        consume_excitation_type,
        download_org_id,
        course_category_id,
        is_use_default_img,
        is_allow_note,
        is_audit_note,
        limit_show,
        source_type,
        source_id,
        upload_way,
        sum_play_time
    </sql>

    <update id="autoPublishCourse">
        update course co
        set co.is_publish        = 1,
            co.publish_time      = co.auto_publish_time,
            co.publish_by        = co.update_by,
            co.auto_publish_time = null
        where co.is_publish = 0
          and co.auto_publish_time <![CDATA[ <= ]]> NOW()
          and co.is_del = 0
          and co.is_publish = 0
          and (select count(cw.id) from courseware cw where cw.course_id = co.id and cw.is_del = 0) > 0
    </update>

    <update id="addCourseVoteNumber" parameterType="java.lang.String">
        update course c
        set c.vote_number = c.vote_number + 1
        where c.id = #{courseId,jdbcType=VARCHAR}
    </update>

    <update id="addCourseClickNumber" parameterType="java.lang.String">
        update course c
        set c.click_number = c.click_number + 1
        where c.id = #{courseId,jdbcType=VARCHAR}
    </update>

    <update id="updateLikeNum" parameterType="string">
        update course
        set like_num = (select count(*) from course_vote ev where ev.course_id = #{id} and ev.category_type = 1)
        where id = #{id}
    </update>

    <update id="updateViewNum" parameterType="string">
        update course
        set view_num = (select count(*) from course_view ev where ev.course_id = #{id})
        where id = #{id}
    </update>

    <update id="reduceCourseVoteNumber">
        update course c
        set c.vote_number = c.vote_number - 1
        where c.id = #{courseId,jdbcType=VARCHAR}
    </update>

    <update id="updateCoursewareComment">
        update courseware
        set comment_number = #{commentNumber}
        where id = #{coursewareId}
    </update>

    <update id="updateCourseSumPlayTime">
        UPDATE course c
        SET c.sum_play_time = #{courseSumPlayTime}
        WHERE c.id = #{id,jdbcType=VARCHAR}
    </update>

    <select id="getCwListInfoByCourseId"
      resultType="com.wunding.learn.course.service.client.dto.CoursewareDetailClientDTO" useCache="false">
        select cw.id,
               cw.cw_name,
               cw.chapter_id,
               cw.mime,
               cw.cw_type        as type,
               cw.descriptions,
               cw.class_hour,
               cw.course_id,
               cw.real_play_time as playTime,
               cw.play_time      as setPlayTime,
               c.publish_time       publishTime,
               cw.sort_no,
               cw.create_time,
               cw.exam_id,
               d.full_name       as create_by,
               cw.version
        from courseware cw
                 left join course c on c.id = cw.course_id
                 left join sys_user d on cw.create_by = d.id
        <where>
            cw.is_del = 0
              and cw.is_available = 1
              and cw.transform_status = 2
              and c.is_del = 0
              and c.is_publish = 1
            <if test="params.id != null and params.id != ''">
                and cw.course_id = #{params.id}
            </if>
            <if test="params.searchKey != null and params.searchKey != ''">
                and instr(cw.cw_name, #{params.searchKey}) > 0
            </if>
        </where>
        order by cw.sort_no asc, cw.create_time desc
    </select>

    <select id="getCourseDeleteInfoById" resultType="com.wunding.learn.common.dto.ResourceDeleteInfoDTO"
      parameterType="java.lang.String">
        select id,
               is_del      isDel,
               update_time updateTime,
               update_by   updateBy
        from course
        where id = #{id}
    </select>

    <select id="getCourseIsDel" resultType="com.wunding.learn.course.api.dto.CourseInfoDTO" useCache="false">
        select c.id,
               c.is_del
        from course c
        <where>
            <if test="courseIds != null and courseIds.size() > 0">
                c.id in
                <foreach collection="courseIds" item="courseId" open="(" separator="," close=")">
                    #{courseId}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getAICourse" resultType="com.wunding.learn.course.service.client.dto.CourseListDTO">
        select *
        from (select c.id,
                     c.course_name,
                     c.update_time,
                     c.click_number clickNumber,
                     c.vote_number  voteNumber
              from course c
                       inner join w_resource_view_limit rv on c.id = rv.resource_id
                  and rv.resource_type = 'CourseViewLimit'
                  and exists(select 1
                             from w_view_limit_user v
                             where rv.view_limit_id = v.view_limit_id
                               and v.user_id = #{userId})
                  <if test="contentRange != null and contentRange == 2 and orgId != null and orgId != ''">
                     left join sys_org so on c.org_id = so.id
                  </if>
                  <if test="categoryId != null and categoryId != ''">
                     left join categorys ct on ct.id = c.course_cate_id
                  </if>
              where c.is_del = 0
                and c.is_publish = 1
                and c.is_train = 0
                and c.id in (select crf.course_id from course_ref_dify crf)
                and c.is_copy = 0
                and c.course_type like concat(concat('%', #{courseType}), '%')
                <if test="contentRange != null and contentRange == 2 and orgId != null and orgId != ''">
                    and instr(so.level_path, concat('/', #{orgId}, '/')) > 0
                </if>
                <if test="categoryId != null and categoryId != ''">
                    and ct.category_type = 'CourseCate'
                    and ct.level_path like concat(ifnull((select level_path from categorys where id = #{categoryId}), ''), '%')
                    and ct.is_del = 0
                    and ct.is_available = 1
                </if>
                ) a
        order by a.update_time desc
    </select>

    <select id="isExistCourseNeedAudit" resultType="java.lang.Boolean">
        SELECT CASE
                   WHEN COUNT(*) > 0 THEN TRUE
                   ELSE FALSE
                   END
        FROM course
        WHERE id IN
        <foreach collection="courseIds" item="courseId" open="(" separator="," close=")">
            #{courseId}
        </foreach>
        AND audit_status NOT IN (2, 5)
    </select>

    <update id="addCoursePV" parameterType="java.lang.String">
        update course c
        set c.pv = c.pv + 1
        where c.id = #{courseId,jdbcType=VARCHAR}
    </update>

    <select id="selectMyApplyCourseAudit"
      resultType="com.wunding.learn.course.service.admin.dto.CourseProcessListDTO" useCache="false">
        select pir.id
             , pir.process_code
             , pir.process_definition_id
             , su.full_name           as applyUserName
             , su.login_name          as applyUserLoginName
             , c.audit_status         as auditStatus
             , pir.process_apply_type as applyType
             , c.course_name
             , c.id                      courseId
             , c.course_no
             , c.course_cate_id
             , cs.category_name
             , c.author               as authorName
        from course c
                 left join process_instance_resource pir on pir.resource_id = c.id
                 left join categorys cs on c.course_cate_id = cs.id
                 left join sys_user su on pir.applicant_user_id = su.id
        where pir.applicant_user_id = #{params.applyUser}
          and c.is_del = 0
          and pir.is_del = 0
        <if test="null != params.courseName and params.courseName != ''">
            and INSTR(c.course_name, #{params.courseName}) > 0
        </if>
        <if test="null != params.courseNo and params.courseNo != ''">
            and INSTR(c.course_no, #{params.courseNo}) > 0
        </if>
        <if test="params.auditStatus != '' and null != params.auditStatus">
            and c.audit_status = #{params.auditStatus}
        </if>
        <if test="params.auditStatus == '' or params.auditStatus == null">
            and c.audit_status in (1, 2, 3, 4)
        </if>
        <if test="params.applyType != null">
            and pir.process_apply_type = #{params.applyType}
        </if>
        <if test="null != params.applyStartTime and params.applyStartTime != ''">
            and DATE_FORMAT(c.register_time, '%Y-%m-%d') >= DATE_FORMAT(#{params.applyStartTime}, '%Y-%m-%d')
        </if>
        <if test="null != params.applyEndTime and params.applyEndTime != ''">
            and DATE_FORMAT(#{params.applyEndTime}, '%Y-%m-%d') >= DATE_FORMAT(c.register_time, '%Y-%m-%d')
        </if>
        order by c.register_time desc, pir.id desc
    </select>

    <select id="selectMyDealCourseAudit"
      resultType="com.wunding.learn.course.service.admin.dto.CourseProcessListDTO" useCache="false">
        select pir.id
             , pir.process_code
             , pir.process_definition_id
             , su.full_name           as applyUserName
             , su.login_name          as applyUserLoginName
             , c.register_time        as applyTime
             , c.audit_status         as auditStatus
             , pir.process_apply_type as applyType
             , c.course_name
             , c.id                      courseId
             , c.course_no
             , c.course_cate_id
             , cs.category_name
             , c.author               as authorName
        from course c
                 inner join process_instance_resource pir on pir.resource_id = c.id
                 left join categorys cs on c.course_cate_id = cs.id
                 left join sys_user su on pir.applicant_user_id = su.id
                 left join sys_org so on su.org_id = so.id
        where c.is_del = 0
          and pir.is_del = 0
        <if test="params.sql != null and params.sql != ''">
            and exists(${params.sql})
        </if>
        <if test="null != params.courseName and params.courseName != ''">
            and INSTR(c.course_name, #{params.courseName}) > 0
        </if>
        <if test="null != params.courseNo and params.courseNo != ''">
            and INSTR(c.course_no, #{params.courseNo}) > 0
        </if>
        <if test="params.auditStatus != '' and null != params.auditStatus">
            and c.audit_status = #{params.auditStatus}
        </if>
        <if test="params.auditStatus == '' or params.auditStatus == null">
            and c.audit_status in (1, 2, 3, 4)
        </if>
        <if test="params.applyType != null">
            and pir.process_apply_type = #{params.applyType}
        </if>
        <if test="null != params.applyUser and params.applyUser != ''">
            and pir.applicant_user_id = #{params.applyUser}
        </if>
        <if test="null != params.applyStartTime and params.applyStartTime != ''">
            and DATE_FORMAT(c.register_time, '%Y-%m-%d') >= DATE_FORMAT(#{params.applyStartTime}, '%Y-%m-%d')
        </if>
        <if test="null != params.applyEndTime and params.applyEndTime != ''">
            and DATE_FORMAT(#{params.applyEndTime}, '%Y-%m-%d') >= DATE_FORMAT(c.register_time, '%Y-%m-%d')
        </if>
        order by c.register_time desc, pir.id desc
    </select>

    <select id="selectAllCourseAudit"
      resultType="com.wunding.learn.course.service.admin.dto.CourseProcessListDTO" useCache="false">
        select pir.id,
               pir.process_code,
               pir.process_definition_id,
               su.full_name           as applyUserName,
               su.login_name          as applyUserLoginName,
               c.register_time        as applyTime,
               c.audit_status         as auditStatus,
               pir.process_apply_type as applyType,
               c.course_name,
               c.id                      courseId,
               c.course_no,
               c.course_cate_id,
               cs.category_name,
               c.author               as authorName
        from course c
                 inner join process_instance_resource pir on pir.resource_id = c.id
                 left join categorys cs on c.course_cate_id = cs.id
                 left join sys_user su on pir.applicant_user_id = su.id
                 left join sys_org so on su.org_id = so.id
        where c.is_del = 0
          and pir.is_del = 0
        <if test="null != params.managerAreaOrgIds and params.managerAreaOrgIds.size() > 0">
            and
            <foreach collection="params.managerAreaOrgIds" item="levelPath" open="(" close=")" separator="or">
                so.level_path like concat(#{levelPath}, '%')
            </foreach>
        </if>
        <if test="null != params.courseName and params.courseName != ''">
            and INSTR(c.course_name, #{params.courseName}) > 0
        </if>
        <if test="null != params.courseNo and params.courseNo != ''">
            and INSTR(c.course_no, #{params.courseNo}) > 0
        </if>
        <if test="params.auditStatus != '' and null != params.auditStatus">
            and c.audit_status = #{params.auditStatus}
        </if>
        <if test="params.auditStatus == '' or params.auditStatus == null">
            and c.audit_status in (1, 2, 3, 4)
        </if>
        <if test="params.applyType != null">
            and pir.process_apply_type = #{params.applyType}
        </if>
        <if test="null != params.applyUser and params.applyUser != ''">
            and pir.applicant_user_id = #{params.applyUser}
        </if>
        <if test="null != params.applyStartTime and params.applyStartTime != ''">
            and DATE_FORMAT(c.register_time, '%Y-%m-%d') >= DATE_FORMAT(#{params.applyStartTime}, '%Y-%m-%d')
        </if>
        <if test="null != params.applyEndTime and params.applyEndTime != ''">
            and DATE_FORMAT(#{params.applyEndTime}, '%Y-%m-%d') >= DATE_FORMAT(c.register_time, '%Y-%m-%d')
        </if>
        order by c.register_time desc, pir.id desc
    </select>


    <select id="getNewCourse" resultType="com.wunding.learn.course.service.client.dto.CourseListDTO" useCache="false">
        select c.id,
               c.course_name,
               c.update_time,
               c.click_number clickNumber,
               c.vote_number  voteNumber
        from course c
                 inner join w_resource_view_limit rv on c.id = rv.resource_id
            and rv.resource_type = 'CourseViewLimit'
            and exists(select 1
                       from w_view_limit_user v
                       where rv.view_limit_id = v.view_limit_id
                         and v.user_id = #{userId})
        <if test="contentRange != null and contentRange == 2 and orgId != null and orgId != ''">
            left join sys_org so on c.org_id = so.id
        </if>
        <if test="categoryId != null and categoryId != ''">
            left join categorys ct on ct.id = c.course_cate_id
        </if>
        where c.is_del = 0
          and c.is_publish = 1
          and c.is_train = 0
          and c.is_copy = 0
          and c.course_type like concat(concat('%', #{courseType}), '%')
        <if test="contentRange != null and contentRange == 2 and orgId != null and orgId != ''">
            and instr(so.level_path, concat('/', #{orgId}, '/')) > 0
        </if>
        <if test="categoryId != null and categoryId != ''">
            and ct.category_type = 'CourseCate'
            and ct.level_path like concat(ifnull((select level_path from categorys where id = #{categoryId}), ''), '%')
            and ct.is_del = 0
            and ct.is_available = 1
        </if>
        <choose>
            <when test="contentRule != null and contentRule == 'newCourseCreateTime'">
                order by c.id desc
            </when>
            <when test="contentRule != null and contentRule == 'newCoursePublishTime'">
                order by c.publish_time desc
            </when>
            <otherwise>
            </otherwise>
        </choose>
    </select>

    <select id="getCourseTotal" resultType="java.lang.Integer" useCache="false">
        select count(c.id)
        from course c
                 inner join sys_org g on g.id = c.org_id
                 left join categorys ct on ct.id = c.course_cate_id
        where c.is_del = 0
          and c.is_copy = 0
          and c.is_train = 0
        <if test="managerAreaOrgIds != null and managerAreaOrgIds.size() > 0">
            and (
            <foreach collection="managerAreaOrgIds" item="levelPath" open="(" close=")" separator="or">
                g.level_path like concat(#{levelPath}, '%')
            </foreach>
            or c.org_id = #{currentOrgId} or c.create_by = #{currentUserId}
                )
        </if>
    </select>

    <select id="getCourseCategoryStat" resultType="com.wunding.learn.course.service.admin.dto.CourseCategoryStatDTO"
      useCache="false">
        select ct.category_name, count(ct.id) as courseCount
        from course c
                 inner join sys_org g on g.id = c.org_id
                 left join categorys ct on ct.id = c.course_cate_id
        where c.is_del = 0
          and c.is_copy = 0
          and c.is_train = 0
        <if test="managerAreaOrgIds != null and managerAreaOrgIds.size() > 0">
            and (
            <foreach collection="managerAreaOrgIds" item="levelPath" open="(" close=")" separator="or">
                g.level_path like concat(#{levelPath}, '%')
            </foreach>
            or c.org_id = #{currentOrgId} or c.create_by = #{currentUserId}
                )
        </if>
        group by ct.id
        order by courseCount desc
        limit 8
    </select>

    <select id="listPublishedCourseIds" resultType="java.lang.String">
        select id
        from course
        where is_publish = 1
            and is_del = 0
            and is_train = 0
            and is_copy = 0
          <if test="courseIds != null and courseIds.size() > 0">
              and id IN
              <foreach collection="courseIds" item="courseId" open="(" separator="," close=")">
                  #{courseId}
              </foreach>
          </if>
    </select>
    <select id="getCourseSumPlayTime" resultType="java.lang.Integer">
        SELECT IFNULL(SUM(play_time), 0) AS total_play_time
        FROM courseware cw
        WHERE cw.is_del = 0
          and cw.course_id = #{courseId,jdbcType=VARCHAR}
        GROUP BY course_id
    </select>
</mapper>
