package com.wunding.learn.course.service.admin.query;

import com.wunding.learn.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.Parameter;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合并课件到课程查询参数对象
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MergeCourseQuery extends BaseEntity {

    @Parameter(description = "部门id")
    private String orgId;

    @Parameter(description = "课件名称")
    private String cwName;

    @Parameter(description = "上传者列表集合")
    private String userIds;

    @Parameter(hidden = true)
    private List<String> userIdsVo;
}
