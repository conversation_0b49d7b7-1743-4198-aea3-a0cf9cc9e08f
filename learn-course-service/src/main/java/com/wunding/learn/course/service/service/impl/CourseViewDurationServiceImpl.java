package com.wunding.learn.course.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.dto.ExcitationBizBaseDTO;
import com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum;
import com.wunding.learn.common.enums.excitation.ExcitationEventEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.mq.dto.ExcitationMQEventDTO;
import com.wunding.learn.common.mq.event.ResourceInteractEvent;
import com.wunding.learn.common.mq.event.ResourceInteractEvent.ResourceInteractEventRoutingKeyConstants;
import com.wunding.learn.common.mq.event.course.CourseFinishEvent;
import com.wunding.learn.common.mq.event.course.CourseLearnEvent;
import com.wunding.learn.common.mq.event.excitation.ExcitationMQEvent;
import com.wunding.learn.common.mq.event.mapshape.MapKnowledgeLearningFinishEvent;
import com.wunding.learn.common.mq.event.reading.ReadingSignFinishEvent;
import com.wunding.learn.common.mq.service.impl.RabbitMqProducer;
import com.wunding.learn.common.mybatis.service.impl.BaseServiceImpl;
import com.wunding.learn.common.redis.component.RedisScheduledService;
import com.wunding.learn.common.redis.util.RedisLockUtil;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.date.DateUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.course.api.dto.CourseDurationDTO;
import com.wunding.learn.course.service.client.dto.CourseViewDurationClientDTO;
import com.wunding.learn.course.service.client.dto.CourseViewDurationDTO;
import com.wunding.learn.course.service.client.dto.CourseViewDurationWithBizBO;
import com.wunding.learn.course.service.client.dto.LearnTimeDTO;
import com.wunding.learn.course.service.client.dto.LearnTimeReportResultDTO;
import com.wunding.learn.course.service.client.dto.UserCourseDurationDTO;
import com.wunding.learn.course.service.client.query.CourseViewDurationClientQuery;
import com.wunding.learn.course.service.client.query.UserCourseDurationQuery;
import com.wunding.learn.course.service.client.query.UserCourseDurationSimpleQuery;
import com.wunding.learn.course.service.mapper.CourseMapper;
import com.wunding.learn.course.service.mapper.CourseViewDurationMapper;
import com.wunding.learn.course.service.mapper.CourseViewMapper;
import com.wunding.learn.course.service.mapper.CoursewareMapper;
import com.wunding.learn.course.service.mapper.CoursewareUserRecordMapper;
import com.wunding.learn.course.service.mapper.UserCourseRecordMapper;
import com.wunding.learn.course.service.model.Course;
import com.wunding.learn.course.service.model.CourseView;
import com.wunding.learn.course.service.model.CourseViewDuration;
import com.wunding.learn.course.service.model.Courseware;
import com.wunding.learn.course.service.model.CoursewareUserRecord;
import com.wunding.learn.course.service.model.UserCourseRecord;
import com.wunding.learn.course.service.service.ICourseService;
import com.wunding.learn.course.service.service.ICourseViewDurationService;
import com.wunding.learn.exam.api.dto.ExamResultDTO;
import com.wunding.learn.exam.api.service.AnswerRecordFeign;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.service.ParaFeign;
import com.wunding.learn.user.api.service.UserFeign;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <p> 课件学时上报表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">余学益</a>
 * @date 2022-05-10
 */
@Slf4j
@Service("courseViewDurationService")
public class CourseViewDurationServiceImpl extends
    BaseServiceImpl<CourseViewDurationMapper, CourseViewDuration> implements
    ICourseViewDurationService {

    @Resource
    private CoursewareUserRecordMapper coursewareUserRecordMapper;
    @Resource
    private CoursewareMapper coursewareMapper;
    @Resource
    private CourseViewMapper courseViewMapper;
    @Resource
    private RedisScheduledService redisScheduledService;
    @Resource
    private UserCourseRecordMapper userCourseRecordMapper;
    @Resource
    private RabbitMqProducer mqProducer;
    @Resource
    private AnswerRecordFeign answerRecordFeign;
    @Resource
    private UserFeign userFeign;
    @Resource
    private CourseMapper courseMapper;

    @Resource
    @Lazy
    private ICourseService courseService;

    @Resource
    private ParaFeign paraFeign;

    @Override
    public CourseViewDurationClientDTO sumCourseStudyTime(CourseViewDurationClientQuery courseView) {
        return baseMapper.sumCourseStudyTime(courseView);
    }

    @Override
    public Map<String, CourseViewDurationDTO> sumCourseStudyTime(
        List<CourseViewDurationClientQuery> courseViewList) {

        List<CourseViewDurationDTO> courseViewDurationDTOS = new ArrayList<>(courseViewList.size());

        if (courseViewList.size() > 10000L) {
            List<List<CourseViewDurationClientQuery>> partition = Lists.partition(courseViewList, 10000);
            partition.forEach(
                query -> courseViewDurationDTOS.addAll(baseMapper.sumCourseStudyTimeToMap(query)));
        } else {
            courseViewDurationDTOS.addAll(baseMapper.sumCourseStudyTimeToMap(courseViewList));
        }

        return courseViewDurationDTOS.stream()
            .collect(Collectors.toMap(CourseViewDurationDTO::getMapKey, Function.identity()));
    }

    @Override
    public Integer getNewProgress(CourseViewDurationClientQuery courseView) {
        return baseMapper.getNewProgress(courseView);
    }

    @Override
    @Async
    public CompletableFuture<LearnTimeReportResultDTO> reportLearnTime(List<LearnTimeDTO> learnTimeDTOList) {
        String reportLearnTimeKey = "reportLearnTime:" + UserThreadContext.getUserId();
        LearnTimeReportResultDTO resultDTO = new LearnTimeReportResultDTO();
        try {
            RedisLockUtil.acquire(reportLearnTimeKey, 30);
            // course_view_duration 的 view_time 和 courseware_user_record 的 endTime 有误差使用同一个时间参数
            Date dateContext = new Date();
            long start = System.currentTimeMillis();
            // 学时上报记录列表
            List<CourseViewDurationWithBizBO> courseViewDurations = new ArrayList<>(learnTimeDTOList.size());
            // 课程的学习时长
            Map<String, Integer> courseLearnTime = new HashMap<>(learnTimeDTOList.size());
            // 课件的学习时长
            Map<String, Integer> courseWareLearnTime = new HashMap<>(learnTimeDTOList.size());
            dealWithLearnTime(learnTimeDTOList, courseLearnTime, courseWareLearnTime, dateContext, courseViewDurations);
            log.info("courseViewDurations 数量:" + courseViewDurations.size());
            if (courseViewDurations.size() > 1) {
                log.info(JsonUtil.objToJson(courseViewDurations));
            }
            // 保存学时上报记录
            saveBatch2(courseViewDurations.stream().map(bo -> {
                CourseViewDuration duration = new CourseViewDuration();
                BeanUtils.copyProperties(bo, duration);
                return duration;
            }).collect(Collectors.toList()));
            // 处理课件和课程的记录、积分等
            resultDTO = reportTimeCourseWareHandler(UserThreadContext.getUserId(), courseLearnTime, courseWareLearnTime,
                courseViewDurations, dateContext);

            // 把用户放到今日的学习名单中，存放在redis中
            redisScheduledService.addLearnUser(UserThreadContext.getUserId());
            log.info("学时上报消耗总时长:" + (System.currentTimeMillis() - start) + "毫秒。");
        } catch (Exception e) {
            log.error("=======学时上报失败====", e);
        } finally {
            RedisLockUtil.release(reportLearnTimeKey);
        }
        return CompletableFuture.completedFuture(resultDTO);
    }

    private void dealWithLearnTime(List<LearnTimeDTO> learnTimeDTOList, Map<String, Integer> courseLearnTime,
        Map<String, Integer> courseWareLearnTime, Date dateContext,
        List<CourseViewDurationWithBizBO> courseViewDurations) {
        for (LearnTimeDTO learnTime : learnTimeDTOList) {
            // 计算上报中每个课程的总时长
            courseLearnTime.merge(learnTime.getCourseId(), learnTime.getTime(), Integer::sum);
            // 计算上报中每个课件的总时长
            courseWareLearnTime.merge(learnTime.getCourseWareId(), learnTime.getTime(), Integer::sum);
            //此处要进行去重操作
            CourseViewDurationWithBizBO bo = getCourseViewDuration(learnTime, dateContext);
            //判断这个list是否存在这个对象
            boolean isExist = false;
            for (CourseViewDurationWithBizBO subbo : courseViewDurations) {
                isExist = isExist(subbo, bo, isExist);
            }
            if (!isExist) {
                courseViewDurations.add(bo);
            }
        }
    }

    private boolean isExist(CourseViewDurationWithBizBO subbo, CourseViewDurationWithBizBO bo, boolean isExist) {
        if (subbo.getCwId().equals(bo.getCwId()) && subbo.getViewBy().equals(bo.getViewBy())) {
            isExist = true;
            //设置最早开始时间
            if (subbo.getViewTime().compareTo(bo.getViewTime()) > 0) {
                subbo.setViewTime(bo.getViewTime());
            }
            //设置最新的结束时间
            if (subbo.getEndTime().compareTo(bo.getEndTime()) < 0) {
                subbo.setEndTime(bo.getEndTime());
            }
            //累加学习时长
            subbo.setDuration(subbo.getDuration() + bo.getDuration());
            //更新进度
            if (subbo.getProgress().compareTo(bo.getProgress()) < 0) {
                subbo.setProgress(bo.getProgress());
            }
        }
        return isExist;
    }

    @Override
    public Double getUserCourseDruation(String userId) {
        QueryWrapper<CourseViewDuration> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("view_by", userId);
        queryWrapper.select("sum(duration) as duration");
        CourseViewDuration courseViewDuration = getOne(queryWrapper);
        if (!Objects.isNull(courseViewDuration)) {
           return Double.valueOf(courseViewDuration.getDuration());
        }
        return 0.00;
    }


    /**
     * 解析上报的记录，生成记录对象
     *
     * @param learnTime 上报的对象
     * @return {@link CourseViewDuration}
     */
    private CourseViewDurationWithBizBO getCourseViewDuration(LearnTimeDTO learnTime, Date now) {
        CourseViewDurationWithBizBO courseViewDuration = new CourseViewDurationWithBizBO();
        BeanUtils.copyProperties(learnTime, courseViewDuration);
        courseViewDuration.setId(StringUtil.newId());
        courseViewDuration.setCourseId(learnTime.getCourseId());
        courseViewDuration.setCwId(learnTime.getCourseWareId());
        courseViewDuration.setDuration(learnTime.getTime());
        courseViewDuration.setProgress(learnTime.getProgress());
        courseViewDuration.setViewBy(UserThreadContext.getUserId());
        courseViewDuration.setOrgId(UserThreadContext.getOrgId());
        // 这个时间记录其实不准确，因为一次可以提交多个课件，肯定时间是不对的
        courseViewDuration.setViewTime(new Date(now.getTime() - learnTime.getTime() * 1000));
        courseViewDuration.setEndTime(now);
        // 是否离线，不在使用，考虑是否可以去掉字段
        courseViewDuration.setIsOffline(0);
        // 上报来源，统一接口，没有区分，考虑是否可以去掉
        courseViewDuration.setIsSource(1);
        return courseViewDuration;
    }

    /**
     * 学时上报中课件的处理，包含课件学习记录、积分 同时处理课件相关的课程
     *
     * @param userId              用户 ID
     * @param courseLearnTime     课程上报时长 Map
     * @param courseWareLearnTime 课件上报时长 Map
     * @throws Exception 异常信息
     */
    @SuppressWarnings("squid:S3776")
    private LearnTimeReportResultDTO reportTimeCourseWareHandler(String userId, Map<String, Integer> courseLearnTime,
        Map<String, Integer> courseWareLearnTime, List<CourseViewDurationWithBizBO> courseViewDurations, Date now) {

        log.info("reportTimeCourseWareHandler进来了1:" + JsonUtil.objToJson(courseLearnTime));
        log.info("reportTimeCourseWareHandler进来了2" + JsonUtil.objToJson(courseWareLearnTime));
        log.info("reportTimeCourseWareHandler进来了3" + JsonUtil.objToJson(courseViewDurations));
        log.info("reportTimeCourseWareHandler进来了4" + JsonUtil.objToJson(userId));

        LearnTimeReportResultDTO resultDTO = new LearnTimeReportResultDTO();
        // 需要处理积分的课件 ID 列表
        Set<String> courseIdSet = courseLearnTime.keySet();
        Map<String, CourseViewDurationWithBizBO> cwDurationWithBizBOMap = courseViewDurations.stream()
            .collect(Collectors.toMap(CourseViewDurationWithBizBO::getCwId, Function.identity(), (v1, v2) -> v1));
        Map<String, ExcitationBizBaseDTO> courseBizMap = courseViewDurations.stream()
            .collect(Collectors.toMap(CourseViewDurationWithBizBO::getCourseId, bo -> {
                ExcitationBizBaseDTO bizBaseDTO = new ExcitationBizBaseDTO();
                BeanUtils.copyProperties(bo, bizBaseDTO);
                return bizBaseDTO;
            }, (v1, v2) -> {
                v1.setIsExchange(v1.getIsExchange() == 1 || v2.getIsExchange() == 1 ? 1 : 0);
                return v1;
            }));
        // 获取已有的课件学习记录
        LambdaQueryWrapper<CoursewareUserRecord> userRecordWrapper = new LambdaQueryWrapper<>();
        userRecordWrapper.in(!CollectionUtils.isEmpty(courseIdSet), CoursewareUserRecord::getCourseId, courseIdSet)
            .eq(CoursewareUserRecord::getUserId, UserThreadContext.getUserId());
        List<CoursewareUserRecord> coursewareUserRecordList = coursewareUserRecordMapper.selectList(userRecordWrapper);

        log.info("coursewareUserRecordList:" + JsonUtil.objToJson(coursewareUserRecordList));
        if (CollectionUtils.isEmpty(coursewareUserRecordList)) {

            coursewareUserRecordList = coursewareUserRecordMapper.selectListFromDB(courseIdSet,
                UserThreadContext.getUserId());
            log.info("reportTimeCourseWareHandlercoursewareUserRecordList 缓存失效了:" + JsonUtil.objToJson(
                coursewareUserRecordList));

            log.info(
                "reportTimeCourseWareHandlercoursewareUserRecordList 打印查询条件:" + JsonUtil.objToJson(courseIdSet));
            log.info("reportTimeCourseWareHandlercoursewareUserRecordList 打印查询条件:" + JsonUtil.objToJson(
                UserThreadContext.getUserId()));
            if (CollectionUtils.isEmpty(coursewareUserRecordList)) {
                log.info("reportTimeCourseWareHandler完成了，数据库也没查到，十有八九要跪");
            }
        }
        Map<String, Map<String, CoursewareUserRecord>> coursewareUserRecordMap = coursewareUserRecordList.stream()
            .collect(Collectors.groupingBy(CoursewareUserRecord::getCourseId,
                Collectors.toMap(CoursewareUserRecord::getCoursewareId, a -> a, (key1, key2) -> key1)));

        // 获取课程下的讲师
        LambdaQueryWrapper<Courseware> coursewareWrapper = new LambdaQueryWrapper<>();
        coursewareWrapper.in(!CollectionUtils.isEmpty(courseIdSet), Courseware::getCourseId, courseIdSet);
        List<Courseware> coursewareList = coursewareMapper.selectList(coursewareWrapper);
        log.info("reportTimeCourseWareHandlercoursewareList:" + JsonUtil.objToJson(coursewareList));

        if (CollectionUtils.isEmpty(coursewareList)) {

            coursewareList = coursewareMapper.selectListFromDB(courseIdSet);
            log.info("reportTimeCourseWareHandlercoursewareList 缓存失效了:" + JsonUtil.objToJson(coursewareList));
            log.info("reportTimeCourseWareHandlercoursewareList 打印查询条件:" + JsonUtil.objToJson(courseIdSet));
            if (CollectionUtils.isEmpty(coursewareList)) {
                log.info("reportTimeCourseWareHandler完成了，数据库也没查到，十有八九要跪");
            }
        }

        Map<String, List<Courseware>> coursewareMap = coursewareList.stream()
            .collect(Collectors.groupingBy(Courseware::getCourseId));

        log.info("reportTimeCourseWareHandler到这里了。");
        for (String courseId : courseIdSet) {
            ExcitationBizBaseDTO bizBaseDTO = courseBizMap.get(courseId);
            boolean courseIsLearned = true;
            // 获取已有的课件学习记录
            Map<String, CoursewareUserRecord> cwUserRecordMap = coursewareUserRecordMap.get(courseId);
            if (CollectionUtils.isEmpty(cwUserRecordMap)) {
                cwUserRecordMap = new HashMap<>();
            }
            List<Courseware> courseWares = coursewareMap.get(courseId);
            if (courseWares == null) {
                courseWares = List.of();
            }
            log.info("reportTimeCourseWareHandler 到这里了。courseWares.size:" + courseWares.size());
            for (Courseware courseware : courseWares) {
                //完成类型
                Integer finishType = courseware.getFinishType();
                //设置上报课件需要学习时长（目前只会一个课件一个课件的上报，所以直接拿集合的第一个数据就是上报的课件）
                CourseViewDurationWithBizBO reportCourseware = courseViewDurations.get(0);
                if (null != reportCourseware && courseware.getId().equals(reportCourseware.getCwId())) {
                    resultDTO.setSetPlayTime(courseware.getPlayTime());
                    resultDTO.setFinishType(courseware.getFinishType());
                }

                if (!courseWareLearnTime.containsKey(courseware.getId())) {
                    // 此课件没上报，查询是否有学习记录
                    CoursewareUserRecord courseWareUserRecord = cwUserRecordMap.get(courseware.getId());
                    // 如果课件没有学习记录或者学习状态是未学完
                    if (null == courseWareUserRecord || 0 == courseWareUserRecord.getIsLearned()) {
                        // 设置为未学完
                        courseIsLearned = false;
                    }
                    continue;
                }

                // 获取到当前课件的学时上报中的学习时长
                CourseViewDurationWithBizBO durationWithBizBO = Optional.ofNullable(
                    cwDurationWithBizBOMap.get(courseware.getId())).orElse(new CourseViewDurationWithBizBO());
                Integer progress = Optional.ofNullable(durationWithBizBO.getProgress()).orElse(0);

                // 校验课程关联考试是否完成
                boolean isFinishExam = checkIsFinishExam(courseware);

                // 此课件有上报，处理
                int sumCourseWareDuration;
                CoursewareUserRecord courseWareUserRecord;
                log.info("reportTimeCourseWareHandler到这里了2，cwUserRecordMap：" + JsonUtil.objToJson(cwUserRecordMap));
                log.info("reportTimeCourseWareHandler到这里了2，courseware：" + JsonUtil.objToJson(courseware));
                // 如果有课件历史学习记录(数据库已有课件学时记录) 且 历史记录中有当前课件的学习记录
                if (!CollectionUtils.isEmpty(cwUserRecordMap) && cwUserRecordMap.containsKey(courseware.getId())) {
                    // 获取当前课件的历史学习记录
                    courseWareUserRecord = cwUserRecordMap.get(courseware.getId());
                    // 求课件的学习时长总和 = 本次上报学习时长 + 已经(历史)学习时长
                    long learnTime =
                        courseWareUserRecord.getDuration() + courseWareLearnTime.get(courseware.getId());
                    courseWareUserRecord.setDuration(learnTime);
                    resultDTO.setLearnedTime(learnTime);


                    //这里如果这个人是已经完成的会被搞回去重新学了。要进行判断,如果库里面的进度大于现在的进度不进行更新了
                    if (courseWareUserRecord.getProgress().compareTo(progress.longValue()) < 0) {
                        courseWareUserRecord.setProgress(progress.longValue());
                    }

                    // 按时长: 课件的学习时长总和 >= 课件时长 且 完成了对应考试要求
                    // 按进度: 该课件是视频或音频课件 且播放进度 >= 课件完成进度要求 且 完成了对应考试要求
                    boolean learned;
                    if (Objects.equals(0, finishType)) {
                        learned = (courseWareUserRecord.getDuration() >= courseware.getPlayTime() && isFinishExam);
                    } else {
                        learned = (courseWareUserRecord.getProgress() >= courseware.getRequiredProgress()
                            && ("Video".equals(courseware.getCwType()) || "Audio".equals(courseware.getCwType()))
                            && isFinishExam
                        );
                    }

                    resultDTO.setLearnedTime(courseWareUserRecord.getDuration());
                    resultDTO.setIsLearned(learned);
                    if (learned) {
                        courseWareUserRecord.setProgress(100L);
                        courseWareUserRecord.setIsLearned(1);
                        courseWareUserRecord.setFinishTime(new Date());
                        // 修改已完成课件的积分情况
                        mqProducer.sendMsg(new ExcitationMQEvent(
                            new ExcitationMQEventDTO(
                                userId,
                                ExcitationEventEnum.finishCourseWare.name(),
                                courseware.getId(),
                                ExcitationEventCategoryEnum.COURSE_WARE.getCode())
                                .setTargetName(courseware.getCwName())
                                .setBizType(Optional.ofNullable(durationWithBizBO.getBizType())
                                    .orElse(ExcitationEventCategoryEnum.COURSE.getCode()))
                                .setBizId(Optional.ofNullable(durationWithBizBO.getBizId())
                                    .orElse(durationWithBizBO.getCourseId()))
                                .setIsExchange(durationWithBizBO.getIsExchange())));
                        // 发送 学完课件（按课件时长）事件
                        mqProducer.sendMsg(new ExcitationMQEvent(
                            new ExcitationMQEventDTO(
                                userId,
                                ExcitationEventEnum.finishCourseWareByTime.name(),
                                courseware.getId(),
                                ExcitationEventCategoryEnum.COURSE_WARE.getCode())
                                .setTargetName(courseware.getCwName())
                                .setBizType(Optional.ofNullable(durationWithBizBO.getBizType())
                                    .orElse(ExcitationEventCategoryEnum.COURSE.getCode()))
                                .setBizId(Optional.ofNullable(durationWithBizBO.getBizId())
                                    .orElse(durationWithBizBO.getCourseId()))
                                .setIsExchange(durationWithBizBO.getIsExchange())));
                    } else {
                        // 如果该课件未完成，则该课程也未完成
                        courseIsLearned = false;
                    }
                    courseWareUserRecord.setEndTime(now);
                    coursewareUserRecordMapper.updateById(courseWareUserRecord);
                } else {

                    // 当前用户没有该课程的课件历史学习记录或者课件学习记录中不包含当前课件
                    // 第一次学习这个课件
                    courseWareUserRecord = new CoursewareUserRecord();
                    //在这里增加一个查询
                    LambdaQueryWrapper<CoursewareUserRecord> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(CoursewareUserRecord::getUserId, userId)
                        .eq(CoursewareUserRecord::getCoursewareId, courseware.getId());
                    List<CoursewareUserRecord> list = coursewareUserRecordMapper.selectList(queryWrapper);
                    if (CollectionUtils.isEmpty(coursewareUserRecordList)) {

                        coursewareUserRecordList = coursewareUserRecordMapper.selectListFromDB(courseIdSet,
                            UserThreadContext.getUserId());
                        log.info("看一下数据库是否真的没有：" + JsonUtil.objToJson(coursewareUserRecordList));
                    }
                    if (CollectionUtils.isEmpty(list)) {
                        //第一次新增
                        log.info("第一次学习，就放过他");
                        courseWareUserRecord.setId(StringUtil.newId());
                    } else {
                        courseWareUserRecord = list.get(0);
                    }

                    courseWareUserRecord.setUserId(userId);
                    courseWareUserRecord.setCourseId(courseware.getCourseId());
                    courseWareUserRecord.setCoursewareId(courseware.getId());
                    courseWareUserRecord.setProgress(progress.longValue());
                    courseWareUserRecord.setDuration(courseWareLearnTime.get(courseware.getId()).longValue());

                    // 按时长: 课件的学习时长总和 >= 课件时长 且 完成了对应考试要求
                    // 按进度: 该课件是视频或音频课件 且播放进度 >= 课件完成进度要求 且 完成了对应考试要求
                    boolean learned;
                    if (Objects.equals(0, finishType)) {
                        learned = (courseWareUserRecord.getDuration() >= courseware.getPlayTime() && isFinishExam);
                    } else {
                        learned = (courseWareUserRecord.getProgress() >= courseware.getRequiredProgress()
                            && ("Video".equals(courseware.getCwType()) || "Audio".equals(courseware.getCwType()))
                            && isFinishExam
                        );
                    }

                    resultDTO.setLearnedTime(courseWareUserRecord.getDuration());
                    resultDTO.setIsLearned(learned);

                    if (learned) {
                        courseWareUserRecord.setProgress(100L);
                        courseWareUserRecord.setIsLearned(1);
                        courseWareUserRecord.setFinishTime(new Date());
                        // 修改已完成课件的积分情况
                        mqProducer.sendMsg(new ExcitationMQEvent(
                            new ExcitationMQEventDTO(
                                userId,
                                ExcitationEventEnum.finishCourseWare.name(),
                                courseware.getId(),
                                ExcitationEventCategoryEnum.COURSE_WARE.getCode())
                                .setTargetName(courseware.getCwName())
                                .setBizType(Optional.ofNullable(durationWithBizBO.getBizType())
                                    .orElse(ExcitationEventCategoryEnum.COURSE.getCode()))
                                .setBizId(Optional.ofNullable(durationWithBizBO.getBizId())
                                    .orElse(durationWithBizBO.getCourseId()))
                                .setIsExchange(durationWithBizBO.getIsExchange())));
                        // 学完课件（按课件时长）
                        mqProducer.sendMsg(new ExcitationMQEvent(
                            new ExcitationMQEventDTO(
                                userId,
                                ExcitationEventEnum.finishCourseWareByTime.name(),
                                courseware.getId(),
                                ExcitationEventCategoryEnum.COURSE_WARE.getCode())
                                .setTargetName(courseware.getCwName())
                                .setBizType(Optional.ofNullable(durationWithBizBO.getBizType())
                                    .orElse(ExcitationEventCategoryEnum.COURSE.getCode()))
                                .setBizId(Optional.ofNullable(durationWithBizBO.getBizId())
                                    .orElse(durationWithBizBO.getCourseId()))
                                .setIsExchange(durationWithBizBO.getIsExchange())));
                    } else {
                        courseIsLearned = false;
                        courseWareUserRecord.setIsLearned(0);
                    }

                    courseWareUserRecord
                        .setStartTime(new Date(now.getTime() - courseWareLearnTime.get(courseware.getId()) * 1000));
                    courseWareUserRecord.setEndTime(now);
                    UserDTO userDTO = userFeign.getUserById(userId);
                    courseWareUserRecord.setLevelPath(userDTO.getLevelPath());
                    log.info("新增课件学习,{}", courseWareUserRecord.toString());

                    if (CollectionUtils.isEmpty(list)) {
                        coursewareUserRecordMapper.insert(courseWareUserRecord);
                    } else {
                        //修复三秒的BUG.
                        log.info("还真的是更新这玩意：" + JsonUtil.objToJson(courseWareUserRecord));
                        coursewareUserRecordMapper.update(courseWareUserRecord,
                            new LambdaUpdateWrapper<CoursewareUserRecord>().eq(CoursewareUserRecord::getId,
                                courseWareUserRecord.getCourseId()));
                    }
                }
                // 维护冗余courseView表
                if (courseWareUserRecord.getIsLearned() == 1) {
                    saveCourseView(courseware, now);
                }
                // 课件学习总时长（单位秒）
                sumCourseWareDuration = courseWareUserRecord.getDuration().intValue();
//                // 学完某一个课件就算打卡或者或者观看某一个大于十分钟的课件 满十分钟
                // 课件时长小于10分钟且已学完，或课件大于十分钟，但是学习时长超过了十分钟，则判定为已学完可以打卡
                // 学习够10分钟才算打卡，个人觉得这个很不人性化，不如上面的人性

                // 功能优化,取消固定的(10分钟)学习时间,有管理端定义学习时长.

                //发送完成课程事件
                ReadingSignFinishEvent readingSignFinishEvent = new ReadingSignFinishEvent(courseId, userId,
                    new Date(), sumCourseWareDuration);
                log.info("send readingSinEvent：{}", readingSignFinishEvent);
                mqProducer.sendMsg(readingSignFinishEvent);

            }
            // 处理课程
            reportTimeCourseHandler(userId, courseLearnTime.get(courseId), courseId, courseIsLearned, bizBaseDTO, now);
        }
        return resultDTO;
    }

    /**
     * 校验课程关联考试是否完成 【课件关联考试时的完成条件（-1:不涉及考试 0:提交考试即完成 1:通过考试才完成，仅在 exam_id 不为空时生效）】
     */
    private boolean checkIsFinishExam(Courseware courseware) {

        // 获取关联考试完成条件的设置
        Integer examFinishType = courseware.getExamFinishType();

        // 不存在关联考试 或 关联考试完成条件为-1:不涉及考试,则直接返回true
        if (StringUtils.isEmpty(courseware.getExamId()) || examFinishType.equals(-1)) {
            return true;
        }

        // 查询考试结果
        Set<String> examIdSet = new HashSet<>(1);
        examIdSet.add(courseware.getExamId());
        List<ExamResultDTO> examResultList = answerRecordFeign.getExamResultByIds(examIdSet);

        // 没有考试记录
        if (CollectionUtils.isEmpty(examResultList)) {
            return false;
        }

        // 存在考试记录，且关联考试完成条件为0:提交考试即完成，则直接返回true
        if (examFinishType.equals(0)) {
            return true;
        }

        // 存在考试记录，且关联考试完成条件为1:通过考试才完成，则判断考试结果
        return examFinishType.equals(1) && GeneralJudgeEnum.CONFIRM.getValue()
            .equals(examResultList.getLast().getPass());
    }


    /**
     * 维护老表courseView，在系统中很多地方用到它
     *
     * @param courseware
     * @param now
     * @throws Exception 异常信息
     */
    private void saveCourseView(Courseware courseware, Date now) {
        LambdaQueryWrapper<CourseView> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CourseView::getCourseId, courseware.getCourseId()).eq(CourseView::getCwId, courseware.getId())
            .eq(CourseView::getViewBy, UserThreadContext.getUserId());
        Long count = courseViewMapper.selectCount(wrapper);
        if (count == 0) {
            log.info("课件检查通过，向courseView表中添加记录");
            CourseView cv = new CourseView();
            cv.setId(StringUtil.newId());
            cv.setCourseId(courseware.getCourseId());
            cv.setCwId(courseware.getId());
            cv.setViewBy(UserThreadContext.getUserId());
            cv.setViewTime(now);
            cv.setOrgId(UserThreadContext.getOrgId());
            courseViewMapper.insert(cv);
            // 冗余更新课件综合星级
            mqProducer.sendMsg(
                new ResourceInteractEvent(ResourceInteractEventRoutingKeyConstants.COURSE_WARE_CLICK_NUMBER_EVENT,
                    courseware.getId()));
        }
    }

    /**
     * 学时上报后课程处理逻辑，包含学习课程记录、积分、头条相关处理
     *
     * @param userId          用户 ID
     * @param courseLearnTime 上报课程的学时
     * @param courseId        课程 ID
     * @param courseIsLearned 课程是否已学
     * @param courseIsLearned 激励业务属性数据对象
     */
    private void reportTimeCourseHandler(String userId, Integer courseLearnTime, String courseId,
        boolean courseIsLearned, ExcitationBizBaseDTO bizBaseDTO, Date now) {
        String lockName = courseId + ":" + userId;
        RedisLockUtil.acquire(lockName, 30);
        // 查询课程学习记录
        LambdaQueryWrapper<UserCourseRecord> userCourseRecordWrapper = new LambdaQueryWrapper<>();
        userCourseRecordWrapper.eq(UserCourseRecord::getCourseId, courseId).eq(UserCourseRecord::getUserId, userId);
        UserCourseRecord userCourseRecord = userCourseRecordMapper.selectOne(userCourseRecordWrapper);
        if (null == userCourseRecord) {
            // 保存课程学习记录
            userCourseRecord = new UserCourseRecord();
            userCourseRecord.setId(StringUtil.newId());
            userCourseRecord.setIsLearned(courseIsLearned ? 1 : 0);
            userCourseRecord.setCourseId(courseId);
            userCourseRecord.setUserId(userId);
            userCourseRecord.setDuration(Long.valueOf(courseLearnTime));
            //课程学习开始时间根据=上报时间-所学秒速
            userCourseRecord.setCreateTime(new Date(now.getTime() - courseLearnTime * 1000));
            userCourseRecord.setUpdateTime(now);
            UserDTO userDTO = userFeign.getUserById(userId);
            userCourseRecord.setLevelPath(userDTO.getLevelPath());
            if (courseIsLearned) {
                userCourseRecord.setFinishTime(now);
            }
            log.info("新增课程学习,{}", userCourseRecord);
            userCourseRecordMapper.insert(userCourseRecord);
            log.info("course clickNumber increment, curseId: {}", courseId);
            courseService.addCourseClickNumber(courseId);

        } else {
            // 更新课程学习记录
            userCourseRecord.setDuration(userCourseRecord.getDuration() + courseLearnTime);
            // 只有以前未学完的课程，才会更新这个课程的已学状态
            if (userCourseRecord.getIsLearned() == 0 && courseIsLearned) {
                // 第一次完成课程
                userCourseRecord.setIsLearned(1);
                userCourseRecord.setFinishTime(now);
            }
            userCourseRecord.setUpdateTime(now);
            userCourseRecordMapper.updateById(userCourseRecord);
        }

        mqProducer.sendMsg(
            new CourseLearnEvent(courseId, userId, userCourseRecord.getDuration(), userCourseRecord.getIsLearned()));

        // 课程已学完
        if (1 == userCourseRecord.getIsLearned()) {
            Course course = courseMapper.selectById(courseId);
            if (null != course) {
                //  发送消息[课程完成]
                mqProducer.sendMsg(new ExcitationMQEvent(
                    new ExcitationMQEventDTO(
                        userId,
                        ExcitationEventEnum.finishCourse.name(),
                        courseId,
                        ExcitationEventCategoryEnum.COURSE.getCode()).
                        setTargetName(course.getCourseName())
                        .setBizType(Optional.ofNullable(bizBaseDTO.getBizType())
                            .orElse(ExcitationEventCategoryEnum.COURSE.getCode()))
                        .setBizId(Optional.ofNullable(bizBaseDTO.getBizId()).orElse(courseId))
                        .setIsExchange(bizBaseDTO.getIsExchange()).setIsTrain(course.getIsTrain())));
                //  TODO: 移除了关于课程的业务（验证课程是否头条，并更新头条状态） 需要等待跨模块的头条业务外部接口调用

                //发送完成课程事件
                mqProducer.sendMsg(new CourseFinishEvent(courseId, userId, userId));

                //发送完成能力事件
                mqProducer.sendMsg(new MapKnowledgeLearningFinishEvent(courseId, userId));
            }
        }
        RedisLockUtil.release(lockName);
    }


    @Override
    public PageInfo<UserCourseDurationDTO> getUserCourseDurationByPage(
        UserCourseDurationQuery userCourseDurationQuery) {
        userCourseDurationQuery.setUserId(UserThreadContext.getUserId());
        PageInfo<CourseViewDuration> courseViewDurationPageInfo = PageMethod.startPage(
                userCourseDurationQuery.getPageNo(), userCourseDurationQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.selectUserCourseViewDurationList(userCourseDurationQuery));
        //复制
        PageInfo<UserCourseDurationDTO> result = new PageInfo<>();
        BeanUtils.copyProperties(courseViewDurationPageInfo, result);

        List<UserCourseDurationDTO> userCourseDurationDTOList = new ArrayList<>();

        List<CourseViewDuration> courseViewDurationList = courseViewDurationPageInfo.getList();
        for (CourseViewDuration courseViewDuration : courseViewDurationList) {
            UserCourseDurationDTO courseViewDurationVo = new UserCourseDurationDTO();

            courseViewDurationVo.setId(courseViewDuration.getId());
            courseViewDurationVo.setTitle(getCourseWareName(courseViewDuration.getCwId()));
            courseViewDurationVo.setDuration(DateUtil.convertTimeRules2((long) courseViewDuration.getDuration()));

            courseViewDurationVo.setDurationTime(DateUtil.formatDate(courseViewDuration.getEndTime()));
            userCourseDurationDTOList.add(courseViewDurationVo);
        }
        result.setList(userCourseDurationDTOList);
        return result;
    }

    /**
     * 获取课件名称
     *
     * @param id 课件id
     * @return
     */
    private String getCourseWareName(String id) {
        Courseware courseWare = coursewareMapper.selectById(id);
        return null != courseWare ? courseWare.getCwName() : Strings.EMPTY;
    }


    @Override
    public String findUserCourseDuration(UserCourseDurationSimpleQuery userCourseDurationSimpleQuery) {
        userCourseDurationSimpleQuery.setUserId(UserThreadContext.getUserId());
        Long number = baseMapper.selectUserCourseViewDuration(userCourseDurationSimpleQuery);
        double v = (double) number / (Long.parseLong("60") * 60);
        return String.format("%.2f", v);
    }

    @Override
    public Long findUserCourseDurationByDate(UserCourseDurationSimpleQuery userCourseDurationSimpleQuery) {
        if (StringUtils.isEmpty(userCourseDurationSimpleQuery.getUserId())) {
            userCourseDurationSimpleQuery.setUserId(UserThreadContext.getUserId());
        }
        return baseMapper.selectUserCourseViewDuration(userCourseDurationSimpleQuery);
    }


    @Override
    public Map<String, Long> getCourseDurationMap(Collection<String> userIds,
        Collection<String> courseIds) {
        if (CollectionUtils.isEmpty(userIds) || CollectionUtils.isEmpty(courseIds)) {
            return Map.of();
        }
        List<CourseDurationDTO> courseDurationMap = baseMapper.getCourseDurationMap(userIds, courseIds);
        if (CollectionUtils.isEmpty(courseDurationMap)) {
            return Map.of();
        }
        return courseDurationMap.stream()
            .collect(Collectors.toMap(
                CourseDurationDTO::getUserId,
                CourseDurationDTO::getDuration,
                (existing, replacement) -> existing)
            );
    }

    @Override
    @SuppressWarnings("squid:S6809")
    public CompletableFuture<LearnTimeReportResultDTO> controlsReportLearnTime(List<LearnTimeDTO> learnTimeDTOList) {
        String paraValue = paraFeign.getParaValue("309");
        Integer isAsynchronous = Integer.valueOf(paraValue);
        if (GeneralJudgeEnum.NEGATIVE.getValue().equals(isAsynchronous)) {
            return reportLearnTime(learnTimeDTOList);
        }
        ICourseViewDurationService courseViewDurationService = SpringUtil.getBean("courseViewDurationService",
            ICourseViewDurationService.class);
        assert courseViewDurationService != null;
        return courseViewDurationService.reportLearnTime(learnTimeDTOList);
    }
}
