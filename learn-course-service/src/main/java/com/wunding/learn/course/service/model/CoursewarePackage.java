package com.wunding.learn.course.service.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * </p> WE微课件上传表
 *
 * <AUTHOR> href="mailto:<EMAIL>">余学益</a>
 * @date 2022-05-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("courseware_package")
@Schema(name = "CoursewarePackage对象", description = "WE微课件上传表")
public class CoursewarePackage implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键id
     */
    @Schema(description = "主键id")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;


    /**
     * 课件id
     */
    @Schema(description = "课件id")
    @TableField("courseware_id")
    private String coursewareId;


    /**
     * 课件名称
     */
    @Schema(description = "课件名称")
    @TableField("cw_name")
    private String cwName;


    /**
     * 课件来源
     */
    @Schema(description = "课件来源")
    @TableField("source")
    private String source;


    /**
     * 课件类型
     */
    @Schema(description = "课件类型")
    @TableField("cw_type")
    private String cwType;


    /**
     * mime类型
     */
    @Schema(description = "mime类型")
    @TableField("mime")
    private String mime;


    /**
     *
     */
    @Schema(description = "")
    @TableField("version")
    private String version;


    /**
     * 是否审核
     */
    @Schema(description = "是否审核")
    @TableField("is_audit")
    private Integer isAudit;


    /**
     * 审核时间
     */
    @Schema(description = "审核时间")
    @TableField("audit_time")
    private Date auditTime;


    /**
     * 审核结果
     */
    @Schema(description = "审核结果")
    @TableField("audit_result")
    private String auditResult;


    /**
     * 是否可用
     */
    @Schema(description = "是否可用")
    @TableField("is_available")
    private Integer isAvailable;


    /**
     * 组织id
     */
    @Schema(description = "组织id")
    @TableField("org_id")
    private String orgId;


    /**
     * 企业id
     */
    @Schema(description = "企业id")
    @TableField("customer_id")
    private String customerId;


    /**
     * 下个审核人
     */
    @Schema(description = "下个审核人")
    @TableField("next_auditor")
    private String nextAuditor;


    /**
     * 拒绝原因
     */
    @Schema(description = "拒绝原因")
    @TableField("refuse_reason")
    private String refuseReason;


    /**
     * 来自哪里   0是客户端   1是h5端
     */
    @Schema(description = "来自哪里   0是客户端   1是h5端")
    @TableField("come_type")
    private Integer comeType;


    /**
     * 此字段暂未使用
     */
    @Schema(description = "此字段暂未使用")
    @TableField("remark")
    private String remark;


    /**
     * 文件时长
     */
    @Schema(description = "文件时长")
    @TableField("play_time")
    private String playTime;


    /**
     * 视频转换状态  1 转换中  2 转换完成 3 转换失败
     */
    @Schema(description = "视频转换状态  1 转换中  2 转换完成 3 转换失败")
    @TableField("transform_status")
    private Integer transformStatus;


    /**
     * 旧mime类型
     */
    @Schema(description = "旧mime类型")
    @TableField("old_mime")
    private String oldMime;


    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;


    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 更新人
     */
    @Schema(description = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;


    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    @TableField("is_del")
    @TableLogic(value = "0", delval = "1")
    private Integer isDel;


    /**
     * 课件审核说明
     */
    @Schema(description = "课件审核说明")
    @TableField("descriptions")
    private String descriptions;




}
