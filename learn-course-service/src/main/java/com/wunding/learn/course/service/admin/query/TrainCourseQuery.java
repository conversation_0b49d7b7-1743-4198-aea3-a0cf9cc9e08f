package com.wunding.learn.course.service.admin.query;

import com.wunding.learn.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.Set;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.format.annotation.DateTimeFormat.ISO;

/**
 * <AUTHOR> href="mailto:<EMAIL>">yuxueyi</a>
 * @version V7.0.0
 * @since 2022/5/10  13:40
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TrainCourseQuery extends BaseEntity {

    @Parameter(description = "是否发布")
    private Integer isPublish;

    @Parameter(description = "是否共享")
    private Integer isShare;

    @Parameter(description = "课程来源： 0-课程管理+培训班,3-培训班")
    @NotNull(message = "课程来源不可为空")
    private String sourceType;

    @Parameter(description = "是否推荐")
    private Integer isRecommend;


    @Parameter(description = "课程分类")
    private String courseCateId;


    @Parameter(description = "开始发布时间")
    @DateTimeFormat(iso = ISO.DATE_TIME)
    private Date publishBeginTime;

    @Parameter(description = "结束发布时间")
    @DateTimeFormat(iso = ISO.DATE_TIME)
    private Date publishEndTime;

    @Parameter(description = "课程标签")
    private String courseTag;

    @Parameter(description = "课程名称")
    private String courseName;

    @Parameter(description = "课程id")
    private String courseId;

    @Parameter(description = "作者")
    private String author;

    @Parameter(description = "课程编号")
    private String courseNo;

    @Parameter(description = "创建组织")
    private String createOrgId;

    @Parameter(description = "createAndUnderOrgIds 自己及其组织以下的所有组织id", hidden = true)
    private Set<String> createAndUnderOrgIds;

    @Parameter(description = "管辖范围orgId", hidden = true)
    private Set<String> managerAreaOrgIds;

    @Schema(description = "该课程所关联的业务id(如培训班id)")
    private String relevanceId;

    @Schema(description = "目录id,多个用逗号分割", hidden = true)
    private String directoryId;

}
