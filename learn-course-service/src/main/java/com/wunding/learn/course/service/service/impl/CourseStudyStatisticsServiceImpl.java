package com.wunding.learn.course.service.service.impl;

import com.alibaba.excel.util.DateUtils;
import com.alibaba.ttl.threadpool.TtlExecutors;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import com.wunding.learn.common.constant.other.ErrorNoEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.other.SystemConfigCodeEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.mq.event.CourseStatisticsInitFinishEvent;
import com.wunding.learn.common.mq.service.MqProducer;
import com.wunding.learn.common.table.partition.component.TablePartitionComponent;
import com.wunding.learn.common.table.partition.enums.TablePartitionEnum;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.date.DateHelper;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.common.viewlimit.dto.ResourceViewLimitBakDTO;
import com.wunding.learn.common.viewlimit.service.IResourceViewLimitBakService;
import com.wunding.learn.course.service.admin.dto.CourseStudyDetailDTO;
import com.wunding.learn.course.service.admin.dto.CourseStudyStatisticsDTO;
import com.wunding.learn.course.service.admin.dto.CourseStudyStatisticsUserDTO;
import com.wunding.learn.course.service.admin.query.CourseStudyStatisticsQuery;
import com.wunding.learn.course.service.admin.query.CourseStudyStatisticsUserQuery;
import com.wunding.learn.course.service.constant.CourseStatisticsConstant;
import com.wunding.learn.course.service.mapper.CourseStudyStatisticsMapper;
import com.wunding.learn.course.service.model.CourseBak;
import com.wunding.learn.course.service.model.CourseResourceTaskExecute;
import com.wunding.learn.course.service.model.CourseStudyStatistics;
import com.wunding.learn.course.service.model.UserCourseRecord;
import com.wunding.learn.course.service.service.ICourseBakService;
import com.wunding.learn.course.service.service.ICourseStudyStatisticsService;
import com.wunding.learn.course.service.service.IResourceTaskExecuteService;
import com.wunding.learn.course.service.service.IUserCourseRecordService;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.dto.AbstractExportDataDTO;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.user.api.dto.OrgDTO;
import com.wunding.learn.user.api.dto.OrgShowDTO;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.dto.UserOrgDTO;
import com.wunding.learn.user.api.service.OrgFeign;
import com.wunding.learn.user.api.service.ParaFeign;
import com.wunding.learn.user.api.service.UserFeign;
import jakarta.annotation.Resource;
import java.net.InetAddress;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <p> 课程学习统计按部门 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">zhongchaoheng</a>
 * @since 2023-05-11
 */
@Slf4j
@Service("courseStudyStatisticsService")
public class CourseStudyStatisticsServiceImpl extends
    ServiceImpl<CourseStudyStatisticsMapper, CourseStudyStatistics> implements ICourseStudyStatisticsService {

    public static final String ORDER_BY_A_ID_ASC = " order by a.id asc ";
    public static final String ERROR = ",error:";
    @Resource
    OrgFeign orgFeign;

    @Resource
    UserFeign userFeign;

    @Resource
    ExportComponent exportComponent;

    @Resource
    IUserCourseRecordService userCourseRecordService;

    @Resource
    TablePartitionComponent tablePartitionComponent;

    @Resource
    IResourceTaskExecuteService resourceTaskExecuteService;

    @Resource
    IResourceViewLimitBakService resourceViewLimitBakService;

    @Resource
    ICourseBakService courseBakService;

    @Resource
    private MqProducer mqProducer;

    @Resource
    @SuppressWarnings("rawtypes")
    private RedisTemplate redisTemplate;

    @Resource
    ParaFeign paraFeign;

    //获取cpu核心数
    private static final int CPU_CORES = Runtime.getRuntime().availableProcessors();
    //默认最大线程数
    private static final int DEFAULT_MAXIMUM_POOL_SIZE = CPU_CORES * 2;


    private static final String CREATE_INDEX = "create index  ";

    private static final String DROP_INDEX = "drop index  ";

    private static final String W_VIEW_LIMIT_USER_BAK = "w_view_limit_user_bak_";

    private static final String COURSEWARE_USER_RECORD_BAK = "courseware_user_record_bak_";

    private static final String USER_COURSE_RECORD_BAK = "user_course_record_bak_";

    private static final String INFO_END = ">>>>>>>>>>>getRecalculateResource end ,source:{},type:{},executeDateLimit:{},size:{}";

    private static final String SYSTEM = "system";

    private static final String LEARN_STATE = "learnState";

    private static final String COURSE_STUDY_STATISTICS_SERVICE = "courseStudyStatisticsService";

    @Override
    public PageInfo<CourseStudyStatisticsDTO> getCourseStudyStatisticsPage(CourseStudyStatisticsQuery query) {
        log.info("getCourseStudyStatisticsPage param:{}", query);
        PageInfo<CourseStudyStatisticsDTO> result;
        //默认查询全部层级数据
        if (null != query.getOnlyCurrentLevel() && query.getOnlyCurrentLevel()) {
            result = selectCurrentLevelData(query);
        } else {
            result = selectAllLevelData(query);
        }
        log.info("getCourseStudyStatisticsPage result:{}", result);
        return result;
    }

    /**
     * 查询当前层级数据
     *
     * @param query
     * @return
     */
    private PageInfo<CourseStudyStatisticsDTO> selectCurrentLevelData(CourseStudyStatisticsQuery query) {
        Set<String> orgIdSet;
        if (StringUtils.hasText(query.getOrgId())) {
            //只查当前层级数据
            orgIdSet = new HashSet<>(Arrays.asList(query.getOrgId().split(",")));
        } else {
            //管辖范围
            orgIdSet = orgFeign.getUserManageAreaOrgId(UserThreadContext.getUserId());
        }
        query.setOrgIdList(orgIdSet);
        //获取课程历史备份数据
        CourseBak courseBak = courseBakService.getById(query.getCourseId());
        if (null == courseBak) {
            throw new BusinessException(ErrorNoEnum.ERR_RESOURCE_BAK_NOT_EXIST);
        }

        //设置分表存储查询位置
        String tablePartition = TablePartitionEnum
            .getTablePartitionName(TablePartitionEnum.course_study_statistics.getTableName(), courseBak.getHashIndex());
        query.setTablePartition(tablePartition);
        PageInfo<CourseStudyStatisticsDTO> result = PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> baseMapper.selectCurrentLevelData(query));
        if (CollectionUtils.isEmpty(result.getList())) {
            return result;
        }
        //处理部门简称
        Set<String> orgIds = result.getList().stream().map(CourseStudyStatisticsDTO::getOrgId)
            .collect(Collectors.toSet());
        Map<String, OrgShowDTO> orgShowDTOMap = orgFeign.getOrgShowDTO(orgIds);
        result.getList().forEach(item -> {
            OrgShowDTO orgShowDTO = orgShowDTOMap.get(item.getOrgId());
            if (null != orgShowDTO) {
                item.setOrgPath(orgShowDTO.getLevelPathName());
            }
        });
        return result;
    }

    /**
     * 查询所有层级数据
     *
     * @param query
     * @return
     */
    private PageInfo<CourseStudyStatisticsDTO> selectAllLevelData(CourseStudyStatisticsQuery query) {
        Set<String> orgIdSet;
        if (StringUtils.hasText(query.getOrgId())) {
            orgIdSet = new HashSet<>();
            //合并组织
            Set<String> orgIds = orgFeign.mergeOrgId(query.getOrgId());
            //查询该组织集合下所有的下级组织id
            for (String orgId : orgIds) {
                orgIdSet.addAll(orgFeign.getAllChildrenId(orgId));
            }
        } else {
            //管辖范围
            orgIdSet = orgFeign.getUserManageAreaOrgId(UserThreadContext.getUserId());
        }
        query.setOrgIdList(orgIdSet);
        PageInfo<CourseStudyStatisticsDTO> result = PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> baseMapper.selectAllLevelData(query));

        //处理部门简称
        Set<String> orgIds = result.getList().stream().map(CourseStudyStatisticsDTO::getOrgId)
            .collect(Collectors.toSet());
        Map<String, OrgShowDTO> orgShowDTOMap = orgFeign.getOrgShowDTO(orgIds);
        result.getList().forEach(item -> {
            OrgShowDTO orgShowDTO = orgShowDTOMap.get(item.getOrgId());
            if (null != orgShowDTO) {
                item.setOrgPath(orgShowDTO.getLevelPathName());
            }
        });
        return result;
    }


    @Override
    public void exportCourseStudyStatistics(CourseStudyStatisticsQuery query) {
        log.info("exportCourseStudyStatistics param:{}", query);
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<ICourseStudyStatisticsService, CourseStudyStatisticsDTO>(
            query) {
            @Override
            protected ICourseStudyStatisticsService getBean() {
                return SpringUtil.getBean(COURSE_STUDY_STATISTICS_SERVICE, ICourseStudyStatisticsService.class);
            }

            @Override
            protected PageInfo<CourseStudyStatisticsDTO> getPageInfo() {
                return getBean().getCourseStudyStatisticsPage((CourseStudyStatisticsQuery) queryDTO);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.CourseStudyStatistics;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.CourseStudyStatistics.getType();
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }


    @Override
    public PageInfo<CourseStudyStatisticsUserDTO> getCourseStudyStatisticsUserList(
        CourseStudyStatisticsUserQuery query) {
        log.info("getCourseStudyStatisticsUserList param:{}", query);
        Set<String> manageOrgLevelPaths = null;
        manageOrgLevelPaths = getManageOrgLevelPaths(query, manageOrgLevelPaths);

        query.setManageOrgLevelPaths(manageOrgLevelPaths);

        //获取可见范围用户分区
        ResourceViewLimitBakDTO resourceViewLimitBak = Optional.ofNullable(resourceViewLimitBakService
                .getLimitUserPartition(query.getCourseId()))
            .orElseThrow(() -> new BusinessException(ErrorNoEnum.ERR_VIEW_LIMIT_USER_BAK_NOT_EXIST));
        String viewLimitUserPartition = TablePartitionEnum
            .getTablePartitionName(TablePartitionEnum.w_view_limit_user_bak.getTableName(),
                resourceViewLimitBak.getHashIndex());
        query.setViewLimitUserPartition(viewLimitUserPartition);
        query.setViewLimitId(resourceViewLimitBak.getViewLimitId());

        //获取课程学习记录备份分区
        CourseBak courseBak = Optional.ofNullable(courseBakService.getById(query.getCourseId()))
            .orElseThrow(() -> new BusinessException(ErrorNoEnum.ERR_RESOURCE_BAK_NOT_EXIST));
        String userCourseRecordPartition = TablePartitionEnum
            .getTablePartitionName(TablePartitionEnum.user_course_record_bak.getTableName(), courseBak.getHashIndex());
        query.setUserCourseRecordPartition(userCourseRecordPartition);

        PageInfo<CourseStudyStatisticsUserDTO> pageInfo = PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> baseMapper.getCourseStudyStatisticsUserList(query));
        List<CourseStudyStatisticsUserDTO> list = pageInfo.getList();
        if (CollectionUtils.isEmpty(list)) {
            return new PageInfo<>();
        }
        List<String> userIdList = list.stream().map(CourseStudyStatisticsUserDTO::getUserId)
            .collect(Collectors.toList());
        Map<String, UserDTO> userDTOMap = userFeign.getUserNameMapByIds(userIdList);
        Map<String, List<UserOrgDTO>> userRelateOrgMap = userFeign.getUserOrgByParams(userIdList, 0);
        Set<String> orgIds = userDTOMap.values().stream().map(UserDTO::getOrgId).collect(Collectors.toSet());
        Map<String, OrgShowDTO> orgShowDTOMap = orgFeign.getOrgShowDTO(orgIds);
        list.forEach(item -> dealWithRecord(item, userDTOMap, orgShowDTOMap,
            userRelateOrgMap));
        pageInfo.setList(list);
        log.info("getCourseStudyStatisticsUserList result:{}", pageInfo);
        return pageInfo;
    }

    private static void dealWithRecord(CourseStudyStatisticsUserDTO courseStudyStatisticsUserDTO, Map<String, UserDTO> userDTOMap,
        Map<String, OrgShowDTO> orgShowDTOMap, Map<String, List<UserOrgDTO>> userRelateOrgMap) {
        //处理用户名
        UserDTO user = userDTOMap.get(courseStudyStatisticsUserDTO.getUserId());
        if (Objects.nonNull(user)) {
            courseStudyStatisticsUserDTO.setFullName(user.getFullName());
            courseStudyStatisticsUserDTO.setLoginName(user.getLoginName());
            courseStudyStatisticsUserDTO.setOrgName(user.getOrgName());
            courseStudyStatisticsUserDTO.setLevelPathName(user.getLevelPathName());
            //处理部门简称
            OrgShowDTO orgShowDTO = orgShowDTOMap.get(user.getOrgId());
            if (null != orgShowDTO) {
                courseStudyStatisticsUserDTO.setOrgPath(orgShowDTO.getLevelPathName());
            }
        }

        //处理关联部门
        List<UserOrgDTO> userOrgs = userRelateOrgMap.get(courseStudyStatisticsUserDTO.getUserId());
        if (!CollectionUtils.isEmpty(userOrgs)) {
            String relateLevelPathName;
            Set<String> relateLevelPathNames = new HashSet<>();
            for (UserOrgDTO item : userOrgs) {
                String levelPathName = StringUtil.isEmpty(item.getEmployeeNo()) ? item.getLevelPathName()
                    : item.getLevelPathName() + "(" + item.getEmployeeNo() + ")";
                relateLevelPathNames.add(levelPathName);
            }
            relateLevelPathName = String.join(",", relateLevelPathNames);
            courseStudyStatisticsUserDTO.setRelateLevelPathName(relateLevelPathName);
        }
    }

    private Set<String> getManageOrgLevelPaths(CourseStudyStatisticsUserQuery query, Set<String> manageOrgLevelPaths) {
        if (StringUtils.hasText(query.getOrgId())) {
            //默认查询全部层级数据
            if (null != query.getOnlyCurrentLevel() && query.getOnlyCurrentLevel()) {
                //只查当前层级数据
                Set<String> orgIds = new HashSet<>(Arrays.asList(query.getOrgId().split(",")));
                query.setOrgIds(orgIds);
            } else {
                //合并多选的组织id
                Set<String> orgIds = orgFeign.mergeOrgId(query.getOrgId());
                Set<OrgDTO> manageOrgs = orgFeign.getByIds(orgIds);
                manageOrgLevelPaths = manageOrgs.stream().map(OrgDTO::getLevelPath).collect(Collectors.toSet());
            }
        } else {
            //查询用户管辖范围
            List<OrgDTO> manageOrgs = orgFeign.findUserManageArea(UserThreadContext.getUserId());
            manageOrgLevelPaths = manageOrgs.stream().map(OrgDTO::getLevelPath).collect(Collectors.toSet());
        }
        return manageOrgLevelPaths;
    }

    @Override
    public void exportCourseStudyStatisticsUser(CourseStudyStatisticsUserQuery query) {
        log.info("exportCourseStudyStatisticsUser param:{}", query);
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<ICourseStudyStatisticsService, CourseStudyStatisticsUserDTO>(
            query) {
            @Override
            protected ICourseStudyStatisticsService getBean() {
                return SpringUtil.getBean(COURSE_STUDY_STATISTICS_SERVICE, ICourseStudyStatisticsService.class);
            }

            @Override
            protected PageInfo<CourseStudyStatisticsUserDTO> getPageInfo() {
                return getBean().getCourseStudyStatisticsUserList((CourseStudyStatisticsUserQuery) queryDTO);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.CourseStudyStatisticsUser;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.CourseStudyStatisticsUser.getType();
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public PageInfo<CourseStudyDetailDTO> courseStudyStatisticsDetail(CourseStudyStatisticsQuery query) {
        //用户id
        if (StringUtils.hasText(query.getUserIds())) {
            query.setUserIdsVo(Arrays.asList(query.getUserIds().split(",")));
        }
        Set<String> orgIdSet = setOrgIdSet(query);
        query.setUnderOrgIds(orgIdSet);

        //获取可见范围用户备份分区
        ResourceViewLimitBakDTO resourceViewLimitBak = resourceViewLimitBakService
            .getLimitUserPartition(query.getCourseId());
        if (null == resourceViewLimitBak) {
            throw new BusinessException(ErrorNoEnum.ERR_VIEW_LIMIT_USER_BAK_NOT_EXIST);
        }
        String viewLimitUserPartition = TablePartitionEnum
            .getTablePartitionName(TablePartitionEnum.w_view_limit_user_bak.getTableName(),
                resourceViewLimitBak.getHashIndex());
        query.setViewLimitUserPartition(viewLimitUserPartition);
        query.setViewLimitId(resourceViewLimitBak.getViewLimitId());

        CourseBak courseBak = courseBakService.getById(query.getCourseId());
        if (null == courseBak) {
            throw new BusinessException(ErrorNoEnum.ERR_RESOURCE_BAK_NOT_EXIST);
        }
        //获取课件学习记录备份分区
        String coursewareUserRecordPartition = TablePartitionEnum
            .getTablePartitionName(TablePartitionEnum.courseware_user_record_bak.getTableName(),
                courseBak.getHashIndex());
        query.setCoursewareUserRecordPartition(coursewareUserRecordPartition);
        //获取课程学习记录备份分区
        String userCourseRecordPartition = TablePartitionEnum
            .getTablePartitionName(TablePartitionEnum.user_course_record_bak.getTableName(), courseBak.getHashIndex());
        query.setUserCourseRecordPartition(userCourseRecordPartition);

        PageInfo<CourseStudyDetailDTO> pageInfo = PageMethod.startPage(query.getPageNo(),
                query.getPageSize())
            .doSelectPageInfo(() -> userCourseRecordService.courseStudyStatisticsDetail(query));
        List<String> userIds = pageInfo.getList().stream().map(CourseStudyDetailDTO::getUserId)
            .collect(Collectors.toList());
        Map<String, UserOrgDTO> userMap = orgFeign.getUserAndOrgMapByUserIds(userIds);
        Map<String, List<UserOrgDTO>> userRelateOrgMap = userFeign.getUserOrgByParams(userIds, 0);
        Set<String> orgIds = pageInfo.getList().stream().map(CourseStudyDetailDTO::getOrgId)
            .collect(Collectors.toSet());
        Map<String, OrgShowDTO> orgShowDTOMap = orgFeign.getOrgShowDTO(orgIds);

        pageInfo.getList().forEach(csd -> {
            handCourseStudyStatisticsDetail(csd,userMap,userRelateOrgMap,orgShowDTOMap);
        });
        return pageInfo;
    }

    /**
     * 处理查询结果
     * @param csd 处理数据
     * @param userMap 用户集合
     * @param userRelateOrgMap 用户关联部门集合
     * @param orgShowDTOMap 部门简称集合
     */
    private void handCourseStudyStatisticsDetail(CourseStudyDetailDTO csd, Map<String, UserOrgDTO> userMap,Map<String
        , List<UserOrgDTO>> userRelateOrgMap, Map<String, OrgShowDTO> orgShowDTOMap){
        //未学课件数
        if (csd.getCourseWareCount() != null && csd.getLearnedCourseWareCount() != null) {
            csd.setRemainderCourseWareCount(csd.getCourseWareCount() - csd.getLearnedCourseWareCount());
        }
        UserOrgDTO userOrgDTO = userMap.get(csd.getUserId());
        if (null != userOrgDTO) {
            csd.setLoginName(userOrgDTO.getLoginName());
            csd.setFullName(userOrgDTO.getFullName());
            csd.setOrgName(userOrgDTO.getOrgName());
            csd.setOrgLevelPathName(userOrgDTO.getLevelPathName());
            csd.setJobName(userOrgDTO.getPostName());
        }
        //处理部门简称
        OrgShowDTO orgShowDTO = orgShowDTOMap.get(csd.getOrgId());
        if (null != orgShowDTO) {
            csd.setOrgPath(orgShowDTO.getLevelPathName());
        }
        //处理关联部门
        List<UserOrgDTO> userOrgs = userRelateOrgMap.get(csd.getUserId());
        if (!CollectionUtils.isEmpty(userOrgs)) {
            String relateLevelPathName;
            Set<String> relateLevelPathNames = new HashSet<>();
            for (UserOrgDTO item : userOrgs) {
                String levelPathName = StringUtil.isEmpty(item.getEmployeeNo()) ? item.getLevelPathName()
                    : item.getLevelPathName() + "(" + item.getEmployeeNo() + ")";
                relateLevelPathNames.add(levelPathName);
            }
            relateLevelPathName = String.join(",", relateLevelPathNames);
            csd.setRelateLevelPathName(relateLevelPathName);
        }

    }

    /**
     * 设置组织查询条件
     * @param query 查询条件
     * @return
     */
    private Set<String> setOrgIdSet(CourseStudyStatisticsQuery query){
        Set<String> orgIdSet;
        if (StringUtils.hasText(query.getOrgId())) {
            //默认查询全部层级数据
            if (null != query.getOnlyCurrentLevel() && query.getOnlyCurrentLevel()) {
                //只查当前层级数据
                orgIdSet = new HashSet<>(Arrays.asList(query.getOrgId().split(",")));
            } else {
                //合并多选的组织id
                Set<String> orgIds = orgFeign.mergeOrgId(query.getOrgId());
                //查询该组织集合下所有的下级组织id
                orgIdSet = new TreeSet<>();
                for (String orgId : orgIds) {
                    orgIdSet.addAll(orgFeign.getAllChildrenId(orgId));
                }
            }
        } else {
            //查询用户管辖范围
            orgIdSet = orgFeign.getUserManageAreaOrgId(UserThreadContext.getUserId());
        }
        return orgIdSet;
    }


    @Override
    public void exportStudyStatisticsData(CourseStudyStatisticsQuery query) {
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<ICourseStudyStatisticsService, CourseStudyDetailDTO>(
            query) {

            @Override
            protected ICourseStudyStatisticsService getBean() {
                return SpringUtil.getBean(COURSE_STUDY_STATISTICS_SERVICE, ICourseStudyStatisticsService.class);
            }

            @Override
            protected PageInfo<CourseStudyDetailDTO> getPageInfo() {
                return getBean().courseStudyStatisticsDetail((CourseStudyStatisticsQuery) queryDTO);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.CourseStudyDetail;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.CourseStudyDetail.getType();
            }

            @Override
            protected void afterCreateBeanMap(Map<String, Object> map) {
                Object learnState = map.get(LEARN_STATE);
                if (Objects.equals(learnState, 1)) {
                    map.put(LEARN_STATE, "完成");
                } else {
                    map.put(LEARN_STATE, "学习中");
                }
            }

        };
        exportComponent.exportRecord(exportDataDTO);
    }


    /**
     * 课程统计-按部门定时任务执行体
     *
     * @param batchId
     * @return
     */
    @Override
    public void courseStudyStatisticsExecute(String batchId) {
        log.info(">>>>>>>>>>>courseStudyStatisticsExecute start batchId:" + batchId);
        long start = System.currentTimeMillis();
        log.info(">>>>>>>>>>>courseStudyStatisticsExecute create executeThreadPool start");
        String corePoolSizeParam = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_901.getCode());
        int corePoolSize = StringUtils.hasText(corePoolSizeParam) ? Integer.valueOf(corePoolSizeParam) : CPU_CORES;
        String maximumPoolSizeParam = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_902.getCode());
        int maximumPoolSize = StringUtils.hasText(maximumPoolSizeParam) ? Integer.valueOf(maximumPoolSizeParam)
            : DEFAULT_MAXIMUM_POOL_SIZE;
        String executeThreadPoolName = "execute-courseStudyStatisticsJob-pool-" + batchId + "-";
        //创建线程池
        ExecutorService executeThreadPool = TtlExecutors
            .getTtlExecutorService(
                new ThreadPoolExecutor(
                    corePoolSize,
                    maximumPoolSize,
                    0L,
                    TimeUnit.MILLISECONDS,
                    new LinkedBlockingQueue<>(),
                    new CustomizableThreadFactory(executeThreadPoolName)
                )
            );
        log.info("<<<<<<<<<<<courseStudyStatisticsExecute create executeThreadPool end");
        try {
            //redis数据存储key
            final String DATA_KEY = CourseStatisticsConstant.COURSE_STUDY_STATISTICS_EXECUTE_KEY + ":" + batchId;
            Set<String> keys = redisTemplate.opsForHash().keys(DATA_KEY);
            List<CourseResourceTaskExecute> redisData = redisTemplate.opsForHash().multiGet(DATA_KEY, keys);
            //执行次数
            int count = 1;
            while (!CollectionUtils.isEmpty(redisData)) {

                for (CourseResourceTaskExecute courseResourceTaskExecute : redisData) {
                    SaveDataThread saveDataThread = new SaveDataThread(DATA_KEY, courseResourceTaskExecute);
                    executeThreadPool.execute(
                        saveDataThread
                    );
                }
                log.info(">>>>>>>>>>>courseStudyStatisticsExecute 第" + count + "次调用线程完成,batchId:" + batchId);
                count++;
                //更新剩余待执行任务列表
                boolean isFinish = updateTaskProgress(redisData,DATA_KEY,keys,batchId);
                if (isFinish) {
                    break;
                }
                //等待五分钟后再次执行
                log.info(
                    ">>>>>>>>>>>courseStudyStatisticsExecute 等待第" + count + "次调用线程,batchId:" + batchId
                        + ",剩余资源数:"
                        + redisData.size());
                Thread.sleep(5 * 60 * 1000L);
            }
        } catch (Exception e) {
            Thread.currentThread().interrupt();
            log.error("<<<<<<<<<<<courseStudyStatisticsExecute end error:{}", e);
        } finally {
            //阻塞等待线程执行完，销毁线程池,线程池的任务执行完才会进行销毁
            List<Runnable> droppedTasks = executeThreadPool.shutdownNow();
            boolean shutdownSuccess = executeThreadPool.isTerminated();
            if (!shutdownSuccess) {
                try {
                    log.info(
                        ">>>>>>>>>>>courseStudyStatisticsExecute 执行awaitTermination,等待线程执行完成,销毁线程池 ");
                    executeThreadPool.awaitTermination(3, TimeUnit.SECONDS);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.error(">>>>>>>>>>>courseStudyStatisticsExecute 执行awaitTermination error:", e);
                }
            }
            if (executeThreadPool.isTerminated()) {
                log.info(">>>>>>>>>>>courseStudyStatisticsExecute 线程池已关闭，所有线程已终止");
            } else {
                log.info(">>>>>>>>>>>courseStudyStatisticsExecute 线程池关闭，但所有线程未全部终止，droppedTasks:{}",
                    droppedTasks);
            }
        }

        log.info(
            "<<<<<<<<<<<courseStudyStatisticsExecute end batchId:" + batchId + ", 耗时:" + (System.currentTimeMillis()
                - start));
    }

    /**
     * 更新剩余待执行任务列表
     * @param redisData
     * @param DATA_KEY
     * @param keys
     * @param batchId
     * @return
     */
    private boolean updateTaskProgress( List<CourseResourceTaskExecute> redisData,String DATA_KEY,Set<String> keys,
        String batchId){
        boolean isFinish = false;
        redisData = redisTemplate.opsForHash().multiGet(DATA_KEY, keys);
        Long expireTime = Optional.ofNullable(redisTemplate.opsForHash().getOperations().getExpire(DATA_KEY))
            .orElse(-1L);
        if (CollectionUtils.isEmpty(redisData) || 0 > expireTime) {
            log.info("<<<<<<<<<<<courseStudyStatisticsExecute redis中没有待执行资源任务,batchId:" + batchId);
            isFinish = true;
        }
        redisData = redisData.stream().filter(item -> {
            return Objects.equals(1, item.getExecuteStatus()) || Objects.equals(0, item.getExecuteStatus()) || (
                Objects.equals(-1, item.getExecuteStatus()) && item.getExecuteTimes() < item
                    .getMaxExecuteTimes());
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(redisData)) {
            log.info("<<<<<<<<<<<courseStudyStatisticsExecute 没有待执行资源任务,batchId:" + batchId);
            isFinish = true;
        }
        return isFinish;
    }

    @Override
    public void courseStudyStatisticsCollect(String batchId, String resourceId) {
        long start = System.currentTimeMillis();
        log.info(">>>>>>>>>>>courseStudyStatisticsCollect start");
        CourseResourceTaskExecute updateCollectStatusData = new CourseResourceTaskExecute();
        String hostName = null;
        try {
            hostName = InetAddress.getLocalHost().getHostName();
            //汇总数据锁key
            final String dataKey = CourseStatisticsConstant.COURSE_STUDY_STATISTICS_EXECUTE_KEY + ":" + batchId;
            //获取该批次可以执行的资源
            CourseResourceTaskExecute resourceTask = resourceTaskExecuteService
                .getCollectResource(dataKey, batchId, resourceId);
            if (null == resourceTask) {
                return;
            }
            log.info(
                "<<<<<<<<<<<courseStudyStatisticsCollect 执行getCollectResource结束,抢到可执行资源,batchId:{},resourceId:{}",
                batchId, resourceId);

            List<OrgDTO> orgIdList = new ArrayList<>();
            //部门级组织
            List<OrgDTO> orgIdList1 = orgFeign.getOrgByDimension(1);
            //公司级组织
            List<OrgDTO> orgIdList2 = orgFeign.getOrgByDimension(2);
            orgIdList.addAll(orgIdList1);
            orgIdList.addAll(orgIdList2);
            long collectStart = System.currentTimeMillis();

            List<CourseStudyStatistics> saveList = new ArrayList<>();
            //获取课程备份数据
            CourseBak courseBak = courseBakService.getById(resourceId);
            if (null == courseBak) {
                throw new BusinessException(ErrorNoEnum.ERR_RESOURCE_BAK_NOT_EXIST);
            }
            String tablePartition = TablePartitionEnum
                .getTablePartitionName(TablePartitionEnum.course_study_statistics.getTableName(),
                    courseBak.getHashIndex());

            long selectStart = System.currentTimeMillis();
            log.info(
                ">>>>>>>>>>>courseStudyStatisticsCollect batchId:{},resourceId:{},tablePartition:{},汇总数据查询开始",
                batchId, resourceId,
                tablePartition);
            for (OrgDTO orgDTO : orgIdList) {
                String orgId = orgDTO.getId();
                //获取课程的学习数据-汇总
                CourseStudyStatistics courseLearnData = baseMapper
                    .getCourseLearnDataCollect(resourceId, orgDTO.getLevelPath(), tablePartition);
                courseLearnData.setId(resourceId + "/" + orgId);
                courseLearnData.setResourceId(resourceId);
                courseLearnData.setOrgId(orgId);
                courseLearnData.setLevelPath(orgDTO.getLevelPath());
                courseLearnData.setCreateBy(SYSTEM);
                courseLearnData.setUpdateBy(SYSTEM);
                saveList.add(courseLearnData);
            }
            log.info(
                "<<<<<<<<<<<courseStudyStatisticsCollect batchId:{},resourceId:{},tablePartition:{},汇总数据查询结束,耗时:{}",
                batchId, resourceId, tablePartition, (System.currentTimeMillis() - selectStart));

            //删除历史数据
            long deleteStart = System.currentTimeMillis();
            log.info(
                ">>>>>>>>>>>courseStudyStatisticsCollect batchId:{},resourceId:{},tablePartition:{},删除历史数据开始",
                batchId, resourceId,
                tablePartition);
            List<String> deleteIds = baseMapper.getCourseLearnDataCollectIds(resourceId);
            List<List<String>> deleteBatch = Lists.partition(deleteIds, 500);
            for (List<String> list : deleteBatch) {
                baseMapper.deleteCourseLearnDataCollect(list);
            }
            log.info(
                "<<<<<<<<<<<courseStudyStatisticsCollect batchId:{},resourceId:{},tablePartition:{},删除历史数据结束,耗时:{}",
                batchId, resourceId, tablePartition, (System.currentTimeMillis() - deleteStart));

            //新增新的统计数据
            long saveStartTime = System.currentTimeMillis();
            List<List<CourseStudyStatistics>> saveBatchList = Lists.partition(saveList, 1000);
            log.info(
                ">>>>>>>>>>>courseStudyStatisticsCollect batchId:{},resourceId:{},tablePartition:{},保存数据开始,共{}批数据,数据量为{}",
                batchId, resourceId, tablePartition, saveBatchList.size(), saveList.size());
            int count = 1;
            for (List<CourseStudyStatistics> list : saveBatchList) {
                long saveStart = System.currentTimeMillis();
                log.info(
                    ">>>>>>>>>>>courseStudyStatisticsCollect batchId:{},resourceId:{},tablePartition:{},保存第{}批数据开始",
                    batchId, resourceId, tablePartition, count);
                baseMapper.insertCourseLearnDataCollect(list);
                log.info(
                    "<<<<<<<<<<<courseStudyStatisticsCollect batchId:{},resourceId:{},tablePartition:{},保存第{}批数据结束,耗时:{}",
                    batchId, resourceId, tablePartition, count, (System.currentTimeMillis() - saveStart));
                count++;
            }
            log.info(
                "<<<<<<<<<<<courseStudyStatisticsCollect batchId:{},resourceId:{},tablePartition:{},保存数据结束,共{}批数据,数据量为{},耗时:{}",
                batchId, resourceId, tablePartition, saveBatchList.size(), saveList.size(),
                (System.currentTimeMillis() - saveStartTime));

            //更新任务列表中的汇总状态
            long updateStatusStart = System.currentTimeMillis();
            log.info(
                ">>>>>>>>>>>courseStudyStatisticsCollect batchId:{},resourceId:{},tablePartition:{},更新任务列表中的汇总状态开始",
                batchId, resourceId,
                tablePartition);

            updateCollectStatusData.setCollectStatus(2);
            updateCollectStatusData.setUpdateBy(hostName + "-collect-finish");
            updateCollectStatusData.setCollectTime(new Date());
            resourceTaskExecuteService.update(updateCollectStatusData, new LambdaQueryWrapper<CourseResourceTaskExecute>()
                .eq(CourseResourceTaskExecute::getBatchId, batchId)
                .eq(CourseResourceTaskExecute::getResourceId, resourceId));
            log.info(
                "<<<<<<<<<<<courseStudyStatisticsCollect batchId:{},resourceId:{},tablePartition:{},更新任务列表中的汇总状态结束,耗时:{}",
                batchId, resourceId, tablePartition, (System.currentTimeMillis() - updateStatusStart));

            log.info(
                "<<<<<<<<<<<courseStudyStatisticsCollect batchId:{},resourceId:{},tablePartition:{},汇总完成,耗时:{}",
                batchId, resourceId, tablePartition, (System.currentTimeMillis() - collectStart));
        } catch (Exception e) {
            //更新任务列表中的汇总状态为失败
            String collectLog =
                "hostName:" + hostName + ",logTime:" + DateUtils.format(new Date()) + ERROR + e.toString();
            updateCollectStatusData.setCollectStatus(-1);
            updateCollectStatusData.setCollectTime(new Date());
            updateCollectStatusData.setUpdateBy(hostName + "-collect-fail");
            updateCollectStatusData.setCollectLog(collectLog);
            resourceTaskExecuteService.update(updateCollectStatusData, new LambdaQueryWrapper<CourseResourceTaskExecute>()
                .eq(CourseResourceTaskExecute::getBatchId, batchId)
                .eq(CourseResourceTaskExecute::getResourceId, resourceId));
            log.error(
                "<<<<<<<<<<<courseStudyStatisticsCollect batchId:{},resourceId:{},汇总失败,error:{}",
                batchId, resourceId, e.toString());
        }
        log.info("<<<<<<<<<<<courseStudyStatisticsCollect end 耗时:{}", (System.currentTimeMillis() - start));
    }

    @Override
    public String getLastCollectTime(String resourceId) {
        String lastCollectTime = resourceTaskExecuteService.getLastCollectTime(resourceId, 0);
        return lastCollectTime;
    }


    /**
     * 初始化课程资源定时任务执行列表
     *
     * @param
     * @return
     */
    @Override
    public void initResourceTaskExecuteData(Set<String> resourceIds, String type) {
        String batchId = DateHelper.formatDate(new Date(), DateHelper.YYYYMMDDHHMMSS);
        long start = System.currentTimeMillis();
        log.info(">>>>>>>>>>>initResourceTaskExecuteData start,批次id:{}", batchId);
        List<CourseResourceTaskExecute> courseResourceTaskExecuteList = new ArrayList<>();
        String hostName = null;
        try {
            hostName = InetAddress.getLocalHost().getHostName();
            if (CollectionUtils.isEmpty(resourceIds)) {
                resourceIds = getRecalculateResource("CourseBak", type);
            }
            if (CollectionUtils.isEmpty(resourceIds)) {
                log.info(
                    "<<<<<<<<<<<initResourceTaskExecuteData 初始化课程资源定时任务执行列表，没有待初始化的资源数据");
                return;
            }

            final String DATA_KEY = CourseStatisticsConstant.COURSE_STUDY_STATISTICS_EXECUTE_KEY + ":" + batchId;

            long deleteStartTime = System.currentTimeMillis();
            log.info(">>>>>>>>>>>initResourceTaskExecuteData 删除历史统计数据开始,批次id:{},资源数量:{}", batchId,
                resourceIds.size());
            List<CourseBak> courseBakList = courseBakService.listByIds(resourceIds);
            Map<String, CourseBak> courseBakMap = courseBakList.stream()
                .collect(Collectors.toMap(CourseBak::getId, item -> item, (key1, key2) -> key1));
            for (String resourceId : resourceIds) {
                CourseResourceTaskExecute courseResourceTaskExecute = new CourseResourceTaskExecute();
                courseResourceTaskExecute.setId(StringUtil.newId());
                courseResourceTaskExecute.setType(CourseStatisticsConstant.COURSE_STATISTICS_DEPT_JOB);
                courseResourceTaskExecute.setBatchId(batchId);
                courseResourceTaskExecute.setResourceId(resourceId);
                courseResourceTaskExecute.setExecuteStatus(0);
                courseResourceTaskExecute.setExecuteTimes(0);
                courseResourceTaskExecute.setMaxExecuteTimes(2);
                courseResourceTaskExecute.setCreateBy(hostName + "-init");
                courseResourceTaskExecute.setUpdateBy(hostName + "-init");
                courseResourceTaskExecute.setCollectStatus(0);
                courseResourceTaskExecuteList.add(courseResourceTaskExecute);

                //删除历史课程统计数据
                CourseBak resourceBak = courseBakMap.get(resourceId);
                if (null != resourceBak) {
                    String tablePartition = TablePartitionEnum
                        .getTablePartitionName(TablePartitionEnum.course_study_statistics.getTableName(),
                            resourceBak.getHashIndex());
                    tablePartitionComponent.deletePartitionData(resourceId, tablePartition);
                } else {
                    log.info(
                        ">>>>>>>>>>>initResourceTaskExecuteData 删除历史统计数据失败,批次id:{},没有找到备份数据,resourceId:{}",
                        batchId, resourceId);
                }

                //装载资源任务列表进入缓存
                String lockName = batchId + ":" + courseResourceTaskExecute.getResourceId();
                redisTemplate.opsForHash().put(DATA_KEY, lockName, courseResourceTaskExecute);
            }
            log.info("<<<<<<<<<<<initResourceTaskExecuteData 删除历史统计数据结束,批次id:{}资源数量:{}，耗时:{}",
                batchId, resourceIds.size(),
                System.currentTimeMillis() - deleteStartTime);

            //设置10个小时过期
            redisTemplate.expire(DATA_KEY, 10, TimeUnit.HOURS);
            //持久化资源任务列表入数据库
            resourceTaskExecuteService.saveBatch(courseResourceTaskExecuteList);
            //发送mq给各个节点进行执行任务处理数据
            mqProducer
                .sendMsg(
                    new CourseStatisticsInitFinishEvent(batchId, CourseStatisticsConstant.COURSE_STATISTICS_DEPT_JOB));

            Set<String> keys = redisTemplate.opsForHash().keys(DATA_KEY);
            List<CourseResourceTaskExecute> redisData;
            while (true) {
                //更新剩余待执行任务列表
                redisData = redisTemplate.opsForHash().multiGet(DATA_KEY, keys);
                Long expireTime = redisTemplate.opsForHash().getOperations().getExpire(DATA_KEY);
                if (CollectionUtils.isEmpty(redisData) || 0 > expireTime) {
                    log.info("<<<<<<<<<<<initResourceTaskExecuteData redis中没有待执行资源任务,batchId:" + batchId);
                    break;
                }
                redisData = redisData.stream().filter(
                    item -> Objects.equals(1, item.getExecuteStatus()) || Objects.equals(0, item.getExecuteStatus())
                        || (
                        Objects.equals(-1, item.getExecuteStatus()) && item.getExecuteTimes() < item
                            .getMaxExecuteTimes())).collect(Collectors.toList());

                if (CollectionUtils.isEmpty(redisData)) {
                    log.info("<<<<<<<<<<<initResourceTaskExecuteData 没有待执行资源任务,batchId:" + batchId);
                    break;
                }
                //等待1分钟后再次进行检查
                log.info(">>>>>>>>>initResourceTaskExecuteData 等待统计任务执行结束,剩余资源数:" + redisData.size()
                    + ",batchId:"
                    + batchId);
                Thread.sleep(1 * 60 * 1000L);
            }
        } catch (Exception e) {
            Thread.currentThread().interrupt();
            log.error("<<<<<<<<<<<initResourceTaskExecuteData end error:{}", e);
        }

        log.info("<<<<<<<<<<<initResourceTaskExecuteData end,批次id:{},耗时:{}", batchId,
            System.currentTimeMillis() - start);
    }


    /**
     * 获取需要跑的数据 需要跑的条件[1,统计记录明细表没有数据的; 2,统计记录表有数据但是执行日期之后的X天还有新的学习记录; ]
     *
     * @param source 数据源[CourseBak/Course]
     * @param type   全量/增量[ALL/PART]
     * @return
     */
    private Set<String> getRecalculateResource(String source, String type) {
        Set<String> result = new HashSet<>();
        Date nowDate = new Date();
        String executeDateLimitParam = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_905.getCode());
        int executeDateLimit = StringUtils.hasText(executeDateLimitParam) ? Integer.valueOf(executeDateLimitParam) : 3;
        log.info(">>>>>>>>>>>getRecalculateResource start ,source:{},type:{},executeDateLimit:{}", source, type,
            executeDateLimit);
        //从源表拿数据，用于做备份;从备份表拿数据，用于做统计
        List<CourseBak> courseBakList = courseBakService.getRecalculateResource(source);

        if (CollectionUtils.isEmpty(courseBakList)) {
            log.info(INFO_END, source, type, executeDateLimit, result.size());
            return result;
        }
        //全量数据，直接返回不做条件过滤
        if ("ALL".equals(type)) {
            result = courseBakList.stream().map(CourseBak::getId).collect(Collectors.toSet());
            log.info(INFO_END, source, type, executeDateLimit, result.size());
            return result;
        }

        dealWithCourseBak(courseBakList, result, executeDateLimit, nowDate);
        log.info(INFO_END, source, type, executeDateLimit, result.size());
        return result;
    }

    private void dealWithCourseBak(List<CourseBak> courseBakList, Set<String> result, int executeDateLimit,
        Date nowDate) {
        //下面是增量统计/备份的判断条件
        Map<Integer, List<CourseBak>> hashGroupMap = courseBakList.stream()
            .collect(Collectors.groupingBy(CourseBak::getHashIndex));
        for (Entry<Integer, List<CourseBak>> entry : hashGroupMap.entrySet()) {
            List<CourseBak> groupList = entry.getValue();
            if (CollectionUtils.isEmpty(groupList)) {
                continue;
            }
            Set<String> courseIds = groupList.stream().map(CourseBak::getId).collect(Collectors.toSet());
            //统计分表存储位置
            String tablePartition = TablePartitionEnum
                .getTablePartitionName(TablePartitionEnum.course_study_statistics.getTableName(), entry.getKey());
            //上一次统计记录数据
            List<CourseStudyStatistics> lastOneList = baseMapper.getJobExecuteLastOne(courseIds, tablePartition);
            Map<String, CourseStudyStatistics> lastOneMap = lastOneList.stream()
                .collect(Collectors.toMap(CourseStudyStatistics::getResourceId, item -> item, (key1, key2) -> key1));

            Set<String> exitsStatistics = new HashSet<>();
            dealWithStatistics(result, courseIds, lastOneMap, exitsStatistics);

            //2,统计记录明细表有数据的,但是执行日期之后的X天还有新的学习记录,需要统计
            List<UserCourseRecord> records = baseMapper.getLearnRecord(exitsStatistics);
            Map<String, UserCourseRecord> recordMap = records.stream()
                .collect(Collectors.toMap(UserCourseRecord::getCourseId, item -> item, (key1, key2) -> key1));
            for (Entry<String, UserCourseRecord> item : recordMap.entrySet()) {
                UserCourseRecord userCourseRecord = item.getValue();
                if (userCourseRecord == null) {
                    continue;
                }
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(userCourseRecord.getUpdateTime());
                calendar.add(Calendar.DAY_OF_MONTH, executeDateLimit);
                Date executeDate = calendar.getTime();
                if (nowDate.compareTo(executeDate) < 0) {
                    result.add(item.getKey());
                }
            }
        }
    }

    private static void dealWithStatistics(Set<String> result, Set<String> courseIds,
        Map<String, CourseStudyStatistics> lastOneMap, Set<String> exitsStatistics) {
        for (String item : courseIds) {
            CourseStudyStatistics courseStudyStatistics = lastOneMap.get(item);
            if (null == courseStudyStatistics) {
                //1,统计记录明细表没有数据的;
                result.add(item);
            } else {
                //2,统计记录明细表有数据的,进行校验;
                exitsStatistics.add(item);
            }
        }
    }


    /**
     * 保存数据线程
     *
     * @return
     */
    public class SaveDataThread implements Runnable {

        private String dataKey;

        private CourseResourceTaskExecute courseResourceTaskExecute;

        public SaveDataThread(String dataKey, CourseResourceTaskExecute courseResourceTaskExecute) {
            this.dataKey = dataKey;
            this.courseResourceTaskExecute = courseResourceTaskExecute;
        }


        @Override
        public void run() {
            long startTime = System.currentTimeMillis();
            String resourceId = courseResourceTaskExecute.getResourceId();
            //保存数据线程执行
            //获取该批次可以执行的资源
            CourseResourceTaskExecute resourceTask = resourceTaskExecuteService.getResource(dataKey,
                courseResourceTaskExecute);
            if (null == resourceTask) {
                //log.info("<<<<<<<<<<<courseStudyStatisticsExecute 执行getResource结束,没有抢到可执行资源,resourceId:{}", resourceId);
                return;
            }
            log.info("<<<<<<<<<<<courseStudyStatisticsExecute 执行getResource结束,抢到可执行资源,resourceId:{}",
                resourceId);
            //处理并保存统计数据
            log.info(">>>>>>>>>>>courseStudyStatisticsExecute 执行saveStatisticsData开始,resourceId:{}",
                resourceId);
            saveStatisticsData(dataKey, resourceTask);
            log.info("<<<<<<<<<<<courseStudyStatisticsExecute 执行saveStatisticsData结束,resourceId:{},耗时:{}",
                resourceId,
                System.currentTimeMillis() - startTime);
        }
    }


    /**
     * 处理并保存统计数据
     *
     * @param courseResourceTaskExecute
     * @return
     */
    private void saveStatisticsData(String dataKey, CourseResourceTaskExecute courseResourceTaskExecute) {
        long startTime = System.currentTimeMillis();
        String batchId = courseResourceTaskExecute.getBatchId();
        String resourceId = courseResourceTaskExecute.getResourceId();
        String lockName = courseResourceTaskExecute.getBatchId() + ":" + courseResourceTaskExecute.getResourceId();
        log.info(">>>>>>>>>>>courseStudyStatisticsExecute 处理数据开始resourceId:{}", resourceId);
        String hostName = null;
        try {
            hostName = InetAddress.getLocalHost().getHostName();
            //获取课程的学习数据
            long selectTime = System.currentTimeMillis();
            log.info(">>>>>>>>>>>courseStudyStatisticsExecute 获取课程的学习数据开始resourceId:{}", resourceId);
            //获取资源对应的下发用户所在的分区索引
            ResourceViewLimitBakDTO resourceViewLimitBak = resourceViewLimitBakService
                .getLimitUserPartition(resourceId);
            if (null == resourceViewLimitBak) {
                throw new BusinessException(ErrorNoEnum.ERR_VIEW_LIMIT_USER_BAK_NOT_EXIST);
            }
            String limitUserTablePartition = TablePartitionEnum
                .getTablePartitionName(TablePartitionEnum.w_view_limit_user_bak.getTableName(),
                    resourceViewLimitBak.getHashIndex());
            //获取课程分区索引
            CourseBak courseBak = courseBakService.getById(resourceId);
            if (null == courseBak) {
                throw new BusinessException(ErrorNoEnum.ERR_RESOURCE_BAK_NOT_EXIST);
            }
            //获取课程用户学习记录分区索引
            String userCourseRecordPartition = TablePartitionEnum
                .getTablePartitionName(TablePartitionEnum.user_course_record_bak.getTableName(),
                    courseBak.getHashIndex());
            //课程统计分区索引
            String tablePartition = TablePartitionEnum
                .getTablePartitionName(TablePartitionEnum.course_study_statistics.getTableName(),
                    courseBak.getHashIndex());

            List<CourseStudyStatistics> saveList = baseMapper
                .getCourseLearnDataBak(resourceId, userCourseRecordPartition, limitUserTablePartition,
                    resourceViewLimitBak.getViewLimitId());

            for (CourseStudyStatistics courseLearnData : saveList) {
                String orgId = courseLearnData.getOrgId();
                courseLearnData.setId(resourceId + "/" + orgId);
                courseLearnData.setResourceId(resourceId);
                courseLearnData.setCreateBy(SYSTEM);
                courseLearnData.setUpdateBy(SYSTEM);
            }
            log.info("<<<<<<<<<<<courseStudyStatisticsExecute 获取课程的学习数据结束resourceId:{}，耗时:{}", resourceId,
                System.currentTimeMillis() - selectTime);

            //分表存储统计数据
            long saveStartTime = System.currentTimeMillis();

            List<List<CourseStudyStatistics>> saveBatchList = Lists.partition(saveList, 2000);
            log.info(
                ">>>>>>>>>>>courseStudyStatisticsExecute resourceId:{},tablePartition:{},保存数据开始,共{}批数据,数据量为{}",
                resourceId, tablePartition, saveBatchList.size(), saveList.size());
            int count = 1;
            for (List<CourseStudyStatistics> list : saveBatchList) {
                long saveStart = System.currentTimeMillis();
                log.debug(">>>>>>>>>>>courseStudyStatisticsExecute resourceId:{},tablePartition:{},保存第{}批数据开始",
                    resourceId, tablePartition, count);
                baseMapper.saveStatisticsDataHash(list, tablePartition);
                log.debug(
                    "<<<<<<<<<<<courseStudyStatisticsExecute resourceId:{},tablePartition:{},保存第{}批数据结束,耗时:{}",
                    resourceId, tablePartition, count, (System.currentTimeMillis() - saveStart));
                count++;
            }
            log.info(
                "<<<<<<<<<<<courseStudyStatisticsExecute resourceId:{},tablePartition:{},保存数据结束,共{}批数据,数据量为{},耗时:{}",
                resourceId, tablePartition, saveBatchList.size(), saveList.size(),
                (System.currentTimeMillis() - saveStartTime));

            //更新资源执行状态,更新为执行成功
            long statusTime = System.currentTimeMillis();
            log.info(">>>>>>>>>>>courseStudyStatisticsExecute 更新资源执行状态开始resourceId:{}", resourceId);
            courseResourceTaskExecute.setExecuteStatus(2);
            courseResourceTaskExecute.setUpdateTime(new Date());
            courseResourceTaskExecute.setUpdateBy(hostName + "-execute-finish");
            resourceTaskExecuteService.updateById(courseResourceTaskExecute);
            //更新缓存中的资源信息
            redisTemplate.opsForHash().put(dataKey, lockName, courseResourceTaskExecute);
            log.info("<<<<<<<<<<<courseStudyStatisticsExecute  更新资源执行状态结束resourceId:{}，耗时:{}", resourceId,
                System.currentTimeMillis() - statusTime);
            //发送mq给各个节点进行汇总处理数据
            CourseStatisticsInitFinishEvent event = new CourseStatisticsInitFinishEvent(batchId,
                CourseStatisticsConstant.COURSE_STATISTICS_DEPT_COLLECT_JOB);
            event.setResourceId(resourceId);
            mqProducer.sendMsg(event);
        } catch (Exception e) {

            //更新任务列表中的汇总状态为失败
            String executeLog =
                "hostName:" + hostName + ",logTime:" + DateUtils.format(new Date()) + ERROR + e.toString();
            courseResourceTaskExecute.setExecuteStatus(-1);
            courseResourceTaskExecute.setUpdateTime(new Date());
            courseResourceTaskExecute.setUpdateBy(hostName + "-execute-fail");
            courseResourceTaskExecute.setExecuteLog(executeLog);
            resourceTaskExecuteService.updateById(courseResourceTaskExecute);
            //更新缓存中的资源信息
            redisTemplate.opsForHash().put(dataKey, lockName, courseResourceTaskExecute);
            log.error("<<<<<<<<<<<courseStudyStatisticsExecute  处理数据异常resourceTaskExecute:{},error:{}",
                courseResourceTaskExecute, e.getMessage());
        }
        log.info(">>>>>>>>>>>courseStudyStatisticsExecute 处理数据结束resourceId:{},耗时:{}", resourceId,
            System.currentTimeMillis() - startTime);
    }


    @Override
    public void courseBackupResourceData(String type) {
        String hostName = null;
        String tenantId = UserThreadContext.getTenantId();
        //初始化执行状态,给备份数据上锁,防止并发
        final String backupStatusKey = "courseBackupStatus" + tenantId;
        Boolean isGetLock = redisTemplate.opsForValue().setIfAbsent(backupStatusKey, 0);
        if (Boolean.FALSE.equals(isGetLock)) {
        log.info("<<<<<<<<<<<courseBackupResourceData get lock fail end");
        return;
        }
      //更新备份数据为执行中
        redisTemplate.opsForValue().set(backupStatusKey, 1, 3, TimeUnit.HOURS);
      long startTime = System.currentTimeMillis();
      log.info(">>>>>>>>>>>courseBackupResourceData start");

      String corePoolSizeParam = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_901.getCode());
      int corePoolSize = StringUtils.hasText(corePoolSizeParam) ? Integer.parseInt(corePoolSizeParam) : CPU_CORES;
      String maximumPoolSizeParam = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_902.getCode());
      int maximumPoolSize = StringUtils.hasText(maximumPoolSizeParam) ? Integer.parseInt(maximumPoolSizeParam)
            : DEFAULT_MAXIMUM_POOL_SIZE;
      //创建线程池  fix:40015 线程池参数改为从系统配置中动态获取，对于耗时长的业务不占满服务器的核心数。
      ExecutorService backupDataExecuteThreadPool = TtlExecutors
            .getTtlExecutorService(
                new ThreadPoolExecutor(
                    corePoolSize,
                    maximumPoolSize,
                    0L,
                    TimeUnit.MILLISECONDS,
                    new LinkedBlockingQueue<>(),
                    new CustomizableThreadFactory("execute-courseBackupResourceData-pool-")
                )
       );

      try {

        hostName = InetAddress.getLocalHost().getHostName();
        baseMapper.cleanTransferLog();
        baseMapper.addTransferLog("hostName: " + hostName + ",courseBackupResourceData begin", new Date(), null);
        //关闭日志
        long closeStart = System.currentTimeMillis();
        log.info(">>>>>>>>>>>courseBackupResourceData close log start");
        log.info("<<<<<<<<<<<courseBackupResourceData close log end 耗时:{}",
            System.currentTimeMillis() - closeStart);

        //备份基础数据
        long baseDataStart = System.currentTimeMillis();
        log.info(">>>>>>>>>>>courseBackupResourceData backup base data start");
        this.backupBaseData();
        log.info("<<<<<<<<<<<courseBackupResourceData backup base data end 耗时:{}",
            System.currentTimeMillis() - baseDataStart);

          //全量/增量--备份课程&课件学习数据,课件学习&课程学习记录表,50个分区

          log.info(">>>>>>>>>>>courseBackupResourceData backUpAllData data start");
          Set<String> resourceIds = getRecalculateResource("Course", type);
          List<CourseBak> courseBakList = Collections.emptyList();
          if (!CollectionUtils.isEmpty(resourceIds)) {
              courseBakList = courseBakService.listByIds(resourceIds);
          }
          Map<Integer, List<CourseBak>> hashGroupMap = courseBakList.stream()
              .collect(Collectors.groupingBy(CourseBak::getHashIndex));

          //备份业务数据
          backUpResourceData(backupDataExecuteThreadPool, type, hashGroupMap);
          //备份下发用户数据
          backUpLimitUserData(backupDataExecuteThreadPool, type, courseBakList);


      } catch (Exception e) {
        log.error("<<<<<<<<<<<courseBackupResourceData error:{}", e.getMessage());
      } finally {
          //开启日志
          long openStart = System.currentTimeMillis();
          log.info(">>>>>>>>>>>courseBackupResourceData open log start");
          log.info("<<<<<<<<<<<courseBackupResourceData open log end 耗时:{}",
              System.currentTimeMillis() - openStart);
          //删除redis的备份状态key
          redisTemplate.delete(backupStatusKey);


          try {
              // 阻塞等待线程执行完，销毁线程池, 线程池的任务执行完才会进行销毁
              backupDataExecuteThreadPool.shutdown();
              if (!backupDataExecuteThreadPool.awaitTermination(30, TimeUnit.MINUTES)) {
                  // 如果超时，再调用 shutdownNow() 强制终止仍在运行的任务
                  List<Runnable> droppedTasks = backupDataExecuteThreadPool.shutdownNow();
                  log.info(">>>>>>>>>>>backupDataExecuteThreadPool 线程池关闭，但所有线程未全部终止，droppedTasks:{}",
                      droppedTasks);
              } else {
                  log.info(">>>>>>>>>>>backupDataExecuteThreadPool 线程池已关闭，所有线程已终止");
              }
          } catch (InterruptedException e) {
              // 如果当前线程被中断，也强制终止剩余任务
              backupDataExecuteThreadPool.shutdownNow();
              Thread.currentThread().interrupt();
              log.error(">>>>>>>>>>>backupDataExecuteThreadPool 执行awaitTermination error:", e);
          }


      }
      baseMapper.addTransferLog("hostName: " + hostName + ",courseBackupResourceData end", new Date(), null);
      log.info("<<<<<<<<<<<courseBackupResourceData end,耗时:" + (System.currentTimeMillis() - startTime));
    }


    /**
     * 备份业务数据
     *
     * @param executeThreadPool
     * @param type
     * @return
     */
    private void backUpResourceData(ExecutorService executeThreadPool, String type,
        Map<Integer, List<CourseBak>> hashGroupMap) {
        long recordStart = System.currentTimeMillis();
        for (int i = 0; i <= 49; i++) {
            String courseRecordDataSql;
            String courseRecordSourceTable = "user_course_record";
            String courseRecordTargetTable = USER_COURSE_RECORD_BAK + i;

            String coursewareRecordDataSql;
            String coursewareRecordSourceTable = "courseware_user_record";
            String coursewareRecordTargetTable = COURSEWARE_USER_RECORD_BAK + i;

            if ("ALL".equals(type)) {

                //清除所有课程学习数据
                cleanAllData(courseRecordSourceTable, courseRecordTargetTable);
                courseRecordDataSql =
                    "select a.*  from user_course_record a , course_bak b  where a.course_id  = b.id AND b.hash_index = "
                        + i + ORDER_BY_A_ID_ASC;

                //清除所有课件学习数据
                cleanAllData(coursewareRecordSourceTable, coursewareRecordTargetTable);
                coursewareRecordDataSql =
                    "select a.*  from courseware_user_record a , course_bak b  where a.course_id  = b.id AND b.hash_index = "
                        + i + ORDER_BY_A_ID_ASC;
            } else {
                List<CourseBak> courseBaks = hashGroupMap.get(i);
                if (CollectionUtils.isEmpty(courseBaks)) {
                    log.info(">>>>>>>>>>>courseBackupResourceData 分区：{},没有资源,无需备份", i);
                    continue;
                }
                String resourceIdsStr = courseBaks.stream().map(CourseBak::getId).collect(Collectors.toSet())
                    .stream().map(item -> "\"" + item + "\"")
                    .collect(Collectors.joining(","));

                //清除部分课程学习数据
                //数据就不用删除了
                //String cleanCourseRecordDataSql =
                //    "delete from " + courseRecordTargetTable + " a where a.course_id in(" + resourceIdsStr + ");";

                //cleanPartData(cleanCourseRecordDataSql);
                String userCourseRecordSql =
                    "    select date_format( date_sub( max(update_time),   INTERVAL 3 day) ,'%Y-%m-%d %H:%i:%S' ) updateTime from  "
                        + courseRecordTargetTable + "  x " +
                        "    where x.course_id in( " + resourceIdsStr + ")";
                String maxuserCourseRecordDate = getDate(userCourseRecordSql);
                String queryByUpdateTime = "";
                if (org.apache.commons.lang3.StringUtils.isNotBlank(maxuserCourseRecordDate)) {
                    queryByUpdateTime = " and a.update_time  >= '" + maxuserCourseRecordDate + "' ";
                }

                courseRecordDataSql =
                    "select a.*  from user_course_record a , course_bak b  where a.course_id  = b.id AND b.hash_index = "
                        + i + " and b.id in ( " + resourceIdsStr + " )  " +
                        queryByUpdateTime + ORDER_BY_A_ID_ASC;

                //清除部分课件学习数据
                //String cleanCoursewareRecordDataSql =
                //    "delete from " + coursewareRecordTargetTable + " a where a.course_id in(" + resourceIdsStr + ");";
                //cleanPartData(cleanCoursewareRecordDataSql);
                String oursewareRecordSql =
                    "    select date_format( date_sub( max(end_time),   INTERVAL 3 day) ,'%Y-%m-%d %H:%i:%S' ) updateTime from  "
                        + coursewareRecordTargetTable + "  x " +
                        "    where x.course_id in( " + resourceIdsStr + ")";
                String oursewareRecordDate = getDate(oursewareRecordSql);
                String queryByEndTime = "";
                if (org.apache.commons.lang3.StringUtils.isNotBlank(oursewareRecordDate)) {
                    queryByEndTime = " and a.end_time  >= '" + oursewareRecordDate + "' ";
                }
                coursewareRecordDataSql =
                    "select a.*  from courseware_user_record a , course_bak b  where a.course_id  = b.id AND b.hash_index = "
                        + i + " and b.id in ( " + resourceIdsStr + " ) " +
                        queryByEndTime + ORDER_BY_A_ID_ASC;

            }
            log.info(">>>>>>>>>>>courseBackupResourceData 备份课程学习数据,tenantId:{}", UserThreadContext.getTenantId());
            //备份课程学习数据
            backupRecordData(executeThreadPool, courseRecordDataSql, courseRecordSourceTable,
                courseRecordTargetTable, 1000, type);
            //备份课件学习数据
            log.info(">>>>>>>>>>>courseBackupResourceData 备份课件学习数据,tenantId:{}",
                UserThreadContext.getTenantId());
            backupRecordData(executeThreadPool, coursewareRecordDataSql, coursewareRecordSourceTable,
                coursewareRecordTargetTable,
                1000, type);

        }
        log.info("<<<<<<<<<<<courseBackupResourceData backUpAllData end 耗时:{}",
            System.currentTimeMillis() - recordStart);
    }

    private String getDate(String maxTaskProgressDateSql) {
        String date = baseMapper.getDate(maxTaskProgressDateSql);
        return date;
    }
    /**
     * 清除部分数据
     *
     * @param cleanDataSql
     * @return
     */
    private Long cleanPartData(String cleanDataSql) {
        Long num = baseMapper.cleanPartData(cleanDataSql);
        return num;
    }

    /**
     * 清除全部数据
     *
     * @param sourceTable
     * @param targetTable
     * @return
     */
    private void cleanAllData(String sourceTable, String targetTable) {
        //重新创建备份表,清除全部数据
        long startTime = System.currentTimeMillis();
        log.info(">>>>>>>>>>>backupRecordData createBackupTable start,sourceTable:{},targetTable:{}", sourceTable,
            targetTable);
        createBackupTable(sourceTable, targetTable);
        log.info("<<<<<<<<<<<backupRecordData createBackupTable end,sourceTable:{},targetTable:{},耗时:{}", sourceTable,
            targetTable, (System.currentTimeMillis() - startTime));
    }

    /**
     * 备份下发人员表
     *
     * @param executeThreadPool
     * @return
     */
    private void backUpLimitUserData(ExecutorService executeThreadPool, String type, List<CourseBak> courseBaks) {
        //备份下发人员数据表,100个分区
        long limitUserStart = System.currentTimeMillis();
        int start = 0, end = 99;
        log.info(">>>>>>>>>>>courseBackupResourceData backUpLimitUser start {} to {}  start", start, end);
        for (int i = start; i <= end; i++) {
            String limitUserSourceTable = "w_view_limit_user";
            String limitUserTargetTable = W_VIEW_LIMIT_USER_BAK + i;
            if ("ALL".equals(type)) {
                String limitUserDataSql =
                    "select a.* from w_view_limit_user a  where  exists (select 1 from  w_resource_view_limit_bak b where a.view_limit_id  = b.view_limit_id AND b.hash_index = "
                        + i + ") " + " order by a.id asc ";

                //清除所有下发数据
                cleanAllData(limitUserSourceTable, limitUserTargetTable);
                backupRecordData(executeThreadPool, limitUserDataSql, limitUserSourceTable, limitUserTargetTable, 5000,
                    type);
            } else {
                //List<CourseBak> courseBaks = hashGroupMap.get(i);
                if (CollectionUtils.isEmpty(courseBaks)) {
                    log.info(">>>>>>>>>>>courseBackupResourceData 分区：{},没有资源,无需备份", i);
                    continue;
                }
                String resourceIdsStr = courseBaks.stream().map(CourseBak::getId).collect(Collectors.toSet())
                    .stream().map(item -> "\"" + item + "\"")
                    .collect(Collectors.joining(","));
                String cleanLimitDataSql =
                    "delete from " + limitUserTargetTable
                        + " a where a.view_limit_id in ( select view_limit_id from w_resource_view_limit_bak " +
                        " where resource_id in (  " + resourceIdsStr + ") ) ;";
                cleanPartData(cleanLimitDataSql);

                String limitUserDataSql =
                    "select a.* from w_view_limit_user a  where  exists (select 1 from  w_resource_view_limit_bak b " +
                        " where a.view_limit_id  = b.view_limit_id AND b.hash_index = "
                        + i + " and b.resource_id in (" + resourceIdsStr + ")) " + " order by a.id asc ";
                backupRecordData(executeThreadPool, limitUserDataSql, limitUserSourceTable, limitUserTargetTable, 5000,
                    type);
            }
        }
        log.info("<<<<<<<<<<<courseBackupResourceData backUpLimitUser end {} to {} end 耗时:{}", start, end,
            System.currentTimeMillis() - limitUserStart);
    }


    /**
     * 备份数据
     *
     * @param sourceDataSql
     * @param sourceTable
     * @param targetTable
     * @param pageSize
     * @return
     */
    @SuppressWarnings("squid:S2438")
    private void backupRecordData(ExecutorService executeThreadPool, String sourceDataSql, String sourceTable,
        String targetTable, int pageSize, String type) {
        BackUpDataThread thread = new BackUpDataThread(sourceDataSql, sourceTable, targetTable, pageSize, type);
        executeThreadPool.submit(thread);
    }

    /**
     * 备份数据线程
     *
     * @return
     */
    public class BackUpDataThread extends Thread {

        private String sourceDataSql;

        private String sourceTable;

        private String targetTable;

        private Integer pageSize;

        private String type;


        public BackUpDataThread(String sourceDataSql, String sourceTable, String targetTable, Integer pageSize,
            String type) {
            this.sourceDataSql = sourceDataSql;
            this.sourceTable = sourceTable;
            this.targetTable = targetTable;
            this.pageSize = pageSize;
            this.type = type;

        }


        @Override
        public void run() {
            backupRecordDataExecute(sourceDataSql, sourceTable, targetTable, pageSize, type);
        }
    }

    @SuppressWarnings("java:S3776")
    private void backupRecordDataExecute(String sourceDataSql, String sourceTable, String targetTable, int pageSize,
        String type) {
        long backupRecordDataStart = System.currentTimeMillis();
        log.info(">>>>>>>>>>>backupRecordData start,sourceTable:{},targetTable:{}", sourceTable,
            targetTable);

        if ("ALL".equals(type)) {
            dealWithDropIndex(targetTable);
        }


        log.info(">>>>>>>>>>>backupRecordData getTotalCount start,sourceTable:{},targetTable:{},tenantId:{}",
            sourceTable,
            targetTable, UserThreadContext.getTenantId());
        Integer totalCount = baseMapper.getTotalCount(sourceDataSql);
        log.info("<<<<<<<<<<<backupRecordData getTotalCount end,sourceTable:{},targetTable:{},totalCount:{},耗时:{}",
            sourceTable, targetTable, totalCount, (System.currentTimeMillis() - backupRecordDataStart));
        try {

            long pages = calculatePages(totalCount, pageSize);
            log.info(">>>>>>>>>>>backupRecordData insertBackupData start, pages:{},pageSize:{}", pages, pageSize);
            baseMapper.addTransferLog("begin " + targetTable, new Date(), totalCount);
//            baseMapper.addTransferLog("begin " + sourceDataSql, new Date(), totalCount);
            Integer insertCount = 0;
            for (long pageIndex = 1L; pageIndex <= pages; pageIndex++) {
                long startIndex = (pageIndex - 1) * pageSize;
                List<Map<String, Object>> recordList = baseMapper.getBackupData(sourceDataSql, startIndex, pageSize);
                if (!CollectionUtils.isEmpty(recordList)) {
                    //设置表头
                    Set<String> tableFields = recordList.get(0).keySet();
                    //设置数据
                    LinkedList<LinkedList<Object>> rowDatas = new LinkedList<>();
                    for (Map<String, Object> item : recordList) {
                        LinkedList<Object> rowData = new LinkedList<>();
                        for (String tableField : tableFields) {
                            rowData.add(item.get(tableField));
                        }
                        rowDatas.add(rowData);
                    }
                    insertCount = insertCount + rowDatas.size();
                    String txt = "backupRecordDataExecute rowData: " + rowDatas.size() + ", index:" + pageIndex + ","
                        + " targetTable:" + targetTable + "";
                    //if (targetTable.indexOf(COURSEWARE_USER_RECORD_BAK) >= 0
                    //    || targetTable.indexOf(USER_COURSE_RECORD_BAK) >= 0) {
                    baseMapper.addTransferLog(txt, new Date(), insertCount);
                    //}
                    log.info("backupRecordDataExecute rowData:{},table:{},index:{}", rowDatas.size(), targetTable,
                        pageIndex);
                    if (targetTable.indexOf(COURSEWARE_USER_RECORD_BAK) >= 0
                        || targetTable.indexOf(USER_COURSE_RECORD_BAK) >= 0) {
                        baseMapper.replaceBackupData(targetTable, tableFields, rowDatas);
                    } else {
                        baseMapper.insertBackupData(targetTable, tableFields, rowDatas);
                    }

                } else {
                    String txt =
                        "backupRecordDataExecute recordList:没有查到数据, index:" + pageIndex + "," + " targetTable:"
                            + targetTable + "";
                    if (targetTable.indexOf(COURSEWARE_USER_RECORD_BAK) >= 0
                        || targetTable.indexOf(USER_COURSE_RECORD_BAK) >= 0) {
                        baseMapper.addTransferLog(txt, new Date(), insertCount);
                    }
                    log.info("backupRecordDataExecute recordList:没有查到数据 table:{},index:{}", targetTable,
                        pageIndex);
                }
            }
            baseMapper.addTransferLog("end " + targetTable, new Date(), totalCount);
            log.info("<<<<<<<<<<<backupRecordData insertBackupData end, pages:{},pageSize:{}", pages, pageSize);
        } catch (Exception e) {
            baseMapper.addTransferLog(
                "<<<<<<<<<<<backupRecordData sourceTable:" + sourceTable + ",targetTable:" + targetTable + ERROR
                    + e.getMessage() + "", new Date(), 0);
            log.error("<<<<<<<<<<<backupRecordData sourceTable:{},targetTable:{},error:{}", sourceTable, targetTable,
                e.toString());
        }
        if ("ALL".equals(type)) {
            dealWithCreateIndex(targetTable);
        }
        log.info("<<<<<<<<<<<backupRecordData end,sourceTable:{},targetTable:{},耗时:{}", sourceTable, targetTable,
            (System.currentTimeMillis() - backupRecordDataStart));

    }

    private void dealWithCreateIndex(String targetTable) {
        if (targetTable.indexOf(COURSEWARE_USER_RECORD_BAK) >= 0) {
            baseMapper.addTransferLog(CREATE_INDEX + targetTable, new Date(), 0);
            createCourseWareIndex(targetTable);
        }
        if (targetTable.indexOf(USER_COURSE_RECORD_BAK) >= 0) {
            baseMapper.addTransferLog(CREATE_INDEX + targetTable, new Date(), 0);
            createCourseIndex(targetTable);
        }
        if (targetTable.indexOf(W_VIEW_LIMIT_USER_BAK) >= 0) {
            baseMapper.addTransferLog(CREATE_INDEX + targetTable, new Date(), 0);
            createLimitIndex(targetTable);
        }
    }

    private void dealWithDropIndex(String targetTable) {
        if (targetTable.indexOf(COURSEWARE_USER_RECORD_BAK) >= 0) {
            baseMapper.addTransferLog(DROP_INDEX + targetTable, new Date(), 0);
            dropCourseWareIndex(targetTable);
        }
        if (targetTable.indexOf(USER_COURSE_RECORD_BAK) >= 0) {
            baseMapper.addTransferLog(DROP_INDEX + targetTable, new Date(), 0);
            dropCourseIndex(targetTable);
        }
        if (targetTable.indexOf(W_VIEW_LIMIT_USER_BAK) >= 0) {
            baseMapper.addTransferLog(DROP_INDEX + targetTable, new Date(), 0);
            dropLimitIndex(targetTable);
        }
    }

    private void createCourseWareIndex(String targetTable) {
        baseMapper.createCourseWareIndex(targetTable);
    }

    private void createCourseIndex(String targetTable) {
        baseMapper.createCourseIndex(targetTable);
    }

    private void dropCourseWareIndex(String targetTable) {
        baseMapper.dropCourseWareIndex(targetTable);
    }

    private void dropCourseIndex(String targetTable) {
        baseMapper.dropCourseIndex(targetTable);
    }

    private void createLimitIndex(String targetTable) {
        baseMapper.createLimitIndex(targetTable);
    }

    private void dropLimitIndex(String targetTable) {
        baseMapper.dropLimitIndex(targetTable);
    }

    /**
     * 创建备份表
     *
     * @param sourceTable
     * @param targetTable
     * @return
     */
    private void createBackupTable(String sourceTable, String targetTable) {
        baseMapper.createBackupTable(sourceTable, targetTable);
    }


    private long calculatePages(long totalCount, Integer pageSize) {
        if (totalCount == 0) {
            return 0L;
        }
        long pages = totalCount / pageSize;
        if (totalCount % pageSize != 0) {
            pages++;
        }
        return pages;
    }

    /**
     * 备份基础数据 备份组织表，备份用户表，备份考试数据,50个分区，备份下发策略表,100个分区
     */
    public void backupBaseData() {
        //备份组织表
        backUpSysOrgData();
        //备份组织表
        backUpSysUserData();
        //课程表
        backUpCourseData();

        //课件表
        backupCourseWareData();

        //备份组织表
        backUpResourceViewLimitData();
    }

    /**
     * 备份课件表
     */
    private void backupCourseWareData() {
        baseMapper.addTransferLog("courseware_bak begin", new Date(), null);
        //清空备份表数据
        baseMapper.cleanCourseWareData();
        //备份组织数据
        baseMapper.insertCourseWareData();
        baseMapper.addTransferLog("courseware_bak end", new Date(), null);
    }

    /**
     * 备份组织表
     */
    private void backUpSysOrgData() {
        baseMapper.addTransferLog("sys_org_bak begin", new Date(), null);
        //清空备份表数据
        baseMapper.cleanSysOrgData();
        //备份组织数据
        baseMapper.insertSysOrgData();
        baseMapper.addTransferLog("sys_org_bak end", new Date(), null);
    }

    /**
     * 备份用户表
     */
    private void backUpSysUserData() {
        baseMapper.addTransferLog("sys_user_bak begin", new Date(), null);
        //清空备份表数据
        baseMapper.cleanSysUserData();
        //备份组织数据
        baseMapper.insertSysUserData();
        baseMapper.addTransferLog("sys_user_bak end", new Date(), null);
    }

    /**
     * 备份考试表(帶索引的)
     */
    private void backUpCourseData() {
        baseMapper.addTransferLog("course_bak begin", new Date(), null);
        //清空备份表数据
        baseMapper.cleanCourseData();
        //备份组织数据
        baseMapper.insertCourseData();
        baseMapper.addTransferLog("course_bak end", new Date(), null);
    }

    /**
     * 备份下发人员表
     */
    private void backUpResourceViewLimitData() {
        baseMapper.addTransferLog("w_resource_view_limit_bak begin", new Date(), null);
        //清空备份表数据
        baseMapper.cleanResourceViewLimitData();
        //备份组织数据
        baseMapper.insertResourceViewLimitData();
        baseMapper.addTransferLog("w_resource_view_limit_bak end", new Date(), null);
    }
}
