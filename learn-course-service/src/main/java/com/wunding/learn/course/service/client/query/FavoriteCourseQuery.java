package com.wunding.learn.course.service.client.query;

import com.wunding.learn.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.Parameter;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 搜藏课程查询对象
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">wgr</a>
 * @date 2022/11/5 15:41
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FavoriteCourseQuery extends BaseEntity implements Serializable {

    private static final long serialVersionUID = -5915630326534896588L;

    @Parameter(hidden = true)
    private String userId;


}
