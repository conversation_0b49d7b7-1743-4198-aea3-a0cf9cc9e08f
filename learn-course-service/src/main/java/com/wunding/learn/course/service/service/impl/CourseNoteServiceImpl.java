package com.wunding.learn.course.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.wunding.learn.common.constant.course.CourseConstant;
import com.wunding.learn.common.constant.course.CourseErrorNoEnum;
import com.wunding.learn.common.constant.course.CourseNoteEnum;
import com.wunding.learn.common.constant.other.DelEnum;
import com.wunding.learn.common.constant.other.JudgeEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.ResourceTypeEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.mybatis.service.impl.BaseServiceImpl;
import com.wunding.learn.common.util.bean.BeanListUtils;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.date.DateUtil;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.common.util.string.TranslateUtil;
import com.wunding.learn.course.api.dto.CourseWareNameDetailDTO;
import com.wunding.learn.course.service.admin.dto.CourseNoteDetailListDTO;
import com.wunding.learn.course.service.admin.dto.CourseNoteListDTO;
import com.wunding.learn.course.service.admin.query.CourseNoteQuery;
import com.wunding.learn.course.service.client.dto.CourseNoteClientDTO;
import com.wunding.learn.course.service.client.dto.CourseNoteDetailClientDTO;
import com.wunding.learn.course.service.client.dto.SaveCourseNoteDTO;
import com.wunding.learn.course.service.client.query.CourseNoteClientQuery;
import com.wunding.learn.course.service.dao.CourseNoteDao;
import com.wunding.learn.course.service.mapper.CourseNoteMapper;
import com.wunding.learn.course.service.model.Course;
import com.wunding.learn.course.service.model.CourseNote;
import com.wunding.learn.course.service.model.Courseware;
import com.wunding.learn.course.service.service.ICourseNoteService;
import com.wunding.learn.course.service.service.ICourseService;
import com.wunding.learn.course.service.service.ICoursewareService;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.constant.ImageBizType;
import com.wunding.learn.file.api.dto.AbstractExportDataDTO;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.file.api.dto.SaveFileDTO;
import com.wunding.learn.file.api.dto.export.course.CourseNoteExportDTO;
import com.wunding.learn.file.api.service.FileFeign;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.service.OrgFeign;
import com.wunding.learn.user.api.service.RouterFeign;
import com.wunding.learn.user.api.service.SysSensitiveWordFeign;
import com.wunding.learn.user.api.service.UserFeign;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <p> 课程笔记表 服务实现类
 *
 * <AUTHOR> href="mailto:"></a>
 * @since 2023-11-22
 */
@Slf4j
@Service("courseNoteService")
public class CourseNoteServiceImpl extends BaseServiceImpl<CourseNoteMapper, CourseNote> implements ICourseNoteService {

    @Resource
    ExportComponent exportComponent;
    @Lazy
    @Resource
    private ICourseService courseService;
    @Resource
    private FileFeign fileFeign;
    @Resource
    private OrgFeign orgFeign;
    @Resource
    private UserFeign userFeign;
    @Resource
    @Lazy
    private ICoursewareService coursewareService;
    @Resource(name = "courseNoteDao")
    private CourseNoteDao courseNoteDao;
    @Resource
    SysSensitiveWordFeign sysSensitiveWordFeign;
    @Resource
    private RouterFeign routerFeign;

    @Override
    public void updateCourseNoteStatus(String courseId, Integer isAuditNote) {
        if (JudgeEnum.DENY.getValue().equals(isAuditNote)) {
            LambdaUpdateWrapper<CourseNote> courseNoteLambdaQueryWrapper = new LambdaUpdateWrapper<>();
            courseNoteLambdaQueryWrapper.eq(CourseNote::getAuditStatus, CourseNoteEnum.AUDITSTATUS_ING.getErrorCode());
            courseNoteLambdaQueryWrapper.eq(CourseNote::getCourseId, courseId);
            courseNoteLambdaQueryWrapper.set(CourseNote::getAuditStatus,
                CourseNoteEnum.AUDITSTATUS_SUCCESS.getErrorCode());
            courseNoteLambdaQueryWrapper.set(CourseNote::getAuditTime, new Date());
            courseNoteLambdaQueryWrapper.set(CourseNote::getAuditBy, UserThreadContext.getUserId());
            update(courseNoteLambdaQueryWrapper);
        }
    }

    @Override
    public void removeNoteByCwId(List<String> idsList) {
        LambdaUpdateWrapper<CourseNote> courseNoteLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        courseNoteLambdaUpdateWrapper.eq(CourseNote::getCoursewareId, idsList);
        courseNoteLambdaUpdateWrapper.set(CourseNote::getIsDel, DelEnum.DELETED.getValue());
        update(courseNoteLambdaUpdateWrapper);
    }

    @Override
    public PageInfo<CourseNoteListDTO> findCourseNoteListByPage(CourseNoteQuery courseNoteQuery) {
        // 模糊查询获得创建人
        // 然后根据创建根据条件去拿出所有得课程笔记，待条件的、
        // 填充
        courseNoteQuery.setUserIdList(TranslateUtil.translateBySplit(courseNoteQuery.getUserIds(), String.class));
        // 先获取页数
        Integer pageNo = courseNoteQuery.getPageNo();
        Integer pageSize = courseNoteQuery.getPageSize();
        // 先获取所有用户
        Set<String> userIds = new HashSet<>();
        List<CourseNoteDetailListDTO> courseNoteListByPage;
        courseNoteListByPage = baseMapper.findCourseNoteListByPage(courseNoteQuery);
        PageInfo<CourseNoteListDTO> result = new PageInfo<>();
        if (CollectionUtils.isEmpty(courseNoteQuery.getUserIdList())) {
            courseNoteListByPage.forEach(e -> userIds.add(e.getUserId()));
            List<String> pageUserIds = userIds.stream().skip((long) (pageNo - 1) * pageSize).limit(pageSize)
                .collect(Collectors.toList());
            List<String> nextPage = userIds.stream().skip((long) (pageNo) * pageSize).limit(pageSize)
                .collect(Collectors.toList());
            result.setIsLastPage(CollectionUtils.isEmpty(nextPage));
            courseNoteListByPage = courseNoteListByPage.stream().filter(e -> pageUserIds.contains(e.getUserId()))
                .collect(Collectors.toList());
        }
        List<CourseNoteListDTO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(courseNoteListByPage)) {
            List<String> auditUser = courseNoteListByPage.stream().map(CourseNoteDetailListDTO::getAuditBy)
                .collect(Collectors.toList());
            Map<String, UserDTO> auditUserMap = userFeign.getUserNameMapByIds(auditUser);
            courseNoteListByPage.stream().forEach(dto -> {
                Optional.ofNullable(fileFeign.getImageUrl(dto.getId(), ImageBizType.COURSE_NOTE_IMAGE.name()))
                    .ifPresent(dto::setScreenshotPath);
            });
            Map<String, List<CourseNoteDetailListDTO>> collect = courseNoteListByPage.stream()
                .collect(Collectors.groupingBy(dto -> dto.getUserId() + "#" + dto.getCoursewareId()));
            List<String> cwIds = courseNoteListByPage.stream().map(CourseNoteDetailListDTO::getCoursewareId)
                .collect(Collectors.toList());
            Map<String, CourseWareNameDetailDTO> courseWareNameDetailDTOMap = coursewareService
                .getCourseWareNameDetailByCwIds(
                    cwIds).stream().collect(Collectors.toMap(CourseWareNameDetailDTO::getId, e -> e));
            List<String> getUserNameMapByIds = courseNoteListByPage.stream().map(CourseNoteDetailListDTO::getUserId)
                .collect(Collectors.toList());
            Map<String, UserDTO> userNameMapByIds = userFeign.getUserNameMapByIds(getUserNameMapByIds);
            collect.keySet().forEach(dto -> {
                String[] split = dto.split("#");
                String userId = split[0];
                String cwId = split[1];
                CourseNoteListDTO courseNoteListDTO = new CourseNoteListDTO();
                UserDTO userDTO = userNameMapByIds.get(userId);

                if (userDTO != null) {
                    courseNoteListDTO.setUserId(userId);
                    courseNoteListDTO.setFullName(userDTO.getFullName());
                    courseNoteListDTO.setOrgId(userDTO.getOrgId());
                    courseNoteListDTO.setOrgName(userDTO.getOrgName());
                    courseNoteListDTO.setAva(userDTO.getAvatar());
                    courseNoteListDTO.setPostName(userDTO.getPostName());
                    courseNoteListDTO.setCwId(cwId);
                    CourseWareNameDetailDTO courseWareNameDetailDTO = courseWareNameDetailDTOMap.get(cwId);
                    courseNoteListDTO.setCwName(
                        courseWareNameDetailDTO != null ? courseWareNameDetailDTO.getCwName() : StringUtils.EMPTY);
                    List<CourseNoteDetailListDTO> courseNoteDetailListDTOS = collect.get(dto);
                    courseNoteDetailListDTOS.stream().forEach(e -> {
                        if (StringUtils.isNotBlank(e.getAuditBy())) {
                            Optional.ofNullable(auditUserMap.get(e.getAuditBy()).getFullName())
                                .ifPresent(d -> e.setAuditBy(d));
                        }
                    });
                    courseNoteListDTO.setCourseNoteListDetailDTO(courseNoteDetailListDTOS);
                    CourseNoteDetailListDTO courseNoteDetailListDTO = courseNoteDetailListDTOS.stream()
                        .sorted(Comparator.comparing(CourseNoteDetailListDTO::getCreateTime).reversed())
                        .collect(Collectors.toList()).get(0);
                    courseNoteListDTO.setDate(courseNoteDetailListDTO.getCreateTime());
                    list.add(courseNoteListDTO);
                }
            });
        }
        result.setList(list.stream().sorted(Comparator.comparing(CourseNoteListDTO::getDate).reversed())
            .collect(Collectors.toList()));
        result.setTotal(list.size());
        result.setPageNum(pageNo);
        result.setPageSize(pageSize);
        return result;
    }

    @Override
    public PageInfo<CourseNoteExportDTO> findExportCourseNoteListByPage(CourseNoteQuery courseNoteQuery) {
        // 模糊查询获得创建人
        // 然后根据创建根据条件去拿出所有得课程笔记，待条件的、
        // 填充
        courseNoteQuery.setUserIdList(TranslateUtil.translateBySplit(courseNoteQuery.getUserIds(), String.class));
        PageInfo<CourseNoteDetailListDTO> pageInfo = PageMethod.startPage(courseNoteQuery.getPageNo(),
            courseNoteQuery.getPageSize()).doSelectPageInfo(() -> baseMapper.findCourseNoteListByPage(courseNoteQuery));

        List<CourseNoteExportDTO> courseNoteExportDTOS = new ArrayList<>();
        if (!CollectionUtils.isEmpty(pageInfo.getList())) {
            List<String> userIds = pageInfo.getList().stream().map(CourseNoteDetailListDTO::getUserId)
                .collect(Collectors.toList());
            List<String> auditBy = pageInfo.getList().stream().map(CourseNoteDetailListDTO::getAuditBy)
                .collect(Collectors.toList());
            Map<String, UserDTO> userNameMapByIds = userFeign.getUserNameMapByIds(userIds);
            Map<String, UserDTO> userNameAuditMapByIds = userFeign.getUserNameMapByIds(auditBy);
            pageInfo.getList().stream().forEach(dto -> {
                Optional.ofNullable(userNameMapByIds.get(dto.getUserId())).ifPresent(userDTO -> {
                    CourseNoteExportDTO courseNoteExportDTO = new CourseNoteExportDTO();
                    BeanUtils.copyProperties(dto, courseNoteExportDTO);
                    courseNoteExportDTO.setFullName(userDTO.getFullName());
                    courseNoteExportDTO.setOrgName(userDTO.getLevelPathName());
                    UserDTO audit = Optional.ofNullable(userNameAuditMapByIds.get(dto.getAuditBy()))
                        .orElseGet(() -> new UserDTO().setFullName(StringUtils.EMPTY));
                    courseNoteExportDTO.setAuditBy(audit.getFullName());
                    Optional.ofNullable(coursewareService.getCourseWareById(dto.getCoursewareId()))
                        .ifPresent(courseWareById -> courseNoteExportDTO.setCwName(courseWareById.getCwName()));
                    Optional.ofNullable(fileFeign.getImageUrl(dto.getId(), ImageBizType.COURSE_NOTE_IMAGE.name()))
                        .ifPresent(courseNoteExportDTO::setScreenshotPath);
                    courseNoteExportDTO.setMarkPosition(String.valueOf(dto.getMarkPosition()));
                    if (dto.getCwType().equals("Text") || dto.getCwType().equals("Pic")) {
                        courseNoteExportDTO.setMarkPosition(StringUtils.EMPTY);
                    } else if (dto.getCwType().equals("Audio") || dto.getCwType().equals("Video")) {
                        courseNoteExportDTO.setMarkPosition(DateUtil.getHMSBySecond(dto.getMarkPosition()));
                    }
                    Optional.of(DateUtil.formatDate(dto.getAuditTime(), DateUtil.YYMMDD_HHMMSS))
                        .ifPresent(courseNoteExportDTO::setAuditTime);
                    Optional.of(DateUtil.formatDate(dto.getCreateTime(), DateUtil.YYMMDD_HHMMSS))
                        .ifPresent(courseNoteExportDTO::setCreateTime);
                    Optional.ofNullable(dto.getAuditStatus()).ifPresent(auditStatus -> {
                        switch (auditStatus) {
                            case 0:
                                courseNoteExportDTO.setAuditStatus("待审核");
                                break;
                            case 1:
                                courseNoteExportDTO.setAuditStatus("已通过");
                                break;
                            case 2:
                                courseNoteExportDTO.setAuditStatus("未通过");
                                break;
                            // 可以添加 default 分支处理未考虑的情况
                            default:
                                // 可以选择设置一个默认的值，或者什么也不做，视具体情况而定
                                break;
                        }
                    });
                    courseNoteExportDTOS.add(courseNoteExportDTO);
                });
            });
        }
        PageInfo<CourseNoteExportDTO> result = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, result);
        result.setList(courseNoteExportDTOS);
        return result;
    }

    @Override
    public void reviewNote(String ids, Integer type) {
        List<String> idsList = TranslateUtil.translateBySplit(ids, String.class);
        List<CourseNote> courseNotes = listByIds(idsList);
        courseNotes.forEach(e -> {
            e.setAuditStatus(type);
            courseNoteDao.reviewNote(e);
        });
    }

    @Override
    public void del(String ids) {
        List<String> idsList = TranslateUtil.translateBySplit(ids, String.class);
        List<CourseNote> courseNotes = listByIds(idsList);
        courseNotes.forEach(e -> courseNoteDao.del(e));
    }

    @Override
    public void saveOrUpdateCourseNote(SaveCourseNoteDTO saveCourseNoteDTO) {

        //校验
        CourseNote courseNote = checkCourseNote(saveCourseNoteDTO);

        if (StringUtils.isBlank(saveCourseNoteDTO.getId())) {
            courseNote.setId(StringUtil.newId()).setIsOld(0);
        } else {
            //编辑时查看内容是否有变化，如果没有变化，审批状态不用修改
            CourseNote tempCourseNote = getById(saveCourseNoteDTO.getId());
            if (tempCourseNote.getContent().equals(saveCourseNoteDTO.getContent())) {
                courseNote.setAuditStatus(null);
            }
            // 编辑时默认每次图片都做了修改
            fileFeign.deleteImageByBizIdAndBizType(saveCourseNoteDTO.getId(), ImageBizType.COURSE_NOTE_IMAGE.name());
        }

        // 保存图片，拷贝临时目录至正式目录
        if (StringUtils.isNotBlank(saveCourseNoteDTO.getScreenshotPath())) {
            SaveFileDTO image = fileFeign.saveImage(courseNote.getId(), ImageBizType.COURSE_NOTE_IMAGE.name(),
                saveCourseNoteDTO.getScreenshotName(), saveCourseNoteDTO.getScreenshotPath());
            if (null != image) {
                courseNote.setScreenshotPath(image.getPath());
            }
        }
        saveOrUpdate2(courseNote);
    }

    private CourseNote checkCourseNote(SaveCourseNoteDTO saveCourseNoteDTO) {
        //如果是复制的课程，则笔记要保存在原始课程里
        Course tempCourse = courseService.getById(saveCourseNoteDTO.getCourseId());
        if (Optional.ofNullable(tempCourse).isPresent() && tempCourse.getIsCopy().equals(1)) {
            Courseware tempCourseware = coursewareService.getById(saveCourseNoteDTO.getCoursewareId());
            if (Optional.ofNullable(tempCourseware).isPresent() && tempCourseware.getIsCopy().equals(1)) {
                saveCourseNoteDTO
                    .setIndependentCourseId(saveCourseNoteDTO.getCourseId())
                    .setIndependentCoursewareId(saveCourseNoteDTO.getCoursewareId())
                    .setCourseId(tempCourse.getAuthenticId())
                    .setCoursewareId(tempCourseware.getAuthenticId());

            }
        }

        //课程笔记内容校验
        if (StringUtils.isBlank(saveCourseNoteDTO.getContent()) && (
            StringUtils.isBlank(saveCourseNoteDTO.getScreenshotPath()) || StringUtils.isBlank(
                saveCourseNoteDTO.getScreenshotName()))) {
            throw new BusinessException(CourseErrorNoEnum.ERR_NOTE_EMPTY);
        }
        //检测课件是否存在
        Courseware courseware = Optional.ofNullable(
                coursewareService.getBaseMapper().selectById(saveCourseNoteDTO.getCoursewareId()))
            .orElseThrow(() -> new BusinessException(CourseErrorNoEnum.ERR_COURSEWARE_NULL));
        //课程笔记初始化
        CourseNote courseNote = new CourseNote();
        BeanUtils.copyProperties(saveCourseNoteDTO, courseNote);
        courseNote.setCwMime(courseware.getMime()).setCwType(courseware.getCwType());
        //替换html特殊字符，比如：&nbsp &amp
        courseNote.setContent(org.apache.commons.text.StringEscapeUtils.unescapeHtml4(saveCourseNoteDTO.getContent()));
        List<String> routerIds = routerFeign.getRouterNames();
        // 检验系统版本权限是否包含要校验的内容；
        if(routerIds.contains(ResourceTypeEnum.SENSITIVE_CONFIG.getRouter())){
            // 敏感词校验
            String descStr = sysSensitiveWordFeign.checkWordsReturnStr(courseNote.getContent());
            if (!StringUtils.isEmpty(descStr)) {
                throw new BusinessException(CourseErrorNoEnum.ERR_NOTE_HAS_SENSITIVE_WORD_EXCEPTION, null, descStr);
            }
        }
        //查询该笔记对应的课程是否开启了审核，是的话笔记审核状态为审核中，否则为审核通过
        Course course = Optional.ofNullable(courseService.getById(saveCourseNoteDTO.getCourseId()))
            .orElseThrow(() -> new BusinessException(CourseErrorNoEnum.ERR_COURSE_NULL));
        courseNote.setAuditStatus(course.getIsAuditNote() == 0 ? CourseNoteEnum.AUDITSTATUS_SUCCESS.getErrorCode()
            : CourseNoteEnum.AUDITSTATUS_ING.getErrorCode());
        return courseNote;
    }

    @Override
    public List<CourseNoteDetailClientDTO> getMyCourseNote(String courseId, String coursewareId) {

        Boolean isQuoteCourse = false;
        Course tempCourse = courseService.getById(courseId);
        if (Optional.ofNullable(tempCourse).isPresent() && tempCourse.getIsCopy().equals(1)) {
            isQuoteCourse = true;
        }
        String userId = UserThreadContext.getUserId();
        List<CourseNote> list = null;
        if (!isQuoteCourse) {
            list = list(new LambdaQueryWrapper<CourseNote>().eq(CourseNote::getCreateBy, userId)
                .eq(CourseNote::getCourseId, courseId)
                .eq(CourseNote::getCoursewareId, coursewareId)
                .eq(CourseNote::getIndependentCourseId, "")
                .orderByDesc(CourseNote::getUpdateTime).orderByDesc(CourseNote::getCreateTime));
        } else {
            list = list(new LambdaQueryWrapper<CourseNote>().eq(CourseNote::getCreateBy, userId)
                .eq(CourseNote::getIndependentCourseId, courseId)
                .eq(CourseNote::getIndependentCoursewareId, coursewareId)
                .orderByDesc(CourseNote::getUpdateTime).orderByDesc(CourseNote::getCreateTime));
        }
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<CourseNoteDetailClientDTO> result = BeanListUtils.copyList(list, CourseNoteDetailClientDTO.class);
        Set<String> ids = list.stream().map(CourseNote::getId).collect(Collectors.toSet());
        Map<String, String> imgMap = fileFeign.getImageUrlsByIds(ids, ImageBizType.COURSE_NOTE_IMAGE.name());
        result.forEach(dto -> {
            dto.setCourseId(courseId)
                .setCoursewareId(coursewareId)
                .setScreenshotUrl(imgMap.get(dto.getId()));
        });
        return result;
    }


    @Override
    public void delMyCourseNote(String id) {
        CourseNote courseNote = Optional.ofNullable(getById(id))
            .orElseThrow(() -> new BusinessException(CourseErrorNoEnum.ERR_NOTE_NULL));
        if (!courseNote.getCreateBy().equals(UserThreadContext.getUserId())) {
            throw new BusinessException(CourseErrorNoEnum.ERR_NO_POWER_DELETE_NOTE);
        }
        removeById(id);
    }

    @Override
    public PageInfo<CourseNoteClientDTO> findCourseNoteListByPage(CourseNoteClientQuery courseNoteClientQuery) {

        Boolean isQuote = false;
        Course tempCourse = courseService.getById(courseNoteClientQuery.getCourseId());
        if (Optional.ofNullable(tempCourse).isPresent() && tempCourse.getIsCopy().equals(1)) {
            isQuote = true;
        }

        courseNoteClientQuery.setCurrentUserId(UserThreadContext.getUserId());
        Boolean finalIsQuote = isQuote;
        PageInfo<CourseNoteClientDTO> pageInfo = PageMethod.startPage(courseNoteClientQuery.getPageNo(),
                courseNoteClientQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.findClientCourseNoteListByPage(courseNoteClientQuery, finalIsQuote));
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return pageInfo;
        }

        //课件id
        Set<String> coursewareIds = pageInfo.getList().stream().map(CourseNoteClientDTO::getCwId)
            .collect(Collectors.toSet());
        //用户id
        Set<String> userIds = pageInfo.getList().stream().map(CourseNoteClientDTO::getCreateBy)
            .collect(Collectors.toSet());
        List<CourseNote> courseNoteDetailList = list(buildQueryWrapper(courseNoteClientQuery, coursewareIds, isQuote));
        Map<String, List<CourseNote>> courseNoteMap = null;
        if (finalIsQuote) {
            courseNoteMap = courseNoteDetailList.stream()
                .collect(Collectors.groupingBy(dto -> dto.getCreateBy() + "#" + dto.getIndependentCoursewareId()));
        } else {
            courseNoteMap = courseNoteDetailList.stream()
                .collect(Collectors.groupingBy(dto -> dto.getCreateBy() + "#" + dto.getCoursewareId()));
        }

        Map<String, UserDTO> userNameMapByIds = userFeign.getUserNameMapByIds(userIds);
        Set<String> ids = courseNoteDetailList.stream().map(CourseNote::getId).collect(Collectors.toSet());
        Map<String, String> imgMap = fileFeign.getImageUrlsByIds(ids, ImageBizType.COURSE_NOTE_IMAGE.name());

        Map<String, List<CourseNote>> finalCourseNoteMap = courseNoteMap;
        pageInfo.getList().forEach(dto -> {
            UserDTO userDTO = userNameMapByIds.get(dto.getCreateBy());
            if (userDTO != null) {
                dto.setFullName(userDTO.getFullName());
                dto.setUserAvatar(userDTO.getAvatar());
            }
            List<CourseNote> courseNotes = finalCourseNoteMap.get(dto.getCreateBy() + "#" + dto.getCwId());
            if (CollectionUtils.isEmpty(courseNotes)) {
                dto.setList(Collections.emptyList());
            } else {
                List<CourseNoteDetailClientDTO> result = new ArrayList<>();
                courseNotes.forEach(courseNote -> {
                    CourseNoteDetailClientDTO courseNoteDetailClientDTO = new CourseNoteDetailClientDTO();
                    BeanUtils.copyProperties(courseNote, courseNoteDetailClientDTO);
                    courseNoteDetailClientDTO
                        .setScreenshotUrl(imgMap.get(courseNote.getId()));
                    if (finalIsQuote) {
                        courseNoteDetailClientDTO.setCourseId(courseNote.getIndependentCourseId());
                        courseNoteDetailClientDTO.setCoursewareId(courseNote.getIndependentCoursewareId());
                    }
                    result.add(courseNoteDetailClientDTO);
                });
                dto.setList(result);
            }
        });
        return pageInfo;
    }

    @Override
    public void exportCourseNote(CourseNoteQuery courseNoteQuery) {

        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<ICourseNoteService, CourseNoteExportDTO>(
            courseNoteQuery) {

            @Override
            protected ICourseNoteService getBean() {
                return SpringUtil.getBean("courseNoteService", ICourseNoteService.class);
            }

            @Override
            protected PageInfo<CourseNoteExportDTO> getPageInfo() {
                return getBean().findExportCourseNoteListByPage((CourseNoteQuery) queryDTO);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.CourseNote;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.CourseNote.getType();
            }

        };

        exportComponent.exportRecord(exportDataDTO);

    }

    /**
     * 构造查询条件
     *
     * @param query
     * @param coursewareIds
     * @return
     */
    private LambdaQueryWrapper<CourseNote> buildQueryWrapper(CourseNoteClientQuery query,
        Collection<String> coursewareIds, Boolean isQuote) {
        LambdaQueryWrapper<CourseNote> queryWrapper = new LambdaQueryWrapper<CourseNote>();
        if (isQuote) {
            queryWrapper.eq(CourseNote::getIndependentCourseId, query.getCourseId())
                .eq(StringUtils.isNotBlank(query.getCoursewareId()), CourseNote::getIndependentCoursewareId,
                    query.getCoursewareId())
                .in(CourseNote::getIndependentCoursewareId, coursewareIds);
        } else {
            queryWrapper.eq(CourseNote::getCourseId, query.getCourseId())
                .eq(StringUtils.isNotBlank(query.getCoursewareId()), CourseNote::getCoursewareId,
                    query.getCoursewareId())
                .in(CourseNote::getCoursewareId, coursewareIds)
                .eq(CourseNote::getIndependentCoursewareId, "");
        }
        queryWrapper.eq(CourseNote::getAuditStatus, 1)
            .eq(Optional.ofNullable(query.getIsReadMyNote()).isPresent() && CourseConstant.ONLY_READ_MY_NOTE.equals(
                query.getIsReadMyNote()), CourseNote::getCreateBy, query.getCurrentUserId())
            .and(
                wrapper -> wrapper.eq(CourseNote::getIsSelfVisible, 0).or()
                    .eq(CourseNote::getCreateBy, query.getCurrentUserId())).orderByDesc(CourseNote::getUpdateTime);
        return queryWrapper;
    }
}
