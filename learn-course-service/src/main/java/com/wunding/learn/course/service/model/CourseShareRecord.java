package com.wunding.learn.course.service.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">axr</a>
 * @since 2024-08-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("course_share_record")
@Schema(name = "CourseShareRecord对象", description = "")
public class CourseShareRecord implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键id
     */
    @Schema(description = "主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /**
     * 课程id
     */
    @Schema(description = "课程id")
    @TableField("course_id")
    private String courseId;


    /**
     * 下发方式：0 部分可见 1仅创建者可见 2所有人可见
     */
    @Schema(description = "下发方式：0 部分可见 1仅创建者可见 2所有人可见")
    @TableField("view_type")
    private Integer viewType;


    /**
     * 下发方案id
     */
    @Schema(description = "下发方案id")
    @TableField("view_limit_id")
    private Long viewLimitId;


}
