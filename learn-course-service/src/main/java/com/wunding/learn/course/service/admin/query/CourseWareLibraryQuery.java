package com.wunding.learn.course.service.admin.query;

import com.wunding.learn.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.format.annotation.DateTimeFormat.ISO;

/**
 * @Author: aixinrong
 * @Date: 2022/6/13 13:54
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CourseWareLibraryQuery extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Parameter(description = "课件名")
    private String cwName;

    @Parameter(description = "入库类别（courseComplete:已入库，coursePending:未入库）")
    private String categoryType;

    @Parameter(description = "入库者ids集合,分割")
    private String createByIds;

    @Parameter(hidden = true)
    private List<String> createByIdsVo;

    @Parameter(description = "编辑者ids集合,分割")
    private String updateByIds;

    @Parameter(hidden = true)
    private List<String> updateByIdsVo;

    @Parameter(description = "课件库分类id")
    private String libraryCateId;

    @Parameter(description = "课件类型(Word:Word,PPT:PPT,ZIP:ZIP,PDF:PDF,MP4/3mv:Video,MP3:Audio,在线做课:Text)")
    private String cwType;

    @Parameter(description = "入库时间:开始")
    @DateTimeFormat(iso = ISO.DATE_TIME)
    private Date createBeginTime;

    @Parameter(description = "入库时间:结束")
    @DateTimeFormat(iso = ISO.DATE_TIME)
    private Date createEndTime;

    @Parameter(description = "编辑时间:开始")
    @DateTimeFormat(iso = ISO.DATE_TIME)
    private Date updateBeginTime;

    @Parameter(description = "编辑时间:结束")
    @DateTimeFormat(iso = ISO.DATE_TIME)
    private Date updateEndTime;

    @Parameter(description = "管辖范围", hidden = true)
    Set<String> userManageAreaOrgIdList;

    @Parameter(description = "课件启用状态 0禁言 1启用(该字段提供给引用界面使用)")
    private Integer isAvailable;

    @Schema(description = "文件转码状态 1-转码中 2-转码成功 3-转码失败 4-排队中")
    private Integer transformStatus;
}
