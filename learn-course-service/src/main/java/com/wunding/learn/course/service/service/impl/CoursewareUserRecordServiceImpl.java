package com.wunding.learn.course.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.common.dto.IdNumber;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.mq.event.course.CourseFinishEvent;
import com.wunding.learn.common.mq.event.mapshape.MapKnowledgeLearningFinishEvent;
import com.wunding.learn.common.mq.service.impl.RabbitMqProducer;
import com.wunding.learn.course.service.admin.dto.CoursewareStudyDetailDTO;
import com.wunding.learn.course.service.admin.query.CoursewareStudyQuery;
import com.wunding.learn.course.service.client.query.CourseViewDurationClientQuery;
import com.wunding.learn.course.service.mapper.CoursewareMapper;
import com.wunding.learn.course.service.mapper.CoursewareUserRecordMapper;
import com.wunding.learn.course.service.mapper.UserCourseRecordMapper;
import com.wunding.learn.course.service.model.Courseware;
import com.wunding.learn.course.service.model.CoursewareUserRecord;
import com.wunding.learn.course.service.model.UserCourseRecord;
import com.wunding.learn.course.service.service.ICoursewareUserRecordService;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <p> 用户学习课件记录表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">余学益</a>
 * @date 2022-05-10
 */
@Slf4j
@Service("coursewareUserRecordService")
public class CoursewareUserRecordServiceImpl extends
    ServiceImpl<CoursewareUserRecordMapper, CoursewareUserRecord> implements ICoursewareUserRecordService {

    @Resource
    private CoursewareMapper coursewareMapper;
    @Resource
    private UserCourseRecordMapper userCourseRecordMapper;
    @Resource
    private RabbitMqProducer mqProducer;

    @Override
    public List<CoursewareStudyDetailDTO> getCoursewareStudyDetail(CoursewareStudyQuery coursewareStudyQuery) {
        if (coursewareStudyQuery.getIsTrain() != null && coursewareStudyQuery.getIsTrain() == 0) {
            return baseMapper.getCoursewareStudyDetailByViewLimit(coursewareStudyQuery);
        }
        return baseMapper.getCoursewareStudyDetail(coursewareStudyQuery);
    }

    @Override
    public Integer getCoursewareStudyDuration(CourseViewDurationClientQuery query) {
        return baseMapper.getCoursewareStudyDuration(query);
    }

    @Override
    public Map<String, Long> countLearnedUserCountByCWIds(List<String> cwIds) {
        List<IdNumber> list = baseMapper.countLearnedUserCountByCWIds(cwIds);
        return list.stream().collect(Collectors.toMap(IdNumber::getId, IdNumber::getNumber));
    }

    @Override
    public void updateUserRecord(Integer examFinishType, String examId, String userId) {

        // 课件关联考试时的完成条件（-1:不涉及考试 0:提交考试即完成 1:通过考试才完成，仅在 exam_id 不为空时生效）
        if (examFinishType == null || examFinishType == -1) {
            log.error("课件关联考试相关MQ事件参数异常： examFinishType is null or examFinishType == -1");
            return;
        }

        LambdaQueryWrapper<Courseware> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Courseware::getExamId, examId);
        queryWrapper.eq(Courseware::getIsDel, GeneralJudgeEnum.NEGATIVE.getValue());
        queryWrapper.eq(Courseware::getExamFinishType, examFinishType);

        List<Courseware> list = coursewareMapper.selectList(queryWrapper);
        // 返回被修改课件完成记录的课件
        List<Courseware> updateCoursewareUserRecord = updateCoursewareUserRecord(list, userId);

        if (CollectionUtils.isEmpty(updateCoursewareUserRecord)) {
            return;
        }

        Set<String> courseIdSet = updateCoursewareUserRecord.stream().map(Courseware::getCourseId)
            .filter(StringUtils::isNotBlank)
            .collect(Collectors.toSet());
        LambdaQueryWrapper<Courseware> coursewareWrapper = new LambdaQueryWrapper<>();
        coursewareWrapper.in(!CollectionUtils.isEmpty(courseIdSet), Courseware::getCourseId, courseIdSet);
        //获取课程下所有的课件
        List<Courseware> coursewareList = coursewareMapper.selectList(coursewareWrapper);
        Map<String, List<Courseware>> coursewareMap = coursewareList.stream()
            .collect(Collectors.groupingBy(Courseware::getCourseId));

        //获取所有课程的学习记录
        LambdaQueryWrapper<CoursewareUserRecord> userRecordWrapper = new LambdaQueryWrapper<>();
        userRecordWrapper.in(!CollectionUtils.isEmpty(courseIdSet), CoursewareUserRecord::getCourseId,
            courseIdSet).eq(CoursewareUserRecord::getUserId, userId);
        List<CoursewareUserRecord> coursewareUserRecordList = baseMapper.selectList(userRecordWrapper);
        Map<String, Map<String, CoursewareUserRecord>> coursewareUserRecordMap = coursewareUserRecordList.stream()
            .collect(Collectors.groupingBy(CoursewareUserRecord::getCourseId,
                Collectors.toMap(CoursewareUserRecord::getCoursewareId, a -> a, (key1, key2) -> key1)));

        for (String courseId : courseIdSet) {
            boolean courseIsLearned;
            List<Courseware> courseWares = coursewareMap.get(courseId);
            // 获取已有的课件学习记录
            Map<String, CoursewareUserRecord> cwUserRecordMap = coursewareUserRecordMap.get(courseId);
            if (CollectionUtils.isEmpty(cwUserRecordMap)) {
                cwUserRecordMap = new HashMap<>();
            }

            //遍历课程下的所有课件校对所有的课件是否都学完了
            courseIsLearned = checkAllLearn(courseWares, cwUserRecordMap);

            // 查询课程学习记录
            LambdaQueryWrapper<UserCourseRecord> userCourseRecordWrapper = new LambdaQueryWrapper<>();
            userCourseRecordWrapper.eq(UserCourseRecord::getCourseId, courseId)
                .eq(UserCourseRecord::getUserId, userId);
            UserCourseRecord userCourseRecord = userCourseRecordMapper.selectOne(userCourseRecordWrapper);
            if (Objects.isNull(userCourseRecord)) {
                // 可能存在课程已经删除仍然还有人参加考试的情况
                return;
            }
            // 只有以前未学完的课程，才会更新这个课程的已学状态
            if (userCourseRecord.getIsLearned() == 0 && courseIsLearned) {
                // 第一次完成课程
                userCourseRecord.setIsLearned(1);
                userCourseRecord.setFinishTime(userCourseRecord.getUpdateTime());
                userCourseRecordMapper.updateById(userCourseRecord);

                //发送完成课程事件
                mqProducer.sendMsg(new CourseFinishEvent(courseId, userId, userId));

                //发送完成能力事件
                mqProducer.sendMsg(new MapKnowledgeLearningFinishEvent(courseId, userId));
            }
        }
    }

    private boolean checkAllLearn(List<Courseware> courseWares, Map<String, CoursewareUserRecord> cwUserRecordMap) {
        for (Courseware cw : courseWares) {
            // 此课件没上报，查询是否有学习记录
            CoursewareUserRecord courseWareUserRecord = cwUserRecordMap.get(cw.getId());
            // 如果课件没有学习记录或者学习状态是未学完
            if (null == courseWareUserRecord || 0 == courseWareUserRecord.getIsLearned()) {
                // 设置为未学完
                return false;
            }
        }
        return true;
    }

    private List<Courseware> updateCoursewareUserRecord(List<Courseware> list, String userId) {
        List<Courseware> removeList = new ArrayList<>();
        for (Courseware courseware : list) {
            // 查询实际学习时长大于课件时长并且是未完成状态的记录
            LambdaQueryWrapper<CoursewareUserRecord> query = new LambdaQueryWrapper<>();
            query.eq(CoursewareUserRecord::getCoursewareId, courseware.getId());
            query.eq(CoursewareUserRecord::getUserId, userId);
            query.eq(CoursewareUserRecord::getIsLearned, GeneralJudgeEnum.NEGATIVE.getValue());
            CoursewareUserRecord one = this.getOne(query);

            if (Objects.nonNull(one)
                &&
                (
                    // 完成类型[0:按时长，1:按进度] - 按时长
                    (courseware.getFinishType().equals(0) && one.getDuration() >= courseware.getPlayTime()) ||
                        // 完成类型[0:按时长，1:按进度] - 按进度
                        (courseware.getFinishType().equals(1) && one.getProgress() >= courseware.getRequiredProgress())
                )
            ) {
                one.setIsLearned(GeneralJudgeEnum.CONFIRM.getValue());
                one.setFinishTime(one.getEndTime());
                this.updateById(one);
            } else {
                // 排除未受影响的课件
                removeList.add(courseware);
            }
        }
        list.removeAll(removeList);
        return list;
    }

    @Override
    public List<CoursewareUserRecord> queryListByUserIdAndCwIds(String userId, Collection<String> cwIds) {
        if (CollectionUtils.isEmpty(cwIds)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<CoursewareUserRecord>().eq(CoursewareUserRecord::getUserId, userId)
            .in(CoursewareUserRecord::getCoursewareId, cwIds));
    }

    @Override
    public CoursewareUserRecord getByUserIdAndCwId(String userId, String coursewareId) {
        return getOne(new LambdaQueryWrapper<CoursewareUserRecord>().eq(CoursewareUserRecord::getUserId, userId)
            .eq(CoursewareUserRecord::getCoursewareId, coursewareId));
    }

    @Override
    public Map<String, Long> getCoursewareProgressByUserIdAndCwId(String userId, List<String> cwIds) {
        if (CollectionUtils.isEmpty(cwIds)) {
            return new HashMap<>();
        }
        List<CoursewareUserRecord> list = baseMapper.getCoursewareProgressByUserIdAndCwId(userId, cwIds);
        if (CollectionUtils.isEmpty(list)) {
            return new HashMap<>();
        }
        return list.stream().collect(Collectors
            .toMap(CoursewareUserRecord::getCoursewareId, CoursewareUserRecord::getProgress, (key1, key2) -> key1));
    }
}
