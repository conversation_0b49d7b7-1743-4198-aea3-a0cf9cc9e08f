<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.course.service.mapper.CoursewareUserRecordMapper">
    <!-- 开启二级缓存 -->
    <!--
  <cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
   <!-- <cache-ref namespace="com.wunding.learn.course.service.mapper.CoursewareUserRecordMapper"/>-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.course.service.model.CoursewareUserRecord">
        <!--@Table courseware_user_record-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="user_id" jdbcType="VARCHAR"
          property="userId"/>
        <result column="courseware_id" jdbcType="VARCHAR"
          property="coursewareId"/>
        <result column="course_id" jdbcType="VARCHAR"
          property="courseId"/>
        <result column="is_learned" jdbcType="TINYINT"
          property="isLearned"/>
        <result column="duration" jdbcType="BIGINT"
          property="duration"/>
        <result column="start_time" jdbcType="TIMESTAMP"
          property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP"
          property="endTime"/>
        <result column="progress" jdbcType="BIGINT"
          property="progress"/>
        <result column="finish_time" jdbcType="TIMESTAMP"
          property="finishTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , user_id, courseware_id, course_id, is_learned, duration, start_time, end_time, progress, finish_time
    </sql>

    <select id="getCoursewareStudyDetail"
      parameterType="com.wunding.learn.course.service.admin.query.CoursewareStudyQuery"
      resultType="com.wunding.learn.course.service.admin.dto.CoursewareStudyDetailDTO" useCache="false">
        select cwr.user_id,
        cw.exam_id,
        cwr.start_time beginLearnTime,
        cwr.end_time lastLearnTime,
        cwr.finish_time finishLearnTime,
        cwr.duration,
        cwr.is_learned learnState
        ,cw.cw_name courseWareName
        from courseware_user_record cwr
        left join courseware cw on cwr.courseware_id = cw.id
        <where>
        <if test="params.courseId != null and params.courseId != ''">
            and cw.course_id = #{params.courseId}
        </if>
        <if test="params.cwId != null and params.cwId != ''">
            and cwr.courseware_id = #{params.cwId}
        </if>
        <if test="params.courseWareName != null and params.courseWareName != ''">
            and instr(cw.cw_name,#{params.courseWareName}) > 0
        </if>
        <if test="params.learnState != null">
        and cwr.is_learned = #{params.learnState}
        </if>
        <if test="params.userIdsVo != null and params.userIdsVo.size() > 0">
            <foreach collection="params.userIdsVo" item="uid" open="and cwr.user_id in (" close=")"
              separator=",">
                #{uid}
            </foreach>
        </if>
        <if test="params.levelPath != null and params.levelPath != ''">
            and cwr.level_path like concat(#{params.levelPath},'%')
        </if>
        </where>
    </select>

    <select id="getCoursewareStudyDetailByViewLimit" resultType="com.wunding.learn.course.service.admin.dto.CoursewareStudyDetailDTO" useCache="false">
        select * from
          (
          select wvlu.user_id               user_id,
                 cw.exam_id,
                 cwr.start_time             beginLearnTime,
                 cwr.end_time               lastLearnTime,
                 cwr.finish_time            finishLearnTime,
                 cwr.duration,
                 ifnull(cwr.is_learned, -1) learnState
            ,
                 cw.cw_name                 courseWareName
          from courseware cw
          left join w_resource_view_limit wrvl on wrvl.resource_id = cw.course_id
          left join w_view_limit_user wvlu on wvlu.view_limit_id = wrvl.view_limit_id
          left join courseware_user_record cwr on cwr.courseware_id = cw.id and cwr.user_id = wvlu.user_id
        <if test="params.levelPath != null and params.levelPath != ''">
            left join sys_user su on su.id = wvlu.user_id
            left join sys_org so on so.id = su.org_id
        </if>
        <where>
            cw.is_del = 0
              and resource_type = 'CourseViewLimit'
            <if test="params.courseId != null and params.courseId != ''">
                and cw.course_id = #{params.courseId}
            </if>
            <if test="params.cwId != null and params.cwId != ''">
                and cw.id = #{params.cwId}
            </if>
            <if test="params.courseWareName != null and params.courseWareName != ''">
                and instr(cw.cw_name
                      , #{params.courseWareName})
                  > 0
            </if>
            <if test="params.userIdsVo != null and params.userIdsVo.size() > 0">
                <foreach collection="params.userIdsVo" item="uid" open="and wvlu.user_id in (" close=")"
                  separator=",">
                    #{uid}
                </foreach>
            </if>
            <if test="params.levelPath != null and params.levelPath != ''">
                and so.level_path like concat(#{params.levelPath}, '%')
            </if>
            </where>
        )t
        <where>
            <if test="params.learnState != null">
                learnState = #{params.learnState}
            </if>
        </where>
        order by learnState asc
</select>
    <select id="getCoursewareStudyDuration" resultType="java.lang.Integer"
      parameterType="com.wunding.learn.course.service.client.query.CourseViewDurationClientQuery">
        select progress
        from courseware_user_record
        where course_id = #{params.courseId}
          and user_id = #{params.viewBy}
          and courseware_id = #{params.cwId}
    </select>

    <select id="countLearnedUserCountByCWIds" resultType="com.wunding.learn.common.dto.IdNumber">
        select courseware_id id,count(courseware_id) number from courseware_user_record
        where courseware_id in
        <foreach collection="cwIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        group by courseware_id
    </select>

    <select id="selectSumTime" resultType="_long" useCache="false">
        select sum(duration) as duration from course_view_duration where view_by = #{userId} and cw_id = #{cwId}
    </select>

    <select id="selectMaxDetailProgress" resultType="_long" useCache="false">
        select max(progress) as progress from course_view_duration where view_by = #{userId} and cw_id = #{cwId}
    </select>

    <select id="selectListFromDB"
      resultMap="BaseResultMap" useCache="false">
        select t.*
        from courseware_user_record t
        left outer join course a on t.course_id = t.id
        where 1 =1
        <if test="courseIdSet != null and courseIdSet.size() > 0">
            <foreach collection="courseIdSet" item="courseId" open="and t.course_id in (" close=")"
              separator=",">
                #{courseId}
            </foreach>
        </if>
        <if test="userId != null and userId != ''">
            and t.user_id = #{userId}
        </if>
    </select>

    <select id="getCoursewareProgressByUserIdAndCwId"
      resultType="com.wunding.learn.course.service.model.CoursewareUserRecord">
        SELECT courseware_id, progress
        FROM courseware_user_record
        where user_id = #{userId}
        and courseware_id in
        <foreach collection="cwIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>
</mapper>

