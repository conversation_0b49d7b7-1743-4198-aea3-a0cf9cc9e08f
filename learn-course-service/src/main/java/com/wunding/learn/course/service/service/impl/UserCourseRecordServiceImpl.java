package com.wunding.learn.course.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.common.dto.ResourceIdAndUserIdDTO;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.redis.annotaion.RedisCacheable;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.course.api.dto.CourseDurationDTO;
import com.wunding.learn.course.api.dto.UserCourseStudyInfoDTO;
import com.wunding.learn.course.api.dto.UserCoursewareStudyInfoDTO;
import com.wunding.learn.course.api.query.CourseDurationQuery;
import com.wunding.learn.course.api.query.UserCourseStudyInfoQuery;
import com.wunding.learn.course.service.admin.dto.CourseLearnDetailDTO;
import com.wunding.learn.course.service.admin.dto.CourseStudyDetailDTO;
import com.wunding.learn.course.service.admin.query.CourseLearnQuery;
import com.wunding.learn.course.service.admin.query.CourseStudyQuery;
import com.wunding.learn.course.service.admin.query.CourseStudyStatisticsQuery;
import com.wunding.learn.course.service.client.dto.CourseClickNumberDTO;
import com.wunding.learn.course.service.mapper.UserCourseRecordMapper;
import com.wunding.learn.course.service.model.UserCourseRecord;
import com.wunding.learn.course.service.service.IUserCourseRecordService;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <p> 用户课程学习记录 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">余学益</a>
 * @date 2022-05-10
 */
@Slf4j
@Service("userCourseRecordService")
public class UserCourseRecordServiceImpl extends ServiceImpl<UserCourseRecordMapper, UserCourseRecord> implements
    IUserCourseRecordService {

    @Override
    public List<CourseLearnDetailDTO> getCourseLearnDetail(CourseLearnQuery courseLearnQuery) {
        return baseMapper.getCourseLearnDetail(courseLearnQuery);
    }

    @Override
    public List<CourseStudyDetailDTO> getCourseStudyDetail(CourseStudyQuery courseStudyQuery) {
        if (null != courseStudyQuery.getIsTrain() && courseStudyQuery.getIsTrain() == 0) {
            return baseMapper.getCourseStudyDetailByViewLimit(courseStudyQuery);
        }
        return baseMapper.getCourseStudyDetail(courseStudyQuery);
    }

    @Override
    public List<CourseStudyDetailDTO> courseStudyStatisticsDetail(CourseStudyStatisticsQuery query) {
        return baseMapper.courseStudyStatisticsDetail(query);
    }




    @RedisCacheable(expire = 60)
    @Override
    public Map<String, Integer> selectCountByCourseIdList(List<String> courseIdList) {
        if (CollectionUtils.isEmpty(courseIdList)) {
            return new HashMap<>();
        }
        // 数据库操作
        List<CourseClickNumberDTO> list = baseMapper.selectCountByCourseIdList(courseIdList);
        if (CollectionUtils.isEmpty(list)) {
            return new HashMap<>();
        }
        // 防止缓存取值时转换报错
        return list.stream().collect(
            Collectors.toMap(CourseClickNumberDTO::getCourseId, CourseClickNumberDTO::getClickNumber, (a, b) -> a));
    }

    @Override
    public Integer getUserCourseHour(String userId, String courseId) {
        return baseMapper.getUserCourseHour(userId, courseId);
    }

    @Override
    public List<CourseDurationDTO> findUserCourseRecord(List<CourseDurationQuery> courseDurationQueryList) {
        List<CourseDurationDTO> userCourseRecordList = new ArrayList<>();
        for (CourseDurationQuery courseDurationQuery : courseDurationQueryList) {
            String userId = courseDurationQuery.getUserId();
            String courseId = courseDurationQuery.getCourseId();
            UserCourseRecord userCourseRecord = lambdaQuery().eq(UserCourseRecord::getUserId, userId)
                .eq(UserCourseRecord::getCourseId, courseId).one();

            CourseDurationDTO dto = new CourseDurationDTO();
            dto.setUserId(userId);
            dto.setCourseId(courseId);
            if (userCourseRecord != null) {
                dto.setDuration(userCourseRecord.getDuration());
            }
            userCourseRecordList.add(dto);
        }
        return userCourseRecordList;
    }

    @Override
    public List<String> getLearnedCourseIds(String userId, Collection<String> ids) {
        if (ids.size() > 0) {
            LambdaQueryWrapper<UserCourseRecord> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(UserCourseRecord::getUserId, userId);
            queryWrapper.eq(UserCourseRecord::getIsLearned, GeneralJudgeEnum.CONFIRM.getValue());
            queryWrapper.in(UserCourseRecord::getCourseId, ids);
            return list(queryWrapper).stream().map(UserCourseRecord::getCourseId).collect(Collectors.toList());
        }
        return List.of();
    }

    @Override
    public Map<String, Integer> getUserCourseHourByListParams(Collection<ResourceIdAndUserIdDTO> userIdAndContendId) {
        Map<String, Integer> map = new HashMap<>();
        for (ResourceIdAndUserIdDTO dto : userIdAndContendId) {
            Integer userCourseHour = getUserCourseHour(dto.getUserId(), dto.getResourceId());
            map.put(dto.getUserId() + dto.getResourceId(), userCourseHour);
        }
        return map;
    }

    @Override
    public Double getUserCourseDruation(String userId) {
        QueryWrapper<UserCourseRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.select("sum(duration) as duration");
        UserCourseRecord userCourseRecord = getOne(queryWrapper);
        if (!Objects.isNull(userCourseRecord)) {
            return Double.valueOf(userCourseRecord.getDuration());
        }
        return 0.00;
    }


    @Override
    public List<UserCourseStudyInfoDTO> getUserCourseStudyInfo(UserCourseStudyInfoQuery query) {
        if (StringUtil.isEmpty(query.getUserId()) || query.getIdList().isEmpty()) {
            return List.of();
        }
        return baseMapper.getUserCourseStudyInfo(query);
    }

    @Override
    public List<UserCoursewareStudyInfoDTO> getUserCoursewareStudyInfo(String courseId, String userId) {
        if (StringUtil.isEmpty(courseId) || StringUtil.isEmpty(userId)) {
            return List.of();
        }

        // 获取课件学习信息
        List<UserCoursewareStudyInfoDTO> userCoursewareStudyInfo = baseMapper.getUserCoursewareStudyInfo(courseId,
            userId);

        // 学时数据处理
        userCoursewareStudyInfo.forEach(dto -> {
            // 处理 duration
            BigDecimal duration = dto.getDuration();
            if (duration != null) {
                if (duration.compareTo(BigDecimal.valueOf(0.01)) >= 0) {
                    // 大于等于0.01，四舍五入
                    dto.setDuration(duration.setScale(2, RoundingMode.HALF_UP));
                } else {
                    // 小于0.01，截断为0
                    dto.setDuration(BigDecimal.ZERO);
                }
            }

            // 处理 totalDuration
            BigDecimal totalDuration = dto.getTotalDuration();
            if (totalDuration != null) {
                if (totalDuration.compareTo(BigDecimal.valueOf(0.01)) >= 0) {
                    // 大于等于0.01，四舍五入
                    dto.setTotalDuration(totalDuration.setScale(2, RoundingMode.HALF_UP));
                } else {
                    // 小于0.01，截断为0
                    dto.setTotalDuration(BigDecimal.ZERO);
                }
            }
        });
        return userCoursewareStudyInfo;
    }
}
