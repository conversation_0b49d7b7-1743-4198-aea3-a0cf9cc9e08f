package com.wunding.learn.course.service.consumer;

import com.rabbitmq.client.Channel;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.file.TranscodeStatusEnum;
import com.wunding.learn.common.mq.event.TransCodeFinishEvent;
import com.wunding.learn.common.mq.util.ConsumerAckUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.course.service.mapper.CoursewarePackageMapper;
import com.wunding.learn.course.service.model.Courseware;
import com.wunding.learn.course.service.model.CoursewarePackage;
import com.wunding.learn.course.service.service.ICoursewareService;
import com.wunding.learn.websocket.api.constant.MessageType;
import com.wunding.learn.websocket.api.dto.WsMsg;
import com.wunding.learn.websocket.api.service.WebSocketFeign;
import jakarta.annotation.Resource;
import java.io.IOException;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

/**
 * 课件转码完成消息队列消费者
 *
 * <AUTHOR>
 * @date 2022/7/1
 */
@Component
@Slf4j
public class CoursewareTransCodeFinishConsumer {

    /**
     * Routing Key
     */
    public static final String COURSEWARE_TRANS_CODE_FINISH_ROUTING_KEY = "CourseWareFile";

    /**
     * 课件转码完成消息队列
     */
    private static final String COURSEWARE_TRANS_CODE_FINISH_CONSUMER_QUEUE = "coursewareTransCodeFinishConsumerQueue";

    /**
     * 学员端上传转码回调
     */
    private static final String COURSE_WARE_PACKAGES_TRANS_CODE_FINISH_ROUTING_KEY = "CourseWarePackages";

    /**
     * 学员端上传转码完成消息队列
     */
    private static final String COURSEWARE_PACKAGES_TRANS_CODE_FINISH_CONSUMER_QUEUE = "courseWarePackagesTransCodeFinishConsumerQueue";

    @Resource
    private ICoursewareService coursewareService;

    @Resource
    private CoursewarePackageMapper coursewarePackageMapper;

    @Resource
    private WebSocketFeign webSocketFeign;

    /**
     * 课件转码完成回调通知处理器
     *
     * @param transCodeFinishEvent
     * @param deliveryTag
     * @param channel
     * @throws IOException
     */
    @RabbitListener(concurrency = "1", bindings = @QueueBinding(value = @Queue(value = COURSEWARE_TRANS_CODE_FINISH_CONSUMER_QUEUE), exchange = @Exchange(value = TransCodeFinishEvent.EXCHANGE, type = ExchangeTypes.TOPIC), key = COURSEWARE_TRANS_CODE_FINISH_ROUTING_KEY), id = "coursewareTransCodeFinishHandler")
    public void coursewareTransCodeFinishHandler(@Payload TransCodeFinishEvent transCodeFinishEvent,
        @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag, Channel channel) throws IOException {
        log.info("coursewareTransCodeFinishHandler transCodeFinishEvent: {}", JsonUtil.objToJson(transCodeFinishEvent));

        UserThreadContext.setTenantId(transCodeFinishEvent.getTenantId());

        // 课件转码状态回调更新
        if (transCodeFinishEvent.getStatus().equals(TranscodeStatusEnum.TRANSFORM_FAILED.value)) {
            // 发送转码失败websocket消息
            try {
                sendTransFailWsMsg(transCodeFinishEvent.getBizId(),
                    JsonUtil.objToJson(coursewareService.getById(transCodeFinishEvent.getBizId())));
            } catch (Exception e) {
                log.error("发送课件转码失败websocket消息异常，不阻塞仅输出日志处理: ", e);
            }
        }
        Courseware courseware = new Courseware();
        courseware.setId(transCodeFinishEvent.getBizId());
        courseware.setTransformStatus(transCodeFinishEvent.getStatus());
        courseware.setMime(transCodeFinishEvent.getMime());
//        courseware.setUpdateBy("");
        courseware.setUpdateTime(new Date());
        courseware.setTransformEndTime(new Date());
        courseware.setReason(transCodeFinishEvent.getReason());
        coursewareService.updateById(courseware);

        // 课件库转码状态回调更新
        if (StringUtils.isNotBlank(transCodeFinishEvent.getLibId())) {
//            if (transCodeFinishEvent.getStatus().equals(TranscodeStatusEnum.TRANSFORM_FAILED.value)) {
//                // 发送转码失败websocket消息
//                sendTransFailWsMsg(transCodeFinishEvent.getLibId(), JsonUtil.objToJson(coursewareService.getById(transCodeFinishEvent.getLibId())));
//            }
            Courseware coursewareLib = new Courseware();
            coursewareLib.setId(transCodeFinishEvent.getLibId());
            coursewareLib.setTransformStatus(transCodeFinishEvent.getStatus());
            coursewareLib.setMime(transCodeFinishEvent.getMime());
//            coursewareLib.setUpdateBy("");
            coursewareLib.setUpdateTime(new Date());
            coursewareLib.setTransformEndTime(new Date());
            coursewareLib.setReason(transCodeFinishEvent.getReason());
            coursewareService.updateById(coursewareLib);
        }

        UserThreadContext.remove();
        ConsumerAckUtil.basicAck(transCodeFinishEvent, channel, deliveryTag, false);
    }

    private void sendTransFailWsMsg(String coursewareId, String message) {
        Courseware courseware = coursewareService.getById(coursewareId);
        if (courseware == null) {
            return;
        }
        sengToCreateBy(coursewareId, message, courseware);
        sengToUpdateBy(coursewareId, message, courseware);
    }

    private void sengToUpdateBy(String coursewareId, String message, Courseware courseware) {
        WsMsg wsMsg = new WsMsg();
        wsMsg.setMessageType(MessageType.COURSEWARE_TRANSFORM_CODE_FAIL.getType());
        wsMsg.setBizId(coursewareId);
        wsMsg.setMessage(message);
        wsMsg.setTenantId(UserThreadContext.getTenantId());
        wsMsg.setSendUserId(courseware.getUpdateBy());
        // 肯定要发送给创建人。但如果创建人和更新人是同一个人，只需发送一次。
        if (!courseware.getUpdateBy().equals(courseware.getCreateBy())) {
            log.info("sendCoursewareTransformCodeFailWsMsg:{}", JsonUtil.objToJson(wsMsg));
            webSocketFeign.syncSend(wsMsg);
        }
    }

    private void sengToCreateBy(String coursewareId, String message, Courseware courseware) {
        WsMsg wsMsg = new WsMsg();
        wsMsg.setMessageType(MessageType.COURSEWARE_TRANSFORM_CODE_FAIL.getType());
        wsMsg.setBizId(coursewareId);
        wsMsg.setMessage(message);
        wsMsg.setTenantId(UserThreadContext.getTenantId());
        wsMsg.setSendUserId(courseware.getCreateBy());

        log.info("sendCoursewareTransformCodeFailWsMsg:{}", JsonUtil.objToJson(wsMsg));
        webSocketFeign.syncSend(wsMsg);
    }

    /**
     * 课件转码完成回调通知处理器
     *
     * @param transCodeFinishEvent
     * @param deliveryTag
     * @param channel
     * @throws IOException
     */
    @RabbitListener(concurrency = "1", bindings = @QueueBinding(value = @Queue(value = COURSEWARE_PACKAGES_TRANS_CODE_FINISH_CONSUMER_QUEUE), exchange = @Exchange(value = TransCodeFinishEvent.EXCHANGE, type = ExchangeTypes.TOPIC), key = COURSE_WARE_PACKAGES_TRANS_CODE_FINISH_ROUTING_KEY), id = "courseWarePackagesTransCodeFinishHandler")
    public void courseWarePackagesTransCodeFinishHandler(@Payload TransCodeFinishEvent transCodeFinishEvent,
        @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag, Channel channel) throws IOException {
        log.info("courseWarePackagesTransCodeFinishHandler transCodeFinishEvent: {}",
            JsonUtil.objToJson(transCodeFinishEvent));

        UserThreadContext.setTenantId(transCodeFinishEvent.getTenantId());

        // 课件转码状态回调更新
        CoursewarePackage coursewarePackage = new CoursewarePackage();
        coursewarePackage.setId(transCodeFinishEvent.getBizId());
        coursewarePackage.setTransformStatus(transCodeFinishEvent.getStatus());
        coursewarePackage.setMime(transCodeFinishEvent.getMime());
        coursewarePackage.setUpdateBy("");
        coursewarePackage.setUpdateTime(new Date());
        coursewarePackageMapper.updateById(coursewarePackage);
        UserThreadContext.remove();
        ConsumerAckUtil.basicAck(transCodeFinishEvent, channel, deliveryTag, false);
    }

}
