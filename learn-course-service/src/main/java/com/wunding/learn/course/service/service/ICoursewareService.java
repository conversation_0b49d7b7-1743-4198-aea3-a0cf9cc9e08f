package com.wunding.learn.course.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.ai.outline.dto.SaveOrUpdateAiOutlineItemDTO;
import com.wunding.learn.common.ai.outline.dto.SaveAiOutlineDTO;
import com.wunding.learn.common.dto.LecturerCoursewareDetailDTO;
import com.wunding.learn.course.api.dto.CourseInfoDTO;
import com.wunding.learn.course.api.dto.CourseWareInfoApiDTO;
import com.wunding.learn.course.api.dto.CourseWareNameDetailDTO;
import com.wunding.learn.course.api.dto.SaveCwLibraryDTO;
import com.wunding.learn.course.api.query.CourseWareSummaryQuery;
import com.wunding.learn.course.service.admin.dto.AddAllCourseWareDTO;
import com.wunding.learn.course.service.admin.dto.AiGeneratedContentDTO;
import com.wunding.learn.course.service.admin.dto.AiQuestionDTO;
import com.wunding.learn.course.service.admin.dto.AiSwitchConfigDTO;
import com.wunding.learn.course.service.admin.dto.CourseWareDTO;
import com.wunding.learn.course.service.admin.dto.CourseWareLearnListDTO;
import com.wunding.learn.course.service.admin.dto.CourseWareLibraryDTO;
import com.wunding.learn.course.service.admin.dto.CourseWareQuestionDTO;
import com.wunding.learn.course.service.admin.dto.CoursewareListDTO;
import com.wunding.learn.course.service.admin.dto.CoursewareTransformFailDTO;
import com.wunding.learn.course.service.admin.dto.CwQuestionAnswerRecordDTO;
import com.wunding.learn.course.service.admin.dto.EditCwLibraryDTO;
import com.wunding.learn.course.service.admin.dto.ReGenerateDTO;
import com.wunding.learn.course.service.admin.dto.SaveCwDescDTO;
import com.wunding.learn.course.service.admin.dto.courseware.BatchCourseWareDTO;
import com.wunding.learn.course.service.admin.dto.courseware.CourseWareAiDTO;
import com.wunding.learn.course.service.admin.dto.courseware.CourseWareDetailDTO;
import com.wunding.learn.course.service.admin.dto.courseware.CoursewareReTranscodingDTO;
import com.wunding.learn.course.service.admin.dto.courseware.PriorityTranscodingDTO;
import com.wunding.learn.course.service.admin.dto.courseware.SaveCourseWareDTO;
import com.wunding.learn.course.service.admin.dto.courseware.UpdateCourseWareDTO;
import com.wunding.learn.course.service.admin.query.CourseWareLearnQuery;
import com.wunding.learn.course.service.admin.query.CourseWareLibraryQuery;
import com.wunding.learn.course.service.admin.query.CoursewareQuery;
import com.wunding.learn.course.service.admin.query.CoursewareTransformFailQuery;
import com.wunding.learn.course.service.admin.query.CwQuestionAnswerRecordQuery;
import com.wunding.learn.course.service.client.dto.AnswerCourseWareQuestionDTO;
import com.wunding.learn.course.service.client.dto.AnswerResultDTO;
import com.wunding.learn.course.service.client.dto.CourseWareInfoDTO;
import com.wunding.learn.course.service.client.dto.CourseWareInfoQuery;
import com.wunding.learn.course.service.client.dto.ProjectTaskCoursewareSaveDTO;
import com.wunding.learn.course.service.client.query.CourseWareIsLearnedQueryDTO;
import com.wunding.learn.course.service.model.Courseware;
import com.wunding.learn.exam.api.dto.ViewExamFeignDTO;
import com.wunding.learn.user.api.dto.ParaDTO;
import com.wunding.learn.user.api.dto.UserDTO;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import org.springframework.scheduling.annotation.Async;

/**
 * <p> 课件表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">余学益</a>
 * @date 2022-05-10
 */
public interface ICoursewareService extends IService<Courseware> {

    /**
     * 后台-课件学习明细
     *
     * @param coursewareLearnQuery 参数对象
     * @return 课件学习明细列表
     */
    PageInfo<CourseWareLearnListDTO> getUserDetailData(CourseWareLearnQuery coursewareLearnQuery);

    /**
     * 课件分页查询
     *
     * @param coursewareQuery 查询对象
     * @return 课件列表
     */
    PageInfo<CourseWareDTO> findCourseWareWareListByPage(CoursewareQuery coursewareQuery);


    /**
     * 分页查询讲师课件
     *
     * @param coursewareQuery 查询对象
     * @return 课件列表
     */
    PageInfo<LecturerCoursewareDetailDTO> findLecturerCourseWareWareListByPage(CourseWareSummaryQuery coursewareQuery);

    /**
     * 根据开始结束时间、课程名称导出用户创建的课件
     *
     * @param coursewareQuery
     * @return
     */
    void exportCourseWareData(CourseWareSummaryQuery coursewareQuery);

    /**
     * 保存课件
     *
     * @param saveCourseWareDTO 保存课件
     */
    void saveCourseWare(SaveCourseWareDTO saveCourseWareDTO);

    /**
     * 更新课件
     *
     * @param updateCourseWareDTO 更新课件对象
     */
    void updateCourseWare(UpdateCourseWareDTO updateCourseWareDTO);

    /**
     * 查询课件详情
     *
     * @param id 课件id
     * @return 课件详情
     */
    CourseWareDetailDTO getCourseWareById(String id);

    /**
     * 删除课件
     *
     * @param ids 课件id
     */
    void delCourseWareById(String ids);

    /**
     * 更新可见课程关键字
     *
     * @param idsList
     */
    void updateCourseCwKeyword(List<String> idsList);

    /**
     * 修改课件显示顺序
     *
     * @param id         课件id
     * @param changeSort 排序
     */
    void changeSort(String id, Integer changeSort);

    /**
     * 获取某单一课件详情信息
     *
     * @param query
     * @return CourseWareInfoDTO
     */
    CourseWareInfoDTO getCourseWareInfo(CourseWareInfoQuery query);

    /**
     * 获取课程课件列表
     *
     * @param id 课程主键
     * @return 课件数据List
     */
    List<CoursewareListDTO> getCourseWareList(String id);

    /**
     * 获取课件考试详情
     *
     * @param courseWareIsLearnedQueryDTO
     * @return
     */
    ViewExamFeignDTO courseWareIsLearned(CourseWareIsLearnedQueryDTO courseWareIsLearnedQueryDTO);

    /**
     * 获取课件库列表详情
     *
     * @param courseWareLibraryQuery 查询对象
     * @return 课件库列表详情
     */
    PageInfo<CourseWareLibraryDTO> getCwLibraryListData(CourseWareLibraryQuery courseWareLibraryQuery);

    /**
     * 删除入库课件
     *
     * @param ids 课件ids
     */
    void deleteCoursewareLibrary(String ids);

    /**
     * 启用/禁用课件
     *
     * @param ids         课件ids
     * @param isAvailable 操作类型
     */
    void isAvailable(String ids, Integer isAvailable);

    /**
     * 课件入库存储
     *
     * @param saveCwLibraryDTO 课件对象
     */
    String coursewareStorage(SaveCwLibraryDTO saveCwLibraryDTO);

    /**
     * 课件入库存储
     *
     * @param saveCwLibraryDTO 课件对象
     */
    String coursewareStorageJob(SaveCwLibraryDTO saveCwLibraryDTO);

    /**
     * 编辑课件
     *
     * @param editCwLibraryDTO 编辑对象
     */
    void edit(EditCwLibraryDTO editCwLibraryDTO);


    /**
     * 下载课件文件
     */
    void downloadCourseWare(HttpServletRequest request, HttpServletResponse response, String cwId);

    /**
     * 将未入库的课件入库
     *
     * @param ids
     */
    void storage(String ids);


    /**
     * 课程批量添加课件
     *
     * @param addAllCourseWareDTO
     */
    void addAllCourseWare(AddAllCourseWareDTO addAllCourseWareDTO);

    /**
     * 导出课件学习明细列表
     */
    @Async
    void exportData(CourseWareLearnQuery coursewareLearnQuery);

    /**
     * 导出课件库列表的数据
     *
     * @param courseWareLibraryQuery
     * @return
     */
    @Async
    void exportLib(CourseWareLibraryQuery courseWareLibraryQuery);

    /**
     * 获取课件默认时长
     *
     * @return
     */
    String getPlayTime();

    /**
     * 讲师工作台-批量创建课件
     *
     * @param userInfo
     * @param courseId
     * @param coursewareSaveList
     */
    void saveCourseWareList(UserDTO userInfo, String courseId, List<ProjectTaskCoursewareSaveDTO> coursewareSaveList);

    /**
     * 引用课件
     */
    String saveLibCourseWare(CourseWareInfoApiDTO dto);

    /**
     * 上传课件
     *
     * @param dto
     * @return
     */
    String saveFileCourseWare(CourseWareInfoApiDTO dto);

    /**
     * 投票入库
     *
     * @param id
     * @return
     */
    String voteCourseWare2Lib(String id);

    /**
     * 获取课程配置
     *
     * @return
     */
    List<ParaDTO> getCoursePara();

    /**
     * 根据课件ID返回课程ID
     *
     * @param cwId
     * @return
     */
    String getCourseByCwId(String cwId);

    /**
     * 根据课件ID集合返回课程ID集合
     *
     * @param cwIds
     * @return
     */
    List<String> getCourseIdsByCwIds(Collection<String> cwIds);

    /**
     * 课件重新转码
     *
     * @param coursewareReTranscodingDTO
     */
    void reTranscoding(CoursewareReTranscodingDTO coursewareReTranscodingDTO);

    /**
     * 获取用户创建的课件
     *
     * @param userId
     * @return
     */
    List<com.wunding.learn.course.api.dto.CourseWareDTO> getCourseWareByUserId(String userId);

    /**
     * 按课件id查课件(包括删除的课件)
     *
     * @param cwIds 课件id
     * @return 课件列表
     */
    List<CourseWareNameDetailDTO> getCourseWareNameDetailByCwIds(Collection<String> cwIds);

    /**
     * 获取课程下课时总数
     *
     * @param courseId
     * @return
     */
    BigDecimal getTotalCoursewareDuration(String courseId);

    /**
     * 课件重新转码
     */
    void reTranscoding();

    List<CourseWareDTO> findCourseWareListByCourseId(String courseId);

    List<CourseWareQuestionDTO> findQuestionListByCourseWareId(String courseWareId);

    PageInfo<CwQuestionAnswerRecordDTO> queryCoursewareQuestionAnswerRecordList(CwQuestionAnswerRecordQuery query);

    AnswerResultDTO answerCourseWareQuestion(AnswerCourseWareQuestionDTO answerCourseWareQuestionDTO);

    void exportCoursewareQuestionAnswerRecordList(CwQuestionAnswerRecordQuery query);

    AiSwitchConfigDTO aiSwitch();

    /**
     * 获取Ai生成的内容
     *
     * @param cwId 课件id
     * @return AI生成的内容
     */
    AiGeneratedContentDTO aiGeneratedContent(String cwId);

    /**
     * 保存摘要
     *
     * @param saveCwDescDTO 摘要信息
     */
    void saveCwDesc(SaveCwDescDTO saveCwDescDTO);

    /**
     * AI重新生成
     *
     * @param reGenerate 参数对象
     */
    Long generate(ReGenerateDTO reGenerate, String type);

    /**
     * 获取课件AI生成内容状态
     *
     * @param cwId 课件id
     * @param type AI生成类型
     * @return 课件AI生成内容状态
     */
    Integer getGenerateStatus(String cwId, String type);

    /**
     * 根据课件id获取所属课程的信息
     *
     * @param courseWareId 课件id
     * @return
     */
    CourseInfoDTO getCourseByCourseWareId(String courseWareId);

    /**
     * 根据课件id集合获取课件id和课程的Map
     *
     * @param courseWareIds 课件id集合
     * @return
     */
    Map<String, CourseInfoDTO> getCoursesByCourseWareIds(List<String> courseWareIds);

    /**
     * 通过课件id查询课件信息并校验
     *
     * @param id 课件id
     * @return {@link Courseware}
     */
    Courseware get(String id);

    /**
     * 课件综合评星星级冗余同步
     *
     * @param resourceId 课件id
     */
    void updateCoursewareCommonStar(String resourceId);

    /**
     * 课件浏览量冗余同步
     *
     * @param resourceId 课件id
     */
    void updateCoursewareClickNumber(String resourceId);

    /**
     * 添加课件库下载记录
     *
     * @param id 知识库材料ID
     */
    void addDownloadRecord(String id);

    /**
     * 批量上传课件
     *
     * @param batchCourseWareDTO
     */
    void saveCourseWareBatch(BatchCourseWareDTO batchCourseWareDTO);

    /**
     * 保存课件题目
     *
     * @param cwId 课件id
     * @param list {@link AiQuestionDTO}
     */
    void saveCourseCwAiQuestion(String cwId, List<AiQuestionDTO> list);

    /**
     * 通过dify根据AI课程配置生成课程资源
     *
     * @param reGenerate {@link ReGenerateDTO}
     * @param type       ai类型
     */
    void difyGenerate(ReGenerateDTO reGenerate, String type);

    /**
     * 课件ai文件列表
     *
     * @param coursewareQuery {@link CoursewareQuery}
     * @return {@link CourseWareAiDTO}
     */
    List<CourseWareAiDTO> getCoursewareAiList(CoursewareQuery coursewareQuery);

    Long selectCoursewareNum(LambdaQueryWrapper<Courseware> countCourseware);

    /**
     * 课件转码失败列表，不考虑下发只筛选自己创建的
     */
    PageInfo<CoursewareTransformFailDTO> transformFailList(CoursewareTransformFailQuery query);

    /**
     * 将案例文件转为课件文件
     *
     * @param courseWareInfoApiDTO {@link CourseWareInfoApiDTO}
     * @return 课件id
     */
    String saveExampleToCourseWare(CourseWareInfoApiDTO courseWareInfoApiDTO);

    /**
     * 保存课件大纲
     *
     * @param saveAiOutlineDTO 大纲信息
     */
    void saveCwOutline(SaveAiOutlineDTO saveAiOutlineDTO);

    /**
     * 保存课件大纲条目
     *
     * @param saveOrUpdateAiOutlineItemDTO 大纲信息
     */
    void saveOrUpdateCwOutlineItem(SaveOrUpdateAiOutlineItemDTO saveOrUpdateAiOutlineItemDTO);

    /**
     * 删除大纲条目
     *
     * @param ids
     */
    void deleteCwOutlineItem(String ids);

    /**
     * 课件优先转码
     *
     */
    void priorityTranscoding(PriorityTranscodingDTO priorityTranscodingDTO);
}
