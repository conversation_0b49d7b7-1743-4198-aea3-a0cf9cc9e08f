package com.wunding.learn.course.service.admin.rest;

import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.bean.Result;
import com.wunding.learn.course.api.dto.UserCoursewareStudyInfoDTO;
import com.wunding.learn.course.service.admin.dto.CourseLearnDetailDTO;
import com.wunding.learn.course.service.admin.query.CourseLearnQuery;
import com.wunding.learn.course.service.service.ICourseService;
import com.wunding.learn.course.service.service.impl.UserCourseRecordServiceImpl;
import com.wunding.learn.file.api.dto.ExportResultDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: aixinrong
 * @Date: 2022/5/12 13:28
 */
@RestController
@RequestMapping("${module.course.contentPath:/}courseLearn")
@Tag(description = "学员课程学习管理", name = "CourseLearnRest")
public class CourseLearnRest {

    @Resource
    ICourseService courseService;
    @Resource
    private UserCourseRecordServiceImpl userCourseRecordService;

    @GetMapping("/getUserDetailData")
    @Operation(operationId = "getUserDetailData", summary = "课程学习明细", description = "课程学习明细")
    public Result<PageInfo<CourseLearnDetailDTO>> getUserDetailData(
        @ParameterObject @Valid CourseLearnQuery courseLearnQuery) {
        PageInfo<CourseLearnDetailDTO> data = courseService.getUserDetailData(courseLearnQuery);
        return Result.success(data);
    }

    /**
     *  导出课程学习明细列表
     */
    @PostMapping("/courseLearnExportData")
    @Operation(operationId = "courseLearnExportData", summary = "导出课程学习明细列表", description = "导出课程学习明细列表")
    public Result<ExportResultDTO> courseLearnExportData(CourseLearnQuery courseLearnQuery) {
        courseService.courseLearnExportData(courseLearnQuery);
        return Result.success();
    }

    @GetMapping("/getUserCoursewareStudyInfo/{courseId}/{userId}")
    @Operation(operationId = "getUserCoursewareStudyInfo", summary = "用户指定课程课件学习明细", description = "用户指定课程课件学习明细")
    public Result<List<UserCoursewareStudyInfoDTO>> getUserCoursewareStudyInfo(
        @Parameter(description = "课程ID", required = true) @PathVariable String courseId,
        @Parameter(description = "用户ID", required = true) @PathVariable String userId
    ) {
        List<UserCoursewareStudyInfoDTO> data = userCourseRecordService.getUserCoursewareStudyInfo(courseId, userId);
        return Result.success(data);
    }

}
