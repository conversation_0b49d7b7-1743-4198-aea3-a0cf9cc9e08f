package com.wunding.learn.course.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/6/15 13:37
 */
@Data
@Schema(name = "EditCwLibraryDTO", description = "课件入库编辑对象")
public class EditCwLibraryDTO implements Serializable {

    public static final long serialVersionUID = 1L;

    @Schema(description = "课件id", required = true)
    private String id;

    @Schema(description = "课件名称")
    private String cwName;

    @Schema(description = "资源库类型id")
    private String libraryCateId;

    @Schema(description = "创建、归属部门Id")
    private String orgId;

    @Schema(description = "课件时长")
    private Integer playTime;
}
