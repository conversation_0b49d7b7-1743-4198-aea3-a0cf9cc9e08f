package com.wunding.learn.course.service.client.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: MyCourseWareDTO
 * @projectName: mlearn
 * @description：
 * @date 2022/6/1 9:51
 */
@Data
@Schema(name = "MyCourseWareDTO", description = "获取我的课件列表返回对象")
public class MyCourseWareDTO {

    @Schema(description = "主键id WE微课件Id")
    private String id;

    @Schema(description = "课件名称")
    private String cwName;

    @Schema(description = "mime类型")
    private String mime;

    @Schema(description = "课件类型 Word-WORD,PPT-PPT,PDF-PDF,Video-视频,Audio-音频,Scorm-ZIP,Pic-ZIP,Text-图文")
    private String cwType;

    @Schema(description = "视频转换状态  1-转码中 2-转码成功 3-转码失败 4-排队中")
    private Integer transformStatus;

    @Schema(description = "课件简介")
    private String remark;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "审核状态0 审核中 1成功 2失败")
    private Integer auditState;

    @Schema(description = "课程地址")
    private String courseUrl;

    @Schema(description = "图片地址")
    private String picUrl;


}
