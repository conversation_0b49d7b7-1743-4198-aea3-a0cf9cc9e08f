package com.wunding.learn.course.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import com.wunding.learn.course.service.model.CoursePushTask;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 课程推送任务表 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">余学益</a>
 * @date 2022-05-10
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")
})
public interface CoursePushTaskMapper extends BaseMapper<CoursePushTask> {

}
