package com.wunding.learn.course.service.admin.rest;


import com.wunding.learn.common.bean.Result;
import com.wunding.learn.common.constant.other.DirectoryErrorNoEnum;
import com.wunding.learn.common.directory.dto.DirectoryInfoDTO;
import com.wunding.learn.common.directory.dto.SaveOrUpdateDirectoryDTO;
import com.wunding.learn.common.directory.dto.SaveOrUpdateResourceDirectoryDTO;
import com.wunding.learn.common.directory.query.DirectoryAllQuery;
import com.wunding.learn.common.directory.query.DirectoryQuery;
import com.wunding.learn.common.directory.service.IDirectoryService;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.course.service.admin.dto.TrainCourseListDTO;
import com.wunding.learn.course.service.admin.query.TrainCourseQuery;
import com.wunding.learn.course.service.service.ICourseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: aixinrong
 * @Date: 2023/3/02 10:10
 */
@RestController
@RequestMapping("${module.course.contentPath:/}resourceDirectory")
@Tag(description = "资源目录管理", name = "CourseResourceDirectoryRest")
@Validated
public class CourseResourceDirectoryRest {

    @Resource
    private IDirectoryService directoryService;

    @Resource
    private ICourseService courseService;

    @PostMapping("/saveOrUpdateDirectory")
    @Operation(operationId = "saveOrUpdateDirectory", summary = "添加/更新目录", description = "添加/更新目录")
    public Result<Void> saveOrUpdateDirectory(@RequestBody @Valid SaveOrUpdateDirectoryDTO saveOrUpdateDirectoryDTO) {
        directoryService.saveOrUpdateDirectory(saveOrUpdateDirectoryDTO);
        return Result.success();
    }

    @DeleteMapping("/deleteDirectory")
    @Operation(operationId = "deleteDirectory", summary = "删除目录", description = "删除目录")
    public Result<Void> deleteDirectory(
        @Parameter(description = "目录ids(用,分割)") @RequestParam @NotBlank(message = "目录ids不可为空") String ids) {
        //校验资源目录是否包含资源
        TrainCourseQuery trainCourseQuery = new TrainCourseQuery();
        trainCourseQuery.setDirectoryId(ids);
        List<TrainCourseListDTO> resourceList = courseService.getTrainCourseByDirectoryId(trainCourseQuery);
        if(!CollectionUtils.isEmpty(resourceList)){
            throw new BusinessException(DirectoryErrorNoEnum.ERR_HAVE_RESOURCE_DIRECTORY);
        }
        directoryService.deleteDirectory(ids);
        return Result.success();
    }

    @GetMapping("/getDirectoryInfo/{id}")
    @Operation(operationId = "getDirectoryInfo", summary = "获取单个目录", description = "获取单个目录")
    public Result<DirectoryInfoDTO> getDirectoryInfo(@Parameter(description = "目录ID") @PathVariable String id) {
        return Result.success(directoryService.getDirectoryInfo(id));
    }

    @GetMapping("/getDirectoryList")
    @Operation(operationId = "getDirectoryList", summary = "获取目录-每次获取一层目录", description = "获取目录-每次获取一层目录")
    public Result<List<DirectoryInfoDTO>> getDirectoryList(@Valid @ParameterObject DirectoryQuery directoryQuery) {
        return Result.success(directoryService.getDirectoryList(directoryQuery));
    }

    @GetMapping("/getAllDirectory")
    @Operation(operationId = "getAllDirectory", summary = "获取全部目录", description = "获取全部目录")
    public Result<List<DirectoryInfoDTO>> getAllDirectory(@Valid @ParameterObject DirectoryAllQuery directoryAllQuery) {
        return Result.success(directoryService.getAllDirectory(directoryAllQuery));
    }

    @PostMapping("/saveOrUpdateResourceDirectory")
    @Operation(operationId = "saveOrUpdateResourceDirectory", summary = "添加/更新资源目录关联", description = "添加/更新资源目录关联")
    public Result<Void> saveOrUpdateResourceDirectory(
        @RequestBody @Valid SaveOrUpdateResourceDirectoryDTO saveOrUpdateResourceDirectoryDTO) {
        directoryService.saveOrUpdateResourceDirectory(saveOrUpdateResourceDirectoryDTO);
        return Result.success();
    }
}
