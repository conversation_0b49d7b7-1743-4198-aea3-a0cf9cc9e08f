package com.wunding.learn.course.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.common.dto.ResourceIdAndUserIdDTO;
import com.wunding.learn.course.api.dto.CourseDurationDTO;
import com.wunding.learn.course.api.dto.UserCourseStudyInfoDTO;
import com.wunding.learn.course.api.dto.UserCoursewareStudyInfoDTO;
import com.wunding.learn.course.api.query.CourseDurationQuery;
import com.wunding.learn.course.api.query.UserCourseStudyInfoQuery;
import com.wunding.learn.course.service.admin.dto.CourseLearnDetailDTO;
import com.wunding.learn.course.service.admin.dto.CourseStudyDetailDTO;
import com.wunding.learn.course.service.admin.query.CourseLearnQuery;
import com.wunding.learn.course.service.admin.query.CourseStudyQuery;
import com.wunding.learn.course.service.admin.query.CourseStudyStatisticsQuery;
import com.wunding.learn.course.service.model.UserCourseRecord;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p> 用户课程学习记录 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">余学益</a>
 * @date 2022-05-10
 */
public interface IUserCourseRecordService extends IService<UserCourseRecord> {

    /**
     * 查询课程学习详情
     *
     * @param courseLearnQuery
     * @return
     */
    List<CourseLearnDetailDTO> getCourseLearnDetail(CourseLearnQuery courseLearnQuery);

    /**
     * 根据courseId获取相应的相应的count 这里需要实现缓存
     *
     * @param courseIdList
     * @return
     */
    Map<String, Integer> selectCountByCourseIdList(List<String> courseIdList);

    /**
     * 课程管理-课程学习明细
     *
     * @param courseStudyQuery
     * @return
     */
    List<CourseStudyDetailDTO> getCourseStudyDetail(CourseStudyQuery courseStudyQuery);

    /**
     * 课程统计-课程学习明细
     *
     * @param query
     * @return
     */
    List<CourseStudyDetailDTO> courseStudyStatisticsDetail(CourseStudyStatisticsQuery query);


    /**
     * 获取用户在某个课程学习的总学时
     *
     * @param userId
     * @param courseId
     * @return
     */
    Integer getUserCourseHour(String userId, String courseId);


    /**
     * 查询用户的课程学习记录
     *
     * @param courseDurationQueryList
     * @return
     */
    List<CourseDurationDTO> findUserCourseRecord(List<CourseDurationQuery> courseDurationQueryList);


    /**
     * 获取该用户已学课程id
     *
     * @param userId 用户id
     * @param ids    课程id集合
     * @return {@link List}<{@link String}>
     */
    List<String> getLearnedCourseIds(String userId, Collection<String> ids);

    /**
     *
     * @param userIdAndContendId
     * @return
     */
    Map<String, Integer> getUserCourseHourByListParams(Collection<ResourceIdAndUserIdDTO> userIdAndContendId);

    /**
     * 获取用户学时
     *
     * @param userId
     * @return
     */
    Double getUserCourseDruation(String userId);

    /**
     * 查询用户指定课程学习时长统计数据（已学时长、课程总计时长）
     */
    List<UserCourseStudyInfoDTO> getUserCourseStudyInfo(UserCourseStudyInfoQuery query);

    /**
     * 查询用户指定课程下课件学习时长统计数据（已学时长、课程总计时长）
     */
    List<UserCoursewareStudyInfoDTO> getUserCoursewareStudyInfo(String courseId, String userId);
}
