<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.course.service.mapper.CourseViewDurationMapper">
    <!-- 开启二级缓存 -->
    <!--
<cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.course.service.mapper.CourseViewDurationMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.course.service.model.CourseViewDuration">
        <!--@Table course_view_duration-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="course_id" jdbcType="VARCHAR"
          property="courseId"/>
        <result column="cw_id" jdbcType="VARCHAR"
          property="cwId"/>
        <result column="view_by" jdbcType="VARCHAR"
          property="viewBy"/>
        <result column="view_time" jdbcType="TIMESTAMP"
          property="viewTime"/>
        <result column="end_time" jdbcType="TIMESTAMP"
          property="endTime"/>
        <result column="is_offline" jdbcType="TINYINT"
          property="isOffline"/>
        <result column="duration" jdbcType="INTEGER"
          property="duration"/>
        <result column="progress" jdbcType="DECIMAL"
          property="progress"/>
        <result column="org_id" jdbcType="VARCHAR"
          property="orgId"/>
        <result column="is_source" jdbcType="TINYINT"
          property="isSource"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, course_id, cw_id, view_by, view_time, end_time, is_offline, duration, progress, org_id, is_source
    </sql>

    <select id="sumCourseStudyTime"
      resultType="com.wunding.learn.course.service.client.dto.CourseViewDurationClientDTO">
        select ifnull(sum(duration), 0) duration, ifnull(max(progress), 0) progress
        from course_view_duration
        where course_id = #{params.courseId}
          and view_by = #{params.viewBy}
          and cw_id = #{params.cwId}
    </select>

    <select id="getNewProgress" resultType="java.lang.Integer">
        select t.progress
        from (select row_number() over (order by end_time desc) as rnum, progress
              from course_view_duration
              where course_id = #{params.courseId}
                and view_by = #{params.viewBy}
                and cw_id = #{params.cwId}) t
        where t.rnum = 1
    </select>

    <select id="selectUserCourseViewDurationList"
      resultType="com.wunding.learn.course.service.model.CourseViewDuration">
        select *
        from course_view_duration
        where 1=1
        <if test="userId != null and userId != ''">
            and view_by = #{userId}
        </if>
        <if test="null != startTime ">
            and date_format(end_time,'%Y-%m-%d') &gt;= date_format(#{startTime},'%Y-%m-%d')
        </if>
        <if test="null != endTime">
            and date_format(end_time,'%Y-%m-%d') &lt;= date_format(#{endTime},'%Y-%m-%d')
        </if>
        order by end_time desc
    </select>

    <select id="selectUserCourseViewDuration" resultType="java.lang.Long">
        select
        ifnull(sum(duration), 0)
        from course_view_duration
        where 1=1
        <if test="userId != null and userId != ''">
            and view_by = #{userId}
        </if>
        <if test="null != startTime ">
            and date_format(end_time,'%Y-%m-%d') &gt;= date_format(#{startTime},'%Y-%m-%d')
        </if>
        <if test="null != endTime">
            and date_format(end_time,'%Y-%m-%d') &lt;= date_format(#{endTime},'%Y-%m-%d')
        </if>
    </select>

    <select id="sumCourseStudyTimeToMap"
            resultType="com.wunding.learn.course.service.client.dto.CourseViewDurationDTO">
        select
            concat(course_id,'_',courseware_id,'_',user_id) as mapKey,
            is_learned,
            duration,
            progress
        from courseware_user_record
        <where>
            <if test="paramList != null and paramList.size() > 0">
                <foreach collection="paramList" item="params" open="(" close=")" separator="or">
                    (
                    course_id = #{params.courseId}
                    and user_id = #{params.viewBy}
                    and courseware_id = #{params.cwId}
                    )
                </foreach>
            </if>
        </where>
    </select>

    <select id="getCourseDurationMap" resultType="com.wunding.learn.course.api.dto.CourseDurationDTO" useCache="false">
        select
            user_id as userId,
            ifnull(sum(duration), 0) as duration
        from
            user_course_record
        where
            is_del = 0
          and user_id in <foreach collection="userIds" item="userId" open="(" close=")" separator=",">#{userId}</foreach>
          and course_id in <foreach collection="courseIds" item="courseId" open="(" close=")" separator=",">#{courseId}</foreach>
        group by
            user_id;
    </select>

</mapper>
