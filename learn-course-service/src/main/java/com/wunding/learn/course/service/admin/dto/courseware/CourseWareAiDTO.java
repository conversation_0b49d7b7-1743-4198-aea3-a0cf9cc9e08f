package com.wunding.learn.course.service.admin.dto.courseware;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

/**
 * 课件新增和保存时的公共参数
 *
 * <AUTHOR> href="mailto:<EMAIL>">yuxueyi</a>
 * @version V7.0.0
 * @since 2022/6/6  14:40
 */
@Data
@ToString(callSuper = true)
@Schema(name = "CourseWareAiDTO", description = "课件ai对象")
public class CourseWareAiDTO {

    @Schema(description = "主键id")
    private String id;

    @Schema(description = "课件名称")
    private String cwName;

    @Schema(description = "视频转换状态  1-转码中 2-转码成功 3-转码失败 4-排队中")
    private Integer transformStatus;

    @Schema(description = "课件创建方式： 0：在线做课，1：上传课件，2:资源库的课件，3：引用课件 8:待入库课件  9学员上传课件,10课件模板")
    private Integer isSource;

    @Schema(description = "显示顺序")
    private Integer sortNo;

    @Schema(description = "课程章节名称")
    private String chapterName;

    @Schema(description = "课件源文件类型")
    private String sourceFileType;

    @Schema(description = "课件用于ai生成文件地址,转码完成的才有地址")
    private String aiFileUrl;

    @Schema(description = "课件用于ai生成文件类型，截取文件后缀")
    private String aiFileType;

    @Schema(description = "向量化状态：0-已纳入问答，向量化中，1-已纳入问答，向量化完成，2-未纳入问答")
    private Integer indexingStatus;
}
