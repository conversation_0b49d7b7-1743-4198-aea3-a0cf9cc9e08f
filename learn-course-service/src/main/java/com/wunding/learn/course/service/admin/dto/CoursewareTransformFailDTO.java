package com.wunding.learn.course.service.admin.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;

@Data
@Schema(name = "CoursewareTransformFailDTO", description = "课程转码失败列表对象")
public class CoursewareTransformFailDTO {

    @Schema(description = "课件id")
    private String cwId;

    @Schema(description = "课件名称")
    private String cwName;

    @Schema(description = "课程id")
    private String courseId;

    @Schema(description = "课程名称")
    private String courseName;

    @Schema(description = "课程编号")
    private String courseNo;

    @Schema(description = "是否内置课程。0-否，1-是。内置课程可以赠送给新租户使用。")
    private Integer buildinFlag;

    @Schema(description = "课件转码状态  1-转码中 2-转码成功 3-转码失败 4-排队中")
    private Integer transformStatus;

    @Schema(description = "转码开始时间")
    private Date transformStartTime;

    @Schema(description = "转码结束时间")
    private Date transformEndTime;

}
