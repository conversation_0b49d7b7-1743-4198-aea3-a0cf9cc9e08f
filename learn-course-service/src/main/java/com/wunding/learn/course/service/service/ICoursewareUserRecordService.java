package com.wunding.learn.course.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.course.service.admin.dto.CoursewareStudyDetailDTO;
import com.wunding.learn.course.service.admin.query.CoursewareStudyQuery;
import com.wunding.learn.course.service.client.query.CourseViewDurationClientQuery;
import com.wunding.learn.course.service.model.CoursewareUserRecord;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p> 用户学习课件记录表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">余学益</a>
 * @date 2022-05-10
 */
public interface ICoursewareUserRecordService extends IService<CoursewareUserRecord> {

    /**
     * 课程管理-课件学习明细
     *
     * @param coursewareStudyQuery
     * @return
     */
    List<CoursewareStudyDetailDTO> getCoursewareStudyDetail(CoursewareStudyQuery coursewareStudyQuery);

    Integer getCoursewareStudyDuration(CourseViewDurationClientQuery query);

    /**
     * @param cwIds
     * @return
     */
    Map<String, Long> countLearnedUserCountByCWIds(List<String> cwIds);

    /**
     * 课件关联考试时，更新用户课件学习记录的学习状态及完成时间
     * @param examFinishType 课件关联考试时的完成条件（-1:不涉及考试 0:提交考试即完成 1:通过考试才完成，仅在 exam_id 不为空时生效）
     * @param examId 考试ID
     * @param userId 用户ID
     */
    void updateUserRecord(Integer examFinishType, String examId, String userId);

    /**
     * 通过用户id和课件id集合查询学习课件记录列表
     *
     * @param userId 用户id
     * @param cwIds  课件id集合
     * @return 学习课件记录列表
     */
    List<CoursewareUserRecord> queryListByUserIdAndCwIds(String userId, Collection<String> cwIds);

    /**
     * 通过用户id和课件id查询学习课件记录
     *
     * @param userId       用户id
     * @param coursewareId 课件id
     * @return 学习课件记录
     */
    CoursewareUserRecord getByUserIdAndCwId(String userId, String coursewareId);

    Map<String, Long> getCoursewareProgressByUserIdAndCwId(String userId, List<String> cwIds);
}
