package com.wunding.learn.course.service.client.dto;

import com.wunding.learn.common.ai.outline.dto.AiOutlineItemDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * </p> 课件列表客户端dto对象
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @since 2024-08-06
 */
@Data
@Accessors(chain = true)
@Schema(name = "CoursewareListClientDTO", description = "课件列表客户端dto对象")
public class CoursewareListClientDTO implements Serializable {

    private static final long serialVersionUID = 2455269195829509114L;

    /**
     * 课件id
     */
    @Schema(description = "课件id")
    private String id;

    /**
     * 课程章节ID
     */
    @Schema(description = "课程章节ID")
    private String chapterId;

    /**
     * 课件名称
     */
    @Schema(description = "课件名称")
    private String cwName;

    /**
     * 课件类型
     */
    @Schema(description = "课件类型")
    private String cwType;

    /**
     * 课件MIME
     */
    @Schema(description = "课件MIME")
    private String mime;

    /**
     * 简介
     */
    @Schema(description = "简介")
    private String descriptions;

    /**
     * 课件时长
     */
    @Schema(description = "课件时长")
    private Integer playTime;

    /**
     * 课件真实时长（视频/mp3）
     */
    @Schema(description = "课件真实时长（视频/mp3）")
    private Integer realPlayTime;

    /**
     * 是否完成 0否 1是
     */
    @Schema(description = "是否完成 0否 1是")
    private Integer isLearned;

    /**
     * 已学时长
     */
    @Schema(description = "已学时长")
    private Long learnedTime;

    /**
     * 关联考试id
     */
    @Schema(description = "关联考试id")
    private String examId;

    /**
     * 关联考试名称
     */
    @Schema(description = "关联考试名称")
    private String examName;

    /**
     * 课件大纲条目列表
     */
    @Schema(description = "课件大纲条目列表")
    private List<AiOutlineItemDTO> aiOutlineItemDTOList;

    /**
     * 完成类型[0:按时长，1:按进度]
     */
    @Schema(description = "完成类型[0:按时长，1:按进度]")
    private Integer finishType;

    /**
     * 当完成类型为按进度时的课件学习进度百分比要求(0-100)，仅在finish_type=1时有效
     */
    @Schema(description = "当完成类型为按进度时的课件学习进度百分比要求(0-100)，仅在finish_type=1时有效")
    private Integer requiredProgress;

    /**
     * 课件关联考试时的完成条件（-1:不涉及考试 0:提交考试即完成 1:通过考试才完成，仅在 exam_id 不为空时生效）
     */
    @Schema(description = "课件关联考试时的完成条件（-1:不涉及考试 0:提交考试即完成 1:通过考试才完成，仅在 exam_id 不为空时生效）")
    private Integer examFinishType;

}
