package com.wunding.learn.course.service.admin.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.wunding.learn.file.api.dto.VideoClarityDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR> href="mailto:<EMAIL>">yuxueyi</a>
 * @version V7.0.0
 * @since 2022/5/10  16:00
 */
@Data
@Schema(name = "CourseWareDTO", description = "课程列表对象")
public class CourseWareDTO {


    @Schema(description = "主键id")
    private String id;

    @Schema(description = "课件名称")
    private String cwName;

    @Schema(description = "课件作者名字，可手动输入，可不为系统用户")
    private String cwAuthor;

    @Schema(description = "课件类型 Word-WORD,PPT-PPT,PDF-PDF,Video-视频,Audio-音频,Scorm-ZIP,Pic-ZIP,Text-图文")
    private String cwType;

    @Schema(description = "视频转换状态  1-转码中 2-转码成功 3-转码失败 4-排队中")
    private Integer transformStatus;

    @Schema(description = "课件创建方式： 0：在线做课，1：上传课件，2:资源库的课件，3：引用课件 8:待入库课件  9学员上传课件,10课件模板")
    private Integer isSource;

    @Schema(description = "考试id")
    private String examId;

    @Schema(description = "考试名称")
    private String examName;

    @Schema(description = "评星分数")
    private Double synthesizeStar;

    @Schema(description = "评星人数")
    private Long starUserCount;

    @Schema(description = "显示顺序")
    private Integer sortNo;

    @Schema(description = "课程章节名称")
    private String chapterName;

    @Schema(description = "课件原文件地址")
    private String fileUrl;

    @Schema(description = "课件下载文件名")
    private String fileName;

    @Schema(description = "课程关键词")
    private List<KeyWordResultDTO> keyword;

    @Schema(description = "时间类型，0-音/视频进度时间 1-页面停留时间")
    private Integer showType;

    @Schema(description = "课件时长,单位秒，管理员输入的时长")
    private Integer playTime;

    @Schema(description = "课件真实时长（视频/mp3）,即接口返回的时长", hidden = true)
    private Integer realTime;

    @Schema(description = "课件真实时长（视频/mp3）,即接口返回的时长 格式： 00:00:00")
    private String realPlayTime;

    @Schema(description = "课程下载申请是否通过  1: 申请通过  0: 申请不通过")
    private Integer isDownload;

    @Schema(description = "是否有题目  1: 是  0: 否")
    private Integer isContainQuestion;

    /**
     * 是否内置课程。0-否，1-是。内置课程可以赠送给新租户使用。
     */
    @Schema(description = "是否内置课程。0-否，1-是。内置课程可以赠送给新租户使用。")
    @TableField("buildin_flag")
    private Integer buildinFlag;

    @Schema(description = "课件预览地址")
    private String filePreviewUrl;

    @Schema(description = "课件清晰度地址")
    private List<VideoClarityDTO> videoClarityList;

    @Schema(description = "新增时间")
    private Date createTime;

    @Schema(description = "是否需要审核。0-否，1-是。")
    private Integer auditStatus;
}
