package com.wunding.learn.course.service.service.impl;


import static com.github.houbb.heaven.util.util.DateUtil.now;
import static com.wunding.learn.common.util.string.StringUtil.newId;
import static com.wunding.learn.course.api.constants.CWCreateSourceEnum.EXAMPLE;
import static com.wunding.learn.course.api.constants.CWCreateSourceEnum.H5;
import static com.wunding.learn.course.api.constants.CWCreateSourceEnum.REFERENCE;
import static com.wunding.learn.course.api.constants.CWCreateSourceEnum.REPOSITORY;
import static com.wunding.learn.course.api.constants.CWCreateSourceEnum.STOCK_PENDING;
import static com.wunding.learn.course.api.constants.CWCreateSourceEnum.UPLOAD;
import static com.wunding.learn.course.api.constants.CWCreateSourceEnum.getInstance;

import com.alibaba.excel.util.ListUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.wunding.learn.apply.api.service.ApplyFeign;
import com.wunding.learn.comment.api.service.CommentFeign;
import com.wunding.learn.common.ai.outline.dto.SaveAiOutlineDTO;
import com.wunding.learn.common.ai.outline.dto.SaveOrUpdateAiOutlineItemDTO;
import com.wunding.learn.common.ai.outline.service.IAiOutlineItemService;
import com.wunding.learn.common.category.model.Categorys;
import com.wunding.learn.common.category.service.ICategorysService;
import com.wunding.learn.common.constant.course.CourseConstant;
import com.wunding.learn.common.constant.course.CourseErrorNoEnum;
import com.wunding.learn.common.constant.exam.ExamErrorNoEnum;
import com.wunding.learn.common.constant.http.WebConstantUtil;
import com.wunding.learn.common.constant.other.AvailableEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.dto.LecturerCoursewareDetailDTO;
import com.wunding.learn.common.dto.ResourceIdAndUserIdDTO;
import com.wunding.learn.common.enums.comment.CommentTypeEnum;
import com.wunding.learn.common.enums.course.SysDictCodeType;
import com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum;
import com.wunding.learn.common.enums.excitation.ExcitationEventEnum;
import com.wunding.learn.common.enums.file.CWTypeEnum;
import com.wunding.learn.common.enums.file.TranscodeStatusEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.other.OperationEnum;
import com.wunding.learn.common.enums.other.RouterVisitEnum;
import com.wunding.learn.common.enums.other.SynUpdateCopyDataEventEnum;
import com.wunding.learn.common.enums.other.SystemConfigCodeEnum;
import com.wunding.learn.common.enums.process.ProcessStatusEnum;
import com.wunding.learn.common.enums.push.PushType;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.exception.handler.ApiAssert;
import com.wunding.learn.common.library.record.enums.HandleTypeEnum;
import com.wunding.learn.common.library.record.enums.LibraryTypeEnum;
import com.wunding.learn.common.library.record.service.BaseLibraryRecordService;
import com.wunding.learn.common.mq.dto.ExcitationMQEventDTO;
import com.wunding.learn.common.mq.event.HomeRouterVisitEvent;
import com.wunding.learn.common.mq.event.ResourceOperateEvent;
import com.wunding.learn.common.mq.event.SynUpdateCopyCourseEvent;
import com.wunding.learn.common.mq.event.TransCodeEvent;
import com.wunding.learn.common.mq.event.excitation.ExcitationMQEvent;
import com.wunding.learn.common.mq.service.MqProducer;
import com.wunding.learn.common.mybatis.service.impl.BaseServiceImpl;
import com.wunding.learn.common.util.bean.BeanListUtils;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.date.DateUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.util.string.StringPool;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.common.util.string.TranslateUtil;
import com.wunding.learn.course.api.constants.CWCreateSourceEnum;
import com.wunding.learn.course.api.dto.CourseInfoDTO;
import com.wunding.learn.course.api.dto.CourseWareInfoApiDTO;
import com.wunding.learn.course.api.dto.CourseWareNameDetailDTO;
import com.wunding.learn.course.api.dto.SaveCwLibraryDTO;
import com.wunding.learn.course.api.query.CourseWareSummaryQuery;
import com.wunding.learn.course.service.admin.dto.AddAllCourseWareDTO;
import com.wunding.learn.course.service.admin.dto.AiGeneratedContentDTO;
import com.wunding.learn.course.service.admin.dto.AiQuestionDTO;
import com.wunding.learn.course.service.admin.dto.AiQuestionOptionDTO;
import com.wunding.learn.course.service.admin.dto.AiSwitchConfigDTO;
import com.wunding.learn.course.service.admin.dto.AiSwitchConfigItem;
import com.wunding.learn.course.service.admin.dto.CourseCashDTO;
import com.wunding.learn.course.service.admin.dto.CourseWareCashDTO;
import com.wunding.learn.course.service.admin.dto.CourseWareDTO;
import com.wunding.learn.course.service.admin.dto.CourseWareLearnListDTO;
import com.wunding.learn.course.service.admin.dto.CourseWareLibraryDTO;
import com.wunding.learn.course.service.admin.dto.CourseWareQuestionDTO;
import com.wunding.learn.course.service.admin.dto.CoursewareListDTO;
import com.wunding.learn.course.service.admin.dto.CoursewareTransformFailDTO;
import com.wunding.learn.course.service.admin.dto.CwQuestionAnswerRecordDTO;
import com.wunding.learn.course.service.admin.dto.EditCwLibraryDTO;
import com.wunding.learn.course.service.admin.dto.ISaveCoursewareDTO;
import com.wunding.learn.course.service.admin.dto.KeyWordResultDTO;
import com.wunding.learn.course.service.admin.dto.ReGenerateDTO;
import com.wunding.learn.course.service.admin.dto.SaveCwDescDTO;
import com.wunding.learn.course.service.admin.dto.courseware.BatchCourseWareDTO;
import com.wunding.learn.course.service.admin.dto.courseware.BatchSaveCourseWareDTO;
import com.wunding.learn.course.service.admin.dto.courseware.CourseWareAiDTO;
import com.wunding.learn.course.service.admin.dto.courseware.CourseWareBaseDTO;
import com.wunding.learn.course.service.admin.dto.courseware.CourseWareDetailDTO;
import com.wunding.learn.course.service.admin.dto.courseware.CourseWareNoteCountDTO;
import com.wunding.learn.course.service.admin.dto.courseware.CoursewareReTranscodingDTO;
import com.wunding.learn.course.service.admin.dto.courseware.PriorityTranscodingDTO;
import com.wunding.learn.course.service.admin.dto.courseware.SaveCourseWareDTO;
import com.wunding.learn.course.service.admin.dto.courseware.UpdateCourseWareDTO;
import com.wunding.learn.course.service.admin.query.CourseWareLearnQuery;
import com.wunding.learn.course.service.admin.query.CourseWareLibraryQuery;
import com.wunding.learn.course.service.admin.query.CoursewareQuery;
import com.wunding.learn.course.service.admin.query.CoursewareTransformFailQuery;
import com.wunding.learn.course.service.admin.query.CwQuestionAnswerRecordQuery;
import com.wunding.learn.course.service.client.dto.AnswerCourseWareQuestionDTO;
import com.wunding.learn.course.service.client.dto.AnswerResultDTO;
import com.wunding.learn.course.service.client.dto.CourseViewDurationClientDTO;
import com.wunding.learn.course.service.client.dto.CourseWareInfoDTO;
import com.wunding.learn.course.service.client.dto.CourseWareInfoQuery;
import com.wunding.learn.course.service.client.dto.CoursewareMp3DTO;
import com.wunding.learn.course.service.client.dto.CoursewareStarDTO;
import com.wunding.learn.course.service.client.dto.ProjectTaskCoursewareSaveDTO;
import com.wunding.learn.course.service.client.factory.CoursewareContentFactory;
import com.wunding.learn.course.service.client.query.CourseViewDurationClientQuery;
import com.wunding.learn.course.service.client.query.CourseWareIsLearnedQueryDTO;
import com.wunding.learn.course.service.component.CourseViewLimitComponent;
import com.wunding.learn.course.service.component.CoursewareLibViewLimitComponent;
import com.wunding.learn.course.service.constant.CoursewareQuestionShowTypeEnum;
import com.wunding.learn.course.service.dao.CoursewareDao;
import com.wunding.learn.course.service.mapper.CourseMapper;
import com.wunding.learn.course.service.mapper.CourseViewMapper;
import com.wunding.learn.course.service.mapper.CourseVoteMapper;
import com.wunding.learn.course.service.mapper.CoursewareMapper;
import com.wunding.learn.course.service.mapper.CoursewareQuestionMapper;
import com.wunding.learn.course.service.mapper.CoursewareUserRecordMapper;
import com.wunding.learn.course.service.model.AiCwConfig;
import com.wunding.learn.course.service.model.AiGeneratedContentRecord;
import com.wunding.learn.course.service.model.Course;
import com.wunding.learn.course.service.model.CourseCwAiQuestion;
import com.wunding.learn.course.service.model.CourseCwAiQuestionOption;
import com.wunding.learn.course.service.model.CourseNote;
import com.wunding.learn.course.service.model.CourseView;
import com.wunding.learn.course.service.model.CourseVote;
import com.wunding.learn.course.service.model.Courseware;
import com.wunding.learn.course.service.model.CoursewarePackage;
import com.wunding.learn.course.service.model.CoursewareQuestion;
import com.wunding.learn.course.service.model.CoursewareQuestionAnswerRecord;
import com.wunding.learn.course.service.model.CoursewareQuestionOption;
import com.wunding.learn.course.service.model.CoursewareStar;
import com.wunding.learn.course.service.model.CoursewareUserRecord;
import com.wunding.learn.course.service.model.CwRefDifyKnowledge;
import com.wunding.learn.course.service.model.DocumentCourseware;
import com.wunding.learn.course.service.service.IAiCwConfigService;
import com.wunding.learn.course.service.service.IAiGeneratedContentRecordService;
import com.wunding.learn.course.service.service.ICourseCashService;
import com.wunding.learn.course.service.service.ICourseCwAiQuestionOptionService;
import com.wunding.learn.course.service.service.ICourseCwAiQuestionService;
import com.wunding.learn.course.service.service.ICourseDocumentService;
import com.wunding.learn.course.service.service.ICourseKeywordService;
import com.wunding.learn.course.service.service.ICourseNoteService;
import com.wunding.learn.course.service.service.ICourseViewDurationService;
import com.wunding.learn.course.service.service.ICourseWareCashService;
import com.wunding.learn.course.service.service.ICoursewarePackageMergeRecordService;
import com.wunding.learn.course.service.service.ICoursewarePackageService;
import com.wunding.learn.course.service.service.ICoursewareQuestionAnswerRecordService;
import com.wunding.learn.course.service.service.ICoursewareQuestionOptionService;
import com.wunding.learn.course.service.service.ICoursewareQuestionService;
import com.wunding.learn.course.service.service.ICoursewareService;
import com.wunding.learn.course.service.service.ICoursewareStarService;
import com.wunding.learn.course.service.service.ICwRefDifyKnowledgeService;
import com.wunding.learn.exam.api.dto.ExamInfoDTO;
import com.wunding.learn.exam.api.dto.ExamResultDTO;
import com.wunding.learn.exam.api.dto.ViewExamFeignDTO;
import com.wunding.learn.exam.api.query.ExamQuery;
import com.wunding.learn.exam.api.service.AnswerRecordFeign;
import com.wunding.learn.exam.api.service.ExamFeign;
import com.wunding.learn.example.api.dto.ExampleDTO;
import com.wunding.learn.example.api.service.ExampleFeign;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.component.FileResourceOptComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.constant.FileBizType;
import com.wunding.learn.file.api.constant.FileResourceEnum;
import com.wunding.learn.file.api.constant.ImageBizType;
import com.wunding.learn.file.api.dto.AbstractExportDataDTO;
import com.wunding.learn.file.api.dto.AbstractExportNoEntityDataDTO;
import com.wunding.learn.file.api.dto.CoursewareOnlineTempInfo;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.file.api.dto.IExportNoEntityDataDTO;
import com.wunding.learn.file.api.dto.NamePath;
import com.wunding.learn.file.api.dto.SaveFileDTO;
import com.wunding.learn.file.api.service.FileFeign;
import com.wunding.learn.file.api.service.ZuoKeFeign;
import com.wunding.learn.flowable.api.constant.ProcessRedisKeyEnum;
import com.wunding.learn.flowable.api.dto.ProcessDefinitionFeignDTO;
import com.wunding.learn.flowable.api.dto.ProcessDefinitionTaskDTO;
import com.wunding.learn.flowable.api.feign.ProcessFeign;
import com.wunding.learn.flowable.api.model.ProcessInstanceResource;
import com.wunding.learn.flowable.api.service.IProcessInstanceResourceService;
import com.wunding.learn.maxkb.api.constant.AiTypeConstant;
import com.wunding.learn.maxkb.api.service.AiService;
import com.wunding.learn.user.api.dto.AiBaseConfigDTO;
import com.wunding.learn.user.api.dto.AiCwSaveConfig;
import com.wunding.learn.user.api.dto.OrgShowDTO;
import com.wunding.learn.user.api.dto.ParaDTO;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.dto.UserOrgDTO;
import com.wunding.learn.user.api.dto.viewlimit.ViewLimitBaseInfoDTO;
import com.wunding.learn.user.api.service.AiBaseConfigFeign;
import com.wunding.learn.user.api.service.OrgFeign;
import com.wunding.learn.user.api.service.ParaFeign;
import com.wunding.learn.user.api.service.UserFeign;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;


/**
 * <p> 课件表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">余学益</a>
 * @date 2022-05-10
 */
@Slf4j
@Service("coursewareService")
public class CoursewareServiceImpl extends BaseServiceImpl<CoursewareMapper, Courseware> implements ICoursewareService {

    public static final int HUNDRED = 100;
    /**
     * 课件创建来源 - 课件库
     */
    public static final int IS_SOURCE_LIBRARY = 2;
    private static final String TAG_ID_SEPARATOR = ",";
    @Resource
    private UserFeign userFeign;


    @Resource(name = "coursewareDao")
    private CoursewareDao coursewareDao;

    @Resource
    private AnswerRecordFeign answerRecordFeign;

    @Resource
    private OrgFeign orgFeign;

    @Resource
    private FileFeign fileFeign;

    @Resource
    private ExamFeign examFeign;

    @Resource
    private ZuoKeFeign zuoKeFeign;

    @Resource
    @Lazy
    private ApplyFeign applyFeign;

    @Resource
    private MqProducer mqProducer;

    @Resource
    private ICoursewareStarService coursewareStarService;

    @Resource
    private BaseLibraryRecordService baseLibraryRecordService;

    @Resource
    private CoursewareLibViewLimitComponent coursewareLibViewLimitComponent;

    @Resource
    private CourseViewLimitComponent courseViewLimitComponent;

    @Resource
    private CoursewareMapper coursewareMapper;

    @Resource
    private CourseMapper courseMapper;

    @Resource
    private CourseVoteMapper courseVoteMapper;

    @Resource
    private CourseViewMapper courseViewMapper;

    @Resource
    private CoursewareUserRecordMapper coursewareUserRecordMapper;

    @Resource
    private ICourseViewDurationService courseViewDurationService;

    @Resource
    private CoursewareContentFactory coursewareContentFactory;
    @Resource
    private FileResourceOptComponent fileResourceOptComponent;
    @Resource
    private ParaFeign paraFeign;
    @Resource
    private ExportComponent exportComponent;

    @Resource
    private CommentFeign commentFeign;

    @Resource
    private ICourseDocumentService courseDocumentService;

    @Resource
    private ICourseCashService courseCashService;

    @Resource
    private ICourseWareCashService courseWareCashService;

    @Resource
    @Lazy
    private ICourseKeywordService courseKeywordService;

    @Resource
    private ICourseNoteService courseNoteService;

    @Resource
    private CoursewareQuestionMapper coursewareQuestionMapper;

    @Resource
    private ICoursewareQuestionAnswerRecordService coursewareQuestionAnswerRecordService;

    @Resource(name = "coursewareQuestionService")
    @Lazy
    private ICoursewareQuestionService coursewareQuestionService;

    @Resource
    @Lazy
    private ICoursewareQuestionOptionService coursewareQuestionOptionService;

    @Resource
    private ICoursewarePackageService coursewarePackageService;

    private final static String COURSEWARE_SERVICE = "coursewareService";

    private final static String LEARN_STATE = "learnState";

    @Resource
    private AiBaseConfigFeign aiBaseConfigFeign;

    @Resource(name = "redisTemplate")
    private RedisTemplate<String, Object> redisTemplate;

    @Resource(name = "aiService")
    private AiService aiService;

    @Resource(name = "aiCwConfigService")
    private IAiCwConfigService aiCwConfigService;

    @Resource(name = "aiGeneratedContentRecordService")
    private IAiGeneratedContentRecordService aiGeneratedContentRecordService;

    @Resource
    private ICategorysService categorysService;

    @Resource
    private IAiOutlineItemService aiOutlineItemService;

    @Resource(name = "courseCwAiQuestionService")
    private ICourseCwAiQuestionService courseCwAiQuestionService;

    @Resource(name = "courseCwAiQuestionOptionService")
    private ICourseCwAiQuestionOptionService courseCwAiQuestionOptionService;

    @Resource(name = "commonTaskThreadPool")
    private Executor asyncThreadPool;

    @Resource
    private ICoursewarePackageMergeRecordService coursewarePackageMergeRecordService;

    @Resource(name = "courseAsyncThreadPool")
    private Executor courseAsyncThreadPool;

    @Resource
    private ICwRefDifyKnowledgeService cwRefDifyKnowledgeService;

    @Resource
    @Lazy
    private ProcessFeign processFeign;

    @Resource
    private IProcessInstanceResourceService processInstanceResourceService;

    @Resource
    private ExampleFeign exampleFeign;

    @Override
    public PageInfo<CourseWareLearnListDTO> getUserDetailData(CourseWareLearnQuery coursewareLearnQuery) {
        dealWithQuery(coursewareLearnQuery);
        PageInfo<CourseWareLearnListDTO> pageInfo = PageMethod.startPage(coursewareLearnQuery.getPageNo(),
                coursewareLearnQuery.getPageSize(), coursewareLearnQuery.getIsCount())
            .doSelectPageInfo(() -> baseMapper.getCwUserDetail(coursewareLearnQuery));
        List<CourseWareLearnListDTO> list = pageInfo.getList();
        if (!CollectionUtils.isEmpty(list)) {
            Set<String> userIds = Sets.newHashSetWithExpectedSize(list.size());
            Set<String> orgIds = Sets.newHashSetWithExpectedSize(list.size());
            Set<String> cwIds = Sets.newHashSetWithExpectedSize(list.size());
            Set<String> cIds = Sets.newHashSetWithExpectedSize(list.size());
            Set<String> cateIds = Sets.newHashSetWithExpectedSize(list.size());
            List<ResourceIdAndUserIdDTO> resourceIdAndUserIdDTOList = new ArrayList<>();
            for (CourseWareLearnListDTO listDTO : list) {
                userIds.add(listDTO.getUserId());
                orgIds.add(listDTO.getOrgId());
                cwIds.add(listDTO.getCwId());
                cIds.add(listDTO.getId());
                cateIds.add(listDTO.getCourseCategoryId());

                ResourceIdAndUserIdDTO userIdDTO = new ResourceIdAndUserIdDTO();
                userIdDTO.setResourceId(listDTO.getExamId());
                userIdDTO.setUserId(listDTO.getUserId());
                resourceIdAndUserIdDTOList.add(userIdDTO);
            }

            Map<String, CourseCashDTO> courseCashMapByIds = courseCashService.getCourseCashMapByIds(cIds);
            Map<String, CourseWareCashDTO> courseWareCashDTOMap = courseWareCashService.getCourseWareCashMapByIds(
                cwIds);
            Map<String, UserDTO> userNameMapByIds = userFeign.getUserNameMapByIds(userIds);
            Map<String, OrgShowDTO> orgShowDTOMap = orgFeign.getOrgShowDTO(orgIds);
            Map<String, BigDecimal> scoreMapByExamIdAndUserId = answerRecordFeign.getScoreMapByExamIdAndUserId(
                resourceIdAndUserIdDTOList);
            Set<String> manageCourseOrgIds = list.stream().map(CourseWareLearnListDTO::getManageCourseOrgId)
                .collect(Collectors.toSet());
            Map<String, OrgShowDTO> manageCourseOrgShowDTOMap = orgFeign.getOrgShowDTO(manageCourseOrgIds);
            //完善数据
            pageInfo.getList().forEach(cw -> {
                Optional.ofNullable(courseCashMapByIds.get(cw.getId())).ifPresent(courseCashById -> {
                    cw.setCourseName(courseCashById.getCourseName());
                    cw.setCourseNo(courseCashById.getCourseNo());
                });
                Optional.ofNullable(courseWareCashDTOMap.get(cw.getCwId())).ifPresent(courseWareCashById -> {
                    cw.setCwName(courseWareCashById.getCwName());
                    cw.setPlayTime(courseWareCashById.getPlayTime());
                    cw.setExamId(courseWareCashById.getExamId());
                    //考试成绩
                    BigDecimal score = scoreMapByExamIdAndUserId.get(courseWareCashById.getExamId() + cw.getUserId());
                    cw.setUserScore(score);
                });
                //用户信息
                Optional.ofNullable(userNameMapByIds.get(cw.getUserId())).ifPresent(userDTO -> {
                    cw.setOrgPath(userDTO.getLevelPathName());
                    cw.setLoginName(userDTO.getLoginName());
                    cw.setFullName(userDTO.getFullName());
                    cw.setJobName(userDTO.getPostName());
                    cw.setLevelPathName(userDTO.getLevelPathName());
                });
                //组织名
                Optional.ofNullable(orgShowDTOMap.get(cw.getOrgId())).ifPresent(orgDTO -> {
                    cw.setOrgName(orgDTO.getOrgShortName());
                    if (null != cw.getDuration() && null != cw.getPlayTime()) {
                        cw.setLearnState(cw.getDuration() - cw.getPlayTime() >= 0 ? 1 : 0);
                    } else {
                        cw.setLearnState(0);
                    }
                });
                Optional.ofNullable(manageCourseOrgShowDTOMap.get(cw.getManageCourseOrgId())).ifPresent(orgShowDTO -> {
                    cw.setManageCourseOrgName(orgShowDTO.getOrgShortName());
                    cw.setManageCourseOrgLevelPath(orgShowDTO.getLevelPathName());
                });
            });
        }
        return pageInfo;
    }

    private void dealWithQuery(CourseWareLearnQuery coursewareLearnQuery) {
        Set<String> managerAreaOrgIds = orgFeign.findUserManageAreaLevelPath(UserThreadContext.getUserId());
        if (!managerAreaOrgIds.contains("/0/")) {
            coursewareLearnQuery.setManagerAreaOrgIds(managerAreaOrgIds);
        }
        coursewareLearnQuery.setCurrentOrgId(UserThreadContext.getOrgId());
        coursewareLearnQuery.setCurrentUserId(UserThreadContext.getUserId());
        //用户id
        coursewareLearnQuery.setUserIdsVo(
            TranslateUtil.translateBySplit(coursewareLearnQuery.getUserIds(), String.class));

        if (StringUtils.isNotBlank(coursewareLearnQuery.getCreateOrgId())) {
            Optional.ofNullable(orgFeign.getById(coursewareLearnQuery.getCreateOrgId()))
                .ifPresent(orgDTO -> coursewareLearnQuery.setLevelPath(orgDTO.getLevelPath()));
        }
    }

    @Override
    public PageInfo<CourseWareDTO> findCourseWareWareListByPage(CoursewareQuery coursewareQuery) {
        PageInfo<CourseWareDTO> pageInfo = PageMethod.startPage(coursewareQuery.getPageNo(),
            coursewareQuery.getPageSize()).doSelectPageInfo(() -> baseMapper.getCourseWareListByPage(coursewareQuery));
        // 未查询到课件，直接响应
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return pageInfo;
        }

        // 有课件，关联信息查询
        // 课件关键词
        List<String> cwIds = pageInfo.getList().stream().map(CourseWareDTO::getId).collect(Collectors.toList());
        Map<String, List<KeyWordResultDTO>> keywordMap = courseKeywordService.getKeywordMapByCourseWareIds(cwIds, 3);
        // 关联考试
        ExamQuery examQuery = new ExamQuery();
        examQuery.setExamIds(pageInfo.getList().stream().map(CourseWareDTO::getExamId).collect(Collectors.toSet()));
        Map<String, ViewExamFeignDTO> examMap = examFeign.getExamInfoMapByIds(examQuery);

        //根据当前用户及查询课程判断是否存在申请成功的课程下载申请
        String userId = UserThreadContext.getUserId();
        String resourcesId = coursewareQuery.getCourseId();
        int isDownload = applyFeign.queryCheckApplyStatus(userId, resourcesId);
        Map<String, CoursewareQuestion> courseQuestionMap = coursewareQuestionService.getCourseQuestionMap(cwIds);

        pageInfo.getList().forEach(dto -> {
            // 评星人数
            LambdaQueryWrapper<CoursewareStar> coursewareStarLambdaQueryWrapper = new LambdaQueryWrapper<>();
            coursewareStarLambdaQueryWrapper.eq(CoursewareStar::getCwId, dto.getId());
            dto.setStarUserCount(coursewareStarService.count(coursewareStarLambdaQueryWrapper));
            // 评星分数
            dto.setSynthesizeStar(coursewareStarService.getCourseWareSynthesizeStar(dto.getId()));
            // 考试名称
            ViewExamFeignDTO examInfo = examMap.get(dto.getExamId());
            dto.setExamName(Optional.ofNullable(examInfo).isEmpty() ? "" : examInfo.getExamName());
            // 附件信息
            NamePath fileNamePathInfo = fileFeign.getFileNamePathInfo(dto.getId(), FileBizType.CourseWareFile.name(),
                true);
            dto.setFileUrl(fileNamePathInfo.getUrl());
            dto.setFileName(fileNamePathInfo.getName());
            dto.setFilePreviewUrl(fileFeign.getFileUrl(dto.getId(), FileBizType.CourseWareFile.name()));
            // 设置视频清晰度URL
            dto.setVideoClarityList(fileFeign.getVideoClarity(dto.getId()));
            // 关键词
            dto.setKeyword(keywordMap.get(dto.getId()));
            // 课件真实时长（视频/mp3）
            dto.setRealPlayTime(DateUtil.getHMSBySecond(dto.getRealTime()));
            //课件是否可以下载
            dto.setIsDownload(isDownload > 0 ? 1 : 0);
            //课件是否包含题目
            dto.setIsContainQuestion(courseQuestionMap.get(dto.getId()) == null ? 0 : 1);
        });

        return pageInfo;
    }

    @Override
    public PageInfo<LecturerCoursewareDetailDTO> findLecturerCourseWareWareListByPage(
        CourseWareSummaryQuery coursewareQuery) {
        // 查讲师下符合查询条件的课程
        PageInfo<LecturerCoursewareDetailDTO> coursePageInfo = PageMethod.startPage(coursewareQuery.getPageNo(),
                coursewareQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.findCourseWareSummaryByPage(coursewareQuery));

        List<String> idList = coursePageInfo.getList().stream().map(LecturerCoursewareDetailDTO::getId)
            .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(idList)) {
            // 评论数
            Map<String, Integer> commentMap = commentFeign.getDiscussCount(idList, CommentTypeEnum.COURSEWARE);
            coursePageInfo.getList()
                .forEach(dto -> dto.setTotalComment(Optional.ofNullable(commentMap.get(dto.getId())).orElse(0)));
            // 笔记数
            List<CourseWareNoteCountDTO> nodeCountList = baseMapper.findCourseWareNodeCount(idList);
            Map<String, Integer> nodeCountMap = nodeCountList.stream().collect(
                Collectors.toMap(CourseWareNoteCountDTO::getId, CourseWareNoteCountDTO::getNoteCount, (k1, k2) -> k1));
            coursePageInfo.getList()
                .forEach(dto -> dto.setNoteCount(Optional.ofNullable(nodeCountMap.get(dto.getId())).orElse(0)));
        }
        return coursePageInfo;
    }

    @Override
    public void exportCourseWareData(CourseWareSummaryQuery coursewareQuery) {
        Locale locale = new Locale(
            StringUtils.isNotBlank(UserThreadContext.getAcceptLanguage()) ? UserThreadContext.getAcceptLanguage()
                : "zh");
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<ICoursewareService, LecturerCoursewareDetailDTO>(
            coursewareQuery) {

            @Override
            protected ICoursewareService getBean() {
                return SpringUtil.getBean(COURSEWARE_SERVICE, ICoursewareService.class);
            }

            @Override
            protected PageInfo<LecturerCoursewareDetailDTO> getPageInfo() {
                if (StringUtils.isNotBlank(UserThreadContext.getAcceptLanguage())) {
                    LocaleContextHolder.setLocale(locale);
                }
                return getBean().findLecturerCourseWareWareListByPage((CourseWareSummaryQuery) pageQueryDTO);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.LecturerCourseWareDetail;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.LecturerCourseWareDetail.getType();
            }

        };
        exportComponent.exportRecord(exportDataDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveCourseWare(SaveCourseWareDTO saveCourseWareDTO) {
        Courseware courseWare = new Courseware();
        // 检查关联考试是否结束
        checkExamFinished(saveCourseWareDTO.getExamId());
        String courseWareId = StringUtil.newId();
        saveCourseWareDTO.setId(courseWareId);

        BeanUtils.copyProperties(saveCourseWareDTO, courseWare);

        //课件所属组织
        courseWare.setOrgId(UserThreadContext.getOrgId());

        //处理不同的课件类型，并返回是否支持保存入库
        //复制一份到课件库
        String libCourseWareId = StringUtil.newId();
        saveCourseWareDTO.setCourseLibId(libCourseWareId);
        log.info("添加课件-> id:{} , 课件对象:{}", courseWareId, JsonUtil.objToJson(saveCourseWareDTO));
        boolean supportCourseWareToLib = this.disposeCourseWareFile(courseWare, saveCourseWareDTO);

        //这里课件的下发范围没什么意义，直接使用课程的下发范围
        courseWare.setIsSameCourse(1);

        if (StringUtils.isBlank(courseWare.getCwAuthor())) {
            //不输入则为当前操作人
            UserDTO userById = userFeign.getUserById(UserThreadContext.getUserId());
            courseWare.setCwAuthor(null != userById ? userById.getFullName() : null);
        }

        //保存课件
        coursewareDao.saveCourseware(courseWare);
        String courseId = saveCourseWareDTO.getCourseId();
        unPublishAuditCourse(courseId, "insert", courseWare);

        // 同步课件时长
        updateCourseSumPlayTime(courseWare.getCourseId());

        //保存课件AI配置
        saveAiCwConfig(courseWare.getId(), saveCourseWareDTO.getAiSwitch());

        //3.同时保存至课件库
        if (supportCourseWareToLib) {
            courseWare.setId(libCourseWareId);
            saveCourseWareToLib(courseWare, courseWareId, saveCourseWareDTO.getIsRepository());
        }

        // 课程相关数据变动，同步变动课程数据
        mqProducer.sendMsg(new SynUpdateCopyCourseEvent(courseWareId, SynUpdateCopyDataEventEnum.COURSEWARE.getKey()));
        mqProducer.sendMsg(
            new ResourceOperateEvent(OperationEnum.UPDATE, PushType.COURSE.getKey(), courseWare.getCourseId()));
    }

    private void updateCourseSumPlayTime(String courseId) {
        // 查询课程时长
        Integer courseSumPlayTime = courseMapper.getCourseSumPlayTime(courseId);
        // 更新课程时长
        courseMapper.updateCourseSumPlayTime(courseSumPlayTime, courseId);
    }

    private void saveAiCwConfig(String cwId, List<AiCwSaveConfig> aiSwitchList) {
        // 默认清除配置
        aiCwConfigService.deleteByCwId(cwId);

        if (!CollectionUtils.isEmpty(aiSwitchList)) {
            for (AiCwSaveConfig aiSwitch : aiSwitchList) {
                AiCwConfig config = new AiCwConfig();
                config.setCwId(cwId);
                config.setType(aiSwitch.getId());
                config.setIsAvailable(1);
                config.setCount(aiSwitch.getQuestionCount());
                config.setContent(StringUtils.join(aiSwitch.getQuestionTypeList(), ","));
                aiCwConfigService.save(config);
            }
        }
    }

    @Override
    public void saveCourseWareList(UserDTO userInfo, String courseId,
        List<ProjectTaskCoursewareSaveDTO> coursewareSaveList) {
        List<Courseware> newCoursewareList = new ArrayList<>();
        for (ProjectTaskCoursewareSaveDTO projectTaskCoursewareSaveDTO : coursewareSaveList) {
            String courseWareId = StringUtil.newId();
            DocumentCourseware validResource = courseDocumentService.getValidResource(
                projectTaskCoursewareSaveDTO.getId());
            if (Optional.ofNullable(validResource).isEmpty()) {
                continue;
            }
            // 避免没有效的真实时长
            Integer realPlayTime = 0;
            if (StringUtils.isNotBlank((validResource.getPlayTime()))) {
                realPlayTime = Integer.valueOf(validResource.getPlayTime());
            }
            Courseware newCourseware = new Courseware().setId(courseWareId).setOrgId(UserThreadContext.getOrgId())
                .setIsSameCourse(1).setCourseId(courseId).setCwAuthor(userInfo.getFullName())
                .setCwName(projectTaskCoursewareSaveDTO.getCwName())
                .setPlayTime(projectTaskCoursewareSaveDTO.getPlayTime()).setTransformStatus(2)
                .setSortNo(projectTaskCoursewareSaveDTO.getSortNo()).setIsSource(1).setCwType(validResource.getCwType())
                .setMime(validResource.getMime()).setRealPlayTime(realPlayTime).setCwContent(StringUtils.EMPTY);

            // 拷贝课件文件引用关系
            fileFeign.copySameFile(validResource.getId(), FileBizType.COURSE_WARE_DOCUMENT_FILE.name(), courseWareId,
                FileBizType.CourseWareFile.name());

            newCoursewareList.add(newCourseware);
        }

        if (CollectionUtils.isEmpty(newCoursewareList)) {
            throw new BusinessException(CourseErrorNoEnum.ERR_VALID_COURSEWARE_IS_NULL);
        }

        //批量保存课件
        saveBatch2(newCoursewareList);
    }

    private void checkExamFinished(String examId) {
        if (StringUtils.isNotBlank(examId)) {
            ExamInfoDTO examInfoDTO = Optional.ofNullable(examFeign.getById(examId))
                .orElseThrow(() -> new BusinessException(ExamErrorNoEnum.ERR_EXAM_IS_NOT_EXIST));

            Date now = new Date();
            if (now.after(examInfoDTO.getEndTime())) {
                throw new BusinessException(CourseErrorNoEnum.ERR_EXAM_IS_END);
            }
        }
    }

    @Override
    public void updateCourseWare(UpdateCourseWareDTO updateCourseWareDTO) {
        // 检查关联考试是否结束
        checkExamFinished(updateCourseWareDTO.getExamId());
        Courseware courseWare = new Courseware();
        BeanUtils.copyProperties(updateCourseWareDTO, courseWare);

        // 处理不同的课件类型
        this.updateCourseWareFile(courseWare, updateCourseWareDTO);

        // 保存课件
        coursewareDao.updateCourseware(courseWare);

        // 同步课件时长
        Courseware db = baseMapper.selectById(updateCourseWareDTO.getId());
        if (db == null) {
            throw new BusinessException(CourseErrorNoEnum.ERR_COURSEWARE_NULL);
        }
        updateCourseSumPlayTime(db.getCourseId());

        //保存课件AI配置
        saveAiCwConfig(courseWare.getId(), updateCourseWareDTO.getAiSwitch());

        updateCourseCwKeyword(Collections.singletonList(updateCourseWareDTO.getId()));

        // 课程相关数据变动，同步变动课程数据
        mqProducer.sendMsg(
            new SynUpdateCopyCourseEvent(updateCourseWareDTO.getId(), SynUpdateCopyDataEventEnum.COURSEWARE.getKey()));
        mqProducer.sendMsg(
            new ResourceOperateEvent(OperationEnum.UPDATE, PushType.COURSE.getKey(),
                getById(courseWare.getId()).getCourseId()));

        if (updateCourseWareDTO.getIsUpdateFile() == 1) {
            unPublishAuditCourse(db.getCourseId(), "update", courseWare);
        }
    }

    private void unPublishAuditCourse(String courseId, String operateScene, Courseware courseWare) {

        Course course = courseMapper.selectById(courseId);
        if (course.getIsTrain() != 0) {
            // 课程不涉及审核
            return;
        }
        Categorys category = categorysService.get(course.getCourseCateId());
        String levelPath = category.getLevelPath();
        String[] parts = levelPath.replaceAll("(^/)|(/$)", "").split("/");
        List<String> categoryIdList = new ArrayList<>();
        for (int i = parts.length - 1; i >= 0; i--) {
            categoryIdList.add(parts[i]);
        }
        ProcessDefinitionFeignDTO processDefinitionFeignDTO = processFeign.getNearestProcessDefinitionByProcessContentId(categoryIdList);
        if (processDefinitionFeignDTO == null) {
            log.warn("课程不需要审核");
            courseWare.setAuditStatus(0);
            baseMapper.updateById(courseWare);
            return;
        }
        String exemptRoles = processDefinitionFeignDTO.getExemptRoles();
        List<String> exemptRolesList = JsonUtil.json2List(exemptRoles, String.class);
        String userId = UserThreadContext.getUserId();
        List<String> roleIdByUserId = userFeign.getRoleIdByUserId(userId);
        if (roleIdByUserId.stream().anyMatch(exemptRolesList::contains)) {
            log.warn("当前操作人为该课程的免审核人员");
            courseWare.setAuditStatus(0);
            baseMapper.updateById(courseWare);
            return;
        }
        String processScene = processDefinitionFeignDTO.getProcessScene();
        if (processScene.contains(operateScene)) {
            LambdaUpdateWrapper<Course> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(Course::getId, course.getId());
            updateWrapper.set(Course::getIsPublish, 0);
            updateWrapper.set(Course::getPublishBy, StringUtils.EMPTY);
            updateWrapper.set(Course::getPublishTime, null);

            if (course.getAuditStatus() != 0) {
                updateWrapper.set(Course::getAuditStatus, 0);
                processInstanceResourceService.remove(new LambdaQueryWrapper<ProcessInstanceResource>().eq(ProcessInstanceResource::getResourceId, courseId));
                processFeign.deleteProcessInstanceByResourceId(courseId);

                // 获取流程任务信息
                ProcessDefinitionTaskDTO taskDTO = processFeign.getProcessDefinitionTaskByProcessDefinitionId(processDefinitionFeignDTO.getId());

                // 生成新的申请实例
                ProcessInstanceResource newInstance = new ProcessInstanceResource();
                newInstance.setId(newId());
                newInstance.setProcessCode(generateProcessCode());
                newInstance.setProcessDefinitionId(processDefinitionFeignDTO.getId());
                newInstance.setResourceId(course.getId());
                newInstance.setApplicantUserId(UserThreadContext.getUserId());
                newInstance.setProcessApplyType(1);
                newInstance.setCurrentAssigneeRole(taskDTO.getAssigneeRole());
                newInstance.setCurrentAssignee(taskDTO.getAssignee());
                newInstance.setStatus(ProcessStatusEnum.RUNNING.getStatus());
                newInstance.setIsAvailable(1);
                processInstanceResourceService.save(newInstance);
            }

            courseMapper.update(course, updateWrapper);
            courseWare.setAuditStatus(1);
            baseMapper.updateById(courseWare);
        }
    }

    private String generateProcessCode() {
        Object v = redisTemplate.opsForValue().get(ProcessRedisKeyEnum.PROCESS_CODE_NUM.getKey());
        if (v == null) {
            initProcessCode();
        }
        return "P" + redisTemplate.opsForValue().increment(ProcessRedisKeyEnum.PROCESS_CODE_NUM.getKey(), 1);
    }

    private void initProcessCode() {
        // 查询当前最大的审批流程编号
        String processCodeMax = processFeign.getProcessCodeMax();
        long currNum = 1000000L;
        if (StringUtils.isEmpty(processCodeMax)) {
            redisTemplate.opsForValue().set(ProcessRedisKeyEnum.PROCESS_CODE_NUM.getKey(), currNum);
        } else {
            String oldNo = processCodeMax;
            Long redisNum = null;
            try {
                currNum = Long.parseLong(oldNo.substring(1));
                Object v = redisTemplate.opsForValue().get(ProcessRedisKeyEnum.PROCESS_CODE_NUM.getKey());
                if (v instanceof Integer) {
                    redisNum = ((Integer) v).longValue();
                } else if (v instanceof Long) {
                    redisNum = (Long) v;
                }
            } catch (NumberFormatException e) {
                log.error("发生异常", e);
                currNum = 1000000L;
            }
            if (redisNum == null || redisNum < currNum) {
                redisTemplate.opsForValue().set(ProcessRedisKeyEnum.PROCESS_CODE_NUM.getKey(), currNum);
            }
        }
    }

    /**
     * 保存课件文件
     *
     * @param courseware        课件
     * @param saveCourseWareDTO 保存的课件内容
     */
    private boolean disposeCourseWareFile(Courseware courseware, CourseWareBaseDTO saveCourseWareDTO) {

        String courseWareId = courseware.getId();
        //是否支持保存入库
        boolean supportCourseWareToLib = true;
        courseware.setTransformStatus(TranscodeStatusEnum.TRANSFORMED.value);
        String tmpCourseWarePath = saveCourseWareDTO.getFilePath();
        //1.根据不同资源类型进行不同的处理
        Integer isSource = saveCourseWareDTO.getIsSource();
        CoursewareOnlineTempInfo coursewareOnlineTempInfo;
        switch (getInstance(isSource)) {
            case ON_LINE:
                //在线做课
                courseware.setCwType(CWTypeEnum.Text.name());
                courseware.setMime(WebConstantUtil.CW_CONTENT_TYPE_TEXT_HTML);
                // tmpCourseWarePath
                coursewareOnlineTempInfo = zuoKeFeign.generateWeCourseZip(courseware.getCwName(),
                    courseware.getCwContent());

                //将zip包临时目录文件转移至正式目录
                fileFeign.saveSourceFile(courseWareId, FileBizType.CourseWareFile.name(),
                    courseware.getCwName() + ".zip", coursewareOnlineTempInfo.getZipTempPath());
                //将index.html临时目录文件转移至正式目录
                fileFeign.saveFile(courseWareId, FileBizType.CourseWareFile.name(), courseware.getCwName(),
                    coursewareOnlineTempInfo.getHtmlTempPath());
                break;
            case UPLOAD:
                //上传课件根据类型去做转码处理
                ApiAssert.notNullParams(tmpCourseWarePath);
                // 保存源文件
                fileFeign.saveSourceFile(courseWareId, saveCourseWareDTO.getBizType().name(),
                    saveCourseWareDTO.getFileName(), saveCourseWareDTO.getFilePath());
                Integer transformStatus = fileResourceOptComponent.dealWithFileResourceUpload(courseware.getId(),
                    saveCourseWareDTO);
                courseware.setTransformStatus(transformStatus);
                if (transformStatus == TranscodeStatusEnum.TRANSFORMING.value) {
                    courseware.setTransformStartTime(new Date());
                }
                break;
            case REPOSITORY:
                //选择引用时资源库id不能为空
                ApiAssert.notNullParams(saveCourseWareDTO.getLibraryId());
                // 课件库课件
                Courseware existCourseWare = getById(saveCourseWareDTO.getLibraryId());
                ApiAssert.notNullResource(existCourseWare);
                if (TranscodeStatusEnum.TRANSFORMED.value != existCourseWare.getTransformStatus()) {
                    throw new BusinessException(CourseErrorNoEnum.ERR_CW_NOT_TRANSFORM_CANNOT_REFERENCE);
                }

                courseware.setCwType(existCourseWare.getCwType());
                courseware.setMime(existCourseWare.getMime());
                courseware.setPlayTime(saveCourseWareDTO.getPlayTime());
                courseware.setRealPlayTime(existCourseWare.getRealPlayTime());

                // 如果是在线做课就得复制内容
                if (existCourseWare.getIsSource() == REFERENCE.getSource()) {
                    courseware.setCwContent(existCourseWare.getCwContent());
                }

                // 拷贝课件文件引用关系
                fileFeign.copySameBizFile(saveCourseWareDTO.getLibraryId(), courseware.getId(),
                    FileBizType.CourseWareFile.name());

                //引用课件库不支持课件入库，其他均支持
                supportCourseWareToLib = false;
                break;
            case EXAMPLE:
                //选择引用时案例库id不能为空
                ApiAssert.notNullParams(saveCourseWareDTO.getLibraryId());
                // 课件库课件
                ExampleDTO exampleDTO = exampleFeign.getExampleById(saveCourseWareDTO.getLibraryId());
                ApiAssert.notNullResource(exampleDTO);
                if (TranscodeStatusEnum.TRANSFORMED.value != exampleDTO.getTransformStatus()) {
                    throw new BusinessException(CourseErrorNoEnum.ERR_CW_NOT_TRANSFORM_CANNOT_REFERENCE);
                }

                courseware.setCwType(exampleDTO.getFileType());
                courseware.setMime(exampleDTO.getMine());
                courseware.setPlayTime(saveCourseWareDTO.getPlayTime());
                courseware.setRealPlayTime(exampleDTO.getPlayTime().intValue());
                courseware.setSourceId(saveCourseWareDTO.getLibraryId());

                // 复制files文件
                // 拷贝课件文件引用关系
                fileFeign.copySameFile(saveCourseWareDTO.getLibraryId(), FileBizType.EXAMPLE_FILE.name(),
                    courseware.getId(),
                    FileBizType.CourseWareFile.name());

                //引用案例库不支持课件入库
                supportCourseWareToLib = false;
                break;
            case TEMPLATE:
                //课件模板
                courseware.setCwType(CWTypeEnum.Text.name());
                courseware.setMime(WebConstantUtil.CW_CONTENT_TYPE_TEXT_HTML);
                coursewareOnlineTempInfo = zuoKeFeign.generateWeCourseZip(courseware.getCwName(),
                    courseware.getCwContent());

                //将临时目录文件转移至正式目录
                fileFeign.saveSourceFile(courseWareId, FileBizType.CourseWareFile.name(),
                    saveCourseWareDTO.getCwName() + ".zip", coursewareOnlineTempInfo.getZipTempPath());//将临时目录文件转移至正式目录
                fileFeign.saveFile(courseWareId, FileBizType.CourseWareFile.name(), saveCourseWareDTO.getCwName(),
                    coursewareOnlineTempInfo.getHtmlTempPath());
                break;
            default:
                throw new BusinessException(CourseErrorNoEnum.ERR_STU_CW_NOT_SUPPORT_EDIT);
        }

        //设置旧的mime,可以理解为转码前的文件mime
        courseware.setOldMime(saveCourseWareDTO.getMime());
        return supportCourseWareToLib;
    }

    /**
     * 处理课件上传逻辑
     *
     * @param courseWareId 课件id
     * @deprecated 废弃
     */
    @Deprecated(since = "1.0")
    private Integer dealWithCourseWareUpload(String courseWareId, ISaveCoursewareDTO saveCourseWareDTO) {

        String tmpCourseWarePath = saveCourseWareDTO.getFilePath();
        //默认不需要转码
        int transformStatus = TranscodeStatusEnum.TRANSFORMED.value;
        if (StringUtils.isNotEmpty(saveCourseWareDTO.getCwType()) && CWTypeEnum.getNeedTransFormTypes()
            .contains(saveCourseWareDTO.getCwType())) {
            //需要进行转码
            transformStatus = TranscodeStatusEnum.TRANSFORMING.value;
            //将临时目录文件转移至正式目录并进行异步转码
            SaveFileDTO saveFileDTO;
            if (saveCourseWareDTO.getCwType().equals(CWTypeEnum.PPT.name())) {
                //上传ppt时可能存在mp3附件
                //保存主文件
                saveFileDTO = fileFeign.saveFile(courseWareId, FileBizType.CourseWareFile.name(),
                    saveCourseWareDTO.getFileName(), tmpCourseWarePath);

                if (!StringUtil.isEmpty(saveCourseWareDTO.getMp3filePath())) {
                    //保存附件
                    fileFeign.saveAttachmentFile(courseWareId, FileBizType.CourseWareFile.name(),
                        saveCourseWareDTO.getMp3filePath(), saveCourseWareDTO.getMp3fileName());
                }
            } else {
                saveFileDTO = fileFeign.saveFile(courseWareId, FileBizType.CourseWareFile.name(),
                    saveCourseWareDTO.getFileName(), tmpCourseWarePath);
            }

            //发送消息转码
            mqProducer.sendMsg(new TransCodeEvent(saveFileDTO.getId(), courseWareId, FileBizType.CourseWareFile.name(),
                tmpCourseWarePath, saveCourseWareDTO.getMime(), saveCourseWareDTO.getCwType(),
                saveCourseWareDTO.getCourseLibId()));
        } else {
            //不需要进行转码
            //将临时目录文件转移至正式目录
            fileFeign.saveFile(courseWareId, FileBizType.CourseWareFile.name(), saveCourseWareDTO.getFileName(),
                tmpCourseWarePath);
        }

        return transformStatus;
    }

    /**
     * 更新课件文件
     *
     * @param courseWare          课件
     * @param updateCourseWareDTO 保存的课件内容
     */
    private void updateCourseWareFile(Courseware courseWare, UpdateCourseWareDTO updateCourseWareDTO) {
        //1.根据不同资源类型进行不同的处理
        Integer isSource = updateCourseWareDTO.getIsSource();

        CWCreateSourceEnum cwCreateSourceEnum = getInstance(isSource);
        if (cwCreateSourceEnum.equals(CWCreateSourceEnum.ON_LINE) || cwCreateSourceEnum.equals(
            CWCreateSourceEnum.TEMPLATE)) {
            handleOnlineOrTemplateCourseware(courseWare);
            return;
        }

        if (cwCreateSourceEnum.equals(CWCreateSourceEnum.UPLOAD)) {
            handleUploadCourseware(courseWare, updateCourseWareDTO);
            return;
        }

        if (cwCreateSourceEnum.equals(H5)) {
            //不允许更新资源库的课件内容
            return;
        }

        if (cwCreateSourceEnum.equals(REPOSITORY)) {
            handleRepositoryCourseware(courseWare, updateCourseWareDTO);
            return;
        }

        if (cwCreateSourceEnum.equals(EXAMPLE)) {
            handleExampleCourseware(courseWare, updateCourseWareDTO);
            return;
        }

        throw new BusinessException(CourseErrorNoEnum.ERR_STU_CW_NOT_SUPPORT_EDIT);
    }

    private void handleRepositoryCourseware(Courseware courseWare, UpdateCourseWareDTO updateCourseWareDTO) {
        if (StringUtils.isNotBlank(updateCourseWareDTO.getLibraryId())) {
            // 选择了资源库中的另外一个课件
            updateCoursewareFromRepository(courseWare, updateCourseWareDTO);
        } else {
            // 没有选择另外的课件，还是原来的课件
            return;
        }
    }

    private void handleExampleCourseware(Courseware courseWare, UpdateCourseWareDTO updateCourseWareDTO) {
        if (StringUtils.isNotBlank(updateCourseWareDTO.getLibraryId())) {
            // 选择了案例库中的另外一个课件
            updateCoursewareFromExample(courseWare, updateCourseWareDTO);
        } else {
            // 没有选择另外的课件，还是原来的课件
            return;
        }
    }

    private void updateCoursewareFromRepository(Courseware courseWare, UpdateCourseWareDTO updateCourseWareDTO) {
        //课件重新上传，需要做标记
        courseWare.setIsReUpload(1);
        //删除之前的旧文件
        fileFeign.deleteFileByBizIdAndBizType(courseWare.getId(), FileBizType.CourseWareFile.name(), 0);
        //选择引用时资源库id不能为空
        ApiAssert.notNullParams(updateCourseWareDTO.getLibraryId());
        // 课件库课件
        Courseware existCourseWare = getById(updateCourseWareDTO.getLibraryId());
        ApiAssert.notNullResource(existCourseWare);
        if (TranscodeStatusEnum.TRANSFORMED.value != existCourseWare.getTransformStatus()) {
            throw new BusinessException(CourseErrorNoEnum.ERR_CW_NOT_TRANSFORM_CANNOT_REFERENCE);
        }

        courseWare.setCwType(existCourseWare.getCwType());
        courseWare.setMime(existCourseWare.getMime());
        courseWare.setPlayTime(updateCourseWareDTO.getPlayTime());
        courseWare.setRealPlayTime(existCourseWare.getRealPlayTime());

        // 如果是在线做课就得复制内容
        if (existCourseWare.getIsSource() == REFERENCE.getSource()) {
            courseWare.setCwContent(existCourseWare.getCwContent());
        }

        // 拷贝课件文件引用关系
        fileFeign.copySameBizFile(updateCourseWareDTO.getLibraryId(), updateCourseWareDTO.getId(),
            FileBizType.CourseWareFile.name());
        //将修改课件前的笔记设置为旧笔记
        courseNoteService.update(new LambdaUpdateWrapper<CourseNote>().set(CourseNote::getIsOld, 1)
            .eq(CourseNote::getCoursewareId, courseWare.getId()));
    }

    private void updateCoursewareFromExample(Courseware courseWare, UpdateCourseWareDTO updateCourseWareDTO) {
        //课件重新上传，需要做标记
        courseWare.setIsReUpload(1);
        //删除之前的旧文件
        fileFeign.deleteFileByBizIdAndBizType(courseWare.getId(), FileBizType.CourseWareFile.name(), 0);
        //选择引用时资源库id不能为空
        ApiAssert.notNullParams(updateCourseWareDTO.getLibraryId());
        // 课件库课件
        ExampleDTO exampleDTO = exampleFeign.getExampleById(updateCourseWareDTO.getLibraryId());
        ApiAssert.notNullResource(exampleDTO);
        if (TranscodeStatusEnum.TRANSFORMED.value != exampleDTO.getTransformStatus()) {
            throw new BusinessException(CourseErrorNoEnum.ERR_CW_NOT_TRANSFORM_CANNOT_REFERENCE);
        }

        courseWare.setCwType(exampleDTO.getFileType());
        courseWare.setMime(exampleDTO.getMine());
        courseWare.setPlayTime(updateCourseWareDTO.getPlayTime());
        courseWare.setRealPlayTime(exampleDTO.getPlayTime().intValue());
        courseWare.setSourceId(updateCourseWareDTO.getLibraryId());

        fileFeign.copySameFile(updateCourseWareDTO.getLibraryId(), FileBizType.EXAMPLE_FILE.name(),
            courseWare.getId(),
            FileBizType.CourseWareFile.name());
        //将修改课件前的笔记设置为旧笔记
        courseNoteService.update(new LambdaUpdateWrapper<CourseNote>().set(CourseNote::getIsOld, 1)
            .eq(CourseNote::getCoursewareId, courseWare.getId()));
    }

    private void handleUploadCourseware(Courseware courseWare, UpdateCourseWareDTO updateCourseWareDTO) {

        if (updateCourseWareDTO.getIsUpdateMp3() == 1) {
            //附件mp3存在更新
            //删除附件
            fileFeign.deleteFileByBizIdAndBizType(courseWare.getId(), FileBizType.CourseWareFile.name(), -1);
        } else {
            //不更新附件
            updateCourseWareDTO.setMp3filePath(null);
        }

        if (updateCourseWareDTO.getIsUpdateFile() == 1) {
            updateUploadedCourseware(courseWare, updateCourseWareDTO);
        } else if (
            updateCourseWareDTO.getIsUpdateMp3() == 1
                && StringUtils.isNotBlank(updateCourseWareDTO.getAdjunctPath())
        ) {
            //只保存附件
            fileFeign.saveAttachmentFile(courseWare.getId(), updateCourseWareDTO.getBizType().name(),
                updateCourseWareDTO.getAdjunctPath(), updateCourseWareDTO.getAdjunctName());
        }

        //设置旧的mime,可以理解为转码前的文件mime
        courseWare.setOldMime(updateCourseWareDTO.getMime());
        //将修改课件前的笔记设置为旧笔记
        courseNoteService.update(new LambdaUpdateWrapper<CourseNote>().set(CourseNote::getIsOld, 1)
            .eq(CourseNote::getCoursewareId, courseWare.getId()));
    }

    private void updateUploadedCourseware(Courseware courseWare, UpdateCourseWareDTO updateCourseWareDTO) {
        //课件重新上传，需要做标记
        courseWare.setIsReUpload(1);
        //删除旧文件，只删主文件
        fileFeign.deleteFileByBizIdAndBizType(courseWare.getId(), FileBizType.CourseWareFile.name(), 0);
        //保存新文件
        fileFeign.saveSourceFile(courseWare.getId(), updateCourseWareDTO.getBizType().name(),
            updateCourseWareDTO.getFileName(), updateCourseWareDTO.getFilePath());
        Integer transformStatus = fileResourceOptComponent.dealWithFileResourceUpload(courseWare.getId(),
            updateCourseWareDTO);
        courseWare.setTransformStatus(transformStatus);
        String cwType = updateCourseWareDTO.getCwType();
        // 更新课件时， 修改时间类型
        // 当课件由音频/视频，改为其他类型课件时，时间类型修改为页面停留时间
        if (!StringUtils.equals(cwType, CWTypeEnum.Video.name())
            && !StringUtils.equals(cwType, CWTypeEnum.Audio.name())) {
            courseWare.setShowType(CoursewareQuestionShowTypeEnum.DEFAULT.getShowType());
        }


    }

    private void handleOnlineOrTemplateCourseware(Courseware courseWare) {
        //删除之前的旧文件
        fileFeign.deleteFileByBizIdAndBizType(courseWare.getId(), FileBizType.CourseWareFile.name(), 0);
        //课件模板
        CoursewareOnlineTempInfo coursewareOnlineTempInfo = zuoKeFeign.generateWeCourseZip(courseWare.getCwName(),
            courseWare.getCwContent());

        String courseWareId = courseWare.getId();
        //将临时目录文件转移至正式目录
        fileFeign.saveSourceFile(courseWareId, FileBizType.CourseWareFile.name(),
            courseWare.getCwName() + ".zip", coursewareOnlineTempInfo.getZipTempPath());
        //将临时目录文件转移至正式目录
        fileFeign.saveFile(courseWareId, FileBizType.CourseWareFile.name(), courseWare.getCwName(),
            coursewareOnlineTempInfo.getHtmlTempPath());
    }

    /**
     * 保存课件库
     */
    private void saveCourseWareToLib(Courseware courseware, String sourceResourceId, Integer isRepository) {

        if (isRepository == 1) {
            //已经入库
            courseware.setIsSource(REFERENCE.getSource());
        } else {
            //待入库状态
            courseware.setIsSource(STOCK_PENDING.getSource());
        }

        //复制一份到课件库
        String libCourseWareId = courseware.getId();

        //拷贝课程的下发范围给课件库中的课件
        ViewLimitBaseInfoDTO viewLimitBaseInfo = courseViewLimitComponent
            .getViewLimitBaseInfo(courseware.getCourseId());
        coursewareLibViewLimitComponent.handleNewViewLimit(viewLimitBaseInfo.getProgrammeId(), libCourseWareId);

        //给课件库单独拷贝文件 只拷贝文件关联关系，删除时也只删除文件关联数据不会删除物理文件
        fileFeign.copySameBizFile(sourceResourceId, courseware.getId(), FileBizType.CourseWareFile.name());

        //保存课件库
        courseware.setCourseId(null);
        super.save(courseware);

        // 待入库不进记录表
        if (isRepository == 1) {
            //   保存课件库操控记录
            baseLibraryRecordService.saveOperationRecord(courseware.getCwName(), 1, HandleTypeEnum.TP_ADD_RECORD,
                LibraryTypeEnum.LIB_COURSE);
        }
    }

    @Override
    public CourseWareDetailDTO getCourseWareById(String id) {
        Courseware courseWare = getById(id);
        ApiAssert.notNullResource(courseWare);

        CourseWareDetailDTO courseWareDetailDTO = new CourseWareDetailDTO();
        BeanUtils.copyProperties(courseWare, courseWareDetailDTO);

        if (!StringUtil.isEmpty(courseWare.getExamId())) {
            ExamInfoDTO examInfoById = examFeign.getById(courseWare.getExamId());
            if (null != examInfoById) {
                // 查询考试名称
                courseWareDetailDTO.setExamName(examInfoById.getExamName());
            } else {
                courseWareDetailDTO.setExamId(StringUtils.EMPTY);
            }
        }

        //前端回显用
        Optional.ofNullable(
                fileFeign.getFileByCategoryTypeAndIsAdjunct(courseWare.getId(), FileBizType.CourseWareFile.name(), 0))
            .ifPresent(namePath -> courseWareDetailDTO.setCwFileName(namePath.getName()));
        Optional.ofNullable(
                fileFeign.getFileByCategoryTypeAndIsAdjunct(courseWare.getId(), FileBizType.CourseWareFile.name(), 1))
            .ifPresent(namePath -> courseWareDetailDTO.setCwMp3Name(namePath.getName()));
        courseWareDetailDTO.setIsUpdateFile(GeneralJudgeEnum.NEGATIVE.getValue())
            .setIsUpdateMp3(GeneralJudgeEnum.NEGATIVE.getValue());

        courseWareDetailDTO.setAiSwitch(
            aiCwConfigService.list(new LambdaQueryWrapper<AiCwConfig>().eq(AiCwConfig::getCwId, id)).stream()
                .map(aiCwConfig -> {
                    AiCwSaveConfig aiCwSaveConfig = new AiCwSaveConfig().setId(aiCwConfig.getType())
                        .setQuestionCount(aiCwConfig.getCount());
                    if (StringUtils.isNotBlank(aiCwConfig.getContent())) {
                        aiCwSaveConfig.setQuestionTypeList(
                            TranslateUtil.translateBySplit(aiCwConfig.getContent(), Integer.class));
                    }
                    return aiCwSaveConfig;
                }).toList());

        return courseWareDetailDTO;
    }

    @Override
    public void delCourseWareById(String ids) {
        List<String> idsList = TranslateUtil.translateBySplit(ids, String.class);
        List<Courseware> coursewareList = listByIds(idsList);

        coursewareList.forEach(
            courseware -> {
                // 遍历执行课件删除操作，同时记录日志
                coursewareDao.delCourseware(courseware);

                updateCourseSumPlayTime(courseware.getCourseId());
                // 删除课件合并记录
                coursewarePackageMergeRecordService.delByCwId(courseware.getId());
                mqProducer.sendMsg(
                    new SynUpdateCopyCourseEvent(courseware.getId(), SynUpdateCopyDataEventEnum.COURSEWARE.getKey()));
                mqProducer.sendMsg(
                    new ResourceOperateEvent(OperationEnum.UPDATE, PushType.COURSE.getKey(), courseware.getCourseId()));
            });
        updateCourseCwKeyword(idsList);
        courseNoteService.removeNoteByCwId(idsList);

        cwRefDifyKnowledgeService.deleteDifyDocumentByCwIdList(idsList);
        // 物理删除文件
        String physicallyDeleteFileSwitch = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_28009.getCode());
        if( !CollectionUtils.isEmpty(idsList) && String.valueOf(GeneralJudgeEnum.CONFIRM.getValue()).equals(Objects.toString(physicallyDeleteFileSwitch, "0"))){
                fileFeign.deletePhysicalFileByIds(idsList);
        }

    }

    @Override
    public void updateCourseCwKeyword(List<String> idsList) {
        // 删除课件关键词
        courseKeywordService.removeCwKeywordByCwIds(idsList);
        // 更新课程关键词
        for (String id : idsList) {
            String courseId = this.getCourseByCwId(id);
            courseKeywordService.updateKeywordScore(courseId);
        }
    }

    @Override
    public String getCourseByCwId(String cwId) {
        return baseMapper.getCourseIdByCwId(cwId);
    }

    @Override
    public List<String> getCourseIdsByCwIds(Collection<String> cwIds) {
        if (cwIds.isEmpty()) {
            return Collections.emptyList();
        }
        return baseMapper.getCourseIdsByCwIds(cwIds);
    }

    /**
     * 课件重新转码
     */
    @Override
    public void reTranscoding(CoursewareReTranscodingDTO coursewareReTranscodingDTO) {
        // 学员课件转码
        if (null != coursewareReTranscodingDTO.getType() && 1 == coursewareReTranscodingDTO.getType()) {
            coursewarePackageReTranscoding(coursewareReTranscodingDTO.getIds());
            return;
        }
        List<String> trainsCwIds = new ArrayList<>();

        // 1.筛选哪些课件需要转码
        List<Courseware> coursewares = getBaseMapper().selectBatchIds(coursewareReTranscodingDTO.getIds());
        List<Courseware> reTransCoursewares = new ArrayList<>();
        if (coursewares.isEmpty()) {
            return;
        }
        for (Courseware courseware : coursewares) {
            if (UPLOAD.getSource() != courseware.getIsSource()
                && REFERENCE.getSource() != courseware.getIsSource()) {
                continue;
            }

            // 是否需要转码
            if (!FileResourceEnum.isNeedTransform(courseware.getCwType())) {
                continue;
            }
            courseware.setTransformStatus(TranscodeStatusEnum.PENDING.value);
            courseware.setTransformStartTime(new Date());
            courseware.setUpdateBy(UserThreadContext.getUserId());
            courseware.setUpdateTime(now());
            trainsCwIds.add(courseware.getId());
            reTransCoursewares.add(courseware);
        }
        if (trainsCwIds.isEmpty()) {
            return;
        }
        // 2.将课件转码状态设置为正在转码 [遍历更新，同时记录业务日志]
        reTransCoursewares.forEach(e -> coursewareDao.updateCourseware(e));

        // 3.将转码文件地址修改为源文件地址
        fileFeign.resetTransFile(trainsCwIds, FileBizType.CourseWareFile.name());
        // 4.重新发送转码mq
        for (Courseware reTransCourseware : reTransCoursewares) {
            fileResourceOptComponent.reTranscoding(reTransCourseware.getId(), FileBizType.CourseWareFile.name(),
                reTransCourseware.getMime(), reTransCourseware.getCwType(), null);
        }
    }

    private void coursewarePackageReTranscoding(List<String> ids) {

        // 1.筛选哪些课件需要转码
        List<CoursewarePackage> coursewarePackages = coursewarePackageService.listByIds(ids);
        if (CollectionUtils.isEmpty(coursewarePackages)) {
            return;
        }

        List<String> trainsCwIds = coursewarePackages.stream().map(CoursewarePackage::getId)
            .collect(Collectors.toList());
        // 查询源文件id、path
        Map<String, NamePath> fileNamePathMap = fileFeign.getSourceFileMapByCategoryIds(trainsCwIds);
        List<CoursewarePackage> reTransCoursewares = new ArrayList<>();
        for (CoursewarePackage coursewarePackage : coursewarePackages) {
            //转码中或者不存在源文件不能转码
            if (TranscodeStatusEnum.TRANSFORMING.value == coursewarePackage.getTransformStatus() ||
                null == fileNamePathMap.get(coursewarePackage.getId())) {
                continue;
            }
            coursewarePackage.setTransformStatus(TranscodeStatusEnum.TRANSFORMING.value);
            trainsCwIds.add(coursewarePackage.getId());
            reTransCoursewares.add(coursewarePackage);
        }
        if (CollectionUtils.isEmpty(trainsCwIds)) {
            return;
        }
        // 2.将课件转码状态设置为正在转码 [遍历更新，同时记录业务日志]
        coursewarePackageService.updateBatchById(reTransCoursewares);

        // 3.将转码文件地址修改为源文件地址
        fileFeign.resetTransFile(trainsCwIds, FileBizType.CourseWarePackages.name());
        // 4.重新发送转码mq
        for (CoursewarePackage reTransCourseware : reTransCoursewares) {
            fileResourceOptComponent.reTranscoding(reTransCourseware.getId(), FileBizType.CourseWarePackages.name(),
                reTransCourseware.getMime(), reTransCourseware.getCwType(), null);
        }
    }

    @Override
    public void changeSort(String id, Integer changeSort) {
        Courseware byId = getById(id);
        Courseware courseware = new Courseware();
        courseware.setId(id);
        courseware.setCwName(byId.getCwName());
        courseware.setSortNo(changeSort);
        coursewareDao.updateCourseware(courseware);
    }

    @Override
    public CourseWareInfoDTO getCourseWareInfo(CourseWareInfoQuery infoQuery) {
        String id = infoQuery.getId();
        String userId = UserThreadContext.getUserId();
        //获得课件详细信息
        CourseWareInfoDTO courseWareInfo = baseMapper.getCourseWareInfo(id, userId);
        if (courseWareInfo == null) {
            throw new BusinessException(CourseErrorNoEnum.ERR_COURSEWARE_NULL);
        }
        // 发送消息[点击学习]
        CourseInfoDTO course = getCourseByCourseWareId(id);
        Integer isTrain = Optional.ofNullable(course).map(CourseInfoDTO::getIsTrain).orElse(null);
        mqProducer.sendMsg(new ExcitationMQEvent(
            new ExcitationMQEventDTO(userId, ExcitationEventEnum.learnCourseWare.name(), courseWareInfo.getId(),
                ExcitationEventCategoryEnum.COURSE_WARE.getCode()).setTargetName(courseWareInfo.getCwName())
                .setBizType(infoQuery.getBizType()).setBizId(infoQuery.getBizId())
                .setIsExchange(infoQuery.getIsExchange()).setIsTrain(isTrain)));
        Integer maxProgress = 0;
        courseWareInfo.setProgress(0);
        // 兼容投票课件详情查看
        if (StringUtils.isNotEmpty(courseWareInfo.getCourseId())) {
            // 获取课件上次观看占比
            CourseViewDurationClientQuery query = new CourseViewDurationClientQuery();
            query.setCourseId(courseWareInfo.getCourseId());
            query.setCwId(courseWareInfo.getId());
            query.setViewBy(userId);
            Optional.ofNullable(courseViewDurationService.getNewProgress(query)).ifPresent(courseWareInfo::setProgress);
            // 查询该课件本人的学习累加时间
            CourseViewDurationClientQuery durationClientQuery = new CourseViewDurationClientQuery();
            durationClientQuery.setCourseId(courseWareInfo.getCourseId());
            durationClientQuery.setCwId(courseWareInfo.getId());
            durationClientQuery.setViewBy(userId);
            CourseViewDurationClientDTO durationClientDTO = courseViewDurationService.sumCourseStudyTime(
                durationClientQuery);
            maxProgress = durationClientDTO.getProgress();
            //设置播放进度
            if (Objects.equals(courseWareInfo.getProgress(), HUNDRED)) {
                courseWareInfo.setProgress(0);
            } else {
                courseWareInfo.setProgress(courseWareInfo.getProgress() * courseWareInfo.getPlayTime() / 100);
            }

            //设置对应课程图片
            String courseImage = fileFeign.getImageUrl(courseWareInfo.getCourseId(), ImageBizType.CourseImgIcon.name());
            courseWareInfo.setCourseImage(courseImage);
        }
        //todo  系统配置表

        //设置可否拖动,进度100则必定可拖拽
        if (Objects.equals(maxProgress, 100)) {
            courseWareInfo.setCanDrag(1);
        }

        courseWareInfo.setIsShowExcitation(0);
        courseWareInfo.setIsComment(1);
        courseWareInfo.setIsDownload(1);
        courseWareInfo.setMyCompany(1);
        courseWareInfo.setOrientation("P");
        courseWareInfo.setEnableRating(1);

        Map<String, Integer> commentMap = commentFeign.getDiscussCount(List.of(courseWareInfo.getId()),
            CommentTypeEnum.COURSEWARE);
        courseWareInfo.setCommentNumber(Optional.ofNullable(commentMap.get(courseWareInfo.getId())).orElse(0));
        //todo 用户激励配置 user_excitation_config

        // 课件未关联课程，则不可评论
        if (StringUtils.isEmpty(courseWareInfo.getCourseId())) {
            courseWareInfo.setIsComment(0);
        }
        courseWareInfo.setIsLearned(courseWareInfo.getIsLearned() > 0 ? 1 : 0);

        //设置作者名称
        if (StringUtils.isEmpty(courseWareInfo.getCwAuthor())) {
            UserDTO userById = userFeign.getUserById(courseWareInfo.getCreateBy());
            courseWareInfo.setCwAuthor(userById.getFullName());
        }

        //查询课件评星
        CoursewareStarDTO coursewareStarDTO = coursewareStarService.queryCoursewareStar(courseWareInfo.getId());
        courseWareInfo.setCommonStar(
            Optional.ofNullable(coursewareStarDTO).map(CoursewareStarDTO::getCommonStar).orElse(new BigDecimal("0.0"))
                .toString());
        courseWareInfo.setStarCount(
            Optional.ofNullable(coursewareStarDTO).map(CoursewareStarDTO::getStarUserCount).orElse(0));
        // 查询自我评星
        int oneselfStar = coursewareStarService.list(
            new LambdaQueryWrapper<CoursewareStar>().eq(CoursewareStar::getCwId, courseWareInfo.getId())
                .eq(CoursewareStar::getCreateBy, userId)).stream().mapToInt(CoursewareStar::getStarCount).sum();

        courseWareInfo.setMyStar(oneselfStar);
        //设置对应课程图片
        String currentPath = fileFeign.getFileUrl(courseWareInfo.getId(), FileBizType.CourseWareFile.name());
        courseWareInfo.setUrl(currentPath);
        // 设置视频清晰度URL
        courseWareInfo.setVideoClarityList(fileFeign.getVideoClarity(courseWareInfo.getId()));
        // 课件访问地址url
        coursewareContentFactory.fillCoursewareContent(courseWareInfo);
        List<String> pptType = Arrays.asList(CWTypeEnum.PPT.getFileType());
        if (pptType.contains(courseWareInfo.getType().toLowerCase())) {
            Optional.ofNullable(
                fileFeign.getFileByCategoryTypeAndIsAdjunct(courseWareInfo.getId(), FileBizType.CourseWareFile.name(),
                    1)).ifPresent(
                mp3File -> courseWareInfo.setCoursewareMp3DTO(new CoursewareMp3DTO().setUrl(mp3File.getUrl())));
        }
        return courseWareInfo;
    }

    @Override
    public List<CoursewareListDTO> getCourseWareList(String id) {
        // 获取课程课件
        LambdaQueryWrapper<Courseware> coursewareWrapper = new LambdaQueryWrapper<>();
        coursewareWrapper.eq(Courseware::getCourseId, id)
            .eq(Courseware::getIsAvailable, AvailableEnum.AVAILABLE.getValue())
            .eq(Courseware::getTransformStatus, CourseConstant.COURSEWARE_TRANSFORM_STATUS_SUCCESS)
            .orderByAsc(Courseware::getSortNo).orderByDesc(Courseware::getCreateTime);
        List<Courseware> coursewareList = coursewareMapper.selectList(coursewareWrapper);
        if (CollectionUtils.isEmpty(coursewareList)) {
            return new ArrayList<>();
        }
        Set<String> coursewareIdSet = coursewareList.stream().map(Courseware::getId).collect(Collectors.toSet());

        List<CoursewareListDTO> coursewareListDTOList = coursewareList.stream().map(
            courseware -> CoursewareListDTO.builder().id(courseware.getId()).courseId(courseware.getCourseId())
                .chapterId(courseware.getChapterId()).classHour(courseware.getClassHour())
                .cwName(courseware.getCwName()).type(courseware.getCwType()).mime(courseware.getMime())
                .description(courseware.getDescriptions()).playTime(courseware.getRealPlayTime())
                .setPlayTime(courseware.getPlayTime()).examId(courseware.getExamId()).createBy(courseware.getCreateBy())
                .build()).collect(Collectors.toList());

        // 获取发布时间（课程的发布时间）
        buildCourseInfo(coursewareListDTOList, id);

        // 获取点赞数量
        buildFavoriteNumber(coursewareListDTOList, coursewareIdSet, id);

        // 获取课件浏览量
        buildViewCount(coursewareListDTOList, coursewareIdSet, id);

        // 课件学习信息
        buildLearnInfo(coursewareListDTOList, coursewareIdSet, id);

        // 获取相关的考试信息 考试名称 是否完成了考试
        buildExamInfo(coursewareListDTOList);

        // 课件相关的激励信息
        // TODO: 暂时无激励信息表，积分业务待补充

        // 补充课件信息
        coursewareListDTOList.forEach(dto -> coursewareContentFactory.fillCoursewareContent(dto));
        return coursewareListDTOList;
    }

    private void buildCourseInfo(List<CoursewareListDTO> coursewareListDTOList, String id) {
        Course course = courseMapper.selectById(id);
        coursewareListDTOList.forEach(coursewareListDTO -> coursewareListDTO.setPublishTime(course.getPublishTime()));
    }

    private void buildFavoriteNumber(List<CoursewareListDTO> coursewareListDTOList, Set<String> coursewareIdSet,
        String id) {
        LambdaQueryWrapper<CourseVote> courseVoteWrapper = new LambdaQueryWrapper<>();
        courseVoteWrapper.eq(CourseVote::getCourseId, id).in(CourseVote::getCoursewareId, coursewareIdSet);
        List<CourseVote> courseVoteList = courseVoteMapper.selectList(courseVoteWrapper);
        Map<String, Long> courseVoteMap = courseVoteList.stream()
            .collect(Collectors.groupingBy(CourseVote::getCoursewareId, Collectors.counting()));
        if (CollectionUtils.isEmpty(courseVoteMap)) {
            return;
        }
        coursewareListDTOList.forEach(
            coursewareListDTO -> coursewareListDTO.setFavoriteNumber(courseVoteMap.get(coursewareListDTO.getId())));
    }

    private void buildViewCount(List<CoursewareListDTO> coursewareListDTOList, Set<String> coursewareIdSet, String id) {
        LambdaQueryWrapper<CourseView> courseViewWrapper = new LambdaQueryWrapper<>();
        courseViewWrapper.eq(CourseView::getCourseId, id).in(CourseView::getCwId, coursewareIdSet);
        List<CourseView> courseViewList = courseViewMapper.selectList(courseViewWrapper);
        Map<String, Long> courseViewCountMap = courseViewList.stream()
            .collect(Collectors.groupingBy(CourseView::getCwId, Collectors.counting()));
        coursewareListDTOList.forEach(
            coursewareListDTO -> coursewareListDTO.setViewCount(courseViewCountMap.get(coursewareListDTO.getId())));
    }

    private void buildLearnInfo(List<CoursewareListDTO> coursewareListDTOList, Set<String> coursewareIdSet, String id) {
        // 获取学习数量
        LambdaQueryWrapper<CoursewareUserRecord> cwUserRecordWrapper = new LambdaQueryWrapper<>();
        cwUserRecordWrapper.in(CoursewareUserRecord::getCoursewareId, coursewareIdSet)
            .eq(CoursewareUserRecord::getCourseId, id);
        List<CoursewareUserRecord> coursewareUserRecordList = coursewareUserRecordMapper.selectList(
            cwUserRecordWrapper);
        if (CollectionUtils.isEmpty(coursewareUserRecordList)) {
            return;
        }
        Map<String, Long> coursewareLearnCountMap = coursewareUserRecordList.stream()
            .filter(coursewareUserRecord -> StringUtils.isNotBlank(coursewareUserRecord.getUserId()))
            .collect(Collectors.groupingBy(CoursewareUserRecord::getCoursewareId, Collectors.counting()));
        coursewareListDTOList.forEach(coursewareListDTO -> {
            Long count = coursewareLearnCountMap.get(coursewareListDTO.getId());
            if (count == null) {
                coursewareListDTO.setLearnedCount(0L);
            }
            coursewareListDTO.setLearnedCount(count);
        });
        // 获取当前用户学习详情
        cwUserRecordWrapper.eq(CoursewareUserRecord::getUserId, UserThreadContext.getUserId());
        coursewareUserRecordList = coursewareUserRecordMapper.selectList(cwUserRecordWrapper);
        if (CollectionUtils.isEmpty(coursewareUserRecordList)) {
            return;
        }
        Map<String, CoursewareUserRecord> coursewareUserRecordMap = coursewareUserRecordList.stream()
            .filter(coursewareUserRecord -> StringUtils.isNotBlank(coursewareUserRecord.getUserId())).collect(
                Collectors.toMap(CoursewareUserRecord::getUserId, coursewareUserRecord -> coursewareUserRecord,
                    (key1, key2) -> key2));
        coursewareListDTOList.forEach(dto -> {
            CoursewareUserRecord coursewareUserRecord = coursewareUserRecordMap.get(dto.getId());
            if (null == coursewareUserRecord) {
                dto.setLearnedTime(0L);
                dto.setIsLearned(GeneralJudgeEnum.NEGATIVE.getValue());
                return;
            }
            dto.setIsLearned(coursewareUserRecord.getIsLearned());
            dto.setLearnedTime(coursewareUserRecord.getDuration());
        });
    }

    private void buildExamInfo(List<CoursewareListDTO> coursewareListDTOList) {
        Set<String> examIdSet = coursewareListDTOList.stream().map(CoursewareListDTO::getExamId)
            .filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(examIdSet)) {
            return;
        }
        coursewareListDTOList.stream()
            .filter(coursewareListDTO -> StringUtils.isNotBlank(coursewareListDTO.getExamId()))
            .forEach(coursewareListDTO -> {
                ExamQuery examQuery = new ExamQuery();
                examQuery.setId(coursewareListDTO.getExamId());
                ViewExamFeignDTO viewExamFeignDTO = examFeign.getExamInfoById(examQuery);
                if (null == viewExamFeignDTO) {
                    return;
                }
                coursewareListDTO.setExamName(viewExamFeignDTO.getExamName());
            });

        // 获取考试结果
        List<ExamResultDTO> examResultDTOList = answerRecordFeign.getExamResultByIds(examIdSet);
        if (CollectionUtils.isEmpty(examResultDTOList)) {
            return;
        }
        Map<String, ExamResultDTO> examResultDtoMap = examResultDTOList.stream()
            .collect(Collectors.toMap(ExamResultDTO::getExamId, examResultDTO -> examResultDTO, (key1, key2) -> key2));
        coursewareListDTOList.stream()
            .filter(coursewareListDTO -> StringUtils.isNotBlank(coursewareListDTO.getExamId()))
            .forEach(coursewareListDTO -> {
                ExamResultDTO examResultDTO = examResultDtoMap.get(coursewareListDTO.getExamId());
                if (null == examResultDTO) {
                    return;
                }
                coursewareListDTO.setIsFinishExam(examResultDTO.getPass());
            });
    }

    @Override
    public ViewExamFeignDTO courseWareIsLearned(CourseWareIsLearnedQueryDTO courseWareIsLearnedQueryDTO) {
        String userId = UserThreadContext.getUserId();
        CoursewareUserRecord coursewareUserRecord = coursewareUserRecordMapper.selectOne(
            new LambdaQueryWrapper<CoursewareUserRecord>().eq(CoursewareUserRecord::getCoursewareId,
                courseWareIsLearnedQueryDTO.getCourseWareId()).eq(CoursewareUserRecord::getUserId, userId));

        if (coursewareUserRecord == null || coursewareUserRecord.getIsLearned() != 1) {
            throw new BusinessException(CourseErrorNoEnum.ERR_CW_NOT_FINISH_CANNOT_VIEW_EXAM);
        }
        ExamQuery examQuery = new ExamQuery();
        examQuery.setId(courseWareIsLearnedQueryDTO.getExamId());
        return examFeign.getExamInfoById(examQuery);
    }

    @Override
    public PageInfo<CourseWareLibraryDTO> getCwLibraryListData(CourseWareLibraryQuery courseWareLibraryQuery) {
        replenishParam(courseWareLibraryQuery);

        PageInfo<CourseWareLibraryDTO> pageInfo = PageMethod.startPage(courseWareLibraryQuery.getPageNo(),
                courseWareLibraryQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.getCWLibraryListData(courseWareLibraryQuery));
        Set<String> userIds = new HashSet<>();
        Set<String> ids = new HashSet<>();
        for (CourseWareLibraryDTO c : pageInfo.getList()) {
            if (StringUtils.isNotBlank(c.getCreateBy())) {
                userIds.add(c.getCreateBy());
            }
            if (StringUtils.isNotBlank(c.getUpdateBy())) {
                userIds.add(c.getUpdateBy());
            }
            ids.add(c.getId());
        }

        Map<String, Integer> mapCwAgree = new HashMap<String, Integer>();
        if (!CollectionUtils.isEmpty(ids)) {
            List<CourseWareLibraryDTO> data = baseMapper.getCWVote(ids);
            mapCwAgree = data.stream()
                .collect(Collectors.toMap(CourseWareLibraryDTO::getId, CourseWareLibraryDTO::getCwAgree));
        }
        Map<String, UserOrgDTO> userMap = orgFeign.getUserAndOrgMapByUserIds(userIds);
        log.info("ids:" + JsonUtil.objToJson(ids));
        Map<String, NamePath> fileMap = fileFeign.getSourceFileMapByCategoryIds(ids);
        log.info("ids:" + JsonUtil.objToJson(fileMap));
        Map<String, Integer> commentMap = commentFeign.getDiscussCount(ids, CommentTypeEnum.COURSEWARE);

        Map<String, Integer> finalMapCwAgree = mapCwAgree;
        pageInfo.getList().forEach(c -> {
            c.setCwComment(Optional.ofNullable(commentMap.get(c.getId())).orElse(0));
            Optional.ofNullable(userMap.get(c.getCreateBy())).ifPresent(user -> c.setCreateByName(user.getFullName()));
            Optional.ofNullable(userMap.get(c.getUpdateBy())).ifPresent(user -> c.setUpdateByName(user.getFullName()));
            Optional.ofNullable(fileMap.get(c.getId())).ifPresent(file -> {
                c.setCwPath(file.getName());
                c.setCwFileUrl(file.getUrl());
            });
            if (finalMapCwAgree.containsKey(c.getId())) {
                c.setCwAgree(Optional.ofNullable(finalMapCwAgree.get(c.getId())).orElse(0));
            }
        });

        //路由访问埋点
        mqProducer.sendMsg(
            new HomeRouterVisitEvent(RouterVisitEnum.CoursewareDepot.getRouterId(), UserThreadContext.getUserId(),
                RouterVisitEnum.CoursewareDepot.getName()));

        return pageInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCoursewareLibrary(String ids) {
        List<String> tagIds = TranslateUtil.translateBySplit(ids, String.class);
        List<Courseware> coursewares = baseMapper.selectBatchIds(tagIds);
        List<Integer> enableList = coursewares.stream().map(Courseware::getIsAvailable).collect(Collectors.toList());
        if (enableList.contains(GeneralJudgeEnum.CONFIRM.getValue())) {
            throw new BusinessException(CourseErrorNoEnum.ERR_COURSEWARE_AVAILABLE);
        }

        // 遍历修改，标记删除，同时记录日志
        coursewares.forEach(e -> coursewareDao.delCoursewareLibrary(e));

        // 保存操作记录
        String nameString = getNameString(coursewares);
        baseLibraryRecordService.saveOperationRecord(nameString, tagIds.size(), HandleTypeEnum.TP_DELETE_RECORD,
            LibraryTypeEnum.LIB_COURSE);
    }

    /**
     * 获取资源名称字符串，多个名称之间以“,”分隔
     *
     * @param list 资源列表
     * @return
     */
    private String getNameString(List<Courseware> list) {
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return list.stream().map(Courseware::getCwName)
            .collect(Collectors.joining(StringPool.COMMA));
    }

    @Override
    public void isAvailable(String ids, Integer isAvailable) {
        List<String> idList = Arrays.asList(ids.split(TAG_ID_SEPARATOR));
        List<Courseware> coursewareList = listByIds(idList);
        coursewareList.forEach(e -> {
            Courseware courseware = new Courseware();
            courseware.setId(e.getId());
            courseware.setCwName(e.getCwName());
            courseware.setIsAvailable(isAvailable);

            if (GeneralJudgeEnum.CONFIRM.getValue().equals(isAvailable)) {
                coursewareDao.coursewareLibraryEnable(courseware);
            } else {
                coursewareDao.coursewareLibraryDisable(courseware);
            }
        });

        String nameString = getNameString(coursewareList);
        baseLibraryRecordService.saveOperationRecord(nameString, coursewareList.size(),
            isAvailable.equals(GeneralJudgeEnum.NEGATIVE.getValue())
                ? HandleTypeEnum.TP_DISABLE_RECORD : HandleTypeEnum.TP_ENABLE_RECORD,
            LibraryTypeEnum.LIB_COURSE);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String coursewareStorage(SaveCwLibraryDTO saveCwLibraryDTO) {
        Courseware courseware = new Courseware();
        saveCwLibraryDTO.setId(newId());
        BeanUtils.copyProperties(saveCwLibraryDTO, courseware);
        courseware.setOrgId(UserThreadContext.getOrgId());
        courseware.setIsSource(3);
        courseware.setIsAvailable(1);
        courseware.setCwContent(StringUtils.EMPTY);
        courseware.setOldMime(saveCwLibraryDTO.getMime());
        UserDTO userById = userFeign.getUserById(UserThreadContext.getUserId());
        courseware.setCwAuthor(null != userById ? userById.getFullName() : null);
        // 保存原文件
        fileFeign.saveSourceFile(saveCwLibraryDTO.getId(), FileBizType.CourseWareFile.name(),
            saveCwLibraryDTO.getFileName(), saveCwLibraryDTO.getFilePath());
        Integer transformStatus = fileResourceOptComponent.dealWithFileResourceUpload(courseware.getId(),
            saveCwLibraryDTO);
        courseware.setTransformStatus(transformStatus);
        if (transformStatus == 1) {
            courseware.setTransformStartTime(now());
        }
        // 保存课件，同时记录日志
        coursewareDao.saveCoursewareLibrary(courseware);
        baseLibraryRecordService.saveOperationRecord(courseware.getCwName(), 1, HandleTypeEnum.TP_ADD_RECORD,
            LibraryTypeEnum.LIB_COURSE);
        return courseware.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String coursewareStorageJob(SaveCwLibraryDTO saveCwLibraryDTO) {
        UserDTO userById = userFeign.getUserById(saveCwLibraryDTO.getCwAuthorId());

        Courseware courseware = new Courseware();
        BeanUtils.copyProperties(saveCwLibraryDTO, courseware);
        courseware.setId(newId());
        courseware.setOrgId(null != userById ? userById.getOrgId() : "0");
        courseware.setIsSource(3);
        courseware.setIsAvailable(1);
        courseware.setCwContent(StringUtils.EMPTY);
        courseware.setOldMime(saveCwLibraryDTO.getMime());
        courseware.setCwAuthor(null != userById ? userById.getFullName() : null);
        courseware.setCreateBy(null != userById ? userById.getId() : null);
        Integer transformStatus = fileResourceOptComponent.dealWithFileResourceUpload(courseware.getId(),
            saveCwLibraryDTO);
        courseware.setTransformStatus(transformStatus);
        //保存课件
        super.save(courseware);
        baseLibraryRecordService.saveOperationRecord(courseware.getCwName(), 1, HandleTypeEnum.TP_ADD_RECORD,
            LibraryTypeEnum.LIB_COURSE);
        return courseware.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(EditCwLibraryDTO editCwLibraryDTO) {
        Courseware courseware = new Courseware();
        BeanUtils.copyProperties(editCwLibraryDTO, courseware);
        coursewareDao.updateCoursewareLibrary(courseware);
        baseLibraryRecordService.saveOperationRecord(courseware.getCwName(), 1, HandleTypeEnum.TP_UPDATE_RECORD,
            LibraryTypeEnum.LIB_COURSE);
    }

    private void replenishParam(CourseWareLibraryQuery courseWareLibraryQuery) {
        String currentUserId = UserThreadContext.getUserId();
        courseWareLibraryQuery.setCurrentUserId(currentUserId);
        courseWareLibraryQuery.setCurrentOrgId(UserThreadContext.getOrgId());
        courseWareLibraryQuery.setUserManageAreaOrgIdList(orgFeign.findUserManageAreaLevelPath(currentUserId));
        if (StringUtils.isNotBlank(courseWareLibraryQuery.getCreateByIds())) {
            courseWareLibraryQuery.setCreateByIdsVo(
                Arrays.asList(courseWareLibraryQuery.getCreateByIds().split(TAG_ID_SEPARATOR)));
        }
        if (StringUtils.isNotBlank(courseWareLibraryQuery.getUpdateByIds())) {
            courseWareLibraryQuery.setUpdateByIdsVo(
                Arrays.asList(courseWareLibraryQuery.getUpdateByIds().split(TAG_ID_SEPARATOR)));
        }
    }

    @Override
    public void downloadCourseWare(HttpServletRequest request, HttpServletResponse response, String cwId) {
        // default implementation ignored
    }

    @Override
    public void storage(String ids) {
        List<String> tagIds = TranslateUtil.translateBySplit(ids, String.class);
        // 获取待更新资源状态的课件列表
        LambdaQueryWrapper<Courseware> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Courseware::getIsSource, 8);
        queryWrapper.in(Courseware::getId, tagIds);
        List<Courseware> list = this.list(queryWrapper);

        // 遍历执行资源状态更新，同时记录日志
        list.forEach(e -> {
            Courseware courseware = new Courseware();
            courseware.setId(e.getId());
            courseware.setCwName(e.getCwName());
            courseware.setIsSource(3);
            coursewareDao.updateCoursewareLibrary(courseware);
        });
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addAllCourseWare(AddAllCourseWareDTO addAllCourseWareDTO) {
        for (String libraryId : addAllCourseWareDTO.getLibraryIdList()) {
            SaveCourseWareDTO saveCourseWareDTO = new SaveCourseWareDTO();
            saveCourseWareDTO.setCourseId(addAllCourseWareDTO.getCourseId());
            saveCourseWareDTO.setLibraryId(libraryId);
            saveCourseWareDTO.setIsSource(IS_SOURCE_LIBRARY);
            Courseware courseware = this.getById(libraryId);
            saveCourseWareDTO.setCwName(courseware.getCwName());
            saveCourseWareDTO.setCwType(courseware.getCwType());
            saveCourseWareDTO.setPlayTime(courseware.getPlayTime());
            saveCourseWareDTO.setRealPlayTime(courseware.getRealPlayTime());
            ICoursewareService coursewareService = SpringUtil.getBean(COURSEWARE_SERVICE, ICoursewareService.class);
            assert coursewareService != null;
            coursewareService.saveCourseWare(saveCourseWareDTO);
        }
    }

    @Override
    public void exportData(CourseWareLearnQuery coursewareLearnQuery) {

        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<ICoursewareService, CourseWareLearnListDTO>(
            coursewareLearnQuery) {

            @Override
            protected ICoursewareService getBean() {
                return SpringUtil.getBean(COURSEWARE_SERVICE, ICoursewareService.class);
            }

            @Override
            protected PageInfo<CourseWareLearnListDTO> getPageInfo() {
                return getBean().getUserDetailData((CourseWareLearnQuery) queryDTO);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.CourseWareLearn;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.CourseWareLearn.getType();
            }

            @Override
            protected void afterCreateBeanMap(Map<String, Object> map) {
                Object learnState = map.get(LEARN_STATE);
                if (Objects.equals(learnState, 1)) {
                    map.put(LEARN_STATE, "完成");
                } else {
                    map.put(LEARN_STATE, "学习中");
                }
                Object userScore = map.get("userScore");
                if (Objects.equals(userScore, BigDecimal.valueOf(-1.0))) {
                    map.put("userScore", null);
                }
                Object orgName = map.get("orgName");
                map.put("manageOrgName", orgName);

            }
        };

        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public void exportLib(CourseWareLibraryQuery courseWareLibraryQuery) {

        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<ICoursewareService, CourseWareLibraryDTO>(
            courseWareLibraryQuery) {

            @Override
            protected ICoursewareService getBean() {
                return SpringUtil.getBean(COURSEWARE_SERVICE, ICoursewareService.class);
            }

            @Override
            protected PageInfo<CourseWareLibraryDTO> getPageInfo() {
                return getBean().getCwLibraryListData((CourseWareLibraryQuery) queryDTO);

            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.CourseWareLib;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.CourseWareLib.getType();
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public String getPlayTime() {
        String playTime = Optional.ofNullable(
                paraFeign.getParaValue(SysDictCodeType.GRAPHIC_COURSE_WARE_LEARN_TIME.getValue()))
            .orElse(StringUtils.EMPTY);
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(playTime);
        ArrayList<String> paraCodeList = new ArrayList<>();
        paraCodeList.add("302");
        paraCodeList.add("303");
        List<ParaDTO> paraByCodeList = paraFeign.getParaByCodeList(paraCodeList);
        if (!CollectionUtils.isEmpty(paraByCodeList)) {
            paraByCodeList = paraByCodeList.stream()
                .sorted(Comparator.comparing(ParaDTO::getParaCode, Comparator.nullsFirst(String::compareTo)))
                .collect(Collectors.toList());
            paraByCodeList.forEach(para -> {
                stringBuilder.append("|").append(para.getParaValue());
            });
        }
        return stringBuilder.toString();
    }

    @Override
    public String saveLibCourseWare(CourseWareInfoApiDTO dto) {

        Courseware libCourseWare = getOne(new LambdaQueryWrapper<Courseware>().eq(Courseware::getId, dto.getOldId()));

        Courseware courseware = new Courseware();
        BeanUtils.copyProperties(libCourseWare, courseware);

        courseware.setId(newId());
        courseware.setCourseId(dto.getCourseId());
        courseware.setIsAvailable(1);
        courseware.setDescriptions(null);
        courseware.setCreateBy(UserThreadContext.getUserId());
        courseware.setCreateTime(new Date());
        courseware.setUpdateBy(null);
        courseware.setUpdateTime(null);
        courseware.setPlayTime(dto.getPlayTime());
        courseware.setIsSource(CWCreateSourceEnum.REPOSITORY.getSource());

        // 复制files文件
        fileFeign.copySameBizFile(libCourseWare.getId(), courseware.getId(), FileBizType.CourseWareFile.name());
        courseware.setCwContent(StringUtils.EMPTY);
        save(courseware);
        return courseware.getId();
    }

    @Override
    public String saveFileCourseWare(CourseWareInfoApiDTO dto) {
        Courseware courseware = new Courseware();
        courseware.setId(newId());

        BeanUtils.copyProperties(dto, courseware);
        courseware.setIsAvailable(1);
        courseware.setDescriptions(null);
        courseware.setCreateBy(UserThreadContext.getUserId());
        courseware.setCreateTime(new Date());
        courseware.setUpdateBy(null);
        courseware.setUpdateTime(null);
        courseware.setIsSource(CWCreateSourceEnum.UPLOAD.getSource());

        SaveCourseWareDTO saveCourseWareDTO = new SaveCourseWareDTO();
        BeanUtils.copyProperties(dto, saveCourseWareDTO);

        //上传课件根据类型去做转码处理
        ApiAssert.notNullParams(dto.getFilePath());
        // 保存源文件
        fileFeign.saveSourceFile(courseware.getId(), FileBizType.CourseWareFile.name(), dto.getFileName(),
            dto.getFilePath());
        Integer transformStatus = fileResourceOptComponent.dealWithFileResourceUpload(courseware.getId(),
            saveCourseWareDTO);

        courseware.setTransformStatus(transformStatus);
        courseware.setCwContent(StringUtils.EMPTY);
        save(courseware);
        return courseware.getId();
    }

    @Override
    public String voteCourseWare2Lib(String id) {
        Courseware courseware = getOne(new LambdaQueryWrapper<Courseware>().eq(Courseware::getId, id));

        Courseware libCourseware = new Courseware();
        BeanUtils.copyProperties(courseware, libCourseware);
        libCourseware.setId(newId());
        libCourseware.setCreateBy(UserThreadContext.getUserId());
        libCourseware.setCreateTime(new Date());
        libCourseware.setCourseId(null);
        libCourseware.setIsAvailable(1);
        libCourseware.setDescriptions(null);
        libCourseware.setUpdateBy(UserThreadContext.getUserId());
        libCourseware.setUpdateTime(new Date());
        libCourseware.setIsSource(3);
        libCourseware.setOrgId(UserThreadContext.getOrgId());
        save(libCourseware);

        // 复制files文件
        fileFeign.copySameBizFile(courseware.getId(), libCourseware.getId(), FileBizType.CourseWareFile.name());

        baseLibraryRecordService.saveOperationRecord(libCourseware.getCwName(), 1, HandleTypeEnum.TP_ADD_RECORD,
            LibraryTypeEnum.LIB_COURSE);
        return libCourseware.getId();
    }

    @Override
    public List<ParaDTO> getCoursePara() {
        ArrayList<String> paraCodeList = new ArrayList<>();
        paraCodeList.add("301");
        paraCodeList.add("302");
        paraCodeList.add("303");
        return paraFeign.getParaByCodeList(paraCodeList);
    }

    @Override
    public List<com.wunding.learn.course.api.dto.CourseWareDTO> getCourseWareByUserId(String userId) {
        return baseMapper.getCourseWareByUserId(userId);
    }

    @Override
    public List<CourseWareNameDetailDTO> getCourseWareNameDetailByCwIds(Collection<String> cwIds) {
        if (CollectionUtils.isEmpty(cwIds)) {
            return Collections.emptyList();
        }
        return baseMapper.getCourseWareNameDetailByCwIds(cwIds);
    }

    @Override
    public BigDecimal getTotalCoursewareDuration(String courseId) {
        return baseMapper.getTotalCoursewareDuration(courseId);
    }

    @Override
    public void reTranscoding() {
        // 1.筛选哪些课件需要转码
        List<Courseware> coursewareList = list().stream().filter(
            courseware ->
                (courseware.getCwType().equals(CWTypeEnum.PDF.name()) || courseware.getCwType()
                    .equals(CWTypeEnum.PPT.name()) || courseware.getCwType().equals(CWTypeEnum.Word.name())) &&
                    (courseware.getIsSource() == UPLOAD.getSource() || courseware.getIsSource() == REFERENCE
                        .getSource())
                    && courseware.getIsCopy() == 0
        ).collect(Collectors.toList());
        List<String> trainsCwIds = new ArrayList<>();
        List<Courseware> reTransCoursewares = new ArrayList<>();
        if (coursewareList.isEmpty()) {
            return;
        }
        List<List<Courseware>> list = Lists.partition(coursewareList, 100);
        for (List<Courseware> coursewares : list) {
            trainsCwIds.clear();
            reTransCoursewares.clear();
            for (Courseware courseware : coursewares) {
                if (TranscodeStatusEnum.TRANSFORMING.value == courseware.getTransformStatus()) {
                    continue;
                }
                courseware.setTransformStatus(TranscodeStatusEnum.TRANSFORMING.value);
                trainsCwIds.add(courseware.getId());
                reTransCoursewares.add(courseware);
            }
            if (trainsCwIds.isEmpty()) {
                return;
            }
            // 2.将课件转码状态设置为正在转码
            updateBatchById2(reTransCoursewares);
            // 3.将转码文件地址修改为源文件地址
            fileFeign.resetTransFile(trainsCwIds, FileBizType.CourseWareFile.name());

            // 查询文件id、path
            Map<String, NamePath> fileNamePathMap = fileFeign.getSourceFileMapByCategoryIds(trainsCwIds);
            // 4.重新发送转码mq
            for (Courseware reTransCourseware : reTransCoursewares) {
                fileResourceOptComponent.reTranscoding(reTransCourseware.getId(), FileBizType.CourseWareFile.name(),
                    reTransCourseware.getMime(), reTransCourseware.getCwType(), null,
                    fileNamePathMap.get(reTransCourseware.getId()));
            }
        }
    }

    @Override
    public List<CourseWareDTO> findCourseWareListByCourseId(String courseId) {
        return baseMapper.findCourseWareListByCourseId(courseId);
    }

    @Override
    public List<CourseWareQuestionDTO> findQuestionListByCourseWareId(String courseWareId) {
        return coursewareQuestionMapper.findQuestionListByCourseWareId(courseWareId);
    }

    @Override
    public PageInfo<CwQuestionAnswerRecordDTO> queryCoursewareQuestionAnswerRecordList(
        CwQuestionAnswerRecordQuery query) {
        String coursewareId = query.getCoursewareId();
        List<String> questionIdList = query.getQuestionIdList();
        // 这两个条件必须选择
        if (StringUtils.isEmpty(coursewareId) || CollectionUtils.isEmpty(questionIdList)) {
            return new PageInfo<>();
        }
        query.setUserIds(TranslateUtil.translateBySplit(query.getUserIdList(), String.class));
        Courseware courseware = this.getById(coursewareId);
        if (courseware == null) {
            return new PageInfo<>();
        }
        ViewLimitBaseInfoDTO viewLimitBaseInfo = courseViewLimitComponent.getViewLimitBaseInfo(
            courseware.getCourseId());
        query.setViewLimitId(
            Optional.ofNullable(viewLimitBaseInfo).isPresent() ? viewLimitBaseInfo.getProgrammeId() : null);
        PageInfo<CwQuestionAnswerRecordDTO> pageInfo = PageMethod.startPage(query.getPageNo(),
                query.getPageSize())
            .doSelectPageInfo(() -> baseMapper.queryCoursewareQuestionAnswerRecordListByViewLimit(query));
        List<CwQuestionAnswerRecordDTO> list = pageInfo.getList();
        if (CollectionUtils.isEmpty(list)) {
            return new PageInfo<>();
        }
        // 查询用户信息
        List<String> userIds = list.stream().map(CwQuestionAnswerRecordDTO::getUserId).collect(Collectors.toList());
        Map<String, UserOrgDTO> userNameMapByIds = orgFeign.getUserAndOrgMapByUserIds(userIds);
        // 查询题目
        List<CoursewareQuestion> coursewareQuestionList = coursewareQuestionService.lambdaQuery()
            .in(CoursewareQuestion::getId, questionIdList)
            .orderByAsc(CoursewareQuestion::getShowTime)
            .orderByAsc(CoursewareQuestion::getSortNo)
            .orderByAsc(CoursewareQuestion::getId)
            .list();
        // 批量查询每个人的第一次答题记录
        Map<String, List<CoursewareQuestionAnswerRecord>> userAnswerMap = coursewareQuestionAnswerRecordService
            .lambdaQuery()
            .in(CoursewareQuestionAnswerRecord::getQuestionId, questionIdList)
            .in(CoursewareQuestionAnswerRecord::getUserId, userIds)
            .eq(CoursewareQuestionAnswerRecord::getIsFirst, GeneralJudgeEnum.CONFIRM.getValue()).list().stream()
            .collect(Collectors.groupingBy(CoursewareQuestionAnswerRecord::getUserId));
        for (CwQuestionAnswerRecordDTO dto : list) {
            dto.setCoursewareName(courseware.getCwName());
            List<CourseWareQuestionDTO> courseWareQuestionList = new ArrayList<>();
            Map<String, CoursewareQuestionAnswerRecord> answerRecordMap = new HashMap<>();
            List<CoursewareQuestionAnswerRecord> answerRecordList = userAnswerMap.get(dto.getUserId());
            if (!CollectionUtils.isEmpty(answerRecordList)) {
                answerRecordMap = answerRecordList.stream().collect(
                    Collectors.toMap(CoursewareQuestionAnswerRecord::getQuestionId, Function.identity(),
                        (key1, key2) -> key1));
            }
            for (CoursewareQuestion question : coursewareQuestionList) {
                CourseWareQuestionDTO courseWareQuestionDTO = new CourseWareQuestionDTO();
                courseWareQuestionDTO.setId(question.getId());
                courseWareQuestionDTO.setQuestionName(question.getQuestionName());
                // 未学习则一定是未答题
                if (!dto.getLearnState().equals(-1)) {
                    Optional.ofNullable(answerRecordMap.get(question.getId())).ifPresent(answerRecord -> {
                        courseWareQuestionDTO.setIsCorrect(answerRecord.getIsCorrect());
                        courseWareQuestionDTO.setAnswerTime(answerRecord.getAnswerTime());
                    });
                }
                courseWareQuestionList.add(courseWareQuestionDTO);
            }
            dto.setCourseWareQuestionList(courseWareQuestionList);
            String userId = dto.getUserId();
            UserOrgDTO userDTO = userNameMapByIds.get(userId);
            if (userDTO == null) {
                continue;
            }
            dto.setFullName(userDTO.getFullName());
            dto.setOrgName(userDTO.getOrgShortName());
            dto.setLevelPathName(userDTO.getShowLevel());
            dto.setLoginName(userDTO.getLoginName());
        }
        pageInfo.setList(list);
        return pageInfo;
    }

    @Override
    public AnswerResultDTO answerCourseWareQuestion(AnswerCourseWareQuestionDTO dto) {
        AnswerResultDTO result = new AnswerResultDTO();
        CoursewareQuestion one = coursewareQuestionService.lambdaQuery()
            .eq(CoursewareQuestion::getId, dto.getQuestionId()).one();
        if (one == null) {
            return result;
        }
        List<CoursewareQuestionOption> list = coursewareQuestionOptionService.lambdaQuery()
            .eq(CoursewareQuestionOption::getQuestionId, dto.getQuestionId())
            .eq(CoursewareQuestionOption::getIsReference, 1).list();
        List<String> correctOptionIdList = list.stream().map(CoursewareQuestionOption::getId)
            .collect(Collectors.toList());
        String optionId = dto.getOptionId();
        List<String> paramOptionId = Arrays.asList(optionId.split(","));
        String userId = UserThreadContext.getUserId();
        CoursewareQuestionAnswerRecord coursewareQuestionAnswerRecord = new CoursewareQuestionAnswerRecord();
        coursewareQuestionAnswerRecord.setId(newId());
        coursewareQuestionAnswerRecord.setCoursewareId(dto.getCoursewareId());
        coursewareQuestionAnswerRecord.setQuestionId(dto.getQuestionId());
        coursewareQuestionAnswerRecord.setOptionId(optionId);
        coursewareQuestionAnswerRecord.setUserId(userId);
        coursewareQuestionAnswerRecord.setAnswerTime(new Date());
        String answer = one.getAnswer();
        if (correctOptionIdList.stream().sorted().collect(Collectors.joining())
            .equals(paramOptionId.stream().sorted().collect(Collectors.joining()))) {
            coursewareQuestionAnswerRecord.setIsCorrect(1);
        } else {
            coursewareQuestionAnswerRecord.setIsCorrect(0);
        }
        coursewareQuestionAnswerRecord.setCreateBy(userId);
        coursewareQuestionAnswerRecord.setCreateTime(new Date());
        Long count = coursewareQuestionAnswerRecordService.lambdaQuery()
            .eq(CoursewareQuestionAnswerRecord::getCoursewareId, dto.getCoursewareId())
            .eq(CoursewareQuestionAnswerRecord::getQuestionId, dto.getQuestionId())
            .eq(CoursewareQuestionAnswerRecord::getUserId, userId)
            .count();
        if (count <= 0) {
            coursewareQuestionAnswerRecord.setIsFirst(1);
        }
        coursewareQuestionAnswerRecordService.save(coursewareQuestionAnswerRecord);

        result.setIsCorrect(coursewareQuestionAnswerRecord.getIsCorrect());
        result.setAnswer(answer);
        result.setQuestionDesc(one.getQuestionDesc());
        return result;
    }

    @Async
    @Override
    public void exportCoursewareQuestionAnswerRecordList(CwQuestionAnswerRecordQuery query) {
        IExportNoEntityDataDTO exportDataDTO = new AbstractExportNoEntityDataDTO<ICoursewareService, CwQuestionAnswerRecordDTO>(
            query) {

            @Override
            protected ICoursewareService getBean() {
                return SpringUtil.getBean(COURSEWARE_SERVICE, ICoursewareService.class);
            }

            @Override
            protected List<List<Object>> getPageInfo() {
                List<List<Object>> data = ListUtils.newArrayList();
                PageInfo<CwQuestionAnswerRecordDTO> pageInfo = getBean().queryCoursewareQuestionAnswerRecordList(query);
                pageInfo.getList().forEach(dto -> {
                    List<Object> rowData = ListUtils.newArrayList();
                    rowData.add(dto.getFullName());
                    rowData.add(dto.getLoginName());
                    rowData.add(dto.getOrgName());
                    rowData.add(dto.getCoursewareName());
                    if (dto.getLearnState() == 0) {
                        rowData.add("学习中");
                    } else if (dto.getLearnState() == 1) {
                        rowData.add("已学习");
                    } else {
                        rowData.add("未开始");
                    }

                    dto.getCourseWareQuestionList().forEach(question -> {
                        Optional.ofNullable(question.getIsCorrect()).ifPresent(isCorrect -> {
                            if (isCorrect == 0) {
                                rowData.add(
                                    "N:" + DateUtil.formatDate(question.getAnswerTime(), DateUtil.YYMMDD_HHMMSS));
                            } else if (isCorrect == 1) {
                                rowData.add(
                                    "Y:" + DateUtil.formatDate(question.getAnswerTime(), DateUtil.YYMMDD_HHMMSS));
                            }
                        });
                    });
                    data.add(rowData);
                });
                return data;
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.CoursewareQuestionAnswerRecord;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.CoursewareQuestionAnswerRecord.getType();
            }
        };

        List<List<String>> head = getExportHeader(query);
        exportComponent.exportNoEntityRecord(exportDataDTO, head);
    }

    /**
     * 构建课件答题明细统计数据导出excel表头
     */
    private List<List<String>> getExportHeader(CwQuestionAnswerRecordQuery query) {
        List<List<String>> head = new ArrayList<>();
        head.add(List.of("姓名"));
        head.add(List.of("账户"));
        head.add(List.of("部门"));
        head.add(List.of("课件"));
        head.add(List.of("学习状态"));
        List<String> questionIdList = query.getQuestionIdList();
        List<CoursewareQuestion> coursewareQuestionList = coursewareQuestionService.lambdaQuery()
            .in(CoursewareQuestion::getId, questionIdList)
            .orderByAsc(CoursewareQuestion::getShowTime)
            .orderByAsc(CoursewareQuestion::getSortNo)
            .orderByAsc(CoursewareQuestion::getId)
            .list();
        for (CoursewareQuestion question : coursewareQuestionList) {
            head.add(List.of(question.getQuestionName()));
        }
        return head;
    }


    @Override
    public AiSwitchConfigDTO aiSwitch() {
        AiSwitchConfigDTO aiSwitchConfigDTO = new AiSwitchConfigDTO();

        AiBaseConfigDTO aiConfig = aiBaseConfigFeign.getAiConfig();

        List<AiSwitchConfigItem> aiSwitchList = new ArrayList<>();
        if (Objects.equals(getAiCwDesc(aiConfig), 1)) {
            aiSwitchList
                .add(new AiSwitchConfigItem(AiTypeConstant.AI_CW_DESC.getValue(), AiTypeConstant.AI_CW_DESC.getName(),
                    aiConfig.getAiCwDescCount()));
        }
        if (Objects.equals(getAiCwOutline(aiConfig), 1)) {
            aiSwitchList.add(
                new AiSwitchConfigItem(AiTypeConstant.AI_CW_OUTLINE.getValue(),
                    AiTypeConstant.AI_CW_OUTLINE.getName()));
        }
        if (Objects.equals(getAiCwKeyword(aiConfig), 1)) {
            aiSwitchList.add(
                new AiSwitchConfigItem(AiTypeConstant.AI_CW_KEYWORD.getValue(),
                    AiTypeConstant.AI_CW_KEYWORD.getName()));
        }
        if (Objects.equals(getAiCwQuestion(aiConfig), 1)) {
            aiSwitchList.add(
                new AiSwitchConfigItem(AiTypeConstant.AI_CW_QUESTION.getValue(),
                    AiTypeConstant.AI_CW_QUESTION.getName(),
                    aiConfig.getAiCwQuestionCount()));
        }
        if (Objects.equals(aiConfig.getAiCourseAsk(), 1)) {
            aiSwitchList.add(
                new AiSwitchConfigItem(AiTypeConstant.AI_COURSE_ASK.getValue(),
                    AiTypeConstant.AI_COURSE_ASK.getName()));
        }
        aiSwitchConfigDTO.setAiSwitch(aiSwitchList);
        return aiSwitchConfigDTO;
    }

    @Override
    public AiGeneratedContentDTO aiGeneratedContent(String cwId) {
        AiGeneratedContentDTO aiGeneratedContentDTO = new AiGeneratedContentDTO();
        Courseware courseware = coursewareDao.getById(cwId);
        aiGeneratedContentDTO.setCwId(courseware.getId());
        aiGeneratedContentDTO.setAiCwDesc(courseware.getDescriptions());
        aiGeneratedContentDTO.setAiOutlineItemDTOList(aiOutlineItemService.getAiOutlineItemDTOListByResourceId(cwId));
        return aiGeneratedContentDTO;
    }

    @Override
    public void saveCwDesc(SaveCwDescDTO saveCwDescDTO) {

        Courseware courseware = new Courseware();
        courseware.setId(saveCwDescDTO.getCwId());
        courseware.setDescriptions(saveCwDescDTO.getAiCwDesc());
        coursewareDao.updateCourseware(courseware);
    }

    private void checkAiCwSourceType(String cwId) {
        Courseware cw = getById(cwId);
        if (!Objects.equals(cw.getIsSource(), UPLOAD.getSource())) {
            throw new BusinessException(CourseErrorNoEnum.ERR_AI_GENERATE_CW_TYPE);
        }
        if (!Objects.equals(cw.getTransformStatus(), TranscodeStatusEnum.TRANSFORMED.value)) {
            throw new BusinessException(CourseErrorNoEnum.ERR_AI_GENERATE_CW_TRANS);
        }
    }

    private void checkFileExt(String fileExt) {
        if (Objects.equals(fileExt, "zip")) {
            log.info("zip ai不生成内容");
            throw new BusinessException(CourseErrorNoEnum.ERR_AI_GENERATE_CW_TYPE);
        }
    }


    /**
     * 根据不同的文件类型和AI配置，生成相应的AI描述内容。
     *
     * @param reGenerate 重新生成DTO，包含需要生成AI描述的课程ware的ID。
     * @param type       生成的类型，用于确定生成策略。
     */
    @Override
    public Long generate(ReGenerateDTO reGenerate, String type) {
        // 校验课程ware的来源类型是否支持
        checkAiCwSourceType(reGenerate.getCwId());

        // 通过远程调用获取源文件信息
        NamePath sourceFileInfo = fileFeign.getSourceFileInfo(reGenerate.getCwId(),
            FileBizType.CourseWareFile.name());
        // 解析出文件路径和扩展名
        String filePath = sourceFileInfo.getPath();
        String fileExt = FilenameUtils.getExtension(filePath).toLowerCase();
        // 校验文件扩展名是否支持
        checkFileExt(fileExt);

        //更新生成题目最后一次生成配置
        saveLastGenerateQuestionCount(reGenerate, type);

        // 获取AI基础配置信息
        AiBaseConfigDTO aiConfig = aiBaseConfigFeign.getAiConfig();
        // 处理参数中的配置.
        // 数量
        AiCwSaveConfig param = reGenerate.getAiSwitch().stream()
            .filter(c -> c.getId().equals(AiTypeConstant.AI_CW_QUESTION.getValue()))
            .findFirst()
            .orElse(new AiCwSaveConfig());
        if (
            Objects.equals(param.getId(), AiTypeConstant.AI_CW_QUESTION.getValue())
                && param.getQuestionCount() > aiConfig.getAiCwQuestionCount()
        ) {
            throw new BusinessException(CourseErrorNoEnum.ERR_AI_QUESTION_COUNT, null,
                aiConfig.getAiCwQuestionCount().toString());
        }
        aiConfig.setAiCwQuestionCount(param.getQuestionCount());
        // 题目类型
        List<Integer> questionTypeList = param.getQuestionTypeList();
        String replacement = getQuestionTypePromptKeyword(questionTypeList);
        String aiCwQuestionPrompt1 = aiConfig.getAiCwQuestionPrompt1();
        String aiCwQuestionPrompt2 = aiConfig.getAiCwQuestionPrompt2();
        aiCwQuestionPrompt1 = aiCwQuestionPrompt1.replace("{{题型}}", replacement);
        aiCwQuestionPrompt2 = aiCwQuestionPrompt2.replace("{{题型}}", replacement);
        aiConfig.setAiCwQuestionPrompt1(aiCwQuestionPrompt1);
        aiConfig.setAiCwQuestionPrompt2(aiCwQuestionPrompt2);

        // 根据类型和AI配置生成提示信息和消息
        String prompt = getPrompt(type, aiConfig);
        String message = getAiMessage(type, aiConfig);

        // 定义支持的视频文件扩展名列表
        List<String> extList = List.of("mp4", "mp3", "avi", "mpeg", "mpg", "navi", "asf",
            "mov", "3gp", "wmv", "divx", "xvid", "rm", "rmvb", "flv", "f4v");
        // 如果是视频文件，则修改文件路径为文本文件路径，并更新提示信息
        if (extList.contains(fileExt)) {
            filePath = FilenameUtils.getPath(filePath) + FilenameUtils.getBaseName(filePath) + ".txt";
            prompt = getPrompt(type, aiConfig, 2);
        }
        // 如果是PPT或DOC文件，则修改文件路径为PDF路径
        if (
            Objects.equals(fileExt, "ppt")
                || Objects.equals(fileExt, "pptx")
                || Objects.equals(fileExt, "doc")
        ) {
            filePath = FilenameUtils.getPath(filePath) + FilenameUtils.getBaseName(filePath) + "/index.pdf";
        }
        // 记录生成任务的日志信息
        log.info(
            "ai generate type:{}, path:{}, prompt:{}, message:{}",
            type,
            filePath,
            prompt,
            message
        );
        // 保存AI生成内容的记录
        AiGeneratedContentRecord aiGeneratedContentRecord = aiGeneratedContentRecordService
            .saveAiGeneratedContentRecord(
                type,
                filePath,
                prompt,
                message
            );
        // 初始化生成的内容
        // 调用AI服务生成内容
        String finalFilePath = filePath;
        String finalPrompt = prompt;
        CompletableFuture<String> aiGeneratedFuture = CompletableFuture
            .supplyAsync(() ->
                aiService.generatedContent(
                    finalFilePath,
                    finalPrompt,
                    message
                ), asyncThreadPool)
            .thenApply(content -> {
                // 更新生成记录的内容和状态
                log.info("ai desc content:{}", content);
                aiGeneratedContentRecordService.updateAiGeneratedContentRecord(
                    aiGeneratedContentRecord.getId(), content, 2);
                if (Objects.equals(type, AiTypeConstant.AI_CW_DESC.getValue())) {
                    // 如果是AI生成，则更新课程ware的描述信息
                    saveCwDesc(reGenerate.getCwId(), content);
                } else if (Objects.equals(type, AiTypeConstant.AI_CW_QUESTION.getValue())) {
                    saveCwQuestion(reGenerate.getCwId(), content);
                }
                return content;
            }).exceptionally(e -> {
                // 如果生成过程中出现异常，更新生成记录的状态和错误信息
                aiGeneratedContentRecordService.updateAiGeneratedContentRecord(aiGeneratedContentRecord.getId(), "",
                    3);
                log.error("ai generated content error ", e);
                return null;
            });
        if (!Objects.equals(type, AiTypeConstant.AI_CW_QUESTION.getValue())) {
            try {
                String content = aiGeneratedFuture.get();
                log.info("ai generated content:{}", content);
            } catch (InterruptedException e) {
                // 如果生成过程中出现异常，更新生成记录的状态和错误信息
                aiGeneratedContentRecordService.updateAiGeneratedContentRecord(aiGeneratedContentRecord.getId(), "",
                    3);
                log.error("ai generated content InterruptedException error ", e);
                Thread.currentThread().interrupt();
                return null;
            } catch (ExecutionException e) {
                // 如果生成过程中出现异常，更新生成记录的状态和错误信息
                aiGeneratedContentRecordService.updateAiGeneratedContentRecord(aiGeneratedContentRecord.getId(), "",
                    3);
                log.error("ai generated content ExecutionException error ", e);
                return null;
            }
        }

        HashOperations<String, String, Long> operations = redisTemplate.opsForHash();
        operations.put("AI_GENERATED_IDS_" + UserThreadContext.getUserId() + "_" + reGenerate.getCwId(), type,
            aiGeneratedContentRecord.getId());
        return aiGeneratedContentRecord.getId();

    }

    private static String getQuestionTypePromptKeyword(List<Integer> questionTypeList) {
        List<String> replacementList = new ArrayList<>();
        if (questionTypeList.contains(1)) {
            replacementList.add("单选题");
        }
        if (questionTypeList.contains(2)) {
            replacementList.add("多选题");
        }
        if (questionTypeList.contains(4)) {
            replacementList.add("判断题");
        }
        return String.join("、", replacementList);
    }

    private void saveLastGenerateQuestionCount(ReGenerateDTO reGenerate, String type) {
        List<AiCwConfig> aiCwConfigList = aiCwConfigService.getByCwId(reGenerate.getCwId(), List.of(type));
        for (AiCwConfig aiCwConfig : aiCwConfigList) {
            if (Objects.equals(aiCwConfig.getType(), AiTypeConstant.AI_CW_QUESTION.getValue())) {
                for (AiCwSaveConfig aiSwitch : reGenerate.getAiSwitch()) {
                    if (aiSwitch.getId().equals(aiCwConfig.getType())) {
                        aiCwConfig.setCount(aiSwitch.getQuestionCount());
                        aiCwConfig.setContent(StringUtils.join(aiSwitch.getQuestionTypeList(), ","));
                        aiCwConfigService.updateById(aiCwConfig);
                    }
                }
            }
        }
    }


    @Override
    public Integer getGenerateStatus(String cwId, String type) {

        HashOperations<String, String, Object> operations = redisTemplate.opsForHash();
        Long id = null;
        try {
            id = Long.parseLong(
                operations.get("AI_GENERATED_IDS_" + UserThreadContext.getUserId() + "_" + cwId, type) + "");
        } catch (NumberFormatException e) {
            log.error("get ai generated id failed, cwId: {}, type: {} , id: ｛｝", cwId, type, id);
        }
        if (id == null) {
            return -1;
        }

        AiGeneratedContentRecord aiGeneratedContentRecord = aiGeneratedContentRecordService.getById(id);
        if (aiGeneratedContentRecord == null) {
            return -1;
        }
        return aiGeneratedContentRecord.getStatus();
    }

    private void saveCwDesc(String cwId, String descriptions) {
        // 更新课程ware的描述信息
        Courseware courseware = new Courseware();
        courseware.setId(cwId);
        courseware.setDescriptions(descriptions);
        coursewareDao.updateCourseware(courseware);
    }

    private void saveCwQuestion(String cwId, String content) {

        if (StringUtils.isEmpty(content)) {
            log.error("ai question content is empty");
            return;
        }

        try {
            content = dealWithContent(content);
        } catch (JsonProcessingException e) {
            log.error("convert content failed, content: {}", content);
            throw new BusinessException(CourseErrorNoEnum.ERR_AI_GENERATE_QUESTIONS);
        }

        List<AiQuestionDTO> list = JsonUtil.json2List(content, AiQuestionDTO.class);
        if (CollectionUtils.isEmpty(list)) {
            log.error("convert content to aiQuestionDto failed, content: {}", content);
            throw new BusinessException(CourseErrorNoEnum.ERR_AI_GENERATE_QUESTIONS);
        }

        saveCourseCwAiQuestion(cwId, list);

    }

    @Override
    public void saveCourseCwAiQuestion(String cwId, List<AiQuestionDTO> list) {
        Courseware courseware = getById(cwId);
        String batchId = DateUtil.getYmdhmsStr();
        for (AiQuestionDTO aiQuestionDTO : list) {
            CourseCwAiQuestion courseCwAiQuestion = new CourseCwAiQuestion();
            courseCwAiQuestion.setCourseId(courseware.getCourseId());
            courseCwAiQuestion.setCwId(cwId);
            courseCwAiQuestion.setBatchId(batchId);
            courseCwAiQuestion.setQuestionType(aiQuestionDTO.getQuestionType());
            courseCwAiQuestion.setQuestionName(aiQuestionDTO.getQuestion());
            courseCwAiQuestion.setContentTime(aiQuestionDTO.getContentTime());
            courseCwAiQuestion.setAnswer(aiQuestionDTO.getAnswer());
            courseCwAiQuestion.setQuestionDesc(aiQuestionDTO.getAnswerAnalysis());
            courseCwAiQuestion.setCreateBy(UserThreadContext.getUserId());
            courseCwAiQuestion.setCreateTime(new Date());

            courseCwAiQuestionService.save(courseCwAiQuestion);
            if (!CollectionUtils.isEmpty(aiQuestionDTO.getOption())) {
                for (AiQuestionOptionDTO aiQuestionOptionDTO : aiQuestionDTO.getOption()) {
                    CourseCwAiQuestionOption courseCwAiQuestionOption = new CourseCwAiQuestionOption();
                    courseCwAiQuestionOption.setQuestionId(courseCwAiQuestion.getId());
                    courseCwAiQuestionOption.setOptionCode(aiQuestionOptionDTO.getOptionCode());
                    courseCwAiQuestionOption.setOptionName(aiQuestionOptionDTO.getOptionName());
                    courseCwAiQuestionOption.setOptionIndex(aiQuestionOptionDTO.getOptionIndex());
                    courseCwAiQuestionOption.setCreateBy(UserThreadContext.getUserId());
                    courseCwAiQuestionOption.setCreateTime(new Date());
                    courseCwAiQuestionOptionService.save(courseCwAiQuestionOption);
                }
            }
        }
    }

    private String dealWithContent(String content) throws JsonProcessingException {
        if (content.startsWith("```json")) {
            log.info("this content is json format");
            content = content.substring(7);
            if (content.endsWith("```")) {
                content = content.substring(0, content.length() - 3);
            }

            if (content.startsWith("{")) {
                content = "[" + content;
            }
            if (content.endsWith("}")) {
                content = content + "]";
            }
        } else if (content.startsWith("```yaml")) {
            log.info("this content is yaml format");
            content = content.substring(7);
            if (content.endsWith("```")) {
                content = content.substring(0, content.length() - 3);
            }

            ObjectMapper yamlReader = new ObjectMapper(new YAMLFactory());
            Object obj = yamlReader.readValue(content, Object.class);

            ObjectMapper jsonWriter = new ObjectMapper();
            content = jsonWriter.writeValueAsString(obj);
        } else {
            log.warn("unKnow this content format, content: {}", content);
        }
        return content;
    }

    @Override
    public CourseInfoDTO getCourseByCourseWareId(String courseWareId) {
        String courseId = getCourseByCwId(courseWareId);
        if (StringUtils.isBlank(courseId)) {
            return null;
        }
        Course course = courseMapper.selectById(courseId);
        if (Objects.isNull(course)) {
            return null;
        }
        CourseInfoDTO courseInfoDTO = new CourseInfoDTO();
        BeanUtils.copyProperties(course, courseInfoDTO);
        return courseInfoDTO;
    }

    @Override
    public Map<String, CourseInfoDTO> getCoursesByCourseWareIds(List<String> courseWareIds) {
        if (courseWareIds.isEmpty()) {
            return new HashMap<>();
        }
        List<String> courseIds = getCourseIdsByCwIds(courseWareIds);
        if (courseIds.isEmpty()) {
            return new HashMap<>();
        }
        //获取课程id和课程本身的map
        Map<String, CourseInfoDTO> courseMap = courseMapper.selectBatchIds(courseIds).stream().map(course -> {
            CourseInfoDTO dto = new CourseInfoDTO();
            BeanUtils.copyProperties(course, dto);
            return dto;
        }).collect(Collectors.toMap(CourseInfoDTO::getId, Function.identity()));
        //获取课件列表
        List<Courseware> coursewareList = lambdaQuery().in(Courseware::getId, courseWareIds).list();

        Map<String, CourseInfoDTO> resultMap = new HashMap<>();
        coursewareList.forEach(cw -> {
            if (courseMap.get(cw.getCourseId()) != null) {
                resultMap.put(cw.getId(), courseMap.get(cw.getCourseId()));
            }
        });

        return resultMap;

    }


    private String getPrompt(String type, AiBaseConfigDTO aiConfig) {
        return getPrompt(type, aiConfig, 1);
    }

    private String getPrompt(String type, AiBaseConfigDTO aiConfig, Integer num) {
        if (Objects.equals(AiTypeConstant.AI_CW_DESC.getValue(), type)) {
            if (num == 1) {
                return aiConfig.getAiCwDescPrompt1();
            }
            return aiConfig.getAiCwDescPrompt2();
        }
        if (Objects.equals(AiTypeConstant.AI_CW_KEYWORD.getValue(), type)) {
            if (num == 1) {
                return aiConfig.getAiCwKeywordPrompt();
            }
            return aiConfig.getAiCwKeywordPrompt();
        }
        if (Objects.equals(AiTypeConstant.AI_CW_QUESTION.getValue(), type)) {
            if (num == 1) {
                return aiConfig.getAiCwQuestionPrompt1();
            }
            return aiConfig.getAiCwQuestionPrompt2();
        }
        if (Objects.equals(AiTypeConstant.AI_CW_OUTLINE.getValue(), type)) {
            if (num == 1) {
                return aiConfig.getAiCwOutlinePrompt1();
            }
            return aiConfig.getAiCwOutlinePrompt2();
        }
        return "";
    }

    private String getAiMessage(String type, AiBaseConfigDTO aiConfig) {
        if (Objects.equals(AiTypeConstant.AI_CW_DESC.getValue(), type)) {
            return aiConfig.getAiCwDescCount() + "";
        }
        if (Objects.equals(AiTypeConstant.AI_CW_KEYWORD.getValue(), type)) {
            return "";
        }
        if (Objects.equals(AiTypeConstant.AI_CW_QUESTION.getValue(), type)) {
            return aiConfig.getAiCwQuestionCount() + "";
        }
        if (Objects.equals(AiTypeConstant.AI_CW_OUTLINE.getValue(), type)) {
            return aiConfig.getAiCwOutlineCount() + "";
        }
        return "";
    }

    private Integer getAiCwDesc(AiBaseConfigDTO aiConfig) {
        if (Objects.equals(aiConfig.getAiSwitch(), 1)) {
            return Integer.parseInt(paraFeign.getParaValue(SystemConfigCodeEnum.AI_MAX_KB_SWITCH_DESC.getCode()));
        }
        return 0;
    }

    private Integer getAiCwKeyword(AiBaseConfigDTO aiConfig) {
        if (Objects.equals(aiConfig.getAiSwitch(), 1)) {
            return Integer.parseInt(paraFeign.getParaValue(SystemConfigCodeEnum.AI_MAX_KB_SWITCH_KEYWORD.getCode()));
        }
        return 0;
    }

    private Integer getAiCwQuestion(AiBaseConfigDTO aiConfig) {
        if (Objects.equals(aiConfig.getAiSwitch(), 1)) {
            return Integer.parseInt(paraFeign.getParaValue(SystemConfigCodeEnum.AI_MAX_KB_SWITCH_QUESTION.getCode()));
        }
        return 0;
    }

    private Integer getAiCwOutline(AiBaseConfigDTO aiConfig) {
        if (Objects.equals(aiConfig.getAiSwitch(), 1)) {
            return Integer.parseInt(paraFeign.getParaValue(SystemConfigCodeEnum.AI_MAX_KB_SWITCH_OUTLINE.getCode()));
        }
        return 0;
    }

    @Override
    public Courseware get(String id) {
        Courseware courseware = baseMapper.selectById(id);
        if (courseware == null) {
            throw new BusinessException(CourseErrorNoEnum.ERR_COURSEWARE_NULL);
        }
        return courseware;
    }

    @Override
    public void updateCoursewareCommonStar(String cwId) {
        BigDecimal commonStar = baseMapper.getCoursewareCommonStarByCoursewareId(cwId);
        baseMapper.updateCoursewareCommonStar(commonStar, cwId);
    }

    @Override
    public void updateCoursewareClickNumber(String cwId) {
        Integer clickNumber = baseMapper.getCoursewareClickNumberByCoursewareId(cwId);
        baseMapper.updateCoursewareClickNumber(clickNumber, cwId);
    }

    @Override
    public void addDownloadRecord(String id) {
        Courseware courseware = getById(id);
        if (courseware == null) {
            return;
        }
        baseLibraryRecordService.saveOperationRecord(courseware.getCwName(), 1, HandleTypeEnum.TP_DOWNLOAD_RECORD,
            LibraryTypeEnum.LIB_COURSE);
    }

    @Override
    @SuppressWarnings("java:S3776")
    public void saveCourseWareBatch(BatchCourseWareDTO batchCourseWareDTO) {
        String courseId = batchCourseWareDTO.getCourseId();
        Course course = courseMapper.selectById(courseId);
        List<Courseware> courseWares = new ArrayList<>();
        // 1、 属性设置遍历循环处理
        // 2、 跨服务调用： 改为单次批量（如 调用 file  、 调用 user 服务）
        //不输入则为当前操作人
        UserDTO userById = userFeign.getUserById(UserThreadContext.getUserId());
        String orgId = UserThreadContext.getOrgId();
        int sortNo = coursewareMapper.getMaxSortNo(courseId);
        // 3、 mq消息：
        for (BatchSaveCourseWareDTO batchSaveCourseWareDTO : batchCourseWareDTO.getBatchSaveCourseWareDTOList()) {
            Courseware courseWare = new Courseware();
            String courseWareId = StringUtil.newId();
            batchSaveCourseWareDTO.setSortNo(++sortNo);
            batchSaveCourseWareDTO.setId(courseWareId);
            BeanUtils.copyProperties(batchSaveCourseWareDTO, courseWare);
            courseWare.setCourseId(courseId);
            
            // 当课件完成类型为按进度时，设置课件学习进度百分比要求为 100%
            if (Objects.equals(batchSaveCourseWareDTO.getFinishType(), 1)) {
                batchSaveCourseWareDTO.setRequiredProgress(100);
            }

            //课件所属组织
            courseWare.setOrgId(orgId);
            //处理不同的课件类型，并返回是否支持保存入库
            //复制一份到课件库
            String libCourseWareId = StringUtil.newId();
            batchSaveCourseWareDTO.setCourseLibId(libCourseWareId);

            //这里课件的下发范围没什么意义，直接使用课程的下发范围
            courseWare.setIsSameCourse(1);
            if (StringUtils.isBlank(courseWare.getCwAuthor())) {
                courseWare.setCwAuthor(null != userById ? userById.getFullName() : null);
            }

            //设置旧的mime,可以理解为转码前的文件mime
            courseWare.setOldMime(batchSaveCourseWareDTO.getMime());

            // 无需转码则默认为转换完成
            courseWare.setTransformStatus(TranscodeStatusEnum.TRANSFORMED.value);
            if (FileResourceEnum.isNeedTransform(batchSaveCourseWareDTO.getFileType())) {
                courseWare.setTransformStatus(TranscodeStatusEnum.PENDING.value);
            }
            courseWares.add(courseWare);
        }

        // 批量保存课件
        coursewareDao.saveBatch(courseWares);

        CompletableFuture.runAsync(() -> {
            // 后置处理，产品交互接受异步处理，  文件处理交互、课件上传到OSS全部异步化，前面的同步仅仅处理DB层数据
            // 沿用单个课件处理的旧逻辑
            Map<String, BatchSaveCourseWareDTO> courseWareDTOMap = batchCourseWareDTO.getBatchSaveCourseWareDTOList()
                .stream()
                .collect(Collectors.toMap(BatchSaveCourseWareDTO::getId, Function.identity()));
            for (Courseware courseWare : courseWares) {
                if (course.getAuditStatus() == 0) {
                    unPublishAuditCourse(courseId, "insert", courseWare);
                }else {
                    unPublishAuditCourse(courseId, "update", courseWare);
                }
                BatchSaveCourseWareDTO batchSaveCourseWareDTO = courseWareDTOMap.get(courseWare.getId());
                String libCourseWareId = StringUtil.newId();
                batchSaveCourseWareDTO.setCourseLibId(libCourseWareId);

                this.disposeCourseWareFile(courseWare, batchSaveCourseWareDTO);

                courseWare.setId(libCourseWareId);

                saveCourseWareToLib(courseWare, batchSaveCourseWareDTO.getId(),
                    batchSaveCourseWareDTO.getIsRepository());

                // 课程相关数据变动，同步变动课程数据
                mqProducer.sendMsg(new SynUpdateCopyCourseEvent(batchSaveCourseWareDTO.getId(),
                    SynUpdateCopyDataEventEnum.COURSEWARE.getKey()));
            }
            // 同步课件时长
            updateCourseSumPlayTime(courseId);

            mqProducer.sendMsg(
                new ResourceOperateEvent(OperationEnum.UPDATE, PushType.COURSE.getKey(), courseId));
        }, courseAsyncThreadPool);
    }

    @Override
    public void difyGenerate(ReGenerateDTO reGenerate, String type) {
        /*// 校验课程ware的来源类型是否支持
        checkAiCwSourceType(reGenerate.getCwId());

        // 通过远程调用获取源文件信息
        NamePath sourceFileInfo = fileFeign.getSourceFileInfo(reGenerate.getCwId(),
            FileBizType.CourseWareFile.name());
        // 解析出文件路径和扩展名
        String filePath = sourceFileInfo.getPath();
        String fileExt = FilenameUtils.getExtension(filePath).toLowerCase();
        // 校验文件扩展名是否支持
        checkFileExt(fileExt);

        //更新生成题目最后一次生成配置
        saveLastGenerateQuestionCount(reGenerate, type);

        // 获取AI基础配置信息
        AiBaseConfigDTO aiConfig = aiBaseConfigFeign.getAiConfig();
        // 处理参数中的配置.
        // 数量
        AiCwSaveConfig param = reGenerate.getAiSwitch().stream()
            .filter(c -> c.getId().equals(AiTypeConstant.AI_CW_QUESTION.getValue()))
            .findFirst()
            .orElse(new AiCwSaveConfig());

        aiConfig.setAiCwQuestionCount(param.getQuestionCount());

        // 定义支持的视频文件扩展名列表
        List<String> extList = List.of("mp4", "mp3", "avi", "mpeg", "mpg", "navi", "asf",
            "mov", "3gp", "wmv", "divx", "xvid", "rm", "rmvb", "flv", "f4v");
        // 如果是视频文件，则修改文件路径为文本文件路径，并更新提示信息
        if (extList.contains(fileExt)) {
            filePath = FilenameUtils.getPath(filePath) + FilenameUtils.getBaseName(filePath) + ".txt";
        }
        // 如果是PPT或DOC文件，则修改文件路径为PDF路径
        if (
            Objects.equals(fileExt, "ppt")
                || Objects.equals(fileExt, "pptx")
                || Objects.equals(fileExt, "doc")
        ) {
            filePath = FilenameUtils.getPath(filePath) + FilenameUtils.getBaseName(filePath) + "/index.pdf";
        }
        // 记录生成任务的日志信息
        log.info(
            "dify ai generate type:{}, path:{}",
            type,
            filePath
        );
        // 保存AI生成内容的记录
        AiGeneratedContentRecord aiGeneratedContentRecord = aiGeneratedContentRecordService
            .saveAiGeneratedContentRecord(
                type,
                filePath,
                "",
                ""
            );
        // 初始化生成的内容
        // 调用AI服务生成内容
        String finalFilePath = filePath;
        Map<String, AiCwSaveConfig> aiTypeMap = reGenerate.getAiSwitch().stream()
            .collect(Collectors.toMap(AiCwSaveConfig::getId, Function.identity(), (key1, key2) -> key1));
        CompletableFuture<String> aiGeneratedFuture = CompletableFuture
            .supplyAsync(() -> difyRunService.generatedContent(aiTypeMap.get(type), finalFilePath, type),
                asyncThreadPool)
            .thenApply(content -> {
                // 更新生成记录的内容和状态
                log.info("ai desc content:{}", content);
                aiGeneratedContentRecordService.updateAiGeneratedContentRecord(
                    aiGeneratedContentRecord.getId(), content, 2);
                if (Objects.equals(type, AiTypeConstant.AI_CW_DESC.getValue())) {
                    // 如果是AI生成，则更新课程ware的描述信息
                    saveCwDesc(reGenerate.getCwId(), content);
                } else if (Objects.equals(type, AiTypeConstant.AI_CW_QUESTION.getValue())) {
                    saveCwQuestion(reGenerate.getCwId(), content);
                }
                return content;
            }).exceptionally(e -> {
                // 如果生成过程中出现异常，更新生成记录的状态和错误信息
                aiGeneratedContentRecordService.updateAiGeneratedContentRecord(aiGeneratedContentRecord.getId(), "",
                    3);
                log.error("ai generated content error ", e);
                return null;
            });
        if (!Objects.equals(type, AiTypeConstant.AI_CW_QUESTION.getValue())) {
            try {
                String content = aiGeneratedFuture.get();
                log.info("ai generated content:{}", content);
            } catch (InterruptedException e) {
                // 如果生成过程中出现异常，更新生成记录的状态和错误信息
                aiGeneratedContentRecordService.updateAiGeneratedContentRecord(aiGeneratedContentRecord.getId(), "",
                    3);
                log.error("ai generated content InterruptedException error ", e);
                Thread.currentThread().interrupt();
                return null;
            } catch (ExecutionException e) {
                // 如果生成过程中出现异常，更新生成记录的状态和错误信息
                aiGeneratedContentRecordService.updateAiGeneratedContentRecord(aiGeneratedContentRecord.getId(), "",
                    3);
                log.error("ai generated content ExecutionException error ", e);
                return null;
            }
        }

        HashOperations<String, String, Long> operations = redisTemplate.opsForHash();
        operations.put("AI_GENERATED_IDS_" + UserThreadContext.getUserId() + "_" + reGenerate.getCwId(), type,
            aiGeneratedContentRecord.getId());
        return aiGeneratedContentRecord.getId();*/
    }

    @Override
    public List<CourseWareAiDTO> getCoursewareAiList(CoursewareQuery coursewareQuery) {
        List<CourseWareDTO> courseWareListByPage = baseMapper.getCourseWareListByPage(coursewareQuery);
        if (!CollectionUtils.isEmpty(courseWareListByPage)) {
            List<CourseWareAiDTO> coursewareAiList = BeanListUtils.copyListProperties(courseWareListByPage,
                CourseWareAiDTO::new);

            // 获取所有课件ID
            List<String> cwIds = coursewareAiList.stream()
                .map(CourseWareAiDTO::getId)
                .collect(Collectors.toList());

            // 批量查询课件向量化状态
            List<CwRefDifyKnowledge> cwRefDifyKnowledgeList = cwRefDifyKnowledgeService.lambdaQuery()
                .in(CwRefDifyKnowledge::getCwId, cwIds)
                .list();

            // 构建课件ID到向量化状态的映射
            Map<String, Integer> cwIdToIndexingStatusMap = cwRefDifyKnowledgeList.stream()
                .collect(
                    Collectors.toMap(CwRefDifyKnowledge::getCwId, CwRefDifyKnowledge::getIndexingStatus, (a, b) -> a));

            // 转码完成的才有文件地址
            coursewareAiList.forEach(courseWare -> {
                if (TranscodeStatusEnum.TRANSFORMED.value == courseWare.getTransformStatus()) {
                    String filePath = getFilePath(courseWare);
                    if (fileFeign.checkFileExists(filePath)) {
                        courseWare.setAiFileUrl(fileFeign.getFileUrl(filePath));
                        courseWare.setAiFileType(FilenameUtils.getExtension(filePath).toLowerCase());
                    } else {
                        courseWare.setAiFileUrl("-1");
                        courseWare.setAiFileType("-1");
                    }
                }

                // 设置向量化状态
                courseWare.setIndexingStatus(cwIdToIndexingStatusMap.getOrDefault(courseWare.getId(), 2));
            });
            return coursewareAiList;
        }
        return new ArrayList<>();
    }

    @Override
    public Long selectCoursewareNum(LambdaQueryWrapper<Courseware> countCourseware) {
        return coursewareMapper.selectCount(countCourseware);
    }

    @Override
    public PageInfo<CoursewareTransformFailDTO> transformFailList(CoursewareTransformFailQuery query) {
        query.setCurrentUserId(UserThreadContext.getUserId());
        PageInfo<CoursewareTransformFailDTO> pageInfo = PageMethod.startPage(query.getPageNo(),
            query.getPageSize()).doSelectPageInfo(() -> baseMapper.transformFailList(query));
        return pageInfo;
    }

    @Override
    public String saveExampleToCourseWare(CourseWareInfoApiDTO dto) {
        Courseware courseware = new Courseware();
        BeanUtils.copyProperties(dto, courseware);

        courseware.setId(newId());
        courseware.setIsAvailable(1);
        courseware.setDescriptions(null);
        courseware.setCreateBy(UserThreadContext.getUserId());
        courseware.setCreateTime(new Date());
        courseware.setUpdateBy(null);
        courseware.setUpdateTime(null);
        courseware.setIsSource(CWCreateSourceEnum.REPOSITORY.getSource());

        // 复制files文件
        // 拷贝课件文件引用关系
        fileFeign.copySameFile(dto.getOldId(), FileBizType.EXAMPLE_FILE.name(), courseware.getId(),
            FileBizType.CourseWareFile.name());
        courseware.setCwContent(StringUtils.EMPTY);
        save(courseware);
        return courseware.getId();
    }

    @Override
    public void saveCwOutline(SaveAiOutlineDTO saveAiOutlineDTO) {
        aiOutlineItemService.batchSaveAiOutlineItem(saveAiOutlineDTO);
    }

    @Override
    public void saveOrUpdateCwOutlineItem(SaveOrUpdateAiOutlineItemDTO saveOrUpdateAiOutlineItemDTO) {
        if (StringUtils.isBlank(saveOrUpdateAiOutlineItemDTO.getId())) {
            aiOutlineItemService.saveAiOutlineItem(saveOrUpdateAiOutlineItemDTO);
        } else {
            aiOutlineItemService.updateAiOutlineItem(saveOrUpdateAiOutlineItemDTO);
        }

    }

    @Override
    public void deleteCwOutlineItem(String ids) {
        List<String> idList = List.of(ids.split(","));
        aiOutlineItemService.removeBatchByIds(idList);
    }

    private String getFilePath(CourseWareAiDTO courseWare) {
        NamePath sourceFileInfo = fileFeign.getFileNamePathInfo(courseWare.getId(),
            FileBizType.CourseWareFile.name(), true);
        // 解析出文件路径和扩展名
        String filePath = sourceFileInfo.getPath();
        String fileExt = FilenameUtils.getExtension(filePath).toLowerCase();
        courseWare.setSourceFileType(fileExt);
        // 定义支持的视频文件扩展名列表
        List<String> extList = List.of("mp4", "mp3", "avi", "mpeg", "mpg", "navi", "asf",
            "mov", "3gp", "wmv", "divx", "xvid", "rm", "rmvb", "flv", "f4v");
        // 如果是视频文件，则修改文件路径为文本文件路径，并更新提示信息
        if (extList.contains(fileExt)) {
            filePath = FilenameUtils.getPath(filePath) + FilenameUtils.getBaseName(filePath) + ".txt";
        }
        // 如果是PPT或DOC文件，则修改文件路径为PDF路径
        if (Objects.equals(fileExt, "ppt") || Objects.equals(fileExt, "pptx") || Objects.equals(fileExt,
            "doc")) {
            filePath = FilenameUtils.getPath(filePath) + FilenameUtils.getBaseName(filePath) + "/index.pdf";
        }
        return filePath;
    }

    /**
     * 课件优先转码
     */
    @Override
    public void priorityTranscoding(PriorityTranscodingDTO priorityTranscodingDTO) {

        List<String> trainsCwIds = new ArrayList<>();

        // 1.筛选哪些课件需要转码
        List<Courseware> coursewares = getBaseMapper().selectByIds(priorityTranscodingDTO.getIds());
        List<Courseware> toTransCoursewares = new ArrayList<>();
        if (coursewares.isEmpty()) {
            return;
        }
        // 是否需要转码
        coursewares.stream().filter(courseware -> UPLOAD.getSource() == courseware.getIsSource()
                || REFERENCE.getSource() == courseware.getIsSource())
            .filter(courseware -> FileResourceEnum.isNeedTransform(courseware.getCwType())).forEach(courseware -> {
                courseware.setTransformStatus(TranscodeStatusEnum.PENDING.value);
                courseware.setTransformStartTime(new Date());
                courseware.setUpdateBy(UserThreadContext.getUserId());
                courseware.setUpdateTime(now());
                trainsCwIds.add(courseware.getId());
                toTransCoursewares.add(courseware);
            });
        if (trainsCwIds.isEmpty()) {
            return;
        }
        // 2.将课件转码状态设置为正在转码 [遍历更新，同时记录业务日志]
        toTransCoursewares.forEach(e -> coursewareDao.updateCourseware(e));

        // 3.将转码文件地址修改为源文件地址
        fileFeign.resetTransFile(trainsCwIds, FileBizType.CourseWareFile.name());
        // 4.重新发送转码mq
        for (Courseware ele : toTransCoursewares) {
            log.info("优先队列参数：send transcode mq, coursewareId:{}, fileBizType:{}, mimeType:{}, fileType:{}", ele.getId(),
                FileBizType.CourseWareFile.name(), ele.getMime(), ele.getCwType());
            fileResourceOptComponent.priorityTranscoding(ele.getId(), FileBizType.CourseWareFile.name(),
                ele.getMime(), ele.getCwType(), null);
        }
    }
}
