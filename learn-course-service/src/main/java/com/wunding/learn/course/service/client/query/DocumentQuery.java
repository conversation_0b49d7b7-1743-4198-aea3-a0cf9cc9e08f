package com.wunding.learn.course.service.client.query;

import com.wunding.learn.common.query.DocumentBaseQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p> 资料查询对象
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @since 2023-01-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
 @Schema(name = "DocumentQuery", description = "资料查询对象")
public class DocumentQuery extends DocumentBaseQuery {

    /**
     * 资源类型：1-课件，2-课程封面
     */
    @Schema(description = "资源类型：1-课件，2-课程封面")
    private Integer resourceType;

}
