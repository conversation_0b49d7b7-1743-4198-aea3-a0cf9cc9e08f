package com.wunding.learn.course.service.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 课件表
 *
 * <AUTHOR> href="mailto:<EMAIL>">余学益</a>
 * @since 2022-05-24
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@TableName("courseware")
@Schema(name = "Courseware对象", description = "课件表")
public class Courseware implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键id
     */
    @Schema(description = "主键id")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;


    /**
     * 课程Id
     */
    @Schema(description = "课程Id")
    @TableField("course_id")
    private String courseId;


    /**
     * 课件名称
     */
    @Schema(description = "课件名称")
    @TableField("cw_name")
    private String cwName;


    /**
     * 课件作者名字，可手动输入，可不为系统用户
     */
    @Schema(description = "课件作者名字，可手动输入，可不为系统用户")
    @TableField("cw_author")
    private String cwAuthor;


    /**
     * 课件类型
     */
    @Schema(description = "课件类型")
    @TableField("cw_type")
    private String cwType;


    /**
     * 部门id
     */
    @Schema(description = "部门id")
    @TableField("org_id")
    private String orgId;


    /**
     * 课件MIME
     */
    @Schema(description = "课件MIME")
    @TableField("mime")
    private String mime;


    /**
     * 版本号
     */
    @Schema(description = "版本号")
    @TableField("version")
    private String version;


    /**
     * 课件简介
     */
    @Schema(description = "课件简介")
    @TableField("descriptions")
    private String descriptions;


    /**
     * 显示顺序
     */
    @Schema(description = "显示顺序")
    @TableField("sort_no")
    private Integer sortNo;


    /**
     * 是否可用
     */
    @Schema(description = "是否可用")
    @TableField("is_available")
    private Integer isAvailable;


    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    @TableField("is_del")
    @TableLogic(value = "0", delval = "1")
    private Integer isDel;


    /**
     * 能否倍速[0:否，1:是]
     */
    @Schema(description = "能否倍速[0:否，1:是]")
    @TableField("can_speed")
    private Integer canSpeed;


    /**
     * 能否拖拽进度条[0:否，1:是]
     */
    @Schema(description = "能否拖拽进度条[0:否，1:是]")
    @TableField("can_drag")
    private Integer canDrag;

    /**
     * 完成类型[0:按时长，1:按进度]
     */
    @Schema(description = "完成类型[0:按时长，1:按进度]")
    @TableField("finish_type")
    private Integer finishType;

    /**
     * 当完成类型为按进度时的课件学习进度百分比要求(0-100)，仅在finish_type=1时有效
     */
    @Schema(description = "当完成类型为按进度时的课件学习进度百分比要求(0-100)，仅在finish_type=1时有效")
    @TableField("required_progress")
    private Integer requiredProgress;

    /**
     * 课件时长
     */
    @Schema(description = "课件时长")
    @TableField("play_time")
    private Integer playTime;



    /**
     * 添加人
     */
    @Schema(description = "添加人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;


    /**
     * 新增时间
     */
    @Schema(description = "新增时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 修改人
     */
    @Schema(description = "修改人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;


    /**
     * 最后编辑时间
     */
    @Schema(description = "最后编辑时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 讲师
     */
    @Schema(description = "讲师")
    @TableField("lecturer")
    private String lecturer;


    /**
     * 授课时长
     */
    @Schema(description = "授课时长")
    @TableField("class_hour")
    private BigDecimal classHour;


    /**
     * 满意度评分
     */
    @Schema(description = "满意度评分")
    @TableField("agree_score")
    private BigDecimal agreeScore;


    /**
     * 课件创建方式： 0：在线做课，1：上传课件，2:资源库的课件，3：引用课件 8:待入库课件  9学员上传课件,10课件模板
     *
     * 课件库直接添加的课件，is_source直接设为3
     *
     * 在课程中：
     * 1. 上传课件。course_id不为空的courseware记录is_source=1。course_id不空的courseware记录is_source=8，可在课件库筛选待入库课件查到
     * 2. 在线做课。course_id不为空的courseware记录is_source=0。course_id不空的courseware记录is_source=8，可在课件库筛选待入库课件查到
     * 3. 课件模板。course_id不为空的courseware记录is_source=10。course_id不空的courseware记录is_source=8，可在课件库筛选待入库课件查到
     * 4. 引用课件库课件。course_id不为空的courseware记录is_source=2。不存在course_id不空的courseware记录。
     *
     * 待入库课件入库：is_source=8 → is_source=3
     *
     */
    @Schema(description = "课件创建方式： 0：在线做课，1：上传课件，2:资源库的课件，3：引用课件 8:待入库课件  9学员上传课件,10课件模板,11案例库")
    @TableField("is_source")
    private Integer isSource;


    /**
     * 课件类容
     */
    @Schema(description = "课件类容")
    @TableField("cw_content")
    private String cwContent;


    /**
     * 是否与课程下发范围相同
     */
    @Schema(description = "是否与课程下发范围相同")
    @TableField("is_same_course")
    private Integer isSameCourse;


    /**
     * 模板类型 1：模板1,2：模板2 , 3：模板3, 4：模板4
     */
    @Schema(description = "模板类型 1：模板1,2：模板2, 3：模板3, 4：模板4")
    @TableField("model_type")
    private Integer modelType;


    /**
     * 互动时间（指浏览、评论、评星的日期）
     */
    @Schema(description = "互动时间（指浏览、评论、评星的日期）")
    @TableField("active_time")
    private Date activeTime;


    /**
     * 学员上传用户id（前端上传）
     */
    @Schema(description = "学员上传用户id（前端上传）")
    @TableField("upload_by")
    private String uploadBy;


    /**
     * 课程章节ID
     */
    @Schema(description = "课程章节ID")
    @TableField("chapter_id")
    private String chapterId;


    /**
     * 考试Id
     */
    @Schema(description = "考试Id")
    @TableField("exam_id")
    private String examId;

    /**
     * 课件关联考试时的完成条件（-1:不涉及考试 0:提交考试即完成 1:通过考试才完成，仅在 exam_id 不为空时生效）
     */
    @Schema(description = "课件关联考试时的完成条件（-1:不涉及考试 0:提交考试即完成 1:通过考试才完成，仅在 exam_id 不为空时生效）")
    @TableField("exam_finish_type")
    private Integer examFinishType;


    /**
     * 课件真实时长（视频/mp3）
     */
    @Schema(description = "课件真实时长（视频/mp3）")
    @TableField("real_play_time")
    private Integer realPlayTime;


    /**
     * 资源库类型
     */
    @Schema(description = "资源库类型")
    @TableField("library_cate_id")
    private String libraryCateId;


    /**
     * 视频转换状态  1 转换中  2 转换完成 3 转换失败
     */
    @Schema(description = "视频转换状态  1 转换中  2 转换完成 3 转换失败")
    @TableField("transform_status")
    private Integer transformStatus;


    /**
     * 转码开始时间
     */
    @Schema(description = "转码开始时间")
    @TableField(value = "transform_start_time", fill = FieldFill.INSERT)
    private Date transformStartTime;


    /**
     * 转码结束时间
     */
    @Schema(description = "转码结束时间")
    @TableField(value = "transform_end_time")
    private Date transformEndTime;


    /**
     * 旧课件MIME
     */
    @Schema(description = "旧课件MIME")
    @TableField("old_mime")
    private String oldMime;


    /**
     * 是否播放防挂机（1：是，0：否）
     */
    @Schema(description = "是否播放防挂机（1：是，0：否）")
    @TableField("is_hangup")
    private Integer isHangup;


    /**
     * 防挂机时长（分）
     */
    @Schema(description = "防挂机时长（分）")
    @TableField("hangup_duration_minute")
    private Integer hangupDurationMinute;


    /**
     * 防挂机倒计时长（秒）
     */
    @Schema(description = "防挂机倒计时长（秒）")
    @TableField("hangup_duration_second")
    private Integer hangupDurationSecond;

    /**
     * 是否是复制出来（1：是，0：否）
     */
    @Schema(description = "是否是复制出来（1：是，0：否）")
    @TableField("is_copy")
    private Integer isCopy;


    /**
     * 复制数据的原id
     */
    @Schema(description = "复制数据的原id")
    @TableField("authentic_id")
    private String authenticId;

    /**
     * 是否是重新上传的课件，0-否，1-是
     */
    @Schema(description = "是否是重新上传的课件，0-否，1-是")
    @TableField("is_re_upload")
    private Integer isReUpload;

    @Schema(description = "关联文件相对路径：目前针对scorm课件index.html位置")
    @TableField("href")
    private String href;

    /**
     * 时间类型，0-音/视频进度时间 1-页面停留时间
     */
    @Schema(description = "时间类型，0-音/视频进度时间 1-页面停留时间")
    @TableField("show_type")
    private Integer showType;

    @Schema(description = "评论数")
    @TableField("comment_number")
    private Integer commentNumber;

    /**
     * 浏览量
     */
    @Schema(description = "浏览量")
    @TableField("click_number")
    private Integer clickNumber;

    /**
     * 综合评星星级
     */
    @Schema(description = "综合评星星级")
    @TableField("common_star")
    private BigDecimal commonStar;

    @Schema(description = "审核状态，0-否，1-是")
    @TableField("audit_status")
    private Integer auditStatus;

    @Schema(description = "关联文件相对路径：目前针对scorm课件index.html位置")
    @TableField("source_id")
    private String sourceId;

    @Schema(description = "课件转码可以记录的失败原因")
    @TableField("reason")
    private String reason;
}
