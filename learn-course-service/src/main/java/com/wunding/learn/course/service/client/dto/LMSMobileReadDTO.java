package com.wunding.learn.course.service.client.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * scorm课件解析DTO
 *
 * <AUTHOR>
 * @date 2023年12月11
 */
@Data
@Schema(name = "LMSMobileReadDTO", description = "scorm课件解析DTO")
public class LMSMobileReadDTO {

    @NotBlank(message = "课件Id不能为空")
    @Schema(description = "课件Id")
    private String cwId;

    @NotBlank(message = "scorm课件学习记录Id不能为空")
    @Schema(description = "scorm课件学习记录Id")
    private String isId;

    @NotBlank(message = "课件地址不能为空")
    @Schema(description = "课件地址（绝对地址）")
    private String cwUrl;

}
