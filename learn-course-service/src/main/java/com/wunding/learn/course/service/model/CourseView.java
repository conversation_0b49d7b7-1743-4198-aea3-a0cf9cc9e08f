package com.wunding.learn.course.service.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * </p> 课程访问记录
 *
 * <AUTHOR> href="mailto:<EMAIL>">余学益</a>
 * @date 2022-05-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("course_view")
@Schema(name = "CourseView对象", description = "课程访问记录")
public class CourseView implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键id
     */
    @Schema(description = "主键id")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;


    /**
     * 课程id
     */
    @Schema(description = "课程id")
    @TableField("course_id")
    private String courseId;


    /**
     * 课件id
     */
    @Schema(description = "课件id")
    @TableField("cw_id")
    private String cwId;


    /**
     * 查看人
     */
    @Schema(description = "查看人")
    @TableField("view_by")
    private String viewBy;


    /**
     * 查看时间（开始）
     */
    @Schema(description = "查看时间（开始）")
    @TableField("view_time")
    private Date viewTime;


    /**
     * 组织id
     */
    @Schema(description = "组织id")
    @TableField("org_id")
    private String orgId;


}
