package com.wunding.learn.course.service.feign;

import static com.wunding.learn.common.util.string.StringUtil.newId;

import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.wunding.learn.comment.api.service.CommentFeign;
import com.wunding.learn.common.category.model.Categorys;
import com.wunding.learn.common.category.service.ICategorysService;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.directory.dto.SaveOrUpdateDirectoryDTO;
import com.wunding.learn.common.directory.service.IDirectoryService;
import com.wunding.learn.common.dto.CerDitchDTO;
import com.wunding.learn.common.dto.CertificationContentDTO;
import com.wunding.learn.common.dto.CompleteNumQuery;
import com.wunding.learn.common.dto.LecturerCourseDetailDTO;
import com.wunding.learn.common.dto.PublishDTO;
import com.wunding.learn.common.dto.ResourceBaseDTO;
import com.wunding.learn.common.dto.ResourceDeleteInfoDTO;
import com.wunding.learn.common.dto.ResourceIdAndUserIdDTO;
import com.wunding.learn.common.dto.ResourceMemberDTO;
import com.wunding.learn.common.dto.ResourceUserDTO;
import com.wunding.learn.common.enums.comment.CommentTypeEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.other.PublishStatusEnum;
import com.wunding.learn.common.mybatis.util.MybatisParameterUtils;
import com.wunding.learn.common.query.ResourceBaseQuery;
import com.wunding.learn.common.query.ResourceUserQuery;
import com.wunding.learn.common.util.bean.BeanListUtils;
import com.wunding.learn.course.api.dto.CourseCategoryDTO;
import com.wunding.learn.course.api.dto.CourseInfoDTO;
import com.wunding.learn.course.api.dto.CourseLearnDetailDTO;
import com.wunding.learn.course.api.dto.CourseSimpleInfoDTO;
import com.wunding.learn.course.api.dto.CourseViewLimitCheckResultDTO;
import com.wunding.learn.course.api.dto.CourseWareLearnDetailDTO;
import com.wunding.learn.course.api.dto.ImportUserCourseRecordDTO;
import com.wunding.learn.course.api.dto.LecturerCourseDTO;
import com.wunding.learn.course.api.dto.MarchCourseTaskDTO;
import com.wunding.learn.course.api.dto.SaveCwLibraryDTO;
import com.wunding.learn.course.api.dto.UserCourseRecordDTO;
import com.wunding.learn.course.api.dto.UserCourseStudyInfoDTO;
import com.wunding.learn.course.api.query.CourseLearnQuery;
import com.wunding.learn.course.api.query.CourseListQuery;
import com.wunding.learn.course.api.query.CourseSummaryListQuery;
import com.wunding.learn.course.api.query.UserCourseListQuery;
import com.wunding.learn.course.api.query.UserCourseStudyInfoQuery;
import com.wunding.learn.course.api.service.CourseFeign;
import com.wunding.learn.course.service.biz.studyplan.ICourseStudyPlanBiz;
import com.wunding.learn.course.service.client.dto.AssessCourseDTO;
import com.wunding.learn.course.service.client.dto.CourseCollectionDTO;
import com.wunding.learn.course.service.client.query.CourseCollectionQuery;
import com.wunding.learn.course.service.client.query.UserCourseDurationSimpleQuery;
import com.wunding.learn.course.service.component.CourseViewLimitComponent;
import com.wunding.learn.course.service.mapper.CourseFavorateMapper;
import com.wunding.learn.course.service.mapper.CourseMapper;
import com.wunding.learn.course.service.mapper.CourseViewMapper;
import com.wunding.learn.course.service.mapper.UserCourseRecordMapper;
import com.wunding.learn.course.service.model.Course;
import com.wunding.learn.course.service.model.CourseCategory;
import com.wunding.learn.course.service.model.CourseChapter;
import com.wunding.learn.course.service.model.CourseStar;
import com.wunding.learn.course.service.model.CourseSuggestRecord;
import com.wunding.learn.course.service.model.CourseTag;
import com.wunding.learn.course.service.model.CourseVote;
import com.wunding.learn.course.service.model.Courseware;
import com.wunding.learn.course.service.model.UserCourseRecord;
import com.wunding.learn.course.service.service.ICourseCategoryService;
import com.wunding.learn.course.service.service.ICourseChapterService;
import com.wunding.learn.course.service.service.ICourseService;
import com.wunding.learn.course.service.service.ICourseStarService;
import com.wunding.learn.course.service.service.ICourseSuggestRecordService;
import com.wunding.learn.course.service.service.ICourseTagService;
import com.wunding.learn.course.service.service.ICourseViewDurationService;
import com.wunding.learn.course.service.service.ICourseViewLimitService;
import com.wunding.learn.course.service.service.ICourseVoteService;
import com.wunding.learn.course.service.service.ICoursewareService;
import com.wunding.learn.course.service.service.IUserCourseRecordService;
import com.wunding.learn.exam.api.dto.ViewExamFeignDTO;
import com.wunding.learn.exam.api.query.UserIdpStatisticQuery;
import com.wunding.learn.exam.api.service.ExamFeign;
import com.wunding.learn.excitation.api.dto.AlignmentUserConfigDTO;
import com.wunding.learn.excitation.api.service.ExcitationConfigUserFeign;
import com.wunding.learn.file.api.constant.ImageBizType;
import com.wunding.learn.file.api.dto.NamePath;
import com.wunding.learn.file.api.service.FileFeign;
import com.wunding.learn.file.api.service.ImagesViewFeign;
import com.wunding.learn.user.api.dto.MemberCardFeignDTO;
import com.wunding.learn.user.api.dto.viewlimit.ViewLimitBaseInfoDTO;
import com.wunding.learn.user.api.service.MemberCardFeign;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2022-07-22 16:39
 **/
@RestController
@RequestMapping("${module.course.contentPath:/}")
@Slf4j
public class CourseFeignImpl implements CourseFeign {

    @Resource
    @Lazy
    private ICourseService courseService;
    @Resource
    private CourseViewLimitComponent courseViewLimitComponent;
    @Resource
    private ICourseViewLimitService courseViewLimitService;
    @Resource
    private ICourseViewDurationService courseViewDurationService;
    @Resource
    private ICoursewareService coursewareService;
    @Resource
    private IUserCourseRecordService userCourseRecordService;
    @Resource
    private CourseViewMapper courseViewMapper;
    @Resource
    private UserCourseRecordMapper userCourseRecordMapper;
    @Resource
    private CourseMapper courseMapper;
    @Resource
    private ICourseVoteService courseVoteService;
    @Resource
    private ICourseStarService courseStarService;
    @Resource
    private CommentFeign commentFeign;
    @Resource
    private ExamFeign examFeign;
    @Resource
    private FileFeign fileFeign;
    @Resource
    private ImagesViewFeign imagesViewFeign;
    @Resource
    private ExcitationConfigUserFeign excitationConfigUserFeign;
    @Resource
    private ICourseChapterService courseChapterService;
    @Resource
    private ICourseTagService courseTagService;
    @Resource
    private ICourseCategoryService courseCategoryService;
    @Resource
    private ICategorysService categoryService;

    @Resource
    private MemberCardFeign memberCardFeign;

    @Resource
    private IDirectoryService directoryService;
    @Resource
    private CourseFavorateMapper courseFavorateMapper;

    @Resource
    private ICourseStudyPlanBiz courseStudyPlanBiz;

    @Resource
    private ICourseSuggestRecordService courseSuggestRecordService;

    @Override
    public CourseInfoDTO getById(String id) {
        Course course = courseService.getById(id);
        if (course == null) {
            return null;
        }
        CourseInfoDTO courseInfoDTO = new CourseInfoDTO();
        BeanUtils.copyProperties(course, courseInfoDTO);

        // 将课程类别属性赋值到培训分类属性上
        courseInfoDTO.setTrainCategoryId(course.getCourseCategoryId());
        ViewLimitBaseInfoDTO viewLimitBaseInfo = courseViewLimitComponent.getViewLimitBaseInfo(id);
        if (Objects.nonNull(viewLimitBaseInfo)) {
            courseInfoDTO.setProgrammeId(viewLimitBaseInfo.getProgrammeId());
        }
        return courseInfoDTO;
    }

    @Override
    public CourseInfoDTO getRealityById(String id) {
        CourseInfoDTO courseInfoDTO = courseService.getRealityById(id);
        if (courseInfoDTO == null) {
            return null;
        }
        ViewLimitBaseInfoDTO viewLimitBaseInfo = courseViewLimitComponent.getViewLimitBaseInfo(id);
        courseInfoDTO.setProgrammeId(viewLimitBaseInfo == null ? null : viewLimitBaseInfo.getProgrammeId());
        return courseInfoDTO;
    }

    @Override
    public int getCoursePubState(String id) {
        Course course = courseService.getById(id);
        if (course == null) {
            return 0;
        }
        return course.getIsPublish();
    }

    @Override
    public void publishCourse(String id, Integer isPublish) {
        PublishDTO publishDTO = new PublishDTO();
        publishDTO.setIds(List.of(id));
        publishDTO.setIsPublish(isPublish);
        courseService.publish(publishDTO);
    }

    @Override
    public void removeCourse(String id) {
        courseService.delCourseById(id);
    }

    @Override
    public Map<String, Integer> getCourseLearnNum(CompleteNumQuery completeNumQuery) {
        Collection<String> userIds = completeNumQuery.getUserIds();
        Collection<String> courseIds = completeNumQuery.getSourceId();
        ConcurrentHashMap<String, Integer> result = new ConcurrentHashMap<>(userIds.size());
        for (String userId : userIds) {
            Integer num = courseService.getCourseLearnNum(userId, courseIds);
            result.put(userId, num);
        }
        return result;
    }

    @Override
    public List<String> getCourseIdListByCategory(String courseCategoryId) {
        return courseService.list(new LambdaQueryWrapper<Course>().eq(Course::getCourseCateId, courseCategoryId))
            .stream().map(Course::getId).collect(Collectors.toList());
    }

    @Override
    public Integer getUserCourseHour(String userId, String courseId) {
        return userCourseRecordService.getUserCourseHour(userId, courseId);
    }

    @Override
    public Double getUserCourseDruation(String userId) {
        return userCourseRecordService.getUserCourseDruation(userId);
    }


    @Override
    public List<UserCourseStudyInfoDTO> getUserCourseStudyInfo(UserCourseStudyInfoQuery query) {
        return userCourseRecordService.getUserCourseStudyInfo(query);
    }

    @Override
    public Map<String, String> getNameBatchIds(Collection<String> batchIds) {
        if (CollectionUtils.isEmpty(batchIds)) {
            return new HashMap<>();
        }
        return courseService.getBaseMapper().selectBatchIds(batchIds).stream()
            .collect(Collectors.toMap(Course::getId, Course::getCourseName, (key1, key2) -> key1));
    }


    @Override
    public Map<String, String> getNameIncludeIsDelByIds(Collection<String> batchIds) {
        if (CollectionUtils.isEmpty(batchIds)) {
            return new HashMap<>();
        }
        return courseMapper.getRealityCourseList(batchIds).stream()
            .collect(Collectors.toMap(Course::getId, Course::getCourseName, (key1, key2) -> key1));
    }

    @Override
    public Map<String, ResourceMemberDTO> getResourceMemberBatchIds(Collection<String> batchIds) {
        Map<String, ResourceMemberDTO> result = new HashMap<>();
        if (CollectionUtils.isEmpty(batchIds)) {
            return result;
        }
        Map<String, String> map = courseService.getBaseMapper().selectBatchIds(batchIds).stream()
            .collect(Collectors.toMap(Course::getId, Course::getCourseName, (key1, key2) -> key1));
        // 取资源对应的会员卡信息
        Map<String, Set<MemberCardFeignDTO>> resourceCardMap = memberCardFeign.getMemberCardMapByResourceIds(batchIds);

        result = map.entrySet().stream().collect(Collectors.toMap(entry -> entry.getKey(), entry -> {
            ResourceMemberDTO resourceMemberDTO = new ResourceMemberDTO();
            resourceMemberDTO.setName(entry.getValue());
            if (null != resourceCardMap) {
                Set<MemberCardFeignDTO> cardMap = resourceCardMap.get(entry.getKey());
                if (!CollectionUtils.isEmpty(cardMap)) {
                    List<String> memberIconUrls = cardMap.stream().filter(p -> null != p).distinct()
                        .sorted((dto1, dto2) -> dto1.getSort().compareTo(dto2.getSort()))
                        .map(MemberCardFeignDTO::getCoinImgUrl)
                        .collect(Collectors.toList());
                    resourceMemberDTO.setMemberIconUrls(memberIconUrls);
                }

            }
            return resourceMemberDTO;

        }));
        return result;
    }


    @Override
    public Map<String, String> getAuthenticIdBatchIds(Collection<String> batchIds) {
        if (CollectionUtils.isEmpty(batchIds)) {
            return new HashMap<>();
        }
        return courseService.getBaseMapper().selectBatchIds(batchIds).stream()
            .collect(Collectors.toMap(Course::getId, Course::getAuthenticId, (key1, key2) -> key1));
    }

    @Override
    public Map<String, CourseInfoDTO> getCourseBatchIds(Collection<String> batchIds) {
        if (CollectionUtils.isEmpty(batchIds)) {
            return new HashMap<>();
        }
        List<CourseInfoDTO> courseList = courseService.getBaseMapper().selectBatchIds(batchIds).stream().map(c -> {
            CourseInfoDTO info = new CourseInfoDTO();
            BeanUtils.copyProperties(c, info);
            LambdaQueryWrapper<Courseware> coursewareQueryWrapper = new LambdaQueryWrapper<>();
            coursewareQueryWrapper.eq(Courseware::getCourseId, info.getId());
            info.setCoursewareCount(coursewareService.count(coursewareQueryWrapper));
            LambdaQueryWrapper<CourseVote> courseVoteQueryWrapper = new LambdaQueryWrapper<>();
            courseVoteQueryWrapper.eq(CourseVote::getCourseId, info.getId());
            info.setCourseVoteCount(courseVoteService.count(courseVoteQueryWrapper));
            LambdaQueryWrapper<CourseStar> courseStarQueryWrapper = new LambdaQueryWrapper<>();
            courseStarQueryWrapper.eq(CourseStar::getCourseId, info.getId());
            info.setCourseStarCount(courseStarService.count(courseStarQueryWrapper));
            return info;
        }).collect(Collectors.toList());
        Set<String> courseIdList = courseList.stream().map(CourseInfoDTO::getId).collect(Collectors.toSet());
        Map<String, Integer> commentMap = commentFeign.getDiscussCount(courseIdList, CommentTypeEnum.COURSE);
        Set<String> examIdList = courseList.stream().map(CourseInfoDTO::getExamId).filter(StringUtils::isNotBlank)
            .collect(Collectors.toSet());
        Map<String, ViewExamFeignDTO> examMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(examIdList)) {
            examMap = examFeign.getValidExamInfoMapByExamIds(examIdList);
        }
        Map<String, ViewExamFeignDTO> finalExamMap = examMap;
        courseList.forEach(c -> {
            Optional.ofNullable(commentMap.get(c.getId()))
                .ifPresent(commentCount -> c.setCourseCommentCount(Long.valueOf(commentCount)));
            ViewExamFeignDTO exam = finalExamMap.get(c.getExamId());
            if (Optional.ofNullable(exam).isPresent()) {
                c.setExamName(exam.getExamName());
                c.setPostExamCount(exam.getPostExamCount());
                c.setCheckExamCount(exam.getCheckExamCount());
                c.setIsRelevancyExam(GeneralJudgeEnum.CONFIRM.getValue());
            } else {
                c.setIsRelevancyExam(GeneralJudgeEnum.NEGATIVE.getValue());
            }
        });
        return courseList.stream().collect(Collectors.toMap(CourseInfoDTO::getId, c -> c));
    }

    @Override
    public List<CourseInfoDTO> getCourseSimpleInfoBatchIds(Collection<String> batchIds) {
        if (CollectionUtils.isEmpty(batchIds)) {
            return new ArrayList<>();
        }
        return courseService.getBaseMapper().selectBatchIds(batchIds).stream().map(c -> {
            CourseInfoDTO info = new CourseInfoDTO();
            BeanUtils.copyProperties(c, info);
            return info;
        }).collect(Collectors.toList());
    }

    @Override
    public List<String> getEffectiveCourseIds() {
        LambdaQueryWrapper<Course> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Course::getId)
            .eq(Course::getIsDel, 0);
        List<Course> courses = courseService.getBaseMapper().selectList(queryWrapper);
        return courses.stream().map(Course::getId).collect(Collectors.toList());
    }

    @Override
    public Map<String, CourseInfoDTO> getCourseBatchNos(Collection<String> batchNos) {
        if (CollectionUtils.isEmpty(batchNos)) {
            return new HashMap<>();
        }
        LambdaQueryWrapper<Course> queryWrapper = new LambdaQueryWrapper<>();
        MybatisParameterUtils.cutInParameter(queryWrapper, Course::getCourseNo,
            batchNos.stream().distinct().collect(Collectors.toList()));
        return courseService.getBaseMapper().selectList(queryWrapper).stream().map(course -> {
            CourseInfoDTO dto = new CourseInfoDTO();
            BeanUtils.copyProperties(course, dto);
            return dto;
        }).collect(Collectors.toMap(CourseInfoDTO::getCourseNo, Function.identity(), (v1, v2) -> v1));
    }

    @Override
    public void updateCourseById(CourseInfoDTO courseInfoDTO) {
        Course course = new Course();
        BeanUtils.copyProperties(courseInfoDTO, course);
        courseService.updateById(course);
    }

    @Override
    public CourseInfoDTO getOneCourse(String id) {
        // getById 带上了isDel 而这个就是需要isDel状态
        Course course = courseService.getOne(new QueryWrapper<Course>().lambda().eq(Course::getId, id));
        if (course == null) {
            return null;
        }
        CourseInfoDTO courseInfoDTO = new CourseInfoDTO();
        BeanUtils.copyProperties(course, courseInfoDTO);
        return courseInfoDTO;
    }

    @Override
    public void saveSyncCourseViewLimit(String resourceId, Long programmeId) {
        courseViewLimitComponent.handleNewViewLimit(programmeId, resourceId);
    }

    @Override
    public boolean checkViewLimit(String userId, String resourceId) {
        return courseViewLimitService.checkViewLimit(userId, resourceId);
    }

    @Override
    public CourseViewLimitCheckResultDTO checkViewPermision(String userId, String resourceId, String bizId,
        String activityId) {
        return courseViewLimitService.checkViewPermision(userId, resourceId, bizId, activityId);
    }

    @Override
    public CerDitchDTO getDitch(String contentId) {
        return courseService.getDitch(contentId);
    }

    @Override
    public CourseInfoDTO getOneCourseIncludeDel(String id) {
        Course course = courseMapper.selectOneCourseIncludeDel(id);
        if (course == null) {
            return null;
        }
        CourseInfoDTO courseInfoDTO = new CourseInfoDTO();
        BeanUtils.copyProperties(course, courseInfoDTO);
        return courseInfoDTO;
    }

    @Override
    public List<String> findCourseStatusByIdList(Collection<String> courseIdList) {
        if (CollectionUtils.isEmpty(courseIdList)) {
            return new ArrayList<>();
        }
        List<Course> courseList = courseService.lambdaQuery().eq(Course::getIsDel, 0).eq(Course::getIsPublish, 1)
            .in(Course::getId, courseIdList).list();
        return courseList.stream().map(Course::getId).collect(Collectors.toList());
    }

    @Override
    public int getIdpCourseStatistic(String year, String type) {
        UserIdpStatisticQuery userIdpStatisticQuery = new UserIdpStatisticQuery();
        userIdpStatisticQuery.setUserId(UserThreadContext.getUserId());
        userIdpStatisticQuery.setType(type);
        userIdpStatisticQuery.setYear(year);
        return userCourseRecordMapper.getIdpCourseStatistic(userIdpStatisticQuery);
    }

    @Override
    public int getIdpCourseWareStatistic(String year, String type) {
        UserIdpStatisticQuery userIdpStatisticQuery = new UserIdpStatisticQuery();
        userIdpStatisticQuery.setUserId(UserThreadContext.getUserId());
        userIdpStatisticQuery.setType(type);
        userIdpStatisticQuery.setYear(year);
        return courseViewMapper.getIdpCourseWareStatistic(userIdpStatisticQuery);
    }

    @Override
    public Map<String, Long> getViewCount(Collection<String> resourceId) {
        return courseService.getViewCount(resourceId);
    }

    @Override
    public Boolean getUserCourseStatus(String userId, String id) {
        return userCourseRecordMapper.getUserCourseStatus(userId, id) > 0;
    }

    @Override
    public String coursewareStorage(SaveCwLibraryDTO saveCwLibraryDTO) {
        return coursewareService.coursewareStorage(saveCwLibraryDTO);
    }

    @Override
    public String coursewareStorageJob(SaveCwLibraryDTO saveCwLibraryDTO) {
        return coursewareService.coursewareStorageJob(saveCwLibraryDTO);
    }

    @Override
    public List<String> getLearnedCourseIds(String userId, Collection<String> ids) {
        return userCourseRecordService.getLearnedCourseIds(userId, ids);
    }

    @Override
    public List<String> getInvalidCourseId(Collection<String> courseIdList) {
        if (CollectionUtils.isEmpty(courseIdList)) {
            return new ArrayList<>();
        }
        return courseService.getInvalidCourseId(courseIdList);
    }

    @Override
    public UserCourseRecordDTO getUserCourseRecord(String userId, String courseId) {
        LambdaQueryWrapper<UserCourseRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserCourseRecord::getUserId, userId);
        queryWrapper.eq(UserCourseRecord::getCourseId, courseId);
        UserCourseRecord userCourseRecord = userCourseRecordService.getOne(queryWrapper);
        if (Optional.ofNullable(userCourseRecord).isEmpty()) {
            return null;
        }
        UserCourseRecordDTO userCourseRecordDTO = new UserCourseRecordDTO();
        BeanUtils.copyProperties(userCourseRecord, userCourseRecordDTO);
        return userCourseRecordDTO;
    }

    @Override
    public Map<String, Long> getCourseLearnDuration(Collection<String> userIds, String courseId) {
        if (CollectionUtils.isEmpty(userIds) || StringUtils.isBlank(courseId)) {
            return new HashMap<>();
        }
        LambdaQueryWrapper<UserCourseRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(UserCourseRecord::getUserId, userIds);
        queryWrapper.eq(UserCourseRecord::getCourseId, courseId);
        return userCourseRecordService.list(queryWrapper).stream().collect(
            Collectors.toMap(UserCourseRecord::getUserId, UserCourseRecord::getDuration, (key1, key2) -> key1));
    }

    @Override
    public String voteCourseWare2Lib(String id) {
        return coursewareService.voteCourseWare2Lib(id);
    }

    @Override
    public Map<String, Integer> getUserCourseHourByListParams(Collection<ResourceIdAndUserIdDTO> userIdAndContendId) {
        return userCourseRecordService.getUserCourseHourByListParams(userIdAndContendId);
    }

    @Override
    public int getResourceIsNotDeleteAndIsPublish(String resourceId) {
        LambdaQueryWrapper<Course> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Course::getId, Course::getIsPublish);
        queryWrapper.eq(Course::getId, resourceId);
        Course course = courseService.getOne(queryWrapper);
        return course == null || course.getIsPublish().equals(PublishStatusEnum.IS_NO_PUBLISH.getValue()) ? 1 : 0;
    }

    @Override
    public Map<String, Integer> getLikeCount(Collection<String> courseIds) {
        return courseService.getLikeCount(courseIds);
    }

    @Override
    public Map<String, CourseInfoDTO> getCommentCount(Set<String> courseIds) {
        return courseService.getCommentCount(courseIds);
    }

    @Override
    public Map<String, Long> getUserDevelopCourseNumMap(Collection<String> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return new HashMap<>();
        }
        List<Course> courseList = courseService.list(new LambdaQueryWrapper<Course>().in(Course::getAuthorId, userIds));
        return courseList.stream().collect(
            Collectors.groupingBy(Course::getAuthorId, Collectors.mapping(Course::getId, Collectors.counting())));
    }

    @Override
    public void publishOrUnPublishCourse(String id, Integer publishType) {

        PublishDTO publishDTO = new PublishDTO();
        List<String> ids = new ArrayList<>();
        ids.add(id);
        publishDTO.setIds(ids);
        publishDTO.setIsPublish(publishType);

        courseService.publish(publishDTO);
    }

    @Override
    public void saveOrUpdateDirectory(SaveOrUpdateDirectoryDTO dto) {
        directoryService.saveOrUpdateDirectory(dto);
    }

    @Override
    public PageInfo<LecturerCourseDTO> userCourseList(UserCourseListQuery query) {
        PageInfo<Course> pageInfo = PageMethod.startPage(query.getPageNo(), query.getPageSize()).doSelectPageInfo(
            () -> courseService.list(new LambdaQueryWrapper<Course>().eq(Course::getAuthorId, query.getUserId())
                .between(Objects.nonNull(query.getStartTime()) && Objects.nonNull(query.getEndTime()),
                    Course::getCreateTime, query.getStartTime(), query.getEndTime())
                .orderByDesc(Course::getCreateTime)));
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return new PageInfo<>();
        }
        PageInfo<LecturerCourseDTO> result = BeanListUtils.copyPageInfo(pageInfo, LecturerCourseDTO.class);
        List<String> courseIds = pageInfo.getList().stream().map(Course::getId).collect(Collectors.toList());
        // 获取课件数量
        List<Courseware> coursewareList = coursewareService.list(
            new LambdaQueryWrapper<Courseware>().in(Courseware::getCourseId, courseIds));
        Map<String, Long> coursewareNumMap = CollectionUtils.isEmpty(coursewareList) ? new HashMap<>()
            : coursewareList.stream().collect(Collectors.groupingBy(Courseware::getCourseId, Collectors.counting()));
        result.getList().forEach(data -> Optional.ofNullable(coursewareNumMap.get(data.getId()))
            .ifPresent(num -> data.setCoursewareNum(num.intValue())));
        return result;
    }

    @Override
    public Map<String, CourseCategoryDTO> getCourseCategoryMap(Collection<String> categoryIds) {
        //这里应该取本模块的课程分类
        List<Categorys> categories = categoryService.getRealityCategoryList(categoryIds);
        if (CollectionUtils.isEmpty(categories)) {
            return new HashMap<>();
        }
        Map<String, List<String>> categoryParentIdMap = categories.stream().collect(Collectors.toMap(Categorys::getId,
            categorys -> Arrays.stream(categorys.getLevelPath().split("/")).filter(StringUtils::isNotBlank)
                .collect(Collectors.toList())));
        Set<String> parentIdSet = categoryParentIdMap.values().stream().flatMap(Collection::stream)
            .collect(Collectors.toSet());
        List<Categorys> categorys = categoryService.getRealityCategoryList(parentIdSet);
        Map<String, String> parentCategoryNameMap = categorys.stream()
            .collect(Collectors.toMap(Categorys::getId, Categorys::getCategoryName));
        return categoryParentIdMap.entrySet().stream().collect(Collectors.toMap(Entry::getKey, entry -> {
            CourseCategoryDTO categoryDTO = new CourseCategoryDTO();
            categoryDTO.setCategoryName(parentCategoryNameMap.get(entry.getKey()));
            if (!CollectionUtils.isEmpty(entry.getValue())) {
                categoryDTO.setCategoryFullName(entry.getValue().stream().map(parentCategoryNameMap::get)
                    .collect(Collectors.joining("/")));
            }
            return categoryDTO;
        }));
    }

    @Override
    public String getCourseIdByCategoryName(String categoryName) {
        List<Categorys> categoryList = categoryService.list(
            new LambdaQueryWrapper<Categorys>().eq(Categorys::getCategoryName, categoryName)
                .orderByAsc(Categorys::getSortNo));
        if (CollectionUtils.isEmpty(categoryList)) {
            return StringUtils.EMPTY;
        }
        return categoryList.get(0).getId();
    }

    @Override
    public String copyCourseByQuotedCourse(String courseId) {
        // 先查出来源课程数据
        Course course = courseService.getById(courseId);
        if (course == null) {
            log.info("course not exist! [{}]", courseId);
            return null;
        }
        HashMap<String, String> courseChapterIdMap = new HashMap<>();
        Course copyCourse = new Course();
        BeanUtils.copyProperties(course, copyCourse);
        // 重新set一个uuid
        String copyCourseId = newId();
        copyCourse.setId(copyCourseId);
        // 复制体
        copyCourse.setIsCopy(1);
        // 源课程id
        copyCourse.setAuthenticId(courseId);
        // copy course的数据都设置好了，再处理图片，激励，下发范围等；不直接走saveCourse(SaveCourseDTO)，复制代码再写一遍，防止改动引起原有功能失常
        // 保存图片
        // 先查源课程的图片信息
        NamePath authenticImageNamePath = fileFeign.getImageFileNamePath(courseId, ImageBizType.CourseImgIcon.name());
        if (authenticImageNamePath != null) {
            fileFeign.saveImage(copyCourseId, ImageBizType.CourseImgIcon.name(), authenticImageNamePath.getName(),
                authenticImageNamePath.getPath());
            imagesViewFeign.save(UserThreadContext.getUserId(), copyCourseId, ImageBizType.CourseImgIcon.name(),
                authenticImageNamePath.getName(), authenticImageNamePath.getPath());
        }

        // 复制了课程
        courseService.save(copyCourse);

        // 复制源课程当前的激励配置
        excitationConfigUserFeign.copyCourseExcitationConfigByTargetId(courseId, copyCourseId);

        // 复制章节 同样的，查库->改id、改关联id->入库
        List<CourseChapter> courseChapterList = courseChapterService.lambdaQuery()
            .eq(CourseChapter::getCourseId, courseId).list();
        if (!CollectionUtils.isEmpty(courseChapterList)) {
            courseChapterList.forEach(courseChapter -> {
                String copyCourseChapterId = newId();
                courseChapterIdMap.put(courseChapter.getId(), copyCourseChapterId);
                courseChapter.setId(copyCourseChapterId);
                courseChapter.setCourseId(copyCourseId);
            });
            courseChapterService.saveBatch(courseChapterList);
        }

        // 复制标签关联关系
        List<CourseTag> courseTagList = courseTagService.lambdaQuery().eq(CourseTag::getCourseId, courseId).list();
        if (!CollectionUtils.isEmpty(courseTagList)) {
            courseTagList.forEach(courseTag -> {
                courseTag.setId(newId());
                courseTag.setCourseId(copyCourseId);
            });
            courseTagService.saveBatch(courseTagList);
        }

        // 复制课程岗位关联数据
        List<CourseCategory> courseCategoryList = courseCategoryService.lambdaQuery()
            .eq(CourseCategory::getCourseId, courseId).list();
        if (!CollectionUtils.isEmpty(courseCategoryList)) {
            courseCategoryList.forEach(courseCategory -> {
                courseCategory.setId(newId());
                courseCategory.setCourseId(copyCourseId);
            });
            courseCategoryService.saveBatch(courseCategoryList);
        }

        // 保存下发范围
        ViewLimitBaseInfoDTO viewLimitBaseInfo = courseViewLimitComponent.getViewLimitBaseInfo(courseId);
        courseViewLimitComponent.handleNewViewLimit(viewLimitBaseInfo.getProgrammeId(), copyCourseId);

        // 不为空时说明是合并课件到课程 复制课件
        List<Courseware> coursewareList = coursewareService.lambdaQuery().eq(Courseware::getCourseId, courseId).list();
        if (!CollectionUtils.isEmpty(coursewareList)) {
            coursewareList.forEach(courseware -> {
                String coursewareId = courseware.getId();
                String copyCoursewareId = newId();
                courseware.setId(copyCoursewareId);
                courseware.setCourseId(copyCourseId);
                courseware.setIsCopy(1);
                courseware.setAuthenticId(coursewareId);
                courseware.setChapterId(courseChapterIdMap.get(courseware.getChapterId()));
                // 修改课件文件关联类型 课件数据也复制了一份新的，关联复制课程，那么文件库的课件记录数据也要复制一份，关联复制课件
                // 再拿复制课件id
                // 先在循环中处理，暂时未想到好地对应关系
                fileFeign.copyCoursewareFileRecord(coursewareId, copyCoursewareId);
            });
            coursewareService.saveBatch(coursewareList);
            // 同步课件的激励配置
            coursewareList.forEach(courseware -> excitationConfigUserFeign.alignmentUserConfig(
                new AlignmentUserConfigDTO().setTargetId(courseware.getAuthenticId())
                    .setCopyTargetIds(List.of(courseware.getId()))));
        }

        return copyCourseId;
    }

    @Override
    public List<CourseSimpleInfoDTO> getCourseByUserId(String userId) {
        return courseService.getCourseByUserId(userId);
    }

    @Override
    public List<CourseInfoDTO> getRealityCourseListBatchIds(Collection<String> batchIds) {
        return courseService.getRealityCourseList(batchIds).stream().map(c -> {
            CourseInfoDTO info = new CourseInfoDTO();
            BeanUtils.copyProperties(c, info);
            return info;
        }).collect(Collectors.toList());
    }

    @Override
    public Map<String, Integer> getCourseFinish(ResourceUserQuery resourceUserQuery) {
        if (StringUtils.isBlank(resourceUserQuery.getResourceId()) || CollectionUtils.isEmpty(
            resourceUserQuery.getUserIdList())) {
            return new HashMap<>();
        }
        return userCourseRecordMapper.getUserCourseStatusList(resourceUserQuery).stream()
            .collect(Collectors.toMap(ResourceUserDTO::getUserId, ResourceUserDTO::getIsFinish, (k1, k2) -> k1));
    }

    @Override
    public Long getTotalCoursewareDuration(Collection<String> courseIdList) {
        long totalCoursewareMinuteDuration = 0L;
        if (!CollectionUtils.isEmpty(courseIdList)) {
            BigDecimal totalCoursewareSecondDuration = BigDecimal.valueOf(0.0);
            for (String courseId : courseIdList) {
                BigDecimal totalCoursewareDuration = coursewareService.getTotalCoursewareDuration(courseId);
                totalCoursewareSecondDuration = totalCoursewareSecondDuration.add(totalCoursewareDuration);
            }
            totalCoursewareMinuteDuration = totalCoursewareSecondDuration.divide(new BigDecimal("60"), 0,
                RoundingMode.CEILING).longValue();
        }
        return totalCoursewareMinuteDuration;
    }

    @Override
    public Map<String, ResourceBaseDTO> getCourseBaseInfo(ResourceBaseQuery resourceBaseQuery) {
        if (CollectionUtils.isEmpty(resourceBaseQuery.getResourceIdList())) {
            return new HashMap<>();
        }
        List<ResourceBaseDTO> resourceBaseList = courseService.getCourseBaseList(resourceBaseQuery);
        if (!CollectionUtils.isEmpty(resourceBaseQuery.getManagerAreaOrgIds())) {
            resourceBaseList.forEach(resource -> {
                Integer inManageArea = GeneralJudgeEnum.NEGATIVE.getValue();
                for (String managerOrgId : resourceBaseQuery.getManagerAreaOrgIds()) {
                    if (resource.getLevelPath().startsWith(managerOrgId)) {
                        inManageArea = GeneralJudgeEnum.CONFIRM.getValue();
                        break;
                    }
                }
                resource.setInManageArea(inManageArea);
            });
        }
        // 各课程总时长
        resourceBaseList.forEach(resource -> resource.setTotalCoursewareDuration(
            coursewareService.getTotalCoursewareDuration(resource.getId())
                .divide(new BigDecimal("60"), 1, RoundingMode.CEILING)));
        return resourceBaseList.stream()
            .collect(Collectors.toMap(ResourceBaseDTO::getId, Function.identity(), (key1, key2) -> key1));
    }

    @Override
    public List<CourseLearnDetailDTO> getCourseLearnDetail(CourseLearnQuery courseLearnQuery) {
        if (CollectionUtils.isEmpty(courseLearnQuery.getCourseIdList())) {
            return new ArrayList<>();
        }
        List<CourseLearnDetailDTO> courseLearnDetailList = courseMapper.getCourseLearnDetail(courseLearnQuery);
        courseLearnDetailList.forEach(courseLearnDetailDTO -> {
            // 查询剩余待学时长
            List<CourseWareLearnDetailDTO> coursewareAwaitLearnDurationList = courseMapper.getCoursewareAwaitLearnDurationList(
                courseLearnDetailDTO.getCourseId(), courseLearnQuery.getCurrentUserId());
            long awaitLearnDuration = 0L;
            for (CourseWareLearnDetailDTO cw : coursewareAwaitLearnDurationList) {
                awaitLearnDuration += cw.getPlayTime() - cw.getDuration();
            }
            courseLearnDetailDTO.setAwaitLearnDuration(awaitLearnDuration);
        });
        return courseLearnDetailList;
    }

    @Override
    public List<CourseInfoDTO> getCreateCourse(String userId) {
        return courseMapper.getCreateCourse(userId);
    }

    @Override
    public Integer getCourseCollectCount(String userId) {
        CourseCollectionQuery collectionQuery = new CourseCollectionQuery();
        collectionQuery.setCurrentOrgId(userId);
        collectionQuery.setCurrentUserId(UserThreadContext.getUserId());
        List<CourseCollectionDTO> list = courseFavorateMapper.courseCollectionList(collectionQuery);
        return list.size();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createImportUserCourseRecord(List<ImportUserCourseRecordDTO> userCourseRecordDTOS) {
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(userCourseRecordDTOS)) {
            return;
        }
        LambdaQueryWrapper<UserCourseRecord> queryWrapper = new LambdaQueryWrapper<>();
        List<UserCourseRecord> userCourseRecords = new ArrayList<>();
        for (ImportUserCourseRecordDTO recordDTO : userCourseRecordDTOS) {
            queryWrapper
                .eq(UserCourseRecord::getUserId, recordDTO.getUserId())
                .eq(UserCourseRecord::getCourseId, recordDTO.getCourseId());
            UserCourseRecord userCourseRecord = userCourseRecordService.getOne(queryWrapper);
            if (userCourseRecord == null) {
                BigDecimal coursewareDuration = coursewareService.getTotalCoursewareDuration(recordDTO.getCourseId());
                userCourseRecord = new UserCourseRecord();
                userCourseRecord.setId(newId());
                userCourseRecord.setCourseId(recordDTO.getCourseId());
                userCourseRecord.setLevelPath(recordDTO.getLevelPath());
                userCourseRecord.setUserId(recordDTO.getUserId());
                userCourseRecord.setDuration((long) Math.ceil(coursewareDuration.doubleValue()));
                userCourseRecord.setIsLearned(1);
                userCourseRecord.setFinishTime(recordDTO.getFinishTime());
            } else {
                userCourseRecord.setIsLearned(1);
            }
            userCourseRecords.add(userCourseRecord);
        }
        userCourseRecordService.saveOrUpdateBatch(userCourseRecords);
    }

    @Override
    public Integer getCourseIsDelById(String id) {
        return courseMapper.getCourseIsDelById(id);
    }

    @Override
    public Map<String, CertificationContentDTO> getCertificationContentList(Collection<String> batchIds) {
        if (CollectionUtils.isEmpty(batchIds)) {
            return new HashMap<>();
        }
        return courseMapper.getCertificationContentList(batchIds).stream()
            .collect(
                Collectors.toMap(CertificationContentDTO::getId, certificationContentDTO -> certificationContentDTO));
    }

    @Override
    public Integer checkCourseManagePermissions(Collection<String> userManageAreaOrgId, String id) {
        return courseMapper.checkCourseManagePermissions(userManageAreaOrgId, id);
    }

    @Override
    public CourseInfoDTO getByNo(String courseNo) {
        Course course = courseService.lambdaQuery().eq(Course::getCourseNo, courseNo).one();

        if (course == null) {
            return null;
        }
        CourseInfoDTO courseInfoDTO = new CourseInfoDTO();
        BeanUtils.copyProperties(course, courseInfoDTO);
        return courseInfoDTO;
    }

    @Override
    public List<CourseInfoDTO> getCourseInfoBatchIds(Collection<String> batchIds, String courseName) {
        if (CollectionUtils.isEmpty(batchIds)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<Course> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Course::getId, batchIds);
        queryWrapper.like(StringUtils.isNotBlank(courseName), Course::getCourseName, courseName);
        return courseService.list(queryWrapper).stream().map(c -> {
            CourseInfoDTO info = new CourseInfoDTO();
            BeanUtils.copyProperties(c, info);
            return info;
        }).collect(Collectors.toList());
    }

    @Override
    public PageInfo<CourseInfoDTO> getPublishCourseInfoPageByIds(CourseListQuery query) {

        if (query.getIdList().isEmpty()) {
            return new PageInfo<>();
        }

        // 查询条件
        LambdaQueryWrapper<Course> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Course::getId, query.getIdList());
        queryWrapper.eq(Course::getIsPublish, GeneralJudgeEnum.CONFIRM.getValue());
        queryWrapper.orderByDesc(Course::getCreateTime);

        // 分页处理
        return PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> courseService.list(queryWrapper).stream()
                .map(c -> {
                    CourseInfoDTO info = new CourseInfoDTO();
                    BeanUtils.copyProperties(c, info);
                    return info;
                }).collect(Collectors.toList()));
    }

    @Override
    public PageInfo<LecturerCourseDetailDTO> getCourseSummaryPageByUseId(CourseSummaryListQuery courseDetailListQuery) {
        if (StringUtils.isEmpty(courseDetailListQuery.getUserId())) {
            return new PageInfo<>();
        }
        return courseService.findCourseSummaryListByPage(courseDetailListQuery);
    }

    @Override
    public void exportCourseData(CourseSummaryListQuery courseDetailListQuery) {
        courseService.exportCourseData(courseDetailListQuery);
    }

    @Override
    public Long getSuggestToMe(String userId) {
        LambdaQueryWrapper<CourseSuggestRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CourseSuggestRecord::getUserId, userId);
        queryWrapper.select(CourseSuggestRecord::getId);
        return courseSuggestRecordService.count(queryWrapper);
    }


    /**
     * 获取课程是否被推荐的信息
     * <p>
     * 此方法接收一个课程ID集合，并返回一个映射，其中包含每个课程ID及其是否被当前用户推荐的标志 使用 HashMap 来存储和返回结果，以提供高效的查找操作
     *
     * @param courseIdSet 课程ID的集合
     * @return 包含课程ID及其是否被推荐标志的映射
     */
    @Override
    public Map<String, Integer> getCourseIsSuggest(Collection<String> courseIdSet) {

        // 初始化一个空的HashMap来存储课程ID与是否被推荐的标志
        Map<String, Integer> suggestMap = new HashMap<>();

        // 检查输入的课程ID集合是否为空，如果为空则直接返回空映射
        if (CollectionUtils.isEmpty(courseIdSet)) {
            return suggestMap;
        }

        // 使用 HashSet 进行转换，避免 ClassCastException
        // 这一步确保了输入集合中没有重复的课程ID，并避免了类型转换异常的可能性
        Set<String> courseIdSetConverted = new HashSet<>(courseIdSet);

        // 调用服务方法获取与当前用户相关的课程推荐记录，并将结果转换为映射
        // 这里使用了Stream API来过滤和转换数据，确保每个课程ID在映射中最多出现一次
        Map<String, CourseSuggestRecord> suggestRecordMap = courseSuggestRecordService.getSuggestToMe(
                courseIdSetConverted).stream()
            .collect(Collectors.toMap(CourseSuggestRecord::getCourseId, Function.identity(), (key1, key2) -> key1));

        // 遍历原始课程ID集合，根据suggestRecordMap中的记录确定每个课程是否被推荐
        // 使用Optional来处理可能的空值，确保代码的健壮性
        courseIdSet.forEach(courseId -> suggestMap.put(courseId,
            Optional.ofNullable(suggestRecordMap.get(courseId)).isPresent() ? GeneralJudgeEnum.CONFIRM.getValue()
                : GeneralJudgeEnum.NEGATIVE.getValue()));

        // 返回填充好地映射，其中包含了每个课程ID及其是否被推荐的标志
        return suggestMap;
    }

    @Override
    public void assessCourseToPlan(Collection<String> courseIdSet, String userId) {
        AssessCourseDTO assessCourseDTO = new AssessCourseDTO();
        assessCourseDTO.setUserId(userId);
        assessCourseDTO.setCourseIdSet(new HashSet<>(courseIdSet));
        assessCourseDTO.setAddPlan(GeneralJudgeEnum.CONFIRM.getValue());
        courseStudyPlanBiz.assessCourse(assessCourseDTO);
    }

    @Override
    public List<MarchCourseTaskDTO> getMarchCourseTaskInfoByCourseIdList(List<String> courseIdList) {
        if (CollectionUtils.isEmpty(courseIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<Course> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Course::getId, Course::getVoteNumber, Course::getClickNumber);
        queryWrapper.in(Course::getId, courseIdList);
        List<Course> courseList = courseService.list(queryWrapper);
        if (CollectionUtils.isEmpty(courseList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<UserCourseRecord> queryWrapper2 = new LambdaQueryWrapper<>();
        queryWrapper2.select(UserCourseRecord::getCourseId, UserCourseRecord::getIsLearned);
        queryWrapper2.in(UserCourseRecord::getCourseId, courseIdList);
        queryWrapper2.eq(UserCourseRecord::getUserId, UserThreadContext.getUserId());
        List<UserCourseRecord> courseRecordList = userCourseRecordService.list(queryWrapper2);
        Map<String, Integer> userCourseRecordMap = courseRecordList.stream()
            .collect(
                Collectors.toMap(UserCourseRecord::getCourseId, UserCourseRecord::getIsLearned, (key1, key2) -> key1));

        Map<String, String> imgMap = fileFeign.getImageUrlsByIds(courseIdList, ImageBizType.CourseImgIcon.name());
        return courseList.stream().map(c -> {
            MarchCourseTaskDTO info = new MarchCourseTaskDTO();
            BeanUtils.copyProperties(c, info);
            String courseId = c.getId();
            info.setCourseId(courseId);
            Integer isLearned = userCourseRecordMap.get(courseId);
            info.setIsLearned(isLearned == null ? 0 : isLearned);
            String imageUrl = imgMap.get(courseId);
            info.setImageUrl(org.apache.commons.lang3.StringUtils.isEmpty(imageUrl) ? "" : imageUrl);
            return info;
        }).collect(Collectors.toList());
    }

    @Override
    public Long findUserCourseDurationByDate(Date date) {
        UserCourseDurationSimpleQuery queryDTO = new UserCourseDurationSimpleQuery();
        queryDTO.setStartTime(date);

        return courseViewDurationService.findUserCourseDurationByDate(queryDTO);
    }

    @Override
    public ResourceDeleteInfoDTO getCourseDeleteInfoById(String resourceId) {
        return courseMapper.getCourseDeleteInfoById(resourceId);
    }

    @Override
    public int getResourceIsDel(String resourceId) {
        return courseMapper.getCourseIsDelById(resourceId);
    }

    @Override
    public Map<String, Integer> getCourseIsDel(Collection<String> courseIds) {
        List<CourseInfoDTO> courseInfoDTOS = courseMapper.getCourseIsDel(courseIds);
        if (courseInfoDTOS.isEmpty()) {
            return new HashMap<>();
        }
        return courseInfoDTOS.stream().collect(Collectors.toMap(CourseInfoDTO::getId, CourseInfoDTO::getIsDel));
    }

    @Override
    public List<String> listPublishedCourseIds(List<String> courseIds) {
        return courseMapper.listPublishedCourseIds(courseIds);
    }


    @Override
    public Map<String, Long> getCourseDurationMap(Collection<String> userIds,
        Collection<String> courseIds) {
        return courseViewDurationService.getCourseDurationMap(userIds, courseIds);
    }
}


