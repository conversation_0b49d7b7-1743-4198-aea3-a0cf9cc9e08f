package com.wunding.learn.course.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import com.wunding.learn.course.api.dto.CourseDurationDTO;
import com.wunding.learn.course.service.client.dto.CourseViewDurationClientDTO;
import com.wunding.learn.course.service.client.dto.CourseViewDurationDTO;
import com.wunding.learn.course.service.client.query.CourseViewDurationClientQuery;
import com.wunding.learn.course.service.client.query.UserCourseDurationQuery;
import com.wunding.learn.course.service.client.query.UserCourseDurationSimpleQuery;
import com.wunding.learn.course.service.model.CourseViewDuration;
import java.util.Collection;
import java.util.List;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 课件学时上报表 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">余学益</a>
 * @date 2022-05-10
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")
})
public interface CourseViewDurationMapper extends BaseMapper<CourseViewDuration> {

    /**
     * 查询该课件的学习总时间
     *
     * @param courseView 查询对象
     * @return 学习总时间
     */
    CourseViewDurationClientDTO sumCourseStudyTime(@Param("params") CourseViewDurationClientQuery courseView);

    /**
     * 查询该课件的最新上报的时长
     *
     * @param courseView 查询对象
     * @return 最新上报的时长
     */
    Integer getNewProgress(@Param("params") CourseViewDurationClientQuery courseView);


    /**
     * 查询用户的课时记录
     *
     * @param userCourseDurationQuery
     * @return
     */
    List<CourseViewDuration> selectUserCourseViewDurationList(UserCourseDurationQuery userCourseDurationQuery);


    /**
     * 查询用户的课时
     *
     * @param userCourseDurationSimpleQuery
     * @return
     */
    Long selectUserCourseViewDuration(UserCourseDurationSimpleQuery userCourseDurationSimpleQuery);


    /**
     * 将课程学习时间汇总到地图
     *
     * @param courseViewList 课程视图列表
     * @return {@link List}<{@link CourseViewDurationDTO}>
     */
    List<CourseViewDurationDTO> sumCourseStudyTimeToMap(
        @Param("paramList") List<CourseViewDurationClientQuery> courseViewList);

    /*
     * 根据用户ID集合查询指定课程的学习时长
     * @param userIds 用户ID集合
     * @param courseIds 课程ID集合
     * @return 用户累计学时集合
     */
    List<CourseDurationDTO> getCourseDurationMap(Collection<String> userIds, Collection<String> courseIds);
}
