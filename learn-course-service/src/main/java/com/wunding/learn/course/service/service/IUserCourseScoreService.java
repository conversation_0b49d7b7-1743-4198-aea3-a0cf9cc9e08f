package com.wunding.learn.course.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.course.service.model.UserCourseScore;

/**
 * <p> 用户课程考试成绩 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">余学益</a>
 * @date 2022-05-10
 */
public interface IUserCourseScoreService extends IService<UserCourseScore> {


    /**
     * 修改考试成绩
     * @param id 课程Id
     * @param cwid 课件Id
     * @param score 成绩
     */
    void updateCourseScore(String id, String cwid, String score);
}
