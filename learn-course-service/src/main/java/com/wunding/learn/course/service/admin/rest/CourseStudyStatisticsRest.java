package com.wunding.learn.course.service.admin.rest;


import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.bean.Result;
import com.wunding.learn.common.constant.dynamicDataSource.DynamicDataSourceConstant;
import com.wunding.learn.common.constant.other.ErrorNoEnum;
import com.wunding.learn.common.constant.redis.TenantRedisKeyConstant;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.course.service.admin.dto.CourseStudyDetailDTO;
import com.wunding.learn.course.service.admin.dto.CourseStudyStatisticsDTO;
import com.wunding.learn.course.service.admin.dto.CourseStudyStatisticsUserDTO;
import com.wunding.learn.course.service.admin.query.CourseStudyStatisticsQuery;
import com.wunding.learn.course.service.admin.query.CourseStudyStatisticsUserQuery;
import com.wunding.learn.course.service.service.ICourseStudyStatisticsService;
import com.wunding.learn.file.api.dto.ExportResultDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import java.util.Map;
import java.util.Set;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>  课程学习统计按部门 前端控制器
 *
 * <AUTHOR> href="mailto:<EMAIL>">zhongchaoheng</a>
 * @since 2023-05-11
 */
@Slf4j
@RestController
@RequestMapping("${module.course.contentPath:/}service/courseStudyStatistics")
@Tag(description = "课程学习统计-部门", name = "CourseStudyStatisticsRest")
public class CourseStudyStatisticsRest {

    @Resource
    ICourseStudyStatisticsService courseStudyStatisticsService;

    @Resource
    @SuppressWarnings("rawtypes")
    RedisTemplate redisTemplate;


    @GetMapping("/getCourseStudyStatisticsPage")
    @Operation(operationId = "getCourseStudyStatisticsPage", summary = "课程管理-学习统计-按部门", description = "课程管理-学习统计-按部门")
    public Result<PageInfo<CourseStudyStatisticsDTO>> getCourseStudyStatisticsPage( @Valid @ParameterObject CourseStudyStatisticsQuery query){
        return Result.success(courseStudyStatisticsService.getCourseStudyStatisticsPage(query));
    }


    @PostMapping("/exportCourseStudyStatistics")
    @Operation(operationId = "exportCourseStudyStatistics", summary = "导出课程管理-学习统计-按部门", description = "导出课程管理-学习统计-按部门")
    public Result<ExportResultDTO> exportCourseStudyStatistics(@RequestBody CourseStudyStatisticsQuery query) {
        courseStudyStatisticsService.exportCourseStudyStatistics(query);
        return Result.success();
    }

    @GetMapping("/getCourseStudyStatisticsUserList")
    @Operation(operationId = "getCourseStudyStatisticsUserList", summary = "课程管理-学习统计-按部门-人员明细", description = "课程管理-学习统计-按部门-人员明细")
    public Result<PageInfo<CourseStudyStatisticsUserDTO>> getCourseStudyStatisticsUserList(
        @Valid @ParameterObject CourseStudyStatisticsUserQuery query) {
        return Result.success(courseStudyStatisticsService.getCourseStudyStatisticsUserList(query));
    }

    @PostMapping("/exportCourseStudyStatisticsUser")
    @Operation(operationId = "exportCourseStudyStatisticsUser", summary = "导出课程管理-学习统计-按部门-人员明细", description = "导出课程管理-学习统计-按部门-人员明细")
    public Result<ExportResultDTO> exportCourseStudyStatisticsUser(
        @RequestBody CourseStudyStatisticsUserQuery query) {
        courseStudyStatisticsService.exportCourseStudyStatisticsUser(query);
        return Result.success();
    }

    @GetMapping("/courseStudyStatisticsDetail")
    @Operation(operationId = "courseStudyStatisticsDetail", summary = "课程统计-按部门-课程学习明细", description = "课程管理-按部门-课程学习明细")
    public Result<PageInfo<CourseStudyDetailDTO>> courseStudyStatisticsDetail(
        @ParameterObject @Valid CourseStudyStatisticsQuery query) {
        return Result.success(courseStudyStatisticsService.courseStudyStatisticsDetail(query));
    }


    /**
     * 导出学课程习明细列表
     */
    @PostMapping("/exportStudyStatisticsData")
    @Operation(operationId = "exportStudyStatisticsData", summary = "课程统计-按部门-导出学课程习明细列表", description = "课程统计-按部门-导出学课程习明细列表")
    public Result<ExportResultDTO> exportStudyStatisticsData(@ParameterObject @Valid CourseStudyStatisticsQuery query) {
        courseStudyStatisticsService.exportStudyStatisticsData(query);
        return Result.success();
    }

    @GetMapping("/getLastCollectTime_CourseStudyStatisticsRest")
    @Operation(operationId = "getLastCollectTime_CourseStudyStatisticsRest", summary = "课程统计-按部门-获取最后一次统计时间", description = "课程统计-按部门-获取最后一次统计时间")
    public Result<String> getLastCollectTime(String resourceId){
        return Result.success(courseStudyStatisticsService.getLastCollectTime(resourceId));
    }

    @PostMapping("/courseStudyStatisticsJob")
    @Operation(operationId = "courseStudyStatisticsJob", summary = "课程统计-按部门-定时任务", description = "课程统计-按部门-定时任务")
    public Result<String> courseStudyStatisticsJob(@RequestBody Set<String> resourceIds,@Parameter(description = "type 全量/增量[ALL/PART]") @RequestParam String type){
        Map<String, String> dataSources = redisTemplate.opsForHash().entries(TenantRedisKeyConstant.DB_KEY);
        if (CollectionUtils.isEmpty(dataSources)) {
            log.info("data_source_is_empty,current_job:courseStudyStatistics()");
            throw new BusinessException(ErrorNoEnum.ERR_NOT_EXISTS);
        }
        for (String dbName : dataSources.keySet()) {
            UserThreadContext.setTenantId(dbName.replace(DynamicDataSourceConstant.DB_LOOK_UP_KEY_PREFIX, ""));
            courseStudyStatisticsService.initResourceTaskExecuteData(resourceIds,type);
        }
        UserThreadContext.remove();
        return Result.success();
    }

    @GetMapping("/courseBackupResourceDataJob")
    @Operation(operationId = "courseBackupResourceDataJob", summary = "课程统计-按部门-备份数据-定时任务 ", description = "课程统计-按部门-备份数据-定时任务")
    public Result<String> courseBackupResourceDataJob(@Parameter(description = "type 全量/增量[ALL/PART]") @RequestParam String type){
        Map<String, String> dataSources = redisTemplate.opsForHash().entries(TenantRedisKeyConstant.DB_KEY);
        if (CollectionUtils.isEmpty(dataSources)) {
            log.info("data_source_is_empty,current_job:courseBackupResourceDataJob()");
            throw new BusinessException(ErrorNoEnum.ERR_NOT_EXISTS);
        }
        for (String dbName : dataSources.keySet()) {
            UserThreadContext.setTenantId(dbName.replace(DynamicDataSourceConstant.DB_LOOK_UP_KEY_PREFIX, ""));
            courseStudyStatisticsService.courseBackupResourceData(type);

        }
        UserThreadContext.remove();
        return Result.success();
    }


}
