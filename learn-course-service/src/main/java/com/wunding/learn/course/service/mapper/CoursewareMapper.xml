<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.course.service.mapper.CoursewareMapper">
    <!-- 开启二级缓存 -->
    <!--
<cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.course.service.mapper.CoursewareMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.course.service.model.Courseware">
        <!--@Table courseware-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="course_id" jdbcType="VARCHAR"
          property="courseId"/>
        <result column="cw_name" jdbcType="VARCHAR"
          property="cwName"/>
        <result column="cw_author" jdbcType="VARCHAR"
          property="cwAuthor"/>
        <result column="cw_type" jdbcType="VARCHAR"
          property="cwType"/>
        <result column="org_id" jdbcType="VARCHAR"
          property="orgId"/>
        <result column="mime" jdbcType="VARCHAR"
          property="mime"/>
        <result column="version" jdbcType="VARCHAR"
          property="version"/>
        <result column="descriptions" jdbcType="VARCHAR"
          property="descriptions"/>
        <result column="sort_no" jdbcType="INTEGER"
          property="sortNo"/>
        <result column="is_available" jdbcType="TINYINT"
          property="isAvailable"/>
        <result column="is_del" jdbcType="TINYINT"
          property="isDel"/>
        <result column="play_time" jdbcType="INTEGER"
          property="playTime"/>
        <result column="can_speed" jdbcType="INTEGER"
          property="canSpeed"/>
        <result column="can_drag" jdbcType="INTEGER"
          property="canDrag"/>
        <result column="finish_type" jdbcType="INTEGER"
          property="finishType"/>
        <result column="required_progress" jdbcType="INTEGER"
          property="requiredProgress"/>
        <result column="create_by" jdbcType="VARCHAR"
          property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP"
          property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR"
          property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP"
          property="updateTime"/>
        <result column="lecturer" jdbcType="VARCHAR"
          property="lecturer"/>
        <result column="class_hour" jdbcType="DECIMAL"
          property="classHour"/>
        <result column="agree_score" jdbcType="DECIMAL"
          property="agreeScore"/>
        <result column="is_source" jdbcType="TINYINT"
          property="isSource"/>
        <result column="cw_content" jdbcType="LONGVARCHAR"
          property="cwContent"/>
        <result column="is_same_course" jdbcType="INTEGER"
          property="isSameCourse"/>
        <result column="model_type" jdbcType="TINYINT"
          property="modelType"/>
        <result column="active_time" jdbcType="TIMESTAMP"
          property="activeTime"/>
        <result column="upload_by" jdbcType="VARCHAR"
          property="uploadBy"/>
        <result column="chapter_id" jdbcType="VARCHAR"
          property="chapterId"/>
        <result column="exam_id" jdbcType="VARCHAR"
          property="examId"/>
        <result column="exam_finish_type" jdbcType="INTEGER"
          property="examFinishType"/>
        <result column="real_play_time" jdbcType="INTEGER"
          property="realPlayTime"/>
        <result column="library_cate_id" jdbcType="VARCHAR"
          property="libraryCateId"/>
        <result column="transform_status" jdbcType="INTEGER"
          property="transformStatus"/>
        <result column="old_mime" jdbcType="VARCHAR"
          property="oldMime"/>
        <result column="is_hangup" jdbcType="TINYINT"
          property="isHangup"/>
        <result column="hangup_duration_minute" jdbcType="INTEGER"
          property="hangupDurationMinute"/>
        <result column="hangup_duration_second" jdbcType="INTEGER"
          property="hangupDurationSecond"/>
        <result column="is_re_upload" jdbcType="TINYINT"
          property="isReUpload"/>
        <result column="show_type" jdbcType="TINYINT"
          property="showType"/>
        <result column="click_number" jdbcType="INTEGER"
          property="clickNumber"/>
        <result column="common_star" jdbcType="DECIMAL"
          property="commonStar"/>
        <result column="comment_number" jdbcType="INTEGER"
          property="commentNumber"/>
        <result column="audit_status" jdbcType="INTEGER"
                property="auditStatus"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
             , course_id
             , cw_name
             , cw_author
             , cw_type
             , org_id
             , mime
             , version
             , descriptions
             , sort_no
             , is_available
             , is_del
             , finish_type
             , required_progress
             , play_time
             , create_by
             , create_time
             , update_by
             , update_time
             , lecturer
             , class_hour
             , agree_score
             , is_source
             , cw_content
             , is_same_course
             , model_type
             , active_time
             , upload_by
             , chapter_id
             , exam_id
             , exam_finish_type
             , real_play_time
             , library_cate_id
             , transform_status
             , old_mime
             , is_hangup
             , hangup_duration_minute
             , hangup_duration_second
             , click_number
             , common_star
             , comment_number
             , audit_status
    </sql>

    <resultMap id="coursewareQuestionAnswerRecordMap"
      type="com.wunding.learn.course.service.admin.dto.CwQuestionAnswerRecordDTO">
        <id property="userId" column="userId"/>
        <result property="coursewareName" column="cwName"/>
        <result property="learnState" column="isLearned"/>
        <collection property="courseWareQuestionList" javaType="list"
          ofType="com.wunding.learn.course.service.admin.dto.CourseWareQuestionDTO">
            <id property="id" column="cwqId"/>
            <result property="questionName" column="questionName"/>
            <result property="isCorrect" column="isCorrect"/>
            <result property="answerTime" column="answerTime"/>
        </collection>
    </resultMap>

    <select id="getCourseWareListByPage" parameterType="com.wunding.learn.course.service.admin.query.CoursewareQuery"
      resultType="com.wunding.learn.course.service.admin.dto.CourseWareDTO" useCache="false">
        select a.id,
               a.cw_name,
               a.cw_author,
               a.cw_type,
               a.transform_status,
               a.is_source,
               a.sort_no,
               a.exam_id,
               b.chapter_name,
               a.show_type,
               a.play_time,
               a.real_play_time as realTime,
               a.buildin_flag,
               a.create_time,
               a.audit_status
        from courseware a
                 left join course_chapter b on a.chapter_id = b.id
        where a.course_id = #{params.courseId,jdbcType=VARCHAR}
          and a.is_del = 0
          and a.is_available = 1
          and a.is_source <![CDATA[ <> ]]> 3
        <if test="params.courseChapterName != null and params.courseChapterName != ''">
            and instr(b.chapter_name, #{params.courseChapterName,jdbcType=VARCHAR}) > 0
        </if>
        <if test="params.courseChapterId != null and params.courseChapterId != ''">
            <choose>
                <when test="params.courseChapterId == '1'.toString()">
                    and a.chapter_id = ''
                </when>
                <otherwise>
                    and a.chapter_id = #{params.courseChapterId}
                </otherwise>
            </choose>
        </if>
        order by b.sort_no, a.sort_no, a.id
    </select>

    <select id="findCourseWareSummaryByPage" resultType="com.wunding.learn.common.dto.LecturerCoursewareDetailDTO"
      useCache="false">
        select cw.id,
        cw.create_time uploadTime,
        c.course_name courseName,
        cw.cw_name cwName,
        cw.is_source uploadType
        from courseware cw
        join course c on c.id = cw.course_id
        where cw.create_by = #{params.userId}
        and cw.is_source != 8
        <if test="null != params.uploadBeginDate ">
            and date_format(cw.create_time,'%Y-%m-%d') >= date_format(#{params.uploadBeginDate},'%Y-%m-%d')
        </if>
        <if test="null != params.uploadEndDate ">
            and date_format(#{params.uploadEndDate},'%Y-%m-%d') >= date_format(cw.create_time,'%Y-%m-%d')
        </if>
        <if test="null != params.courseName and params.courseName != ''">
            and instr(c.course_name, #{params.courseName}) > 0
        </if>
        <if test="null != params.courseWareName and params.courseWareName != ''">
            and instr(cw.cw_name, #{params.courseWareName}) > 0
        </if>
        order by c.create_time desc
    </select>

    <select id="findCourseWareNodeCount" resultType="com.wunding.learn.course.service.admin.dto.courseware.CourseWareNoteCountDTO"
      useCache="false">
        select cn.courseware_id id,
        count(*) noteCount
        from course_note cn
        where cn.audit_status = 1
        <if test="cwIds != null and cwIds.size() > 0">
            and cn.courseware_id in
            <foreach collection="cwIds" item="cwId" open=" (" close=")" separator=",">
                #{cwId}
            </foreach>
        </if>
        group by cn.courseware_id
    </select>


    <!--嵌套子查询-待优化-->
    <select id="getCwUserDetail" parameterType="com.wunding.learn.course.service.admin.query.CourseWareLearnQuery"
      resultType="com.wunding.learn.course.service.admin.dto.CourseWareLearnListDTO" useCache="false">
        select
        cur.id curId,
        c.id,
        cw.id cwId,
        c.org_id                                                                   manageCourseOrgId,
        su.org_id,
        c.course_no,
        c.course_name,
        cw.cw_name,
        cw.play_time,
        cw.exam_id,
        cw.org_id,
        cur.start_time,
        cur.end_time,
        cur.duration,
        cur.user_id,
        (select ca.category_name from categorys ca where ca.id=c.course_cate_id) courseCategoryName,
        c.course_cate_id as courseCategoryId,
        cur.start_time recordCreateTime,
        cur.end_time recordUpdateTime
        from course c
                 left join courseware_user_record cur on cur.course_id = c.id
            <if test="params.updateRecordTimeStart != null">
                force index(courseware_user_record_end_time_IDX)
            </if>
                left join courseware cw on cur.courseware_id = cw.ID
                 left join sys_org so on c.org_id = so.id
                 left join sys_user su on cur.user_id = su.id
        where c.is_train = 0
          and c.is_del = 0
          and cw.is_del = 0
        <if test="params.managerAreaOrgIds != null and params.managerAreaOrgIds.size() > 0">
            and (
            <foreach collection="params.managerAreaOrgIds" item="levelPath" open="(" close=")" separator="or">
                so.level_path like concat(#{levelPath}, '%')
            </foreach>
            or (c.org_id = #{params.currentOrgId} and c.create_by = #{params.currentUserId})
                )
        </if>
        <if test="params.courseWare != null and params.courseWare != ''">
            and instr(cw.cw_name, #{params.courseWare,jdbcType=VARCHAR}) > 0
        </if>
        <if test="params.courseId != null and params.courseId != ''">
            and c.id = #{params.courseId}
        </if>
        <if test="params.courseCode != null and params.courseCode != ''">
            and instr(c.course_no, #{params.courseCode,jdbcType=VARCHAR}) > 0
        </if>
        <if test="params.courseName != null and params.courseName != ''">
            and instr(c.course_name, #{params.courseName,jdbcType=VARCHAR}) > 0
        </if>
        <if test="params.userIdsVo != null and params.userIdsVo.size() > 0">
            <foreach collection="params.userIdsVo" item="uid" open="and cur.user_id in (" close=")"
              separator=",">
                #{uid}
            </foreach>
        </if>
        <if test="params.levelPath != null and params.levelPath != ''">
            and cur.level_path like concat(#{params.levelPath}, '%')
        </if>
        <if test="params.updateRecordTimeStart != null">
            and cur.end_time >= #{params.updateRecordTimeStart}
        </if>
        <if test="params.updateRecordTimeEnd != null">
            and #{params.updateRecordTimeEnd} >= cur.end_time
        </if>
        <if test="params.isLearned != null ">
            and cur.is_learned = #{params.isLearned}
        </if>
        order by cur.end_time desc
    </select>

    <select id="getCourseWareInfo" resultType="com.wunding.learn.course.service.client.dto.CourseWareInfoDTO"
      useCache="false">
        select cw.id,
               cw.cw_name,
               cw.mime,
               cw.descriptions                                                        description,
               cw.cw_type                                                             type,
               cw.create_by,
               cw.cw_author,
               c.id                                                                   course_id,
               c.publish_time                                                         course_Publish_Time,
               c.course_cate_id,
               c.descriptions                                                         course_descriptions,
               cw.play_time setPlayTime,
               d.duration learnedTime,
               ifnull((select complete_percent
                       from courseware_user_scorm_info
                       where user_id = #{userId}
                         and cw_id = cw.id), 0)                                       isLearned,
               ifnull((select count(id) from course_vote where course_id = cw.id), 0) voteNumber,
               cw.real_play_time                                                      play_time,
               cw.play_time,
               cw.can_drag,
               cw.can_speed,
               cw.finish_type,
                   ifnull((select count(1) from courseware_user_record cwur where cwur.courseware_id = cw.id),
                      0)                                                              learnedNumber,
               ifnull(
                 (select count(cvw.id) from course_view cvw where cvw.course_id = cw.course_id and cw.id = cvw.cw_id),
                 0)                                                                   clickNumber,
               cw.is_hangup,
               cw.hangup_duration_minute,
               cw.hangup_duration_second
        from courseware cw
                 left join course c on c.id = cw.course_id
                 left join courseware_user_record d on cw.id = d.courseware_id and d.user_id = #{userId}
        where cw.is_available = 1
          and cw.is_del = 0
          and cw.id = #{id}
        order by cw.sort_no
    </select>
    <select id="getCWVote"
      resultType="com.wunding.learn.course.service.admin.dto.CourseWareLibraryDTO" useCache="false">
        select courseware_id as id ,  if(ifnull(sum(vote_count), 0) = 0, 0, sum(vote_count)) as cwAgree
        from course_vote
        where courseware_id
        <foreach collection="ids" item="item" open="in (" close=")"
          separator=",">
            #{item}
        </foreach>
        group by courseware_id
    </select>


    <!--嵌套子查询-待优化-->
    <select id="getCWLibraryListData"
      parameterType="com.wunding.learn.course.service.admin.query.CourseWareLibraryQuery"
      resultType="com.wunding.learn.course.service.admin.dto.CourseWareLibraryDTO" useCache="false">
        select distinct cw.id,
                        cw.cw_name,
                        cw.cw_type,
                        cw.transform_status,
                        cw.is_source,
                        cw.create_time,
                        cw.create_by,
                        cw.is_available,
                        cw.update_time,
                        cw.update_by,
                        cw.upload_By,
                        0 as cwView,
                        0 as                                              cwAgree,
                        cw.real_play_time,
                        cw.play_time,
                        cw.org_id,
                        if(g.is_del = 0,g.org_name,concat(g.org_name,'(删除)')) org_name,
                        c.id                                                                          libraryCateId,
                        c.category_name                                                               libraryName
        from courseware cw
            inner join sys_org g on g.id = cw.org_id
        <choose>
            <when test="params.libraryCateId != null and params.libraryCateId != ''">
                inner join categorys c on c.id = cw.library_cate_id
                    and c.category_type = 'CourseLibraryCate'
                    and c.level_path like concat(ifnull((select level_path
                                                         from categorys
                                                         where id =
                                                               #{params.libraryCateId}), ''), '%')
                    and c.is_del = 0 and c.is_available = 1
            </when>
            <otherwise>
                left join categorys c on c.id = cw.library_cate_id
                    and c.category_type = 'CourseLibraryCate'
                    and c.is_del = 0 and c.is_available = 1
            </otherwise>
        </choose>
        where cw.is_del = 0
        <if test="params.userManageAreaOrgIdList != null and params.userManageAreaOrgIdList.size() > 0">
            <foreach collection="params.userManageAreaOrgIdList" item="item" open="and (" separator="or">
                g.level_path like concat(#{item}, '%')
            </foreach>
            or cw.create_by = #{params.currentUserId})
        </if>
        <if test='params.categoryType == "courseComplete" or params.categoryType == null or params.categoryType == ""'>
            and cw.is_source = 3
        </if>
        <if test="params.transformStatus != null">
            and cw.transform_status = #{params.transformStatus}
        </if>
        <if test='params.categoryType == "coursePending"'>
            and cw.is_source = 8
        </if>
        <if test="params.createBeginTime != null">
            and cw.create_time >= #{params.createBeginTime}
        </if>
        <if test="params.createEndTime != null">
            and #{params.createEndTime} >= cw.create_time
        </if>
        <if test="params.updateBeginTime != null">
            and cw.update_time >= #{params.updateBeginTime}
        </if>
        <if test="params.updateEndTime != null">
            and #{params.updateEndTime} >= cw.update_time
        </if>
        <if test="params.createByIdsVo != null and params.createByIdsVo.size() > 0">
            <foreach collection="params.createByIdsVo" item="item" open="and cw.create_by in (" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="params.updateByIdsVo != null and params.updateByIdsVo.size() > 0">
            <foreach collection="params.updateByIdsVo" item="item" open="and cw.update_by in (" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="params.isAvailable != null">
            and cw.is_available = #{params.isAvailable}
        </if>
        <if test="params.cwName != null and params.cwName.trim() != ''">
            and instr(cw.cw_name, #{params.cwName}) > 0
        </if>
        <if test="params.cwType != null and params.cwType != ''">
            <choose>
                <when test="params.cwType == 'Word'">
                    and cw.cw_type in ('Word', 'DOC', 'DOCX')
                </when>
                <when test="params.cwType == 'PPT'">
                    and cw.cw_type in ('PPT', 'PPTX')
                </when>
                <when test="params.cwType == 'ZIP'">
                    and cw.cw_type in ('Scorm', 'Pic')
                </when>
                <otherwise>
                    and cw.cw_type = #{params.cwType}
                </otherwise>
            </choose>
        </if>
        order by  cw.id desc
    </select>

    <!--嵌套子查询-待优化-->
    <select id="getCwListByUserId" resultType="com.wunding.learn.course.api.dto.CourseWareDTO" useCache="false">
        select cw.id,
               cw.cw_name,
               cw.mime,
               cw.update_time,
               cw.class_hour,
               cw.descriptions,
               cw.course_id,
               (select c.publish_time
                from course c
                where c.id = cw.course_id)   publish_time,
               (select count(id)
                from course_vote
                where course_id = cw.id)     favorite_Number,
               (select count(a.id)
                from course_view a
                where a.cw_id = cw.id
                  and a.course_id = cw.course_id
                  and a.view_By = #{userId}) learned,
               cw.sort_no,
               cw.create_time,
               (select ifnull(cwu.duration, 0)
                from courseware_user_record cwu
                where cwu.courseware_id = cw.id
                  and cwu.user_id
                    =
                      #{userId})             learned_time
        from courseware cw
        where cw.is_del = 0
        <foreach collection="cwIds" item="cwId" open="and cw.id  in (" close=")" separator=",">
            #{cwId}
        </foreach>
        <if test="cwName != null and cwName != ''">
            and instr(cw.cw_name, #{cwName}) > 0
        </if>
    </select>

    <select id="getCourseWareCashById" resultType="com.wunding.learn.course.service.admin.dto.CourseWareCashDTO"
      useCache="true">
        select id, cw_name, play_time, exam_id
        from courseware
        where id = #{id}
    </select>

    <select id="getCourseWareCashMapByIds" resultType="com.wunding.learn.course.service.admin.dto.CourseWareCashDTO">
        select id, cw_name, play_time, exam_id, course_id courseId
        from courseware
        <where>
            <if test="cwIds != null and cwIds.size() > 0">
                <foreach collection="cwIds" item="item" open=" id in (" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getCourseIdByCwId" resultType="java.lang.String" useCache="false">
        select course_id
        from courseware
        where id = #{id}
    </select>

    <select id="getCourseWareByUserId" resultType="com.wunding.learn.course.api.dto.CourseWareDTO">
        select cw.*, c.course_name courseName
        from courseware cw
                 join course c on cw.course_id = c.id
        where cw.create_by = #{userId}
        order by cw.create_time desc
    </select>

    <select id="getCourseWareNameDetailByCwIds" resultType="com.wunding.learn.course.api.dto.CourseWareNameDetailDTO">
        select cw.id, cw.cw_name, c.course_name courseName
        from courseware cw
                 join course c on cw.course_id = c.id
        <where>
            <if test="cwIds != null and cwIds.size() > 0">
                <foreach collection="cwIds" item="item" open=" cw.id in (" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getTotalCoursewareDuration" resultType="java.math.BigDecimal">
        select coalesce(sum(play_time), 0.0)
        from courseware
        where is_del = 0
          and course_id = #{courseId}
    </select>
    <select id="findCourseWareListByCourseId" resultType="com.wunding.learn.course.service.admin.dto.CourseWareDTO">
        select id, cw_name cwName
        from courseware
        where course_id = #{courseId}
    </select>
    <select id="queryCoursewareQuestionAnswerRecordList"
      resultMap="coursewareQuestionAnswerRecordMap" useCache="false">
        select cur.is_learned isLearned,
                cw.cw_name cwName,
                cqar.is_correct isCorrect,
                cqar.answer_time answerTime,
                cqar.user_id userId,
                cwq.id cwqId,
                cwq.question_name questionName
        from
        courseware_question_answer_record cqar
        inner join courseware cw on cqar.courseware_id = cw.id
        inner join courseware_user_record cur on cur.courseware_id = cw.id and cqar.user_id = cur.user_id
        inner join courseware_question cwq on cw.id = cwq.courseware_id and cqar.question_id = cwq.id
        inner join sys_user su on su.id = cqar.user_id
        left join sys_org so on so.id = su.org_id
        where cqar.is_first = 1
          and cw.id = #{query.coursewareId}
          and cqar.question_id in
        <foreach collection="query.questionIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="query.userIds != null and query.userIds.size() > 0">
            and cqar.user_id in
            <foreach collection="query.userIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.isLearned != null">
            and cur.is_learned = #{query.isLearned}
        </if>
        <if test="query.orgId != null and query.orgId != ''">
            and so.level_path like concat((select level_path from sys_org where id = #{query.orgId}), '%')
        </if>
        order by cwq.show_time, cwq.sort_no, cwq.id
    </select>

    <update id="updateCourseIdById">
        update courseware
        set is_del       = 0,
            course_id    = #{courseId},
            update_by    = #{userId},
            is_available = 1,
            update_time  = now()
        where id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <select id="queryCoursewareQuestionAnswerRecordListByViewLimit"
      resultType="com.wunding.learn.course.service.admin.dto.CwQuestionAnswerRecordDTO"
      useCache="false">
        select wvlu.user_id               userId,
               ifnull(cur.is_learned, -1) learnState
        from w_view_limit_user wvlu
            left join courseware_user_record cur
                      on wvlu.user_id = cur.user_id and cur.courseware_id = #{query.coursewareId}
        <if test="query.orgId != null and query.orgId != ''">
            inner join sys_user su on su.id = wvlu.user_id
            left join sys_org so on so.id = su.org_id
        </if>
        where wvlu.view_limit_id = #{query.viewLimitId}
        <if test="query.userIds != null and query.userIds.size() > 0">
            and wvlu.user_id in
            <foreach collection="query.userIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.orgId != null and query.orgId != ''">
            and so.level_path like concat((select level_path from sys_org where id = #{query.orgId}), '%')
        </if>
        <if test="query.isLearned != null and (query.isLearned == 1 or query.isLearned == 0)">
            and cur.is_learned = #{query.isLearned}
        </if>
        <if test="query.isLearned != null and query.isLearned == -1">
            and cur.is_learned is null
        </if>
        order by cur.is_learned desc, wvlu.user_id
    </select>

    <select id="selectListFromDB"
      resultMap="BaseResultMap" useCache="false">
        select t.*
        from courseware t left outer  join course a on a.id  =t.course_id
        where 1 =1
        <if test="courseIdSet != null and courseIdSet.size() > 0">
            <foreach collection="courseIdSet" item="courseId" open="and t.course_id in (" close=")"
              separator=",">
                #{courseId}
            </foreach>
        </if>
    </select>

    <select id="getCourseIdsByCwIds" resultType="java.lang.String">
        select cw.course_id
        from courseware cw
        where
        cw.id in
        <foreach collection="cwIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getCoursewareCommonStarByCoursewareId" resultType="java.math.BigDecimal" useCache="false">
        select round(avg(star_count), 1) from courseware_star cs where cs.cw_id = #{cwId}
    </select>

    <select id="getCoursewareClickNumberByCoursewareId" resultType="java.lang.Integer" useCache="false">
        select count(cvw.id) from course_view cvw where cvw.cw_id = #{cwId}
    </select>

    <update id="updateCoursewareCommonStar">
        update courseware cw set cw.common_star = #{commonStar} where cw.id = #{cwId}
    </update>

    <update id="updateCoursewareClickNumber">
        update courseware cw set cw.click_number = #{clickNumber} where cw.id = #{cwId}
    </update>

    <select id="getMaxSortNo" resultType="int">
        select
            ifnull( max( sort_no ), 0 ) sort_no
        from
            courseware
        where
            course_id = #{courseId}
            and is_del = 0
    </select>

    <select id="transformFailList"
            resultType="com.wunding.learn.course.service.admin.dto.CoursewareTransformFailDTO" useCache="false">
        select
        cw.id as cwId,
        cw.cw_name,
        cw.transform_status,
        cw.transform_start_time,
        cw.transform_end_time,
        c.id as courseId,
        c.course_name,
        c.course_no,
        c.buildin_flag
        from courseware cw
        left join course c on cw.course_id = c.id
        where cw.is_del = 0
        and cw.transform_status = 3
        and cw.is_source != 8 -- 排除待入库课件
        <if test="whereToQuery != null and whereToQuery == 'CourseLib'">
            and cw.is_source <![CDATA[ <> ]]> 3
            and c.is_del = 0
            and c.is_train = 0
        </if>
        <if test="whereToQuery != null and whereToQuery == 'CoursewareLib'">
            and cw.is_source = 3
        </if>
        <if test="currentUserId != null and currentUserId != ''">
            and cw.create_by = #{currentUserId}
        </if>
        order by cw.transform_end_time desc , cw.id desc
    </select>

</mapper>
