package com.wunding.learn.market.api.query;

import com.wunding.learn.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">aixinrong</a>
 * @since 2024/7/5
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(name = "SignInScreenQuery", description = "签到大屏查询对象")
public class SignInScreenQuery extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @Parameter(description = "签到id")
    @NotBlank(message = "签到id不能为空")
    private String signId;

    @Parameter(description = "业务id：学习项目id、培训项目id")
    private String bizId;
}
