package com.wunding.learn.project.service.event;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.wunding.learn.common.constant.mq.MqConst;
import com.wunding.learn.common.mq.event.AbstractEvent;
import lombok.Getter;

/**
 *
 */
@Getter
public class ProjectHoldMonthStatisticEvent extends AbstractEvent {

    /**
     * 批次id
     */
    private String batchId;


    @JsonCreator
    public ProjectHoldMonthStatisticEvent(String batchId) {
        super(MqConst.EXCHANGE_TOPIC, EXCHANGE_TOPIC, MqConst.PROJECT_HOLD_MONTH_STATISTIC_ROUTING_KEY);
        this.batchId = batchId;
    }
}
