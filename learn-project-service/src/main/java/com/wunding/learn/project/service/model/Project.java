package com.wunding.learn.project.service.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 项目主表
 *
 * <AUTHOR> href="mailto:<EMAIL>">赖卓成</a>
 * @since 2022-07-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("project")
@Schema(name = "Project对象", description = "项目主表")
public class Project implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 项目ID
     */
    @Schema(description = "项目ID")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;


    /**
     * 项目名称
     */
    @Schema(description = "项目名称")
    @TableField("pro_name")
    private String proName;


    /**
     * 项目编号
     */
    @Schema(description = "项目编号")
    @TableField("pro_no")
    private String proNo;


    /**
     * 计划类型[0固定日期，1固定周期]
     */
    @Schema(description = "计划类型[0固定日期，1固定周期]")
    @TableField("type")
    private Integer type;


    /**
     * 项目开始时间
     */
    @Schema(description = "项目开始时间")
    @TableField("start_time")
    private Date startTime;


    /**
     * 项目结束时间
     */
    @Schema(description = "项目结束时间")
    @TableField("end_time")
    private Date endTime;


    /**
     * 项目描述
     */
    @Schema(description = "项目描述")
    @TableField("pro_desc")
    private String proDesc;


    /**
     * 封面图片
     */
    @Schema(description = "封面图片")
    @TableField("cover_image")
    private String coverImage;


    /**
     * 固定周期天数
     */
    @Schema(description = "固定周期天数")
    @TableField("cycle_day")
    private Long cycleDay;


    /**
     * 项目发布时间
     */
    @Schema(description = "项目发布时间")
    @TableField("publish_time")
    private Date publishTime;


    /**
     * 项目发布人
     */
    @Schema(description = "项目发布人")
    @TableField("publish_by")
    private String publishBy;


    /**
     * 是否发布
     */
    @Schema(description = "是否发布")
    @TableField("is_publish")
    private Integer isPublish;


    /**
     * 是否启用
     */
    @Schema(description = "是否启用")
    @TableField("is_available")
    private Integer isAvailable;


    /**
     * 项目启用工具
     */
    @Schema(description = "项目启用工具")
    @TableField("project_item")
    private String projectItem;


    /**
     * 学习人数
     */
    @Schema(description = "学习人数")
    @TableField("person")
    private Long person;


    /**
     * 0:未结业，1：已结业，2：培训班超过24小时已发邮件(周期项目，字段始终为0，不结业)
     */
    @Schema(description = "0:未结业，1：已结业，2：培训班超过24小时已发邮件(周期项目，字段始终为0，不结业)")
    @TableField("is_completion")
    private Integer isCompletion;


    /**
     * 0-面授 1-远程 2-面授加远程
     */
    @Schema(description = "项目类型  0-面授 1-远程 2-面授加远程")
    @TableField("pro_method")
    private String proMethod;


    /**
     * 是否锁定计划时间 0否 1是
     */
    @Schema(description = "是否锁定计划时间 0否 1是")
    @TableField("is_lock_time")
    private Integer isLockTime;


    /**
     * 班主任
     */
    @Schema(description = "班主任")
    @TableField("leader")
    private String leader;

    /**
     * 班主任不参与统计
     */
    @Schema(description = "班主任不参与统计 0-否，讲师参与统计 1是，讲师不参与统计")
    @TableField("leader_dont_stat")
    private Integer leaderDontStat;


    /**
     * 学习项目地点
     */
    @Schema(description = "学习项目地点")
    @TableField("address")
    private String address;


    /**
     * 学校项目备注
     */
    @Schema(description = "学校项目备注")
    @TableField("mark")
    private String mark;


    /**
     * 教室
     */
    @Schema(description = "教室")
    @TableField("room")
    private String room;

    /**
     * 创建人部门id
     */
    @Schema(description = "创建人部门id")
    @TableField("org_id")
    private String orgId;


    /**
     * 专题分类：在系统“分类管理”配置
     */
    @Schema(description = "专题分类：在系统“分类管理”配置")
    @TableField("the_matic_class")
    private String theMaticClass;


    /**
     * 区分学习项目和专题（为空或0是学习项目，1是专题）
     */
    @Schema(description = "区分学习项目和专题（为空或0是学习项目，1是专题）")
    @TableField("flag")
    private Integer flag;


    /**
     * 适应终端 1 PC 2 APP （复选框）
     */
    @Schema(description = "适应终端 1 PC 2 APP （复选框）")
    @TableField("adaptive_terminal")
    private String adaptiveTerminal;


    /**
     * 标签 最多选5个
     */
    @Schema(description = "标签 最多选5个")
    @TableField("label")
    private String label;


    /**
     * 项目类型 0普通项目 1快速培训项目 2线上课程学习
     */
    @Schema(description = "项目类型 0普通项目 1快速培训项目 2线上课程学习")
    @TableField("project_type")
    private Integer projectType;


    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;


    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 更新人
     */
    @Schema(description = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;


    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    @TableField("is_del")
    @TableLogic(value = "0", delval = "1")
    private Integer isDel;

    /**
     * 下发方式：0 部分可见 1仅创建者可见 2所有人可见
     */
    @Schema(description = "下发方式：0 部分可见 1仅创建者可见 2所有人可见")
    @TableField("view_type")
    private Integer viewType;


    /**
     * 是否使用默认图片，0-否，1-是
     */
    @Schema(description = "是否使用默认图片，0-否，1-是")
    @TableField("is_use_default_img")
    private Integer isUseDefaultImg;


    /**
     * 所有课程学完之后才允许考试，0-不允许，1-允许
     */
    @Schema(description = "所有课程学完之后才允许考试，0-不允许，1-允许")
    @TableField("after_finished_course_can_start_exam")
    private Integer afterFinishedCourseCanStartExam;

    /**
     * 引用类型  0=默认值  1=培训项目
     */
    @Schema(description = "引用类型  0=默认值  1=培训项目")
    @TableField("referenced_type")
    private Integer referencedType;


    /**
     * 引用资源的ID
     */
    @Schema(description = "引用资源的ID")
    @TableField("referenced_id")
    private String referencedId;


    /**
     * 引用资源名称
     */
    @Schema(description = "引用资源名称")
    @TableField("referenced_name")
    private String referencedName;


    /**
     * 培训计划清单id
     */
    @Schema(description = "培训计划清单id")
    @TableField("plan_inventory_id")
    private String planInventoryId;


    /**
     * 培训类别id
     */
    @Schema(description = "培训类别id")
    @TableField("train_category_id")
    private String trainCategoryId;


    /**
     * 开班类型id
     */
    @Schema(description = "开班类型id")
    @TableField("class_category_id")
    private String classCategoryId;


    /**
     * 培训课题分类id
     */
    @Schema(description = "培训课题分类id")
    @TableField("subject_category_id")
    private String subjectCategoryId;


    /**
     * 最大请假课数
     */
    @Schema(description = "最大请假课数")
    @TableField("max_absent_class")
    private Integer maxAbsentClass;


    /**
     * 请假规则说明
     */
    @Schema(description = "请假规则说明")
    @TableField("absent_rule")
    private String absentRule;


    /**
     * 培训部门id
     */
    @Schema(description = "培训部门id")
    @TableField("train_org_id")
    private String trainOrgId;


    /**
     * 学费
     */
    @Schema(description = "学费")
    @TableField("tuition")
    private BigDecimal tuition;


    /**
     * 详细介绍地址[网址]
     */
    @Schema(description = "详细介绍地址[网址]")
    @TableField("detail_address")
    private String detailAddress;

    /**
     * 培训对象
     */
    @Schema(description = "培训对象")
    @TableField("train_object")
    private String trainObject;


    /**
     * 培训计划id(冗余)
     */
    @Schema(description = "培训计划id(冗余)")
    @TableField("plan_id")
    private String planId;


    /**
     * 是否允许项目内激励(积分)兑换
     */
    @Schema(description = "是否允许项目内激励(积分)兑换", required = true)
    @TableField("enable_excitation_exchange")
    @NotNull(message = "是否允许积分兑换值不能为空")
    private Integer enableExcitationExchange;


    /**
     * 是否是兑换加入
     */
    @Schema(description = "是否是允许兑换加入学习项目")
    @TableField("enable_consume_join")
    private Integer enableConsumeJoin;


    /**
     * 兑换学习消耗激励数量
     */
    @Schema(description = "兑换学习消耗激励数量")
    @TableField("consume_excitation_num")
    private BigDecimal consumeExcitationNum;


    /**
     * 兑换学习消耗激励类型
     */
    @Schema(description = "兑换学习消耗激励类型")
    @TableField("consume_excitation_type")
    private String consumeExcitationType;


    /**
     * 证明材料示例文件转码状态 1 转换中  2 转换完成 3 转换失败
     */
    @Schema(description = "证明材料示例文件转码状态 1 转换中  2 转换完成 3 转换失败")
    @TableField("certification_transform_status")
    private Integer certificationTransformStatus;


    /**
     * 证明材料示例文件mime
     */
    @Schema(description = "证明材料示例文件mime")
    @TableField("certification_mime")
    private String certificationMime;


    /**
     * 其他材料示例文件转码状态 1 转换中  2 转换完成 3 转换失败
     */
    @Schema(description = "其他材料示例文件转码状态 1 转换中  2 转换完成 3 转换失败")
    @TableField("other_transform_status")
    private Integer otherTransformStatus;


    /**
     * 其他材料示例文件mime
     */
    @Schema(description = "其他材料示例文件mime")
    @TableField("other_mime")
    private String otherMime;


    /**
     * 是否隐藏计划时间 0-否 1-是
     */
    @Schema(description = "是否隐藏计划时间 0-否 1-是")
    @TableField("is_hide_time")
    private Integer isHideTime;


    /**
     * 是否隐藏地点 0-否 1-是
     */
    @Schema(description = "是否隐藏地点 0-否 1-是")
    @TableField("is_hide_address")
    private Integer isHideAddress;


    /**
     * 上传材料提示
     */
    @Schema(description = "上传材料提示")
    @TableField("upload_file_remark")
    private String uploadFileRemark;


}
