package com.wunding.learn.project.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.dto.StatisticPersonRankDTO;
import com.wunding.learn.evaluation.api.dto.EvalDTO;
import com.wunding.learn.excitation.api.dto.UserExcitationRecordDTO;
import com.wunding.learn.excitation.api.query.UserProjectExcitationRecordQuery;
import com.wunding.learn.project.service.admin.dto.AnalysisByTypeUserDTO;
import com.wunding.learn.project.service.admin.dto.HeaderUnitDTO;
import com.wunding.learn.project.service.admin.dto.ProjectAnalysisByOrgDTO;
import com.wunding.learn.project.service.admin.dto.ProjectAnalysisByProjectDTO;
import com.wunding.learn.project.service.admin.dto.ProjectAnalysisByTypeDTO;
import com.wunding.learn.project.service.admin.dto.ProjectAnalysisByUserDTO;
import com.wunding.learn.project.service.admin.dto.ProjectJoinUserPageDTO;
import com.wunding.learn.project.service.admin.dto.StatisticLecturerEvaluationDTO;
import com.wunding.learn.project.service.admin.dto.StatisticOrgCompleteDTO;
import com.wunding.learn.project.service.admin.dto.StatisticOrgCompleteUserDetailDTO;
import com.wunding.learn.project.service.admin.dto.StatisticOrgLearnRankDTO;
import com.wunding.learn.project.service.admin.dto.StatisticProjectCompletionDTO;
import com.wunding.learn.project.service.admin.dto.StatisticProjectDTO;
import com.wunding.learn.project.service.admin.dto.StatisticSimpleTaskDTO;
import com.wunding.learn.project.service.admin.dto.StatisticTaskDTO;
import com.wunding.learn.project.service.admin.dto.StatisticTaskDetailDTO;
import com.wunding.learn.project.service.admin.dto.StatisticTaskTypeDTO;
import com.wunding.learn.project.service.admin.dto.StatisticTeamCompleteDTO;
import com.wunding.learn.project.service.admin.dto.StatisticTeamDTO;
import com.wunding.learn.project.service.admin.dto.StatisticTeamLearnRankDTO;
import com.wunding.learn.project.service.admin.dto.UserCoursewareStudyDetailDTO;
import com.wunding.learn.project.service.admin.query.AnalysisByTypeUserQuery;
import com.wunding.learn.project.service.admin.query.ProjectAnalysisByOrgQuery;
import com.wunding.learn.project.service.admin.query.ProjectAnalysisByProjectQuery;
import com.wunding.learn.project.service.admin.query.ProjectAnalysisByTypeQuery;
import com.wunding.learn.project.service.admin.query.ProjectAnalysisByUserQuery;
import com.wunding.learn.project.service.admin.query.ProjectJoinUserQuery;
import com.wunding.learn.project.service.admin.query.StatisticLecturerEvaluationQuery;
import com.wunding.learn.project.service.admin.query.StatisticOrgCompleteQuery;
import com.wunding.learn.project.service.admin.query.StatisticOrgCompleteUserDetailQuery;
import com.wunding.learn.project.service.admin.query.StatisticOrgLearnRankQuery;
import com.wunding.learn.project.service.admin.query.StatisticPersonRankQuery;
import com.wunding.learn.project.service.admin.query.StatisticProjectCompletionQuery;
import com.wunding.learn.project.service.admin.query.StatisticProjectOrgQuery;
import com.wunding.learn.project.service.admin.query.StatisticProjectQuery;
import com.wunding.learn.project.service.admin.query.StatisticTaskDetailQuery;
import com.wunding.learn.project.service.admin.query.StatisticTaskQuery;
import com.wunding.learn.project.service.admin.query.StatisticTeamCompleteQuery;
import com.wunding.learn.project.service.admin.query.StatisticTeamCompleteUserDetailQuery;
import com.wunding.learn.project.service.admin.query.StatisticTeamLearnRankQuery;
import com.wunding.learn.project.service.admin.query.UserProjectCoursewareStudyDetailQuery;
import com.wunding.learn.project.service.client.dto.StatisticMyRankInfo;
import com.wunding.learn.project.service.client.dto.StatisticalInfoDTO;
import com.wunding.learn.project.service.client.dto.StatisticsRankPageDTO;
import com.wunding.learn.project.service.client.query.StatisticsMyRankQuery;
import com.wunding.learn.project.service.client.query.StatisticsPageQuery;
import com.wunding.learn.project.service.model.Project;
import jakarta.validation.Valid;
import java.net.UnknownHostException;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import org.springframework.scheduling.annotation.Async;

/**
 * <p>
 * 项目统计
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">wgr</a>
 * @date 2022/9/22 17:11
 */
public interface IProjectStatisticService extends IService<Project> {


    /**
     * 分页查询部门 学习项目统计
     *
     * @param statisticOrgCompleteQuery
     * @return
     */
    PageInfo<StatisticOrgCompleteDTO> findOrgStatisticCompleteByPage(
        StatisticOrgCompleteQuery statisticOrgCompleteQuery);


    /**
     * 分页查询团队 学习项目统计
     *
     * @param statisticTeamCompleteQuery
     * @return
     */
    PageInfo<StatisticTeamCompleteDTO> findTeamStatisticCompleteByPage(
        StatisticTeamCompleteQuery statisticTeamCompleteQuery);


    /**
     * 分页查询学习项目，任务完成情况统计
     *
     * @param statisticTaskQuery
     * @return
     */
    PageInfo<StatisticTaskDTO> findTaskStatisticByPage(StatisticTaskQuery statisticTaskQuery);


    /**
     * 分页查询培训班统计
     *
     * @param statisticProjectQuery
     * @return
     */
    PageInfo<StatisticProjectDTO> findProjectStatisticByPage(StatisticProjectQuery statisticProjectQuery);


    /**
     * 导出培训班统计
     *
     * @param statisticProjectQuery
     */
    @Async
    void exportProjectStatistic(StatisticProjectQuery statisticProjectQuery);


    /**
     * 生成班级统计
     */
    void makeProjectStatistic();


    /**
     * 查询讲师评估统计
     *
     * @param statisticLecturerEvaluationQuery
     * @return
     */
    PageInfo<StatisticLecturerEvaluationDTO> findLecturerEvaluationStatisticByPage(
        StatisticLecturerEvaluationQuery statisticLecturerEvaluationQuery);


    /**
     * 导出培训班讲师统计
     *
     * @param statisticLecturerEvaluationQuery
     */
    @Async
    void exportLecturerEvaluationStatistic(StatisticLecturerEvaluationQuery statisticLecturerEvaluationQuery);


    /**
     * 培训班组织统计
     *
     * @param statisticProjectOrgQuery
     * @return
     */
    PageInfo<Map<String, Object>> findProjectOrgByPage(StatisticProjectOrgQuery statisticProjectOrgQuery);

    /**
     * 查询培训班组织统计表头
     *
     * @param statisticProjectOrgQuery
     * @return
     */
    List<EvalDTO> findProjectEvaluation(StatisticProjectOrgQuery statisticProjectOrgQuery);

    /**
     * 查询培训班组织统计表头
     *
     * @param statisticProjectOrgQuery
     * @return
     */
    List<HeaderUnitDTO> findProjectOrgHeader(StatisticProjectOrgQuery statisticProjectOrgQuery);



    /**
     * 导出培训班组织统计
     *
     * @param statisticProjectOrgQuery
     */
    @Async
    void exportProjectOrgStatistic(StatisticProjectOrgQuery statisticProjectOrgQuery);

    /**
     * 获取学习项目统计信息
     *
     * @param projectId
     * @return
     */
    StatisticalInfoDTO getProjectStatisticalInfo(String projectId);


    /**
     * 查询各个排名列表
     *
     * @param query
     * @return
     */
    PageInfo<StatisticsRankPageDTO> getStatisticsRankPage(StatisticsPageQuery query);


    /**
     * 查询我的排名信息
     *
     * @param query
     * @return
     */
    StatisticMyRankInfo findStatisticMyRankInfo(StatisticsMyRankQuery query);


    /**
     * 查询任务类型
     *
     * @param projectId
     * @return
     */
    List<StatisticTaskTypeDTO> findStatisticTaskTypeList(String projectId);


    /**
     * 查询任务列表
     *
     * @param projectId
     * @param taskType
     * @return
     */
    List<StatisticSimpleTaskDTO> findStatisticTaskList(String projectId, String taskType);


    /**
     * 查询项目的团队
     *
     * @param projectId
     * @return
     */
    List<StatisticTeamDTO> findProjectTeamList(String projectId);


    /**
     * 分页查询部门 学习项目统计
     *
     * @param statisticTaskDetailQuery
     * @return
     */
    PageInfo<StatisticTaskDetailDTO> findTaskDetailByPage(StatisticTaskDetailQuery statisticTaskDetailQuery);


    /**
     * 查询个人学习排名
     *
     * @param statisticPersonRankQuery
     * @return
     */
    PageInfo<StatisticPersonRankDTO> findPersonRankByPage(StatisticPersonRankQuery statisticPersonRankQuery);


    /**
     * 查询团队学习排名统计
     *
     * @param statisticTeamLearnRankQuery
     * @return
     */
    PageInfo<StatisticTeamLearnRankDTO> findTeamLearnRankByPage(
        StatisticTeamLearnRankQuery statisticTeamLearnRankQuery);

    /**
     * 查询部门学习排名统计
     *
     * @param statisticOrgLearnRankQuery
     * @return
     */
    PageInfo<StatisticOrgLearnRankDTO> findOrgLearnRankByPage(StatisticOrgLearnRankQuery statisticOrgLearnRankQuery);

    /**
     * 导出学习项目任务明细统计
     */
    @Async
    void exportTaskDetailData(StatisticTaskDetailQuery queryDTO);

    /**
     * 导出学习项目个人学习排名统计
     */
    @Async
    void exportPersonRankData(StatisticPersonRankQuery queryDTO);

    /**
     * 导出学习项目团队学习排名统计
     */
    @Async
    void exportTeamLearnRankData(StatisticTeamLearnRankQuery queryDTO);

    /**
     * 导出学习项目部门学习排名统计
     */
    @Async
    void exportOrgLearnRankData(StatisticOrgLearnRankQuery queryDTO);

    PageInfo<ProjectJoinUserPageDTO> getProjectJoinUserData(ProjectJoinUserQuery query);

    void exportProjectJoinUserData(ProjectJoinUserQuery query);

    PageInfo<ProjectAnalysisByProjectDTO> stateAnalysisByProject(ProjectAnalysisByProjectQuery query)
        throws ExecutionException, InterruptedException;

    void exportAnalysisByProject(ProjectAnalysisByProjectQuery query);

    PageInfo<ProjectAnalysisByOrgDTO> stateAnalysisByOrg(ProjectAnalysisByOrgQuery query);

    void exportAnalysisByOrg(ProjectAnalysisByOrgQuery query);

    PageInfo<ProjectAnalysisByUserDTO> stateAnalysisByUser(ProjectAnalysisByUserQuery query);

    void exportAnalysisByUser(ProjectAnalysisByUserQuery query);

    /**
    * 学习项目情况统计-按类型(实时查询)
    * @param query
    * @return
    */
    PageInfo<ProjectAnalysisByTypeDTO> stateAnalysisByType(ProjectAnalysisByTypeQuery query);

    /**
     * 导出学习项目情况统计-按类型(实时查询)
     * @param query
     * @return
     */
    @Async
    void exportAnalysisByType(ProjectAnalysisByTypeQuery query);

    /**
     * 获取项目情况统计-按类型-人员数据
     * @param query
     * @return
     */
    PageInfo<AnalysisByTypeUserDTO> getAnalysisByTypeUser(AnalysisByTypeUserQuery query);

    /**
    * 导出项目情况统计-按类型-人员数据
    * @param query
    * @return
    */
    void exportAnalysisByTypeUser(AnalysisByTypeUserQuery query);

    /**
     * 学习项目情况统计-按部门-全量/增量
     * @param type 全量/增量[ALL/PART]
     * @return
     */
    void statisticRecalculateResource(String batchId,String type);

    void statisticRecalculateResource(String batchId, Collection<String> resourceIds);

    /**
    * 学习项目情况统计-按部门-全量/增量
    * @param type 全量/增量[ALL/PART]
    * @return
    */
    void projectStatisticByOrg(String type);

    void projectStatisticByOrg(String batchId, Collection<String> resourceIds);


    /**
    * 根据类型-全量/增量备份项目数据
    * @param type [ALL/PART]
    * @return
    */
    void projectBackupResourceDataByType(String type);

    /**
     * 获取计算学习项目资源
     * @param source 数据源[ProjectBak/Project]
     * @param type 全量/增量[ALL/PART]
     * @return
     */
    Set<String> getRecalculateResource(String source,String type);


    void projectResourceStatisticNew(String batchId) throws UnknownHostException;

    /**
     * 分页查询部门 学习项目统计-部门完成情况-用户明细
     *
     * @param query
     * @return
     */
    PageInfo<StatisticOrgCompleteUserDetailDTO> findOrgStatisticCompleteUserDetailByPage(
        StatisticOrgCompleteUserDetailQuery query);

    /**
     * 导出学习项目-部门完成统计-用户详情
     * @param query
     * @return
     */
    @Async
    void exportOrgCompleteStatisticUserDetail(@Valid StatisticOrgCompleteUserDetailQuery query);

    /**
     * 分页查询部门 学习项目统计-团队完成情况-用户明细
     *
     * @param query
     * @return
     */
    PageInfo<StatisticOrgCompleteUserDetailDTO> findTeamStatisticCompleteUserDetailByPage(
        @Valid StatisticTeamCompleteUserDetailQuery query);

    /**
     * 导出学习项目-团队完成统计-用户详情
     * @param query
     * @return
     */
    @Async
    void exportTeamCompleteStatisticUserDetail(@Valid StatisticTeamCompleteUserDetailQuery query);

    Long getProjectStudentNumById(String projectId);

    /**
     * 项目完成情况统计
     *
     * @param query 项目完成情况统计查询对象
     * @return 项目完成情况统计
     */
    PageInfo<StatisticProjectCompletionDTO> findProjectCompletionStatisticByPage(StatisticProjectCompletionQuery query);

    /**
     * 导出项目完成情况统计
     *
     * @param query 项目完成情况统计查询对象
     */
    @Async
    void exportProjectCompletionStatistic(@Valid StatisticProjectCompletionQuery query);


    /**
     * 获取用户项目激励获取记录
     *
     * @param query 用户项目激励获取记录查询对象
     * @return {@link UserExcitationRecordDTO}
     */
    PageInfo<UserExcitationRecordDTO> findUserProjectExcitationRecordByPage(
        UserProjectExcitationRecordQuery query);

    /**
     * 导出用户项目激励获取记录
     *
     * @param query 用户项目激励获取记录查询对象
     */
    @Async
    void exportUserProjectExcitationRecord(UserProjectExcitationRecordQuery query);

    /**
     * 用户学习项目课时统计
     */
    PageInfo<UserCoursewareStudyDetailDTO> findUserProjectCoursewareStudyDetailByPage(
        UserProjectCoursewareStudyDetailQuery query);

    /**
     * 导出用户学习项目课时统计
     */
    @Async
    void exportUserProjectCoursewareStudyDetail(UserProjectCoursewareStudyDetailQuery query);
}
