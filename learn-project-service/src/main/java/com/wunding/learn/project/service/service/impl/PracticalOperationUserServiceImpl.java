package com.wunding.learn.project.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.mybatis.service.impl.BaseServiceImpl;
import com.wunding.learn.project.api.dto.ProjectPracticalOperationSuperviseInfoDTO;
import com.wunding.learn.project.api.dto.ProjectPracticalOperationUserInfoDTO;
import com.wunding.learn.project.service.admin.dto.PracticalOperationResultDTO;
import com.wunding.learn.project.service.admin.dto.PracticalOperationUserBySuperviseDTO;
import com.wunding.learn.project.service.admin.dto.PracticalOperationUserPageInfoDTO;
import com.wunding.learn.project.service.admin.query.PracticalOperationUserBySuperviseQuery;
import com.wunding.learn.project.service.admin.query.PracticalOperationUserQuery;
import com.wunding.learn.project.service.client.dto.PracticalOperationUserClientDTO;
import com.wunding.learn.project.service.client.query.PracticalOperationUserClientQuery;
import com.wunding.learn.project.service.mapper.PracticalOperationUserMapper;
import com.wunding.learn.project.service.model.PracticalOperationUser;
import com.wunding.learn.project.service.service.IPracticalOperationUserService;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <p> 实操监督人员表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">ydq</a>
 * @since 2024-03-13
 */
@Slf4j
@Service("practicalOperationUserService")
public class PracticalOperationUserServiceImpl extends
    BaseServiceImpl<PracticalOperationUserMapper, PracticalOperationUser> implements IPracticalOperationUserService {

    @Override
    public PageInfo<PracticalOperationUserPageInfoDTO> queryPage(PracticalOperationUserQuery query) {
        return PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> baseMapper.queryPage(query));
    }

    @Override
    public PageInfo<PracticalOperationUserBySuperviseDTO> queryPageBySupervise(
        PracticalOperationUserBySuperviseQuery query) {
        return PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> baseMapper.queryPageBySupervise(query));
    }

    @Override
    public Long getCountByStatus(String superviseId, Integer status) {
        return baseMapper.getCountByStatus(superviseId, status);
    }

    @Override
    public void savePracticalOperationUser(PracticalOperationUser practicalOperationUser) {
        practicalOperationUser.setUpdateBy(UserThreadContext.getUserId());
        practicalOperationUser.setUpdateTime(new Date());
        saveOrUpdate2(practicalOperationUser);
    }

    @Override
    public PracticalOperationUser getPracticalOperationUser(String practicalId, String userId) {
        return lambdaQuery().eq(PracticalOperationUser::getPracticalId, practicalId)
            .eq(PracticalOperationUser::getUserId, userId).one();
    }

    @Override
    public PageInfo<PracticalOperationUserClientDTO> superviseUserList(PracticalOperationUserClientQuery query) {
        return PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> baseMapper.superviseUserList(query));
    }

    @Override
    public List<ProjectPracticalOperationUserInfoDTO> getListByParam(Collection<String> userIdList,
        Collection<String> idList) {
        return baseMapper.getListByParam(userIdList, idList);
    }

    @Override
    public List<ProjectPracticalOperationSuperviseInfoDTO> getSuperviseListByParam(Collection<String> userIdList,
        Collection<String> idList) {
        return baseMapper.getSuperviseListByParam(userIdList, idList);
    }

    @Override
    public Integer getCount(String userId, String practicalId) {
        Long count = lambdaQuery().eq(PracticalOperationUser::getUserId, userId)
            .eq(PracticalOperationUser::getPracticalId, practicalId).isNotNull(PracticalOperationUser::getStatus)
            .count();
        return count.intValue();
    }


    @Override
    public List<PracticalOperationResultDTO> getPracticalOperationResult(String userId,
        Collection<String> practicalIds) {
        if (StringUtils.isBlank(userId) || practicalIds.isEmpty()) {
            return List.of();
        }

        // 查询条件
        LambdaQueryWrapper<PracticalOperationUser> query = new LambdaQueryWrapper<PracticalOperationUser>()
            .eq(PracticalOperationUser::getUserId, userId)
            .in(PracticalOperationUser::getPracticalId, practicalIds)
            .select(
                PracticalOperationUser::getPracticalId,
                PracticalOperationUser::getStatus,
                PracticalOperationUser::getScore
            );

        // 查询数据
        List<PracticalOperationUser> practicalOperationUsers = baseMapper.selectList(query);

        // 数据转换，返回结果
        return practicalOperationUsers.stream().map(practicalOperationUser -> {
            PracticalOperationResultDTO dto = new PracticalOperationResultDTO();
            dto.setId(practicalOperationUser.getPracticalId());
            dto.setStatus(practicalOperationUser.getStatus());
            dto.setPassScore(practicalOperationUser.getScore());
            return dto;
        }).toList();
    }
}
