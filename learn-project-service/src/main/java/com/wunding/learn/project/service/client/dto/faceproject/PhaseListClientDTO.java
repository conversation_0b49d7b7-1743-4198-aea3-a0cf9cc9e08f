package com.wunding.learn.project.service.client.dto.faceproject;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

@Data
@Schema(description = "客户端面授项目主题列表数据对象", name = "phaseListClientDTO")
public class PhaseListClientDTO {

    @Schema(description = "主题id")
    private String id;

    @Schema(description = "主题名称")
    private String name;

    @Schema(description = "日程列表")
    private List<ScheduleListClientDTO> scheduleList;
}
