package com.wunding.learn.project.service.admin.dto.faceproject;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * <p> 面授项目文件列表dto
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2024-03-20
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(name = "faceProjectListDTO", description = "文件管理列表对象")
public class FaceProjectFileListDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "文件ID")
    private String id;

    @Schema(description = "文件名称")
    private String title;

    @Schema(description = "文件类型")
    private String fileType;

    @Schema(description = "目录名称")
    private String directoryId;

    @Schema(description = "目录名称")
    private String directoryName;

    @Schema(description = "文件大小(单位：m)")
    private BigDecimal fileSize;

    @Schema(description = "文件名称")
    private String fileName;

    @Schema(description = "文件下载地址")
    private String downloadUrl;

    @Schema(description = "添加时间")
    private Date createTime;

    @Schema(description = "是否对文件进行了修改 0否 1是")
    private Integer updateFile;

    @Schema(description = "转码状态（1-转码中 2-转码成功 3-转码失败 4-排队中）")
    private Integer transformStatus;

    @Schema(description = "文件预览地址")
    private String previewUrl;

    @Schema(description = "文件MIME")
    private String mime;
}
