package com.wunding.learn.project.service.admin.query;

import com.wunding.learn.common.bean.BasePageQuery;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 项目完成情况统计查询对象
 *
 * <AUTHOR>
 * @since 2025-06-13
 */
@Data
public class StatisticProjectCompletionQuery extends BasePageQuery implements Serializable {


    private static final long serialVersionUID = -7911632544247438387L;

    @Parameter(description = "学习项目ID")
    @NotBlank(message = "学习项目id不可为空")
    private String projectId;

    /**
     * 用户ID，多个用户ID之间用逗号分隔
     */
    @Parameter(description = "用户ID，多个用户ID之间用逗号分隔")
    private String userId;


    @Parameter(description = "用户id", hidden = true)
    private List<String> userIdList;

    /**
     * 部门（组织机构Id）
     */
    @Parameter(description = "部门（组织机构Id）")
    private String orgId;

    /**
     * 团队ID
     */
    @Parameter(description = "团队ID")
    private String teamId;


    /**
     * 班主任
     */
    @Parameter(description = "班主任", hidden = true)
    private String leader;

    /**
     * 班主任不参与统计
     */
    @Parameter(description = "班主任不参与统计 0-否，班主任参与统计 1是，班主任不参与统计", hidden = true)
    private Integer leaderDontStat;
}
