package com.wunding.learn.project.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import com.wunding.learn.project.service.model.FormColumnRecordBlock;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 辅导栏目记录屏蔽表 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">li<PERSON><PERSON></a>
 * @since 2022-09-02
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")
})
public interface FormColumnRecordBlockMapper extends BaseMapper<FormColumnRecordBlock> {


}
