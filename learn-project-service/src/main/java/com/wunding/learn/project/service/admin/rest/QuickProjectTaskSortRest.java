package com.wunding.learn.project.service.admin.rest;


import com.wunding.learn.common.bean.Result;
import com.wunding.learn.project.service.admin.dto.QuickProjectTaskSortDTO;
import com.wunding.learn.project.service.service.IQuickProjectTaskSortService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>   前端控制器
 *
 * <AUTHOR> href="mailto:<EMAIL>">cjn</a>
 * @since 2023-06-12
 */
@RestController
@RequestMapping("${module.project.contentPath:/}quickProjectTaskSort")
@Tag(description = "快速培训的任务显示排序", name = "QuickProjectTaskSortRest")
public class QuickProjectTaskSortRest {

    @Resource
    private IQuickProjectTaskSortService quickProjectTaskSortService;

    @PostMapping("/save")
    @Operation(operationId = "saveQuickProjectTaskSort", summary = "添加快速培训的任务显示排序", description = "添加快速培训的任务显示排序")
    public Result<Void> saveQuickProjectTaskSort(@RequestBody QuickProjectTaskSortDTO quickProjectTaskSortDTO) {
        quickProjectTaskSortService.saveOrUpdateQuickProjectTaskSort(quickProjectTaskSortDTO);
        return Result.success();
    }

    @GetMapping("/detail")
    @Operation(operationId = "getDetailByQuickProjectId", summary = "根据快速培训id获取排序", description = "根据快速培训id获取排序")
    public Result<String> getDetailByQuickProjectId(@RequestParam("quickProjectId") @Parameter(description = "快速培训id", required = true) String quickProjectId) {
        return Result.success(quickProjectTaskSortService.getDetailByQuickProjectId(quickProjectId));
    }
}
