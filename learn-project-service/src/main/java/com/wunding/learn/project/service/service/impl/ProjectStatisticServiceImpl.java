package com.wunding.learn.project.service.service.impl;

import static com.wunding.learn.common.util.string.StringUtil.newId;

import com.alibaba.excel.util.DateUtils;
import com.alibaba.ttl.threadpool.TtlExecutors;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import com.wunding.learn.common.category.service.ICategorysService;
import com.wunding.learn.common.constant.other.ErrorNoEnum;
import com.wunding.learn.common.constant.project.ProjectErrorNoEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.dto.StatisticPersonRankDTO;
import com.wunding.learn.common.enums.evaluation.EvaluationTypeEnum;
import com.wunding.learn.common.enums.evaluation.QuestionType;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.other.SystemConfigCodeEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.i18n.util.I18nUtil;
import com.wunding.learn.common.mq.service.impl.RabbitMqProducer;
import com.wunding.learn.common.table.partition.enums.TablePartitionEnum;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.date.DateHelper;
import com.wunding.learn.common.util.date.DateUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.util.math.NumberOperationUtils;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.common.util.string.TranslateUtil;
import com.wunding.learn.common.viewlimit.constant.LimitTable;
import com.wunding.learn.common.viewlimit.dto.ResourceViewLimitBakDTO;
import com.wunding.learn.common.viewlimit.model.ResourceViewLimit;
import com.wunding.learn.common.viewlimit.service.IResourceViewLimitBakService;
import com.wunding.learn.common.viewlimit.service.IResourceViewLimitService;
import com.wunding.learn.course.api.dto.CourseDurationDTO;
import com.wunding.learn.course.api.dto.CourseWareNameDetailDTO;
import com.wunding.learn.course.api.dto.UserCourseStudyInfoDTO;
import com.wunding.learn.course.api.query.CourseDurationQuery;
import com.wunding.learn.course.api.query.UserCourseStudyInfoQuery;
import com.wunding.learn.course.api.service.CourseFeign;
import com.wunding.learn.course.api.service.CourseRecordFeign;
import com.wunding.learn.course.api.service.CourseWareFeign;
import com.wunding.learn.evaluation.api.dto.EvalDTO;
import com.wunding.learn.evaluation.api.dto.EvalQuestionFeignDTO;
import com.wunding.learn.evaluation.api.dto.EvalQuestionItemFeignDTO;
import com.wunding.learn.evaluation.api.dto.EvaluationListDTO;
import com.wunding.learn.evaluation.api.dto.ProjectEvalAvgScore;
import com.wunding.learn.evaluation.api.dto.ProjectSimpleEvalOrgDTO;
import com.wunding.learn.evaluation.api.query.EvaluationProjectQuery;
import com.wunding.learn.evaluation.api.service.EvaluationFeign;
import com.wunding.learn.evaluation.api.service.EvaluationQuestionFeign;
import com.wunding.learn.exam.api.dto.ExamScoreDTO;
import com.wunding.learn.exam.api.query.ExamScoreQuery;
import com.wunding.learn.exam.api.service.ExamFeign;
import com.wunding.learn.excitation.api.dto.UserExcitationRecordDTO;
import com.wunding.learn.excitation.api.dto.UserExcitationStatisticDTO;
import com.wunding.learn.excitation.api.query.UserProjectExcitationRecordQuery;
import com.wunding.learn.excitation.api.service.ExcitationFeign;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.dto.AbstractExportDataDTO;
import com.wunding.learn.file.api.dto.AbstractExportNoEntityDataDTO;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.file.api.dto.IExportNoEntityDataDTO;
import com.wunding.learn.lecturer.api.dto.LecturerExaminationDetailDTO;
import com.wunding.learn.lecturer.api.service.LecturerExaminationFeign;
import com.wunding.learn.market.api.dto.SignNumDTO;
import com.wunding.learn.market.api.service.SignFeign;
import com.wunding.learn.project.service.admin.dto.AnalysisByTypeUserDTO;
import com.wunding.learn.project.service.admin.dto.HeaderUnitDTO;
import com.wunding.learn.project.service.admin.dto.ProjectAnalysisByOrgDTO;
import com.wunding.learn.project.service.admin.dto.ProjectAnalysisByProjectDTO;
import com.wunding.learn.project.service.admin.dto.ProjectAnalysisByTypeDTO;
import com.wunding.learn.project.service.admin.dto.ProjectAnalysisByUserDTO;
import com.wunding.learn.project.service.admin.dto.ProjectJoinUserPageDTO;
import com.wunding.learn.project.service.admin.dto.ProjectStatisticSimpleDTO;
import com.wunding.learn.project.service.admin.dto.StatisticLecturerEvaluationDTO;
import com.wunding.learn.project.service.admin.dto.StatisticOrgCompleteDTO;
import com.wunding.learn.project.service.admin.dto.StatisticOrgCompleteUserDetailDTO;
import com.wunding.learn.project.service.admin.dto.StatisticOrgLearnRankDTO;
import com.wunding.learn.project.service.admin.dto.StatisticProjectCompletionDTO;
import com.wunding.learn.project.service.admin.dto.StatisticProjectDTO;
import com.wunding.learn.project.service.admin.dto.StatisticProjectOrgDTO;
import com.wunding.learn.project.service.admin.dto.StatisticSimpleTaskDTO;
import com.wunding.learn.project.service.admin.dto.StatisticTaskDTO;
import com.wunding.learn.project.service.admin.dto.StatisticTaskDetailDTO;
import com.wunding.learn.project.service.admin.dto.StatisticTaskTypeDTO;
import com.wunding.learn.project.service.admin.dto.StatisticTeamCompleteDTO;
import com.wunding.learn.project.service.admin.dto.StatisticTeamDTO;
import com.wunding.learn.project.service.admin.dto.StatisticTeamLearnRankDTO;
import com.wunding.learn.project.service.admin.dto.UserCoursewareStudyDetailDTO;
import com.wunding.learn.project.service.admin.dto.UserStatisticProjectCompletionDTO;
import com.wunding.learn.project.service.admin.query.AnalysisByTypeUserQuery;
import com.wunding.learn.project.service.admin.query.ProjectAnalysisByOrgQuery;
import com.wunding.learn.project.service.admin.query.ProjectAnalysisByProjectQuery;
import com.wunding.learn.project.service.admin.query.ProjectAnalysisByTypeQuery;
import com.wunding.learn.project.service.admin.query.ProjectAnalysisByUserQuery;
import com.wunding.learn.project.service.admin.query.ProjectJoinUserQuery;
import com.wunding.learn.project.service.admin.query.StatisticLecturerEvaluationQuery;
import com.wunding.learn.project.service.admin.query.StatisticOrgCompleteQuery;
import com.wunding.learn.project.service.admin.query.StatisticOrgCompleteUserDetailQuery;
import com.wunding.learn.project.service.admin.query.StatisticOrgLearnRankQuery;
import com.wunding.learn.project.service.admin.query.StatisticPersonRankQuery;
import com.wunding.learn.project.service.admin.query.StatisticProjectCompletionQuery;
import com.wunding.learn.project.service.admin.query.StatisticProjectOrgQuery;
import com.wunding.learn.project.service.admin.query.StatisticProjectQuery;
import com.wunding.learn.project.service.admin.query.StatisticTaskDetailQuery;
import com.wunding.learn.project.service.admin.query.StatisticTaskQuery;
import com.wunding.learn.project.service.admin.query.StatisticTeamCompleteQuery;
import com.wunding.learn.project.service.admin.query.StatisticTeamCompleteUserDetailQuery;
import com.wunding.learn.project.service.admin.query.StatisticTeamLearnRankQuery;
import com.wunding.learn.project.service.admin.query.UserProjectCoursewareStudyDetailQuery;
import com.wunding.learn.project.service.client.dto.ProjectStatisticDTO;
import com.wunding.learn.project.service.client.dto.StatisticMyRankInfo;
import com.wunding.learn.project.service.client.dto.StatisticalInfoDTO;
import com.wunding.learn.project.service.client.dto.StatisticalInfoDTO.SimpleRankInfo;
import com.wunding.learn.project.service.client.dto.StatisticsRankPageDTO;
import com.wunding.learn.project.service.client.query.StatisticsMyRankQuery;
import com.wunding.learn.project.service.client.query.StatisticsPageQuery;
import com.wunding.learn.project.service.constant.ProjectConstant;
import com.wunding.learn.project.service.enums.HomeWorkSourceEnum;
import com.wunding.learn.project.service.enums.ProjectStatisticalTypeEnum;
import com.wunding.learn.project.service.enums.ProjectTaskTypeEnum;
import com.wunding.learn.project.service.event.ProjectResourceStatisticEvent;
import com.wunding.learn.project.service.mapper.ProjectMapper;
import com.wunding.learn.project.service.mapper.ProjectStatisticMapper;
import com.wunding.learn.project.service.mapper.ResourceViewLimitRecordMapper;
import com.wunding.learn.project.service.mapper.StatisticMapper;
import com.wunding.learn.project.service.mapper.TaskProgressMapper;
import com.wunding.learn.project.service.model.App;
import com.wunding.learn.project.service.model.Progress;
import com.wunding.learn.project.service.model.Project;
import com.wunding.learn.project.service.model.ProjectBak;
import com.wunding.learn.project.service.model.ProjectResourceTaskExecute;
import com.wunding.learn.project.service.model.ProjectStatisticByOrg;
import com.wunding.learn.project.service.model.ProjectStatisticByOrgBatch;
import com.wunding.learn.project.service.model.ProjectStatisticByOrgCollect;
import com.wunding.learn.project.service.model.Statistic;
import com.wunding.learn.project.service.model.Task;
import com.wunding.learn.project.service.model.TaskProgress;
import com.wunding.learn.project.service.model.Team;
import com.wunding.learn.project.service.service.IProjectBakService;
import com.wunding.learn.project.service.service.IProjectService;
import com.wunding.learn.project.service.service.IProjectStatisticByOrgBatchService;
import com.wunding.learn.project.service.service.IProjectStatisticByOrgCollectService;
import com.wunding.learn.project.service.service.IProjectStatisticByOrgService;
import com.wunding.learn.project.service.service.IProjectStatisticByTypeService;
import com.wunding.learn.project.service.service.IProjectStatisticService;
import com.wunding.learn.project.service.service.IResourceTaskExecuteService;
import com.wunding.learn.project.service.service.IStatisticService;
import com.wunding.learn.project.service.service.ITaskService;
import com.wunding.learn.project.service.service.ITeamService;
import com.wunding.learn.user.api.dto.OrgDTO;
import com.wunding.learn.user.api.dto.OrgShowDTO;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.dto.UserIdentityFeignDTO;
import com.wunding.learn.user.api.dto.UserOrgDTO;
import com.wunding.learn.user.api.service.OrgFeign;
import com.wunding.learn.user.api.service.ParaFeign;
import com.wunding.learn.user.api.service.UserFeign;
import com.wunding.learn.user.api.service.UserIdentityFeign;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 项目统计
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">wgr</a>
 * @date 2022/9/22 17:16
 */
@Slf4j
@Service(ProjectConstant.PROJECT_STATISTIC_SERVICE)
public class ProjectStatisticServiceImpl extends ServiceImpl<ProjectStatisticMapper, Project> implements
    IProjectStatisticService {

    public static final String BY_ORG= "byOrg";
    public static final String PROJECT_STATISTIC_SERVICE = "projectStatisticService";
    public static final String IS_JOIN = "isJoin";
    public static final String ADMIN = "admin";
    public static final String SYSTEM = "System";
    public static final String TASK_PROGRESS_BAK = "task_progress_bak_";
    public static final String W_VIEW_LIMIT_USER_BAK = "w_view_limit_user_bak_";
    public static final String PROJECT_PROGRESS_BAK = "project_progress_bak_";
    private static final String IS_IMPORT = "isImport";
    private static final String TASK_NAME = "taskName";
    private static final String IS_REQUIRED = "isRequired";


    @Resource
    private ITaskService taskService;

    @Resource
    private ICategorysService categorysService;

    @Resource
    private IProjectService projectService;

    @Resource
    private ITeamService teamService;

    @Resource
    private UserFeign userFeign;

    @Resource
    private UserIdentityFeign userIdentityFeign;

    @Resource
    private OrgFeign orgFeign;

    @Resource
    private CourseRecordFeign courseRecordFeign;

    @Resource
    private ExamFeign examFeign;
    
    @Resource
    private TaskProgressMapper taskProgressMapper;
    @Resource
    private ProjectMapper projectMapper;

    @Resource
    private ExportComponent exportComponent;

    @Resource
    private StatisticMapper statisticMapper;

    @Resource
    private IStatisticService statisticService;

    @Resource
    private LecturerExaminationFeign lecturerExaminationFeign;

    @Resource
    private EvaluationFeign evaluationFeign;

    @Resource
    private EvaluationQuestionFeign evaluationQuestionFeign;

    @Resource
    private CourseWareFeign courseWareFeign;

    @Resource
    private SignFeign signFeign;
    @Resource
    private ResourceViewLimitRecordMapper resourceViewLimitRecordMapper;
    @Resource
    private RabbitMqProducer mqProducer;
    @Resource
    private IProjectStatisticByOrgService projectStatisticByOrgService;
    @Resource
    private IProjectStatisticByOrgCollectService projectStatisticByOrgCollectService;
    @Resource
    private IProjectStatisticByTypeService projectStatisticByTypeService;
    @Resource
    private IProjectBakService projectBakService;
    @Resource
    private IResourceViewLimitBakService resourceViewLimitBakService;
    @Resource
    private AppBaseServiceImpl appService;
    @Resource
    private ExcitationFeign excitationFeign;
    @Resource
    private CourseFeign courseFeign;

    @Resource
    private ParaFeign paraFeign;

    @Resource
    private ProgressServiceImpl progressService;

    @Resource
    private IResourceTaskExecuteService resourceTaskExecuteService;
    @Resource
    private IProjectStatisticByOrgBatchService projectStatisticByOrgBatchService;

    /**
     * 每次处理10W条数据
     */
    private static final Integer MAX_SIZE = 10000;
    private static final Integer PAGE_SIZE = 100;
    private static final String PROJECT_COMPUTED_RESOURCE_REDIS_KEY = "ProjectComputedResourceRedisKey";

    @Resource
    @SuppressWarnings("rawtypes")
    private RedisTemplate redisTemplate;

    @Resource
    private IResourceViewLimitService resourceViewLimitService;

    //获取cpu核心数
    private static final int cpuCores = Runtime.getRuntime().availableProcessors();
    //默认最大线程数
    private static final int defaultMaximumPoolSize = cpuCores * 2;

    private final ExecutorService restThreadPool = TtlExecutors
        .getTtlExecutorService(
            new ThreadPoolExecutor(
                cpuCores,
                defaultMaximumPoolSize,
                0L,
                TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(),
                new CustomizableThreadFactory("execute-projectStatisticsJob-pool-")
            )
        );
    ExecutorService backupDataExecuteThreadPool = TtlExecutors
        .getTtlExecutorService(
            new ThreadPoolExecutor(
                cpuCores,
                defaultMaximumPoolSize,
                0L,
                TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(),
                new CustomizableThreadFactory("project-execute-backupData-pool")
            )
        );



    @Override
    public PageInfo<StatisticOrgCompleteDTO> findOrgStatisticCompleteByPage(
        StatisticOrgCompleteQuery statisticOrgCompleteQuery) {
        Project project = getById(statisticOrgCompleteQuery.getProjectId());
        statisticOrgCompleteQuery.setLeader(project.getLeader());
        statisticOrgCompleteQuery.setLeaderDontStat(project.getLeaderDontStat());

        //判断查询的统计部门层级 1=实际组织部门 2=二级组织 3=三级组织 4=四级组织
        PageInfo<StatisticOrgCompleteDTO> sqlPageInfo = PageMethod.startPage(statisticOrgCompleteQuery.getPageNo(),
                statisticOrgCompleteQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.selectOrgStatisticCompleteByPage(statisticOrgCompleteQuery));

        List<StatisticOrgCompleteDTO> statisticCompleteDTOList = sqlPageInfo.getList();
        Set<String> orgIds = statisticCompleteDTOList.stream().map(StatisticOrgCompleteDTO::getOrgId)
            .collect(Collectors.toSet());
        // 这里应该通过字段同步修改OrgName那个冗余字段,先为了改bug后续再补
        Map<String, OrgShowDTO> orgShowDTOMap = orgFeign.getOrgShowDTO(orgIds);
        for (StatisticOrgCompleteDTO statisticOrgCompleteDTO : statisticCompleteDTOList) {
            Integer totalJoin = statisticOrgCompleteDTO.getTotalJoin();
            Integer totalFinished = statisticOrgCompleteDTO.getTotalFinished();

            Integer totalNotFinished = totalJoin - totalFinished;
            statisticOrgCompleteDTO.setTotalNotFinished(totalNotFinished);

            BigDecimal rate = NumberOperationUtils.divide(totalFinished, totalJoin, 4);
            String finishedPercent = rate.doubleValue() * 100 + "%";
            statisticOrgCompleteDTO.setFinishedPercent(finishedPercent);

            BigDecimal avgScore = NumberOperationUtils.divide(statisticOrgCompleteDTO.getTotalTaskScore(), totalJoin,
                2);
            statisticOrgCompleteDTO.setAverage(avgScore.floatValue());
            Optional.ofNullable(orgShowDTOMap.get(statisticOrgCompleteDTO.getOrgId())).ifPresentOrElse(orgShowDTO -> {
                statisticOrgCompleteDTO.setOrgName(orgShowDTO.getOrgShortName());
                statisticOrgCompleteDTO.setOrgPath(orgShowDTO.getLevelPathName());
            }, () -> {
                statisticOrgCompleteDTO.setOrgName(null);
                statisticOrgCompleteDTO.setOrgPath(null);
            });
        }
        return sqlPageInfo;
    }


    @Override
    public PageInfo<StatisticTeamCompleteDTO> findTeamStatisticCompleteByPage(StatisticTeamCompleteQuery query) {
        Project project = projectService.getById(query.getProjectId());
        query.setLeader(project.getLeader());
        query.setLeaderDontStat(project.getLeaderDontStat());

        PageInfo<StatisticTeamCompleteDTO> pageInfo = PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> baseMapper.selectTeamStatisticCompleteByPage(query));

        pageInfo.getList().forEach(dto -> {
            // 总计未完成人数
            dto.setTotalNotFinished(dto.getTotalJoin() - dto.getTotalFinished());
            // 完成比例
            BigDecimal rate = NumberOperationUtils.divide(dto.getTotalFinished(), dto.getTotalJoin(), 4);
            String finishedPercent = rate.doubleValue() * 100 + "%";
            dto.setFinishedPercent(finishedPercent);
            // 总计完成任务分 为空时处理
            if (Optional.ofNullable(dto.getTotalTaskScore()).isEmpty()) {
                dto.setAverage((float) 0);
                dto.setTotalTaskScore(0);
            } else {
                BigDecimal avgScore = NumberOperationUtils.divide(dto.getTotalTaskScore(), dto.getTotalJoin(), 2);
                dto.setAverage(avgScore.floatValue());
            }
        });

        return pageInfo;
    }

    @Override
    public PageInfo<StatisticTaskDTO> findTaskStatisticByPage(StatisticTaskQuery statisticTaskQuery) {
        Project project = projectService.getById(statisticTaskQuery.getProjectId());
        statisticTaskQuery.setLeader(project.getLeader());
        statisticTaskQuery.setLeaderDontStat(project.getLeaderDontStat());

        PageInfo<StatisticTaskDTO> sqlPageInfo = PageMethod.startPage(statisticTaskQuery.getPageNo(),
                statisticTaskQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.selectTaskStatisticByPage(statisticTaskQuery));

        List<StatisticTaskDTO> statisticTaskDTOList = sqlPageInfo.getList();
        for (StatisticTaskDTO statisticTaskDTO : statisticTaskDTOList) {
            Integer totalJoin = statisticTaskDTO.getTotalJoin();
            Integer totalFinished = statisticTaskDTO.getTotalFinished();

            Integer totalNotFinished = totalJoin - totalFinished;
            statisticTaskDTO.setTotalNotFinished(totalNotFinished);

            BigDecimal rate = NumberOperationUtils.divide(totalFinished, totalJoin, 4);
            String finishedPercent = rate.doubleValue() * 100 + "%";
            statisticTaskDTO.setFinishedPercent(finishedPercent);

        }
        return sqlPageInfo;
    }


    @Override
    public PageInfo<StatisticProjectDTO> findProjectStatisticByPage(StatisticProjectQuery statisticProjectQuery) {
        PageInfo<StatisticProjectDTO> sqlPageInfo = PageMethod.startPage(statisticProjectQuery.getPageNo(),
                statisticProjectQuery.getPageSize())
            .doSelectPageInfo(() -> statisticMapper.selectStatisticProjectByPage(statisticProjectQuery));

        List<StatisticProjectDTO> statisticProjectDTOList = sqlPageInfo.getList();
        if (CollectionUtils.isEmpty(statisticProjectDTOList)) {
            return sqlPageInfo;
        }

        Set<String> userIdSet = statisticProjectDTOList.stream().map(StatisticProjectDTO::getLeader)
            .collect(Collectors.toSet());
        //查询用户信息
        Map<String, UserDTO> userDTOMap = userFeign.getUserNameMapByIds(userIdSet);

        statisticProjectDTOList.forEach(statisticProjectDTO -> {
            String userId = statisticProjectDTO.getLeader();
            UserDTO userDTO = userDTOMap.get(userId);
            if (userDTO != null) {
                statisticProjectDTO.setLeaderFullName(userDTO.getFullName());
                statisticProjectDTO.setLeaderUserName(userDTO.getLoginName());
            }

            // 课程完成率转化百分比
            statisticProjectDTO.setCourseTaskCompleteRatePercent(
                statisticProjectDTO.getCourseTaskCompleteRate().doubleValue() * 100 + "%");
        });

        return sqlPageInfo;
    }

    @Override
    public void exportProjectStatistic(StatisticProjectQuery statisticProjectQuery) {
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IProjectStatisticService, StatisticProjectDTO>(
            statisticProjectQuery) {

            @Override
            protected IProjectStatisticService getBean() {
                return SpringUtil.getBean(ProjectConstant.PROJECT_STATISTIC_SERVICE, IProjectStatisticService.class);
            }

            @Override
            protected PageInfo<StatisticProjectDTO> getPageInfo() {
                return getBean().findProjectStatisticByPage(statisticProjectQuery);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.ProjectStatistic;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.ProjectStatistic.getType();
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }


    @Override
    public void makeProjectStatistic() {

        //查询培训项目引用的班级，已经结束的班级
        long startTime = System.currentTimeMillis();
        log.info("------makeProjectStatistic----");
        for (int i = 0; i < MAX_SIZE; i++) {
            List<Statistic> statisticList = statisticMapper.selectProjectListForStatistic(i * PAGE_SIZE, PAGE_SIZE);
            if (CollectionUtils.isEmpty(statisticList)) {
                break;
            }

            //每次都统计一遍吧，因为也不确定，哪些需要重新统计
            Set<String> projectIdSet = statisticList.stream().map(Statistic::getProjectId).collect(Collectors.toSet());

            //查询签到相关的数据
            Map<String, SignNumDTO> signNumDTOMap = signFeign.getSignUserNumInfoByProjectId(projectIdSet);

            //查询课程任务数
            List<Statistic> courseStatisticList = statisticMapper.selectProjectCourseTaskNum(projectIdSet);
            Map<String, Integer> courseTaskNumMap = courseStatisticList.stream()
                .collect(Collectors.toMap(Statistic::getProjectId, Statistic::getCourseTaskNum, (key1, key2) -> key1));

            //查询课程任务完成率
            List<ProjectStatisticSimpleDTO> projectStatisticSimpleDTOList = statisticMapper.selectProjectCourseTaskCompleteNum(
                projectIdSet);
            Map<String, Integer> courseTaskFinishNumMap = projectStatisticSimpleDTOList.stream().collect(
                Collectors.toMap(ProjectStatisticSimpleDTO::getProjectId, ProjectStatisticSimpleDTO::getTaskFinishNum,
                    (key1, key2) -> key1));

            //讲师评估分平均分 班级评估分平均分
            List<ProjectEvalAvgScore> projectEvalAvgScoreList = evaluationFeign.findProjectEvalAvgScore(projectIdSet);
            Map<String, ProjectEvalAvgScore> projectEvalAvgScoreMap = projectEvalAvgScoreList.stream()
                .collect(Collectors.toMap(ProjectEvalAvgScore::getProjectId, obj -> obj, (key1, key2) -> key1));

            //组装插入数据
            for (Statistic statistic : statisticList) {
                handle(signNumDTOMap, courseTaskNumMap, courseTaskFinishNumMap, projectEvalAvgScoreMap, statistic);
            }

            //插入更新数据
            statisticService.saveOrUpdateBatch(statisticList);

        }

        log.info("------makeProjectStatistic----end : " + (System.currentTimeMillis() - startTime));
    }

    private void handle(Map<String, SignNumDTO> signNumDTOMap, Map<String, Integer> courseTaskNumMap,
        Map<String, Integer> courseTaskFinishNumMap, Map<String, ProjectEvalAvgScore> projectEvalAvgScoreMap,
        Statistic statistic) {
        String projectId = statistic.getProjectId();

        Statistic dbStatistic = statisticService.lambdaQuery().eq(Statistic::getProjectId, projectId).one();
        if (dbStatistic == null) {
            statistic.setId(newId());
        } else {
            statistic.setId(dbStatistic.getId());
        }

        //组装数据
        SignNumDTO signNumDTO = signNumDTOMap.get(projectId);
        statistic.setSignNum(
            signNumDTO != null && signNumDTO.getSignUserNum() != null ? signNumDTO.getSignUserNum() : 0);
        statistic.setSignCompleteNum(
            signNumDTO != null && signNumDTO.getUserAllSignNum() != null ? signNumDTO.getUserAllSignNum() : 0);

        //课程任务数
        statistic.setCourseTaskNum(courseTaskNumMap.get(projectId));

        //课程完成率  课程完成数/(总人数*课程任务数)
        Integer courseTaskFinishNum = courseTaskFinishNumMap.get(projectId);
        Integer peopleNum = statistic.getPeopleNum();
        Integer courseTaskNum = statistic.getCourseTaskNum();
        if ((courseTaskFinishNum != null && courseTaskFinishNum != 0) && (peopleNum != null && peopleNum != 0)
            && (courseTaskNum != null && courseTaskNum != 0)) {
            statistic.setCourseTaskCompleteRate(
                NumberOperationUtils.divide(courseTaskFinishNum, peopleNum * courseTaskNum, 4));
        }

        //讲师评估平均分
        ProjectEvalAvgScore evalAvgScore = projectEvalAvgScoreMap.get(projectId);
        statistic.setLecturerSatisfaction(
            evalAvgScore != null ? evalAvgScore.getLecturerAvgScore() : new BigDecimal(0));
        statistic.setProjectSatisfaction(
            evalAvgScore != null ? evalAvgScore.getProjectAvgScore() : new BigDecimal(0));
    }

    @Override
    public PageInfo<StatisticLecturerEvaluationDTO> findLecturerEvaluationStatisticByPage(
        StatisticLecturerEvaluationQuery statisticLecturerEvaluationQuery) {

        String trainId = statisticLecturerEvaluationQuery.getTrainId();
        //根据培训项目ID 查询培训项目下面的学习项目
        List<Project> projectList = projectService.lambdaQuery().eq(Project::getReferencedId, trainId).list();

        Set<String> projectIdList = projectList.stream().map(Project::getId).collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(projectIdList)) {
            return new PageInfo<>();
        }

        //查询出所有的讲师授课名称，数量比较大的情况下，可能有性能问题
        List<String> examinationIds = lecturerExaminationFeign.getIdsListByProjectIdList(projectIdList);

        if (CollectionUtils.isEmpty(examinationIds)) {
            return new PageInfo<>();
        }

        //去查询评估列表
        EvaluationProjectQuery query = new EvaluationProjectQuery();

        //管辖范围
        query.setCurrentUserId(UserThreadContext.getUserId());
        query.setCurrentOrgId(UserThreadContext.getOrgId());
        query.setManagerAreaOrgIds(orgFeign.findUserManageAreaLevelPath(UserThreadContext.getUserId()));

        //讲师授课
        query.setExaminationIds(examinationIds);

        PageInfo<EvaluationListDTO> pageInfo = evaluationFeign.listPageProject(query);

        List<EvaluationListDTO> evaluationList = pageInfo.getList();
        if (CollectionUtils.isEmpty(evaluationList)) {
            return new PageInfo<>();
        }

        //组装数据
        PageInfo<StatisticLecturerEvaluationDTO> resultPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, resultPageInfo);

        // 需要查询讲师名称的明细id
        List<String> detailIds = evaluationList.stream()
            .filter(dto -> Objects.equals(dto.getEvaluationType(), EvaluationTypeEnum.LECTURER_EXAMINATION.getValue()))
            .map(EvaluationListDTO::getEvaluationObject).collect(Collectors.toList());

        Map<String, LecturerExaminationDetailDTO> lecturerExaminationMap = lecturerExaminationFeign.getLecturerExaminationMapByIds(
            detailIds);

        //查询班级名称，课程名称，讲师信息
        Map<String, String> cwIdCourseNameMap = new HashMap<>();
        Map<String, UserDTO> userNameMap = new HashMap<>();
        Map<String, String> projectNameMap = new HashMap<>();
        queryProjectName(lecturerExaminationMap, cwIdCourseNameMap, userNameMap, projectNameMap);

        //组装数据
        List<StatisticLecturerEvaluationDTO> statisticLecturerEvaluationDTOList = new ArrayList<>();
        Map<String, String> finalProjectNameMap = projectNameMap;
        Map<String, UserDTO> finalUserNameMap = userNameMap;
        evaluationList.forEach(evaluationListDTO -> {
            String id = evaluationListDTO.getEvaluationObject();
            LecturerExaminationDetailDTO examinationDetailDTO = lecturerExaminationMap.get(id);
            if (examinationDetailDTO != null) {
                StatisticLecturerEvaluationDTO dto = new StatisticLecturerEvaluationDTO();
                dto.setProjectName(finalProjectNameMap.get(examinationDetailDTO.getProjectId()));
                dto.setCourseName(cwIdCourseNameMap.get(examinationDetailDTO.getCwId()));
                dto.setStartTime(examinationDetailDTO.getStartTime());
                dto.setEndTime(examinationDetailDTO.getEndTime());
                UserDTO userDTO = finalUserNameMap.get(examinationDetailDTO.getLecturerUserId());
                if (userDTO != null) {
                    dto.setLecturerFullName(userDTO.getFullName());
                    dto.setLecturerLoginName(userDTO.getLoginName());
                }
                dto.setLecturerNumber(examinationDetailDTO.getLecturerNumber());
                dto.setAvgScore(evaluationListDTO.getAvgScore());

                statisticLecturerEvaluationDTOList.add(dto);
            }
        });

        resultPageInfo.setList(statisticLecturerEvaluationDTOList);
        return resultPageInfo;
    }


    @Override
    public void exportLecturerEvaluationStatistic(StatisticLecturerEvaluationQuery statisticLecturerEvaluationQuery) {
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IProjectStatisticService, StatisticLecturerEvaluationDTO>(
            statisticLecturerEvaluationQuery) {

            @Override
            protected IProjectStatisticService getBean() {
                return SpringUtil.getBean(ProjectConstant.PROJECT_STATISTIC_SERVICE, IProjectStatisticService.class);
            }

            @Override
            protected PageInfo<StatisticLecturerEvaluationDTO> getPageInfo() {
                return getBean().findLecturerEvaluationStatisticByPage(statisticLecturerEvaluationQuery);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.ProjectStatisticLecturer;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.ProjectStatisticLecturer.getType();
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }

    /**
     * 查询班级，课程，讲师名称
     *
     * @param lecturerExaminationMap
     * @param cwIdCourseNameMap
     * @param userNameMap
     * @param projectNameMap
     */
    private void queryProjectName(Map<String, LecturerExaminationDetailDTO> lecturerExaminationMap,
        Map<String, String> cwIdCourseNameMap, Map<String, UserDTO> userNameMap, Map<String, String> projectNameMap) {
        Set<String> cwIdList = new HashSet<>();
        Set<String> userIdList = new HashSet<>();
        Set<String> proIdList = new HashSet<>();

        for (Map.Entry<String, LecturerExaminationDetailDTO> entry : lecturerExaminationMap.entrySet()) {
            LecturerExaminationDetailDTO dto = lecturerExaminationMap.get(entry.getKey());

            String cwId = dto.getCwId();
            if (!StringUtil.isEmpty(cwId)) {
                cwIdList.add(cwId);
            }

            String userId = dto.getLecturerUserId();
            if (!StringUtil.isEmpty(userId)) {
                userIdList.add(userId);
            }

            String proId = dto.getProjectId();
            if (!StringUtil.isEmpty(proId)) {
                proIdList.add(proId);
            }
        }

        //根据课件查询课程名称
        List<CourseWareNameDetailDTO> courseWareNameDetailDTOList = courseWareFeign.getCourseWareNameDetailDTOByIds(
            cwIdList);
        Map<String, String> tempCwIdCourseNameMap = courseWareNameDetailDTOList.stream()
            .collect(Collectors.toMap(CourseWareNameDetailDTO::getId, CourseWareNameDetailDTO::getCourseName));

        cwIdCourseNameMap.putAll(tempCwIdCourseNameMap);

        //根据用户ID查询用户名称
        Map<String, UserDTO> tempUserNameMap = new HashMap<>(userIdList.size());
        if (!CollectionUtils.isEmpty(userIdList)) {
            tempUserNameMap = userFeign.getUserNameMapByIds(userIdList);
        }

        userNameMap.putAll(tempUserNameMap);

        //根据学习项目ID 查询学习项目名称
        Map<String, String> tempProjectNameMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(proIdList)) {
            List<Project> projectList1 = projectService.lambdaQuery().in(Project::getId, proIdList).list();
            tempProjectNameMap = projectList1.stream().collect(Collectors.toMap(Project::getId, Project::getProName));
        }

        projectNameMap.putAll(tempProjectNameMap);
    }

    @Override
    public PageInfo<Map<String, Object>> findProjectOrgByPage(StatisticProjectOrgQuery statisticProjectOrgQuery) {
        //先通过学习项目，拿到评估列表ID
        PageInfo<StatisticProjectOrgDTO> sqlPageInfo = PageMethod
            .startPage(statisticProjectOrgQuery.getPageNo(), statisticProjectOrgQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.selectEvaIdListByTrainId(statisticProjectOrgQuery));

        List<StatisticProjectOrgDTO> statisticProjectOrgDTOList = sqlPageInfo.getList();
        if (CollectionUtils.isEmpty(statisticProjectOrgDTOList)) {
            return new PageInfo<>();
        }

        //然后根据评估ID 去批量查询各个评估的平均分和各个项的得分
        Set<String> evalIdSet = statisticProjectOrgDTOList.stream().map(StatisticProjectOrgDTO::getEvaId)
            .collect(Collectors.toSet());

        List<ProjectSimpleEvalOrgDTO> projectSimpleEvalOrgDTOList = evaluationFeign.findEvalAvgScoreAndQuestionItemAvgScore(
            evalIdSet);

        //组装数据
        Map<String, List<ProjectSimpleEvalOrgDTO>> evalOrgMap = projectSimpleEvalOrgDTOList.stream()
            .collect(Collectors.groupingBy(ProjectSimpleEvalOrgDTO::getEvalId,
                Collectors.toList()));

        PageInfo<Map<String, Object>> page = new PageInfo<>();
        BeanUtils.copyProperties(sqlPageInfo, page);

        List<Map<String, Object>> data = new ArrayList<>();

        for (StatisticProjectOrgDTO dto : statisticProjectOrgDTOList) {
            String evaId = dto.getEvaId();
            List<ProjectSimpleEvalOrgDTO> simpleEvalOrgDTOList = evalOrgMap.get(evaId);
            Map<String, Object> map = new HashMap<>();
            map.put(ProjectConstant.PROJECT_NAME, dto.getProjectName());
            map.put(ProjectConstant.START_TIME, dto.getStartTime());
            map.put(ProjectConstant.END_TIME, dto.getEndTime());

            if (!CollectionUtils.isEmpty(simpleEvalOrgDTOList)) {
                Map<String, Float> eveUnitMap = new HashMap<>();
                float sumScore = 0.0f;
                for (ProjectSimpleEvalOrgDTO simpleEvalOrgDTO : simpleEvalOrgDTOList) {
                    sumScore += simpleEvalOrgDTO.getItemAvgScore();
                    eveUnitMap.put(simpleEvalOrgDTO.getQuestionItemId(), simpleEvalOrgDTO.getItemAvgScore());
                }
                map.putAll(eveUnitMap);
                BigDecimal avgScore = new BigDecimal(String.valueOf(sumScore))
                    .divide(new BigDecimal(simpleEvalOrgDTOList.size()), 2,
                        RoundingMode.HALF_UP);
                dto.setAvgScore(avgScore.floatValue());
                map.put(ProjectConstant.AVG_SCORE, avgScore);
            }
            data.add(map);
        }

        page.setList(data);
        return page;
    }

    @Override
    public List<EvalDTO> findProjectEvaluation(StatisticProjectOrgQuery statisticProjectOrgQuery) {
        //根据培训ID查询 查询一个学习项目中的所有评估id
        List<EvalDTO> result = new ArrayList<>();
        Set<String> evaluationIds = baseMapper.getAllEvaIdsByTrainId(statisticProjectOrgQuery);
        if (CollectionUtils.isEmpty(evaluationIds)) {
            return result;
        }
        List<EvalDTO> evalDTOList = evaluationFeign.getEvalInfoListByIdList(
            evaluationIds);
        if (CollectionUtils.isEmpty(evalDTOList)) {
            return result;
        }
        result.addAll(evalDTOList);
        return result;
    }

    @Override
    public List<HeaderUnitDTO> findProjectOrgHeader(StatisticProjectOrgQuery statisticProjectOrgQuery) {
        //根据培训ID查询 查询一个学习项目中的评估，根据约定，这些学习项目评估都使用同一个评估模板
        String evaluationId = statisticProjectOrgQuery.getEvaluationId();

        if (StringUtil.isEmpty(evaluationId)) {
            return new ArrayList<>();
        }

        List<EvalQuestionFeignDTO> evalQuestionFeignDTOList = evaluationQuestionFeign.getEvalQuestionDTOList(
            evaluationId);

        if (CollectionUtils.isEmpty(evalQuestionFeignDTOList)) {
            return new ArrayList<>();
        }

        //组装数据
        List<HeaderUnitDTO> headerUnitDTOList = new ArrayList<>();

        for (EvalQuestionFeignDTO evalQuestionFeignDTO : evalQuestionFeignDTOList) {
            if (evalQuestionFeignDTO.getQuestionType() != null
                && QuestionType.GRADE.getKey() == evalQuestionFeignDTO.getQuestionType()) {
                HeaderUnitDTO headerUnitDTO = new HeaderUnitDTO();
                headerUnitDTO.setProp(evalQuestionFeignDTO.getId());
                headerUnitDTO.setLabel(evalQuestionFeignDTO.getQuestionName());

                List<EvalQuestionItemFeignDTO> questionOptions = evalQuestionFeignDTO.getQuestionOptions();
                if (!CollectionUtils.isEmpty(questionOptions)) {
                    List<HeaderUnitDTO> childHeaderUnitDTOList = new ArrayList<>();
                    for (EvalQuestionItemFeignDTO itemFeignDTO : questionOptions) {
                        HeaderUnitDTO dto = new HeaderUnitDTO();
                        dto.setProp(itemFeignDTO.getId());
                        dto.setLabel(itemFeignDTO.getItemName());
                        childHeaderUnitDTOList.add(dto);
                    }
                    headerUnitDTO.setChildHeaderUnitDTOList(childHeaderUnitDTOList);
                }

                headerUnitDTOList.add(headerUnitDTO);
            }
        }

        return headerUnitDTOList;
    }

    @Override
    public void exportProjectOrgStatistic(StatisticProjectOrgQuery statisticProjectOrgQuery) {

        log.info("export exportProjectOrgStatistic:" + JsonUtil.objToJson(statisticProjectOrgQuery));

        IExportNoEntityDataDTO exportDataDTO = new AbstractExportNoEntityDataDTO<IProjectStatisticService, Map<String, Object>>(
            statisticProjectOrgQuery) {
            @Override
            protected IProjectStatisticService getBean() {
                return SpringUtil.getBean(ProjectConstant.PROJECT_STATISTIC_SERVICE, IProjectStatisticService.class);
            }

            @Override
            protected List<List<Object>> getPageInfo() {
                PageInfo<Map<String, Object>> projectOrgMapPageInfo = findProjectOrgByPage(statisticProjectOrgQuery);

                //获取动态表头
                List<HeaderUnitDTO> headerUnitDTOList = findProjectOrgHeader(statisticProjectOrgQuery);
                List<String> propIdList = new ArrayList<>();
                for (HeaderUnitDTO dto : headerUnitDTOList) {
                    List<HeaderUnitDTO> childHeaderUnitDTOList = dto.getChildHeaderUnitDTOList();
                    for (HeaderUnitDTO child : childHeaderUnitDTOList) {
                        propIdList.add(child.getProp());
                    }
                }

                List<List<Object>> result = new ArrayList<>();
                List<Map<String, Object>> projectOrgMapList = projectOrgMapPageInfo.getList();
                if (!CollectionUtils.isEmpty(projectOrgMapList)) {
                    addResult(propIdList, result, projectOrgMapList);
                }
                return result;
            }

            @Override
            public Integer getCustomerHandler() {
                return 4;
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.ProjectOrgStatistic;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.ProjectOrgStatistic.getType();

            }
        };
        List<List<String>> header = getExportHeader(statisticProjectOrgQuery);
        exportComponent.exportNoEntityRecord(exportDataDTO, header);
    }

    private void addResult(List<String> propIdList, List<List<Object>> result,
        List<Map<String, Object>> projectOrgMapList) {
        for (Map<String, Object> map : projectOrgMapList) {
            List<Object> rowData = new ArrayList<>();

            rowData.add(Objects.nonNull(map.get(ProjectConstant.PROJECT_NAME)) ? map.get(
                ProjectConstant.PROJECT_NAME) : "");
            rowData.add(Objects.nonNull(map.get(ProjectConstant.START_TIME)) ? DateUtil.formatToStr(
                (Date) map.get(ProjectConstant.START_TIME), DateUtil.YYMMDD_HHMMSS) : "");
            rowData.add(Objects.nonNull(map.get(ProjectConstant.END_TIME)) ? DateUtil.formatToStr(
                (Date) map.get(ProjectConstant.END_TIME), DateUtil.YYMMDD_HHMMSS) : "");
            rowData.add(
                Objects.nonNull(map.get(ProjectConstant.AVG_SCORE)) ? map.get(ProjectConstant.AVG_SCORE)
                    : "");

            //动态表头的数据，根据ID匹配
            for (String propId : propIdList) {
                rowData.add(Objects.nonNull(map.get(propId)) ? map.get(propId) : "");
            }

            result.add(rowData);
        }
    }


    /**
     * 获取表头
     *
     * @param statisticProjectOrgQuery
     * @return
     */
    public List<List<String>> getExportHeader(StatisticProjectOrgQuery statisticProjectOrgQuery) {

        List<HeaderUnitDTO> headerUnitDTOList = findProjectOrgHeader(statisticProjectOrgQuery);

        //组装表头
        List<List<String>> headerList = new ArrayList<>();
        List<String> head0 = new ArrayList<>();
        head0.add("班级名称");
        head0.add("班级名称");

        List<String> head1 = new ArrayList<>();
        head1.add("开始时间");
        head1.add("开始时间");

        List<String> head2 = new ArrayList<>();
        head2.add("结束时间");
        head2.add("结束时间");

        List<String> head3 = new ArrayList<>();
        head3.add("综合分");
        head3.add("综合分");

        headerList.add(head0);
        headerList.add(head1);
        headerList.add(head2);
        headerList.add(head3);

        for (HeaderUnitDTO headerUnitDTO : headerUnitDTOList) {
            String label = headerUnitDTO.getLabel();
            List<HeaderUnitDTO> childHeaderUnitDTOList = headerUnitDTO.getChildHeaderUnitDTOList();
            for (HeaderUnitDTO childDto : childHeaderUnitDTOList) {
                List<String> head = new ArrayList<>();
                head.add(label);
                head.add(childDto.getLabel());
                headerList.add(head);
            }
        }
        return headerList;
    }

    @Override
    public List<StatisticTaskTypeDTO> findStatisticTaskTypeList(String projectId) {
        List<Task> taskList = taskService.lambdaQuery().select(Task::getTaskType).eq(Task::getProId, projectId)
            .and(q -> q.ne(Task::getTaskContent, "").or().likeRight(Task::getTaskType, "20")).list();

        List<StatisticTaskTypeDTO> result = new ArrayList<>();
        Set<String> taskTypeList = taskList.stream().map(Task::getTaskType).collect(Collectors.toSet());
        for (String type : taskTypeList) {
            StatisticTaskTypeDTO t = new StatisticTaskTypeDTO();
            t.setTaskType(type);
            if (ProjectTaskTypeEnum.isForm(type)) {
                Optional.ofNullable(categorysService.getById(type))
                    .ifPresent(category -> t.setTaskTypeName(category.getCategoryName()));
            } else {
                t.setTaskTypeName(ProjectTaskTypeEnum.getTaskName(type));
            }
            result.add(t);
        }
        return result;
    }

    @Override
    public List<StatisticSimpleTaskDTO> findStatisticTaskList(String projectId, String taskType) {
        List<Task> taskList = null;
        if (StringUtil.isEmpty(taskType)) {
            taskList = taskService.lambdaQuery()
                .select(Task::getId, Task::getTaskName, Task::getTaskType, Task::getTaskContent)
                .eq(Task::getProId, projectId)
                .list();
        } else {
            taskList = taskService.lambdaQuery()
                .select(Task::getId, Task::getTaskName, Task::getTaskType, Task::getTaskContent)
                .eq(Task::getProId, projectId)
                .eq(Task::getTaskType, taskType).list();
        }

        return taskList.stream().map(task -> {
            // 创建任务,但没有关联资源的数据过滤掉,保持和列表数据一致
            if (task.getTaskType().length() < 36 && StringUtils.isEmpty(task.getTaskContent())) {
                return null;
            }
            StatisticSimpleTaskDTO statisticSimpleTaskDTO = new StatisticSimpleTaskDTO();
            statisticSimpleTaskDTO.setTaskId(task.getId());
            statisticSimpleTaskDTO.setTaskName(task.getTaskName());
            return statisticSimpleTaskDTO;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public List<StatisticTeamDTO> findProjectTeamList(String projectId) {
        List<Team> teamList = teamService.findTeamListByProjectId(projectId);

        return teamList.stream().map(team -> {
            StatisticTeamDTO statisticTeamDTO = new StatisticTeamDTO();
            statisticTeamDTO.setTeamId(team.getId());
            statisticTeamDTO.setTeamName(team.getTeamName());
            return statisticTeamDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public PageInfo<StatisticTaskDetailDTO> findTaskDetailByPage(StatisticTaskDetailQuery statisticTaskDetailQuery) {
        Project project = projectService.getById(statisticTaskDetailQuery.getProjectId());
        statisticTaskDetailQuery.setLeader(project.getLeader());
        statisticTaskDetailQuery.setLeaderDontStat(project.getLeaderDontStat());

        Boolean showSource = showSource();
        PageInfo<StatisticTaskDetailDTO> sqlPageInfo = PageMethod.startPage(statisticTaskDetailQuery.getPageNo(),
                statisticTaskDetailQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.selectTaskDetailByPage(statisticTaskDetailQuery));

        //查询姓名 用户名 部门 岗位
        List<StatisticTaskDetailDTO> statisticTaskDetailDTOList = sqlPageInfo.getList();

        List<String> userIdList = new ArrayList<>();

        List<CourseDurationQuery> courseDurationQueryList = new ArrayList<>();
        List<ExamScoreQuery> examScoreQueryList = new ArrayList<>();
        for (StatisticTaskDetailDTO dto : statisticTaskDetailDTOList) {
            String userId = dto.getUserId();
            userIdList.add(userId);
            String taskType = dto.getTaskType();
            Integer taskScore = dto.getTaskScore();
            dto.setShowSource(showSource);
            // 设置来源
            dto.setSourceName(HomeWorkSourceEnum.getByName(dto.getSource()));
            if (taskScore != null && taskScore.intValue() == -1) {
                dto.setTaskScore(null);
            }

            if ("course".equals(taskType)) {
                CourseDurationQuery query = new CourseDurationQuery();
                query.setUserId(userId);
                query.setCourseId(dto.getTaskContent());
                courseDurationQueryList.add(query);
            } else if ("exam".equals(taskType)) {
                ExamScoreQuery query = new ExamScoreQuery();
                query.setUserId(userId);
                query.setExamId(dto.getTaskContent());
                examScoreQueryList.add(query);
            }
        }
        //查询用户信息
        Map<String, UserDTO> userDTOMap = userFeign.getUserNameMapByIds(userIdList);
        // 查询部门简称
        Map<String, OrgShowDTO> orgShowDTOMap = orgFeign.getOrgShowDTO(
            userDTOMap.values().stream().map(UserDTO::getOrgId).collect(Collectors.toSet()));
        //查询课时
        Map<String, Long> map = handleCourseDuration(courseDurationQueryList);
        //查询考试分数
        Map<String, BigDecimal> scoreMap = handleExamScore(examScoreQueryList);

        //组装数据
        for (StatisticTaskDetailDTO taskDetailDTO : statisticTaskDetailDTOList) {
            handleDTO(userDTOMap, orgShowDTOMap, map, scoreMap, taskDetailDTO);
        }
        return sqlPageInfo;
    }

    private void handleDTO(Map<String, UserDTO> userDTOMap, Map<String, OrgShowDTO> orgShowDTOMap,
        Map<String, Long> map, Map<String, BigDecimal> scoreMap, StatisticTaskDetailDTO taskDetailDTO) {
        String userId = taskDetailDTO.getUserId();
        String taskType = taskDetailDTO.getTaskType();
        if (!StringUtil.isEmpty(userId)) {
            UserDTO userDTO = userDTOMap.get(userId);
            if (userDTO != null) {
                taskDetailDTO.setFullName(userDTO.getFullName());
                taskDetailDTO.setLoginName(userDTO.getLoginName());
                taskDetailDTO.setPostName(userDTO.getPostName());
                Optional.ofNullable(orgShowDTOMap.get(userDTO.getOrgId())).ifPresent(orgShowDTO -> {
                    taskDetailDTO.setOrgName(orgShowDTO.getOrgShortName());
                    taskDetailDTO.setOrgPath(orgShowDTO.getLevelPathName());
                });
            }
        }
        taskDetailDTO.setCostTime(0L);
        if ("course".equals(taskType)) {
            String taskContent = taskDetailDTO.getTaskContent();
            String key = userId + taskContent;
            Long costTime = map.get(key);
            if (costTime != null) {
                taskDetailDTO.setCostTime(map.get(key));
            }
        }

        if ("exam".equals(taskType)) {
            String taskContent = taskDetailDTO.getTaskContent();
            String key = userId + taskContent;
            BigDecimal score = scoreMap.get(key);
            // 考试中暂未交卷的 分数查询时为 -1，未交卷统计时是暂无分数的 数据处理为 null
            if (Objects.nonNull(score)) {
                taskDetailDTO.setUserScore(score.compareTo(new BigDecimal(-1)) != 0 ? score.floatValue() : null);
            }
        }
    }

    /**
     * 处理课时
     *
     * @param courseDurationQueryList
     * @return
     */
    private Map<String, Long> handleCourseDuration(List<CourseDurationQuery> courseDurationQueryList) {
        //查询课时
        List<CourseDurationDTO> courseDurationDTOList = courseRecordFeign.findUserCourseRecord(courseDurationQueryList);

        Map<String, Long> map = new HashMap<>();
        if (!CollectionUtils.isEmpty(courseDurationDTOList)) {
            for (CourseDurationDTO dto : courseDurationDTOList) {
                String userId = dto.getUserId();
                String courseId = dto.getCourseId();
                String key = userId + courseId;
                Long duration = dto.getDuration();
                map.put(key, duration);
            }
        }

        return map;
    }

    /**
     * 处理考试分数
     *
     * @param examScoreQueryList
     * @return
     */
    private Map<String, BigDecimal> handleExamScore(List<ExamScoreQuery> examScoreQueryList) {
        List<ExamScoreDTO> examScoreDTOList = examFeign.findUserExamScoreListFilterNotCheck(examScoreQueryList);

        Map<String, BigDecimal> scoreMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(examScoreDTOList)) {
            for (ExamScoreDTO examScoreDTO : examScoreDTOList) {
                String userId = examScoreDTO.getUserId();
                String examId = examScoreDTO.getExamId();
                String key = userId + examId;
                BigDecimal userScore = examScoreDTO.getUserScore();
                scoreMap.put(key, userScore);
            }
        }

        return scoreMap;
    }

    @Override
    @SuppressWarnings("java:S3776")
    public StatisticalInfoDTO getProjectStatisticalInfo(String projectId) {

        StatisticalInfoDTO statisticalInfoDTO = new StatisticalInfoDTO();

        String userId = UserThreadContext.getUserId();

        //获取我的完成进度
        Progress progress = progressService.getOne(
            new LambdaQueryWrapper<Progress>().eq(Progress::getProId, projectId).eq(Progress::getUserId, userId));
        String taskCompletion = "0";
        if (progress != null) {
            taskCompletion = progress.getProgressPercent().toString();
        }
        statisticalInfoDTO.setProgress(taskCompletion + "%");

        List<SimpleRankInfo> simpleRankInfoList = new ArrayList<>();
        //获取各个类别的简单排名情况
        List<String> flagList = ProjectStatisticalTypeEnum.getStatisticalTypeList();
        StatisticsPageQuery rankQuery = new StatisticsPageQuery();
        rankQuery.setProjectId(projectId);
        rankQuery.setRow("1");
        rankQuery.setUid(userId);
        for (String flag : flagList) {
            rankQuery.setFlag(flag);
            List<ProjectStatisticDTO> statisticalList = projectMapper.getProjectStatisticList(rankQuery);
            String first = null;
            String second = null;
            String third = null;
            Integer mine = null;
            for (ProjectStatisticDTO statistical : statisticalList) {
                if (statistical != null) {
                    if (statistical.getRank() == 1) {
                        first = statistical.getName();
                    }
                    if (statistical.getRank() == 2) {
                        second = statistical.getName();
                    }
                    if (statistical.getRank() == 3) {
                        third = statistical.getName();
                    }
                    if (ProjectStatisticalTypeEnum.PERSON.getStatisticalType().equals(flag)) {
                        if (userId.equals(statistical.getFlagId())) {
                            mine = statistical.getRank();
                        }
                    } else {
                        if (statistical.getId().equals(statistical.getFlagId())) {
                            mine = statistical.getRank();
                        }
                    }
                }
            }

            // 针对团队，如果我的排名为空，则不返回团队。（学习项目是否存在团队判断逻辑）
            if ("team".equals(flag) && mine == null) {
                continue;
            }

            SimpleRankInfo simpleRankInfo = new SimpleRankInfo();
            simpleRankInfo.setId(flag);
            simpleRankInfo.setTitle(ProjectStatisticalTypeEnum.getStatisticalName(flag));
            simpleRankInfo.setDesc("");
            simpleRankInfo.setFlag(flag);
            simpleRankInfo.setFirst(first);
            simpleRankInfo.setSecond(second);
            simpleRankInfo.setThird(third);
            simpleRankInfo.setMine(mine);
            simpleRankInfoList.add(simpleRankInfo);
        }

        statisticalInfoDTO.setSimpleRankInfoList(simpleRankInfoList);
        return statisticalInfoDTO;
    }

    @Override
    public PageInfo<StatisticsRankPageDTO> getStatisticsRankPage(StatisticsPageQuery query) {
        String userId = UserThreadContext.getUserId();
        query.setUid(userId);
        PageInfo<ProjectStatisticDTO> sqlPageInfo = PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> projectMapper.getProjectStatisticList(query));

        //组装数据
        PageInfo<StatisticsRankPageDTO> resultPage = new PageInfo<>();
        BeanUtils.copyProperties(sqlPageInfo, resultPage);
        List<StatisticsRankPageDTO> statisticsRankPageDTOList = new ArrayList<>();
        List<ProjectStatisticDTO> projectStatisticDTOList = sqlPageInfo.getList();
        String flag = query.getFlag();
        if (!CollectionUtils.isEmpty(projectStatisticDTOList)) {
            //
            Map<String, UserDTO> userDTOMap = new HashMap<>();
            if ("person".equals(flag)) {
                List<String> userIdList = projectStatisticDTOList.stream().map(ProjectStatisticDTO::getId)
                    .collect(Collectors.toList());
                userDTOMap = userFeign.getUserNameMapByIds(userIdList);
            }

            Map<String, OrgDTO> orgMap = orgFeign.getOrgMapByIds(
                projectStatisticDTOList.stream().map(ProjectStatisticDTO::getOrgId).collect(Collectors.toList()));
            for (ProjectStatisticDTO statisticDTO : projectStatisticDTOList) {
                StatisticsRankPageDTO rankPageDTO = new StatisticsRankPageDTO();
                String id = statisticDTO.getId();
                rankPageDTO.setId(id);
                rankPageDTO.setTitle(statisticDTO.getName());
                rankPageDTO.setDesc("");
                rankPageDTO.setImage("");
                OrgDTO orgDTO = orgMap.get(statisticDTO.getOrgId());
                setDep(rankPageDTO, orgDTO);
                rankPageDTO.setRank(statisticDTO.getRank());
                rankPageDTO.setScore(statisticDTO.getScore());
                if ("person".equals(flag)) {
                    UserDTO userDTO = userDTOMap.get(id);
                    if (userDTO != null) {
                        rankPageDTO.setImage(userDTO.getAvatar());
                        rankPageDTO.setTitle(userDTO.getFullName());
                    }
                }
                statisticsRankPageDTOList.add(rankPageDTO);
            }
        }
        resultPage.setList(statisticsRankPageDTOList);
        return resultPage;
    }

    private void setDep(StatisticsRankPageDTO rankPageDTO, OrgDTO orgDTO) {
        if (orgDTO != null) {
            rankPageDTO.setDep(orgDTO.getOrgName());
        } else {
            rankPageDTO.setDep("");
        }
    }

    @Override
    public StatisticMyRankInfo findStatisticMyRankInfo(StatisticsMyRankQuery query) {
        StatisticMyRankInfo statisticMyRankInfo = new StatisticMyRankInfo();
        String userId = UserThreadContext.getUserId();
        query.setUserId(userId);
        query.setUid(userId);
        StatisticsPageQuery statisticsPageQuery = new StatisticsPageQuery();
        BeanUtils.copyProperties(query, statisticsPageQuery);
        List<ProjectStatisticDTO> projectStatisticDTOList = projectMapper.getProjectStatisticList(statisticsPageQuery);
        UserDTO userDTO = userFeign.getUserById(userId);
        if (!CollectionUtils.isEmpty(projectStatisticDTOList)) {
            ProjectStatisticDTO projectStatisticDTO = projectStatisticDTOList.get(0);
            statisticMyRankInfo.setMyName(userDTO.getFullName());
            statisticMyRankInfo.setMyDep(projectStatisticDTO.getOrgName());
            statisticMyRankInfo.setMyRank(projectStatisticDTO.getRank());
            statisticMyRankInfo.setMyScore(projectStatisticDTO.getScore());
            statisticMyRankInfo.setMyImage(userDTO.getAvatar());
        }
        return statisticMyRankInfo;
    }

    @Override
    public PageInfo<StatisticPersonRankDTO> findPersonRankByPage(StatisticPersonRankQuery statisticPersonRankQuery) {
        Project project = projectService.getById(statisticPersonRankQuery.getProjectId());
        statisticPersonRankQuery.setLeader(project.getLeader());
        statisticPersonRankQuery.setLeaderDontStat(project.getLeaderDontStat());

        if (StringUtils.isNotBlank(statisticPersonRankQuery.getUserId())) {
            statisticPersonRankQuery.setUserIdList(
                TranslateUtil.translateBySplit(statisticPersonRankQuery.getUserId(), String.class));
        }
        PageInfo<StatisticPersonRankDTO> sqlPageInfo = PageMethod.startPage(statisticPersonRankQuery.getPageNo(),
                statisticPersonRankQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.selectPersonRankByPage(statisticPersonRankQuery));

        List<StatisticPersonRankDTO> statisticPersonRankDTOList = sqlPageInfo.getList();

        if (!CollectionUtils.isEmpty(statisticPersonRankDTOList)) {

            List<String> userIdList = statisticPersonRankDTOList.stream().map(StatisticPersonRankDTO::getUserId)
                .collect(Collectors.toList());
            Map<String, UserDTO> userDTOMap = userFeign.getUserNameMapByIds(userIdList);
            Map<String, OrgShowDTO> orgShowDTOMap = orgFeign.getOrgShowDTO(userFeign.getUserOrdIdByUserIds(userIdList));
            for (StatisticPersonRankDTO personRankDTO : statisticPersonRankDTOList) {
                String userId = personRankDTO.getUserId();
                UserDTO userDTO = userDTOMap.get(userId);
                if (userDTO != null) {
                    personRankDTO.setFullName(userDTO.getFullName());
                    Optional.ofNullable(orgShowDTOMap.get(userDTO.getOrgId())).ifPresent(orgShowDTO -> {
                        personRankDTO.setOrgName(orgShowDTO.getOrgShortName());
                        personRankDTO.setOrgPath(orgShowDTO.getLevelPathName());
                    });
                }
            }

        }

        return sqlPageInfo;
    }

    @Override
    public PageInfo<StatisticTeamLearnRankDTO> findTeamLearnRankByPage(
        StatisticTeamLearnRankQuery statisticTeamLearnRankQuery) {
        Project project = projectService.getById(statisticTeamLearnRankQuery.getProjectId());
        statisticTeamLearnRankQuery.setLeader(project.getLeader());
        statisticTeamLearnRankQuery.setLeaderDontStat(project.getLeaderDontStat());

        return PageMethod.startPage(statisticTeamLearnRankQuery.getPageNo(), statisticTeamLearnRankQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.selectTeamLearnRankByPage(statisticTeamLearnRankQuery));
    }

    @Override
    public PageInfo<StatisticOrgLearnRankDTO> findOrgLearnRankByPage(
        StatisticOrgLearnRankQuery statisticOrgLearnRankQuery) {
        Project project = projectService.getById(statisticOrgLearnRankQuery.getProjectId());
        statisticOrgLearnRankQuery.setLeader(project.getLeader());
        statisticOrgLearnRankQuery.setLeaderDontStat(project.getLeaderDontStat());

        PageInfo<StatisticOrgLearnRankDTO> pageInfo = PageMethod.startPage(statisticOrgLearnRankQuery.getPageNo(),
                statisticOrgLearnRankQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.selectOrgLearnRankByPage(statisticOrgLearnRankQuery));
        if (!CollectionUtils.isEmpty(pageInfo.getList())) {
            List<StatisticOrgLearnRankDTO> list = pageInfo.getList();
            Set<String> orgIds = list.stream().map(StatisticOrgLearnRankDTO::getOrgId).collect(Collectors.toSet());
            Map<String, OrgShowDTO> orgShowDTOMap = orgFeign.getOrgShowDTO(orgIds);
            list.forEach(rankDTO -> Optional.ofNullable(orgShowDTOMap.get(rankDTO.getOrgId())).ifPresent(orgShowDTO -> {
                rankDTO.setOrgName(orgShowDTO.getOrgShortName());
                rankDTO.setOrgPath(orgShowDTO.getLevelPathName());
            }));
        }
        return pageInfo;
    }

    @Override
    public void exportTaskDetailData(StatisticTaskDetailQuery queryDTO) {
        Integer projectType = queryDTO.getProjectType();
        // 学习任务数据是否显示数据导入标记
        Boolean finalShowSource = showSource();
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IProjectStatisticService, StatisticTaskDetailDTO>(
            queryDTO) {

            @Override
            protected IProjectStatisticService getBean() {
                return SpringUtil.getBean(ProjectConstant.PROJECT_STATISTIC_SERVICE, IProjectStatisticService.class);
            }

            @Override
            protected PageInfo<StatisticTaskDetailDTO> getPageInfo() {
                return getBean().findTaskDetailByPage((StatisticTaskDetailQuery) pageQueryDTO);
            }

            @Override
            public ExportBizType getType() {
                return getExportBizType(projectType, finalShowSource);
            }

            @Override
            public String getFileName() {
                if (Objects.nonNull(projectType) && projectType == 1) {
                    // 周期项目
                    return ExportFileNameEnum.PROJECT_FIXED_CYCLE_STATISTIC_TASK_DETAIL.getType();
                } else if (Objects.nonNull(projectType) && projectType == 2) {
                    // 快速培训
                    return ExportFileNameEnum.QuickProjectStatisticTaskDetail.getType();
                } else if (Objects.nonNull(projectType) && projectType == 3) {
                    // 课程学习任务
                    return ExportFileNameEnum.CourseTaskProjectStatisticTaskDetail.getType();
                } else if (Objects.nonNull(projectType) && projectType == 4) {
                    // 面授项目任务
                    return ExportFileNameEnum.FaceProjectStatisticTaskDetail.getType();
                } else {
                    return ExportFileNameEnum.ProjectStatisticTaskDetail.getType();
                }
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }

    @NotNull
    private ExportBizType getExportBizType(Integer projectType, Boolean finalShowSource) {
        if (Objects.nonNull(projectType) && projectType == 1) {
            return ExportBizType.ProjectFixedCycleStatisticTaskDetail;
        } else if (Objects.nonNull(projectType) && projectType == 2) {
            // 快速培训 导出没有 团队 列
            return ExportBizType.QuickProjectStatisticTaskDetail;
        } else if (Objects.nonNull(projectType) && projectType == 3) {
            // 课程学习任务 导出没有 团队、任务分 列
            return ExportBizType.CourseTaskProjectStatisticTaskDetail;
        } else if (Objects.nonNull(projectType) && projectType == 4) {
            // 面授项目 多了来源、说明
            if (Boolean.TRUE.equals(finalShowSource)) {
                return ExportBizType.ProjectStatisticTaskDetailWithSource;
            }
            return ExportBizType.FaceProjectStatisticTaskDetail;
        } else {
            return ExportBizType.ProjectStatisticTaskDetail;
        }
    }

    @Override
    public void exportPersonRankData(StatisticPersonRankQuery queryDTO) {
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IProjectStatisticService, StatisticPersonRankDTO>(
            queryDTO) {

            @Override
            protected IProjectStatisticService getBean() {
                return SpringUtil.getBean(ProjectConstant.PROJECT_STATISTIC_SERVICE, IProjectStatisticService.class);
            }

            @Override
            protected PageInfo<StatisticPersonRankDTO> getPageInfo() {
                return getBean().findPersonRankByPage((StatisticPersonRankQuery) pageQueryDTO);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.ProjectStatisticPersonRank;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.ProjectStatisticPersonRank.getType();
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public void exportTeamLearnRankData(StatisticTeamLearnRankQuery queryDTO) {
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IProjectStatisticService, StatisticTeamLearnRankDTO>(
            queryDTO) {

            @Override
            protected IProjectStatisticService getBean() {
                return SpringUtil.getBean(ProjectConstant.PROJECT_STATISTIC_SERVICE, IProjectStatisticService.class);
            }

            @Override
            protected PageInfo<StatisticTeamLearnRankDTO> getPageInfo() {
                return getBean().findTeamLearnRankByPage((StatisticTeamLearnRankQuery) pageQueryDTO);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.ProjectStatisticTeamLearnRank;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.ProjectStatisticTeamLearnRank.getType();
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public void exportOrgLearnRankData(StatisticOrgLearnRankQuery queryDTO) {
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IProjectStatisticService, StatisticOrgLearnRankDTO>(
            queryDTO) {

            @Override
            protected IProjectStatisticService getBean() {
                return SpringUtil.getBean(ProjectConstant.PROJECT_STATISTIC_SERVICE, IProjectStatisticService.class);
            }

            @Override
            protected PageInfo<StatisticOrgLearnRankDTO> getPageInfo() {
                return getBean().findOrgLearnRankByPage((StatisticOrgLearnRankQuery) pageQueryDTO);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.ProjectStatisticOrgLearnRank;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.ProjectStatisticOrgLearnRank.getType();
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }

    private Boolean showSource() {
        boolean showSource = false;
        String paraValue = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_711.getCode());
        if (Objects.equals(GeneralJudgeEnum.CONFIRM.getValue(), Integer.parseInt(paraValue))) {
            showSource = true;
        }
        return showSource;
    }


    @Override
    public PageInfo<ProjectJoinUserPageDTO> getProjectJoinUserData(ProjectJoinUserQuery query) {
        long startTime = System.currentTimeMillis();
        log.info(">>>>>>>>>>>>>>method>getProjectJoinUserData, params:{}", JsonUtil.objToJson(query));
        query.setCurrentUserId(UserThreadContext.getUserId());

        if (StringUtils.isNotBlank(query.getUserIds())) {
            query.setUserIdCollection(TranslateUtil.translateBySplit(query.getUserIds(), String.class));
        }
        if (StringUtils.isNotBlank(query.getOrgId())) {
            OrgDTO orgDTO = orgFeign.getById(query.getOrgId());
            query.setOrgLevelPath(orgDTO.getLevelPath());
        }
        query.setManageOrgLevelPaths(orgFeign.findUserManageAreaLevelPath(UserThreadContext.getUserId()));
        if (BY_ORG.equals(query.getBizType())) {
            //获取资源对应的下发用户所在的分区索引
            ResourceViewLimitBakDTO resourceViewLimitBak = resourceViewLimitBakService.getLimitUserPartition(
                query.getProjectId());
            String viewLimitUserPartition = resourceViewLimitBak.getTablePartition();
            Long viewLimitId = resourceViewLimitBak.getViewLimitId();
            if (Objects.isNull(viewLimitId)) {
                PageInfo<ProjectJoinUserPageDTO> pageInfo = new PageInfo<>();
                ArrayList<ProjectJoinUserPageDTO> list = new ArrayList<>();
                pageInfo.setList(list);
                return pageInfo;
            }
            query.setViewLimitUserPartition(viewLimitUserPartition);
            query.setViewLimitId(viewLimitId);
            //获取项目备份数据
            ProjectBak projectBak = projectBakService.getById(query.getProjectId());
            if (null == projectBak) {
                throw new BusinessException(ErrorNoEnum.ERR_RESOURCE_BAK_NOT_EXIST);
            }
            //项目的创建人
            query.setCreateBy(projectBak.getCreateBy());

            //获取用户学习项目记录分区索引
            String projectProgressPartition = TablePartitionEnum.getTablePartitionName(
                TablePartitionEnum.project_progress_bak.getTableName(), projectBak.getHashIndex());
            //用户学习项目任务进度记录分区索引
            String taskProgressPartition = TablePartitionEnum.getTablePartitionName(
                TablePartitionEnum.task_progress_bak.getTableName(), projectBak.getHashIndex());
            query.setProjectProgressPartition(projectProgressPartition);
            query.setTaskProgressPartition(taskProgressPartition);
        } else {
            Project project = Optional.ofNullable(projectService.getById(query.getProjectId())).orElseThrow(() ->
                new BusinessException(ProjectErrorNoEnum.PROJECT_NOT_EXIST));
            //项目的创建人
            query.setCreateBy(project.getCreateBy());
            // 取资源的下发方案id
            ResourceViewLimit resourceViewLimit = resourceViewLimitService.getByResourceIdAndResourceType(
                query.getProjectId(), LimitTable.ProjectViewLimit.name());
            if (Objects.isNull(resourceViewLimit)) {
                PageInfo<ProjectJoinUserPageDTO> pageInfo = new PageInfo<>();
                ArrayList<ProjectJoinUserPageDTO> list = new ArrayList<>();
                pageInfo.setList(list);
                return pageInfo;
            }
            query.setViewLimitId(resourceViewLimit.getViewLimitId());
        }

        long queryTime = System.currentTimeMillis();
        log.info(">>>>>>>>>>>>>>>>method>getProjectJoinUserData>query>getProjectJoinUserData");
        PageInfo<ProjectJoinUserPageDTO> objectPageInfo = PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> projectMapper.getProjectJoinUserData(query));
        log.info("<<<<<<<<<<<<<<<<<method>getProjectJoinUserData>query>getProjectJoinUserData, spend:{}",
            System.currentTimeMillis() - queryTime);

        if (CollectionUtils.isEmpty(objectPageInfo.getList())) {
            return objectPageInfo;
        }

        ProjectJoinUserQuery taskQuery = new ProjectJoinUserQuery();
        BeanUtils.copyProperties(query, taskQuery);
        log.info("<<<<<<<<<<<<<<<<<method>getProjectJoinUserData>query>getProjectJoinUserData, taskQuery:{}",
            taskQuery);
        if (CollectionUtils.isEmpty(query.getUserIdCollection())) {
            taskQuery.setUserIdCollection(objectPageInfo.getList().stream().map(ProjectJoinUserPageDTO::getUserId)
                .collect(Collectors.toSet()));
        }

        Set<String> orgIds = objectPageInfo.getList().stream().map(ProjectJoinUserPageDTO::getOrgId)
            .collect(Collectors.toSet());
        //查询关联组织部门
        Map<String, List<UserOrgDTO>> userRelateOrgMap = userFeign.getUserOrgByParams(taskQuery.getUserIdCollection(),
            0);
        //查部门简称
        Map<String, OrgShowDTO> orgShowDTOMap = orgFeign.getOrgShowDTO(orgIds);

        // 查询用户任务情况
        List<TaskProgress> taskProgresses = taskProgressMapper.getProjectStatisticUserData(taskQuery);
        Map<String, Long> userTaskNumMap = taskProgresses.stream()
            .collect(Collectors.groupingBy(TaskProgress::getUserId,
                Collectors.mapping(TaskProgress::getTaskId, Collectors.counting())));
        Map<String, Long> userFinishTaskNumMap = taskProgresses.stream()
            .filter(data -> Objects.nonNull(data.getIsFinish()) && Objects.equals(data.getIsFinish(), 1))
            .collect(Collectors.groupingBy(TaskProgress::getUserId,
                Collectors.mapping(TaskProgress::getTaskId, Collectors.counting())));

        dealPageInfo(objectPageInfo, userRelateOrgMap, orgShowDTOMap, userTaskNumMap, userFinishTaskNumMap);
        log.info("<<<<<<<<<<<<<<<<<getProjectJoinUserData, spend:{}", System.currentTimeMillis() - startTime);
        return objectPageInfo;
    }

    private void dealPageInfo(PageInfo<ProjectJoinUserPageDTO> objectPageInfo,
        Map<String, List<UserOrgDTO>> userRelateOrgMap, Map<String, OrgShowDTO> orgShowDTOMap,
        Map<String, Long> userTaskNumMap, Map<String, Long> userFinishTaskNumMap) {
        objectPageInfo.getList().forEach(data -> {
            data.setTaskNum("0");
            data.setProgress("0");
            data.setTaskFinishNum(0);
            Optional.ofNullable(userTaskNumMap.get(data.getUserId()))
                .ifPresent(taskNum -> {
                    data.setTaskNummber(Math.toIntExact(taskNum));
                });
            Optional.ofNullable(userFinishTaskNumMap.get(data.getUserId()))
                .ifPresent(taskFinishNum -> data.setTaskFinishNum(Math.toIntExact(taskFinishNum)));
            Optional.ofNullable(data.getJoinTime()).ifPresent(date -> {
                data.setIsJoin(GeneralJudgeEnum.CONFIRM.getValue());
                data.setTaskNum("0");
                Optional.ofNullable(data.getTaskNummber()).ifPresent(taskNumber ->
                    data.setTaskNum(String.valueOf(taskNumber)));
                data.setProgress(data.getTaskFinishNum() + "/" + data.getTaskNum());
            });

            //处理部门简称
            OrgShowDTO orgShowDTO = orgShowDTOMap.get(data.getOrgId());
            if (null != orgShowDTO) {
                data.setOrgPath(orgShowDTO.getLevelPathName());
            }

            //处理关联部门
            List<UserOrgDTO> userOrgs = userRelateOrgMap.get(data.getUserId());
            if (!CollectionUtils.isEmpty(userOrgs)) {
                String relateLevelPathName;
                Set<String> relateLevelPathNames = new HashSet<>();
                for (UserOrgDTO item : userOrgs) {
                    String levelPathName = StringUtil.isEmpty(item.getEmployeeNo()) ? item.getLevelPathName()
                        : item.getLevelPathName() + "(" + item.getEmployeeNo() + ")";
                    relateLevelPathNames.add(levelPathName);
                }
                relateLevelPathName = String.join(",", relateLevelPathNames);
                data.setRelateLevelPathName(relateLevelPathName);
            }

        });
    }

    @Override
    public void exportProjectJoinUserData(ProjectJoinUserQuery query) {
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IProjectStatisticService, ProjectJoinUserPageDTO>(
            query) {

            @Override
            protected IProjectStatisticService getBean() {
                return SpringUtil.getBean(PROJECT_STATISTIC_SERVICE, IProjectStatisticService.class);
            }

            @Override
            protected PageInfo<ProjectJoinUserPageDTO> getPageInfo() {
                return getBean().getProjectJoinUserData((ProjectJoinUserQuery) query);
            }

            @Override
            public ExportBizType getType() {
                if (Objects.equals(query.getBizType(), BY_ORG)) {
                    return ExportBizType.ProjectStatisticByOrgJoinUser;
                } else {
                    return ExportBizType.ProjectStatisticByProjectJoinUser;
                }
            }

            @Override
            public String getFileName() {
                return dealFileName(query);
            }

            @Override
            protected void afterCreateBeanMap(Map<String, Object> map) {
                map.put(IS_JOIN, Objects.nonNull(map.get(IS_JOIN)) && (Integer) map.get(IS_JOIN) > 0 ? "是" : "否");
                String JOIN_TIME = "joinTime";
                Optional.ofNullable(map.get(JOIN_TIME)).ifPresent(joinTime -> {
                    if (joinTime instanceof Date) {
                        map.put(JOIN_TIME, DateUtil.formatToStr((Date) joinTime, "yyyy-MM-dd HH:mm:ss"));
                    }
                    if (joinTime instanceof String) {
                        map.put(JOIN_TIME, joinTime);
                    }
                });
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }

    @NotNull
    private String dealFileName(ProjectJoinUserQuery query) {
        String fileName = ExportFileNameEnum.ProjectStatisticJoinUser.getType();
        fileName += Objects.equals(query.getBizType(), BY_ORG) ? "-按部门" : "-按项目";
        if (Objects.isNull(query.getUserType())) {
            fileName += "-下发人员";
        } else if (query.getUserType() == 1) {
            fileName += "-参加人员";
        } else if (query.getUserType() == 2) {
            fileName += "-未参加人员";
        } else if (query.getUserType() == 3) {
            fileName += "-完成人员";
        }
        return fileName;
    }

    @Override
    public PageInfo<ProjectAnalysisByProjectDTO> stateAnalysisByProject(ProjectAnalysisByProjectQuery query)
        throws ExecutionException, InterruptedException {
        long startTime = System.currentTimeMillis();
        log.info(">>>>>>>>>>stateAnalysisByProject");
        query.setCurrentUserId(UserThreadContext.getUserId());
        if (StringUtils.isNotBlank(query.getProjectId())) {
            query.setProjectIdList(TranslateUtil.translateBySplit(query.getProjectId(), String.class));
        }

        log.info(">>>>>>>>>>stateAnalysisByProject feign findUserManageAreaLevelPath");
        Set<String> manageAreaLevelPaths = orgFeign.findUserManageAreaLevelPath(UserThreadContext.getUserId());
        query.setManageOrgLevelPaths(manageAreaLevelPaths);
        long time1 = System.currentTimeMillis();
        log.info("<<<<<<<<<<stateAnalysisByProject feign findUserManageAreaLevelPath spend {}", time1 - startTime);

        log.info(">>>>>>>>>>stateAnalysisByProject query stateAnalysisByProject");
        PageInfo<ProjectAnalysisByProjectDTO> result = PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> projectMapper.stateAnalysisByProject(query));
        long time2 = System.currentTimeMillis();
        log.info("<<<<<<<<<<stateAnalysisByProject query stateAnalysisByProject spend {}", time2 - time1);
        List<ProjectAnalysisByProjectDTO> list = result.getList();
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }

        // 取资源归属的部门id信息
        log.info(">>>>>>>>>>stateAnalysisByProject feign getOrgMapByIds");
        long time3 = System.currentTimeMillis();
        log.info("<<<<<<<<<<stateAnalysisByProject feign getOrgMapByIds spend {}", time3 - time2);

        // 查询的时候管辖组织条件
        AtomicReference<Set<String>> queryManageLevelPaths = new AtomicReference<>(new HashSet<>());
        log.info(">>>>>>>>>>stateAnalysisByProject circle fit field");
        for (ProjectAnalysisByProjectDTO projectDTO : list) {
            log.info(">>>>>>>>>>stateAnalysisByProject circle fit field");
            //设置管辖范围
            queryManageLevelPaths.set(manageAreaLevelPaths);
            // 多线程分批执行查询数据
            Long completableFutureTime = System.currentTimeMillis();
            // 查询下发数量
            Long queryViewLimitTime = System.currentTimeMillis();
            CompletableFuture<Integer> viewLimitNumCompletableFuture = CompletableFuture.supplyAsync(() -> {
                log.info(">>>>>>>>>>>stateAnalysisByProject circle fit field query getProjectViewLimit");
                // 这里是空的
                Integer projectViewLimit = projectMapper.getProjectViewLimit(projectDTO.getViewLimitId(),
                    queryManageLevelPaths.get(), projectDTO.getCreateBy(), query.getCurrentUserId());
                log.info("<<<<<<<<<<<stateAnalysisByProject circle fit field query getProjectViewLimit spend {}",
                    System.currentTimeMillis() - queryViewLimitTime);
                return projectViewLimit;
            }, restThreadPool);
            // 查询参加数量
            Long queryJoinTime = System.currentTimeMillis();
            CompletableFuture<Integer> joinNumCompletableFuture = CompletableFuture.supplyAsync(() -> {
                log.info(">>>>>>>>>>> stateAnalysisByProject circle fit field query getProjectJoinNum");
                Integer projectJoinNum = projectMapper.getProjectJoinNum(projectDTO.getProjectId(),
                    projectDTO.getViewLimitId(), queryManageLevelPaths.get(), null, projectDTO.getCreateBy(),
                    query.getCurrentUserId());
                log.info("<<<<<<<<<< stateAnalysisByProject circle fit field query getProjectJoinNum spend {}",
                    System.currentTimeMillis() - queryJoinTime);
                return projectJoinNum;
            }, restThreadPool);
            // 查询已完成数量
            long queryFinishTime = System.currentTimeMillis();
            CompletableFuture<Integer> finishNumCompletableFuture = CompletableFuture.supplyAsync(() -> {
                log.info(
                    ">>>>>>>>>>>>>> stateAnalysisByProject circle fit field query getProjectJoinNum(getProjectFinishNum)");
                Integer projectFinishNum = projectMapper.getProjectJoinNum(projectDTO.getProjectId(),
                    projectDTO.getViewLimitId(), queryManageLevelPaths.get(), GeneralJudgeEnum.CONFIRM.getValue(),
                    projectDTO.getCreateBy(), query.getCurrentUserId());
                log.info(
                    ">>>>>>>>>>>>>> stateAnalysisByProject circle fit field query getProjectJoinNum(getProjectFinishNum) expend {}",
                    System.currentTimeMillis() - queryFinishTime);
                return projectFinishNum;
            }, restThreadPool);

            // 阻塞线程完成
            CompletableFuture<Void> completableFuture = CompletableFuture.allOf(viewLimitNumCompletableFuture,
                joinNumCompletableFuture, finishNumCompletableFuture);
            completableFuture.thenRun(() -> {
                log.info("<<<<<<<<<<<<<<========== stateAnalysisByProject circle fit field spend {}",
                    System.currentTimeMillis() - completableFutureTime);
            });

            // 设置下发人数
            Integer viewLimitNum = viewLimitNumCompletableFuture.get();
            projectDTO.setViewLimitNum(viewLimitNum);
            // 设置参加人数
            Integer joinNum = joinNumCompletableFuture.get();
            projectDTO.setJoinNum(joinNum);
            // 设置完成人数
            Integer finishNum = finishNumCompletableFuture.get();
            projectDTO.setFinishNum(finishNum);
            // 计算未参加人数
            projectDTO.setNotJoinNum(viewLimitNum - joinNum);
            // 计算参加率
            projectDTO.setJoinRate(NumberOperationUtils.getPercentStr(joinNum, viewLimitNum, 2));
            // 计算整体完成率
            projectDTO.setTotalFinishRate(NumberOperationUtils.getPercentStr(finishNum, viewLimitNum, 2));
            // 计算完成率
            projectDTO.setFinishRate(NumberOperationUtils.getPercentStr(finishNum, joinNum, 2));
        }
        log.info(">>>>>>>>>>stateAnalysisByProject circle fit field spend {}", System.currentTimeMillis() - time3);

        result.setList(list);
        log.info("<<<<<<<<<<<stateAnalysisByProject spend {}", System.currentTimeMillis() - startTime);
        return result;
    }

    @Override
    public void exportAnalysisByProject(ProjectAnalysisByProjectQuery query) {
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IProjectStatisticService, ProjectAnalysisByProjectDTO>(
            query) {

            @Override
            protected IProjectStatisticService getBean() {
                return SpringUtil.getBean(PROJECT_STATISTIC_SERVICE, IProjectStatisticService.class);
            }

            @Override
            protected PageInfo<ProjectAnalysisByProjectDTO> getPageInfo() {
                PageInfo<ProjectAnalysisByProjectDTO> pageInfo = PageInfo.emptyPageInfo();
                try {
                    pageInfo = getBean().stateAnalysisByProject((ProjectAnalysisByProjectQuery) query);
                } catch (InterruptedException e) {
                    log.error("exportAnalysisByProject error:{}", e);
                    Thread.currentThread().interrupt();
                } catch (Exception e) {
                    log.error("exportAnalysisByProject error:{}", e);
                }
                return pageInfo;
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.ProjectStatisticByProject;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.ProjectStatisticByProject.getType();
            }

            @Override
            protected void afterCreateBeanMap(Map<String, Object> map) {
                String IS_EXPIRE = "isExpire";
                map.put(IS_EXPIRE,
                    Objects.nonNull(map.get(IS_EXPIRE)) && (Integer) map.get(IS_EXPIRE) > 0 ? "是" : "否");
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public PageInfo<ProjectAnalysisByOrgDTO> stateAnalysisByOrg(ProjectAnalysisByOrgQuery query) {
        PageInfo<ProjectAnalysisByOrgDTO> result = PageInfo.emptyPageInfo();
        query.setCurrentUserId(UserThreadContext.getUserId());
        Set<String> manageAreaLevelPaths = orgFeign.findUserManageAreaLevelPath(UserThreadContext.getUserId());
        query.setManageOrgLevelPaths(manageAreaLevelPaths);
        if (StringUtils.isNotBlank(query.getOrgId())) {
            List<String> orgIds = TranslateUtil.translateBySplit(query.getOrgId(), String.class);
            query.setOrgIds(orgIds);
            Set<OrgDTO> orgDTOS = orgFeign.getByIds(orgIds);
            Set<String> orgLevelPaths = orgDTOS.stream().map(OrgDTO::getLevelPath).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(orgLevelPaths)) {
                return result;
            }
            query.setOrgLevelPaths(orgLevelPaths);
        }
        ProjectBak projectBak = Optional.ofNullable(projectBakService.getById(query.getProjectId())).orElseThrow(() ->
            new BusinessException(ErrorNoEnum.ERR_RESOURCE_BAK_NOT_EXIST));
        // 获取学习项目统计明细数据记录存储分区索引
        String statisticPartition =
            TablePartitionEnum.getTablePartitionName(TablePartitionEnum.project_statistic_by_org.getTableName(),
                projectBak.getHashIndex());
        query.setStatisticPartition(statisticPartition);

        result = PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> baseMapper.stateAnalysisByOrg(query));

        if (CollectionUtils.isEmpty(result.getList())) {
            return result;
        }
        //处理部门简称
        Set<String> orgIds = result.getList().stream().map(ProjectAnalysisByOrgDTO::getOrgId)
            .collect(Collectors.toSet());
        Map<String, OrgShowDTO> orgShowDTOMap = orgFeign.getOrgShowDTO(orgIds);
        result.getList().forEach(item -> {
            OrgShowDTO orgShowDTO = orgShowDTOMap.get(item.getOrgId());
            if (null != orgShowDTO) {
                item.setOrgPath(orgShowDTO.getLevelPathName());
            }
        });

        return result;
    }

    @Override
    public void exportAnalysisByOrg(ProjectAnalysisByOrgQuery query) {
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IProjectStatisticService, ProjectAnalysisByOrgDTO>(
            query) {

            @Override
            protected IProjectStatisticService getBean() {
                return SpringUtil.getBean(PROJECT_STATISTIC_SERVICE, IProjectStatisticService.class);
            }

            @Override
            protected PageInfo<ProjectAnalysisByOrgDTO> getPageInfo() {
                return getBean().stateAnalysisByOrg((ProjectAnalysisByOrgQuery) query);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.ProjectStatisticByOrg;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.ProjectStatisticByOrg.getType();
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }


    @Override
    public PageInfo<ProjectAnalysisByUserDTO> stateAnalysisByUser(ProjectAnalysisByUserQuery query) {
        long startTime = System.currentTimeMillis();
        log.info(">>>>>>>>>>stateAnalysisByUser start>>>>>>>>>");
        query.setCurrentUserId(UserThreadContext.getUserId());
        query.setManageOrgLevelPaths(orgFeign.findUserManageAreaLevelPath(UserThreadContext.getUserId()));
        if (StringUtils.isNotBlank(query.getProjectId())) {
            query.setProjectIdList(TranslateUtil.translateBySplit(query.getProjectId(), String.class));
        }
        if (StringUtils.isNotBlank(query.getOrgId())) {
            query.setOrgIds(TranslateUtil.translateBySplit(query.getOrgId(), String.class));
        }
        if (StringUtils.isNotBlank(query.getUserIds())) {
            query.setUserIdCollection(TranslateUtil.translateBySplit(query.getUserIds(), String.class));
        }
        PageInfo<ProjectAnalysisByUserDTO> objectPageInfo = PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> projectService.stateAnalysisByUser(query));
        //获取关联部门全路径
        Set<String> userIdList = objectPageInfo.getList().stream().map(ProjectAnalysisByUserDTO::getUserId)
            .collect(Collectors.toSet());
        Map<String, List<UserOrgDTO>> userOrgMap = userFeign.getUserOrgByParams(userIdList, 0);

        //处理部门简称
        Set<String> orgIds = objectPageInfo.getList().stream().map(ProjectAnalysisByUserDTO::getOrgId)
            .collect(Collectors.toSet());
        Map<String, OrgShowDTO> orgShowDTOMap = orgFeign.getOrgShowDTO(orgIds);

        objectPageInfo.getList().forEach(data -> {
            data.setTaskProcess(data.getFinishNum() + "/" + data.getTaskNum());
            data.setFinishRate(NumberOperationUtils.getPercentStr(data.getFinishNum(), data.getTaskNum(), 2));

            // 处理部门简称
            OrgShowDTO orgShowDTO = orgShowDTOMap.get(data.getOrgId());
            if (null != orgShowDTO) {
                data.setOrgPath(orgShowDTO.getLevelPathName());
            }

            //处理关联部门
            List<UserOrgDTO> userOrgs = userOrgMap.get(data.getUserId());
            if (!CollectionUtils.isEmpty(userOrgs)) {
                String relateLevelPathName;
                Set<String> relateLevelPathNames = new HashSet<>();
                for (UserOrgDTO item : userOrgs) {
                    String levelPathName = StringUtil.isEmpty(item.getEmployeeNo()) ? item.getLevelPathName()
                        : item.getLevelPathName() + "(" + item.getEmployeeNo() + ")";
                    relateLevelPathNames.add(levelPathName);
                }
                relateLevelPathName = String.join(",", relateLevelPathNames);
                data.setRelateLevelPathName(relateLevelPathName);
            }
        });
        log.info("<<<<<<<<<<<stateAnalysisByUser end 耗时:{}<<<<<<<<<<<", System.currentTimeMillis() - startTime);
        return objectPageInfo;
    }

    @Override
    public void exportAnalysisByUser(ProjectAnalysisByUserQuery query) {
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IProjectStatisticService, ProjectAnalysisByUserDTO>(
            query) {

            @Override
            protected IProjectStatisticService getBean() {
                return SpringUtil.getBean(PROJECT_STATISTIC_SERVICE, IProjectStatisticService.class);
            }

            @Override
            protected PageInfo<ProjectAnalysisByUserDTO> getPageInfo() {
                return getBean().stateAnalysisByUser((ProjectAnalysisByUserQuery) query);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.ProjectStatisticByUser;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.ProjectStatisticByUser.getType();
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public PageInfo<ProjectAnalysisByTypeDTO> stateAnalysisByType(ProjectAnalysisByTypeQuery query) {
        return projectStatisticByTypeService.selectProjectStatisticByType(query);
    }

    @Override
    public void exportAnalysisByType(ProjectAnalysisByTypeQuery query) {
        IExportNoEntityDataDTO exportDataDTO = new AbstractExportNoEntityDataDTO<IProjectStatisticService, ProjectAnalysisByTypeDTO>(
            query) {

            @Override
            protected IProjectStatisticService getBean() {
                return SpringUtil.getBean(PROJECT_STATISTIC_SERVICE, IProjectStatisticService.class);
            }

            @Override
            protected List<List<Object>> getPageInfo() {
                PageInfo<ProjectAnalysisByTypeDTO> pageInfo =
                    stateAnalysisByType(query);

                List<List<Object>> result = new ArrayList<>();
                List<ProjectAnalysisByTypeDTO> resultList = pageInfo.getList();
                if (!CollectionUtils.isEmpty(resultList)) {
                    for (ProjectAnalysisByTypeDTO resultInfo : resultList) {
                        List<Object> rowData = new ArrayList<>();
                        rowData.add(resultInfo.getProjectName());
                        rowData.add(resultInfo.getOrgName());
                        rowData.add(resultInfo.getLevelPathName());
                        rowData.add(resultInfo.getViewLimitNum());

                        rowData.add(resultInfo.getCourseTotalNum());
                        rowData.add(resultInfo.getCourseFinishNum());
                        rowData.add(resultInfo.getCourseFinishRateStr());

                        rowData.add(resultInfo.getExamTotalNum());
                        rowData.add(resultInfo.getExamFinishNum());
                        rowData.add(resultInfo.getExamFinishRateStr());

                        rowData.add(resultInfo.getExerciseTotalNum());
                        rowData.add(resultInfo.getExerciseFinishNum());
                        rowData.add(resultInfo.getExerciseFinishRateStr());

                        rowData.add(resultInfo.getLiveTotalNum());
                        rowData.add(resultInfo.getLiveFinishNum());
                        rowData.add(resultInfo.getLiveFinishRateStr());

                        rowData.add(resultInfo.getSurveyTotalNum());
                        rowData.add(resultInfo.getSurveyFinishNum());
                        rowData.add(resultInfo.getSurveyFinishRateStr());

                        rowData.add(resultInfo.getFormTotalNum());
                        rowData.add(resultInfo.getFormFinishNum());
                        rowData.add(resultInfo.getFormFinishRateStr());

                        rowData.add(resultInfo.getProjectTotalNum());
                        rowData.add(resultInfo.getProjectFinishNum());
                        rowData.add(resultInfo.getProjectFinishRateStr());
                        result.add(rowData);
                    }
                }
                return result;
            }

            @Override
            public Integer getCustomerHandler() {
                return 4;
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.ProjectStatisticByType;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.ProjectStatisticByType.getType();
            }

        };

        List<List<String>> header = getAnalysisByTypeExportHeader();
        exportComponent.exportNoEntityRecord(exportDataDTO, header);
    }

    @Override
    public PageInfo<AnalysisByTypeUserDTO> getAnalysisByTypeUser(AnalysisByTypeUserQuery query) {
        return projectStatisticByTypeService.getAnalysisByTypeUser(query);
    }

    @Override
    public void exportAnalysisByTypeUser(AnalysisByTypeUserQuery query) {
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IProjectStatisticService, AnalysisByTypeUserDTO>(
            query) {

            @Override
            protected IProjectStatisticService getBean() {
                return SpringUtil.getBean(PROJECT_STATISTIC_SERVICE, IProjectStatisticService.class);
            }

            @Override
            protected PageInfo<AnalysisByTypeUserDTO> getPageInfo() {
                return getBean().getAnalysisByTypeUser((AnalysisByTypeUserQuery) query);
            }


            @Override
            public ExportBizType getType() {
                if (query.getUserType() == 0) {
                    return ExportBizType.ProjectStatisticByTypeUserLimit;
                } else {
                    return ExportBizType.ProjectStatisticByTypeUser;
                }
            }

            @Override
            protected void afterCreateBeanMap(Map<String, Object> map) {
                String isJoin = Objects.isNull(map.get(IS_JOIN)) ? null : (String) map.get(IS_JOIN);
                isJoin = "1".equals(isJoin) ? "是" : "否";
                map.put(IS_JOIN, isJoin);
            }

            @Override
            public String getFileName() {
                String fileName = ExportFileNameEnum.ProjectStatisticByTypeUser.getType();
                if (query.getUserType() == 0) {
                    fileName += "下发人员";
                } else if (query.getUserType() == 1) {
                    fileName += "任务人员";
                } else if (query.getUserType() == 2) {
                    fileName += "完成人员";
                }
                return fileName;
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }


    private List<List<String>> getAnalysisByTypeExportHeader() {
        //组装表头
        List<List<String>> headerList = new ArrayList<>();
        List<String> head0 = new ArrayList<>();
        head0.add("项目名称");
        head0.add("项目名称");

        List<String> head1 = new ArrayList<>();
        head1.add("部门");
        head1.add("部门");

        List<String> head2 = new ArrayList<>();
        head2.add("全路径");
        head2.add("全路径");

        List<String> head3 = new ArrayList<>();
        head3.add("下发人数");
        head3.add("下发人数");

        List<String> head4 = new ArrayList<>();
        head4.add("课程");
        head4.add("任务人数");
        List<String> head5 = new ArrayList<>();
        head5.add("课程");
        head5.add("完成人数");
        List<String> head6 = new ArrayList<>();
        head6.add("课程");
        head6.add("完成率");

        List<String> head7 = new ArrayList<>();
        head7.add("考试");
        head7.add("任务人数");
        List<String> head8 = new ArrayList<>();
        head8.add("考试");
        head8.add("完成人数");
        List<String> head9 = new ArrayList<>();
        head9.add("考试");
        head9.add("完成率");

        List<String> head10 = new ArrayList<>();
        head10.add("练习");
        head10.add("任务人数");
        List<String> head11 = new ArrayList<>();
        head11.add("练习");
        head11.add("完成人数");
        List<String> head12 = new ArrayList<>();
        head12.add("练习");
        head12.add("完成率");

        List<String> head13 = new ArrayList<>();
        head13.add("直播");
        head13.add("任务人数");
        List<String> head14 = new ArrayList<>();
        head14.add("直播");
        head14.add("完成人数");
        List<String> head15 = new ArrayList<>();
        head15.add("直播");
        head15.add("完成率");

        List<String> head16 = new ArrayList<>();
        head16.add("调研");
        head16.add("任务人数");
        List<String> head17 = new ArrayList<>();
        head17.add("调研");
        head17.add("完成人数");
        List<String> head18 = new ArrayList<>();
        head18.add("调研");
        head18.add("完成率");

        List<String> head19 = new ArrayList<>();
        head19.add("辅导");
        head19.add("任务人数");
        List<String> head20 = new ArrayList<>();
        head20.add("辅导");
        head20.add("完成人数");
        List<String> head21 = new ArrayList<>();
        head21.add("辅导");
        head21.add("完成率");

        List<String> head22 = new ArrayList<>();
        head22.add("项目");
        head22.add("任务人数");
        List<String> head23 = new ArrayList<>();
        head23.add("项目");
        head23.add("完成人数");
        List<String> head24 = new ArrayList<>();
        head24.add("项目");
        head24.add("完成率");

        headerList.add(head0);
        headerList.add(head1);
        headerList.add(head2);
        headerList.add(head3);
        headerList.add(head4);
        headerList.add(head5);
        headerList.add(head6);
        headerList.add(head7);
        headerList.add(head8);
        headerList.add(head9);
        headerList.add(head10);
        headerList.add(head11);
        headerList.add(head12);
        headerList.add(head13);
        headerList.add(head14);
        headerList.add(head15);
        headerList.add(head16);
        headerList.add(head17);
        headerList.add(head18);
        headerList.add(head19);
        headerList.add(head20);
        headerList.add(head21);
        headerList.add(head22);
        headerList.add(head23);
        headerList.add(head24);

        return headerList;
    }


    @Override
    public void statisticRecalculateResource(String batchId, String type) {
        long startTime = System.currentTimeMillis();
        log.warn(">>>>>>>>>>>开始处理需要进行计算的学习项目资源");

        //获取需要统计的资源
        long time1 = System.currentTimeMillis();
        log.warn(">>>>>>>>>>>获取需要进行计算的学习项目资源,开始");
        Set<String> resourceIds = getRecalculateResource("ProjectBak", type);
        if(CollectionUtils.isEmpty(resourceIds)){
            log.warn("<<<<<<<<<<<获取需要进行计算的学习项目资源,完成,没有待执行数据,耗时:{}", System.currentTimeMillis() - time1);
            return;
        }

        List<ProjectBak> projectBakList = projectBakService.list(new LambdaQueryWrapper<ProjectBak>()
            .in(ProjectBak::getId, resourceIds).eq(ProjectBak::getIsDel, 0)
            .eq(ProjectBak::getIsPublish, 1).orderByDesc(ProjectBak::getUpdateTime)
            .orderByDesc(ProjectBak::getCreateTime));
        Map<String, ProjectBak> projectBakIdMap = projectBakList.stream()
            .collect(Collectors.toMap(ProjectBak::getId, Function.identity()));

        log.warn("<<<<<<<<<<<获取需要进行计算的学习项目资源,完成,耗时:{}", System.currentTimeMillis() - time1);

        // 过滤没有备份的数据不纳入统计
        resourceIds = resourceIds.stream().filter(resourceId -> Objects.nonNull(projectBakIdMap.get(resourceId)))
            .collect(Collectors.toSet());

        long removeAllTime = System.currentTimeMillis();
        log.warn(">>>>>>>>>>>>删除需要计算的历史数据");
        dealHistoryData(batchId, resourceIds, projectBakIdMap);
        log.warn("<<<<<<<<<<<删除需要计算的历史数据，耗时:{}", System.currentTimeMillis() - removeAllTime);

        log.warn(">>>>>>>>>>>保存需要重新计算的资源");
        // 保存进资源任务执行列表
        List<ProjectResourceTaskExecute> taskExecutes = resourceIds.stream().map(resourceId -> new ProjectResourceTaskExecute()
            .setId(StringUtil.newTimeId()).setResourceId(resourceId).setCreateBy(ADMIN).setBatchId(batchId)
            .setCreateTime(new Date()).setExecuteStatus(0).setMaxExecuteTimes(2).setExecuteTimes(0)
            .setIsCollect(0).setUpdateBy(ADMIN).setUpdateTime(new Date()).setIsDel(0)
        ).collect(Collectors.toList());
        List<List<ProjectResourceTaskExecute>> listList = Lists.partition(taskExecutes, 1000);
        for (List<ProjectResourceTaskExecute> executes : listList) {
            resourceTaskExecuteService.saveBatch(executes);
        }
        log.warn("<<<<<<<<<<<保存完成");
        // 写入redis
        for (ProjectResourceTaskExecute taskExecute : taskExecutes) {
            redisTemplate.opsForHash()
                .put(PROJECT_COMPUTED_RESOURCE_REDIS_KEY + "_" + batchId, batchId + ":" + taskExecute.getResourceId(),
                    taskExecute);
        }
        redisTemplate.opsForHash().getOperations()
            .expire(PROJECT_COMPUTED_RESOURCE_REDIS_KEY + "_" + batchId, 5, TimeUnit.HOURS);
        log.warn("<<<<<<<<<<需要进行计算的学习项目资源处理完成，耗时：{}", System.currentTimeMillis() - startTime);
    }

    private void dealHistoryData(String batchId, Set<String> resourceIds, Map<String, ProjectBak> projectBakIdMap) {
        for (String resourceId : resourceIds) {
            try {
                ProjectBak projectBak = projectBakIdMap.get(resourceId);
                // 学习项目统计数据存储分表
                String tablePartition = TablePartitionEnum.getTablePartitionName(
                    TablePartitionEnum.project_statistic_by_org.getTableName(), projectBak.getHashIndex());
                // 删除统计数据
                List<String> projectStatisticIds = projectStatisticByOrgService.selectIdsFromPartition(tablePartition,
                    resourceId);
                if (CollectionUtils.isNotEmpty(projectStatisticIds)) {
                    List<List<String>> removeIdListList = Lists.partition(projectStatisticIds, 500);
                    for (List<String> removeIds : removeIdListList) {
                        projectStatisticByOrgService.removeByIdsFromPartition(tablePartition, removeIds);
                    }
                }
                // 移除旧的汇总数据
                long removeOldTime = System.currentTimeMillis();
                log.warn(">>>>>>>>>移除学习项目-{}旧的汇总数据======", resourceId);
                List<ProjectStatisticByOrgCollect> removeDataList = projectStatisticByOrgCollectService.list(
                    new LambdaQueryWrapper<ProjectStatisticByOrgCollect>()
                        .eq(ProjectStatisticByOrgCollect::getProjectId, resourceId)
                        .select(ProjectStatisticByOrgCollect::getId)
                );
                if (CollectionUtils.isNotEmpty(removeDataList)) {
                    List<String> removeIdList = removeDataList.stream().map(ProjectStatisticByOrgCollect::getId)
                        .collect(Collectors.toList());
                    List<List<String>> removeIdListList = Lists.partition(removeIdList, 500);
                    for (List<String> removeIds : removeIdListList) {
                        projectStatisticByOrgCollectService.removeDataByIds(removeIds);
                    }
                }
                log.warn("<<<<<<<<<<<<移除学习项目{}旧的汇总数据完成，耗时:{}", resourceId,
                    System.currentTimeMillis() - removeOldTime);
            } catch (Exception e) {
                String log = "处理需要进行计算的学习项目资源执行失败,资源id-" + resourceId + ",堆栈信息:" + e;
                resourceTaskExecuteService.save(new ProjectResourceTaskExecute().setId(StringUtil.newId()).setBatchId(batchId)
                    .setExecuteLog(log).setCreateTime(new Date()).setCreateBy(ADMIN));
            }
        }
    }


    /**
     * 获取计算学习项目资源
     *
     * @param source 数据源[ProjectBak/Project]
     * @param type   全量/增量[ALL/PART]
     * @return
     */
    @Override
    public Set<String> getRecalculateResource(String source, String type) {
        long startTime = System.currentTimeMillis();
        log.info(">>>>>>>>>>>获取计算学习项目资源,开始");
        Set<String> resourceIds = new HashSet<>();
        // 取需要计算的时间基线
        Date nowDate = new Date();
        String executeDateLimitParam = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_905.getCode());
        int executeDateLimit =
            org.springframework.util.StringUtils.hasText(executeDateLimitParam) ? Integer.valueOf(executeDateLimitParam)
                : 3;

        //获取所有的项目备份数据(根据来源拿数据)
        List<ProjectBak> projectBakList;
        if ("ProjectBak".equals(source)) {
            //从备份表拿数据
            projectBakList = projectBakService.list(
                new LambdaQueryWrapper<ProjectBak>().eq(ProjectBak::getIsDel, 0).eq(ProjectBak::getIsPublish, 1)
                    .orderByDesc(ProjectBak::getUpdateTime).orderByDesc(ProjectBak::getCreateTime));
        } else {
            //从源表拿数据
            projectBakList = projectBakService.getRecalculateResource();
        }

        if (CollectionUtils.isEmpty(projectBakList)) {
            return resourceIds;
        }

        //全量数据，直接返回不做条件过滤
        if ("ALL".equals(type)) {
            resourceIds = projectBakList.stream().map(ProjectBak::getId).collect(Collectors.toSet());
            return resourceIds;
        }

        // 下面是增量统计/备份的判断条件
        // 需要重新计算的资源有3种情况

        // 1.统计无数据，没有计算过的资源
        long time1 = System.currentTimeMillis();
        log.info(">>>>>>>>>>>1.处理没有计算过的学习项目资源");
        List<String> ids1 = projectMapper.getNotCalcutateProjectIds();
        log.info("<<<<<<<<<<<没有计算过的学习项目资源处理完成,耗时:{}", System.currentTimeMillis() - time1);
        resourceIds.addAll(ids1);

        long time2 = System.currentTimeMillis();
        log.info(">>>>>>>>>>>2.处理项目过期过滤");
        for (ProjectBak item : projectBakList) {
            //进行过期判断
            boolean isExpired = false;
            Date endDate = item.getEndTime();
            if (null != endDate) {
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(endDate);
                calendar.add(Calendar.DAY_OF_MONTH, executeDateLimit);
                Date timeLine = calendar.getTime();
                isExpired = nowDate.compareTo(timeLine) > 0;
            }

            if (!isExpired) {
                resourceIds.add(item.getId());
            }
        }
        log.info("<<<<<<<<<<<处理项目过期过滤完成,耗时:{}", System.currentTimeMillis() - time2);

        log.info("<<<<<<<<<<获取计算学习项目资源,完成,数据量:{}，耗时：{}", resourceIds.size(),
            System.currentTimeMillis() - startTime);
        return resourceIds;
    }


    @Override
    public void statisticRecalculateResource(String batchId, Collection<String> resourceIds) {
        Date timeLine = DateUtil.getMaxTimeOfDate(DateUtil.getLast(new Date()));
        long startTime = System.currentTimeMillis();
        log.info(">>>>>>>>>>>开始处理需要进行计算的学习项目资源");
        long removeAllTime = System.currentTimeMillis();
        log.info(">>>>>>>>>>>>删除需要计算的历史数据");
        for (String resourceId : resourceIds) {
            ProjectBak projectBak = projectBakService.getById(resourceId);
            // 学习项目统计数据存储分表
            String tablePartition = TablePartitionEnum.getTablePartitionName(
                TablePartitionEnum.project_statistic_by_org.getTableName(), projectBak.getHashIndex());
            // 删除统计数据
            List<String> projectStatisticIds = projectStatisticByOrgService.selectIdsFromPartition(tablePartition,
                resourceId);
            if (CollectionUtils.isNotEmpty(projectStatisticIds)) {
                List<List<String>> removeIdListList = Lists.partition(projectStatisticIds, 500);
                for (List<String> removeIds : removeIdListList) {
                    projectStatisticByOrgService.removeByIdsFromPartition(tablePartition, removeIds);
                }
            }
            // 移除旧的汇总数据
            long removeOldTime = System.currentTimeMillis();
            log.info(">>>>>>>>>移除学习项目-{}旧的汇总数据======", resourceId);
            List<ProjectStatisticByOrgCollect> removeDataList = projectStatisticByOrgCollectService.list(
                new LambdaQueryWrapper<ProjectStatisticByOrgCollect>()
                    .eq(ProjectStatisticByOrgCollect::getProjectId, resourceId)
                    .select(ProjectStatisticByOrgCollect::getId)
            );
            if (CollectionUtils.isNotEmpty(removeDataList)) {
                List<String> removeIdList = removeDataList.stream().map(ProjectStatisticByOrgCollect::getId)
                    .collect(Collectors.toList());
                List<List<String>> removeIdListList = Lists.partition(removeIdList, 500);
                for (List<String> removeIds : removeIdListList) {
                    projectStatisticByOrgCollectService.removeDataByIds(removeIds);
                }
            }
            log.info("<<<<<<<<<<<<移除学习项目{}旧的汇总数据完成，耗时:{}======", resourceId,
                System.currentTimeMillis() - removeOldTime);
        }
        log.info("<<<<<<<<<<<删除需要计算的历史数据，耗时:{}", System.currentTimeMillis() - removeAllTime);

        log.info(">>>>>>>>>>>保存需要重新计算的资源");
        // 增加记录保存
        projectStatisticByOrgBatchService.save(new ProjectStatisticByOrgBatch().setBatchId(batchId)
            .setResourceNum(CollectionUtils.isEmpty(resourceIds) ? 0 : resourceIds.size())
            .setCreateTime(new Date())
            .setCreateBy(SYSTEM)
            // 取昨天的最后时间
            .setStatisticTime(timeLine)
        );
        // 保存进资源任务执行列表
        List<ProjectResourceTaskExecute> taskExecutes = resourceIds.stream().map(resourceId -> new ProjectResourceTaskExecute()
            .setId(StringUtil.newTimeId()).setResourceId(resourceId).setCreateBy(ADMIN).setBatchId(batchId)
            .setCreateTime(new Date()).setExecuteStatus(0).setMaxExecuteTimes(2).setExecuteTimes(0)
            .setIsCollect(0).setUpdateBy(ADMIN).setUpdateTime(new Date()).setIsDel(0)
        ).collect(Collectors.toList());
        List<List<ProjectResourceTaskExecute>> listList = Lists.partition(taskExecutes, 1000);
        for (List<ProjectResourceTaskExecute> executes : listList) {
            resourceTaskExecuteService.saveBatch(executes);
        }
        log.info("<<<<<<<<<<<保存完成");
        // 写入redis
        for (ProjectResourceTaskExecute taskExecute : taskExecutes) {
            redisTemplate.opsForHash()
                .put(PROJECT_COMPUTED_RESOURCE_REDIS_KEY + "_" + batchId, batchId + ":" + taskExecute.getResourceId(),
                    taskExecute);
        }
        redisTemplate.opsForHash().getOperations()
            .expire(PROJECT_COMPUTED_RESOURCE_REDIS_KEY + "_" + batchId, 5, TimeUnit.HOURS);
        log.info("<<<<<<<<<<需要进行计算的学习项目资源处理完成，耗时：{}", System.currentTimeMillis() - startTime);
    }

    @Override
    public void projectStatisticByOrg(String type) {
        log.info(">>>>>>>>>>>>>>开始学习项目数据统计前的准备工作");
        long startTime = System.currentTimeMillis();
        String batchId = DateHelper.formatDate(new Date(), DateHelper.YYYYMMDDHHMMSS);
        // 统计需要重新计算的资源
        this.statisticRecalculateResource(batchId, type);
        // 发送广播消息，所有节点开始计算
        ProjectResourceStatisticEvent statisticEvent = new ProjectResourceStatisticEvent(batchId);
        log.info("开始发发送学习项目情况日统计广播消息:{}", JsonUtil.objToJson(statisticEvent));
        mqProducer.sendMsg(statisticEvent);
        log.info("<<<<<<<<<<<<<<学习项目数据统计前的准备工作完成，耗时:{}", System.currentTimeMillis() - startTime);
    }

    @Override
    public void projectStatisticByOrg(String batchId, Collection<String> resourceIds) {
        log.info(">>>>>>>>>>>>>>开始学习项目数据统计前的准备工作");
        long startTime = System.currentTimeMillis();
        // 统计需要重新计算的资源
        this.statisticRecalculateResource(batchId, resourceIds);
        // 发送广播消息，所有节点开始计算
        ProjectResourceStatisticEvent statisticEvent = new ProjectResourceStatisticEvent(batchId);
        log.info("开始发发送学习项目情况日统计广播消息:{}", JsonUtil.objToJson(statisticEvent));
        mqProducer.sendMsg(statisticEvent);
        log.info("<<<<<<<<<<<<<<学习项目数据统计前的准备工作完成，耗时:{}", System.currentTimeMillis() - startTime);
    }

    @SuppressWarnings("java:S2438")
    @Override
    public void projectResourceStatisticNew(String batchId) throws UnknownHostException {
        final String DATA_KEY = PROJECT_COMPUTED_RESOURCE_REDIS_KEY + "_" + batchId;
        long startTime = System.currentTimeMillis();

        log.info("<<<<<<<<<<<<<<<学习项目统计，第{}批次任务开始执行", batchId);
        // 竞争资源
        Set<String> keys = redisTemplate.opsForHash().keys(DATA_KEY);
        List<ProjectResourceTaskExecute> dataList = redisTemplate.opsForHash().multiGet(DATA_KEY, keys);
        if (CollectionUtils.isNotEmpty(dataList)) {
            for (ProjectResourceTaskExecute taskExecute : dataList) {
                restThreadPool.execute(new ProjectResourceStatisticExecutorNew(batchId, DATA_KEY, taskExecute));
            }
        }
        log.info(">>>>>>>>>>>>>学习项目统计，第{}批次任务完成执行，耗时:{}", batchId,
            System.currentTimeMillis() - startTime);
    }

    @Override
    public PageInfo<StatisticOrgCompleteUserDetailDTO> findOrgStatisticCompleteUserDetailByPage(
        StatisticOrgCompleteUserDetailQuery query) {
        Project project = projectService.getById(query.getProjectId());
        query.setLeader(project.getLeader());
        query.setLeaderDontStat(project.getLeaderDontStat());

        PageInfo<StatisticOrgCompleteUserDetailDTO> pageInfo = PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> baseMapper.selectOrgStatisticCompleteUserDetailByPage(query));

        List<StatisticOrgCompleteUserDetailDTO> list = pageInfo.getList();
        Set<String> userIds = list.stream().map(StatisticOrgCompleteUserDetailDTO::getUserId)
            .collect(Collectors.toSet());
        Map<String, UserIdentityFeignDTO> userIdIdentityMap = userIdentityFeign.getUserIdIdentityMapByUserIds(
            userIds);
        for (StatisticOrgCompleteUserDetailDTO dto : list) {
            UserIdentityFeignDTO identity = userIdIdentityMap.get(dto.getUserId());
            if (Objects.nonNull(identity)) {
                dto.setPostName(identity.getIdentityName());
            }
        }

        return pageInfo;
    }

    @Override
    public void exportOrgCompleteStatisticUserDetail(StatisticOrgCompleteUserDetailQuery query) {
        query.setExport(true);
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IProjectStatisticService, StatisticOrgCompleteUserDetailDTO>(
            query) {

            @Override
            protected IProjectStatisticService getBean() {
                return SpringUtil.getBean(ProjectConstant.PROJECT_STATISTIC_SERVICE, IProjectStatisticService.class);
            }

            @Override
            protected PageInfo<StatisticOrgCompleteUserDetailDTO> getPageInfo() {
                return getBean().findOrgStatisticCompleteUserDetailByPage(query);
            }

            @Override
            public ExportBizType getType() {
                switch (query.getIsFinish()) {
                    case ProjectConstant.PROGRESS_STATUS_COMPLETED:
                        return ExportBizType.ProjectStatisticOrgCompleteUserDetailIsFinish;
                    case ProjectConstant.PROGRESS_STATUS_UNDERWAY:
                        return ExportBizType.ProjectStatisticOrgCompleteUserDetailNotFinish;
                    default:
                        throw new BusinessException(ProjectErrorNoEnum.ERR_CATEGORY_NULL);
                }
            }

            @Override
            public String getFileName() {
                switch (query.getIsFinish()) {
                    case ProjectConstant.PROGRESS_STATUS_COMPLETED:
                        return ExportFileNameEnum.ProjectStatisticOrgCompleteUserDetailIsFinish.getType();
                    case ProjectConstant.PROGRESS_STATUS_UNDERWAY:
                        return ExportFileNameEnum.ProjectStatisticOrgCompleteUserDetailNotFinish.getType();
                    default:
                        throw new BusinessException(ProjectErrorNoEnum.ERR_CATEGORY_NULL);
                }
            }

            @Override
            protected void afterCreateBeanMap(Map<String, Object> map) {
                // 是否是导入的数据 0-否 1-是
                Integer isImport = (Integer) map.get(IS_IMPORT);
                switch (isImport) {
                    case 0:
                        map.put(IS_IMPORT, "否");
                        break;
                    case 1:
                        map.put(IS_IMPORT, "是");
                        break;
                    default:
                }
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public PageInfo<StatisticOrgCompleteUserDetailDTO> findTeamStatisticCompleteUserDetailByPage(
        StatisticTeamCompleteUserDetailQuery query) {
        Project project = projectService.getById(query.getProjectId());
        query.setLeader(project.getLeader());
        query.setLeaderDontStat(project.getLeaderDontStat());

        PageInfo<StatisticOrgCompleteUserDetailDTO> pageInfo = PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> baseMapper.selectTeamStatisticCompleteUserDetailByPage(query));

        List<StatisticOrgCompleteUserDetailDTO> list = pageInfo.getList();
        Set<String> userIds = list.stream().map(StatisticOrgCompleteUserDetailDTO::getUserId)
            .collect(Collectors.toSet());
        Map<String, UserIdentityFeignDTO> userIdIdentityMap = userIdentityFeign.getUserIdIdentityMapByUserIds(
            userIds);
        for (StatisticOrgCompleteUserDetailDTO dto : list) {
            UserIdentityFeignDTO identity = userIdIdentityMap.get(dto.getUserId());
            if (Objects.nonNull(identity)) {
                dto.setPostName(identity.getIdentityName());
            }
        }

        return pageInfo;
    }

    @Override
    public void exportTeamCompleteStatisticUserDetail(StatisticTeamCompleteUserDetailQuery query) {
        query.setExport(true);
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IProjectStatisticService, StatisticOrgCompleteUserDetailDTO>(
            query) {

            @Override
            protected IProjectStatisticService getBean() {
                return SpringUtil.getBean(ProjectConstant.PROJECT_STATISTIC_SERVICE, IProjectStatisticService.class);
            }

            @Override
            protected PageInfo<StatisticOrgCompleteUserDetailDTO> getPageInfo() {
                return getBean().findTeamStatisticCompleteUserDetailByPage(query);
            }

            @Override
            public ExportBizType getType() {
                switch (query.getIsFinish()) {
                    case ProjectConstant.PROGRESS_STATUS_COMPLETED:
                        return ExportBizType.ProjectStatisticOrgCompleteUserDetailIsFinish;
                    case ProjectConstant.PROGRESS_STATUS_UNDERWAY:
                        return ExportBizType.ProjectStatisticOrgCompleteUserDetailNotFinish;
                    default:
                        throw new BusinessException(ProjectErrorNoEnum.ERR_CATEGORY_NULL);
                }
            }

            @Override
            public String getFileName() {
                switch (query.getIsFinish()) {
                    case ProjectConstant.PROGRESS_STATUS_COMPLETED:
                        return ExportFileNameEnum.ProjectStatisticTeamCompleteUserDetailIsFinish.getType();
                    case ProjectConstant.PROGRESS_STATUS_UNDERWAY:
                        return ExportFileNameEnum.ProjectStatisticTeamCompleteUserDetailNotFinish.getType();
                    default:
                        throw new BusinessException(ProjectErrorNoEnum.ERR_CATEGORY_NULL);
                }
            }

            @Override
            protected void afterCreateBeanMap(Map<String, Object> map) {
                // 是否是导入的数据 0-否 1-是
                Integer isImport = (Integer) map.get(IS_IMPORT);
                switch (isImport) {
                    case 0:
                        map.put(IS_IMPORT, "否");
                        break;
                    case 1:
                        map.put(IS_IMPORT, "是");
                        break;
                    default:
                }
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public Long getProjectStudentNumById(String projectId) {
        return progressService.getProjectStudentNumById(projectId);
    }

    /**
     * 获取项目资源ID列表
     *
     * @param projectId 项目ID
     *  @param isOnlyCourse 是否只获取课程ID 0-否 1-是 默认为0
     * @return 学习项目相关资源ID列表
     */
    private List<String> getProjectResourceIdList(String projectId, boolean isOnlyCourse) {
        List<String> idList = new ArrayList<>();

        // 添加项目ID
        idList.add(projectId);

        // 获取项目任务资源ID和项目类型
        List<Task> taskList = taskService.list(
            new LambdaQueryWrapper<Task>().eq(Task::getProId, projectId).select(Task::getTaskContent, Task::getTaskType)
        );
        Set<String> taskContentSet = taskList.stream()
            .map(Task::getTaskContent)
            .filter(StringUtils::isNotBlank)
            .collect(Collectors.toSet());
        if (!taskContentSet.isEmpty()) {
            idList.addAll(taskContentSet);
        }

        // 筛选出课程ID,获取对应课件资源ID
        List<String> courseIds = taskList.stream()
            .filter(task -> ProjectTaskTypeEnum.COURSE.getTaskType().equals(task.getTaskType()))
            .map(Task::getTaskContent).collect(Collectors.toList());

        // 仅获取课程ID
        if (isOnlyCourse) {
            return courseIds;
        }

        // 查询课程下的课件ID
        if (!courseIds.isEmpty()) {
            List<String> coursewareIdListByCourseIdList = courseWareFeign.getCoursewareIdListByCourseIdList(courseIds);
            if (!coursewareIdListByCourseIdList.isEmpty()) {
                idList.addAll(coursewareIdListByCourseIdList);
            }
        }

        // 获取项目应用资源ID
        List<App> appList = appService.list(
            new LambdaQueryWrapper<App>().eq(App::getProjectId, projectId).select(App::getResourceId)
        );
        Set<String> appResourceIdSet = appList.stream()
            .map(App::getResourceId)
            .filter(StringUtils::isNotBlank)
            .collect(Collectors.toSet());
        if (!appResourceIdSet.isEmpty()) {
            idList.addAll(appResourceIdSet);
        }

        // 返回学习项目相关资源ID列表
        return idList;
    }


    @Override
    public PageInfo<StatisticProjectCompletionDTO> findProjectCompletionStatisticByPage(
        StatisticProjectCompletionQuery query) {

        long startTime = System.currentTimeMillis();
        log.info("开始执行 findProjectCompletionStatisticByPage，参数：{}", JsonUtil.objToJson(query));

        // 拆分用户ID
        if (!StringUtil.isEmpty(query.getUserId())) {
            query.setUserIdList(Arrays.asList(query.getUserId().split(",")));
        }

        // 获取项目信息 确认是否统计班主任的学习数据
        Project project = projectService.getById(query.getProjectId());
        query.setLeader(project.getLeader());
        query.setLeaderDontStat(project.getLeaderDontStat());

        // 获取项目完成情况统计基础数据 （从项目任务进度表查询该项目参与人员的分页数据，执行查询条件过滤，得到总体任务进度）
        long pageQueryStartTime = System.currentTimeMillis();
        PageInfo<StatisticProjectCompletionDTO> pageInfo = PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> baseMapper.findProjectCompletionStatisticByPage(query));
        log.info("findProjectCompletionStatisticByPage 分页查询耗时 {} ms",
            System.currentTimeMillis() - pageQueryStartTime);

        // 统计结果数据填充
        List<StatisticProjectCompletionDTO> dtoList = pageInfo.getList();

        // 如果未查询到数据，则返回空结果
        if (dtoList.isEmpty()) {
            log.info("findProjectCompletionStatisticByPage 分页数据为空，直接返回");
            return pageInfo;
        }

        // 用户ID列表
        List<String> userIdList = dtoList.stream().map(StatisticProjectCompletionDTO::getUserId).toList();

        // 批量获取用户部门信息（根据开发规范，部门名称的显示需要根据系统配置动态处理）
        long getOrgShowDTOTime = System.currentTimeMillis();
        CompletableFuture<Map<String, OrgShowDTO>> orgShowDTOFuture = CompletableFuture.supplyAsync(() -> {
            Set<String> orgIds = dtoList.stream().map(StatisticProjectCompletionDTO::getOrgId)
                .collect(Collectors.toSet());
            return orgFeign.getOrgShowDTO(orgIds);
        });
        log.info("findProjectCompletionStatisticByPage 异步发起批量获取用户部门信息请求耗时 {} ms",
            System.currentTimeMillis() - getOrgShowDTOTime);

        // 逐项查询任务执行进度，由于每人的任务数量可能不一样，必须单独查询
        long underwayUserMapTime = System.currentTimeMillis();
        Set<String> userIdSet = new HashSet<>(userIdList); // 去重
        CompletableFuture<Map<String, UserStatisticProjectCompletionDTO>> userRatioMapFuture = CompletableFuture.supplyAsync(
            () -> {
                Map<String, UserStatisticProjectCompletionDTO> map = new HashMap<>();
                userIdSet.forEach(uid -> {
                    // 获取用户项目完成情况
                    UserStatisticProjectCompletionDTO userStatisticProjectCompletion = taskService.getUserStatisticProjectCompletion(
                        uid, query.getProjectId());
                    map.put(uid, userStatisticProjectCompletion);
                });
                return map;
            });
        log.info("findProjectCompletionStatisticByPage 异步发起获取进行中用户的详细进度请求耗时 {} ms",
            System.currentTimeMillis() - underwayUserMapTime);

        // 获取课程学时数据
        long courseDurationTime = System.currentTimeMillis();
        CompletableFuture<Map<String, Long>> courseDurationMapFuture = CompletableFuture.supplyAsync(() -> {
            List<String> courseIdList = getProjectResourceIdList(query.getProjectId(), true);
            return courseFeign.getCourseDurationMap(userIdList, courseIdList);
        });
        log.info("findProjectCompletionStatisticByPage 异步发起获取课程学时数据请求耗时 {} ms",
            System.currentTimeMillis() - courseDurationTime);

        // 获取激励数据
        long excitationStatisticTime = System.currentTimeMillis();
        CompletableFuture<Map<String, UserExcitationStatisticDTO>> userExcitationStatisticMapFuture = CompletableFuture.supplyAsync(
            () -> {
                List<String> projectResourceIdList = getProjectResourceIdList(query.getProjectId(), false);
                return excitationFeign.getUserExcitationStatistic(
                    userIdList, projectResourceIdList);
            });
        log.info("findProjectCompletionStatisticByPage 异步发起获取激励数据请求耗时 {} ms",
            System.currentTimeMillis() - excitationStatisticTime);

        // 阻塞线程完成
        long allOfTime = System.currentTimeMillis();
        CompletableFuture.allOf(orgShowDTOFuture, userRatioMapFuture, courseDurationMapFuture,
            userExcitationStatisticMapFuture).join(); // 使用 join() 替代 get()，避免抛出检查异常
        log.info("findProjectCompletionStatisticByPage 所有异步任务完成耗时 {} ms",
            System.currentTimeMillis() - allOfTime);

        // 异步获取数据
        Map<String, OrgShowDTO> orgShowDTOMap;
        Map<String, UserStatisticProjectCompletionDTO> userRatioMap;
        Map<String, Long> courseDurationMap;
        Map<String, UserExcitationStatisticDTO> userExcitationStatisticMap;

        try {
            orgShowDTOMap = orgShowDTOFuture.get();
            userRatioMap = userRatioMapFuture.get();
            courseDurationMap = courseDurationMapFuture.get();
            userExcitationStatisticMap = userExcitationStatisticMapFuture.get();
        } catch (InterruptedException | ExecutionException e) {
            Thread.currentThread().interrupt();
            log.error("findProjectCompletionStatisticByPage 异步获取数据异常！", e);
            throw new BusinessException(ErrorNoEnum.ERR_SERVER);
        }

        // 数据填充
        for (StatisticProjectCompletionDTO dto : dtoList) {

            // 装配用户项目完成率
            buildUserProjectFinishRatio(dto, userRatioMap);

            // 装配用户激励数据
            buildUserExcitationStatistic(dto, userExcitationStatisticMap);

            // 装配用户课时数据
            buildUserCourseDuration(dto, courseDurationMap);

            // 补全部门名称数据
            Optional.ofNullable(orgShowDTOMap.get(dto.getOrgId())).ifPresentOrElse(orgShowDTO -> {
                dto.setOrgName(orgShowDTO.getOrgShortName());
                dto.setOrgPath(orgShowDTO.getLevelPathName());
            }, () -> {
                dto.setOrgName(null);
                dto.setOrgPath(null);
            });
        }

        // 这句代码是否可以省略？
        pageInfo.setList(dtoList);

        log.info("findProjectCompletionStatisticByPage 方法总耗时 {} ms", System.currentTimeMillis() - startTime);
        return pageInfo;
    }

    /***
     * 装配用户课时数据
     *
     * @param dto 用户项目完成情况
     * @param courseDurationMap 用户课时数据
     */
    private static void buildUserCourseDuration(StatisticProjectCompletionDTO dto,
        Map<String, Long> courseDurationMap) {

        Long courseDuration = courseDurationMap.get(dto.getUserId());
        if (courseDuration != null) {
            double hours = courseDuration.doubleValue() / 3600;

            // 课时（小时） 不足0.01小时按0计算，否则四舍五入
            if (hours < 0.01) {
                hours = 0.00;
            }

            // 类型转换
            BigDecimal result = BigDecimal.valueOf(hours).setScale(2, RoundingMode.HALF_UP);
            dto.setCourseTime(result);
        } else {
            dto.setCourseTime(BigDecimal.valueOf(0.00));
        }
    }

    /***
     * 装配用户激励数据
     *
     * @param dto 用户项目完成情况
     * @param userExcitationStatisticMap 用户激励数据
     */
    private static void buildUserExcitationStatistic(StatisticProjectCompletionDTO dto,
        Map<String, UserExcitationStatisticDTO> userExcitationStatisticMap) {
        UserExcitationStatisticDTO userExcitationStatisticDTO = userExcitationStatisticMap.get(dto.getUserId());
        if (userExcitationStatisticDTO != null) {
            dto.setLearnTime(userExcitationStatisticDTO.getLearnTime());
            dto.setIntegral(userExcitationStatisticDTO.getIntegral());
            dto.setGoldCoin(userExcitationStatisticDTO.getGoldCoin());
            dto.setCredit(userExcitationStatisticDTO.getCredit());
        }
    }

    /***
     * 装配用户项目完成率
     *
     * @param dto 用户项目完成情况
     * @param underwayUserMap 进行中的用户ID集合 - 需要进行逐项查询
     */
    private static void buildUserProjectFinishRatio(StatisticProjectCompletionDTO dto,
        Map<String, UserStatisticProjectCompletionDTO> underwayUserMap) {

        // 设置3种项目完成率（所有任务完成率，课程任务中计算必修、 必修课程完成率）
        UserStatisticProjectCompletionDTO userStatisticProjectCompletion = underwayUserMap.get(dto.getUserId());
        if (userStatisticProjectCompletion != null) {
            dto.setFinishRatio(userStatisticProjectCompletion.getFinishRatio())
                .setFinishOnlyMustRatio(userStatisticProjectCompletion.getFinishOnlyMustRatio())
                .setCourseMustFinishRatio(userStatisticProjectCompletion.getCourseMustFinishRatio());
        } else {
            dto.setFinishOnlyMustRatio(BigDecimal.valueOf(0));
            dto.setCourseMustFinishRatio(BigDecimal.valueOf(0));
        }

        // 设置百分比文本
        dto.setFinishRatioStr(dto.getFinishRatio() == null ? "" : dto.getFinishRatio() + "%");
        dto.setFinishOnlyMustRatioStr(dto.getFinishOnlyMustRatio() == null ? "" : dto.getFinishOnlyMustRatio() + "%");
        dto.setCourseMustFinishRatioStr(
            dto.getCourseMustFinishRatio() == null ? "" : dto.getCourseMustFinishRatio() + "%");
    }


    @Override
    public void exportProjectCompletionStatistic(StatisticProjectCompletionQuery query) {
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IProjectStatisticService, StatisticProjectCompletionDTO>(query) {

            @Override
            protected IProjectStatisticService getBean() {
                return SpringUtil.getBean(PROJECT_STATISTIC_SERVICE, IProjectStatisticService.class);
            }

            @Override
            protected PageInfo<StatisticProjectCompletionDTO> getPageInfo() {
                    return getBean().findProjectCompletionStatisticByPage(query);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.ProjectCompletionStatistic;
            }

            @Override
            public String getFileName() {
                Project project = projectService.getById(query.getProjectId());
                return project.getProName() + "_" + ExportFileNameEnum.ProjectCompletionStatistic.getType();
            }
        };

        exportComponent.exportRecord(exportDataDTO);
    }


    /**
     * 据类型-全量/增量备份项目数据
     *
     * @param type [ALL/PART]
     * @return
     */
    @Override
    public void projectBackupResourceDataByType(String type) {
        String hostName = null;
        String tenantId = UserThreadContext.getTenantId();
        //初始化执行状态,给备份数据上锁,防止并发
        final String backupStatusKey = "projectBackupStatus-" + tenantId + "-" + type;
        Boolean isGetLock = redisTemplate.opsForValue().setIfAbsent(backupStatusKey, 0);
        if (!isGetLock) {
            log.info("<<<<<<<<<<<projectBackupResourceData get lock fail end");
            return;
        }
        //更新备份数据为执行中
        redisTemplate.opsForValue().set(backupStatusKey, 1, 3, TimeUnit.HOURS);
        long startTime = System.currentTimeMillis();
        log.info(">>>>>>>>>>>projectBackupResourceData start");
        //创建线程池
        String executeThreadPoolName = "execute-backupData-pool";

        try {
            hostName = InetAddress.getLocalHost().getHostName();
            baseMapper.cleanTransferLog();
            baseMapper.addTransferLog("hostName: " + hostName + ",projectBackupResourceData begin", new Date(), null);

            //关闭日志
            long closeStart = System.currentTimeMillis();
            log.info(">>>>>>>>>>>projectBackupResourceData close log start");
            log.info("<<<<<<<<<<<projectBackupResourceData close log end 耗时:{}",
                System.currentTimeMillis() - closeStart);

            //备份基础数据
            long baseDataStart = System.currentTimeMillis();
            log.info(">>>>>>>>>>>projectBackupResourceData backup base data start");
            backupBaseData();
            log.info("<<<<<<<<<<<projectBackupResourceData backup base data end 耗时:{}",
                System.currentTimeMillis() - baseDataStart);
            Set<String> resourceIds = getRecalculateResource("Project", type);

            List<ProjectBak> projectBakList = projectBakService.listByIds(resourceIds);
            Map<Integer, List<ProjectBak>> hashGroupMap = projectBakList.stream()
                .collect(Collectors.groupingBy(ProjectBak::getHashIndex));

            //备份业务数据
            backUpResourceData(backupDataExecuteThreadPool, type, hashGroupMap);

            //备份下发用户数据
            backUpLimitUserData(backupDataExecuteThreadPool, type, projectBakList);


        } catch (Exception e) {
            log.error("<<<<<<<<<<< projectBackupResourceData error:", e);
        } finally {

            //开启日志
            long openStart = System.currentTimeMillis();
            log.info(">>>>>>>>>>>projectBackupResourceData open log start");
            log.info("<<<<<<<<<<<projectBackupResourceData open log end 耗时:{}",
                System.currentTimeMillis() - openStart);
            //删除redis的备份状态key
            redisTemplate.delete(backupStatusKey);
        }

        baseMapper.addTransferLog("hostName: " + hostName + ",projectBackupResourceData end", new Date(), null);
        log.info("<<<<<<<<<<<projectBackupResourceData end,耗时:" + (System.currentTimeMillis() - startTime));
    }

    private void backUpResourceData(ExecutorService executeThreadPool, String type,
        Map<Integer, List<ProjectBak>> hashGroupMap) {
        //全量/增量--备份用户学习项目记录数据,用户学习项目任务进度记录表,50个分区
        long recordStart = System.currentTimeMillis();
        log.info(">>>>>>>>>>>projectBackupResourceData backUpAllData data start");

        for (int i = 0; i <= 49; i++) {
            String projectProgressDataSql;
            String projectProgressSourceTable = "project_progress";
            String projectProgressTargetTable = PROJECT_PROGRESS_BAK + i;

            String taskProgressDataSql;
            String taskProgressSourceTable = "task_progress";
            String taskProgressTargetTable = TASK_PROGRESS_BAK + i;
            if ("ALL".equals(type)) {

                //清除所有项目进度数据
                cleanAllData(projectProgressSourceTable, projectProgressTargetTable);
                projectProgressDataSql =
                    "select a.*  from project_progress a , project_bak b  where a.pro_id  = b.id AND b.hash_index = "
                        + i + " order by a.id asc";

                //清除所有任务进度数据
                cleanAllData(taskProgressSourceTable, taskProgressTargetTable);
                taskProgressDataSql =
                    "select a.*  from task_progress a , project_bak b  where a.pro_id  = b.id AND b.hash_index = " + i
                        + " order by a.id asc ";
            } else {

                List<ProjectBak> projectBaks = hashGroupMap.get(i);
                if (org.springframework.util.CollectionUtils.isEmpty(projectBaks)) {
                    log.error(">>>>>>>>>>>projectBackupResourceData 分区：{},没有资源,无需备份", i);
                    continue;
                }
                String resourceIdsStr = projectBaks.stream().map(ProjectBak::getId).collect(Collectors.toSet())
                    .stream().map(item -> "\"" + item + "\"")
                    .collect(Collectors.joining(","));

                //清除部分项目进度数据
                //String cleanProjectProgressSql =
                //    "delete from " + projectProgressTargetTable + " a where a.pro_id in(" + resourceIdsStr + ");";
                //cleanPartData(cleanProjectProgressSql);
                String maxPorjectProgressDateSql =
                    "    select date_format( date_sub( max(update_time),   INTERVAL 1 day) ,'%Y-%m-%d %H:%i:%S' ) updateTime from  "
                        + projectProgressTargetTable + "  x " +
                        "    where x.pro_id in( " + resourceIdsStr + ")";
                String maxProjectProgressDate = getDate(maxPorjectProgressDateSql);
                String queryByUpdateTime = "";
                if (StringUtils.isNotBlank(maxProjectProgressDate)) {
                    queryByUpdateTime = " and a.update_time  >= '" + maxProjectProgressDate + "'";
                }
                projectProgressDataSql =
                    "select a.*  from project_progress a , project_bak b  where a.pro_id  = b.id AND b.hash_index = "
                        + i + " and b.id in ( " + resourceIdsStr + " ) " +
                        queryByUpdateTime +
                        " order by a.id asc ";

                //清除部分任务进度数据
                //String cleanTaskProgressSql =
                //    "delete from " + taskProgressTargetTable + " a where a.pro_id in(" + resourceIdsStr + ");";
                //cleanPartData(cleanTaskProgressSql);
                String maxTaskProgressDateSql =
                    "    select date_format( date_sub( max(update_time),   INTERVAL 1 day) ,'%Y-%m-%d %H:%i:%S' ) updateTime from  "
                        + taskProgressTargetTable + "  x " +
                        "    where x.pro_id in( " + resourceIdsStr + ")";
                String maxTaskProgressDate = getDate(maxTaskProgressDateSql);
                String queryTpByUpdateTime = "";
                if (StringUtils.isNotBlank(maxTaskProgressDate)) {
                    queryTpByUpdateTime = " and a.update_time  >=  '" + maxTaskProgressDate + "' ";
                }
                taskProgressDataSql =
                    "select a.*  from task_progress a , project_bak b  where a.pro_id  = b.id AND b.hash_index = " + i
                        + " and b.id in ( " + resourceIdsStr + " ) " +
                        queryTpByUpdateTime +
                        "order by a.id asc ";

            }
            //备份项目进度数据
            backupRecordData(executeThreadPool, projectProgressDataSql, projectProgressSourceTable,
                projectProgressTargetTable, 1000, type);
            //备份任务进度数据
            backupRecordData(executeThreadPool, taskProgressDataSql, taskProgressSourceTable, taskProgressTargetTable,
                1000, type);

        }
        log.info("<<<<<<<<<<<projectBackupResourceData backUpAllData end 耗时:{}",
            System.currentTimeMillis() - recordStart);
    }

    private String getDate(String maxTaskProgressDateSql) {
        String date = baseMapper.getDate(maxTaskProgressDateSql);
        return date;
    }


    /**
     * 清除部分数据
     *
     * @param cleanDataSql
     * @return
     */
    private Long cleanPartData(String cleanDataSql) {
        Long num = baseMapper.cleanPartData(cleanDataSql);
        return num;
    }

    /**
     * 清除全部数据
     *
     * @param sourceTable
     * @param targetTable
     * @return
     */
    private void cleanAllData(String sourceTable, String targetTable) {
        //重新创建备份表,清除全部数据
        long startTime = System.currentTimeMillis();
        log.info(">>>>>>>>>>>backupRecordData createBackupTable start,sourceTable:{},targetTable:{}", sourceTable,
            targetTable);
        createBackupTable(sourceTable, targetTable);
        log.info("<<<<<<<<<<<backupRecordData createBackupTable end,sourceTable:{},targetTable:{},耗时:{}", sourceTable,
            targetTable, (System.currentTimeMillis() - startTime));
    }

    /**
     * 备份下发人员表
     *
     * @param executeThreadPool
     * @return
     */
    private void backUpLimitUserData(ExecutorService executeThreadPool, String type, List<ProjectBak> projectBakList) {
        //备份下发人员数据表,100个分区
        long limitUserStart = System.currentTimeMillis();
        int start = 0, end = 99;
        log.info(">>>>>>>>>>>projectBackupResourceData backUpLimitUser start {} to {}  start", start, end);
        for (int i = start; i <= end; i++) {
            String limitUserSourceTable = "w_view_limit_user";
            String limitUserTargetTable = W_VIEW_LIMIT_USER_BAK + i;
            if ("ALL".equals(type)) {
                String limitUserDataSql =
                    "select a.* from w_view_limit_user a  where  exists (select 1 from  w_resource_view_limit_bak b where a.view_limit_id  = b.view_limit_id AND b.hash_index = "
                        + i + ") " + " order by a.id asc ";
                //清除所有下发数据
                cleanAllData(limitUserSourceTable, limitUserTargetTable);
                backupRecordData(executeThreadPool, limitUserDataSql, limitUserSourceTable, limitUserTargetTable, 5000,
                    type);
            } else {

                if (org.springframework.util.CollectionUtils.isEmpty(projectBakList)) {
                    log.info(">>>>>>>>>>>courseBackupResourceData 分区：{},没有资源,无需备份", i);
                    continue;
                }
                String resourceIdsStr = projectBakList.stream().map(ProjectBak::getId).collect(Collectors.toSet())
                    .stream().map(item -> "\"" + item + "\"")
                    .collect(Collectors.joining(","));
                String cleanLimitDataSql =
                    "delete from " + limitUserTargetTable
                        + " a where a.view_limit_id in ( select view_limit_id from w_resource_view_limit_bak " +
                        " where resource_id in (  " + resourceIdsStr + ") ) ;";
                cleanPartData(cleanLimitDataSql);

                String limitUserDataSql =
                    "select a.* from w_view_limit_user a  where  exists (select 1 from  w_resource_view_limit_bak b " +
                        " where a.view_limit_id  = b.view_limit_id AND b.hash_index = "
                        + i + " and b.resource_id in (" + resourceIdsStr + ")) " + " order by a.id asc ";
                backupRecordData(executeThreadPool, limitUserDataSql, limitUserSourceTable, limitUserTargetTable, 5000,
                    type);
            }
        }
        log.info("<<<<<<<<<<<projectBackupResourceData backUpLimitUser end {} to {} end 耗时:{}", start, end,
            System.currentTimeMillis() - limitUserStart);
    }


    public class ProjectResourceStatisticExecutorNew extends Thread {

        private String batchId;

        private String DATA_KEY;

        private ProjectResourceTaskExecute projectResourceTaskExecute;

        public ProjectResourceStatisticExecutorNew(String batchId, String DATA_KEY, ProjectResourceTaskExecute execute) {
            this.projectResourceTaskExecute = execute;
            this.batchId = batchId;
            this.DATA_KEY = DATA_KEY;
        }

        @Override
        public void run() {
            long startTime = System.currentTimeMillis();
            String theadName = Thread.currentThread().getName();
            //保存数据线程执行
            log.info("----------执行getResource开始,resourceTaskExecute:{}----", projectResourceTaskExecute);
            //获取该批次可以执行的资源
            ProjectResourceTaskExecute resourceTask = resourceTaskExecuteService.getResource(DATA_KEY,
                projectResourceTaskExecute);
            if (null == resourceTask) {
                log.info("------执行getResource结束，没有抢到可执行资源:resourceTaskExecute:{}----",
                    projectResourceTaskExecute);
                return;
            }
            log.info("------执行getResource结束----抢到可执行资源resourceTask:{}----", resourceTask);
            //处理并保存统计数据
            log.info("------线程：{}----执行projectStatisticByOrgNew开始,resourceTaskExecute:{}----", theadName,
                resourceTask);
            projectStatisticByOrgNew(batchId, DATA_KEY, resourceTask);
            log.info("------线程：{}----执行saveStatisticsData结束,resourceTaskExecute:{},耗时:{}----", theadName,
                resourceTask,
                System.currentTimeMillis() - startTime);
        }
    }

    public void projectStatisticByOrgNew(String batchId, String dataKey, ProjectResourceTaskExecute resourceTask) {
        String projectId = resourceTask.getResourceId();
        String hostName = null;
        try {
            hostName = InetAddress.getLocalHost().getHostName();
            projectStatisticByOrgBodyNew(batchId, dataKey, resourceTask);
        } catch (Exception e) {
            e.printStackTrace();
            String executeLog =
                "hostName:" + hostName + ",logTime:" + DateUtils.format(new Date()) + ",error:" + e.toString();
            log.error("批次号:{},学习项目资源-{}计算执行失败, {}", batchId, projectId, executeLog);
            // 更新资源为执行失败状态
            resourceTaskExecuteService.update(new LambdaUpdateWrapper<ProjectResourceTaskExecute>()
                .eq(ProjectResourceTaskExecute::getBatchId, batchId)
                .eq(ProjectResourceTaskExecute::getResourceId, projectId)
                .set(ProjectResourceTaskExecute::getExecuteStatus, -1)
                .set(ProjectResourceTaskExecute::getExecuteLog, executeLog)
                .set(ProjectResourceTaskExecute::getUpdateBy, SYSTEM)
                .set(ProjectResourceTaskExecute::getUpdateTime, new Date())
            );
            //更新缓存中的资源信息
            resourceTask.setExecuteStatus(-1)
                .setExecuteLog(executeLog)
                .setUpdateBy(SYSTEM)
                .setUpdateTime(new Date());
            redisTemplate.opsForHash().put(dataKey, batchId + ":" + resourceTask.getResourceId(), resourceTask);
        }
    }


    private void projectStatisticByOrgBodyNew(String batchId, String dataKey, ProjectResourceTaskExecute resourceTask) {
        String projectId = resourceTask.getResourceId();
        long startTime = System.currentTimeMillis();
        log.info(">>>>>>>>>>>>>>开始学习项目-{}情况统计,当前执行批次:{}", projectId, batchId);
        List<ProjectStatisticByOrg> saveList;

        //获取资源对应的下发用户所在的分区索引
        ResourceViewLimitBakDTO resourceViewLimitBak = resourceViewLimitBakService.getLimitUserPartition(projectId);
        if (null == resourceViewLimitBak) {
            log.info(">>>>>>>>>>>>>>统计学习项目-{}情况统计, 资源备份数据不存在", projectId);
            throw new BusinessException(ErrorNoEnum.ERR_RESOURCE_BAK_NOT_EXIST);
        }
        String limitUserPartition = resourceViewLimitBak.getTablePartition();
        Long viewLimitId = resourceViewLimitBak.getViewLimitId();
        // 查询下发数量
        Long queryViewLimitTime = System.currentTimeMillis();
        log.info(">>>>>>>>>>>查询学习项目下发用户数量属性");
        saveList = projectStatisticByOrgService.queryProjectViewLimitFieldList(projectId, limitUserPartition,
            viewLimitId);
        log.info("<<<<<<<<<<<查询学习项目下发用户数量属性 耗时： {}", System.currentTimeMillis() - queryViewLimitTime);
        // 查询参加数量
        Long queryJoinTime = System.currentTimeMillis();
        log.info(">>>>>>>>>>> 查询学习项目参加用户数量属性");
        Map<String, ProjectStatisticByOrg> projectJoinNumMap =
            projectStatisticByOrgService.queryProjectJoinNumField(projectId, limitUserPartition, viewLimitId);
        log.info("<<<<<<<<<< 查询学习项目参加用户数量属性 耗时 {}", System.currentTimeMillis() - queryJoinTime);
        Long queryFinishTime = System.currentTimeMillis();
        log.info(">>>>>>>>>>>>>> 查询学习项目已完成用户数量属性");
        Map<String, ProjectStatisticByOrg> projectFinishNumMap =
            projectStatisticByOrgService.queryProjectFinishNumField(projectId, limitUserPartition, viewLimitId);
        log.info("<<<<<<<<<<<<<< 查询学习项目已完成用户数量属性 耗时 {}", System.currentTimeMillis() - queryFinishTime);
        Long queryScoreTime = System.currentTimeMillis();
        log.info(">>>>>>>>>>>>>> 查询学习项目已完成用户任务分属性");
        Map<String, ProjectStatisticByOrg> projectTaskScoreMap
            = projectStatisticByOrgService.queryProjectTaskScoreField(projectId, limitUserPartition, viewLimitId);
        log.info("<<<<<<<<<<<<<< 查询学习项目已完成用户任务分属性 耗时 {}",
            System.currentTimeMillis() - queryScoreTime);

        long fitTime = System.currentTimeMillis();
        log.info(">>>>>>>>>>装配部门统计数据属性");

        saveList.stream().forEach(element -> {
            element.setId(StringUtil.newTimeId())
                .setProjectId(projectId)
                .setBatchId(batchId)
                .setTaskScore(0)
                .setJoinNum(0)
                .setFinishNum(0)
                .setIsDel(0)
            ;
            Optional.ofNullable(projectJoinNumMap.get(element.getOrgId()))
                .ifPresent(res -> element.setJoinNum(res.getJoinNum()));
            Optional.ofNullable(projectFinishNumMap.get(element.getOrgId()))
                .ifPresent(res -> element.setFinishNum(res.getFinishNum()));
            Optional.ofNullable(projectTaskScoreMap.get(element.getOrgId()))
                .ifPresent(res -> {
                    if (Objects.nonNull(res.getTaskScore())) {
                        element.setTaskScore(res.getTaskScore());
                    }
                });
            Integer viewLimitNum = element.getViewLimitNum();
            Integer joinNum = element.getJoinNum();
            Integer finishNum = element.getFinishNum();
            // 计算未参加人数
            element.setNotJoinNum(viewLimitNum - joinNum);
            // 计算参加率
            element.setJoinRate(NumberOperationUtils.getPercentStr(joinNum, viewLimitNum, 2));
            // 计算整体完成率
            element.setTotalFinishRate(NumberOperationUtils.getPercentStr(finishNum, viewLimitNum, 2));
            // 计算完成率
            element.setFinishRate(NumberOperationUtils.getPercentStr(finishNum, joinNum, 2));
            element.setCreateBy(SYSTEM).setCreateTime(new Date()).setUpdateBy(SYSTEM).setUpdateTime(new Date());
        });
        log.info("<<<<<<<<<<<<<<装配部门统计数据属性完成，耗时:{}", System.currentTimeMillis() - fitTime);

        ProjectBak projectBak = projectBakService.getById(projectId);
        // 学习项目统计数据存储分表
        String tablePartition = TablePartitionEnum.getTablePartitionName(
            TablePartitionEnum.project_statistic_by_org.getTableName(), projectBak.getHashIndex());

        // 插入新的统计数据
        long insertByOrgTime = System.currentTimeMillis();
        log.info(">>>>>>>>>>>>>开始批量插入学习项目统计数据，共计{}条数据", saveList.size());
        List<List<ProjectStatisticByOrg>> listList = Lists.partition(saveList, 1000);
        for (List<ProjectStatisticByOrg> list : listList) {
            projectStatisticByOrgService.saveToPartition(tablePartition, list);
        }
        log.info("<<<<<<<<<<<<批量插入学习项目统计数据完成，耗时：{}", System.currentTimeMillis() - insertByOrgTime);
        resourceTaskExecuteService.update(new LambdaUpdateWrapper<ProjectResourceTaskExecute>()
            .eq(ProjectResourceTaskExecute::getBatchId, batchId)
            .eq(ProjectResourceTaskExecute::getResourceId, projectId)
            .set(ProjectResourceTaskExecute::getExecuteStatus, 2)
            .set(ProjectResourceTaskExecute::getUpdateBy, SYSTEM)
            .set(ProjectResourceTaskExecute::getUpdateTime, new Date())
        );
        //更新缓存中的资源信息
        resourceTask.setExecuteStatus(2)
            .setUpdateBy(SYSTEM)
            .setUpdateTime(new Date());
        redisTemplate.opsForHash().put(dataKey, batchId + ":" + resourceTask.getResourceId(), resourceTask);
        // 开始汇总数据
        projectStatisticByOrgCollect(projectId, batchId, dataKey, saveList, resourceTask);
        log.info("<<<<<<<<<<<<<<学习项目-{}执行情况统计完成,当前执行批次:{}, 耗时:{}", projectId, batchId,
            System.currentTimeMillis() - startTime);
    }

    private void projectStatisticByOrgCollect(String projectId, String batchId, String dataKey,
        List<ProjectStatisticByOrg> allOrgStatisticList, ProjectResourceTaskExecute taskExecute) {
        long startTime = System.currentTimeMillis();
        log.info(">>>>>>>>>>>>>>>开始汇总学习项目{}数据", projectId);
        String hostName = null;
        try {
            hostName = InetAddress.getLocalHost().getHostName();
            // 取出需要汇总的组织
            List<OrgDTO> orgs = new ArrayList<>();
            long queryOrgTime = System.currentTimeMillis();
            log.info(">>>>>>>>>>>>>>>开始取部门数据");
            // 取部门级
            orgs.addAll(orgFeign.getOrgByDimension(1));
            // 取公司级
            orgs.addAll(orgFeign.getOrgByDimension(2));
            log.info("<<<<<<<<<<<<<<<取部门数据完成,耗时{}", System.currentTimeMillis() - queryOrgTime);

            if (CollectionUtils.isEmpty(orgs)) {
                log.info("=======没有需要汇总数据的组织=======");
                return;
            }
            List<ProjectStatisticByOrg> dataList;
            List<ProjectStatisticByOrgCollect> collectDataList = new ArrayList<>(orgs.size());
            ProjectStatisticByOrgCollect collectData;
            long circleTime = System.currentTimeMillis();
            log.info(">>>>>>>>>>>>>>>开始循环统计部门数据");
            for (OrgDTO org : orgs) {
                long time1 = System.currentTimeMillis();
                log.info(">>>>>>>>>>>>>>>开始便利当前部门下的数据");
                // 取当前组织及下级组织的统计数据
                dataList = allOrgStatisticList.stream()
                    .filter(data -> StringUtils.isNotBlank(data.getOrgLevelPath())
                        && data.getOrgLevelPath().startsWith(org.getLevelPath()))
                    .collect(Collectors.toList());
                log.info("<<<<<<<<<<<<<<便利当前部门下的数据完成, 耗时：{}", System.currentTimeMillis() - time1);
                if (CollectionUtils.isEmpty(dataList)) {
                    log.info("======学习项目-{}，在组织{}-{}下，没有可以汇总的数据======", projectId, org.getOrgName(),
                        org.getId());
                    continue;
                }
                collectData = new ProjectStatisticByOrgCollect();
                collectData.setId(StringUtil.newTimeId())
                    .setBatchId(batchId)
                    .setProjectId(projectId)
                    .setOrgId(org.getId())
                    .setOrgLevelPath(org.getLevelPath())
                    .setCreateBy(SYSTEM)
                    .setCreateTime(new Date())
                    .setUpdateBy(SYSTEM)
                    .setUpdateTime(new Date());
                // 计算任务分
                collectData.setTaskScore(dataList.stream().filter(data -> Objects.nonNull(data.getTaskScore()))
                    .mapToInt(ProjectStatisticByOrg::getTaskScore).sum());
                // 计算参加人数
                int joinNum = dataList.stream().filter(data -> Objects.nonNull(data.getJoinNum()))
                    .mapToInt(ProjectStatisticByOrg::getJoinNum).sum();
                collectData.setJoinNum(joinNum);
                // 计算下发人数
                int viewLimitNum = dataList.stream().filter(data -> Objects.nonNull(data.getViewLimitNum()))
                    .mapToInt(ProjectStatisticByOrg::getViewLimitNum).sum();
                collectData.setViewLimitNum(viewLimitNum);
                // 计算完成人数
                int finishNum = dataList.stream().filter(data -> Objects.nonNull(data.getFinishNum()))
                    .mapToInt(ProjectStatisticByOrg::getFinishNum).sum();
                collectData.setFinishNum(finishNum);
                collectData.setNotJoinNum(collectData.getViewLimitNum() - collectData.getJoinNum());
                // 计算参加率
                collectData.setJoinRate(NumberOperationUtils.getPercentStr(joinNum, viewLimitNum, 2));
                // 计算完成率
                collectData.setFinishRate(NumberOperationUtils.getPercentStr(finishNum, joinNum, 2));
                // 计算整体完成率
                collectData.setTotalFinishRate(NumberOperationUtils.getPercentStr(finishNum, viewLimitNum, 2));
                collectDataList.add(collectData);
            }
            log.info("<<<<<<<<<<<<<<循环统计部门数据完成,耗时:{}", System.currentTimeMillis() - circleTime);

            if (CollectionUtils.isEmpty(collectDataList)) {
                // 没有汇总数据
                log.info("======学习项目-{}，没有汇总数据======", projectId);
                return;
            }
            log.info(">>>>>>>>>>>>>>>>>保存学习项目-{}汇总数据开始======", projectId);
            long saveTime = System.currentTimeMillis();
            List<List<ProjectStatisticByOrgCollect>> listList = Lists.partition(collectDataList, 1000);
            for (List<ProjectStatisticByOrgCollect> list : listList) {
                projectStatisticByOrgCollectService.saveBatch(list);
            }
            log.info("<<<<<<<<<<<<<保存学习项目-{}汇总数据结束，耗时：{}======", projectId,
                System.currentTimeMillis() - saveTime);
            // 更新资源为执行已统计状态
            resourceTaskExecuteService.update(new LambdaUpdateWrapper<ProjectResourceTaskExecute>()
                .eq(ProjectResourceTaskExecute::getBatchId, batchId)
                .eq(ProjectResourceTaskExecute::getResourceId, projectId)
                .set(ProjectResourceTaskExecute::getIsCollect, 1)
                .set(ProjectResourceTaskExecute::getCollectTime, new Date())
            );
            //更新缓存中的资源信息
            taskExecute.setIsCollect(1)
                .setCollectTime(new Date());
            redisTemplate.opsForHash().put(dataKey, batchId + ":" + taskExecute.getResourceId(), taskExecute);
        } catch (Exception e) {
            String collectLog =
                "hostName:" + hostName + ",logTime:" + DateUtils.format(new Date()) + ",error:" + e.toString();
            log.error("批次号:{},学习项目资源-{}汇总失败, {}", batchId, projectId, collectLog);
            // 更新资源汇总失败日志
            resourceTaskExecuteService.update(new LambdaUpdateWrapper<ProjectResourceTaskExecute>()
                .eq(ProjectResourceTaskExecute::getBatchId, batchId)
                .eq(ProjectResourceTaskExecute::getResourceId, projectId)
                .set(ProjectResourceTaskExecute::getCollectLog, collectLog)
                .set(ProjectResourceTaskExecute::getCollectTime, new Date())
            );
            //更新缓存中的资源信息
            taskExecute
                .setCollectLog(collectLog)
                .setCollectTime(new Date());
            redisTemplate.opsForHash().put(dataKey, batchId + ":" + taskExecute.getResourceId(), taskExecute);
        }
        log.info("<<<<<<<<<<<<<<<<<<<<<汇总学习项目{}数据,耗时:{}", projectId,
            System.currentTimeMillis() - startTime);
    }


    /**
     * 备份数据
     *
     * @param sourceDataSql
     * @param sourceTable
     * @param targetTable
     * @param pageSize
     * @return
     */
    @SuppressWarnings("java:S2438")
    private void backupRecordData(ExecutorService executeThreadPool, String sourceDataSql, String sourceTable,
        String targetTable, int pageSize, String type) {
        BackUpDataThread thread = new BackUpDataThread(sourceDataSql, sourceTable, targetTable, pageSize, type);
        executeThreadPool.submit(thread);
    }

    /**
     * 备份数据线程
     *
     * @return
     */
    public class BackUpDataThread extends Thread {

        private String sourceDataSql;

        private String sourceTable;

        private String targetTable;

        private Integer pageSize;

        private String type;


        public BackUpDataThread(String sourceDataSql, String sourceTable, String targetTable, Integer pageSize,
            String type) {
            this.sourceDataSql = sourceDataSql;
            this.sourceTable = sourceTable;
            this.targetTable = targetTable;
            this.pageSize = pageSize;
            this.type = type;
        }


        @Override
        public void run() {
            backupRecordDataExecute(sourceDataSql, sourceTable, targetTable, pageSize, type);
        }
    }

    /**
     * 备份数据--单纯插入操作
     *
     * @param sourceDataSql
     * @param sourceTable
     * @param targetTable
     * @param pageSize
     * @return
     */
    @SuppressWarnings("java:S3776")
    private void backupRecordDataExecute(String sourceDataSql, String sourceTable, String targetTable, int pageSize,
        String type) {
        long backupRecordDataStart = System.currentTimeMillis();
        log.info(">>>>>>>>>>>backupRecordData start,sourceTable:{},targetTable:{}", sourceTable, targetTable);
        String DROP_INDEX = "drop index ";
        String CREATE_INDEX = "create index  ";
        if ("ALL".equals(type)) {
            dropIndex(targetTable, DROP_INDEX);
        }

        log.info(">>>>>>>>>>>backupRecordData getTotalCount start,sourceTable:{},targetTable:{}", sourceTable,
            targetTable);
        Integer totalCount = baseMapper.getTotalCount(sourceDataSql);
        log.info("<<<<<<<<<<<backupRecordData getTotalCount end,sourceTable:{},targetTable:{},totalCount:{},耗时:{}",
            sourceTable, targetTable, totalCount, (System.currentTimeMillis() - backupRecordDataStart));
        try {

            long pages = calculatePages(totalCount, pageSize);
            log.info(">>>>>>>>>>>backupRecordData insertBackupData start, pages:{},pageSize:{}", pages, pageSize);
            baseMapper.addTransferLog("begin " + targetTable, new Date(), totalCount);
//            baseMapper.addTransferLog("begin " + sourceDataSql, new Date(), totalCount);
            Integer insertCount = 0;
            for (long pageIndex = 1L; pageIndex <= pages; pageIndex++) {
                long startIndex = (pageIndex - 1) * pageSize;
                List<Map<String, Object>> recordList = baseMapper.getBackupData(sourceDataSql, startIndex, pageSize);
                if (!org.springframework.util.CollectionUtils.isEmpty(recordList)) {
                    //设置表头
                    Set<String> tableFields = recordList.get(0).keySet();
                    //设置数据
                    LinkedList<LinkedList<Object>> rowDatas = new LinkedList<>();
                    for (Map<String, Object> item : recordList) {
                        LinkedList<Object> rowData = new LinkedList<>();
                        for (String tableField : tableFields) {
                            rowData.add(item.get(tableField));
                        }
                        rowDatas.add(rowData);
                    }
                    insertCount = insertCount + rowDatas.size();
                    String txt = "backupRecordDataExecute rowData: " + rowDatas.size() + ", index:" + pageIndex + ","
                        + " targetTable:" + targetTable + "";
                    if (targetTable.indexOf(PROJECT_PROGRESS_BAK) >= 0
                        || targetTable.indexOf(TASK_PROGRESS_BAK) >= 0) {
                        baseMapper.addTransferLog(txt, new Date(), insertCount);
                    }
                    log.info("backupRecordDataExecute rowData:{},table:{},index:{}", rowDatas.size(), targetTable,
                        pageIndex);
                    if (targetTable.indexOf(PROJECT_PROGRESS_BAK) >= 0
                        || targetTable.indexOf(TASK_PROGRESS_BAK) >= 0) {
                        baseMapper.replaceBackupData(targetTable, tableFields, rowDatas);
                    } else {
                        baseMapper.insertBackupData(targetTable, tableFields, rowDatas);
                    }
                } else {
                    String txt =
                        "backupRecordDataExecute recordList:没有查到数据, index:" + pageIndex + "," + " targetTable:"
                            + targetTable + "";
                    if (targetTable.indexOf(PROJECT_PROGRESS_BAK) >= 0
                        || targetTable.indexOf(TASK_PROGRESS_BAK) >= 0) {
                        baseMapper.addTransferLog(txt, new Date(), insertCount);
                    }
                    log.info("backupRecordDataExecute recordList:没有查到数据 table:{},index:{}", targetTable,
                        pageIndex);
                }
            }
            baseMapper.addTransferLog("end " + targetTable, new Date(), totalCount);
            log.info("<<<<<<<<<<<backupRecordData insertBackupData end, pages:{},pageSize:{}", pages, pageSize);
        } catch (Exception e) {
            log.error("<<<<<<<<<<<backupRecordData sourceTable:{},targetTable:{},error:{}", sourceTable, targetTable,
                e.toString());
        }
        if ("ALL".equals(type)) {
            dealWithCreateIndex(targetTable, CREATE_INDEX);
        }
        log.info("<<<<<<<<<<<backupRecordData end,sourceTable:{},targetTable:{},耗时:{}", sourceTable, targetTable,
            (System.currentTimeMillis() - backupRecordDataStart));

    }


    private void dealWithCreateIndex(String targetTable, String CREATE_INDEX) {
        if (targetTable.indexOf(PROJECT_PROGRESS_BAK) >= 0) {
            baseMapper.addTransferLog(CREATE_INDEX + targetTable, new Date(), 0);
            createProjectIndex(targetTable);
        }
        if (targetTable.indexOf(TASK_PROGRESS_BAK) >= 0) {
            baseMapper.addTransferLog(CREATE_INDEX + targetTable, new Date(), 0);
            createTaskIndex(targetTable);
        }
        if (targetTable.indexOf(W_VIEW_LIMIT_USER_BAK) >= 0) {
            baseMapper.addTransferLog(CREATE_INDEX + targetTable, new Date(), 0);
            createLimitIndex(targetTable);
        }
    }

    private void dropIndex(String targetTable, String DROP_INDEX) {
        if (targetTable.indexOf(PROJECT_PROGRESS_BAK) >= 0) {
            baseMapper.addTransferLog(DROP_INDEX + targetTable, new Date(), 0);
            dropProjectIndex(targetTable);
        }
        if (targetTable.indexOf(TASK_PROGRESS_BAK) >= 0) {
            baseMapper.addTransferLog(DROP_INDEX + targetTable, new Date(), 0);
            dropTaskIndex(targetTable);
        }
        if (targetTable.indexOf(W_VIEW_LIMIT_USER_BAK) >= 0) {
            baseMapper.addTransferLog(DROP_INDEX + targetTable, new Date(), 0);
            dropLimitIndex(targetTable);
        }
    }

    private void createProjectIndex(String targetTable) {
        baseMapper.createProjectIndex(targetTable);
    }

    private void createTaskIndex(String targetTable) {
        baseMapper.createTaskIndex(targetTable);
    }

    private void dropProjectIndex(String targetTable) {
        baseMapper.dropProjectIndex(targetTable);
    }

    private void dropTaskIndex(String targetTable) {
        baseMapper.dropTaskIndex(targetTable);
    }

    private void createLimitIndex(String targetTable) {
        baseMapper.createLimitIndex(targetTable);
    }

    private void dropLimitIndex(String targetTable) {
        baseMapper.dropLimitIndex(targetTable);
    }

    /**
     * 创建备份表
     *
     * @param sourceTable
     * @param targetTable
     * @return
     */
    private void createBackupTable(String sourceTable, String targetTable) {
        baseMapper.createBackupTable(sourceTable, targetTable);
    }


    private long calculatePages(long totalCount, Integer pageSize) {
        if (totalCount == 0) {
            return 0L;
        }
        long pages = totalCount / pageSize;
        if (totalCount % pageSize != 0) {
            pages++;
        }
        return pages;
    }

    /**
     * 备份基础数据
     * 备份组织表，备份用户表，备份考试数据,50个分区，备份下发策略表,100个分区
     */
    public void backupBaseData(){
        //备份组织表
        backUpSysOrgData();
        //备份组织表
        backUpSysUserData();
        //项目表
        backUpProjectData();
        //备份组织表
        backUpResourceViewLimitData();
        //项目任务表
        backUpProjectTask();

    }

    /**
     * 备份组织表
     */
    private void backUpSysOrgData() {
        baseMapper.addTransferLog("sys_org_bak begin", new Date(),null);
        //清空备份表数据
        baseMapper.cleanSysOrgData();
        //备份组织数据
        baseMapper.insertSysOrgData();
        baseMapper.addTransferLog("sys_org_bak end", new Date(),null);
    }

    /**
     * 备份用户表
     */
    private void backUpSysUserData() {
        baseMapper.addTransferLog("sys_user_bak begin", new Date(),null);
        //清空备份表数据
        baseMapper.cleanSysUserData();
        //备份组织数据
        baseMapper.insertSysUserData();
        baseMapper.addTransferLog("sys_user_bak end", new Date(),null);
    }

    /**
     * 备份项目(帶索引的)
     */
    private void backUpProjectData() {
        baseMapper.addTransferLog("project_bak begin", new Date(), null);
        //清空备份表数据
        baseMapper.cleanProjectData();
        //备份组织数据
        baseMapper.insertProjectData();
        baseMapper.addTransferLog("project_bak end", new Date(), null);
    }

    /**
     * 备份下发人员表
     */
    private void backUpResourceViewLimitData() {
        baseMapper.addTransferLog("w_resource_view_limit_bak begin", new Date(),null);
        //清空备份表数据
        baseMapper.cleanResourceViewLimitData();
        //备份组织数据
        baseMapper.insertResourceViewLimitData();
        baseMapper.addTransferLog("w_resource_view_limit_bak end", new Date(),null);
    }

    /**
     * 备份项目任务表
     */
    private void backUpProjectTask(){
        baseMapper.addTransferLog("project_task_bak begin", new Date(),null);
        //清空备份表数据
        baseMapper.cleanProjectTaskData();
        //备份任务数据
        baseMapper.insertProjectTaskData();
        baseMapper.addTransferLog("project_task_bak end", new Date(),null);
    }


    /**
     * @param query 用户项目激励获取记录查询对象
     */
    @Override
    public PageInfo<UserExcitationRecordDTO> findUserProjectExcitationRecordByPage(
        UserProjectExcitationRecordQuery query) {

        // 获取项目所有激励目标资源ID列表
        List<String> projectResourceIdList = getProjectResourceIdList(query.getProjectId(), false);

        // 无激励目标资源，直接返回 空
        if (projectResourceIdList.isEmpty()) {
            return PageInfo.emptyPageInfo();
        }

        // 补充参数
        query.setTargetIds(projectResourceIdList);

        // 获取激励列表
        return excitationFeign.getUserProjectExcitationRecord(query);
    }


    public void exportUserProjectExcitationRecord(UserProjectExcitationRecordQuery query) {
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IProjectStatisticService, UserExcitationRecordDTO>(query) {

            @Override
            protected IProjectStatisticService getBean() {
                return SpringUtil.getBean(PROJECT_STATISTIC_SERVICE, IProjectStatisticService.class);
            }

            @Override
            protected PageInfo<UserExcitationRecordDTO> getPageInfo() {
                return getBean().findUserProjectExcitationRecordByPage(query);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.ProjectUserProjectExcitationRecord;
            }

            @Override
            public String getFileName() {
                Project project = projectService.getById(query.getProjectId());
                return project.getProName() + "_" + ExportFileNameEnum.ProjectUserProjectExcitationRecord.getType();
            }
        };

        exportComponent.exportRecord(exportDataDTO);
    }


    @Override
    public PageInfo<UserCoursewareStudyDetailDTO> findUserProjectCoursewareStudyDetailByPage(
        UserProjectCoursewareStudyDetailQuery query) {

        // 分页查询课程任务列表
        PageInfo<UserCoursewareStudyDetailDTO> pageInfo = PageMethod
            .startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> baseMapper.getUserProjectCoursewareStudyDetail(query));

        if (pageInfo.getList().isEmpty()) {
            return PageInfo.emptyPageInfo();
        }

        // 获取课程ID列表
        List<UserCoursewareStudyDetailDTO> list = pageInfo.getList();
        List<String> courseIds = list.stream().map(UserCoursewareStudyDetailDTO::getCourseId)
            .collect(Collectors.toList());
        if (courseIds.isEmpty()) {
            return PageInfo.emptyPageInfo();
        }

        // 获取用户课程学习信息
        UserCourseStudyInfoQuery userCourseStudyInfoQuery = new UserCourseStudyInfoQuery();
        userCourseStudyInfoQuery.setUserId(query.getUserId());
        userCourseStudyInfoQuery.setIdList(courseIds);
        List<UserCourseStudyInfoDTO> userCourseStudyInfo = courseFeign.getUserCourseStudyInfo(userCourseStudyInfoQuery);
        Map<String, UserCourseStudyInfoDTO> courseStudyInfoMap = userCourseStudyInfo.stream()
            .collect(Collectors.toMap(UserCourseStudyInfoDTO::getId, dto -> dto, (existing, replacement) -> existing));

        list.forEach(dto -> {

            UserCourseStudyInfoDTO info = courseStudyInfoMap.get(dto.getCourseId());
            if (info != null) {

                // 用户已学时长（不足0.01小时按0计算，否则四舍五入）
                BigDecimal hours = (BigDecimal.valueOf(info.getDuration().doubleValue() / 3600));
                if (hours.compareTo(new BigDecimal("0.01")) < 0) {
                    hours = new BigDecimal("0.00");
                }
                hours = hours.setScale(2, RoundingMode.HALF_UP);
                dto.setDuration(hours);

                // 课程累计时长（不足0.01小时按0计算，否则四舍五入）
                BigDecimal totalHours = BigDecimal.valueOf(info.getTotalDuration().doubleValue() / 3600);
                if (totalHours.compareTo(new BigDecimal("0.01")) < 0) {
                    totalHours = new BigDecimal("0.00");
                }
                totalHours = totalHours.setScale(2, RoundingMode.HALF_UP);
                dto.setTotalDuration(totalHours);
            }

        });
        return pageInfo;
    }


    @Override
    public void exportUserProjectCoursewareStudyDetail(UserProjectCoursewareStudyDetailQuery query) {
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IProjectStatisticService, UserCoursewareStudyDetailDTO>(query) {

            @Override
            protected IProjectStatisticService getBean() {
                return SpringUtil.getBean(PROJECT_STATISTIC_SERVICE, IProjectStatisticService.class);
            }

            @Override
            protected PageInfo<UserCoursewareStudyDetailDTO> getPageInfo() {
                return getBean().findUserProjectCoursewareStudyDetailByPage(query);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.ProjectUserProjectCoursewareStudyDetail;
            }

            @Override
            public String getFileName() {
                Project project = projectService.getById(query.getProjectId());
                return project.getProName() + "_" + ExportFileNameEnum.ProjectUserProjectCoursewareStudyDetail.getType();
            }

            @Override
            protected void afterCreateBeanMap(Map<String, Object> map) {

                Object taskName = map.get(TASK_NAME);
                Object isRequired = map.get(IS_REQUIRED);
                if (isRequired != null && isRequired.equals(1)) {
                    map.put(TASK_NAME, "[" + I18nUtil.getDefaultMessage("必修") + "]" + taskName);
                } else if (isRequired != null && isRequired.equals(0)) {
                    map.put(TASK_NAME, "[" + I18nUtil.getDefaultMessage("选修") + "]" + taskName);
                }

            }
        };

        exportComponent.exportRecord(exportDataDTO);
    }


}
