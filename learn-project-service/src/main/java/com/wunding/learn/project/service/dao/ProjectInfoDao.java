package com.wunding.learn.project.service.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.project.service.model.Project;

/**
 * <AUTHOR>
 */
public interface ProjectInfoDao extends IService<Project> {

    /**
     * 保存项目
     *
     * @param project
     */
    void saveProject(Project project);



    /**
     * 更新项目
     * @param project
     */
    void updateProject(Project project);


    /**
     * 删除项目
     * @param project
     */
    void delProject(Project project);

    /**
     * 发布
     * @param project
     */
    void publishProject(Project project);

    /**
     * 取消发布
     * @param project
     */
    void unPublishProject(Project project);

    /**
     * 更新删除状态
     * @param project
     */
    void updateQuickProjectIsDelById(Project project);

    /**
     * 清空学习项目可能需要设置为null的信息
     * @param project
     */
    void updateProjectNullField(Project project);
}
