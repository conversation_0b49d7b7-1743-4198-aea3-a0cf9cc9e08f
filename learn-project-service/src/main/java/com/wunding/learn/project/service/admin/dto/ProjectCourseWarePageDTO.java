package com.wunding.learn.project.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @program: mlearn
 * @description: <p>辅导模板列表对象
 * @author: cdl
 * @create: 2022-07-12 10:05
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(name = "ProjectCourseWarePageDTO", description = "学习项目课件列表对象")
public class ProjectCourseWarePageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 课件ID
     */
    @Schema(description = "课件ID")
    private String id;

    /**
     * 课件名称
     */
    @Schema(description = "课件名称")
    private String courseWareName;
    

}
