package com.wunding.learn.project.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.project.service.admin.dto.PriceDetailDTO;
import com.wunding.learn.project.service.admin.dto.PriceSaveDTO;
import com.wunding.learn.project.service.model.Cost;

/**
 * <p> 费用表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">赖卓成</a>
 * @since 2022-07-19
 */
public interface ICostService extends IService<Cost> {

    /**
     * 费用明细
     *
     * @param id 项目id
     * @return
     */
    PriceDetailDTO detail(String id);

    /**
     * 添加/编辑费用明细
     *
     * @param priceSaveDTO
     */
    void saveOrUpdate(PriceSaveDTO priceSaveDTO);
}
