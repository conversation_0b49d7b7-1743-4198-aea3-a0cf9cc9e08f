package com.wunding.learn.project.service.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 报名名单表
 *
 * <AUTHOR> href="mailto:<EMAIL>">cdl</a>
 * @since 2022-08-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("project_apply_user")
@Schema(name = "ApplyUser对象", description = "报名名单表")
public class ApplyUser implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键id
     */
    @Schema(description = "主键id")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;


    /**
     * 报名id
     */
    @Schema(description = "报名id")
    @TableField("apply_id")
    private String applyId;


    /**
     * 用户id
     */
    @Schema(description = "用户id")
    @TableField("user_id")
    private String userId;


    /**
     * 报名时间
     */
    @Schema(description = "报名时间")
    @TableField("apply_time")
    private Date applyTime;


    /**
     * 报名状态：0 未审核 1 审核通过 2 审核未通过
     */
    @Schema(description = "报名状态：0 未审核 1 审核通过 2 审核未通过 3未报名")
    @TableField("apply_state")
    private Integer applyState;


    /**
     * 审核人
     */
    @Schema(description = "审核人")
    @TableField("audit_by")
    private String auditBy;


    /**
     * 审核时间
     */
    @Schema(description = "审核时间")
    @TableField("audit_time")
    private Date auditTime;
    
    /**
     * 审核备注
     */
    @Schema(description = "审核备注")
    @TableField("remark")
    private String remark;
    
    
    /**
     * 报名类型： 0 自报名 1 代报名
     */
    @Schema(description = "报名类型： 0 自报名 1 代报名")
    @TableField("apply_type")
    private Integer applyType;

    /**
     * 代报名用户id
     */
    @Schema(description = "代报名用户id")
    @TableField("agent_user_id")
    private String agentUserId;

    /**
     * 代报名单位联系人id
     */
    @Schema(description = "代报名单位联系人id")
    @TableField("agent_invoice_user_id")
    private String agentInvoiceUserId;

}
