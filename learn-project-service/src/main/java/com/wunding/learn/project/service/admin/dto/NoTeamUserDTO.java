package com.wunding.learn.project.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;

/**
 * @Author: aixinrong
 * @Date: 2022/7/12 14:29
 */
@Data
@Schema(name = "NoTeamUserDTO", description = "未组队人员对象")
public class NoTeamUserDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "用户名")
    private String fullName;

    @Schema(description = "账号")
    private String loginName;

    @Schema(description = "部门")
    private String orgName;

    @Schema(description = "部门全称 orgPath")
    private String orgPath;

    @Schema(description = "团队id")
    private String teamId;

    @Schema(description = "是否锁定 0不锁定 1锁定")
    private Integer isLock;

    /**
     * 面授项目采用默认报名表单 使用这个字段
     */
    @Schema(description = "行业名称")
    private String professionName;

    /**
     * 公司/机构名称
     */
    @Schema(description = "公司/机构名称")
    private String companyName;
}
