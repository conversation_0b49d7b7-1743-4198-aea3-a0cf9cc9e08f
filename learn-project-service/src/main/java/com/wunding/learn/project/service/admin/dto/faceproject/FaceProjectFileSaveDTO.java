package com.wunding.learn.project.service.admin.dto.faceproject;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.hibernate.validator.constraints.Length;


/**
 * <p> face项目文件保存dto
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2024-03-20
 */
@Data
@Schema(name = "FaceProjectFileSaveDTO", description = "文件保存对象")
public class FaceProjectFileSaveDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "项目id")
    @NotBlank(message = "项目id不可为空")
    private String proId;

    @Schema(description = "目录id")
    @NotBlank(message = "目录id不可为空")
    private String directoryId;

    @Schema(description = "文件标题")
    @NotBlank(message = "文件标题不可为空")
    @Length(max = 80, message = "文件标题长度不能超过80")
    private String title;

    @Schema(description = "文件名称")
    @NotBlank(message = "文件名称不可为空")
    @Length(max = 80, message = "文件名称长度不能超过80")
    private String fileName;

    @Schema(description = "文件临时目录地址")
    @NotBlank(message = "文件地址不可为空")
    private String filePath;

    @Schema(description = "文件类型，上传文件时必填，从课件文件上传接口获取")
    @NotBlank(message = "文件类型不可为空")
    private String fileType;

    @Schema(description = "文件MIME")
    @NotBlank(message = "文件MIME不可为空")
    private String mime;
}
