package com.wunding.learn.project.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.common.dto.StatisticPersonRankDTO;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import com.wunding.learn.project.service.admin.dto.ProjectAnalysisByOrgDTO;
import com.wunding.learn.project.service.admin.dto.StatisticOrgCompleteDTO;
import com.wunding.learn.project.service.admin.dto.StatisticOrgCompleteUserDetailDTO;
import com.wunding.learn.project.service.admin.dto.StatisticOrgLearnRankDTO;
import com.wunding.learn.project.service.admin.dto.StatisticProjectCompletionDTO;
import com.wunding.learn.project.service.admin.dto.StatisticProjectOrgDTO;
import com.wunding.learn.project.service.admin.dto.StatisticTaskDTO;
import com.wunding.learn.project.service.admin.dto.StatisticTaskDetailDTO;
import com.wunding.learn.project.service.admin.dto.StatisticTeamCompleteDTO;
import com.wunding.learn.project.service.admin.dto.StatisticTeamLearnRankDTO;
import com.wunding.learn.project.service.admin.dto.UserCoursewareStudyDetailDTO;
import com.wunding.learn.project.service.admin.query.ProjectAnalysisByOrgQuery;
import com.wunding.learn.project.service.admin.query.StatisticOrgCompleteQuery;
import com.wunding.learn.project.service.admin.query.StatisticOrgCompleteUserDetailQuery;
import com.wunding.learn.project.service.admin.query.StatisticOrgLearnRankQuery;
import com.wunding.learn.project.service.admin.query.StatisticPersonRankQuery;
import com.wunding.learn.project.service.admin.query.StatisticProjectCompletionQuery;
import com.wunding.learn.project.service.admin.query.StatisticProjectOrgQuery;
import com.wunding.learn.project.service.admin.query.StatisticTaskDetailQuery;
import com.wunding.learn.project.service.admin.query.StatisticTaskQuery;
import com.wunding.learn.project.service.admin.query.StatisticTeamCompleteQuery;
import com.wunding.learn.project.service.admin.query.StatisticTeamCompleteUserDetailQuery;
import com.wunding.learn.project.service.admin.query.StatisticTeamLearnRankQuery;
import com.wunding.learn.project.service.admin.query.UserProjectCoursewareStudyDetailQuery;
import com.wunding.learn.project.service.model.Project;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

/**
 * <p>
 * 项目统计
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">wgr</a>
 * @date 2022/9/22 19:15
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")
})
public interface ProjectStatisticMapper extends BaseMapper<Project> {

    /**
     * 查询部门完成情况统计
     *
     * @param statisticOrgCompleteQuery
     * @return
     */
    List<StatisticOrgCompleteDTO> selectOrgStatisticCompleteByPage(StatisticOrgCompleteQuery statisticOrgCompleteQuery);

    /**
     * 查询团队完成情况统计
     *
     * @param statisticTeamCompleteQuery
     * @return
     */
    List<StatisticTeamCompleteDTO> selectTeamStatisticCompleteByPage(
        StatisticTeamCompleteQuery statisticTeamCompleteQuery);


    /**
     * 查询学习项目，任务完成情况统计
     *
     * @param statisticTaskQuery
     * @return
     */
    List<StatisticTaskDTO> selectTaskStatisticByPage(StatisticTaskQuery statisticTaskQuery);


    /**
     * 查询明细统计
     *
     * @param statisticTaskDetailQuery
     * @return
     */
    List<StatisticTaskDetailDTO> selectTaskDetailByPage(StatisticTaskDetailQuery statisticTaskDetailQuery);


    /**
     * 查询学习项目  个人学习排名统计
     *
     * @param statisticPersonRankQuery
     * @return
     */
    List<StatisticPersonRankDTO> selectPersonRankByPage(
        @Param("params") StatisticPersonRankQuery statisticPersonRankQuery);


    /**
     * 查询团队学习排名统计
     *
     * @param statisticTeamLearnRankQuery
     * @return
     */
    List<StatisticTeamLearnRankDTO> selectTeamLearnRankByPage(StatisticTeamLearnRankQuery statisticTeamLearnRankQuery);


    /**
     * 查询部门学习排名统计
     *
     * @param statisticTeamOrgRankQuery
     * @return
     */
    List<StatisticOrgLearnRankDTO> selectOrgLearnRankByPage(StatisticOrgLearnRankQuery statisticTeamOrgRankQuery);

    /**
     * 查询培训项目的某个一个学习项目评估，用来获取评估模板
     * @param trainId
     * @return
     */
    String selectOneProjectEvaIdByTrainId(@Param("trainId") String trainId);

    /**
    * 获取培训项目的所有评估
    * @param statisticProjectOrgQuery
    * @return
    */
    Set<String> getAllEvaIdsByTrainId(StatisticProjectOrgQuery statisticProjectOrgQuery);

    /**
     * 查询培训项目的班级评估
     * @param statisticProjectOrgQuery
     * @return
     */
    List<StatisticProjectOrgDTO> selectEvaIdListByTrainId(StatisticProjectOrgQuery statisticProjectOrgQuery);


    List<ProjectAnalysisByOrgDTO> stateAnalysisByOrg(ProjectAnalysisByOrgQuery query);

    /**
     * 添加备份日志
     *
     * @param
     * @return
     */
    void addTransferLog(String remark, Date transferTime,Integer totalCount);

    /**
     * 清楚备份日志
     * @param
     * @return
     */
    void cleanTransferLog();


    /**
     * 获取数据总数
     * @param sourceDataSql
     * @return
     */
    Integer getTotalCount(String sourceDataSql);



    /**
     * 获取备份数据
     * @param sourceDataSql
     * @param startIndex
     * @param pageSize
     * @return
     */
    List<Map<String,Object>> getBackupData(String sourceDataSql, long startIndex, int pageSize);

    /**
     * 插入备份数据
     * @param rowDatas
     * @return
     */
    void insertBackupData(String targetTable, Set<String> tableFields, LinkedList<LinkedList<Object>> rowDatas);


    void replaceBackupData(String targetTable, Set<String> tableFields, LinkedList<LinkedList<Object>> rowDatas);
    /**
     * 创建备份表
     * @param sourceTable
     * @param targetTable
     * @return
     */
    void createBackupTable(String sourceTable, String targetTable);


    void createProjectIndex( String targetTable);

    void dropProjectIndex(  String targetTable);

    void createTaskIndex(String targetTable);

    void dropTaskIndex(String targetTable);

    void createLimitIndex(String targetTable);

    void dropLimitIndex(String targetTable);

    Long cleanPartData(String cleanDataSql);

    /**
     * 清空组织备份表数据
     */
    void cleanSysOrgData();

    /**
     * 添加组织备份表数据
     */
    void insertSysOrgData();


    /**
     * 清空用戶备份表数据
     */
    void cleanSysUserData();

    /**
     * 添加用戶备份表数据
     */
    void insertSysUserData();

    /**
     * 清空项目备份表数据
     */
    void cleanProjectData();

    /**
     * 添加项目备份表数据
     */
    void insertProjectData();

    /**
     * 清空备份下发人员表数据
     */
    void cleanResourceViewLimitData();

    /**
     * 添加备份下发人员表数据
     */
    void insertResourceViewLimitData();


    /**
     * 清空备份下发人员表数据
     */
    void cleanProjectTaskData();

    /**
     * 添加备份下发人员表数据
     */
    void insertProjectTaskData();

    /**
     * 查询部门完成情况统计用户详情-部门完成情况
     *
     * @param query
     * @return
     */
    List<StatisticOrgCompleteUserDetailDTO> selectOrgStatisticCompleteUserDetailByPage(StatisticOrgCompleteUserDetailQuery query);

    /**
     * 查询部门完成情况统计用户详情-团队完成情况
     *
     * @param query
     * @return
     */
    List<StatisticOrgCompleteUserDetailDTO> selectTeamStatisticCompleteUserDetailByPage(StatisticTeamCompleteUserDetailQuery query);

    String getDate(String sourceDataSql);

    /**
     * 项目完成情况统计
     *
     * @param query 项目完成情况统计查询对象
     * @return 项目完成情况统计
     */
    List<StatisticProjectCompletionDTO> findProjectCompletionStatisticByPage(StatisticProjectCompletionQuery query);

    /**
     * 用户学习项目课时统计
     *
     * @param query 查询参数
     * @return 用户学习课件详情
     */
    List<UserCoursewareStudyDetailDTO> getUserProjectCoursewareStudyDetail(UserProjectCoursewareStudyDetailQuery query);
}
