package com.wunding.learn.project.service.service.impl;

import static com.wunding.learn.common.util.string.StringUtil.newId;

import com.alibaba.ttl.threadpool.TtlExecutors;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Sets;
import com.wunding.learn.common.aop.log.annotation.Log;
import com.wunding.learn.common.aop.log.annotation.Log.Type;
import com.wunding.learn.common.bean.BaseEntity;
import com.wunding.learn.common.constant.excitation.ExcitationErrorNoEnum;
import com.wunding.learn.common.constant.file.FileErrorNoEnum;
import com.wunding.learn.common.constant.other.AvailableEnum;
import com.wunding.learn.common.constant.other.CommonConstants;
import com.wunding.learn.common.constant.other.ErrorNoEnum;
import com.wunding.learn.common.constant.other.JudgeEnum;
import com.wunding.learn.common.constant.other.PublishEnum;
import com.wunding.learn.common.constant.project.ProjectErrorNoEnum;
import com.wunding.learn.common.constant.tenant.TenantRedisKeyConstant;
import com.wunding.learn.common.constant.user.UserErrorNoEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.dto.ImportResultDTO;
import com.wunding.learn.common.dto.PlanStatisticSourceDataSaveDTO;
import com.wunding.learn.common.dto.PublishDTO;
import com.wunding.learn.common.dto.ResourceBaseDTO;
import com.wunding.learn.common.dto.ResourceRecordSyncDTO;
import com.wunding.learn.common.dto.ResourceSyncDTO;
import com.wunding.learn.common.enums.ResourceTypeEnum;
import com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum;
import com.wunding.learn.common.enums.excitation.ExcitationEventEnum;
import com.wunding.learn.common.enums.excitation.ExcitationTypeEnum;
import com.wunding.learn.common.enums.market.FirstInfoContentEnum;
import com.wunding.learn.common.enums.other.CategoryTypeEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.other.OperationEnum;
import com.wunding.learn.common.enums.other.PublishStatusEnum;
import com.wunding.learn.common.enums.other.ResourceTypeCodeEnum;
import com.wunding.learn.common.enums.other.RouterVisitEnum;
import com.wunding.learn.common.enums.other.SystemConfigCodeEnum;
import com.wunding.learn.common.enums.other.TradeTypeEnum;
import com.wunding.learn.common.enums.project.ProjectAppType;
import com.wunding.learn.common.enums.push.PushType;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.i18n.util.I18nUtil;
import com.wunding.learn.common.interceptor.ProjectRequestRepeatIntercept;
import com.wunding.learn.common.mq.dto.ExcitationMQEventDTO;
import com.wunding.learn.common.mq.dto.ResourceConfigInitDTO;
import com.wunding.learn.common.mq.event.HomeRouterVisitEvent;
import com.wunding.learn.common.mq.event.InventoryResourceChangeEvent;
import com.wunding.learn.common.mq.event.InventoryResourceUpdateChangeEvent;
import com.wunding.learn.common.mq.event.ResourceChangeEvent;
import com.wunding.learn.common.mq.event.ResourceOperateEvent;
import com.wunding.learn.common.mq.event.ResourceRecordSyncEvent;
import com.wunding.learn.common.mq.event.ResourceSyncEvent;
import com.wunding.learn.common.mq.event.excitation.ExcitationInitMqEvent;
import com.wunding.learn.common.mq.event.excitation.ExcitationMQEvent;
import com.wunding.learn.common.mq.event.market.FirstInfoViewLimitChangeEvent;
import com.wunding.learn.common.mq.event.project.ActivityStatusChangeEvent;
import com.wunding.learn.common.mq.service.impl.RabbitMqProducer;
import com.wunding.learn.common.query.ResourceBaseQuery;
import com.wunding.learn.common.share.dto.PosterShareClientDTO;
import com.wunding.learn.common.share.dto.PosterShareResourceDTO;
import com.wunding.learn.common.share.service.IPosterShareService;
import com.wunding.learn.common.util.bean.BeanListUtils;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.date.DateHelper;
import com.wunding.learn.common.util.date.DateUtil;
import com.wunding.learn.common.util.excel.ExcelCheckMessage;
import com.wunding.learn.common.util.http.WebUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.common.viewlimit.constant.LimitTable;
import com.wunding.learn.common.viewlimit.service.IResourceViewLimitService;
import com.wunding.learn.course.api.dto.CourseInfoDTO;
import com.wunding.learn.course.api.service.CourseFeign;
import com.wunding.learn.course.api.service.CourseWareFeign;
import com.wunding.learn.evaluation.api.dto.EvalDTO;
import com.wunding.learn.evaluation.api.dto.EvaluationLimitViewUpdateDTO;
import com.wunding.learn.evaluation.api.service.EvaluationFeign;
import com.wunding.learn.evaluation.api.service.EvaluationReplyFeign;
import com.wunding.learn.exam.api.dto.ExamInfoDTO;
import com.wunding.learn.exam.api.service.ExamFeign;
import com.wunding.learn.excitation.api.dto.UserExcitationReduceDTO;
import com.wunding.learn.excitation.api.dto.UserExcitationTradeBaseDTO;
import com.wunding.learn.excitation.api.service.ExcitationFeign;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.constant.FileBizType;
import com.wunding.learn.file.api.constant.ImageBizType;
import com.wunding.learn.file.api.dto.AbstractExportDataDTO;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.file.api.dto.ImportDataDTO;
import com.wunding.learn.file.api.dto.NamePath;
import com.wunding.learn.file.api.service.FileFeign;
import com.wunding.learn.file.api.service.ImportDataFeign;
import com.wunding.learn.forum.api.dto.ForumSectionLimitViewUpdateDTO;
import com.wunding.learn.forum.api.dto.ForumSectionSaveDTO;
import com.wunding.learn.forum.api.service.ForumFeign;
import com.wunding.learn.lecturer.api.dto.EditLecturerExaminationDTO;
import com.wunding.learn.lecturer.api.dto.ExaminationLeaderUpdateDTO;
import com.wunding.learn.lecturer.api.dto.LecturerCategoryDTO;
import com.wunding.learn.lecturer.api.dto.LecturerDTO;
import com.wunding.learn.lecturer.api.service.LecturerExaminationFeign;
import com.wunding.learn.lecturer.api.service.LecturerFeign;
import com.wunding.learn.market.api.dto.ProjectSignTaskDTO;
import com.wunding.learn.market.api.service.SignFeign;
import com.wunding.learn.plan.api.service.FormTemplateFeign;
import com.wunding.learn.plan.api.service.PlanInventoryFeign;
import com.wunding.learn.plan.api.service.dto.PlanFormInfoDTO;
import com.wunding.learn.plan.api.service.dto.PlanMapDTO;
import com.wunding.learn.project.api.dto.FaceProjectApiDTO;
import com.wunding.learn.project.api.dto.ProjectApiDTO;
import com.wunding.learn.project.api.dto.ProjectBaseDTO;
import com.wunding.learn.project.api.dto.ProjectDetailDTO;
import com.wunding.learn.project.api.dto.ProjectListDTO;
import com.wunding.learn.project.api.dto.ProjectSaveDTO;
import com.wunding.learn.project.api.dto.ProjectStatisticDTO;
import com.wunding.learn.project.api.dto.ProjectUpdateDTO;
import com.wunding.learn.project.api.query.ProjectListQuery;
import com.wunding.learn.project.api.query.ProjectStatisticQueryDTO;
import com.wunding.learn.project.service.admin.dto.AppSaveDTO;
import com.wunding.learn.project.service.admin.dto.ApplyDetailDTO;
import com.wunding.learn.project.service.admin.dto.ApplySaveDTO;
import com.wunding.learn.project.service.admin.dto.CompletionConditionDTO;
import com.wunding.learn.project.service.admin.dto.CompletionHandleDTO;
import com.wunding.learn.project.service.admin.dto.ProCompletionUserDTO;
import com.wunding.learn.project.service.admin.dto.ProjectAdminDTO;
import com.wunding.learn.project.service.admin.dto.ProjectAdminSaveDTO;
import com.wunding.learn.project.service.admin.dto.ProjectAdminUpdateDTO;
import com.wunding.learn.project.service.admin.dto.ProjectAnalysisByUserDTO;
import com.wunding.learn.project.service.admin.dto.ProjectAndTaskInfoDTO;
import com.wunding.learn.project.service.admin.dto.ProjectCourseWarePageDTO;
import com.wunding.learn.project.service.admin.dto.ProjectDTO;
import com.wunding.learn.project.service.admin.dto.ProjectLecturerImportDTO;
import com.wunding.learn.project.service.admin.dto.ProjectManagerDTO;
import com.wunding.learn.project.service.admin.dto.ProjectManagerSaveDTO;
import com.wunding.learn.project.service.admin.dto.ProjectManagerUpdateDTO;
import com.wunding.learn.project.service.admin.dto.ProjectManagerUpdateDTO.ProphaseSaveDTO;
import com.wunding.learn.project.service.admin.dto.ProjectStatisticUserDetailDTO;
import com.wunding.learn.project.service.admin.dto.ProjectUserImportDTO;
import com.wunding.learn.project.service.admin.dto.RemarkDTO;
import com.wunding.learn.project.service.admin.dto.RemindersDTO;
import com.wunding.learn.project.service.admin.dto.UserRemindersDTO;
import com.wunding.learn.project.service.admin.dto.faceproject.ScheduleStatDTO;
import com.wunding.learn.project.service.admin.query.LecturerQuery;
import com.wunding.learn.project.service.admin.query.ProjectAnalysisByUserQuery;
import com.wunding.learn.project.service.admin.query.ProjectManagerQueryDTO;
import com.wunding.learn.project.service.admin.query.ProjectUserQuery;
import com.wunding.learn.project.service.admin.query.RemindersQuery;
import com.wunding.learn.project.service.admin.query.SelectProjectListQuery;
import com.wunding.learn.project.service.admin.query.faceproject.ScheduleStatQuery;
import com.wunding.learn.project.service.biz.impl.ProjectBizImpl;
import com.wunding.learn.project.service.client.dto.ApplyCheckDTO;
import com.wunding.learn.project.service.client.dto.ApplyUserDTO;
import com.wunding.learn.project.service.client.dto.FaceProjectApplyInfoDTO;
import com.wunding.learn.project.service.client.dto.FaceProjectPageDTO;
import com.wunding.learn.project.service.client.dto.FaceProjectUserApplyCountDTO;
import com.wunding.learn.project.service.client.dto.FaceProjectUserApplyIdentity;
import com.wunding.learn.project.service.client.dto.FaceProjectUserApplyIdentity.IdentityEnum;
import com.wunding.learn.project.service.client.dto.IdpProjectDTO;
import com.wunding.learn.project.service.client.dto.LearnProjectDTO;
import com.wunding.learn.project.service.client.dto.MentorForm;
import com.wunding.learn.project.service.client.dto.MonthIdpProjectDTO;
import com.wunding.learn.project.service.client.dto.ProjectInfoDTO;
import com.wunding.learn.project.service.client.dto.ProjectMaterialExampleFileApiDTO;
import com.wunding.learn.project.service.client.dto.ProjectMaterialExampleFileApiDTO.FileInfo;
import com.wunding.learn.project.service.client.dto.ProjectPageDTO;
import com.wunding.learn.project.service.client.dto.QuickProjectDTO;
import com.wunding.learn.project.service.client.dto.QuickProjectRoleDTO;
import com.wunding.learn.project.service.client.dto.QuickProjectUserStatDTO;
import com.wunding.learn.project.service.client.dto.SubordinateProjectDTO;
import com.wunding.learn.project.service.client.dto.SuperviseProjectPageDTO;
import com.wunding.learn.project.service.client.dto.SuperviseProjectPageQuery;
import com.wunding.learn.project.service.client.dto.TrainOrgHomeDTO;
import com.wunding.learn.project.service.client.dto.UserIdpProjectDTO;
import com.wunding.learn.project.service.client.dto.UserIdpProjectVO;
import com.wunding.learn.project.service.client.dto.lecturerworkbench.WorkbenchProjectPageDTO;
import com.wunding.learn.project.service.client.query.FaceProjectPageQuery;
import com.wunding.learn.project.service.client.query.MentorNoSolveQuery;
import com.wunding.learn.project.service.client.query.ProjectHomePageQuery;
import com.wunding.learn.project.service.client.query.ProjectPageQuery;
import com.wunding.learn.project.service.client.query.ProjectSearchQuery;
import com.wunding.learn.project.service.client.query.QuickProjectUserStatQuery;
import com.wunding.learn.project.service.client.query.StudentProjectQuery;
import com.wunding.learn.project.service.client.query.SubordinateProjectQuery;
import com.wunding.learn.project.service.client.query.TrainProjectQuery;
import com.wunding.learn.project.service.client.query.UserIdpProjectQuery;
import com.wunding.learn.project.service.client.query.lecturerworkbench.LecturerProjectQuery;
import com.wunding.learn.project.service.component.ProjectViewLimitComponent;
import com.wunding.learn.project.service.constant.ProjectConstant;
import com.wunding.learn.project.service.dao.AppDao;
import com.wunding.learn.project.service.dao.ApplyDao;
import com.wunding.learn.project.service.dao.ApplyUserDao;
import com.wunding.learn.project.service.dao.MentorRecordDao;
import com.wunding.learn.project.service.dao.PhaseDao;
import com.wunding.learn.project.service.dao.ProjectInfoDao;
import com.wunding.learn.project.service.dao.TrainPlanMainDao;
import com.wunding.learn.project.service.enums.ApplyFormTemplateType;
import com.wunding.learn.project.service.enums.ProjectTaskTypeEnum;
import com.wunding.learn.project.service.enums.QuickProjectRoleEnum;
import com.wunding.learn.project.service.enums.TrainPlanMainStatus;
import com.wunding.learn.project.service.imports.ProjectLecturerExcelTemplate;
import com.wunding.learn.project.service.imports.ProjectUserExcelTemplate;
import com.wunding.learn.project.service.mapper.ProjectMapper;
import com.wunding.learn.project.service.mapper.ProjectUserStudyConditionMapper;
import com.wunding.learn.project.service.model.App;
import com.wunding.learn.project.service.model.Apply;
import com.wunding.learn.project.service.model.ApplyUser;
import com.wunding.learn.project.service.model.Classroom;
import com.wunding.learn.project.service.model.CompletionCondition;
import com.wunding.learn.project.service.model.MentorRecord;
import com.wunding.learn.project.service.model.Phase;
import com.wunding.learn.project.service.model.Progress;
import com.wunding.learn.project.service.model.Project;
import com.wunding.learn.project.service.model.ProjectElement;
import com.wunding.learn.project.service.model.ProjectUserStudyCondition;
import com.wunding.learn.project.service.model.Task;
import com.wunding.learn.project.service.model.TaskProgress;
import com.wunding.learn.project.service.model.TrainPlanMain;
import com.wunding.learn.project.service.model.TrainPlanPrjRelation;
import com.wunding.learn.project.service.model.UserSelectRecord;
import com.wunding.learn.project.service.query.ProjectJoinQuery;
import com.wunding.learn.project.service.service.IAppService;
import com.wunding.learn.project.service.service.IApplyService;
import com.wunding.learn.project.service.service.IApplyUserService;
import com.wunding.learn.project.service.service.IClassroomService;
import com.wunding.learn.project.service.service.ICompletionConditionService;
import com.wunding.learn.project.service.service.ICompletionUserService;
import com.wunding.learn.project.service.service.IMentorRecordService;
import com.wunding.learn.project.service.service.IPhaseService;
import com.wunding.learn.project.service.service.IProgressService;
import com.wunding.learn.project.service.service.IProjectElementService;
import com.wunding.learn.project.service.service.IProjectLecturerExaminationService;
import com.wunding.learn.project.service.service.IProjectService;
import com.wunding.learn.project.service.service.IProjectUserStudyConditionService;
import com.wunding.learn.project.service.service.IRemarkService;
import com.wunding.learn.project.service.service.ITaskProgressService;
import com.wunding.learn.project.service.service.ITaskService;
import com.wunding.learn.project.service.service.ITeamUserService;
import com.wunding.learn.project.service.service.ITrainPlanMainService;
import com.wunding.learn.project.service.service.ITrainPlanPrjRelationService;
import com.wunding.learn.project.service.service.IUserSelectRecordService;
import com.wunding.learn.project.service.service.ProjectDao;
import com.wunding.learn.push.api.component.PushComponent;
import com.wunding.learn.push.api.dto.PushAttributeDTO;
import com.wunding.learn.push.api.dto.PushNoticeSetDTO;
import com.wunding.learn.push.api.dto.PushResourceDTO;
import com.wunding.learn.push.api.dto.SendPushDTO;
import com.wunding.learn.push.api.enums.RemindPushEnum;
import com.wunding.learn.push.api.service.PushFeign;
import com.wunding.learn.user.api.dto.IndexOrgDTO;
import com.wunding.learn.user.api.dto.OrgDTO;
import com.wunding.learn.user.api.dto.OrgShowDTO;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.dto.UserListDTO;
import com.wunding.learn.user.api.dto.UserOrgDTO;
import com.wunding.learn.user.api.dto.viewlimit.ResourceViewLimitInfoDTO;
import com.wunding.learn.user.api.dto.viewlimit.ViewLimitBaseInfoDTO;
import com.wunding.learn.user.api.query.IndexOrgQuery;
import com.wunding.learn.user.api.query.UserQuery;
import com.wunding.learn.user.api.service.CategoryFeign;
import com.wunding.learn.user.api.service.MemberCardFeign;
import com.wunding.learn.user.api.service.OrgFeign;
import com.wunding.learn.user.api.service.ParaFeign;
import com.wunding.learn.user.api.service.RouterFeign;
import com.wunding.learn.user.api.service.UserFeign;
import com.wunding.learn.user.api.service.ViewLimitFeign;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

/**
 * <p> 项目主表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">赖卓成</a>
 * @since 2022-07-19
 */
@Slf4j
@Service(ProjectConstant.PROJECT_SERVICE)
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class ProjectServiceImpl extends ServiceImpl<ProjectMapper, Project> implements IProjectService {

    /**
     * 话题板块是否公共 -非公共
     */
    public static final int FORUM_SECTION_NOT_PUBLIC = 0;
    /**
     * 话题板块 非匿名
     */
    public static final int FORUM_SECTION_IS_ANONYMOUS = 0;
    /**
     * 话题板块 是学习项目
     */
    public static final int FORUM_SECTION_IS_TRAIN = 1;
    /**
     * 话题板块 启用
     */
    public static final int FORUM_SECTION_IS_ENABLE = 1;
    /**
     * 学习项目默认应用：费用/资料
     */
    public static final String DEFAULT_APPLICATION = "cost,datum";
    /**
     * 默认话题 名称
     */
    public static final String DEFAULT_SECTION_NAME = "默认话题";
    /**
     * 快速培训项目
     */
    public static final String QUICK_TRAINING = "QuickTrainingManage";

    /**
     * 学习项目应用：请假
     */
    public static final String APP_VACATE = "vacate";

    private final TransactionTemplate transactionTemplate;

    @Resource
    @Lazy
    private ProjectDao projectDao;
    @Resource
    private RabbitMqProducer mqProducer;
    @Resource
    private OrgFeign orgFeign;
    @Resource
    private FileFeign fileFeign;
    @Resource
    private ExamFeign examFeign;
    @Resource
    private UserFeign userFeign;
    @Resource
    private LecturerFeign lecturerFeign;
    @Resource
    @Lazy
    private IAppService appService;
    @Resource
    @Lazy
    private ITaskService taskService;
    @Resource
    private IPhaseService phaseService;
    @Resource(name = "projectApplyService")
    private IApplyService applyService;
    @Resource
    @Lazy
    private IApplyUserService applyUserService;
    @Resource
    @Lazy
    private IProgressService progressService;
    @Resource
    private IClassroomService classroomService;
    @Resource
    private IMentorRecordService mentorRecordService;
    @Resource
    private ITrainPlanMainService trainPlanMainService;
    @Resource
    private IResourceViewLimitService resourceViewLimitService;
    @Resource
    private ProjectViewLimitComponent projectViewLimitComponent;
    @Resource
    private ITrainPlanPrjRelationService trainPlanPrjRelationService;
    @Resource
    @Lazy
    private IProjectLecturerExaminationService projectLecturerExaminationService;
    @Resource
    private ForumFeign forumFeign;
    @Resource
    private ITaskProgressService taskProgressService;
    @Resource
    private ProjectMapper projectMapper;
    @Resource
    private CourseWareFeign courseWareFeign;
    @Resource
    private ITeamUserService teamUserService;
    @Resource
    private ExportComponent exportComponent;
    @Resource
    private LecturerExaminationFeign lecturerExaminationFeign;
    @Resource
    @Lazy
    private ICompletionUserService completionUserService;
    @Resource
    private PushComponent pushComponent;
    @Resource
    private ExcitationFeign excitationFeign;
    @Resource
    private ICompletionConditionService completionConditionService;
    @Resource
    private PlanInventoryFeign planInventoryFeign;
    @Resource
    private FormTemplateFeign formTemplateFeign;
    @Resource
    private CategoryFeign categoryFeign;
    @Resource
    private EvaluationFeign evaluationFeign;
    @Resource
    private MemberCardFeign memberCardFeign;
    @Resource
    private IUserSelectRecordService userSelectRecordService;
    @Resource
    private RouterFeign routerFeign;
    @Resource
    private SignFeign signFeign;
    @Resource
    private CourseFeign courseFeign;
    @Resource
    private PushFeign pushFeign;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private IRemarkService remarkService;
    @Resource
    private IProjectElementService projectElementService;
    @Resource
    private IProjectUserStudyConditionService projectUserStudyConditionService;
    @Resource
    private ProjectUserStudyConditionMapper projectUserStudyConditionMapper;
    @Resource
    private IPosterShareService posterShareService;

    @Resource(name = "projectInfoDao")
    private ProjectInfoDao projectInfoDao;
    @Resource(name = "mentorRecordDao")
    private MentorRecordDao mentorRecordDao;
    @Resource(name = "trainPlanMainDao")
    private TrainPlanMainDao trainPlanMainDao;
    @Resource(name = "applyUserDao")
    private ApplyUserDao applyUserDao;
    @Resource(name = "phaseDao")
    private PhaseDao phaseDao;
    @Resource(name = "applyDao")
    private ApplyDao applyDao;
    @Resource(name = "appDao")
    private AppDao appDao;
    @Resource
    private ParaFeign paraFeign;
    @Resource
    private EvaluationReplyFeign evaluationReplyFeign;
    @Resource
    private ViewLimitFeign viewLimitFeign;
    @Resource
    private Executor commonTaskThreadPool;

    @Resource
    private ImportDataFeign importDataFeign;
    private static final ExecutorService IMPORT_DATA_THREAD_POOL;
    static {
        IMPORT_DATA_THREAD_POOL = TtlExecutors.getTtlExecutorService(
            new ThreadPoolExecutor(2, 2, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(),
                new CustomizableThreadFactory("import-project-data-pool-")));

    }

    @Override
    public IProjectService getBean() {
        return SpringUtil.getBean(ProjectConstant.PROJECT_SERVICE, IProjectService.class);
    }


    @Override
    public PageInfo<ProjectManagerDTO> queryPage(ProjectManagerQueryDTO projectManagerQueryDTO) {
        long startTime = System.currentTimeMillis();
        log.info(">>>>>>>>>>>>>>project manager queryPage");
        String userId = UserThreadContext.getUserId();
        OrgDTO orgDTO = orgFeign.getOrgByUserId(userId);
        //是否查询所有数据(不校验任何下发，管辖权限)
        if (null == projectManagerQueryDTO.getIsAll() || !projectManagerQueryDTO.getIsAll()) {
            //管辖范围
            Set<String> userManageAreaOrgId = orgFeign.findUserManageAreaLevelPath(userId);
            projectManagerQueryDTO.setCurrentUserId(userId);
            Optional.ofNullable(orgDTO).ifPresent(org -> projectManagerQueryDTO.setCurrentOrgId(org.getId()));
            projectManagerQueryDTO.setUserManageAreaOrgId(userManageAreaOrgId);
        }
        long queryPageTime = System.currentTimeMillis();
        log.info(">>>>>>>>>>>>>>project manager queryPage query");
        PageInfo<ProjectManagerDTO> sqlPageInfo = PageMethod.startPage(projectManagerQueryDTO.getPageNo(),
            projectManagerQueryDTO.getPageSize()).doSelectPageInfo(() -> baseMapper.queryPage(projectManagerQueryDTO));
        log.info("<<<<<<<<<<<<<<project manager queryPage query spend {}", System.currentTimeMillis() - queryPageTime);
        Set<String> ids = sqlPageInfo.getList().stream().map(ProjectManagerDTO::getPublishBy)
            .filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
        Map<String, UserDTO> userNameMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(ids)) {
            userNameMap = userFeign.getUserNameMapByIds(ids);
        }
        for (ProjectManagerDTO projectManagerDTO : sqlPageInfo.getList()) {
            String fullNameById = StringUtils.EMPTY;
            if (projectManagerDTO.getIsPublish() == PublishEnum.PUBLISHED.getValue()) {
                UserDTO userDTO = userNameMap.get(projectManagerDTO.getPublishBy());
                if (userDTO != null) {
                    fullNameById = userDTO.getFullName();
                }
            }
            projectManagerDTO.setPublishBy(fullNameById);
        }
        log.info("<<<<<<<<<<<<<<<<project manager queryPage spend {}", System.currentTimeMillis() - startTime);

        if (Objects.equals(projectManagerQueryDTO.getProjectType(), GeneralJudgeEnum.NEGATIVE.getValue())) {
            //路由访问埋点
            mqProducer.sendMsg(
                new HomeRouterVisitEvent(RouterVisitEnum.StudyProjManage.getRouterId(), UserThreadContext.getUserId(),
                    RouterVisitEnum.StudyProjManage.getName()));
        } else if (Objects.equals(projectManagerQueryDTO.getProjectType(), 3)) {
            //面授项目路由埋点
            mqProducer.sendMsg(new HomeRouterVisitEvent(RouterVisitEnum.FaceTeachStudyProjManage.getRouterId(),
                UserThreadContext.getUserId(), RouterVisitEnum.FaceTeachStudyProjManage.getName()));
        }

        return sqlPageInfo;
    }

    @Override
    public PageInfo<ProjectListDTO> queryReferencedProjectPage(ProjectListQuery projectListQuery) {
        ProjectManagerQueryDTO projectManagerQueryDTO = new ProjectManagerQueryDTO();
        BeanUtils.copyProperties(projectListQuery, projectManagerQueryDTO);

        // 原参数是 List 类型，新参数是 Set 类型，无法通过属性COPY，需要手动赋值
        if (!CollectionUtils.isEmpty(projectListQuery.getReferencedIdList())) {
            projectManagerQueryDTO.setReferencedIdList(new HashSet<>(projectListQuery.getReferencedIdList()));
        }
        PageInfo<ProjectManagerDTO> pageInfo = queryPage(projectManagerQueryDTO);

        PageInfo<ProjectListDTO> projectListDTOPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, projectListDTOPageInfo);

        List<ProjectListDTO> projectListDTOList = new ArrayList<>();

        List<ProjectManagerDTO> projectManagerDTOList = pageInfo.getList();
        projectManagerDTOList.forEach(projectManagerDTO -> {
            ProjectListDTO projectListDTO = new ProjectListDTO();
            BeanUtils.copyProperties(projectManagerDTO, projectListDTO);
            projectListDTOList.add(projectListDTO);
        });

        projectListDTOPageInfo.setList(projectListDTOList);

        return projectListDTOPageInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Project create(ProjectAdminSaveDTO projectManagerSaveDTO, String id) {

        projectManagerSaveDTO.setEnableExcitationExchange(
            Optional.ofNullable(projectManagerSaveDTO.getEnableExcitationExchange())
                .orElse(GeneralJudgeEnum.NEGATIVE.getValue()));
        // 校验固定日期项目
        fixedTypeProjectValid(projectManagerSaveDTO.getType(), projectManagerSaveDTO.getCycleDay());
        // 检验日期项目开始结束时间
        checkProjectTime(projectManagerSaveDTO);
        // 校验海报分享必填属性
        posterShareService.checkPosterShare(projectManagerSaveDTO.getPosterShareDTO());
        Project project = new Project();
        projectManagerSaveDTO.setId(StringUtils.isBlank(id) ? newId() : id);
        BeanUtils.copyProperties(projectManagerSaveDTO, project);
        // 拼接项目编号（同培训班）
        SimpleDateFormat sdf = new SimpleDateFormat(DateHelper.YYYYMMDD2);
        project.setProNo(sdf.format(new Date()) + StringUtil.random(4));

        project.setOrgId(orgFeign.getOrgByUserId(UserThreadContext.getUserId()).getId());
        project.setLeader(projectManagerSaveDTO.getLeaderId());
        project.setPlanId(projectManagerSaveDTO.getTrainPlanId());
        if (StringUtils.isBlank(projectManagerSaveDTO.getTrainPlanId()) && StringUtils.isNotBlank(
            projectManagerSaveDTO.getPlanInventoryId())) {
            // 根据清单取培训计划id
            project.setPlanId(planInventoryFeign.getPlanIdByInventoryId(projectManagerSaveDTO.getPlanInventoryId()));
        }

        String projectId = project.getId();
        // 保存图片
        saveProjectImage(projectManagerSaveDTO, projectId);

        // 添加默认应用：费用/资料
        StringBuilder projectItem = addProjectItem(projectManagerSaveDTO);

        project.setProjectItem(projectItem.toString());

        if (!CollectionUtils.isEmpty(projectManagerSaveDTO.getProphaseSaveDTOS())) {
            setProPhase(projectManagerSaveDTO.getProphaseSaveDTOS(), projectId);
        }
        // 设置是否允许兑换加入学习项目属性
        buildProjectEnableConsumeJoin(project);
        // 保存下发范围
        projectViewLimitComponent.handleNewViewLimit(projectManagerSaveDTO.getProgrammeId(), projectId);
        // 这样处理原因见ProjectManagerRest.updateQuickProjectAndProjectTaskAndProjectApp方法注释
        if (projectManagerSaveDTO.getProjectType() != null && (projectManagerSaveDTO.getProjectType() == 1
            || projectManagerSaveDTO.getProjectType() == 2)) {
            project.setIsDel(1);
        }
        Integer isPublish = projectManagerSaveDTO.getIsPublish();
        // 开启了配置才能在添加时直接发布
        checkProjectPublish(isPublish, project);
        // 保存海报分享信息
        posterShareService.savePosterShare(projectId,
            ProjectBizImpl.getResourceTypeEnum(projectManagerSaveDTO.getType(), projectManagerSaveDTO.getProjectType()),
            projectManagerSaveDTO.getPosterShareDTO());

        projectInfoDao.saveProject(project);
        // 初始化学习项目的激励配置
        mqProducer.sendMsg(new ExcitationInitMqEvent(new ResourceConfigInitDTO().setResourceId(projectId)
            .setResourceType(ExcitationEventCategoryEnum.PROJECT.getCode())));

        // 培训计划
        String planInventoryId = projectManagerSaveDTO.getPlanInventoryId();
        if (StringUtils.isNotBlank(planInventoryId)) {
            // 添加项目培训计划关系表
            trainPlanPrjRelationService.save(
                new TrainPlanPrjRelation().setId(newId()).setTpId(planInventoryId).setPrjId(projectId));
        }

        //  添加默认话题
        log.info("添加默认话题--->{}", projectManagerSaveDTO);
        initProjectDefaultForum(projectId, projectManagerSaveDTO.getViewType(), projectManagerSaveDTO.getProgrammeId(),
            projectManagerSaveDTO.getIsTrain());

        // 若学习项目关联了计划清单 则发送信息保存资源与的关联
        if (StringUtils.isNotBlank(project.getPlanInventoryId())) {
            OrgDTO byId = orgFeign.getById(project.getOrgId());
            mqProducer.sendMsg(new InventoryResourceChangeEvent(
                new PlanStatisticSourceDataSaveDTO().setSourceId(projectId)
                    .setSourceType(ExcitationEventCategoryEnum.PROJECT.getCode())
                    .setPlanInventoryId(project.getPlanInventoryId()).setStartTime(project.getStartTime())
                    .setEndTime(project.getEndTime()).setOrgId(project.getOrgId()).setOrgName(byId.getOrgName())
                    .setTrainName(project.getProName()).setTrainNo(project.getProNo())));
        }

        // 启用才给资源配置推送
        resourceOperate(projectManagerSaveDTO, project);
        if (StringUtils.isNotBlank(project.getTrainCategoryId())) {
            handleCategoryCanDel();
        }
        return project;
    }

    private void checkProjectPublish(Integer isPublish, Project project) {
        String paraValue = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_916.getCode());
        if (isPublish != null && isPublish == PublishStatusEnum.IS_PUBLISH.getValue()) {
            if (StringUtils.isNotBlank(paraValue) && GeneralJudgeEnum.CONFIRM.getValue()
                .equals(Integer.valueOf(paraValue))) {
                project.setIsPublish(isPublish);
                project.setPublishBy(UserThreadContext.getUserId());
                project.setPublishTime(new Date());
            } else {
                throw new BusinessException(ProjectErrorNoEnum.PROJECT_NOT_HAVE_TASK_EXIST);
            }
        }
    }

    /**
     * 检验日期项目开始结束时间
     *
     * @param projectManagerSaveDTO projectManagerSaveDTO
     */
    private void checkProjectTime(ProjectAdminSaveDTO projectManagerSaveDTO) {
        if (Objects.isNull(projectManagerSaveDTO)) {
            return;
        }
        Integer projectType = projectManagerSaveDTO.getType();
        // 除周期项目外，其他项目时间是必填的
        if (Objects.equals(projectType, ProjectConstant.PROJECT_TYPE_FIXED_DATE)) {
            if (Objects.isNull(projectManagerSaveDTO.getStartTime())) {
                throw new BusinessException(ProjectErrorNoEnum.ERR_PROJECT_START_DATE_TIME);
            }

            if (Objects.isNull(projectManagerSaveDTO.getEndTime())) {
                throw new BusinessException(ProjectErrorNoEnum.ERR_PROJECT_END_DATE_TIME);
            }
        }
    }

    /**
     * 资源配置推送
     */
    private void resourceOperate(ProjectAdminSaveDTO projectManagerSaveDTO, Project project) {
        if (GeneralJudgeEnum.CONFIRM.getValue().equals(projectManagerSaveDTO.getIsPublish())
            && projectManagerSaveDTO.getProjectType() != 1 && Optional.ofNullable(
            projectManagerSaveDTO.getPushNoticeSetDTO()).isPresent()) {

            // 推送处理逻辑
            sendPushFeign(projectManagerSaveDTO, 0);

            // 发送资源操作事件消息
            String type;
            if (Objects.equals(project.getProjectType(), 1)) {
                // 快速培训
                type = PushType.QUICK_PROJECT.getKey();
            } else {
                // 学习项目
                type = PushType.PROJECT.getKey();
            }
            mqProducer.sendMsg(new ResourceOperateEvent(
                Objects.equals(projectManagerSaveDTO.getIsPublish(), GeneralJudgeEnum.NEGATIVE.getValue())
                    ? OperationEnum.PUBLISH_CANCEL : OperationEnum.PUBLISH, type, project.getId()));
        }
    }

    /**
     * 学习项目添加默认应用：费用/资料
     */
    private StringBuilder addProjectItem(ProjectAdminSaveDTO projectManagerSaveDTO) {
        // 快速培训的时候不要默认应用：费用/资料
        StringBuilder projectItem;
        if (projectManagerSaveDTO.getProjectType() != null && projectManagerSaveDTO.getProjectType() == 1) {
            projectItem = new StringBuilder();
        } else {
            projectItem = new StringBuilder(DEFAULT_APPLICATION);
        }
        for (String item : projectManagerSaveDTO.getProjectItem()) {
            if (DEFAULT_APPLICATION.contains(item)) {
                continue;
            }
            projectItem.append(CommonConstants.A_COMMA_IN_ENGLISH).append(item);
        }
        // 面授项目添加应用：请假
        if (projectManagerSaveDTO.getProjectType() != null
            && projectManagerSaveDTO.getProjectType() == ProjectConstant.PROJECT_TYPE_FACE) {
            projectItem.append(CommonConstants.A_COMMA_IN_ENGLISH).append(APP_VACATE);
            projectItem.append(CommonConstants.A_COMMA_IN_ENGLISH).append(ProjectAppType.INVOICE.getAppType());
        }
        return projectItem;
    }

    /**
     * 保存学习项目图片
     */
    private void saveProjectImage(ProjectAdminSaveDTO projectManagerSaveDTO, String projectId) {
        if (StringUtils.isNotBlank(projectManagerSaveDTO.getCoverImagePath())) {
            fileFeign.saveImage(projectId, ImageBizType.STUDY_PROJECT_FIRST.name(),
                projectManagerSaveDTO.getCoverImageName(), projectManagerSaveDTO.getCoverImagePath());
        } else if (projectManagerSaveDTO.getIsUseDefaultImg() != null
            && projectManagerSaveDTO.getIsUseDefaultImg() == 1) {
            fileFeign.saveDefaultImage(projectId, ImageBizType.STUDY_PROJECT_FIRST.name(), "projectDefaultImage.png",
                "/file/quickProjectDefaultImage/projectDefaultImage.png");
        }
    }


    /**
     * 固定日期项目校验
     */
    private void fixedTypeProjectValid(Integer projectType, Long cycleDay) {
        if (Objects.equals(projectType, ProjectConstant.PROJECT_TYPE_FIXED_DATE)) {
            return;
        }
        if (cycleDay.compareTo(1L) < 0) {
            throw new BusinessException(ProjectErrorNoEnum.PERIOD_DAY_ERROR);
        }
    }

    /**
     * 构建学习项目是否允许兑换加入属性
     */
    private void buildProjectEnableConsumeJoin(Project project) {
        if (Objects.isNull(project)) {
            return;
        }
        // 设置是否允许兑换加入(默认不允许)
        project.setEnableConsumeJoin(GeneralJudgeEnum.NEGATIVE.getValue());
        if (Optional.ofNullable(project.getConsumeExcitationNum()).isPresent()
            && project.getConsumeExcitationNum().compareTo(BigDecimal.ZERO) > 0) {
            if (!ExcitationTypeEnum.isTradeExcitationType(project.getConsumeExcitationType())) {
                throw new BusinessException(ExcitationErrorNoEnum.ERR_EXCITATION_TYPE);
            }
            project.setEnableConsumeJoin(GeneralJudgeEnum.CONFIRM.getValue());
        }
    }

    /**
     * 添加默认话题
     *
     * @param projectId 学习项目ID
     */
    private void initProjectDefaultForum(String projectId, Integer viewType, Long programmeId, Integer isTrain) {
        ForumSectionSaveDTO forumSectionSaveDTO = new ForumSectionSaveDTO();
        forumSectionSaveDTO.setIsPublic(FORUM_SECTION_NOT_PUBLIC);
        forumSectionSaveDTO.setSectionName(DEFAULT_SECTION_NAME);
        forumSectionSaveDTO.setProjectId(projectId);
        forumSectionSaveDTO.setIsAnonymous(FORUM_SECTION_IS_ANONYMOUS);
        //默认是学习项目
        forumSectionSaveDTO.setIsTrain(isTrain == null ? FORUM_SECTION_IS_TRAIN : isTrain);
        forumSectionSaveDTO.setIsEnable(FORUM_SECTION_IS_ENABLE);
        forumSectionSaveDTO.setViewType(viewType);
        forumSectionSaveDTO.setProgrammeId(programmeId);
        forumFeign.saveForumSection(forumSectionSaveDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveProject(ProjectSaveDTO projectSaveDTO) {
        log.info("添加默认话题saveProject--->projectSaveDTO:{}", projectSaveDTO);
        ProjectAdminSaveDTO projectManagerSaveDTO = new ProjectAdminSaveDTO();
        BeanUtils.copyProperties(projectSaveDTO, projectManagerSaveDTO);
        log.info("添加默认话题saveProject--->projectManagerSaveDTO:{}", projectManagerSaveDTO);

        List<ProjectBaseDTO.ProphaseSaveDTO> prophaseSaveDTOS1 = projectSaveDTO.getProphaseSaveDTOS();
        if (!CollectionUtils.isEmpty(prophaseSaveDTOS1)) {
            List<ProjectManagerSaveDTO.ProphaseSaveDTO> prophaseSaveDTOS = new ArrayList<>();
            prophaseSaveDTOS1.forEach(prophaseSaveDTO -> {
                ProjectManagerSaveDTO.ProphaseSaveDTO dto = new ProjectManagerSaveDTO.ProphaseSaveDTO();
                BeanUtils.copyProperties(prophaseSaveDTO, dto);
                prophaseSaveDTOS.add(dto);
            });
            projectManagerSaveDTO.setProphaseSaveDTOS(prophaseSaveDTOS);
        }

        return getBean().create(projectManagerSaveDTO, StringUtils.EMPTY).getId();
    }

    private void updateTrainPlanMain(String trainPlanId) {

        TrainPlanMain trainPlanMain = trainPlanMainService.getById(trainPlanId);
        boolean flag =
            null != trainPlanMain && (trainPlanMain.getTrainStatus() == TrainPlanMainStatus.STATUS_PASS.getStatus()
                || trainPlanMain.getTrainStatus() == TrainPlanMainStatus.STATUS_IN_IMPLIMENTATION.getStatus()
                || trainPlanMain.getTrainStatus() == TrainPlanMainStatus.STATUS_COMPLETED.getStatus());
        if (flag) {
            TrainPlanPrjRelation query = new TrainPlanPrjRelation();
            query.setTpId(trainPlanId);
            List<String> projectIds = trainPlanPrjRelationService.list(
                    new LambdaUpdateWrapper<TrainPlanPrjRelation>().eq(TrainPlanPrjRelation::getTpId, trainPlanId)).stream()
                .map(TrainPlanPrjRelation::getPrjId).toList();
            int status;
            if (projectIds.isEmpty()) {
                status = TrainPlanMainStatus.STATUS_PASS.getStatus();
            } else if (projectIds.size() >= trainPlanMain.getTrainPeriod()) {
                status = TrainPlanMainStatus.STATUS_COMPLETED.getStatus();
            } else {
                status = TrainPlanMainStatus.STATUS_IN_IMPLIMENTATION.getStatus();
            }

            TrainPlanMain updateParams = new TrainPlanMain();
            updateParams.setId(trainPlanId);
            updateParams.setTrainStatus(status);
            trainPlanMainDao.updateTrainPlanMain(updateParams);
        }

    }

    /**
     * 添加学习项目时，添加学习项目阶段
     */
    public void setProPhase(List<ProjectManagerSaveDTO.ProphaseSaveDTO> prophaseSaveDTOList, String id) {
        ArrayList<Phase> list = new ArrayList<>();
        for (ProjectManagerSaveDTO.ProphaseSaveDTO prophaseSaveDto : prophaseSaveDTOList) {
            Phase phase = new Phase();
            phase.setId(newId());
            phase.setPhaseName(prophaseSaveDto.getName());
            phase.setSortNo(prophaseSaveDto.getSort());
            phase.setProId(id);
            list.add(phase);
        }
        phaseService.saveBatch(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProject(ProjectUpdateDTO projectUpdateDTO) {
        ProjectAdminUpdateDTO projectManagerUpdateDTO = new ProjectAdminUpdateDTO();
        BeanUtils.copyProperties(projectUpdateDTO, projectManagerUpdateDTO);

        List<ProjectBaseDTO.ProphaseSaveDTO> prophaseSaveDTOS1 = projectUpdateDTO.getProphaseSaveDTOS();
        if (!CollectionUtils.isEmpty(prophaseSaveDTOS1)) {
            List<ProjectManagerUpdateDTO.ProphaseSaveDTO> prophaseSaveDTOS = new ArrayList<>();
            prophaseSaveDTOS1.forEach(prophaseSaveDTO -> {
                ProjectManagerUpdateDTO.ProphaseSaveDTO dto = new ProjectManagerUpdateDTO.ProphaseSaveDTO();
                BeanUtils.copyProperties(prophaseSaveDTO, dto);
                prophaseSaveDTOS.add(dto);
            });
            projectManagerUpdateDTO.setProphaseSaveDTOS(prophaseSaveDTOS);
        }
        Project oldProject = getById(projectManagerUpdateDTO.getId());
        //一层套一层搁这这套娃呢?这里关联的是培训计划中的表单内容不是培训计划，看半天才看懂
        oldProject.setPlanInventoryId(oldProject.getPlanId());
        projectManagerUpdateDTO.setPlanInventoryId(projectUpdateDTO.getTrainPlanId());
        getBean().update(oldProject, projectManagerUpdateDTO);
    }

    @Override
    @Log(type = Type.UPDATE, targetId = "#projectAdminUpdateDTO.id", targetName = "#projectAdminUpdateDTO.proName", targetType = Log.TargetType.PROJECT)
    @Transactional(rollbackFor = Exception.class)
    public Project update(Project oldProject, ProjectAdminUpdateDTO projectAdminUpdateDTO) {

        oldProject = Optional.ofNullable(oldProject)
            .orElseThrow(() -> new BusinessException(ProjectErrorNoEnum.PROJECT_NOT_EXIST));
        // 固定日期项目校验
        fixedTypeProjectValid(oldProject.getType(), projectAdminUpdateDTO.getCycleDay());
        // 校验海报分享
        posterShareService.checkPosterShare(projectAdminUpdateDTO.getPosterShareDTO());
        Project project = new Project();
        BeanUtils.copyProperties(projectAdminUpdateDTO, project);
        project.setLeader(projectAdminUpdateDTO.getLeaderId());
        project.setPlanId(projectAdminUpdateDTO.getTrainPlanId());

        String projectId = project.getId();
        // 保存图片 更新图片后图片名称会变
        saveProjectImage(oldProject, projectAdminUpdateDTO, projectId);

        // 判断班主任是否发生修改,发生修改删除原有班主任的记录
        updateProjectLeader(oldProject, projectAdminUpdateDTO);

        // 添加默认应用：费用/资料
        setProjectItem(projectAdminUpdateDTO, project);

        // 项目阶段
        saveOrUpdateProPhase(projectAdminUpdateDTO.getProphaseSaveDTOS(), projectId,
            projectAdminUpdateDTO.getProjectType());

        // 更新海报分享
        posterShareService.updatePosterShare(projectId,
            ProjectBizImpl.getResourceTypeEnum(oldProject.getType(), projectAdminUpdateDTO.getProjectType()),
            projectAdminUpdateDTO.getPosterShareDTO());

        // 保存下发范围
        projectViewLimitComponent.handleNewViewLimit(projectAdminUpdateDTO.getProgrammeId(), projectId);
        // 更新学习项目评估的下发范围
        evaluationFeign.updateViewLimitByProjectId(new EvaluationLimitViewUpdateDTO().setProjectId(projectId)
            .setProgrammeId(projectAdminUpdateDTO.getProgrammeId()));
        // 更新论坛版块的下发范围
        forumFeign.updateViewLimitByProjectId(new ForumSectionLimitViewUpdateDTO().setProjectId(projectId)
            .setProgrammeId(projectAdminUpdateDTO.getProgrammeId()));
        // 更新头条下发范围
        mqProducer.sendMsg(new FirstInfoViewLimitChangeEvent(projectId, FirstInfoContentEnum.project.name(),
            projectAdminUpdateDTO.getProgrammeId()));

        changePublish(projectAdminUpdateDTO, project);

        // 构建学习项目是否允许兑换加入属性
        buildProjectEnableConsumeJoin(project);
        projectInfoDao.updateProject(project);
        // 更新可以设置为空的属性
        projectInfoDao.updateProjectNullField(project);

        // 培训计划
        setTrainPlanPrjRelation(projectAdminUpdateDTO, projectId, project);

        //去更新讲师授课的培训类别 和 计划类别
        PlanMapDTO planDTO = projectLecturerExaminationService.getPlanDTO(project);
        lecturerExaminationFeign.updateExaminationByTrainProId(
            new EditLecturerExaminationDTO().setTrainProId(project.getId())
                .setTrainCategory(project.getTrainCategoryId())
                .setPlanCategory(planDTO != null ? planDTO.getPlanCategory() : ""));

        // 修改直接添加任务的可见范围
        List<Task> taskList = taskService.list(
            new QueryWrapper<Task>().lambda().eq(Task::getCreateType, ProjectConstant.CREATE_TYPE_DIRECT)
                .eq(Task::getProId, projectId));
        taskService.syncSaveViewLimit(projectId, taskList);

        // 若学习项目关联了计划清单 则发送信息保存资源与的关联
        sendPlanInventoryMqMsg(oldProject, projectAdminUpdateDTO, project);

        synUpdateAllTaskAndApp(projectAdminUpdateDTO, project);

        // 与添加逻辑类似
        if (GeneralJudgeEnum.CONFIRM.getValue().equals(projectAdminUpdateDTO.getIsPublish())
            && projectAdminUpdateDTO.getProjectType() != 1 && Optional.ofNullable(
            projectAdminUpdateDTO.getPushNoticeSetDTO()).isPresent()) {

            SendPushDTO sendPushDTO = new SendPushDTO();
            sendPushDTO.setLang(LocaleContextHolder.getLocale().getLanguage());
            sendPushDTO.setServerName(Objects.requireNonNull(WebUtil.getRequest()).getServerName());
            PushNoticeSetDTO pushNoticeSetDTO = projectAdminUpdateDTO.getPushNoticeSetDTO();
            sendPushDTO.setPushNoticeSetDTO(pushNoticeSetDTO);

            // 手动推送时采用内容封面图片处理
            if (Objects.equals(pushNoticeSetDTO.getPushMethod(), 1)) {
                pushNoticeSetDTO.getManualPushSet().getCustomPushContents().forEach(customPushContent -> {
                    if (Objects.equals(customPushContent.getPushImage(), 1)) {
                        customPushContent.setImagePath(fileFeign.getImageFileNamePath(projectAdminUpdateDTO.getId(),
                            ImageBizType.STUDY_PROJECT_FIRST.name()));
                    }
                });
            }

            // 为空默认是0
            int isTrain = 0;
            String resourceName = projectAdminUpdateDTO.getProName();
            PushResourceDTO pushResourceDTO = new PushResourceDTO().setResourceId(pushNoticeSetDTO.getResourceId())
                .setResourceName(resourceName).setIsTrain(isTrain).setOperateState(1)
                .setProgrammeId(projectAdminUpdateDTO.getProgrammeId());

            sendPushDTO.setPushResourceDTO(pushResourceDTO);

            PushAttributeDTO pushAttributeDTO = new PushAttributeDTO();

            boolean condition = false;
            pushAttributeDTO.setName(pushNoticeSetDTO.getResourceName());
            pushResourceDTO.setResourceId(projectAdminUpdateDTO.getId());

            pushResourceDTO.setResourceType(pushNoticeSetDTO.getResourceType());
            pushAttributeDTO.setExistSecondary(condition);
            pushAttributeDTO.setStartTime(projectAdminUpdateDTO.getStartTime());
            pushAttributeDTO.setEndTime(projectAdminUpdateDTO.getEndTime());
            pushAttributeDTO.setIntro(projectAdminUpdateDTO.getMark());
            // 使用签到点字段用来接受面授项目地址,字段复用
            pushAttributeDTO.setSignCheckinSpot(projectAdminUpdateDTO.getAddress());

            String systemName = (String) redisTemplate.opsForHash()
                .get(TenantRedisKeyConstant.SYSTEM_NAME_KEY, UserThreadContext.getTenantId());
            pushAttributeDTO.setSystemName(systemName);

            sendPushDTO.setPushAttributeDTO(pushAttributeDTO);

            pushFeign.sendPush(sendPushDTO);
        }

        // 发送资源操作事件消息
        sendMqMsg(oldProject, projectAdminUpdateDTO, project);

        handleCategoryCanDel();
        return project;
    }

    private void changePublish(ProjectAdminUpdateDTO projectAdminUpdateDTO, Project project) {
        // 开启了配置则可不校验直接发布
        String paraValue = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_916.getCode());
        if (StringUtils.isNotBlank(paraValue) && GeneralJudgeEnum.NEGATIVE.getValue().equals(Integer.valueOf(paraValue))
            && projectAdminUpdateDTO.getIsPublish() == PublishEnum.PUBLISHED.getValue()) {
            // 先校验项目下面是否有任务（没有任务的不允许发布）
            long count = taskService.count(new LambdaQueryWrapper<Task>().in(Task::getProId, project.getId()));
            if (count == 0) {
                throw new BusinessException(ProjectErrorNoEnum.PROJECT_NOT_HAVE_TASK_EXIST);
            }
        }
        Integer isPublish = projectAdminUpdateDTO.getIsPublish();
        if (Objects.nonNull(isPublish)) {
            if (isPublish == PublishStatusEnum.IS_PUBLISH.getValue()) {
                projectInfoDao.publishProject(project);
                project.setPublishBy(UserThreadContext.getUserId());
            } else {
                projectInfoDao.unPublishProject(project);
            }
        }
    }

    private void synUpdateAllTaskAndApp(ProjectAdminUpdateDTO projectAdminUpdateDTO, Project project) {
        // 更新快速培训时间同步更新快速培训下的任务及应用时间
        if (projectAdminUpdateDTO.getProjectType() != null && projectAdminUpdateDTO.getProjectType() == 1) {
            synUpdateQuickProjectAllTaskAndAllApp(project);
        }

        // 更新课程学习任务时间同步更新课程学习任务下的任务时间
        if (projectAdminUpdateDTO.getProjectType() != null && projectAdminUpdateDTO.getProjectType() == 2) {
            synUpdateCourseTaskProjectAllTaskTime(project);
        }
    }

    private void sendPlanInventoryMqMsg(Project oldProject, ProjectAdminUpdateDTO projectAdminUpdateDTO,
        Project project) {
        // 若学习项目关联了计划清单 则发送信息保存资源与的关联
        if (!(StringUtils.isBlank(oldProject.getPlanInventoryId()) && StringUtils.isBlank(
            project.getPlanInventoryId()))) {
            //检查项目中有任意一人进行结业，说明该项目是结业的
            Integer isCompletion = checkTrainCompletion(projectAdminUpdateDTO.getId());
            OrgDTO byId =
                StringUtils.isNotBlank(project.getOrgId()) ? orgFeign.getById(project.getOrgId()) : new OrgDTO();
            mqProducer.sendMsg(new InventoryResourceChangeEvent(
                new PlanStatisticSourceDataSaveDTO().setSourceId(projectAdminUpdateDTO.getId())
                    .setOldPlanInventoryId(oldProject.getPlanInventoryId())
                    .setSourceType(ExcitationEventCategoryEnum.PROJECT.getCode())
                    .setPlanInventoryId(project.getPlanInventoryId()).setStartTime(project.getStartTime())
                    .setEndTime(project.getEndTime()).setOrgId(project.getOrgId()).setOrgName(byId.getOrgName())
                    .setTrainName(project.getProName()).setTrainNo(oldProject.getProNo())
                    .setIsPublish(project.getIsPublish()).setIsCompletion(isCompletion)));
        }
    }

    private void sendMqMsg(Project oldProject, ProjectAdminUpdateDTO projectAdminUpdateDTO, Project project) {
        // 发送资源操作事件消息
        String type;
        if (Objects.equals(project.getProjectType(), 1)) {
            // 快速培训
            type = PushType.QUICK_PROJECT.getKey();
        } else {
            // 学习项目
            type = PushType.PROJECT.getKey();
        }
        mqProducer.sendMsg(new ResourceOperateEvent(
            Objects.equals(projectAdminUpdateDTO.getIsPublish(), GeneralJudgeEnum.NEGATIVE.getValue())
                ? OperationEnum.PUBLISH_CANCEL : OperationEnum.PUBLISH, type, oldProject.getId()));
        mqProducer.sendMsg(new ResourceOperateEvent(OperationEnum.UPDATE, type, oldProject.getId()));
    }

    private void setTrainPlanPrjRelation(ProjectAdminUpdateDTO projectAdminUpdateDTO, String projectId,
        Project project) {
        // 培训计划
        String planInventoryId = projectAdminUpdateDTO.getPlanInventoryId();
        if (StringUtils.isNotEmpty(planInventoryId)) {
            //获取旧关联
            TrainPlanPrjRelation trainPlanPrjRelation = trainPlanPrjRelationService.getOne(
                new LambdaQueryWrapper<TrainPlanPrjRelation>().eq(TrainPlanPrjRelation::getPrjId, projectId));
            //如果关联更改了
            if (trainPlanPrjRelation != null && !trainPlanPrjRelation.getTpId().equals(planInventoryId)) {
                //删除旧关联
                trainPlanPrjRelationService.removeById(trainPlanPrjRelation.getId());
                //添加新关联
                trainPlanPrjRelationService.save(
                    new TrainPlanPrjRelation().setId(newId()).setTpId(planInventoryId).setPrjId(projectId));
            }
            //如果未关联，则创建新的关联
            if (trainPlanPrjRelation == null) {
                //添加新关联
                trainPlanPrjRelationService.save(
                    new TrainPlanPrjRelation().setId(newId()).setTpId(planInventoryId).setPrjId(projectId));
            }
        } else {
            // 移除和培训计划的关联
            trainPlanPrjRelationService.remove(
                new LambdaQueryWrapper<TrainPlanPrjRelation>().eq(TrainPlanPrjRelation::getPrjId, project.getId()));
        }
    }

    private static void setProjectItem(ProjectAdminUpdateDTO projectAdminUpdateDTO, Project project) {
        // 添加默认应用：费用/资料
        if (!CollectionUtils.isEmpty(projectAdminUpdateDTO.getProjectItem())) {
            // 快速培训的时候不要默认应用：费用/资料
            StringBuilder projectItem;
            if (projectAdminUpdateDTO.getProjectType() != null
                && projectAdminUpdateDTO.getProjectType() == ProjectConstant.PROJECT_TYPE_QUICK) {
                projectItem = new StringBuilder();
            } else {
                projectItem = new StringBuilder(DEFAULT_APPLICATION);
            }
            // 面授项目添加应用：请假
            if (projectAdminUpdateDTO.getProjectType() != null
                && projectAdminUpdateDTO.getProjectType() == ProjectConstant.PROJECT_TYPE_FACE) {
                projectItem.append(CommonConstants.A_COMMA_IN_ENGLISH).append(APP_VACATE);
                projectItem.append(CommonConstants.A_COMMA_IN_ENGLISH).append(ProjectAppType.INVOICE.getAppType());
            }
            for (String item : projectAdminUpdateDTO.getProjectItem()) {
                if (DEFAULT_APPLICATION.contains(item) || APP_VACATE.contains(item)
                    || ProjectAppType.INVOICE.getAppType().contains(item)) {
                    continue;
                }
                projectItem.append(CommonConstants.A_COMMA_IN_ENGLISH).append(item);
            }
            project.setProjectItem(projectItem.toString());
        }
    }

    private void updateProjectLeader(Project oldProject, ProjectAdminUpdateDTO projectAdminUpdateDTO) {
        // 判断班主任是否发生修改,发生修改删除原有班主任的记录
        if (oldProject.getLeader() != null && !oldProject.getLeader().equals(projectAdminUpdateDTO.getLeaderId())) {
            LambdaQueryWrapper<App> appQueryWrapper = new LambdaQueryWrapper<>();
            appQueryWrapper.eq(App::getResourceType, ProjectAppType.APPLY.getNo());
            appQueryWrapper.eq(App::getProjectId, oldProject.getId());
            App app = appService.getOne(appQueryWrapper);
            if (app != null) {
                // 查询出项目以及用户id
                LambdaQueryWrapper<ApplyUser> applyUserQueryWrapper = new LambdaQueryWrapper<>();
                applyUserQueryWrapper.eq(ApplyUser::getApplyId, app.getResourceId());
                applyUserQueryWrapper.eq(ApplyUser::getUserId, oldProject.getLeader());
                ApplyUser applyUser = applyUserService.getOne(applyUserQueryWrapper);
                if (applyUser != null) {
                    ApplyDetailDTO applyInfo = applyService.getApplyInfo(applyUser.getApplyId());
                    String applyName = applyInfo.getApplyName();
                    String applyId = applyInfo.getId();
                    applyUserDao.delApplyUser(applyId, applyName, applyUser);
                }
            }

            // 删除项目关联,这里做物理删除，因为唯一索引判断不了is_del
            progressService.deleteProjectProgress(oldProject.getId(), oldProject.getLeader());
            // 删除项目任务关联
            taskProgressService.deleteProjectTaskProgress(oldProject.getId(), oldProject.getLeader());
            // 删除团队用户关联
            teamUserService.deleteProjectTeamUser(oldProject.getId(), oldProject.getLeader());
            // 更新讲师授课记录冗余班主任字段
            lecturerExaminationFeign.updateProjectLeader(
                new ExaminationLeaderUpdateDTO().setProjectId(oldProject.getId())
                    .setLeader(projectAdminUpdateDTO.getLeaderId()));
        }
    }

    private void saveProjectImage(Project oldProject, ProjectAdminUpdateDTO projectAdminUpdateDTO, String projectId) {
        // 保存图片 更新图片后图片名称会变
        if (StringUtils.isNotBlank(projectAdminUpdateDTO.getCoverImageName())) {
            fileFeign.deleteImageByBizIdAndBizType(projectId, ImageBizType.STUDY_PROJECT_FIRST.name());
            fileFeign.saveImage(projectId, ImageBizType.STUDY_PROJECT_FIRST.name(),
                projectAdminUpdateDTO.getCoverImageName(), projectAdminUpdateDTO.getCoverImagePath());
        } else if (oldProject.getIsUseDefaultImg() == 0 && projectAdminUpdateDTO.getIsUseDefaultImg() != null
            && projectAdminUpdateDTO.getIsUseDefaultImg() == 1) {
            fileFeign.deleteImageByBizIdAndBizType(projectId, ImageBizType.STUDY_PROJECT_FIRST.name());
            fileFeign.saveDefaultImage(projectId, ImageBizType.STUDY_PROJECT_FIRST.name(), "projectDefaultImage.png",
                "/file/quickProjectDefaultImage/projectDefaultImage.png");
        }
    }

    /**
     * 检查项目是否结业
     */
    private Integer checkTrainCompletion(String projectId) {
        return completionUserService.getProjectCompletion(projectId);
    }

    private void synUpdateQuickProjectAllTaskAndAllApp(Project project) {
        String projectId = project.getId();
        Date startTime = project.getStartTime();
        Date endTime = project.getEndTime();
        String projectName = project.getProName();
        // 任务
        List<Task> projectTaskList = taskService.getProjectTaskListByProId(projectId);
        projectTaskList.forEach(task -> {
            if ("exam".equals(task.getTaskType())) {
                task.setStartTime(startTime).setEndTime(endTime).setTaskName(projectName);
                ExamInfoDTO examInfoDTO = examFeign.getById(task.getTaskContent());
                examInfoDTO.setExamName(projectName).setStartTime(startTime).setEndTime(endTime);
                examFeign.updateExamById(examInfoDTO);
            }
            if ("course".equals(task.getTaskType())) {
                task.setStartTime(startTime).setEndTime(endTime).setTaskName(projectName);
                CourseInfoDTO courseInfoDTO = courseFeign.getById(task.getTaskContent());
                courseInfoDTO.setCourseName(projectName);
                courseFeign.updateCourseById(courseInfoDTO);
            }
        });
        taskService.updateBatchById(projectTaskList);

        // 签到
        ProjectSignTaskDTO sign = new ProjectSignTaskDTO();
        sign.setProId(projectId);
        sign.setStartTime(startTime);
        sign.setEndTime(endTime);
        sign.setTaskName(projectName);
        signFeign.updateTimeByQuickProjectId(sign);

        // 评估
        EvalDTO evalDTO = new EvalDTO();
        evalDTO.setEvaluationObject(projectId);
        evalDTO.setStartTime(startTime);
        evalDTO.setEndTime(endTime);
        evalDTO.setEvalName(projectName);
        evaluationFeign.updateTimeByQuickProjectId(evalDTO);
    }

    private void synUpdateCourseTaskProjectAllTaskTime(Project project) {
        String projectId = project.getId();
        Date startTime = project.getStartTime();
        Date endTime = project.getEndTime();
        // 任务
        List<Task> projectTaskList = taskService.getProjectTaskListByProId(projectId);
        if (CollectionUtils.isEmpty(projectTaskList)) {
            return;
        }
        projectTaskList.forEach(task -> {
            if (StringUtils.isEmpty(task.getTaskContent())) {
                log.error("this exam task without task content, task id is : {}", task.getId());
                return;
            }

            //引用创建的考试不更新时间
            if (!"exam".equals(task.getTaskType()) || task.getCreateType() == 1) {
                task.setStartTime(startTime).setEndTime(endTime);
            }

            if ("exam".equals(task.getTaskType()) && task.getCreateType() == 1) {
                ExamInfoDTO examInfoDTO = examFeign.getById(task.getTaskContent());
                if (examInfoDTO == null) {
                    log.error("this exam not exist in exam db, exam id is : {}", task.getTaskContent());
                    return;
                }
                examInfoDTO.setStartTime(startTime).setEndTime(endTime);
                examFeign.updateExamById(examInfoDTO);
            }
        });
        taskService.updateBatchById(projectTaskList);
    }

    /**
     * 保存或更新项目阶段
     *
     * @param dtoList 项目阶段信息
     * @param id      学习项目id
     */
    private void saveOrUpdateProPhase(List<ProphaseSaveDTO> dtoList, String id, Integer projectType) {
        // 面授项目 不需要在修改项目接口内进行删除阶段
        if (projectType != null && projectType == ProjectConstant.PROJECT_TYPE_FACE) {
            return;
        }

        // 原项目阶段信息
        List<Phase> oldPhases = phaseService.list(new LambdaQueryWrapper<Phase>().eq(Phase::getProId, id));

        if (CollectionUtils.isEmpty(dtoList)) {
            if (!CollectionUtils.isEmpty(oldPhases)) {
                // 删除原项目阶段信息
                oldPhases.forEach(old -> phaseDao.delPhase(old));
            }
            return;
        }
        // 当前更新项目阶段信息
        List<Phase> newPhases = dtoList.stream().map(
                prophaseSaveDTO -> new Phase().setId(prophaseSaveDTO.getId()).setProId(id)
                    .setPhaseName(prophaseSaveDTO.getName()).setSortNo(prophaseSaveDTO.getSort()))
            .toList();
        List<Phase> adds = newPhases.stream().filter(phase -> StringUtils.isEmpty(phase.getId()))
            .toList().stream().map(phase -> phase.setId(newId()))
            .toList();
        adds.forEach(e -> phaseDao.savePhase(e));
        List<Phase> updates = newPhases.stream()
            .filter(phase -> oldPhases.stream().map(Phase::getId).toList().contains(phase.getId()))
            .toList();
        updates.forEach(e -> phaseDao.updatePhase(e));
        List<Phase> deletes = oldPhases.stream().filter(
            oldPhase -> !newPhases.stream().filter(newPhase -> StringUtils.isNotEmpty(newPhase.getId()))
                .toList().stream().map(Phase::getId).toList()
                .contains(oldPhase.getId())).toList();
        deletes.forEach(e -> phaseDao.delPhase(e));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Project> remove(String ids) {
        if (StringUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }

        String[] idList = ids.split(CommonConstants.A_COMMA_IN_ENGLISH);
        Set<String> idSet = Arrays.stream(idList).collect(Collectors.toSet());
        // 校验项目是否已发布，已发布不可以直接删除
        List<Project> projects = listByIds(idSet);
        if (projects.isEmpty()) {
            return new ArrayList<>();
        }
        List<Integer> projectTypes = projects.stream().map(Project::getProjectType).toList();
        checkForDelete(projects);
        List<Project> projectList = list(new LambdaQueryWrapper<Project>().in(Project::getId, idSet));
        projectList.forEach(p -> projectInfoDao.delProject(p));

        // 删除项目下直接创建的考试任务
        deleteProjectExam(idSet);

        // 更新讲师授课记录冗余的项目状态
        lecturerExaminationFeign.deleteProject(idSet);

        // 删除导师记录
        long mentorRecordCount = mentorRecordService.count(
            new LambdaQueryWrapper<MentorRecord>().in(MentorRecord::getProId, idSet));
        if (mentorRecordCount > 0) {
            List<MentorRecord> mentorRecords = mentorRecordService.list(
                new LambdaQueryWrapper<MentorRecord>().in(MentorRecord::getProId, idSet));
            mentorRecords.forEach(mentorRecord -> mentorRecordDao.delMentorRecord(mentorRecord));
            //删除项目下任务 快速培训暂时不删除
            if (!CollectionUtils.isEmpty(projects)) {
                // 获取删除的学习项目/ 2-快速培训类型
                // 快速培训暂时不删除任务/资源
                Project project = projects.get(0);
                if (project.getProjectType() != 1) {
                    taskService.remove(String.join(CommonConstants.A_COMMA_IN_ENGLISH, idSet));
                }
            }
        }

        //删除培训计划关系
        List<TrainPlanPrjRelation> trainPlanPrjRelations = trainPlanPrjRelationService.list(
            new LambdaQueryWrapper<TrainPlanPrjRelation>().in(TrainPlanPrjRelation::getPrjId, idSet));

        if (!CollectionUtils.isEmpty(trainPlanPrjRelations)) {
            // 更新培训计划状态
            for (TrainPlanPrjRelation trainPlanPrjRelation : trainPlanPrjRelations) {
                updateTrainPlanMain(trainPlanPrjRelation.getTpId());
            }
        }

        // 发送消息，同步状态到岗位发展管理
        ActivityStatusChangeEvent.sendMsg(new ArrayList<>(idSet), ResourceTypeCodeEnum.PROJECT, null, 1, mqProducer);
        // 发送资源修改信息
        FirstInfoContentEnum resourceType = getResourceType(projectTypes.get(0));
        mqProducer.sendMsg(
            new ResourceChangeEvent(resourceType.name(), new ArrayList<>(idSet), GeneralJudgeEnum.CONFIRM.getValue(),
                GeneralJudgeEnum.NEGATIVE.getValue()));

        // 发送资源操作事件消息
        for (Project project : projects) {
            String type;
            if (Objects.equals(project.getProjectType(), 1)) {
                // 快速培训
                type = PushType.QUICK_PROJECT.getKey();
            } else {
                // 学习项目
                type = PushType.PROJECT.getKey();
            }
            mqProducer.sendMsg(new ResourceOperateEvent(OperationEnum.DELETE, type, project.getId()));
        }

        handleCategoryCanDel();
        idSet.forEach(id -> projectViewLimitComponent.delViewLimit(id));
        return projects;
    }

    private static void checkForDelete(List<Project> projects) {
        for (Project project : projects) {
            if (Objects.equals(project.getIsPublish(), PublishStatusEnum.IS_PUBLISH.getValue())) {
                if (Objects.equals(project.getProjectType(), ProjectConstant.PROJECT_TYPE_QUICK)) {
                    throw new BusinessException(ProjectErrorNoEnum.QUICK_PROJECT_IS_PUBLISH_DELETE_FILED);
                }
                throw new BusinessException(ProjectErrorNoEnum.PROJECT_IS_PUBLISH_DELETE_FILED);
            }
        }
    }

    /**
     * 删除项目下直接创建的考试任务
     *
     * @param idSet 项目id集合
     */
    private void deleteProjectExam(Set<String> idSet) {
        LambdaQueryWrapper<Task> queryWrapper = new LambdaQueryWrapper<Task>()
            .select(Task::getTaskContent)
            .eq(Task::getTaskType, ProjectTaskTypeEnum.EXAM.getTaskType())
            .eq(Task::getCreateType, ProjectConstant.CREATE_TYPE_DIRECT)
            .in(Task::getProId, idSet);
        List<Task> examTaskList = taskService.list(queryWrapper);
        if (examTaskList.isEmpty()) {
            return;
        }

        // 获取考试ID列表
        List<String> idList = examTaskList.stream().map(Task::getTaskContent).filter(StringUtils::isNotBlank)
            .toList();
        if (!CollectionUtils.isEmpty(idList)) {
            // 组装考试ID
            String ids = String.join(",", idList);

            // 不再校验考试的发布状态，直接执行删除操作
            examFeign.removeExam(ids, GeneralJudgeEnum.NEGATIVE.getValue());
        }
    }


    @Override
    @Log(type = Type.DELETE, targetId = "#project.id", targetName = "#project.proName", targetType = Log.TargetType.PROJECT)
    public void delLog(Project project) {
        // 记录日志
    }

    @Override
    public List<Project> publish(PublishDTO publishDTO) {
        List<Project> projects = listByIds(publishDTO.getIds());
        List<Integer> projectType = projects.stream().map(Project::getProjectType).toList();
        //发布项目
        publishCheck(publishDTO);
        //更新项目状态
        List<Project> projectList = listByIds(publishDTO.getIds());
        projectList.forEach(p -> {
            if (publishDTO.getIsPublish() == PublishEnum.PUBLISHED.getValue()) {
                projectInfoDao.publishProject(p);
            } else {
                projectInfoDao.unPublishProject(p);
            }
        });

        // 发送消息，同步状态到岗位发展管理
        ActivityStatusChangeEvent.sendMsg(publishDTO.getIds(), ResourceTypeCodeEnum.PROJECT, publishDTO.getIsPublish(),
            null, mqProducer);
        // 发送资源修改信息
        FirstInfoContentEnum resourceType = getResourceType(projectType.get(0));
        mqProducer.sendMsg(
            new ResourceChangeEvent(resourceType.name(), publishDTO.getIds(), GeneralJudgeEnum.NEGATIVE.getValue(),
                publishDTO.getIsPublish()));
        // 发送资源修改信息
        mqProducer.sendMsg(new InventoryResourceUpdateChangeEvent(publishDTO.getIds(), publishDTO.getIsPublish()));

        // 发送资源操作事件消息
        for (Project project : projects) {
            String type;
            if (Objects.equals(project.getProjectType(), 1)) {
                // 快速培训
                type = PushType.QUICK_PROJECT.getKey();
            } else {
                // 学习项目
                type = PushType.PROJECT.getKey();
            }

            // 获取学习项目资源业务类型
            ResourceTypeEnum resourceTypeEnum = getResourceTypeEnum(project.getType(), project.getProjectType());
            // 面授项目未适配我的任务，先不同步消息
            if (resourceTypeEnum != ResourceTypeEnum.FACE_PROJECT) {
                mqProducer.sendMsg(new ResourceSyncEvent(
                    new ResourceSyncDTO(OperationEnum.UPDATE, resourceTypeEnum.name(), project.getId(), null, null,
                        null, null, publishDTO.getIsPublish(), 0, null, null, UserThreadContext.getUserId(),
                        new Date())));
            }

            mqProducer.sendMsg(new ResourceOperateEvent(
                Objects.equals(publishDTO.getIsPublish(), GeneralJudgeEnum.NEGATIVE.getValue())
                    ? OperationEnum.PUBLISH_CANCEL : OperationEnum.PUBLISH, type, project.getId()));
            // 获取学习项目资源业务类型
            ResourceTypeEnum resourceTypeEnum = ProjectBizImpl.getResourceTypeEnum(project.getType(),
                project.getProjectType());
            if (resourceTypeEnum != null) {
                // 面授项目未适配我的任务，先不同步消息
                if (resourceTypeEnum != ResourceTypeEnum.FACE_PROJECT) {
                    mqProducer.sendMsg(new ResourceSyncEvent(
                        new ResourceSyncDTO(OperationEnum.UPDATE, resourceTypeEnum.name(), project.getId(), null, null,
                            null, null, publishDTO.getIsPublish(), 0, null, null, UserThreadContext.getUserId(),
                            new Date())));
                }
            }
        }
        // 删除学习项目首页缓存

        return projects;
    }

    public static ResourceTypeEnum getResourceTypeEnum(int type, int projectType) {
        ResourceTypeEnum resourceTypeEnum = null;
        switch (projectType) {
            case ProjectConstant.PROJECT_TYPE_FACE:
                resourceTypeEnum = ResourceTypeEnum.FACE_PROJECT;
                break;
            case ProjectConstant.PROJECT_TYPE_DEFAULT:
                resourceTypeEnum = type == ProjectConstant.PROJECT_TYPE_FIXED_DATE ? ResourceTypeEnum.TRAIN_COURSE
                    : ResourceTypeEnum.PERIODIC_PROJECT;
                break;
            case ProjectConstant.PROJECT_TYPE_QUICK:
                resourceTypeEnum = ResourceTypeEnum.RAPID_TRAIN;
                break;
            case ProjectConstant.PROJECT_COURSE_TASK:
                resourceTypeEnum = ResourceTypeEnum.COURSE_LEARNING_TASK;
                break;
            default:
        }
        return resourceTypeEnum;
    }

    private void publishCheck(PublishDTO publishDTO) {
        // 开启了配置才能直接发布
        String paraValue = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_916.getCode());
        if (StringUtils.isNotBlank(paraValue) && GeneralJudgeEnum.NEGATIVE.getValue().equals(Integer.valueOf(paraValue))
            && publishDTO.getIsPublish() == PublishEnum.PUBLISHED.getValue()) {
            // 先校验项目下面是否有任务（没有任务的不允许发布）
            long count = taskService.count(new LambdaQueryWrapper<Task>().in(Task::getProId, publishDTO.getIds()));
            if (count == 0) {
                if (publishDTO.getFormType() != null && publishDTO.getFormType() == 1) {
                    throw new BusinessException(ProjectErrorNoEnum.PROJECT_TRAIN_NOT_HAVE_TASK_EXIST);
                } else {
                    throw new BusinessException(ProjectErrorNoEnum.PROJECT_NOT_HAVE_TASK_EXIST);
                }
            }
        }
    }

    /**
     * 获取资源类型
     */
    private FirstInfoContentEnum getResourceType(Integer projectType) {
        if (Objects.equals(projectType, ProjectConstant.PROJECT_TYPE_FACE)) {
            return FirstInfoContentEnum.faceProject;
        }
        // 默认返回 project ，头条通知添加其他项目类型时再扩展
        return FirstInfoContentEnum.project;
    }

    @Override
    public PageInfo<RemindersDTO> remindersList(String projectId, RemindersQuery remindersQueryDTO) {
        String userIdString = remindersQueryDTO.getUserIds();
        ArrayList<String> userIdList = new ArrayList<>();
        if (StringUtils.isNotBlank(userIdString)) {
            Set<String> collect = Arrays.stream(userIdString.split(CommonConstants.A_COMMA_IN_ENGLISH))
                .collect(Collectors.toSet());
            userIdList.addAll(collect);
        }

        PageInfo<String> projectUserIdsPage = PageMethod.startPage(remindersQueryDTO.getPageNo(),
                remindersQueryDTO.getPageSize())
            .doSelectPageInfo(() -> baseMapper.queryProjectReminders(projectId, userIdList));

        HashSet<String> userIds = new HashSet<>(projectUserIdsPage.getList());

        Map<String, UserDTO> userNameMap = userFeign.getUserNameMapByIds(userIds);

        List<RemindersDTO> remindersDTOList = projectUserIdsPage.getList().stream().map(userId -> {
            RemindersDTO remindersDTO = new RemindersDTO();
            remindersDTO.setUserId(userId);
            UserDTO userDTO = userNameMap.get(userId);
            remindersDTO.setFullName(userDTO.getFullName());
            remindersDTO.setLoginName(userDTO.getLoginName());
            remindersDTO.setOrgName(userDTO.getOrgName());
            Progress progress = progressService.getOne(
                new LambdaQueryWrapper<Progress>().eq(Progress::getUserId, userId).eq(Progress::getProId, projectId));
            if (progress == null) {
                remindersDTO.setState(ProjectConstant.PROJECT_JOIN_STATUS_NOT_JOIN);
            } else {
                remindersDTO.setState(ProjectConstant.PROJECT_JOIN_STATUS_JOIN);
            }
            return remindersDTO;
        }).toList();

        PageInfo<RemindersDTO> pageInfo = new PageInfo<>();
        BeanUtils.copyProperties(projectUserIdsPage, pageInfo);
        pageInfo.setList(remindersDTOList);
        return pageInfo;
    }

    @Override
    public void userReminders(UserRemindersDTO userRemindersDTO, String projectId) {
        Project project = getById(projectId);
        if (project == null) {
            throw new BusinessException(ProjectErrorNoEnum.PROJECT_NOT_EXIST);
        }

        List<Task> taskList = taskService.list(new LambdaQueryWrapper<Task>().eq(Task::getProId, projectId)
            .eq(Task::getIsPublish, PublishEnum.PUBLISHED.getValue()));
        List<String> sysUserList = userRemindersDTO.getUserIds();
        Map<String, UserDTO> userNameMap = userFeign.getUserNameMapByIds(sysUserList);
        StringBuilder pushArea = new StringBuilder();
        sysUserList.forEach(user -> pushArea.append(userNameMap.get(user).getFullName()).append(","));
        userMailSend(project, taskList, sysUserList, pushArea);

    }

    public void userMailSend(Project project, List<Task> taskList, List<String> sysUserList, StringBuilder pushArea) {
        StringBuilder content = new StringBuilder();
        content.append(project.getProName());
        if (!taskList.isEmpty()) {
            content.append("<br>学习任务名称：");
            for (int i = 0; i < taskList.size(); i++) {
                if (i == 0) {
                    content.append(i + 1).append(".").append(taskList.get(i).getTaskName()).append("<br> ");
                }
                if (i > 0) {
                    content.append("             ").append(i + 1).append(".").append(taskList.get(i).getTaskName())
                        .append("<br> ");
                }
            }
        }
        Integer projectType = project.getProjectType();
        if (!StringUtils.isNotBlank(project.getReferencedId()) || Objects.equals(projectType,
            ProjectConstant.PROJECT_TYPE_DEFAULT)) {
            pushComponent.remindPush(sysUserList, pushArea, RemindPushEnum.PROJECT.getBizType(), project.getId(),
                content.toString());
        } else if (Objects.equals(projectType, ProjectConstant.PROJECT_TYPE_QUICK)) {
            pushComponent.remindPush(sysUserList, pushArea, RemindPushEnum.QUICK_PROJECT.getBizType(), project.getId(),
                content.toString());
        } else {
            pushComponent.remindPush(sysUserList, pushArea, RemindPushEnum.TRAINCLASS.getBizType(), project.getId(),
                content.toString());
        }
    }

    @Override
    public ProjectDetailDTO findProjectDetailById(String projectId) {
        ProjectDetailDTO projectDetailDTO = new ProjectDetailDTO();
        ProjectAdminDTO projectDTO = one(projectId);
        BeanUtils.copyProperties(projectDTO, projectDetailDTO);

        //拷贝学习阶段
        List<ProjectDetailDTO.ProphaseSaveDTO> newProphaseSaveDTOList = new ArrayList<>();
        List<ProjectDTO.ProphaseSaveDTO> prophaseSaveDTOList = projectDTO.getProphaseSaveDTOS();
        if (!CollectionUtils.isEmpty(prophaseSaveDTOList)) {

            prophaseSaveDTOList.forEach(prophaseSaveDTO -> {
                ProjectDetailDTO.ProphaseSaveDTO dto = new ProjectDetailDTO.ProphaseSaveDTO();
                BeanUtils.copyProperties(prophaseSaveDTO, dto);
                newProphaseSaveDTOList.add(dto);
            });
        }
        projectDetailDTO.setProphaseSaveDTOS(newProphaseSaveDTOList);
        return projectDetailDTO;
    }

    @Override
    public ProjectAdminDTO one(String id) {
        Project project = getById(id);
        if (project == null) {
            throw new BusinessException(ProjectErrorNoEnum.PROJECT_NOT_EXIST);
        }

        ProjectAdminDTO projectDTO = new ProjectAdminDTO();
        BeanUtils.copyProperties(project, projectDTO);
        // 返回归属部门名称
        projectDTO.setOrgName(orgFeign.getById(project.getOrgId()).getOrgName());
        //图片
        NamePath namePath = fileFeign.getImageFileNamePath(id, ImageBizType.STUDY_PROJECT_FIRST.name());
        Optional.ofNullable(namePath).ifPresent(e -> {
            projectDTO.setCoverImagePath(e.getPath());
            projectDTO.setCoverImageUrl(e.getUrl());
        });
        //关联计划
        projectDTO.setTrainPlanId(project.getPlanId());
        TrainPlanPrjRelation trainPlanPrjRelation = trainPlanPrjRelationService.getOne(
            new LambdaQueryWrapper<TrainPlanPrjRelation>().eq(TrainPlanPrjRelation::getPrjId, id));
        if (trainPlanPrjRelation != null) {
            //根据计划清单ID 查询培训计划名称
            String planId = trainPlanPrjRelation.getTpId();
            if (!StringUtils.isEmpty(planId)) {
                Set<String> planIdSet = new HashSet<>();
                planIdSet.add(planId);
                Map<String, String> planNameMap = planInventoryFeign.getPlanNameBatchPlanInventoryIds(planIdSet);
                //查询培训计划
                String planName = planNameMap.get(planId);
                projectDTO.setPlanInventoryId(planId);
                projectDTO.setTrainPlanName(planName);
            }
        }
        //班主任
        if (StringUtils.isNotEmpty(project.getLeader())) {
            String fullName = userFeign.getUserFullNameById(project.getLeader());
            if (fullName != null) {
                projectDTO.setLeaderId(project.getLeader());
                projectDTO.setLeaderName(fullName);
            }
        }

        //教室
        setClassRoom(project, projectDTO);

        //下发范围
        projectDTO.setLimit(projectViewLimitComponent.getViewLimitBaseInfo(id));

        //项目阶段
        projectDTO.setProphaseSaveDTOS(projectDao.phaseList(id));

        //项目开通应用
        projectDTO.setProjectItem(Arrays.stream(
                project.getProjectItem().replace(DEFAULT_APPLICATION, "").split(CommonConstants.A_COMMA_IN_ENGLISH))
            .filter(StringUtils::isNotEmpty).toList());

        //关联能力数量
        projectDTO.setProjectElementCount(
            projectElementService.lambdaQuery().eq(ProjectElement::getProId, projectDTO.getId()).count());

        // 取报名数据对象
        ApplySaveDTO applySaveDTO = new ApplySaveDTO();
        // 构造默认不启用报名数据
        applySaveDTO.setIsAvailable(AvailableEnum.NOT_AVAILABLE.getValue());
        applySaveDTO.setProjectId(id);
        // 取关联关系
        List<App> applyAppList = appDao.list(new LambdaQueryWrapper<App>().eq(App::getProjectId, id)
            .eq(App::getResourceType, ProjectAppType.APPLY.getNo()).orderByDesc(App::getCreateTime));

        // 取培训部门名称
        if (StringUtils.isNotBlank(project.getTrainOrgId())) {
            Optional.ofNullable(orgFeign.getById(project.getTrainOrgId()))
                .ifPresent(orgDTO -> projectDTO.setTrainOrgName(orgDTO.getOrgName()));
        }

        if (!CollectionUtils.isEmpty(applyAppList)) {
            // 取最新的报名
            String applyId = applyAppList.get(0).getResourceId();
            if (StringUtils.isNotBlank(applyId)) {
                Apply apply = applyDao.getById(applyId);
                BeanUtils.copyProperties(apply, applySaveDTO);
                // 校验默认是否被删除,删除的模板让编辑的时候重新选择
                PlanFormInfoDTO formTemplateInfo = formTemplateFeign.findFormTemplateInfoById(
                    apply.getFormTemplateId());
                if (Objects.isNull(formTemplateInfo)) {
                    applySaveDTO.setFormTemplateId(null);
                }
                applySaveDTO.setPersonCount(apply.getPlanPerson());
            }
        }
        projectDTO.setApplySaveDTO(applySaveDTO);
        // 海报分享
        projectDTO.setPosterShareIsOpen(posterShareService.isOpenShare());
        projectDTO.setPosterShareDTO(posterShareService.getPosterShare(project.getId(),
            ProjectBizImpl.getResourceTypeEnum(project.getType(), project.getProjectType())));

        return projectDTO;
    }

    private void setClassRoom(Project project, ProjectAdminDTO projectDTO) {
        if (StringUtils.isNotEmpty(project.getRoom())) {
            Classroom classroom = classroomService.getById(project.getRoom());
            if (classroom != null) {
                projectDTO.setRoom(project.getRoom());
                projectDTO.setRoomName(classroom.getRoomName());
            }
        }
    }

    @Override
    public ResourceViewLimitInfoDTO getProjectViewLimit(String projectId) {
        Project project = Optional.ofNullable(getById(projectId))
            .orElseThrow(() -> new BusinessException(ProjectErrorNoEnum.PROJECT_NOT_EXIST));
        //下发范围
        ResourceViewLimitInfoDTO resourceViewLimitInfoDTO = new ResourceViewLimitInfoDTO();
        ViewLimitBaseInfoDTO viewLimitBaseInfo = projectViewLimitComponent.getViewLimitBaseInfo(project.getId());
        BeanUtils.copyProperties(viewLimitBaseInfo, resourceViewLimitInfoDTO);
        return resourceViewLimitInfoDTO.setViewType(project.getViewType());
    }

    @Override
    public PageInfo<ProjectPageDTO> getProjectPage(ProjectPageQuery query) {
        Integer pageNo = query.getPageNo();
        Integer pageSize = query.getPageSize();
        Integer isExpire = query.getIsExpire();
        String userId = UserThreadContext.getUserId();
        query.setUserId(userId);
        if (null != query.getRole() && 2 == query.getRole()) {
            query.setUserManageLevelPath(orgFeign.findUserManageAreaLevelPath(UserThreadContext.getUserId()));
        }

        PageInfo<ProjectPageDTO> pageInfo = PageMethod.startPage(pageNo, pageSize, query.isCount())
            .doSelectPageInfo(() -> baseMapper.getProjectPage(query));
        pageInfo.setIsLastPage(pageSize != pageInfo.getList().size());
        // 学习项目id集合
        List<String> projectIds = pageInfo.getList().stream().map(ProjectPageDTO::getId).toList();

        // 查询项目图片
        Map<String, String> imageUrlMap = fileFeign.getImageUrlsByIds(projectIds,
            ImageBizType.STUDY_PROJECT_FIRST.name());
        // 查询项目应用-报名（借助客户id存储学习项目id输出）
        Map<String, Apply> applyMap = applyService.getByProjectIdList(projectIds).stream()
            .collect(Collectors.toMap(Apply::getCustomerId, Function.identity(), (o1, o2) -> o1));
        // 查询项目应用-报名用户
        Map<String, ApplyUserDTO> applyUserMap = applyUserService.getByProjectIdList(projectIds).stream()
            .collect(Collectors.toMap(ApplyUserDTO::getProjectId, Function.identity(), (k1, k2) -> k1));
        Map<String, List<String>> resourceMemberCoinMap = getResourceMemberCoinMap(pageInfo.getList());
        // 响应数据处理
        pageInfo.getList().forEach(project -> {
            String imageUrl = imageUrlMap.get(project.getId());
            Apply apply = applyMap.get(project.getId());
            ApplyUserDTO applyUser = applyUserMap.get(project.getId());

            project.setMemberIconUrls(resourceMemberCoinMap.get(project.getId()));
            project.setImageUrl(imageUrl);
            project.setIsClassTeacher(userId.equals(project.getLeader()) ? 1 : 0);
            project.setIsOperation(null != project.getProgress() ? 1 : 0);

            // 固定周期项目 项目时间处理
            setPeriodDateForFixedCycle(project);

            // 固定日期项目 项目时间处理
            if (project.getType().equals(ProjectConstant.PROJECT_TYPE_FIXED_DATE)) {
                setPeriodDateForFixedDate(project, apply, isExpire);
            }

            // 报名相关处理
            if (null != project.getProgress()) {
                project.setApplyStatus(ProjectConstant.APPLY_STATUS_PASS);
            } else if (null != applyUser) {
                project.setApplyStatus(applyUser.getApplyState());
            } else {
                project.setApplyStatus(ProjectConstant.APPLY_STATUS_NOT_APPLY);
            }

            // 是否结业
            project.setIsCompletion(
                null != project.getProgress() ? completionUserService.getCompletionStatus(userId, project.getId()) : 0);

        });
        return pageInfo;
    }

    private static void setPeriodDateForFixedDate(ProjectPageDTO project, Apply apply, Integer isExpire) {
        project.setStarted(1);
        project.setPersistentDays(Math.toIntExact(
            (System.currentTimeMillis() - project.getStartTime().getTime()) / (24 * 60 * 60 * 1000)));
        // 项目开始时间、结束时间处理（未参加项目列表查询时项目时间优先取报名时间显示，默认项目时间）
        if (null == project.getProgress() && null != apply) {
            project.setStartTime(apply.getStartTime());
            project.setEndTime(apply.getEndTime());
        }
        // 搜索时判断待参加项目是否过期（搜索时是否过期字段为null）
        if (null == project.getProgress() && null == isExpire) {
            Date endDate = null != apply ? apply.getEndTime() : project.getEndTime();
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime start = LocalDateTime.ofInstant(project.getStartTime().toInstant(),
                ZoneId.systemDefault());
            LocalDateTime end = LocalDateTime.ofInstant(endDate.toInstant(), ZoneId.systemDefault());
            // 有报名 校验当前时间是否大于报名结束时间，是则过期
            // 无报名 校验当前时间是否大于项目结束时间且项目开始时间是否大于一个月前，是则过期
            boolean timeIsExpire =
                null != apply ? now.isAfter(end) : now.isAfter(end) && start.isAfter(now.minusMonths(1));
            project.setIsEnd(timeIsExpire ? 1 : 0);
        }
    }

    private static void setPeriodDateForFixedCycle(ProjectPageDTO project) {
        // 固定周期项目 项目时间处理
        if (project.getType().equals(ProjectConstant.PROJECT_TYPE_FIXED_CYCLE)) {
            project.setStarted(null != project.getProgress() ? 1 : 0);
            if (null != project.getProgress()) {
                // 已持续的天数
                project.setPersistentDays(Math.toIntExact(
                    (System.currentTimeMillis() - project.getJoinTime().getTime()) / (24 * 60 * 60 * 1000)));
                // 设置周期项目的周期
                project.setPeriodStartDate(project.getJoinTime());
                project.setPeriodEndDate(
                    com.xxl.job.core.util.DateUtil.addDays(project.getJoinTime(), Math.toIntExact(project.getPeriod())));
            }
        }
    }

    @Override
    public PageInfo<FaceProjectPageDTO> getFaceProjectPage(FaceProjectPageQuery query) {
        Integer pageNo = query.getPageNo();
        Integer pageSize = query.getPageSize();
        String userId = UserThreadContext.getUserId();
        query.setUserId(userId);

        PageInfo<FaceProjectPageDTO> pageInfo = PageMethod.startPage(pageNo, pageSize, true)
            .doSelectPageInfo(() -> baseMapper.getFaceProjectPage(query));
        pageInfo.setIsLastPage(pageSize != pageInfo.getList().size());
        Map<String, OrgDTO> orgMap = new HashMap<>();
        Set<String> orgIds = pageInfo.getList().stream().filter(e -> StringUtils.isNotBlank(e.getTrainOrgId()))
            .map(FaceProjectPageDTO::getTrainOrgId).collect(Collectors.toSet());
        if (!CollectionUtils.isEmpty(orgIds)) {
            orgMap = orgFeign.getOrgMapByIds(orgIds);
        }
        Map<String, OrgDTO> finalOrgMap = orgMap;
        // 学习项目id集合
        List<String> projectIds = pageInfo.getList().stream().map(FaceProjectPageDTO::getId)
            .toList();

        // 查询项目图片
        Map<String, String> imageUrlMap = fileFeign.getImageUrlsByIds(projectIds,
            ImageBizType.STUDY_PROJECT_FIRST.name());
        // 查询项目应用-报名（借助客户id存储学习项目id输出）
        Map<String, Apply> applyMap = applyService.getByProjectIdList(projectIds).stream()
            .collect(Collectors.toMap(Apply::getCustomerId, Function.identity(), (o1, o2) -> o1));
        // 查询项目应用-报名用户
        Map<String, ApplyUserDTO> applyUserMap = applyUserService.getByProjectIdList(projectIds).stream()
            .collect(Collectors.toMap(ApplyUserDTO::getProjectId, Function.identity(), (k1, k2) -> k1));
        List<String> applyIdList = pageInfo.getList().stream().map(FaceProjectPageDTO::getApplyId)
            .toList();

        // 查询报名是否已经满了
        Map<String, ApplyCheckDTO> checkDTOMap = new HashMap<>();
        // 查询已报名人员
        Map<String, Set<String>> applyUserNumMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(applyIdList)) {
            checkDTOMap = applyService.applyCheckByApplyIdList(applyIdList);
            applyUserNumMap = applyUserService.getApplyUserMap(applyIdList);
        }

        // 获取自定义信息
        Map<String, List<RemarkDTO>> remarksMap = remarkService.getRemarksMap(projectIds);
        // 响应数据处理
        Map<String, ApplyCheckDTO> finalCheckDTOMap = checkDTOMap;
        Map<String, Set<String>> finalApplyUserNumMap = applyUserNumMap;
        pageInfo.getList().forEach(project -> {
            String imageUrl = imageUrlMap.get(project.getId());
            Apply apply = applyMap.get(project.getId());
            ApplyUserDTO applyUser = applyUserMap.get(project.getId());

            project.setImageUrl(imageUrl);
            // 自定义信息
            project.setRemarkList(remarksMap.get(project.getId()));
            // 设置是否报名已满属性
            Optional.ofNullable(finalCheckDTOMap.get(project.getApplyId()))
                .ifPresent(data -> project.setIsApplyFull(data.getIsApplyFull()));
            //设置培训部门名称
            OrgDTO orgDTO = finalOrgMap.get(project.getTrainOrgId());
            if (Optional.ofNullable(orgDTO).isPresent()) {
                project.setTrainOrgName(orgDTO.getOrgName());
            }
            // 设置已报名人数
            Optional.ofNullable(finalApplyUserNumMap.get(project.getApplyId()))
                .ifPresentOrElse(strings -> project.setApplyUserNum(strings.size() + ""),
                    () -> project.setApplyUserNum("0"));
            // 项目是否有效 默认有效
            Integer isValid = GeneralJudgeEnum.CONFIRM.getValue();
            Integer isEnd = GeneralJudgeEnum.NEGATIVE.getValue();
            Integer inApplyTime = GeneralJudgeEnum.CONFIRM.getValue();
            Date currentTime = new Date();
            Date startTime = project.getStartTime();
            Date endTime = project.getEndTime();
            Date applyStartTime = project.getApplyStartTime();
            Date applyEndTime = project.getApplyEndTime();
            isValid = checkProjectIsValid(startTime, endTime, applyStartTime, applyEndTime, currentTime, isValid);
            isEnd = checkProjectIsEnd(startTime, endTime, currentTime, isEnd);
            inApplyTime = checkProjectInApplyTime(applyStartTime, applyEndTime, currentTime, inApplyTime);

            project.setIsValid(isValid);
            project.setIsEnd(isEnd);
            project.setInApplyTime(inApplyTime);

            // 设置模板类型属性
            setProjectApplyFormTemplateType(project);

            setProjectStarted(project);

            // 报名相关处理
            int applyRequire = ProjectConstant.APPLY_REQUIRED_NO;
            if (null != apply && apply.getIsPublish() == 1 && apply.getIsAvailable() == 1) {
                applyRequire = ProjectConstant.APPLY_REQUIRED_YES;
            }
            project.setApplyRequired(applyRequire);
            if (null != applyUser) {
                project.setApplyStatus(applyUser.getApplyState());
            } else {
                project.setApplyStatus(ProjectConstant.APPLY_STATUS_NOT_APPLY);
            }
            Optional.ofNullable(applyUser).ifPresent(u -> project.setApplyRecordId(u.getId()));

            // 是否结业
            setProjectIsCompletion(project, userId);

            // 上课时间、地点 隐藏
            if (Objects.equals(project.getIsHideTime(), JudgeEnum.CONFIRM.getValue())) {
                project.setStartTime(null);
                project.setEndTime(null);
            }
            if (Objects.equals(project.getIsHideAddress(), JudgeEnum.CONFIRM.getValue())) {
                project.setAddress(null);
            }
        });
        return pageInfo;
    }

    private void setProjectIsCompletion(FaceProjectPageDTO project, String userId) {
        // 是否结业
        project.setIsCompletion(
            null != project.getProgress() ? completionUserService.getCompletionStatus(userId, project.getId()) : 0);
    }

    private static void setProjectStarted(FaceProjectPageDTO project) {
        // 固定周期项目 项目时间处理
        if (project.getType().equals(ProjectConstant.PROJECT_TYPE_FIXED_CYCLE)) {
            project.setStarted(null != project.getProgress() ? 1 : 0);
        }

        // 固定日期项目 项目时间处理
        if (project.getType().equals(ProjectConstant.PROJECT_TYPE_FIXED_DATE)) {
            project.setStarted(1);
        }
    }

    private static void setProjectApplyFormTemplateType(FaceProjectPageDTO project) {
        if (StringUtils.isNotBlank(project.getApplyFormTemplateId())) {
            project.setApplyFormTemplateType(ApplyFormTemplateType.CUSTOM.getValue());
            if (Objects.equals(project.getApplyFormTemplateId(),
                com.wunding.learn.common.constant.project.ProjectConstant.DEFAULT_APPLY_FORM_TEMPLATE_ID)) {
                project.setApplyFormTemplateType(ApplyFormTemplateType.DEFAULT.getValue());
            }
        }
    }

    private static Integer checkProjectInApplyTime(Date applyStartTime, Date applyEndTime, Date currentTime,
        Integer inApplyTime) {
        if (ObjectUtils.allNotNull(applyStartTime, applyEndTime) && (applyStartTime.after(currentTime)
            || applyEndTime.before(currentTime))) {
            inApplyTime = GeneralJudgeEnum.NEGATIVE.getValue();
        }
        return inApplyTime;
    }

    private static Integer checkProjectIsEnd(Date startTime, Date endTime, Date currentTime, Integer isEnd) {
        if (ObjectUtils.allNotNull(startTime, endTime) && currentTime.after(endTime)) {
            isEnd = GeneralJudgeEnum.CONFIRM.getValue();
        }
        return isEnd;
    }

    private static Integer checkProjectIsValid(Date startTime, Date endTime, Date applyStartTime, Date applyEndTime,
        Date currentTime, Integer isValid) {
        if (ObjectUtils.allNotNull(startTime, endTime) && ObjectUtils.allNotNull(applyStartTime, applyEndTime) && (
            startTime.after(currentTime) || endTime.before(currentTime)) && (applyStartTime.after(currentTime)
            || applyEndTime.before(currentTime))) {
            isValid = GeneralJudgeEnum.NEGATIVE.getValue();
        }
        return isValid;
    }

    private Map<String, List<String>> getResourceMemberCoinMap(List<ProjectPageDTO> trainActivityList) {
        Map<String, List<String>> result = new HashMap<>();
        if (CollectionUtils.isEmpty(trainActivityList)) {
            return result;
        }
        // 取培訓的會員權限map
        List<String> resourceIds = trainActivityList.stream().map(ProjectPageDTO::getId).toList();

        // 取会员身份对应图标
        Map<String, Set<String>> memberIconMap = memberCardFeign.getMemberIconMapByResourceIds(resourceIds);
        // 返回身份对应资源的会员图标Map
        for (String resourceId : resourceIds) {
            List<String> icons = new ArrayList<>();
            Set<String> iconMap = memberIconMap.get(resourceId);
            if (null != iconMap) {
                icons = new ArrayList<>(iconMap);
            }
            result.put(resourceId, icons);
        }

        return result;

    }


    @Override
    public void exchangeStudy(String projectId) {
        // 学习项目是否存在
        Project project = Optional.ofNullable(getById(projectId))
            .orElseThrow(() -> new BusinessException(ProjectErrorNoEnum.PROJECT_NOT_EXIST));
        // 学习项目是否需要兑换
        if (Objects.isNull(project.getEnableConsumeJoin()) || Objects.equals(project.getEnableConsumeJoin(),
            GeneralJudgeEnum.NEGATIVE.getValue())) {
            throw new BusinessException(ProjectErrorNoEnum.ERR_PROJECT_NOT_CONSUME_JOIN);
        }

        //  减少用户激励(积分兑换是否足够兑换学习项目、是否已经兑换)
        UserExcitationReduceDTO reduceDTO = new UserExcitationReduceDTO().setEnableRepeatReduce(false);
        reduceDTO.setUserId(UserThreadContext.getUserId()).setEventEnum(ExcitationEventEnum.PROJECT_CONSUME_STUDY)
            .setExcitationNum(project.getConsumeExcitationNum()).setExcitationType(project.getConsumeExcitationType())
            .setTargetId(projectId).setTargetName(project.getProName()).setTradeType(TradeTypeEnum.PROJECT.name())
            .setBizId(projectId).setBizType(TradeTypeEnum.PROJECT.name())
            .setIsExchange(GeneralJudgeEnum.CONFIRM.getValue());
        excitationFeign.reduceUserExcitationWithCheck(reduceDTO);
    }

    @Override
    @Async
    public void allUserReminders(String projectId) {
        Project project = getById(projectId);
        if (project == null) {
            throw new BusinessException(ProjectErrorNoEnum.PROJECT_NOT_EXIST);
        }

        List<Task> taskList = taskService.list(new LambdaQueryWrapper<Task>().eq(Task::getProId, projectId)
            .eq(Task::getIsPublish, PublishEnum.PUBLISHED.getValue()));

        PageInfo<String> projectUserIdsPage = new PageInfo<>();
        projectUserIdsPage.setHasNextPage(true);
        int pageNo = 1;
        int pageSize = 500;
        while (projectUserIdsPage.isHasNextPage()) {
            projectUserIdsPage = resourceViewLimitService.getUserIdByResourceId(projectId,
                LimitTable.ProjectViewLimit.name(), null, pageNo, pageSize);

            HashSet<String> userIds = new HashSet<>(projectUserIdsPage.getList());

            List<String> sysUserList = projectUserIdsPage.getList();

            Map<String, UserDTO> userNameMap = userFeign.getUserNameMapByIds(userIds);
            StringBuilder pushArea = new StringBuilder();
            sysUserList.forEach(user -> pushArea.append(userNameMap.get(user).getFullName()).append(","));
            userMailSend(project, taskList, sysUserList, pushArea);
            pageNo++;
        }

    }

    @Override
    public void completionProject(String projectId, String updateName) {
        baseMapper.completionProject(projectId, updateName);
    }

    @Override
    public ProjectInfoDTO getProjectInfo(String id, Integer isIgnoreView) {
        String userId = UserThreadContext.getUserId();
        Project project = getProject(id, isIgnoreView, userId);

        // 项目任务完成进度百分比
        Progress progress = progressService.getByProjectAndUserId(id, userId);
        // 项目应用报名信息（校验是否可以报名信息）
        ApplyCheckDTO applyCheckDTO = applyUserService.checkEnableApplyInfo(project.getId(), userId);
        // 项目参与人数
        long joinCount = progressService.getJoinCount(id);

        ProjectInfoDTO projectInfoDTO = new ProjectInfoDTO().setId(project.getId()).setTitle(project.getProName())
            .setImageUrl(fileFeign.getImageUrl(project.getId(), ImageBizType.STUDY_PROJECT_FIRST.name()))
            .setDescription(project.getProDesc())
            .setProgress(null == progress ? BigDecimal.ZERO : progress.getProgressPercent()).setIsClassTeacher(
                userId.equals(project.getLeader()) ? ProjectConstant.IS_TEACHER_YES : ProjectConstant.IS_TEACHER_NO)
            .setTaskCount(taskService.getProjectTaskCount(id)).setApplyRequired(applyCheckDTO.getApplyRequired())
            .setApplyStatus(applyCheckDTO.getApplyStatus()).setEnableCancelApply(applyCheckDTO.getEnableCancelApply())
            .setIsOperation(null != progress ? 1 : 0).setEnableApply(applyCheckDTO.getEnableApply())
            .setIsManagerOrLecturer(lecturerExaminationFeign.getLecturerUserIdsByProjectId(id).contains(userId) ? 1 : 0)
            .setPeriod(Math.toIntExact(project.getCycleDay())).setType(project.getType()).setShowExcitation(0)
            .setEnableConsumeJoin(project.getEnableConsumeJoin())
            .setConsumeExcitationNum(project.getConsumeExcitationNum())
            .setConsumeExcitationType(project.getConsumeExcitationType())
            .setIsExchange(project.getEnableExcitationExchange()).setIsFinishConsume(Boolean.TRUE.equals(
                excitationFeign.isFinishExcitationTrade(
                    new UserExcitationTradeBaseDTO().setUserId(UserThreadContext.getUserId())
                        .setEventEnum(ExcitationEventEnum.PROJECT_CONSUME_STUDY)
                        .setExcitationNum(project.getConsumeExcitationNum())
                        .setExcitationType(project.getConsumeExcitationType()).setTargetId(project.getId())
                        .setTradeType("project"))) ? GeneralJudgeEnum.CONFIRM.getValue()
                : GeneralJudgeEnum.NEGATIVE.getValue()).setProjectType(project.getProjectType())
            .setJoinNumber(BigDecimal.valueOf(joinCount));

        // 根据项目类型填充项目开始时间和结束时间
        setPersistentDays(id, project, projectInfoDTO, userId);

        // 是否需要初始化任务
        projectInfoDTO.setNeedInit(
            taskProgressService.countByProjectIdAndUserId(id, userId) > 0 ? GeneralJudgeEnum.NEGATIVE.getValue()
                : GeneralJudgeEnum.CONFIRM.getValue());
        return projectInfoDTO;
    }

    private void setPersistentDays(String id, Project project, ProjectInfoDTO projectInfoDTO, String userId) {
        // 固定日期
        if (project.getType().equals(ProjectConstant.PROJECT_TYPE_FIXED_DATE)) {
            projectInfoDTO.setStartTime(project.getStartTime());
            projectInfoDTO.setEndTime(project.getEndTime());
            if (project.getStartTime().after(new Date())) {
                throw new BusinessException(ProjectErrorNoEnum.PROJECT_NOT_START);
            }
            // 校验是否已经加入学习项目
            ProjectJoinQuery projectJoinQuery = new ProjectJoinQuery();
            projectJoinQuery.setUserId(UserThreadContext.getUserId());
            projectJoinQuery.setProjectId(id);
            boolean isJoin = progressService.isJoinProject(projectJoinQuery);
            if (isJoin) {
                projectInfoDTO.setIsEnd(ProjectConstant.PROJECT_IS_EXPIRED_NO);
            } else {
                projectInfoDTO.setIsEnd(
                    project.getEndTime().compareTo(new Date()) < 0 ? ProjectConstant.PROJECT_IS_EXPIRED_YES
                        : ProjectConstant.PROJECT_IS_EXPIRED_NO);
            }

            projectInfoDTO.setPersistentDays(Math.toIntExact(
                (System.currentTimeMillis() - project.getStartTime().getTime()) / (24 * 60 * 60 * 1000)));
        }
        // 固定周期
        if (project.getType().equals(ProjectConstant.PROJECT_TYPE_FIXED_CYCLE)) {
            Progress userProgress = progressService.getByProjectAndUserId(id, userId);
            projectInfoDTO.setPersistentDays(null != userProgress ? Math.toIntExact(
                (System.currentTimeMillis() - userProgress.getCreateTime().getTime()) / (24 * 60 * 60 * 1000)) : 0);
        }
    }

    @NotNull
    private Project getProject(String id, Integer isIgnoreView, String userId) {
        // 1 校验下发权限
        if (Objects.equals(GeneralJudgeEnum.NEGATIVE.getValue(), isIgnoreView)
            && (!resourceViewLimitService.checkViewLimit(id, LimitTable.ProjectViewLimit.name(), userId))) {
            throw new BusinessException(ErrorNoEnum.ERR_NO_POWER);
        }
        // 2 查该学习项目是否发布
        Project project = projectDao.projectExist(id);
        if (Objects.equals(PublishEnum.NOT_PUBLISH.getValue(), project.getIsPublish())) {
            throw new BusinessException(ErrorNoEnum.ERR_IS_NO_PUBLISH);
        }
        if (Objects.equals(ProjectConstant.PROJECT_TYPE_FACE, project.getProjectType())) {
            Date now = new Date();
            if (project.getStartTime().after(now)) {
                throw new BusinessException(ProjectErrorNoEnum.FACE_PROJECT_NOT_START);
            }
        }
        return project;
    }

    @Override
    public PageInfo<ProjectManagerDTO> relatedProjectList(String projectId, BaseEntity baseEntity) {

        PageInfo<ProjectManagerDTO> selectPageInfo = PageMethod.startPage(baseEntity.getPageNo(),
            baseEntity.getPageSize()).doSelectPageInfo(() -> baseMapper.relatedProjectList(projectId));

        Set<String> publishUserIdSet = selectPageInfo.getList().stream().map(ProjectManagerDTO::getPublishBy)
            .filter(StringUtils::isNotEmpty).collect(Collectors.toSet());

        if (!CollectionUtils.isEmpty(publishUserIdSet)) {
            Map<String, UserDTO> userNameMapByIds = userFeign.getUserNameMapByIds(publishUserIdSet);

            selectPageInfo.getList().forEach(projectManagerDTO -> {
                UserDTO userDTO = userNameMapByIds.get(projectManagerDTO.getPublishBy());
                if (null != userDTO) {
                    projectManagerDTO.setPublishBy(userDTO.getFullName());
                }
            });
        }
        return selectPageInfo;
    }

    @Override
    public PageInfo<SubordinateProjectDTO> getSubordinateProject(SubordinateProjectQuery subordinateProjectQuery) {
        UserDTO user = userFeign.getUserById(subordinateProjectQuery.getUserId());
        if (Optional.ofNullable(user).isEmpty()) {
            throw new BusinessException(UserErrorNoEnum.USER_NOT_EXISTS);
        }
        PageInfo<SubordinateProjectDTO> pageInfo = PageMethod.startPage(subordinateProjectQuery.getPageNo(),
                subordinateProjectQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.getSubordinateProject(subordinateProjectQuery));
        Set<String> projectIdSet = pageInfo.getList().stream().map(SubordinateProjectDTO::getId)
            .filter(org.apache.commons.lang3.StringUtils::isNotBlank).collect(Collectors.toSet());
        Map<String, String> imageMap = fileFeign.getImageUrlsByIds(projectIdSet,
            ImageBizType.STUDY_PROJECT_FIRST.name());
        for (SubordinateProjectDTO dto : pageInfo.getList()) {
            Optional.ofNullable(imageMap.get(dto.getId())).ifPresent(dto::setImageUrl);
            if (dto.getIsFinish() == 1) {
                dto.setProgress("100%");
            } else if (dto.getTotalScore() > 0 && dto.getScore() > 0) {
                NumberFormat numberFormat = NumberFormat.getInstance();
                numberFormat.setMaximumFractionDigits(0);
                numberFormat.setRoundingMode(RoundingMode.DOWN);
                String progress = numberFormat.format((float) dto.getScore() / (float) dto.getTotalScore() * 100);
                dto.setProgress(progress.concat("%"));
            } else {
                dto.setProgress("0%");
            }
            dto.setIsEnd(0);
            dto.setApplyStatus(1);
            dto.setIsOperation(0);
        }
        return pageInfo;
    }

    @Override
    public UserIdpProjectDTO getUserIdpProjectByYear(UserIdpProjectQuery userIdpProjectQuery) {
        UserIdpProjectDTO userIdpProject = new UserIdpProjectDTO();
        UserDTO userInfo = userFeign.getUserById(UserThreadContext.getUserId());
        if (Optional.ofNullable(userInfo).isEmpty()) {
            throw new BusinessException(UserErrorNoEnum.USER_NOT_EXISTS);
        }
        userIdpProject.setPostName(userInfo.getPostName());
        //设置当前用户id
        userIdpProjectQuery.setUserId(UserThreadContext.getUserId());
        //获取用户idp项目数据
        List<UserIdpProjectVO> list = projectMapper.getProjectIdpTreeByYear(userIdpProjectQuery);
        // 整合每个项目状态
        List<IdpProjectDTO> userIdpProjectDTOList = new ArrayList<>();
        //判断项目数据是否为空
        if (!CollectionUtils.isEmpty(list)) {
            //当不为空时
            //使用迭代器获取每一个项目信息
            Iterator<UserIdpProjectVO> iterator = list.iterator();
            //获取当前时间
            Calendar calendar = Calendar.getInstance();
            Date now = calendar.getTime();
            while (iterator.hasNext()) {
                UserIdpProjectVO userIdpProjectVO = iterator.next();
                //判断用户项目状态
                //如果项目未参与且已经结束，就删除项目
                if (userIdpProjectVO.getIsOperation() == 0 && userIdpProjectVO.getEndTime() != null && now.after(
                    userIdpProjectVO.getEndTime())) {
                    iterator.remove();
                    continue;
                }
                IdpProjectDTO userIdpProjectDTO = new IdpProjectDTO();
                //设置项目id
                userIdpProjectDTO.setId(userIdpProjectVO.getId());
                //设置项目名称
                userIdpProjectDTO.setProName(userIdpProjectVO.getProName());
                //设置项目开始时间
                if (null == userIdpProjectVO.getStartTime()) {
                    // 将时间设置为上一分钟的
                    Date startTime = DateUtil.getBeforeOrAfterTime(new Date(), Calendar.MINUTE, -1);
                    userIdpProjectDTO.setStartTime(startTime);
                } else {
                    userIdpProjectDTO.setStartTime(userIdpProjectVO.getStartTime());
                }
                //设置项目是否完成
                userIdpProjectDTO.setIsFinish(userIdpProjectVO.getIsFinish());
                //是否可以直接进入任务: 0不可以, 1可以
                userIdpProjectDTO.setIsOperation(userIdpProjectVO.getIsOperation());
                //添加项目对象到userIdpProjectDTOList
                userIdpProjectDTOList.add(userIdpProjectDTO);
            }
        }
        if (!CollectionUtils.isEmpty(userIdpProjectDTOList)) {
            //根据用户项目项目开始时间对项目进行排序
            List<MonthIdpProjectDTO> monthIdpProjectList = new ArrayList<>();
            Collections.sort(userIdpProjectDTOList, ((o1, o2) -> o2.getStartTime().compareTo(o1.getStartTime())));
            for (int i = 12; i > 0; i--) {
                MonthIdpProjectDTO monthIdpProjectDTO = new MonthIdpProjectDTO();
                monthIdpProjectDTO.setMonth(i);
                LinkedList<IdpProjectDTO> userIdpProjectDTOLinkedList = getIdpProjectDTOS(userIdpProjectDTOList, i);
                monthIdpProjectDTO.setIdpList(userIdpProjectDTOLinkedList);
                monthIdpProjectList.add(monthIdpProjectDTO);
            }
            userIdpProject.setMonthIdpList(monthIdpProjectList);
        }
        return userIdpProject;
    }

    @NotNull
    private static LinkedList<IdpProjectDTO> getIdpProjectDTOS(List<IdpProjectDTO> userIdpProjectDTOList, int i) {
        LinkedList<IdpProjectDTO> userIdpProjectDTOLinkedList = new LinkedList<>();
        for (IdpProjectDTO userIdpProjectDTO : userIdpProjectDTOList) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(userIdpProjectDTO.getStartTime());
            // 注意月份是从0开始的,比如当前7月，获得的month为6
            Integer month = cal.get(Calendar.MONTH) + 1;
            if (month == i) {
                userIdpProjectDTO.setDesc(null);
                userIdpProjectDTO.setStatus(0);
                // 用户参与的状态: 尚未进行 0, 正在进行 1, 已经完成 2 （1 这种中间状态，如果某项活动没有这种逻辑，则只存在0 和 2）
                userIdpProjectDTO.setUserStatus(0);
                if (userIdpProjectDTO.getIsFinish() == 1) {
                    userIdpProjectDTO.setStatus(2);
                    userIdpProjectDTO.setUserStatus(2);
                } else {
                    if (null != userIdpProjectDTO.getStartTime() && userIdpProjectDTO.getStartTime()
                        .before(new Date())) {
                        userIdpProjectDTO.setStatus(1);
                    }
                }
                userIdpProjectDTOLinkedList.add(userIdpProjectDTO);
            }
        }
        return userIdpProjectDTOLinkedList;
    }

    @Override
    public PageInfo<MentorForm> getMentorNoSolveList(MentorNoSolveQuery mentorNoSolveQuery) {
        return PageMethod.startPage(mentorNoSolveQuery.getPageNo(), mentorNoSolveQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.getMentorNoSolveList(mentorNoSolveQuery));
    }

    @Override
    public PageInfo<LearnProjectDTO> getStudentProjectList(StudentProjectQuery studentProjectQuery) {
        return PageMethod.startPage(studentProjectQuery.getPageNo(), studentProjectQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.getStudentProjectList(studentProjectQuery));
    }


    @Override
    public void projectExportData(ProjectManagerQueryDTO query) {
        query.setType(1);
        IExportDataDTO exportDataDTO = new IExportDataDTO() {
            @Override
            public List getData(Integer pageNo, Integer pageSize) {
                IProjectService service = SpringUtil.getBean(ProjectConstant.PROJECT_SERVICE, IProjectService.class);
                query.setExport(true);
                query.setPageNo(pageNo);
                query.setPageSize(pageSize);
                PageInfo<ProjectManagerDTO> pageInfo = service.queryPage(query);
                List<Map<String, Object>> listExportDTOS = new ArrayList<>();

                for (ProjectManagerDTO dto : pageInfo.getList()) {
                    Map<String, Object> beanMap = JsonUtil.parseObjectToMap(dto);
                    listExportDTOS.add(beanMap);
                }
                return listExportDTOS;
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.ProjectFixedCycle;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.ProjectFixedCycle.getType();
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public void projectFixedDateExportData(ProjectManagerQueryDTO query) {
//        query.setType(0);
        query.setExport(true);
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IProjectService, ProjectManagerDTO>(query) {

            @Override
            protected IProjectService getBean() {
                return SpringUtil.getBean(ProjectConstant.PROJECT_SERVICE, IProjectService.class);
            }

            @Override
            protected PageInfo<ProjectManagerDTO> getPageInfo() {
                return getBean().queryPage(query);
            }

            @Override
            public ExportBizType getType() {
                // 快速培训
                if (Objects.equals(query.getProjectType(), ProjectConstant.PROJECT_TYPE_QUICK)) {
                    return ExportBizType.QuickProject;
                }
                // 线上课程学习
                if (Objects.equals(query.getProjectType(), ProjectConstant.PROJECT_COURSE_TASK)) {
                    return ExportBizType.CourseTaskProject;
                }
                // 面授项目
                if (Objects.equals(query.getProjectType(), ProjectConstant.PROJECT_TYPE_FACE)) {
                    return ExportBizType.FaceProject;
                }
                return ExportBizType.ProjectFixedDate;
            }

            @Override
            public String getFileName() {
                // 快速培训
                if (Objects.equals(query.getProjectType(), ProjectConstant.PROJECT_TYPE_QUICK)) {
                    return ExportFileNameEnum.QuickProject.getType();
                }
                // 线上课程学习
                if (Objects.equals(query.getProjectType(), ProjectConstant.PROJECT_COURSE_TASK)) {
                    return ExportFileNameEnum.CourseTaskProject.getType();
                }
                // 面授项目
                if (Objects.equals(query.getProjectType(), ProjectConstant.PROJECT_TYPE_FACE)) {
                    return ExportFileNameEnum.FaceProject.getType();
                }
                return ExportFileNameEnum.StudyProject.getType();
            }

            @Override
            protected void afterCreateBeanMap(Map<String, Object> map) {
                final String typeStr = "type";
                final String startTimeStr = "startTime";
                final String endTimeStr = "endTime";

                // 周期项目
                Object type = map.get(typeStr);
                if (type.equals(1)) {
                    map.put(startTimeStr, I18nUtil.getMessage("不涉及"));
                    map.put(endTimeStr, I18nUtil.getMessage("不涉及"));
                }
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }


    @Override
    public PageInfo<ProjectPageDTO> searchProject(ProjectSearchQuery projectSearchQuery) {
        ProjectPageQuery query = new ProjectPageQuery();
        BeanUtils.copyProperties(projectSearchQuery, query);
        query.setCount(true);
        return getProjectPage(query);
    }

    @Override
    public PageInfo<ProjectPageDTO> queryTrainProject(TrainProjectQuery trainProjectQuery) {
        ProjectPageQuery query = new ProjectPageQuery();
        BeanUtils.copyProperties(trainProjectQuery, query);
        return getProjectPage(query);
    }

    @Override
    public List<String> getInvalidProjectId(Collection<String> projectIdList) {
        return baseMapper.getInvalidProjectId(projectIdList);
    }

    @Override
    public PageInfo<ProjectPageDTO> findProjectHomePageList(ProjectHomePageQuery projectHomePageQuery, String userId) {
        // 指定分类查时，要包含所有子分类
        if (StringUtils.isNotBlank(projectHomePageQuery.getCategoryId())) {
            Set<String> categoryIds = categoryFeign.getCurAndSubCategoryIds(
                projectHomePageQuery.getCategoryId(), CategoryTypeEnum.TrainType.name());
            categoryIds.add(projectHomePageQuery.getCategoryId());
            projectHomePageQuery.setCategoryIds(categoryIds);
        }
        projectHomePageQuery.setUserId(UserThreadContext.getUserId());
        PageInfo<ProjectPageDTO> sqlPageInfo = PageMethod.startPage(projectHomePageQuery.getPageNo(),
                projectHomePageQuery.getPageSize(), false)
            .doSelectPageInfo(() -> baseMapper.selectProjectHomePageList(projectHomePageQuery));
        List<ProjectPageDTO> projectPageDTOList = sqlPageInfo.getList();
        if (CollectionUtils.isEmpty(projectPageDTOList)) {
            return sqlPageInfo;
        }
        List<String> projectIds = projectPageDTOList.stream().map(ProjectPageDTO::getId).toList();
        // 查询项目图片
        Map<String, String> imageUrlsByIds = fileFeign.getImageUrlsByIds(projectIds,
            ImageBizType.STUDY_PROJECT_FIRST.name());
        projectPageDTOList.forEach(p -> {
            p.setImageUrl(imageUrlsByIds.get(p.getId()));
            p.setIsOperation(null != p.getProgress() ? 1 : 0);
        });
        sqlPageInfo.setList(projectPageDTOList);
        sqlPageInfo.setIsLastPage(projectHomePageQuery.getPageSize() > sqlPageInfo.getList().size());
        /*redisTemplate.opsForValue().set(redisKey, sqlPageInfo);
        redisTemplate.expire(redisKey, 300, TimeUnit.SECONDS);*/
        return sqlPageInfo;
    }

    @Override
    public void initProject(String projectId, Integer isIgnoreView) {
        // 获取登录用户信息
        String userId = UserThreadContext.getUserId();
        if (Optional.ofNullable(userId).isEmpty()) {
            throw new BusinessException(UserErrorNoEnum.USER_NOT_EXISTS);
        }

        // 校验下发权限
        if (Objects.equals(GeneralJudgeEnum.NEGATIVE.getValue(), isIgnoreView)
            && !resourceViewLimitService.checkViewLimit(projectId, LimitTable.ProjectViewLimit.name(), userId)) {
            throw new BusinessException(ErrorNoEnum.ERR_NO_POWER);
        }
        // 校验快速培训开始结束时间
        Project byId = this.getById(projectId);

        // 是否存在
        if (byId == null) {
            throw new BusinessException(ErrorNoEnum.ERR_RESOURCE_NOT_EXIST);
        }
        // 2 查该学习项目是否发布
        if (PublishEnum.NOT_PUBLISH.getValue() == byId.getIsPublish()) {
            throw new BusinessException(ErrorNoEnum.ERR_IS_NO_PUBLISH);
        }
        // 3 校验时间
        if (byId.getProjectType() == ProjectConstant.PROJECT_TYPE_QUICK) {
            if (byId.getStartTime().getTime() > System.currentTimeMillis()) {
                //未开始
                throw new BusinessException(ErrorNoEnum.ERR_NOT_ALLOW_OPERATE);
            }
            if (byId.getEndTime().getTime() < System.currentTimeMillis()) {
                //已过期
                throw new BusinessException(ErrorNoEnum.ERR_NOT_ALLOW_OPERATE);
            }
        }

        // 加入学习项目
        IProjectService projectService = SpringUtil.getBean(ProjectConstant.PROJECT_SERVICE, IProjectService.class);
        projectService.joinProject(userId, projectId);
    }

    @Override
    @ProjectRequestRepeatIntercept("/joinProject")
    public void joinProject(String userId, String projectId) {
        ProjectJoinQuery projectJoinQuery = new ProjectJoinQuery();
        projectJoinQuery.setProjectId(projectId);
        projectJoinQuery.setUserId(userId);
        Project project = getById(projectId);
        if (Objects.isNull(project)) {
            throw new BusinessException(ProjectErrorNoEnum.PROJECT_NOT_EXIST);
        }

        // 校验是否已经加入学习项目
        boolean isJoin = progressService.isJoinProject(projectJoinQuery);
        if (isJoin) {
            return;
        }

        // 查询项目任务列表
        List<Task> taskList = taskService.getProjectTaskListByProId(projectJoinQuery.getProjectId());

        // 周期项目任务完成时间分配
        boolean isFixedCycle = project.getType() == ProjectConstant.PROJECT_TYPE_FIXED_CYCLE;
        int period = 0;
        LocalDateTime startTime = null;
        LocalDateTime endTime = null;
        if (isFixedCycle && (taskList != null && !CollectionUtils.isEmpty(taskList))) {
            // 分配间隔天数
            long periodDay = project.getCycleDay() / taskList.size();
            period = periodDay > 0 ? (int) periodDay : 1;
            // 分配开始时间
            LocalDateTime today = LocalDateTime.now().withHour(12).withMinute(0).withSecond(0);
            LocalDateTime tomorrow = today.plusDays(1);
            startTime = LocalDateTime.now().getHour() < 12 ? today : tomorrow;
            // 分配结束时间
            endTime = startTime.plusDays(project.getCycleDay());
        }

        // 生成任务完成表记录
        List<TaskProgress> list = new ArrayList<>();
        int finalPeriod = period;
        AtomicReference<LocalDateTime> finalStartTime = new AtomicReference<>(startTime);
        AtomicReference<LocalDateTime> finalEndTime = new AtomicReference<>(endTime);
        // 用户信息
        UserDTO userInfo = Optional.ofNullable(userFeign.getUserById(projectJoinQuery.getUserId()))
            .orElse(new UserDTO());
        assert taskList != null;
        taskList.forEach(task -> {
            TaskProgress taskProgress = new TaskProgress();
            taskProgress.setId(newId());
            taskProgress.setTaskId(task.getId());
            taskProgress.setUserId(projectJoinQuery.getUserId());
            taskProgress.setProId(project.getId());
            taskProgress.setOrgId(userInfo.getOrgId());
            taskProgress.setOrgName(userInfo.getOrgName());
            taskProgress.setUserName(userInfo.getFullName());
            // 分配时间
            setTaskProgressTime(task, isFixedCycle, finalStartTime, taskProgress, finalPeriod, finalEndTime);
            Map<String, Integer> userResourceMap = taskService.getUserFinish(task, List.of(userId));
            taskService.checkActivityFinish(taskProgress, userResourceMap);
            list.add(taskProgress);
        });
        //通过getBean方法加入代理，让事务在joinProjectSave启用(保存入库，前面校验太耗时间了，把事务粒度缩小减少代码响应慢)
        getBean().joinProjectSave(projectJoinQuery, projectId, project, userId, period, list);
    }

    @Transactional(rollbackFor = Exception.class)
    public void joinProjectSave(ProjectJoinQuery projectJoinQuery, String projectId, Project project, String userId,
        int period,
        List<TaskProgress> list) {
        // 保存项目完成表记录
        Progress progress = progressService.insertProgress(projectJoinQuery);
        // 保存学习项目用户学习情况表记录
        projectUserStudyConditionService.insertProjectUserStudyCondition(projectJoinQuery);

        if (!list.isEmpty()) {
            taskProgressService.saveBatch(list);
            // 将全部已完成资源做奖励同步
            List<TaskProgress> finishActivityProgressList = list.stream()
                .filter(a -> GeneralJudgeEnum.CONFIRM.getValue().equals(a.getIsFinish())).toList();
            // 规避全部资源都是完成的情况
            if (!CollectionUtils.isEmpty(finishActivityProgressList) && finishActivityProgressList.size() == list.size()
                && projectMapper.checkProjectTaskProgressIsAllFinish(projectId, userId) == 0) {
                //如果学习项目的任务，对这个人的任务都已经完成，则将学习项目置为完成
                Progress progress1 = progressService.lambdaQuery().eq(Progress::getProId, projectId)
                    .eq(Progress::getUserId, userId).one();
                if (progress1.getStatus() != null && progress1.getStatus() == 0) {
                    progress1.setStatus(1);
                    progress1.setFinishTime(new Date());
                    progress1.setProgressPercent(BigDecimal.valueOf(100));
                    progressService.updateById(progress1);
                }
            }
        }
        // 发送参加项目消息
        sendJoinEvent(progress, project);
    }

    private static void setTaskProgressTime(Task task, boolean isFixedCycle,
        AtomicReference<LocalDateTime> finalStartTime,
        TaskProgress taskProgress, int finalPeriod, AtomicReference<LocalDateTime> finalEndTime) {
        if (isFixedCycle) {
            // 周期项目
            LocalDateTime taskStartTime = finalStartTime.get();
            taskProgress.setStartTime(Date.from(taskStartTime.atZone(ZoneId.systemDefault()).toInstant()));
            finalStartTime.set(taskStartTime.plusDays(finalPeriod));
            LocalDateTime taskEndTime = finalStartTime.get();
            taskProgress.setEndTime(Date.from(taskEndTime.atZone(ZoneId.systemDefault()).toInstant()));
            if (!finalEndTime.get().isAfter(taskEndTime)) {
                finalStartTime.set(taskEndTime.minusDays(finalPeriod));
            }
        } else {
            // 日期项目
            taskProgress.setStartTime(task.getStartTime());
            taskProgress.setEndTime(task.getEndTime());
        }
    }

    private void sendJoinEvent(Progress progress, Project project) {
        ResourceTypeEnum resourceTypeEnum = ProjectBizImpl.getResourceTypeEnum(project.getType(),
            project.getProjectType());
        if (resourceTypeEnum == null) {
            log.info("学习项目资源业务类型为空");
            return;
        }
        // 有报名时，取报名的时间
        Date createTime = progress.getCreateTime();
        App app = appService.getOne(new LambdaQueryWrapper<App>().eq(App::getProjectId, project.getId())
            .eq(App::getResourceType, ProjectAppType.APPLY.getNo()).eq(App::getIsAvailable, 1));
        if (app != null) {
            ApplyUser applyUser = applyUserService.getByUserId(progress.getUserId(), app.getResourceId());
            if (applyUser != null) {
                createTime = applyUser.getApplyTime();
                // 面授项目未适配我的任务，先不同步消息
                if (resourceTypeEnum != ResourceTypeEnum.FACE_PROJECT) {
                    mqProducer.sendMsg(new ResourceRecordSyncEvent(
                        new ResourceRecordSyncDTO(OperationEnum.DELETE, resourceTypeEnum.name(), applyUser.getId(),
                            null, null, null, null, null, null, null)));
                }
            }
        }
        // 面授项目未适配我的任务，先不同步消息
        if (resourceTypeEnum != ResourceTypeEnum.FACE_PROJECT) {
            //参加项目消息
            mqProducer.sendMsg(new ResourceRecordSyncEvent(
                new ResourceRecordSyncDTO(OperationEnum.CREATE, resourceTypeEnum.name(), progress.getId(),
                    project.getId(), progress.getUserId(), 0, UserThreadContext.getUserId(), createTime,
                    progress.getUserId(), progress.getCreateTime()
                    , progress.getProgressPercent() == null ? new BigDecimal(0) : progress.getProgressPercent())));
        }
    }

    @Override
    public Set<String> getLeaderAndLecturerUserIdsByProjectId(String projectId) {
        Project project = getById(projectId);
        if (null == project) {
            throw new BusinessException(ProjectErrorNoEnum.PROJECT_NOT_EXIST);
        }
        // 学习项目讲师用户id集合
        Set<String> userIds = new HashSet<>(lecturerExaminationFeign.getLecturerUserIdsByProjectId(projectId));
        // 学习项目班主任
        if (StringUtils.isNotBlank(project.getLeader())) {
            userIds.add(project.getLeader());
        }
        return userIds;
    }

    @Override
    public List<ProjectCourseWarePageDTO> getProjectLectureWare(String projectId) {
        Collection<String> projectLecturerByProjectId = taskService.getProjectLecturerByProjectId(projectId);
        List<String> collect = new ArrayList<>(projectLecturerByProjectId);
        List<ProjectCourseWarePageDTO> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(collect)) {
            return new ArrayList<>();
        }
        List<com.wunding.learn.course.api.dto.CourseWareDTO> coursewareList = courseWareFeign.getCoursewareByCourseIds(
            collect);
        if (CollectionUtils.isEmpty(coursewareList)) {
            return new ArrayList<>();
        }
        for (com.wunding.learn.course.api.dto.CourseWareDTO courseware : coursewareList) {
            ProjectCourseWarePageDTO dto = new ProjectCourseWarePageDTO();
            dto.setId(courseware.getId());
            dto.setCourseWareName(courseware.getCwName());
            list.add(dto);
        }
        return list;
    }

    @Override
    public PageInfo<ProjectCourseWarePageDTO> getProjectWarePage(LecturerQuery lecturerQuery) {
        PageInfo<Task> selectPageInfo = PageMethod.startPage(lecturerQuery.getPageNo(), lecturerQuery.getPageSize())
            .doSelectPageInfo(() -> taskService.list(new LambdaQueryWrapper<Task>().eq(Task::getTaskType, "course")
                .eq(Task::getProId, lecturerQuery.getProjectId()).select(Task::getTaskContent)));

        List<String> projectLecturerByProjectIds = selectPageInfo.getList().stream().map(Task::getTaskContent)
            .filter(StringUtils::isNotBlank).toList();

        if (CollectionUtils.isEmpty(projectLecturerByProjectIds)) {
            return new PageInfo<>(new ArrayList<>());
        }

        List<com.wunding.learn.course.api.dto.CourseWareDTO> courseWareDTOList = courseWareFeign.getCoursewareByCourseIds(
            projectLecturerByProjectIds);

        if (CollectionUtils.isEmpty(courseWareDTOList)) {
            return new PageInfo<>(new ArrayList<>());
        }

        List<ProjectCourseWarePageDTO> courseWarePageDTOList = new ArrayList<>();
        ProjectCourseWarePageDTO dto;
        for (com.wunding.learn.course.api.dto.CourseWareDTO courseWareDTO : courseWareDTOList) {
            dto = new ProjectCourseWarePageDTO();
            dto.setId(courseWareDTO.getId());
            dto.setCourseWareName(courseWareDTO.getCwName());
            courseWarePageDTOList.add(dto);
        }

        PageInfo<ProjectCourseWarePageDTO> projectCourseWarePageDTOPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(selectPageInfo, projectCourseWarePageDTOPageInfo);

        projectCourseWarePageDTOPageInfo.setList(courseWarePageDTOList);
        return projectCourseWarePageDTOPageInfo;
    }

    @Override
    public Map<String, Project> getProjectInfoMapByIdList(Set<String> idSet) {
        if (CollectionUtils.isEmpty(idSet)) {
            return Collections.emptyMap();
        }
        List<Project> list = projectMapper.getListByProjectId(idSet);
        return list.stream().collect(Collectors.toMap(Project::getId, Function.identity()));
    }

    @Override
    public ProjectAndTaskInfoDTO getProjectAndTaskInfoByProIdAndTaskId(String proId, String taskId) {
        return baseMapper.getProjectAndTaskInfoByProIdAndTaskId(proId, taskId);
    }

    @Override
    public PageInfo<ProjectApiDTO> selectProjectList(SelectProjectListQuery query) {

        String userId = UserThreadContext.getUserId();
        OrgDTO orgDTO = orgFeign.getOrgByUserId(userId);
        //管辖范围
        Set<String> userManageAreaOrgId = orgFeign.findUserManageAreaLevelPath(userId);

        query.setCurrentUserId(userId);
        query.setCurrentOrgId(orgDTO.getId());
        query.setManagerAreaOrgIds(userManageAreaOrgId);

        return PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> baseMapper.selectProjectList(query));

    }

    @Override
    public void viewLimitChange(Integer viewType, Map<String, Long> viewLimitChangeMap) {
        log.info("viewLimitChange:{}", JsonUtil.objToJson(viewLimitChangeMap));
        for (Map.Entry<String, Long> entry : viewLimitChangeMap.entrySet()) {
            String key = entry.getKey();
            Long value = viewLimitChangeMap.get(key);
            // 更新下发范围
            projectViewLimitComponent.handleNewViewLimit(value, key);
            if (null != viewType) {
                //更新下发范围类型
                Project project = new Project();
                project.setId(key);
                project.setViewType(viewType);
                baseMapper.updateById(project);
            }
            // 更新学习项目评估的下发范围
            evaluationFeign.updateViewLimitByProjectId(new EvaluationLimitViewUpdateDTO().setProjectId(key)
                .setProgrammeId(value));


        }
    }

    @Override
    public void projectAutoCloseCase() {
        ProjectManagerQueryDTO projectManagerQueryDTO = new ProjectManagerQueryDTO();
        projectManagerQueryDTO.setIsPublish(1);
        //查询项目列表
        List<ProjectManagerDTO> listProject = baseMapper.queryPage(projectManagerQueryDTO);
        //根据列表查询对应的结案规则
        if (CollectionUtils.isEmpty(listProject)) {
            return;
        }
        for (ProjectManagerDTO dto : listProject) {
            String projectId = dto.getId();
            //根据结案规则及项目查询可以结案的人
            LambdaQueryWrapper<CompletionCondition> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CompletionCondition::getTrainId, projectId);
            queryWrapper.eq(CompletionCondition::getIsAutoClose, "1");
            CompletionCondition condition = completionConditionService.getOne(queryWrapper);
            if (Optional.ofNullable(condition).isEmpty()) {
                log.info("没有找到结业条件:" + projectId);
                continue;
            }
            CompletionConditionDTO completionConditionDTO = new CompletionConditionDTO();
            BeanUtils.copyProperties(condition, completionConditionDTO);
            //生成预结案清单
            completionUserService.proCompletionUser(projectId);
            //批量结案
            CompletionHandleDTO comDto = new CompletionHandleDTO();
            comDto.setProjectId(projectId);
            completionUserService.autoCompletionAllUser(comDto);
        }

    }

    @Override
    public Map<String, BigDecimal> getPorjectNumber(Set<String> projectIds) {
        List<ProjectInfoDTO> projectInfoDTOS = baseMapper.getPorjectNumber(projectIds);
        return projectInfoDTOS.stream()
            .collect(Collectors.toMap(ProjectInfoDTO::getId, ProjectInfoDTO::getJoinNumber, (key1, key2) -> key1));
    }


    @Override
    public Map<String, BigDecimal> getPorjectApplyNumber(Set<String> projectIds) {
        List<ProjectInfoDTO> projectInfoDTOS = baseMapper.getPorjectApplyNumber(projectIds);
        return projectInfoDTOS.stream()
            .collect(Collectors.toMap(ProjectInfoDTO::getId, ProjectInfoDTO::getJoinNumber, (key1, key2) -> key1));
    }


    @Override
    public PageInfo<WorkbenchProjectPageDTO> selectLecturerWorkProjectPageDTOList(LecturerProjectQuery query) {
        query.setUserId(UserThreadContext.getUserId());
        PageInfo<WorkbenchProjectPageDTO> objectPageInfo = PageMethod.startPage(query.getPageNo(), query.getPageSize(),
            false).doSelectPageInfo(() -> baseMapper.queryLecturerWorkProjectPageDTOList(query));
        objectPageInfo.setIsLastPage(query.getPageSize() != objectPageInfo.getList().size());
        // 设置 imageUrl
        List<String> ids = objectPageInfo.getList().stream().map(WorkbenchProjectPageDTO::getId)
            .toList();
        Map<String, String> imageUrls = fileFeign.getImageUrlsByIds(ids, ImageBizType.STUDY_PROJECT_FIRST.name());
        objectPageInfo.getList().forEach(data -> data.setImageUrl(imageUrls.get(data.getId())));
        return objectPageInfo;
    }

    @Override
    public List<ProjectAnalysisByUserDTO> stateAnalysisByUser(ProjectAnalysisByUserQuery query) {
        return baseMapper.stateAnalysisByUser(query);
    }

    @Override
    public QuickProjectDTO getQuickProject(String id, Integer isOngoingCount, Integer role, Integer projectType) {
        if (isOngoingCount != null && 1 == isOngoingCount) {
            // 只返回正在进行的培训数
            return baseMapper.getOngoingCount(orgFeign.findUserManageAreaLevelPath(UserThreadContext.getUserId()));
        }

        QuickProjectDTO dto = new QuickProjectDTO();
        String userId = UserThreadContext.getUserId();
        // 获取用户最近选择的
        UserSelectRecord userSelectRecord = userSelectRecordService.getUserSelectRecord(userId, role, projectType);

        if (StringUtil.isEmpty(id)) {
            Project newestPublishDTO = baseMapper.getNewestPublishQuickProject(userId, role, projectType);
            if (newestPublishDTO == null) {
                return null;
            }
            // 有新发布的，选择最新的
            BeanUtils.copyProperties(newestPublishDTO, dto);
            if (userSelectRecord != null) {
                Project project = baseMapper.selectById(userSelectRecord.getProjectId());
                if (project != null && project.getIsDel() == 0 && project.getIsPublish() == 1
                    && userSelectRecord.getUpdateTime().getTime() > newestPublishDTO.getPublishTime().getTime()) {
                    // 不是用户切换后发布的，返回用户历史切换的
                    BeanUtils.copyProperties(project, dto);
                }
            }
        } else {
            // 学习项目信息
            Project project = projectDao.projectExist(id);

            //校验快速培训开始结束时间
            checkQuickProjectTime(project);

            BeanUtils.copyProperties(project, dto);

            // 记录用户切换的
            if (userSelectRecord == null) {
                userSelectRecord = new UserSelectRecord();
                userSelectRecord.setRoleType(role);
                userSelectRecord.setUserId(userId);
                userSelectRecord.setId(newId());
                userSelectRecord.setCreateBy(userId);
                userSelectRecord.setCreateTime(new Date());
            }
            userSelectRecord.setProjectId(dto.getId());
            userSelectRecord.setUpdateBy(userId);
            userSelectRecord.setUpdateTime(new Date());
            userSelectRecordService.saveOrUpdate(userSelectRecord);
        }
        // 是否需要初始化任务
        dto.setNeedInit(taskProgressService.countByProjectIdAndUserId(dto.getId(), userId) > 0
            ? GeneralJudgeEnum.NEGATIVE.getValue() : GeneralJudgeEnum.CONFIRM.getValue());
        return dto;
    }

    //校验快速培训开始结束时间
    private static void checkQuickProjectTime(Project project) {
        if (project.getProjectType() == ProjectConstant.PROJECT_TYPE_QUICK) {
            if (project.getStartTime().getTime() > System.currentTimeMillis()) {
                //未开始
                throw new BusinessException(ProjectErrorNoEnum.ERROR_QUICK_PROJECT_IS_NOT_START);
            }
            if (project.getEndTime().getTime() < System.currentTimeMillis()) {
                //已过期
                throw new BusinessException(ProjectErrorNoEnum.ERROR_QUICK_PROJECT_IS_END);
            }
        }
    }

    @Override
    public PageInfo<QuickProjectUserStatDTO> getUserStat(QuickProjectUserStatQuery query) {
        String userId = UserThreadContext.getUserId();
        query.setUserManageLevelPath(orgFeign.findUserManageAreaLevelPath(UserThreadContext.getUserId()));
        query.setCreateBy(userId);
        setQueryStartTimeAndEndTime(query);

        PageInfo<QuickProjectUserStatDTO> pageInfo;
        if (null != query.getRole() && 2 == query.getRole()) {
            pageInfo = getQuickProjectUserStatDTOPageInfo1(query, userId);
        } else {
            pageInfo = getQuickProjectUserStatDTOPageInfo2(query);
        }
        return pageInfo;
    }

    @NotNull
    private PageInfo<QuickProjectUserStatDTO> getQuickProjectUserStatDTOPageInfo2(QuickProjectUserStatQuery query) {
        PageInfo<QuickProjectUserStatDTO> pageInfo;
        pageInfo = PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> baseMapper.getUserStat(query));
        for (QuickProjectUserStatDTO dto : pageInfo.getList()) {
            if (dto != null) {
                query.setUserId(dto.getId());
                QuickProjectUserStatDTO r = baseMapper.getUserStatByUserId(query);
                dto.setProjectTargetCount(
                    r.getProjectTargetCount() == null ? new BigDecimal(0) : r.getProjectTargetCount());
                dto.setProjectDoneCount(
                    r.getProjectDoneCount() == null ? new BigDecimal(0) : r.getProjectDoneCount());
                dto.setProjectDoneRate(dto.getProjectTargetCount().intValue() == 0 ? new BigDecimal(0)
                    : dto.getProjectDoneCount().multiply(new BigDecimal(100))
                        .divide(dto.getProjectTargetCount(), 0, RoundingMode.DOWN));
            }
        }
        return pageInfo;
    }

    @NotNull
    private PageInfo<QuickProjectUserStatDTO> getQuickProjectUserStatDTOPageInfo1(QuickProjectUserStatQuery query,
        String userId) {
        PageInfo<QuickProjectUserStatDTO> pageInfo;
        pageInfo = new PageInfo<>();
        List<QuickProjectUserStatDTO> list = new ArrayList<>();
        UserQuery userQuery = new UserQuery();
        BeanUtils.copyProperties(query, userQuery);
        userQuery.setUserId(userId);
        PageInfo<UserListDTO> userPage = userFeign.getTrainUserByManageArea(userQuery);
        for (UserListDTO user : userPage.getList()) {
            QuickProjectUserStatDTO dto = new QuickProjectUserStatDTO();
            BeanUtils.copyProperties(user, dto);
            query.setCreateBy(dto.getId());
            QuickProjectUserStatDTO r = baseMapper.getTrainUserStatByUserId(query);
            dto.setProjectTargetCount(
                r.getProjectTargetCount() == null ? new BigDecimal(0) : r.getProjectTargetCount());
            dto.setProjectDoneCount(r.getProjectDoneCount() == null ? new BigDecimal(0) : r.getProjectDoneCount());
            dto.setProjectDoneRate(dto.getProjectTargetCount().intValue() == 0 ? new BigDecimal(0)
                : dto.getProjectDoneCount().multiply(new BigDecimal(100))
                    .divide(dto.getProjectTargetCount(), 0, RoundingMode.DOWN));
            list.add(dto);
        }
        BeanUtils.copyProperties(userPage, pageInfo);
        pageInfo.setList(list);
        return pageInfo;
    }

    private static void setQueryStartTimeAndEndTime(QuickProjectUserStatQuery query) {
        query.setEndTime(new Date());
        if (0 == query.getStatType()) {
            // 本周
            query.setStartTime(DateUtil.getThisWeekMonday());
        } else if (1 == query.getStatType()) {
            // 下周
            query.setStartTime(DateUtil.getLastWeekMonday());
            query.setEndTime(DateUtil.getLastWeekSunday());
        }
        if (2 == query.getStatType()) {
            // 本月
            query.setStartTime(DateUtil.getThisMonthStartDay());
        } else if (3 == query.getStatType()) {
            // 上月
            query.setStartTime(DateUtil.getLastMonthStartDay());
            query.setEndTime(DateUtil.getLastMonthEndDay());
        } else if (4 == query.getStatType()) {
            // 本年度
            query.setStartTime(DateUtil.getThisYearStartDay());
        } else if (5 == query.getStatType()) {
            // 上年
            query.setStartTime(DateUtil.getLastYearStartDay());
            query.setEndTime(DateUtil.getLastYearEndDay());
        }
    }

    @Override
    public List<QuickProjectRoleDTO> getRole() {
        // 所有用户有学员角色
        List<QuickProjectRoleDTO> roleList = new ArrayList<>();
        roleList.add(QuickProjectRoleEnum.getRole(QuickProjectRoleEnum.STUDENT));

        String userId = UserThreadContext.getUserId();
        // 用户是否是培训师
        if (Boolean.TRUE.equals(routerFeign.hasRouterPermission(userId, QUICK_TRAINING))) {
            roleList.add(QuickProjectRoleEnum.getRole(QuickProjectRoleEnum.TRAINER));
        }
        // 获取用户管辖范围
        Set<String> userManageAreaOrgId = orgFeign.getUserManageAreaOrgId(userId);
        if (!CollectionUtils.isEmpty(userManageAreaOrgId)) {
            roleList.add(QuickProjectRoleEnum.getRole(QuickProjectRoleEnum.ADMIN));
        }
        return roleList;
    }

    @Override
    public void quickProjectAllUserReminders(String projectId) {
        Project project = getById(projectId);
        if (project == null) {
            throw new BusinessException(ProjectErrorNoEnum.PROJECT_NOT_EXIST);
        }
        List<Task> taskList = taskService.list(new LambdaQueryWrapper<Task>().eq(Task::getProId, projectId)
            .eq(Task::getIsPublish, PublishEnum.PUBLISHED.getValue()));

        PageInfo<String> projectUserIdsPage = new PageInfo<>();
        projectUserIdsPage.setHasNextPage(true);
        int pageNo = 1;
        int pageSize = 500;
        while (projectUserIdsPage.isHasNextPage()) {
            // 获取未完成项目的用户id
            projectUserIdsPage = progressService.getProjectNotCompleteUserId(projectId, pageNo, pageSize);
            List<String> sysUserList = projectUserIdsPage.getList();
            if (CollectionUtils.isEmpty(sysUserList)) {
                throw new BusinessException(ProjectErrorNoEnum.NO_USER);
            }
            Map<String, UserDTO> userNameMap = userFeign.getUserNameMapByIds(sysUserList);
            StringBuilder pushArea = new StringBuilder();
            sysUserList.forEach(user -> pushArea.append(userNameMap.get(user).getFullName()).append(","));
            userMailSend(project, taskList, sysUserList, pushArea);
            pageNo++;
        }
    }

    @Override
    public void updateQuickProject(String quickProjectId) {
        Project project = baseMapper.selectProjectById(quickProjectId);
        projectInfoDao.updateQuickProjectIsDelById(project.setIsDel(0));
    }

    private void handleCategoryCanDel() {
        List<Project> projectList = list();
        if (CollectionUtils.isEmpty(projectList)) {
            categoryFeign.deleteCategoryByType(CategoryTypeEnum.TrainType.getType());
            return;
        }
        List<String> collect = projectList.stream().map(Project::getTrainCategoryId).toList();
        if (CollectionUtils.isEmpty(collect)) {
            categoryFeign.deleteCategoryByType(CategoryTypeEnum.TrainType.getType());
            return;
        }
        categoryFeign.updateCategoryByListCanDel(collect, CategoryTypeEnum.TrainType.getType());
    }

    @Override
    public Long getProjectViewLimitId(String projectId) {
        Project project = Optional.ofNullable(getById(projectId))
            .orElseThrow(() -> new BusinessException(ProjectErrorNoEnum.PROJECT_NOT_EXIST));
        //下发范围
        ViewLimitBaseInfoDTO viewLimitBaseInfo = projectViewLimitComponent.getViewLimitBaseInfo(project.getId());
        return viewLimitBaseInfo.getProgrammeId();
    }

    @Override
    public List<ResourceBaseDTO> getProjectBaseList(ResourceBaseQuery resourceBaseQuery) {
        return baseMapper.getProjectBaseList(resourceBaseQuery);
    }

    @Override
    public PageInfo<SuperviseProjectPageDTO> superviseProjectList(SuperviseProjectPageQuery query) {
        Integer pageNo = query.getPageNo();
        Integer pageSize = query.getPageSize();
        String userId = UserThreadContext.getUserId();
        query.setUserId(userId);
        return PageMethod.startPage(pageNo, pageSize)
            .doSelectPageInfo(() -> baseMapper.superviseProjectList(userId, query.getAbilityId(),
                GeneralJudgeEnum.NEGATIVE.getValue(), query.getIsJoin()));
    }

    @Override
    public PageInfo<TrainOrgHomeDTO> findTrainOrgHomePageList(ProjectHomePageQuery projectHomePageQuery,
        String userId) {
        PageInfo<TrainOrgHomeDTO> result = new PageInfo<>();
        IndexOrgQuery query = new IndexOrgQuery();
        BeanUtils.copyProperties(projectHomePageQuery, query);
        // 获取首页展示的组织机构
        PageInfo<IndexOrgDTO> pageInfo = orgFeign.getIndexOrg(query);
        List<IndexOrgDTO> orgDTOS = pageInfo.getList();
        BeanUtils.copyProperties(pageInfo, result);
        if (CollectionUtils.isEmpty(orgDTOS)) {
            return result;
        }
        List<String> orgIds = orgDTOS.stream().map(IndexOrgDTO::getId).toList();
        // 获取组织下面授项目报名人数
        Map<String, FaceProjectApplyInfoDTO> infoDTOMap = getFaceProjectApplyInfo(orgIds,
            projectHomePageQuery.getCategoryId(), UserThreadContext.getUserId()).stream()
            .collect(Collectors.toMap(FaceProjectApplyInfoDTO::getOrgId, Function.identity(), (k1, k2) -> k1));
        List<TrainOrgHomeDTO> resultList = BeanListUtils.copyListProperties(orgDTOS, TrainOrgHomeDTO::new);
        resultList.forEach(trainOrgHomeDTO -> {
            trainOrgHomeDTO.setIsFull(false);
            trainOrgHomeDTO.setApplyNum(0);
            trainOrgHomeDTO.setHasApply(false);
            Optional.ofNullable(infoDTOMap.get(trainOrgHomeDTO.getId())).ifPresent(applyInfoDTO -> {
                trainOrgHomeDTO.setApplyNum(applyInfoDTO.getApplyNum());
                trainOrgHomeDTO.setIsFull(Objects.equals(applyInfoDTO.getApplyNum(), applyInfoDTO.getPlanNum())
                    || applyInfoDTO.getApplyNum() > applyInfoDTO.getPlanNum());
                trainOrgHomeDTO.setHasApply(Objects.equals(1, applyInfoDTO.getHasApply()));
            });
        });

        result.setList(resultList);

        return result;
    }

    @Override
    public FaceProjectUserApplyIdentity getFaceProjectUserApplyIdentity() {
        //没有在途培训、没有代报名，返回 无身份
        //没有在途、有代理报名，返回 代报名人员
        //只要有在途的培训，返回 培训人员
        FaceProjectUserApplyIdentity applyIdentity = new FaceProjectUserApplyIdentity();
        applyIdentity.setCode(IdentityEnum.NONE.getCode());
        applyIdentity.setName(IdentityEnum.NONE.getName());
        FaceProjectUserApplyCountDTO countDTO = baseMapper.getUserFaceProjectUserApplyCountDTO(
            UserThreadContext.getUserId());
        if (countDTO.getAgentApplyCount() > 0) {
            applyIdentity.setCode(IdentityEnum.APPLY_AGENT.getCode());
            applyIdentity.setName(IdentityEnum.APPLY_AGENT.getName());
        }
        if (countDTO.getApplyCount() > 0) {
            applyIdentity.setCode(IdentityEnum.TRAIN_LEANER.getCode());
            applyIdentity.setName(IdentityEnum.TRAIN_LEANER.getName());
        }
        return applyIdentity;
    }

    @Override
    public Map<String, FaceProjectApiDTO> getFaceProjectId(Collection<String> categoryId, String currentUserId) {
        if (CollectionUtils.isEmpty(categoryId)) {
            return new HashMap<>();
        }
        Map<String, Long> joinCountMap = progressService.getJoinCountMap(categoryId);
        List<FaceProjectApiDTO> faceProjectApiDTOS = baseMapper.getFaceProjectById(categoryId, currentUserId);
        List<String> applyIds = faceProjectApiDTOS.stream()
            .map(FaceProjectApiDTO::getApplyId)
            .filter(StringUtils::isNotBlank).toList();
        // 查询报名是否已经满了
        Map<String, ApplyCheckDTO> applyCheckDTOMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(applyIds)) {
            applyCheckDTOMap = applyService.applyCheckByApplyIdList(applyIds);
        }

        for (FaceProjectApiDTO projectApiDTO : faceProjectApiDTOS) {
            String applyId = projectApiDTO.getApplyId();
            String projectId = projectApiDTO.getId();
            // 设置参加人数
            Optional.ofNullable(joinCountMap.get(projectId))
                .ifPresentOrElse(projectApiDTO::setJoinNumber, () -> projectApiDTO.setJoinNumber(0L));
            // 无报名
            if (StringUtils.isBlank(applyId)) {
                projectApiDTO.setApplyId(null);
                continue;
            }
            if (Objects.isNull(projectApiDTO.getApplyStatus())) {
                projectApiDTO.setApplyStatus(ProjectConstant.APPLY_STATUS_NOT_APPLY);
            }
            // 报名模板类型
            if (StringUtils.equals(projectApiDTO.getApplyFormTemplateId(),
                com.wunding.learn.common.constant.project.ProjectConstant.DEFAULT_APPLY_FORM_TEMPLATE_ID)) {
                projectApiDTO.setApplyFormTemplateType(ApplyFormTemplateType.DEFAULT.getValue());
            } else {
                projectApiDTO.setApplyFormTemplateType(ApplyFormTemplateType.CUSTOM.getValue());
            }
            Optional.ofNullable(applyCheckDTOMap.get(applyId))
                .ifPresent(dto -> projectApiDTO.setIsApplyFull(dto.getIsApplyFull()));
        }
        return faceProjectApiDTOS.stream()
            .collect(Collectors.toMap(FaceProjectApiDTO::getId, Function.identity(), (k1, k2) -> k1));
    }

    @Override
    public ProjectMaterialExampleFileApiDTO findExampleFile(String id, FileBizType fileBizType) {
        Project project = Optional.ofNullable(getById(id))
            .orElseThrow(() -> new BusinessException(ProjectErrorNoEnum.PROJECT_NOT_EXIST));
        ProjectMaterialExampleFileApiDTO apiDTO = new ProjectMaterialExampleFileApiDTO();
        if (Objects.equals(fileBizType, FileBizType.APPLY_CERTIFICATION_MATERIAL_EXAMPLE)) {
            apiDTO.setTransCodeStatus(project.getCertificationTransformStatus());
        }
        if (Objects.equals(fileBizType, FileBizType.APPLY_OTHER_MATERIAL_EXAMPLE)) {
            apiDTO.setTransCodeStatus(project.getOtherTransformStatus());
        }
        Optional.ofNullable(fileFeign.getSourceFileInfo(id, fileBizType.name())).ifPresent(sourceFile -> {
            FileInfo fileInfo = new FileInfo();
            fileInfo.setFileName(sourceFile.getName());
            fileInfo.setFileUrl(sourceFile.getUrl());
            apiDTO.setSourceFile(fileInfo);
        });
        Optional.ofNullable(fileFeign.getFileNamePath(id, fileBizType.name())).ifPresent(sourceFile -> {
            FileInfo fileInfo = new FileInfo();
            fileInfo.setFileName(sourceFile.getName());
            fileInfo.setFileUrl(sourceFile.getUrl());
            // 使用转码之后的url
            Map<String, String> mainFileUrlByIds = fileFeign.getMainFileUrlByIds(Sets.newHashSet(id),
                fileBizType.name());
            Optional.ofNullable(mainFileUrlByIds.get(id)).ifPresent(url -> fileInfo.setFileUrl(sourceFile.getUrl()));
            if (Objects.equals(fileBizType, FileBizType.APPLY_CERTIFICATION_MATERIAL_EXAMPLE)) {
                fileInfo.setMime(project.getCertificationMime());
            }
            if (Objects.equals(fileBizType, FileBizType.APPLY_OTHER_MATERIAL_EXAMPLE)) {
                fileInfo.setMime(project.getOtherMime());
            }
            apiDTO.setTransCodeFile(fileInfo);
        });
        return apiDTO;
    }

    @Override
    public PageInfo<ScheduleStatDTO> getUserListByQuery(ScheduleStatQuery query) {
        return PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> baseMapper.getUserListByQuery(query));
    }

    @Override
    public String getUploadFileRemark(String id) {
        Project project = getOne(
            new LambdaQueryWrapper<Project>().select(Project::getUploadFileRemark).eq(Project::getId, id));
        if (Objects.nonNull(project)) {
            return project.getUploadFileRemark();
        }
        return null;
    }

    private List<FaceProjectApplyInfoDTO> getFaceProjectApplyInfo(List<String> orgIds, String categoryId,
        String userId) {
        return baseMapper.selectFaceProjectApplyInfo(orgIds, categoryId, userId);
    }

    /**
     * 发送推送组装DTO对象逻辑
     *
     * @param projectDTO   项目dto
     * @param operateState 操作状态：0-添加 1-编辑
     */
    private void sendPushFeign(ProjectAdminSaveDTO projectDTO, Integer operateState) {

        // 组装基础信息
        SendPushDTO sendPushDTO = new SendPushDTO();
        sendPushDTO.setLang(LocaleContextHolder.getLocale().getLanguage());
        sendPushDTO.setServerName(Objects.requireNonNull(WebUtil.getRequest()).getServerName());
        PushNoticeSetDTO pushNoticeSetDTO = projectDTO.getPushNoticeSetDTO();
        pushNoticeSetDTO.setResourceId(projectDTO.getId());
        sendPushDTO.setPushNoticeSetDTO(pushNoticeSetDTO);

        // 手动推送时采用内容封面图片处理
        if (Objects.equals(pushNoticeSetDTO.getPushMethod(), 1)) {
            pushNoticeSetDTO.getManualPushSet().getCustomPushContents().forEach(customPushContent -> {
                if (Objects.equals(customPushContent.getPushImage(), 1)) {
                    customPushContent.setImagePath(
                        fileFeign.getImageFileNamePath(projectDTO.getId(), ImageBizType.STUDY_PROJECT_FIRST.name()));
                }
            });
        }

        // 组装resource操作信息
        String resourceName = projectDTO.getProName();
        PushResourceDTO pushResourceDTO = new PushResourceDTO().setResourceId(projectDTO.getId())
            .setResourceName(resourceName).setResourceType(pushNoticeSetDTO.getResourceType()).setIsTrain(0)
            .setOperateState(operateState).setProgrammeId(projectDTO.getProgrammeId());
        sendPushDTO.setPushResourceDTO(pushResourceDTO);

        PushAttributeDTO pushAttributeDTO = new PushAttributeDTO();

        pushAttributeDTO.setName(pushNoticeSetDTO.getResourceName());
        pushResourceDTO.setResourceId(projectDTO.getId());

        pushAttributeDTO.setExistSecondary(false);
        pushAttributeDTO.setStartTime(projectDTO.getStartTime());
        pushAttributeDTO.setEndTime(projectDTO.getEndTime());
        pushAttributeDTO.setIntro(projectDTO.getMark());
        pushAttributeDTO.setSignCheckinSpot(projectDTO.getAddress());

        @SuppressWarnings("unchecked") String systemName = (String) redisTemplate.opsForHash()
            .get(TenantRedisKeyConstant.SYSTEM_NAME_KEY, UserThreadContext.getTenantId());
        pushAttributeDTO.setSystemName(systemName);

        sendPushDTO.setPushAttributeDTO(pushAttributeDTO);

        pushFeign.sendPush(sendPushDTO);
    }

    @Override
    public Project get(String id) {
        Project project = getById(id);
        if (Optional.ofNullable(project).isEmpty()) {
            throw new BusinessException(ErrorNoEnum.ERR_RESOURCE_NOT_EXIST);
        }
        return project;
    }

    @Override
    public void checkProjectValid(String projectId, Integer isIgnoreView) {
        // 查该学习项目是否发布
        Project project = projectDao.projectExist(projectId);
        if (Objects.equals(PublishEnum.NOT_PUBLISH.getValue(), project.getIsPublish())) {
            throw new BusinessException(ErrorNoEnum.ERR_IS_NO_PUBLISH);
        }
        // 校验时间
        Date now = new Date();
        if (project.getStartTime().after(now)) {
            throw new BusinessException(ProjectErrorNoEnum.PROJECT_NOT_START);
        }
        if (now.after(project.getEndTime())) {
            throw new BusinessException(ProjectErrorNoEnum.PROJECT_END);
        }
        // 校验下发权限
        String userId = UserThreadContext.getUserId();
        if (Objects.equals(GeneralJudgeEnum.NEGATIVE.getValue(), isIgnoreView)
            && (!resourceViewLimitService.checkViewLimit(projectId, LimitTable.ProjectViewLimit.name(), userId))) {
            throw new BusinessException(ErrorNoEnum.ERR_NO_POWER);
        }
    }

    @Override
    public void projectUserStudyConditionOldDate(String targetId) {
        // 清理表数据
        LambdaQueryWrapper<ProjectUserStudyCondition> query = new LambdaQueryWrapper<>();
        projectUserStudyConditionMapper.delete(query);

        List<String> projectList = new ArrayList<>();

        // 更新指定的项目
        if (!StringUtils.isBlank(targetId)) {
            String[] split = targetId.split(",");
            List<String> stringList = new ArrayList<>(Arrays.asList(split));
            for (String id : stringList) {
                QueryWrapper<Project> wrapper = new QueryWrapper<Project>().eq("id", id).eq("flag", 0);
                Project one = baseMapper.selectOne(wrapper);
                if (one != null) {
                    projectList.add(id);
                }
            }
        }

        // 如果 projectList 为空，则查询项目列表
        if (CollectionUtils.isEmpty(projectList)) {
            projectList = baseMapper.getProjectAll();
        }

        // 根据列表查询参与的学员
        for (String projectId : projectList) {
            // 查询项目参与的学员
            List<String> allUserId = progressService.getAllProUserIds(projectId);
            // 根据项目id及学员名单查询学员情况
            List<ProCompletionUserDTO> proCompletionUserDtoList = progressService.getUserStudyCondition(projectId,
                allUserId);
            for (ProCompletionUserDTO proCompletionUserDTO : proCompletionUserDtoList) {
                ProjectUserStudyCondition projectUserStudyCondition = new ProjectUserStudyCondition();
                projectUserStudyCondition.setId(newId());
                projectUserStudyCondition.setUserId(proCompletionUserDTO.getUserId());
                projectUserStudyCondition.setProjectId(projectId);
                projectUserStudyCondition.setElectiveNum(proCompletionUserDTO.getElectiveNum());
                projectUserStudyCondition.setEvaNum(proCompletionUserDTO.getEvaNum());
                projectUserStudyCondition.setExamNum(proCompletionUserDTO.getIsExam());
                projectUserStudyCondition.setSignNum(proCompletionUserDTO.getSignNum());
                projectUserStudyCondition.setHomeworkNum(proCompletionUserDTO.getIsHomework());
                projectUserStudyCondition.setRequiredNum(proCompletionUserDTO.getIsLearned());
                projectUserStudyCondition.setSurveyNum(proCompletionUserDTO.getComSurvey());
                projectUserStudyCondition.setCreateBy(proCompletionUserDTO.getUserId());
                projectUserStudyCondition.setCreateTime(new Date());
                projectUserStudyCondition.setUpdateBy(proCompletionUserDTO.getUserId());
                projectUserStudyCondition.setUpdateTime(new Date());
                // 保存学员信息到用户学习情况表
                projectUserStudyConditionMapper.insert(projectUserStudyCondition);
            }
        }
    }

    @Override
    public boolean getPosterShareStatus(String id) {
        return posterShareService.resourceIsOpenShare(id);
    }

    @Override
    public PosterShareClientDTO getPosterShare(String id) {
        Project project = get(id);
        if (Objects.isNull(project)) {
            throw new BusinessException(ProjectErrorNoEnum.PROJECT_NOT_EXIST);
        }
        return posterShareService
            .getPosterShareInfo(id, ProjectBizImpl.getResourceTypeEnum(project.getType(), project.getProjectType()),
                ImageBizType.STUDY_PROJECT_FIRST,
                resourceId -> {
                    String userId = UserThreadContext.getUserId();
                    String createBy = project.getCreateBy();
                    Map<String, UserDTO> simpleUserMap = userFeign.getSimpleUserMap(List.of(userId, createBy));
                    PosterShareResourceDTO resourceDTO = new PosterShareResourceDTO();
                    // 项目名称
                    resourceDTO.setResourceTitle(project.getProName());
                    // 分享人
                    Optional.ofNullable(simpleUserMap.get(userId))
                        .ifPresentOrElse(userDTO -> resourceDTO.setShareUserName(userDTO.getFullName()),
                            () -> resourceDTO.setShareUserName(StringUtils.EMPTY));
                    // 作者
                    Optional.ofNullable(simpleUserMap.get(createBy))
                        .ifPresentOrElse(userDTO -> resourceDTO.setAuthor(userDTO.getFullName()),
                            () -> resourceDTO.setAuthor(StringUtils.EMPTY));
                    // 参与人数
                    resourceDTO.setJoinNum(progressService.getJoinCount(id));
                    return resourceDTO;
                });
    }

    @Override
    public MentorForm getMentorNoSolve(String currentUserId, String taskRecordId, String taskContentId) {
        return baseMapper.getMentorNoSolve(currentUserId, taskRecordId, taskContentId);
    }

    @Override
    public PageInfo<ProjectStatisticDTO> statisticList(ProjectStatisticQueryDTO projectStatisticQueryDTO) {
        String userId = UserThreadContext.getUserId();
        //管辖范围
        Set<String> userManageAreaOrgId = orgFeign.findUserManageAreaLevelPath(userId);
        projectStatisticQueryDTO.setCurrentUserId(userId);
        projectStatisticQueryDTO.setUserManageAreaOrgId(userManageAreaOrgId);
        projectStatisticQueryDTO.setCurrentOrgId(UserThreadContext.getOrgId());
        // 处理时间
        if (projectStatisticQueryDTO.getStartTime() != null) {
            projectStatisticQueryDTO.setStartTime(DateUtil.getDateStartTime(projectStatisticQueryDTO.getStartTime()));
        }
        if (projectStatisticQueryDTO.getEndTime() != null) {
            projectStatisticQueryDTO.setEndTime(DateUtil.getDateEndTime(projectStatisticQueryDTO.getEndTime()));
        }
        PageInfo<ProjectStatisticDTO> pageInfo = PageMethod.startPage(projectStatisticQueryDTO.getPageNo(),
                projectStatisticQueryDTO.getPageSize())
            .doSelectPageInfo(() -> baseMapper.statisticList(projectStatisticQueryDTO));
        List<ProjectStatisticDTO> pageInfoList = pageInfo.getList();
        if (CollectionUtils.isEmpty(pageInfoList)) {
            return pageInfo;
        }
        Set<String> orgIds = pageInfoList.stream().map(ProjectStatisticDTO::getOrgId).collect(Collectors.toSet());
        Set<String> proIds = pageInfoList.stream().map(ProjectStatisticDTO::getId).collect(Collectors.toSet());

        CompletableFuture<Map<String, OrgShowDTO>> orgShowDTOMapFuture = CompletableFuture.supplyAsync(
            () -> orgFeign.getOrgShowDTO(orgIds), commonTaskThreadPool);

        CompletableFuture<Map<String, Long>> lecturerMapFuture = CompletableFuture.supplyAsync(
            () -> lecturerExaminationFeign.findProjectLecturerByProjectId(proIds), commonTaskThreadPool);

        CompletableFuture<Map<String, BigDecimal>> evaluationScoreMapFuture = CompletableFuture.supplyAsync(
            () -> evaluationReplyFeign.getScoreMapByProjectIds(proIds), commonTaskThreadPool);

        // 阻塞线程完成
        CompletableFuture.allOf(orgShowDTOMapFuture, lecturerMapFuture, evaluationScoreMapFuture);

        Map<String, OrgShowDTO> orgShowDTOMap;
        Map<String, Long> lecturerMap;
        Map<String, BigDecimal> evaluationScoreMap;
        try {
            orgShowDTOMap = orgShowDTOMapFuture.get();
            lecturerMap = lecturerMapFuture.get();
            evaluationScoreMap = evaluationScoreMapFuture.get();
        } catch (InterruptedException | ExecutionException e) {
            Thread.currentThread().interrupt();
            log.error("ExecutionException | TimeoutException!", e);
            throw new BusinessException(ErrorNoEnum.ERR_SERVER);
        }
        Map<String, OrgShowDTO> finalOrgShowDTOMap = orgShowDTOMap;
        Map<String, Long> finalLecturerMap = lecturerMap;
        Map<String, BigDecimal> finalEvaluationScoreMap = evaluationScoreMap;
        pageInfoList.forEach(projectStatisticDTO -> {
            Optional.ofNullable(finalOrgShowDTOMap.get(projectStatisticDTO.getOrgId())).ifPresent(
                orgShowDTO -> projectStatisticDTO.setOrgName(orgShowDTO.getOrgShortName())
                    .setOrgPath(orgShowDTO.getLevelPathName()));
            String id = projectStatisticDTO.getId();
            projectStatisticDTO.setLecturerNum(finalLecturerMap.getOrDefault(id, 0L));
            if (projectStatisticDTO.getStartTime() != null && projectStatisticDTO.getEndTime() != null) {
                projectStatisticDTO.setTrainDays(
                    DateUtil.daysBetween3(projectStatisticDTO.getStartTime(), projectStatisticDTO.getEndTime()));
            }
            BigDecimal score = finalEvaluationScoreMap.get(id);
            projectStatisticDTO.setProjectScore(score == null ? StringUtils.EMPTY : score.toPlainString());
        });
        return pageInfo;
    }

    @Override
    public void statisticExport(ProjectStatisticQueryDTO query) {

        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IProjectService, ProjectStatisticDTO>(query) {

            @Override
            public ExportBizType getType() {
                // 面授项目
                if (Objects.equals(query.getProjectType(), ProjectConstant.PROJECT_TYPE_FACE)) {
                    return ExportBizType.FaceProjectStatistic;
                }
                return ExportBizType.LearnProjectStatistic;
            }

            @Override
            public String getFileName() {
                // 面授项目
                if (Objects.equals(query.getProjectType(), ProjectConstant.PROJECT_TYPE_FACE)) {
                    return ExportFileNameEnum.FaceProjectStatistic.getType();
                }
                return ExportFileNameEnum.LearnProjectStatistic.getType();
            }

            @Override
            protected IProjectService getBean() {
                return SpringUtil.getBean(ProjectConstant.PROJECT_SERVICE, IProjectService.class);
            }

            @Override
            protected PageInfo<ProjectStatisticDTO> getPageInfo() {
                return getBean().statisticList(query);
            }
        };
        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public PageInfo<ProjectStatisticUserDetailDTO> getProjectUser(ProjectUserQuery query) {
        //组织Id
        String orgId = query.getOrgId();
        if (StringUtils.isNotBlank(orgId)) {
            OrgDTO orgDTO = orgFeign.getById(orgId);
            if (orgDTO != null) {
                query.setLevelPath(orgDTO.getLevelPath());
            }
        }
        // 用户ID
        if (StringUtils.isNotEmpty(query.getUserIds())) {
            String[] stringArray = query.getUserIds().split(",");
            query.setUserIdsVo(Arrays.stream(stringArray).toList());
        }
        PageInfo<ProjectStatisticUserDetailDTO> pageInfo = PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> baseMapper.getProjectUser(query));
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return pageInfo;
        }
        Set<String> userIdSet = pageInfo.getList().stream().map(ProjectStatisticUserDetailDTO::getUserId)
            .collect(Collectors.toSet());
        Map<String, UserOrgDTO> userMap = orgFeign.getUserAndOrgMapByUserIds(userIdSet);
        pageInfo.getList().forEach(dto -> Optional.ofNullable(userMap.get(dto.getUserId())).ifPresent(userInfo -> {
            dto.setUserName(userInfo.getFullName());
            dto.setLoginName(userInfo.getLoginName());
            dto.setPostName(userInfo.getPostName());
            dto.setOrgName(userInfo.getOrgShortName());
            dto.setOrgPath(userInfo.getLevelPathName());
            Integer status = dto.getStatus();
            if (Objects.equals(1, status)) {
                dto.setStatusStr(I18nUtil.getMessage("已完成"));
            } else if (Objects.equals(0, status)) {
                dto.setStatusStr(I18nUtil.getMessage("未完成"));
            } else if (Objects.equals(3, status)) {
                dto.setStatusStr(I18nUtil.getMessage("未参加"));
            }
            dto.setProgressPercentStr(dto.getProgressPercent() == null ? "-" : dto.getProgressPercent().toPlainString() + "%");
        }));
        return pageInfo;
    }

    @Override
    public void exportProjectUser(ProjectUserQuery query) {
        Project project = getById(query.getProjectId());
        if (project == null) {
            throw new BusinessException(ProjectErrorNoEnum.PROJECT_NOT_EXIST);
        }
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IProjectService, ProjectStatisticUserDetailDTO>(query) {

            @Override
            public ExportBizType getType() {
                return ExportBizType.ProjectStatisticUserDetail;
            }

            @Override
            public String getFileName() {
                return project.getProName() + "_" + ExportFileNameEnum.ProjectStatisticUserDetail.getType();
            }

            @Override
            protected IProjectService getBean() {
                return SpringUtil.getBean(ProjectConstant.PROJECT_SERVICE, IProjectService.class);
            }

            @Override
            protected PageInfo<ProjectStatisticUserDetailDTO> getPageInfo() {
                return getBean().getProjectUser(query);
            }

            @Override
            protected void afterCreateBeanMap(Map<String, Object> map) {
                final String isImport = "isImport";
                Object checkFinished = map.get(isImport);
                if (Objects.equals(checkFinished, 1)) {
                    map.put(isImport, "是");
                } else {
                    map.put(isImport, "否");
                }
            }
        };
        exportComponent.exportRecord(exportDataDTO);
    }


    @Override
    @Async("commonTaskThreadPool")
    public Future<ImportResultDTO> importProjectUserData(String excelFilePath) {
        if (IMPORT_DATA_THREAD_POOL == null) {
            throw new BusinessException(FileErrorNoEnum.FILE_OPERATION_FAIL);
        }
        return IMPORT_DATA_THREAD_POOL.submit(() -> processUserImport(excelFilePath));
    }

    private ImportResultDTO processUserImport(String filePath) {
        ImportResultDTO result = new ImportResultDTO();
        result.setIsSuccess(false);

        try {
            ImportDataDTO importData = importDataFeign.getImportData(filePath);
            if (importData.getExcel() == null) {
                throw new BusinessException(FileErrorNoEnum.ERR_POST_CONTENT_NULL);
            }

            String[] fixedHead = {"项目编码", "项目名称", "姓名", "用户账号", "进入项目日期", "完成项目日期"};
            ExcelCheckMessage excelCheckMessage = new ProjectUserExcelTemplate(fixedHead).check(importData.getExcel());
            if (hasValidationErrors(excelCheckMessage)) {
                result.setMsg(JsonUtil.objToJson(excelCheckMessage.getMessage()));
                result.setMsgs(excelCheckMessage.getMessage());
                return result;
            }

            List<ProjectUserImportDTO> importList = (List<ProjectUserImportDTO>) excelCheckMessage.getObjects();
            if (checkProjectUserImportData(importList, result)) {
                importProjectUserData(importList, result);
            }
            return result;
        } catch (BusinessException e) {
            result.setMsg(e.getMessage());
            return result;
        }
    }



    // 校验结果处理
    private boolean hasValidationErrors(ExcelCheckMessage message) {
        return !CollectionUtils.isEmpty(message.getMessage());
    }


    // 数据处理流程
    @SuppressWarnings("unchecked")
    private List<ProjectUserImportDTO> castImportData(ExcelCheckMessage message) {
        return (List<ProjectUserImportDTO>) message.getObjects();
    }

    private void importProjectUserData(List<ProjectUserImportDTO> importList, ImportResultDTO result) {
        long startTime = System.currentTimeMillis();

        Set<String> projectIds = importList.stream().map(ProjectUserImportDTO::getProjectId)
            .collect(Collectors.toSet());
        Map<String, Project> projectIdMap = listByIds(projectIds).stream()
            .collect(Collectors.toMap(Project::getId, Function.identity()));
        transactionTemplate.execute(status -> {
            importList.forEach(dto -> projectImportSingleUser(dto, projectIdMap.get(dto.getProjectId())));
            return true;
        });

        result.setIsSuccess(true);
        result.setMsg("成功导入" + importList.size() + "条项目学员数据");
        log.info("saveProjectUserImportData 耗时{}ms", System.currentTimeMillis() - startTime);
    }


    private void projectImportSingleUser(ProjectUserImportDTO dto, Project project) {
        // 先加入项目初始化一次，再做用户项目进度更新、完成项目激励处理
        joinProject(dto.getUserId(), dto.getProjectId());
        Progress progress = getOrCreateProgress(dto, project);
        updateProjectProgressStatusToFinish(progress, dto, project);
        sendProjectCompletionEvent(progress.getUserId(), project);
    }

    private Progress getOrCreateProgress(ProjectUserImportDTO dto, Project project) {
        Progress progress = progressService.getOne(
            Wrappers.<Progress>lambdaQuery()
                .eq(Progress::getProId, project.getId())
                .eq(Progress::getUserId, dto.getUserId())
        );
        return progress != null ? progress : createNewProjectProgress(dto, project);
    }

    private Progress createNewProjectProgress(ProjectUserImportDTO dto, Project project) {
        Progress progress = new Progress();
        progress.setProId(project.getId());
        progress.setUserId(dto.getUserId());
        // 设置其他默认字段
        return progress;
    }

    private void updateProjectProgressStatusToFinish(Progress progress,
        ProjectUserImportDTO dto,
        Project project) {
        progress.setIsImport(1);
        progress.setStatus(1);
        progress.setProgressPercent(BigDecimal.valueOf(100));
        progress.setStartTime(Optional.ofNullable(dto.getEnterProjectTime()).orElse(project.getStartTime()));
        progress.setFinishTime(Optional.ofNullable(dto.getFinishProjectTime()).orElse(project.getEndTime()));
        progressService.saveOrUpdate(progress);
    }


    private void sendProjectCompletionEvent(String userId, Project project) {
        ExcitationMQEvent event = new ExcitationMQEvent(
            new ExcitationMQEventDTO()
                .setEventId(ExcitationEventEnum.finishProject.name())
                .setTargetName(project.getProName())
                .setTargetId(project.getId())
                .setUserId(userId)
        );
        mqProducer.sendMsg(event);
    }


    @Override
    @Async("commonTaskThreadPool")
    public Future<ImportResultDTO> importProjectLecturerData(String excelFilePath) {
        if (IMPORT_DATA_THREAD_POOL == null) {
            throw new BusinessException(FileErrorNoEnum.FILE_OPERATION_FAIL);
        }
        return IMPORT_DATA_THREAD_POOL.submit(() -> processImportProjectLecturerData(excelFilePath));
    }

    @Override
    public List<String> getProjectAllMentorCateIds(String projectId) {
        return baseMapper.getProjectAllMentorCateIds(projectId);
    }

    private ImportResultDTO processImportProjectLecturerData(String excelFilePath) {
        ImportResultDTO result = new ImportResultDTO();
        result.setIsSuccess(false);

        try {
            ImportDataDTO importData = importDataFeign.getImportData(excelFilePath);
            if (importData.getExcel() == null) {
                throw new BusinessException(FileErrorNoEnum.ERR_POST_CONTENT_NULL);
            }

            String[] fixedHead = {"项目编码", "项目名称", "讲师姓名", "用户账号", "讲师编号", "授课课程名称",
                "授课开始时间", "授课结束时间", "授课课时（分钟）", "授课评估分"};
            ExcelCheckMessage checkMessage = new ProjectLecturerExcelTemplate(fixedHead).check(importData.getExcel());
            if (!CollectionUtils.isEmpty(checkMessage.getMessage())) {
                result.setMsg(JsonUtil.objToJson(checkMessage.getMessage()));
                result.setMsgs(checkMessage.getMessage());
                return result;
            }

            List<ProjectLecturerImportDTO> importList = (List<ProjectLecturerImportDTO>) checkMessage.getObjects();
            if (checkProjectLecturerImportData(importList, result)) {
                importProjectLecturerData(importList, result);
            }
            return result;
        } catch (BusinessException e) {
            result.setMsg(e.getMessage());
            return result;
        }
    }

    private void importProjectLecturerData(List<ProjectLecturerImportDTO> importList, ImportResultDTO result) {
        long startTime = System.currentTimeMillis();

        Set<String> projectIds = importList.stream().map(ProjectLecturerImportDTO::getProjectId)
            .collect(Collectors.toSet());
        Map<String, Project> projectIdMap = listByIds(projectIds).stream()
            .collect(Collectors.toMap(Project::getId, Function.identity()));
        Set<String> categoryCodes = importList.stream().map(ProjectLecturerImportDTO::getLecturerCategoryCode)
            .collect(Collectors.toSet());
        Map<String, LecturerCategoryDTO> categoryCodeLecturerCategoryDTOMap = lecturerFeign.getLecturerCategoryMapByLecturerCategoryCodes(
            categoryCodes);

        List<AppSaveDTO> appSaveDTOList = new ArrayList<>();
        List<EditLecturerExaminationDTO> saveLecturerExaminationDTOList = new ArrayList<>();
        importList.forEach(dto -> {
            Project project = projectIdMap.get(dto.getProjectId());
            appSaveDTOList.add(createAppSaveDTO(dto, project));
            saveLecturerExaminationDTOList.add(createExaminationDTO(dto, project, categoryCodeLecturerCategoryDTOMap));
        });

        transactionTemplate.execute(status -> {
            lecturerExaminationFeign.saveExaminationBatch(saveLecturerExaminationDTOList);
            appService.saveResourceBatch(appSaveDTOList);
            return true;
        });

        result.setIsSuccess(true);
        result.setMsg("成功导入" + importList.size() + "条项目讲师数据");
        log.info("importProjectLecturerData 耗时{}", System.currentTimeMillis() - startTime);
    }

    private AppSaveDTO createAppSaveDTO(ProjectLecturerImportDTO dto, Project project) {
        AppSaveDTO appSave = new AppSaveDTO();
        appSave.setId(newId());
        appSave.setProjectId(project.getId());
        appSave.setResourceType(ProjectAppType.LECTURER.getNo());
        appSave.setResourceId(dto.getLecturerId());
        appSave.setCreateBy(UserThreadContext.getUserId());
        return appSave;
    }

    private EditLecturerExaminationDTO createExaminationDTO(ProjectLecturerImportDTO dto,
        Project project,
        Map<String, LecturerCategoryDTO> categories) {
        EditLecturerExaminationDTO exam = new EditLecturerExaminationDTO();
        exam.setId(newId());
        exam.setIsImport(1);
        exam.setImportedTeachCourseName(dto.getTeachCourseName());
        exam.setImportedTeachEvalScore(dto.getTeachEvaluationScore());
        exam.setBeginTime(Objects.isNull(dto.getTeachBeginTime()) ? project.getStartTime() : dto.getTeachBeginTime());
        exam.setEndTime(Objects.isNull(dto.getTeachEndTime()) ? project.getEndTime() : dto.getTeachEndTime());
        exam.setInstructionTime(dto.getInstructionTime());
        exam.setLecturerId(dto.getLecturerId());
        exam.setTrainProId(dto.getProjectId());
        exam.setCreateBy(UserThreadContext.getUserId());
        exam.setTrainProType("project");
        exam.setStatus(1);
        exam.setOrgId(project.getOrgId());
        exam.setEvalType(3);
        exam.setIsProDel(project.getIsDel());
        exam.setLecturerTypeId(categories.get(dto.getLecturerCategoryCode()).getId());
        return exam;
    }

    /**
     * 校验导入讲师数据
     *
     * @param importDTOList  项目导入讲师数据对象
     * @param importResultDTO 消息返回对象
     * @return
     */
    private boolean checkProjectLecturerImportData(List<ProjectLecturerImportDTO> importDTOList,
        ImportResultDTO importResultDTO) {
        long beginTime = System.currentTimeMillis();
        log.info("====================开始校验excel数据====================");
        Map<Integer, List<String>> errorMap = new HashMap<>();

        // 校验项目编码和项目名称、以及进入项目日期和完成项目日期
        projectImportLecturerCheckProjectAndTime(importDTOList, errorMap);

        // 校验姓名和用户账号
        projectImportLecturerCheckLecturerAndUser(importDTOList, errorMap);

        log.info("====================结束校验excel数据====================");
        log.info("总计耗时:{}", System.currentTimeMillis() - beginTime);
        // 移除value为空的键值对
        List<Integer> removeKeyList = errorMap.entrySet().stream()
            .filter(entry -> com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(entry.getValue())).map(
                Entry::getKey).toList();
        removeKeyList.forEach(errorMap::remove);
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(errorMap)) {
            return true;
        }
        importResultDTO.setIsSuccess(false);
        List<String> messageList = errorMap.entrySet().stream()
            .map(entry -> I18nUtil.getMessage("第") + entry.getKey() + I18nUtil.getMessage("行") + StringUtils.joinWith("、", entry.getValue()))
            .toList();
        importResultDTO.setMsg(JsonUtil.objToJson(messageList));
        return false;
    }

    private void projectImportLecturerCheckProjectAndTime(List<ProjectLecturerImportDTO> importDTOList,
        Map<Integer, List<String>> errorMap) {
        Set<String> projectNos = importDTOList.stream().map(ProjectLecturerImportDTO::getProjectNo).collect(Collectors.toSet());
        List<Project> projectList = list(Wrappers.<Project>lambdaQuery().in(Project::getProNo, projectNos));
        Map<String, Project> proNoProjectMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(projectList)) {
            proNoProjectMap = projectList.stream().collect(Collectors.toMap(Project::getProNo, project -> project, (k1, k2) -> k1));
        }
        for (ProjectLecturerImportDTO dto : importDTOList) {
            List<String> messageList = Optional.ofNullable(errorMap.get(dto.getRow())).orElse(new ArrayList<>());

            String projectNo = dto.getProjectNo();
            Project project = proNoProjectMap.get(projectNo);
            if (Objects.isNull(project)) {
                // 项目不存在
                messageList.add(I18nUtil.getMessage("项目编码对应的项目不存在"));
            } else {
                if (!dto.getProjectName().equals(project.getProName())) {
                    messageList.add(I18nUtil.getMessage("项目编码对应的项目与项目名称不匹配"));
                }
                dto.setProjectId(project.getId());
                // 校验进入项目日期和完成项目日期
                projectImportLecturerCheckTime(project, dto, messageList);

            }
            errorMap.put(dto.getRow(), messageList);
        }

    }

    private void projectImportLecturerCheckTime(Project project, ProjectLecturerImportDTO dto,
        List<String> messageList) {
        // 进入项目日期
        Date teachBeginTime = dto.getTeachBeginTime();
        Date projectStartTime = project.getStartTime();
        if (Objects.nonNull(teachBeginTime)) {
            if (teachBeginTime.before(projectStartTime)) {
                messageList.add(I18nUtil.getMessage("授课开始时间需要大于项目开始日期"));
            }
            dto.setTeachBeginTime(teachBeginTime);
        } else {
            dto.setTeachBeginTime(projectStartTime);
        }

        // 完成项目日期
        Date teachEndTime = dto.getTeachEndTime();
        Date projectEndTime = project.getEndTime();
        if (Objects.nonNull(teachEndTime)) {
            if (teachEndTime.after(projectEndTime)) {
                messageList.add(I18nUtil.getMessage("授课结束时间需要小于等于项目结束日期"));
            }
            dto.setTeachEndTime(teachEndTime);
        } else {
            dto.setTeachEndTime(projectEndTime);
        }
    }

    private void projectImportLecturerCheckLecturerAndUser(List<ProjectLecturerImportDTO> importDTOList,
        Map<Integer, List<String>> errorMap) {
        Set<String> userLoginNames = importDTOList.stream().map(ProjectLecturerImportDTO::getUserLoginName).collect(Collectors.toSet());
        Set<String> lecturerNumbers = importDTOList.stream().map(ProjectLecturerImportDTO::getLecturerNumber).collect(Collectors.toSet());
        Map<String, UserDTO> loginNameUserDTOMap = userFeign.getUserMapByLoginNames(userLoginNames);
        Map<String, LecturerDTO> lecturerNumberLecturerDTOMap = lecturerFeign.getLecturerMapByLecturerNumbers(lecturerNumbers);

        for (ProjectLecturerImportDTO dto : importDTOList) {
            List<String> messages = errorMap.computeIfAbsent(dto.getRow(), k -> new ArrayList<>());

            LecturerDTO lecturer = lecturerNumberLecturerDTOMap.get(dto.getLecturerNumber());
            UserDTO user = loginNameUserDTOMap.get(dto.getUserLoginName());

            if (!validateLecturerExists(lecturer, messages)) {
                continue;
            }
            // 校验讲师姓名是否匹配
            validateLecturerNameMatch(dto, lecturer, messages);
            // 校验内部讲师的用户信息
            validateInternalLecturerUserInfo(dto, lecturer, user, messages);

            if (user != null) {
                dto.setUserId(user.getId());
            }
            dto.setLecturerId(lecturer.getId());
            dto.setLecturerLevelId(lecturer.getLevelId());
            dto.setLecturerCategoryCode(lecturer.getCategoryCode());
        }
    }

    // 校验讲师是否存在
    private boolean validateLecturerExists(LecturerDTO lecturer, List<String> messages) {
        if (lecturer == null) {
            messages.add(I18nUtil.getMessage("讲师编号对应的讲师不存在"));
            return false;
        }
        return true;
    }

    // 校验讲师姓名是否匹配
    private void validateLecturerNameMatch(ProjectLecturerImportDTO dto, LecturerDTO lecturer, List<String> messages) {
        if (!dto.getLecturerName().equals(lecturer.getName())) {
            messages.add(I18nUtil.getMessage("讲师编号对应的讲师与讲师姓名不匹配"));
        }
    }

    // 校验内部讲师的用户信息
    private void validateInternalLecturerUserInfo(ProjectLecturerImportDTO dto, LecturerDTO lecturer, UserDTO user, List<String> messages) {
        if (lecturer.getType() != 0) return;

        if (StringUtils.isBlank(dto.getUserLoginName())) {
            messages.add(I18nUtil.getMessage("内部讲师对应的用户账号不能为空！"));
            return;
        }

        if (Objects.isNull(user)) {
            messages.add(I18nUtil.getMessage("用户账号对应的用户不存在！"));
            return;
        }

        if (!user.getId().equals(lecturer.getUserId())) {
            messages.add(I18nUtil.getMessage("讲师对应的用户不一致！"));
        }
    }

    /**
     * 校验导入学员数据
     *
     * @param importDTOList  项目导入学员数据对象
     * @param importResultDTO 消息返回对象
     * @return
     */
    private boolean checkProjectUserImportData(List<ProjectUserImportDTO> importDTOList,
        ImportResultDTO importResultDTO) {
        long beginTime = System.currentTimeMillis();
        log.info("====================开始校验excel数据====================");
        Map<Integer, List<String>> errorMap = new HashMap<>();

        // 校验项目编码和项目名称、以及进入项目日期和完成项目日期
        projectImportUserCheckProjectAndTime(importDTOList, errorMap);

        // 校验姓名和用户账号
        projectImportUserCheckUser(importDTOList, errorMap);

        log.info("====================结束校验excel数据====================");
        log.info("总计耗时:{}", System.currentTimeMillis() - beginTime);
        // 移除value为空的键值对
        List<Integer> removeKeyList = errorMap.entrySet().stream()
            .filter(entry -> com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(entry.getValue())).map(
                Entry::getKey).toList();
        removeKeyList.forEach(errorMap::remove);
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(errorMap)) {
            return true;
        }
        importResultDTO.setIsSuccess(false);
        List<String> messageList = errorMap.entrySet().stream()
            .map(entry -> I18nUtil.getMessage("第") + entry.getKey() + I18nUtil.getMessage("行") + StringUtils.joinWith(
                "、", entry.getValue()))
            .toList();
        importResultDTO.setMsg(JsonUtil.objToJson(messageList));
        return false;
    }



    private void projectImportUserCheckUser(List<ProjectUserImportDTO> importDTOList, Map<Integer, List<String>> errorMap) {
        Set<String> userLoginNames = importDTOList.stream().map(ProjectUserImportDTO::getUserLoginName).collect(Collectors.toSet());
        Map<String, UserDTO> loginNameUserDTOMap = userFeign.getUserMapByLoginNames(userLoginNames);
        for (ProjectUserImportDTO dto : importDTOList) {
            List<String> messageList = Optional.ofNullable(errorMap.get(dto.getRow())).orElse(new ArrayList<>());

            String userLoginName = dto.getUserLoginName();
            UserDTO userDTO = loginNameUserDTOMap.get(userLoginName);
            if (Objects.isNull(userDTO)) {
                // 项目不存在
                messageList.add(I18nUtil.getMessage("用户账号对应的用户不存在"));
            } else {
                if (!dto.getUserName().equals(userDTO.getFullName())) {
                    messageList.add(I18nUtil.getMessage("用户账号对应的用户与用户名称不匹配"));
                }
                dto.setUserId(userDTO.getId());
            }
            errorMap.put(dto.getRow(), messageList);
        }
    }

    private void projectImportUserCheckProjectAndTime(List<ProjectUserImportDTO> importDTOList, Map<Integer, List<String>> errorMap) {
        Set<String> projectNos = importDTOList.stream().map(ProjectUserImportDTO::getProjectNo).collect(Collectors.toSet());
        List<Project> projectList = list(Wrappers.<Project>lambdaQuery().in(Project::getProNo, projectNos));
        Map<String, Project> proNoProjectMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(projectList)) {
            proNoProjectMap = projectList.stream().collect(Collectors.toMap(Project::getProNo, project -> project, (k1, k2) -> k1));
        }
        for (ProjectUserImportDTO dto : importDTOList) {
            List<String> messageList = Optional.ofNullable(errorMap.get(dto.getRow())).orElse(new ArrayList<>());

            String projectNo = dto.getProjectNo();
            Project project = proNoProjectMap.get(projectNo);
            if (Objects.isNull(project)) {
                // 项目不存在
                messageList.add(I18nUtil.getMessage("项目编码对应的项目不存在"));
            } else {
                if (!dto.getProjectName().equals(project.getProName())) {
                    messageList.add(I18nUtil.getMessage("项目编码对应的项目与项目名称不匹配"));
                }
                dto.setProjectId(project.getId());
                // 校验进入项目日期和完成项目日期
                projectImportUserCheckTime(project, dto, messageList);

            }
            errorMap.put(dto.getRow(), messageList);
        }
    }

    private void projectImportUserCheckTime(Project project, ProjectUserImportDTO dto, List<String> messageList) {
        // 进入项目日期
        Date enterProjectTime = dto.getEnterProjectTime();
        Date projectStartTime = project.getStartTime();
        if (Objects.nonNull(enterProjectTime)) {
            if (enterProjectTime.before(projectStartTime)) {
                messageList.add(I18nUtil.getMessage("进入项目日期需要大于项目开始日期"));
            }
            dto.setEnterProjectTime(enterProjectTime);
        } else {
            dto.setEnterProjectTime(projectStartTime);
        }

        // 完成项目日期
        Date finishProjectTime = dto.getFinishProjectTime();
        Date projectEndTime = project.getEndTime();
        if (Objects.nonNull(finishProjectTime)) {
            if (finishProjectTime.after(projectEndTime)) {
                messageList.add(I18nUtil.getMessage("完成项目日期需要小于等于项目结束日期"));
            }
            dto.setFinishProjectTime(finishProjectTime);
        } else {
            dto.setFinishProjectTime(projectEndTime);
        }
    }
}
