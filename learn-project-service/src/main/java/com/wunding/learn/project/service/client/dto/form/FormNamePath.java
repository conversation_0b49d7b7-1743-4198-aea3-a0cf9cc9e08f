package com.wunding.learn.project.service.client.dto.form;

import com.wunding.learn.file.api.dto.NamePath;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(name = "FormNamePath", description = "辅导附件对象")
public class FormNamePath extends NamePath {
    
    private String mime;
    
    private String type;
    
    /**
     * 文件id
     */
    @Schema(hidden = true)
    private String sourceId;
    
    @Schema(description = "1-转码中 2-转码成功 3-转码失败 4-排队中")
    private Integer transformStatus;
    
    private NamePath sourceFile;
}
