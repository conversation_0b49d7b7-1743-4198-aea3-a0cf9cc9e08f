package com.wunding.learn.project.service.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 培训报名表
 *
 * <AUTHOR> href="mailto:<EMAIL>">cdl</a>
 * @since 2022-08-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("project_apply")
@Schema(name = "Apply对象", description = "培训报名表")
public class Apply implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键id
     */
    @Schema(description = "主键id")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;


    /**
     * 报名名称
     */
    @Schema(description = "报名名称")
    @TableField("apply_name")
    private String applyName;


    /**
     * 计划人数
     */
    @Schema(description = "计划人数")
    @TableField("plan_person")
    private Integer planPerson;


    /**
     * 报名地址
     */
    @Schema(description = "报名地址")
    @TableField("address")
    private String address;


    /**
     * 培训简介
     */
    @Schema(description = "培训简介")
    @TableField("description")
    private String description;


    /**
     * 培训时间
     */
    @Schema(description = "培训时间")
    @TableField("train_time")
    private String trainTime;


    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    @TableField("start_time")
    private Date startTime;


    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    @TableField("end_time")
    private Date endTime;


    /**
     * 联系电话
     */
    @Schema(description = "联系电话")
    @TableField("telephone")
    private String telephone;


    /**
     * 是否需要审核 0-否 1-是
     */
    @Schema(description = "是否需要审核 0-否 1-是")
    @TableField("is_need_audit")
    private Integer isNeedAudit;


    /**
     * 是否允许取消报名（审核通过后）0-否 1-是
     */
    @Schema(description = "是否允许取消报名（审核通过后）0-否 1-是")
    @TableField("is_allow_cancel")
    private Integer isAllowCancel;


    /**
     * 是否置顶 0-否 1-是
     */
    @Schema(description = "是否置顶 0-否 1-是")
    @TableField("is_top")
    private Integer isTop;


    /**
     * 是否启用 0-否 1-是
     */
    @Schema(description = "是否启用 0-否 1-是")
    @TableField("is_available")
    private Integer isAvailable;


    /**
     * 是否删除 0-否 1-是
     */
    @Schema(description = "是否删除 0-否 1-是")
    @TableField("is_del")
    @TableLogic(value = "0", delval = "1")
    private Integer isDel;


    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;


    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 是否培训班课程 0-否 1-是
     */
    @Schema(description = "是否培训班课程 0-否 1-是")
    @TableField("is_train")
    private Integer isTrain;


    /**
     * 是否发布： 0-否 1-是
     */
    @Schema(description = "是否发布： 0-否 1-是")
    @TableField("is_publish")
    private Integer isPublish;


    /**
     * 发布人
     */
    @Schema(description = "发布人")
    @TableField("publish_by")
    private String publishBy;


    /**
     * 发布时间
     */
    @Schema(description = "发布时间")
    @TableField("publish_time")
    private Date publishTime;


    /**
     * 更新人
     */
    @Schema(description = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;


    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 部门id
     */
    @Schema(description = "部门id")
    @TableField("org_id")
    private String orgId;


    /**
     * 客户id
     */
    @Schema(description = "客户id")
    @TableField("customer_id")
    private String customerId;


    /**
     * 联系人
     */
    @Schema(description = "联系人")
    @TableField("contact")
    private String contact;


    /**
     * 是否取消报名：0-否 1-是
     */
    @Schema(description = "是否取消报名：0-否 1-是")
    @TableField("is_cancel_regis")
    private Integer isCancelRegis;


    /**
     * 报名模板ID
     */
    @Schema(description = "报名模板ID")
    @TableField("form_template_id")
    private String formTemplateId;


    /**
     * 报名条件
     */
    @Schema(description = "报名条件")
    @TableField("apply_condition")
    private String applyCondition;


}
