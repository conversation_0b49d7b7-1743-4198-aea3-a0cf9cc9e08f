package com.wunding.learn.project.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.common.category.service.ICategorysService;
import com.wunding.learn.common.constant.other.AvailableEnum;
import com.wunding.learn.common.constant.project.ProjectErrorNoEnum;
import com.wunding.learn.common.enums.ResourceTypeEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.other.SystemConfigCodeEnum;
import com.wunding.learn.common.enums.project.ProjectAppType;
import com.wunding.learn.common.enums.project.ProjectTaskType;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.i18n.util.I18nUtil;
import com.wunding.learn.common.util.bean.BeanListUtils;
import com.wunding.learn.common.util.string.TranslateUtil;
import com.wunding.learn.project.service.admin.dto.ConfigurationListDTO;
import com.wunding.learn.project.service.admin.dto.ConfigurationSaveDTO;
import com.wunding.learn.project.service.admin.dto.SetDTO;
import com.wunding.learn.project.service.admin.query.ProjectTaskTypeQuery;
import com.wunding.learn.project.service.constant.ProjectConstant;
import com.wunding.learn.project.service.dao.SetDao;
import com.wunding.learn.project.service.enums.ProjectTaskTypeEnum;
import com.wunding.learn.project.service.mapper.SetMapper;
import com.wunding.learn.project.service.model.Project;
import com.wunding.learn.project.service.model.Set;
import com.wunding.learn.project.service.service.IProjectService;
import com.wunding.learn.project.service.service.ISetService;
import com.wunding.learn.user.api.service.ParaFeign;
import com.wunding.learn.user.api.service.RouterFeign;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <p> 学习项目配置表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">赖卓成</a>
 * @since 2022-07-19
 */
@Slf4j
@Service("setService")
public class SetServiceImpl extends ServiceImpl<SetMapper, Set> implements ISetService {

    /**
     * 项目应用
     */
    public static final int PROJECT_APP = 2;
    @Resource
    @Lazy
    IProjectService projectService;
    @Resource
    private ICategorysService categorysService;
    @Resource(name = "setDao")
    private SetDao setDao;
    @Resource
    private ParaFeign paraFeign;

    @Resource
    private RouterFeign routerFeign;


    @Override
    public List<SetDTO> allOpenApp(Integer type, Integer projectType) {
        List<Set> setList = list(new LambdaQueryWrapper<Set>().eq(Set::getType, PROJECT_APP)
            .eq(Set::getProjectType, Optional.ofNullable(projectType).orElse(ProjectConstant.PROJECT_TYPE_FIXED_DATE))
            .orderByAsc(Set::getId));
        List<String> routerIds = routerFeign.getRouterNames();
        log.info("routerIds: " + routerIds);
        List<SetDTO> setDTOList = new ArrayList<>();
        setList.stream().filter(set -> type.equals(GeneralJudgeEnum.CONFIRM.getValue()) || (
            type.equals(GeneralJudgeEnum.NEGATIVE.getValue()) && (
                !set.getCode().equals(ProjectAppType.APPLY.getAppType()) && !set.getCode()
                    .equals(ProjectAppType.SIGN.getAppType())))).forEach(set -> {
            SetDTO setDTO = new SetDTO();
            if (StringUtils.equals(set.getCode(), "lecturer") && routerIds.contains(ResourceTypeEnum.LECTURER.getRouter())) {
                BeanUtils.copyProperties(set, setDTO);
                setDTO.setName(I18nUtil.getDefaultMessage(setDTO.getName()));
                setDTOList.add(setDTO);
            }
            if (!StringUtils.equals(set.getCode(), "lecturer")){
                BeanUtils.copyProperties(set, setDTO);
                setDTO.setName(I18nUtil.getDefaultMessage(setDTO.getName()));
                setDTOList.add(setDTO);
            }
        });
        return setDTOList;
    }

    @Override
    public List<SetDTO> openApp(String projectId) {
        Project project = projectService.getById(projectId);
        if (project == null) {
            throw new BusinessException(ProjectErrorNoEnum.PROJECT_NOT_EXIST);
        }

        String projectItem = project.getProjectItem();
        List<String> itemList = TranslateUtil.translateBySplit(projectItem, String.class);
        Collection<String> setCodes = new HashSet<>(Objects.requireNonNull(itemList));
        if (!CollectionUtils.isEmpty(setCodes) && setCodes.contains(ProjectAppType.TOPIC.getAppType())) {
            setCodes.add(ProjectAppType.TOPIC_MANAGE.getAppType());
        }

        String paraValue = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_713.getCode());
        // 根据参数配置显示开票管理
        if (!CollectionUtils.isEmpty(setCodes) && StringUtils.equals(paraValue,
            GeneralJudgeEnum.NEGATIVE.getValue().toString())) {
            setCodes.remove(ProjectAppType.INVOICE.getAppType());
        }

        java.util.Set<SetDTO> collect1 = setCodes.stream().map(setCode -> {
            SetDTO setDTO = new SetDTO();
            setDTO.setCode(setCode);
            ProjectAppType projectAppType = ProjectAppType.getByAppType(setCode);
            if (null != projectAppType) {
                setDTO.setNo(projectAppType.getSortNo());
                setDTO.setName(projectAppType.getAppName());
            }
            return setDTO;
        }).collect(Collectors.toSet());

        List<SetDTO> collect = new ArrayList<>(collect1);

        collect.sort(Comparator.comparing(SetDTO::getNo));

        return collect;
    }

    @Override
    public List<SetDTO> faceProjectOpenApp(String projectId) {
        Project project = projectService.getById(projectId);
        if (project == null) {
            throw new BusinessException(ProjectErrorNoEnum.PROJECT_NOT_EXIST);
        }

        String projectItem = project.getProjectItem();
        List<String> itemList = TranslateUtil.translateBySplit(projectItem, String.class);
        Collection<String> setCodes = new HashSet<>(Objects.requireNonNull(itemList));
        if (!CollectionUtils.isEmpty(setCodes) && setCodes.contains(ProjectAppType.TOPIC.getAppType())) {
            setCodes.add(ProjectAppType.TOPIC_MANAGE.getAppType());
        }

        List<ProjectAppType> faceProjectAppType = ProjectAppType.getFaceProjectAppType();

        String paraValue = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_713.getCode());
        // 根据参数配置显示开票管理
        if (!CollectionUtils.isEmpty(setCodes) && StringUtils.equals(paraValue,
            GeneralJudgeEnum.NEGATIVE.getValue().toString())) {
            setCodes.remove(ProjectAppType.INVOICE.getAppType());
        }
        // 学费为0和请假0，学员端顶部菜单栏不显示开票和请假入口
        if (project.getTuition().compareTo(BigDecimal.ZERO) <= 0) {
            setCodes.remove(ProjectAppType.INVOICE.getAppType());
        }
        if (GeneralJudgeEnum.NEGATIVE.getValue().equals(project.getMaxAbsentClass())) {
            setCodes.remove(ProjectAppType.VACATE.getAppType());
        }

        // 面授应用和学习项目应用排序不同  所以这里不用枚举里面的排序值排序
        return faceProjectAppType.stream().filter(
            appType -> Objects.equals(appType, ProjectAppType.SCHEDULE) || Objects.equals(appType, ProjectAppType.FILE)
                || setCodes.contains(appType.getAppType())).map(appType -> {
            SetDTO setDTO = new SetDTO();
            setDTO.setCode(appType.getAppType());
            setDTO.setNo(appType.getSortNo());
            setDTO.setName(appType.getAppName());
            return setDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<SetDTO> taskTypeList(ProjectTaskTypeQuery query) {
        // 查询项目配置
        LambdaQueryWrapper<Set> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Set::getIsAvailable, AvailableEnum.AVAILABLE.getValue());
        if (Objects.equals(ProjectConstant.PROJECT_TYPE_FACE, query.getProjectType())) {
            // 面授项目
            queryWrapper.eq(Set::getProjectType, ProjectConstant.PROJECT_TYPE_FACE);
            // 面授项目是否需要过滤应用处理
            boolean isFilterApp = Objects.equals(GeneralJudgeEnum.CONFIRM.getValue(), query.getIsFilterApp());
            queryWrapper.and(wrapper -> wrapper.eq(Set::getType, 1).or(!isFilterApp)
                .in(!isFilterApp, Set::getCode, ProjectAppType.getFaceProjectTaskAppType()));
        } else {
            // 其他项目
            queryWrapper.eq(Set::getProjectType, ProjectConstant.PROJECT_TYPE_DEFAULT);
            queryWrapper.eq(Set::getType, 1);
        }
        queryWrapper.orderByAsc(Set::getId);
        List<Set> list = list(queryWrapper);

        // 默认的这一批类型需要做国际化处理
        list.forEach(set -> set.setName(I18nUtil.getDefaultMessage(set.getName())));

        /*
         备注：此处代码有删减：去掉了动态添加辅导任务分类的处理。原逻辑（创建方式 直接创建 补充动态表单类型）
        */

        // 响应数据处理
        Stream<Set> stream = list.stream();
        // 创建方式 引用创建 排除辅导任务类型
        if (Objects.equals(ProjectConstant.CREATE_TYPE_QUOTE, query.getAddType())) {
            stream = stream.filter(
                set -> !Objects.equals(ProjectConstant.DEFAULT_FORM_TASK_CATEGORY_ID, set.getCode()));
        }
        // 创建方式 直接创建 排除项目类型
        if (Objects.equals(ProjectConstant.CREATE_TYPE_DIRECT, query.getAddType())) {
            stream = stream.filter(set -> !Objects.equals(ProjectTaskTypeEnum.PROJECT.getTaskType(), set.getCode()));
        }
        // 项目类型 快速培训项目 排除直播任务类型
        if (Objects.equals(ProjectConstant.PROJECT_TYPE_QUICK, query.getProjectType())) {
            stream = stream.filter(set -> !Objects.equals(ProjectTaskTypeEnum.LIVE.getTaskType(), set.getCode()));
        }
        // 计划类型 固定周期 排除直播任务类型
        if (Objects.equals(ProjectConstant.PROJECT_TYPE_FIXED_CYCLE, query.getType())) {
            stream = stream.filter(set -> !Objects.equals(ProjectTaskTypeEnum.LIVE.getTaskType(), set.getCode()));
        }
        // 引用添加 过滤
        if (Objects.equals(ProjectConstant.CREATE_TYPE_QUOTE, query.getAddType())) {
            stream = stream.filter(
                set -> !Objects.equals(ProjectTaskTypeEnum.PRACTICAL_OPERATION.getTaskType(), set.getCode()));
        }

        List<String> routerIds = routerFeign.getRouterNames();
        // 检验系统版本权限是否包含要校验的内容；
        if(!routerIds.contains(ResourceTypeEnum.COACH_TMP_MANAGE.getRouter())){
            stream = stream.filter(set -> !Objects.equals(ProjectConstant.DEFAULT_FORM_TASK_CATEGORY_ID, set.getCode()));
        }

        return BeanListUtils.copyList(stream.collect(Collectors.toList()), SetDTO.class);
    }

    @Override
    public List<SetDTO> getTaskTypeList() {
        return list(new LambdaQueryWrapper<Set>().eq(Set::getIsAvailable, 1).eq(Set::getType, 1)
            .in(Set::getCode, ProjectTaskType.course, ProjectTaskType.exam, ProjectTaskType.survey)
            // 分析代码后仅讲师工作台使用本方法,即仅普通项目使用
            .eq(Set::getProjectType, 0).orderByAsc(Set::getId)).stream().map(set -> {
            SetDTO setDTO = new SetDTO();
            setDTO.setName(set.getName());
            setDTO.setCode(set.getCode());
            return setDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<ConfigurationListDTO> getConfigurationList(Integer type) {
        List<Set> list = this.list(new LambdaQueryWrapper<Set>().eq(Set::getProjectType,
            Optional.ofNullable(type).orElse(ProjectConstant.PROJECT_TYPE_FIXED_DATE)));
        return list.stream().filter(s -> !s.getCode().equals(ProjectAppType.APPLY.getAppType())).map(
            set -> new ConfigurationListDTO().setName(I18nUtil.getDefaultMessage(set.getName())).setType(set.getType())
                .setId(set.getId()).setIsAvailable(set.getIsAvailable())).collect(Collectors.toList());
    }

    @Override
    public void saveConfigurationList(List<ConfigurationSaveDTO> configurationSaveDTOList) {
        for (ConfigurationSaveDTO configurationSaveDTO : configurationSaveDTOList) {
            Set set = this.getById(configurationSaveDTO.getId());
            // 微服务项目 下面两个字段作废
            set.setIsProject(null).setIsSpecial(null).setIsAvailable(configurationSaveDTO.getIsAvailable());
            setDao.updateSet(set);
        }
    }
}
