<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.project.service.mapper.ProjectResourceTaskExecuteMapper">
    <select id="getExecuteProjectId" resultType="com.wunding.learn.project.service.model.ProjectResourceTaskExecute">
        select id, resource_id resourceId, execute_times executeTimes
        from project_resource_task_execute
        where (
                    execute_status = 0
                or( execute_status = -1 and execute_times <![CDATA[< ]]> max_execute_times)
            ) and is_del = 0
            limit 1
    </select>

</mapper>
