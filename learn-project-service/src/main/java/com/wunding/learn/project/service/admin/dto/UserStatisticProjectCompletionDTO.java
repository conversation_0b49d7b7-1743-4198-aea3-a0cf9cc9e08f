package com.wunding.learn.project.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户项目完成情况统计
 *
 * <AUTHOR>
 * @since 20025-06-14
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(name = "UserStatisticProjectCompletionDTO", description = "用户项目完成情况统计")
public class UserStatisticProjectCompletionDTO implements Serializable {

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private String userId;

    /**
     * 学习项目ID
     */
    @Schema(description = "学习项目ID")
    private String projectId;

    /**
     * 总任务数
     */
    @Schema(description = "总任务数")
    private Integer totalCount;

    /**
     * 总任务完成数
     */
    @Schema(description = "总任务完成数")
    private Integer totalFinishCount;


    /**
     * 项目完成率（所有任务完成率）
     */
    @Schema(description = "项目完成率（所有任务完成率）")
    private BigDecimal finishRatio;

    /**
     * 总任务数（课程任务中计算必修）
     */
    @Schema(description = "总任务数（课程任务中计算必修）")
    private Integer totalOnlyMustCount;

    /**
     * 总任务完成数（课程任务中计算必修）
     */
    @Schema(description = "总任务完成数（课程任务中计算必修）")
    private Integer totalOnlyMustFinishCount;

    /**
     * 项目完成率（所有任务完成率，课程任务中计算必修）
     */
    @Schema(description = "项目完成率（所有任务完成率，课程任务中计算必修）")
    private BigDecimal finishOnlyMustRatio;

    /**
     * 必修课程总数
     */
    @Schema(description = "必修课程总数")
    private Integer totalCourseMustCount;

    /**
     * 必修课程总完成数
     */
    @Schema(description = "必修课程总完成数")
    private Integer totalCourseMustFinishCount;

    /**
     * 必修课程完成率
     */
    @Schema(description = "必修课程完成率")
    private BigDecimal courseMustFinishRatio;
}
