package com.wunding.learn.project.service.admin.rest;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.bean.Result;
import com.wunding.learn.common.dto.StatisticPersonRankDTO;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.evaluation.api.dto.EvalDTO;
import com.wunding.learn.excitation.api.dto.UserExcitationRecordDTO;
import com.wunding.learn.excitation.api.query.UserProjectExcitationRecordQuery;
import com.wunding.learn.file.api.dto.ExportResultDTO;
import com.wunding.learn.project.service.admin.dto.AnalysisByTypeUserDTO;
import com.wunding.learn.project.service.admin.dto.HeaderUnitDTO;
import com.wunding.learn.project.service.admin.dto.ProjectAnalysisByOrgDTO;
import com.wunding.learn.project.service.admin.dto.ProjectAnalysisByProjectDTO;
import com.wunding.learn.project.service.admin.dto.ProjectAnalysisByTypeDTO;
import com.wunding.learn.project.service.admin.dto.ProjectAnalysisByUserDTO;
import com.wunding.learn.project.service.admin.dto.ProjectJoinUserPageDTO;
import com.wunding.learn.project.service.admin.dto.ProjectStatisticByOrgDTO;
import com.wunding.learn.project.service.admin.dto.StatisticLecturerEvaluationDTO;
import com.wunding.learn.project.service.admin.dto.StatisticOrgCompleteDTO;
import com.wunding.learn.project.service.admin.dto.StatisticOrgCompleteUserDetailDTO;
import com.wunding.learn.project.service.admin.dto.StatisticOrgLearnRankDTO;
import com.wunding.learn.project.service.admin.dto.StatisticProjectCompletionDTO;
import com.wunding.learn.project.service.admin.dto.StatisticProjectDTO;
import com.wunding.learn.project.service.admin.dto.StatisticSimpleTaskDTO;
import com.wunding.learn.project.service.admin.dto.StatisticTaskDTO;
import com.wunding.learn.project.service.admin.dto.StatisticTaskDetailDTO;
import com.wunding.learn.project.service.admin.dto.StatisticTaskTypeDTO;
import com.wunding.learn.project.service.admin.dto.StatisticTeamCompleteDTO;
import com.wunding.learn.project.service.admin.dto.StatisticTeamDTO;
import com.wunding.learn.project.service.admin.dto.StatisticTeamLearnRankDTO;
import com.wunding.learn.project.service.admin.dto.UserCoursewareStudyDetailDTO;
import com.wunding.learn.project.service.admin.dto.UserTaskDTO;
import com.wunding.learn.project.service.admin.query.AnalysisByTypeUserQuery;
import com.wunding.learn.project.service.admin.query.ProjectAnalysisByOrgQuery;
import com.wunding.learn.project.service.admin.query.ProjectAnalysisByProjectQuery;
import com.wunding.learn.project.service.admin.query.ProjectAnalysisByTypeQuery;
import com.wunding.learn.project.service.admin.query.ProjectAnalysisByUserQuery;
import com.wunding.learn.project.service.admin.query.ProjectJoinUserQuery;
import com.wunding.learn.project.service.admin.query.StatisticLecturerEvaluationQuery;
import com.wunding.learn.project.service.admin.query.StatisticOrgCompleteQuery;
import com.wunding.learn.project.service.admin.query.StatisticOrgCompleteUserDetailQuery;
import com.wunding.learn.project.service.admin.query.StatisticOrgLearnRankQuery;
import com.wunding.learn.project.service.admin.query.StatisticPersonRankQuery;
import com.wunding.learn.project.service.admin.query.StatisticProjectCompletionQuery;
import com.wunding.learn.project.service.admin.query.StatisticProjectOrgQuery;
import com.wunding.learn.project.service.admin.query.StatisticProjectQuery;
import com.wunding.learn.project.service.admin.query.StatisticTaskDetailQuery;
import com.wunding.learn.project.service.admin.query.StatisticTaskQuery;
import com.wunding.learn.project.service.admin.query.StatisticTeamCompleteQuery;
import com.wunding.learn.project.service.admin.query.StatisticTeamCompleteUserDetailQuery;
import com.wunding.learn.project.service.admin.query.StatisticTeamLearnRankQuery;
import com.wunding.learn.project.service.admin.query.UserProjectCoursewareStudyDetailQuery;
import com.wunding.learn.project.service.admin.query.UserTaskListQuery;
import com.wunding.learn.project.service.enums.ProjectAnalysisBizTypeEnum;
import com.wunding.learn.project.service.service.IHoldStatisticService;
import com.wunding.learn.project.service.service.IProjectStatisticByOrgCollectService;
import com.wunding.learn.project.service.service.IProjectStatisticService;
import com.wunding.learn.project.service.service.ITaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.constraints.Length;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 学习项目统计
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">wgr</a>
 * @date 2022/9/22 20:01
 */

@Tag(description = "学习项目项目统计", name = "ProjectStatisticRest")
@RestController
@RequestMapping("${module.project.contentPath:/}projectStatistic")
@Slf4j
@Validated
public class ProjectStatisticRest {

    @Resource
    private ITaskService taskService;

    @Resource
    private IProjectStatisticService projectStatisticService;

    @Resource
    private IProjectStatisticByOrgCollectService projectStatisticByOrgCollectService;
    @Resource
    private IHoldStatisticService holdStatisticService;

    @GetMapping("/orgCompleteStatistic")
    @Operation(operationId = "orgCompleteStatistic", summary = "学习项目部门完成统计", description = "学习项目部门完成统计")
    public Result<PageInfo<StatisticOrgCompleteDTO>> findOrgStatisticCompleteByPage(
        StatisticOrgCompleteQuery statisticOrgCompleteQuery) {
        return Result.success(projectStatisticService.findOrgStatisticCompleteByPage(statisticOrgCompleteQuery));
    }

    @GetMapping("/orgCompleteStatistic/userDetail")
    @Operation(operationId = "orgCompleteStatisticUserDetail", summary = "学习项目部门完成统计用户详情", description = "学习项目部门完成统计用户详情")
    public Result<PageInfo<StatisticOrgCompleteUserDetailDTO>> findOrgStatisticCompleteUserDetailByPage(
        @Valid @ParameterObject StatisticOrgCompleteUserDetailQuery query) {
        return Result.success(projectStatisticService.findOrgStatisticCompleteUserDetailByPage(query));
    }

    /**
     * 导出学习项目部门完成统计用户详情
     */
    @PostMapping("/exportOrgCompleteStatisticUserDetail")
    @Operation(operationId = "exportOrgCompleteStatisticUserDetail", summary = "导出学习项目部门完成统计用户详情", description = "导出学习项目部门完成统计用户详情")
    public Result<ExportResultDTO> exportOrgCompleteStatisticUserDetail(@Valid @ParameterObject StatisticOrgCompleteUserDetailQuery query) {
        projectStatisticService.exportOrgCompleteStatisticUserDetail(query);
        return Result.success();
    }


    @GetMapping("/teamCompleteStatistic")
    @Operation(operationId = "teamCompleteStatistic", summary = "学习项目团队完成统计", description = "学习项目团队完成统计")
    public Result<PageInfo<StatisticTeamCompleteDTO>> findTeamStatisticCompleteByPage(
        StatisticTeamCompleteQuery statisticTeamCompleteQuery) {
        return Result.success(projectStatisticService.findTeamStatisticCompleteByPage(statisticTeamCompleteQuery));
    }

    @GetMapping("/teamCompleteStatistic/userDetail")
    @Operation(operationId = "teamCompleteStatisticUserDetail", summary = "学习项目团队完成统计用户详情", description = "学习项目团队完成统计用户详情")
    public Result<PageInfo<StatisticOrgCompleteUserDetailDTO>> findTeamStatisticCompleteUserDetailByPage(
        @Valid @ParameterObject StatisticTeamCompleteUserDetailQuery query) {
        return Result.success(projectStatisticService.findTeamStatisticCompleteUserDetailByPage(query));
    }

    /**
     * 导出学习项目团队完成统计用户详情
     */
    @PostMapping("/exportTeamCompleteStatisticUserDetail")
    @Operation(operationId = "exportTeamCompleteStatisticUserDetail", summary = "导出学习项目团队完成统计用户详情", description = "导出学习项目团队完成统计用户详情")
    public Result<ExportResultDTO> exportTeamCompleteStatisticUserDetail(@Valid @ParameterObject StatisticTeamCompleteUserDetailQuery query) {
        projectStatisticService.exportTeamCompleteStatisticUserDetail(query);
        return Result.success();
    }

    @GetMapping("/taskStatistic")
    @Operation(operationId = "taskStatistic", summary = "学习项目任务完成情况统计", description = "学习项目任务完成情况统计")
    public Result<PageInfo<StatisticTaskDTO>> findTaskStatisticByPage(StatisticTaskQuery statisticTaskQuery) {
        return Result.success(projectStatisticService.findTaskStatisticByPage(statisticTaskQuery));
    }

    @GetMapping("/taskTypeList/{projectId}")
    @Operation(operationId = "taskTypeList", summary = "学习项目项目统计明细统计任务类型", description = "学习项目项目统计明细统计任务类型")
    public Result<List<StatisticTaskTypeDTO>> findStatisticTaskTypeList(
        @Parameter(description = "学习项目id") @PathVariable @Length(max = 36, message = "id长度不能超过36") String projectId) {
        return Result.success(projectStatisticService.findStatisticTaskTypeList(projectId));
    }


    @GetMapping("/taskList")
    @Operation(operationId = "taskList", summary = "学习项目项目统计明细统计任务名称", description = "学习项目项目统计明细统计任务名称")
    public Result<List<StatisticSimpleTaskDTO>> findStatisticTaskTypeList(
        @Parameter(description = "学习项目id", required = true) @Length(max = 36, message = "id长度不能超过36") String projectId,
        @Parameter(description = "任务类型") String taskType) {
        return Result.success(projectStatisticService.findStatisticTaskList(projectId, taskType));
    }


    @GetMapping("/teamList/{projectId}")
    @Operation(operationId = "teamList", summary = "学习项目项目统计明细统计团队列表", description = "学习项目项目统计明细统计团队列表")
    public Result<List<StatisticTeamDTO>> findProjectTeamList(
        @Parameter(description = "学习项目id") @PathVariable @Length(max = 36, message = "id长度不能超过36") String projectId) {
        return Result.success(projectStatisticService.findProjectTeamList(projectId));
    }

    @GetMapping("/taskDetail")
    @Operation(operationId = "taskDetail", summary = "学习项目任务明细统计", description = "学习项目任务明细统计")
    public Result<PageInfo<StatisticTaskDetailDTO>> findTaskDetailByPage(
        StatisticTaskDetailQuery statisticTaskDetailQuery) {
        return Result.success(projectStatisticService.findTaskDetailByPage(statisticTaskDetailQuery));
    }


    @GetMapping("/personRank")
    @Operation(operationId = "personRank", summary = "学习项目个人学习排名统计", description = "学习项目个人学习排名统计")
    public Result<PageInfo<StatisticPersonRankDTO>> findPersonRankByPage(
        StatisticPersonRankQuery statisticPersonRankQuery) {
        return Result.success(projectStatisticService.findPersonRankByPage(statisticPersonRankQuery));
    }

    @GetMapping("/teamLearnRank")
    @Operation(operationId = "teamLearnRank", summary = "学习项目团队学习排名统计", description = "学习项目团队学习排名统计")
    public Result<PageInfo<StatisticTeamLearnRankDTO>> findTeamLearnRankByPage(
        StatisticTeamLearnRankQuery statisticTeamLearnRankQuery) {
        return Result.success(projectStatisticService.findTeamLearnRankByPage(statisticTeamLearnRankQuery));
    }

    @GetMapping("/orgLearnRank")
    @Operation(operationId = "orgLearnRank", summary = "学习项目部门学习排名统计", description = "学习项目部门学习排名统计")
    public Result<PageInfo<StatisticOrgLearnRankDTO>> findOrgLearnRankByPage(
        StatisticOrgLearnRankQuery statisticOrgLearnRankQuery) {
        return Result.success(projectStatisticService.findOrgLearnRankByPage(statisticOrgLearnRankQuery));
    }

    /**
     * 项目完成情况统计
     */
    @GetMapping("/projectCompletion")
    @Operation(operationId = "projectCompletion", summary = "项目完成情况统计", description = "项目完成情况统计")
    public Result<PageInfo<StatisticProjectCompletionDTO>> findProjectCompletionByPage(
        StatisticProjectCompletionQuery query) {
        return Result.success(projectStatisticService.findProjectCompletionStatisticByPage(query));
    }

    /**
     * 导出学项目完成情况统计
     */
    @PostMapping("/exportProjectCompletion")
    @Operation(operationId = "exportProjectCompletion", summary = "导出项目完成情况统计", description = "导出项目完成情况统计")
    public Result<ExportResultDTO> exportProjectCompletion(
        @Valid @ParameterObject StatisticProjectCompletionQuery query) {
        projectStatisticService.exportProjectCompletionStatistic(query);
        return Result.success();
    }

    /**
     * 用户项目任务完成情况统计
     */
    @GetMapping("/userTaskList")
    @Operation(operationId = "userTaskList", summary = "用户项目任务完成情况统计", description = "用户项目任务完成情况统计")
    public Result<PageInfo<UserTaskDTO>> findUserTaskListByPage(UserTaskListQuery query) {
        return Result.success(taskService.findUserTaskListByPage(query));
    }

    /**
     * 导出用户项目任务完成情况统计
     */
    @PostMapping("/exportUserTaskList")
    @Operation(operationId = "exportUserTaskList", summary = "导出用户项目任务完成情况统计", description = "导出用户项目任务完成情况统计")
    public Result<ExportResultDTO> exportUserTaskList(UserTaskListQuery query) {
        taskService.exportUserTask(query);
        return Result.success();
    }

    /**
     * 用户项目激励获取记录
     */
    @GetMapping("/userProjectExcitationRecord")
    @Operation(operationId = "userProjectExcitationRecord", summary = "用户项目激励获取记录", description = "用户项目激励获取记录")
    public Result<PageInfo<UserExcitationRecordDTO>> findUserProjectExcitationRecordByPage(
        UserProjectExcitationRecordQuery query) {
        return Result.success(projectStatisticService.findUserProjectExcitationRecordByPage(query));
    }

    /**
     * 导出用户项目激励获取记录
     */
    @PostMapping("/exportUserProjectExcitationRecord")
    @Operation(operationId = "exportUserProjectExcitationRecord", summary = "导出用户项目激励获取记录", description = "导出用户项目激励获取记录")
    public Result<ExportResultDTO> exportUserProjectExcitationRecord(UserProjectExcitationRecordQuery query) {
        projectStatisticService.exportUserProjectExcitationRecord(query);
        return Result.success();
    }

    /**
     * 用户学习项目课时统计
     */
    @GetMapping("/userProjectCoursewareStudyDetail")
    @Operation(operationId = "userProjectCoursewareStudyDetail", summary = "用户学习项目课时统计", description = "用户学习项目课时统计")
    public Result<PageInfo<UserCoursewareStudyDetailDTO>> findUserProjectCoursewareStudyDetailByPage(
        UserProjectCoursewareStudyDetailQuery query) {
        return Result.success(projectStatisticService.findUserProjectCoursewareStudyDetailByPage(query));
    }

    /**
     * 导出用户学习项目课时统计
     */
    @PostMapping("/exportUserProjectCoursewareStudyDetail")
    @Operation(operationId = "exportUserProjectCoursewareStudyDetail", summary = "导出用户学习项目课时统计", description = "导出用户学习项目课时统计")
    public Result<ExportResultDTO> exportUserProjectCoursewareStudyDetail(UserProjectCoursewareStudyDetailQuery query) {
        projectStatisticService.exportUserProjectCoursewareStudyDetail(query);
        return Result.success();
    }


    @GetMapping("/projectStatistic")
    @Operation(operationId = "projectStatistic", summary = "培训班统计", description = "培训班统计")
    public Result<PageInfo<StatisticProjectDTO>> findProjectStatisticByPage(
        StatisticProjectQuery statisticProjectQuery) {
        return Result.success(projectStatisticService.findProjectStatisticByPage(statisticProjectQuery));
    }

    /**
     * 导出学习项目部门学习排名统计
     */
    @PostMapping("/exportProjectStatistic")
    @Operation(operationId = "exportProjectStatistic", summary = "导出培训班统计", description = "导出培训班统计")
    public Result<ExportResultDTO> exportProjectStatistic(StatisticProjectQuery statisticProjectQuery) {
        projectStatisticService.exportProjectStatistic(statisticProjectQuery);
        return Result.success();
    }


    @GetMapping("/findLecturerEvaluationStatisticByPage")
    @Operation(operationId = "findLecturerEvaluationStatisticByPage", summary = "培训班讲师统计", description = "培训班讲师统计")
    public Result<PageInfo<StatisticLecturerEvaluationDTO>> findLecturerEvaluationStatisticByPage(
        StatisticLecturerEvaluationQuery statisticLecturerEvaluationQuery) {
        return Result.success(
            projectStatisticService.findLecturerEvaluationStatisticByPage(statisticLecturerEvaluationQuery));
    }

    /**
     * 导出学习项目部门学习排名统计
     */
    @PostMapping("/exportLecturerEvaluationStatistic")
    @Operation(operationId = "exportLecturerEvaluationStatistic", summary = "导出培训班讲师统计", description = "导出培训班讲师统计")
    public Result<ExportResultDTO> exportLecturerEvaluationStatistic(
        StatisticLecturerEvaluationQuery statisticLecturerEvaluationQuery) {
        projectStatisticService.exportLecturerEvaluationStatistic(statisticLecturerEvaluationQuery);
        return Result.success();
    }

    @GetMapping("/findProjectEvaluation")
    @Operation(operationId = "findProjectEvaluation", summary = "培训班组织统计-获取评估列表", description = "培训班组织统计-获取评估列表")
    public Result<List<EvalDTO>> findProjectEvaluation(
        StatisticProjectOrgQuery statisticProjectOrgQuery) {
        return Result.success(projectStatisticService.findProjectEvaluation(statisticProjectOrgQuery));
    }


    @GetMapping("/findProjectOrgHeader")
    @Operation(operationId = "findProjectOrgHeader", summary = "培训班组织统计表头", description = "培训班组织统计表头")
    public Result<List<HeaderUnitDTO>> findProjectOrgHeader(
        StatisticProjectOrgQuery statisticProjectOrgQuery) {
        return Result.success(projectStatisticService.findProjectOrgHeader(statisticProjectOrgQuery));
    }

    @GetMapping("/findProjectOrgByPage")
    @Operation(operationId = "findProjectOrgByPage", summary = "培训班组织统计", description = "培训班组织统计")
    public Result<PageInfo<Map<String, Object>>> findProjectOrgByPage(
        StatisticProjectOrgQuery statisticProjectOrgQuery) {
        return Result.success(projectStatisticService.findProjectOrgByPage(statisticProjectOrgQuery));
    }

    @PostMapping("/exportProjectOrgStatistic")
    @Operation(operationId = "exportProjectOrgStatistic", summary = "导出培训班组织统计", description = "导出培训班组织统计")
    public Result<ExportResultDTO> exportData(
        @Parameter(description = "培训班组织统计查询对象") @RequestBody @Valid StatisticProjectOrgQuery statisticProjectOrgQuery) {
        projectStatisticService.exportProjectOrgStatistic(statisticProjectOrgQuery);
        return Result.success();
    }

    /**
     * 导出学习项目任务明细统计
     */
    @PostMapping("/exportTaskDetailData")
    @Operation(operationId = "exportTaskDetailData", summary = "导出学习项目任务明细统计", description = "导出学习项目任务明细统计")
    public Result<ExportResultDTO> exportTaskDetailData(StatisticTaskDetailQuery queryDTO) {
        projectStatisticService.exportTaskDetailData(queryDTO);
        return Result.success();
    }

    /**
     * 导出学习项目个人学习排名统计
     */
    @PostMapping("/exportPersonRankData")
    @Operation(operationId = "exportPersonRankData", summary = "导出学习项目个人学习排名统计", description = "导出学习项目个人学习排名统计")
    public Result<ExportResultDTO> exportPersonRankData(StatisticPersonRankQuery queryDTO) {
        projectStatisticService.exportPersonRankData(queryDTO);
        return Result.success();
    }

    /**
     * 导出学习项目团队学习排名统计
     */
    @PostMapping("/exportTeamLearnRankData")
    @Operation(operationId = "exportTeamLearnRankData", summary = "导出学习项目团队学习排名统计", description = "导出学习项目团队学习排名统计")
    public Result<ExportResultDTO> exportTeamLearnRankData(StatisticTeamLearnRankQuery queryDTO) {
        projectStatisticService.exportTeamLearnRankData(queryDTO);
        return Result.success();
    }

    /**
     * 导出学习项目部门学习排名统计
     */
    @PostMapping("/exportOrgLearnRankData")
    @Operation(operationId = "exportOrgLearnRankData", summary = "导出学习项目部门学习排名统计", description = "导出学习项目部门学习排名统计")
    public Result<ExportResultDTO> exportOrgLearnRankData(StatisticOrgLearnRankQuery queryDTO) {
        projectStatisticService.exportOrgLearnRankData(queryDTO);
        return Result.success();
    }

    @GetMapping("/projectJoinUserData")
    @Operation(operationId = "getProjectJoinUserData", summary = "获取学习项目参加人员分页数据", description = "获取学习项目参加人员分页数据")
    public Result<PageInfo<ProjectJoinUserPageDTO>>getProjectJoinUserData(@ParameterObject @Valid ProjectJoinUserQuery query){
        if (StringUtils.isBlank(query.getBizType())){
            query.setBizType(ProjectAnalysisBizTypeEnum.BY_PROJECT.getValue());
        }
        if (Objects.isNull(query.getOnlyCurrentOrg())){
            query.setOnlyCurrentOrg(GeneralJudgeEnum.CONFIRM.getValue());
        }
        return Result.success(projectStatisticService.getProjectJoinUserData(query));
    }

    @PostMapping("/exportProjectJoinUserData")
    @Operation(operationId = "exportProjectJoinUserData", summary = "导出学习项目参加人员分页数据", description = "导出学习项目参加人员分页数据")
    public Result<ExportResultDTO> exportProjectJoinUserData(@RequestBody @Valid ProjectJoinUserQuery query){
        projectStatisticService.exportProjectJoinUserData(query);
        return Result.success();
    }

    @GetMapping("/stateAnalysisTime/{projectId}")
    @Operation(operationId = "stateAnalysisTime", summary = "项目情况统计时间", description = "项目情况统计时间")
    public Result<String> stateAnalysisTime(@PathVariable("projectId") String projectId) {
        String time = "";
        Date statisticTime = projectStatisticByOrgCollectService.getProjectStatisticTime(projectId);
        if (Objects.nonNull(statisticTime)){
            time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(statisticTime);
        }
        return Result.success(time);
    }

    @GetMapping("/stateAnalysisByProject")
    @Operation(operationId = "stateAnalysisByProject", summary = "项目情况统计-按项目", description = "项目情况统计-按项目")
    public Result<PageInfo<ProjectAnalysisByProjectDTO>> stateAnalysisByProject(
        @ParameterObject ProjectAnalysisByProjectQuery query) throws ExecutionException, InterruptedException {
        return Result.success(projectStatisticService.stateAnalysisByProject(query));
    }

    @PostMapping("/exportAnalysisByProject")
    @Operation(operationId = "exportAnalysisByProject", summary = "导出项目情况统计-按项目", description = "导出项目情况统计-按项目")
    public Result<ExportResultDTO> exportAnalysisByProject(
        @RequestBody @Valid ProjectAnalysisByProjectQuery query) {
        projectStatisticService.exportAnalysisByProject(query);
        return Result.success();
    }

    @GetMapping("/stateAnalysisByOrg")
    @Operation(operationId = "stateAnalysisByOrg", summary = "项目情况统计-按部门", description = "项目情况统计-按部门")
    public Result<PageInfo<ProjectAnalysisByOrgDTO>> stateAnalysisByOrg(
        @ParameterObject ProjectAnalysisByOrgQuery query) {
        return Result.success(projectStatisticService.stateAnalysisByOrg(query));
    }

    @PostMapping("/exportAnalysisByOrg")
    @Operation(operationId = "exportAnalysisByOrg", summary = "导出项目情况统计-按部门", description = "导出项目情况统计-按部门")
    public Result<ExportResultDTO> exportAnalysisByOrg(
        @RequestBody @Valid ProjectAnalysisByOrgQuery query) {
        projectStatisticService.exportAnalysisByOrg(query);
        return Result.success();
    }

    @GetMapping("/stateAnalysisByUser")
    @Operation(operationId = "stateAnalysisByUser", summary = "项目情况统计-按用户", description = "项目情况统计-按用户")
    public Result<PageInfo<ProjectAnalysisByUserDTO>> stateAnalysisByUser(
        @ParameterObject ProjectAnalysisByUserQuery query) {
        return Result.success(projectStatisticService.stateAnalysisByUser(query));
    }

    @PostMapping("/exportAnalysisByUser")
    @Operation(operationId = "exportAnalysisByUser", summary = "导出项目情况统计-按用户", description = "导出项目情况统计-按用户")
    public Result<ExportResultDTO> exportAnalysisByUser(
        @RequestBody @Valid ProjectAnalysisByUserQuery query) {
        projectStatisticService.exportAnalysisByUser(query);
        return Result.success();
    }

    @GetMapping("/stateAnalysisByType")
    @Operation(operationId = "stateAnalysisByType", summary = "项目情况统计-按类型", description = "项目情况统计-按类型")
    public Result<PageInfo<ProjectAnalysisByTypeDTO>> stateAnalysisByType(
        @ParameterObject ProjectAnalysisByTypeQuery query) {
        return Result.success(projectStatisticService.stateAnalysisByType(query));
    }

    @PostMapping("/exportAnalysisByType")
    @Operation(operationId = "exportAnalysisByType", summary = "导出项目情况统计-按类型", description = "导出项目情况统计-按类型")
    public Result<ExportResultDTO> exportAnalysisByType(
        @RequestBody @Valid ProjectAnalysisByTypeQuery query) {
        projectStatisticService.exportAnalysisByType(query);
        return Result.success();
    }

    @GetMapping("/getAnalysisByTypeUser")
    @Operation(operationId = "getAnalysisByTypeUser", summary = "获取项目情况统计-按类型-人员数据", description = "获取项目情况统计-按类型-人员数据")
    public Result<PageInfo<AnalysisByTypeUserDTO>>getAnalysisByTypeUser(@ParameterObject @Valid AnalysisByTypeUserQuery query){
        return Result.success(projectStatisticService.getAnalysisByTypeUser(query));
    }

    @PostMapping("/exportAnalysisByTypeUser")
    @Operation(operationId = "exportAnalysisByTypeUser", summary = "导出项目情况统计-按类型-人员数据", description = "导出项目情况统计-按类型-人员数据")
    public Result<ExportResultDTO>exportAnalysisByTypeUser(@RequestBody @Valid AnalysisByTypeUserQuery query){
        projectStatisticService.exportAnalysisByTypeUser(query);
        return Result.success();
    }



    @Operation(operationId = "projectStatisticByOrg", summary = "按组织-统计指定资源接口", description = "按组织-统计指定资源接口")
    @PostMapping("/projectStatisticByOrg")
    public Result<Void> projectStatisticByOrg(@RequestBody ProjectStatisticByOrgDTO dto){
        if (Objects.isNull(dto) || StringUtils.isBlank(dto.getBacthId()) || CollectionUtils.isEmpty(dto.getResourceIds())){
            log.info(">>>>>>>>>>非法数据,结束");
            return Result.success();
        }
        projectStatisticService.projectStatisticByOrg(dto.getBacthId(), dto.getResourceIds());
        return Result.success();
    }

    @Operation(operationId = "projectBackupResourceDataByType", summary = "学习项目备份数据", description = "学习项目备份数据")
    @PostMapping("/projectBackupResourceDataByType")
    public Result<Void> projectBackupResourceDataByType(String type){
        projectStatisticService.projectBackupResourceDataByType(type);
        return Result.success();
    }

    @Operation(operationId = "makeProjectHoldMonthStaticDataReady", summary = "学习项目举办统计准备数据", description = "学习项目举办统计准备数据")
    @PostMapping("/makeProjectHoldMonthStaticDataReady")
    public Result<Void> makeProjectHoldMonthStaticDataReady(){
        holdStatisticService.makeProjectHoldMonthStaticDataReady();
        return Result.success();
    }

    @GetMapping("/getProjectStudentNumById")
    @Operation(operationId = "getProjectStudentNumById", summary = "学习项目学习人数统计", description = "学习项目学习人数统计")
    public Result<Long> getProjectStudentNumById(
        @Parameter(description = "学习项目id") @Length(max = 36, message = "id长度不能超过36") String projectId) {
        return Result.success(projectStatisticService.getProjectStudentNumById(projectId));
    }

}
