package com.wunding.learn.project.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import com.wunding.learn.project.service.admin.dto.ApplyUserStaticDTO;
import com.wunding.learn.project.service.client.dto.AgentApplyDetailDTO;
import com.wunding.learn.project.service.client.dto.ApplyApiInfoDTO;
import com.wunding.learn.project.service.client.dto.UserApplyInfoDTO;
import com.wunding.learn.project.service.client.query.AgentApplyDetailPageQuery;
import com.wunding.learn.project.service.model.Apply;
import java.util.List;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 培训报名表 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">cdl</a>
 * @since 2022-08-02
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")
})
public interface ProjectApplyMapper extends BaseMapper<Apply> {

    /**
     * 通过项目id列表查询项目报名列表
     *
     * @param projectIds 项目id列表
     * @return {@link List}<{@link Apply}>
     */
    List<Apply> getByProjectIdList(@Param("projectIds") List<String> projectIds);

    /**
     * 报名用户面板数据
     *
     * @param id
     * @return
     */
    ApplyUserStaticDTO getApplyUserStatic(@Param("id") String id);

    /**
     * 根据项目id获取用户报名信息
     *
     * @param projectId
     * @param userId
     * @return
     */
    UserApplyInfoDTO getUserApplyInfoByProId(@Param("projectId") String projectId, @Param("userId") String userId);

    /**
     * 根据报名id检查报名状态
     * @param applyIds
     * @return
     */
    List<ApplyApiInfoDTO> applyApiInfoByIds(@Param("applyIds")List<String> applyIds);
    
    /**
     * 获取代报名列表
     * @param query
     * @return
     */
    List<AgentApplyDetailDTO> selectAgentApplyPage(AgentApplyDetailPageQuery query);
    
    /**
     * 获取我的培训代理数量
     * @param userId
     * @return
     */
    Long getAgentApplyStat(String userId);
}
