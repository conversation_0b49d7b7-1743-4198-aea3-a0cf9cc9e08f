package com.wunding.learn.project.service.admin.query;

import com.wunding.learn.common.bean.BasePageQuery;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import lombok.Data;

/**
 * 用户学习项目课时详情查询参数
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Data
public class UserProjectCoursewareStudyDetailQuery extends BasePageQuery implements Serializable {


    private static final long serialVersionUID = 5554569620009110909L;
    /**
     * 学习项目ID
     */
    @Parameter(description = "学习项目ID")
    @NotBlank(message = "学习项目ID不可为空")
    private String projectId;

    /**
     * 用户ID
     */
    @Parameter(description = "用户ID")
    @NotBlank(message = "用户ID不可为空")
    private String userId;
}
