package com.wunding.learn.project.service.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;


/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("project_task_form_files")
@ToString
@Schema(name = "TaskFormFiles对象", description = "辅导记录文件表")
public class TaskFormFiles implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;


    /**
     * 任务记录主键
     */
    @Schema(description = "辅导任务记录id")
    @TableField("detail_id")
    private String detailId;
    
    @Schema(description = "源文件id")
    @TableField("source_id")
    private String sourceId;
    
    
    @Schema(description = "文件媒体类型")
    @TableField("mime")
    private String mime;
    
    @Schema(description = "1-转码中 2-转码成功 3-转码失败 4-排队中")
    @TableField("transform_status")
    private Integer transformStatus;
    
    
    @Schema(description = "课件类型  与courseware 表cwType保持一致")
    @TableField("cw_type")
    private String cwType;


    /**
     * 逻辑删除
     */
    @Schema(description = "逻辑删除")
    @TableField("is_del")
    @TableLogic(value = "0", delval = "1")
    private Integer isDel;


    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;


    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 更新人
     */
    @Schema(description = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;


    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;



}
