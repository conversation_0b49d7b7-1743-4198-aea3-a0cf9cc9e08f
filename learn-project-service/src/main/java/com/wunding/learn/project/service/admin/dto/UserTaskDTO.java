package com.wunding.learn.project.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户项目任务完成情况统计
 *
 * <AUTHOR>
 * @since 2025-06-14
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(name = "UserTaskDTO", description = "用户项目任务完成情况统计")
public class UserTaskDTO implements Serializable {


    private static final long serialVersionUID = -8908160996836325352L;

    /**
     * 序号
     */
    @Schema(description = "序号")
    private Integer no;

    /**
     * 项目阶段ID
     */
    @Schema(description = "项目阶段ID")
    private String phaseId;

    /**
     * 项目阶段名称
     */
    @Schema(description = "项目阶段名称")
    private String phaseName;


    /**
     * 任务主键
     */
    @Schema(description = "任务主键")
    private String id;

    /**
     * 任务类型
     */
    @Schema(description = "任务类型 course课程 exam考试 exercise练习 survey调研 live直播 train培训班 project项目 apply报名 sign签到 form表单 ")
    private String taskType;

    /**
     * 任务名称
     */
    @Schema(description = "任务名称")
    private String taskName;

    /**
     * 课程任务是否必修(0 - 选修  1 - 必修)
     */
    @Schema(description = "课程任务是否必修(0 - 选修  1 - 必修)")
    private Integer isRequired;

    /**
     * 任务关联内容（关联资源）
     */
    @Schema(description = "任务关联内容", hidden = true)
    private String taskContent;
    /**
     * 用户参与的状态: 尚未进行 0, 正在进行 1, 已经完成 2 （1 这种中间状态，如果某项活动没有这种逻辑，则只存在0 和 2）
     */
    @Schema(description = "用户参与的状态: 尚未进行 0, 正在进行 1, 已经完成 2 （1 这种中间状态，如果某项活动没有这种逻辑，则只存在0 和 2）")
    private Integer userStatus;

    /**
     * 任务执行结果（仅针对 实操 和 考试显示对应的结果） 如：80分，通过
     */
    @Schema(description = "任务执行结果（仅针对 实操 和 考试显示对应的结果） 如：80分，通过")
    private String taskResult;


}
