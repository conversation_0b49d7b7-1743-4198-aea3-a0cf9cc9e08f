package com.wunding.learn.project.service.dao.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.common.aop.log.annotation.Log;
import com.wunding.learn.project.service.dao.SetDao;
import com.wunding.learn.project.service.mapper.SetMapper;
import com.wunding.learn.project.service.model.Set;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository("setDao")
public class SetDaoImpl extends ServiceImpl<SetMapper, Set> implements SetDao {

    @Override
    @Log(type = Log.Type.CREATE, targetId = "#set.id", targetName = "#set.name", targetType = Log.TargetType.SET)
    public void saveSet(Set set) {
        save(set);
    }

    @Override
    @Log(type = Log.Type.UPDATE, targetId = "#set.id", targetName = "#set.name", targetType = Log.TargetType.SET)
    public void updateSet(Set set) {
        updateById(set);
    }

    @Override
    @Log(type = Log.Type.DELETE, targetId = "#set.id", targetName = "#set.name", targetType = Log.TargetType.SET)
    public void delSet(Set set) {
        removeById(set);
    }
}
