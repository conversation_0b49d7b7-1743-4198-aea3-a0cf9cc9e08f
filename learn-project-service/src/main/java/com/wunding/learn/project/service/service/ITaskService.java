package com.wunding.learn.project.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.dto.ImportExcelDTO;
import com.wunding.learn.common.dto.ImportResultDTO;
import com.wunding.learn.common.dto.LearningCalendarScheduleQuery;
import com.wunding.learn.common.dto.LearningCalendarTaskDTO;
import com.wunding.learn.common.dto.LearningCalendarTaskQuery;
import com.wunding.learn.common.dto.PublishDTO;
import com.wunding.learn.common.dto.TaskAppResourceDTO;
import com.wunding.learn.project.api.dto.UserAbilityPracticalListDTO;
import com.wunding.learn.project.api.query.AbilityPracticalListQuery;
import com.wunding.learn.project.service.admin.dto.PreFormSaveDTO;
import com.wunding.learn.project.service.admin.dto.ProTaskDTO;
import com.wunding.learn.project.service.admin.dto.ProjectTaskManagerDTO;
import com.wunding.learn.project.service.admin.dto.ProjectTaskManagerQuoteSaveDTO;
import com.wunding.learn.project.service.admin.dto.ProjectTaskManagerSaveDTO;
import com.wunding.learn.project.service.admin.dto.ProjectTaskManagerUpdateDTO;
import com.wunding.learn.project.service.admin.dto.PublishTaskDTO;
import com.wunding.learn.project.service.admin.dto.RemindersDTO;
import com.wunding.learn.project.service.admin.dto.ReplenishDTO;
import com.wunding.learn.project.service.admin.dto.SaveTaskContentDTO;
import com.wunding.learn.project.service.admin.dto.TaskEchoDTO;
import com.wunding.learn.project.service.admin.dto.TaskFormTemplatePageDTO;
import com.wunding.learn.project.service.admin.dto.TaskFormTemplatePageSaveDTO;
import com.wunding.learn.project.service.admin.dto.TaskManagerDTO;
import com.wunding.learn.project.service.admin.dto.TaskPreFormPageDTO;
import com.wunding.learn.project.service.admin.dto.UserRemindersDTO;
import com.wunding.learn.project.service.admin.dto.UserStatisticProjectCompletionDTO;
import com.wunding.learn.project.service.admin.dto.UserTaskDTO;
import com.wunding.learn.project.service.admin.dto.faceproject.ScheduleTaskInfo;
import com.wunding.learn.project.service.admin.dto.faceproject.TaskGroupByScheduleIdDTO;
import com.wunding.learn.project.service.admin.query.ProjectTaskManagerQuery;
import com.wunding.learn.project.service.admin.query.TaskFormTemplatePageQuery;
import com.wunding.learn.project.service.admin.query.TaskPreFormPageQuery;
import com.wunding.learn.project.service.admin.query.TaskRemindersQuery;
import com.wunding.learn.project.service.admin.query.UserTaskListQuery;
import com.wunding.learn.project.service.admin.query.faceproject.ScheduleStatQuery;
import com.wunding.learn.project.service.client.dto.ProjectTaskBaseDTO;
import com.wunding.learn.project.service.client.dto.ProjectTaskListDTO;
import com.wunding.learn.project.service.client.dto.ProjectTaskSaveDTO;
import com.wunding.learn.project.service.client.dto.ProjectTaskUpdateDTO;
import com.wunding.learn.project.service.client.dto.TaskDTO;
import com.wunding.learn.project.service.client.dto.lecturerworkbench.ProjectTaskInfoDTO;
import com.wunding.learn.project.service.client.query.ProjectTaskListQuery;
import com.wunding.learn.project.service.client.query.ProjectTaskQuery;
import com.wunding.learn.project.service.client.query.lecturerworkbench.TaskOrAppListQuery;
import com.wunding.learn.project.service.model.Task;
import com.wunding.learn.project.service.model.TaskProgress;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.hibernate.validator.constraints.Length;
import org.springframework.scheduling.annotation.Async;

/**
 * <p> 项目任务表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">赖卓成</a>
 * @since 2022-07-19
 */
public interface ITaskService extends IService<Task> {

    ITaskService getBean();

    /**
     * 学习项目任务列表
     *
     * @param projectTaskManagerQueryDTO
     * @return
     */
    PageInfo<ProjectTaskManagerDTO> list(ProjectTaskManagerQuery projectTaskManagerQueryDTO);

    /**
     * 引用创建学习项目任务
     *
     * @param projectTaskManagerQuoteSaveDTO
     */
    void quoteCreate(ProjectTaskManagerQuoteSaveDTO projectTaskManagerQuoteSaveDTO);

    /**
     * 更新学习项目
     *
     * @param projectTaskManagerUpdateDTO
     */
    void update(ProjectTaskManagerUpdateDTO projectTaskManagerUpdateDTO);

    /**
     * 发布/取消发布学习项目
     *
     * @param publishDTO
     */
    void publish(PublishDTO publishDTO);

    /**
     * 删除学习项目
     *
     * @param ids
     */
    void remove(String ids);

    /**
     * 直接创建学习项目任务
     *
     * @param projectTaskManagerSaveDTO
     * @return <{@link TaskEchoDTO}>
     */
    TaskEchoDTO create(ProjectTaskManagerSaveDTO projectTaskManagerSaveDTO);

    /**
     * 该学习项目下所有已发布的任务
     *
     * @param projectId 学习项目id
     * @param taskId    任务id
     * @return {@link List}<{@link TaskDTO}>
     */
    List<TaskDTO> publishTask(String projectId, String taskId);

    /**
     * 任务补派
     *
     * @param replenishDTO
     */
    void replenish(ReplenishDTO replenishDTO);

    /**
     * 学习项目催办列表
     *
     * @param taskRemindersQueryDTO 催办查询dto
     * @return {@link PageInfo}<{@link RemindersDTO}>
     */
    PageInfo<RemindersDTO> remindersList(TaskRemindersQuery taskRemindersQueryDTO);

    /**
     * 学习项目任务催办
     *
     * @param taskId 任务id
     * @return {@link RemindersDTO}
     */
    void userReminders(UserRemindersDTO userRemindersDTO, String taskId);

    /**
     * 学习项目任务一键催办
     *
     * @param taskId
     */
    void allUserReminders(String taskId);

    /**
     * 客户端-项目任务列表
     *
     * @param id    id
     * @param query 查询对象
     * @return {@link ProjectTaskListDTO}
     */
    ProjectTaskListDTO getProjectTaskList(String id, ProjectTaskListQuery query);

    /**
     * 获取用户项目完成情况（此方法不对项目的下发范围进行校验）
     *
     * @param userId    用户id
     * @param projectId 项目id
     * @return {@link UserStatisticProjectCompletionDTO} 用户项目完成情况
     */
    UserStatisticProjectCompletionDTO getUserStatisticProjectCompletion(String userId, String projectId);

    PageInfo<TaskFormTemplatePageDTO> formList(TaskFormTemplatePageQuery taskFormTemplatePageQueryDTO);

    /**
     * 学习项目任务 表单类型添加辅导方案
     *
     * @param taskFormTemplatePageSaveDTO
     */
    void addForm(TaskFormTemplatePageSaveDTO taskFormTemplatePageSaveDTO);

    /**
     * 学习项目任务 表单类型删除辅导方案
     *
     * @param ids
     */
    void deleteForm(String ids);

    /**
     * 获取项目任务数
     *
     * @param id 项目id
     * @return {@link Integer}
     */
    Integer getProjectTaskCount(String id);

    /**
     * 学习项目任务 表单类型添加前置辅导方案
     *
     * @param preFormSaveDTO
     */
    void preForm(PreFormSaveDTO preFormSaveDTO);

    PageInfo<TaskPreFormPageDTO> preFormList(TaskPreFormPageQuery taskPreFormPageQueryDTO);

    /**
     * 获取一个学习项目任务
     *
     * @param id
     * @return {@link TaskManagerDTO}
     */
    TaskManagerDTO one(String id, Integer isPushLimit);

    /**
     * 已发布学习项目任务列表
     *
     * @param projectId
     * @return
     */
    List<PublishTaskDTO> publishList(String projectId);

    /**
     * 获取项目某个任务的同时向数据库记录实际开始时间
     *
     * @param id         id
     * @param taskId     任务id
     * @param activityId 活动id
     */
    void saveProjectRecordTime(String id, String taskId, String activityId);

    /**
     * 直接创建项目任务 保存项目任务内容关联
     *
     * @param saveTaskContentDTO
     */
    void saveTaskContent(SaveTaskContentDTO saveTaskContentDTO);

    /**
     * 获取学习项目下的课程的id
     *
     * @param projectId
     * @return
     */
    Collection<String> getProjectLecturerByProjectId(String projectId);

    /**
     * 查询学习日历的任务
     *
     * @param learningCalendarTaskQuery
     * @return
     */
    Collection<LearningCalendarTaskDTO> findLearningCalenderTaskList(
        LearningCalendarTaskQuery learningCalendarTaskQuery);

    /**
     * 查询学习日历的日程
     *
     * @param learningCalendarScheduleQuery
     * @return
     */
    Collection<LearningCalendarTaskDTO> findLearningCalenderScheduleList(
        LearningCalendarScheduleQuery learningCalendarScheduleQuery);

    /**
     * 获取项目任务进度
     *
     * @param proId  项目id
     * @param userId 用户id
     * @return {@link List}
     */
    List<TaskProgress> getTaskProgressByProIdAndUserId(String proId, String userId);

    /**
     * 同步任务下发范围
     *
     * @param projectId
     * @param taskList
     */
    void syncSaveViewLimit(String projectId, List<Task> taskList);

    /**
     * 获取
     *
     * @param projectId
     * @return
     */
    List<Task> getProjectTaskListByProId(String projectId);

    /**
     * 更新任务完成状态
     *
     * @param taskContent
     * @param userId
     */
    void updateTaskCompletedStatus(String taskContent, String userId);

    /**
     * 更新任务完成状态 (主要给辅导任务使用)
     *
     * @param taskList 任务列表
     * @param userId   用户id
     */
    void updateTaskCompletedStatus(List<Task> taskList, String userId);


    /**
     * 按类型与创建人搜索任务列表(直接添加)
     *
     * @param projectTaskQuery
     * @return
     */
    PageInfo<ProjectTaskBaseDTO> getResourceIdList(ProjectTaskQuery projectTaskQuery);


    /**
     * 讲师工作台创建学习项目任务
     *
     * @param projectTaskSaveDTO
     * @return <{@link TaskEchoDTO}>
     */
    TaskEchoDTO createLecturerWorkbenchTask(ProjectTaskSaveDTO projectTaskSaveDTO);

    /**
     * 讲师工作台编辑学习项目任务
     *
     * @param projectTaskUpdateDTO
     */
    void updateLecturerWorkbenchTask(ProjectTaskUpdateDTO projectTaskUpdateDTO);

    /**
     * 获取单个任务详情
     *
     * @param taskId
     * @return
     */
    ProjectTaskBaseDTO getTaskInfo(String taskId);

    /**
     * 获取自建的任务列表
     *
     * @param taskOrAppListQuery
     * @return
     */
    PageInfo<ProjectTaskInfoDTO> getSelfBuildTaskList(TaskOrAppListQuery taskOrAppListQuery);

    void checkTaskTime(String taskId);

    Map<String, ProTaskDTO> getPorjectTaskCount(String trainId);

    /**
     * 获取用户完成状态
     *
     * @param task
     * @param userIdList
     * @return
     */
    Map<String, Integer> getUserFinish(Task task, List<String> userIdList);

    /**
     * 校验资源完成情况
     *
     * @param taskProgress
     * @param userResourceMap
     */
    void checkActivityFinish(TaskProgress taskProgress, Map<String, Integer> userResourceMap);

    /**
     * 获取用户参与的项目任务的实操信息
     *
     * @param query 查询对象
     * @return 实操信息
     */
    PageInfo<UserAbilityPracticalListDTO> getProjectTaskByUserIdList(AbilityPracticalListQuery query);

    /**
     * 获取用户参与的项目任务的监督信息
     *
     * @param query 查询对象
     * @return 监督信息
     */
    PageInfo<UserAbilityPracticalListDTO> getUserAbilitySuperviseProjectList(AbilityPracticalListQuery query);

    /**
     * 更新学员学习项目进度百分比
     *
     * @param proId
     */
    void updateUserProjectProgress(String proId);

    /**
     * 面授任务导入
     *
     * @param importExcelDTO
     * @return
     */
    ImportResultDTO faceProjectImportTask(ImportExcelDTO importExcelDTO);

    /**
     * 根据日程id获取日程任务map
     *
     * @param scheduleIds
     * @return
     */
    Map<String, List<Task>> getTaskMapByScheduleIds(Collection<String> scheduleIds);


    /**
     * 按日程id 和 用户id查询任务完成状态
     *
     * @param userIdList        用户id列表
     * @param scheduleQueryList 日程查询列表
     */
    List<ScheduleTaskInfo> getUserFinishByScheduleId(Collection<String> userIdList,
        Collection<String> scheduleQueryList, String proId);

    /**
     * 按计划id获取任务列表组
     *
     * @param query 查询
     * @return {@link List}<{@link TaskGroupByScheduleIdDTO}>
     */
    List<TaskGroupByScheduleIdDTO> getTaskListScheduleId(ScheduleStatQuery query);


    /**
     * 查询任务
     *
     * @param taskName
     * @param proId
     * @return
     */
    Task getByTaskNameAndProId(String taskName, String proId);

    /**
     * 获取计划任务列表
     *
     * @param proId 亲id
     * @return {@link List}<{@link TaskGroupByScheduleIdDTO}>
     */
    List<TaskGroupByScheduleIdDTO> getScheduleTaskList(String proId);

    /**
     * 获取学习项目的任务资源Map
     *
     * @param id
     * @return
     */
    HashMap<String, Map<Integer, List<TaskAppResourceDTO>>> getProjectTaskResourceMap(String id);

    /**
     * 面授项目任务完成状态更新
     *
     * @param taskId     任务id
     * @param resourceId 资源id
     * @param toolType   任务类型
     * @param userId     用户id
     */
    @Async(value = "commonTaskThreadPool")
    void asyncUpdateTaskStatus(String taskId, String resourceId, String toolType, String userId);

    /**
     * 根据学习项目id列表获取实操信息
     *
     * @param proIdList 学习项目id
     * @return {@link Set}
     */
    Set<String> getProjectPracticalTaskList(List<String> proIdList);

    /**
     * 该学习项目下所有已发布的任务最大的排序
     *
     * @param projectId
     * @param taskId
     * @return
     */
    Integer projectTaskMaxSort(@Length(max = 36, message = "id长度不能超过36") String projectId, String taskId);

    void replenishExecute(ReplenishDTO replenishDTO);

    /**
     * 获取学习项目任务
     *
     * @param taskId 任务id
     * @return {@link ProjectTaskBaseDTO}
     */
    ProjectTaskBaseDTO getProjectTaskById(String taskId);

    /**
     * 获取用户任务列表
     *
     * @param query 用户项目任务完成情况统计查询对象
     * @return {@link PageInfo}<{@link UserTaskDTO}>
     */
    PageInfo<UserTaskDTO> findUserTaskListByPage(UserTaskListQuery query);

    /**
     * 导出用户任务列表
     *
     * @param query 用户项目任务完成情况统计查询对象
     */
    @Async
    void exportUserTask(UserTaskListQuery query);
}
