package com.wunding.learn.project.service.service.impl;

import static com.wunding.learn.project.service.constant.ProjectConstant.TASK_IS_COMPLETED_NO;
import static com.wunding.learn.project.service.constant.ProjectConstant.TASK_IS_COMPLETED_YES;
import static com.wunding.learn.project.service.enums.ProjectTaskTypeEnum.COURSE;
import static com.wunding.learn.project.service.enums.ProjectTaskTypeEnum.EXAM;
import static com.wunding.learn.project.service.enums.ProjectTaskTypeEnum.EXERCISE;
import static com.wunding.learn.project.service.enums.ProjectTaskTypeEnum.FORM;
import static com.wunding.learn.project.service.enums.ProjectTaskTypeEnum.isForm;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.wunding.learn.common.category.model.Categorys;
import com.wunding.learn.common.category.service.ICategorysService;
import com.wunding.learn.common.constant.other.AvailableEnum;
import com.wunding.learn.common.constant.other.BaseErrorNoEnum;
import com.wunding.learn.common.constant.other.CommonConstants;
import com.wunding.learn.common.constant.other.DelEnum;
import com.wunding.learn.common.constant.other.ErrorNoEnum;
import com.wunding.learn.common.constant.other.PublishEnum;
import com.wunding.learn.common.constant.other.SignTypeEnum;
import com.wunding.learn.common.constant.project.ProjectErrorNoEnum;
import com.wunding.learn.common.constant.tenant.TenantRedisKeyConstant;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.dto.ImportExcelDTO;
import com.wunding.learn.common.dto.ImportResultDTO;
import com.wunding.learn.common.dto.LearningCalendarScheduleQuery;
import com.wunding.learn.common.dto.LearningCalendarTaskDTO;
import com.wunding.learn.common.dto.LearningCalendarTaskQuery;
import com.wunding.learn.common.dto.PublishDTO;
import com.wunding.learn.common.dto.ResourceRecordSyncDTO;
import com.wunding.learn.common.dto.TaskAppResourceDTO;
import com.wunding.learn.common.enums.MapBehaviorEnum;
import com.wunding.learn.common.enums.ResourceTypeEnum;
import com.wunding.learn.common.enums.course.ParaTypeEnum;
import com.wunding.learn.common.enums.excitation.ExcitationEventEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.other.OperationEnum;
import com.wunding.learn.common.enums.other.PublishStatusEnum;
import com.wunding.learn.common.enums.project.ProjectAppType;
import com.wunding.learn.common.enums.push.PushType;
import com.wunding.learn.common.enums.viewlimit.ViewLimitTypeEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.i18n.util.I18nUtil;
import com.wunding.learn.common.mq.dto.ExcitationMQEventDTO;
import com.wunding.learn.common.mq.event.ResourceOperateEvent;
import com.wunding.learn.common.mq.event.ResourceRecordSyncEvent;
import com.wunding.learn.common.mq.event.excitation.ExcitationMQEvent;
import com.wunding.learn.common.mq.event.mapshape.MapOperationLearningFinishEvent;
import com.wunding.learn.common.mq.event.project.ProjectCompletedEvent;
import com.wunding.learn.common.mq.service.MqProducer;
import com.wunding.learn.common.query.ResourceUserQuery;
import com.wunding.learn.common.util.bean.BeanListUtils;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.date.DateUtil;
import com.wunding.learn.common.util.excel.ExcelCheckMessage;
import com.wunding.learn.common.util.http.WebUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.util.string.StringPool;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.common.viewlimit.constant.LimitTable;
import com.wunding.learn.common.viewlimit.dto.ViewLimitTypeDTO;
import com.wunding.learn.common.viewlimit.service.IResourceViewLimitService;
import com.wunding.learn.course.api.dto.ImportUserCourseRecordDTO;
import com.wunding.learn.course.api.dto.UserCourseRecordDTO;
import com.wunding.learn.course.api.service.CourseFeign;
import com.wunding.learn.evaluation.api.dto.EvalDTO;
import com.wunding.learn.evaluation.api.dto.UserEvalStatusInfoDTO;
import com.wunding.learn.evaluation.api.query.UserEvalStatusInfoQuery;
import com.wunding.learn.evaluation.api.service.EvaluationFeign;
import com.wunding.learn.exam.api.dto.ExamInfoDTO;
import com.wunding.learn.exam.api.dto.ExamTimeDTO;
import com.wunding.learn.exam.api.dto.ExamUserScoreDTO;
import com.wunding.learn.exam.api.dto.ImportUserExamRecordDTO;
import com.wunding.learn.exam.api.dto.ViewExamFeignDTO;
import com.wunding.learn.exam.api.service.AnswerRecordFeign;
import com.wunding.learn.exam.api.service.ExamFeign;
import com.wunding.learn.exam.api.service.ExerciseFeign;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.constant.ImageBizType;
import com.wunding.learn.file.api.dto.AbstractExportDataDTO;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.file.api.dto.ImportDataDTO;
import com.wunding.learn.file.api.service.FileFeign;
import com.wunding.learn.file.api.service.ImportDataFeign;
import com.wunding.learn.lecturer.api.dto.EditLecturerExaminationDTO;
import com.wunding.learn.lecturer.api.dto.LecturerDTO;
import com.wunding.learn.lecturer.api.dto.WorkTimeDTO;
import com.wunding.learn.lecturer.api.query.WorkTimeQuery;
import com.wunding.learn.lecturer.api.service.LecturerExaminationFeign;
import com.wunding.learn.lecturer.api.service.LecturerFeign;
import com.wunding.learn.lecturer.api.service.NotWorkdayFeign;
import com.wunding.learn.live.api.dto.LiveDTO;
import com.wunding.learn.live.api.service.LiveFeign;
import com.wunding.learn.market.api.dto.ProjectSignTaskDTO;
import com.wunding.learn.market.api.dto.SignPageDTO;
import com.wunding.learn.market.api.dto.SignTimeUpdateDTO;
import com.wunding.learn.market.api.dto.TaskSignEditDTO;
import com.wunding.learn.market.api.query.ProjectSignTaskQuery;
import com.wunding.learn.market.api.query.SignListQuery;
import com.wunding.learn.market.api.service.SignFeign;
import com.wunding.learn.project.api.dto.UserAbilityPracticalListDTO;
import com.wunding.learn.project.api.query.AbilityPracticalListQuery;
import com.wunding.learn.project.service.admin.dto.PracticalOperationResultDTO;
import com.wunding.learn.project.service.admin.dto.PreFormSaveDTO;
import com.wunding.learn.project.service.admin.dto.ProTaskDTO;
import com.wunding.learn.project.service.admin.dto.ProjectTaskManagerDTO;
import com.wunding.learn.project.service.admin.dto.ProjectTaskManagerQuoteSaveDTO;
import com.wunding.learn.project.service.admin.dto.ProjectTaskManagerSaveDTO;
import com.wunding.learn.project.service.admin.dto.ProjectTaskManagerUpdateDTO;
import com.wunding.learn.project.service.admin.dto.PublishTaskDTO;
import com.wunding.learn.project.service.admin.dto.RemindersDTO;
import com.wunding.learn.project.service.admin.dto.ReplenishDTO;
import com.wunding.learn.project.service.admin.dto.SaveTaskContentDTO;
import com.wunding.learn.project.service.admin.dto.TaskEchoDTO;
import com.wunding.learn.project.service.admin.dto.TaskFormTemplatePageDTO;
import com.wunding.learn.project.service.admin.dto.TaskFormTemplatePageSaveDTO;
import com.wunding.learn.project.service.admin.dto.TaskManagerDTO;
import com.wunding.learn.project.service.admin.dto.TaskPreFormPageDTO;
import com.wunding.learn.project.service.admin.dto.UserRemindersDTO;
import com.wunding.learn.project.service.admin.dto.UserStatisticProjectCompletionDTO;
import com.wunding.learn.project.service.admin.dto.UserTaskDTO;
import com.wunding.learn.project.service.admin.dto.faceproject.ImportTaskDTO;
import com.wunding.learn.project.service.admin.dto.faceproject.ScheduleTaskInfo;
import com.wunding.learn.project.service.admin.dto.faceproject.TaskGroupByScheduleIdDTO;
import com.wunding.learn.project.service.admin.query.ProjectTaskManagerQuery;
import com.wunding.learn.project.service.admin.query.TaskFormTemplatePageQuery;
import com.wunding.learn.project.service.admin.query.TaskPreFormPageQuery;
import com.wunding.learn.project.service.admin.query.TaskRemindersQuery;
import com.wunding.learn.project.service.admin.query.UserTaskListQuery;
import com.wunding.learn.project.service.admin.query.faceproject.ScheduleStatQuery;
import com.wunding.learn.project.service.biz.impl.ProjectBizImpl;
import com.wunding.learn.project.service.client.dto.LearnProjectTask;
import com.wunding.learn.project.service.client.dto.ProjectTaskBaseDTO;
import com.wunding.learn.project.service.client.dto.ProjectTaskListDTO;
import com.wunding.learn.project.service.client.dto.ProjectTaskSaveDTO;
import com.wunding.learn.project.service.client.dto.ProjectTaskUpdateDTO;
import com.wunding.learn.project.service.client.dto.StageDTO;
import com.wunding.learn.project.service.client.dto.TaskDTO;
import com.wunding.learn.project.service.client.dto.lecturerworkbench.ProjectTaskInfoDTO;
import com.wunding.learn.project.service.client.query.ProjectTaskListQuery;
import com.wunding.learn.project.service.client.query.ProjectTaskQuery;
import com.wunding.learn.project.service.client.query.lecturerworkbench.TaskOrAppListQuery;
import com.wunding.learn.project.service.component.FormTaskViewLimitComponent;
import com.wunding.learn.project.service.component.ProjectViewLimitComponent;
import com.wunding.learn.project.service.constant.ProjectConstant;
import com.wunding.learn.project.service.dao.TaskContentDao;
import com.wunding.learn.project.service.dao.TaskDao;
import com.wunding.learn.project.service.enums.ProjectTaskTypeEnum;
import com.wunding.learn.project.service.mapper.FormTemplateColumnMapper;
import com.wunding.learn.project.service.mapper.ProgressMapper;
import com.wunding.learn.project.service.mapper.ProjectFormTemplateMapper;
import com.wunding.learn.project.service.mapper.ProjectMapper;
import com.wunding.learn.project.service.mapper.ProjectTaskMapper;
import com.wunding.learn.project.service.mapper.SetMapper;
import com.wunding.learn.project.service.mapper.TaskContentMapper;
import com.wunding.learn.project.service.mapper.TaskProgressMapper;
import com.wunding.learn.project.service.mapper.TaskRecordDetailMapper;
import com.wunding.learn.project.service.model.App;
import com.wunding.learn.project.service.model.FormTemplateColumn;
import com.wunding.learn.project.service.model.Phase;
import com.wunding.learn.project.service.model.Progress;
import com.wunding.learn.project.service.model.Project;
import com.wunding.learn.project.service.model.ProjectUserStudyCondition;
import com.wunding.learn.project.service.model.ReplenishTaskLog;
import com.wunding.learn.project.service.model.ScheduleTool;
import com.wunding.learn.project.service.model.Task;
import com.wunding.learn.project.service.model.TaskContent;
import com.wunding.learn.project.service.model.TaskProgress;
import com.wunding.learn.project.service.model.TaskRecordDetail;
import com.wunding.learn.project.service.query.ProjectJoinQuery;
import com.wunding.learn.project.service.service.IAppService;
import com.wunding.learn.project.service.service.ICompletionUserService;
import com.wunding.learn.project.service.service.IFormCycleRecordService;
import com.wunding.learn.project.service.service.IPhaseService;
import com.wunding.learn.project.service.service.IPracticalOperationService;
import com.wunding.learn.project.service.service.IPracticalOperationUserService;
import com.wunding.learn.project.service.service.IProgressService;
import com.wunding.learn.project.service.service.IProjectService;
import com.wunding.learn.project.service.service.IProjectUserStudyConditionService;
import com.wunding.learn.project.service.service.IQuickProjectTaskSortService;
import com.wunding.learn.project.service.service.IReplenishTaskLogService;
import com.wunding.learn.project.service.service.IScheduleService;
import com.wunding.learn.project.service.service.IScheduleToolService;
import com.wunding.learn.project.service.service.ITaskExamService;
import com.wunding.learn.project.service.service.ITaskProgressService;
import com.wunding.learn.project.service.service.ITaskRecordDetailService;
import com.wunding.learn.project.service.service.ITaskService;
import com.wunding.learn.project.service.service.ProjectDao;
import com.wunding.learn.project.service.strategy.BaseStrategy;
import com.wunding.learn.project.service.template.TaskExcelTemplate;
import com.wunding.learn.push.api.component.PushComponent;
import com.wunding.learn.push.api.dto.PushAttributeDTO;
import com.wunding.learn.push.api.dto.PushNoticeSetDTO;
import com.wunding.learn.push.api.dto.PushResourceDTO;
import com.wunding.learn.push.api.dto.SendPushDTO;
import com.wunding.learn.push.api.enums.RemindPushEnum;
import com.wunding.learn.push.api.service.PushFeign;
import com.wunding.learn.survey.api.service.SurveyFeign;
import com.wunding.learn.user.api.dto.OrgDTO;
import com.wunding.learn.user.api.dto.OrgShowDTO;
import com.wunding.learn.user.api.dto.ParaDTO;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.dto.UserNameDTO;
import com.wunding.learn.user.api.dto.viewlimit.ViewLimitBaseInfoDTO;
import com.wunding.learn.user.api.service.OrgFeign;
import com.wunding.learn.user.api.service.ParaFeign;
import com.wunding.learn.user.api.service.UserFeign;
import com.wunding.learn.user.api.service.ViewLimitFeign;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * <p> 项目任务表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">赖卓成</a>
 * @since 2022-07-19
 */
@Slf4j
@Service("projectTaskService")
public class TaskServiceImpl extends ServiceImpl<ProjectTaskMapper, Task> implements ITaskService {

    private static final String TASK_TYPE = "taskType";
    private static final String USER_STATUS = "userStatus";
    private static final String TASK_NAME = "taskName";
    private static final String IS_REQUIRED = "isRequired";

    /**
     * 评估任务类型
     */
    private static final String TASK_TYPE_EVALUATION = "evaluation";

    // 任务类型 course课程 exam考试 exercise练习 survey调研 live直播 train培训班 project项目 apply报名 sign签到 form表单


    // 课程引用类型
    private static final String TASK_TYPE_COURSE = "course";
    private static final String TASK_TYPE_EXAM = "exam";
    private static final String TASK_TYPE_EXERCISE = "exercise";
    private static final String TASK_TYPE_SURVEY = "survey";
    private static final String TASK_TYPE_LIVE = "live";
    private static final String TASK_TYPE_TRAIN = "train";
    private static final String TASK_TYPE_PROJECT = "project";
    private static final String TASK_TYPE_APPLY = "apply";
    private static final String TASK_TYPE_SIGN = "sign";
    private static final String TASK_TYPE_FORM = "form";
    private static final String TASK_TYPE_PRACTICAL_OPERATION = "practicalOperation";

    @Resource
    private UserFeign userFeign;
    @Resource
    private SignFeign signFeign;
    @Resource
    private IPhaseService phaseService;
    @Resource
    private ExamFeign examFeign;
    @Resource
    private SurveyFeign surveyFeign;
    @Resource
    private CourseFeign courseFeign;
    @Resource
    private AnswerRecordFeign answerRecordFeign;
    @Resource
    private ExerciseFeign exerciseFeign;
    @Resource
    private ProgressMapper progressMapper;
    @Resource
    @Lazy
    private ProjectDao projectDao;
    @Resource
    private TaskProgressMapper taskProgressMapper;
    @Resource
    private ITaskProgressService taskProgressService;
    @Resource
    private ProjectMapper projectMapper;
    @Resource
    private TaskContentMapper taskContentMapper;
    @Resource
    private FormTemplateColumnMapper formTemplateColumnMapper;
    @Resource
    private TaskRecordDetailMapper taskRecordDetailMapper;
    @Resource
    private OrgFeign orgFeign;
    @Resource
    private ProjectFormTemplateMapper projectFormTemplateMapper;
    @Resource
    private SetMapper setMapper;
    @Resource
    private ICategorysService categorysService;
    @Resource
    private LiveFeign liveFeign;
    @Resource
    private ProjectViewLimitComponent projectViewLimitComponent;
    @Resource
    private FormTaskViewLimitComponent formTaskViewLimitComponent;
    @Resource
    @Lazy
    private IProgressService progressService;
    @Resource
    @Lazy
    private IAppService appService;
    @Resource(name = "commonTaskThreadPool")
    private Executor asyncThreadPool;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private EvaluationFeign evaluationFeign;
    @Resource
    private PushComponent pushComponent;
    @Resource
    private IQuickProjectTaskSortService quickProjectTaskSortService;
    @Resource
    private IResourceViewLimitService resourceViewLimitService;
    @Resource
    private LecturerFeign lecturerFeign;
    @Resource
    private LecturerExaminationFeign examinationFeign;
    @Resource
    private NotWorkdayFeign notWorkdayFeign;
    @Resource
    private ParaFeign paraFeign;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private PushFeign pushFeign;
    @Resource
    private FileFeign fileFeign;
    @Resource
    private ITaskRecordDetailService taskRecordDetailService;
    @Resource
    @Lazy
    private ITaskExamService taskExamService;
    @Resource(name = "taskDao")
    private TaskDao taskDao;
    @Resource(name = "taskContentDao")
    private TaskContentDao taskContentDao;
    @Resource
    private ImportDataFeign importDataFeign;
    @Resource
    private IScheduleService scheduleService;
    @Resource
    private IScheduleToolService scheduleToolService;
    @Resource
    @Lazy
    private IFormCycleRecordService formCycleRecordService;
    @Resource
    private IPracticalOperationService practicalOperationService;
    @Resource
    private IPracticalOperationUserService practicalOperationUserService;
    @Resource
    private IProjectUserStudyConditionService projectUserStudyConditionService;
    @Resource
    @Lazy
    private ICompletionUserService completionUserService;
    @Resource
    private IReplenishTaskLogService replenishTaskLogService;
    @Resource
    private ViewLimitFeign viewLimitFeign;
    @Resource
    private ExportComponent exportComponent;
    @Resource
    @Lazy
    private IProjectService projectService;

    @Override
    public ITaskService getBean() {
        return SpringUtil.getBean("projectTaskService", ITaskService.class);
    }

    @Override
    public PageInfo<ProjectTaskManagerDTO> list(ProjectTaskManagerQuery projectTaskManagerQueryDTO) {
        Project project = projectMapper.selectById(projectTaskManagerQueryDTO.getProjectId());
        if (project == null) {
            throw new BusinessException(ProjectErrorNoEnum.PROJECT_NOT_EXIST);
        }

        PageInfo<ProjectTaskManagerDTO> sqlPageInfo = PageMethod.startPage(projectTaskManagerQueryDTO.getPageNo(),
                projectTaskManagerQueryDTO.getPageSize())
            .doSelectPageInfo(() -> baseMapper.list(projectTaskManagerQueryDTO));

        List<ProjectTaskManagerDTO> projectTaskManagerDTOList = sqlPageInfo.getList();
        if (CollectionUtils.isEmpty(projectTaskManagerDTOList)) {
            return sqlPageInfo;
        }

        Set<String> ids = sqlPageInfo.getList().stream().map(ProjectTaskManagerDTO::getPublishBy)
            .filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
        Map<String, UserDTO> userNameMapByIds = new HashMap<>(ids.size());
        if (!CollectionUtils.isEmpty(ids)) {
            userNameMapByIds = userFeign.getUserNameMapByIds(ids);
        }

        Set<String> examIdList = projectTaskManagerDTOList.stream()
            .filter(projectTaskManagerDTO -> EXAM.getTaskType().equals(projectTaskManagerDTO.getTaskType()))
            .map(ProjectTaskManagerDTO::getTaskContent).collect(Collectors.toSet());

        Map<String, ViewExamFeignDTO> viewExamFeignDTOMap = examFeign.getExamInfoMapByExamIds(examIdList);

        Set<String> liveIdList = projectTaskManagerDTOList.stream().filter(
                projectTaskManagerDTO -> ProjectTaskTypeEnum.LIVE.getTaskType().equals(projectTaskManagerDTO.getTaskType()))
            .map(ProjectTaskManagerDTO::getTaskContent).collect(Collectors.toSet());
        Map<String, LiveDTO> viewLiveMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(liveIdList)) {
            viewLiveMap = liveFeign.getLiveByIdList(liveIdList);
        }

        for (ProjectTaskManagerDTO projectTaskManagerDTO : projectTaskManagerDTOList) {
            handleDTO(userNameMapByIds, viewExamFeignDTOMap, viewLiveMap, projectTaskManagerDTO);
        }

        return sqlPageInfo;
    }

    private void handleDTO(Map<String, UserDTO> userNameMapByIds, Map<String, ViewExamFeignDTO> viewExamFeignDTOMap,
        Map<String, LiveDTO> viewLiveMap, ProjectTaskManagerDTO projectTaskManagerDTO) {
        UserDTO userDTO = userNameMapByIds.get(projectTaskManagerDTO.getPublishBy());
        if (Objects.equals(projectTaskManagerDTO.getTaskStatus(), PublishEnum.PUBLISHED.getValue())
            && userDTO != null) {
            projectTaskManagerDTO.setPublishBy(userDTO.getFullName());
        }
        //名称
        if (!isForm(projectTaskManagerDTO.getTaskType())) {
            String resourceName = Objects.requireNonNull(
                    ProjectTaskTypeEnum.getStrategy(projectTaskManagerDTO.getTaskType()))
                .getResourceName(projectTaskManagerDTO.getTaskContent());
            projectTaskManagerDTO.setResourceName(resourceName);
        }
        //获取资源发布状态 和 资源删除状态
        if (isForm(projectTaskManagerDTO.getTaskType())) {
            projectTaskManagerDTO.setTaskType(ProjectTaskTypeEnum.FORM.getTaskType());
            projectTaskManagerDTO.setResourceStatus(projectTaskManagerDTO.getTaskStatus());
        } else {
            // 发布状态
            int resourceState = Objects.requireNonNull(
                    ProjectTaskTypeEnum.getStrategy(projectTaskManagerDTO.getTaskType()))
                .getResourcePubState(projectTaskManagerDTO.getTaskContent());
            projectTaskManagerDTO.setResourceStatus(resourceState);

            // 资源删除状态
            int resourceIsDel = Objects.requireNonNull(
                    ProjectTaskTypeEnum.getStrategy(projectTaskManagerDTO.getTaskType()))
                .getResourceIsDel(projectTaskManagerDTO.getTaskContent());
            projectTaskManagerDTO.setResourceIsDel(resourceIsDel);
        }

        //如果是考试，则需要返回考试来源
        if (EXAM.getTaskType().equals(projectTaskManagerDTO.getTaskType())) {
            String taskContent = projectTaskManagerDTO.getTaskContent();
            ViewExamFeignDTO examFeignDTO = viewExamFeignDTOMap.get(taskContent);
            if (examFeignDTO != null) {
                projectTaskManagerDTO.setSourceType(examFeignDTO.getSourceType());
                projectTaskManagerDTO.setCheckPaperMethod(examFeignDTO.getCheckPaperMethod());
                //考试的组卷状态
                projectTaskManagerDTO.setCompositionStatus(examFeignDTO.getCompositionStatus());
                projectTaskManagerDTO.setIsAutoPublish(examFeignDTO.getIsPublish());
                projectTaskManagerDTO.setCheckExam(examFeignDTO.getCheckExam());
            }
        }
        //如果是直播，则返回直播是否结束
        if (ProjectTaskTypeEnum.LIVE.getTaskType().equals(projectTaskManagerDTO.getTaskType())) {
            String taskContent = projectTaskManagerDTO.getTaskContent();
            LiveDTO liveDTO = viewLiveMap.get(taskContent);
            if (liveDTO != null) {
                projectTaskManagerDTO.setLiveIsEnd(liveDTO.getEndTime().compareTo(new Date()) < 0);
            }
        }

        if (COURSE.getTaskType().equals(projectTaskManagerDTO.getTaskType())) {
            buildCourseTaskAppInfo(projectTaskManagerDTO);
        }
    }

    private void buildCourseTaskAppInfo(ProjectTaskManagerDTO taskDTO) {
        LambdaQueryWrapper<App> wrapper = new LambdaQueryWrapper<>();
        // 增加课程任务考试id
        List<App> examApp = appService.list(
            wrapper.eq(App::getTaskId, taskDTO.getId()).eq(App::getResourceType, ProjectAppType.EXAM.getNo())
                .orderByDesc(App::getCreateTime));
        if (CollectionUtils.isNotEmpty(examApp)) {
            taskDTO.setExamId(examApp.get(0).getResourceId());
        }
        wrapper.clear();
        // 课程任务评估id
        List<App> evaluationApp = appService.list(
            wrapper.eq(App::getTaskId, taskDTO.getId()).eq(App::getResourceType, ProjectAppType.ASSESS.getNo())
                .orderByDesc(App::getCreateTime));
        if (CollectionUtils.isNotEmpty(evaluationApp)) {
            taskDTO.setEvaluationId(evaluationApp.get(0).getResourceId());
        }
        // 课程任务签到id、签退id
        List<TaskSignEditDTO> signList = signFeign.getTaskSignList(taskDTO.getId());
        if (CollectionUtils.isNotEmpty(signList)) {
            Map<Integer, List<TaskSignEditDTO>> listMap = signList.stream()
                .collect(Collectors.groupingBy(TaskSignEditDTO::getType));
            for (Map.Entry<Integer, List<TaskSignEditDTO>> entry : listMap.entrySet()) {
                Integer type = entry.getKey();
                if (Objects.equals(type, SignTypeEnum.SIGN_IN.getValue())) {
                    taskDTO.setSignInId(listMap.get(type).get(0).getId());
                }
                if (Objects.equals(type, SignTypeEnum.SIGN_OUT.getValue())) {
                    taskDTO.setSignOutId(listMap.get(type).get(0).getId());
                }
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    @SuppressWarnings("java:S3776")
    public void quoteCreate(ProjectTaskManagerQuoteSaveDTO quoteSaveDTO) {
        Project project = commonCheck(quoteSaveDTO.getProjectId(), null, quoteSaveDTO.getSort(),
            quoteSaveDTO.getStartTime(), quoteSaveDTO.getEndTime(), quoteSaveDTO.getProjectType());
        Task task = new Task();
        BeanUtils.copyProperties(quoteSaveDTO, task);
        task.setId(StringUtil.newId());
        task.setProId(quoteSaveDTO.getProjectId());
        task.setPredecessors(quoteSaveDTO.getPreTaskId());
        task.setSort(quoteSaveDTO.getSort());
        task.setScore(quoteSaveDTO.getScore());
        task.setCreateType(ProjectConstant.CREATE_TYPE_QUOTE);
        task.setIsPublish(GeneralJudgeEnum.CONFIRM.getValue());
        task.setPublishBy(UserThreadContext.getUserId());
        task.setPublishTime(new Date());

        if (quoteSaveDTO.getTaskType().equals(TASK_TYPE_COURSE) && quoteSaveDTO.getIsIndependent() != null
            && quoteSaveDTO.getIsIndependent() == 1) {
            // 使用引用的课程直接复制创建一个新的课程，目的是课程在不同项目中单独学习
            String courseId = quoteSaveDTO.getTaskContent();
            String copyCourseId = courseFeign.copyCourseByQuotedCourse(courseId);
            log.info("copy course success, copy course id : {}", copyCourseId);
            // 保存完课程后，再更新taskContent
            // 是否要复制一个task，不复制直接更新就是task绑定的copyCourseId，那它就和源课程无关了；如果复制，则这个项目有两个任务，完全分离了，
            task.setTaskContent(copyCourseId);
        }
        //课程任务需要补派给所有用户
        if (Objects.equals(ProjectConstant.PROJECT_COURSE_TASK, project.getProjectType())){
            task.setTaskReplenishmentStatus(3);
        }
        taskDao.saveTask(task);
        // 面授，学习，周期，班级，课程任务 需要进行补派处理，其余不操作
        if (Objects.equals(ProjectConstant.PROJECT_TYPE_FACE, project.getProjectType())
            || Objects.equals(ProjectConstant.PROJECT_TYPE_DEFAULT, project.getProjectType())
        ) {
            ReplenishDTO replenishDTO = new ReplenishDTO().setId(task.getId());
            ReplenishDTO replenishDTO1 = new ReplenishDTO().setId(task.getId());
            // 任务更新完成后，进行任务补派
            if (Objects.nonNull(quoteSaveDTO.getTaskReplenishmentStatus())
                && quoteSaveDTO.getTaskReplenishmentStatus() == 3) {
                replenishDTO.setType(ProjectConstant.COMPLETED_REPLENISH);
                replenishDTO1.setType(ProjectConstant.UNDERWAY_REPLENISH);
                replenish(replenishDTO);
                replenish(replenishDTO1);
            } else if (Objects.nonNull(quoteSaveDTO.getTaskReplenishmentStatus())
                && quoteSaveDTO.getTaskReplenishmentStatus() == 1) {
                replenishDTO.setType(ProjectConstant.UNDERWAY_REPLENISH);
                replenish(replenishDTO);
            }
        }
        //课程任务需要补派给所有用户
        if (Objects.equals(ProjectConstant.PROJECT_COURSE_TASK, project.getProjectType())){
            ReplenishDTO replenishDTO2 = new ReplenishDTO().setId(task.getId());
            ReplenishDTO replenishDTO3 = new ReplenishDTO().setId(task.getId());
            replenishDTO2.setType(ProjectConstant.COMPLETED_REPLENISH);
            replenishDTO3.setType(ProjectConstant.UNDERWAY_REPLENISH);
            replenish(replenishDTO2);
            replenish(replenishDTO3);
        }

        quoteSaveDTO.setIsPublish(GeneralJudgeEnum.NEGATIVE.getValue());
        // 启用才给资源配置推送
        if (GeneralJudgeEnum.CONFIRM.getValue().equals(quoteSaveDTO.getIsPublish())) {
            sendPushFeign(quoteSaveDTO, 0);
        }

        // 面授项目任务逻辑处理
        if ((Objects.equals(ProjectConstant.PROJECT_TYPE_FACE, project.getProjectType())
            || Objects.equals(ProjectConstant.PROJECT_COURSE_TASK, project.getProjectType()))
            && StringUtils.isNotBlank(task.getScheduleId())) {
                ScheduleTool scheduleTool = new ScheduleTool().setProjectId(task.getProId())
                    .setPhaseId(task.getPhaseId()).setScheduleId(task.getScheduleId()).setToolType(task.getTaskType())
                    .setTaskId(task.getId()).setTaskName(task.getTaskName()).setResourceId(task.getTaskContent())
                    .setStartTime(task.getStartTime()).setEndTime(task.getEndTime());
                scheduleToolService.saveOrUpdateByTask(scheduleTool);
                // 工具添加的任务自动发布
                PublishDTO publishDTO = new PublishDTO().setIds(List.of(task.getId()))
                    .setIsPublish(PublishEnum.PUBLISHED.getValue());
                getBean().publish(publishDTO);
            }

    }

    private void sendPushFeign(ProjectTaskManagerQuoteSaveDTO projectTaskManagerQuoteSaveDTO, Integer operateState) {
        SendPushDTO sendPushDTO = new SendPushDTO();
        sendPushDTO.setLang(LocaleContextHolder.getLocale().getLanguage());
        sendPushDTO.setServerName(Objects.requireNonNull(WebUtil.getRequest()).getServerName());
        PushNoticeSetDTO pushNoticeSetDTO = projectTaskManagerQuoteSaveDTO.getPushNoticeSetDTO();
        sendPushDTO.setPushNoticeSetDTO(pushNoticeSetDTO);

        // 为空默认是0
        Integer isTrain = Optional.ofNullable(projectTaskManagerQuoteSaveDTO.getIsTrain()).orElse(0);

        PushResourceDTO pushResourceDTO = new PushResourceDTO().setIsTrain(isTrain).setOperateState(operateState)
            .setProgrammeId(projectTaskManagerQuoteSaveDTO.getProgrammeId());

        sendPushDTO.setPushResourceDTO(pushResourceDTO);

        PushAttributeDTO pushAttributeDTO = new PushAttributeDTO();

        boolean condition = isTrain != 0;

        if (condition) {
            // 非 0 证明是属于二级内容过来
            String[] name = pushResourceDTO.getResourceName().split("_");
            pushAttributeDTO.setName(name[0]);
            pushAttributeDTO.setSecondaryName(name[1]);
        } else {
            pushAttributeDTO.setName(pushNoticeSetDTO.getResourceName());
        }

        pushResourceDTO.setResourceId(pushNoticeSetDTO.getResourceId());
        pushResourceDTO.setResourceName(pushNoticeSetDTO.getResourceName());
        pushResourceDTO.setResourceType(pushNoticeSetDTO.getResourceType());
        pushAttributeDTO.setExistSecondary(condition);
        pushAttributeDTO.setStartTime(projectTaskManagerQuoteSaveDTO.getStartTime());
        pushAttributeDTO.setEndTime(projectTaskManagerQuoteSaveDTO.getEndTime());
        pushAttributeDTO.setIntro(projectTaskManagerQuoteSaveDTO.getTaskContent());
        pushAttributeDTO.setExamDuration(projectTaskManagerQuoteSaveDTO.getDuration());

        @SuppressWarnings("unchecked") String systemName = (String) redisTemplate.opsForHash()
            .get(TenantRedisKeyConstant.SYSTEM_NAME_KEY, UserThreadContext.getTenantId());
        pushAttributeDTO.setSystemName(systemName);

        sendPushDTO.setPushAttributeDTO(pushAttributeDTO);

        pushFeign.sendPush(sendPushDTO);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @SuppressWarnings("java:S3776")
    public void update(ProjectTaskManagerUpdateDTO updateDTO) {
        Task dbTask = Optional.ofNullable(getById(updateDTO.getId()))
            .orElseThrow(() -> new BusinessException(ProjectErrorNoEnum.PROJECT_TASK_NOT_EXIST));
        Task task = new Task();
        Project project = projectMapper.selectById(dbTask.getProId());
        BeanUtils.copyProperties(updateDTO, task);
        // fix bug 33533
        task.setIsPublish(dbTask.getIsPublish());
        task.setPredecessors(updateDTO.getPreTaskId());
        //课程任务需要补派给所有用户
        if (Objects.equals(ProjectConstant.PROJECT_COURSE_TASK, project.getProjectType())){
            task.setTaskReplenishmentStatus(3);
        }
        updateTask(task);

        // 面授，学习，周期，班级，课程任务 需要进行补派处理，其余不操作
        if (Objects.equals(ProjectConstant.PROJECT_TYPE_FACE, project.getProjectType())
            || Objects.equals(ProjectConstant.PROJECT_TYPE_DEFAULT, project.getProjectType())
        ) {
            ReplenishDTO replenishDTO = new ReplenishDTO().setId(task.getId());
            ReplenishDTO replenishDTO1 = new ReplenishDTO().setId(task.getId());
            // 任务更新完成后，进行任务补派
            if (Objects.nonNull(updateDTO.getTaskReplenishmentStatus())
                && updateDTO.getTaskReplenishmentStatus() == 3) {
                replenishDTO.setType(ProjectConstant.COMPLETED_REPLENISH);
                replenishDTO1.setType(ProjectConstant.UNDERWAY_REPLENISH);
                replenish(replenishDTO);
                replenish(replenishDTO1);
            } else if (Objects.nonNull(updateDTO.getTaskReplenishmentStatus())
                && updateDTO.getTaskReplenishmentStatus() == 1) {
                replenishDTO.setType(ProjectConstant.UNDERWAY_REPLENISH);
                replenish(replenishDTO);
            }
        }
        //课程任务需要补派给所有用户
        if (Objects.equals(ProjectConstant.PROJECT_COURSE_TASK, project.getProjectType())){
            ReplenishDTO replenishDTO2 = new ReplenishDTO().setId(task.getId());
            ReplenishDTO replenishDTO3 = new ReplenishDTO().setId(task.getId());
            replenishDTO2.setType(ProjectConstant.COMPLETED_REPLENISH);
            replenishDTO3.setType(ProjectConstant.UNDERWAY_REPLENISH);
            replenish(replenishDTO2);
            replenish(replenishDTO3);
        }
        if (Objects.nonNull(updateDTO.getFormTaskProgrammeId())) {
            // 如果是辅导任务，保存权限管理范围
            formTaskViewLimitComponent.handleNewViewLimit(updateDTO.getFormTaskProgrammeId(), task.getId());
        } else {
            // 传null，删除辅导辅导选导师的范围
            resourceViewLimitService.delResourceViewLimitByResourceId(task.getId());
        }
        PublishDTO publishDTO = new PublishDTO();
        publishDTO.setIsPublish(task.getIsPublish());
        publishDTO.setIds(Collections.singletonList(updateDTO.getId()));
        getBean().publish(publishDTO);
        // 日程任务处理
        if (StringUtils.isNotBlank(task.getScheduleId())) {
            ScheduleTool scheduleTool = new ScheduleTool().setProjectId(dbTask.getProId()).setPhaseId(task.getPhaseId())
                .setScheduleId(task.getScheduleId()).setToolType(dbTask.getTaskType()).setTaskId(task.getId())
                .setTaskName(task.getTaskName()).setResourceId(dbTask.getTaskContent())
                .setStartTime(task.getStartTime()).setEndTime(task.getEndTime());
            scheduleToolService.saveOrUpdateByTask(scheduleTool);
        } else {
            // 可能取消了日程，工具表要做相关删除
            ScheduleTool scheduleTool = new ScheduleTool().setProjectId(dbTask.getProId()).setTaskId(task.getId());
            scheduleToolService.removeByTask(scheduleTool);
        }
        // 与添加逻辑类似，推送通知
        if (GeneralJudgeEnum.CONFIRM.getValue().equals(updateDTO.getIsPublish()) && Optional.ofNullable(
            updateDTO.getPushNoticeSetDTO()).isPresent()) {
            sendUpdatePushFeign(updateDTO, 1);
        }
        if (null != updateDTO.getPracticalOperationDTO() && StringUtils.isNotEmpty(
            updateDTO.getPracticalOperationDTO().getId())) {
            // 更新实操信息
            practicalOperationService.updatePracticalOperation(updateDTO.getPracticalOperationDTO());
        }

        // 同步更新签到名称
        updateTaskSignEdit(updateDTO);

        // 只有时间变更才能发生变更
        if (Objects.equals(dbTask.getStartTime(), updateDTO.getStartTime()) && Objects.equals(dbTask.getEndTime(),
            updateDTO.getEndTime())) {
            return;
        }
        // 更新任务应用时间
        updateTaskAppTime(updateDTO);
        // 更新授课时间
        updateLecturerExcitation(updateDTO);
    }

    /**
     * 更新任
     *
     * @param taskUpdateDTO 任务更新dto
     */
    private void updateTaskSignEdit(ProjectTaskManagerUpdateDTO taskUpdateDTO) {
        List<TaskSignEditDTO> taskSignList = signFeign.getTaskSignList(taskUpdateDTO.getId());
        String taskName = taskUpdateDTO.getTaskName();
        taskSignList.forEach(dto -> {
            String signName = taskName + "-" + SignTypeEnum.getName(dto.getType());

            // 拼接任务名称超过了80个字符,则只用任务名称
            if (signName.length() > 80) {
                signName = taskName;
            }

            dto.setSignName(signName);
        });

        signFeign.updateTaskSign(taskSignList);
    }

    /**
     * 更新授课时间
     *
     * @param taskUpdateDTO
     */
    private void updateLecturerExcitation(ProjectTaskManagerUpdateDTO taskUpdateDTO) {
        if (!Objects.equals(taskUpdateDTO.getIsUpdateTaskAppTime(), GeneralJudgeEnum.CONFIRM.getValue())) {
            return;
        }

        // 计算授课时间及加班时间
        WorkTimeDTO workTime = notWorkdayFeign.getWorkTime(
            new WorkTimeQuery().setStartTime(taskUpdateDTO.getStartTime()).setEndTime(taskUpdateDTO.getEndTime()));

        // 取授课信息
        EditLecturerExaminationDTO examinationDTO = new EditLecturerExaminationDTO();
        examinationDTO.setTaskId(taskUpdateDTO.getId()).setBeginTime(taskUpdateDTO.getStartTime())
            .setEndTime(taskUpdateDTO.getEndTime()).setInstructionTime(new BigDecimal(workTime.getWorkMinutes()))
            .setExtraInstructionTime(new BigDecimal(workTime.getExtraWorkMinutes()));
        examinationFeign.updateExaminationByTaskId(examinationDTO);
    }

    /**
     * 更新任务应用时间
     *
     * @param taskUpdateDTO
     */
    private void updateTaskAppTime(ProjectTaskManagerUpdateDTO taskUpdateDTO) {
        if (!Objects.equals(taskUpdateDTO.getIsUpdateTaskAppTime(), GeneralJudgeEnum.CONFIRM.getValue())) {
            return;
        }
        Task task = Optional.ofNullable(getById(taskUpdateDTO.getId()))
            .orElseThrow(() -> new BusinessException(ProjectErrorNoEnum.PROJECT_TASK_NOT_EXIST));
        //更新任务签到时间
        updateTaskSignTime(taskUpdateDTO);
        //更新任务评估时间
        updateTaskEvaluationTime(taskUpdateDTO, task.getProId());
        // 更新考试时间
        List<App> examApps = appService.list(new LambdaQueryWrapper<App>().eq(App::getTaskId, task.getId())
            .eq(App::getResourceType, ProjectAppType.EXAM.getNo()));
        if (CollectionUtils.isNotEmpty(examApps)) {
            examApps.forEach(exam -> examFeign.updateExamTime(
                ExamTimeDTO.builder().id(exam.getResourceId()).startTime(task.getStartTime()).endTime(task.getEndTime())
                    .build()));
        }
    }

    /**
     * 更新任务签到时间
     *
     * @param taskUpdateDTO
     */
    private void updateTaskSignTime(ProjectTaskManagerUpdateDTO taskUpdateDTO) {

        List<TaskSignEditDTO> taskSignList = signFeign.getTaskSignList(taskUpdateDTO.getId());
        if (CollectionUtils.isEmpty(taskSignList)) {
            return;
        }
        // 获取签到/签退提前时间(分钟)
        String signInAdvanceTime = paraFeign.getParaValue(ParaTypeEnum.SIGN_START_TIME_ADVANCE_MINUTES.getCode());
        int advanceTime = StringUtils.isBlank(signInAdvanceTime) ? 5 : Integer.valueOf(signInAdvanceTime);
        String signOutAdvanceTime = paraFeign.getParaValue(ParaTypeEnum.SIGN_END_TIME_AFTER_MINUTES.getCode());
        int afterTime = StringUtils.isBlank(signOutAdvanceTime) ? 5 : Integer.valueOf(signOutAdvanceTime);
        List<SignTimeUpdateDTO> updateDTOS = taskSignList.stream().map(sign -> {
            if (Objects.equals(sign.getType(), SignTypeEnum.SIGN_IN.getValue())) {
                return new SignTimeUpdateDTO().setSignId(sign.getId())
                    .setStartTime(DateUtil.addMinutesTime(taskUpdateDTO.getStartTime(), -advanceTime))
                    .setEndTime(DateUtil.addMinutesTime(taskUpdateDTO.getStartTime(), afterTime))
                    .setSignName(sign.getSignName());
            } else if (Objects.equals(sign.getType(), SignTypeEnum.SIGN_OUT.getValue())) {
                return new SignTimeUpdateDTO().setSignId(sign.getId())
                    .setStartTime(DateUtil.addMinutesTime(taskUpdateDTO.getEndTime(), -advanceTime))
                    .setEndTime(DateUtil.addMinutesTime(taskUpdateDTO.getEndTime(), afterTime))
                    .setSignName(sign.getSignName());
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(updateDTOS)) {
            signFeign.updateSignTime(updateDTOS);
        }
    }

    /**
     * 更新任务评估时间
     *
     * @param taskUpdateDTO
     * @param projectId
     */
    private void updateTaskEvaluationTime(ProjectTaskManagerUpdateDTO taskUpdateDTO, String projectId) {
        List<TaskAppResourceDTO> evaluations = evaluationFeign.getTaskEvaluationByProjectId(projectId);
        if (CollectionUtils.isEmpty(evaluations)) {
            return;
        }
        // 获取评估提前/延后时间(分钟)
        String evaluationStartTime = paraFeign.getParaValue(ParaTypeEnum.EVALUATION_START_DELAY_DEFAULT_TIME.getCode());
        int advanceTime = StringUtils.isBlank(evaluationStartTime) ? 5 : Integer.valueOf(evaluationStartTime);
        String evaluationEndTime = paraFeign.getParaValue(ParaTypeEnum.EVALUATION_END_DELAY_DEFAULT_TIME.getCode());
        int afterTime = StringUtils.isBlank(evaluationEndTime) ? 5 : Integer.valueOf(evaluationEndTime);
        List<EvalDTO> updateList = evaluations.stream().map(param -> new EvalDTO().setId(param.getResourceId())
            .setStartTime(DateUtil.addMinutesTime(taskUpdateDTO.getEndTime(), advanceTime))
            .setEndTime(DateUtil.addMinutesTime(taskUpdateDTO.getEndTime(), afterTime))
            .setEvalName(param.getResourceName())).collect(Collectors.toList());

        evaluationFeign.updateEvalTime(updateList);
    }

    private void sendUpdatePushFeign(ProjectTaskManagerUpdateDTO projectTaskManagerUpdateDTO, Integer operateState) {
        SendPushDTO sendPushDTO = new SendPushDTO();
        sendPushDTO.setLang(LocaleContextHolder.getLocale().getLanguage());
        sendPushDTO.setServerName(Objects.requireNonNull(WebUtil.getRequest()).getServerName());
        PushNoticeSetDTO pushNoticeSetDTO = projectTaskManagerUpdateDTO.getPushNoticeSetDTO();
        sendPushDTO.setPushNoticeSetDTO(pushNoticeSetDTO);

        // 为空默认是0
        Integer isTrain = Optional.ofNullable(projectTaskManagerUpdateDTO.getIsTrain()).orElse(0);

        PushResourceDTO pushResourceDTO = new PushResourceDTO().setIsTrain(isTrain).setOperateState(operateState)
            .setProgrammeId(projectTaskManagerUpdateDTO.getProgrammeId());

        sendPushDTO.setPushResourceDTO(pushResourceDTO);

        PushAttributeDTO pushAttributeDTO = new PushAttributeDTO();

        boolean condition = isTrain != 0;

        if (condition) {
            // 非 0 证明是属于二级内容过来
            String[] name = pushNoticeSetDTO.getResourceName().split("_");
            pushAttributeDTO.setName(name[0]);
            pushAttributeDTO.setSecondaryName(name[1]);
        } else {
            pushAttributeDTO.setName(pushNoticeSetDTO.getResourceName());
        }

        pushResourceDTO.setResourceId(pushNoticeSetDTO.getResourceId());
        pushResourceDTO.setResourceName(pushNoticeSetDTO.getResourceName());
        pushResourceDTO.setResourceType(pushNoticeSetDTO.getResourceType());
        pushAttributeDTO.setExistSecondary(condition);
        pushAttributeDTO.setStartTime(projectTaskManagerUpdateDTO.getStartTime());
        pushAttributeDTO.setEndTime(projectTaskManagerUpdateDTO.getEndTime());
        pushAttributeDTO.setIntro(projectTaskManagerUpdateDTO.getTaskName());
        pushAttributeDTO.setExamDuration(projectTaskManagerUpdateDTO.getDuration());

        @SuppressWarnings("unchecked") String systemName = (String) redisTemplate.opsForHash()
            .get(TenantRedisKeyConstant.SYSTEM_NAME_KEY, UserThreadContext.getTenantId());
        pushAttributeDTO.setSystemName(systemName);

        sendPushDTO.setPushAttributeDTO(pushAttributeDTO);

        pushFeign.sendPush(sendPushDTO);
    }

    private void updateTask(Task updateTask) {
        // 查询当前任务并加锁，如果是直接创建同步更新，考试、调研时间
        Task oldTask = Optional.ofNullable(baseMapper.selectOneByIdForUpdate(updateTask.getId()))
            .orElseThrow(() -> new BusinessException(ProjectErrorNoEnum.PROJECT_TASK_NOT_EXIST));
        Project project = projectMapper.selectById(oldTask.getProId());

        // 添加/编辑学习项目任务公共校验
        commonCheck(project.getId(), updateTask.getId(), updateTask.getSort(), updateTask.getStartTime(),
            updateTask.getEndTime(), null);

        // 如果是直接创建 同步去修改资源  只有考试和调研需要修改时间
        if (Objects.equals(oldTask.getCreateType(), ProjectConstant.CREATE_TYPE_DIRECT)
            && updateTask.getStartTime() != null && (Objects.equals(oldTask.getTaskType(), EXAM.getTaskType())
            || Objects.equals(oldTask.getTaskType(), ProjectTaskTypeEnum.SURVEY.getTaskType()))) {
            BaseStrategy baseStrategy = ProjectTaskTypeEnum.getStrategy(oldTask.getTaskType());
            assert baseStrategy != null;
            baseStrategy.updateResourceTime(oldTask.getTaskContent(), updateTask);
        }

        // 日期项目同步修改用户已参与关联的任务时间
        if (project.getType() == ProjectConstant.PROJECT_TYPE_FIXED_DATE && (
            !oldTask.getStartTime().equals(updateTask.getStartTime()) || !oldTask.getEndTime()
                .equals(updateTask.getEndTime()))) {
            taskProgressService.update(
                new LambdaUpdateWrapper<TaskProgress>().set(TaskProgress::getStartTime, updateTask.getStartTime())
                    .set(TaskProgress::getEndTime, updateTask.getEndTime())
                    .eq(TaskProgress::getTaskId, updateTask.getId()));
        }

        taskDao.updateTask(updateTask);

        // 兼容讲师工作台的发布功能
        Integer isPublish = updateTask.getIsPublish();
        if (Optional.ofNullable(isPublish).isPresent()) {
            if (PublishStatusEnum.IS_PUBLISH.getValue() == isPublish) {
                taskDao.publishTask(updateTask);
            } else {
                taskDao.unPublishTask(updateTask);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void publish(PublishDTO publishDTO) {
        long delCount = baseMapper.isDelCount(publishDTO.getIds());
        List<Task> taskList = list(new LambdaQueryWrapper<Task>().in(Task::getId, publishDTO.getIds()));
        Integer isPublish = publishDTO.getIsPublish();
        if (Objects.equals(isPublish, PublishEnum.PUBLISHED.getValue())) {
            if (delCount > 0) {
                throw new BusinessException(ProjectErrorNoEnum.ONLY_PUBLISH_NOT_DELETED_TASK);
            }
            taskList.forEach(task -> taskDao.publishTask(task));
        } else {
            if (delCount > 0) {
                throw new BusinessException(ProjectErrorNoEnum.ONLY_UNPUBLISH_NOT_DELETED_TASK);
            }
            taskList.forEach(task -> taskDao.unPublishTask(task));
        }

        // 发送资源操作事件消息
        handleResourceMq(taskList, Objects.equals(isPublish, PublishEnum.PUBLISHED.getValue()) ? OperationEnum.PUBLISH
            : OperationEnum.PUBLISH_CANCEL);

        // 过滤引用创建的任务
        taskList.removeIf(task -> Objects.equals(task.getCreateType(), ProjectConstant.CREATE_TYPE_QUOTE));
        // 同步发布/取消发布直接创建资源
        taskList.forEach(task -> {
            if (!isForm(task.getTaskType()) && StringUtils.isNotBlank(task.getTaskContent())) {
                Objects.requireNonNull(ProjectTaskTypeEnum.getStrategy(task.getTaskType()))
                    .publishResource(task.getTaskContent(), isPublish);
            }
        });
        // 删除前置任务关联（取消发布时触发）
        if (PublishEnum.NOT_PUBLISH.getValue() == publishDTO.getIsPublish()) {
            List<Task> list = list(new LambdaQueryWrapper<Task>().in(Task::getPredecessors, publishDTO.getIds()));
            list.forEach(t -> taskDao.updateTask(new Task().setId(t.getId()).setPredecessors("")));
        }
    }

    @Override
    public void remove(String ids) {
        Set<String> idList = Arrays.stream(ids.split(CommonConstants.A_COMMA_IN_ENGLISH))
            .collect(Collectors.toSet());
        List<Task> taskList = list(new LambdaQueryWrapper<Task>().in(Task::getId, idList));

        //对删除的任务进行判断,已发布的任务不能删除
        long publishCount = taskList.stream()
            .filter(task -> task.getIsPublish() == PublishStatusEnum.IS_PUBLISH.getValue()).count();
        if (publishCount > 0) {
            throw new BusinessException(BaseErrorNoEnum.INCLUDING_IS_PUBLISH);
        }
        // 复制一份副本，用于删除任务（下面的代码要做资源过滤）
        List<Task> delTaskList = BeanListUtils.copyListProperties(taskList, Task::new);

        // 只删除直接创建的任务的资源
        taskList.removeIf(task -> Objects.equals(task.getCreateType(), ProjectConstant.CREATE_TYPE_QUOTE));
        // 删除资源
        taskList.stream().filter(task -> !isForm(task.getTaskType()) && StringUtils.isNotBlank(task.getTaskContent()))
            .forEach(task -> Objects.requireNonNull(ProjectTaskTypeEnum.getStrategy(task.getTaskType()))
                .removeResource(task.getTaskContent()));

        // 删除任务（使用之前的列表副本进行操作）
        delTaskList.forEach(task -> {
            taskDao.delTask(task);
            // 日程任务处理
            scheduleToolService.removeByTask(new ScheduleTool().setTaskId(task.getId()));
        });

        // 发送资源推送同步消息
        handleResourceMq(taskList, OperationEnum.DELETE);

        // 删除任务关联的用户
        taskProgressService.remove(new LambdaQueryWrapper<TaskProgress>().in(TaskProgress::getTaskId, idList));

        // 删除任务关联资源
        List<TaskContent> taskContents = taskContentMapper.selectList(
            Wrappers.<TaskContent>lambdaQuery().in(TaskContent::getTaskId, idList));
        taskContents.forEach(taskContent -> taskContentDao.delTaskContent(taskContent));

        // 删除前置任务关联
        List<Task> list = list(new LambdaQueryWrapper<Task>().in(Task::getPredecessors, idList));
        list.forEach(t -> taskDao.updateTask(new Task().setId(t.getId()).setPredecessors("")));
    }

    /**
     * 处理资源状态 mq 消息
     *
     * @param taskList      任务列表
     * @param operationEnum 操作枚举
     */
    private void handleResourceMq(List<Task> taskList, OperationEnum operationEnum) {
        Set<String> proIds = taskList.stream().map(Task::getProId).collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(proIds)) {
            return;
        }

        // 查出属于快速培训的项目
        List<Project> quickProject = projectMapper.selectList(
            Wrappers.<Project>lambdaQuery().select(Project::getId).in(Project::getId, proIds)
                .eq(Project::getProjectType, GeneralJudgeEnum.CONFIRM.getValue()));

        Set<String> projectIds = quickProject.stream().map(Project::getId).collect(Collectors.toSet());

        taskList.stream().map(task -> {
                PushType resourceType = ProjectTaskTypeEnum.getProjectOrQuickPushType(task.getTaskType(),
                    projectIds.contains(task.getProId()));
                if (resourceType == null) {
                    return null;
                }
                // 根据规则组装数据类型
                String resourceId = task.getProId() + StringPool.UNDERSCORE + task.getTaskContent();
                return new ResourceOperateEvent(operationEnum, resourceType.getKey(), resourceId);
            }).filter(Objects::nonNull).collect(Collectors.toList())
            // 发送资源操作事件消息
            .forEach(mqProducer::sendMsg);
    }

    @Override
    @SuppressWarnings("java:S3776")
    public TaskEchoDTO create(ProjectTaskManagerSaveDTO saveDTO) {
        Project project = commonCheck(saveDTO.getProjectId(), null, saveDTO.getSort(), saveDTO.getStartTime(),
            saveDTO.getEndTime(), saveDTO.getProjectType());
        Task task = new Task();
        BeanUtils.copyProperties(saveDTO, task);
        task.setId(StringUtil.newId());
        task.setProId(saveDTO.getProjectId());
        task.setPredecessors(saveDTO.getPreTaskId());
        task.setTaskType(saveDTO.getTaskType());
        task.setTaskName(saveDTO.getTaskName());
        task.setSort(saveDTO.getSort());
        task.setScore(saveDTO.getScore());
        task.setCreateType(ProjectConstant.CREATE_TYPE_DIRECT);
        if (saveDTO.getIsAutoPublish() != null && saveDTO.getIsAutoPublish() == 1) {
            task.setIsPublish(1);
            task.setPublishBy(UserThreadContext.getUserId());
            task.setPublishTime(new Date());
        }
        if (task.getTaskType().equals(ProjectTaskTypeEnum.PRACTICAL_OPERATION.getTaskType())
            && null != saveDTO.getPracticalOperationDTO()) {
            // 如果是实操，则创建
            saveDTO.getPracticalOperationDTO().setTitle(saveDTO.getTaskName());
            practicalOperationService.saveByDTO(saveDTO.getPracticalOperationDTO());
            task.setTaskContent(saveDTO.getPracticalOperationDTO().getId());
        }
        if (Objects.equals(ProjectConstant.PROJECT_COURSE_TASK, project.getProjectType())){
            task.setTaskReplenishmentStatus(3);
        }
        taskDao.saveTask(task);

        // 面授，学习，周期，班级 需要进行补派处理，其余不操作
        if (Objects.equals(ProjectConstant.PROJECT_TYPE_FACE, project.getProjectType())
            || Objects.equals(ProjectConstant.PROJECT_TYPE_DEFAULT, project.getProjectType())
        ) {
            ReplenishDTO replenishDTO = new ReplenishDTO().setId(task.getId());
            ReplenishDTO replenishDTO1 = new ReplenishDTO().setId(task.getId());
            // 任务创建完成后，进行任务补派
            if (Objects.nonNull(saveDTO.getTaskReplenishmentStatus()) && saveDTO.getTaskReplenishmentStatus() == 3){
                replenishDTO.setType(ProjectConstant.COMPLETED_REPLENISH);
                replenishDTO1.setType(ProjectConstant.UNDERWAY_REPLENISH);
                replenish(replenishDTO);
                replenish(replenishDTO1);
            } else if (Objects.nonNull(saveDTO.getTaskReplenishmentStatus()) && saveDTO.getTaskReplenishmentStatus() == 1){
                replenishDTO.setType(ProjectConstant.UNDERWAY_REPLENISH);
                replenish(replenishDTO);
            }
        }
        //课程任务需要补派给所有用户
        if (Objects.equals(ProjectConstant.PROJECT_COURSE_TASK, project.getProjectType())){
            ReplenishDTO replenishDTO2 = new ReplenishDTO().setId(task.getId());
            ReplenishDTO replenishDTO3 = new ReplenishDTO().setId(task.getId());
            replenishDTO2.setType(ProjectConstant.COMPLETED_REPLENISH);
            replenishDTO3.setType(ProjectConstant.UNDERWAY_REPLENISH);
            replenish(replenishDTO2);
            replenish(replenishDTO3);
        }

        if (Objects.nonNull(saveDTO.getFormTaskProgrammeId())) {
            // 如果是辅导任务，保存权限管理范围
            formTaskViewLimitComponent.handleNewViewLimit(saveDTO.getFormTaskProgrammeId(), task.getId());
        }

        TaskEchoDTO taskEchoDTO = new TaskEchoDTO();
        taskEchoDTO.setId(task.getId());
        taskEchoDTO.setTaskContent(task.getTaskContent());
        // 启用才给资源配置推送
        boolean isForm = isForm(saveDTO.getTaskType());
        if (isForm && (GeneralJudgeEnum.CONFIRM.getValue().equals(saveDTO.getIsPublish()))) {
            sendPushFeignCreateFormTask(saveDTO, taskEchoDTO.getId(), 0);
        }

        // 日程任务处理
        if (StringUtils.isNotBlank(task.getScheduleId())) {
            ScheduleTool scheduleTool = new ScheduleTool().setProjectId(task.getProId()).setPhaseId(task.getPhaseId())
                .setScheduleId(task.getScheduleId()).setToolType(task.getTaskType()).setTaskId(task.getId())
                .setTaskName(task.getTaskName()).setResourceId(task.getTaskContent()).setStartTime(task.getStartTime())
                .setEndTime(task.getEndTime()).setIsAvailable(
                    isForm(task.getTaskType()) ? AvailableEnum.AVAILABLE.getValue()
                        : AvailableEnum.NOT_AVAILABLE.getValue());
            scheduleToolService.saveOrUpdateByTask(scheduleTool);
            if (isForm) {
                PublishDTO publishDTO = new PublishDTO().setIds(List.of(task.getId()))
                    .setIsPublish(PublishEnum.PUBLISHED.getValue());
                getBean().publish(publishDTO);
            }
        }

        return taskEchoDTO;
    }

    private void sendPushFeignCreateFormTask(ProjectTaskManagerSaveDTO projectTaskManagerSaveDTO, String taskId,
        Integer operateState) {
        if (Objects.isNull(projectTaskManagerSaveDTO.getPushNoticeSetDTO())) {
            return;
        }
        SendPushDTO sendPushDTO = new SendPushDTO();
        sendPushDTO.setLang(LocaleContextHolder.getLocale().getLanguage());
        sendPushDTO.setServerName(Objects.requireNonNull(WebUtil.getRequest()).getServerName());
        PushNoticeSetDTO pushNoticeSetDTO = projectTaskManagerSaveDTO.getPushNoticeSetDTO();
        //辅导任务特殊处理
        pushNoticeSetDTO.setResourceId(pushNoticeSetDTO + taskId);
        sendPushDTO.setPushNoticeSetDTO(pushNoticeSetDTO);

        // 为空默认是0
        Integer isTrain = Optional.ofNullable(projectTaskManagerSaveDTO.getIsTrain()).orElse(0);

        PushResourceDTO pushResourceDTO = new PushResourceDTO().setIsTrain(isTrain).setOperateState(operateState)
            .setProgrammeId(projectTaskManagerSaveDTO.getProgrammeId());

        sendPushDTO.setPushResourceDTO(pushResourceDTO);

        PushAttributeDTO pushAttributeDTO = new PushAttributeDTO();

        boolean condition = isTrain != 0;

        if (condition) {
            // 非 0 证明是属于二级内容过来
            String[] name = pushResourceDTO.getResourceName().split("_");
            pushAttributeDTO.setName(name[0]);
            pushAttributeDTO.setSecondaryName(name[1]);
        } else {
            pushAttributeDTO.setName(pushNoticeSetDTO.getResourceName());
        }

        pushResourceDTO.setResourceId(pushNoticeSetDTO.getResourceId());
        pushResourceDTO.setResourceName(pushNoticeSetDTO.getResourceName());
        pushResourceDTO.setResourceType(pushNoticeSetDTO.getResourceType());
        pushAttributeDTO.setExistSecondary(condition);
        pushAttributeDTO.setStartTime(projectTaskManagerSaveDTO.getStartTime());
        pushAttributeDTO.setEndTime(projectTaskManagerSaveDTO.getEndTime());
        pushAttributeDTO.setIntro(projectTaskManagerSaveDTO.getTaskName());

        @SuppressWarnings("unchecked") String systemName = (String) redisTemplate.opsForHash()
            .get(TenantRedisKeyConstant.SYSTEM_NAME_KEY, UserThreadContext.getTenantId());
        pushAttributeDTO.setSystemName(systemName);

        sendPushDTO.setPushAttributeDTO(pushAttributeDTO);

        pushFeign.sendPush(sendPushDTO);

    }

    @Override
    public TaskEchoDTO createLecturerWorkbenchTask(ProjectTaskSaveDTO projectTaskSaveDTO) {
        // 当前设计不用前端传的序号值 所以获取当前周期项目的任务最高序号递增1
        Project project = Optional.ofNullable(projectMapper.selectById(projectTaskSaveDTO.getProjectId()))
            .orElseThrow(() -> new BusinessException(ProjectErrorNoEnum.PROJECT_NOT_EXIST));
        if (ProjectConstant.PROJECT_TYPE_FIXED_CYCLE == project.getType()) {
            Long maxSort = baseMapper.getMaxSort(project.getId());
            projectTaskSaveDTO.setSort(
                Optional.ofNullable(maxSort).isPresent() ? maxSort + 1 : GeneralJudgeEnum.CONFIRM.getValue());
        }
        commonCheck(projectTaskSaveDTO.getProjectId(), null, projectTaskSaveDTO.getSort(),
            projectTaskSaveDTO.getStartTime(), projectTaskSaveDTO.getEndTime(), null);
        Task task = new Task();
        BeanUtils.copyProperties(projectTaskSaveDTO, task);
        task.setId(StringUtil.newId());
        task.setProId(projectTaskSaveDTO.getProjectId());
        task.setTaskType(projectTaskSaveDTO.getTaskType());
        task.setTaskName(projectTaskSaveDTO.getTaskName());
        task.setSort(projectTaskSaveDTO.getSort());
        task.setScore(projectTaskSaveDTO.getScore());
        Integer isPublish = projectTaskSaveDTO.getIsPublish();
        if (isPublish == PublishStatusEnum.IS_PUBLISH.getValue()) {
            task.setIsPublish(isPublish);
            task.setPublishBy(UserThreadContext.getUserId());
            task.setPublishTime(new Date());
        }
        task.setCreateType(ProjectConstant.CREATE_TYPE_DIRECT);
        save(task);
        TaskEchoDTO taskEchoDTO = new TaskEchoDTO();
        taskEchoDTO.setId(task.getId());
        return taskEchoDTO;
    }

    @Override
    public void updateLecturerWorkbenchTask(ProjectTaskUpdateDTO projectTaskUpdateDTO) {
        Task update = new Task();
        BeanUtils.copyProperties(projectTaskUpdateDTO, update);
        // 当前设计不可编辑序号
        Task oldTask = Optional.ofNullable(baseMapper.selectOneByIdForUpdate(update.getId()))
            .orElseThrow(() -> new BusinessException(ProjectErrorNoEnum.PROJECT_TASK_NOT_EXIST));
        update.setSort(oldTask.getSort());
        updateTask(update);
    }

    /**
     * 添加/编辑学习项目任务公共校验
     *
     * @param projectId 学习项目id
     * @param taskId    任务id（为空添加，非空编辑）
     * @param sort      序号
     * @param startTime 开始时间
     * @param endTime   结束时间
     */
    private Project commonCheck(String projectId, String taskId, Long sort, Date startTime, Date endTime,
        Integer projectType) {
        // 学习项目校验
        Project project;
        if (projectType != null && projectType == 1) {
            // 快速培训
            log.info("获取快速培训");
            project = projectMapper.selectQuickProjectById(projectId);
        } else if (projectType != null && projectType == 2) {
            // 课程学习任务
            log.info("获取课程任务");
            project = projectMapper.selectCourseTaskById(projectId);
        } else {
            project = projectMapper.selectById(projectId);
        }
        if (null == project) {
            throw new BusinessException(ProjectErrorNoEnum.PROJECT_NOT_EXIST);
        }
        // 日期项目 任务开始、结束时间校验
        boolean timeIsNull = null == startTime || null == endTime;
        if (ProjectConstant.PROJECT_TYPE_FIXED_DATE == project.getType() && timeIsNull) {
            throw new BusinessException(ProjectErrorNoEnum.ERR_PROJECT_TASK_TIME_NULL);
        }
        // 周期项目 任务排序校验
        checkCycleProject(projectId, taskId, sort, project);
        // 课程学习任务 任务排序校验不允许重复
        if (ProjectConstant.PROJECT_COURSE_TASK == project.getProjectType()) {
            Long count = baseMapper.judgeSortIsExist(projectId, sort, taskId);
            if (count > 0) {
                log.error("该课程任务排序重复，项目id: [{}], 任务sort: [{}]", projectId, sort);
                throw new BusinessException(ProjectErrorNoEnum.TASK_SORT_EXIST);
            }
        }
        return project;
    }

    private void checkCycleProject(String projectId, String taskId, Long sort, Project project) {
        if (ProjectConstant.PROJECT_TYPE_FIXED_CYCLE == project.getType()) {
            LambdaQueryWrapper<Task> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Task::getProId, projectId);
            queryWrapper.eq(Task::getSort, sort);
            // 非空编辑则排除自己
            queryWrapper.ne(StringUtils.isNotEmpty(taskId), Task::getId, taskId);
            List<Task> taskList = list(queryWrapper);
            if (CollectionUtils.isNotEmpty(taskList)) {
                for (Task task : taskList) {
                    if (ProjectTaskTypeEnum.isForm(task.getTaskType()) || (
                        !ProjectTaskTypeEnum.isForm(task.getTaskType()) && StringUtils.isNotBlank(
                            task.getTaskContent()))) {
                        throw new BusinessException(ProjectErrorNoEnum.TASK_SORT_EXIST);
                    }
                }
            }
        }
    }

    @Override
    public List<TaskDTO> publishTask(String projectId, String taskId) {
        List<Task> publishTaskList = list(new LambdaQueryWrapper<Task>().eq(Task::getProId, projectId)
            .eq(Task::getIsPublish, PublishEnum.PUBLISHED.getValue())
            .ne(StringUtils.isNotEmpty(taskId), Task::getId, taskId));

        return publishTaskList.stream().map(task -> {
            TaskDTO taskDTO = new TaskDTO();
            taskDTO.setId(task.getId());
            taskDTO.setTaskName(task.getTaskName());
            return taskDTO;
        }).collect(Collectors.toList());
    }


    @Override
    public void replenish(ReplenishDTO replenishDTO) {
        //这里因为是同步上锁，异步释放锁，所以不能使用线程锁
        String lockName = "replenish-task:" + replenishDTO.getId() + ":type:" + replenishDTO.getType();
        Boolean isGetLock = redisTemplate.opsForValue().setIfAbsent(lockName, 1, 1, TimeUnit.SECONDS);
        log.info("isGetLock: " + isGetLock);
        if (Objects.equals(isGetLock, true)) {
            replenishDTO.setLockName(lockName);
            getBean().replenishExecute(replenishDTO);
        } else {
            throw new BusinessException(ProjectErrorNoEnum.ERR_TASK_REPLENISH_REPEAT);
        }
    }

    @Async
    public void replenishExecute(ReplenishDTO replenishDTO) {
        ReplenishTaskLog logInfo = new ReplenishTaskLog();
        logInfo.setTaskId(replenishDTO.getId());
        logInfo.setParam(JsonUtil.objToJson(replenishDTO));
        try {
            long startTime = System.currentTimeMillis();
            log.error("replenishExecut,replenishDTO:{}", replenishDTO);
            logInfo.setRemark("replenishExecute 任务派发开始");
            replenishTaskLogService.addLog(logInfo);
            Task task = getById(replenishDTO.getId());
            log.info("task: " + task);
            if (task == null) {
                throw new BusinessException(ProjectErrorNoEnum.PROJECT_TASK_NOT_EXIST);
            }
            Project project = projectMapper.selectById(task.getProId());
            List<TaskProgress> taskProgressList = getTaskProgressList(replenishDTO.getType(), task, project);

            if (StringUtils.isNotEmpty(replenishDTO.getBatchLockName())) {
                //如果存在批次锁，则所有任务处理完才把项目状态已完成更新为未完成(如果一次补派多个任务，已完成补派，会存在第一个任务补派完后立刻更新项目状态成未完成，这时候后面的任务就会补派不成功)
                Integer executeNum = (Integer) redisTemplate.opsForValue().get(replenishDTO.getBatchLockName()) + 1;
                if (Objects.equals(replenishDTO.getBatchSize(), executeNum)) {
                    updateProjectToUnFinish(project.getId());
                }
            } else {
                updateProjectToUnFinish(project.getId());
            }

            log.info("taskProgressList: " + taskProgressList);
            if (!CollectionUtils.isEmpty(taskProgressList)) {
                taskProgressService.saveBatch(taskProgressList, 100);
                Set<String> userIds = taskProgressList.stream().map(TaskProgress::getUserId).collect(Collectors.toSet());
                // 更新项目进度百分比
                userIds.forEach(
                    uid -> progressService.lambdaUpdate()
                        .set(Progress::getProgressPercent, progressMapper.getProgressById(project.getId(), uid))
                        .eq(Progress::getProId, project.getId()).eq(Progress::getUserId, uid).update());

                // 将全部已完成资源做奖励同步
                List<TaskProgress> finishActivityProgressList = taskProgressList.stream()
                    .filter(a -> GeneralJudgeEnum.CONFIRM.getValue().equals(a.getIsFinish())).collect(
                        Collectors.toList());
                if (!CollectionUtils.isEmpty(finishActivityProgressList)) {
                    // 规避全部资源都是完成的情况
                    progressService.updateProgress(task.getProId(), finishActivityProgressList);
                }
                long costTime = System.currentTimeMillis() - startTime;
                log.error("replenishExecute 任务派发完成,replenishDTO:{},耗时:{}", replenishDTO, costTime);
                logInfo.setResult(JsonUtil.objToJson(userIds));
                logInfo.setRemark("replenishExecute 任务派发完成,耗时:"+costTime);
                replenishTaskLogService.addLog(logInfo);
            } else {
                long costTime = System.currentTimeMillis() - startTime;
                log.error("replenishExecute 任务派发完成,没有待派发的任务,replenishDTO:{},耗时:{}", replenishDTO,
                    costTime);
                logInfo.setRemark("replenishExecute 任务派发完成,没有待派发的任务,耗时:"+costTime);
                replenishTaskLogService.addLog(logInfo);
            }
            //更新预结业名单
            completionUserService.proCompletionUser(project.getId());

        } catch (Exception e) {
            log.error("replenishExecute 任务派发失败,replenishDTO:" + JsonUtil.objToJson(replenishDTO) + ",error:", e);
            logInfo.setIsSuccess(0);
            logInfo.setRemark("replenishExecute 任务派发失败,异常:" + ExceptionUtils.getStackTrace(e));
            replenishTaskLogService.addLog(logInfo);
        } finally {
            String lockName = replenishDTO.getLockName();
            if (!StringUtils.isEmpty(lockName)) {
                redisTemplate.delete(lockName);
                log.error("replenishExecute 释放锁:{},replenishDTO:{},", lockName, replenishDTO);
            }
            //存在批次锁,并且所有任务都处理完
            String batchLockName = replenishDTO.getBatchLockName();
            //如果存在批次锁，则标记执行处理完成+1
            if (StringUtils.isNotEmpty(batchLockName)) {
                redisTemplate.opsForValue().increment(replenishDTO.getBatchLockName(), 1);
                //全部任务都补派完成
                if (Objects.equals(replenishDTO.getBatchSize(),
                    redisTemplate.opsForValue().get(replenishDTO.getBatchLockName()))) {
                    redisTemplate.delete(batchLockName);
                    log.error("replenishExecute 释放批次锁:{},replenishDTO:{},", batchLockName, replenishDTO);
                }
            }
        }
    }

    private List<TaskProgress> getTaskProgressList(Integer type, Task task, Project project) {
        if (type == ProjectConstant.COMPLETED_REPLENISH) {
            // 已完成，更新项目成员关联任务
            return getCompletedTaskProgresses(task, project);
        } else if (type == ProjectConstant.UNDERWAY_REPLENISH) {
            // 进行中，更新项目成员关联任务
            return getUnderwayTaskProgresses(task, project);
        }
        return new ArrayList<>();
    }

    private List<TaskProgress> getUnderwayTaskProgresses(Task task, Project project) {
        List<TaskProgress> taskProgressList = new ArrayList<>();
        List<Progress> projectTaskProgressList = progressMapper.getProjectTaskProgressList(task);
        if (CollectionUtils.isEmpty(projectTaskProgressList)) {
            return new ArrayList<>();
        }
        //获取用户组织信息并添加到task_progress表
        List<UserDTO> userList = new ArrayList<>();
        List<String> userIdList = new ArrayList<>();
        projectTaskProgressList.stream().forEach(item -> {
            String userId = item.getUserId();
            String orgId = item.getOrgId();
            String orgName = item.getOrgName();
            UserDTO userDTO = new UserDTO();
            userDTO.setId(userId);
            userDTO.setOrgId(orgId);
            userDTO.setOrgName(orgName);
            userIdList.add(item.getUserId());
            userList.add(userDTO);
        });

        //获取数据库中的任务数据，已存在的任务不用再进行补派(之前一直都创建新的任务，导致补派不成功)
        List<TaskProgress> dbTaskProgress = taskProgressMapper.selectList(new LambdaQueryWrapper<TaskProgress>()
            .eq(TaskProgress::getTaskId, task.getId()).eq(TaskProgress::getProId, project.getId())
            .in(TaskProgress::getUserId, userIdList));
        Map<String, TaskProgress> userTaskProgressMap = dbTaskProgress.stream()
            .collect(Collectors.toMap(TaskProgress::getUserId, item -> item, (key1, key2) -> key1));

        Map<String, UserDTO> userMap = userList.stream()
            .collect(Collectors.toMap(UserDTO::getId, item -> item, (key1, key2) -> key1));
        Map<String, Integer> userResourceMap = getUserFinish(task, userIdList);
        for (Progress progress : projectTaskProgressList) {
            //已存在任务，不需要进行补派
            if (null != userTaskProgressMap.get(progress.getUserId())) {
                continue;
            }
            TaskProgress taskProgress = new TaskProgress();
            taskProgress.setId(StringUtil.newId());
            checkActivityFinish(taskProgress, userResourceMap);
            taskProgress.setIsShowGuide(0);
            taskProgress.setTaskId(task.getId());
            String userId = progress.getUserId();

            UserDTO user = userMap.get(progress.getUserId());
            if (null != user) {
                taskProgress.setOrgId(user.getOrgId());
                taskProgress.setOrgName(user.getOrgName());
                taskProgress.setUserName(user.getFullName());
            }
            // 固定周期 开始时间 / 结束时间处理
            if (project.getType() == ProjectConstant.PROJECT_TYPE_FIXED_CYCLE) {
                // 周期项目未完成才分配任务

                taskProgress.setUserId(userId);
                taskProgress.setProId(task.getProId());
                Calendar calendar = Calendar.getInstance();
                // 开始时间

                Date maxStartTime = taskProgressMapper.getMaxStartTime(progress);

                if (maxStartTime == null || maxStartTime.before(new Date())) {
                    // 任务分配最大开始时间在当前时间之前（当天12点）
                    calendar.setTime(new Date());
                    taskProgress.setStartTime(DateUtil.getSettingDate(calendar, 12, 0, 0));
                } else {
                    // 否则（已分配任务最大开始时间）
                    calendar.setTime(maxStartTime);
                    taskProgress.setStartTime(maxStartTime);
                }
                // 结束时间（一天后）
                calendar.add(Calendar.DAY_OF_MONTH, 1);
                taskProgress.setEndTime(DateUtil.getSettingDate(calendar, 12, 0, 0));
            }
            // 固定日期 开始时间 / 结束时间处理
            if (project.getType() == ProjectConstant.PROJECT_TYPE_FIXED_DATE) {
                taskProgress.setUserId(userId);
                taskProgress.setProId(task.getProId());
                taskProgress.setStartTime(task.getStartTime());
                taskProgress.setEndTime(task.getEndTime());
            }
            taskProgressList.add(taskProgress);
        }
        return taskProgressList;
    }

    private List<TaskProgress> getCompletedTaskProgresses(Task task, Project project) {
        List<TaskProgress> taskProgressList = new ArrayList<>();
        List<Progress> progressList = progressMapper.selectList(
            new LambdaQueryWrapper<Progress>().eq(Progress::getProId, project.getId())
                .eq(Progress::getStatus, ProjectConstant.PROGRESS_STATUS_COMPLETED));
        if (CollectionUtils.isEmpty(progressList)) {
            return new ArrayList<>();
        }
        //获取用户组织信息并添加到task_progress表
        List<UserDTO> userList = new ArrayList<>();
        List<String> userIdList = new ArrayList<>();
        progressList.stream().forEach(item -> {
            String userId = item.getUserId();
            String orgId = item.getOrgId();
            String orgName = item.getOrgName();
            UserDTO userDTO = new UserDTO();
            userDTO.setId(userId);
            userDTO.setOrgId(orgId);
            userDTO.setOrgName(orgName);
            userIdList.add(item.getUserId());
            userList.add(userDTO);
        });
        Map<String, UserDTO> userMap = userList.stream()
            .collect(Collectors.toMap(UserDTO::getId, item -> item, (key1, key2) -> key1));
        //获取数据库中的任务数据，已存在的任务不用再进行补派(之前一直都创建新的任务，导致补派不成功)
        List<TaskProgress> dbTaskProgress = taskProgressMapper.selectList(new LambdaQueryWrapper<TaskProgress>()
            .eq(TaskProgress::getTaskId, task.getId()).eq(TaskProgress::getProId, project.getId())
            .in(TaskProgress::getUserId, userIdList));
        Map<String, TaskProgress> userTaskProgressMap = dbTaskProgress.stream()
            .collect(Collectors.toMap(TaskProgress::getUserId, item -> item, (key1, key2) -> key1));

        Map<String, Integer> userResourceMap = getUserFinish(task, userIdList);
        for (Progress progress : progressList) {
            String userId = progress.getUserId();
            //已存在任务，不需要进行补派
            if (null != userTaskProgressMap.get(userId)) {
                continue;
            }
            TaskProgress taskProgress = new TaskProgress();
            taskProgress.setId(StringUtil.newId());
            checkActivityFinish(taskProgress, userResourceMap);
            taskProgress.setIsShowGuide(0);
            UserDTO user = userMap.get(progress.getUserId());
            if (null != user) {
                taskProgress.setOrgId(user.getOrgId());
                taskProgress.setOrgName(user.getOrgName());
                taskProgress.setUserName(user.getFullName());
            }
            // 固定周期 开始时间 / 结束时间处理
            if (project.getType() == ProjectConstant.PROJECT_TYPE_FIXED_CYCLE) {
                // 周期项目未完成才分配任务
                //
                taskProgress.setTaskId(task.getId());
                taskProgress.setProId(task.getProId());
                taskProgress.setUserId(userId);
                Calendar calendar = Calendar.getInstance();
                // 开始时间

                Date maxStartTime = taskProgressMapper.getMaxStartTime(progress);
                if (maxStartTime == null || maxStartTime.before(new Date())) {
                    // 任务分配最大开始时间在当前时间之前（当天12点）
                    calendar.setTime(new Date());
                    taskProgress.setStartTime(DateUtil.getSettingDate(calendar, 12, 0, 0));
                } else {
                    // 否则（已分配任务最大开始时间）
                    calendar.setTime(maxStartTime);
                    taskProgress.setStartTime(maxStartTime);
                }
                // 结束时间（一天后）
                calendar.add(Calendar.DAY_OF_MONTH, 1);
                taskProgress.setEndTime(DateUtil.getSettingDate(calendar, 12, 0, 0));

            }

            // 固定日期 开始时间 / 结束时间处理
            if (project.getType() == ProjectConstant.PROJECT_TYPE_FIXED_DATE) {
                taskProgress.setTaskId(task.getId());
                taskProgress.setUserId(userId);
                taskProgress.setProId(task.getProId());
                taskProgress.setStartTime(task.getStartTime());
                taskProgress.setEndTime(task.getEndTime());
            }
            taskProgressList.add(taskProgress);
        }
        return taskProgressList;
    }

    /**
     * 项目已经完成但是任务未完成的用户数据修改成项目未完成(不管任务维度)
     *
     * @param projectId
     */
    private void updateProjectToUnFinish(String projectId) {
        Set<String> userIds = progressMapper.getToUnFinishUserIds(projectId);
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        Progress progress = new Progress();
        progress.setStatus(GeneralJudgeEnum.NEGATIVE.getValue());
        progress.setUpdateTime(new Date());
        progress.setUpdateBy(UserThreadContext.getUserId());
        progressMapper.update(progress,
            new LambdaQueryWrapper<Progress>().eq(Progress::getProId, projectId).in(Progress::getUserId, userIds));
    }

    @Override
    public PageInfo<RemindersDTO> remindersList(TaskRemindersQuery taskRemindersQueryDTO) {
        PageInfo<TaskProgress> selectPageInfo = PageMethod.startPage(taskRemindersQueryDTO.getPageNo(),
            taskRemindersQueryDTO.getPageSize()).doSelectPageInfo(() -> {
            LambdaQueryWrapper<TaskProgress> queryWrapper = new LambdaQueryWrapper<TaskProgress>().eq(
                    TaskProgress::getTaskId, taskRemindersQueryDTO.getTaskId())
                .eq(TaskProgress::getIsFinish, GeneralJudgeEnum.NEGATIVE.getValue());
            if (StringUtils.isNotBlank(taskRemindersQueryDTO.getUserIds())) {
                queryWrapper.in(TaskProgress::getUserId,
                    Arrays.stream(taskRemindersQueryDTO.getUserIds().split(CommonConstants.A_COMMA_IN_ENGLISH))
                        .collect(Collectors.toSet()));
            }
            taskProgressService.list(queryWrapper);
        });

        Map<String, UserDTO> userNameMapByIds = userFeign.getUserNameMapByIds(
            selectPageInfo.getList().stream().map(TaskProgress::getUserId).collect(Collectors.toSet()));

        List<RemindersDTO> remindersDTOList = selectPageInfo.getList().stream().map(taskProgress -> {
            RemindersDTO remindersDTO = new RemindersDTO();
            remindersDTO.setUserId(taskProgress.getUserId());
            remindersDTO.setLoginName(userNameMapByIds.get(taskProgress.getUserId()).getLoginName());
            remindersDTO.setFullName(userNameMapByIds.get(taskProgress.getUserId()).getFullName());
            remindersDTO.setOrgName(userNameMapByIds.get(taskProgress.getUserId()).getOrgName());
            remindersDTO.setState(taskProgress.getIsFinish());
            return remindersDTO;
        }).collect(Collectors.toList());

        PageInfo<RemindersDTO> pageInfo = new PageInfo<>();
        BeanUtils.copyProperties(selectPageInfo, pageInfo);
        pageInfo.setList(remindersDTOList);

        return pageInfo;
    }

    @Override
    public void userReminders(UserRemindersDTO userRemindersDTO, String taskId) {
        Task task = getById(taskId);
        if (task == null) {
            throw new BusinessException(ProjectErrorNoEnum.PROJECT_TASK_NOT_EXIST);
        }
        Project project = projectMapper.selectById(task.getProId());
        if (project == null) {
            throw new BusinessException(ProjectErrorNoEnum.PROJECT_NOT_EXIST);
        }

        List<UserDTO> userDTOList = userFeign.getUseListByIds(userRemindersDTO.getUserIds());

        StringBuilder pushArea = new StringBuilder();
        userDTOList.forEach(user -> pushArea.append(user.getFullName()).append(","));

        userMailSend(project, task, userRemindersDTO.getUserIds(), pushArea);
    }

    @Override
    @Async
    public void allUserReminders(String taskId) {
        Task task = getById(taskId);
        if (task == null) {
            throw new BusinessException(ProjectErrorNoEnum.PROJECT_TASK_NOT_EXIST);
        }

        Project project = projectMapper.selectById(task.getProId());
        if (project == null) {
            throw new BusinessException(ProjectErrorNoEnum.PROJECT_NOT_EXIST);
        }

        int pageNo = 1;
        int pageSize = 500;
        PageInfo<TaskProgress> taskProgressPageInfo = new PageInfo<>();
        taskProgressPageInfo.setHasNextPage(true);
        while (taskProgressPageInfo.isHasNextPage()) {
            taskProgressPageInfo = PageMethod.startPage(pageNo, pageSize).doSelectPageInfo(
                () -> taskProgressService.list(
                    new LambdaQueryWrapper<TaskProgress>().eq(TaskProgress::getTaskId, taskId)));
            List<TaskProgress> taskProgressList = taskProgressPageInfo.getList();
            if (Objects.nonNull(taskProgressList)) {
                StringBuilder pushArea = new StringBuilder();
                List<String> sysUserList = new ArrayList<>();
                Map<String, UserDTO> userNameMapByIds = userFeign.getUserNameMapByIds(
                    taskProgressList.stream().map(TaskProgress::getUserId).collect(Collectors.toSet()));
                taskProgressList.forEach(taskProgress -> {
                    UserDTO user = userNameMapByIds.get(taskProgress.getUserId());
                    pushArea.append(user.getFullName()).append(",");
                    sysUserList.add(taskProgress.getUserId());
                });
                userMailSend(project, task, sysUserList, pushArea);
            }
            pageNo++;
        }
    }

    @Override
    public PageInfo<TaskFormTemplatePageDTO> formList(TaskFormTemplatePageQuery taskFormTemplatePageQueryDTO) {
        PageInfo<TaskFormTemplatePageDTO> selectPageInfo = PageMethod.startPage(
                taskFormTemplatePageQueryDTO.getPageNo(), taskFormTemplatePageQueryDTO.getPageSize())
            .doSelectPageInfo(() -> taskContentMapper.queryPage(taskFormTemplatePageQueryDTO));

        Set<String> userIdSet = selectPageInfo.getList().stream().map(TaskFormTemplatePageDTO::getPublishBy)
            .filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        Set<String> coachPlanCollect = selectPageInfo.getList().stream().map(TaskFormTemplatePageDTO::getCoachPlan)
            .filter(StringUtils::isNotBlank).collect(Collectors.toSet());

        Map<String, ParaDTO> paraMapByIds = new HashMap<>();
        if (!CollectionUtils.isEmpty(coachPlanCollect)) {
            paraMapByIds = paraFeign.getParaByCodeList(coachPlanCollect).stream().collect(Collectors.toMap(
                ParaDTO::getParaCode, dto -> dto));
        }

        Map<String, UserDTO> userNameMapByIds = new HashMap<>();
        if (!CollectionUtils.isEmpty(userIdSet)) {
            userNameMapByIds = userFeign.getUserNameMapByIds(userIdSet);
        }
        // 查询部门简称
        Map<String, OrgShowDTO> orgShowDTOMap = orgFeign.getOrgShowDTO(
            userNameMapByIds.values().stream().map(UserDTO::getOrgId).collect(Collectors.toSet()));


        for (TaskFormTemplatePageDTO taskFormTemplatePageDTO : selectPageInfo.getList()) {
            UserDTO userDTO = userNameMapByIds.get(taskFormTemplatePageDTO.getPublishBy());
            if (userDTO != null) {
                taskFormTemplatePageDTO.setPublishBy(userDTO.getFullName());
                Optional.ofNullable(orgShowDTOMap.get(userDTO.getOrgId())).ifPresent(orgShowDTO ->
                    taskFormTemplatePageDTO.setOrgName(orgShowDTO.getOrgShortName())
                );
            }
            ParaDTO para = paraMapByIds.get(taskFormTemplatePageDTO.getCoachPlan());
            if (para != null) {
                taskFormTemplatePageDTO.setCoachPlanName(para.getParaName());
            }

        }

        return selectPageInfo;
    }

    @Override
    public void addForm(TaskFormTemplatePageSaveDTO taskFormTemplatePageSaveDTO) {
        Task task = getById(taskFormTemplatePageSaveDTO.getTaskId());
        if (task == null) {
            throw new BusinessException(ProjectErrorNoEnum.PROJECT_TASK_NOT_EXIST);
        }
        if (!isForm(task.getTaskType())) {
            throw new BusinessException(ProjectErrorNoEnum.PROJECT_TASK_TYPE_ERROR);
        }
        Integer maxSort = taskContentMapper.getMaxSortFormTaskContent(task.getId());

        for (String formTemplateId : taskFormTemplatePageSaveDTO.getFormTemplateIdList()) {
            maxSort++;
            TaskContent content = new TaskContent();
            content.setId(StringUtil.newId());
            content.setTaskId(task.getId());
            content.setContentId(formTemplateId);
            content.setContentType(task.getTaskType());
            content.setCreateType(ProjectConstant.CREATE_TYPE_QUOTE);
            content.setSort(maxSort);
            taskContentDao.saveTaskContent(content);
        }
    }

    @Override
    public void deleteForm(String ids) {
        String[] idArray = ids.split(CommonConstants.A_COMMA_IN_ENGLISH);
        List<String> idList = Arrays.asList(idArray);
        checkData(idList);
        String taskId = null;
        for (String id : idList) {
            TaskContent taskContent = taskContentMapper.selectById(id);
            taskId = taskContent.getTaskId();
            // 根据表单模板ID查找下的栏目列表
            List<FormTemplateColumn> columns = formTemplateColumnMapper.selectList(
                new LambdaQueryWrapper<FormTemplateColumn>().eq(FormTemplateColumn::getFormTemplateId,
                        taskContent.getContentId()).orderByAsc(FormTemplateColumn::getSortNo)
                    .orderByAsc(FormTemplateColumn::getCreateTime));
            if (!columns.isEmpty()) {
                for (FormTemplateColumn column : columns) {
                    // 查询有多少个详情记录
                    List<TaskRecordDetail> detailList = taskRecordDetailMapper.selectList(
                        new LambdaQueryWrapper<TaskRecordDetail>().eq(TaskRecordDetail::getTaskTemplateId,
                            taskContent.getId()).eq(TaskRecordDetail::getTaskColumnId, column.getId()));
                    if (!CollectionUtils.isEmpty(detailList)) {
                        for (TaskRecordDetail detail : detailList) {
                            // 删除详情记录
                            taskRecordDetailMapper.deleteById(detail.getId());
                        }
                    }
                }
            }
        }
        taskContentMapper.deleteBatchIds(idList);
        idList.forEach(id -> taskContentDao.delTaskContent(new TaskContent().setId(id)));
        //辅导任务关联序号刷新
        baseMapper.refreshFormTaskContentSort(taskId);
    }

    private void checkData(List<String> idList) {
        long count = taskRecordDetailService.count(
            new LambdaQueryWrapper<TaskRecordDetail>().in(TaskRecordDetail::getTaskTemplateId, idList)
                .eq(TaskRecordDetail::getStatus, 1));
        if (count > 0) {
            throw new BusinessException(ProjectErrorNoEnum.ERR_FORM_TEMPLATE_USED);
        }
    }

    @Override
    public void preForm(PreFormSaveDTO preFormSaveDTO) {
        TaskContent taskContent = taskContentMapper.selectById(preFormSaveDTO.getContentId());
        if (Optional.ofNullable(taskContent).isPresent()) {
            taskContent.setParentId(preFormSaveDTO.getPreContentId());
            taskContentDao.updateTaskContent(taskContent);
        }
    }

    @Override
    public PageInfo<TaskPreFormPageDTO> preFormList(TaskPreFormPageQuery taskPreFormPageQueryDTO) {
        String userId = UserThreadContext.getUserId();
        OrgDTO orgDTO = orgFeign.getOrgByUserId(userId);
        //管辖范围
        Set<String> userManageAreaOrgId = orgFeign.findUserManageAreaLevelPath(userId);

        taskPreFormPageQueryDTO.setCurrentUserId(userId);
        taskPreFormPageQueryDTO.setCurrentOrgId(orgDTO.getId());
        taskPreFormPageQueryDTO.setUserManageAreaOrgId(userManageAreaOrgId);

        PageInfo<TaskPreFormPageDTO> sqlPageInfo = PageMethod.startPage(taskPreFormPageQueryDTO.getPageNo(),
                taskPreFormPageQueryDTO.getPageSize())
            .doSelectPageInfo(() -> projectFormTemplateMapper.queryPrePage(taskPreFormPageQueryDTO));
        // 查询部门简称
        Map<String, OrgShowDTO> orgShowDTOMap = orgFeign.getOrgShowDTO(
            sqlPageInfo.getList().stream().map(TaskPreFormPageDTO::getOrgId).collect(Collectors.toList()));

        Set<String> coachPlanCollect = sqlPageInfo.getList().stream().map(TaskPreFormPageDTO::getCoachPlan)
            .filter(StringUtils::isNotBlank).collect(Collectors.toSet());

        Map<String, ParaDTO> paraMapByIds = new HashMap<>();
        if (!CollectionUtils.isEmpty(coachPlanCollect)) {
            paraMapByIds = paraFeign.getParaByCodeList(coachPlanCollect).stream().collect(Collectors.toMap(
                ParaDTO::getParaCode, dto -> dto));
        }
        for (TaskPreFormPageDTO taskPreFormPageDTO : sqlPageInfo.getList()) {
            OrgShowDTO org = orgShowDTOMap.get(taskPreFormPageDTO.getOrgId());
            if (org != null) {
                taskPreFormPageDTO.setOrgName(org.getOrgShortName());
            }

            ParaDTO para = paraMapByIds.get(taskPreFormPageDTO.getCoachPlan());
            if (para != null) {
                // 前端说：为了与其他模块统一，用coachPlan代表辅导方案名称
                taskPreFormPageDTO.setCoachPlan(para.getParaName());
                taskPreFormPageDTO.setCoachPlanName(para.getParaName());
            }
        }

        return sqlPageInfo;
    }

    @Override
    public TaskManagerDTO one(String id, Integer isPushLimit) {
        Task task = baseMapper.selectOneById(id);
        if (task == null) {
            throw new BusinessException(ProjectErrorNoEnum.PROJECT_TASK_NOT_EXIST);
        }

        TaskManagerDTO taskManagerDTO = new TaskManagerDTO();
        BeanUtils.copyProperties(task, taskManagerDTO);
        Project project = projectMapper.selectOneIncludeDel(task.getProId());
        if (task.getIsPublish() == 1 && project.getIsPublish() == 1) {
            taskManagerDTO.setIsPublish(1);
        } else {
            taskManagerDTO.setIsPublish(0);
        }
        //前置任务
        if (StringUtils.isNotBlank(task.getPredecessors())) {
            Task preTask = this.getById(task.getPredecessors());
            taskManagerDTO.setPreTaskName(preTask.getTaskName());
            taskManagerDTO.setPreTaskId(preTask.getId());
        }
        //项目阶段
        setPhase(task, taskManagerDTO);
        //项目类型
        if (isForm(task.getTaskType())) {
            //表单分类
            Optional.ofNullable(categorysService.getEntity(task.getTaskType())).ifPresent(category -> {
                taskManagerDTO.setTaskType(category.getId());
                taskManagerDTO.setTaskTypeName(category.getCategoryName());
                //表单类型任务内容
                taskManagerDTO.setTaskContentName(category.getCategoryName());
            });
        } else {
            com.wunding.learn.project.service.model.Set set = setMapper.selectOne(
                new LambdaQueryWrapper<com.wunding.learn.project.service.model.Set>().eq(
                        com.wunding.learn.project.service.model.Set::getCode, task.getTaskType())
                    .eq(com.wunding.learn.project.service.model.Set::getType, 1)
                    .eq(com.wunding.learn.project.service.model.Set::getProjectType,
                        Objects.equals(project.getProjectType(), ProjectConstant.PROJECT_TYPE_FACE)
                            ? ProjectConstant.PROJECT_TYPE_FACE : 0));
            taskManagerDTO.setTaskType(set.getCode());
            taskManagerDTO.setTaskTypeName(set.getName());
            BaseStrategy strategy = ProjectTaskTypeEnum.getStrategy(task.getTaskType());
            String resourceName = strategy.getResourceName(task.getTaskContent());
            String resourceDuration = strategy.getDuration(task.getTaskContent());
            taskManagerDTO.setTaskContentName(resourceName);
            taskManagerDTO.setDuration(resourceDuration);
        }

        if (null != isPushLimit && 1 == isPushLimit) {
            List<String> userIdList = progressMapper.getStudyProjectPushLimit(task.getProId());
            Set<String> ids = userIdList.stream().collect(Collectors.toSet());
            List<UserNameDTO> userNameDTOList = userFeign.getUserRoleNameAndFullNameByIds(ids);
            List<ViewLimitTypeDTO> viewLimit = new ArrayList<>();
            ViewLimitTypeDTO viewLimitTypeDTO;
            for (UserNameDTO userNameDTO : userNameDTOList) {
                viewLimitTypeDTO = new ViewLimitTypeDTO();
                viewLimitTypeDTO.setCategoryType(ViewLimitTypeEnum.UserLimit.getEnName());
                viewLimitTypeDTO.setCategoryId(userNameDTO.getUserId());
                viewLimitTypeDTO.setCategoryName(userNameDTO.getFullName());
                viewLimit.add(viewLimitTypeDTO);
            }
            taskManagerDTO.setViewLimit(viewLimit);
        }

        if (StringUtils.isNotBlank(taskManagerDTO.getScheduleId())) {
            Optional.ofNullable(scheduleService.getById(taskManagerDTO.getScheduleId()))
                .ifPresent(schedule -> taskManagerDTO.setScheduleName(schedule.getTitle()));
        }

        // 设置辅导任务学员端选择导师时，限制的人员可见范围方案id和基本信息
        Optional.ofNullable(formTaskViewLimitComponent.getViewLimitBaseInfo(
            id)).ifPresent(viewLimitBaseInfo -> {
            taskManagerDTO.setFormTaskProgrammeId(viewLimitBaseInfo.getProgrammeId());
            taskManagerDTO.setFormTaskViewLimitInfo(
                viewLimitFeign.getMainLimitBaseInfo(viewLimitBaseInfo.getProgrammeId()));
        });

        return taskManagerDTO;
    }

    private void setPhase(Task task, TaskManagerDTO taskManagerDTO) {
        if (StringUtils.isNotBlank(task.getPhaseId())) {
            Phase phase = phaseService.getById(task.getPhaseId());
            if (phase != null) {
                taskManagerDTO.setPhaseName(phase.getPhaseName());
                taskManagerDTO.setPhaseId(phase.getId());
            } else {
                taskManagerDTO.setPhaseId(null);
            }

        }
    }

    @Override
    public List<PublishTaskDTO> publishList(String projectId) {
        List<Task> taskList = this.list(new LambdaQueryWrapper<Task>().eq(Task::getProId, projectId)
            .eq(Task::getIsPublish, PublishEnum.PUBLISHED.getValue()));
        return taskList.stream().map(task -> {
            PublishTaskDTO publishTaskDTO = new PublishTaskDTO();
            BeanUtils.copyProperties(task, publishTaskDTO);
            publishTaskDTO.setName(task.getTaskName());
            return publishTaskDTO;
        }).collect(Collectors.toList());
    }

    private void userMailSend(Project project, Task task, List<String> sysUserList, StringBuilder pushArea) {
        StringBuilder content = new StringBuilder();
        if (Objects.nonNull(sysUserList)) {
            content.append(task.getTaskName()).append("<br> ");
            if (!StringUtils.isNotBlank(project.getReferencedId())) {
                pushComponent.remindPush(sysUserList, pushArea, RemindPushEnum.TASK.getBizType(), task.getId(),
                    content.toString());
            } else {
                pushComponent.remindPush(sysUserList, pushArea, RemindPushEnum.TASKTRAIN.getBizType(), task.getId(),
                    content.toString());
            }
        }
    }

    @Override
    public ProjectTaskListDTO getProjectTaskList(String id, ProjectTaskListQuery query) {
        Integer isComplete = query.getIsComplete();
        Integer isExpired = query.getIsExpired();
        String stageId = query.getStageId();
        String userId = UserThreadContext.getUserId();
        // 校验下发权限
        checkViewLimit(id, query, userId);
        Project project = projectDao.projectExist(id);
        Progress progress = getProgress(id, userId);
        // 校验快速培训开始结束时间
        checkProjectTime(project);

        // 项目阶段列表
        List<StageDTO> stageDTOList = getStageList(id);

        // 任务列表
        List<LearnProjectTask> taskList = this.getTaskListByProId(id, stageId, isComplete, isExpired, userId);
        Map<String, LecturerDTO> taskLecturerMap = getTaskLecturer(taskList);
        //获取考试的信息
        Set<String> examIds = taskList.stream()
            .filter(item -> Objects.equals(item.getTaskType(), ProjectTaskTypeEnum.EXAM.getTaskType()))
            .map(LearnProjectTask::getTaskContent).collect(Collectors.toSet());
        Map<String, ViewExamFeignDTO> examInfoMap = examFeign.getValidExamInfoMapByExamIds(examIds);

        // 获取学习项目的任务资源Map
        HashMap<String, Map<Integer, List<TaskAppResourceDTO>>> taskResourceMap = getProjectTaskResourceMap(id);
        Map<String, Integer> taskSort = getTaskSort(id, project);
        List<TaskDTO> taskDTOList = taskList.stream().map(task -> {
            checkExamTime(task, examInfoMap);
            Date startTime = task.getStartTime();
            Date endTime = task.getEndTime();

            TaskDTO taskDTO = new TaskDTO().setId(task.getId()).setTaskName(task.getTaskName())
                .setTaskType(task.getTaskType()).setActivityId(task.getTaskContent())
                .setCreateType(task.getCreateType()).setStartTime(startTime).setEndTime(endTime)
                .setIsRequired(task.getIsRequired())
                .setStatus(task.getStatus()).setUserStatus(task.getUserStatus()).setProjectType(project.getType())
                .setValid(GeneralJudgeEnum.CONFIRM.getValue()).setEnablePlayBack(getEnablePlayBack(task.getLvCount()))
                .setAddress(task.getAddress()).setTaskTypeAlias(task.getTaskTypeAlias());
            // 装配讲师信息
            Optional.ofNullable(taskLecturerMap.get(task.getId()))
                .ifPresent(lecturer -> taskDTO.setLecturerName(lecturer.getName()));
            // 增加任务的状态是否签到、是否评估
            buildTaskAppInfo(taskResourceMap.get(task.getId()), taskDTO);

            perfectTaskDto(project, task, taskDTO, taskSort);
            return taskDTO;
        }).collect(Collectors.toList());

        // 任务总量
        Integer projectTaskCount = taskList.size();

        // 更新学习项目的应用完成状态
        Progress updateProgressById = progressService.lambdaQuery().eq(Progress::getProId, id)
            .eq(Progress::getUserId, userId).one();

        updateProjectFinish(userId, taskList, taskDTOList, updateProgressById, project, progress);

        // 已完成任务量
        long completedCount = taskDTOList.stream()
            .filter(t -> t.getUserStatus().equals(ProjectConstant.USER_TASK_STATUS_COMPLETED)).count();
        Double progressPer = ((double) completedCount / (double) projectTaskCount) * 100;
        BigDecimal progressPercent = BigDecimal.valueOf(progressPer.longValue());
        // 更新学习项目进度百分比, updateTaskCompletedStatus 没办法校验到应用类型的任务,这里重新校验下全部任务状态
        if (completedCount != 0L) {
            if (progressPercent.compareTo(new BigDecimal(100)) >= 0) {
                updateProgressById.setStatus(1);
            }
            updateProgressById.setProgressPercent(progressPercent);
        }
        progressService.updateById(updateProgressById);

        ProjectTaskListDTO projectTaskListDTO = new ProjectTaskListDTO().setStageList(stageDTOList)
            .setTaskList(taskDTOList).setTaskCount(projectTaskCount).setCompletedCount(Math.toIntExact(completedCount));

        // 线上课程学习 所有课程学完之后才允许考试
        if (Objects.equals(project.getProjectType(), ProjectConstant.PROJECT_COURSE_TASK)) {
            projectTaskListDTO.setAfterFinishedCourseCanStartExam(project.getAfterFinishedCourseCanStartExam());
        }

        // 新旧Progress 进度对比，不同则同步更新
        Progress progressDB = getProgress(id, userId);
        if (progress.getProgressPercent() == null
            || (progressDB.getProgressPercent() != null
            && progressDB.getProgressPercent().compareTo(progress.getProgressPercent()) != 0)) {
            ResourceTypeEnum resourceTypeEnum = ProjectBizImpl.getResourceTypeEnum(project.getType(),
                project.getProjectType());
            mqProducer.sendMsg(new ResourceRecordSyncEvent(
                new ResourceRecordSyncDTO(OperationEnum.UPDATE, resourceTypeEnum.name(),
                    progress.getId(), project.getId(), userId, null, userId, null, userId, progress.getUpdateTime()
                    , progressDB.getProgressPercent())));
        }

        return projectTaskListDTO;
    }


    @Override
    public UserStatisticProjectCompletionDTO getUserStatisticProjectCompletion(String userId, String projectId) {

        // 定义返回对象
        UserStatisticProjectCompletionDTO dto = new UserStatisticProjectCompletionDTO().setUserId(userId);

        // 调用客户端任务列表查询接口 (所有已发布项目任务 + 评估 + 签到)
        List<LearnProjectTask> taskList = this.getTaskListByProId(projectId, null, null, null, userId);

        // 添加日志
        log.info("getUserStatisticProjectCompletion params:【userId:{},projectId:{}】 result:{}",
            userId, projectId, JsonUtil.objToJson(taskList));

        // 获取学习项目的任务资源Map
        HashMap<String, Map<Integer, List<TaskAppResourceDTO>>> taskResourceMap = getProjectTaskResourceMap(projectId);

        // 数据转换
        List<TaskDTO> taskDTOList = taskList.stream().map(task -> {
            TaskDTO taskDTO = new TaskDTO()
                .setId(task.getId())
                .setTaskName(task.getTaskName())
                .setTaskType(task.getTaskType())
                .setActivityId(task.getTaskContent())
                .setCreateType(task.getCreateType())
                .setStartTime(task.getStartTime())
                .setEndTime(task.getEndTime())
                .setIsRequired(task.getIsRequired())
                .setStatus(task.getStatus())
                .setUserStatus(task.getUserStatus())
                .setValid(GeneralJudgeEnum.CONFIRM.getValue())
                .setEnablePlayBack(getEnablePlayBack(task.getLvCount()))
                .setAddress(task.getAddress()).setTaskTypeAlias(task.getTaskTypeAlias());

            // 增加任务的状态是否签到、是否评估
            buildTaskAppInfo(taskResourceMap.get(task.getId()), taskDTO);

            return taskDTO;
        }).toList();

        // 项目完成率（所有任务完成率）
        int totalCount = taskDTOList.size();
        int totalFinishCount = (int) taskDTOList.stream()
            .filter(t -> t.getUserStatus().equals(ProjectConstant.USER_TASK_STATUS_COMPLETED)).count();
        int finishRatioPer =
            totalCount == 0 ? 0 : (int) (((double) totalFinishCount / (double) totalCount) * 100);// 去掉小数位
        dto.setTotalCount(totalCount);
        dto.setTotalFinishCount(totalFinishCount);
        dto.setFinishRatio(BigDecimal.valueOf(finishRatioPer));

        // 总任务完成数（课程任务中仅计算必修）- 任务类型不是选修课程
        int totalOnlyMustCount = (int) taskDTOList.stream()
            .filter(t ->
                !(t.getIsRequired() != null // 项目应用的 isRequired 为 null 需过滤
                    && t.getIsRequired().equals(GeneralJudgeEnum.NEGATIVE.getValue())
                    && t.getTaskType().equals(COURSE.getTaskType())
                )
            ).count();
        int totalOnlyMustFinishCount = (int) taskDTOList.stream()
            .filter(t ->
                !(t.getIsRequired() != null  // 项目应用的 isRequired 为 null 需过滤
                    && t.getIsRequired().equals(GeneralJudgeEnum.NEGATIVE.getValue())
                    && t.getTaskType().equals(COURSE.getTaskType())
                )
                    && t.getUserStatus().equals(ProjectConstant.USER_TASK_STATUS_COMPLETED)
            ).count();
        int finishOnlyMustRatioPer =
            totalOnlyMustCount == 0 ? 0
                : (int) (((double) totalOnlyMustFinishCount / (double) totalOnlyMustCount) * 100);// 去掉小数位
        dto.setTotalOnlyMustCount(totalOnlyMustCount);
        dto.setTotalOnlyMustFinishCount(totalOnlyMustFinishCount);
        dto.setFinishOnlyMustRatio(BigDecimal.valueOf(finishOnlyMustRatioPer));

        // 必修课程完成率 - 任务类型是课程且是必修
        int totalCourseMustCount = (int) taskDTOList.stream()
            .filter(t ->
                t.getIsRequired() != null // 项目应用的 isRequired 为 null 需过滤
                    && t.getIsRequired().equals(GeneralJudgeEnum.CONFIRM.getValue())
                    && t.getTaskType().equals(COURSE.getTaskType())
            ).count();
        int totalCourseMustFinishCount = (int) taskDTOList.stream()
            .filter(t ->
                t.getIsRequired() != null // 项目应用的 isRequired 为 null 需过滤
                    && t.getIsRequired().equals(GeneralJudgeEnum.CONFIRM.getValue())
                    && t.getTaskType().equals(COURSE.getTaskType())
                    && t.getUserStatus().equals(ProjectConstant.USER_TASK_STATUS_COMPLETED)
            ).count();
        int finishCourseMustRatioPer =
            totalCourseMustCount == 0 ? 0
                : (int) (((double) totalCourseMustFinishCount / (double) totalCourseMustCount) * 100);// 去掉小数位
        dto.setTotalCourseMustCount(totalCourseMustCount);
        dto.setTotalCourseMustFinishCount(totalCourseMustFinishCount);
        dto.setCourseMustFinishRatio(BigDecimal.valueOf(finishCourseMustRatioPer));

        // 返回结果
        return dto;
    }

    private void updateProjectFinish(String userId, List<LearnProjectTask> taskList,
        List<TaskDTO> taskDTOList, Progress updateProgressById, Project project, Progress progress) {
        // 更新学习项目任务完成状态
        if (!taskList.isEmpty()) {
            for (LearnProjectTask projectTask : taskList) {
                if (checkProjectTaskIsFinish(projectTask, userId)) {
                    updateTaskCompletedStatus(projectTask.getTaskContent(), userId);
                }
            }
            if (checkAppTaskIsFinish(taskDTOList)) {
                updateProgressById.setAppStatus(1);
                progressService.updateById(updateProgressById);
                if (updateProgressById.getStatus() == 1) {
                    // 只有学习项目才能完成培训活动信息
                    sendUserMapLearnFinishEvent(userId, project, project.getId());
                    mqProducer.sendMsg(new ResourceRecordSyncEvent(
                        new ResourceRecordSyncDTO(OperationEnum.CREATE, ResourceTypeEnum.RAPID_TRAIN.name(),
                            progress.getId(), project.getId(), userId, 1, userId, null, userId, progress.getUpdateTime()
                            , progress.getProgressPercent() == null ? new BigDecimal(0)
                            : progress.getProgressPercent())));
                }
            }
        }
    }

    private Integer getEnablePlayBack(int lvCount) {
        return lvCount > 0 ? GeneralJudgeEnum.CONFIRM.getValue() : GeneralJudgeEnum.NEGATIVE.getValue();
    }

    private Map<String, LecturerDTO> getTaskLecturer(List<LearnProjectTask> taskList) {
        return CollectionUtils.isEmpty(taskList) ? new HashMap<>() : lecturerFeign.getTaskLecturerMap(
            taskList.stream().map(LearnProjectTask::getId).collect(Collectors.toSet()));
    }

    private void perfectTaskDto(Project project, LearnProjectTask task, TaskDTO taskDTO,
        Map<String, Integer> taskSort) {
        if (task.getPreState() == 1) {
            TaskProgress taskProgress = taskProgressService.getByTaskId(task.getPredecessors());
            Task preTask = this.getById(task.getPredecessors());
            Integer userPreStatus = this.getUserStatus(taskProgress == null ? 0 : taskProgress.getIsFinish());
            taskDTO.setPreTaskName(preTask.getTaskName());
            taskDTO.setPreTaskId(preTask.getId());
            taskDTO.setPreStatus(userPreStatus);
        }

        boolean form = isForm(task.getTaskType());
        if (form) {
            taskDTO.setTaskType(ProjectTaskTypeEnum.FORM.getTaskType());
            Categorys category = categorysService.getById(task.getTaskType());
            if (category != null) {
                taskDTO.setFormFlag(category.getCategoryName());
            }
        }

        // 快速培训 设置任务排序
        if (Objects.equals(project.getProjectType(), ProjectConstant.PROJECT_TYPE_QUICK)) {
            taskDTO.setSort(taskSort.get(task.getTaskType()));
        }
        if (StringUtils.equals(task.getTaskType(), ProjectTaskTypeEnum.COURSE.getTaskType())) {
            Optional.ofNullable(
                    fileFeign.getImageFileNamePath(task.getTaskContent(), ImageBizType.CourseImgIcon.name()))
                .ifPresent(img -> taskDTO.setImage(img.getUrl()));
        }
    }

    private void checkExamTime(LearnProjectTask task, Map<String, ViewExamFeignDTO> examInfoMap) {
        //考试的任务要展示真实的考试时间
        if (Objects.equals(task.getTaskType(), ProjectTaskTypeEnum.EXAM.getTaskType())) {
            ViewExamFeignDTO examInfo = examInfoMap.get(task.getTaskContent());
            if (null != examInfo) {
                task.setStartTime(examInfo.getStartTime());
                task.setEndTime(examInfo.getEndTime());
            }
        }
    }

    private List<StageDTO> getStageList(String id) {
        List<Phase> stageList = phaseService.getListByProjectId(id);
        List<StageDTO> stageDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(stageList)) {
            stageDTOList = stageList.stream()
                .map(stage -> new StageDTO().setId(stage.getId()).setName(stage.getPhaseName()))
                .collect(Collectors.toList());
        }
        return stageDTOList;
    }

    private void checkProjectTime(Project project) {
        if (project.getProjectType() == ProjectConstant.PROJECT_TYPE_QUICK) {
            if (project.getStartTime().getTime() > System.currentTimeMillis()) {
                // 未开始
                throw new BusinessException(ProjectErrorNoEnum.ERROR_QUICK_PROJECT_IS_NOT_START);
            }
            if (project.getEndTime().getTime() < System.currentTimeMillis()) {
                // 已过期
                throw new BusinessException(ProjectErrorNoEnum.ERROR_QUICK_PROJECT_IS_END);
            }
        }
    }

    private Progress getProgress(String id, String userId) {
        LambdaQueryWrapper<Progress> wrapper = new LambdaQueryWrapper<Progress>().eq(Progress::getProId, id)
            .eq(Progress::getUserId, userId).eq(Progress::getIsDel, GeneralJudgeEnum.NEGATIVE.getValue());
        Progress progress = progressMapper.selectOne(wrapper);
        // 取消报名导致没有进度信息
        if (Objects.isNull(progress)) {
            throw new BusinessException(ProjectErrorNoEnum.ERR_RETRY_PROJECT);
        }
        return progress;
    }

    private void checkViewLimit(String id, ProjectTaskListQuery query, String userId) {
        if (Objects.equals(GeneralJudgeEnum.NEGATIVE.getValue(), query.getIsIgnoreView())
            && !resourceViewLimitService.checkViewLimit(id, LimitTable.ProjectViewLimit.name(), userId)) {
            throw new BusinessException(ErrorNoEnum.ERR_NO_POWER);
        }
    }

    /**
     * 获取快速培训任务排序
     *
     * @param id
     * @param project
     * @return
     */
    public Map<String, Integer> getTaskSort(String id, Project project) {
        Map<String, Integer> sortMap = new HashMap<>(4);
        List<String> sortList = new ArrayList<>();
        if (Objects.equals(project.getProjectType(), ProjectConstant.PROJECT_TYPE_QUICK)) {
            // ex: 课程,签到
            String quickProjectSort = quickProjectTaskSortService.getDetailByQuickProjectId(id);
            if (StringUtils.isNotBlank(quickProjectSort)) {
                sortList = Arrays.asList(quickProjectSort.split(","));
            }
            for (int i = 0; i < sortList.size(); i++) {
                // key : course value : 0
                sortMap.put(sortList.get(i), i);
            }
        }
        log.info("sortMap: " + sortMap);
        return sortMap;
    }

    /**
     * 设置任务应用信息
     *
     * @param resourceTypeMap
     * @param taskDTO
     */
    private void buildTaskAppInfo(Map<Integer, List<TaskAppResourceDTO>> resourceTypeMap, TaskDTO taskDTO) {
        Optional.ofNullable(resourceTypeMap).ifPresent(resourceMap -> {
            Optional.ofNullable(resourceMap.get(ProjectAppType.ASSESS.getNo())).ifPresent(resources ->
                    taskDTO.setEvaluationId(resources.get(0).getResourceId())
                        .setEvaluateStatus(getEvaluateStatus(resources))
                // 获取当前用户的评估 状态设置成已评估
            );
            Optional.ofNullable(resourceMap.get(ProjectAppType.SIGN.getNo())).ifPresent(resourceList -> {
                // 是否有签到
                for (TaskAppResourceDTO sign : resourceList) {
                    if (Objects.equals(sign.getType(), SignTypeEnum.SIGN_IN.getValue())) {
                        taskDTO.setSignInId(sign.getResourceId()).setSignInStatus(getSignInStatus(sign));
                        // 获取当前用户的签到 状态设置成已签到
                        continue;
                    }
                    if (Objects.equals(sign.getType(), SignTypeEnum.SIGN_OUT.getValue())) {
                        taskDTO.setSignOutId(sign.getResourceId()).setSignOutStatus(getSignOutStatus(sign));
                        // 获取当前用户的签到 状态设置成已签到
                    }
                }
            });
            Optional.ofNullable(resourceMap.get(ProjectAppType.EXAM.getNo())).ifPresent(resources ->
                taskDTO.setExamId(resources.get(0).getResourceId()).setExamStatus(getExamStatus(resources)));
        });
    }

    private Integer getSignInStatus(TaskAppResourceDTO sign) {
        return Objects.equals(sign.getIsFinish(), GeneralJudgeEnum.CONFIRM.getValue())
            ? GeneralJudgeEnum.CONFIRM.getValue() : GeneralJudgeEnum.NEGATIVE.getValue();
    }

    private Integer getSignOutStatus(TaskAppResourceDTO sign) {
        return Objects.equals(sign.getIsFinish(), GeneralJudgeEnum.CONFIRM.getValue())
            ? GeneralJudgeEnum.CONFIRM.getValue() : GeneralJudgeEnum.NEGATIVE.getValue();
    }

    private Integer getExamStatus(List<TaskAppResourceDTO> resources) {
        return Objects.equals(resources.get(0).getIsFinish(), GeneralJudgeEnum.CONFIRM.getValue())
            ? GeneralJudgeEnum.CONFIRM.getValue() : GeneralJudgeEnum.NEGATIVE.getValue();
    }

    private Integer getEvaluateStatus(List<TaskAppResourceDTO> resources) {
        return Objects.equals(resources.get(0).getIsFinish(), GeneralJudgeEnum.CONFIRM.getValue())
            ? GeneralJudgeEnum.CONFIRM.getValue() : GeneralJudgeEnum.NEGATIVE.getValue();
    }

    /**
     * 获取学习项目的任务资源Map
     *
     * @param id
     * @return
     */
    @Override
    public HashMap<String, Map<Integer, List<TaskAppResourceDTO>>> getProjectTaskResourceMap(String id) {
        // 查找任务签到
        Map<String, List<TaskAppResourceDTO>> taskSignMap = getTaskSignMap(id);
        // 查找评估
        Map<String, List<TaskAppResourceDTO>> taskEvaluationMap = getTaskEvaluationMap(id);
        // 查找课程任务考试
        Map<String, List<TaskAppResourceDTO>> taskExamMap = getTaskExamMap(id);
        // 获取所有的任务id
        Set<String> taskIds = new HashSet<>();
        Optional.ofNullable(taskSignMap).ifPresent(map -> taskIds.addAll(map.keySet()));
        Optional.ofNullable(taskEvaluationMap).ifPresent(map -> taskIds.addAll(map.keySet()));
        Optional.ofNullable(taskExamMap).ifPresent(map -> taskIds.addAll(map.keySet()));
        // Map<taskId, Map<resourceType, List<TaskAppResourceDTO>>
        HashMap<String, Map<Integer, List<TaskAppResourceDTO>>> map = new HashMap<>();
        taskIds.stream().forEach(taskId -> {
            HashMap<Integer, List<TaskAppResourceDTO>> resourceTypeMap = new HashMap<>();
            resourceTypeMap.put(ProjectAppType.SIGN.getNo(), taskSignMap.get(taskId));
            resourceTypeMap.put(ProjectAppType.ASSESS.getNo(), taskEvaluationMap.get(taskId));
            resourceTypeMap.put(ProjectAppType.EXAM.getNo(), taskExamMap.get(taskId));
            map.put(taskId, resourceTypeMap);
        });
        return map;
    }

    /**
     * 查找任务签到Map
     *
     * @param id
     * @return
     */
    private Map<String, List<TaskAppResourceDTO>> getTaskSignMap(String id) {
        // 查找任务签到
        List<TaskAppResourceDTO> taskSigns = signFeign.getTaskSignByProjectId(id);
        return taskSigns.stream().filter(sign -> StringUtils.isNotBlank(sign.getTaskId()))
            .collect(Collectors.groupingBy(TaskAppResourceDTO::getTaskId));
    }

    /**
     * 查找任务评估Map
     *
     * @param id
     * @return
     */
    private Map<String, List<TaskAppResourceDTO>> getTaskEvaluationMap(String id) {
        List<TaskAppResourceDTO> taskEvaluations = evaluationFeign.getPublishTaskEvaluationByProjectId(id);
        return taskEvaluations.stream().filter(evaluation -> StringUtils.isNotBlank(evaluation.getTaskId()))
            .collect(Collectors.groupingBy(TaskAppResourceDTO::getTaskId));
    }

    /**
     * 查找任务考试Map
     *
     * @param id
     * @return
     */
    private Map<String, List<TaskAppResourceDTO>> getTaskExamMap(String id) {
        List<App> examApps = appService.list(
            new LambdaQueryWrapper<App>().eq(App::getProjectId, id).ne(App::getTaskId, StringUtils.EMPTY)
                .eq(App::getResourceType, ProjectAppType.EXAM.getNo()));
        Map<String, String> examIdTaskIdMap = examApps.stream()
            .collect(Collectors.toMap(App::getResourceId, App::getTaskId, (v1, v2) -> {
                if (StringUtils.isBlank(v1) && StringUtils.isNotBlank(v2)) {
                    return v2;
                } else if (StringUtils.isNotBlank(v1) && StringUtils.isBlank(v2)) {
                    return v1;
                } else {
                    return v2;
                }
            }));
        List<String> examIds = CollectionUtils.isEmpty(examApps) ? null
            : examApps.stream().map(App::getResourceId).collect(Collectors.toList());
        List<TaskAppResourceDTO> taskExam =
            CollectionUtils.isEmpty(examIds) ? new ArrayList<>() : examFeign.getTaskExamByIds(examIds);
        return taskExam.stream().map(exam -> exam.setTaskId(examIdTaskIdMap.get(exam.getResourceId())))
            .filter(evaluation -> StringUtils.isNotBlank(evaluation.getTaskId()))
            .collect(Collectors.groupingBy(TaskAppResourceDTO::getTaskId));
    }

    @NotNull
    private Integer getUserStatus(Integer userStatus) {
        if (null == userStatus) {
            userStatus = ProjectConstant.USER_TASK_STATUS_NOT_START;
        } else if (userStatus == 0) {
            userStatus = ProjectConstant.USER_TASK_STATUS_UNDERWAY;
        } else if (userStatus == 1) {
            userStatus = ProjectConstant.USER_TASK_STATUS_COMPLETED;
        }
        return userStatus;
    }

    private List<LearnProjectTask> getTaskListByProId(String id, String stageId, Integer isComplete, Integer isExpired,
        String userId) {
        List<LearnProjectTask> taskList = baseMapper.getTaskListByProId(id, stageId, isComplete, isExpired, userId);

        List<String> signIds = getSingIdList(id);

        // 项目中的签到
        List<LearnProjectTask> signTaskIdList = appService.getSignTaskIdList(id);
        List<String> sigIdList = signTaskIdList.stream().map(LearnProjectTask::getResourceId)
            .filter(StringUtils::isNotBlank).collect(Collectors.toList());
        // 本次查询的签到
        Map<String, ProjectSignTaskDTO> projectSignMap = signFeign.getProjectSign(
            new ProjectSignTaskQuery().setUserId(userId).setIsComplete(isComplete).setSignIdList(sigIdList)
                .setStageId(stageId));
        if (!CollectionUtils.isEmpty(projectSignMap)) {

            Set<String> searchSignIdList = projectSignMap.keySet();
            List<String> finalSignIds = signIds;
            signTaskIdList = signTaskIdList.stream()
                .filter(e -> finalSignIds.contains(e.getResourceId()) && searchSignIdList.contains(e.getResourceId()))
                .collect(Collectors.toList());

            signTaskIdList.forEach(signTask -> Optional.ofNullable(projectSignMap.get(signTask.getResourceId()))
                .ifPresent(signInfo -> signTask.setPhaseId(signInfo.getPhaseId()).setTaskName(signInfo.getTaskName())
                    .setTaskType(signInfo.getTaskType()).setTaskContent(signInfo.getTaskContent())
                    .setStartTime(signInfo.getStartTime()).setEndTime(signInfo.getEndTime())
                    .setStatus(signInfo.getStatus()).setUserStatus(signInfo.getUserStatus())));
            // 数据整合
            taskList.addAll(signTaskIdList);
        }

        if (CollectionUtils.isEmpty(taskList)) {
            return taskList;
        }
        log.info("快速培训任务列表：{}", JsonUtil.objToJson(taskList));
        // 获取评估相关信息
        Set<String> evalIdSet = taskList.stream()
            .filter(task -> Objects.equals(task.getTaskType(), TASK_TYPE_EVALUATION))
            .map(LearnProjectTask::getTaskContent).collect(Collectors.toSet());

        if (CollectionUtils.isNotEmpty(evalIdSet)) {
            Map<String, UserEvalStatusInfoDTO> userEvalStatusInfo = evaluationFeign.getUserEvalStatusInfo(
                new UserEvalStatusInfoQuery().setEvalIds(evalIdSet).setUserId(userId).setStageId(stageId)
                    .setIsComplete(isComplete).setIsExpired(isExpired));
            // 过滤掉不符合条件的taskList评估信息
            taskList = taskList.stream().filter(task -> {
                if (Objects.equals(task.getTaskType(), TASK_TYPE_EVALUATION)) {
                    UserEvalStatusInfoDTO dto = userEvalStatusInfo.get(task.getTaskContent());
                    return Objects.nonNull(dto);
                }
                return true;
            }).map(task -> {
                if (Objects.equals(task.getTaskType(), TASK_TYPE_EVALUATION)) {
                    BeanUtils.copyProperties(userEvalStatusInfo.get(task.getTaskContent()), task);
                }
                return task;
            }).collect(Collectors.toList());
        }
        log.info("过滤评估任务后的列表：{}", JsonUtil.objToJson(taskList));

        final Map<String, List<LearnProjectTask>> taskMap = taskList.stream()
            .filter(t -> StringUtils.isNotBlank(t.getTaskContent()))
            .collect(Collectors.groupingBy(LearnProjectTask::getTaskType));

        List<String> invalidId = getinvalidId(taskMap);
        Map<String, Integer> liveCountMap = getLiveCountMap(taskMap);
        Project project = projectMapper.selectById(id);
        log.info("taskList ----> {}", JsonUtil.objToJson(taskList));
        List<LearnProjectTask> lastTaskList = getLastTaskList(project, taskList, invalidId);
        setLiveCount(lastTaskList, liveCountMap);
        return lastTaskList;
    }

    private List<LearnProjectTask> getLastTaskList(Project project, List<LearnProjectTask> taskList,
        List<String> invalidId) {
        if (project != null && Objects.equals(project.getProjectType(), ProjectConstant.PROJECT_COURSE_TASK)) {
            // 线上课程按sort排序
            return taskList.stream().filter(l -> {
                    if (!isForm(l.getTaskType())) {
                        return StringUtils.isNotBlank(l.getTaskContent()) && !invalidId.contains(l.getTaskContent());
                    }
                    return true;
                }).sorted(Comparator.comparing(LearnProjectTask::getSort).thenComparing(LearnProjectTask::getStartTime))
                .collect(Collectors.toList());
        } else {
            return taskList.stream().filter(l -> {
                if (!isForm(l.getTaskType())) {
                    return StringUtils.isNotBlank(l.getTaskContent()) && !invalidId.contains(l.getTaskContent());
                }
                return true;
            }).sorted(Comparator.comparing(LearnProjectTask::getStartTime)).collect(Collectors.toList());
        }
    }

    private void setLiveCount(List<LearnProjectTask> lastTaskList, Map<String, Integer> liveCountMap) {
        lastTaskList.forEach(l -> {
            if (l.getTaskType() != null && l.getTaskType().equals(ProjectTaskTypeEnum.LIVE.getTaskType())) {
                Integer lvCount = liveCountMap.get(l.getTaskContent());
                Optional.ofNullable(lvCount).ifPresent(l::setLvCount);
            }
        });
    }

    private List<String> getSingIdList(String id) {
        // 【适应学习项目任务中仅有签到的场景，优先处理签到数据 ---- 2024-04-01 李恒 更改代码执行顺序】因签到在市场运营项目，需另查
        SignListQuery signListQuery = new SignListQuery();
        signListQuery.setProjectId(id);
        signListQuery.setIsPublish(PublishStatusEnum.IS_PUBLISH.getValue());
        signListQuery.setPageNo(1);
        signListQuery.setPageSize(20);
        // 市场模块中的该项目下的签到
        PageInfo<SignPageDTO> signPage = signFeign.getSignPage(signListQuery);
        List<SignPageDTO> list = signPage.getList();
        if (!CollectionUtils.isEmpty(list)) {
            return list.stream().map(SignPageDTO::getId).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    private Map<String, Integer> getLiveCountMap(Map<String, List<LearnProjectTask>> taskMap) {
        // 获取直播回复视频数量
        if (taskMap.get(ProjectTaskTypeEnum.LIVE.getTaskType()) == null) {
            return new HashMap<>();
        }
        Set<String> liveIdList = taskMap.get(ProjectTaskTypeEnum.LIVE.getTaskType()).stream()
            .map(LearnProjectTask::getTaskContent).collect(Collectors.toSet());
        Map<String, Integer> liveCountMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(liveIdList)) {
            liveCountMap = liveFeign.getLiveCount(liveIdList);
        }
        return liveCountMap;
    }

    private List<String> getinvalidId(Map<String, List<LearnProjectTask>> taskMap) {
        // 无效的学习项目任务
        CompletableFuture<List<String>> projectFuture = getProjectFuture(taskMap);

        // 无效的课程任务
        CompletableFuture<List<String>> courseFuture = getCourseFuture(taskMap);

        // 无效的直播任务
        CompletableFuture<List<String>> liveFuture = getLiveFuture(taskMap);

        // 无效的考试和练习
        CompletableFuture<List<String>> examFuture = getExamFuture(taskMap);

        // 无效的调研任务
        CompletableFuture<List<String>> surveyFuture = getSurveyFuture(taskMap);

        List<String> invalidProjectId;
        List<String> invalidCourseId;
        List<String> invalidLiveId;
        List<String> invalidExamId;
        List<String> invalidSurveyId;
        List<String> invalidId;

        try {
            // 线程阻塞至全部完成
            List<CompletableFuture<? extends Object>> completableFutures = List.of(projectFuture, courseFuture,
                liveFuture,
                examFuture, surveyFuture);
            if (!CollectionUtils.isEmpty(completableFutures)) {
                CompletableFuture<Void> voidCompletableFuture = CompletableFuture.allOf(
                    completableFutures.toArray(CompletableFuture[]::new));
                voidCompletableFuture.get(30, TimeUnit.SECONDS);
            }

            invalidProjectId = projectFuture.get();
            invalidCourseId = courseFuture.get();
            invalidLiveId = liveFuture.get();
            invalidExamId = examFuture.get();
            invalidSurveyId = surveyFuture.get();
            invalidId = Stream.of(invalidProjectId, invalidCourseId, invalidLiveId, invalidExamId,
                invalidSurveyId).flatMap(Collection::stream).collect(Collectors.toList());

        } catch (InterruptedException e) {
            log.error("Interrupted!", e);
            Thread.currentThread().interrupt();
            throw new BusinessException(ErrorNoEnum.ERR_SERVER);
        } catch (ExecutionException | TimeoutException e) {
            log.error("ExecutionException | TimeoutException!", e);
            throw new BusinessException(ErrorNoEnum.ERR_SERVER);
        }
        return invalidId;
    }

    private CompletableFuture<List<String>> getSurveyFuture(Map<String, List<LearnProjectTask>> taskMap) {
        return CompletableFuture.supplyAsync(() -> {
            if (taskMap.get(ProjectTaskTypeEnum.SURVEY.getTaskType()) == null) {
                return new ArrayList<>();
            }
            Set<String> surveyIdList = taskMap.get(ProjectTaskTypeEnum.SURVEY.getTaskType()).stream()
                .map(LearnProjectTask::getTaskContent).collect(Collectors.toSet());
            List<String> invalidSurveyId = new ArrayList<>();
            if (!CollectionUtils.isEmpty(surveyIdList)) {
                invalidSurveyId = surveyFeign.getInvalidSurveyId(surveyIdList);
            }
            return invalidSurveyId;
        }, asyncThreadPool);
    }

    private CompletableFuture<List<String>> getExamFuture(Map<String, List<LearnProjectTask>> taskMap) {
        return CompletableFuture.supplyAsync(() -> {
            Set<String> resourceIdList = new HashSet<>();
            Optional.ofNullable(taskMap.get(EXAM.getTaskType())).ifPresent(examList -> resourceIdList.addAll(
                examList.stream().map(LearnProjectTask::getTaskContent).filter(StringUtils::isNotBlank)
                    .collect(Collectors.toSet())));
            Optional.ofNullable(taskMap.get(EXERCISE.getTaskType())).ifPresent(exerciseList -> resourceIdList.addAll(
                exerciseList.stream().map(LearnProjectTask::getTaskContent).filter(StringUtils::isNotBlank)
                    .collect(Collectors.toSet())));

            List<String> invalidExamId = new ArrayList<>();
            if (!CollectionUtils.isEmpty(resourceIdList)) {
                invalidExamId = examFeign.getInvalidExamId(resourceIdList);
            }
            return invalidExamId;
        }, asyncThreadPool);
    }

    private CompletableFuture<List<String>> getLiveFuture(Map<String, List<LearnProjectTask>> taskMap) {
        return CompletableFuture.supplyAsync(() -> {
            if (taskMap.get(ProjectTaskTypeEnum.LIVE.getTaskType()) == null) {
                return new ArrayList<>();
            }
            Set<String> liveIdList = taskMap.get(ProjectTaskTypeEnum.LIVE.getTaskType()).stream()
                .map(LearnProjectTask::getTaskContent).collect(Collectors.toSet());
            List<String> invalidLiveId = new ArrayList<>();
            if (!CollectionUtils.isEmpty(liveIdList)) {
                invalidLiveId = liveFeign.getInvalidLiveId(liveIdList);
            }
            return invalidLiveId;
        }, asyncThreadPool);
    }

    private CompletableFuture<List<String>> getCourseFuture(Map<String, List<LearnProjectTask>> taskMap) {
        return CompletableFuture.supplyAsync(() -> {
            if (taskMap.get(ProjectTaskTypeEnum.COURSE.getTaskType()) == null) {
                return new ArrayList<>();
            }
            Set<String> courseIdList = taskMap.get(ProjectTaskTypeEnum.COURSE.getTaskType()).stream()
                .map(LearnProjectTask::getTaskContent).collect(Collectors.toSet());
            List<String> invalidCourseId = new ArrayList<>();
            if (!CollectionUtils.isEmpty(courseIdList)) {
                invalidCourseId = courseFeign.getInvalidCourseId(courseIdList);
            }
            return invalidCourseId;
        }, asyncThreadPool);
    }

    private CompletableFuture<List<String>> getProjectFuture(Map<String, List<LearnProjectTask>> taskMap) {
        return CompletableFuture.supplyAsync(() -> {
            if (taskMap.get(ProjectTaskTypeEnum.PROJECT.getTaskType()) == null) {
                return new ArrayList<>();
            }
            Set<String> projectIdList = taskMap.get(ProjectTaskTypeEnum.PROJECT.getTaskType()).stream()
                .map(LearnProjectTask::getTaskContent).collect(Collectors.toSet());
            List<String> invalidProjectId = new ArrayList<>();
            if (!CollectionUtils.isEmpty(projectIdList)) {
                invalidProjectId = projectMapper.getInvalidProjectId(projectIdList);
            }
            return invalidProjectId;
        }, asyncThreadPool);
    }

    @Override
    public Integer getProjectTaskCount(String id) {
        LambdaQueryWrapper<Task> wrapper = new LambdaQueryWrapper<Task>().select(Task::getId).eq(Task::getProId, id)
            .eq(Task::getIsDel, DelEnum.NOT_DELETE.getValue()).eq(Task::getIsPublish, PublishEnum.PUBLISHED.getValue());
        long count = this.count(wrapper);
        return Math.toIntExact(count);
    }

    @Override
    public void saveProjectRecordTime(String id, String taskId, String activityId) {
        String userId = UserThreadContext.getUserId();
        Date newDate = new Date();
        log.info("任务实际开始时间:" + newDate);
        progressMapper.saveProjectRecordTime(userId, id, taskId, activityId, newDate);
    }

    @Override
    public List<TaskProgress> getTaskProgressByProIdAndUserId(String proId, String userId) {
        LambdaQueryWrapper<Task> taskQueryWrapper = new LambdaQueryWrapper<>();
        taskQueryWrapper.eq(Task::getProId, proId);
        return taskProgressService.getByProIdAndUserId(userId,
            list(taskQueryWrapper).stream().map(Task::getId).filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet()));
    }

    @Override
    public void saveTaskContent(SaveTaskContentDTO saveTaskContentDTO) {

        Task task = this.getById(saveTaskContentDTO.getProjectTaskId());

        if (null == task) {
            throw new BusinessException(ProjectErrorNoEnum.PROJECT_TASK_NOT_EXIST);
        }

        if (task.getCreateType() != ProjectConstant.CREATE_TYPE_DIRECT) {
            throw new BusinessException(ProjectErrorNoEnum.PROJECT_TASK_CREATE_TYPE_ERROR);
        }

        if (isForm(task.getTaskType())) {
            throw new BusinessException(ProjectErrorNoEnum.PROJECT_TASK_TYPE_ERROR);
        }
        // 保存任务内容 时间
        Objects.requireNonNull(ProjectTaskTypeEnum.getStrategy(task.getTaskType()))
            .updateResourceTime(saveTaskContentDTO.getTaskContent(), task);

        taskDao.updateTask(new Task().setId(task.getId()).setTaskName(task.getTaskName())
            .setTaskContent(saveTaskContentDTO.getTaskContent()));

        // 日程任务处理
        if (StringUtils.isNotBlank(task.getScheduleId())) {
            ScheduleTool scheduleTool = new ScheduleTool().setProjectId(task.getProId()).setPhaseId(task.getPhaseId())
                .setTaskId(task.getId()).setResourceId(saveTaskContentDTO.getTaskContent())
                .setIsAvailable(AvailableEnum.AVAILABLE.getValue());
            scheduleToolService.saveOrUpdateByTask(scheduleTool);
            if (!COURSE.getTaskType().equals(task.getTaskType()) && !EXAM.getTaskType().equals(task.getTaskType())) {
                // 课程类型需要有课件才能发布
                // 考试类型使用自动发布，这里不作发布
                PublishDTO publishDTO = new PublishDTO().setIds(List.of(task.getId()))
                    .setIsPublish(PublishEnum.PUBLISHED.getValue());
                getBean().publish(publishDTO);
            }
        }
    }

    @Override
    public Collection<String> getProjectLecturerByProjectId(String projectId) {
        LambdaQueryWrapper<Task> query = new LambdaQueryWrapper<>();
        query.eq(Task::getProId, projectId);
        query.eq(Task::getTaskType, COURSE.getTaskType());
        query.select(Task::getTaskContent);
        List<Task> list = getBaseMapper().selectList(query);
        if (!CollectionUtils.isEmpty(list)) {
            return list.stream().map(Task::getTaskContent).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        }
        return new ArrayList<>();
    }

    @Override
    public Collection<LearningCalendarTaskDTO> findLearningCalenderScheduleList(
        LearningCalendarScheduleQuery learningCalendarScheduleQuery) {

        Date date = learningCalendarScheduleQuery.getDate();
        if (date == null) {
            throw new BusinessException(ProjectErrorNoEnum.QUERY_PARAMS_IS_NULL);
        }

        Date now = new Date();
        //时间判断，判断是查询今天，还是以往的日程
        String dateStr = DateUtil.getYmdStr(date);
        learningCalendarScheduleQuery.setWhichDay(dateStr);
        String nowStr = DateUtil.getYmdStr(now);
        if (dateStr.equals(nowStr)) {
            learningCalendarScheduleQuery.setToday(nowStr);
        }
        LearningCalendarTaskQuery learningCalendarTaskQuery = new LearningCalendarTaskQuery();
        BeanUtils.copyProperties(learningCalendarScheduleQuery, learningCalendarTaskQuery);
        return findLearningCalenderTaskList(learningCalendarTaskQuery);
    }

    @Override
    public Collection<LearningCalendarTaskDTO> findLearningCalenderTaskList(
        LearningCalendarTaskQuery learningCalendarTaskQuery) {

        learningCalendarTaskQuery.setUserId(UserThreadContext.getUserId());

        List<LearningCalendarTaskDTO> result = new ArrayList<>();

        //查询签到
        List<LearningCalendarTaskDTO> taskSignDTOList = signFeign.findLearningCalenderSignTaskList(
            learningCalendarTaskQuery);

        //查询考试
        List<LearningCalendarTaskDTO> taskExamDTOList = examFeign.findLearningCalenderExamTaskList(
            learningCalendarTaskQuery);

        //查询调研
        List<LearningCalendarTaskDTO> taskSurveyDTOList = surveyFeign.findLearningCalenderSurveyTaskList(
            learningCalendarTaskQuery);

        //查询直播
        List<LearningCalendarTaskDTO> taskLiveDTOList = liveFeign.findLearningCalenderLiveTaskList(
            learningCalendarTaskQuery);

        result.addAll(taskSignDTOList);
        result.addAll(taskExamDTOList);
        result.addAll(taskSurveyDTOList);
        result.addAll(taskLiveDTOList);

        Collections.sort(result);
        return result;
    }

    @Override
    public void syncSaveViewLimit(String projectId, List<Task> taskList) {
        ViewLimitBaseInfoDTO viewLimitBaseInfo = projectViewLimitComponent.getViewLimitBaseInfo(projectId);
        Long programmeId = viewLimitBaseInfo.getProgrammeId();
        taskList.forEach(task -> {
            String resourceId = task.getTaskContent();
            if (StringUtils.isNotBlank(resourceId)) {
                if (ProjectTaskTypeEnum.isCourse(task.getTaskType())) {
                    handleCourse(programmeId, task, resourceId);
                } else if (Objects.equals(ProjectTaskTypeEnum.LIVE.getTaskType(), task.getTaskType())) {
                    // 保存直播的下发
                    liveFeign.saveSyncLiveViewLimit(resourceId, programmeId);
                } else if (Objects.equals(EXAM.getTaskType(), task.getTaskType()) || Objects.equals(
                    EXERCISE.getTaskType(), task.getTaskType())) {
                    // 同步考试/练习的下发
                    examFeign.saveSyncExamViewLimit(resourceId, programmeId);
                } else if (Objects.equals(ProjectTaskTypeEnum.SURVEY.getTaskType(), task.getTaskType())) {
                    // 保存调研的下发
                    surveyFeign.saveSyncSurveyViewLimit(resourceId, programmeId);
                }
            }
        });
    }

    private void handleCourse(Long programmeId, Task task, String resourceId) {
        // 同步课程的下发
        courseFeign.saveSyncCourseViewLimit(resourceId, programmeId);
        //课程可能存在考试，还需要更新对应考试的下发
        String taskExamId = taskExamService.getTaskExamId(task.getId());
        if (StringUtils.isBlank(taskExamId)) {
            return;
        }
        ExamInfoDTO examInfo = examFeign.getById(taskExamId);
        if (examInfo.getSourceType().equals("2")) {
            return;
        }
        examFeign.saveSyncExamViewLimit(taskExamId, programmeId);
    }

    @Override
    public List<Task> getProjectTaskListByProId(String projectId) {
        return list(new LambdaQueryWrapper<Task>().eq(Task::getProId, projectId).orderByAsc(Task::getSort));
    }

    @Override
    public void updateTaskCompletedStatus(String taskContent, String userId) {
        log.info("---updateTaskCompletedStatus---taskContent:{}--userId:{}", taskContent, userId);
        List<Task> taskList = lambdaQuery().eq(Task::getTaskContent, taskContent).list();
        updateTaskCompletedStatus(taskList, userId);
    }

    @Override
    public void updateTaskCompletedStatus(List<Task> taskList, String userId) {
        if (CollectionUtils.isEmpty(taskList)) {
            return;
        }
        // 更新学习项目的状态
        taskList.forEach(task -> {
            if (task == null) {
                log.info("task == null");
                return;
            }
            Date now = new Date();
            String projectId = task.getProId();
            String taskId = task.getId();
            TaskProgress taskProgress = taskProgressService.lambdaQuery().eq(TaskProgress::getTaskId, taskId)
                .eq(TaskProgress::getUserId, userId).one();
            if (taskProgress == null) {
                log.info("taskProgress == null");
                return;
            }

            // 更新用户学习项目学习情况表
            updateUserStudyCondition(userId, task, projectId, taskProgress, now);

            Progress progress = progressService.lambdaQuery().eq(Progress::getProId, projectId)
                .eq(Progress::getUserId, userId).one();
            if (projectMapper.checkProjectTaskProgressIsAllFinish(projectId, userId) == 0) {
                // 如果学习项目的任务，对这个人的任务都已经完成，则将学习项目置为完成
                userFinishProgress(userId, progress, now, projectId);
            } else {
                // 如果没完成则将更新 project_progress 进度字段
                BigDecimal progressById = progressService.getProgressById(projectId);
                progress.setProgressPercent(progressById);
            }
            progressService.updateById(progress);
        });
    }

    private void updateUserStudyCondition(String userId, Task task, String projectId, TaskProgress taskProgress,
        Date now) {
        ProjectUserStudyCondition userStudyCondition = projectUserStudyConditionService.lambdaQuery()
            .eq(ProjectUserStudyCondition::getUserId, userId)
            .eq(ProjectUserStudyCondition::getProjectId, projectId)
            .one();
        log.info("userStudyCondition: {}", userStudyCondition);
        if (null == userStudyCondition) {
            return;
        }
        Integer isFinish = taskProgress.getIsFinish();
        if (isFinish != null && isFinish == TASK_IS_COMPLETED_NO) {
            // 更新任务进度
            taskProgress.setActualStartTime(Optional.ofNullable(taskProgress.getActualStartTime()).orElse(now));
            taskProgress.setActualEndTime(Optional.ofNullable(taskProgress.getActualEndTime()).orElse(now));
            taskProgress.setIsFinish(TASK_IS_COMPLETED_YES);
            taskProgressService.updateById(taskProgress);

            // 更新用户项目学习情况 这里只处理 课程,考试,调研 三种类型任务
            switch (task.getTaskType()) {
                case TASK_TYPE_COURSE:
                    if (1 == task.getIsRequired()) {
                        userStudyCondition.setRequiredNum(userStudyCondition.getRequiredNum() + 1);
                    } else {
                        userStudyCondition.setElectiveNum(userStudyCondition.getElectiveNum() + 1);
                    }
                    break;
                case TASK_TYPE_EXAM:
                    userStudyCondition.setExamNum(userStudyCondition.getExamNum() + 1);
                    break;
                case TASK_TYPE_SURVEY:
                    userStudyCondition.setSurveyNum(userStudyCondition.getSurveyNum() + 1);
                    break;
                default:
                    log.info("{},该任务类型不需要统计!", task.getTaskType());
                    break;
            }
            projectUserStudyConditionService.updateById(userStudyCondition);
        }
    }

    /**
     * 用户完成学习项目
     *
     * @param userId    用户id
     * @param progress  完成进度记录
     * @param now       当前事件
     * @param projectId 完成项目id
     */
    private void userFinishProgress(String userId, Progress progress, Date now, String projectId) {
        if (progress.getStatus() != null && progress.getStatus() == 0) {
            progress.setStatus(1);
            progress.setFinishTime(now);
            // 设置进度 100
            progress.setProgressPercent(new BigDecimal(100));

            // 判断项目是否头条项目，是的话完成
            Project project = projectMapper.selectById(projectId);
            if (project != null) {
                // 课程学习任务不需要激励
                if (!Objects.equals(project.getProjectType(), ProjectConstant.PROJECT_COURSE_TASK)) {
                    // 发送完成学习项目事件
                    mqProducer.sendMsg(new ExcitationMQEvent(
                        new ExcitationMQEventDTO().setEventId(ExcitationEventEnum.finishProject.name())
                            .setTargetName(project.getProName()).setTargetId(project.getId())
                            .setUserId(userId)));
                }
                // 培训活动（学员身份参加）完成事件
                sendUserMapLearnFinishEvent(userId, project, projectId);

                // 学习项目完成发送消息，查看是否是某个项目的引用项目，去更新
                mqProducer.send(new ProjectCompletedEvent(projectId, userId, userId));
                // 学习项目学习完成
                sendProjectCompleteEvent(project, progress, userId, now);
            }
        }
    }

    /**
     * 发送培训活动（学员身份参加）完成事件
     *
     * @param userId    完成人
     * @param project   完成项目
     * @param projectId 完成学习项目id
     */
    private void sendUserMapLearnFinishEvent(String userId, Project project, String projectId) {
        // 只有学习项目才能完成培训活动信息
        if (project.getProjectType().equals(ProjectConstant.PROJECT_TYPE_DEFAULT)) {
            // 发送培训活动（学员身份参加）完成事件
            mqProducer.sendMsg(
                new MapOperationLearningFinishEvent(projectId, userId, MapBehaviorEnum.USER.getCode(),
                    ResourceTypeEnum.PROJECT.getType(), projectId));
        }
    }

    private void sendProjectCompleteEvent(Project project, Progress progress, String userId, Date now) {
        ResourceTypeEnum resourceTypeEnum = ProjectBizImpl.getResourceTypeEnum(project.getType(),
            project.getProjectType());
        if (resourceTypeEnum == null) {
            log.info("学习项目资源业务类型为空");
            return;
        }
        // 培训班、周期项目、线上课程学习完成
        switch (resourceTypeEnum) {
            case TRAIN_COURSE:
            case PERIODIC_PROJECT:
            case COURSE_LEARNING_TASK:
                mqProducer.sendMsg(new ResourceRecordSyncEvent(
                    new ResourceRecordSyncDTO(OperationEnum.CREATE, resourceTypeEnum.name(), progress.getId(),
                        project.getId(), userId, 1, userId, progress.getCreateTime(), userId, now
                        , progress.getProgressPercent() == null ? new BigDecimal(0) : progress.getProgressPercent())));
                break;
            case RAPID_TRAIN:
                // 快速培训完成条件 status = 1 appStatus = 1
                Integer status = progress.getStatus();
                Integer appStatus = progress.getAppStatus();
                if (Objects.equals(status, 1) && Objects.equals(appStatus, 1)) {
                    mqProducer.sendMsg(new ResourceRecordSyncEvent(
                        new ResourceRecordSyncDTO(OperationEnum.CREATE, resourceTypeEnum.name(), progress.getId(),
                            project.getId(), userId, 1, userId, progress.getCreateTime(), userId, now
                            , progress.getProgressPercent() == null ? new BigDecimal(0)
                            : progress.getProgressPercent())));
                }
                break;
            default:
                break;
        }
    }

    private boolean checkProjectTaskIsFinish(LearnProjectTask task, String userId) {
        boolean finish = false;
        // 校验课程是否学完
        if (Objects.equals(task.getTaskType(), ProjectTaskTypeEnum.COURSE.getTaskType())) {
            UserCourseRecordDTO userCourseRecord = courseFeign.getUserCourseRecord(userId, task.getTaskContent());
            if (Objects.nonNull(userCourseRecord)) {
                finish = Objects.equals(userCourseRecord.getIsLearned(), 1);
            }
        }

        // 校验考试是否答题且已提交，需要改卷的要完成改卷
        if (Objects.equals(task.getTaskType(), ProjectTaskTypeEnum.EXAM.getTaskType())) {
            Long examAnswerRecordCount = answerRecordFeign.getExamAnswerRecordCount(userId, task.getTaskContent());
            finish = examAnswerRecordCount > 0;
        }

        // 校验练习是否答题且已提交，需要改卷的要完成改卷
        if (Objects.equals(task.getTaskType(), ProjectTaskTypeEnum.EXERCISE.getTaskType())) {
            Long exerciseAnswerRecordCount = exerciseFeign.getExerciseAnswerRecordCount(userId, task.getTaskContent());
            finish = exerciseAnswerRecordCount > 0;
        }

        // 校验调研是否完成
        if (Objects.equals(task.getTaskType(), ProjectTaskTypeEnum.SURVEY.getTaskType())) {
            Integer surveyRecordCount = surveyFeign.getSurveyRecordCount(userId, task.getTaskContent());
            if (surveyRecordCount != null) {
                finish = surveyRecordCount > 0;
            }
        }

        // 校验直播是否完成
        if (Objects.equals(task.getTaskType(), ProjectTaskTypeEnum.LIVE.getTaskType())) {
            Integer liveRecordCount = liveFeign.getLiveRecordCount(userId, task.getTaskContent());
            if (liveRecordCount != null) {
                finish = liveRecordCount > 0;
            }
        }

        // 校验学习项目是否完成
        if (Objects.equals(task.getTaskType(), ProjectTaskTypeEnum.PROJECT.getTaskType())) {
            Integer status = projectMapper.getProjectProgressStatus(userId, task.getTaskContent());
            if (status != null) {
                finish = status > 0;
            }
        }

        // 校验实操是否完成
        finish = practicalOperationIsFinish(task, userId, finish);

        return finish;
    }

    private boolean practicalOperationIsFinish(LearnProjectTask task, String userId, boolean finish) {
        if (Objects.equals(task.getTaskType(), ProjectTaskTypeEnum.PRACTICAL_OPERATION.getTaskType())) {
            Integer status = practicalOperationUserService.getCount(userId, task.getTaskContent());
            if (status != null) {
                finish = status > 0;
            }
        }
        return finish;
    }

    private boolean checkAppTaskIsFinish(List<TaskDTO> taskDTOList) {
        int count = 0;
        int finishCount = 0;
        for (TaskDTO task : taskDTOList) {
            // 校验签到是否完成
            if (Objects.equals(task.getTaskType(), ProjectTaskTypeEnum.SIGN.getTaskType())) {
                count++;
                if (2 == task.getUserStatus()) {
                    finishCount++;
                }
            }
            // 校验评估是否完成
            if (Objects.equals(task.getTaskType(), ProjectTaskTypeEnum.EVALUATION.getTaskType())) {
                count++;
                if (2 == task.getUserStatus()) {
                    finishCount++;
                }
            }
        }
        return count == finishCount;
    }

    @Override
    public PageInfo<ProjectTaskBaseDTO> getResourceIdList(ProjectTaskQuery projectTaskQuery) {
        return PageMethod.startPage(projectTaskQuery.getPageNo(), projectTaskQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.getResourceIdList(projectTaskQuery));
    }

    @Override
    public ProjectTaskBaseDTO getTaskInfo(String taskId) {
        return baseMapper.getTaskInfo(taskId);
    }

    @Override
    public PageInfo<ProjectTaskInfoDTO> getSelfBuildTaskList(TaskOrAppListQuery taskOrAppListQuery) {
        Project project = projectMapper.selectById(taskOrAppListQuery.getId());
        if (!StringUtils.equals(project.getLeader(), UserThreadContext.getUserId())) {
            taskOrAppListQuery.setIsNotLeader(true);
        }
        PageInfo<ProjectTaskInfoDTO> pageInfo = PageMethod.startPage(taskOrAppListQuery.getPageNo(),
                taskOrAppListQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.getSelfBuildTaskList(taskOrAppListQuery));
        AtomicReference<Integer> taskIndex = new AtomicReference<>(
            (taskOrAppListQuery.getPageNo() - 1) * taskOrAppListQuery.getPageSize());
        pageInfo.getList().forEach(dto -> {
            taskIndex.set(taskIndex.get() + 1);
            dto.setIndex(taskIndex.get());
        });
        return pageInfo;
    }

    @Override
    public void checkTaskTime(String taskId) {
        Task task = getById(taskId);
        if (task == null) {
            throw new BusinessException(ProjectErrorNoEnum.TASK_NOT_EXIST);
        }
        if (task.getStartTime() != null && task.getStartTime().compareTo(new Date()) > 0) {
            throw new BusinessException(ProjectErrorNoEnum.TASK_NOT_START);
        }
        if (task.getEndTime() != null && task.getEndTime().compareTo(new Date()) < 0) {
            throw new BusinessException(ProjectErrorNoEnum.TASK_HAS_ENDED);
        }
    }

    @Override
    public Map<String, Integer> getUserFinish(Task task, List<String> userIdList) {
        Map<String, Integer> map = new HashMap<>();
        ProjectTaskTypeEnum projectTaskTypeEnum = ProjectTaskTypeEnum.getItem(task.getTaskType());
        if (Objects.isNull(projectTaskTypeEnum)) {
            projectTaskTypeEnum = FORM;
        }
        ResourceUserQuery resourceUserQuery = new ResourceUserQuery().setTaskId(task.getId())
            .setResourceId(task.getTaskContent()).setUserIdList(userIdList);
        switch (projectTaskTypeEnum) {
            case COURSE:
                map = courseFeign.getCourseFinish(resourceUserQuery);
                break;
            case EXAM:
                map = examFeign.getExamFinish(resourceUserQuery);
                break;
            case EXERCISE:
                map = exerciseFeign.getExerciseFinish(resourceUserQuery);
                break;
            case SURVEY:
                map = surveyFeign.getSurveyFinish(resourceUserQuery);
                break;
            case LIVE:
                map = liveFeign.getLiveFinish(resourceUserQuery);
                break;
            case PROJECT:
                map = progressService.list(
                        new LambdaQueryWrapper<Progress>().eq(Progress::getProId, resourceUserQuery.getResourceId())
                            .in(Progress::getUserId, resourceUserQuery.getUserIdList())).stream()
                    .collect(Collectors.toMap(Progress::getUserId, Progress::getStatus, (k1, k2) -> k1));
                break;
            case FORM:
                map = formCycleRecordService.getFormFinish(resourceUserQuery);
                break;
            default:
        }
        return map;
    }

    @Override
    public void checkActivityFinish(TaskProgress taskProgress, Map<String, Integer> userResourceMap) {
        Optional.ofNullable(userResourceMap.get(taskProgress.getUserId())).ifPresent(isFinish -> {
            Date now = new Date();
            taskProgress.setIsFinish(isFinish);
            if (GeneralJudgeEnum.CONFIRM.getValue().equals(isFinish)) {
                Date actualStartTime = taskProgress.getActualStartTime();
                if (actualStartTime == null) {
                    taskProgress.setActualStartTime(now);
                }

                Date actualEndTime = taskProgress.getActualEndTime();
                if (actualEndTime == null) {
                    taskProgress.setActualEndTime(now);
                }
            }
        });
    }

    @Override
    public PageInfo<UserAbilityPracticalListDTO> getProjectTaskByUserIdList(AbilityPracticalListQuery query) {
        return PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> baseMapper.getProjectTaskByUserIdList(query.getUserId(), query.getProjectIdList()));
    }

    @Override
    public PageInfo<UserAbilityPracticalListDTO> getUserAbilitySuperviseProjectList(AbilityPracticalListQuery query) {
        return PageMethod.startPage(query.getPageNo(), query.getPageSize()).doSelectPageInfo(
            () -> baseMapper.getUserAbilitySuperviseProjectList(query.getUserId(), query.getProjectIdList()));
    }

    @Override
    public void updateUserProjectProgress(String proId) {
        // 更新已完成项目成员的项目状态
        progressMapper.updateCompletedProProgressToOnGoing(proId);
        //更新所有学员的项目进度百分比
        List<Progress> progressList = progressService.getListByProjectId(proId);
        if (!progressList.isEmpty()) {
            Project project = projectDao.projectExist(proId);
            for (Progress progress : progressList) {
                //获取用户任务总数及任务完成数
                List<LearnProjectTask> taskList = this.getTaskListByProId(progress.getProId(), "", null, null,
                    progress.getUserId());
                Map<String, LecturerDTO> taskLecturerMap = CollectionUtils.isEmpty(taskList) ? new HashMap<>()
                    : lecturerFeign.getTaskLecturerMap(
                        taskList.stream().map(LearnProjectTask::getId).collect(Collectors.toSet()));
                // 获取学习项目的任务资源Map
                HashMap<String, Map<Integer, List<TaskAppResourceDTO>>> taskResourceMap = getProjectTaskResourceMap(
                    proId);
                Map<String, Integer> taskSort = getTaskSort(proId, project);
                List<TaskDTO> taskDTOList = taskList.stream().map(task -> {
                    TaskDTO taskDTO = new TaskDTO().setId(task.getId()).setTaskName(task.getTaskName())
                        .setTaskType(task.getTaskType()).setActivityId(task.getTaskContent())
                        .setCreateType(task.getCreateType()).setStartTime(task.getStartTime())
                        .setEndTime(task.getEndTime()).setStatus(task.getStatus()).setUserStatus(task.getUserStatus())
                        .setProjectType(project.getType()).setValid(GeneralJudgeEnum.CONFIRM.getValue())
                        .setEnablePlayBack(task.getLvCount() > 0 ? GeneralJudgeEnum.CONFIRM.getValue()
                            : GeneralJudgeEnum.NEGATIVE.getValue()).setAddress(task.getAddress());
                    // 装配讲师信息
                    Optional.ofNullable(taskLecturerMap.get(task.getId()))
                        .ifPresent(lecturer -> taskDTO.setLecturerName(lecturer.getName()));
                    // 增加任务的状态是否签到、是否评估
                    buildTaskAppInfo(taskResourceMap.get(task.getId()), taskDTO);

                    perfectTaskDto(project, task, taskDTO, taskSort);
                    return taskDTO;
                }).collect(Collectors.toList());
                Integer projectTaskCount = taskList.size();
                // 已完成任务量
                long completedCount = taskDTOList.stream()
                    .filter(t -> t.getUserStatus() == ProjectConstant.USER_TASK_STATUS_COMPLETED).count();
                //更新学习项目进度百分比
                Double progressPer = ((double) completedCount / (double) projectTaskCount) * 100;
                BigDecimal progressPercent = BigDecimal.valueOf(progressPer.longValue());
                if (completedCount != 0L) {
                    progress.setProgressPercent(progressPercent);
                }
                progressService.updateById(progress);
            }
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImportResultDTO faceProjectImportTask(ImportExcelDTO dto) {
        ImportResultDTO importResultDTO = new ImportResultDTO();
        importResultDTO.setIsSuccess(false);
        long beginTime = System.currentTimeMillis();
        ImportDataDTO importDataDTO = importDataFeign.getImportData(dto.getExcelFile());
        log.info("获取导入的数据耗时：{}，共{}条数据", System.currentTimeMillis() - beginTime,
            importDataDTO.getRowCount());
        String[][] excel = importDataDTO.getExcel();
        String proId = dto.getId();
        if (StringUtils.isBlank(proId)) {
            throw new BusinessException(ProjectErrorNoEnum.PROJECT_NOT_EXIST);
        }
        beginTime = System.currentTimeMillis();
        ExcelCheckMessage excelCheckMessage = new TaskExcelTemplate(userFeign, paraFeign, this,
            taskProgressService, proId, projectViewLimitComponent).check(excel);
        log.info("TaskExcelTemplate check 耗时：{}", System.currentTimeMillis() - beginTime);

        if (!org.springframework.util.CollectionUtils.isEmpty(excelCheckMessage.getMessage())) {
            importResultDTO.setMsg(JsonUtil.objToJson(excelCheckMessage.getMessage()));
            log.info("import_excel_result:[{}]", importResultDTO);
            return importResultDTO;
        }
        List<ImportTaskDTO> importTaskDTOS = (List<ImportTaskDTO>) excelCheckMessage.getObjects();
        List<TaskProgress> importList = importTaskDTOS.stream().map(taskDTO -> {
            TaskProgress taskProgress = new TaskProgress();
            BeanUtils.copyProperties(taskDTO, taskProgress);
            taskProgress.setUserName(taskDTO.getFullName());
            if (taskProgress.getStartTime() == null) {
                taskProgress.setStartTime(taskDTO.getActualStartTime());
            }
            if (taskProgress.getEndTime() == null) {
                taskProgress.setEndTime(taskDTO.getActualEndTime());
            }
            return taskProgress;
        }).collect(Collectors.toList());
        ProjectJoinQuery projectJoinQuery = new ProjectJoinQuery();
        importList.forEach(taskProgress -> {
            projectJoinQuery.setProjectId(proId);
            projectJoinQuery.setUserId(taskProgress.getUserId());
            if (!progressService.isJoinProject(projectJoinQuery)) {
                progressService.insertProgress(projectJoinQuery);
            }
        });
        // 处理考试成绩
        List<ImportUserExamRecordDTO> importUserExamRecordDTOS = importTaskDTOS.stream()
            .map(ImportTaskDTO::getRecordDTO).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(importUserExamRecordDTOS)) {
            examFeign.createImportUserExamRecord(importUserExamRecordDTOS);
        }
        // 处理课程学习记录
        List<ImportUserCourseRecordDTO> importUserCourseRecordDTOS = importTaskDTOS.stream()
            .map(ImportTaskDTO::getCourseRecordDTO).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(importUserCourseRecordDTOS)) {
            courseFeign.createImportUserCourseRecord(importUserCourseRecordDTOS);
        }
        // 保存任务进度
        taskProgressService.saveOrUpdateBatch(importList, 100);
        // 更新项目状态
        importTaskDTOS.forEach(importTaskDTO -> {
            if (StringUtils.isNotBlank(importTaskDTO.getTaskContent())) {
                mqProducer.sendMsg(new ProjectCompletedEvent(importTaskDTO.getTaskContent(), importTaskDTO.getUserId(),
                    UserThreadContext.getUserId()));
            }
        });
        importResultDTO.setIsSuccess(true);
        return importResultDTO;
    }

    @Override
    public Map<String, List<Task>> getTaskMapByScheduleIds(Collection<String> scheduleIds) {
        if (CollectionUtils.isEmpty(scheduleIds)) {
            return new HashMap<>();
        }
        List<Task> taskList = list(new LambdaQueryWrapper<Task>().in(Task::getScheduleId, scheduleIds)
            .eq(Task::getIsPublish, PublishEnum.PUBLISHED.getValue()));
        return taskList.stream().collect(Collectors.groupingBy(Task::getScheduleId));
    }

    @Override
    public List<ScheduleTaskInfo> getUserFinishByScheduleId(Collection<String> userIdList,
        Collection<String> scheduleQueryList, String proId) {
        if (CollectionUtils.isEmpty(scheduleQueryList) || CollectionUtils.isEmpty(userIdList)) {
            return Collections.emptyList();
        }
        return baseMapper.getUserFinishByScheduleId(userIdList, scheduleQueryList, proId);
    }

    @Override
    public List<TaskGroupByScheduleIdDTO> getTaskListScheduleId(ScheduleStatQuery query) {
        List<TaskGroupByScheduleIdDTO> resultList = baseMapper.getTaskListGroupByScheduleId(query);
        if (CollectionUtils.isEmpty(resultList)) {
            return Collections.emptyList();
        }
        return resultList;
    }

    @Override
    public Task getByTaskNameAndProId(String taskName, String proId) {
        List<Task> tasks = list(
            new LambdaQueryWrapper<Task>().eq(Task::getTaskName, taskName).eq(Task::getProId, proId));
        if (CollectionUtils.isNotEmpty(tasks)) {
            return tasks.get(0);
        }
        return null;
    }

    @Override
    public List<TaskGroupByScheduleIdDTO> getScheduleTaskList(String proId) {
        return baseMapper.getScheduleTaskList(proId);
    }

    @Override
    public void asyncUpdateTaskStatus(String taskId, String resourceId, String toolType, String userId) {
        LearnProjectTask learnProjectTask = new LearnProjectTask();
        learnProjectTask.setTaskType(toolType);
        learnProjectTask.setTaskContent(resourceId);
        if (checkProjectTaskIsFinish(learnProjectTask, userId)) {
            mqProducer.sendMsg(new ProjectCompletedEvent(learnProjectTask.getTaskContent(), userId, userId));
        }
    }

    @Override
    public Set<String> getProjectPracticalTaskList(List<String> proIdList) {
        return baseMapper.getProjectPracticalTaskList(proIdList);
    }

    @Override
    public Map<String, ProTaskDTO> getPorjectTaskCount(String trainId) {
        List<ProTaskDTO> projectTaskCount = baseMapper.getProjectTaskCount(trainId);
        if (org.springframework.util.CollectionUtils.isEmpty(projectTaskCount)) {
            return new HashMap<>();
        }
        return projectTaskCount.stream()
            .collect(
                Collectors.toMap(ProTaskDTO::getTaskType, Function.identity(), (k1, k2) -> k1));
    }
    @Override
    public Integer projectTaskMaxSort(String projectId, String taskId) {
        List<Task> taskList = list(new LambdaQueryWrapper<Task>().eq(Task::getProId, projectId)
            .ne(StringUtils.isNotEmpty(taskId), Task::getId, taskId));
        return taskList.stream().map(Task::getSort).filter(Objects::nonNull).map(Long::intValue)
            .max(Integer::compareTo).orElse(0);
    }

    @Override
    public ProjectTaskBaseDTO getProjectTaskById(String taskId) {
        return baseMapper.getProjectTaskById(taskId);
    }


    @Override
    public PageInfo<UserTaskDTO> findUserTaskListByPage(UserTaskListQuery query) {

        // 调用客户端任务列表查询接口 (所有已发布项目任务 + 评估 + 签到)
        List<LearnProjectTask> taskList = this.getTaskListByProId(query.getProjectId(), null, null, null,
            query.getUserId());

        // 获取项目所有阶段
        List<Phase> stageList = phaseService.getListByProjectId(query.getProjectId());
        Map<String, String> phaseNameMap = stageList.stream()
            .collect(Collectors.toMap(Phase::getId, Phase::getPhaseName));

        // 所有任务
        List<UserTaskDTO> taskDTOList = new ArrayList<>();
        for (LearnProjectTask task : taskList) {
            UserTaskDTO userTaskDTO = new UserTaskDTO()
                .setPhaseId(task.getPhaseId())
                .setId(task.getId())
                .setTaskType(task.getTaskType())
                .setTaskName(task.getTaskName())
                .setIsRequired(task.getIsRequired())
                .setTaskContent(task.getTaskContent())
                .setUserStatus(task.getUserStatus());

            // 补充阶段名称数据
            if (StringUtils.isNotBlank(task.getPhaseId())) {
                userTaskDTO.setPhaseName(phaseNameMap.get(task.getPhaseId()));
            }

            taskDTOList.add(userTaskDTO);
        }

        // 所有任务 - 课程任务仅包含必修任务
        if (query.getType().equals(1)) {
            taskDTOList = taskDTOList.stream()
                .filter(t ->
                    !(t.getIsRequired() != null // 项目应用的 isRequired 为 null 需过滤
                        && t.getIsRequired().equals(GeneralJudgeEnum.NEGATIVE.getValue())
                        && t.getTaskType().equals(COURSE.getTaskType())
                    )
                )
                .toList();
        }
        // 所有必修课程任务
        else if (query.getType().equals(2)) {
            taskDTOList = taskDTOList.stream()
                .filter(t ->
                    t.getIsRequired() != null // 项目应用的 isRequired 为 null 需过滤
                        && t.getIsRequired().equals(GeneralJudgeEnum.CONFIRM.getValue())
                        && t.getTaskType().equals(COURSE.getTaskType())
                )
                .toList();
        }

        // 获取用户考试成绩结果
        Map<String, ExamUserScoreDTO> userScoreMap = new HashMap<>();
        Set<String> examIds = taskDTOList.stream()
            .filter(item -> Objects.equals(item.getTaskType(), ProjectTaskTypeEnum.EXAM.getTaskType()))
            .map(UserTaskDTO::getTaskContent).collect(Collectors.toSet());
        // 查询用户考试得分情况（未能加考试、未交卷、未改卷时，返回空值）
        if (!examIds.isEmpty()) {
            List<ExamUserScoreDTO> examUserScoreList = examFeign.getUserScoreInfo(query.getUserId(), examIds);
            userScoreMap = examUserScoreList.stream()
                .collect(
                    Collectors.toMap(ExamUserScoreDTO::getExamId, dto -> dto, (existing, replacement) -> existing));
        }

        // 获取用户实操成绩结果
        Map<String, PracticalOperationResultDTO> userOperationMap = new HashMap<>();
        Set<String> practicalIds = taskDTOList.stream()
            .filter(item -> Objects.equals(item.getTaskType(), ProjectTaskTypeEnum.PRACTICAL_OPERATION.getTaskType()))
            .map(UserTaskDTO::getTaskContent).collect(Collectors.toSet());
        // 查询用户实操通过情况（只有导入的记录才有得分）
        if (!practicalIds.isEmpty()) {
            List<PracticalOperationResultDTO> userOperationList = practicalOperationUserService.getPracticalOperationResult(
                query.getUserId(), practicalIds);
            userOperationMap = userOperationList.stream().collect(Collectors.toMap(
                PracticalOperationResultDTO::getId,
                dto -> dto,
                (existing, replacement) -> existing)
            );
        }

        int no = 0;
        for (UserTaskDTO dto : taskDTOList) {

            // 添加序号
            dto.setNo(++no);

            // 考试 - 组装考试结果
            buildUserExamResult(dto, userScoreMap);

            // 实操 - 组装实操结果
            buildUserPracticalOperationResult(dto, userOperationMap);
        }

        // 处理数据分页
        int total = taskDTOList.size();
        int pageNo = query.getPageNo();
        int pageSize = query.getPageSize();
        int start = (pageNo - 1) * pageSize;
        int end = Math.min(start + pageSize, total);
        PageInfo<UserTaskDTO> pageInfo = new PageInfo<>();
        pageInfo.setPageNum(pageNo);
        pageInfo.setPageSize(pageSize);
        pageInfo.setList(taskDTOList.subList(start, end));
        pageInfo.setTotal(total);

        return pageInfo;
    }

    private static void buildUserPracticalOperationResult(UserTaskDTO dto,
        Map<String, PracticalOperationResultDTO> userOperationMap) {
        if (dto.getTaskType().equals(ProjectTaskTypeEnum.PRACTICAL_OPERATION.getTaskType())
            && StringUtils.isNotBlank(dto.getTaskContent())) {
            PracticalOperationResultDTO userOperation = userOperationMap.get(dto.getTaskContent());
            if (userOperation != null && userOperation.getStatus() != null) {
                Integer userStatus = userOperation.getStatus();
                BigDecimal passScore = userOperation.getPassScore();
                String resultText =
                    (passScore != null ? (passScore + " " + I18nUtil.getMessage("分") + "，") : "")
                        +
                        (userStatus.equals(1) ? I18nUtil.getMessage("通过") : I18nUtil.getMessage("未通过"));
                dto.setTaskResult(resultText);
            }
        }
    }

    private static void buildUserExamResult(UserTaskDTO dto, Map<String, ExamUserScoreDTO> userScoreMap) {
        if (dto.getTaskType().equals(EXAM.getTaskType()) && StringUtils.isNotBlank(dto.getTaskContent())) {
            ExamUserScoreDTO userScoreInfo = userScoreMap.get(dto.getTaskContent());
            if (userScoreInfo != null) {
                BigDecimal passScore = userScoreInfo.getPassScore();
                BigDecimal userScore = userScoreInfo.getUserScore();

                String resultText =
                    userScore + " " + I18nUtil.getMessage("分") + "，"
                        + (passScore.compareTo(userScore) > 0 ? I18nUtil.getMessage("未通过")
                        : I18nUtil.getMessage("通过"));
                dto.setTaskResult(resultText);
            }
        }
    }


    @Override
    public void exportUserTask(UserTaskListQuery query) {
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<ITaskService, UserTaskDTO>(query) {

            @Override
            protected ITaskService getBean() {
                return SpringUtil.getBean("projectTaskService", ITaskService.class);
            }

            @Override
            protected PageInfo<UserTaskDTO> getPageInfo() {
                return getBean().findUserTaskListByPage(query);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.ProjectUserTask;
            }

            @Override
            public String getFileName() {
                Project project = projectService.getById(query.getProjectId());
                return project.getProName() + "_" + ExportFileNameEnum.ProjectUserTask.getType();
            }

            @Override
            protected void afterCreateBeanMap(Map<String, Object> map) {

                Object taskType = getTaskType(map);

                Object userStatus = map.get(USER_STATUS);
                String userStatusStr = String.valueOf(userStatus);
                switch (Integer.parseInt(userStatusStr)) {
                    case 0:
                        map.put(USER_STATUS, I18nUtil.getDefaultMessage("尚未进行"));
                        break;
                    case 1:
                        map.put(USER_STATUS, I18nUtil.getDefaultMessage("正在进行"));
                        break;
                    case 2:
                        map.put(USER_STATUS, I18nUtil.getDefaultMessage("已经完成"));
                        break;
                    default://noting
                }

                Object taskName = map.get(TASK_NAME);
                Object isRequired = map.get(IS_REQUIRED);
                if (isRequired != null && isRequired.equals(1) && taskType.equals(TASK_TYPE_COURSE)) {
                    map.put(TASK_NAME, "[" + I18nUtil.getDefaultMessage("必修") + "]" + taskName);
                } else if (isRequired != null && isRequired.equals(0) && taskType.equals(TASK_TYPE_COURSE)) {
                    map.put(TASK_NAME, "[" + I18nUtil.getDefaultMessage("选修") + "]" + taskName);
                }

            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }

    @NotNull
    private static Object getTaskType(Map<String, Object> map) {
        Object taskType = map.get(TASK_TYPE);
        if (taskType.equals(TASK_TYPE_COURSE)) {
            map.put(TASK_TYPE, I18nUtil.getDefaultMessage("课程"));
        } else if (taskType.equals(TASK_TYPE_EXAM)) {
            map.put(TASK_TYPE, I18nUtil.getDefaultMessage("考试"));
        } else if (taskType.equals(TASK_TYPE_EXERCISE)) {
            map.put(TASK_TYPE, I18nUtil.getDefaultMessage("练习"));
        } else if (taskType.equals(TASK_TYPE_SURVEY)) {
            map.put(TASK_TYPE, I18nUtil.getDefaultMessage("调研"));
        } else if (taskType.equals(TASK_TYPE_LIVE)) {
            map.put(TASK_TYPE, I18nUtil.getDefaultMessage("直播"));
        } else if (taskType.equals(TASK_TYPE_TRAIN)) {
            map.put(TASK_TYPE, I18nUtil.getDefaultMessage("培训班"));
        } else if (taskType.equals(TASK_TYPE_PROJECT)) {
            map.put(TASK_TYPE, I18nUtil.getDefaultMessage("项目"));
        } else if (taskType.equals(TASK_TYPE_APPLY)) {
            map.put(TASK_TYPE, I18nUtil.getDefaultMessage("报名"));
        } else if (taskType.equals(TASK_TYPE_SIGN)) {
            map.put(TASK_TYPE, I18nUtil.getDefaultMessage("签到"));
        } else if (taskType.equals(TASK_TYPE_FORM)) {
            map.put(TASK_TYPE, I18nUtil.getDefaultMessage("表单"));
        } else if (taskType.equals(TASK_TYPE_PRACTICAL_OPERATION)) {
            map.put(TASK_TYPE, I18nUtil.getDefaultMessage("实操"));
        } else if (taskType.equals(ProjectConstant.DEFAULT_FORM_TASK_CATEGORY_ID)) {
            map.put(TASK_TYPE, I18nUtil.getDefaultMessage("辅导"));
        }
        return taskType;
    }
}