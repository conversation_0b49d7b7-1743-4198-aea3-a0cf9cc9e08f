package com.wunding.learn.project.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户学习项目课时统计对象
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "UserCoursewareStudyDetailDTO", description = "用户学习项目课时统计对象")
public class UserCoursewareStudyDetailDTO implements Serializable {

    private static final long serialVersionUID = -2938622265647790522L;
    /**
     * 序号
     */
    @Schema(description = "序号")
    private Integer no;

    /**
     * 主键ID（课程任务ID）
     */
    @Schema(description = "主键ID（课程任务ID）")
    private String id;

    /**
     * 课程ID
     */
    @Schema(description = "课程ID")
    private String courseId;

    /**
     * 课程任务名称
     */
    @Schema(description = "课程任务名称")
    private String taskName;

    /**
     * 课程已学时长
     */
    @Schema(description = "课程已学时长（小时，不足0.01小时按0计算，否则四舍五入）")
    private BigDecimal duration;

    /**
     * 课程总计时长
     */
    @Schema(description = "课程总计时长（小时，不足0.01小时按0计算，否则四舍五入）")
    private BigDecimal totalDuration;

    /**
     * 课程任务是否必修(0 - 选修  1 - 必修)
     */
    @Schema(description = "课程任务是否必修(0 - 选修  1 - 必修)")
    private Integer isRequired;
}
