package com.wunding.learn.project.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 项目完成情况统计
 *
 * <AUTHOR>
 * @since 20025-06-13
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(name = "StatisticProjectCompletionDTO", description = "项目完成情况统计")
public class StatisticProjectCompletionDTO implements Serializable {

    /**
     * 用户ID
     */
    @Schema(hidden = true)
    private String userId;

    /**
     * 姓名
     */
    @Schema(description = "姓名")
    private String fullName;

    /**
     * 账号
     */
    @Schema(description = "账号")
    private String loginName;

    /**
     * 组织ID
     */
    @Schema(description = "组织ID", hidden = true)
    private String orgId;

    /**
     * 部门简称
     */
    @Schema(description = "部门简称")
    private String orgName;

    /**
     * 部门全称
     */
    @Schema(description = "部门全称")
    private String orgPath;

    /**
     * 团队名称
     */
    @Schema(description = "团队名称")
    private String teamName;


    /**
     * 项目完成率（所有任务完成率）
     */
    @Schema(description = "项目完成率（所有任务完成率）")
    private BigDecimal finishRatio;

    /**
     * 项目完成率（所有任务完成率）-百分比文本
     */
    @Schema(description = "项目完成率（所有任务完成率）-百分比文本")
    private String finishRatioStr;

    /**
     * 项目完成率（所有任务完成率，课程任务中计算必修）
     */
    @Schema(description = "项目完成率（所有任务完成率，课程任务中计算必修）")
    private BigDecimal finishOnlyMustRatio;

    /**
     * 项目完成率（所有任务完成率，课程任务中计算必修）-百分比文本
     */
    @Schema(description = "项目完成率（所有任务完成率，课程任务中计算必修）-百分比文本")
    private String finishOnlyMustRatioStr;

    /**
     * 必修课程完成率
     */
    @Schema(description = "必修课程完成率")
    private BigDecimal courseMustFinishRatio;

    /**
     * 必修课程完成率-百分比文本
     */
    @Schema(description = "必修课程完成率-百分比文本")
    private String courseMustFinishRatioStr;

    /**
     * 课时（小时） 不足0.01小时按0计算，否则四舍五入
     */
    @Schema(description = "课时（小时） 不足0.01小时按0计算，否则四舍五入")
    private BigDecimal courseTime;

    /**
     * 学时
     */
    @Schema(description = "学时")
    private BigDecimal learnTime;

    /**
     * 积分
     */
    @Schema(description = "积分")
    private BigDecimal integral;

    /**
     * 金币
     */
    @Schema(description = "金币")
    private BigDecimal goldCoin;

    /**
     * 学分
     */
    @Schema(description = "学分")
    private BigDecimal credit;
}
