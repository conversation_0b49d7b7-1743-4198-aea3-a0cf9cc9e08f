package com.wunding.learn.project.service.client.rest.faceproject;

import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.bean.Result;
import com.wunding.learn.common.directory.dto.DirectoryInfoDTO;
import com.wunding.learn.common.directory.query.DirectoryQuery;
import com.wunding.learn.common.directory.service.IDirectoryService;
import com.wunding.learn.project.service.admin.dto.faceproject.FaceProjectFileListDTO;
import com.wunding.learn.project.service.service.IFaceProjectFilesService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * <p> 面授项目文件api接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2024-03-21
 */
@RestController
@RequestMapping("${module.project.contentPath:/}faceProject/client")
@Tag(description = "学员端资料接口", name = "FaceProjectFileApiRest")
public class FaceProjectFileApiRest {

    @Resource
    private IFaceProjectFilesService faceProjectFilesService;

    @Resource
    private IDirectoryService directoryService;

    @GetMapping("/list")
    @Operation(operationId = "FaceProjectFileApiRest_getFaceProjectFileClientList", summary = "资料", description = "资料")
    public Result<PageInfo<FaceProjectFileListDTO>> getFaceProjectFileClientList(
        @Parameter(description = "培训项目id") @RequestParam("proId") String proId,
        @Parameter(description = "目录id") @RequestParam("directoryId") String directoryId,
        @Parameter(description = "页码") @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
        @Parameter(description = "页大小") @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        return Result.success(
            faceProjectFilesService.getFaceProjectFileClientList(proId, directoryId, pageNo, pageSize));
    }

    @GetMapping("/getDirectoryList")
    @Operation(operationId = "FaceProjectFileApiRest_getDirectoryList", summary = "获取资料目录-每次获取一层目录,分类就是目录", description = "获取资料目录-每次获取一层目录")
    public Result<List<DirectoryInfoDTO>> getDirectoryList(
        @Valid @ParameterObject DirectoryQuery directoryQuery) {
        return Result.success(directoryService.getDirectoryList(directoryQuery));
    }

}
