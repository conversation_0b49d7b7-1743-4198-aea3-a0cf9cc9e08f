package com.wunding.learn.project.service.admin.rest;

import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.bean.Result;
import com.wunding.learn.common.dto.ImportExcelDTO;
import com.wunding.learn.common.dto.ImportResultDTO;
import com.wunding.learn.file.api.dto.ExportResultDTO;
import com.wunding.learn.project.service.admin.dto.MentorPageDTO;
import com.wunding.learn.project.service.admin.dto.MentorSaveDTO;
import com.wunding.learn.project.service.admin.dto.MentorUserPageDTO;
import com.wunding.learn.project.service.admin.query.MentorQuery;
import com.wunding.learn.project.service.admin.query.MentorUserPageQuery;
import com.wunding.learn.project.service.service.IMentorRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @program: mlearn
 * @description: <p>项目管理-公告管理 前端控制器
 * @author: cdl
 * @create: 2022-07-12 10:02
 */
@RestController
@RequestMapping("${module.project.contentPath:/}app/mentor")

@Tag(description = "后台-学习项目管理-项目管理-导师管理", name = "MentorRest")
@Validated
public class MentorRest {

    @Resource
    private IMentorRecordService service;

    @GetMapping("/mentor")
    @Operation(operationId = "getMentorPage", summary = "导师管理列表", description = "导师管理列表")
    public Result<PageInfo<MentorPageDTO>> getMentorPage(MentorQuery query) {
        PageInfo<MentorPageDTO> pageInfo = service.getMentorPage(query);
        return Result.success(pageInfo);
    }

    @GetMapping("/getProjectUserPage")
    @Operation(operationId = "getProjectUserPage", summary = "学员列表", description = "学员列表")
    public Result<PageInfo<MentorUserPageDTO>> getProjectUserPage(MentorUserPageQuery query) {
        PageInfo<MentorUserPageDTO> pageInfo = service.getProjectUserPage(query);
        return Result.success(pageInfo);
    }

    @GetMapping("/{id}")
    @Operation(operationId = "getMentorInfo", summary = "获取单个导师管理信息", description = "获取单个导师管理信息")
    public Result<MentorSaveDTO> getMentorInfo(@Parameter(description = "导师管理列表ID") @PathVariable String id) {
        MentorSaveDTO saveDTO = service.getMentorInfo(id);
        return Result.success(saveDTO);
    }

    @PostMapping
    @Operation(operationId = "saveFormTemplate", summary = "添加导师管理", description = "添加导师管理")
    public Result<Void> saveInfo(@RequestBody @Valid MentorSaveDTO infoSaveDTO) {
        //添加返回信息
        String result = service.saveInfo1(infoSaveDTO);

        if (result != null) {
            //错误码需要和前端确认
            return Result.fail(-1301, result);
        } else {
            return Result.success();
        }
    }

    @DeleteMapping("/{ids}")
    @Operation(operationId = "removeMentorInfo", summary = "删除信息，批量删除用逗号隔开", description = "删除信息，批量删除用逗号隔开")
    public Result<Void> removeMentorInfo(@Parameter(description = "导师IDs,用逗号隔开") @PathVariable String ids) {
        service.removeMentorInfo(ids);
        return Result.success();
    }

    @PutMapping("/updateMentorInfo")
    @Operation(operationId = "updateMentorInfo", summary = "编辑导师信息", description = "编辑导师信息")
    public Result<Void> updateMentorInfo(
        @RequestBody @Valid MentorSaveDTO saveDTO) {
        service.updateMentorInfo(saveDTO);
        return Result.success();
    }

    @PutMapping("/importExcel")
    @Operation(operationId = "importExcel", summary = "批量导入", description = "批量导入")
    public Result<ImportResultDTO> importExcel(
        @Parameter(description = "批量导入对象") @RequestBody ImportExcelDTO importExcelDTO) {
        ImportResultDTO excel = service.importExcel1(importExcelDTO);
        return Result.success(excel);
    }

    /**
     * 导出导师管理列表
     */
    @PostMapping("/exportData")
    @Operation(operationId = "exportData", summary = "导出导师管理列表", description = "导出导师管理列表")
    public Result<ExportResultDTO> exportData(MentorQuery query) {
        service.exportData(query);
        return Result.success();
    }
}
