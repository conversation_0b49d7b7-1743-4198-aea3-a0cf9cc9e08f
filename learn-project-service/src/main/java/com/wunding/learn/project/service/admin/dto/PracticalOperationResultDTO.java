package com.wunding.learn.project.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 用户实操结果
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Data
@Accessors(chain = true)
@Schema(name = "PracticalOperationResultDTO", description = "用户实操结果")
public class PracticalOperationResultDTO implements Serializable {

    private static final long serialVersionUID = -7805758621625847508L;

    /**
     * 实操主键id
     */
    @Schema(description = "实操主键id")
    private String id;

    /**
     * 实操名称
     */
    @Schema(description = "是否通过 0-否 1-是")
    private Integer status;

    /**
     * 评价通过分（仅导入实操记录才有分）
     */
    @Schema(description = "评价通过分（仅导入实操记录才有分）")
    private BigDecimal passScore;


}
