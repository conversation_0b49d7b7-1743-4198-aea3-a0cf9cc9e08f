package com.wunding.learn.project.service.service.impl;

import static com.wunding.learn.common.util.string.StringUtil.newId;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.util.bean.BeanListUtils;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.project.service.admin.dto.RemarkDTO;
import com.wunding.learn.project.service.model.Remark;
import com.wunding.learn.project.service.mapper.RemarkMapper;
import com.wunding.learn.project.service.service.IRemarkService;
import com.wunding.learn.common.mybatis.service.impl.BaseServiceImpl;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * <p> 项目备注表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">liangzm</a>
    * @since 2024-03-11
 */
@Slf4j
@Service("remarkService")
public class RemarkBaseServiceImpl extends BaseServiceImpl<RemarkMapper, Remark> implements IRemarkService {

    @Override
    public IRemarkService getBean() {
        return SpringUtil.getBean("remarkService", IRemarkService.class);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveRemarks(List<RemarkDTO> remarkList, String resourcesId) {
        if (StringUtils.isBlank(resourcesId) || CollectionUtils.isEmpty(remarkList)) {
            return;
        }
        String userId = UserThreadContext.getUserId();
        AtomicInteger sort = new AtomicInteger();
        List<Remark> saveList = remarkList.stream().map(remarkDTO -> {
            Remark remark = new Remark();
            BeanUtils.copyProperties(remarkDTO, remark);
            remark.setId(newId());
            remark.setCreateBy(userId);
            remark.setUpdateBy(userId);
            remark.setCreateTime(new Date());
            remark.setUpdateTime(new Date());
            remark.setProId(resourcesId);
            remark.setSort(sort.getAndIncrement());
            return remark;
        }).collect(Collectors.toList());
    
        saveBatch2(saveList);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRemarks(List<RemarkDTO> remarkList, String resourcesId) {
        List<Remark> oldList = list(new LambdaQueryWrapper<Remark>().eq(Remark::getProId, resourcesId));
        if (CollectionUtils.isEmpty(remarkList)) {
            if (!CollectionUtils.isEmpty(oldList)) {
                removeBatchByIds2(oldList.stream().map(Remark::getId).collect(Collectors.toList()));
            }
            return;
        }
        // 设置排序
        AtomicInteger sort = new AtomicInteger();
        remarkList.forEach(remarkDTO -> remarkDTO.setSort(sort.getAndIncrement()));
        
        // 新增的
        List<RemarkDTO> addList = remarkList.stream().filter(remarkDTO -> StringUtils.isBlank(remarkDTO.getId()))
            .collect(Collectors.toList());
        getBean().saveRemarks(addList, resourcesId);
    
        List<String> curIds = remarkList.stream().map(RemarkDTO::getId).collect(Collectors.toList());
        // 删除的
        List<String> toDel = oldList.stream().map(Remark::getId).filter(id -> !curIds.contains(id))
            .collect(Collectors.toList());
        removeBatchByIds2(toDel);
        
        // 更新的
        List<RemarkDTO> updateList = remarkList.stream().filter(remarkDTO -> StringUtils.isNotBlank(remarkDTO.getId()))
            .collect(Collectors.toList());
        String userId = UserThreadContext.getUserId();
        List<Remark> updateRemarks = updateList.stream().map(remarkDTO -> {
            Remark remark = new Remark();
            BeanUtils.copyProperties(remarkDTO, remark);
            remark.setProId(resourcesId);
            remark.setUpdateBy(userId);
            remark.setUpdateTime(new Date());
            return remark;
        }).collect(Collectors.toList());
        // 更新
        updateBatchById2(updateRemarks);
    }
    
    @Override
    public List<RemarkDTO> getRemarks(String resourcesId) {
        if (StringUtils.isBlank(resourcesId)) {
            return new ArrayList<>();
        }
        List<Remark> remarkList = list(
            new LambdaQueryWrapper<Remark>().eq(Remark::getProId, resourcesId).orderByAsc(Remark::getSort));
        return BeanListUtils.copyListProperties(remarkList, RemarkDTO::new);
    }
    
    @Override
    public Map<String, List<RemarkDTO>> getRemarksMap(List<String> projectIds) {
        if (CollectionUtils.isEmpty(projectIds)) {
            return new HashMap<>();
        }
        List<Remark> remarkList = list(
            new LambdaQueryWrapper<Remark>().in(Remark::getProId, projectIds).orderByAsc(Remark::getProId)
                .orderByAsc(Remark::getSort));
        if (CollectionUtils.isEmpty(remarkList)) {
            return new HashMap<>();
        }
        List<RemarkDTO> remarkDTOS = BeanListUtils.copyListProperties(remarkList, RemarkDTO::new);
        // 转换为map
        return remarkDTOS.stream().collect(Collectors.groupingBy(RemarkDTO::getProId));
    }
}
