<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.project.service.mapper.ProjectStatisticMapper">
    <!--查询部门统计情况-->
    <select id="selectOrgStatisticCompleteByPage"
      resultType="com.wunding.learn.project.service.admin.dto.StatisticOrgCompleteDTO" useCache="false">
        select a.org_id orgId
            ,a.totalFinished
            ,a.totalJoin
            ,a.totalTaskScore
        from (
            select su.org_id
                ,sum(status) totalFinished
                ,count(a.user_id) totalJoin
                ,sum(ifnull(aa.score, 0)) totalTaskScore
            from project_progress a
            left join ( select b.user_id
                        ,sum(c.score) score
                    from task_progress b
                    left join project_task c on b.task_id = c.id
                    where b.is_finish = 1
                        and b.pro_id = #{projectId}
                    group by b.user_id
                ) aa on a.user_id = aa.user_id
            left join sys_user su on su.id = a.user_id
            left join sys_org so on su.org_id = so.id
            where a.pro_id = #{projectId}
            <if test="leaderDontStat != null and leaderDontStat == 1">
                and a.user_id != #{leader}
            </if>
            <if test="levelPathNum != null and levelPathNum != '' and levelPathNum != 1">
                and (length(so.level_path) - length(replace(so.level_path, '/', '')) - 1) = #{levelPathNum}
            </if>
            group by su.org_id) a
        where a.org_id is not null and a.org_id != ''
    </select>


    <!--团队情况统计-->
    <select id="selectTeamStatisticCompleteByPage"
      resultType="com.wunding.learn.project.service.admin.dto.StatisticTeamCompleteDTO" useCache="false">
        select tpt.team_name,
               tpt.id as teamId,
               sum((select count(1)
                    from project_progress
                    where status = 1
                      and pro_id = app.project_id
                      and user_id = u.user_id
                      <if test="leaderDontStat != null and leaderDontStat == 1">
                          and user_id != #{leader}
                      </if>
                    ))   as totalFinished,

               max((select sum(score) from project_task where pro_id = app.project_id and is_del = 0 and is_publish = 1)) *
               (sum((select count(1)
                     from project_progress
                     where status = 1
                       and pro_id = app.project_id
                       and user_id = u.user_id
                       <if test="leaderDontStat != null and leaderDontStat == 1">
                           and user_id != #{leader}
                       </if>
                     ))) as totalTaskScore,

               (count(u.id))                         totalJoin
        from project_team tpt
                 join project_app app on tpt.id = app.resource_id
                 left join project_team_user u on u.team_id = tpt.id
        where app.project_id = #{projectId}
          and tpt.is_del = 0
        <if test="leaderDontStat != null and leaderDontStat == 1">
            and u.user_id != #{leader}
        </if>
        group by tpt.team_name, tpt.id
    </select>


    <!--任务统计-->
    <select id="selectTaskStatisticByPage"
      resultType="com.wunding.learn.project.service.admin.dto.StatisticTaskDTO" useCache="false">
        select *
        from (select tpt.id                                                           as taskId,
                     tpt.task_name                                                       taskName,
                     sum((select count(1)
                          from task_progress
                          where is_finish = 1
                            and task_id = tpt.id
                            <if test="leaderDontStat != null and leaderDontStat == 1">
                                and user_id != #{leader}
                            </if>
                          ))                                    as totalFinished,
                     sum((select count(1) from task_progress where task_id = tpt.id
                            <if test="leaderDontStat != null and leaderDontStat == 1">
                                and user_id != #{leader}
                            </if>                            )) as totalJoin,
                     if((tpt.task_type in ('exam', 'exercise', 'course', 'live', 'survey', 'project') and
                         tpt.task_content = ''), 0, 1)
                                                                                         isShow
              from project_task tpt
              where tpt.pro_id = #{projectId}
                and tpt.is_del = 0
              group by tpt.task_name, tpt.id) temp
        where temp.isShow = 1
    </select>

    <!--任务详情统计-->
    <select id="selectTaskDetailByPage" resultType="com.wunding.learn.project.service.admin.dto.StatisticTaskDetailDTO" useCache="false">
        select *
        from
        (
        select
        tpt.id taskId,
        tpt.task_type taskType,
        tpt.task_name taskName,
        tpt.task_content taskContent,
        ttp.user_id userId,
        su.org_id orgId,
        (select team.team_name from project_team team
        left join project_team_user tu on tu.team_id = team.id
        inner join project_app app on team.app_id = app.id
        where tu.user_id = ttp.user_id and app.project_id = tpt.pro_id and app.resource_type = 4 and team.is_del = 0 limit 1) as
        teamName,
        (select team.id from project_team team
        left join project_team_user tu on tu.team_id = team.id
        inner join project_app app on team.app_id = app.id
        where tu.user_id = ttp.user_id and app.project_id = tpt.pro_id and app.resource_type = 4 and team.is_del = 0 limit 1) as
        teamId,
        tpt.start_time startTime,
        tpt.end_time endTime,
        ttp.actual_start_time as joinTime,
        ttp.actual_end_time as finishedTime,
        ifnull(ttp.is_finish, -1) isFinish,
        ttp.actual_end_time taskFinishedTime,
        (case when ttp.is_finish = 1 then tpt.score else -1 end) taskScore,
        ttp.source,
        ttp.explanation
        from project_task tpt
        inner join task_progress ttp on tpt.id = ttp.task_id
        inner join sys_user su on ttp.user_id = su.id and su.is_del = 0
        where tpt.pro_id =#{projectId} and tpt.is_del = 0 and tpt.is_publish = 1
        order by ttp.actual_start_time desc
        ) t
        where 1 = 1
        <if test="teamId != null and teamId != ''">
            and t.teamId = #{teamId}
        </if>

        <if test="userId != null and userId.size() > 0">
            and t.userId in
            <foreach collection="userId" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="orgId != null and orgId != ''">
            and t.orgId = #{orgId}
        </if>
        <if test="taskId != null and taskId != ''">
            and t.taskId = #{taskId}
        </if>
        <if test="startTime != null ">
            and t.starttime >= #{startTime}
        </if>
        <if test="endTime != null ">
            and t.starttime &lt;= #{endTime}
        </if>
        <if test="taskType != null and taskType != ''">
            and t.taskType = #{taskType}
        </if>
        <if test="leaderDontStat != null and leaderDontStat == 1">
            and t.userId != #{leader}
        </if>
    </select>


    <!-- 获取学习项目个人统计列表 -->
    <select id="selectPersonRankByPage"
      resultType="com.wunding.learn.common.dto.StatisticPersonRankDTO" useCache="false">
        select
        tp.user_id userId,
        sum(t.score) score,
        row_number() over (order by sum(t.score) desc ) ranking
        from task_progress tp, project_task t
        where tp.task_id = t.id and t.is_del = 0 and tp.is_finish = 1 and tp.pro_id = #{params.projectId}
        <if test="params.userIdList != null and params.userIdList.size() != 0">
            <foreach collection="params.userIdList" separator="," item="userId" open="and tp.user_id in(" close=")">
                #{userId}
            </foreach>
        </if>
        <if test="params.orgId != null and params.orgId != ''">
            and tp.org_id = #{params.orgId}
        </if>
        <if test="params.leaderDontStat != null and params.leaderDontStat == 1">
            and tp.user_id != #{params.leader}
        </if>
        group by tp.user_id
        order by ranking,user_id
    </select>

    <select id="selectTeamLearnRankByPage"
      resultType="com.wunding.learn.project.service.admin.dto.StatisticTeamLearnRankDTO" useCache="false">
        select
        a.rn `rank`,
        a.teamScore score,
        a.team_name teamName
        from (select
        a.id,
        a.team_name,
        sum(a.score) teamScore,
        row_number() over (order by sum(a.score) desc ) rn
        from (select
        pt.id,
        pt.team_name,
        ifnull((select
        sum(t.score)
        from task_progress tp, project_task t
        where t.is_del = 0 and tp.task_id = t.id and tp.is_finish = 1 and tp.pro_id = pa.project_id
        and tp.user_id = ptu.user_id),0) score
        from project_app pa, project_team pt
        left join project_team_user ptu on pt.id = ptu.team_id
        where pa.resource_id = pt.id and pa.is_available = 1 and pt.is_del = 0 and pa.project_id = #{projectId}
        <if test="leaderDontStat != null and leaderDontStat == 1">
            and ptu.user_id != #{leader}
        </if>
        ) a
        group by a.id, a.team_name) a
        where 1 = 1
        <if test="teamNameLike != null and teamNameLike != ''">
            and instr(a.team_name,#{teamNameLike}) > 0
        </if>
    </select>

    <select id="selectOrgLearnRankByPage"
      resultType="com.wunding.learn.project.service.admin.dto.StatisticOrgLearnRankDTO" useCache="false">
        select
        a.rn `rank`,
        a.orgScore score,
        a.org_id orgId,
        a.org_name orgName
        from (select
        a.org_id,
        a.org_name,
        sum(a.score) orgScore,
        row_number() over (order by sum(a.score) desc ) rn
        from
        (select
        su.org_id,
        org.org_name,
        ifnull((select sum(t.score) from project_task t where t.is_del = 0 and t.id = tp.task_id), 0) score
        from task_progress tp
        left join sys_user su on su.id = tp.user_id
        left join sys_org org on org.id= su.org_id
        where tp.pro_id = #{projectId} and tp.is_finish = 1
        <if test="leaderDontStat != null and leaderDontStat == 1">
            and tp.user_id != #{leader}
        </if>
        ) a
        group by a.org_id,a.org_name) a
        where 1 = 1
        <if test="orgNameLike != null and orgNameLike != ''">
            and instr(a.org_name,#{orgNameLike}) > 0
        </if>
        <if test="orgId != null and orgId != ''">
            and a.org_id = #{orgId}
        </if>
    </select>

    <select id="selectOneProjectEvaIdByTrainId"
       resultType="java.lang.String">
          select
              pa.resource_id
          from project_app pa
          left join project p on pa.project_id = p.id
          where pa.is_del = 0
          and pa.resource_type = 5 and pa.evaluation_type = 1
          and p.is_del = 0 and p.referenced_id = #{trainId}
          order by pa.create_time desc
          limit 1
    </select>

    <select id="getAllEvaIdsByTrainId"
      resultType="java.lang.String">
        select
            pa.resource_id
        from project_app pa
                 left join project p on pa.project_id = p.id
        where pa.is_del = 0
          and pa.resource_type = 5 and pa.evaluation_type = 1
          and p.is_del = 0 and p.referenced_id = #{trainId}
        order by pa.create_time desc
    </select>


    <select id="selectEvaIdListByTrainId"
            resultType="com.wunding.learn.project.service.admin.dto.StatisticProjectOrgDTO">
        select
            p.pro_name projectName,
            p.start_time start_time,
            p.end_time endTime,
            pa.resource_id evaId
        from project_app pa
                 left join project p on pa.project_id = p.id
        where pa.is_del = 0
          and pa.resource_type = 5 and pa.evaluation_type = 1
          and p.is_del = 0 and p.referenced_id = #{trainId}
          and pa.resource_id = #{evaluationId}
        order by pa.create_time desc
    </select>

    <select id="stateAnalysisByOrg" parameterType="com.wunding.learn.project.service.admin.query.ProjectAnalysisByOrgQuery"
      resultType="com.wunding.learn.project.service.admin.dto.ProjectAnalysisByOrgDTO" useCache="false">
        select
        p.id projectId
        , p.pro_name projectName
        , p.is_publish isPublish
        , p.type
        , b.id orgId
        , b.org_name orgName
        , b.level_path_name orgLevelPathName
        , a.task_score taskScore
        , a.view_limit_num viewLimitNum
        , a.join_num joinNum
        , a.not_join_num notJoinNum
        , a.finish_num finishNum
        , a.join_rate joinRate
        , a.finish_rate finishRate
        , a.total_finish_rate totalFinishRate
        from project_bak p
        <choose>
            <when test="onlyCurrentOrg != null and onlyCurrentOrg == 1">
                inner join ${statisticPartition} a on p.id = a.project_id
            </when>
            <otherwise>
                inner join project_statistic_by_org_collect a on p.id = a.project_id
            </otherwise>
        </choose>
        inner join sys_org_bak b on a.org_id = b.id
        where p.id = #{projectId}
          and a.is_del = 0
        and ((p.create_by = #{currentUserId})
             <foreach collection="manageOrgLevelPaths" open="or (" item="manageOrgLevelPath" separator="or" close=")">
                b.level_path like concat(#{manageOrgLevelPath}, '%')
            </foreach>
        )
        <choose>
            <when test="onlyCurrentOrg != null and onlyCurrentOrg == 1 and orgIds != null and orgIds.size() > 0">
                and a.org_id in
                <foreach collection="orgIds" open="(" item="oId" separator="," close=")">
                    #{oId}
                </foreach>
            </when>
            <otherwise>
                <if test="orgIds != null and orgIds.size() > 0">
                    <if test="orgLevelPaths != null and orgLevelPaths.size() > 0">
                        and
                        <foreach collection="orgLevelPaths" open="(" item="queryLevelPath" separator="or" close=")">
                            a.org_level_path like concat(#{queryLevelPath}, '%')
                        </foreach>
                    </if>
                </if>
            </otherwise>
        </choose>

        order by b.id
    </select>

    <insert id="addTransferLog">
        insert into transfer_log(remark, transfer_time,totalCount)
        values (#{remark,jdbcType=VARCHAR}, #{transferTime,jdbcType=TIMESTAMP},  #{totalCount,jdbcType=TINYINT});
    </insert>

    <delete id="cleanTransferLog">
        delete from transfer_log;
    </delete>

    <delete id="cleanPartData">
        ${cleanDataSql}
    </delete>


    <select id="getTotalCount" resultType="java.lang.Integer">
        select count(1) from ( ${sourceDataSql} ) a
    </select>


    <select id="getBackupData" resultType="java.util.Map">
        ${sourceDataSql} limit #{startIndex} ,  #{pageSize}
    </select>

    <select id="selectOrgStatisticCompleteUserDetailByPage"
            resultType="com.wunding.learn.project.service.admin.dto.StatisticOrgCompleteUserDetailDTO">
        SELECT
            pp.start_time,
            pp.finish_time,
            pp.is_import,
            pp.user_id,
            su.full_name,
            su.login_name,
            so.org_name,
            so.level_path_name
        FROM project_progress pp
                 LEFT JOIN sys_user su ON su.id = pp.user_id
                 LEFT JOIN sys_org so ON su.org_id = so.id
        WHERE pp.is_del = 0
        <if test="projectId != null and projectId != ''">
            AND pp.pro_id = #{projectId}
        </if>
        <if test="isFinish != null">
            AND pp.status = #{isFinish}
        </if>
        <if test="isImport != null">
            AND pp.is_import = #{isImport}
        </if>
        <if test="userNameOrLoginNameLike != null and userNameOrLoginNameLike !=''">
            and (
            instr(su.login_name,#{userNameOrLoginNameLike}) > 0
            or instr(su.full_name,#{userNameOrLoginNameLike}) > 0
            )
        </if>
        <if test="orgId != null and orgId != ''">
            AND so.id = #{orgId}
        </if>
        <if test="leaderDontStat != null and leaderDontStat == 1">
            and pp.user_id != #{leader}
        </if>
        ORDER BY pp.id DESC
    </select>

    <select id="selectTeamStatisticCompleteUserDetailByPage"
            resultType="com.wunding.learn.project.service.admin.dto.StatisticOrgCompleteUserDetailDTO">
        SELECT
            pp.start_time,
            pp.finish_time,
            pp.is_import,
            pp.user_id,
            su.full_name,
            su.login_name,
            so.org_name,
            so.level_path_name
        FROM
            project_team pt
                JOIN
            project_app pa ON pt.id = pa.resource_id
                LEFT JOIN
            project_team_user ptu ON ptu.team_id = pt.id
                LEFT JOIN
            project_progress pp ON pp.user_id = ptu.user_id AND pp.pro_id = pa.project_id
                LEFT JOIN
            sys_user su ON ptu.user_id = su.id
                LEFT JOIN
            sys_org so ON su.org_id = so.id
        WHERE pt.is_del = 0
        <if test="projectId != null and projectId != ''">
            AND pp.pro_id = #{projectId}
        </if>
        <if test="teamId != null and teamId != ''">
            AND pt.id = #{teamId}
        </if>
        <if test="leaderDontStat != null and leaderDontStat == 1">
            and ptu.user_id != #{leader}
        </if>
        <if test="isFinish != null">
            AND pp.status = #{isFinish}
        </if>
        <if test="isImport != null">
            AND pp.is_import = #{isImport}
        </if>
        <if test="userNameOrLoginNameLike != null and userNameOrLoginNameLike !=''">
            and (
            instr(su.login_name,#{userNameOrLoginNameLike}) > 0
            or instr(su.full_name,#{userNameOrLoginNameLike}) > 0
            )
        </if>
        ORDER BY pp.id DESC
    </select>
    <select id="findProjectCompletionStatisticByPage"
      resultType="com.wunding.learn.project.service.admin.dto.StatisticProjectCompletionDTO"  useCache="false">
        select
        pp.user_id as userId,
        pp.user_full_name as fullName,
        pp.user_login_name as loginName,
        pp.org_id as orgId,
        pp.org_name as orgName,
        pp.org_level_path as orgPath,
        (
            select team.team_name
            from project_team team
                inner join project_team_user tu on tu.team_id=team.id
                inner join project_app app on team.app_id=app.id
            where tu.user_id=pp.user_id and app.project_id=pp.pro_id and app.resource_type=4 and team.is_del=0 limit 1
        ) as teamName,
        progress_percent as finishRatio
        from
            project_progress pp
        where
            pp.is_del = 0
            and pp.pro_id = #{projectId}
        <if test="userIdList != null and userIdList.size() != 0">
            <foreach collection="userIdList" separator="," item="userId" open="and pp.user_id in(" close=")">
                #{userId}
            </foreach>
        </if>
        <if test="orgId != null and orgId != ''">
            and instr(pp.org_level_path,concat('/', #{orgId}, '/')) > 0
        </if>
        <if test="teamId != null and teamId != ''">
            and exists (select 1
                        from project_team team
                            inner join project_team_user tu on tu.team_id=team.id
                            inner join project_app app on team.app_id=app.id
                        where
                            team.id = #{teamId}
                          and tu.user_id=pp.user_id
                          and app.project_id=pp.pro_id
                          and app.resource_type=4
                          and team.is_del=0)
        </if>
        <if test="leaderDontStat != null and leaderDontStat == 1">
            and pp.user_id != #{leader}
        </if>
        order by
            pp.id desc
    </select>

    <select id="getUserProjectCoursewareStudyDetail" resultType="com.wunding.learn.project.service.admin.dto.UserCoursewareStudyDetailDTO" useCache="false">
        select
            row_number() over (order by tp.id desc) as no,
            tp.task_id as id,
            pt.task_content as courseId,
            pt.task_name as taskName,
            pt.is_required as isRequired
        from
            task_progress tp
                join project_task pt on tp.task_id = pt.id
        where
            pt.pro_id = #{projectId}
          and pt.task_type = 'course'
          and pt.is_del = 0
          and tp.is_del = 0
          and tp.user_id = #{userId}
        order by
            tp.id desc
    </select>

    <insert id="insertBackupData">
        insert into ${targetTable}
        (
        <foreach collection="tableFields"  item="item" separator=",">
            ${item}
        </foreach>
        )
        values
        <foreach collection="rowDatas" item="row" separator=",">
            <foreach collection="row"  item="value"  open="(" close=")"  separator=",">
                #{value}
            </foreach>
        </foreach>
    </insert>

    <update id="createBackupTable">
        drop table if exists ${targetTable};
        create table  ${targetTable} like ${sourceTable};
    </update>

    <update id="createProjectIndex">
        create index  `idx_pro_id_user_id` on ${targetTable} (`pro_id`,`user_id`) ;
        create index  `idx_pro_id` on ${targetTable} (`pro_id`) ;
    </update>
    <update id="dropProjectIndex">
        alter table ${targetTable} drop index idx_pro_id_user_id;
        alter table ${targetTable} drop index idx_pro_id;
    </update>
    <update id="createTaskIndex">
        create index  `uk_task_progress`  on ${targetTable}(`pro_id`,`task_id`,`user_id`);
        create index  `IDX_TASK_ID`  on ${targetTable}(`task_id`);
        create index  `IDX_TASK_RES_ID`  on ${targetTable}(`pro_id`);
        create index  `IDX_TB_TASK_PROGRESS_USERID`  on ${targetTable}(`user_id`);
        create index  `idx_pro_id_user_id`  on ${targetTable}(`pro_id`,`user_id`) ;
    </update>
    <update id="dropTaskIndex">
        alter table ${targetTable} drop index uk_task_progress;
        alter table ${targetTable} drop index IDX_TASK_ID;
        alter table ${targetTable} drop index IDX_TASK_RES_ID;
        alter table ${targetTable} drop index IDX_TB_TASK_PROGRESS_USERID;
        alter table ${targetTable} drop index idx_pro_id_user_id;
    </update>
    <update id="createLimitIndex">
        create index   `w_view_limit_user_view_limit_id_idx` on ${targetTable} (`view_limit_id`,`user_id`) ;
        create index `w_view_limit_user_user_id_idx` on ${targetTable} (`user_id`);
    </update>
    <update id="dropLimitIndex">
        alter table ${targetTable}  drop index w_view_limit_user_view_limit_id_idx;
        alter table ${targetTable}  drop index w_view_limit_user_user_id_idx;
    </update>

    <update id="cleanSysOrgData">
        drop table if exists  sys_org_bak;
        create table sys_org_bak  like sys_org;
    </update>

    <insert id="insertSysOrgData">
        insert into sys_org_bak
        select  t.* from sys_org t;
    </insert>

    <update id="cleanSysUserData">
        drop table if exists  sys_user_bak;
        create table sys_user_bak  like sys_user;
    </update>

    <insert id="insertSysUserData">
        insert into sys_user_bak
        select  t.* from sys_user t;
    </insert>

    <update id="cleanProjectData">
        drop table if exists project_bak;
        create table project_bak  like project;
        alter table project_bak add column hash_index int ;
    </update>

    <insert id="insertProjectData">
        insert into project_bak
        select  t.*, abs(mod(conv(substring(md5(id),1,16),16,10),50)) as hash_index from project t;
    </insert>

    <update id="cleanResourceViewLimitData">
        drop table if exists  w_resource_view_limit_bak;
        create table w_resource_view_limit_bak  like w_resource_view_limit;
        alter table w_resource_view_limit_bak add column hash_index int ;
    </update>

    <insert id="insertResourceViewLimitData">
        insert into w_resource_view_limit_bak
        select  t.*, abs(mod(conv(substring(md5(id),1,16),16,10),50)) as hash_index from w_resource_view_limit  t;
    </insert>

    <update id="cleanProjectTaskData">
        drop table if exists  project_task_bak;
        create table project_task_bak  like project_task;
    </update>

    <insert id="insertProjectTaskData">
        insert into project_task_bak
        select  t.*  from project_task  t;
    </insert>

    <insert id="replaceBackupData">
        replace INTO ${targetTable}
        (
        <foreach collection="tableFields"  item="item" separator=",">
            ${item}
        </foreach>
        )
        values
        <foreach collection="rowDatas" item="row" separator=",">
            <foreach collection="row"  item="value"  open="(" close=")"  separator=",">
                #{value}
            </foreach>
        </foreach>
    </insert>

    <select id="getDate" resultType="java.lang.String">
        ${sourceDataSql}
    </select>


</mapper>
