package com.wunding.learn.project.service.client.rest;

import com.wunding.learn.common.bean.Result;
import com.wunding.learn.project.service.client.dto.QuickProjectDTO;
import com.wunding.learn.project.service.constant.ProjectConstant;
import com.wunding.learn.project.service.service.IProjectService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 线上课程学习客户端接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("${module.project.contentPath:/}api/courseProject")
@Tag(description = "线上课程学习客户端接口", name = "CourseProjectApiRest")
@Validated
public class CourseProjectApiRest {

    @Resource
    private IProjectService projectService;

    @GetMapping("/get")
    @Operation(operationId = "CourseProjectApiRest_get", summary = "获取培训信息，参数id为空，返回最新发布时间的培训或者上次选择的培训", description = "获取培训信息，参数id为空，返回最新发布时间的培训或者上次选择的培训")
    public Result<QuickProjectDTO> get(
        @Parameter(description = "项目id，可空，返回对应培训信息") @RequestParam(name = "id", required = false) String id) {
        return Result.success(projectService.getQuickProject(id, null, 0, ProjectConstant.PROJECT_COURSE_TASK));
    }
}
