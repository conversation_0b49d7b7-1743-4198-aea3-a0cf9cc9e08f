package com.wunding.learn.project.service.admin.query;

import com.wunding.learn.common.bean.BasePageQuery;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import lombok.Data;

/**
 * 用户项目任务完成情况统计查询对象
 *
 * <AUTHOR>
 * @since 2025-06-13
 */
@Data
public class UserTaskListQuery extends BasePageQuery implements Serializable {


    private static final long serialVersionUID = 5554569620009110909L;
    /**
     * 学习项目ID
     */
    @Parameter(description = "学习项目ID")
    @NotBlank(message = "学习项目ID不可为空")
    private String projectId;

    /**
     * 用户ID
     */
    @Parameter(description = "用户ID")
    @NotBlank(message = "用户ID不可为空")
    private String userId;

    /**
     * 查询类型：0-所有任务，1-所有任务（课程任务仅含必修），2-所有必修课程任务
     */
    @Parameter(description = "查询类型：0-所有任务，1-所有任务（课程任务仅含必修），2-所有必修课程任务")
    @Schema(allowableValues = {"0", "1", "2"}, example = "0")
    @NotBlank(message = "查询类型不可为空")
    private Integer type;
}
