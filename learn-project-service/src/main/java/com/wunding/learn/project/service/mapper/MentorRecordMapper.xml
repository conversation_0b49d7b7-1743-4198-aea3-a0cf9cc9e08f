<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.project.service.mapper.MentorRecordMapper">
    <!-- 开启二级缓存 -->
    <!--
<cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.project.service.mapper.MentorRecordMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.project.service.model.MentorRecord">
        <!--@Table project_mentor_record-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="user_id" jdbcType="VARCHAR"
          property="userId"/>
        <result column="mentor_type" jdbcType="VARCHAR"
          property="mentorType"/>
        <result column="mentor_id" jdbcType="VARCHAR"
          property="mentorId"/>
        <result column="pro_id" jdbcType="VARCHAR"
          property="proId"/>
        <result column="create_by" jdbcType="VARCHAR"
          property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP"
          property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR"
          property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP"
          property="updateTime"/>
        <result column="is_del" jdbcType="TINYINT"
          property="isDel"/>
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap2" type="com.wunding.learn.project.service.model.TaskUserMentor">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="user_id" jdbcType="VARCHAR"
          property="userId"/>
        <result column="mentor_type" jdbcType="VARCHAR"
          property="mentorType"/>
        <result column="mentor_id" jdbcType="VARCHAR"
          property="mentorId"/>
        <result column="create_by" jdbcType="VARCHAR"
          property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP"
          property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR"
          property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP"
          property="updateTime"/>
        <result column="is_del" jdbcType="TINYINT"
          property="isDel"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , user_id, mentor_type, mentor_id, pro_id, create_by, create_time, update_by, update_time, is_del
    </sql>
    <select id="selectListByPage" parameterType="com.wunding.learn.project.service.admin.query.MentorQuery"
      resultType="com.wunding.learn.project.service.admin.dto.MentorPageDTO">
        select mentor.id id,
        mentor.user_id studentNo,
        mentor.mentor_id mentorNo,
        mentor.mentor_type mentorType
        from project_mentor_record mentor
        <where>
            mentor.is_del=0
            <if test="params.mentorId != null and params.mentorId !=''">
                and mentor.mentor_id=#{params.mentorId}
            </if>
            <if test="params.userId != null and params.userId !=''">
                and mentor.user_id=#{params.userId}
            </if>
            <if test="params.mentorTypeId != null and params.mentorTypeId !=''">
                and mentor.mentor_type=#{params.mentorTypeId}
            </if>
            <if test="params.projectId !=null and params.projectId !=''">
                and mentor.pro_id=#{params.projectId}
            </if>
        </where>

        order by mentor.create_time desc,mentor.id desc
    </select>
    <select id="getProjectUserPage" resultType="com.wunding.learn.project.service.admin.dto.MentorUserPageDTO"
      parameterType="com.wunding.learn.project.service.admin.query.MentorUserPageQuery">
        select pp.id,
        pp.user_id userId
        from project_progress pp
        <where>
            pp.pro_id = #{params.projectId}
            <if test="params.userIdsSet!=null and params.userIdsSet.size()>0">
                <foreach collection="params.userIdsSet" item="userId" open="(" close=")" separator="or">
                    pp.user_id =#{userId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="mentorRecordList" resultType="com.wunding.learn.project.service.model.MentorRecord">
        select distinct mentor_id from project_mentor_record where is_del=0 and pro_id=#{params.proId,jdbcType=VARCHAR}
        <if test="params.type == 0 ">
            and mentor_id in (
            <foreach collection="params.userIdList" item="mentorId" open="(" close=")" separator=",">
                #{mentorId}
            </foreach>
            )
        </if>
    </select>

    <select id="getMentorList" resultType="com.wunding.learn.project.service.client.dto.form.MentorRecordDTO">
        select a.mentor_id id
        from (select m.mentor_id
              from project_mentor_record m
              where m.is_del = 0
                and m.pro_id = #{projectId}
                and m.mentor_type = #{mentorTypeId}
                and m.user_id = #{userId}
              group by m.mentor_id) a
        where a.mentor_id != #{userId}
    </select>

    <select id="getMyStudentList" resultMap="BaseResultMap">
        select user_id, mentor_type
        from project_mentor_record
        where mentor_id = #{params.currentUserId}
        and is_del = 0
        <if test="params.userIdsVo != null and params.userIdsVo.size() > 0">
            <foreach collection="params.userIdsVo" item="uid" open="and user_id in (" close=")"
              separator=",">
                #{uid}
            </foreach>
        </if>
        group by user_id, mentor_type
    </select>

    <select id="getProjectNameList" resultType="com.wunding.learn.project.service.client.dto.LearnProjectDTO">
        select distinct a.id,
                        a.pro_name
        from (select distinct pro_id,
                              mentor_type
              from project_mentor_record m
              where m.is_del = 0
                and mentor_id = #{userId}) p
                 inner join project a on p.pro_id = a.id
            and a.is_del = 0
            and a.is_publish = 1
            and a.project_type in (0, 3)
    </select>

    <select id="selectCountByCondition" resultType="java.lang.Integer" parameterType="com.wunding.learn.project.service.model.MentorRecord">
        select count(1)
        from project_mentor_record
        where pro_id = #{proId}
          and user_id = #{userId}
          and mentor_type = #{mentorType}
          and mentor_id = #{mentorId}
          and is_del = 0
    </select>

    <select id="getUserMentor" resultType="java.lang.String">
        select mentor_id from project_mentor_record
        where user_id = #{userId}
          and pro_id = #{proId}
          and is_del = 0
        <if test="mentorType != null and mentorType != ''">
            and mentor_type = #{mentorType}
        </if>
    </select>

    <select id="getMyStudentListFromTaskUserMentor" resultMap="BaseResultMap2" useCache="false">
        select ptum.user_id, ptum.mentor_type
        from project_task_user_mentor ptum
        inner join project_task pt on pt.id = ptum.task_id
        inner join project p on p.id = pt.pro_id
        where ptum.mentor_id = #{params.currentUserId}
        and ptum.is_del = 0
        and ptum.is_return = 0
        and p.is_del = 0
        <if test="params.userIdsVo != null and params.userIdsVo.size() > 0">
            <foreach collection="params.userIdsVo" item="uid" open="and ptum.user_id in (" close=")"
              separator=",">
                #{uid}
            </foreach>
        </if>
        group by ptum.user_id, ptum.mentor_type
    </select>
</mapper>
