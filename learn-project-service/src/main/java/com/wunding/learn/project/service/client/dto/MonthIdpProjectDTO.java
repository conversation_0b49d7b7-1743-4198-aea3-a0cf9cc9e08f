package com.wunding.learn.project.service.client.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

/**
 * @Author: aixinrong
 * @Date: 2022/10/19 14:58
 */
@Data
@Schema(name = "MonthIdpProjectDTO", description = "月份分类idp项目对象")
public class MonthIdpProjectDTO {

    @Schema(description = "月份")
    private Integer month;

    @Schema(description = "项目对象")
    private List<IdpProjectDTO> idpList;
}
