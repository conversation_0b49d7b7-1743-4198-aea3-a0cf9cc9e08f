package com.wunding.learn.project.service.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 任务完成表
 *
 * <AUTHOR> href="mailto:<EMAIL>">赖卓成</a>
 * @since 2022-07-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("task_progress")
@Schema(name = "TaskProgress对象", description = "任务完成表")
public class TaskProgress implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * ID
     */
    @Schema(description = "ID")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;


    /**
     * 任务ID
     */
    @Schema(description = "任务ID")
    @TableField("task_id")
    private String taskId;


    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    @TableField("user_id")
    private String userId;

    /**
     * 用户名称
     */
    @Schema(description = "用户名称")
    @TableField("user_name")
    private String userName;


    /**
     * 用户部门ID
     */
    @Schema(description = "用户部门ID")
    @TableField("org_id")
    private String orgId;

    /**
     * 用户部门名称
     */
    @Schema(description = "组织名称")
    @TableField("org_name")
    private String orgName;


    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    @TableField("start_time")
    private Date startTime;


    /**
     * 完成时间
     */
    @Schema(description = "完成时间")
    @TableField("end_time")
    private Date endTime;


    /**
     * 是否完成
     */
    @Schema(description = "是否完成")
    @TableField("is_finish")
    private Integer isFinish;


    /**
     * 项目ID
     */
    @Schema(description = "项目ID")
    @TableField("pro_id")
    private String proId;


    /**
     * 实际开始时间
     */
    @Schema(description = "实际开始时间")
    @TableField("actual_start_time")
    private Date actualStartTime;


    /**
     * 实际结束时间
     */
    @Schema(description = "实际结束时间")
    @TableField("actual_end_time")
    private Date actualEndTime;


    /**
     * 是否展示过引导页
     */
    @Schema(description = "是否展示过引导页")
    @TableField("is_show_guide")
    private Integer isShowGuide;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;


    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 更新人
     */
    @Schema(description = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;


    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    @TableField("is_del")
    @TableLogic(value = "0", delval = "1")
    private Integer isDel;
    
    @Schema(description = "来源: 0-系统 1-导入")
    @TableField("source")
    private Integer source;
    
    
    @Schema(description = "说明")
    @TableField("explanation")
    private String explanation;


}
