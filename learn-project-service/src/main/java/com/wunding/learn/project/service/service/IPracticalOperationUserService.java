package com.wunding.learn.project.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.project.api.dto.ProjectPracticalOperationSuperviseInfoDTO;
import com.wunding.learn.project.api.dto.ProjectPracticalOperationUserInfoDTO;
import com.wunding.learn.project.service.admin.dto.PracticalOperationResultDTO;
import com.wunding.learn.project.service.admin.dto.PracticalOperationUserBySuperviseDTO;
import com.wunding.learn.project.service.admin.dto.PracticalOperationUserPageInfoDTO;
import com.wunding.learn.project.service.admin.query.PracticalOperationUserBySuperviseQuery;
import com.wunding.learn.project.service.admin.query.PracticalOperationUserQuery;
import com.wunding.learn.project.service.client.dto.PracticalOperationUserClientDTO;
import com.wunding.learn.project.service.client.query.PracticalOperationUserClientQuery;
import com.wunding.learn.project.service.model.PracticalOperationUser;
import java.util.Collection;
import java.util.List;

/**
 * <p> 实操监督人员表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">ydq</a>
 * @since 2024-03-13
 */
public interface IPracticalOperationUserService extends IService<PracticalOperationUser> {

    /**
     * 分页
     *
     * @param query 查询对象
     * @return {@link PageInfo}
     */
    PageInfo<PracticalOperationUserPageInfoDTO> queryPage(PracticalOperationUserQuery query);

    /**
     * 获取
     *
     * @param query 查询对象
     * @return {@link PageInfo}
     */
    PageInfo<PracticalOperationUserBySuperviseDTO> queryPageBySupervise(PracticalOperationUserBySuperviseQuery query);

    /**
     * 获取数量
     *
     * @param superviseId 评价id
     * @param status      状态
     * @return {@link Long}
     */
    Long getCountByStatus(String superviseId, Integer status);

    /**
     * 保存
     *
     * @param practicalOperationUser 保存对象
     */
    void savePracticalOperationUser(PracticalOperationUser practicalOperationUser);

    /**
     * 获取
     *
     * @param practicalId 实操id
     * @param userId      用户id
     * @return {@link PracticalOperationUser}
     */
    PracticalOperationUser getPracticalOperationUser(String practicalId, String userId);

    /**
     * 获取
     *
     * @param query 查询对象
     * @return {@link PageInfo}
     */
    PageInfo<PracticalOperationUserClientDTO> superviseUserList(PracticalOperationUserClientQuery query);

    /**
     * 获取list
     *
     * @param idList     id列表
     * @param userIdList 用户id列表
     * @return {@link List}
     */
    List<ProjectPracticalOperationUserInfoDTO> getListByParam(Collection<String> userIdList, Collection<String> idList);

    /**
     * 获取
     *
     * @param idList     id列表
     * @param userIdList 用户id列表
     * @return {@link List}
     */
    List<ProjectPracticalOperationSuperviseInfoDTO> getSuperviseListByParam(Collection<String> userIdList,
        Collection<String> idList);

    /**
     * 查看是否完成
     *
     * @param practicalId 实操id
     * @param userId      用户id
     * @return {@link Integer}
     */
    Integer getCount(String userId, String practicalId);

    /**
     * 批量获取用户实操结果（仅返回通过状态和得分）
     *
     * @param userId       用户id
     * @param practicalIds 实操id列表
     * @return {@link List} 用户实操结果列表
     */
    List<PracticalOperationResultDTO> getPracticalOperationResult(String userId, Collection<String> practicalIds);
}
