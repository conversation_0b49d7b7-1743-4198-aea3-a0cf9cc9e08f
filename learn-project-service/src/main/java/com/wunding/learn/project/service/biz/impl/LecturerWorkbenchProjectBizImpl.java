package com.wunding.learn.project.service.biz.impl;

import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.constant.other.CommonConstants;
import com.wunding.learn.common.constant.project.ProjectErrorNoEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.project.ProjectAppType;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.util.string.StringPool;
import com.wunding.learn.lecturer.api.dto.LecturerDTO;
import com.wunding.learn.lecturer.api.service.LecturerFeign;
import com.wunding.learn.project.service.biz.ILecturerWorkbenchProjectBiz;
import com.wunding.learn.project.service.client.dto.lecturerworkbench.ProjectAppInfoDTO;
import com.wunding.learn.project.service.client.dto.lecturerworkbench.ProjectInfoDTO;
import com.wunding.learn.project.service.client.dto.lecturerworkbench.ProjectTaskInfoDTO;
import com.wunding.learn.project.service.client.dto.lecturerworkbench.WorkbenchProjectPageDTO;
import com.wunding.learn.project.service.client.query.lecturerworkbench.LecturerProjectQuery;
import com.wunding.learn.project.service.client.query.lecturerworkbench.TaskOrAppListQuery;
import com.wunding.learn.project.service.component.ProjectViewLimitComponent;
import com.wunding.learn.project.service.model.Project;
import com.wunding.learn.project.service.service.IAppService;
import com.wunding.learn.project.service.service.IClassroomService;
import com.wunding.learn.project.service.service.IProjectService;
import com.wunding.learn.project.service.service.ITaskService;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @date 2023/1/11
 */
@Slf4j
@Service("lecturerWorkbenchProjectBiz")
public class LecturerWorkbenchProjectBizImpl implements ILecturerWorkbenchProjectBiz {

    @Resource
    private LecturerFeign lecturerFeign;

    @Resource
    private IProjectService projectService;

    @Resource
    private IClassroomService classroomService;

    @Resource
    private ProjectViewLimitComponent projectViewLimitComponent;

    @Resource(name = "projectTaskService")
    private ITaskService taskService;

    @Resource
    @Lazy
    private IAppService appService;

    @Override
    public PageInfo<WorkbenchProjectPageDTO> getLecturerWorkProjectPageDTOList(LecturerProjectQuery query) {
        query.setUserId(UserThreadContext.getUserId());
        LecturerDTO lecturer = lecturerFeign.getLecturerByUserId(UserThreadContext.getUserId());
        query.setLecturerId(Optional.ofNullable(lecturer).map(LecturerDTO::getId).orElse(StringPool.EMPTY));
        query.setResourceType(ProjectAppType.LECTURER.getNo());
        return projectService.selectLecturerWorkProjectPageDTOList(query);
    }

    @Override
    public ProjectInfoDTO getProjectInfo(String id) {
        Project project = Optional.ofNullable(projectService.getById(id))
            .orElseThrow(() -> new BusinessException(ProjectErrorNoEnum.PROJECT_NOT_EXIST));
        ProjectInfoDTO projectInfo = new ProjectInfoDTO();
        BeanUtils.copyProperties(project, projectInfo);
        if (StringUtils.isNotBlank(project.getRoom())) {
            Optional.ofNullable(classroomService.getById(project.getRoom()))
                .ifPresent(classroom -> projectInfo.setRoomName(classroom.getRoomName()));
        }
        projectInfo.setProjectItem(Arrays.stream(project.getProjectItem().split(CommonConstants.A_COMMA_IN_ENGLISH))
            .filter(item -> StringUtils.isNotBlank(item) && ProjectAppType.isLecturerWorkbenchApp(item))
            .collect(Collectors.toList()));
        List<String> viewLimitUser = projectViewLimitComponent.getViewLimitUser(id);
        projectInfo.setIssueUserCount(
            CollectionUtils.isEmpty(viewLimitUser) ? GeneralJudgeEnum.NEGATIVE.getValue() : viewLimitUser.size());
        return projectInfo;
    }

    @Override
    public PageInfo<ProjectTaskInfoDTO> getTaskList(TaskOrAppListQuery taskOrAppListQuery) {
        taskOrAppListQuery.setCurrentUserId(UserThreadContext.getUserId());
        return taskService.getSelfBuildTaskList(taskOrAppListQuery);
    }

    @Override
    public PageInfo<ProjectAppInfoDTO> getAppList(TaskOrAppListQuery taskOrAppListQuery) {
        taskOrAppListQuery.setCurrentUserId(UserThreadContext.getUserId());
        return appService.getSelfBuildAppList(taskOrAppListQuery);
    }
}
