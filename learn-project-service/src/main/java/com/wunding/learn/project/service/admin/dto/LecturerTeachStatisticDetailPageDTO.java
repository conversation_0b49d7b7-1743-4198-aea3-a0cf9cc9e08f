package com.wunding.learn.project.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/3/8
 */
@Schema(name = "LecturerTeachStatisticDetailPageDTO", description = "讲师培训统计明细分页数据对象")
@Data
@Accessors(chain = true)
public class LecturerTeachStatisticDetailPageDTO {

    @Schema(description = "计划编号")
    private String planNo;

    @Schema(description = "计划培训类型")
    private String planTrainCategory;

    @Schema(description = "项目名称")
    private String proName;

    @Schema(description = "项目编号")
    private String proNo;

    @Schema(description = "课程任务名称")
    private String courseTaskName;

    @Schema(description = "课程等级")
    private String courseLevel;

    @Schema(description = "课程任务开始时间")
    private Date taskStartTime;

    @Schema(description = "课程任务结束时间")
    private Date taskEndTime;

    // 学习项目需要增加该属性 手动输入
    @Schema(description = "项目对象")
    private String proObject;

    // 暂定学习项目的参加人数，也有可能是课程任务的参加人数  有实际开始时间就算参加课程任务
    @Schema(description = "参训人数")
    private Integer joinNum = 0;

    @Schema(description = "授课(小时)")
    private BigDecimal instructionTime;

    @Schema(description = "评估分(小数 保留两位小数)")
    private BigDecimal evalScore;
}
