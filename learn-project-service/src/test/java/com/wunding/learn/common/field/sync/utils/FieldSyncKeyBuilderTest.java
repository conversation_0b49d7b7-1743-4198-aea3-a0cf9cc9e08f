//package com.wunding.learn.common.field.sync.utils;
//
//import com.wunding.learn.project.service.LearnProjectServiceApplication;
//import jakarta.annotation.Resource;
//import org.junit.jupiter.api.MethodOrderer;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.TestMethodOrder;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit.jupiter.SpringExtension;
//
//@ExtendWith(SpringExtension.class)
//@SpringBootTest(
//    classes = LearnProjectServiceApplication.class,
//    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
//@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
//class FieldSyncKeyBuilderTest {
//
//    @Resource
//    private FieldSyncKeyBuilder fieldSyncKeyBuilder;
//
//    @Test
//    public void test() {
//
//        for (int i = 0; i < 100000; i++) {
//            new Thread(() -> {
//                String key = null;
//                synchronized (this) {
//                    key = fieldSyncKeyBuilder
//                        .toExam().toProject().toSurvey().getKey();
//                }
//
//                log.info(
//                    "routingKey = " + key);
//            }).start();
//        }
//    }
//
//}