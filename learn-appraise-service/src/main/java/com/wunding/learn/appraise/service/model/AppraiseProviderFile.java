package com.wunding.learn.appraise.service.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 学员材料表
 *
 * <AUTHOR> href="mailto:<EMAIL>">xiao8</a>
 * @since 2022-08-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("appraise_provider_file")
@Schema(name = "AppraiseProviderFile", description = "学员材料表 ")
public class AppraiseProviderFile implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;


    /**
     * 评价表id
     */
    @Schema(description = "评价表id  ")
    @TableField("appraise_id")
    private String appraiseId;


    /**
     * 评价类型表id
     */
    @Schema(description = "评价类型表id")
    @TableField("file_type_id")
    private String fileTypeId;


    /**
     * 被评人id
     */
    @Schema(description = "被评人id")
    @TableField("provider_id")
    private String providerId;


    /**
     * 课件真实时长（视频/mp3）
     */
    @Schema(description = "课件真实时长（视频/mp3）")
    @TableField("play_time")
    private Integer playTime;


    /**
     * 文件媒体类型
     */
    @Schema(description = "文件媒体类型")
    @TableField("mime")
    private String mime;


    /**
     * 是否已经入库课件 0-否 1-是;课程评估时有用到
     */
    @Schema(description = "是否已经入库课件 0-否 1-是;课程评估时有用到")
    @TableField("is_enter_storage")
    private Integer isEnterStorage;


    /**
     * 1-转码中 2-转码成功 3-转码失败 4-排队中
     */
    @Schema(description = "1-转码中 2-转码成功 3-转码失败 4-排队中")
    @TableField("transform_status")
    private Integer transformStatus;


    /**
     * 课件类型  与courseware 表cwType保持一致
     */
    @Schema(description = "课件类型  与courseware 表cwType保持一致")
    @TableField("cw_type")
    private String cwType;


    /**
     * 材料排序
     */
    @Schema(description = "材料排序")
    @TableField("sort")
    private Integer sort;


    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;


    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 修改人
     */
    @Schema(description = "修改人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;


    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;



}
