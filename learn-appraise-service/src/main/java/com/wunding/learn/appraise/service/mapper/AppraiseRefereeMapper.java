package com.wunding.learn.appraise.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.appraise.service.admin.dto.AppraiseRefereeDTO;
import com.wunding.learn.appraise.service.model.AppraiseReferee;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import java.util.List;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 评价人（评委） Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">xiao8</a>
 * @since 2022-08-08
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")
})
public interface AppraiseRefereeMapper extends BaseMapper<AppraiseReferee> {

    /**
     * 评委分页查询
     *
     * @param appraiseRefereeVo 查询参数
     * @return
     */
    List<AppraiseRefereeDTO> queryPage(@Param("params") AppraiseRefereeDTO appraiseRefereeVo);

    /**
     * 根据关键字查询评价人关联的所有评审id
     *
     * @param keyWord 查询关键字
     * @return
     */
    List<String> selectAppraiseId(String keyWord);
}
