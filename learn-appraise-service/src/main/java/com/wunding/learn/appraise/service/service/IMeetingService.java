package com.wunding.learn.appraise.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.appraise.api.dto.AppraiseRefereeDTO;
import com.wunding.learn.appraise.api.dto.MeetingInfoDTO;
import com.wunding.learn.appraise.service.admin.dto.MeetingListDTO;
import com.wunding.learn.appraise.service.admin.dto.MeetingListQuery;
import com.wunding.learn.appraise.service.admin.dto.MeetingPublicityDTO;
import com.wunding.learn.appraise.service.admin.dto.PublishAppraiseDTO;
import com.wunding.learn.appraise.service.admin.dto.SaveMeetingDTO;
import com.wunding.learn.appraise.service.client.dto.AppraiseDTO;
import com.wunding.learn.appraise.service.client.dto.AppraiseQuery;
import com.wunding.learn.appraise.service.client.dto.AppraiseRefereeDetailDTO;
import com.wunding.learn.appraise.service.client.dto.MaterialsPreviewDTO;
import com.wunding.learn.appraise.service.client.dto.MeetingClientDetailDTO;
import com.wunding.learn.appraise.service.client.dto.MeetingScoreListDTO;
import com.wunding.learn.appraise.service.client.dto.ParticipatorDTO;
import com.wunding.learn.appraise.service.model.Meeting;
import com.wunding.learn.common.bean.Result;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import org.springframework.scheduling.annotation.Async;

/**
 * <p> 评价表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">xiao8</a>
 * @since 2022-08-08
 */
public interface IMeetingService extends IService<Meeting> {

    /**
     * 评价分页查询
     *
     * @param query 查询参数
     * @return
     */
    PageInfo<MeetingListDTO> queryPage(MeetingListQuery query);

    /**
     * 新增评价
     *
     * @param saveMeetingDTO
     */
    String saveMeeting(SaveMeetingDTO saveMeetingDTO);

    /**
     * 更新评价
     *
     * @param saveMeetingDTO
     */
    void updateMeeting(SaveMeetingDTO saveMeetingDTO);

    /**
     * 发布或者取消发布评价
     *
     * @param publishAppraiseDTO 参数对象
     */
    void publishOrUnPublish(PublishAppraiseDTO publishAppraiseDTO);

    /**
     * 删除评价
     *
     * @param ids 评价id 逗号分割
     */
    void deleteMeeting(String ids);

    /**
     * 判断按钮在评分时间开始后能否修改
     *
     * @param id 评价id
     * @return 能否进行操作 0-否 1-是
     */
    Integer canOperate(String id);

    /**
     * 获取评价列表
     *
     * @param pageNo     页码
     * @param pageSize   分页大小
     * @param appraiseAo 评价
     * @return Result<PageModel < AppraiseVO>>
     */
    Result<PageInfo<AppraiseDTO>> getAppraiseList(int pageNo, int pageSize, AppraiseQuery appraiseAo);

    /**
     * 获取评价详情
     *
     * @param meetingId 评价id
     * @return Result<AppraiseDetailVO>
     */
    SaveMeetingDTO getSaveAppraiseDetail(String meetingId);

    /**
     * 客户端获取详情
     *
     * @param meetingId
     * @return
     */
    Result<MeetingClientDetailDTO> getClientAppraiseDetail(String meetingId);

    /**
     * 分页查询待评人 待评人需要提交完材料
     *
     * @param pageNo     页码
     * @param pageSize   分页大小
     * @param appraiseId 评价id
     * @return Result<PageModel < Participator>>
     */
    Result<PageInfo<ParticipatorDTO>> getProviders(int pageNo, int pageSize, String appraiseId);


    /**
     * 被评人-查询评价明细
     *
     * @param appraiseId 评价id
     * @return Result<AppraiseRefereeDetailVO>
     */
    Result<AppraiseRefereeDetailDTO> getAppraiseRefereeDetail(String appraiseId);

    /**
     * 评委查询评价页
     *
     * @param appraiseId 评价id
     * @param providerId 被评人id
     * @return {@link AppraiseRefereeDTO}
     */
    AppraiseRefereeDTO getMeetingReferees(String appraiseId, String providerId);

    /**
     * 评委-查询评价明细
     *
     * @param appraiseId 评价id
     * @param providerId 被评人id
     * @return Result<AppraiseRefereeDetailVO>
     */
    Result<AppraiseRefereeDetailDTO> getBeAppraiseRefereeDetail(String appraiseId, String providerId);

    /**
     * 评委提交评价
     *
     * @param appraiseScoreListAO appraiseScoreListAO
     * @return Result
     */
    Result<Void> submitAppraiseScore(MeetingScoreListDTO appraiseScoreListAO);

    /**
     * 导出评价管理列表的数据
     *
     * @param appraiseListQuery
     * @return
     */
    @Async
    void export(MeetingListQuery appraiseListQuery);

    /**
     * 查询预览详情
     *
     * @param providerFileId
     * @return
     */
    Result<MaterialsPreviewDTO> getMaterialsPreviewVO(String providerFileId);

    /**
     * 获取
     *
     * @param qualificationIdList
     * @return
     */
    List<MeetingInfoDTO> getMeetingInfoList(Collection<String> qualificationIdList);


    /**
     * 根据任职资格获取答辩
     *
     * @param qualificationId
     * @return
     */
    MeetingInfoDTO getMeetingInfoByQualificationId(String qualificationId);
}
