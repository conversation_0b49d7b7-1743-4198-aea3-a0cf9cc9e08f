package com.wunding.learn.appraise.service.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 评价材料类型
 *
 * <AUTHOR> href="mailto:<EMAIL>">xiao8</a>
 * @since 2022-08-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("appraise_file_type")
@Schema(name = "AppraiseFileType", description = "评价材料类型")
public class AppraiseFileType implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;


    /**
     * 评价表id
     */
    @Schema(description = "评价表id")
    @TableField("appraise_id")
    private String appraiseId;


    /**
     * 材料标题
     */
    @Schema(description = "材料标题")
    @TableField("title")
    private String title;


    /**
     * 参考样例文件
     */
    @Schema(description = "参考样例文件")
    @TableField("example_file")
    private String exampleFile;


    /**
     * 材料提供说明
     */
    @Schema(description = "材料提供说明")
    @TableField("description")
    private String description;


    /**
     * 案例文件转码状态 1-转码中 2-转码成功 3-转码失败 4-排队中
     */
    @Schema(description = "案例文件转码状态 1-转码中 2-转码成功 3-转码失败 4-排队中")
    @TableField("transform_status")
    private Integer transformStatus;


    /**
     * 样例文件类型
     */
    @Schema(description = "样例文件类型")
    @TableField("example_file_type")
    private String exampleFileType;


    /**
     * 样例文件mime
     */
    @Schema(description = "样例文件mime")
    @TableField("mime")
    private String mime;


    /**
     * 材料支持类型(1:mp4,2:mp3,3:jpg/png,4:pdf,5:zip,6:word) 多个的话 逗号分割
     */
    @Schema(description = "材料支持类型(1:mp4,2:mp3,3:jpg/png,4:pdf,5:zip,6:word) 多个的话 逗号分割")
    @TableField("file_type")
    private String fileType;


    /**
     * 是否必填(0:否,1:是)
     */
    @Schema(description = "是否必填(0:否,1:是)")
    @TableField("required")
    private Integer required;


    /**
     * 是否待认证课件(0:否,1:是)
     */
    @Schema(description = "是否待认证课件(0:否,1:是)")
    @TableField("is_save_lib")
    private Integer isSaveLib;


    /**
     * 文件大小限制 单位 M
     */
    @Schema(description = "文件大小限制 单位 M")
    @TableField("file_max_size")
    private Integer fileMaxSize;


    /**
     * 排序
     */
    @Schema(description = "排序")
    @TableField("sort")
    private Integer sort;


    /**
     * 是否删除(1:已删除,0未删除)
     */
    @Schema(description = "是否删除(1:已删除,0未删除)")
    @TableField("is_del")
    @TableLogic(value = "0", delval = "1")
    private Integer isDel;


    /**
     * 下发方式：0 部分可见 1仅创建者可见 2所有人可见
     */
    @Schema(description = "下发方式：0 部分可见 1仅创建者可见 2所有人可见")
    @TableField("view_type")
    private Integer viewType;


    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;


    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 修改人
     */
    @Schema(description = "修改人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;


    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;



}
