package com.wunding.learn.evaluation.service.feign;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.wunding.learn.evaluation.api.dto.EvalQuestionFeignDTO;
import com.wunding.learn.evaluation.api.service.EvaluationQuestionFeign;
import com.wunding.learn.evaluation.service.model.EvalQuestion;
import com.wunding.learn.evaluation.service.service.IEvalQuestionService;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/12/12
 */
@RestController
@RequestMapping("${module.evaluation.contentPath:/}")
public class EvaluationQuestionFeignImpl implements EvaluationQuestionFeign {

    @Resource
    private IEvalQuestionService evalQuestionService;


    @Override
    public List<EvalQuestionFeignDTO> getEvalQuestionDTOList(String evalId) {
        return evalQuestionService.getEvalQuestionFeignDTOList(evalId);
    }

    @Override
    public Map<String, Integer> getEvalQuestionCountMap(Collection<String> evalIds) {
        if (CollectionUtils.isEmpty(evalIds)) {
            return Collections.emptyMap();
        }
        Set<String> evalIdSet = evalIds.stream().collect(Collectors.toSet());
        Map<String, Integer> map = new HashMap<>(evalIds.size());
        List<EvalQuestion> evalQuestions = evalQuestionService.list(
            new LambdaQueryWrapper<EvalQuestion>().in(EvalQuestion::getEvalId, evalIdSet));
        Map<String, List<EvalQuestion>> listMap = evalQuestions.stream()
            .collect(Collectors.groupingBy(EvalQuestion::getEvalId));

        for (String evalId : evalIdSet) {
            List<EvalQuestion> list = listMap.get(evalId);
            map.put(evalId, CollectionUtils.isEmpty(list) ? 0 : list.size());
        }
        return map;
    }
}
