package com.wunding.learn.evaluation.service.client.dto;

import com.wunding.learn.evaluation.service.admin.dto.OptionScoreDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @program: mlearn
 * @description: <p>提交评估对象</p>
 * @author: 赖卓成
 * @create: 2022-08-11 10:28
 **/
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(name = "EvaluationReplyDTO", description = "提交评估对象")
public class EvaluationReplyDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 评估问题id
     */
    @Schema(description = "评估问题id")
    private String questionId;

    /**
     * 评估答案id
     */
    @Schema(description = "评估答案id(用户所选的答案id)、单选一个值、多选多个值、判断一个值")
    private List<OptionScoreDTO> optionScore;

    /**
     * 评估答案内容(填空、问答题)
     */
    @Schema(description = "评估答案内容(填空、问答题)")
    private String content;


}
