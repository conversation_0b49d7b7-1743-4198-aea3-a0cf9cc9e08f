<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>learn-tenant</artifactId>
        <groupId>com.wunding</groupId>
        <version>${revision}</version>
    </parent>
    <artifactId>learn-tenant-service</artifactId>
    <version>${tenant.version}</version>
    <name>learn-tenant-service</name>
    <description>learn-tenant-service</description>

    <dependencies>

        <dependency>
            <groupId>com.wunding</groupId>
            <artifactId>learn-common-i18n</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wunding</groupId>
            <artifactId>learn-common-exception</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wunding</groupId>
            <artifactId>learn-common-swagger</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wunding</groupId>
            <artifactId>learn-common-mq</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wunding</groupId>
            <artifactId>learn-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wunding</groupId>
            <artifactId>learn-common-util</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wunding</groupId>
            <artifactId>learn-common-bean</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-api</artifactId>
        </dependency>


        <dependency>
            <artifactId>commons-lang3</artifactId>
            <groupId>org.apache.commons</groupId>
        </dependency>

        <dependency>
            <artifactId>pagehelper</artifactId>
            <groupId>com.github.pagehelper</groupId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>transmittable-thread-local</artifactId>
            <version>2.14.2</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <artifactId>micrometer-registry-prometheus</artifactId>
            <groupId>io.micrometer</groupId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-3-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.mybatis</groupId>
                    <artifactId>mybatis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mybatis</groupId>
                    <artifactId>mybatis-spring</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <artifactId>spring-boot-starter-validation</artifactId>
            <groupId>org.springframework.boot</groupId>
        </dependency>

        <dependency>
            <groupId>com.wunding</groupId>
            <artifactId>learn-common-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>
        
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
        </dependency>
        <dependency>
            <groupId>net.sourceforge.jexcelapi</groupId>
            <artifactId>jxl</artifactId>
            <version>2.6.12</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.sun.mail</groupId>
            <artifactId>jakarta.mail</artifactId>
            <version>2.0.1</version>
        </dependency>
<!--        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore</artifactId>
        </dependency>-->
<!--        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>-->
        <dependency>
            <groupId>org.apache.httpcomponents.client5</groupId>
            <artifactId>httpclient5</artifactId>
            <version>5.2.1</version>
        </dependency>

        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>s3</artifactId>
        </dependency>

        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio-admin</artifactId>
            <version>8.5.2</version>
            <exclusions>
                <exclusion>
                    <groupId>com.squareup.okhttp3</groupId>
                    <artifactId>okhttp</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.squareup.okhttp3/okhttp -->
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>${okhttp.version}</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.fasterxml.jackson.dataformat/jackson-dataformat-xml -->
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wunding</groupId>
            <artifactId>learn-tenant-api</artifactId>
        </dependency>

    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*</include>
                </includes>
                <filtering>false</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>io.fabric8</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <version>0.45.0</version>
                <configuration>
                    <skip>${skipDockerImage}</skip>
                    <!-- Docker 推送镜像仓库地址-->
                    <pushRegistry>${image.registry}</pushRegistry>
                    <images>
                        <image>
                            <!--由于推送到私有镜像仓库，镜像名需要添加仓库地址-->
                            <name>${image.registry}${image.registry.path}tenant:${image.tag}</name>
                            <!--定义镜像构建行为-->
                            <build>
                                <buildx>
                                    <builderName>mybuilder</builderName>
                                    <platforms>
                                        <platform>${docker.platforms}</platform>
                                    </platforms>
                                </buildx>
                                <dockerFileDir>${project.basedir}</dockerFileDir>
                                <dockerFile>${docker.file}</dockerFile>
                            </build>
                        </image>
                    </images>
                </configuration>
                <executions>
                    <execution>
                        <!--在哪个生命周期阶段执行-->
                        <phase>install</phase>
                        <!--执行别名-->
                        <id>build-image</id>
                        <goals>
                            <!--插件目标-->
                            <goal>build</goal>
                            <goal>push</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <!--开启分层编译支持-->
                    <layers>
                        <enabled>true</enabled>
                    </layers>
                    <skip>${spring-boot.repackage.skip}</skip>
                    <finalName>${project.name}-${revision}-fat</finalName>
                </configuration>
                <groupId>org.springframework.boot</groupId>
                <version>${spring-boot.version}</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
    
</project>