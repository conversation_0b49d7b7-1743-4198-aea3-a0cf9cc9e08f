package com.wunding.learn.tenant.service.service.impl;

import java.util.List;
import jakarta.annotation.Resource;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

@SpringBootTest
@Rollback
@Transactional(rollbackFor = Exception.class)
class TenantDbInstanceServiceImplTest {

    // 都只是写查询操作,直接真实处理,不mock
    @Resource
    private TenantDbInstanceServiceImpl tenantDbInstanceService;

    @Test
    void getDbInstanceList() {
        Assertions.assertThat(tenantDbInstanceService.getDbInstanceList("2023031516251313edd0c449b998509c6327")).isNotEmpty();
    }

    @Test
    void getDbInstanceListEmpty() {
        Assertions.assertThat(tenantDbInstanceService.getDbInstanceList("666666")).isEmpty();
    }

    @Test
    void testGetDbInstanceListEmpty() {
        Assertions.assertThat(tenantDbInstanceService.getDbInstanceList(List.of("6666666666"))).isEmpty();
    }

    @Test
    void testGetDbInstanceList() {
        Assertions.assertThat(tenantDbInstanceService.getDbInstanceList(List.of("2023031516251313edd0c449b998509c6327"))).isNotEmpty();
    }
}