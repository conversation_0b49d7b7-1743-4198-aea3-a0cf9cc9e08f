//package com.wunding.learn.tenant.service.service.impl;
//
//import static org.assertj.core.api.Assertions.assertThat;
//import static org.assertj.core.api.Assertions.assertThatThrownBy;
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.ArgumentMatchers.anyMap;
//import static org.mockito.ArgumentMatchers.anyString;
//import static org.mockito.Mockito.doNothing;
//import static org.mockito.Mockito.doReturn;
//import static org.mockito.Mockito.mock;
//import static org.mockito.Mockito.verify;
//import static org.mockito.Mockito.when;
//
//import com.wunding.learn.common.context.user.UserThreadContext;
//import com.wunding.learn.common.exception.BusinessException;
//import com.wunding.learn.common.mq.service.MqProducer;
//import com.wunding.learn.common.util.bean.SpringUtil;
//import com.wunding.learn.common.util.json.JsonUtil;
//import com.wunding.learn.tenant.service.admin.dto.ResponseContentDTO;
//import com.wunding.learn.tenant.service.admin.dto.SaveTenantDTO;
//import com.wunding.learn.tenant.service.admin.dto.TenantConfigDTO;
//import com.wunding.learn.tenant.service.admin.query.CheckRepeatQueryDTO;
//import com.wunding.learn.tenant.service.admin.query.TenantListQueryDTO;
//import com.wunding.learn.tenant.service.mapper.TenantMapper;
//import com.wunding.learn.tenant.service.model.Tenant;
//import com.wunding.learn.tenant.service.model.TenantDeploymentStatus;
//import com.wunding.learn.tenant.service.service.IImagesService;
//import com.wunding.learn.tenant.service.service.ITenantBucketService;
//import com.wunding.learn.tenant.service.service.ITenantConfigService;
//import com.wunding.learn.tenant.service.service.ITenantDbInstanceService;
//import com.wunding.learn.tenant.service.service.ITenantModuleService;
//import com.wunding.learn.tenant.service.util.HttpUtil;
//import com.wunding.learn.tenant.service.util.ImageUtil;
//import com.wunding.learn.tenant.service.util.MinioUtil;
//import com.wunding.learn.tenant.service.util.UrlHelper;
//import java.io.File;
//import java.io.IOException;
//import java.nio.charset.StandardCharsets;
//import java.text.ParseException;
//import java.text.SimpleDateFormat;
//import java.util.ArrayList;
//import java.util.Collections;
//import java.util.Date;
//import java.util.List;
//import jakarta.annotation.Resource;
//import javax.script.ScriptEngine;
//import javax.script.ScriptEngineManager;
//import javax.script.ScriptException;
//import org.apache.commons.io.FileUtils;
//import org.junit.jupiter.api.AfterEach;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.MockedStatic;
//import org.mockito.Mockito;
//import org.mockito.MockitoAnnotations;
//import org.mockito.Spy;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.core.io.ResourceLoader;
//import org.springframework.data.redis.core.HashOperations;
//import org.springframework.data.redis.core.RedisTemplate;
//import org.springframework.test.annotation.Rollback;
//import org.springframework.test.util.ReflectionTestUtils;
//import org.springframework.transaction.annotation.Transactional;
//
//@SpringBootTest
//@Transactional(rollbackFor = Exception.class)
//@Rollback
//class TenantServiceImplTest {
//
//    MockedStatic<UserThreadContext> userThreadContextMock;
//    MockedStatic<HttpUtil> httpUtilMockedStatic;
//    AutoCloseable autoCloseable;
//
//    /**
//     * spy 和 mock 的区别是，mock 是完全模拟一个对象， 而 spy 是部分模拟一个对象。当你使用 mock 时， 你需要为每个方法都定义行为，否则它们什么都不做。 当你使用 spy
//     * 时，你只需要为你想要改变的方法定义行为, 不过因为接口没有实现逻辑,所以不打桩模拟的时候,接口方法永远返回null (坑)
//     */
//    @Spy
//    @InjectMocks
//    private TenantServiceImpl tenantService;
//
//    @Mock
//    private MqProducer rabbitMqProducer;
//
//    @Mock
//    @SuppressWarnings("rawtypes")
//    private RedisTemplate redisTemplate;
//
//    @Mock
//    private MinioUtil minioUtil;
//
//    @Mock
//    @SuppressWarnings("rawtypes")
//    private HashOperations hashOperations;
//
//    @Mock
//    private TenantMapper baseMapper;
//
//    @Resource
//    private ITenantConfigService tenantConfigService;
//
//    @Resource
//    private ITenantModuleService tenantModuleService;
//
//    @Resource
//    private ITenantDbInstanceService tenantDbInstanceService;
//
//    @Resource
//    private TenantDeploymentStatusServiceImpl tenantDeploymentStatusService;
//
//    @Resource
//    private IImagesService imagesService;
//
//    @Resource
//    private UrlHelper urlHelper;
//
//    @Resource
//    private ITenantBucketService tenantBucketService;
//
//    @Resource
//    private ImageUtil imageUtil;
//
//    @Autowired
//    ResourceLoader resourceLoader;
//
//    @BeforeEach
//    @SuppressWarnings("unchecked")
//    void setUp() throws Exception {
//
//        // InjectMocks必须加上
//        this.autoCloseable = MockitoAnnotations.openMocks(this);
//
//        // mock用户静态方法
//        this.userThreadContextMock = Mockito.mockStatic(UserThreadContext.class);
//        this.userThreadContextMock.when(UserThreadContext::getUserId).thenReturn("admin");
//        this.userThreadContextMock.when(UserThreadContext::getOrgId).thenReturn("0");
//
//        // mock Http静态方法
//        this.httpUtilMockedStatic = Mockito.mockStatic(HttpUtil.class);
//        ResponseContentDTO responseContentDTO = new ResponseContentDTO().setDomain("11").setId(1).setSlug("11")
//            .setName("111");
//        this.httpUtilMockedStatic.when(() -> HttpUtil.sendHttpPost(any(), anyMap(), anyString()))
//            .thenReturn(JsonUtil.objToJson(responseContentDTO));
//
//        // mock无关的中间件操作
//        when(redisTemplate.opsForHash()).thenReturn(hashOperations);
//        doReturn(1L).when(redisTemplate).unlink(any());
//        doNothing().when(hashOperations).put(anyString(), anyString(), anyString());
//        doNothing().when(rabbitMqProducer).sendMsg(any());
//        doNothing().when(minioUtil).initMinioBucket(any(String.class));
//
//        // 通过反射注入不需要mock的bean，通过Spring测试框架提供的工具类为目标对象私有属性赋值,这里就会执行真实的方法
//        ReflectionTestUtils.setField(tenantService, "tenantDeploymentStatusService", tenantDeploymentStatusService);
//        ReflectionTestUtils.setField(tenantService, "imageUtil", imageUtil);
//        ReflectionTestUtils.setField(tenantService, "tenantBucketService", tenantBucketService);
//        ReflectionTestUtils.setField(tenantService, "urlHelper", urlHelper);
//        ReflectionTestUtils.setField(tenantService, "imagesService", imagesService);
//        ReflectionTestUtils.setField(tenantService, "tenantDbInstanceService", tenantDbInstanceService);
//        ReflectionTestUtils.setField(tenantService, "tenantModuleService", tenantModuleService);
//        ReflectionTestUtils.setField(tenantService, "tenantConfigService", tenantConfigService);
//
//    }
//
//    @AfterEach
//    void tearDown() throws Exception {
//        this.userThreadContextMock.close();
//        this.httpUtilMockedStatic.close();
//        this.autoCloseable.close();
//    }
//
//    @Test
//    void tenantDetailDateTransform() throws ParseException {
//        String tenantId = "2023021515115764f96ccfaf64f4d9082d1e";
//
//        Tenant tenant = new Tenant()
//            .setId("2023021515115764f96ccfaf64f4d9082d1e")
//            .setLoginName("*********")
//            .setCompanyName("*********")
//            .setCustomCode("T1")
//            .setCompanyShortName("*********")
//            .setApplicationPlatformName("*********")
//            .setSlogan("*********")
//            .setPassword("123456")
//            .setTenantStartTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse("2023-02-15 00:00:00"))
//            .setTenantEndTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse("2023-02-15 00:00:00"))
//            .setTelephone("*********")
//            .setEmail("*********")
//            .setCompanyAddress("*********")
//            .setCompanyDomainName("*********")
//            .setColor("#1E90FFFF")
//            .setBackgroundColor("#1E90FFFF")
//            .setIsAvailable(1)
//            .setIsDel(0)
//            .setCreateBy("admin")
//            .setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse("2023-02-15 00:00:00"))
//            .setUpdateBy("admin")
//            .setUpdateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse("2023-02-15 00:00:00"));
//
//        doReturn(tenant).when(baseMapper).selectById(any());
//        tenantService.tenantDetailDateTransform(tenantId);
//        verify(tenantService).tenantDetailDateTransform(tenantId);
//    }
//
//    @Test
//    void tenantDetailDateTransformStatusZero() throws ParseException {
//        String tenantId = "2023021515115764f96ccfaf64f4d9082d1e";
//
//        Tenant tenant = new Tenant()
//            .setId("2021050710474300995c734e7a9f8e39ddd5")
//            .setLoginName("*********")
//            .setCompanyName("*********")
//            .setCustomCode("T1")
//            .setCompanyShortName("*********")
//            .setApplicationPlatformName("*********")
//            .setSlogan("*********")
//            .setPassword("123456")
//            .setTenantStartTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse("2023-02-15 00:00:00"))
//            .setTenantEndTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse("2023-02-15 00:00:00"))
//            .setTelephone("*********")
//            .setEmail("*********")
//            .setCompanyAddress("*********")
//            .setCompanyDomainName("*********")
//            .setColor("#1E90FFFF")
//            .setBackgroundColor("#1E90FFFF")
//            .setIsAvailable(1)
//            .setIsDel(0)
//            .setCreateBy("admin")
//            .setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse("2023-02-15 00:00:00"))
//            .setUpdateBy("admin")
//            .setUpdateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse("2023-02-15 00:00:00"));
//
//        TenantDeploymentStatus tenantDeploymentStatus = new TenantDeploymentStatus()
//            .setId("666")
//            .setTenantId("2021050710474300995c734e7a9f8e39ddd5")
//            .setStatus(0)
//            .setContainerId("1")
//            .setCreateBy("admin")
//            .setCreateTime(new Date())
//            .setUpdateBy("admin")
//            .setUpdateTime(new Date());
//
//        doReturn(tenant).when(baseMapper).selectById(any());
//        TenantDeploymentStatusServiceImpl mock = mock(TenantDeploymentStatusServiceImpl.class);
//        doReturn(tenantDeploymentStatus).when(mock).getDeploymentStatus(anyString());
//        ReflectionTestUtils.setField(tenantService, "tenantDeploymentStatusService", mock);
//        tenantService.tenantDetailDateTransform(tenantId);
//        verify(tenantService).tenantDetailDateTransform(tenantId);
//    }
//
//    @Test
//    void tenantDetailDateTransformThrow() {
//        String tenantId = "***********";
//        assertThatThrownBy(() -> tenantService.tenantDetailDateTransform(tenantId)).isInstanceOf(
//            BusinessException.class);
//    }
//
//    @Test
//    void tenantDetailDateTransformEmptyModuleThrow() throws ParseException {
//        String tenantId = "6666";
//        Tenant tenant = new Tenant()
//            .setId("6666")
//            .setLoginName("6666")
//            .setCompanyName("手动添加的脏数据")
//            .setCustomCode("6666")
//            .setCompanyShortName("*********")
//            .setApplicationPlatformName("*********")
//            .setSlogan("*********")
//            .setPassword("123456")
//            .setTenantStartTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse("2023-02-15 00:00:00"))
//            .setTenantEndTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse("2023-02-15 00:00:00"))
//            .setTelephone("*********")
//            .setEmail("*********")
//            .setCompanyAddress("*********")
//            .setCompanyDomainName("*********")
//            .setColor("#1E90FFFF")
//            .setBackgroundColor("#1E90FFFF")
//            .setIsAvailable(1)
//            .setIsDel(0)
//            .setCreateBy("admin")
//            .setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse("2023-02-15 00:00:00"))
//            .setUpdateBy("admin")
//            .setUpdateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse("2023-02-15 00:00:00"));
//
//        doReturn(tenant).when(baseMapper).selectById(any());
//        assertThatThrownBy(() -> tenantService.tenantDetailDateTransform(tenantId)).isInstanceOf(
//            BusinessException.class);
//    }
//
//    @Test
//    void testTenantGetByIds() throws ScriptException, IOException, ParseException {
//        List<String> userIds = List.of("admin");
//
//        // 使用mock.js工具生成mock数据
//        ScriptEngine engine = new ScriptEngineManager().getEngineByName("JavaScript");
//        File file = resourceLoader.getResource("classpath:/js/mock-min.js").getFile();
//        String scriptString = FileUtils.readFileToString(file, StandardCharsets.UTF_8);
//        engine.eval(scriptString);
//
//        List<Tenant> value = new ArrayList<>();
//        Tenant tenant = new Tenant()
//            .setId(engine.eval("Mock.Random.id()").toString())
//            .setLoginName(engine.eval("Mock.Random.cname()").toString())
//            .setCompanyName(engine.eval("Mock.Random.string('lower',5)").toString())
//            .setCustomCode(engine.eval("Mock.Random.string('lower',8)").toString())
//            .setCompanyShortName(engine.eval("Mock.Random.cname()").toString())
//            .setApplicationPlatformName(engine.eval("Mock.Random.cname()").toString())
//            .setSlogan(engine.eval("Mock.Random.cname()").toString())
//            .setPassword(engine.eval("Mock.Random.string()").toString())
//            .setTenantStartTime(
//                new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(engine.eval("Mock.Random.datetime()").toString()))
//            .setTenantEndTime(
//                new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(engine.eval("Mock.Random.datetime()").toString()))
//            .setTelephone(engine.eval("Mock.Random.integer()").toString())
//            .setEmail(engine.eval("Mock.Random.email()").toString())
//            .setCompanyAddress(engine.eval("Mock.Random.string()").toString())
//            .setCompanyDomainName(engine.eval("Mock.Random.string()").toString())
//            .setColor(engine.eval("Mock.Random.hex()").toString())
//            .setBackgroundColor(engine.eval("Mock.Random.hex()").toString())
//            .setIsAvailable(Double.valueOf(engine.eval("Mock.Random.integer(0,1)").toString()).intValue())
//            .setIsDel(Double.valueOf(engine.eval("Mock.Random.integer(0,1)").toString()).intValue())
//            .setCreateBy(engine.eval("Mock.Random.string()").toString())
//            .setCreateTime(
//                new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(engine.eval("Mock.Random.datetime()").toString()))
//            .setUpdateBy(engine.eval("Mock.Random.string()").toString())
//            .setUpdateTime(
//                new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(engine.eval("Mock.Random.datetime()").toString()));
//        value.add(tenant);
//
//        doReturn(value).when(baseMapper).selectList(any());
//        assertThat(tenantService.getByIds(userIds)).isNotNull();
//    }
//
//    @Test
//    void testTenantGetByNotIds() {
//        List<String> userIds = List.of("admin");
//        doReturn(Collections.emptyList()).when(baseMapper).selectList(any());
//        assertThat(tenantService.getByIds(userIds)).isEmpty();
//    }
//
//    @Test
//    void testTenantGetByIdsHasValue() {
//        assertThatThrownBy(() -> tenantService.getByIds(null)).isInstanceOf(NullPointerException.class);
//    }
//
//    @Test
//    void testTenantGetByEmptyIds() {
//        assertThat(tenantService.getByIds(Collections.emptyList())).isEmpty();
//    }
//
//    @Test
//    void resetBucket() {
//        String tenantId = "202303101748190b4dd39decf797248bfcbc";
//        // 这里tenantService的baseMapper为空,只能mock掉
//        doReturn(new Tenant().setCustomCode("7381ke6c57")).when(tenantService).getById(anyString());
//        tenantService.resetBucket(tenantId);
//        verify(tenantService).resetBucket(tenantId);
//    }
//
//    @Test
//    void resetBucketAll() {
//        tenantService.resetBucket(null);
//        verify(tenantService).resetBucket(null);
//    }
//
//    @Test
//    void resetDataSource() {
//        String tenantId = "202303101748190b4dd39decf797248bfcbc";
//        tenantService.resetDataSource(tenantId);
//        verify(tenantService).resetDataSource(tenantId);
//    }
//
//    @Test
//    void resetDataSourceAll() {
//        tenantService.resetDataSource(null);
//        verify(tenantService).resetDataSource(null);
//    }
//
//    @Test
//    void checkTenantRepeat() {
//        CheckRepeatQueryDTO checkRepeatQueryDTO = new CheckRepeatQueryDTO().setLoginName("admin_tenant");
//        // 直接拿真实的mapper
//        ReflectionTestUtils.setField(tenantService, "baseMapper", SpringUtil.getBean(TenantMapper.class));
//        assertThat(tenantService.checkTenantRepeat(checkRepeatQueryDTO)).isTrue();
//    }
//
//    @Test
//    void checkTenantRepeatIsFalse() {
//        CheckRepeatQueryDTO checkRepeatQueryDTO = new CheckRepeatQueryDTO().setId("6666").setLoginName("6666");
//        // 直接拿真实的mapper
//        ReflectionTestUtils.setField(tenantService, "baseMapper", SpringUtil.getBean(TenantMapper.class));
//        assertThat(tenantService.checkTenantRepeat(checkRepeatQueryDTO)).isFalse();
//    }
//
//    @Test
//    void findTenantDetailById() throws ParseException {
//        String tenantId = "20230427170651f81606488aaf17485dca24";
//        Tenant tenant = new Tenant()
//            .setId(tenantId)
//            .setLoginName("6666")
//            .setCompanyName("手动添加的脏数据")
//            .setCustomCode("6666")
//            .setCompanyShortName("*********")
//            .setApplicationPlatformName("*********")
//            .setSlogan("*********")
//            .setPassword("123456")
//            .setTenantStartTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse("2023-02-15 00:00:00"))
//            .setTenantEndTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse("2023-02-15 00:00:00"))
//            .setTelephone("*********")
//            .setEmail("*********")
//            .setCompanyAddress("*********")
//            .setCompanyDomainName("*********")
//            .setColor("#1E90FFFF")
//            .setBackgroundColor("#1E90FFFF")
//            .setIsAvailable(1)
//            .setIsDel(0)
//            .setCreateBy("admin")
//            .setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse("2023-02-15 00:00:00"))
//            .setUpdateBy("admin")
//            .setUpdateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse("2023-02-15 00:00:00"));
//
//        doReturn(tenant).when(baseMapper).selectById(anyString());
//
//        assertThat(tenantService.findTenantDetailById(tenantId)).isNotNull();
//    }
//
//    @Test
//    void findTenantDetailByIdNullTenant() {
//        String tenantId = "202303101748190b4dd39decf797248bfcbc";
//
//        doReturn(null).when(baseMapper).selectById(anyString());
//
//        assertThatThrownBy(() -> tenantService.findTenantDetailById(tenantId)).isInstanceOf(BusinessException.class);
//    }
//
//    @Test
//    void deleteTenants() {
//        doReturn(1).when(baseMapper).deleteBatchIds(any());
//        assertThat(tenantService.deleteTenants(List.of("202303101748190b4dd39decf797248bfcbc"))).isTrue();
//    }
//
//    @Test
//    void deleteTenantsIsFalse() {
//        assertThat(tenantService.deleteTenants(Collections.emptyList())).isFalse();
//    }
//
//    @Test
//    void findTenantListByPage() {
//        TenantListQueryDTO tenantListQuery = new TenantListQueryDTO();
//        tenantListQuery.setPageNo(1);
//        tenantListQuery.setPageSize(10);
//
//        // 直接拿真实的mapper
//        ReflectionTestUtils.setField(tenantService, "baseMapper", SpringUtil.getBean(TenantMapper.class));
//
//        assertThat(tenantService.findTenantListByPage(tenantListQuery).getList()).isNotEmpty();
//    }
//
//    @Test
//    void findTenantListByPageIsEmpty() {
//        TenantListQueryDTO tenantListQuery = new TenantListQueryDTO();
//        tenantListQuery.setPageNo(1);
//        tenantListQuery.setPageSize(10);
//
//        assertThat(tenantService.findTenantListByPage(tenantListQuery).getList()).isEmpty();
//    }
//
//    @Test
//    void saveOrUpdateTenant() {
//        SaveTenantDTO saveTenantDTO = new SaveTenantDTO();
//
//        TenantConfigDTO tenantConfigDTO = new TenantConfigDTO();
//        tenantConfigDTO.setPrivacyPolicy(1);
//        tenantConfigDTO.setDownloadApp(1);
//        tenantConfigDTO.setLanguage(1);
//
//        saveTenantDTO.setConfig(tenantConfigDTO);
//        saveTenantDTO.setModules(List.of("user",
//            "file",
//            "exam",
//            "course",
//            "lecturer",
//            "survey",
//            "live",
//            "example",
//            "forum",
//            "project",
//            "excitation",
//            "special",
//            "appraise",
//            "recruiting",
//            "info",
//            "certification",
//            "reading",
//            "push",
//            "business-view",
//            "comment",
//            "market",
//            "evaluation",
//            "promotedgame"));
//        saveTenantDTO.setLoginName("testChen");
//        saveTenantDTO.setCompanyName("testChen");
//        saveTenantDTO.setCustomCode("*********");
//        saveTenantDTO.setCompanyShortName("testChen");
//        saveTenantDTO.setApplicationPlatformName("testChen");
//        saveTenantDTO.setSlogan("testChen");
//        saveTenantDTO.setTenantStartTime(new Date());
//        saveTenantDTO.setTenantEndTime(new Date());
//        saveTenantDTO.setTelephone("***********");
//        saveTenantDTO.setEmail("<EMAIL>");
//        saveTenantDTO.setCompanyDomainName("testChen.saas.wdxuexi.com");
//        saveTenantDTO.setCompanyAddress("***********");
//        saveTenantDTO.setColor("#FF4500FF");
//        saveTenantDTO.setActiveColor("#FF4500FF");
//
//        tenantService.saveOrUpdateTenant(saveTenantDTO);
//    }
//}