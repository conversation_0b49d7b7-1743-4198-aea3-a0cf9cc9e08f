package com.wunding.learn.tenant.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.common.constant.redis.TenantRedisKeyConstant;
import com.wunding.learn.common.mq.event.TenantNoticeEvent;
import com.wunding.learn.common.mq.service.MqProducer;
import com.wunding.learn.tenant.service.admin.dto.TenantNoticeDTO;
import com.wunding.learn.tenant.service.mapper.TenantNoticeMapper;
import com.wunding.learn.tenant.service.model.TenantNotice;
import com.wunding.learn.tenant.service.service.ITenantNoticeService;
import jakarta.annotation.Resource;
import java.util.Date;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

/**
 * <p> 系统更新公告表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gy</a>
 * @since 2024-11-27
 */
@Slf4j
@Service("tenantNoticeService")
public class TenantNoticeServiceImpl extends ServiceImpl<TenantNoticeMapper, TenantNotice> implements
    ITenantNoticeService {

    @Resource
    private MqProducer rabbitMqProducer;

    @Resource
    @SuppressWarnings("rawtypes")
    private RedisTemplate redisTemplate;

    @Override
    public TenantNotice getTenantNotice() {
        LambdaQueryWrapper<TenantNotice> queryWrapper = new LambdaQueryWrapper<TenantNotice>()
            .eq(TenantNotice::getId, "default");
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public void saveOrUpdateTenantNotice(TenantNoticeDTO tenantNoticeDTO) {
        TenantNotice tenantNotice = new TenantNotice();
        if (StringUtils.isNotBlank(tenantNoticeDTO.getId())) {
            tenantNotice.setId(tenantNoticeDTO.getId());
            tenantNotice.setTitle(tenantNoticeDTO.getTitle());
            tenantNotice.setContent(tenantNoticeDTO.getContent());
            tenantNotice.setIsAvailable(tenantNoticeDTO.getIsAvailable());
            tenantNotice.setUpdateBy("admin");
            tenantNotice.setUpdateTime(new Date());
            baseMapper.updateById(tenantNotice);
        } else {
            tenantNotice.setId("default");
            tenantNotice.setTitle(tenantNoticeDTO.getTitle());
            tenantNotice.setContent(tenantNoticeDTO.getContent());
            tenantNotice.setIsAvailable(tenantNoticeDTO.getIsAvailable());
            tenantNotice.setCreateBy("admin");
            tenantNotice.setCreateTime(new Date());
            baseMapper.insert(tenantNotice);
        }
        // 更新每个租户的公告
        Map<String, String> dataSources = redisTemplate.opsForHash().entries(TenantRedisKeyConstant.DB_KEY);
        for (String tenantId : dataSources.keySet()) {
            log.info("租户信息tenantId: " + tenantId);
            rabbitMqProducer.sendMsg(new TenantNoticeEvent(
                tenantNoticeDTO.getTitle(),
                tenantNoticeDTO.getContent(),
                tenantNoticeDTO.getNoticeStartTime(),
                tenantNoticeDTO.getNoticeEndTime(),
                tenantNoticeDTO.getIsAvailable(),
                tenantId
                )
            );
        }
    }
}
