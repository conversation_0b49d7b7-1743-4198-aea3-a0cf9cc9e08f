package com.wunding.learn.tenant.service.interceptor;

import jakarta.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.List;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 系统配置
 *
 * <AUTHOR>
 * @date 2022/02/24
 */
@Configuration
public class ApplicationBeanConfiguration implements WebMvcConfigurer {

    private static final String PATH_PATTERNS_ALL = "/**";
    private static final String[] PATH_PATTERNS_IGNORE = {
        "/login/login",
        "/login",
        "/login/login",
        "/swagger-ui/**",
        "/swagger-ui*/**",
        "/user/swagger-ui/**",
        "/login/swagger-ui/**",
        "/*/swagger-ui/**",
        "/user/swagger-resources/**",
        "/login/swagger-resources/**",
        "/swagger-resources/**",
        "/*/swagger-resources/**",
        "/user/v3/**",
        "/login/v3/**",
        "/*/v3/**",
        "/v3/**",
        "/actuator/**",
        "/user/actuator/**",
        "/login/actuator/**",
        "/druid/**",
        "/error",
        "/tenant/exportData",
        "/user/exportUserData",
        "/weCom/**",
        "/tenantTransfer/getTenantDomainByCorpId"
    };

    /**
     * 处理jwt，用户信息保存的拦截器
     */
    @Resource
    private ValidateUserInterceptor validateUserInterceptor;

    /**
     * 配置系统拦截器
     *
     * @param registry 拦截器注册
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry
            .addInterceptor(validateUserInterceptor)
            .addPathPatterns(PATH_PATTERNS_ALL)
            .excludePathPatterns(PATH_PATTERNS_IGNORE);
    }

    @Override
    public void extendMessageConverters(List<HttpMessageConverter<?>> converters) {
        for (HttpMessageConverter<?> converter : converters) {
            if (converter instanceof StringHttpMessageConverter) {
                ((StringHttpMessageConverter) converter).setDefaultCharset(StandardCharsets.UTF_8);
            }
        }
        WebMvcConfigurer.super.extendMessageConverters(converters);
    }
}
