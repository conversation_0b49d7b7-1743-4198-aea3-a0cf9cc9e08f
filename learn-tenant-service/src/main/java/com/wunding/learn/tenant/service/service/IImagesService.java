package com.wunding.learn.tenant.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.tenant.service.admin.dto.NamePath;
import com.wunding.learn.tenant.service.model.Images;

import java.util.List;
import java.util.Map;

/**
 * <p> 图片表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">ylq</a>
 * @since 2023-02-10
 */
public interface IImagesService extends IService<Images> {


    NamePath getImagePathByTenant(String tenantId);

    NamePath getImageByBizIdAndType(String bizId, String type);

    Map<String, NamePath> getImageByBizIdListAndType(List<String> bizIdList, String type);
}
