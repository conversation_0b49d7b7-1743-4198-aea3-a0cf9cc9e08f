package com.wunding.learn.tenant.service.admin.query;

import com.wunding.learn.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 租户日志查询
 *
 * <AUTHOR>
 * @date 2023/05/17
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TenantLogQuery extends BaseEntity {

    @Parameter(description = "租户id")
    private String tenantId;

}