package com.wunding.learn.tenant.service.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 租户与对象存储桶映射
 *
 * <AUTHOR> href="mailto:<EMAIL>">chh</a>
 * @since 2023-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_tenant_bucket")
@Schema(name = "TenantBucket对象", description = "租户与对象存储桶映射")
public class TenantBucket implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     *
     */
    @Schema(description = "")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;


    /**
     * 租户编号
     */
    @Schema(description = "租户编号")
    @TableField("tenant_custom_code")
    private String tenantCustomCode;


    /**
     * 对象存储桶名
     */
    @Schema(description = "对象存储桶名")
    @TableField("bucket_name")
    private String bucketName;

    /**
     * 对象存储接入点
     */
    @Schema(description = "对象存储接入点")
    @TableField("end_point")
    private String endPoint;

    /**
     * 内网对象存储接入点
     */
    @Schema(description = "内网对象存储接入点")
    @TableField("intranet_end_point")
    private String intranetEndPoint;

    /**
     * VPC对象存储接入点
     */
    @Schema(description = "VPC网络对象存储接入点")
    @TableField("vpc_end_point")
    private String vpcEndPoint;

    /**
     * 对象存储密钥
     */
    @Schema(description = "对象存储密钥")
    @TableField("secret_key")
    private String secretKey;

    /**
     * 对象存储访问key
     */
    @Schema(description = "对象存储访问key")
    @TableField("access_key")
    private String accessKey;

    /**
     * 对象存储类型,默认ali
     */
    @Schema(description = "对象存储类型")
    @TableField("type")
    private String type;

    /**
     * 对象存储访问region 默认shenzhen
     */
    @Schema(description = "对象存储访问region")
    @TableField("region")
    private String region;


    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 对象存储path模式
     */
    private Integer pathMode;
}
