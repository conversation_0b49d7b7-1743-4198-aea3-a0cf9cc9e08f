package com.wunding.learn.tenant.service.client.wecom.dto;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p> 企业微信回调验证请求参数对象
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @since 2023-05-16
 */
@Data
@Accessors(chain = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomCodeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 客户编号
     */
    private String customCode;

    /**
     * 企微代开信息编号
     */
    private String agencyId;

}
