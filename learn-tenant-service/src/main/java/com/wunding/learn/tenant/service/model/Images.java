package com.wunding.learn.tenant.service.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 图片表
 *
 * <AUTHOR> href="mailto:<EMAIL>">ylq</a>
 * @since 2023-02-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_images")
@Schema(name = "Images对象", description = "图片表")
public class Images implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 图片id
     */
    @Schema(name = "图片id")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;


    /**
     * 图片名称
     */
    @Schema(name = "图片名称")
    @TableField("image_name")
    private String imageName;


    /**
     * 图片大小
     */
    @Schema(name = "图片大小")
    @TableField("image_size")
    private Long imageSize;


    /**
     * 图片大小
     */
    @Schema(name = "图片大小")
    @TableField("current_path")
    private String currentPath;


    /**
     * 旧保存路径
     */
    @Schema(name = "旧保存路径")
    @TableField("old_path")
    private String oldPath;


    /**
     * 分辨率
     */
    @Schema(name = "分辨率")
    @TableField("dpi_code")
    private String dpiCode;


    /**
     * 对应分类ID
     */
    @Schema(name = "对应分类ID")
    @TableField("category_id")
    private String categoryId;


    /**
     * 分类类型 对应数据字典表ImagesType
     */
    @Schema(name = "分类类型 对应数据字典表ImagesType")
    @TableField("category_type")
    private String categoryType;


    /**
     * 排序
     */
    @Schema(name = "排序")
    @TableField("sort_no")
    private Integer sortNo;


    /**
     * 是否删除
     */
    @Schema(name = "是否删除")
    @TableField("is_del")
    private Integer isDel;


    /**
     * 新增人
     */
    @Schema(name = "新增人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;


    /**
     * 新增时间
     */
    @Schema(name = "新增时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 最后编辑人
     */
    @Schema(name = "最后编辑人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;


    /**
     * 最后编辑时间
     */
    @Schema(name = "最后编辑时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


}
