package com.wunding.learn.tenant.service.service.impl;


import static com.wunding.learn.common.util.string.StringUtil.newId;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.common.mq.dto.TenantBucketMsg;
import com.wunding.learn.common.mq.service.MqProducer;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.tenant.service.admin.dto.SaveTenantDTO;
import com.wunding.learn.tenant.service.admin.dto.SaveTenantDTO.BucketInfo;
import com.wunding.learn.tenant.service.admin.dto.TenantBucketInfo;
import com.wunding.learn.tenant.service.config.ApplicationProperties;
import com.wunding.learn.tenant.service.config.ApplicationProperties.OssObject;
import com.wunding.learn.tenant.service.mapper.TenantBucketMapper;
import com.wunding.learn.tenant.service.model.TenantBucket;
import com.wunding.learn.tenant.service.mq.event.TenantBucketEvent;
import com.wunding.learn.tenant.service.service.ITenantBucketService;
import jakarta.annotation.Resource;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <p> 租户与对象存储桶映射 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">chh</a>
 * @since 2023-03-03
 */
@Slf4j
@Service("tenantBucketService")
public class TenantBucketServiceImpl extends ServiceImpl<TenantBucketMapper, TenantBucket> implements
    ITenantBucketService {

    @Resource
    private ApplicationProperties applicationProperties;

    @Resource
    private MqProducer rabbitMqProducer;

    @Override
    public void saveBucketInfo(SaveTenantDTO saveTenantDTO) {
        String customCode = saveTenantDTO.getCustomCode();
        if (lambdaQuery().eq(TenantBucket::getTenantCustomCode, customCode).oneOpt().isPresent()) {
            return;
        }
        OssObject ossConfig = applicationProperties.getOss();
        Optional<BucketInfo> bucketInfoOpt = Optional.ofNullable(saveTenantDTO.getBucketInfo());

        // 保存租户与bucketName的映射关系 (一行流代码)
        TenantBucket tenantBucket = new TenantBucket()
            .setId(newId())
            .setTenantCustomCode(customCode)
            .setBucketName(customCode)
            .setPathMode(bucketInfoOpt.map(BucketInfo::getPathMode).orElse(ossConfig.getPathMode()))
            .setEndPoint(bucketInfoOpt.map(BucketInfo::getEndPoint).orElse(ossConfig.getEndPoint()))
            .setIntranetEndPoint(
                bucketInfoOpt.map(BucketInfo::getIntranetEndPoint).orElse(ossConfig.getIntranetEndPoint()))
            .setVpcEndPoint(bucketInfoOpt.map(BucketInfo::getVpcEndPoint).orElse(ossConfig.getVpcEndPoint()))
            .setAccessKey(bucketInfoOpt.map(BucketInfo::getAccessKey).orElse(ossConfig.getAccessKey()))
            .setSecretKey(bucketInfoOpt.map(BucketInfo::getSecretKey).orElse(ossConfig.getSecretKey()))
            .setRegion(bucketInfoOpt.map(BucketInfo::getRegion).orElse(ossConfig.getRegion()))
            .setType(bucketInfoOpt.map(BucketInfo::getType).orElse("ali"));

        save(tenantBucket);
    }

    @Override
    public void updateBucketInfo(SaveTenantDTO saveTenantDTO) {
        BucketInfo bucketInfo = saveTenantDTO.getBucketInfo();
        if (bucketInfo == null) {
            log.info("update_bucket_info_is_null");
            return;
        }

        String endPoint = bucketInfo.getEndPoint();
        String accessKey = bucketInfo.getAccessKey();
        String secretKey = bucketInfo.getSecretKey();
        String intranetEndPoint = bucketInfo.getIntranetEndPoint();
        String vpcEndPoint = bucketInfo.getVpcEndPoint();
        String region = bucketInfo.getRegion();
        String type = bucketInfo.getType();
        Integer pathMode = bucketInfo.getPathMode();

        // 对象存储配置要么全填,要么不填,一旦有一个元素不填则返回
        boolean condition = StringUtils.isEmpty(endPoint)
            && StringUtils.isEmpty(accessKey)
            && StringUtils.isEmpty(secretKey)
            && StringUtils.isEmpty(intranetEndPoint)
            && StringUtils.isEmpty(vpcEndPoint)
            && StringUtils.isEmpty(region)
            && StringUtils.isEmpty(type) && Objects.isNull(pathMode);
        if (condition) {
            log.warn("bucket_info_has_property_is_empty,must_all_property_not_empty");
            return;
        }

        String customCode = saveTenantDTO.getCustomCode();
        lambdaUpdate().set(TenantBucket::getEndPoint, endPoint)
            .set(TenantBucket::getPathMode, pathMode)
            .set(TenantBucket::getIntranetEndPoint, intranetEndPoint)
            .set(TenantBucket::getVpcEndPoint, vpcEndPoint)
            .set(TenantBucket::getAccessKey, accessKey)
            .set(TenantBucket::getSecretKey, secretKey)
            .set(TenantBucket::getRegion, region)
            .set(TenantBucket::getType, type)
            .eq(TenantBucket::getTenantCustomCode, customCode)
            .update();

        // 同步变更到 file 模块
        TenantBucketInfo tenantBucketInfo = new TenantBucketInfo()
            .setPathMode(pathMode)
            .setAccessKey(accessKey)
            .setEndPoint(endPoint)
            .setIntranetEndPoint(intranetEndPoint)
            .setVpcEndPoint(vpcEndPoint)
            .setSecretKey(secretKey)
            .setRootPath(customCode)
            .setRegion(region)
            .setType(type);

        // 序列化数据
        String value = JsonUtil.objToJson(tenantBucketInfo);

        // 发送消息到文件模块,刷新文件模块存储的对象存储信息
        TenantBucketMsg tenantBucketMsg = new TenantBucketMsg()
            .setTenantBucketName(value)
            .setTenantCustomerCode(customCode);
        TenantBucketEvent tenantBucketEvent = new TenantBucketEvent(tenantBucketMsg);
        rabbitMqProducer.sendMsg(tenantBucketEvent);
    }

    @Override
    public BucketInfo getBucketInfo(String customCode) {
        Optional<TenantBucket> tenantBucket = lambdaQuery().eq(TenantBucket::getTenantCustomCode, customCode).oneOpt();
        if (tenantBucket.isEmpty()) {
            return null;
        }

        return new BucketInfo()
            .setPathMode(tenantBucket.map(TenantBucket::getPathMode).orElse(null))
            .setEndPoint(tenantBucket.map(TenantBucket::getEndPoint).orElse(StringUtils.EMPTY))
            .setIntranetEndPoint(tenantBucket.map(TenantBucket::getIntranetEndPoint).orElse(StringUtils.EMPTY))
            .setVpcEndPoint(tenantBucket.map(TenantBucket::getVpcEndPoint).orElse(StringUtils.EMPTY))
            .setRegion(tenantBucket.map(TenantBucket::getRegion).orElse(StringUtils.EMPTY))
            .setType(tenantBucket.map(TenantBucket::getType).orElse(StringUtils.EMPTY))
            .setAccessKey(tenantBucket.map(TenantBucket::getAccessKey).orElse(StringUtils.EMPTY))
            .setSecretKey(tenantBucket.map(TenantBucket::getSecretKey).orElse(StringUtils.EMPTY));
    }
}
