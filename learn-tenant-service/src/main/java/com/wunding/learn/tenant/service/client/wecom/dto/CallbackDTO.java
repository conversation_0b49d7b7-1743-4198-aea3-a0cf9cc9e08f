package com.wunding.learn.tenant.service.client.wecom.dto;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p> 企业微信回调验证请求参数对象
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @since 2023-04-24
 */
@Data
@Accessors(chain = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CallbackDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 企业微信加密签名，msg_signature计算结合了企业填写的token、请求中的timestamp、nonce、加密的消息体。
     */
    private String msgSignature;

    /**
     * 时间戳。与nonce结合使用，用于防止请求重放攻击。
     */
    private String timestamp;

    /**
     * 随机数。与timestamp结合使用，用于防止请求重放攻击。
     */
    private String nonce;

    /**
     * 加密的字符串。需要解密得到消息内容明文，解密后有random、msg_len、msg、receiveid四个字段，其中msg即为消息内容明文
     */
    private String echoStr;

    /**
     * 密文，对应POST请求的数据
     */
    private String body;

    /**
     * 客户公司id
     */
    private String corpId;

    /**
     * 是否是服务商开发,服务商和第三方应用使用的suiteId不一样，授权回调需要判断是哪种类型授权
     */
    private Boolean isAgency;

}
