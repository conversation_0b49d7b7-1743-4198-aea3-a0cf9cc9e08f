package com.wunding.learn.tenant.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(name = "SysMaintenanceDTO", description = "系统维护窗口期")
public class SysMaintenanceDTO {

    @Schema(description = "是否处于维护状态")
    private Boolean enable;

    @Schema(description = "维护开始时间")
    private String startTime;

    @Schema(description = "维护结束时间")
    private String endTime;
}
