package com.wunding.learn.tenant.service.admin.rest;

import com.wunding.learn.common.bean.Result;
import com.wunding.learn.common.constant.other.ErrorNoEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.exception.ErrorNoInterface;
import com.wunding.learn.tenant.service.admin.dto.SysMaintenanceDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import java.time.Duration;
import java.time.LocalTime;
import java.util.Map;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 系统维护窗户管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/sysMaintenance")
@Tag(description = "系统维护窗户管理", name = "SysMaintenanceRest")
public class SysMaintenanceRest {
    private static final Logger log = LoggerFactory.getLogger(SysMaintenanceRest.class);
    private static final String MAINTENANCE_ENABLE_KEY = "sys:maintenance:enable";
    private static final String MAINTENANCE_START_KEY = "sys:maintenance:start_time";
    private static final String MAINTENANCE_END_KEY = "sys:maintenance:end_time";

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @PostMapping("/enableMaintenance")
    @Operation(operationId = "enableMaintenance_SysMaintenanceRest", summary = "开启维护模式", description = "开启维护模式")
    public Result<Void> enableMaintenance(@RequestBody SysMaintenanceDTO sysMaintenanceDTO) {
        // 使用Hash结构存储多个字段
        redisTemplate.opsForHash().put(MAINTENANCE_ENABLE_KEY, "enable", "true");
        redisTemplate.opsForHash().put(MAINTENANCE_START_KEY, "start_time", sysMaintenanceDTO.getStartTime());
        redisTemplate.opsForHash().put(MAINTENANCE_END_KEY, "end_time", sysMaintenanceDTO.getEndTime());
        
        // 设置过期时间（根据结束时间计算）
        Duration duration = Duration.between(LocalTime.now(), LocalTime.parse(sysMaintenanceDTO.getEndTime()));
        if (duration.isNegative()) {
            duration = duration.plusDays(1);
        }
        redisTemplate.expire(MAINTENANCE_ENABLE_KEY, duration);
        redisTemplate.expire(MAINTENANCE_START_KEY, duration);
        redisTemplate.expire(MAINTENANCE_END_KEY, duration);
        
        return Result.success();
    }

    @PostMapping("/disableMaintenance")
    @Operation(operationId = "disableMaintenance_SysMaintenanceRest", summary = "关闭维护模式", description = "关闭维护模式")
    public Result<Void> disableMaintenance() {
        // 删除所有相关键
        redisTemplate.delete(MAINTENANCE_ENABLE_KEY);
        redisTemplate.delete(MAINTENANCE_START_KEY);
        redisTemplate.delete(MAINTENANCE_END_KEY);
        return Result.success();
    }

    @GetMapping("/acquireMaintenanceGap")
    @Operation(operationId = "acquireMaintenanceGap_SysMaintenanceRest", summary = "获取系统维护窗户", description = "获取系统维护窗户")
    public Result<SysMaintenanceDTO> acquireMaintenanceGap() {
        SysMaintenanceDTO sysMaintenanceDTO = new SysMaintenanceDTO();

        // 从Redis读取完整配置
        try {
            Map<Object, Object> enableHash = redisTemplate.opsForHash().entries(MAINTENANCE_ENABLE_KEY);
            Map<Object, Object> startHash = redisTemplate.opsForHash().entries(MAINTENANCE_START_KEY);
            Map<Object, Object> endHash = redisTemplate.opsForHash().entries(MAINTENANCE_END_KEY);

            Boolean enable = Optional.ofNullable(enableHash.get("enable"))
                                     .filter(Boolean.class::isInstance)
                                     .map(Boolean.class::cast)
                                     .orElse(false);
            String startTime = Optional.ofNullable(startHash.get("start_time"))
                                      .filter(String.class::isInstance)
                                      .map(String.class::cast)
                                      .orElse("");
            String endTime = Optional.ofNullable(endHash.get("end_time"))
                                    .filter(String.class::isInstance)
                                    .map(String.class::cast)
                                    .orElse("");

            if (enable) {
                sysMaintenanceDTO.setEnable(true);
                sysMaintenanceDTO.setStartTime(startTime);
                sysMaintenanceDTO.setEndTime(endTime);
            }
        } catch (Exception e) {
            // 记录异常日志并处理异常
            log.error("从Redis获取维护信息失败", e);
            // 可以抛出自定义异常或返回默认值
            throw new BusinessException(ErrorNoEnum.ERR_SERVER);
        }

        return Result.success(sysMaintenanceDTO);
    }
}
