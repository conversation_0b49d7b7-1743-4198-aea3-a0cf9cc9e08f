package com.wunding.learn.tenant.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.wunding.learn.common.constant.other.DelEnum;
import com.wunding.learn.common.dto.IdName;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.tenant.service.admin.query.TenantBusinessVersionQueryDTO;
import com.wunding.learn.tenant.service.enums.BusinessVersionTypeEnum;
import com.wunding.learn.tenant.service.enums.TenantErrorNoEnum;
import com.wunding.learn.tenant.service.mapper.TenantBusinessVersionMapper;
import com.wunding.learn.tenant.service.model.Tenant;
import com.wunding.learn.tenant.service.model.TenantBusinessVersion;
import com.wunding.learn.tenant.service.model.TenantBusinessVersionContent;
import com.wunding.learn.tenant.service.model.TenantBusinessVersionFeatureDetailRecord;
import com.wunding.learn.tenant.service.service.ITenantBusinessVersionContentService;
import com.wunding.learn.tenant.service.service.ITenantBusinessVersionFeatureDetailRecordService;
import com.wunding.learn.tenant.service.service.ITenantBusinessVersionRecordService;
import com.wunding.learn.tenant.service.service.ITenantBusinessVersionService;
import com.wunding.learn.tenant.service.service.ITenantHeadContentRuleTemplateService;
import com.wunding.learn.tenant.service.service.ITenantHeadContentTemplateService;
import com.wunding.learn.tenant.service.service.ITenantHomePageItemConfigService;
import com.wunding.learn.tenant.service.service.ITenantItemTemplateService;
import com.wunding.learn.tenant.service.service.ITenantPermissionConfigService;
import com.wunding.learn.tenant.service.service.ITenantPermissionConfigTemplateService;
import com.wunding.learn.tenant.service.service.ITenantPermissionRouterService;
import com.wunding.learn.tenant.service.service.ITenantRightTemplateService;
import com.wunding.learn.tenant.service.service.ITenantRouterTemplateService;
import com.wunding.learn.tenant.service.util.FileUtil;
import io.micrometer.common.util.StringUtils;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 租户业务版本 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Service
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Slf4j
public class TenantBusinessVersionServiceImpl extends
    ServiceImpl<TenantBusinessVersionMapper, TenantBusinessVersion> implements ITenantBusinessVersionService {

    private final ITenantBusinessVersionContentService tenantBusinessVersionContentService;
    private final ITenantItemTemplateService tenantItemTemplateService;
    private final ITenantRightTemplateService tenantRightTemplateService;
    private final ITenantHeadContentTemplateService tenantHeadContentTemplateService;
    private final ITenantHeadContentRuleTemplateService tenantHeadContentRuleTemplateService;
    private final ITenantPermissionConfigService tenantPermissionConfigService;
    private final ITenantPermissionRouterService tenantPermissionRouterService;
    private final ITenantRouterTemplateService tenantRouterTemplateService;
    private final ITenantHomePageItemConfigService tenantHomePageItemConfigService;
    private final ITenantBusinessVersionRecordService tenantBusinessVersionRecordService;
    private final ITenantBusinessVersionFeatureDetailRecordService tenantBusinessVersionFeatureDetailRecordService;
    private final ITenantPermissionConfigTemplateService tenantPermissionConfigTemplateService;



    @Override
    public List<IdName> getTenantBusinessVersionList() {
        List<TenantBusinessVersion> list = list();
        return list.stream().map(tenantBusinessVersion ->
            new IdName(tenantBusinessVersion.getId() + "", tenantBusinessVersion.getName())
        ).toList();
    }

    @Override
    public Map<BusinessVersionTypeEnum, List<String>> getInsertTableSqlList(Long businessVersionId) {
        Map<BusinessVersionTypeEnum, List<String>> insertTableSqlMap = new HashMap<>();
        List<TenantBusinessVersionContent> versionContentList = tenantBusinessVersionContentService.getByVersionId(
            businessVersionId);
        Map<String, List<String>> contentIdMap = versionContentList.stream()
            .collect(Collectors.groupingBy(TenantBusinessVersionContent::getType,
                Collectors.mapping(TenantBusinessVersionContent::getContentId, Collectors.toList())));
        contentIdMap.forEach((k, v) -> {
            if (Objects.equals(k, BusinessVersionTypeEnum.RIGHT.getType())) {
                insertTableSqlMap.put(BusinessVersionTypeEnum.RIGHT,
                    tenantRightTemplateService.getInsertTableSqlList(v));
            } else if (
                Objects.equals(k, BusinessVersionTypeEnum.ITEM.getType())
                || Objects.equals(k, BusinessVersionTypeEnum.HOME.getType())
                || Objects.equals(k, BusinessVersionTypeEnum.MY_CENTER.getType())
                || Objects.equals(k, BusinessVersionTypeEnum.BOT_MENU.getType())
            ) {
                List<String> sqlList = insertTableSqlMap.get(BusinessVersionTypeEnum.ITEM);
                if (Objects.nonNull(sqlList)) {
                    sqlList.addAll(tenantItemTemplateService.getInsertTableSqlList(v));
                }else{
                    sqlList = new ArrayList<>();
                    sqlList.add("delete from sys_item;");
                    sqlList.addAll(tenantItemTemplateService.getInsertTableSqlList(v));
                }
                insertTableSqlMap.put(BusinessVersionTypeEnum.ITEM, sqlList);
            } else if (Objects.equals(k, BusinessVersionTypeEnum.DIY_HEAD_CONTENT.getType())) {
                insertTableSqlMap.put(BusinessVersionTypeEnum.DIY_HEAD_CONTENT,
                    tenantHeadContentTemplateService.getInsertTableSqlList(v));
            } else if (Objects.equals(k, BusinessVersionTypeEnum.DIY_HEAD_CONTENT_RULE.getType())) {
                insertTableSqlMap.put(BusinessVersionTypeEnum.DIY_HEAD_CONTENT_RULE,
                    tenantHeadContentRuleTemplateService.getInsertTableSqlList(v));
            } else if (Objects.equals(k, BusinessVersionTypeEnum.PERMISSION_CONFIG.getType())) {
                insertTableSqlMap.put(BusinessVersionTypeEnum.PERMISSION_CONFIG,
                    tenantPermissionConfigService.getInsertTableSqlList(v,businessVersionId));
            } else if (Objects.equals(k, BusinessVersionTypeEnum.PERMISSION_ROUTER.getType())) {
                insertTableSqlMap.put(BusinessVersionTypeEnum.PERMISSION_ROUTER,
                    tenantPermissionRouterService.getInsertTableSqlList(v));
            } else if (Objects.equals(k, BusinessVersionTypeEnum.ROUTER.getType())) {
                v.addAll(List.of(
                    "1905",
                    "190501",
                    "190502",
                    "190503",
                    "990",
                    "9901",
                    "9902",
                    "9903",
                    "9904",
                    "9905",
                    "9906",
                    "9907",
                    "9908",
                    "9909"
                ));
                insertTableSqlMap.put(BusinessVersionTypeEnum.ROUTER,
                    tenantRouterTemplateService.getInsertTableSqlList(v));
            } else if (Objects.equals(k, BusinessVersionTypeEnum.HOME_PAGE_ITEM.getType())) {
                insertTableSqlMap.put(BusinessVersionTypeEnum.HOME_PAGE_ITEM,
                    tenantHomePageItemConfigService.getInsertTableSqlList(v));
            }
            insertTableSqlMap.put(BusinessVersionTypeEnum.ROLE_USER, List.of("delete from sys_role_user where user_id = 'admin';"
                + "insert into sys_role_user (id,  role_id,  user_id,  role_type) "
                    + "values ('"+ StringUtil.newId()+"','0','admin','0');")
            );
        });
        return insertTableSqlMap;
    }

    @Override
    public void createBusinessVersionDml(Long businessVersionId, Tenant tenant) {
        String traceId = StringUtil.newId();
        MDC.put("traceId", traceId);
        StringBuilder content = new StringBuilder();
        Map<BusinessVersionTypeEnum, List<String>> insertTableSqlList = getInsertTableSqlList(businessVersionId);
        insertTableSqlList.forEach((k, v) -> {
            content.append("-- ").append(k).append(" begin \n\n");
            for (String sql : v) {
                content.append(sql).append(" \n");
            }
            content.append("-- ").append(k).append(" end \n\n");
        });
        try {
            log.info("DML content: {}", content);
            // 100_dml_user_1.sql
            // 序号_dml_租户编号_库名_业务版本id.sql
            String dmlFileName = "100_dml_"+tenant.getCustomCode()+"_user_"+tenant.getBusinessVersionId()+"_"+System.currentTimeMillis()+".sql";
            String dmlFilePath = tenant.getCustomCode() + "/" + dmlFileName;
            File dir = new File(FileUtil.getPhysicalPath(tenant.getCustomCode()));
            if (!dir.exists()) {
                boolean mkdirs = dir.mkdirs();
                log.info("创建目录{}，路径：{}", mkdirs, dir.getAbsolutePath());
            }
            String dmlPhysicalPath = FileUtil.getPhysicalPath(dmlFilePath);
            saveStringToFile(dmlPhysicalPath, content.toString());
            tenantBusinessVersionRecordService.saveBusinessVersionRecord(tenant, businessVersionId, dmlFilePath);
            TenantBusinessVersionFeatureDetailRecord tenantBusinessVersionFeatureDetailRecord = new TenantBusinessVersionFeatureDetailRecord();
            tenantBusinessVersionFeatureDetailRecord.setVersionId(businessVersionId);
            tenantBusinessVersionFeatureDetailRecord.setActionTime(new Date());
            tenantBusinessVersionFeatureDetailRecord.setContent(MDC.get("log"));
            tenantBusinessVersionFeatureDetailRecordService.save(tenantBusinessVersionFeatureDetailRecord);
        } catch (IOException e) {
            log.error("Failed to write DML content to file", e);
        }
    }

    @Override
    public TenantBusinessVersion createTenantBusinessVersion(TenantBusinessVersion tenantBusinessVersion) {
        tenantBusinessVersion.setIsDel(DelEnum.NOT_DELETE.getValue());
        tenantBusinessVersion.setIsEdit(1);
        save(tenantBusinessVersion);
        
        // 同步权限配置模板数据到权限配置表
        tenantPermissionConfigTemplateService.syncToPermissionConfig(tenantBusinessVersion.getId());
        
        return getTenantBusinessVersionById(tenantBusinessVersion.getId());
    }

    @Override
    public TenantBusinessVersion updateTenantBusinessVersion(TenantBusinessVersion tenantBusinessVersion) {
        updateById(tenantBusinessVersion);
        return tenantBusinessVersion;
    }

    @Override
    public void deleteTenantBusinessVersion(Long id) {
        TenantBusinessVersion tenantBusinessVersion =  getById(id);
        if (Objects.equals(tenantBusinessVersion.getIsEdit(), 0)) {
            throw new BusinessException(TenantErrorNoEnum.TENANT_BUSINESS_VERSION_IS_USE_ERROR);
        }
        removeById(id);
    }

    @Override
    public TenantBusinessVersion getTenantBusinessVersionById(Long id) {
        return getById(id);
    }

    @Override
    public PageInfo<TenantBusinessVersion> pageList(TenantBusinessVersionQueryDTO query) {
        LambdaQueryWrapper<TenantBusinessVersion> wrapper = new LambdaQueryWrapper<>();
        if(StringUtils.isNotBlank(query.getName())){
            wrapper.like(TenantBusinessVersion::getName,query.getName());
        }
        if(query.getIsCustom()!=null){
            wrapper.eq(TenantBusinessVersion::getIsCustom,query.getIsCustom());
        }
        if(query.getIsAvailable() !=null){
            wrapper.eq(TenantBusinessVersion::getIsAvailable,query.getIsAvailable());
        }

        return PageMethod.startPage(query.getPageNo(),query.getPageSize()).doSelectPageInfo(() -> list(wrapper));
    }

    @Override
    public void setEdit(Long businessVersionId,Integer isEdit) {
        TenantBusinessVersion tenantBusinessVersion =  getById(businessVersionId);
        if(tenantBusinessVersion!=null && !Objects.equals(tenantBusinessVersion.getIsEdit(),isEdit)){
            tenantBusinessVersion.setIsEdit(isEdit);
            updateById(tenantBusinessVersion);
        }
    }

    @Override
    public void copyTenantBusinessVersion(Long businessVersionId) {
        //TODO copy TenantBusinessVersion

        //TODO copy TenantPermissionConfig

        //TODO copy TenantPermissionRouter

        //TODO copy TenantHomePageItemConfig

        //TODO copy TenantBusinessVersionContent

    }

    private void saveStringToFile(String fileName, String content) throws IOException {
        try {
            Files.writeString(Paths.get(fileName), content, StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
        } catch (IOException e) {
            log.error("Failed to write content to file: {}", fileName, e);
            throw e;
        }
    }
}

