<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.tenant.service.mapper.TenantVersionMapper">

    <resultMap id="BaseResultMap" type="com.wunding.learn.tenant.service.model.TenantVersion">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="version" column="version" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,version
    </sql>
</mapper>
