package com.wunding.learn.tenant.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

/**
 * </p> 系统租户表
 *
 * <AUTHOR> href="mailto:<EMAIL>">ylq</a>
 * @since 2023-02-07
 */
@Data
@Schema(name = "SaveTenantDTO", description = "系统租户表")
public class SaveTenantDTO implements Serializable {

    private static final long serialVersionUID = -5974296445890407277L;
    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private String id;


    /**
     * 账号
     */
    @Schema(description = "账号")
    @NotBlank(message = "账号不能为空")
    @Length(max = 30, message = "账号长度不能超过30")
    private String loginName;


    /**
     * 公司名称
     */
    @Schema(description = "公司名称")
    @NotBlank(message = "公司名称不能为空")
    @Length(max = 30, message = "公司名称长度不能超过30")
    private String companyName;


    /**
     * 客户编号
     */
    @Schema(description = "客户编号")
    @NotBlank(message = "客户编号不能为空")
    @Pattern(regexp = "^[a-z0-9]{4,23}$")
    private String customCode;


    /**
     * 密码
     */
    @Schema(description = "密码")
    @Length(max = 12, message = "密码长度不能超过12")
    private String password;


    /**
     * 租用开始日期
     */
    @Schema(description = "租期开始日期")
    @NotNull(message = "租期开始日期不能为空")
    private Date tenantStartTime;


    /**
     * 租用结束日期
     */
    @Schema(description = "租期结束日期")
    @NotNull(message = "租期结束日期不能为空")
    private Date tenantEndTime;


    /**
     * 电话
     */
    @Schema(description = "电话")
    @NotBlank(message = "电话不能为空")
    @Length(max = 12, message = "密码长度不能超过10")
    private String telephone;


    /**
     * 邮箱
     */
    @Schema(description = "邮箱")
    @NotBlank(message = "邮箱不能为空")
    private String email;


    /**
     * 公司地址
     */
    @Schema(description = "公司地址")
    @NotBlank(message = "公司地址不能为空")
    @Length(max = 50, message = "公司地址最多50个字符")
    private String companyAddress;


    /**
     * 公司域名
     */
    @Schema(description = "公司域名")
    @NotBlank(message = "公司域名不能为空")
    private String companyDomainName;

    /**
     * 下载课件配置 0-否 1-是
     */
    @Schema(description = "下载课件配置 0-否 1-是 ")
    @Range(min = 0, max = 1, message = "下载课件配置输入值不正确")
    @NotNull(message = "下载课件配置不能为空")
    private Integer downloadCourseware;

    /**
     * 下载APP配置 0-否 1-是
     */
    @Schema(description = "下载APP配置 0-否 1-是 ")
    @Range(min = 0, max = 1, message = "下载APP配置输入值不正确")
    @NotNull(message = "下载APP配置不能为空")
    private Integer downloadApp;

    /**
     * 是否允许小程序注册配置 0-否 1-是
     */
    @Schema(description = "是否允许小程序注册 0-否 1-是 ")
    @Range(min = 0, max = 1, message = "是否允许小程序注册配置输入值不正确")
    @NotNull(message = "是否允许小程序注册配置不能为空")
    private Integer smallAppSignin;


    /**
     * 说明
     */
    @Schema(description = "说明")
    private String description;

    /**
     * 存储桶信息
     */
    @Schema(description = "存储桶信息")
    private BucketInfo bucketInfo;

    /**
     * 模块
     */
    @Schema(description = "模块", hidden = true)
    private List<String> modules;

    /**
     * 业务版本id
     */
    @Schema(description = "业务版本id")
    private Long businessVersionId;

    /**
     * 存储桶信息
     *
     * <AUTHOR>
     * @date 2023/05/25
     */
    @Data
    @Accessors(chain = true)
    @Schema(name = "BucketInfo", description = "存储桶信息")
    public static class BucketInfo implements Serializable {

        private static final long serialVersionUID = -4717784859975878143L;

        /**
         * 对象存储接入点
         */
        @Schema(description = "对象存储接入点")
        private String endPoint;

        /**
         * 内网对象存储接入点
         */
        @Schema(description = "内网对象存储接入点")
        private String intranetEndPoint;

        /**
         * VPC对象存储接入点
         */
        @Schema(description = "VPC网络对象存储接入点")
        private String vpcEndPoint;

        /**
         * 对象存储密钥
         */
        @Schema(description = "对象存储密钥")
        private String secretKey;

        /**
         * 对象存储访问key
         */
        @Schema(description = "对象存储访问key")
        private String accessKey;

        /**
         * 对象存储类型,默认ali
         */
        @Schema(description = "对象存储类型")
        private String type;

        /**
         * 对象存储访问region 默认shenzhen
         */
        @Schema(description = "对象存储访问region")
        private String region;


        /**
         * 对象存储path模式
         */
        private Integer pathMode;
    }
}
