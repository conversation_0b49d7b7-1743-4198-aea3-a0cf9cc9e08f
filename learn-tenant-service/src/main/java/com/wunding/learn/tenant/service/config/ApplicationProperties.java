package com.wunding.learn.tenant.service.config;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 系统配置类
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @since 5.0
 */
@Getter
@Setter
@Component
@ConfigurationProperties("app")
public class ApplicationProperties {

    private Upload upload = new Upload();

    private OssObject oss = new OssObject();

    /**
     * 系统上传目录配置
     */
    @Getter
    @Setter
    public static class Upload {

        /**
         * 上传目录的系统绝对路径
         */
        private String location;
        /**
         * 上传目录
         */
        private String root;
    }

    /**
     * 对象存储配置
     *
     * <AUTHOR>
     * @date 2023/05/26
     */
    @Data
    public static class OssObject {

        /**
         * 对象存储接入点
         */
        private String endPoint;

        /**
         * 内网对象存储接入点
         */
        private String intranetEndPoint;

        /**
         * VPC对象存储接入点
         */
        private String vpcEndPoint;

        /**
         * 对象存储密钥
         */
        private String secretKey;

        /**
         * 对象存储访问key
         */
        private String accessKey;

        /**
         * 对象存储类型,默认ali
         */
        private String type;

        /**
         * 对象存储访问region 默认shenzhen
         */
        private String region;

        /**
         * 对象存储访问region 默认shenzhen (字段应该没啥用)
         */
        private String ramBucketName;

        /**
         * 对象存储path模式
         */
        private Integer pathMode;
    }
}
