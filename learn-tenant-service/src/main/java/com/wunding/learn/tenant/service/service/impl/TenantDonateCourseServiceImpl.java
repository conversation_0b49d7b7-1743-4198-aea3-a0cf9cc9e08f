package com.wunding.learn.tenant.service.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.tenant.service.admin.dto.DonatedCourseDTO;
import com.wunding.learn.tenant.service.admin.dto.SyncCourseDTO;
import com.wunding.learn.tenant.service.admin.query.DonatedCourseListQueryDTO;
import com.wunding.learn.tenant.service.constants.TenantConstants;
import com.wunding.learn.tenant.service.datasource.DynamicDataSourceManager;
import com.wunding.learn.tenant.service.enums.TenantErrorNoEnum;
import com.wunding.learn.tenant.service.model.Categorys;
import com.wunding.learn.tenant.service.model.Course;
import com.wunding.learn.tenant.service.model.Courseware;
import com.wunding.learn.tenant.service.model.Files;
import com.wunding.learn.tenant.service.model.Images;
import com.wunding.learn.tenant.service.model.NewViewLimitMainData;
import com.wunding.learn.tenant.service.model.ResourceStatus;
import com.wunding.learn.tenant.service.model.SysTenantDonatedConfig;
import com.wunding.learn.tenant.service.model.VideoClarity;
import com.wunding.learn.tenant.service.service.ISysTenantDonatedConfigService;
import com.wunding.learn.tenant.service.service.ITenantDonateCourseService;
import jakarta.annotation.Resource;
import java.sql.Types;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import javax.sql.DataSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <p> 系统租户表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">ylq</a>
 * @since 2023-02-07
 */
@Slf4j
@Service("tenantDonateCourseService")
public class TenantDonateCourseServiceImpl implements ITenantDonateCourseService {

  @Resource
  private ISysTenantDonatedConfigService donatedConfigService;

  @Override
  public PageInfo<DonatedCourseDTO> donatedCourseList(DonatedCourseListQueryDTO donatedCourseListQueryDTO) {
    // 根据租户信息切换数据源
    DataSource courseDataSource = DynamicDataSourceManager.getDataSource(
        donatedCourseListQueryDTO.getTenantId() + TenantConstants.COURSE_DS_SUFFIX);
    donatedCourseListQueryDTO.setExport(Boolean.TRUE);
    JdbcTemplate jdbcTemplate = new JdbcTemplate(courseDataSource);
    List<DonatedCourseDTO> courseList;
    try {
      String sql = """
          SELECT c.id, c.course_no AS courseNo, c.course_name AS courseName, COUNT(cw.id) AS courseWareNum  FROM  `course` c  \s
          LEFT JOIN   `courseware` AS cw \s
          ON c.id = cw.course_id \s
          WHERE  c.buildin_flag = 1  AND c.is_del = 0 \s
          GROUP BY c.id, c.course_name;
          """;
      if (StringUtils.isNotBlank(donatedCourseListQueryDTO.getCourseName())) {
        sql = """
                  SELECT c.id, c.course_no AS courseNo, c.course_name AS courseName, COUNT(cw.id) AS courseWareNum  FROM  `course` c  \s
                  LEFT JOIN   `courseware` AS cw \s
                  ON c.id = cw.course_id \s
                  WHERE  c.buildin_flag = 1  AND c.is_del = 0 AND c.course_name LIKE \s"""
              + " '%" + donatedCourseListQueryDTO.getCourseName() + "%' " + """
                  GROUP BY c.id, c.course_name;
                  """;
      }
      courseList = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(DonatedCourseDTO.class));
    } catch (DataAccessException e) {
      log.error("DataAccessException-->", e);
      throw new BusinessException(TenantErrorNoEnum.ACQUIRE_GIFT_COURSE_DATA_FAIL);
    }
    // 分页参数
    int pageNum = donatedCourseListQueryDTO.getPageNo();
    int pageSize = donatedCourseListQueryDTO.getPageSize();
    int total = courseList.size();

    // 计算分页后的子列表
    int fromIndex = (pageNum - 1) * pageSize;
    int toIndex = Math.min(fromIndex + pageSize, total);
    List<DonatedCourseDTO> pageList;
    if (fromIndex < 0 || toIndex > total || fromIndex > toIndex) {
      pageList = new ArrayList<>();
      total = 0;
      pageNum = 0;
    } else {
      pageList = courseList.subList(fromIndex, toIndex);
    }

    // 创建 PageInfo 对象并设置属性
    PageInfo<DonatedCourseDTO> pageInfo = new PageInfo<>(pageList);
    pageInfo.setPageNum(pageNum);
    pageInfo.setPageSize(pageSize);
    pageInfo.setTotal(total);
    pageInfo.setPages((int) Math.ceil((double) total / pageSize));
    pageInfo.setPrePage(pageNum - 1 > 0 ? pageNum - 1 : 1);
    pageInfo.setNextPage(Math.min(pageNum + 1, pageInfo.getPages()));
    pageInfo.setIsFirstPage(pageNum == 1);
    pageInfo.setIsLastPage(pageNum == pageInfo.getPages());
    pageInfo.setHasPreviousPage(pageNum > 1);
    pageInfo.setHasNextPage(pageNum < pageInfo.getPages());
    return pageInfo;
  }

  @Override
  public void donateCourse(SyncCourseDTO syncCourseDTO) {
    // 获取公共租户（赠送课程来源）
    LambdaQueryWrapper<SysTenantDonatedConfig> query = new LambdaQueryWrapper<>();
    SysTenantDonatedConfig tenantDonatedConfig = donatedConfigService.getOne(query);
    if (tenantDonatedConfig == null) {
      throw new BusinessException(TenantErrorNoEnum.TENANT_NOT_EXISTS);
    }
    log.info("donateCourse start, syncCourseDTO: {}, buildinTenantId: {}", JsonUtil.objToJson(syncCourseDTO),
        tenantDonatedConfig.getTenantId());
    // 公共课程数据源【课程 、 课件】
    DataSource buildinCourseDs = DynamicDataSourceManager.getDataSource(
        tenantDonatedConfig.getTenantId() + TenantConstants.COURSE_DS_SUFFIX);

    // 公共文件数据源【文件 、 图片 、 视频】
    DataSource buildinSysFilesDs = DynamicDataSourceManager.getDataSource(
        tenantDonatedConfig.getTenantId() + TenantConstants.SYS_FILES_DS_SUFFIX);

    // 评论状态数据源
    DataSource buildinCommentDs = DynamicDataSourceManager.getDataSource(
        tenantDonatedConfig.getTenantId() + TenantConstants.COMMENT_DS_SUFFIX);

    // 切换数据源
    String targetTenantId = syncCourseDTO.getTenantId();
    DataSource courseDataSource = DynamicDataSourceManager.getDataSource(
        targetTenantId + TenantConstants.COURSE_DS_SUFFIX);
    DataSource fileDataSource = DynamicDataSourceManager.getDataSource(
        targetTenantId + TenantConstants.SYS_FILES_DS_SUFFIX);
    DataSource userDataSource = DynamicDataSourceManager.getDataSource(
        targetTenantId + TenantConstants.SYS_USERS_DS_SUFFIX);
    DataSource commentDataSource = DynamicDataSourceManager.getDataSource(
        targetTenantId + TenantConstants.COMMENT_DS_SUFFIX);

    String[] split = syncCourseDTO.getCourseNos().split(TenantConstants.SEMICOLON);
    // 课程表的课程编码字段未做约束，对于公共租户的课程编码字段，默认唯一。对于写入目标租户时进行校验，已存在则进行更新，不存在则进行插入。
    JdbcTemplate buildinCoursJdbcTemplate = new JdbcTemplate(buildinCourseDs);
    JdbcTemplate buildinSysFilesJdbcTemplate = new JdbcTemplate(buildinSysFilesDs);
    JdbcTemplate buildinCommentJdbcTemplate = new JdbcTemplate(buildinCommentDs);

    JdbcTemplate targetCoursJdbcTemplate = new JdbcTemplate(courseDataSource);
    JdbcTemplate targetSysFilesJdbcTemplate = new JdbcTemplate(fileDataSource);

    // user 库的下发方案数据
    JdbcTemplate targetUserJdbcTemplate = new JdbcTemplate(userDataSource);
    JdbcTemplate targetCommentJdbcTemplate = new JdbcTemplate(commentDataSource);
    // 表  `new_view_limit_main_data` \ new_view_limit_programme
    // new_view_limit_main_data 如果全局下发范围存在则不在写入，使用已有的方案id。

    List<NewViewLimitMainData> viewLimitMainData = targetUserJdbcTemplate.query("""
        SELECT  *  FROM  new_view_limit_main_data   WHERE view_id = 0  AND view_type = 0 AND limit_type = 0 limit 1
        """, new BeanPropertyRowMapper<>(NewViewLimitMainData.class));
    NewViewLimitMainData viewLimitMainDataFirst = null;
    if (!CollectionUtils.isEmpty(viewLimitMainData)) {
      viewLimitMainDataFirst = viewLimitMainData.getFirst();
    }

    handleViewLimitData(targetUserJdbcTemplate, viewLimitMainDataFirst);

    for (String courseNo : split) {
      // 课程信息： course_no 给增加索引，2025年3月7日16:35:25 目前该字段缺少索引信息。
      List<Course> courses = buildinCoursJdbcTemplate.query("""
          SELECT * FROM  `course` WHERE  course_no =? and is_del = 0 limit 1
          """, new BeanPropertyRowMapper<>(Course.class), courseNo.trim());
      if (!CollectionUtils.isEmpty(courses)) {
        // 设置默认分类数据
        Course course = courses.getFirst();
        // user 库的下发范围数据
        saveUserViewLimit(targetUserJdbcTemplate, course, viewLimitMainDataFirst, targetCoursJdbcTemplate);
        // 分类数据
        saveDefaultCategory(targetCoursJdbcTemplate, targetUserJdbcTemplate, viewLimitMainDataFirst);
        // 课程数据
        insertCourseData(targetCoursJdbcTemplate, course, buildinCommentJdbcTemplate, targetCommentJdbcTemplate);

        // 课程封面图片
        handleCourseImgData(buildinSysFilesJdbcTemplate, course, targetSysFilesJdbcTemplate);

        // 课件数据
        handleCoursewareData(buildinCoursJdbcTemplate, course, targetCoursJdbcTemplate, buildinSysFilesJdbcTemplate,
            targetSysFilesJdbcTemplate, buildinCommentJdbcTemplate, targetCommentJdbcTemplate);

      }
    }
  }

  private void saveUserViewLimit(JdbcTemplate targetUserJdbcTemplate, Course course,
      NewViewLimitMainData viewLimitMainDataFirst, JdbcTemplate targetCoursJdbcTemplate) {
    // 课程的下发范围处理： 改为删除再写入
    targetUserJdbcTemplate.update("""
            DELETE FROM w_resource_view_limit WHERE resource_id =? and resource_type = 'CourseViewLimit';
        """, course.getId());
    String insertSql = "insert into `w_resource_view_limit` (`view_limit_id`, `resource_id`, `resource_type`) "
                       + "values(?,?, 'CourseViewLimit');";
    if (viewLimitMainDataFirst != null) {
      targetUserJdbcTemplate.update(insertSql, viewLimitMainDataFirst.getProgrammeId(), course.getId());
    } else {
      targetUserJdbcTemplate.update(insertSql, TenantConstants.DEFAULT_PROGRAMME_ID, course.getId());
    }

    // 手动添加课程库的下发范围处理： 改为删除再写入
    targetCoursJdbcTemplate.update("""
            DELETE FROM w_resource_view_limit WHERE resource_id =? and resource_type = 'CourseViewLimit';
        """, course.getId());
    if (viewLimitMainDataFirst != null) {
      targetCoursJdbcTemplate.update(insertSql, viewLimitMainDataFirst.getProgrammeId(), course.getId());
    } else {
      targetCoursJdbcTemplate.update(insertSql, TenantConstants.DEFAULT_PROGRAMME_ID, course.getId());
    }
  }

  private void handleViewLimitData(JdbcTemplate targetUserJdbcTemplate, NewViewLimitMainData viewLimitMainDataFirst) {
    // `new_view_limit_main_data` \ new_view_limit_programme
    String insert4User = """    
        insert into `w_view_limit_user` ( `view_limit_id`, `user_id`, `single`) values(?,"admin", 1);
        """;

    if (null != viewLimitMainDataFirst) {
      // 准备 w_view_limit_user 的数据
      Integer limitUser4AdmiCunt = targetUserJdbcTemplate.queryForObject(
          "SELECT COUNT(1) FROM `w_view_limit_user` WHERE view_limit_id=" + viewLimitMainDataFirst.getProgrammeId()
          + " and user_id='admin' ;", Integer.class);
      if (Optional.ofNullable(limitUser4AdmiCunt).orElse(0).compareTo(0) <= 0) {
        saveLimitUser4Admin(targetUserJdbcTemplate, insert4User, viewLimitMainDataFirst.getProgrammeId());
      }
    } else {
      saveLimitUser4Admin(targetUserJdbcTemplate, insert4User, TenantConstants.DEFAULT_PROGRAMME_ID);
      Integer cunt = targetUserJdbcTemplate.queryForObject("""
          SELECT COUNT(1) FROM `new_view_limit_main_data` WHERE id = '20250310160527584314bed571411457d0c3';
          """, Integer.class);
      if (Optional.ofNullable(cunt).orElse(0).compareTo(0) <= 0) {
        String sql = """    
                insert into `new_view_limit_main_data` (`id`, `programme_id`, `view_id`, `view_type`, `limit_type`, `add_by`, `add_date`) \s
                values('20250310160527584314bed571411457d0c3',?,'0','0','0','admin','2025-03-10 16:05:27');
            """;
        targetUserJdbcTemplate.update(sql, TenantConstants.DEFAULT_PROGRAMME_ID);
      }

      Integer cunt4Programme = targetUserJdbcTemplate.queryForObject(MessageFormat.format("""
              SELECT COUNT(1) FROM `new_view_limit_programme` WHERE id = {0};
          """, TenantConstants.DEFAULT_PROGRAMME_ID), Integer.class);
      if (Optional.ofNullable(cunt4Programme).orElse(0).compareTo(0) <= 0) {
        String sql = """    
              insert into `new_view_limit_programme` (`id`, `name`, `hash`, `programme_user_count`, `sync_status`, `sync_time`,
               `add_by`, `add_date`) values(?,'','043473cb6b67d1e409d85f8efefbd06d3d2b614c89a7d65ecb1a25afe6fb3fc3','1','1','2025-03-10 16:05:27','admin','2025-03-10 16:05:27');
            """;
        targetUserJdbcTemplate.update(sql, TenantConstants.DEFAULT_PROGRAMME_ID);
      }
    }

  }

  private static void saveLimitUser4Admin(JdbcTemplate targetUserJdbcTemplate, String insert4User,
      Long defaultProgrammeId) {
    try {
      // 写入 w_view_limit_user
      targetUserJdbcTemplate.update(insert4User, defaultProgrammeId);
    } catch (Exception e) {
      log.warn("insert into w_view_limit_user error-->", e);
    }
  }

  private void saveDefaultCategory(JdbcTemplate targetCoursJdbcTemplate, JdbcTemplate targetUserJdbcTemplate,
      NewViewLimitMainData viewLimitMainDataFirst) {
    List<Categorys> categorysList = targetCoursJdbcTemplate.query("""
            SELECT * FROM `categorys` WHERE id = ?;
        """, new BeanPropertyRowMapper<>(Categorys.class), TenantConstants.DONATED_COURSE_CATEGORY);
    if (!CollectionUtils.isEmpty(categorysList)) {
      Categorys categorys = categorysList.getFirst();
      // 如果存在但是状态不对，则修改状态为可用未删除状态。
      if (categorys.getIsAvailable() != 1 || categorys.getIsDel() != 0) {
        targetCoursJdbcTemplate.update("""
            UPDATE `categorys` SET `is_available` = 1, `is_del` = 0 WHERE `id` = ?;
            """, TenantConstants.DONATED_COURSE_CATEGORY);
      }
    } else {
      String sql = """    
            insert into `categorys` (`id`, `category_name`, `parent_id`, `category_type`, `category_level`, `level_path`, `description`,
             `sys_defined`, `is_display`, `is_available`, `is_del`, `sort_no`, `create_by`, `create_time`, `update_by`, `update_time`, \s
             `customer_id`, `org_id`, `default_category_id`, `is_can_delete`) values(?,
             '赠送课程分类','','CourseCate','1',?,'','0','1','1','0','0','admin',now(),
            'admin',now(),'','','','1');
          """;
      // 2025031016063339443fafc6bd5c45098222 、/2025031016063339443fafc6bd5c45098222/
      targetCoursJdbcTemplate.update(sql, TenantConstants.DONATED_COURSE_CATEGORY,
          TenantConstants.SLASH + TenantConstants.DONATED_COURSE_CATEGORY + TenantConstants.SLASH);
    }

    // 分类的下发范围
    Long progrommeId = TenantConstants.DEFAULT_PROGRAMME_ID;
    if (null != viewLimitMainDataFirst) {
      progrommeId = viewLimitMainDataFirst.getProgrammeId();
    }

    String sql = """
        SELECT COUNT(1) FROM `w_resource_view_limit` WHERE resource_id = ? AND resource_type = 'CategorysViewLimit';
        """;
    String sql4Insert = """    
        insert into `w_resource_view_limit` (`view_limit_id`, `resource_id`, `resource_type`) values(?,?,'CategorysViewLimit');
        """;
    Integer cunt4Category = targetCoursJdbcTemplate.queryForObject(sql,
        new Object[]{TenantConstants.DONATED_COURSE_CATEGORY}, new int[]{Types.VARCHAR}, Integer.class);
    if (Optional.ofNullable(cunt4Category).orElse(0).compareTo(0) <= 0) {
      targetCoursJdbcTemplate.update(sql4Insert, progrommeId, TenantConstants.DONATED_COURSE_CATEGORY);
    }

    Integer cunt4CategoryUser = targetUserJdbcTemplate.queryForObject(sql,
        new Object[]{TenantConstants.DONATED_COURSE_CATEGORY}, new int[]{Types.VARCHAR}, Integer.class);
    if (Optional.ofNullable(cunt4CategoryUser).orElse(0).compareTo(0) <= 0) {
      targetUserJdbcTemplate.update(sql4Insert, progrommeId, TenantConstants.DONATED_COURSE_CATEGORY);
    }

  }

  private void handleCoursewareData(JdbcTemplate buildinCoursJdbcTemplate, Course course,
      JdbcTemplate targetCoursJdbcTemplate,
      JdbcTemplate buildinSysFilesJdbcTemplate, JdbcTemplate targetSysFilesJdbcTemplate,
      JdbcTemplate buildinCommentJdbcTemplate, JdbcTemplate targetCommentJdbcTemplate) {
    // 根据课程获取课件列表信息
    List<Courseware> buildCoursewares = buildinCoursJdbcTemplate.query("""
        SELECT * FROM  `courseware` WHERE  course_id =? and is_del = 0
        """, new BeanPropertyRowMapper<>(Courseware.class), course.getId());

    // 根据课件获取所有文件信息
    if (!CollectionUtils.isEmpty(buildCoursewares)) {
      for (Courseware buildCourseware : buildCoursewares) {
        // 处理课件
        insertCourseData(targetCoursJdbcTemplate, buildCourseware, buildinCommentJdbcTemplate,
            targetCommentJdbcTemplate);

        // 课件可能存在多个文件信息
        List<Files> buildCoursewareFiles = buildinSysFilesJdbcTemplate.query("""
            SELECT * FROM  `sys_files` WHERE  category_id =?  and is_del = 0
            """, new BeanPropertyRowMapper<>(Files.class), buildCourseware.getId());

        handleCoursewareDetails(buildinSysFilesJdbcTemplate, targetSysFilesJdbcTemplate, buildCourseware,
            buildCoursewareFiles);
      }
    }
  }

  private void handleCoursewareDetails(JdbcTemplate buildinSysFilesJdbcTemplate,
      JdbcTemplate targetSysFilesJdbcTemplate,
      Courseware buildCourseware, List<Files> buildCoursewareFiles) {
    if (!CollectionUtils.isEmpty(buildCoursewareFiles)) {
      for (Files buildCoursewareFile : buildCoursewareFiles) {
        insertCourseData(targetSysFilesJdbcTemplate, buildCoursewareFile);

        // 查询视频清晰度，再处理
        List<VideoClarity> buildCoursewareVideoClarities = buildinSysFilesJdbcTemplate.query("""
            SELECT * FROM  `sys_video_clarity` WHERE  category_id =?  and is_del = 0
            """, new BeanPropertyRowMapper<>(VideoClarity.class), buildCourseware.getId());
        if (!CollectionUtils.isEmpty(buildCoursewareVideoClarities)) {
          // 先删除再写入 ; 课件id删除视频清晰度资源，可能对应多个清晰度，外层一次性删除，再逐个添加
          targetSysFilesJdbcTemplate.update("""
              DELETE FROM  `sys_video_clarity` WHERE  category_id =?
              """, buildCourseware.getId());
          for (VideoClarity videoClarity : buildCoursewareVideoClarities) {
            insertCourseData(targetSysFilesJdbcTemplate, videoClarity);
          }
        }
      }
    }
  }

  private void handleCourseImgData(JdbcTemplate buildinSysFilesJdbcTemplate, Course course,
      JdbcTemplate targetSysFilesJdbcTemplate) {
    // 查询课件的图片信息[封面图片等] `sys_images`
    List<Images> images = buildinSysFilesJdbcTemplate.query("""
        SELECT * FROM  `sys_images` WHERE  category_id =?  and is_del = 0
        """, new BeanPropertyRowMapper<>(Images.class), course.getId());

    if (!CollectionUtils.isEmpty(images)) {
      for (Images img : images) {
        insertCourseData(targetSysFilesJdbcTemplate, img);
      }
    }
  }

  /**
   * 插入赠送课程表数据
   *
   * @param targetCoursJdbcTemplate 模板工具
   * @param course                  课程数据
   */
  private void insertCourseData(JdbcTemplate targetCoursJdbcTemplate, Course course,
      JdbcTemplate buildinCommentJdbcTemplate, JdbcTemplate targetCommentJdbcTemplate) {
    // 先删除再写入
    targetCoursJdbcTemplate.update("""
        DELETE FROM  `course` WHERE  id =?
        """, course.getId());
    // 再插入
    // 全字段插入SQL（63个参数）
    String sql = """
            INSERT INTO course (
                        id, course_no, course_name, course_cate_id, item_id, relevance_id,
                        course_type, study_type, cover_image_url, descriptions, credit, score,
                        org_id, author, is_required, is_sign, is_audit, is_download, is_comment,
                        is_vote, is_first, is_recommend, is_share, is_favorite, is_allow_share,
                        is_available, is_del, view_type, is_publish, publish_by, publish_time,
                        publish_path, sort_no, click_number, comment_number, download_number,
                        vote_number, favorite_number, share_in_number, source, exam_id, create_by,
                        create_time, update_by, update_time, dev_time, language, version, course_level,
                        share_by, share_time, is_train, csort_no, auto_publish_time, is_public,
                        is_create_sign, is_create_assess, is_hang_up, hang_up_duration_minute,
                        hang_up_duration_second, recommend_time, is_use_default_img, consume_excitation_num,
                        consume_excitation_type, download_org_id, course_category_id, authentic_id,
                        is_copy, limit_show, author_id, view_limit_type, source_type, source_id,
                        is_allow_note, is_audit_note, upload_way, sum_play_time, pv, buildin_flag
                        , audit_status) VALUES (
                        :id, :course_no, :course_name, :course_cate_id, :item_id, :relevance_id,
                        :course_type, :study_type, :cover_image_url, :descriptions, :credit, :score,
                        :org_id, :author, :is_required, :is_sign, :is_audit, :is_download, :is_comment,
                        :is_vote, :is_first, :is_recommend, :is_share, :is_favorite, :is_allow_share,
                        :is_available, :is_del, :view_type, :is_publish, :publish_by, :publish_time,
                        :publish_path, :sort_no, :click_number, :comment_number, :download_number,
                        :vote_number, :favorite_number, :share_in_number, :source, :exam_id, :create_by,
                        :create_time, :update_by, :update_time, :dev_time, :language, :version,
                        :course_level, :share_by, :share_time, :is_train, :csort_no, :auto_publish_time,
                        :is_public, :is_create_sign, :is_create_assess, :is_hang_up,
                        :hang_up_duration_minute, :hang_up_duration_second, :recommend_time,
                        :is_use_default_img, :consume_excitation_num, :consume_excitation_type,
                        :download_org_id, :course_category_id, :authentic_id, :is_copy, :limit_show,
                        :author_id, :view_limit_type, :source_type, :source_id, :is_allow_note,
                        :is_audit_note, :upload_way, :sum_play_time, :pv, :buildin_flag, :audit_status)
        """;

    MapSqlParameterSource params = new MapSqlParameterSource()
        // 生成UUID主键
        .addValue("id", course.getId())

        // 基础信息字段（示例前10个字段）
        .addValue("course_no", course.getCourseNo().replace("C", "WD"))
        .addValue("course_name", course.getCourseName())
        // 统一设置为一个默认的赠送课程分类
        .addValue("course_cate_id", TenantConstants.DONATED_COURSE_CATEGORY)
        .addValue("item_id", course.getItemId())
        .addValue("relevance_id", course.getRelevanceId())
        .addValue("course_type", course.getCourseType())
        .addValue("study_type", course.getStudyType())
        .addValue("cover_image_url", course.getCoverImageUrl())
        .addValue("descriptions", course.getDescriptions())

        // 数值型字段
        .addValue("credit", course.getCredit(), Types.DECIMAL)
        .addValue("score", course.getScore(), Types.DECIMAL)

        // 组织信息
        .addValue("org_id", course.getOrgId())
        .addValue("author", course.getAuthor())

        // 布尔类型字段（统一使用TINYINT映射）
        .addValue("is_required", course.getIsRequired(), Types.TINYINT)
        .addValue("is_sign", course.getIsSign(), Types.TINYINT)
        .addValue("is_audit", course.getIsAudit(), Types.TINYINT)
        .addValue("is_download", course.getIsDownload(), Types.TINYINT)
        .addValue("is_comment", course.getIsComment(), Types.TINYINT)
        .addValue("is_vote", course.getIsVote(), Types.TINYINT)
        .addValue("is_first", course.getIsFirst(), Types.TINYINT)
        .addValue("is_recommend", course.getIsRecommend(), Types.TINYINT)
        .addValue("is_share", course.getIsShare(), Types.TINYINT)
        .addValue("is_favorite", course.getIsFavorite(), Types.TINYINT)
        .addValue("is_allow_share", course.getIsAllowShare(), Types.TINYINT)
        .addValue("is_available", course.getIsAvailable(), Types.TINYINT)
        .addValue(TenantConstants.IS_DEL, course.getIsDel(), Types.TINYINT)

        // 视图设置
        .addValue("view_type", 2)
        .addValue("is_publish", 1)

        // 发布信息
        .addValue("publish_by", TenantConstants.ADMIN)
        .addValue("publish_time", LocalDateTime.now(), Types.TIMESTAMP)
        .addValue("publish_path", course.getPublishPath())

        // 排序和统计
        .addValue(TenantConstants.SORT_NO, course.getSortNo())
        .addValue("click_number", 0)
        .addValue("comment_number", 0)
        .addValue("download_number", 0)
        .addValue("vote_number", 0)
        .addValue("favorite_number", 0)
        .addValue("share_in_number", 0)

        // 来源信息
        .addValue("source", course.getSource())
        .addValue("exam_id", course.getExamId())

        // 创建信息
        .addValue(TenantConstants.CREATE_BY, TenantConstants.ADMIN)
        .addValue(TenantConstants.CREATE_TIME, LocalDateTime.now(), Types.TIMESTAMP)
        .addValue(TenantConstants.UPDATE_BY, TenantConstants.ADMIN)
        .addValue(TenantConstants.UPDATE_TIME, LocalDateTime.now(), Types.TIMESTAMP)

        // 开发信息
        .addValue("dev_time", course.getDevTime())
        .addValue("language", course.getLanguage())
        .addValue("version", course.getVersion())
        .addValue("course_level", course.getCourseLevel())

        // 共享设置
        .addValue("share_by", course.getShareBy())
        .addValue("share_time", course.getShareTime(), Types.TIMESTAMP)
        .addValue("is_train", course.getIsTrain(), Types.TINYINT)
        .addValue("csort_no", course.getCsortNo())

        // 自动发布
        .addValue("auto_publish_time", course.getAutoPublishTime(), Types.TIMESTAMP)
        .addValue("is_public", course.getIsPublic(), Types.TINYINT)

        // 关联设置
        .addValue("is_create_sign", course.getIsCreateSign(), Types.TINYINT)
        .addValue("is_create_assess", course.getIsCreateAssess(), Types.TINYINT)
        .addValue("is_hang_up", course.getIsHangUp(), Types.TINYINT)
        .addValue("hang_up_duration_minute", course.getHangUpDurationMinute())
        .addValue("hang_up_duration_second", course.getHangUpDurationSecond())

        // 时间标记
        .addValue("recommend_time", course.getRecommendTime(), Types.TIMESTAMP)
        .addValue("is_use_default_img", course.getIsUseDefaultImg(), Types.INTEGER)

        // 激励设置
        .addValue("consume_excitation_num", course.getConsumeExcitationNum(), Types.DECIMAL)
        .addValue("consume_excitation_type", course.getConsumeExcitationType())

        // 组织关联
        .addValue("download_org_id", course.getDownloadOrgId())
        .addValue("course_category_id", course.getCourseCategoryId())
        .addValue("authentic_id", course.getAuthenticId())
        .addValue("is_copy", course.getIsCopy(), Types.TINYINT)
        .addValue("limit_show", course.getLimitShow(), Types.TINYINT)

        // 作者信息
        .addValue("author_id", course.getAuthorId())
        .addValue("view_limit_type", GeneralJudgeEnum.NEGATIVE.getValue(), Types.TINYINT)

        // 来源类型
        .addValue("source_type", course.getSourceType(), Types.TINYINT)
        .addValue("source_id", course.getSourceId())

        // 学习设置
        .addValue("is_allow_note", course.getIsAllowNote(), Types.TINYINT)
        .addValue("is_audit_note", course.getIsAuditNote(), Types.TINYINT)
        .addValue("upload_way", course.getUploadWay())

        // 统计信息
        .addValue("sum_play_time", course.getSumPlayTime())
        .addValue("pv", 0)
        .addValue("buildin_flag", 1, Types.TINYINT)
        // 审核状态 - 5：不涉及 。  课程审核需求后做， 赠送时设置为不涉及审核，补充该字段。 因为字段默认0-草稿是需要审核的。fix： 42871
        .addValue("audit_status", 5, Types.TINYINT);

    NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(targetCoursJdbcTemplate);
    jdbcTemplate.update(sql, params);

    handleCommentStatusData(course.getId(), 0, buildinCommentJdbcTemplate, targetCommentJdbcTemplate);
  }

  private static void handleCommentStatusData(String resourceId, Integer resourceType,
      JdbcTemplate buildinCommentJdbcTemplate,
      JdbcTemplate targetCommentJdbcTemplate) {
    // 处理课程的评论状态数据，存在则插入，否则不插入 "评论类型 0 课程,1资讯,2话题,3案例,4共读,5项目,6专题,7课件"
    List<ResourceStatus> commentStatusData = buildinCommentJdbcTemplate.query("""
        SELECT * FROM  `resource_status` WHERE  resource_id =? and resource_type = ?
        """, new BeanPropertyRowMapper<>(ResourceStatus.class), resourceId, resourceType);
    if (!CollectionUtils.isEmpty(commentStatusData)) {
      // 先删除
      targetCommentJdbcTemplate.update("""
          DELETE FROM resource_status WHERE resource_id =? and resource_type = ?;
          """, resourceId, resourceType);
      // 该表只有一条记录
      ResourceStatus oldResourceStatus = commentStatusData.getFirst();
      // 写入目标租户
      String sql4Comment = """
          INSERT INTO resource_status (
          id, resource_id, resource_type, is_comment, is_publish, is_del, create_by, create_time, update_by, update_time
          ) VALUES (:id, :resourceId, :resourceType, :isComment, :isPublish, :isDel, :createBy, :createTime, :updateBy, :updateTime)
          """;
      MapSqlParameterSource params4Comment = new MapSqlParameterSource()
          // 生成UUID主键
          .addValue("id", oldResourceStatus.getId())
          .addValue("resourceId", resourceId)
          .addValue("resourceType", resourceType)
          .addValue("isComment", oldResourceStatus.getIsComment())
          .addValue("isPublish", 1)
          .addValue("isDel", oldResourceStatus.getIsDel())
          .addValue("createBy", oldResourceStatus.getCreateBy())
          .addValue("createTime", oldResourceStatus.getCreateTime())
          .addValue("updateBy", oldResourceStatus.getUpdateBy())
          .addValue("updateTime", oldResourceStatus.getUpdateTime());
      NamedParameterJdbcTemplate jdbcTemplate4Comment = new NamedParameterJdbcTemplate(targetCommentJdbcTemplate);
      jdbcTemplate4Comment.update(sql4Comment, params4Comment);
    }
  }

  /**
   * 插入赠送课件表数据
   */
  private void insertCourseData(JdbcTemplate targetCoursJdbcTemplate, Courseware cw,
      JdbcTemplate buildinCommentJdbcTemplate,
      JdbcTemplate targetCommentJdbcTemplate) {
    // 先删除再写入
    targetCoursJdbcTemplate.update("DELETE FROM  `courseware` WHERE  id =?", cw.getId());

    // 再插入
    String sql =
        """
            INSERT INTO courseware (
                            id, course_id, cw_name, cw_author, cw_type, org_id, mime, version,
                            descriptions, sort_no, is_available, is_del, play_time, can_drag,
                            can_speed, finish_type, create_by, create_time, update_by, update_time,
                            lecturer, class_hour, agree_score, is_source, cw_content, is_same_course,
                            model_type, source_type, active_time, upload_by, chapter_id, read_type,
                            exam_id, real_play_time, library_cate_id, transform_status, old_mime,
                            is_hangup, hangup_duration_minute, hangup_duration_second, authentic_id,
                            is_copy, is_re_upload, show_type, href, comment_number, click_number,
                            common_star, buildin_flag
                            ) VALUES (
                            :id, :course_id, :cw_name, :cw_author, :cw_type, :org_id, :mime, :version,
                            :descriptions, :sort_no, :is_available, :is_del, :play_time, :can_drag,
                            :can_speed, :finish_type, :create_by, :create_time, :update_by, :update_time,
                            :lecturer, :class_hour, :agree_score, :is_source, :cw_content, :is_same_course,
                            :model_type, :source_type, :active_time, :upload_by, :chapter_id, :read_type,
                            :exam_id, :real_play_time, :library_cate_id, :transform_status, :old_mime,
                            :is_hangup, :hangup_duration_minute, :hangup_duration_second, :authentic_id,
                            :is_copy, :is_re_upload, :show_type, :href, :comment_number, :click_number,
                            :common_star, :buildin_flag);
            """;

    MapSqlParameterSource params = new MapSqlParameterSource()
        // 主键和必填字段
        .addValue("id", cw.getId())
        .addValue("is_source", cw.getIsSource(), Types.TINYINT)
        .addValue("cw_content", cw.getCwContent())

        // 基础信息
        .addValue("course_id", cw.getCourseId())
        .addValue("cw_name", cw.getCwName())
        .addValue("cw_author", cw.getCwAuthor())
        .addValue("cw_type", cw.getCwType())
        .addValue("org_id", cw.getOrgId())
        .addValue("mime", cw.getMime())
        .addValue("version", cw.getVersion())
        .addValue("descriptions", cw.getDescriptions())

        // 数值型配置
        .addValue(TenantConstants.SORT_NO, cw.getSortNo())
        .addValue("play_time", cw.getPlayTime())
        .addValue("can_drag", cw.getCanDrag(), Types.TINYINT)
        .addValue("can_speed", cw.getCanSpeed(), Types.TINYINT)
        .addValue("finish_type", cw.getFinishType())
        .addValue("class_hour", cw.getClassHour(), Types.DECIMAL)
        .addValue("agree_score", cw.getAgreeScore(), Types.DECIMAL)

        // 状态标识
        .addValue("is_available", cw.getIsAvailable(), Types.TINYINT)
        .addValue(TenantConstants.IS_DEL, cw.getIsDel(), Types.TINYINT)
        .addValue("is_same_course", cw.getIsSameCourse())
        .addValue("model_type", cw.getModelType(), Types.TINYINT)

        // 时间相关
        .addValue(TenantConstants.CREATE_BY, TenantConstants.ADMIN)
        .addValue(TenantConstants.CREATE_TIME, LocalDateTime.now(), Types.TIMESTAMP)
        .addValue(TenantConstants.UPDATE_BY, TenantConstants.ADMIN)
        .addValue(TenantConstants.UPDATE_TIME, LocalDateTime.now(), Types.TIMESTAMP)
        .addValue("active_time", LocalDateTime.now(), Types.TIMESTAMP)

        // 教学相关
        .addValue("lecturer", cw.getLecturer())
        .addValue("read_type", GeneralJudgeEnum.NEGATIVE.getValue(), Types.TINYINT)
        .addValue("exam_id", cw.getExamId())
        .addValue("real_play_time", cw.getRealPlayTime())

        // 文件处理
        .addValue("library_cate_id", cw.getLibraryCateId())
        .addValue("transform_status", cw.getTransformStatus())
        .addValue("old_mime", cw.getOldMime())
        .addValue("href", cw.getHref())

        // 防挂机设置
        .addValue("is_hangup", cw.getIsHangup(), Types.TINYINT)
        .addValue("hangup_duration_minute", cw.getHangupDurationMinute())
        .addValue("hangup_duration_second", cw.getHangupDurationSecond())

        // 复制相关
        .addValue("authentic_id", cw.getAuthenticId())
        .addValue("is_copy", cw.getIsCopy(), Types.TINYINT)
        .addValue("is_re_upload", cw.getIsReUpload(), Types.TINYINT)

        // 展示设置
        .addValue("show_type", cw.getShowType(), Types.TINYINT)
        .addValue("buildin_flag", 1)

        // 统计信息
        .addValue("comment_number", cw.getCommentNumber())
        .addValue("click_number", cw.getClickNumber())
        .addValue("common_star", cw.getCommonStar(), Types.DECIMAL)

        // 其他字段
        .addValue("source_type", GeneralJudgeEnum.NEGATIVE.getValue(), Types.TINYINT)
        .addValue("upload_by", TenantConstants.ADMIN)
        .addValue("chapter_id", cw.getChapterId());

    NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(targetCoursJdbcTemplate);
    jdbcTemplate.update(sql, params);

    handleCommentStatusData(cw.getId(), 7, buildinCommentJdbcTemplate, targetCommentJdbcTemplate);
  }

  /**
   * 插入赠送课件表文件的数据
   *
   * @param targetSysFilesJdbcTemplate 模板工具
   * @param file                       课件数据
   */
  private void insertCourseData(JdbcTemplate targetSysFilesJdbcTemplate, Files file) {
    // 先删除再写入
    targetSysFilesJdbcTemplate.update("DELETE FROM  `sys_files` WHERE  id =?", file.getId());

    // 再插入
    String sql = """
            INSERT INTO sys_files (
                            id, file_name, file_size, current_path, category_id, category_type,
                            dpi, width, height, sort_no, is_attachment, video_old_path, is_del,
                            create_by, create_time, update_by, update_time, is_adjunct, is_source
                            ) VALUES (
                            :id, :file_name, :file_size, :current_path, :category_id, :category_type,
                            :dpi, :width, :height, :sort_no, :is_attachment, :video_old_path, :is_del,
                            :create_by, :create_time, :update_by, :update_time, :is_adjunct, :is_source)
        """;

    MapSqlParameterSource params = new MapSqlParameterSource()
        // 主键和必填字段
        .addValue("id", file.getId())
        .addValue("file_name", file.getFileName())

        // 文件基础信息
        .addValue("file_size", file.getFileSize())
        .addValue(TenantConstants.CURRENT_PATH, file.getCurrentPath())
        .addValue(TenantConstants.CATEGORY_ID, file.getCategoryId())
        .addValue("category_type", file.getCategoryType())

        // 媒体参数
        .addValue("dpi", file.getDpi())
        .addValue("width", file.getWidth())
        .addValue("height", file.getHeight())

        // 排序和状态
        .addValue(TenantConstants.SORT_NO, file.getSortNo())
        .addValue("is_attachment", file.getIsAttachment(), Types.TINYINT)
        .addValue("video_old_path", file.getVideoOldPath())
        .addValue(TenantConstants.IS_DEL, file.getIsDel(), Types.TINYINT)

        // 创建信息
        .addValue(TenantConstants.CREATE_BY, TenantConstants.ADMIN)
        .addValue(TenantConstants.CREATE_TIME, LocalDateTime.now(), Types.TIMESTAMP)

        // 更新信息
        .addValue(TenantConstants.UPDATE_BY, TenantConstants.ADMIN)
        .addValue(TenantConstants.UPDATE_TIME, LocalDateTime.now(), Types.TIMESTAMP)

        // 关联标识
        .addValue("is_adjunct", file.getIsAdjunct())
        .addValue("is_source", file.getIsSource());

    NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(targetSysFilesJdbcTemplate);
    jdbcTemplate.update(sql, params);

  }

  /**
   * 插入赠送课件表文件的视频清晰度数据
   *
   * @param targetSysFilesJdbcTemplate 模板工具
   * @param clarity                    视频清晰度数据
   */
  private void insertCourseData(JdbcTemplate targetSysFilesJdbcTemplate, VideoClarity clarity) {

    String sql =
        "INSERT INTO sys_video_clarity (" +
        "category_id, clarity_name, current_path, is_del, " +
        "create_by, create_time, update_by, update_time" +
        ") VALUES (" +
        ":category_id, :clarity_name, :current_path, :is_del, " +
        ":create_by, :create_time, :update_by, :update_time)";

    MapSqlParameterSource params = new MapSqlParameterSource()
        // 基础信息
        .addValue("category_id", clarity.getCategoryId())
        .addValue("clarity_name", clarity.getClarityName())
        .addValue("current_path", clarity.getCurrentPath())

        // 状态标识（使用数据库默认值时可省略）
        .addValue(TenantConstants.IS_DEL, clarity.getIsDel(), Types.TINYINT)

        // 创建信息
        .addValue("create_by", TenantConstants.ADMIN)
        .addValue("create_time", LocalDateTime.now(), Types.TIMESTAMP)

        // 更新信息（可选）
        .addValue("update_by", TenantConstants.ADMIN)
        .addValue("update_time", LocalDateTime.now(), Types.TIMESTAMP);

    NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(targetSysFilesJdbcTemplate);
    jdbcTemplate.update(sql, params);
  }

  /**
   * 插入赠送课程的图片信息
   *
   * @param targetSysFilesJdbcTemplate 模板工具
   * @param image                      课程的图片信息数据
   */
  private void insertCourseData(JdbcTemplate targetSysFilesJdbcTemplate, Images image) {
    // 先删除再写入
    targetSysFilesJdbcTemplate.update("DELETE FROM  `sys_images` WHERE  id =?", image.getId());

    String sql = """
        INSERT INTO sys_images (
        id, image_name, image_size, current_path, old_path, dpi_code,
        category_id, category_type, sort_no, width, height, is_del,
        create_by, create_time, update_by, update_time
        ) VALUES (
        :id, :image_name, :image_size, :current_path, :old_path, :dpi_code,
        :category_id, :category_type, :sort_no, :width, :height, :is_del,
        :create_by, :create_time, :update_by, :update_time);
        """;

    MapSqlParameterSource params = new MapSqlParameterSource()
        // 主键和必填字段
        .addValue("id", image.getId())
        .addValue("image_name", image.getImageName())
        .addValue("image_size", image.getImageSize())

        // 路径信息
        .addValue("current_path", image.getCurrentPath())
        .addValue("old_path", image.getOldPath())
        .addValue("dpi_code", image.getDpiCode())

        // 分类信息
        .addValue("category_id", image.getCategoryId())
        .addValue("category_type", image.getCategoryType())

        // 尺寸和排序
        .addValue(TenantConstants.SORT_NO, image.getSortNo())
        .addValue("width", GeneralJudgeEnum.NEGATIVE.getValue(), Types.TINYINT)
        .addValue("height", GeneralJudgeEnum.NEGATIVE.getValue(), Types.TINYINT)

        // 状态标识
        .addValue(TenantConstants.IS_DEL, image.getIsDel(), Types.TINYINT)

        // 时间信息
        .addValue("create_by", TenantConstants.ADMIN)
        .addValue("create_time", LocalDateTime.now(), Types.TIMESTAMP)
        .addValue("update_by", TenantConstants.ADMIN)
        .addValue("update_time", LocalDateTime.now(), Types.TIMESTAMP);

    NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(targetSysFilesJdbcTemplate);
    jdbcTemplate.update(sql, params);

  }
}
