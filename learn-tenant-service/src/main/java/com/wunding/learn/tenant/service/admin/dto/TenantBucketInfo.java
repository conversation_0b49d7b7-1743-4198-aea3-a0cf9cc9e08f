package com.wunding.learn.tenant.service.admin.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class TenantBucketInfo {

    /**
     * 对象存储类型 ali tencent aws minio ceph
     */
    private String type;

    /**
     * 接入点
     */
    private String endPoint;

    /**
     * 内网对象存储接入点
     */
    private String intranetEndPoint;

    /**
     * VPC对象存储接入点
     */
    private String vpcEndPoint;

    /**
     * accessKey
     */
    private String accessKey;

    /**
     * secretKey
     */
    private String secretKey;

    /**
     * 区域
     */
    private String region;

    /**
     * 根路径
     */
    private String rootPath;

    /**
     * 对象存储path模式
     */
    private Integer pathMode;
}
