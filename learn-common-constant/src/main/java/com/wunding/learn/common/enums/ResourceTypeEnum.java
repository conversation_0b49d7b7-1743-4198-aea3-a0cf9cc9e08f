package com.wunding.learn.common.enums;

import com.google.common.base.Objects;
import com.wunding.learn.common.annotation.EnumI18nProperty;
import com.wunding.learn.common.i18n.util.EnumI18n;
import java.util.ArrayList;
import java.util.List;

/**
 * 全局资源类型枚举
 * <p>
 * 说明：当前全局资源类型枚举定义参考业务名称定义，尽量保持与英文翻译同名（名词）。
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @since 2023-11-16
 */
@SuppressWarnings("squid:S1192")
public enum ResourceTypeEnum implements EnumI18n {

    /**
     * 考试
     */
    EXAM("Exam", "exam", "考试", "exam", "ExamManagement"),

    /**
     * 练习
     */
    EXERCISE("Exercise", "", "练习", "exercise", "PracticeManagement"),

    /**
     * 课程
     */
    COURSE("Course", "course", "课程", "course", "CourseManagement"),

    /**
     * 共享库课程
     */
    SHARED_LIBRARY("SharedLibrary", "course", "共享库课程", "course", "Share"),

    /**
     * 资讯
     */
    NEWS("News", "news", "资讯", "news", "InformationList"),

    /**
     * 直播
     */
    LIVE("Live", "live", "直播", "live", "LiveManagement"),

    /**
     * 共读
     */
    READ("Read", "reading", "共读", "reading","CoreadingList"),

    /**
     * 投票
     */
    VOTE("Vote", "", "投票", "vote", "VotingManagement"),

    /**
     * 闯关
     */
    QUIZ("Quiz", "challenge", "闯关", "challenge", "PassGameManage"),

    /**
     * 招募
     */
    RECRUIT("Recruit", "recruting", "招募", "recruiting", "RecruitList"),

    /**
     * 评价
     */
    APPRAISE("Appraise", "appraise", "评价", "appraise", "AssessCommonManagement"),

    /**
     * 答辩会议
     */
    MEETING("Meeting", "Meeting", "答辩会议", "meeting",""),

    /**
     * 话题版块
     */
    TOPIC_SECTION("TopicSection", "", "话题版块", "topic", "TopicManagement"),

    /**
     * 调研
     */
    SURVEY("Survey", "", "调研", "survey","OnlineSurvey"),

    /**
     * 签到
     */
    SIGN_IN("SignIn", "", "签到", "sign","ActivitySignIn"),

    /**
     * 考试竞赛
     */
    TEST_CONTEST("TestContest", "", "考试竞赛", "examCompetition", "ExamCompetitions"),

    /**
     * 专题学习
     */
    SPECIAL("Special", "special", "专题", "special", "SubjectManagement"),

    /**
     * 专题考试任务
     */
    SPECIAL_TASK_EXAM("SpecialTaskExam", "specialtopictask_exam", "专题考试任务", "", ""),

    /**
     * 专题练习任务
     */
    SPECIAL_TASK_EXERCISE("SpecialTaskExercise", "specialtopictask_exercise", "专题练习任务", "", ""),

    /**
     * 专题课程任务
     */
    SPECIAL_TASK_COURSE("SpecialTaskCourse", "specialtopictask_course", "专题课程任务", "", ""),

    /**
     * 专题直播任务
     */
    SPECIAL_TASK_LIVE("SpecialTaskLive", "specialtopictask_live", "专题直播任务", "", ""),

    /**
     * 专题调研任务
     */
    SPECIAL_TASK_SURVEY("SpecialTaskSurvey", "specialtopictask_survey", "专题调研任务", "", ""),

    /**
     * 课程学习任务
     */
    COURSE_LEARNING_TASK("CourseLearnTask", "", "课程任务", "courseTaskProject", "CourseTaskList"),

    /**
     * 课程学习考试任务
     */
    COURSE_LEARNING_TASK_EXAM("CourseLearnTaskExam", "", "课程学习考试任务", "", ""),

    /**
     * 课程学习课程任务
     */
    COURSE_LEARNING_TASK_COURSE("CourseLearnTaskCourse", "", "课程学习课程任务", "", ""),

    /**
     * 快速培训
     */
    RAPID_TRAIN("RapidTrain", "", "快速培训", "quickProject", "QuickTrainingManage"),

    /**
     * 快速培训签到应用
     */
    RAPID_TRAIN_APP_SIGN_IN("RapidTrainAppSignIn", "", "快速培训签到应用", "", ""),

    /**
     * 快速培训评估应用
     */
    RAPID_TRAIN_APP_EVALUATION("RapidTrainAppEvaluation", "", "快速培训评估应用", "", ""),

    /**
     * 快速培训课程任务
     */
    RAPID_TRAIN_TASK_COURSE("RapidTrainTaskCourse", "", "快速培训课程任务", "", ""),

    /**
     * 快速培训考试任务
     */
    RAPID_TRAIN_TASK_EXAM("RapidTrainTaskExam", "", "快速培训考试任务", "", ""),

    /**
     * 培训班
     */
    TRAIN_COURSE("TrainCourse", "project", "学习项目", "project", "TrainingResultManagePage"),

    /**
     * 学习项目
     */
    PROJECT("Project", "project", "学习项目", "project", "StudyProjManage"),

    /**
     * 培训班公告应用
     */
    TRAIN_COURSE_APP_NEWS("TrainCourseAppNews", "", "培训班公告应用", "", ""),

    /**
     * 培训班报名应用
     */
    TRAIN_COURSE_APP_APPLY("TrainCourseAppApply", "", "培训班报名应用", "", ""),

    /**
     * 培训班签到应用
     */
    TRAIN_COURSE_APP_SIGN_IN("TrainCourseAppSignIn", "", "培训班签到应用", "", ""),

    /**
     * 培训班话题版块应用
     */
    TRAIN_COURSE_APP_TOPIC_SECTION("TrainCourseAppTopicSection", "", "培训班话题版块应用", "", ""),

    /**
     * 培训班评估应用
     */
    TRAIN_COURSE_APP_EVALUATION("TrainCourseAppEvaluation", "", "培训班评估应用", "", ""),

    /**
     * 培训班课程任务
     */
    TRAIN_COURSE_TASK_COURSE("TrainCourseTaskCourse", "projecttask_course", "培训班课程任务", "", ""),

    /**
     * 培训班考试任务
     */
    TRAIN_COURSE_TASK_EXAM("TrainCourseTaskExam", "projecttask_exam", "培训班考试任务", "", ""),

    /**
     * 培训班练习任务
     */
    TRAIN_COURSE_TASK_EXERCISE("TrainCourseTaskExercise", "projecttask_exercise", "培训班练习任务", "", ""),

    /**
     * 培训班调研任务
     */
    TRAIN_COURSE_TASK_SURVEY("TrainCourseTaskSurvey", "projecttask_survey", "培训班调研任务", "", ""),

    /**
     * 培训班直播任务
     */
    TRAIN_COURSE_TASK_LIVE("TrainCourseTaskLive", "projecttask_live", "培训班直播任务", "", ""),

    /**
     * 培训班项目任务
     */
    TRAIN_COURSE_TASK_PROJECT("TrainCourseTaskProject", "projecttask_project", "培训班项目任务", "", ""),

    /**
     * 培训班辅导任务
     */
    TRAIN_COURSE_TASK_TUTOR("TrainCourseTaskTutor", "projecttask_form", "培训班辅导任务", "", ""),

    /**
     * 周期项目
     */
    PERIODIC_PROJECT("PeriodicProject", "project", "周期项目", "project","CycleProjManage"),

    /**
     * 周期项目公告应用
     */
    PERIODIC_PROJECT_APP_NEWS("PeriodicProjectAppNews", "", "周期项目公告应用", "", ""),

    /**
     * 周期项目话题版块应用
     */
    PERIODIC_PROJECT_APP_TOPIC_SECTION("PeriodicProjectAppTopicSection", "", "周期项目话题版块应用", "", ""),

    /**
     * 周期项目评估应用
     */
    PERIODIC_PROJECT_APP_EVALUATION("PeriodicProjectAppEvaluation", "", "周期项目评估应用", "", ""),

    /**
     * 周期项目课程任务
     */
    PERIODIC_PROJECT_TASK_COURSE("PeriodicProjectTaskCourse", "projecttask_course", "周期项目课程任务", "", ""),

    /**
     * 周期项目考试任务
     */
    PERIODIC_PROJECT_TASK_EXAM("PeriodicProjectTaskExam", "projecttask_exam", "周期项目考试任务", "", ""),

    /**
     * 周期项目练习任务
     */
    PERIODIC_PROJECT_TASK_EXERCISE("PeriodicProjectTaskExercise", "projecttask_exercise", "周期项目练习任务", "", ""),

    /**
     * 周期项目调研任务
     */
    PERIODIC_PROJECT_TASK_SURVEY("PeriodicProjectTaskSurvey", "projecttask_survey", "周期项目调研任务", "", ""),

    /**
     * 周期项目项目任务
     */
    PERIODIC_PROJECT_TASK_PROJECT("PeriodicProjectTaskProject", "projecttask_project", "周期项目项目任务", "", ""),

    /**
     * 周期项目辅导任务
     */
    PERIODIC_PROJECT_TASK_TUTOR("PeriodicProjectTaskForm", "projecttask_form", "周期项目辅导任务", "", ""),

    /**
     * 培训项目
     */
    TRAIN_PROGRAM("TrainProgram", "trainproject", "培训项目", "train", "TrainingProjManage"),

    /**
     * 培训项目课程活动
     */
    TRAIN_PROGRAM_ACTIVITY_COURSE("TrainProgramActivityCourse", "trainprojecttask_course", "培训项目课程活动", "", ""),

    /**
     * 培训项目考试活动
     */
    TRAIN_PROGRAM_ACTIVITY_EXAM("TrainProgramActivityExam", "trainprojecttask_exam", "培训项目考试活动", "", ""),

    /**
     * 培训项目班级活动
     */
    TRAIN_PROGRAM_ACTIVITY_CLASS("TrainProgramActivityClass", "trainprojecttask_project", "培训项目班级活动", "", ""),

    /**
     * 培训项目直播活动
     */
    TRAIN_PROGRAM_ACTIVITY_LIVE("TrainProgramActivityLive", "trainprojecttask_live", "培训项目直播活动", "", ""),

    /**
     * 培训项目调研活动
     */
    TRAIN_PROGRAM_ACTIVITY_SURVEY("TrainProgramActivitySurvey", "trainprojecttask_survey", "培训项目调研活动", "", ""),

    /**
     * 培训项目招募活动
     */
    TRAIN_PROGRAM_ACTIVITY_RECRUIT("TrainProgramActivityRecruit", "trainprojecttask_recruiting", "培训项目招募活动",
        "", ""),

    /**
     * 培训项目评价活动
     */
    TRAIN_PROGRAM_ACTIVITY_APPRAISE("TrainProgramActivityAppraise", "trainprojecttask_appraise", "培训项目评价活动",
        "", ""),

    /**
     * 培训项目表单活动
     */
    TRAIN_PROGRAM_ACTIVITY_FORM("TrainProgramActivityForm", "", "培训项目表单活动", "", ""),

    /**
     * 培训项目签到活动
     */
    TRAIN_PROGRAM_ACTIVITY_SIGN_IN("TrainProgramActivitySignIn", "trainprojecttask_signin", "培训项目签到活动", "", ""),

    /**
     * 培训项目投票活动
     */
    TRAIN_PROGRAM_ACTIVITY_VOTE("TrainProgramActivityVote", "trainprojecttask_vote", "培训项目投票活动", "", ""),

    /**
     * 培训项目实操活动
     */
    TRAIN_PROGRAM_ACTIVITY_PRACTICAL_OPERATION("TrainProgramActivityPracticalOperation", "", "培训项目实操活动", "", ""),

    /**
     * 学习地图执行
     */
    LEARN_MAP_EXEC("LearnMapExec", "learnMap", "学习地图", "learnMap", "LearningMap"),

    /**
     * 面授项目
     */
    FACE_PROJECT("FaceProject", "faceProject", "面授项目", "face", "FaceTeachStudyProjManage"),

    /**
     * 面授项目日程
     */
    FACE_PROJECT_SCHEDULE("FaceProjectSchedule", "faceProject_schedule", "面授项目日程", "", ""),

    /**
     * 实操
     */
    PRACTICAL_OPERATION("PracticalOperation", "practicalOperation", "实操", "", ""),
    /**
     * 人才测评
     */
    TALENT_ASSESS("TalentAssess", "TalentAssess", "人才测评", "", "TalentAssess"),
    /**
     * 讲师
     */
    LECTURER("TeacherManagement", "TeacherManagement", "讲师", "", "TeacherManagement"),

    /**
     * 课程审核
     */
    COURSE_AUDIT("courseAudit", "courseAudit", "课程审核", "courseAudit", ""),

    /**
     * 资讯审核
     */
    INFO_AUDIT("infoAudit", "infoAudit", "资讯审核", "infoAudit", ""),

    /**
     * 专题审核
     */
    SPECIAL_AUDIT("specialAudit", "specialAudit", "专题审核", "specialAudit", ""),

    COACH_TMP_MANAGE("CoachTmpManage","CoachTmpManage","辅导模版","app_tutor","CoachTmpManage"),

    SENSITIVE_CONFIG("sensitiveConfig","sensitiveConfig","敏感词配置","","sensitiveConfig"),

    QUALIFICATIONS_MANAGEMENT("QualificationsManagement","QualificationsManagement","任职资格管理","Qualification","QualificationsManagement"),

    CASE_LIBRARY_MANAGE("CaselibraryManage","CaselibraryManage","案例库","example","CaselibraryManage"),

    ACTIVITY_COIN_EXCHANGE("ActivityCoinExchange","ActivityCoinExchange","金币兑换","creditstore","ActivityCoinExchange"),

    NEW_ABILITY_LEARNING_MAP("newAbilityLearningMap","newAbilityLearningMap","新建胜任力地图","competencyBoard","newAbilityLearningMap"),

    ;

    /**
     * 全局资源类型
     */
    private final String type;

    /**
     * 全局资源标识，对应前端二维码识别类型
     */
    private final String code;

    /**
     * 资源类型名称
     */
    @EnumI18nProperty
    private final String name;

    /**
     * 权限点，对应前端授权功能
     */
    private final String right;

    /**
     * 后台路由
     */
    private final String router;

    ResourceTypeEnum(String type, String code, String name, String right,String router) {
        this.type = type;
        this.code = code;
        this.name = name;
        this.right = right;
        this.router = router;
    }

    /**
     * 获取学习地图活动资源类型
     *
     * @return 资源类型
     */
    public static List<ResourceTypeEnum> getLearnMapActivityAllType() {
        List<ResourceTypeEnum> list = new ArrayList<>();
        list.add(ResourceTypeEnum.COURSE);
        list.add(ResourceTypeEnum.EXAM);
        list.add(ResourceTypeEnum.EXERCISE);
        list.add(ResourceTypeEnum.SURVEY);
        list.add(ResourceTypeEnum.LIVE);
        list.add(ResourceTypeEnum.PROJECT);
        list.add(ResourceTypeEnum.TRAIN_PROGRAM);
        list.add(ResourceTypeEnum.SPECIAL);
        return list;
    }

    /**
     * 获取我的任务资源类型
     *
     * @return 资源类型
     */
    public static List<ResourceTypeEnum> getMyTaskAllType() {
        List<ResourceTypeEnum> list = new ArrayList<>();
        list.add(ResourceTypeEnum.COURSE_LEARNING_TASK);
        list.add(ResourceTypeEnum.EXAM);
        list.add(ResourceTypeEnum.TRAIN_COURSE);
        list.add(ResourceTypeEnum.QUIZ);
        list.add(ResourceTypeEnum.LIVE);
        list.add(ResourceTypeEnum.SPECIAL);
        list.add(ResourceTypeEnum.PERIODIC_PROJECT);
        list.add(ResourceTypeEnum.RAPID_TRAIN);
        list.add(ResourceTypeEnum.TRAIN_PROGRAM);
        list.add(ResourceTypeEnum.SURVEY);
        list.add(ResourceTypeEnum.EXERCISE);
        list.add(ResourceTypeEnum.READ);
        list.add(ResourceTypeEnum.RECRUIT);
        list.add(ResourceTypeEnum.APPRAISE);
        list.add(ResourceTypeEnum.SIGN_IN);
        list.add(ResourceTypeEnum.LEARN_MAP_EXEC);
        list.add(ResourceTypeEnum.VOTE);
        return list;
    }

    public static ResourceTypeEnum getResourceTypeEnumByType(String type) {
        for (ResourceTypeEnum resourceTypeEnum : getLearnMapActivityAllType()) {
            if (Objects.equal(resourceTypeEnum.getType(), type)) {
                return resourceTypeEnum;
            }
        }
        return null;
    }

    public static ResourceTypeEnum getResourceTypeEnumByRight(String right) {
        for (ResourceTypeEnum resourceTypeEnum : ResourceTypeEnum.values()) {
            if (Objects.equal(resourceTypeEnum.getRight(), right)) {
                return resourceTypeEnum;
            }
        }
        return null;
    }

    /**
     * 按类型获取资源类型名称
     *
     * @param type 类型
     * @return {@link String }
     */
    public static String getResourceTypeNameByType(String type) {
        ResourceTypeEnum typeEnum = getByType(type);
        if (typeEnum == null) {
            return "";
        }
        return switch (typeEnum) {
            case COURSE_AUDIT -> "课程";
            case INFO_AUDIT -> "资讯";
            case SPECIAL_AUDIT -> "专题";
            case null -> null;
            default -> typeEnum.getName();
        };
    }

    public static ResourceTypeEnum getByResourceTypeEnumName(String name) {
        for (ResourceTypeEnum resourceTypeEnum : ResourceTypeEnum.values()) {
            if (Objects.equal(resourceTypeEnum.name(), name)) {
                return resourceTypeEnum;
            }
        }
        return null;
    }

    public static ResourceTypeEnum getByType(String type) {
        for (ResourceTypeEnum resourceTypeEnum : ResourceTypeEnum.values()) {
            if (Objects.equal(resourceTypeEnum.getCode(), type)) {
                return resourceTypeEnum;
            }
        }
        return null;
    }

    public String getType() {
        return type;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return i18n(name(), this.name);
    }

    public String getRight() {
        return right;
    }

    public String getRouter() {
        return router;
    }
}
