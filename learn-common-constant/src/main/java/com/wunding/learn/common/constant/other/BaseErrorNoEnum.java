package com.wunding.learn.common.constant.other;


import com.wunding.learn.common.exception.ErrorNoInterface;

public enum BaseErrorNoEnum implements ErrorNoInterface {

    /**
     * 不支持的操作
     */
    NOT_SUPPORT_OPERATION(701, "不支持的操作"),

    /**
     * 包含已发布的资源,无法删除
     */
    INCLUDING_IS_PUBLISH(702, "包含已发布的资源,无法删除"),

    /**
     * 无权访问
     */
    ERR_NO_POWER(703, "无权访问"),

    /**
     *
     */
    INCLUDING_IS_AVAILABLE(704, "包含已启用的资源,无法删除"),

    /**
     * 初始化资源无法删除
     */
    ERR_DELETE_INITIALIZATION_RESOURCE(705, "初始化资源无法删除"),

    /**
     * 初始化资源无法禁用
     */
    ERR_BAN_INITIALIZATION_RESOURCE(706, "初始化资源无法禁用"),

    ERR_QUOTE_RESOURCE(707, "包含被引用的资源无法删除"),

    ERR_QUOTE_ENABLE_RESOURCE(708, "包含被引用的资源无法禁用"),

    ERR_NOT_HAVE_RESOURCE_ABLE_DOWNLOAD(709, "没有可下载的资源"),

    ERR_METHOD_EXECUTION_EXCEPTION(710, "方法执行异常"),

    /**
     * 导入数据校验失败
     */
    ERR_IMPORT_DATA_CHECK(711, "{0}"),

    ERR_INVOKE(712, "调用失败,请与管理员联系,{0}"),

    ERR_AI_KEY_NOT_EXIST(713, "该ai应用不存在"),

    ERR_HAS_SENSITIVE_WORD_EXCEPTION(714, "问鼎云学习：内容存在敏感词"),

    ;

    private int errorCode;
    private String message;

    private BaseErrorNoEnum(int errorCode, String message) {
        this.errorCode = errorCode;
        this.message = message;
    }

    @Override
    public int getErrorCode() {
        return errorCode;
    }

    @Override
    public String getMessage() {
        return message;
    }

}
