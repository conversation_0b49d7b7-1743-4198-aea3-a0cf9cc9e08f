package com.wunding.learn.common.enums.other;

import java.util.Objects;
import java.util.function.Predicate;
import lombok.Getter;

/**
 * 系统配置参数编码枚举常量, 需要使用到的可以拓展添加
 *
 * <AUTHOR>
 * @since 2020年10月23日 16:55:00
 */
@Getter
public enum SystemConfigCodeEnum {

    /**
     * 登录验证码
     */
    SYSTEM_CONFIG_CODE_100("100", validatorForZeroOrOne()),
    /**
     * 登录短信验证码
     */
    SYSTEM_CONFIG_CODE_101("101", Objects::nonNull),
    /**
     * 学员端允许多端登录
     */
    SYSTEM_CONFIG_CODE_102("102", validatorForZeroOrOne()),
    /**
     * 下发范围刷新时机配置
     */
    SYSTEM_CONFIG_CODE_105("105", Objects::nonNull),
    /**
     * 下发范围同步池容量
     */
    SYSTEM_CONFIG_CODE_106("106", validatorForPositiveIntegerOrZero()),
    /**
     * 下发范围同步池刷新时间间隔
     */
    SYSTEM_CONFIG_CODE_107("107", validatorForPositiveIntegerOrZero()),
    /**
     * 学员端登录是否开启验证码
     */
    SYSTEM_CONFIG_CODE_108("108", validatorForZeroOrOne()),
    /**
     * 话题回帖是否需要审核
     */
    SYSTEM_CONFIG_CODE_201("201", Objects::nonNull),
    /**
     * 每次导入用户最大数量
     */
    SYSTEM_CONFIG_CODE_202("202", validatorForPositiveIntegerOrZero()),
    /**
     * 外部培训的培训专业认证体系
     */
    SYSTEM_CONFIG_CODE_2048("2048", Objects::nonNull),
    /**
     * 外部培训报名时确认状态默认值
     */
    SYSTEM_CONFIG_CODE_2049("2049", validatorForZeroOrOne()),
    /**
     * 外部培训机构确认状态默认值
     */
    SYSTEM_CONFIG_CODE_2050("2050", validatorForZeroOrOne()),
    /**
     * 任职资格学员内容展示-组织回馈
     */
    SYSTEM_CONFIG_CODE_21001("21001", validatorForZeroOrOne()),
    /**
     * 任职资格学员内容展示-关键任务
     */
    SYSTEM_CONFIG_CODE_21002("21002", validatorForZeroOrOne()),
    /**
     * 任职资格学员内容展示-知识内容
     */
    SYSTEM_CONFIG_CODE_21003("21003", validatorForZeroOrOne()),
    /**
     * 任职资格学员内容展示-关键能力
     */
    SYSTEM_CONFIG_CODE_21004("21004", validatorForZeroOrOne()),
    /**
     * 任职资格学员内容展示-基本条件
     */
    SYSTEM_CONFIG_CODE_21005("21005", Objects::nonNull),
    /**
     * 首页-总用户数
     */
    SYSTEM_CONFIG_CODE_2101("2101", validatorForZeroOrOne()),
    /**
     * 资格认证界面说明文案
     */
    SYSTEM_CONFIG_CODE_21010("21010", Objects::nonNull),
    /**
     * 资格认证结果公示文案
     */
    SYSTEM_CONFIG_CODE_21011("21011", Objects::nonNull),
    /**
     * 任职资格结果展示时间设置(天)
     */
    SYSTEM_CONFIG_CODE_21013("21013", validatorForPositiveIntegerOrZero()),
    /**
     * 首页-已激活用户
     */
    SYSTEM_CONFIG_CODE_2102("2102", validatorForZeroOrOne()),
    /**
     * 首页-未激活用户
     */
    SYSTEM_CONFIG_CODE_2103("2103", validatorForZeroOrOne()),
    /**
     * 首页-发布课程
     */
    SYSTEM_CONFIG_CODE_2201("2201", validatorForZeroOrOne()),
    /**
     * 首页-总课件数
     */
    SYSTEM_CONFIG_CODE_2202("2202", validatorForZeroOrOne()),
    /**
     * 首页-浏览人数
     */
    SYSTEM_CONFIG_CODE_2203("2203", validatorForZeroOrOne()),
    /**
     * 首页-发布资讯
     */
    SYSTEM_CONFIG_CODE_2301("2301", validatorForZeroOrOne()),
    /**
     * 首页-总评论数
     */
    SYSTEM_CONFIG_CODE_2302("2302", validatorForZeroOrOne()),
    /**
     * 首页-浏览人次
     */
    SYSTEM_CONFIG_CODE_2303("2303", validatorForZeroOrOne()),
    /**
     * 首页-话题总数
     */
    SYSTEM_CONFIG_CODE_2401("2401", validatorForZeroOrOne()),
    /**
     * 首页-主题数
     */
    SYSTEM_CONFIG_CODE_2402("2402", validatorForZeroOrOne()),
    /**
     * 首页-投票数
     */
    SYSTEM_CONFIG_CODE_2403("2403", validatorForZeroOrOne()),
    /**
     * 首页-发布学习项目
     */
    SYSTEM_CONFIG_CODE_2501("2501", validatorForZeroOrOne()),
    /**
     * 首页-发布报名数
     */
    SYSTEM_CONFIG_CODE_2502("2502", validatorForZeroOrOne()),
    /**
     * 首页-发布签到数
     */
    SYSTEM_CONFIG_CODE_2503("2503", validatorForZeroOrOne()),
    /**
     * 首页-发布调研
     */
    SYSTEM_CONFIG_CODE_2601("2601", validatorForZeroOrOne()),
    /**
     * 首页-发布考试
     */
    SYSTEM_CONFIG_CODE_2602("2602", validatorForZeroOrOne()),
    /**
     * 首页-发布练习
     */
    SYSTEM_CONFIG_CODE_2603("2603", validatorForZeroOrOne()),
    /**
     * 培训计划汇总统计-项目完成标记
     */
    SYSTEM_CONFIG_CODE_2701("2701", Objects::nonNull),
    /**
     * 培训计划执行统计-部门名称显示
     */
    SYSTEM_CONFIG_CODE_2702("2702", Objects::nonNull),
    /**
     * 签到开始默认提前时间(分钟)
     */
    SYSTEM_CONFIG_CODE_28001("28001", validatorForPositiveIntegerOrZero()),
    /**
     * 签到结束默认延后时间(分钟)
     */
    SYSTEM_CONFIG_CODE_28002("28002", validatorForPositiveIntegerOrZero()),
    /**
     * 具体活动的积分兑换配置
     */
    SYSTEM_CONFIG_CODE_28003("28003", validatorForZeroOrOne()),
    /**
     * 学员端-讲师课酬
     */
    SYSTEM_CONFIG_CODE_28004("28004", validatorForZeroOrOne()),
    /**
     * 后台-讲师积分推送
     */
    SYSTEM_CONFIG_CODE_28005("28005", validatorForZeroOrOne()),
    /**
     * 后台-培训班开班申请表单
     */
    SYSTEM_CONFIG_CODE_28006("28006", Objects::nonNull),
    /**
     * 后台-专题开班申请表单
     */
    SYSTEM_CONFIG_CODE_28007("28007", Objects::nonNull),
    /**
     * 系统特权角色
     */
    SYSTEM_CONFIG_CODE_28008("28008", Objects::nonNull),

    /**
     * 删除课件等资源时是否物理删除文件标志
     */
    SYSTEM_CONFIG_CODE_28009("28009", Objects::nonNull),

    /**
     * 评估结果优良分值
     */
    SYSTEM_CONFIG_CODE_29001("29001", validatorForPositiveIntegerOrZero()),
    /**
     * 评估默认开始时间(分钟)
     */
    SYSTEM_CONFIG_CODE_29002("29002", validatorForPositiveIntegerOrZero()),
    /**
     * 评估默认结束时间(分钟)
     */
    SYSTEM_CONFIG_CODE_29003("29003", validatorForPositiveIntegerOrZero()),
    /**
     * 内训默认评估模板
     */
    SYSTEM_CONFIG_CODE_29004("29004", Objects::nonNull),
    /**
     * 售后默认评估模板
     */
    SYSTEM_CONFIG_CODE_29005("29005", Objects::nonNull),
    /**
     * 图片库控制
     */
    SYSTEM_CONFIG_CODE_30001("30001", validatorForZeroOrOne()),
    /**
     * 企业微信对接数量
     */
    SYSTEM_CONFIG_CODE_30002("30002", validatorForPositiveIntegerOrZero()),
    /**
     * 是否播放防挂机
     */
    SYSTEM_CONFIG_CODE_301("301", validatorForZeroOrOne()),
    /**
     * 防挂机时长（分）
     */
    SYSTEM_CONFIG_CODE_302("302", validatorForPositiveIntegerOrZero()),
    /**
     * 防挂机倒计时长（秒）
     */
    SYSTEM_CONFIG_CODE_303("303", validatorForPositiveIntegerOrZero()),
    /**
     * 首次播放是否倍数
     */
    SYSTEM_CONFIG_CODE_304("304", validatorForZeroOrOne()),
    /**
     * 首次播放是否可拖拽
     */
    SYSTEM_CONFIG_CODE_305("305", validatorForZeroOrOne()),
    /**
     * 直播供应商
     */
    SYSTEM_CONFIG_CODE_306("306", Objects::nonNull),
    /**
     * 转码-视频封装格式
     */
    SYSTEM_CONFIG_CODE_308("308", validatorForZeroOrOne()),
    /**
     * 异步上报学时
     */
    SYSTEM_CONFIG_CODE_309("309", validatorForZeroOrOne()),
    /**
     * 转码-课件转码清晰度支持480
     */
    SYSTEM_CONFIG_CODE_310("310", validatorForZeroOrOne()),
    /**
     * 转码-课件转码清晰度支持720
     */
    SYSTEM_CONFIG_CODE_311("311", validatorForZeroOrOne()),
    /**
     * 转码-课件转码清晰度支持1080
     */
    SYSTEM_CONFIG_CODE_312("312", validatorForZeroOrOne()),
    /**
     * 相关课程查询
     */
    SYSTEM_CONFIG_CODE_313("313", validatorForZeroOrOne()),
    /**
     * 个人中心-收藏
     */
    SYSTEM_CONFIG_CODE_333("333", validatorForZeroOrOne()),
    /**
     * 个人中心-下载
     */
    SYSTEM_CONFIG_CODE_334("334", Objects::nonNull),
    /**
     * 个人中心-签到
     */
    SYSTEM_CONFIG_CODE_335("335", Objects::nonNull),
    /**
     * 个人中心-话题
     */
    SYSTEM_CONFIG_CODE_336("336", validatorForZeroOrOne()),
    /**
     * 个人中心-证书
     */
    SYSTEM_CONFIG_CODE_337("337", validatorForZeroOrOne()),
    /**
     * 个人中心-课时
     */
    SYSTEM_CONFIG_CODE_338("338", validatorForZeroOrOne()),
    /**
     * 个人中心-积分
     */
    SYSTEM_CONFIG_CODE_339("339", validatorForZeroOrOne()),
    /**
     * 个人中心-学时
     */
    SYSTEM_CONFIG_CODE_340("340", validatorForZeroOrOne()),
    /**
     * 个人中心-学分
     */
    SYSTEM_CONFIG_CODE_341("341", validatorForZeroOrOne()),
    /**
     * 个人中心-金币
     */
    SYSTEM_CONFIG_CODE_342("342", validatorForZeroOrOne()),
    /**
     * 个人中心-推荐
     */
    SYSTEM_CONFIG_CODE_343("343", validatorForZeroOrOne()),
    /**
     * 首页-热门课程1-天数
     */
    SYSTEM_CONFIG_CODE_451("451", validatorForPositiveIntegerOrZero()),
    /**
     * 首页-课程-评论数+评星次数
     */
    SYSTEM_CONFIG_CODE_461("461", validatorForPositiveIntegerOrZero()),
    /**
     * 首页-热门话题1-天数
     */
    SYSTEM_CONFIG_CODE_471("471", validatorForPositiveIntegerOrZero()),
    /**
     * 首页-首页-话题-回帖数量
     */
    SYSTEM_CONFIG_CODE_481("481", validatorForPositiveIntegerOrZero()),
    /**
     * 讲师授课记录自动审核
     */
    SYSTEM_CONFIG_CODE_4001("4001", validatorForZeroOrOne()),
    /**
     * 讲师授课批量审核
     */
    SYSTEM_CONFIG_CODE_4002("4002", validatorForZeroOrOne()),
    /**
     * 猜你喜欢的课程加塞设置
     */
    SYSTEM_CONFIG_CODE_501("501", validatorForPositiveIntegerOrZero()),
    /**
     * 是否默认入库
     */
    SYSTEM_CONFIG_CODE_601("601", validatorForZeroOrOne()),
    /**
     * 是否启用智能全文搜索
     */
    SYSTEM_CONFIG_CODE_602("602", validatorForZeroOrOne()),
    /**
     * 课程-课程关键词标签解析
     */
    SYSTEM_CONFIG_CODE_603("603", Objects::nonNull),
    /**
     * 考试-重考次数
     */
    SYSTEM_CONFIG_CODE_700("700", validatorForPositiveIntegerOrZero()),
    /**
     * 讲师资源申请特权角色
     */
    SYSTEM_CONFIG_CODE_7001("7001", Objects::nonNull),
    /**
     * 正常上午上班时间
     */
    SYSTEM_CONFIG_CODE_7002("7002", Objects::nonNull),
    /**
     * 正常上午下班时间
     */
    SYSTEM_CONFIG_CODE_7003("7003", Objects::nonNull),
    /**
     * 正常下午上班时间
     */
    SYSTEM_CONFIG_CODE_7004("7004", Objects::nonNull),
    /**
     * 正常下班时间
     */
    SYSTEM_CONFIG_CODE_7005("7005", Objects::nonNull),
    /**
     * 消息发送默认语言
     */
    SYSTEM_CONFIG_CODE_701("701", Objects::nonNull),
    /**
     * 考试防切屏录屏截图
     */
    SYSTEM_CONFIG_CODE_702("702", validatorForZeroOrOne()),
    /**
     * 允许考试人脸识别
     */
    SYSTEM_CONFIG_CODE_703("703", validatorForZeroOrOne()),
    /**
     * 自动保存考试时间
     */
    SYSTEM_CONFIG_CODE_704("704", validatorForPositiveIntegerOrZero()),
    /**
     * 考试竞赛机器人名字
     */
    SYSTEM_CONFIG_CODE_705("705", Objects::nonNull),
    /**
     * 考试竞赛机器人头像
     */
    SYSTEM_CONFIG_CODE_706("706", Objects::nonNull),
    /**
     * 多人考试竞赛人员上限
     */
    SYSTEM_CONFIG_CODE_707("707", validatorForPositiveIntegerOrZero()),
    /**
     * 组队pk中每个队伍允许加入的最大人数限制
     */
    SYSTEM_CONFIG_CODE_708("708", validatorForPositiveIntegerOrZero()),
    /**
     * 课件视频弹题，是否重复弹题
     */
    SYSTEM_CONFIG_CODE_709("709", validatorForZeroOrOne()),
    /**
     * 组织高级信息展示
     */
    SYSTEM_CONFIG_CODE_710("710", validatorForZeroOrOne()),
    /**
     * 学习任务数据是否显示数据导入标记
     */
    SYSTEM_CONFIG_CODE_711("711", validatorForZeroOrOne()),
    /**
     * 学习项目任务进度明细导入模式 1-仅新增导入,2-新增覆盖导入
     */
    SYSTEM_CONFIG_CODE_712("712", Objects::nonNull),
    /**
     * 面授项目开票
     */
    SYSTEM_CONFIG_CODE_713("713", validatorForZeroOrOne()),
    /**
     * 考试最大组卷数量
     */
    SYSTEM_CONFIG_CODE_714("714", validatorForPositiveIntegerOrZero()),
    /**
     * 考试阅卷是否异步阅卷
     */
    SYSTEM_CONFIG_CODE_715("715", validatorForZeroOrOne()),

    /**
     * 三方链接加密私钥
     */
    SYSTEM_CONFIG_CODE_800("800", Objects::nonNull),
    /**
     * 三方链接加密公钥
     */
    SYSTEM_CONFIG_CODE_801("801", Objects::nonNull),
    /**
     * 签到二维码刷新时间间隔
     */
    SYSTEM_CONFIG_CODE_802("802", validatorForPositiveIntegerOrZero()),
    /**
     * 在线人数刷新时间（分）
     */
    SYSTEM_CONFIG_CODE_900("900", validatorForPositiveIntegerOrZero()),
    /**
     * 资源可无校验直接发布
     */
    SYSTEM_CONFIG_CODE_901("901", validatorForPositiveIntegerOrZero()),
    /**
     * 课程打卡达成时间（分钟）
     */
    SYSTEM_CONFIG_CODE_905("905", validatorForPositiveIntegerOrZero()),
    /**
     * 删除临时文件
     */
    SYSTEM_CONFIG_CODE_919("919", validatorForPositiveIntegerOrZero()),
    /**
     * 课程打卡达成时间（分钟）
     */
    SYSTEM_CONFIG_CODE_920("920", validatorForPositiveIntegerOrZero()),
    /**
     * 连续登录天数重置天数 配置后，若连续登录达到此天数，在发放完当天激励后连续登录天数将被重置清零。次日重新开始计算天数。0或者空都不会重置清零
     */
    SYSTEM_CONFIG_CODE_921("921", validatorForPositiveIntegerOrZero()),
    /**
     * 澳美点击配置组织
     */
    SYSTEM_CONFIG_CODE_AM_900("am_900", Objects::nonNull),
    /**
     * 澳美领韩配置组织
     */
    SYSTEM_CONFIG_CODE_AM_901("am_901", Objects::nonNull),
    /**
     * AI maxKB 用户名
     */
    AI_MAX_KB_USERNAME("400001", Objects::nonNull),
    /**
     * AI maxKB 密码
     */
    AI_MAX_KB_PASSWORD("400002", Objects::nonNull),
    /**
     * AI maxKB 里面的模型id
     */
    AI_MAX_KB_MODEL_ID("400003", Objects::nonNull),

    /**
     * 关键词
     */
    AI_MAX_KB_SWITCH_KEYWORD("400006", Objects::nonNull),
    /**
     * 摘要
     */
    AI_MAX_KB_SWITCH_DESC("400007", Objects::nonNull),
    /**
     * 题目
     */
    AI_MAX_KB_SWITCH_QUESTION("400008", Objects::nonNull),
    /**
     * 大纲
     */
    AI_MAX_KB_SWITCH_OUTLINE("400009", Objects::nonNull),
    /**
     * 课件生成题目最大数量
     */
    AI_MAX_KB_CW_QUESTION_MAX_COUNT("400010", Objects::nonNull),

    /**
     * AI课程问答欢迎语
     */
    AI_COURSE_QA_DIFY_WELCOME_MESSAGE("400011", Objects::nonNull),

    /**
     * 测评报告生成时间
     */
    SYSTEM_CONFIG_CODE_22010("22010", validatorForPositiveIntegerOrZero()),
    /**
     * 测评公司均值参考月数
     */
    SYSTEM_CONFIG_CODE_22011("22011", validatorForPositiveIntegerOrZero()),
    /**
     * 上级推荐的课程，是否允许学员在计划中删除
     */
    SYSTEM_CONFIG_CODE_22012("22012", validatorForZeroOrOne()),
    /**
     * 平台可采纳测评项目分类
     */
    SYSTEM_CONFIG_CODE_22013("22013", Objects::nonNull),
    /**
     * 测评报告建议课程是否自动纳入学习计划
     */
    SYSTEM_CONFIG_CODE_22014("22014", validatorForZeroOrOne()),
    /**
     * jwt 时长
     */
    SYSTEM_JWT_EXPIRE("205", validatorForPositiveIntegerOrZero()),
    /**
     * 默认初始密码
     */
    SYSTEM_CONFIG_CODE_206("206", Objects::nonNull),
    /**
     * 实操评价通过分
     */
    SYSTEM_CONFIG_CODE_21014("21014", validatorForPositiveIntegerOrZero()),
    /**
     * 用户胜任力地图是否可以选择胜任力标准地图
     */
    SYSTEM_CONFIG_CODE_21015("21015", validatorForZeroOrOne()),
    /**
     * 要打开的小程序版本。正式版为"release"，体验版为"trial”，开 发版为"develop"。默认是正式版。
     */
    MINIPROGRAM_ENV_VERSION_23000("23000", Objects::nonNull),
    /**
     * 海报分享控制 0-禁用,1-启用
     */
    POSTER_SHARE_CONTROL("23001", Objects::nonNull),
    /**
     * 海报分享整体品牌名称
     */
    POSTER_SHARE_NAME("23002", Objects::nonNull),
    /**
     * 海报分享（课程）宣传文字
     */
    POSTER_SHARE_COURSE_TEXT("23003", Objects::nonNull),
    /**
     * 海报分享（学习项目）宣传文字
     */
    POSTER_SHARE_PROJECT_TEXT("23004", Objects::nonNull),
    /**
     * 海报分享（直播）宣传文字
     */
    POSTER_SHARE_LIVE_TEXT("23005", Objects::nonNull),

    /**
     * 线程池的最大线程数maximumPoolSize
     */
    SYSTEM_CONFIG_CODE_902("902", validatorForPositiveIntegerOrZero()),

    /**
     * 注册用户默认部门配置编码
     */
    SYSTEM_CONFIG_CODE_906("906", Objects::nonNull),

    /**
     * 微信小程序appId配置编码
     */
    SYSTEM_CONFIG_CODE_907("907", Objects::nonNull),

    /**
     * 微信小程序appSecret配置编码
     */
    SYSTEM_CONFIG_CODE_908("908", Objects::nonNull),

    /**
     * 1元人民币可兑换金币个数
     */
    SYSTEM_CONFIG_CODE_909("909", Objects::nonNull),

    /**
     * 学院运营默认培训项目编码
     */
    SYSTEM_CONFIG_CODE_910("910", Objects::nonNull),

    /**
     * 机构会员卡数量限制规则[0:不含已删除用户,1:含已删除用户]
     */
    SYSTEM_CONFIG_CODE_911("911", Objects::nonNull),

    /**
     * 激励是否启用兑换配置
     */
    SYSTEM_CONFIG_CODE_912("912", validatorForZeroOrOne()),

    /**
     * 版块广场可见账号配置
     */
    SYSTEM_CONFIG_CODE_914("914", validatorForZeroOrOne()),

    /**
     * 是否开启会员
     */
    SYSTEM_CONFIG_CODE_915("915", Objects::nonNull),

    /**
     * 复合式学习活动（如学习项目）无任务可立即发布
     */
    SYSTEM_CONFIG_CODE_916("916", validatorForZeroOrOne()),

    /**
     * 学员端-我的任务-无发布资源的栏目是否隐藏
     */
    SYSTEM_CONFIG_CODE_917("917", validatorForZeroOrOne()),

    //外部课程资源配置 31开头的5位数都是

    //一书一课平台配置
    /**
     * 一书一课平台分类编码
     */
    SYSTEM_CONFIG_CODE_31001("31001", Objects::nonNull),

    /**
     * 一书一课平台JSON格式配置
     */
    SYSTEM_CONFIG_CODE_31002("31002", Objects::nonNull),

    //混沌大学平台配置
    /**
     * 混沌大学平台分类编码
     */
    SYSTEM_CONFIG_CODE_31003("31003", Objects::nonNull),

    /**
     * 混沌大学平台JSON格式配置
     */
    SYSTEM_CONFIG_CODE_31004("31004", Objects::nonNull),

    //嘉宾大学平台配置
    /**
     * 嘉宾大学平台分类编码
     */
    SYSTEM_CONFIG_CODE_31005("31005", Objects::nonNull),

    /**
     * 嘉宾大学平台JSON格式配置
     */
    SYSTEM_CONFIG_CODE_31006("31006", Objects::nonNull),

    /**
     * 优秀评论点赞数
     */
    SYSTEM_CONFIG_CODE_41001("41001", Objects::nonNull),

    /**
     * 私有读资源过期时间(秒)
     */
    SYSTEM_CONFIG_CODE_41002("41002", validatorForPositiveIntegerOrZero()),

    /**
     * 是否启用MPS转码 mpsEnable
     */
    SYSTEM_CONFIG_CODE_1001("1001", validatorForZeroOrOne()),
    /**
     * mps mpsAccessKeyId
     */
    SYSTEM_CONFIG_CODE_1002("1002", Objects::nonNull),
    /**
     * mps mpsAccessKeySecret
     */
    SYSTEM_CONFIG_CODE_1003("1003", Objects::nonNull),
    /**
     * 是否关闭pdf数字认证 pdfNoDRM
     */
    SYSTEM_CONFIG_CODE_1004("1004", validatorForZeroOrOne()),
    /**
     * 是否启用PDF兼容显示 pdfProof
     */
    SYSTEM_CONFIG_CODE_1005("1005", validatorForZeroOrOne()),

    /**
     * 音视频提取文本开关
     */
    SYSTEM_CONFIG_CODE_1006("1006", validatorForZeroOrOne()),

    SYSTEM_CONFIG_CODE_203("203", validatorForPositiveIntegerOrZero()),

    /**
     * 个人总排行榜排名数量
     */
    SYSTEM_CONFIG_CODE_42001("42001", validatorForPositiveIntegerOrZero()),

    /**
     * 是否开启CAS登陆
     */
    CAS_LOGIN_ENABLE("1101", validatorForZeroOrOne()),
    /**
     * CAS登陆服务地址
     */
    CAS_LOGIN_SERVICE_URL("1102", Objects::nonNull),
    /**
     * CAS登陆服务登陆地址
     */
    CAS_LOGIN_SERVICE_URL_LOGIN("1103", Objects::nonNull),
    /**
     * CAS登陆服务登出地址
     */
    CAS_LOGIN_SERVICE_URL_LOGOUT("1104", Objects::nonNull),

    /**
     * 是否启用CAS登出
     */
    CAS_LOGIN_SERVICE_URL_ENABLE_LOGOUT("1105", validatorForZeroOrOne()),

    /**
     * 积分是否统计不可用用户
     */
    SYSTEM_CONFIG_CODE_42003("42003", validatorForZeroOrOne()),

    // ----------------↓数据字典迁移过来的参数配置↓----------------------------
    /**
     * 新增
     */
    ADD("Add", Objects::nonNull),

    /**
     * 返回
     */
    BACK("Back", Objects::nonNull),

    /**
     * 删除
     */
    DELETE("Delete", Objects::nonNull),

    /**
     * 编辑
     */
    EDIT("Edit", Objects::nonNull),

    /**
     * 导出
     */
    EXPORT("Export", Objects::nonNull),

    /**
     * 导入
     */
    IMPORT("Import", Objects::nonNull),

    /**
     * 添加/插入
     */
    INSERT("Insert", Objects::nonNull),

    /**
     * 合并
     */
    MERGE("Merge", Objects::nonNull),

    /**
     * 发布
     */
    PUBLISH("Publish", Objects::nonNull),

    /**
     * 课程推荐
     */
    RECOMMEND("Recommend", Objects::nonNull),

    /**
     * 恢复
     */
    RECOVER("Recover", Objects::nonNull),

    /**
     * 设置
     */
    SETTING("Setting", Objects::nonNull),

    /**
     * 置顶
     */
    TOP("Top", Objects::nonNull),

    /**
     * 取消发布
     */
    UNPUBLISH("UnPublish", Objects::nonNull),

    /**
     * 取消课程推荐
     */
    UNRECOMMEND("UnRecommend", Objects::nonNull),

    /**
     * 取消置顶
     */
    UNTOP("UnTop", Objects::nonNull),

    /**
     * 查看/预览
     */
    VIEW("View", Objects::nonNull),

    /**
     * 奖品分类
     */
    AWARD_CATE("AwardCate", Objects::nonNull),

    /**
     * 朋友圈分类
     */
    CIRCLE_CATE("CircleCate", Objects::nonNull),

    /**
     * 培训班分类
     */
    CLASS_CATE("ClassCate", Objects::nonNull),

    /**
     * 课程分类
     */
    COURSE_CATE("CourseCate", Objects::nonNull),

    /**
     * 课程分类（树）
     */
    COURSE_CATE_4_TREE("CourseCate4Tree", Objects::nonNull),

    /**
     * 课件库分类
     */
    COURSE_LIBRARY_CATE("CourseLibraryCate", Objects::nonNull),

    /**
     * 课程标签
     */
    COURSE_TAG_CATE("CourseTagCate", Objects::nonNull),

    /**
     * 课程标签分类
     */
    COURSE_TAG_NEW_CATE("CourseTagNewCate", Objects::nonNull),

    /**
     * 评估库分类
     */
    EVALUATE_LIBRARY_CATE("EvaluateLibraryCate", Objects::nonNull),

    /**
     * 练习
     */
    EXAM_CATE("ExamCate", Objects::nonNull),

    /**
     * 考题库分类
     */
    EXAM_LIBRARY_CATE("ExamLibraryCate", Objects::nonNull),

    /**
     * 案例审核
     */
    EXAMPLE_AUDIT("ExampleAudit", Objects::nonNull),

    /**
     * 案例分类
     */
    EXAMPLE_CATE("ExampleCate", Objects::nonNull),

    /**
     * 练习库分类
     */
    EXERCISE_LIBRARY_CATE("ExerciseLibraryCate", Objects::nonNull),

    /**
     * 辅导任务分类
     */
    FORM_TASK_CATE("FormTaskCate", Objects::nonNull),

    /**
     * 资讯分类
     */
    INFO_CATE("InfoCate", Objects::nonNull),

    /**
     * 知识库分类
     */
    KNOWLEDGE_BASE_TYPE("KnowledgeBaseType", Objects::nonNull),

    /**
     * 知识库标签
     */
    KNOWLEDGE_TAG_CATE("KnowledgeTagCate", Objects::nonNull),

    /**
     * 讲师领域分类
     */
    LECTURER_DOMAIN_CATE("lecturerDomainCate", Objects::nonNull),

    /**
     * 导师分类
     */
    MENTOR_CATE("MentorCate", Objects::nonNull),

    /**
     * 问题分类
     */
    QUESTION_CATE("QuestionCate", Objects::nonNull),

    /**
     * 调研库分类
     */
    SURVEY_LIBRARY_CATE("SurveyLibraryCate", Objects::nonNull),

    /**
     * 试卷库分类
     */
    TESTPAPER_LIBRARY_CATE("TestPaperLibraryCate", Objects::nonNull),

    /**
     * 专题分类
     */
    THEMATIC_CLASS("ThematicClass", Objects::nonNull),

    /**
     * 专题标签
     */
    THEMATIC_TAG("ThematicTag", Objects::nonNull),

    /**
     * 培训方式
     */
    TRAIN_METHOD_CATE("TrainMethodCate", Objects::nonNull),

    /**
     * 用户等级分类
     */
    USER_LEVEL_CATE("UserLevelCate", Objects::nonNull),

    /**
     * 综合素质
     */
    COMPREHENSIVE_QUALITY("ComprehensiveQuality", Objects::nonNull),

    /**
     * 岗位技能
     */
    JOB_SKILLS("JobSkills", Objects::nonNull),

    /**
     * 职称晋级
     */
    TITLE_PROMOTION("TitlePromotion", Objects::nonNull),

    /**
     * 学习时长上报间隔设置
     */
    CLASS_TIME_REPORT_INTERVAL("ClassTimeReportInterval", validatorForPositiveIntegerOrZero()),

    /**
     * 课件有效学习时长
     */
    GRAPHIC_COURSEWARE_LEARN_TIME("GraphicCoursewareLearnTime", validatorForPositiveIntegerOrZero()),

    /**
     * Android1.6+
     */
    ANDROID_1_6("Android1.6+", Objects::nonNull),

    /**
     * IPhone4
     */
    IPHONE4("IPhone4", Objects::nonNull),

    /**
     * 微课件工具(Android)
     */
    WE_TOOL_ANDROID("WETool_Android", Objects::nonNull),

    /**
     * 微课件工具(ios)
     */
    WE_TOOL_IOS("WETool_ios", Objects::nonNull),

    /**
     * 全流程行动学习
     */
    COACH_PLAN_1("CoachPlan_1", Objects::nonNull),

    /**
     * 标准化行动学习
     */
    COACH_PLAN_2("CoachPlan_2", Objects::nonNull),

    /**
     * 被动微行动学习（测评）
     */
    COACH_PLAN_3("CoachPlan_3", Objects::nonNull),

    /**
     * 被动微行动学习（无测评）
     */
    COACH_PLAN_4("CoachPlan_4", Objects::nonNull),

    /**
     * 无领导行动学习（测评）
     */
    COACH_PLAN_5("CoachPlan_5", Objects::nonNull),

    /**
     * 无领导行动学习（无测评）
     */
    COACH_PLAN_6("CoachPlan_6", Objects::nonNull),

    /**
     * 被动无领导行动学习（测评）
     */
    COACH_PLAN_7("CoachPlan_7", Objects::nonNull),

    /**
     * 主动无领导行动学习（测评）
     */
    COACH_PLAN_8("CoachPlan_8", Objects::nonNull),

    /**
     * 主动无领导行动学习（无测评）
     */
    COACH_PLAN_9("CoachPlan_9", Objects::nonNull),

    /**
     * 一般
     */
    GENERAL("general", Objects::nonNull),

    /**
     * 比较符合
     */
    MORE_INLINE("moreinline", Objects::nonNull),

    /**
     * 不太符合
     */
    NOT_QUIET_SUIT("notquietsuit", Objects::nonNull),

    /**
     * 非常符合
     */
    VERY_FITTING("veryfitting", Objects::nonNull),

    /**
     * 很不符合
     */
    VERY_INCONSISTENT("veryinconsistent", Objects::nonNull),

    /**
     * 双语
     */
    BILINGUAL("bilingual", Objects::nonNull),

    /**
     * 英文
     */
    ENGLISH("english", Objects::nonNull),

    /**
     * 高级
     */
    ADVANCED("advanced", Objects::nonNull),

    /**
     * 中级
     */
    INTERMEDIATE("intermediate", Objects::nonNull),

    /**
     * 初级
     */
    PRIMARY("primary", Objects::nonNull),

    /**
     * 代理商
     */
    AGENT("Agent", Objects::nonNull),

    /**
     * 课件合作商
     */
    COURSE_COOPERATE("CourseCooperate", Objects::nonNull),

    /**
     * 付费客户
     */
    PAY_CUSTOMER("PayCustomer", Objects::nonNull),

    /**
     * 试用客户
     */
    TEST_CUSTOMER("TestCustomer", Objects::nonNull),

    /**
     * 音频
     */
    AUDIO("Audio", Objects::nonNull),

    /**
     * 图文
     */
    PIC("Pic", Objects::nonNull),

    /**
     * Scorm
     */
    SCORM("Scorm", Objects::nonNull),

    /**
     * 视频
     */
    VIDEO("Video", Objects::nonNull),

    /**
     * 考试
     */
    EXAMINATION_EXAM("ExaminationExam", Objects::nonNull),

    /**
     * 测验练习
     */
    EXERCISE_EXAM("ExerciseExam", Objects::nonNull),

    /**
     * 回答喜欢
     */
    ANSWER_FAVORITE("AnswerFavorite", Objects::nonNull),

    /**
     * 课程评论喜欢
     */
    COURSE_COMMENT_FAVORITE("CourseCommentFavorite", Objects::nonNull),

    /**
     * 课程喜欢
     */
    COURSE_FAVORITE("CourseFavorite", Objects::nonNull),

    /**
     * 话题热门值
     */
    FORUM_FAVORITE("ForumFavorite", Objects::nonNull),

    /**
     * ForumFavoritexp 话题热门专家值
     */
    FORUM_FAVORITE_XP("ForumFavoritexp", Objects::nonNull),

    /**
     * 资讯评论喜欢
     */
    INFO_COMMENT_FAVORITE("InfoCommentFavorite", Objects::nonNull),

    /**
     * 客户端安装包
     */
    CLIENT_INSTALL_PACKAGE("ClientInstallPackage", Objects::nonNull),

    /**
     * 课件文件
     */
    COURSEWARE_FILE("CourseWareFile", Objects::nonNull),

    /**
     * 课件工具上传的课件包
     */
    COURSEWARE_PACKAGES("CourseWarePackages", Objects::nonNull),

    /**
     * 导入题目
     */
    IMPORT_EXAM_QUESTIONS("ImportExamQuestions", Objects::nonNull),

    /**
     * 资讯视频
     */
    INFO_VIDEO_NEWS("InfoVideoNews", Objects::nonNull),

    /**
     * 调研问卷
     */
    SURVEY_FILE("SurveyFile", Objects::nonNull),

    /**
     * 课程
     */
    COURSE_FIRST_TYPE("CourseFirstType", Objects::nonNull),

    /**
     * 考试
     */
    EXAM_FIRST_TYPE("ExamFirstType", Objects::nonNull),

    /**
     * 资讯
     */
    INFO_FIRST_TYPE("InfoFirstType", Objects::nonNull),

    /**
     * 练习
     */
    PRACTICE_FIRST_TYPE("PracticeFirstType", Objects::nonNull),

    /**
     * 学习项目
     */
    PROJECT_FIRST_TYPE("ProjectFirstType", Objects::nonNull),

    /**
     * 调研
     */
    SURVEY_FIRST_TYPE("SurveyFirstType", Objects::nonNull),

    /**
     * 培训班
     */
    TRAIN_CLASS_FIRST_TYPE("TrainClassFirstType", Objects::nonNull),

    /**
     * 产品网站广告位图片
     */
    ADS_IMG("AdsImg", Objects::nonNull),

    /**
     * 头像（110 * 110）
     */
    AVATAR("Avatar", Objects::nonNull),

    /**
     * 客户登录banner图
     */
    CORP_BANNER_IMG("CorpBannerImg", Objects::nonNull),

    /**
     * 企业Logo图
     */
    CORP_LOGO_IMG("CorpLogoImg", Objects::nonNull),

    /**
     * 课程头条图片（960 * 510）
     */
    COURSE_IMG_FIRST("CourseImgFirst", Objects::nonNull),

    /**
     * 课程小图标（192 * 192）
     */
    COURSE_IMG_ICO("CourseImgIco", Objects::nonNull),

    /**
     * 课程缩略图（192 * 192）
     */
    COURSE_IMG_THUMBNAIL("CourseImgThumbnail", Objects::nonNull),

    /**
     * 课件描述图片
     */
    COURSEWARE_IMG("CourseWareImg", Objects::nonNull),

    /**
     * 资讯头条图片（960 * 510）
     */
    INFO_IMG_FIRST("InfoImgFirst", Objects::nonNull),

    /**
     * 资讯左栏图片（320 * 240）
     */
    INFO_IMG_ICO("InfoImgIco", Objects::nonNull),

    /**
     * 栏目图标
     */
    ITEM_ICON_IMG("ItemIconImg", Objects::nonNull),

    /**
     * 问答答题图片
     */
    KNOW_ANSWER_IMG("KnowAnswerImg", Objects::nonNull),

    /**
     * 问答问题图片
     */
    KNOW_QUESTION_IMG("KnowQuestionImg", Objects::nonNull),

    /**
     * 专题图（960 * 510）
     */
    SUBJECT_IMG("SubjectImg", Objects::nonNull),

    /**
     * 设置资讯加new字样
     */
    NEWS_1("news_1", Objects::nonNull),

    /**
     * 客户端更多菜单
     */
    APP_MENU_ITEM("AppMenuItem", Objects::nonNull),

    /**
     * 学习栏目
     */
    COURSE_ITEM("CourseItem", Objects::nonNull),

    /**
     * 自定义栏目
     */
    CUSTOMIZE_ITEM("CustomizeItem", Objects::nonNull),

    /**
     * 资讯栏目
     */
    NEWS_ITEM("NewsItem", Objects::nonNull),

    /**
     * 客户端菜单
     */
    TOP_MENU_ITEM("TopMenuItem", Objects::nonNull),

    /**
     * 按组织权限分配
     */
    ORG_LIMIT("OrgLimit", Objects::nonNull),

    /**
     * 按指定用户权限分配
     */
    PERSONAL_LIMIT("PersonalLimit", Objects::nonNull),

    /**
     * 图文
     */
    IMAGE_NEWS("ImageNews", Objects::nonNull),

    /**
     * 视频
     */
    VIDEO_NEWS("videoNews", Objects::nonNull),

    /**
     * 公司
     */
    U("U", Objects::nonNull),

    /**
     * 部门
     */
    UM("UM", Objects::nonNull),

    /**
     * 办事处
     */
    UO("UO", Objects::nonNull),

    /**
     * 面授+远程
     */
    CONTACT_REMOTE("Contact+Remote", Objects::nonNull),

    /**
     * 面授
     */
    PROJECT_CONTACT("ProjectContact", Objects::nonNull),

    /**
     * 远程
     */
    PROJECT_REMOTE("ProjectRemote", Objects::nonNull),

    /**
     * 填空/问答
     */
    BLANKS("Blanks", Objects::nonNull),

    /**
     * 判断
     */
    JUDGE("Judge", Objects::nonNull),

    /**
     * 多选
     */
    MULTI("Multi", Objects::nonNull),

    /**
     * 单选
     */
    SINGLE("Single", Objects::nonNull),

    /**
     * 标题/材料
     */
    TITLE("Title", Objects::nonNull),

    /**
     * 培训报名
     */
    APPLY_MODULE("ApplyModule", Objects::nonNull),

    /**
     * 课程
     */
    COURSE_MODULE("CourseModule", Objects::nonNull),

    /**
     * 培训评估
     */
    EVALUATION_MODULE("EvaluationModule", Objects::nonNull),

    /**
     * 考试
     */
    EXAM_MODULE("ExamModule", Objects::nonNull),

    /**
     * 练习
     */
    EXERCISE_MODULE("ExerciseModule", Objects::nonNull),

    /**
     * 公告
     */
    INFO_MODULE("InfoModule", Objects::nonNull),

    /**
     * 问答
     */
    QA_MODULE("QAModule", Objects::nonNull),

    /**
     * 培训签到
     */
    SIGN_IN_MODULE("SignInInModule", Objects::nonNull),

    /**
     * 学员管理
     */
    STUDENT_MODULE("StudentModule", Objects::nonNull),

    /**
     * 在线调研
     */
    SURVEY_MODULE("SurveyModule", Objects::nonNull),

    /**
     * 320*480
     */
    RESOLUTION_480_320("Resolution_480*320", Objects::nonNull),

    /**
     * 480*800
     */
    RESOLUTION_640_480("Resolution_640*480", Objects::nonNull),

    /**
     * 640*960
     */
    RESOLUTION_960_640("Resolution_960*640", Objects::nonNull),

    /**
     * 反馈奖励积分
     */
    REWARD_FORTUNE_1("RewardFortune_1", Objects::nonNull),

    /**
     * 反馈奖励积分
     */
    REWARD_FORTUNE_2("RewardFortune_2", Objects::nonNull),

    /**
     * 反馈奖励积分
     */
    REWARD_FORTUNE_3("RewardFortune_3", Objects::nonNull),

    /**
     * 不满意
     */
    NOT_SATISFIED("notsatisfied", Objects::nonNull),

    /**
     * 比较满意
     */
    RELATIVELY_SATISFIED("relativelysatisfied", Objects::nonNull),

    /**
     * 满意
     */
    SATISFACTION("satisfaction", Objects::nonNull),

    /**
     * 非常不满意
     */
    VERY_DISSATISFIED("verydissatisfied", Objects::nonNull),

    /**
     * 非常满意
     */
    VERY_SATISFIED("verysatisfied", Objects::nonNull),

    /**
     * 女
     */
    FEMALE("female", Objects::nonNull),

    /**
     * 男
     */
    MALE("male", Objects::nonNull),

    /**
     * 课程
     */
    COURSE_SPACE("coursespace", Objects::nonNull),

    /**
     * 学员课件
     */
    COURSEWARE_SPACE("coursewarespace", Objects::nonNull),

    /**
     * 考试
     */
    EXAM_SPACE("examspace", Objects::nonNull),

    /**
     * 作业
     */
    HOMEWORK_SPACE("homeworkspace", Objects::nonNull),

    /**
     * 话题
     */
    KNOW_SPACE("knowspace", Objects::nonNull),

    /**
     * 内训师
     */
    LECTURER_TYPE("lecturerType", Objects::nonNull),

    /**
     * 直播
     */
    LIVE_SPACE("livespace", Objects::nonNull),

    /**
     * 登陆
     */
    LOGIN_SPACE("loginspace", Objects::nonNull),

    /**
     * 其他
     */
    OTHER_SPACE("otherspace", Objects::nonNull),

    /**
     * 学习项目
     */
    PROJECT_SPACE("projectspace", Objects::nonNull),

    /**
     * 学习项目
     */
    TRAIN_SPACE("trainspace", Objects::nonNull),

    /**
     * 课程专题
     */
    SST_COURSE("SSTCourse", Objects::nonNull),

    /**
     * 资讯专题
     */
    SST_INFO("SSTInfo", Objects::nonNull),

    /**
     * 评论
     */
    COMMENT("comment", Objects::nonNull),

    /**
     * 结业
     */
    COMPLETION("completion", Objects::nonNull),

    /**
     * 课程
     */
    COURSE("course", Objects::nonNull),

    /**
     * 课件
     */
    COURSEWARE("courseware", Objects::nonNull),

    /**
     * 评估
     */
    EVALUATION("evaluation", Objects::nonNull),

    /**
     * 考试
     */
    EXAM("exam", Objects::nonNull),

    /**
     * 练习
     */
    EXERCISE("exercise", Objects::nonNull),

    /**
     * 作业
     */
    HOMEWORK("homework", Objects::nonNull),

    /**
     * 资讯
     */
    NEWS("news", Objects::nonNull),

    /**
     * 朋友圈
     */
    QA("qa", Objects::nonNull),

    /**
     * 签到
     */
    SIGN("sign", Objects::nonNull),

    /**
     * 调研
     */
    SURVEY("survey", Objects::nonNull),

    /**
     * 培训班论坛
     */
    TRAIN_FORUM("trainForum", Objects::nonNull),

    /**
     * 统计
     */
    TRAIN_STATIC("trainStatic", Objects::nonNull),

    /**
     * 面授
     */
    TRAIN_CONTACT("TrainContact", Objects::nonNull),

    SYS_TAG_CONFIGURATION("SysTagConfiguration", Objects::nonNull),

    /**
     * 远程
     */
    TRAIN_REMOTE("TrainRemote", Objects::nonNull)
    // ----------------↑数据字典迁移过来的参数配置↑----------------------------
    ;

    /**
     * 数据校验：只能输入0或正整数
     */
    private static Predicate<String> validatorForPositiveIntegerOrZero() {
        return value -> {
            try {
                return Integer.parseInt(value) >= 0;
            } catch (NumberFormatException e) {
                return false;
            }
        };
    }

    /**
     * 数据校验：只能输入 0或1
     */
    private static Predicate<String> validatorForZeroOrOne() {
        return value -> "0".equals(value) || "1".equals(value);
    }


    /**
     * 编码
     */
    private final String code;

    /**
     * 校验器
     */
    private final Predicate<String> validator;

    /**
     * 通过code获取系统参数配置信息
     *
     * @param code 编码
     * @return {@link SystemConfigCodeEnum}
     */
    public static SystemConfigCodeEnum get(String code) {
        for (SystemConfigCodeEnum systemConfigCodeEnum : SystemConfigCodeEnum.values()) {
            if (Objects.equals(systemConfigCodeEnum.code, code)) {
                return systemConfigCodeEnum;
            }
        }
        return null;
    }

    /**
     * 构造函数
     *
     * @param code      编码
     * @param validator 校验器
     */
    SystemConfigCodeEnum(String code, Predicate<String> validator) {
        this.code = code;
        this.validator = validator;
    }

    /**
     * 校验输入是否合法
     *
     * @param value 输入值
     * @return 是否合法
     */
    public boolean validate(String value) {
        return validator.test(value);
    }

}
