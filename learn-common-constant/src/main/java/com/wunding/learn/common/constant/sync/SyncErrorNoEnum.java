package com.wunding.learn.common.constant.sync;


import com.wunding.learn.common.exception.ErrorNoInterface;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 调研错误码 以 7 开头，后面拼上三位数字
 *
 * <AUTHOR>
 * @since 2022-05-24 17:48
 **/
@Getter
@AllArgsConstructor
public enum SyncErrorNoEnum implements ErrorNoInterface {

    TEST_FAIL(510001, "测试失败"),
    DATA_SOURCE_TYPE_ERROR(510002, "数据源类型错误"),
    LOAD_DATA_ERROR(510003, "读取数据错误"),
    DATA_SOURCE_NOT_EXIST(510004, "数据源不存在"),
    BATCH_DATA_NOT_FOUND(510005, "未找到对应的批次数据"),
    FTP_CONNECT_FAIL(510006, "FTP配置不存在"),
    DOWNLOAD_FILE_FAIL(510007, "下载FTP文件失败"),

    ;

    private final int errorCode;

    private final String message;

}
