package com.wunding.learn.common.enums.push;

import com.wunding.learn.common.annotation.EnumI18nProperty;
import com.wunding.learn.common.i18n.util.EnumI18n;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 推送类型
 *
 * <AUTHOR>
 * @since 2015年5月25日 上午10:31:31
 */
@SuppressWarnings("squid:S1192")
public enum PushType implements EnumI18n {

    /**
     * 注意：类型必须与数据库表名保持一致，发邮件时会用到作为参数
     */
    COURSE("course", "课程", "/course/manager/list", "course"),

    COURSE_Lib("course_lib", "共享库课程", "/course/repository/list", "course"),

    NEWS("news", "资讯", "/info/manager/list", "info"),

    EXAM("exam", "考试", "/exam/manager/list", "exam"),

    EXAM_COMPETITION("examCompetition", "考试竞赛", "", "exam"),

    EXERCISE("exercise", "练习", "/exam/manager/list", "exercise"),

    LIVE("live", "直播", "/live/manager/list", "live"),

    EXAM_UNFINISHED("exam_unfinished", "未考催办", "/exam/manager/list", "exam"),

    SURVEY("survey", "调研", "/survey/manager/list", "survey"),

    SPECIAL_TOPIC("specialtopic", "专题", "/special/manager/list", "specialtopic"),

    SPECIAL_TASK_COURSE("specialtask_course", "专题课程任务", "/special/task", "course"),

    SPECIAL_TASK_EXAM("specialtask_exam", "专题考试任务", "/special/task", "exam"),

    SPECIAL_TASK_EXERCISE("specialtask_exercise", "专题练习任务", "/special/task", "exercise"),

    SPECIAL_TASK_SURVEY("specialtask_survey", "专题调研任务", "/special/task", "survey"),

    SPECIAL_TASK_LIVE("specialtask_live", "专题直播任务", "/special/task", "live"),

    TRAIN_CLASS("train_class", "培训班", "/train/trainClass/list", "train"),

    APPLY("apply", "培训报名", "/train/trainClass/relate/apply", "apply"),

    SIGN_IN("signin", "活动签到", "/train/SignIn/list", "signin"),

    AWARD("award", "奖品", "/space/award/list", "award"),

    PROMOTED_GAME("challenge", "闯关游戏", "/game/promotedGames", "promotedgame"),

    PROJECT("project", "学习项目", "/project/manage", "project"),

    PROJECT_APPLY("project_apply", "学习项目报名审核", "/project/manage", "apply"),

    PROJECT_TASK_COURSE("projecttask_course", "学习项目课程任务", "/project/task", "course"),

    PROJECT_TASK_EXAM("projecttask_exam", "学习项目考试任务", "/project/task", "exam"),

    PROJECT_TASK_EXERCISE("projecttask_exercise", "学习项目练习任务", "/project/task", "exercise"),

    PROJECT_TASK_SURVEY("projecttask_survey", "学习项目调研任务", "/project/task", "survey"),

    PROJECT_TASK_LIVE("projecttask_live", "学习项目直播任务", "/project/task", "live"),

    PROJECT_TASK_TRAIN("project_task_apply", "学习项目培训班任务", "/project/task", "train"),

    PROJECT_TASK_FORM("projecttask_form", "学习项目辅导任务", "/project/task", "train"),

    PROJECT_TASK_PROJECT("projecttask_project", "学习项目学习项目任务", "/project/task", "project"),

    PROJECT_TASK_PRACTICAL_OPERATION("projecttask_practicalOperation", "学习项目实操任务", "/project/task",
        "practical_operation"),

    QUICK_PROJECT("quick_project", "快速培训", "", "project"),

    QUICK_PROJECT_TASK_COURSE("quick_project_task_course", "快速培训课程任务", "", "project"),

    QUICK_PROJECT_TASK_EXAM("quick_project_task_exam", "快速培训考试任务", "", "project"),

    TB_RECRUITING("recruiting", "招募活动", "/recruiting/list", "tb_recruiting"),

    APPRAISE("appraise", "评价", "", ""),


    MEETING("meeting", "答辩会议", "", ""),

    READ("reading", "共读", "", ""),

    EMIGRATED_TASK("emigrated_task", "闯关关卡任务", "", "promotedgame"),

    LECTURER_WARN("lecturer_warn", "讲师预警", "", ""),

    VOTE("vote", "投票", "", ""),

    TRAIN("train", "培训项目", "/train/findTrainList", "train"),

    TRAIN_COURSE("trainproject_course", "培训项目课程活动", "/train/trainActivity", "course"),

    TRAIN_EXAM("trainproject_exam", "培训项目考试活动", "/train/trainActivity", "exam"),

    TRAIN_ACTIVITY("train_activity", "培训项目活动", "/trainActivity/stageActivityList", "train"),

    TRAIN_CLASSES("train_classes", "培训项目班级", "/trainActivity/stageActivityList", "train"),

    LEARN_MAP("learnMap", "学习地图", "", ""),

    FACE_PROJECT("faceProject", "面授项目", "", ""),

    FACE_PROJECT_SCHEDULE("faceProject_schedule", "面授项目日程", "", "project_schedule"),

    FORM("form", "辅导", "", ""),

    Evaluation("evaluation", "评估", "", ""),

    Work("work", "作业", "", ""),

    TRAIN_PROGRAM("trainproject", "培训项目", "", ""),

    ASSESS_PROJECT("assessProject", "测评项目", "", ""),

    FORM_EXAMINE("form_examine", "辅导审核", "", "");


    /**
     * 成员变量
     */
    private String key;

    @EnumI18nProperty
    private String text;
    /**
     * 返回时跳转的路径
     */
    private String url;
    /**
     * 对应的数据库表
     */
    private String dbtable;

    /**
     * 构造方法
     */
    PushType(String key, String text, String url, String dbtable) {
        this.key = key;
        this.text = text;
        this.url = url;
        this.dbtable = dbtable;
    }


    /**
     * 推送渠道
     *
     * @param value 推送类型
     * @return {@link PushType}
     */
    public static PushType getPushType(String value) {
        for (PushType pushType : values()) {
            if (Objects.equals(pushType.getKey(), value)) {
                return pushType;
            }
        }
        return null;
    }


    /**
     * key对应内容 info-资讯
     */
    public static Map<String, String> getPushTypes() {
        Map<String, String> map = new LinkedHashMap<>(8);
        PushType[] types = PushType.values();
        for (PushType t : types) {
            map.put(t.getKey(), t.getText());
        }
        // 过滤掉奖品
        map.remove("award");
        // 删除培训班相关
        map.remove(TRAIN_CLASS.getKey());
        map.remove(APPLY.getKey());
        map.remove(PROJECT_TASK_TRAIN.getKey());
        return map;
    }

    public static String getText(String key) {
        PushType[] types = PushType.values();
        for (PushType t : types) {
            if (Objects.equals(t.getKey(), key)) {
                return t.getText();
            }
        }
        return null;
    }

    /**
     * key对应返回路径 info-/info/manager/
     */
    public static Map<String, String> getPushTypeUrl() {
        Map<String, String> map = new LinkedHashMap<>(8);
        PushType[] types = PushType.values();
        for (PushType t : types) {
            map.put(t.getKey(), t.getUrl());
        }
        return map;
    }

    /**
     * key对应数据表 news-info
     */
    public static Map<String, String> getPushTypeDbTab() {
        Map<String, String> map = new LinkedHashMap<>(8);
        PushType[] types = PushType.values();
        for (PushType t : types) {
            map.put(t.getKey(), t.getDbtable());
        }
        return map;
    }

    public String getKey() {
        return key;
    }

    public String getText() {
        return i18n(name(), this.text);
    }

    public String getUrl() {
        return url;
    }

    public String getDbtable() {
        return dbtable;
    }

}
