package com.wunding.learn.common.constant.file;


import com.wunding.learn.common.exception.ErrorNoInterface;

/**
 * 文件服务错误码 以 1 开头，后面拼上三位数字
 *
 * <AUTHOR>
 * @date 2022/3/16
 */
public enum FileErrorNoEnum implements ErrorNoInterface {

    /**
     * 文件格式错误
     */
    ERR_FILE_FORMAT(1019, "文件格式错误"),

    /**
     * 解压失败
     */
    ERR_FILE_UNZIP(1022, "解压失败"),

    /**
     * 文件打包失败
     */
    ERR_FILE_ZIP(1025, "文件打包失败"),

    /**
     * scorm 课件zip中内容有误
     */
    ERR_SCORM_XML(1023, "内容有误"),

    /**
     * 图文课件格式不正确
     */
    CW_PIC_FORMAT_NO_MSG(1024, "格式不正确"),

    /**
     * 上传内容为空
     */
    ERR_POST_CONTENT_NULL(1027, "上传内容为空"),

    /**
     * 必传文件未上传
     */
    ERR_REQUIRD_FILE_NULL(1028, "必传文件未上传"),

    /**
     * 文件正在转码中
     */
    ERR_FILE_TRANSFORMING(1029, "文件正在转码中，不可预览"),

    /**
     * 课件格式不正确
     */
    CW_FORMAT_ERROR_MSG(1124, "课件格式不正确"),

    /**
     * 不支持flash格式课件
     */
    CW_FLASH_NOT_SUPPORT_MSG(1125, "不支持flash格式课件"),

    /**
     * 课件格式不正确,SCORM包根目录未找到imsmanifest.xml或scorm.xml文件
     */
    CW_FORMAT_ERROR_MSG_NOTFUND_XML(1126, "课件格式不正确,SCORM包根目录未找到imsmanifest.xml或scorm.xml文件"),

    /**
     * 操作失败!
     */
    FILE_OPERATION_FAIL(1500, "操作失败!"),

    /**
     * 导入的数据为空
     */
    IMPORT_DATA_EMPTY(1444, "导入的数据为空"),

    /**
     * 请下载正确的模版进行导入
     */
    IMPORT_DATA_TEMPLATE_ERROR(1445, "请下载正确的模版进行导入"),

    /**
     * 导出数据超出范围，请修改导出格式
     */
    EXPORT_DATA_MAX_LENGTH_ERROR(1446, "导出数据超出范围，请修改导出格式"),

    /**
     * 导出失败
     */
    EXPORT_DATA_ERROR(1050, "导出失败"),

    /**
     * 复制出错
     */
    COPY_FILE_FAIL(1447, "复制出错"),

    /**
     * 无数据导出
     */
    EXPORT_DATA_NULL(1448, "无数据导出"),

    /**
     * 文件资源已损坏
     */
    ERR_FILE_DATA_DESTROY(1449, "文件资源已损坏"),

    /**
     * 文件不得为空
     */
    ERR_FILE_NOT_NULL(1450, "文件不得为空"),

    /**
     * 达到空间大小限制，请与管理员联系
     */
    ERR_BUCKET_QUOTA(1451, "达到空间大小限制，请与管理员联系"),

    /**
     * 证书图片格式错误
     */
    ERR_CERT_RELATE_IMAGE_FORMAT(1452, "证书图片格式错误"),

    ERR_FILE_CATEGORY_ORG_CAN_NOT_NULL(1552, "使用授权范围和分类管理单位不能为空"),

    ERR_MANAGE_ORG_OVER_VIEW_ORG(1453, "分类管理单位超过使用授权范围"),

    ERR_FILE_CATEGORY_NOT_EXIST(1454, "分类不存在"),

    ERR_FILE_CATEGORY_CAN_NOT_USE(1455, "不能将当前分类或者子类作为上级分类"),

    /**
     * 分类已被引用不能删除
     */
    ERR_CATEGORY_NOT_DELETED(1456, "已被引用不能删除"),

    /**
     * 分类不可用,请重新选择
     */
    FILE_CATEGORY_NOT_AVAILABLE(1457, "分类不可用,请重新选择"),

    ERR_HAVEN_NOT_SELECT(1458, "请先选择资源！"),

    ERR_NOT_OPERATION(1459, "共享库分类,不能进行操作！"),

    ERR_FILE_CATEGORY_EXISTS_SOURCE(1460, "该分类下存在图片或下级分类，不允许删除"),

    IMPORT_DATA_TEMPLATE_ANSWER_FORMAT_ERROR(1461, "第{0}题, 标准答案格式不正确"),

    ERR_CLOUD_FILE_CHUNK_IS_NOT_EQUAL_TOTAL(1462, "请上传完所有分片后进行合并"),

    ERR_CLOUD_FILE_CHUNK_NOT_EXIST(1463, "请上传分片后进行合并"),

    ERR_CLOUD_FILE_SUFFIX(1464, "文件格式不正确"),

    ERR_COMPOSE_FAILED(1465, "文件合并失败,传输过程中文件损坏，请刷新页面后重新上传"),

    ERR_SEAL_RECOGNIZE_FAILED(1466, "公章识别失败"),

    ERR_NO_FILES_TO_DOWNLOAD(1467, "没有文件可下载"),

    ERR_NO_FILES_BUSINESS_TYPES(1468, "文件业务类型无效"),

    SAVE_UPLOAD_FILE_ERROR(1469, "保存上传文件失败"),

    IMAGE_FILE_ERROR(1470, "图片文件信息错误"),

    FILE_ERR_DOWNLOAD(1471, "源文件下载错误"),

    DOWNLOAD_FILE_ERROR(1472, "下载文件发生异常"),

    NO_ZIP_FILE(1473, "没有可以压缩下载的文件"),

    CW_UNZIP_FAIL(1474, "解压课件失败"),

    ERR_FILE_TEMPLATE_NOT_EXIST(1475, "模板做课, 模板文件不存在"),

    ERR_FILE_MIME_NOT_SUPPORT(1476, "不支持的媒体类型"),

    ERR_UPLOAD_FILE_NOT_NULL(1478, "上传文件不可为空"),

    ERR_CER_BACKGROUND_IMAGE_NOT_FOUND(1479, "证书背景图片不存在"),

    ERR_PHOTO_FORMAT(1481, "上传文件格式不正确,仅能上传以下格式(png,jpg,jpeg,x-icon,ico,icon)"),

    ERR_CLOUD_FILE_LOCK(1482, "获取锁失败，有其他人正在压缩上传，新刷新后重试"),

    ERR_OPERATION_INLAY_IMG_FAIL(1483, "不允许操作内置图片数据"),

    PDF_ENCRYPTED_ERROR_MSG(1484, "涉密文件不支持处理，请重新上传资源")
    ;


    private int errorCode;
    private String message;

    private FileErrorNoEnum(int errorCode, String message) {
        this.errorCode = errorCode;
        this.message = message;
    }

    @Override
    public int getErrorCode() {
        return errorCode;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
