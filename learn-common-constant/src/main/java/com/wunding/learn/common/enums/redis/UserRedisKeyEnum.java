package com.wunding.learn.common.enums.redis;


import com.wunding.learn.common.context.user.UserThreadContext;

/**
 * 用户模块redis key规则
 *
 * <AUTHOR>
 * @date 2022/2/24
 */
public enum UserRedisKeyEnum {

    /**
     * 登陆用户信息，后面拼用户id
     */
    LOGIN_USER_INFO("user:loginUserInfo:"),

    /**
     * 用户登陆的系统及设备id
     */
    LOGIN_USER_DEVICE_ID("user:login:deviceId"),

    /**
     * 用户登录验证码
     */
    LOGIN_USER_LOGIN_CODE("user:loginCode:"),

    /**
     * 用户导入检查用户名是否存在
     */
    IMPORT_USER_CHECK_LOGIN_NAME("user:import:user:check:loginName:"),

    /**
     * 用户导入检查email是否存在
     */
    IMPORT_USER_CHECK_EMAIL("user:import:user:check:email:"),

    /**
     * 用户导入检查手机号是否存在
     */
    IMPORT_USER_CHECK_MOBILE("user:import:user:check:mobile:"),

    /**
     * 用户导入检查岗位是否存在
     */
    IMPORT_USER_CHECK_POSE_ID("user:import:user:check:postId:"),

    /**
     * 用户导入检查身份证号是否存在
     */
    IMPORT_USER_CHECK_ID_NUMBER("user:import:user:check:idNumber:"),

    /**
     * 路由权限
     */
    ROLE_API("role:api:"),

    /**
     * 导入身份用户
     */
    IMPORT_IDENTITY_USER_CHECK_LOGIN_NAME("user:import:identityUser:check:loginName:"),

    /**
     * 导入用户身份
     */
    IMPORT_USER_IDENTITY_CHECK_LOGIN_NAME("user:import:userIdentity:check:loginName:"),

    /**
     * 每日连续登陆打卡
     */
    INTERVIEW_EVERY_DAY_LOGIN_USERID("user:interview:everyDayLogin:userId:"),
    /**
     * 首页diy打卡签到
     */
    EVERY_DAY_CLOCK_IN_USERID("user:everyDay:clockIn:userId:"),

    /**
     * 下发范围刷新
     */
    VIEW_LIMIT_EXECUTE_KEY("user:viewLimit:execute:"),

    /**
     * 下发变动刷新
     */
    VIEW_CHANGE_REFRESH_KEY("user:viewChange:refresh:"),

    /**
     * 用户任务
     */
    USER_TASK("user:task:"),

    /**
     * 用户登陆的系统及设备id
     */
    ALLOW_USER_MULTIPLE_LOGIN("user:login:multipleSwitch"),

    SHORT_LINK_ID("user:shortLink:id"),
    ;

    private String key;

    UserRedisKeyEnum(String key) {
        this.key = key;
    }

    public String getKey() {
        return key + ":" + UserThreadContext.getTenantId() + ":";
    }
}
