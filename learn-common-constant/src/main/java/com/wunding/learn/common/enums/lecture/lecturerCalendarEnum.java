package com.wunding.learn.common.enums.lecture;

/**
 * 讲师预约枚举类 上午  : 8:00-12:00 下午:  >12:00-18:00 晚上:>18:00~24:00
 *
 * <AUTHOR>
 * @create 2019/3/8 10:51
 */
public enum lecturerCalendarEnum {

    /**
     * 上午开始时间
     */
    MORNINGBEGIN("8:00"),
    /**
     * 上午结束时间
     */
    MORNINGEND("12:00"),
    /**
     * 下午开始时间
     */
    AFTERNOONBEGIN("12:00"),
    /**
     * 下午开始时间
     */
    AFTERNOONEND("18:00"),
    /**
     * 晚上开始时间
     */
    EVENINGBEGIN("18:00"),
    /**
     * 晚上结束时间
     */
    EVENINGEND("24:00");

    private final String value;

    lecturerCalendarEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }

}
