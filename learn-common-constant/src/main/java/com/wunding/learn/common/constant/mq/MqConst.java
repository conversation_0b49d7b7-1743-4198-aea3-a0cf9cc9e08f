package com.wunding.learn.common.constant.mq;

/**
 * Mq发送的一些常量
 *
 * <AUTHOR> href="mailto:<EMAIL>">wgr</a>
 * @since 2022/3/9 10:27
 */
public class MqConst {

    private MqConst() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 点对点交换机
     */
    public static final String EXCHANGE = "learn";

    /**
     * topic交换机
     */
    public static final String EXCHANGE_TOPIC = "learn_topic";

    /**
     * 用户激励消息常量
     */
    public static final String USER_EXCITATION = "user_excitation";

    /**
     * 更新用户可见范围队列
     */
    public static final String USER_VIEW_LIMIT_QUEUE = "user_change_view_limit_queue_";

    /**
     * 用户资源下发范围回调队列
     */
    public static final String USER_VIEW_LIMIT_CHANGE_NOTIFY_QUEUE = "user_view_limit_change_notify_queue_";

    /**
     * 更新用户可见范围
     */
    public static final String USER_VIEW_LIMIT_ROUTING_KEY = "user_view_limit_routing_Key";

    /**
     * 用户激励消息RoutingKey
     */
    public static final String USER_EXCITATION_ROUTING_KEY = "user_excitation_routing_Key";

    /**
     * 全局激励同步消息RoutingKey
     */
    public static final String GLOBAL_EXCITATION_SYNC_ROUTING_KEY = "global_excitation_sync_routing_Key";

    /**
     * 资源激励初始化消息RoutingKey
     */
    public static final String RESOURCES_EXCITATION_INIT_ROUTING_KEY = "resources_excitation_init_routing_key";

    /**
     * 添加签到用户
     */
    public static final String USER_ADD_SIGN_USER = "user_add_sign_user";

    /**
     * 下发范围处理队列
     */
    public static final String VIEW_LIMIT_QUEUE = "ViewLimitQueue_";

    /**
     * 资源发布 删除状态同步队列
     */
    public static final String ACTIVITY_STATUS_CHANGE_QUEUE = "activity_status_change_queue";

    /**
     * 资源发布、删除状态同步
     */
    public static final String ACTIVITY_STATUS_CHANGE_KEY = "activity.status.change.key";

    /**
     * 搜索关键字
     */
    public static final String SEARCH_KEY_ROUTING_KEY = "search_key_routing_Key";

    /**
     * 学习项目完成
     */
    public static final String PROJECT_TASK_COMPLETED_ROUTING_KEY = "project_task_completed_routing_key";

    /**
     * 初始化用户激励数据路由key
     */
    public static final String INIT_USER_EXCITATION_ROUTING_KEY = "init.user.excitation.key";

    /**
     * 激励交易事件路由Key
     */
    public static final String EXCITATION_TRADE_EVENT_ROUTING_KEY = "excitation.trade.event.key";

    /**
     * 激励消费事件路由Key
     */
    public static final String EXCITATION_CONSUME_EVENT_ROUTING_KEY = "excitation.consume.event.key";

    /**
     * 讲师工作台应用资源状态变化路由Key
     */
    public static final String WORKBENCH_STATUS_CHANGE_ROUTING_KEY = "workbench.app.change.event.key";

    /**
     * 第三方组织同步历史记录key
     */
    public static final String ORG_THIRD_SYNC_RECORD_KEY = "org_third_sync_record_key";

    /**
     * 第三方岗位同步历史记录key
     */
    public static final String POST_THIRD_SYNC_RECORD_KEY = "post_third_sync_record_key";

    /**
     * 第三方用户同步历史记录key
     */
    public static final String USER_THIRD_SYNC_RECORD_KEY = "user_third_sync_record_key";

    /**
     * 第三方组织同步历史记录队列
     */
    public static final String ORG_THIRD_SYNC_RECORD_QUEUE = "org_third_sync_record_queue";

    /**
     * 第三方岗位同步历史记录队列
     */
    public static final String POST_THIRD_SYNC_RECORD_QUEUE = "post_third_sync_record_queue";

    /**
     * 第三方用户同步历史记录队列
     */
    public static final String USER_THIRD_SYNC_RECORD_QUEUE = "user_third_sync_record_queue";

    /**
     * 资源删除、取消发布、重新发布时推送消息状态变化路由Key
     */
    public static final String PUSH_MESSAGE_CHANGE_EVENT_KEY = "push_message_change_event_key";

    /**
     * 资源删除、取消发布、重新发布时推送消息变更队列
     */
    public static final String PUSH_MESSAGE_CHANGE_QUEUE = "push_message_change_queue";

    /**
     * 培训计划分类变化路由Key
     */
    public static final String PLAN_CATEGORY_CHANGE_ROUTING_KEY = "plan.category.change.event.key";

    /**
     * 课程培训分类变更事件路由Key
     */
    public static final String COURSE_TRAIN_CATEGORY_CHANGE_ROUTING_KEY = "course.trainCategory.change.event.key";

    /**
     * 课程分类变更事件路由Key
     */
    public static final String COURSE_CATEGORY_CHANGE_ROUTING_KEY = "course.category.change.event.key";

    /**
     * 讲师课酬事件路由Key
     */
    public static final String LECTURER_TEACH_AWARD_EVENT_ROUTING_KEY = "lecturer_teach_award.event.key";

    /**
     * 资源同步队列
     */
    public static final String RESOURCE_SYNC_QUEUE = "resource_sync_queue";

    /**
     * 学员学习事件
     */
    public static final String USER_STUDY_CONDITION_EVENT_SYNC_QUEUE = "user_study_condition_event_sync_queue";

    /**
     * 资源同步RoutingKey
     */
    public static final String RESOURCE_SYNC_ROUTING_KEY = "resource.sync.routing.key";

    /**
     * 资源记录同步队列
     */
    public static final String RESOURCE_RECORD_SYNC_QUEUE = "resource_record_sync_queue";

    /**
     * 资源记录同步RoutingKey
     */
    public static final String RESOURCE_RECORD_SYNC_ROUTING_KEY = "resource.record.sync.routing.key";



    /**
     * 预下单事件路由Key
     */
    public static final String PRE_PAY_ORDER_EVENT_ROUTING_KEY = "pre_pay_order.event.key";

    /**
     * 预下单事件队列
     */
    public static final String PRE_PAY_ORDER_EVENT_QUEUE = "pre_pay_order_event_queue";

    /**
     * 预下单重试事件路由Key
     */
    public static final String PRE_PAY_ORDER_RETRY_EVENT_ROUTING_KEY = "pre_pay_order.retry.event.key";

    /**
     * 预下单重试事件队列
     */
    public static final String PRE_PAY_ORDER_RETRY_EVENT_QUEUE = "pre_pay_order_retry_event_queue";


    /**
     * 订单支付成功事件路由key
     */
    public static final String ORDER_PAY_SUCCESS_EVENT_ROUTING_KEY = "order_pay_success.event.key";

    /**
     * 订单支付成功事件路由key
     */
    public static final String ORDER_PAY_SUCCESS_EVENT_ROUTING_KEY_EXCITATION =
        "order_pay_success.event.key.EXCITATION";

    /**
     * 订单支付成功事件路由key
     */
    public static final String ORDER_PAY_SUCCESS_EVENT_ROUTING_KEY_MEMBER_CARD =
        "order_pay_success.event.key.MEMBER_CARD";

    /**
     * 激励类型订单支付成功事件队列
     */
    public static final String ORDER_OF_EXCITATION_PAY_SUCCESS_EVENT_QUEUE = "order_of_excitation_pay_success_event_queue";

    /**
     * 会员卡类型订单支付成功事件队列
     */
    public static final String ORDER_OF_MEMBER_CARD_PAY_SUCCESS_EVENT_QUEUE = "order_of_member_card_pay_success_event_queue";

    /**
     * 统计初始化完成交换机
     */
    public static final String STATISTICS_INIT_FINISH_EXCHANGE = "statistics_init_finish_exchange";

    /**
     * 课程统计定时任务初始化完成路由key
     */
    public static final String COURSE_STATISTICS_INIT_FINISH_ROUTING_KEY = "course_statistics_init_finish_routing_key";


    /**
     * 学习项目资源统计路由key
     */
    public static final String PROJECT_RESOURCE_STATISTIC_ROUTING_KEY = "project_resource_statistic_routing_key";


    /**
     * 学习项目按月举办统计
     */
    public static final String PROJECT_HOLD_MONTH_STATISTIC_ROUTING_KEY = "project_hold_month_statistic_routing_key";


    /**
     * 学习项目按年举办统计
     */
    public static final String PROJECT_HOLD_YEAR_STATISTIC_ROUTING_KEY = "project_hold_year_statistic_routing_key";


    /**
     * 学习项目按月参与统计
     */
    public static final String PROJECT_JOIN_MONTH_STATISTIC_ROUTING_KEY = "project_join_month_statistic_routing_key";


    /**
     * 学习项目按年参与统计
     */
    public static final String PROJECT_JOIN_YEAR_STATISTIC_ROUTING_KEY = "project_join_year_statistic_routing_key";

    /**
     * 资源下发方案记录路由key
     */
    public static final String RESOURCE_VIEW_LIMIT_RECORD_ROUTING_KEY = "resource_view_limit_record_project";

    /**
     * 资源下发方案记录路由处理队列
     */
    public static final String RESOURCE_VIEW_LIMIT_RECORD_QUEUE = "ViewLimitRecordQueue_";

    /**
     * 考试统计定时任务初始化完成路由key
     */
    public static final String EXAM_STATISTICS_INIT_FINISH_ROUTING_KEY = "exam_statistics_init_finish_routing_key";


    /**
     * 会员身份交换机
     */
    public static final String MEMBER_IDENTITY_EXCHANGE = "member_identity_exchange";

    /**
     * 会员身份变更路由key
     */
    public static final String MEMBER_IDENTITY_CHANGE_ROUTING_KEY = "member_identity_change_routing_key";

    /**
     * 订单退款完成事件路由key
     */
    public static final String ORDER_REFUND_FINISH_EVENT_ROUTING_KEY = "order_refund_finish.event.key";

}
