package com.wunding.learn.common.dto;

import com.wunding.learn.common.constant.train.TrainResourceCheckUnPassType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

/**
 * <p>
 * 会员校验结果对象
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON><PERSON><PERSON><PERSON></a>
 * @date 2024/2/2 16:52
 */
@Data
public class ResourcePermissionCheckDTO {
    /**
     * 资源id
     */
    @Schema(description = "资源id")
    String resourceId;

    /**
     * 用户id
     */
    @Schema(description = "用户id")
    String userId;

    /**
     * 资源是否配置了需要付费
     */
    @Schema(description = "资源是否配置了需要付费")
    Boolean isSetPaid = false;


    /**
     * 资源是否配置了会员
     */
    @Schema(description = "资源是否配置了会员")
    Boolean isSetMember = false;

    /**
     * 资源是否配置了没有权限也展示
     */
    @Schema(description = "资源是否配置了没有权限也展示")
    Boolean isSetDisplayWithOutViewLimit = false;

    /**
     * 是否已经进行过付费
     */
    @Schema(description = "是否已经进行过付费")
    Boolean isPaid;

    /**
     * 是否在下发范围内
     */
    @Schema(description = "是否在下发范围内")
    Boolean isInLimit;


    /**
     * 校验结果
     */
    @Schema(description = "校验结果")
    TrainResourceCheckUnPassType result;

    /**
     * 校验步骤信息
     */
    @Schema(description = "校验步骤信息")
    String remark;


}
