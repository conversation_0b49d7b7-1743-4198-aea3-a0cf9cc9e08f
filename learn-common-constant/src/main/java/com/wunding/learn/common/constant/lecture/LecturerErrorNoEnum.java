package com.wunding.learn.common.constant.lecture;


import com.wunding.learn.common.exception.ErrorNoInterface;
import lombok.Setter;

/**
 * 讲师错误码 以 4 开头，后面拼上三位数字
 *
 * <AUTHOR>
 * @date 2022/4/8 14:29
 */
public enum LecturerErrorNoEnum implements ErrorNoInterface {

    /**
     * 讲师不存在
     */
    ERR_LECTURER_NOT_EXIST(4001, "当前用户无讲师身份，无法访问"),

    /**
     * 讲师分类不存在
     */
    ERR_LECTURER_CATEGORY_NOT_EXIST(4002, "讲师分类不存在"),

    /**
     * 讲师级别不存在
     */
    ERR_LECTURER_LEVEL_NOT_EXIST(4003, "讲师级别不存在"),

    /**
     * 同一分类下等级序号重复
     */
    ERR_LECTURER_LEVEL_SORT_EXISTS(4004, "同一分类下等级序号重复"),

    /**
     * 该用户已经是讲师，不可重复添加！
     */
    ERR_LECTURER_EXISTS(4005, "该用户已经是讲师，不可重复添加或申请！"),

    /**
     * 选择的讲师状态无需改变！
     */
    ERR_LECTURER_STATUS_NOT_CHANGE(4006, "选择的讲师状态无需改变！"),

    /**
     * 启用的配置请输入参数
     */
    ERR_LECTURER_AVAILABLE_PARAM_NOT_EXISTS(4007, "启用的配置请输入参数"),

    /**
     * 请至少启用一条规则
     */
    ERR_LECTURER_RULE_NOT_OPEN(4008, "请至少启用一条规则"),

    /**
     * 规则配置信息不存在
     */
    ERR_LECTURER_RULE_CONFIG_NOT_EXISTS(4009, "规则配置信息不存在"),

    ERR_LECTURER_VOTE_CANT_GIVE_YOURSELF_A_LIKE(4010, "不允许给自己点赞"),

    ERR_LECTURER_VOTE_NO_DOUBLE_LIKES(4011, "不允许给自己点赞"),

    ERR_LECTURER_CATEGORY_HAS_LEVEL(4012, "选择的讲师分类下存在等级，不能删除"),

    /**
     * 该用户已经申请讲师，不可重复申请！
     */
    ERR_APPLY_LECTURER_EXISTS(4013, "该用户已经申请讲师，不可重复申请！"),

    ERR_LECTURER_DETAIL_NOT_EXISTS(4014, "讲师详细资料不存在"),

    ERR_LECTURER_RULE_NOT_EXISTS(4015, "规则信息不存在"),

    ERR_LECTURER_CATEGORY_LEVEL_AVAILABLE(4016, "选择的分类存在已启用等级，不能禁用"),

    ERR_LECTURER_LEVEL_RELEVANCE(4017, "选择的等级已被讲师关联，不能删除"),

    ERR_LECTURER_LEVEL_LECTURER_RELEVANCE(4018, "选择的等级被入库讲师引用，无法禁用"),

    ERR_LECTURER_LEVEL_NOT_AVAILABLE(4019, "选择的讲师 讲师等级已禁用"),

    ERR_LECTURER_CATEGORY_NOT_AVAILABLE(4020, "选择的等级 的讲师分类被禁用"),

    ERR_LECTURER_CODE_MUST_NUMBER(4021, "generateCode fail, the code must be able to get a number !"),

    /**
     * 项目讲师记录已存在，不能重复添加
     */
    ERR_PROJECT_LECTURER_EXISTS(4022, "项目讲师记录已存在，不能重复添加"),

    ERR_LECTURER_NOT_ISSUE(4023, "包含未出库讲师,不能删除"),

    ERR_LECTURER_DELETE_WITH_BUSINESS_DATA(4024, "讲师有关联业务数据,不能删除"),

    ERR_COURSE_IS_DELETED(4025, "由于关联课程被删,不支持编辑 抱歉请谅解"),

    ERR_MATERIAL_NOT_EXIST(4401, "资源不存在"),

    ERR_CATEGORY_BEEN_USED(4501, "分类已经被使用，不能禁用"),

    ERR_CATEGORY_BEEN_AVAILABLE(4502, "分类已经启用，不能删除"),

    ERR_MATERIAL_BEEN_AVAILABLE(4601, "知识库材料已启用，无法删除"),

    ERR_LECTURER_HAS_BEEN_ORDERED(4701, "讲师已经被预约，无法取消"),

    ERR_LECTURER_USER_REPEAT(4801, "导入的讲师登录账号重复，请修改后重新导入"),

    ERR_LECTURER_USER_NOT_EFFECT(4802, "导入的讲师登录账号无效，请修改后重新导入"),

    ERR_LECTURER_PHONE_NOT_EFFECT(4803, "导入的电话号码无效，请修改后重新导入"),

    ERR_LECTURER_EMAIL_NOT_EFFECT(4804, "导入的邮箱无效，请修改后重新导入"),

    ERR_LECTURER_NAME_NOT_EFFECT(4805, "导入的姓名有误，请修改后重新导入"),

    ERR_EXAMINATION_NOT_EXIST(4901, "讲师授课记录不存在"),

    ERR_NOT_WORK_DAY_NOT_EXIST(41001, "非工作日记录不存在"),

    ERR_NOT_CONFIG_NOT_EXIST(42001, "配置规则不存在"),

    ERR_PRIMARY_KEY_CANNOT_BE_EMPTY(4030, "知识库材料主键不能为空"),

    ERR_MATERIAL_NAME_CANNOT_BE_EMPTY(4031, "材料名称不能为空"),

    ERR_KNOWLEDGE_BASE_MATERIAL_FILE_PATH_CANNOT_BE_EMPTY(4032, "知识库材料文件路径不能为空"),

    ERR_ORDER_CANNOT_BE_EMPTY(4033, "显示顺序不能为空"),

    ERR_KNOWLEDGE_CLASSIFICATION_CANNOT_BE_EMPTY(4034, "知识库分类不能为空"),

    ERR_MATERIAL_PRIMARY_KEY_CANNOT_BE_EMPTY(4035, "材料主键不能为空"),

    ERR_DATA_PRIMARY_KEY_CANNOT_BE_EMPTY(4036, "数据主键不能为空"),

    ERR_PRIMARY_KEY_NOT_NULL(4037, "操作的数据主键字符串不能为空"),

    ERR_OPEN_OR_CLOSE_STATUS(4038, "启用/禁用状态参数错误"),

    ERR_TYPE_INVALID(4039, "分类类别无效"),

    ERR_TYPE_NAME_NOT_NULL(4040, "分类名称不能为空"),

    ERR_AUDIT_PRIMARY_KEY_NOT_NULL(4041, "审核数据主键不能为空"),

    ERR_AUDIT_STATUS_INVALID(4042, "审核状态无效"),

    ERR_AUDIT_REFUSE_REASON_NOT_NULL(4043, "审核拒绝时拒绝理由不能为空"),

    ERR_MATERIAL_PRIMARY_KEY_LIST_NOT_EMPTY(4044, "知识库材料主键集合不能为空"),

    ERR_OPEN_OR_CLOSE_STATUS_INVALID(4045, "禁用/启用状态值无效"),

    ERR_CONTENT_RULE_INVALID(4046, "参数：内容规则无效"),

    ERR_IDENTITY_CODE_EXISTS(4047, "已存在此编码"),

    ERR_CONFIG_WARNING_RULE_NOT_NULL(4048, "第{0}条配置规则的【预警规则参数{1}不能为空】"),

    ERR_CONFIG_RULE_PARAM_ONE_NOT_NULL(4049, "第{0}条配置规则的【规则参数{1}不能为空】"),

    ERR_OPEN_RULE_CANNOT_DEL(4050, "已经启用的规则不直接能删除"),

    ERR_OPTION_INCLUDE_AUDIT_RECORD(4051, "所选项包含审核过的授课记录"),

    ERR_USER_IS_DEL(4052, "以下讲师无法启用，其用户账户已被删除：{0}"),

    ERR_CATEGORY_IS_USED_CANNOT_DEL(4053, "分类被使用，无法删除"),

    ERR_PARAMS_LECTURER_TEACH_ID(4054, "内容填写不正确,授课信息主键不能为空"),

    ERR_PARAMS_LECTURER_TEACH_ID_DEL(4055, "内容填写不正确,删除数据主键字符串不能为空"),

    ERR_LECTURER_TEACH_DETAIL_REPEAT(4056, "相同 讲师+授课时间+授课项目+授课课件 的数据已存在"),

    ERR_IMPORT_LECTURER_TEACH_DETAIL_REPEAT(4057, "相同 讲师:{}+授课时间:{}的数据已存在"),
    ERR_LECTURER_AUTH_COURSE_EXISTS(4058, "讲师认证课程数据已经存在，请不要重复添加"),

    ;

    private final int errorCode;

    @Setter
    private final String message;

    LecturerErrorNoEnum(int errorCode, String message) {
        this.errorCode = errorCode;
        this.message = message;
    }

    @Override
    public int getErrorCode() {
        return errorCode;
    }

    @Override
    public String getMessage() {
        return message;
    }

}
