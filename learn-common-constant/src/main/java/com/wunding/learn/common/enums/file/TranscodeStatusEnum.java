package com.wunding.learn.common.enums.file;

import com.wunding.learn.common.annotation.EnumI18nProperty;
import com.wunding.learn.common.i18n.util.EnumI18n;

/**
 * <AUTHOR> href="mailto:<EMAIL>">yuxueyi</a>
 * @version V7.0.0
 * @since 2022/5/23  10:27
 */
public enum TranscodeStatusEnum implements EnumI18n {

    /**
     * 排队中
     */
    PENDING(4, "排队中"),

    /**
     * 转换过程中
     */
    TRANSFORMING(1, "转换中"),

    /***
     * 转换完毕
     */
    TRANSFORMED(2, "转换成功"),

    /**
     * 转换失败
     */
    TRANSFORM_FAILED(3, "转换失败");

    public int value;

    /**
     * sonar:Methods and field names should not be the same or differ only by capitalization
     */
    @EnumI18nProperty
    private String videoTranscodeStatusName;

    public String getVideoTranscodeStatusName() {
        return i18n(name(), this.videoTranscodeStatusName);
    }

    TranscodeStatusEnum(int value, String videoTranscodeStatusName) {
        this.value = value;
        this.videoTranscodeStatusName = videoTranscodeStatusName;
    }
}
