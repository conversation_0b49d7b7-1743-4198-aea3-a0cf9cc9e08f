com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_1.name=首页背景图
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_2.name=标题背景（登录页）
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_3.name=标题背景
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_4.name=首页背景剪影图
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_5.name=其他页背景图
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_6.name=取消按钮
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_7.name=我知道了（完成关卡）
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_8.name=关闭
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_9.name=我知道了（上传步数）
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_10.name=好的
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_11.name=刷新步数
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_12.name=去看故事
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_13.name=确定（首次登录提示语）
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_14.name=立即上传
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_15.name=今日里程
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_16.name=我的按钮
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_17.name=排行榜按钮
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_18.name=活动说明
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_19.name=上传昨日运动按钮
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_20.name=学习专区按钮
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_21.name=进度条小人
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_22.name=旗帜（未解锁)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_23.name=旗帜（已解锁)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_24.name=背景（学习专区)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_25.name=背景（“我的”页)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_26.name=其他页背景剪影图
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_27.name=地点连接图10（未完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_28.name=地点连接图10（已完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_29.name=地点连接图11（未完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_30.name=地点连接图11（已完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_31.name=地点连接图1（未完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_32.name=地点连接图1（已完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_33.name=地点连接图2（未完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_34.name=地点连接图2（已完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_35.name=地点连接图3（未完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_36.name=地点连接图3（已完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_37.name=地点连接图4（未完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_38.name=地点连接图4（已完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_39.name=地点连接图5（未完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_40.name=地点连接图5（已完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_41.name=地点连接图6（未完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_42.name=地点连接图6（已完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_43.name=地点连接图7（未完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_44.name=地点连接图7（已完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_45.name=地点连接图8（未完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_46.name=地点连接图8（已完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_47.name=地点连接图9（未完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_48.name=地点连接图9（已完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_49.name=关卡未解锁光环
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_50.name=关卡解锁光环
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_51.name=关卡解锁旗帜
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_52.name=弹窗头部背景
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_53.name=关卡完成背景
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_54.name=故事弹窗背景
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_55.name=提示弹窗背景)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_56.name=说明标题旗帜)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_57.name=我的页标题背景
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_58.name=我的页卡片背景
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_59.name=学习专区标题
com.wunding.learn.common.enums.other.TradeTypeEnum.PROJECT.value=学习项目
com.wunding.learn.common.enums.other.TradeTypeEnum.COURSE.value=课程
com.wunding.learn.common.enums.other.TradeTypeEnum.TRAIN.value=培训项目
com.wunding.learn.common.enums.other.TradeTypeEnum.TRAIN_APP_FILE.value=培训资料文件
com.wunding.learn.common.enums.other.TradeTypeEnum.TRAIN_APP_EXAMPLE.value=培训案例文件
com.wunding.learn.common.enums.exam.CategoryOrgTypeEnum.VIEWLIMITAREA.name=使用授权范围
com.wunding.learn.common.enums.exam.CategoryOrgTypeEnum.MANAGELIMITAREA.name=分类管理单位
com.wunding.learn.common.enums.exam.ExamQuestionTypeEnum.QUESTION_TYPE_RADIO.name=单选
com.wunding.learn.common.enums.exam.ExamQuestionTypeEnum.QUESTION_TYPE_MULTI_SELECT.name=多选
com.wunding.learn.common.enums.exam.ExamQuestionTypeEnum.QUESTION_TYPE_CLOZE.name=填空
com.wunding.learn.common.enums.exam.ExamQuestionTypeEnum.QUESTION_TYPE_JUDGEMENT.name=判断
com.wunding.learn.common.enums.exam.ExamQuestionTypeEnum.QUESTION_TYPE_QA.name=问答
com.wunding.learn.common.enums.other.LayoutStyleEnums.ROLL_NOTICE.name=滚动展示版式
com.wunding.learn.common.enums.other.LayoutStyleEnums.LIST_NOTICE.name=列表展示版式
com.wunding.learn.common.enums.other.LayoutStyleEnums.ROLL_CASE.name=横向滚动版式
com.wunding.learn.common.enums.other.LayoutStyleEnums.LIST_CASE.name=列表展示版式
com.wunding.learn.common.enums.other.LayoutStyleEnums.VIP_ROLL_CASE.name=横向滚动版式(含会员标记)
com.wunding.learn.common.enums.other.LayoutStyleEnums.VIP_LIST_CASE.name=列表展示版式(含会员标记)
com.wunding.learn.common.enums.other.LayoutStyleEnums.LIST_DATUM.name=列表展示版式
com.wunding.learn.common.enums.other.LayoutStyleEnums.VIP_LIST_DATUM.name=列表展示版式(含会员标记)
com.wunding.learn.common.enums.other.LayoutStyleEnums.DETAIL_LIST_TOPIC.name=详细列表版式
com.wunding.learn.common.enums.other.LayoutStyleEnums.LIST_TOPIC.name=简洁列表版式
com.wunding.learn.common.enums.other.LayoutStyleEnums.CROSS_LIST_COURSE.name=横向滚动版式
com.wunding.learn.common.enums.other.LayoutStyleEnums.LIST_COURSE.name=列表展示版式
com.wunding.learn.common.enums.other.LayoutStyleEnums.VIP_CROSS_LIST_COURSE.name=横向滚动版式(含会员标记)
com.wunding.learn.common.enums.other.LayoutStyleEnums.VIP_LIST_COURSE.name=列表展示版式(含会员标记)
com.wunding.learn.common.enums.other.LayoutStyleEnums.LIST_SCHEDULE.name=列表展示版式
com.wunding.learn.common.enums.other.LayoutStyleEnums.VIP_LIST_SCHEDULE.name=列表展示版式(含会员标记)
com.wunding.learn.common.enums.other.LayoutStyleEnums.ROLL_PHOTO.name=横向滚动版式
com.wunding.learn.common.enums.other.LayoutStyleEnums.BIG_PHOTO.name=大图版式
com.wunding.learn.common.enums.other.LayoutStyleEnums.LIST_CLASS.name=列表展示版式
com.wunding.learn.common.enums.other.LayoutStyleEnums.COVER_CLASS.name=纯封面版式
com.wunding.learn.common.enums.other.LayoutStyleEnums.VIP_LIST_CLASS.name=列表展示版式(含会员标记)
com.wunding.learn.common.enums.other.LayoutStyleEnums.VIP_COVER_CLASS.name=纯封面版式(含会员标记)
com.wunding.learn.common.enums.other.LayoutStyleEnums.BIG_LIVE.name=大图版式
com.wunding.learn.common.enums.other.LayoutStyleEnums.LIST_LIVE.name=列表展示版式
com.wunding.learn.common.enums.other.LayoutStyleEnums.ROLL_LECTURER.name=横向滚动版式
com.wunding.learn.common.enums.other.LayoutStyleEnums.LIST_LECTURER.name=列表展示版式
com.wunding.learn.common.enums.other.LayoutStyleEnums.LIST_SPECIAL.name=列表展示版式
com.wunding.learn.common.enums.other.LayoutStyleEnums.COVER_SPECIAL.name=纯封面版式
com.wunding.learn.common.enums.other.LayoutStyleEnums.VIP_LIST_SPECIAL.name=列表展示版式(含会员标记)
com.wunding.learn.common.enums.other.LayoutStyleEnums.VIP_COVER_SPECIAL.name=纯封面版式(含会员标记)
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.COURSE.name=课程
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.EXAM.name=考试
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.PROJECT.name=学习项目
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.CLASS.name=班级管理
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.FACE_CLASS.name=面授班级
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.CYCLE_PROJECT.name=周期项目
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.QUICK_PROJECT.name=快速培训
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.COURSE_PROJECT.name=课程任务
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.TRAIN.name=培训项目
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.SPECIAL.name=专题
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.SURVEY.name=调研
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.LIVE.name=直播
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.SIGN.name=签到
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.VOTE.name=投票
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.POST.name=话题
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.PROMOTED_GAME.name=闯关
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.READING.name=共读
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.APPRAISE.name=评价
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.RECRUITING.name=招募
com.wunding.learn.maxkb.api.constant.AiTypeConstant.AI_CW_DESC.name=摘要
com.wunding.learn.maxkb.api.constant.AiTypeConstant.AI_COURSE_ASK.name=课程问答
com.wunding.learn.maxkb.api.constant.AiTypeConstant.AI_CW_KEYWORD.name=关键词
com.wunding.learn.maxkb.api.constant.AiTypeConstant.AI_CW_QUESTION.name=题目
com.wunding.learn.maxkb.api.constant.AiTypeConstant.AI_CW_OUTLINE.name=大纲
com.wunding.learn.common.enums.file.TranscodeStatusEnum.TRANSFORMING.videoTranscodeStatusName=转换中
com.wunding.learn.common.enums.file.TranscodeStatusEnum.TRANSFORMED.videoTranscodeStatusName=转换成功
com.wunding.learn.common.enums.file.TranscodeStatusEnum.TRANSFORM_FAILED.videoTranscodeStatusName=转换失败
com.wunding.learn.common.enums.push.PushType.COURSE.text=课程
com.wunding.learn.common.enums.push.PushType.COURSE_Lib.text=共享库课程
com.wunding.learn.common.enums.push.PushType.NEWS.text=资讯
com.wunding.learn.common.enums.push.PushType.EXAM.text=考试
com.wunding.learn.common.enums.push.PushType.EXAM_COMPETITION.text=考试竞赛
com.wunding.learn.common.enums.push.PushType.EXERCISE.text=练习
com.wunding.learn.common.enums.push.PushType.LIVE.text=直播
com.wunding.learn.common.enums.push.PushType.EXAM_UNFINISHED.text=未考催办
com.wunding.learn.common.enums.push.PushType.SURVEY.text=调研
com.wunding.learn.common.enums.push.PushType.SPECIAL_TOPIC.text=专题
com.wunding.learn.common.enums.push.PushType.SPECIAL_TASK_COURSE.text=专题课程任务
com.wunding.learn.common.enums.push.PushType.SPECIAL_TASK_EXAM.text=专题考试任务
com.wunding.learn.common.enums.push.PushType.SPECIAL_TASK_EXERCISE.text=专题练习任务
com.wunding.learn.common.enums.push.PushType.SPECIAL_TASK_SURVEY.text=专题调研任务
com.wunding.learn.common.enums.push.PushType.SPECIAL_TASK_LIVE.text=专题直播任务
com.wunding.learn.common.enums.push.PushType.TRAIN_CLASS.text=培训班
com.wunding.learn.common.enums.push.PushType.APPLY.text=培训报名
com.wunding.learn.common.enums.push.PushType.SIGN_IN.text=活动签到
com.wunding.learn.common.enums.push.PushType.AWARD.text=奖品
com.wunding.learn.common.enums.push.PushType.PROMOTED_GAME.text=闯关游戏
com.wunding.learn.common.enums.push.PushType.PROJECT.text=学习项目
com.wunding.learn.common.enums.push.PushType.PROJECT_APPLY.text=学习项目报名审核
com.wunding.learn.common.enums.push.PushType.PROJECT_TASK_COURSE.text=学习项目课程任务
com.wunding.learn.common.enums.push.PushType.PROJECT_TASK_EXAM.text=学习项目考试任务
com.wunding.learn.common.enums.push.PushType.PROJECT_TASK_EXERCISE.text=学习项目练习任务
com.wunding.learn.common.enums.push.PushType.PROJECT_TASK_SURVEY.text=学习项目调研任务
com.wunding.learn.common.enums.push.PushType.PROJECT_TASK_LIVE.text=学习项目直播任务
com.wunding.learn.common.enums.push.PushType.PROJECT_TASK_TRAIN.text=学习项目培训班任务
com.wunding.learn.common.enums.push.PushType.PROJECT_TASK_FORM.text=学习项目辅导任务
com.wunding.learn.common.enums.push.PushType.PROJECT_TASK_PROJECT.text=学习项目学习项目任务
com.wunding.learn.common.enums.push.PushType.PROJECT_TASK_PRACTICAL_OPERATION.text=学习项目实操任务
com.wunding.learn.common.enums.push.PushType.QUICK_PROJECT.text=快速培训
com.wunding.learn.common.enums.push.PushType.QUICK_PROJECT_TASK_COURSE.text=快速培训课程任务
com.wunding.learn.common.enums.push.PushType.QUICK_PROJECT_TASK_EXAM.text=快速培训考试任务
com.wunding.learn.common.enums.push.PushType.TB_RECRUITING.text=招募活动
com.wunding.learn.common.enums.push.PushType.APPRAISE.text=评价
com.wunding.learn.common.enums.push.PushType.MEETING.text=答辩会议
com.wunding.learn.common.enums.push.PushType.READ.text=共读
com.wunding.learn.common.enums.push.PushType.EMIGRATED_TASK.text=闯关关卡任务
com.wunding.learn.common.enums.push.PushType.LECTURER_WARN.text=讲师预警
com.wunding.learn.common.enums.push.PushType.VOTE.text=投票
com.wunding.learn.common.enums.push.PushType.TRAIN.text=培训项目
com.wunding.learn.common.enums.push.PushType.TRAIN_ACTIVITY.text=培训项目活动
com.wunding.learn.common.enums.push.PushType.TRAIN_CLASSES.text=培训项目班级
com.wunding.learn.common.enums.push.PushType.LEARN_MAP.text=学习地图
com.wunding.learn.common.enums.push.PushType.FACE_PROJECT.text=面授项目
com.wunding.learn.common.enums.push.PushType.FACE_PROJECT_SCHEDULE.text=面授项目日程
com.wunding.learn.common.enums.push.PushType.FORM.text=辅导
com.wunding.learn.common.enums.push.PushType.Evaluation.text=评估
com.wunding.learn.common.enums.push.PushType.Work.text=作业
com.wunding.learn.common.enums.push.PushType.TRAIN_PROGRAM.text=培训项目
com.wunding.learn.common.enums.push.PushType.ASSESS_PROJECT.text=测评项目
com.wunding.learn.common.enums.ResourceTypeEnum.EXAM.name=考试
com.wunding.learn.common.enums.ResourceTypeEnum.EXERCISE.name=练习
com.wunding.learn.common.enums.ResourceTypeEnum.COURSE.name=课程
com.wunding.learn.common.enums.ResourceTypeEnum.SHARED_LIBRARY.name=共享库课程
com.wunding.learn.common.enums.ResourceTypeEnum.NEWS.name=资讯
com.wunding.learn.common.enums.ResourceTypeEnum.LIVE.name=直播
com.wunding.learn.common.enums.ResourceTypeEnum.READ.name=共读
com.wunding.learn.common.enums.ResourceTypeEnum.VOTE.name=投票
com.wunding.learn.common.enums.ResourceTypeEnum.QUIZ.name=闯关
com.wunding.learn.common.enums.ResourceTypeEnum.RECRUIT.name=招募
com.wunding.learn.common.enums.ResourceTypeEnum.APPRAISE.name=评价
com.wunding.learn.common.enums.ResourceTypeEnum.MEETING.name=答辩会议
com.wunding.learn.common.enums.ResourceTypeEnum.TOPIC_SECTION.name=话题版块
com.wunding.learn.common.enums.ResourceTypeEnum.SURVEY.name=调研
com.wunding.learn.common.enums.ResourceTypeEnum.SIGN_IN.name=签到
com.wunding.learn.common.enums.ResourceTypeEnum.TEST_CONTEST.name=考试竞赛
com.wunding.learn.common.enums.ResourceTypeEnum.SPECIAL.name=专题
com.wunding.learn.common.enums.ResourceTypeEnum.SPECIAL_TASK_EXAM.name=专题考试任务
com.wunding.learn.common.enums.ResourceTypeEnum.SPECIAL_TASK_EXERCISE.name=专题练习任务
com.wunding.learn.common.enums.ResourceTypeEnum.SPECIAL_TASK_COURSE.name=专题课程任务
com.wunding.learn.common.enums.ResourceTypeEnum.SPECIAL_TASK_LIVE.name=专题直播任务
com.wunding.learn.common.enums.ResourceTypeEnum.SPECIAL_TASK_SURVEY.name=专题调研任务
com.wunding.learn.common.enums.ResourceTypeEnum.COURSE_LEARNING_TASK.name=课程任务
com.wunding.learn.common.enums.ResourceTypeEnum.COURSE_LEARNING_TASK_EXAM.name=课程学习考试任务
com.wunding.learn.common.enums.ResourceTypeEnum.COURSE_LEARNING_TASK_COURSE.name=课程学习课程任务
com.wunding.learn.common.enums.ResourceTypeEnum.RAPID_TRAIN.name=快速培训
com.wunding.learn.common.enums.ResourceTypeEnum.RAPID_TRAIN_APP_SIGN_IN.name=快速培训签到应用
com.wunding.learn.common.enums.ResourceTypeEnum.RAPID_TRAIN_APP_EVALUATION.name=快速培训评估应用
com.wunding.learn.common.enums.ResourceTypeEnum.RAPID_TRAIN_TASK_COURSE.name=快速培训课程任务
com.wunding.learn.common.enums.ResourceTypeEnum.RAPID_TRAIN_TASK_EXAM.name=快速培训考试任务
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_COURSE.name=学习项目
com.wunding.learn.common.enums.ResourceTypeEnum.PROJECT.name=学习项目
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_COURSE_APP_NEWS.name=培训班公告应用
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_COURSE_APP_APPLY.name=培训班报名应用
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_COURSE_APP_SIGN_IN.name=培训班签到应用
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_COURSE_APP_TOPIC_SECTION.name=培训班话题版块应用
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_COURSE_APP_EVALUATION.name=培训班评估应用
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_COURSE_TASK_COURSE.name=培训班课程任务
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_COURSE_TASK_EXAM.name=培训班考试任务
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_COURSE_TASK_EXERCISE.name=培训班练习任务
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_COURSE_TASK_SURVEY.name=培训班调研任务
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_COURSE_TASK_LIVE.name=培训班直播任务
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_COURSE_TASK_PROJECT.name=培训班项目任务
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_COURSE_TASK_TUTOR.name=培训班辅导任务
com.wunding.learn.common.enums.ResourceTypeEnum.PERIODIC_PROJECT.name=周期项目
com.wunding.learn.common.enums.ResourceTypeEnum.PERIODIC_PROJECT_APP_NEWS.name=周期项目公告应用
com.wunding.learn.common.enums.ResourceTypeEnum.PERIODIC_PROJECT_APP_TOPIC_SECTION.name=周期项目话题版块应用
com.wunding.learn.common.enums.ResourceTypeEnum.PERIODIC_PROJECT_APP_EVALUATION.name=周期项目评估应用
com.wunding.learn.common.enums.ResourceTypeEnum.PERIODIC_PROJECT_TASK_COURSE.name=周期项目课程任务
com.wunding.learn.common.enums.ResourceTypeEnum.PERIODIC_PROJECT_TASK_EXAM.name=周期项目考试任务
com.wunding.learn.common.enums.ResourceTypeEnum.PERIODIC_PROJECT_TASK_EXERCISE.name=周期项目练习任务
com.wunding.learn.common.enums.ResourceTypeEnum.PERIODIC_PROJECT_TASK_SURVEY.name=周期项目调研任务
com.wunding.learn.common.enums.ResourceTypeEnum.PERIODIC_PROJECT_TASK_PROJECT.name=周期项目项目任务
com.wunding.learn.common.enums.ResourceTypeEnum.PERIODIC_PROJECT_TASK_TUTOR.name=周期项目辅导任务
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_PROGRAM.name=培训项目
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_PROGRAM_ACTIVITY_COURSE.name=培训项目课程活动
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_PROGRAM_ACTIVITY_EXAM.name=培训项目考试活动
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_PROGRAM_ACTIVITY_CLASS.name=培训项目班级活动
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_PROGRAM_ACTIVITY_LIVE.name=培训项目直播活动
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_PROGRAM_ACTIVITY_SURVEY.name=培训项目调研活动
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_PROGRAM_ACTIVITY_RECRUIT.name=培训项目招募活动
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_PROGRAM_ACTIVITY_APPRAISE.name=培训项目评价活动
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_PROGRAM_ACTIVITY_FORM.name=培训项目表单活动
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_PROGRAM_ACTIVITY_SIGN_IN.name=培训项目签到活动
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_PROGRAM_ACTIVITY_VOTE.name=培训项目投票活动
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_PROGRAM_ACTIVITY_PRACTICAL_OPERATION.name=培训项目实操活动
com.wunding.learn.common.enums.ResourceTypeEnum.LEARN_MAP_EXEC.name=学习地图
com.wunding.learn.common.enums.ResourceTypeEnum.FACE_PROJECT.name=面授项目
com.wunding.learn.common.enums.ResourceTypeEnum.FACE_PROJECT_SCHEDULE.name=面授项目日程
com.wunding.learn.common.enums.ResourceTypeEnum.PRACTICAL_OPERATION.name=实操
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.ACCEPT_FEEDBACK.name=反馈意见受理
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.AUDIT_COURSEWARE.name=上传课件并审核通过
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.BEST_REPLY_TOPIC.name=最佳话题回帖
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COLLECT_COURSE.name=收藏课程
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COMMENT_COURSE.name=评论课程
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COMMENT_COURSEWARE.name=评论课件
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COMMENT_NEWS.name=评论资讯
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COMMIT_EVALUATE.name=提交评估
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COMMIT_EXAM.name=提交考试
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COMMIT_EXERCISE.name=完成练习
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COMMIT_FEEDBACK.name=提交反馈意见
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COMMIT_HOMEWORK.name=提交作业
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COMMIT_SIGN.name=完成签到
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COURSE_STAR.name=课程评星
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COURSE_VOTE.name=点赞课程
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COURSEWARE_STAR.name=课件被评星
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COURSEWARE_VIEW.name=课件被浏览数
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COURSEWARE_VOTE.name=课件被点赞数
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.EVERYDAY_LEARN_COURSE.name=用户每日学完一个课程算1次打卡
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.EVERYDAY_LEARN_COURSEWARE.name=用户每日学完一个课件算1次打卡
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.EVERYDAY_LOGIN.name=用户每日登录平台即打卡1次，不连续则清零重新计算
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.EXAMPLE_ADD.name=每个学员提交一份案例时奖励
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.EXAMPLE_AUDIT.name=评委进行评分评分时，自己获得的奖励
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.EXAMPLE_AUDIT_OVER.name=案例评审时，评审结束后获得的分数，达到一定程度时的奖励
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.EXAMPLE_COMPANY.name=案例被调整为公司级时奖励，降级不再取消
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.EXAMPLE_EXTRA.name=案例被管理员标识优质，取消优质不再取消
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.EXAMPLE_GROUP.name=案例被调整为集团级时奖励，降级不再取消
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.EXAM_PROCESS.name=根据考试成绩区间进行激励
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.FINISH_COURSE.name=学完课程
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.FINISH_COURSEWARE.name=学完课件（固定）
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.FINISH_COURSEWARE_BYTIME.name=学完课件（按课件时长）
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.FINISH_PROJECT.name=完成项目中所有学习任务获得激励
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.FINISH_PROJECT_APPLY.name=完成项目报名
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.FINISH_SURVEY.name=提交调研
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.FIRST_LOGIN.name=用户首次访问系统时获得，仅1次
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.FOCUS_TOPIC.name=关注话题
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.INTEGRAL_CLEARING.name=积分清零(仅为积分统计使用，实际积分配置规则不使用)
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.LEARN_COURSE.name=任意入口点击打开课程时计算
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.LEARN_COURSEWARE.name=任意入口点击打开课件时计算
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.LECTURER_JOIN_PROJECT_TEACH.name=仅在学习项目结束后7天获得
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.LECTURER_TEACH_COURSEWARE.name=仅在学习项目结束后7天获得
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.LIVE_CONTRIBUTE.name=通过发起直播贡献知识获得贡献学分
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.LIVE_INTERACT.name=在直播间与主播进行互动给分
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.LIVE_INTERACT_COUNT.name=发起的直播达到一定观看互动量给分
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.LIVE_QUESTION_AND_ANSWER.name=主播可在直播间内进行金币悬赏提问，学员在直播间内回答问题可获得金币
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.MAKE_LIVE.name=发起直播给分
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.MAKE_TOPIC.name=发表话题
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.PARTICIPATION_DOUBLE.name=参与获取（双人）
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.PARTICIPATION_GROUP.name=参与获取（组队）
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.PARTICIPATION_MULTI.name=参与获取（多人）
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.PARTICIPATION_SINGLE.name=参与获取（单人）
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.PERSONAL_CHAMPION_MULTI.name=个人获冠军（多人）
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.PERSONAL_SECOND_MULTI.name=个人获亚军（多人）
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.PERSONAL_THIRD_MULTI.name=个人获季军（多人）
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.PERSONAL_WIN_DOUBLE.name=个人获胜利（双人）
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.PERSONAL_WIN_SINGLE.name=个人获胜利（单人）
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.PROJECT_TASK_PROCESS.name=仅在学习项目结束后7天，根据任务完成率区间激励
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.PROMOTED_GAME_FINISH_CHECKPOINT.name=完成闯关关卡
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.READING_BOOK_NUM.name=共读阅读图书数量
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.READING_CLOCK_TIMES.name=共读打卡次数(阅读一本共读图书10分钟以上，记作一次有效打卡)
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.READING_COMMENT_EXPNUM.name=共读评论心得数量
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.READING_EXPERIENCE_TIMES.name=心得提交次数
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.REPLY_COURSE_COMMENT.name=回复课程评论
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.REPLY_COURSEWARE_COMMENT.name=回复课件评论
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.REPLY_TOPIC.name=对话题进行回帖
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.SHARE_COURSE.name=分享课程
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.SHARE_COURSEWARE.name=分享课件
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.STAR_COURSEWARE.name=课件评星
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.TEAM_WIN_GROUP.name=团队获胜（组队）
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.TOPIC_COMMENT_COUNT.name=根据话题回帖数量区间激励版主
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.VIEW_COURSE_PROCESS.name=课程下所有的课件时长进度百分比
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.VIEW_COURSEWARE_PROCESS.name=课件浏览时长进度(百分比)
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.VIEW_LIVE.name=点击观看直播
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.VIEW_LIVE_TOTAL_TIME.name=累计观看直播时长达到指定时长
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.VIEW_NEWS.name=点击查看资讯
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.VOTE_COURSE_COMMENT.name=点赞课程评论
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.VOTE_LECTURER.name=学员点赞讲师
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.VOTE_NEWS_COMMENT.name=点赞资讯评论
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COURSE_POSTER_SHARE.name=分享课程海报
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.PROJECT_POSTER_SHARE.name=分享项目海报
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.LIVE_POSTER_SHARE.name=分享直播海报
com.wunding.learn.common.enums.other.DifficultyTypeEnum.EASY.name=低
com.wunding.learn.common.enums.other.DifficultyTypeEnum.MIDDLE.name=中
com.wunding.learn.common.enums.other.DifficultyTypeEnum.HIGN.name=高
com.wunding.learn.common.enums.market.HeadContentRuleEnum.GUESSYOULIKE.orderRuleDesc=随机排序
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RECOMMENDCOURSE.orderRuleDesc=精选好课时间倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.POPULARCOURSE1.orderRuleDesc=评论数+评星计数 总数倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.POPULARCOURSE2.orderRuleDesc=评论数+评星计数 总数倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.POSTCOURSE.orderRuleDesc=按课程发布时间倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LEARNINGCOURSE.orderRuleDesc=按课程开始学习时间倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.NEWS.orderRuleDesc=发布时间倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.STUDYPROJECT.orderRuleDesc=日期(固定周期项目的开始日期 或者(周期项目的 加入日期， 未加入时日期为发布日期)
com.wunding.learn.common.enums.market.HeadContentRuleEnum.STUDYPROJECTSPECIFYCATEGORY.orderRuleDesc=日期(固定周期项目的开始日期 或者(周期项目的 加入日期， 未加入时日期为发布日期)
com.wunding.learn.common.enums.market.HeadContentRuleEnum.SPECIAL.orderRuleDesc=遵循学习项目的排序
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LECTURERELEGANTDEMEANOUR1.orderRuleDesc=授课数量倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LECTURERELEGANTDEMEANOUR2.orderRuleDesc=授课综合分倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LECTURERELEGANTDEMEANOUR3.orderRuleDesc=按最近授课时间倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LIVINGBROADCAST.orderRuleDesc=按直播开始时间倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.CURRENTLIVE.orderRuleDesc=按直播开始时间倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LIVEPLAYBACK.orderRuleDesc=按直播开始时间倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LATESTTOPICS.orderRuleDesc=发表时间倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTTOPICS1.orderRuleDesc=按回帖数量倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTTOPICS2.orderRuleDesc=按回帖数量倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.ELITEPOSTS1.orderRuleDesc=按设置置顶时间倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASEOFTHEMONTH1.orderRuleDesc=按点赞数倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASEOFTHEMONTH2.orderRuleDesc=按点赞数倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASEOFTHEMONTH3.orderRuleDesc=按点赞数倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASE1.orderRuleDesc=按点赞数倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASE2.orderRuleDesc=按点赞数倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASE3.orderRuleDesc=按点赞数倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RECOMMENDCASE1.orderRuleDesc=按推荐时间倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RECOMMENDCASE2.orderRuleDesc=按推荐时间倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RECOMMENDCASE3.orderRuleDesc=按推荐时间倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.TRAINPROJECT.orderRuleDesc=按照发布时间，最新发布在最前面
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RAINPROJECTSPECIFYCATEGORY.orderRuleDesc=按照发布时间，最新发布在最前面
com.wunding.learn.common.enums.market.HeadContentRuleEnum.ORG_INFO.orderRuleDesc=
com.wunding.learn.common.library.record.enums.HandleTypeEnum.DEFAULT.name=无
com.wunding.learn.common.library.record.enums.HandleTypeEnum.TP_ADD_RECORD.name=入库了
com.wunding.learn.common.library.record.enums.HandleTypeEnum.TP_DELETE_RECORD.name=删除了
com.wunding.learn.common.library.record.enums.HandleTypeEnum.TP_UPDATE_RECORD.name=编辑了
com.wunding.learn.common.library.record.enums.HandleTypeEnum.TP_ENABLE_RECORD.name=启用了
com.wunding.learn.common.library.record.enums.HandleTypeEnum.TP_DISABLE_RECORD.name=禁用了
com.wunding.learn.common.library.record.enums.HandleTypeEnum.TP_IMPORT_QUESTION.name=导入了
com.wunding.learn.common.library.record.enums.HandleTypeEnum.TP_DOWNLOAD_RECORD.name=下载了
com.wunding.learn.common.library.record.enums.HandleTypeEnum.TP_EDIT_RECORD.name=修改了
com.wunding.learn.common.library.record.enums.HandleTypeEnum.TP_USE_RECORD.name=使用
com.wunding.learn.common.library.record.enums.LibraryTypeEnum.DEFAULT.name=无
com.wunding.learn.common.library.record.enums.LibraryTypeEnum.LIB_EXAM.name=试卷库
com.wunding.learn.common.library.record.enums.LibraryTypeEnum.LIB_QUESTION.name=考题库
com.wunding.learn.common.library.record.enums.LibraryTypeEnum.LIB_EXERCISE.name=练习库
com.wunding.learn.common.library.record.enums.LibraryTypeEnum.LIB_SURVEY.name=调研库
com.wunding.learn.common.library.record.enums.LibraryTypeEnum.LIB_COURSE.name=课件库
com.wunding.learn.common.library.record.enums.LibraryTypeEnum.LIB_EVALUATION.name=评估库
com.wunding.learn.common.library.record.enums.LibraryTypeEnum.LIB_MATERIAL.name=知识库
com.wunding.learn.common.enums.other.ModuleEnum.TRAIN.name=培训项目
com.wunding.learn.common.enums.other.ModuleEnum.CERTIFICATION.name=认证
com.wunding.learn.common.enums.viewlimit.ViewLimitTypeEnum.TIME.name=时间身份
com.wunding.learn.common.enums.viewlimit.ViewLimitTypeEnum.BUSINESS.name=业务条线
com.wunding.learn.common.enums.viewlimit.ViewLimitTypeEnum.MANAGE_LEVEL.name=管理者层级
com.wunding.learn.common.enums.viewlimit.ViewLimitTypeEnum.POST.name=岗位族、岗位、岗位层级
com.wunding.learn.common.enums.viewlimit.ViewLimitTypeEnum.LECTURER.name=讲师层级
com.wunding.learn.common.enums.viewlimit.ViewLimitTypeEnum.JOB_LEVEL.name=职级
com.wunding.learn.common.enums.viewlimit.ViewLimitTypeEnum.MemberLimit.name=学院会员
com.wunding.learn.common.enums.viewlimit.ViewLimitTypeEnum.OrgLimit.name=部门
com.wunding.learn.common.enums.viewlimit.ViewLimitTypeEnum.UserLimit.name=人员
com.wunding.learn.common.enums.viewlimit.ViewLimitTypeEnum.WorkLimit.name=工种
com.wunding.learn.common.enums.MapBehaviorEnum.USER.name=培训活动(学员身份参与)
com.wunding.learn.common.enums.MapBehaviorEnum.TEACH.name=培训活动(讲师身份参与)
com.wunding.learn.common.enums.MapBehaviorEnum.SUPERVISOR.name=辅导活动(导师身份参与)
com.wunding.learn.common.enums.MapBehaviorEnum.PRACTICAL.name=实操活动(实操身份参与)
com.wunding.learn.common.enums.MapBehaviorEnum.EVALUATION.name=培训活动(评价身份参与)
com.wunding.learn.common.enums.push.NoticeTypeEnum.GENERIC_TEMPLATE.message=通用模板
com.wunding.learn.common.enums.push.NoticeTypeEnum.AUDIT_TEMPLATE.message=审核专用模板
com.wunding.learn.common.enums.push.NoticeTypeEnum.REMINDER_TEMPLATE.message=催办专用模板
com.wunding.learn.common.enums.push.NoticeTypeEnum.APPROVED_TEMPLATE.message=审核通过模板
com.wunding.learn.common.enums.push.NoticeTypeEnum.APPROVED_FAILED_TEMPLATE.message=审核不通过,允许修改模板
com.wunding.learn.common.enums.push.NoticeTypeEnum.APPROVED_FAILED_NOT_MODIFY_TEMPLATE.message=审核不通过,不允许修改模板
com.wunding.learn.common.enums.push.NoticeTypeEnum.NOTICE_OF_CLASS_SCHEDULE_START.message=开课通知模板
com.wunding.learn.common.enums.push.NoticeTypeEnum.IDENTIFYING_CODE_TEMPLATE.message=验证码模板
com.wunding.learn.common.enums.push.NoticeTypeEnum.NOTICE_OF_CLASS_START.message=开班通知模板
com.wunding.learn.common.enums.other.OperationEnum.CREATE.name=创建
com.wunding.learn.common.enums.other.OperationEnum.PUBLISH.name=发布
com.wunding.learn.common.enums.other.OperationEnum.PUBLISH_CANCEL.name=取消发布
com.wunding.learn.common.enums.other.OperationEnum.DELETE.name=删除
com.wunding.learn.common.enums.other.OperationEnum.UPDATE.name=更新
com.wunding.learn.reading.service.enums.ReadingTaskDescription.READING_TASK_TYPE_SIGN.description=每日只能打卡一次，每次都可以获得相应的运营分，超过要求次数后不再获得。
com.wunding.learn.reading.service.enums.ReadingTaskDescription.READING_TASK_TYPE_EXPERIENCE.description=每本图书的心得算一次，第一次对图书提交心得可获得运营分，超过次数后不再获得。
com.wunding.learn.reading.service.enums.ReadingTaskDescription.READING_TASK_TYPE_READING_N.description=每读完一本书即可获得运营分，超过规定数量后不再获取。
com.wunding.learn.reading.service.enums.ReadingTaskDescription.READING_TASK_TYPE_COMMENT_N.description=对心得每进行一次评论即可获得运营分，超过规定数量后不再获得。
com.wunding.learn.reading.service.enums.ReadingTaskDescription.READING_TASK_TYPE_READING.description=读完指定图书后获得运营分。
com.wunding.learn.reading.service.enums.ReadingTaskDescription.READING_TASK_TYPE_COMMENT.description=第一次针对某本书发表心得体会时获取运营分。
com.wunding.learn.common.enums.push.PushNoticeEventEnum.RECRUITING_46.name=协办待审通知
com.wunding.learn.common.enums.push.PushNoticeEventEnum.RECRUITING_47.name=审核结果通知
com.wunding.learn.common.enums.push.PushNoticeEventEnum.APPRAISE_49.name=评价结果通知
com.wunding.learn.common.enums.push.PushNoticeEventEnum.MEETING_58.name=答辩结果通知
com.wunding.learn.common.enums.evaluation.SourceTypeEnum.MANUAL_UPLOAD.message=手动上传
com.wunding.learn.common.enums.evaluation.SourceTypeEnum.EVAL_LIB.message=评估库
com.wunding.learn.common.enums.evaluation.SourceTypeEnum.DOCUMENT.message=资料库
com.wunding.learn.common.enums.excitation.ExcitationTypeEnum.INTEGRAL.name=积分
com.wunding.learn.common.enums.excitation.ExcitationTypeEnum.CREDIT.name=学分
com.wunding.learn.common.enums.excitation.ExcitationTypeEnum.LEARN_TIME.name=学时
com.wunding.learn.common.enums.excitation.ExcitationTypeEnum.GOLD_COIN.name=金币
com.wunding.learn.common.enums.project.ProjectAppType.NOTICE.appName=公告
com.wunding.learn.common.enums.project.ProjectAppType.MENTOR.appName=导师
com.wunding.learn.common.enums.project.ProjectAppType.LECTURER.appName=讲师
com.wunding.learn.common.enums.project.ProjectAppType.APPLY.appName=报名
com.wunding.learn.common.enums.project.ProjectAppType.SIGN.appName=签到
com.wunding.learn.common.enums.project.ProjectAppType.TOPIC.appName=话题
com.wunding.learn.common.enums.project.ProjectAppType.TOPIC_MANAGE.appName=话题版块
com.wunding.learn.common.enums.project.ProjectAppType.ASSESS.appName=评估
com.wunding.learn.common.enums.project.ProjectAppType.TEAM.appName=团队
com.wunding.learn.common.enums.project.ProjectAppType.HOMEWORK.appName=作业
com.wunding.learn.common.enums.project.ProjectAppType.COMMENT.appName=评论
com.wunding.learn.common.enums.project.ProjectAppType.COMPLETION.appName=结业
com.wunding.learn.common.enums.project.ProjectAppType.COST.appName=费用
com.wunding.learn.common.enums.project.ProjectAppType.DATA.appName=资料
com.wunding.learn.common.enums.project.ProjectAppType.STATISTICS.appName=统计
com.wunding.learn.common.enums.project.ProjectAppType.EXAM.appName=考试
com.wunding.learn.common.enums.project.ProjectAppType.VACATE.appName=请假
com.wunding.learn.common.enums.project.ProjectAppType.INVOICE.appName=开票
com.wunding.learn.common.enums.project.ProjectAppType.FILE.appName=相关资料
com.wunding.learn.common.enums.project.ProjectAppType.SCHEDULE.appName=日程安排
com.wunding.learn.common.enums.other.PassStatusEnum.NO.name=否
com.wunding.learn.common.enums.other.PassStatusEnum.YES.name=是
com.wunding.learn.common.enums.exam.CompetitionSessionUserTypeEnum.DEFAULT_VALUE.name=默认值
com.wunding.learn.common.enums.exam.CompetitionSessionUserTypeEnum.INITIATIVE.name=主动发起
com.wunding.learn.common.enums.exam.CompetitionSessionUserTypeEnum.BE_INVITED.name=被邀请
com.wunding.learn.common.enums.exam.CompetitionSessionUserTypeEnum.RANDOM_MATCH.name=随机匹配
com.wunding.learn.common.enums.exam.CompetitionSessionUserTypeEnum.INVITING.name=邀请中
com.wunding.learn.common.enums.exam.CompetitionSessionUserTypeEnum.NO_RESPONSE.name=无响应
com.wunding.learn.common.enums.exam.CompetitionSessionUserTypeEnum.REFUSE.name=已拒绝
com.wunding.learn.common.enums.other.LayoutStyleEnum.COURSE_1_4.desc=横排
com.wunding.learn.common.enums.other.LayoutStyleEnum.COURSE_2_4.desc=竖排
com.wunding.learn.common.enums.other.LayoutStyleEnum.COURSE_3_8.desc=滑动有标题
com.wunding.learn.common.enums.other.LayoutStyleEnum.NEWS_1_4.desc=竖排无简介
com.wunding.learn.common.enums.other.LayoutStyleEnum.NEWS_2_3.desc=竖排有简介
com.wunding.learn.common.enums.other.LayoutStyleEnum.NEWS_3_8.desc=滑动有标题
com.wunding.learn.common.enums.other.LayoutStyleEnum.PROJECT_1_1.desc=单个无标题
com.wunding.learn.common.enums.other.LayoutStyleEnum.PROJECT_2_3.desc=竖排
com.wunding.learn.common.enums.other.LayoutStyleEnum.PROJECT_3_8.desc=滑动有标题
com.wunding.learn.common.enums.other.LayoutStyleEnum.PROJECT_4_8.desc=滑动无标题
com.wunding.learn.common.enums.other.LayoutStyleEnum.SPECIAL_1_1.desc=单个有标题
com.wunding.learn.common.enums.other.LayoutStyleEnum.SPECIAL_2_3.desc=竖排
com.wunding.learn.common.enums.other.LayoutStyleEnum.SPECIAL_3_8.desc=滑动有标题
com.wunding.learn.common.enums.other.LayoutStyleEnum.SPECIAL_4_8.desc=滑动无标题
com.wunding.learn.common.enums.other.LayoutStyleEnum.SPECIAL_5_1.desc=单个无标题
com.wunding.learn.common.enums.other.LayoutStyleEnum.LECTURER_1_8.desc=滑动
com.wunding.learn.common.enums.other.LayoutStyleEnum.LECTURER_2_4.desc=横排大
com.wunding.learn.common.enums.other.LayoutStyleEnum.LECTURER_3_4.desc=横排小
com.wunding.learn.common.enums.other.LayoutStyleEnum.LIVE_1_1.desc=单个有标题
com.wunding.learn.common.enums.other.LayoutStyleEnum.LIVE_2_8.desc=滑动有标题1
com.wunding.learn.common.enums.other.LayoutStyleEnum.LIVE_3_4.desc=横排
com.wunding.learn.common.enums.other.LayoutStyleEnum.LIVE_4_8.desc=滑动有标题2
com.wunding.learn.common.enums.other.LayoutStyleEnum.TOPICS_1_5.desc=竖排无简介
com.wunding.learn.common.enums.other.LayoutStyleEnum.TOPICS_2_3.desc=竖排有简介
com.wunding.learn.common.enums.other.LayoutStyleEnum.TOPICS_3_8.desc=横排有简介
com.wunding.learn.common.enums.other.LayoutStyleEnum.CASE_1_4.desc=横排
com.wunding.learn.common.enums.other.LayoutStyleEnum.CASE_2_4.desc=竖排
com.wunding.learn.common.enums.other.LayoutStyleEnum.CASE_3_8.desc=滑动有标题
com.wunding.learn.common.enums.other.LayoutStyleEnum.ORG_1_4.desc=横版【仅h5】
com.wunding.learn.common.enums.other.LayoutStyleEnum.CLOCK_IN_1_0.desc=默认
com.wunding.learn.common.enums.other.LayoutStyleEnum.MY_TASK_1_0.desc=默认
com.wunding.learn.apply.service.enums.ApplyStatusEnum.DRAFT.name=草稿
com.wunding.learn.apply.service.enums.ApplyStatusEnum.APPLYING.name=申请中
com.wunding.learn.apply.service.enums.ApplyStatusEnum.SUCCESS.name=申请成功
com.wunding.learn.apply.service.enums.ApplyStatusEnum.FAIL.name=申请失败
com.wunding.learn.apply.service.enums.ApplyStatusEnum.CANCEL.name=申请取消
com.wunding.learn.common.enums.other.ContentRuleEnum.HOT_COURSE.showRuleDesc=热门课程
com.wunding.learn.common.enums.other.ContentRuleEnum.LEARNING_COURSE.showRuleDesc=我正在学
com.wunding.learn.common.enums.other.ContentRuleEnum.SCORE_LECTURER.showRuleDesc=所有内部讲师
com.wunding.learn.common.enums.other.ContentRuleEnum.TIME_LECTURER.showRuleDesc=所有内部讲师
com.wunding.learn.common.enums.other.ContentRuleEnum.NEW_TOPIC_NOT_TOP.showRuleDesc=版块下发范围
com.wunding.learn.common.enums.other.ContentRuleEnum.HOT_TOPIC_COMMENT.showRuleDesc=版块下发范围
com.wunding.learn.common.enums.other.ContentRuleEnum.HOT_TOPIC_VIEW.showRuleDesc=版块下发范围
com.wunding.learn.common.enums.other.ContentRuleEnum.NEW_TOPIC_TOP.showRuleDesc=版块下发范围设置为置顶的帖子
com.wunding.learn.common.enums.other.ContentRuleEnum.HOT_CASE.showRuleDesc=
com.wunding.learn.common.enums.other.ContentRuleEnum.NEW_CASE.showRuleDesc=
com.wunding.learn.common.enums.other.ContentRuleEnum.LIVING_LIVE.showRuleDesc=已经开始 未结束的直播
com.wunding.learn.common.enums.other.ContentRuleEnum.CURRENT_LIVE.showRuleDesc=未结束的直播
com.wunding.learn.common.enums.other.ContentRuleEnum.ROLLBACK_LIVE.showRuleDesc=已经结束的直播，且有回看文件的直播
com.wunding.learn.push.api.enums.PushChannelEnum.APP.desc=APP
com.wunding.learn.push.api.enums.PushChannelEnum.EMAIL.desc=邮箱
com.wunding.learn.push.api.enums.PushChannelEnum.WECOM.desc=企业微信
com.wunding.learn.push.api.enums.PushChannelEnum.WECHAT_OFFICIAL_ACCOUNTS.desc=微信公众号
com.wunding.learn.push.api.enums.PushChannelEnum.WECHAT_MINI_PROGRAMS.desc=微信小程序
com.wunding.learn.push.api.enums.PushChannelEnum.DING_TALK.desc=钉钉
com.wunding.learn.push.api.enums.PushChannelEnum.FEI_SHU.desc=飞书
com.wunding.learn.push.api.enums.PushChannelEnum.SMS.desc=短信
com.wunding.learn.train.service.enums.ActivityResourceTypeEnum.COURSE.name=课程
com.wunding.learn.train.service.enums.ActivityResourceTypeEnum.EXAM.name=考试
com.wunding.learn.train.service.enums.ActivityResourceTypeEnum.PROJECT.name=培训班
com.wunding.learn.train.service.enums.ActivityResourceTypeEnum.LIVE.name=直播
com.wunding.learn.train.service.enums.ActivityResourceTypeEnum.SURVEY.name=调研
com.wunding.learn.train.service.enums.ActivityResourceTypeEnum.RECRUITING.name=招募
com.wunding.learn.train.service.enums.ActivityResourceTypeEnum.APPRAISE.name=评价
com.wunding.learn.train.service.enums.ActivityResourceTypeEnum.FORM.name=表单
com.wunding.learn.train.service.enums.ActivityResourceTypeEnum.SIGN.name=签到
com.wunding.learn.train.service.enums.ActivityResourceTypeEnum.VOTE.name=投票
com.wunding.learn.train.service.enums.ActivityResourceTypeEnum.PRACTICE.name=实操
com.wunding.learn.train.service.enums.ActivityResourceTypeEnum.SPECIAL.name=专题
com.wunding.learn.common.enums.exam.CompetitionGroupNameEnum.A_GROUP.name=A组
com.wunding.learn.common.enums.exam.CompetitionGroupNameEnum.B_GROUP.name=B组
com.wunding.learn.common.enums.user.OrgTypeEnum.VIEWLIMITAREA.name=使用授权范围
com.wunding.learn.common.enums.user.OrgTypeEnum.MANAGELIMITAREA.name=分类管理单位
com.wunding.learn.common.enums.language.LanguageModuleEnum.ORGANIZATION.moduleName=组织
com.wunding.learn.common.enums.language.LanguageModuleEnum.TIME.moduleName=时间
com.wunding.learn.common.enums.language.LanguageModuleEnum.BUSINESS.moduleName=条线
com.wunding.learn.common.enums.language.LanguageModuleEnum.MANAGE_LEVEL.moduleName=管理者层级
com.wunding.learn.common.enums.language.LanguageModuleEnum.POST.moduleName=岗位族
com.wunding.learn.common.enums.language.LanguageModuleEnum.JOB_LEVEL.moduleName=职级
com.wunding.learn.common.enums.language.LanguageModuleEnum.LECTURER.moduleName=讲师
com.wunding.learn.common.enums.language.LanguageModuleEnum.SUPERIOR.moduleName=岗位
com.wunding.learn.common.enums.language.LanguageModuleEnum.CourseCate.moduleName=课程分类
com.wunding.learn.common.enums.language.LanguageModuleEnum.CourseTagCate.moduleName=课程标签
com.wunding.learn.common.enums.language.LanguageModuleEnum.InfoCate.moduleName=资讯分类
com.wunding.learn.common.enums.language.LanguageModuleEnum.AwardCate.moduleName=奖品分类
com.wunding.learn.common.enums.language.LanguageModuleEnum.ThematicTag.moduleName=专题标签
com.wunding.learn.common.enums.language.LanguageModuleEnum.ThematicClass.moduleName=专题分类
com.wunding.learn.common.enums.language.LanguageModuleEnum.MentorCate.moduleName=导师分类
com.wunding.learn.common.enums.language.LanguageModuleEnum.FormTaskCate.moduleName=辅导任务分类
com.wunding.learn.common.enums.language.LanguageModuleEnum.home.moduleName=中部菜单
com.wunding.learn.common.enums.language.LanguageModuleEnum.myLearn.moduleName=我的学习
com.wunding.learn.common.enums.language.LanguageModuleEnum.botMenu.moduleName=底部菜单
com.wunding.learn.common.enums.language.LanguageModuleEnum.app.moduleName=我的应用
com.wunding.learn.common.enums.language.LanguageModuleEnum.systemConfig.moduleName=数据字典
com.wunding.learn.common.enums.language.LanguageModuleEnum.titleManager.moduleName=头衔
com.wunding.learn.common.enums.language.LanguageModuleEnum.sysSpace.moduleName=积分规则
com.wunding.learn.common.enums.language.LanguageModuleEnum.firstInfo.moduleName=头条通知
com.wunding.learn.common.enums.language.LanguageModuleEnum.award.moduleName=积分游戏
com.wunding.learn.common.enums.language.LanguageModuleEnum.systemCertification.moduleName=系统认证
com.wunding.learn.common.enums.language.LanguageModuleEnum.sysClientVersion.moduleName=版本管理
com.wunding.learn.common.enums.language.LanguageModuleEnum.excitationEvent.moduleName=目标管理事件
com.wunding.learn.common.enums.language.LanguageModuleEnum.KnowledgeBaseType.moduleName=知识库分类
com.wunding.learn.common.enums.language.LanguageModuleEnum.KnowledgeTagCate.moduleName=知识库标签
com.wunding.learn.common.enums.language.LanguageModuleEnum.EvaluateLibraryCate.moduleName=评估库分类
com.wunding.learn.common.enums.language.LanguageModuleEnum.ExamLibraryCate.moduleName=考题库分类
com.wunding.learn.common.enums.language.LanguageModuleEnum.ExerciseLibraryCate.moduleName=练习库分类
com.wunding.learn.common.enums.language.LanguageModuleEnum.TestPaperLibraryCate.moduleName=试卷库分类
com.wunding.learn.common.enums.language.LanguageModuleEnum.CourseLibraryCate.moduleName=课件库分类
com.wunding.learn.common.enums.language.LanguageModuleEnum.SurveyLibraryCate.moduleName=调研库分类
com.wunding.learn.common.enums.language.LanguageModuleEnum.CourseTagNewCate.moduleName=课程标签分类
com.wunding.learn.common.enums.language.LanguageModuleEnum.ExampleCate.moduleName=案例分类
com.wunding.learn.common.enums.language.LanguageModuleEnum.lecturerDomainCate.moduleName=讲师领域分类
com.wunding.learn.common.enums.market.HeadContentEnum.COURSE.name=课程
com.wunding.learn.common.enums.market.HeadContentEnum.NEWS.name=资讯
com.wunding.learn.common.enums.market.HeadContentEnum.STUDYPROJECT.name=学习项目
com.wunding.learn.common.enums.market.HeadContentEnum.SPECIAL.name=专题
com.wunding.learn.common.enums.market.HeadContentEnum.LECTURER.name=讲师
com.wunding.learn.common.enums.market.HeadContentEnum.LIVE.name=直播
com.wunding.learn.common.enums.market.HeadContentEnum.TOPIC.name=话题
com.wunding.learn.common.enums.market.HeadContentEnum.CASE_LIBRARY.name=案例库
com.wunding.learn.common.enums.market.HeadContentEnum.TRAIN_PROJECT.name=培训项目
com.wunding.learn.common.enums.market.HeadContentEnum.ORG_INFO.name=组织信息
com.wunding.learn.common.enums.market.HeadContentEnum.CONTINUOUS_CLOCK_IN_SECTION_H5.name=连续签到
com.wunding.learn.common.enums.market.HeadContentEnum.MY_TASK_LIST.name=我的任务
com.wunding.learn.common.enums.push.SuperResourceTypeEnum.project.name=学习项目
com.wunding.learn.common.enums.push.SuperResourceTypeEnum.special.name=专题
com.wunding.learn.common.enums.push.SuperResourceTypeEnum.quick_project.name=快速培训
com.wunding.learn.common.enums.user.SexEnum.UNKNOW.alias=未知
com.wunding.learn.common.enums.user.SexEnum.MAN.alias=先生
com.wunding.learn.common.enums.user.SexEnum.WOMEN.alias=女士
com.wunding.learn.common.enums.other.ExaminationStatusEnum.UN_AUDIT.description=待审核
com.wunding.learn.common.enums.other.ExaminationStatusEnum.AUDIT_PASS.description=审核通过
com.wunding.learn.common.enums.other.ExaminationStatusEnum.AUDIT_REFUSE.description=审核拒绝
com.wunding.learn.common.enums.other.ExcitationOperationEnum.INCREASE.name=增加
com.wunding.learn.common.enums.other.ExcitationOperationEnum.DECREASE.name=减少
com.wunding.learn.common.enums.market.HeadContentRuleEnum.GUESSYOULIKE.name=猜你喜欢
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RECOMMENDCOURSE.name=推荐课程
com.wunding.learn.common.enums.market.HeadContentRuleEnum.POPULARCOURSE1.name=热门课程1
com.wunding.learn.common.enums.market.HeadContentRuleEnum.POPULARCOURSE2.name=热门课程2
com.wunding.learn.common.enums.market.HeadContentRuleEnum.POSTCOURSE.name=岗位课程
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LEARNINGCOURSE.name=我正在学
com.wunding.learn.common.enums.market.HeadContentRuleEnum.NEWS.name=资讯
com.wunding.learn.common.enums.market.HeadContentRuleEnum.STUDYPROJECT.name=学习项目
com.wunding.learn.common.enums.market.HeadContentRuleEnum.STUDYPROJECTSPECIFYCATEGORY.name=指定类别
com.wunding.learn.common.enums.market.HeadContentRuleEnum.SPECIAL.name=我参加的专题
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LECTURERELEGANTDEMEANOUR1.name=讲师风采1
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LECTURERELEGANTDEMEANOUR2.name=讲师风采2
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LECTURERELEGANTDEMEANOUR3.name=讲师风采3
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LIVINGBROADCAST.name=直播中
com.wunding.learn.common.enums.market.HeadContentRuleEnum.CURRENTLIVE.name=当前直播
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LIVEPLAYBACK.name=直播回看
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LATESTTOPICS.name=最新话题
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTTOPICS1.name=热门话题1
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTTOPICS2.name=热门话题2
com.wunding.learn.common.enums.market.HeadContentRuleEnum.ELITEPOSTS1.name=置顶话题1
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASEOFTHEMONTH1.name=本月热门案例1
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASEOFTHEMONTH2.name=本月热门案例2
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASEOFTHEMONTH3.name=本月热门案例3
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASE1.name=热门案例1
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASE2.name=热门案例2
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASE3.name=热门案例3
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RECOMMENDCASE1.name=案例质量等级1
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RECOMMENDCASE2.name=案例质量等级2
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RECOMMENDCASE3.name=案例质量等级3
com.wunding.learn.common.enums.market.HeadContentRuleEnum.TRAINPROJECT.name=培训项目
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RAINPROJECTSPECIFYCATEGORY.name=指定类别
com.wunding.learn.common.enums.market.HeadContentRuleEnum.ORG_INFO.name=组织信息
com.wunding.learn.common.enums.market.HeadContentRuleEnum.CLICK_CLOCK_IN.name=每日点击签到打卡
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LEARN_CLOCK_IN.name=每日课程学习10分钟
com.wunding.learn.common.enums.market.HeadContentRuleEnum.NON_LONG_TASK.name=非长期任务
com.wunding.learn.common.enums.market.HeadContentRuleEnum.CONTAIN_LONG_TASK.name=全部任务
com.wunding.learn.common.enums.exam.CompetitionPeopleModelEnum.SINGLE.text=单人
com.wunding.learn.common.enums.exam.CompetitionPeopleModelEnum.DOUBLE.text=2人
com.wunding.learn.common.enums.exam.CompetitionPeopleModelEnum.MULTI.text=多人
com.wunding.learn.common.enums.exam.CompetitionPeopleModelEnum.GROUP.text=分组
com.wunding.learn.common.enums.market.FirstInfoContentEnum.course.name=课程
com.wunding.learn.common.enums.market.FirstInfoContentEnum.exam.name=考试
com.wunding.learn.common.enums.market.FirstInfoContentEnum.live.name=直播
com.wunding.learn.common.enums.market.FirstInfoContentEnum.exercise.name=练习
com.wunding.learn.common.enums.market.FirstInfoContentEnum.survey.name=调研
com.wunding.learn.common.enums.market.FirstInfoContentEnum.info.name=资讯
com.wunding.learn.common.enums.market.FirstInfoContentEnum.project.name=学习项目
com.wunding.learn.common.enums.market.FirstInfoContentEnum.faceProject.name=面授项目
com.wunding.learn.common.enums.market.FirstInfoContentEnum.vote.name=投票
com.wunding.learn.common.enums.market.FirstInfoContentEnum.special.name=专题学习
com.wunding.learn.common.enums.market.FirstInfoContentEnum.hyperlink.name=外部链接
com.wunding.learn.common.enums.market.FirstInfoContentEnum.accountstatement.name=学习账单
com.wunding.learn.common.enums.push.MyStatisticEnum.COLLECT.title=收藏
com.wunding.learn.common.enums.push.MyStatisticEnum.DOWNLOAD.title=下载
com.wunding.learn.common.enums.push.MyStatisticEnum.ALLSIGNIN.title=签到
com.wunding.learn.common.enums.push.MyStatisticEnum.SUBJECT.title=话题
com.wunding.learn.common.enums.push.MyStatisticEnum.CERTIFICATE.title=证书
com.wunding.learn.common.enums.push.MyStatisticEnum.COURSE_TIME.title=课时
com.wunding.learn.common.enums.push.MyStatisticEnum.INTEGRAL.title=积分
com.wunding.learn.common.enums.push.MyStatisticEnum.LEARN_TIME.title=学时
com.wunding.learn.common.enums.push.MyStatisticEnum.LEARN_CREDIT.title=学分
com.wunding.learn.common.enums.push.MyStatisticEnum.GOLD_COIN.title=金币
com.wunding.learn.common.enums.push.MyStatisticEnum.DESIGNATION.title=头衔
com.wunding.learn.common.enums.push.MyStatisticEnum.MEDAL.title=勋章
com.wunding.learn.common.enums.push.MyStatisticEnum.SUGGEST.title=推荐
com.wunding.learn.project.service.enums.ProjectTaskTypeEnum.COURSE.taskName=课程
com.wunding.learn.project.service.enums.ProjectTaskTypeEnum.EXAM.taskName=考试
com.wunding.learn.project.service.enums.ProjectTaskTypeEnum.EXERCISE.taskName=练习
com.wunding.learn.project.service.enums.ProjectTaskTypeEnum.SURVEY.taskName=调研
com.wunding.learn.project.service.enums.ProjectTaskTypeEnum.LIVE.taskName=直播
com.wunding.learn.project.service.enums.ProjectTaskTypeEnum.TRAIN.taskName=培训班
com.wunding.learn.project.service.enums.ProjectTaskTypeEnum.PROJECT.taskName=项目
com.wunding.learn.project.service.enums.ProjectTaskTypeEnum.APPLY.taskName=报名
com.wunding.learn.project.service.enums.ProjectTaskTypeEnum.SIGN.taskName=签到
com.wunding.learn.project.service.enums.ProjectTaskTypeEnum.EVALUATION.taskName=评估
com.wunding.learn.project.service.enums.ProjectTaskTypeEnum.FORM.taskName=表单
com.wunding.learn.project.service.enums.ProjectTaskTypeEnum.PRACTICAL_OPERATION.taskName=实操
com.wunding.learn.common.enums.other.VoteType.CourseVote.text=课程点赞
com.wunding.learn.common.enums.other.VoteType.CourseCommentVote.text=课程评论点赞
com.wunding.learn.common.enums.other.VoteType.InfoVote.text=资讯点赞
com.wunding.learn.common.enums.other.VoteType.InfoCommentVote.text=资讯评论点赞
com.wunding.learn.common.enums.other.VoteType.SpecialCommentVote.text=专题评论点赞
com.wunding.learn.common.enums.other.VoteType.TrainCommentVote.text=培训班评论点赞
com.wunding.learn.common.enums.other.VoteType.ExampleVote.text=案例点赞
com.wunding.learn.common.enums.other.VoteType.ExampleCommentVote.text=案例评论点赞
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.SYSTEM.name=全局
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.COURSE.name=课程
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.COURSE_WARE.name=课件
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.EXAM.name=考试
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.EXERCISE.name=练习
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.LIVE.name=直播
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.SURVEY.name=调研
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.PROJECT.name=学习项目
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.NEWS.name=资讯
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.EVALUATION.name=评估
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.TOPIC.name=话题版块
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.LECTURER.name=讲师
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.SIGN.name=签到
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.FEEDBACK.name=反馈
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.EXAMPLE.name=案例库
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.READING.name=共读
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.PROMOTEDGAME_CHECKPOINT.name=闯关关卡
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.TRAIN.name=培训项目
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.EXAM_COMPETITION_SINGLE.name=考试竞赛(单人)
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.EXAM_COMPETITION_DOUBLE.name=考试竞赛(双人)
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.EXAM_COMPETITION_MULTI.name=考试竞赛(多人)
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.EXAM_COMPETITION_GROUP.name=考试竞赛(组队)
com.wunding.learn.common.enums.other.OnWorkEnum.NON_WORK.name=离职
com.wunding.learn.common.enums.other.OnWorkEnum.ON_WORK.name=在职
com.wunding.learn.common.enums.other.OnWorkEnum.DELETE.name=删除
com.wunding.learn.common.enums.exam.ScoreRuleTypeEnum.COMPETITION.name=竞赛
com.wunding.learn.common.enums.exam.ScoreRuleTypeEnum.COMPETITION_SESSION.name=场次
com.wunding.learn.common.enums.other.ContentRuleEnum.HOT_COURSE.orderRuleDesc=
com.wunding.learn.common.enums.other.ContentRuleEnum.LEARNING_COURSE.orderRuleDesc=
com.wunding.learn.common.enums.other.ContentRuleEnum.SCORE_LECTURER.orderRuleDesc=按评分倒排
com.wunding.learn.common.enums.other.ContentRuleEnum.TIME_LECTURER.orderRuleDesc=按最新授课时间倒排
com.wunding.learn.common.enums.other.ContentRuleEnum.NEW_TOPIC_NOT_TOP.orderRuleDesc=创建时间倒排
com.wunding.learn.common.enums.other.ContentRuleEnum.HOT_TOPIC_COMMENT.orderRuleDesc=按评论数倒排
com.wunding.learn.common.enums.other.ContentRuleEnum.HOT_TOPIC_VIEW.orderRuleDesc=按游览数倒排
com.wunding.learn.common.enums.other.ContentRuleEnum.NEW_TOPIC_TOP.orderRuleDesc=创建时间倒排
com.wunding.learn.common.enums.other.ContentRuleEnum.HOT_CASE.orderRuleDesc=按下载数倒排
com.wunding.learn.common.enums.other.ContentRuleEnum.NEW_CASE.orderRuleDesc=按创建时间倒排
com.wunding.learn.common.enums.other.ContentRuleEnum.LIVING_LIVE.orderRuleDesc=
com.wunding.learn.common.enums.other.ContentRuleEnum.CURRENT_LIVE.orderRuleDesc=
com.wunding.learn.common.enums.other.ContentRuleEnum.ROLLBACK_LIVE.orderRuleDesc=
com.wunding.learn.common.enums.evaluation.EvaluationSourceEnum.MANUAL_UPLOAD.message=手动上传
com.wunding.learn.common.enums.evaluation.EvaluationSourceEnum.EVAL_LIB.message=评估库
com.wunding.learn.common.enums.evaluation.EvaluationSourceEnum.DOCUMENT.message=资料库
com.wunding.learn.common.enums.exam.GroupPKCompetitionSessionUserTypeEnum.WAIT_JOIN.name=待加入
com.wunding.learn.common.enums.exam.GroupPKCompetitionSessionUserTypeEnum.JOINED.name=已加入
com.wunding.learn.common.enums.exam.GroupPKCompetitionSessionUserTypeEnum.READY.name=已准备
com.wunding.learn.common.enums.exam.GroupPKCompetitionSessionUserTypeEnum.START_ANSWER.name=开始答题
com.wunding.learn.common.enums.other.ResourceChangeEnum.course.name=课程
com.wunding.learn.common.enums.other.ResourceChangeEnum.info.name=资讯
com.wunding.learn.common.enums.other.ResourceChangeEnum.exam.name=考试
com.wunding.learn.common.enums.other.ResourceChangeEnum.live.name=直播
com.wunding.learn.common.enums.other.ResourceChangeEnum.exercise.name=练习
com.wunding.learn.common.enums.other.ResourceChangeEnum.survey.name=调研
com.wunding.learn.common.enums.other.ResourceChangeEnum.project.name=学习项目
com.wunding.learn.common.enums.other.ResourceChangeEnum.vote.name=投票
com.wunding.learn.common.enums.other.ResourceChangeEnum.special.name=专题制度
com.wunding.learn.common.enums.other.ResourceChangeEnum.hyperlink.name=外部链接
com.wunding.learn.common.enums.other.ResourceChangeEnum.accountstatement.name=学习账单
com.wunding.learn.common.enums.other.ResourceChangeEnum.example.name=案例库
com.wunding.learn.common.enums.other.ResourceChangeEnum.orgCate.name=组织分类
com.wunding.learn.common.enums.project.ProjectTaskType.course.name=课程
com.wunding.learn.common.enums.project.ProjectTaskType.exam.name=考试
com.wunding.learn.common.enums.project.ProjectTaskType.exercise.name=练习
com.wunding.learn.common.enums.project.ProjectTaskType.survey.name=调研
com.wunding.learn.common.enums.project.ProjectTaskType.live.name=直播
com.wunding.learn.common.enums.project.ProjectTaskType.project.name=项目
com.wunding.learn.common.enums.project.ProjectTaskType.mentor.name=导师
com.wunding.learn.common.enums.project.ProjectTaskType.lecturer.name=讲师
com.wunding.learn.common.enums.user.SexEnum.UNKNOW.name=未知
com.wunding.learn.common.enums.user.SexEnum.MAN.name=男
com.wunding.learn.common.enums.user.SexEnum.WOMEN.name=女
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.ACCEPT_FEEDBACK.name=反馈意见受理
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.AUDIT_COURSEWARE.name=管理员审核通过学员上传课件
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.BEST_REPLY_TOPIC.name=最佳话题回帖
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COLLECT_COURSE.name=收藏课程
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COMMENT_COURSE.name=评论课程
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COMMENT_COURSEWARE.name=评论课件
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COMMENT_NEWS.name=评论资讯
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COMMIT_EVALUATE.name=提交评估
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COMMIT_EXAM.name=提交考试
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COMMIT_EXERCISE.name=完成练习
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COMMIT_FEEDBACK.name=提交反馈意见
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COMMIT_HOMEWORK.name=提交作业
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COMMIT_SIGN.name=完成签到
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COURSE_STAR.name=课程评星
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COURSE_VOTE.name=点赞课程
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COURSEWARE_STAR.name=课件被评星
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COURSEWARE_VIEW.name=课件被浏览数
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COURSEWARE_VOTE.name=课件被点赞数
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.EVERYDAY_LEARN_COURSE.name=连续每日学完一个课程（打卡次数）
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.EVERYDAY_LEARN_COURSEWARE.name=连续每日学完一个课件（打卡次数）
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.EVERYDAY_LOGIN.name=每日连续登录打卡（打卡次数)
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.EXAMPLE_ADD.name=提交案例
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.EXAMPLE_AUDIT.name=参与审核案例
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.EXAMPLE_AUDIT_OVER.name=案例评审通过后，评审得分激励
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.EXAMPLE_COMPANY.name=案例被设置为公司级
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.EXAMPLE_EXTRA.name=案例被标识优质
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.EXAMPLE_GROUP.name=案例被设置为集团级
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.EXAM_PROCESS.name=考试成绩区间激励
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.FINISH_COURSE.name=学完课程
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.FINISH_COURSEWARE.name=学完课件（固定）
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.FINISH_COURSEWARE_BYTIME.name=学完课件（按课件时长）
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.FINISH_PROJECT.name=完成项目
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.FINISH_PROJECT_APPLY.name=完成项目报名
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.FINISH_SURVEY.name=提交调研
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.FIRST_LOGIN.name=首次激活打卡
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.FOCUS_TOPIC.name=关注话题
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.INTEGRAL_CLEARING.name=积分清零
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.LEARN_COURSE.name=点击课程学习
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.LEARN_COURSEWARE.name=点击课件学习
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.LECTURER_JOIN_PROJECT_TEACH.name=参与授课项目
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.LECTURER_TEACH_COURSEWARE.name=参与授课课件个数
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.LIVE_CONTRIBUTE.name=通过发起直播贡献知识获得贡献学分
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.LIVE_INTERACT.name=在直播间与主播进行互动给分
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.LIVE_INTERACT_COUNT.name=发起的直播达到一定观看互动量给分
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.LIVE_QUESTION_AND_ANSWER.name=主播可在直播间内进行金币悬赏提问，学员在直播间内回答问题可获得金币
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.MAKE_LIVE.name=发起直播给分
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.MAKE_TOPIC.name=发表话题
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.PARTICIPATION_DOUBLE.name=参与获取（双人）
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.PARTICIPATION_GROUP.name=参与获取（组队）
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.PARTICIPATION_MULTI.name=参与获取（多人）
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.PARTICIPATION_SINGLE.name=参与获取（单人）
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.PERSONAL_CHAMPION_MULTI.name=个人获冠军（多人）
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.PERSONAL_SECOND_MULTI.name=个人获亚军（多人）
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.PERSONAL_THIRD_MULTI.name=个人获季军（多人）
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.PERSONAL_WIN_DOUBLE.name=个人获胜利（双人）
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.PERSONAL_WIN_SINGLE.name=个人获胜利（单人）
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.PROJECT_TASK_PROCESS.name=任务完成率
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.PROMOTED_GAME_FINISH_CHECKPOINT.name=闯关关卡完成
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.READING_BOOK_NUM.name=共读阅读图书数量
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.READING_CLOCK_TIMES.name=共读打卡次数
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.READING_COMMENT_EXPNUM.name=共读评论心得数量
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.READING_EXPERIENCE_TIMES.name=共读心得提交次数
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.REPLY_COURSE_COMMENT.name=回复课程评论
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.REPLY_COURSEWARE_COMMENT.name=回复课件评论
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.REPLY_TOPIC.name=回帖
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.SHARE_COURSE.name=分享课程
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.SHARE_COURSEWARE.name=分享课件
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.STAR_COURSEWARE.name=课件评星
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.TEAM_WIN_GROUP.name=团队获胜（组队）
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.TOPIC_COMMENT_COUNT.name=话题回帖数量
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.VIEW_COURSE_PROCESS.name=所有课件的浏览时长进度(百分比)
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.VIEW_COURSEWARE_PROCESS.name=课件浏览时长进度(百分比)
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.VIEW_LIVE.name=点击观看直播
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.VIEW_LIVE_TOTAL_TIME.name=累计观看直播时长达到指定时长
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.VIEW_NEWS.name=点击查看资讯
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.VOTE_COURSE_COMMENT.name=点赞课程评论
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.VOTE_LECTURER.name=学员点赞讲师
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.VOTE_NEWS_COMMENT.name=点赞资讯评论
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COURSE_POSTER_SHARE.name=分享课程
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.PROJECT_POSTER_SHARE.name=分享项目
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.LIVE_POSTER_SHARE.name=分享直播
com.wunding.learn.train.service.enums.LearnMapCategoryEnum.POST.name=岗位
com.wunding.learn.train.service.enums.LearnMapCategoryEnum.GROUP.name=群体场景型
com.wunding.learn.train.service.enums.LearnMapCategoryEnum.COMPETENCY_STANDARD.name=岗位胜任力(标准)
com.wunding.learn.train.service.enums.LearnMapCategoryEnum.COMPETENCY.name=岗位胜任力
com.wunding.learn.common.enums.other.CategoryTypeEnum.TrainType.name=培训类别
com.wunding.learn.common.enums.other.CategoryTypeEnum.ActivityType.name=活动方式
com.wunding.learn.common.enums.other.CategoryTypeEnum.SupplierTerritory.name=供应商合作领域类别
com.wunding.learn.common.enums.other.CategoryTypeEnum.SupplierFile.name=供应商资料类别
com.wunding.learn.common.enums.other.CategoryTypeEnum.FormTemplateCate.name=表单模板分类
com.wunding.learn.common.enums.other.CategoryTypeEnum.TrainSubjectCategory.name=开班培训课题分类
com.wunding.learn.common.enums.other.CategoryTypeEnum.IndustryType.name=行业类型
com.wunding.learn.common.enums.other.CategoryTypeEnum.CompanySize.name=公司规模
com.wunding.learn.common.enums.other.CategoryTypeEnum.CourseCategory.name=课程类别
com.wunding.learn.common.enums.other.CategoryTypeEnum.TrainWithoutExam.name=外部培训-考试分类
com.wunding.learn.common.enums.other.CategoryTypeEnum.CourseWithoutResource.name=外部课程资源
com.wunding.learn.common.enums.other.CategoryTypeEnum.AllocationType.name=激励配置类型
com.wunding.learn.common.enums.other.CategoryTypeEnum.ExamCompetitionTrainType.name=考试竞赛培训类别
com.wunding.learn.common.enums.other.CategoryTypeEnum.LearnMapSpecialty.name=胜任力地图专业
com.wunding.learn.common.enums.other.CategoryTypeEnum.APPLICABLE_HIERARCHY.name=测评工具适用层级
com.wunding.learn.common.enums.other.CategoryTypeEnum.USAGE_CLASSIFICATION.name=测评工具用途分类
com.wunding.learn.common.enums.other.CategoryTypeEnum.ASSESS_PROJECT.name=测评项目
com.wunding.learn.common.enums.other.CategoryTypeEnum.MentorCate.name=导师分类
com.wunding.learn.common.enums.other.CategoryTypeEnum.EXAMPLE_BUSINESS.name=案例条线
com.wunding.learn.common.enums.other.CategoryTypeEnum.ExampleAudit.name=案例审核标准
com.wunding.learn.file.api.constant.ExportFileNameEnum.Course.type=课程列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseStudyDetail.type=课程学习明细列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseCategoryExport.type=课程分类列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CoursewareStudyDetail.type=课件学习明细列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseComment.type=课程评论列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.SharedLibrary.type=课程共享库列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CoursewarePackage.type=学员上传课件列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.MergeCourse.type=合并课件到课程列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseWareLearn.type=学员课件学习明细列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseLearn.type=课程学习明细列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseCategory.type=课程分类管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseTagManage.type=课程标签管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseSysCategory.type=课程标签分类管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseTagSta.type=课程标签统计列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseStudyStatistics.type=课程学习统计-按部门
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseStudyStatisticsUser.type=人员明细
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseNote.type=课程笔记
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseStudyCourseLearnUser.type=学习统计-按课程-人员明细
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseWithout.type=外部课程列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Exam.type=考试管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamCorrect.type=改卷管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamCorrectRecord.type=答题记录列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.SchemaList.type=组卷方案列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Exercise.type=练习管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamEmployeeResultsDetail.type=学员考试成绩明细列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamAnswerOfStatisticalOrg.type=考试答题统计按部门统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamAnswerOfStatisticalPost.type=考试答题统计按岗位统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamAnswerRecord.type=考试成绩明细
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamAnswerRecordDetail.type=考试答题记录明细
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamAnalysisQuestion.type=答题分析
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamQuestion.type=考题题目明细
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamCompetition.type=考试竞赛管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamCompetitionUserScoreRank.type=得分排名
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamCompetitionUserAnswerRank.type=答题排名
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamCompetitionSessionUser.type=场次列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamCompetitionAnswerRecord.type=答题列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamCompetitionSessionStat.type=场次明细
com.wunding.learn.file.api.constant.ExportFileNameEnum.CompetitionAnswerRecordStat.type=答题统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamStatistic.type=按考试情况统计列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamJoinUserByBizRecord.type=考试情况统计-参加用户-按照
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamNotJoinUserByBizRecord.type=考试情况统计-未参加用户-按照
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamPostUserByBizRecord.type=考试情况统计-交卷用户-按照
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamNotPassUserByBizRecord.type=考试情况统计-未通过用户-按照
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamPassUserByBizRecord.type=考试情况统计-通过用户-按照
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamLimitUserByBizRecord.type=考试情况统计-下发用户-按照
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExampleLib.type=案例库管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExpertLib.type=专家库管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExampleBusiness.type=案例条线管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExampleComment.type=案例评论管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExampleCategory.type=案例分类管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExampleAuditCategory.type=案例审核分类管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Live.type=直播管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LiveStatic.type=直播统计列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LiveVod.type=直播回放列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Post.type=话题管理帖子列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.PostBanUser.type=禁言名单管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.PostSection.type=话题板块管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.PostSectionExpert.type=话题板块专家管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.PostCountComment.type=回帖统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.Emigrated.type=闯关游戏管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Checkpoint.type=闯关关卡列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CheckpointTask.type=闯关任务列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.StylesTemplate.type=样式管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Notice.type=公告管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Team.type=团队管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Statistical.type=闯关统计列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.TaskStatistical.type=查看明细列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Info.type=新闻资讯列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Category.type=分类管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Comment.type=资讯评论管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Survey.type=调研列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.SurveyAnalysis.type=调研分析详情
com.wunding.learn.file.api.constant.ExportFileNameEnum.SurveyRecordDetail.type=全部调研明细
com.wunding.learn.file.api.constant.ExportFileNameEnum.StudyProject.type=学习项目管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectFixedDate.type=日期项目管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectFixedCycle.type=周期项目管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.QuickProject.type=快速培训管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseTaskProject.type=课程任务列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.FormTemplate.type=辅导模板管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Position.type=岗位发展列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Classroom.type=教室列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainingPlan.type=培训计划管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectMentor.type=导师管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectLecturer.type=讲师管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectApplyUser.type=报名管理用户列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectSignUser.type=签到管理用户列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectPost.type=话题管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectWork.type=作业管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.EvalReplyUserList.type=评估人员列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectEvaluation.type=评估统计分析结果
com.wunding.learn.file.api.constant.ExportFileNameEnum.PROJECT_EVALUATION_DETAIL.type=评估统计分析明细
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectFormTemplate.type=辅导结果
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectHomeWork.type=作业详情
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectCompletion.type=结业情况
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticTaskDetail.type=学习项目明细统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.PROJECT_FIXED_CYCLE_STATISTIC_TASK_DETAIL.type=周期项目明细统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.QuickProjectStatisticTaskDetail.type=快速培训明细统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseTaskProjectStatisticTaskDetail.type=课程任务明细统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.FaceProjectStatisticTaskDetail.type=面授项目任务明细统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticPersonRank.type=个人学习排名统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticOrgCompleteUserDetailIsFinish.type=部门完成统计用户详情-已完成
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticOrgCompleteUserDetailNotFinish.type=部门完成统计用户详情-未完成
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticTeamCompleteUserDetailIsFinish.type=团队完成统计用户详情-已完成
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticTeamCompleteUserDetailNotFinish.type=团队完成统计用户详情-未完成
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticTeamLearnRank.type=团队学习排名统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticOrgLearnRank.type=部门学习排名统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatistic.type=培训班统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectHoldStatistic.type=学习项目举办统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectJoiningStatistic.type=学习项目参与统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerTrainStatistic.type=讲师培训统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerTrainStatisticByBiz.type=讲师培训统计按
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticLecturer.type=培训班讲师统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectOrgStatistic.type=培训班组织统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.FormTemplateColumn.type=任务记录列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticByType.type=按类型-项目统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticByTypeUser.type=按类型-项目统计-
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticJoinUser.type=项目统计-参加人员详情
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticByProject.type=项目统计-按项目
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticByOrg.type=项目统计-按部门
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticByUser.type=项目统计-按人员
com.wunding.learn.file.api.constant.ExportFileNameEnum.SpecialFixedDate.type=专题管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.SpecialFixedCycle.type=周期专题管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.SpecialCategory.type=专题分类管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.SpecialLabel.type=专题标签管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.SpecialCompletion.type=专题结业情况
com.wunding.learn.file.api.constant.ExportFileNameEnum.SpecialStatisticTaskDetail.type=专题明细统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.Lecturer.type=讲师管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerCategory.type=讲师分类设置列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerLevel.type=讲师等级设置列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerModifyRecord.type=讲师异动记录列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerExaminationAudit.type=完课审核列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerUpgradeConfig.type=规则配置_晋级规则列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerDemotionConfig.type=规则配置_降级规则列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerRemovalConfig.type=规则配置_出库规则列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerExamination.type=授课课时信息明细列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerExaminationAssess.type=授课评估信息明细列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerTeachDetail.type=线下授课明细列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.InsideLecturer.type=讲师统计(按部门)-讲师管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.NotWorkday.type=非工作日列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Certification.type=证书管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CertificationSetup.type=证书体系列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CertificationLevel.type=证书等级管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CertificationRule.type=发证规则列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CertificationRelate.type=持证明细列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CertificationRelateDept.type=部门持证明细列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CertificationHoldUser.type=应持证管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CertificationHoldUserStat.type=应持证统计管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CertificationDeptTarget.type=部门持证目标管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CerDeptTargetReport.type=部门持证目标统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.ASSESS_TOOL_LIST.type=测评工具列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ASSESS_PROJECT_LIST.type=测评项目列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ASSESS_USER_LIST2.type=测评用户列表-导出方式2
com.wunding.learn.file.api.constant.ExportFileNameEnum.ASSESS_USER_LIST1.type=测评用户列表-导出方式1
com.wunding.learn.file.api.constant.ExportFileNameEnum.ASSESS_DEP_MANAGER_USER_LIST.type=部门测评人员管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ASSESS_PROJECT_ASSESS_DETAILS.type=测评项目测评明细列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ASSESS_QUESTION_DETAIL_LIST.type=测评题目明细列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Reading.type=共读管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ReadingBooksManage.type=图书管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ReadingSign.type=打卡列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ReadingBookExperience.type=心得列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ReadingBookExperienceComment.type=心得评论管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ReadingBookExperienceStar.type=心得点赞管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ReadingReports.type=举报列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ReadingStatistics.type=图书统计列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserRankReadingStatistics.type=学员共读排行统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.TaskFinishReadingStatistics.type=任务完成统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.Appraise.type=评价列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.AppraiseLecturer.type=讲师评价列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Meeting.type=任职资格答辩列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.AppraiseReferee.type=评委列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.MeetingReferee.type=评委列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.AppraiseProvider.type=被评人列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.MeetingProvider.type=答辩人列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.AppraiseFileType.type=材料规则列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.AppraiseProviderFile.type=材料管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.AppraiseDetail.type=评分明细列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.MeetingScoreDetail.type=评分统计列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.AppraiseShowDetail.type=查看明细列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.AppraiseHistory.type=打分历史列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Recruiting.type=招募列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerRecruiting.type=讲师招募列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.RecruitingAssistant.type=协办人员列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.RecruitingMaterial.type=宣传材料列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.RecruitingMaterialRule.type=材料规则列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.RecruitingAudit.type=招募审核列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.RecruitingAuditSurveyDetail.type=招募审核调研明细列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseWareLib.type=课件库列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.courseWareLibraryCategory.type=课件库分类
com.wunding.learn.file.api.constant.ExportFileNameEnum.TestPaperLib.type=试卷库列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.TestPaperLibraryCategory.type=试卷库分类
com.wunding.learn.file.api.constant.ExportFileNameEnum.TestPaperQuestion.type=题目管理
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamLib.type=考题库列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamLibQuestion.type=题目管理
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamLibCategory.type=考题库分类
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExerciseLib.type=练习题库列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExerciseLibCategory.type=练习库分类
com.wunding.learn.file.api.constant.ExportFileNameEnum.SurveyLib.type=调研库列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.SurveyLibCategory.type=调研库分类
com.wunding.learn.file.api.constant.ExportFileNameEnum.MaterialLib.type=知识管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.MaterialCategory.type=知识库标签
com.wunding.learn.file.api.constant.ExportFileNameEnum.KnowledgeBaseType.type=知识库分类
com.wunding.learn.file.api.constant.ExportFileNameEnum.ResearchField.type=讲师研究领域
com.wunding.learn.file.api.constant.ExportFileNameEnum.EvaluationLib.type=评估库列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.EvaluationCategory.type=评估库分类列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.FormTemplateManage.type=表单模板管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamOrgStatistic.type=按部门考试情况统计列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectLecturerTeachDetail.type=讲师授课明细列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerTeachStatisticDetail.type=讲师授课统计明细列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Award.type=奖品管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.AwardCategory.type=奖品分类管理
com.wunding.learn.file.api.constant.ExportFileNameEnum.GameLottery.type=积分中奖列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.MailTemplate.type=邮箱模板列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.PushManage.type=推送管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.PushMessageManage.type=消息管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.SignStatistics.type=签到统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.SignStat.type=签到统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectSignStat.type=项目签到统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainSignStat.type=培训签到统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.SignList.type=签到列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectSignList.type=项目签到列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.AwardRedeemRecord.type=金币兑换
com.wunding.learn.file.api.constant.ExportFileNameEnum.AwardExchangeRecord.type=兑换记录
com.wunding.learn.file.api.constant.ExportFileNameEnum.PaymentOrder.type=订单列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.PaymentOrgOrderMember.type=机构订单会员列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.VoteManage.type=投票管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.VoteContent.type=投票内容管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.VoteStatistics.type=投票统计列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.VoteDetail.type=投票明细列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.User.type=用户管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserExpand.type=用户管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserExpandTemplate.type=用户导入模板
com.wunding.learn.file.api.constant.ExportFileNameEnum.Org.type=组织列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Role.type=角色管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Dict.type=数据字典管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Version.type=版本管理
com.wunding.learn.file.api.constant.ExportFileNameEnum.Title.type=头衔设置列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.HomePageConfig.type=首页配置列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Feedback.type=反馈管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.IdentityPostSystemTemplate.type=岗位体系导入模板
com.wunding.learn.file.api.constant.ExportFileNameEnum.IdentityPost.type=岗位身份列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.IdentityPostTemplate.type=岗位身份导入模板
com.wunding.learn.file.api.constant.ExportFileNameEnum.IdentityPostSystemData.type=岗位体系数据列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Expert.type=专家评委库数据列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.IdentityTime.type=时间身份列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Identity.type=身份列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.InfoStatAnalysis.type=资讯访问统计列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnRecordStatAnalysis.type=学员档案列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnRecordCourseStatAnalysis.type=学员档案统计-新学课程列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnRecordExamStatAnalysis.type=学员档案统计-参与考试列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnRecordInfoStatAnalysis.type=学员档案统计-查看资讯列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnRecordProjectStatAnalysis.type=学员档案统计-参与学习项目列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnRecordFaceProjectStatAnalysis.type=学员档案统计-参与面授班级列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnRecordTrainStatAnalysis.type=学员档案统计-参与培训项目列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnRecordSurveyStatAnalysis.type=学员档案统计-参与调研列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ScoreRankStatAnalysis.type=经验排行榜列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserIntegralStatAnalysis.type=积分统计列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerIntegral.type=积分统计列表-按讲师
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserIntegralDetail.type=积分统计详情
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserIntegralDetailStatAnalysis.type=积分统计详情列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseLearnState.type=课程情况统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseLearned.type=课程学习情况记录
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseAgreeDetail.type=课程点赞情况记录
com.wunding.learn.file.api.constant.ExportFileNameEnum.CoursewareLearnStatAnalysis.type=课件学习统计列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CoursewareLearnDetailStatAnalysis.type=课件学习统计详情列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.OrgLearnStatAnalysis.type=部门学习统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamStateStatAnalysis.type=考试情况统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamStateStatPartAnalysis.type=考试情况统计参与人数
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamStateStatPassAnalysis.type=考试情况统计及格人数
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamStateStatPostAnalysis.type=考试情况统计交卷人数
com.wunding.learn.file.api.constant.ExportFileNameEnum.GldTradeStatAnalysis.type=金币交易查询
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExchangeRecordStatAnalysis.type=兑换记录查询
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExcitationCollectStatAnalysis.type=学员激励汇总
com.wunding.learn.file.api.constant.ExportFileNameEnum.InsideLecturerByDepartment.type=讲师统计（按部门）
com.wunding.learn.file.api.constant.ExportFileNameEnum.InsideLecturerByCategory.type=讲师统计（按类别）
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserExcitationRecordStatAnalysis.type=目标激励明细查询
com.wunding.learn.file.api.constant.ExportFileNameEnum.OnlineUserStatAnalysis.type=上线用户统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainPlanStatAnalysis.type=培训计划统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.TimeRegionStatAnalysis.type=访问时段统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.StudentUploadCoursewareStatAnalysis.type=学员课件上传统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.SearchKeyStatAnalysis.type=搜索关键字统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.CertifiedCategory.type=认证分类列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Log.type=操作日志
com.wunding.learn.file.api.constant.ExportFileNameEnum.BizLog.type=业务操作日志
com.wunding.learn.file.api.constant.ExportFileNameEnum.OrgThirdSyncRecord.type=部门同步历史
com.wunding.learn.file.api.constant.ExportFileNameEnum.PostThirdSyncRecord.type=岗位同步历史
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserThirdSyncRecord.type=人员同步历史
com.wunding.learn.file.api.constant.ExportFileNameEnum.CertificationRelateWatermarkImg.type=明细图片
com.wunding.learn.file.api.constant.ExportFileNameEnum.Train.type=培训项目列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainActivity.type=培训项目活动列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainProject.type=班级管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainActivityPracticeRecord.type=实操管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.MedalUserRelation.type=勋章列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.FormManage.type=表单管理清单列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Plan.type=培训计划列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.PlanAudit.type=培训计划管理-审核列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.PlanInventory.type=培训计划清单列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.PlanInventoryTemplate.type=培训计划清单模板
com.wunding.learn.file.api.constant.ExportFileNameEnum.PlanTrain.type=培训项目列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.PlanStatistic.type=培训计划汇总表
com.wunding.learn.file.api.constant.ExportFileNameEnum.PlanInventoryStatistic.type=培训计划条目明细
com.wunding.learn.file.api.constant.ExportFileNameEnum.PlanExecuteMonth.type=培训计划执行统计-按月
com.wunding.learn.file.api.constant.ExportFileNameEnum.PlanExecuteYear.type=培训计划执行统计-按年
com.wunding.learn.file.api.constant.ExportFileNameEnum.Leaflet.type=宣传单列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.PlanCategory.type=培训计划类别
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerCourseAuthByLecturer.type=课程认证讲师按照讲师导出
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerCourseAuthByCourse.type=课程认证讲师按照课程导出
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerCourseList.type=讲师开发课程列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerCourseAuthTeach.type=课程认证讲师-讲师授课明细
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainWithOut.type=外部培训管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainWithOutAssist.type=外部培训协办管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainWithoutStatistics.type=外部培训统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainWithoutApply.type=外部培训报名列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainWithoutApplyAssist.type=外部培训协办报名列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainWithoutResult.type=外部培训结果列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainWithoutApplyDetail.type=外部培训明细列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainWithoutWorkStatistics.type=外部培训统计-专业
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainMembers.type=成员管理-学员
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainCoOrganizer.type=成员管理-协办人员
com.wunding.learn.file.api.constant.ExportFileNameEnum.TaskScore.type=活动分
com.wunding.learn.file.api.constant.ExportFileNameEnum.Supplier.type=供应商管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.SupplierFile.type=供应商资料管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainLecturerTeachDetail.type=供应商讲师授课管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainCourse.type=培训班课程列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainTaskDetail.type=培训项目明细统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainPersonRank.type=培训项目个人学习排名统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainOrgLearn.type=培训项目部门学习排名统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserWhiteRecord.type=推送白名单
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserIdentityInfo.type=用户身份信息
com.wunding.learn.file.api.constant.ExportFileNameEnum.IdentityUserInfo.type=身份用户管理
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserIdentityList.type=用户身份管理
com.wunding.learn.file.api.constant.ExportFileNameEnum.VisibleViewLimitUserList.type=下发人员明细
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserVisitItemRecord.type=链接访问明细
com.wunding.learn.file.api.constant.ExportFileNameEnum.HomeRouterVisitDetail.type=后台访问明细
com.wunding.learn.file.api.constant.ExportFileNameEnum.ItemVisitRecord.type=栏目访问记录
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMap.type=学习地图
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapList.type=学习地图列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapExec.type=学习地图执行列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapLearnDetail.type=学习明细列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapLearnUser.type=学习用户列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapActivityHorizontalStat.type=活动横向统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapPhaseHorizontalStat.type=阶段横向统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapActivity.type=学习地图活动列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapProgressStat.type=学习地图情况
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapActivityProgressStat.type=学习地图活动情况
com.wunding.learn.file.api.constant.ExportFileNameEnum.AbilityDictionary.type=能力词典
com.wunding.learn.file.api.constant.ExportFileNameEnum.AbilityMode.type=能力模型
com.wunding.learn.file.api.constant.ExportFileNameEnum.PermissionConfig.type=权限目录配置
com.wunding.learn.file.api.constant.ExportFileNameEnum.PermissionRouter.type=权限目录路由配置
com.wunding.learn.file.api.constant.ExportFileNameEnum.CoursewareQuestionAnswerRecord.type=课件答题明细
com.wunding.learn.file.api.constant.ExportFileNameEnum.ApplyTemplate.type=面授项目报名模板
com.wunding.learn.file.api.constant.ExportFileNameEnum.FaceProjectApplyUser.type=面授项目用户报名列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.FaceProject.type=面授项目管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.JobQualification.type=任职资格列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.JobAuthentication.type=资格认证列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.JobAuthApplyRecord.type=认证人员列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.PracticalSuperviseUser.type=监督评价员列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.PracticalOperationUserBySupervise.type=监督学员列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.PracticalOperationUser.type=实操记录列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapAbilityOrgStatistic.type=能力项部门初训统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapAbilityOrgStatistic2.type=能力项部门复训统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapUserOrgStatistic.type=胜任力地图部门初训统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapUserOrgStatistic2.type=胜任力地图部门复训统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapUserResourcesStatistic.type=胜任力地图学习明细统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapUserStatistic.type=胜任力地图学习进度明细统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapAbilityAsk.type=学习地图能力学习要求
com.wunding.learn.file.api.constant.ExportFileNameEnum.InvoiceList.type=开票管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectVacate.type=请假管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ScheduleStat.type=日程统计列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectFormTemplateAnnex.type=辅导结果附件
com.wunding.learn.file.api.constant.ExportFileNameEnum.March.type=游戏管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.MarchCheckpoint.type=游戏关卡列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.MarchCheckpointTask.type=游戏任务列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.MarchTeam.type=团队管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.MarchPost.type=话题管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.MarchNotice.type=说明管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.MarchPostCountComment.type=回帖统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.ASSESS_TOOL_QUESTION_TEM.type=测评工具题目导入模板
com.wunding.learn.file.api.constant.ExportFileNameEnum.ASSESS_PROGRESS.type=测评项目进度统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.ASSESS_IMPORT_USER.type=测评用户导入模板
com.wunding.learn.file.api.constant.ExportFileNameEnum.ASSESS_IMPORT_RECORD.type=测评记录导入模板
com.wunding.learn.file.api.constant.ExportFileNameEnum.ASSESS_IMPORT_DETAILS.type=测评明细导入模版
com.wunding.learn.file.api.constant.ExportFileNameEnum.RecordDetail.type=答题明细
com.wunding.learn.file.api.constant.ExportFileNameEnum.ASSESS_REPORT.type=测评报告
com.wunding.learn.file.api.constant.ExportFileNameEnum.COMPETITION_SESSION_USER_DETAIL.type=考试竞赛场次人员信息
com.wunding.learn.file.api.constant.ExportFileNameEnum.INTERVIEW_QUANTITY_STATISTICS.type=访问量统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserGoldCoin.type=用户金币列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserGoldCoinBill.type=用户金币账单列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.MemberCard.type=会员卡管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.MemberOrg.type=会员机构管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Member.type=会员管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerProjectStatistic=讲师统计列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.FaceProjectStatistic=面授项目培训统计列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnProjectStatistic=学习项目培训统计列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainProjectStatistic=培训项目培训统计列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticUserDetail=学员列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.TASK_MENTOR_LIST=任务导师列表
com.wunding.learn.common.enums.other.SystemTypeEnum.AFTER_SALE.message=售后体系
com.wunding.learn.common.enums.other.SystemTypeEnum.INTERNAL_TRAINING.message=内训体系
com.wunding.learn.common.enums.other.ContentRuleEnum.HOT_COURSE.name=热门课程
com.wunding.learn.common.enums.other.ContentRuleEnum.LEARNING_COURSE.name=我正在学
com.wunding.learn.common.enums.other.ContentRuleEnum.SCORE_LECTURER.name=讲师风采1
com.wunding.learn.common.enums.other.ContentRuleEnum.TIME_LECTURER.name=讲师风采2
com.wunding.learn.common.enums.other.ContentRuleEnum.NEW_TOPIC_NOT_TOP.name=最新话题
com.wunding.learn.common.enums.other.ContentRuleEnum.HOT_TOPIC_COMMENT.name=热门话题(按回复数)
com.wunding.learn.common.enums.other.ContentRuleEnum.HOT_TOPIC_VIEW.name=热门话题(按游览数)
com.wunding.learn.common.enums.other.ContentRuleEnum.NEW_TOPIC_TOP.name=最新话题(含置顶)
com.wunding.learn.common.enums.other.ContentRuleEnum.HOT_CASE.name=热门案例
com.wunding.learn.common.enums.other.ContentRuleEnum.NEW_CASE.name=最新案例
com.wunding.learn.common.enums.other.ContentRuleEnum.LIVING_LIVE.name=直播中
com.wunding.learn.common.enums.other.ContentRuleEnum.CURRENT_LIVE.name=当前直播
com.wunding.learn.common.enums.other.ContentRuleEnum.ROLLBACK_LIVE.name=直播回看
com.wunding.learn.common.enums.promotedgame.GameQuestionType.Single.text=单选
com.wunding.learn.common.enums.promotedgame.GameQuestionType.Multi.text=多选
com.wunding.learn.common.enums.promotedgame.GameQuestionType.Judge.text=判断
com.wunding.learn.common.enums.exam.CompetitionSessionStatusEnum.DEFAULT_VALUE.name=默认值
com.wunding.learn.common.enums.exam.CompetitionSessionStatusEnum.READY.name=准备中 
com.wunding.learn.common.enums.exam.CompetitionSessionStatusEnum.ON_GOING.name=进行中
com.wunding.learn.common.enums.exam.CompetitionSessionStatusEnum.END.name=已经结束
com.wunding.learn.project.service.enums.QuickProjectRoleEnum.STUDENT.name=学员
com.wunding.learn.project.service.enums.QuickProjectRoleEnum.TRAINER.name=培训师
com.wunding.learn.project.service.enums.QuickProjectRoleEnum.ADMIN.name=管理员
com.wunding.learn.common.enums.other.MenuItemEnums.INDEX.name=首页
com.wunding.learn.common.enums.other.MenuItemEnums.NOTICE.name=公告
com.wunding.learn.common.enums.other.MenuItemEnums.COURSE.name=课程
com.wunding.learn.common.enums.other.MenuItemEnums.SCHEDULE.name=日程
com.wunding.learn.common.enums.other.MenuItemEnums.ACTIVITY.name=活动
com.wunding.learn.common.enums.other.MenuItemEnums.CLASS.name=班级
com.wunding.learn.common.enums.other.MenuItemEnums.PHOTO.name=照片墙
com.wunding.learn.common.enums.other.MenuItemEnums.DATUM.name=资料
com.wunding.learn.common.enums.other.MenuItemEnums.CASE.name=案例
com.wunding.learn.common.enums.other.MenuItemEnums.TOPIC.name=话题
com.wunding.learn.common.enums.other.MenuItemEnums.RANK.name=排名
com.wunding.learn.common.enums.other.MenuItemEnums.LECTURER.name=讲师
com.wunding.learn.common.enums.other.MenuItemEnums.SPECIAL.name=专题
com.wunding.learn.common.enums.evaluation.QuestionType.SINGLE.text=单选
com.wunding.learn.common.enums.evaluation.QuestionType.MULTI.text=多选
com.wunding.learn.common.enums.evaluation.QuestionType.JUDGE.text=判断
com.wunding.learn.common.enums.evaluation.QuestionType.BLANKS.text=填空
com.wunding.learn.common.enums.evaluation.QuestionType.QA.text=问答
com.wunding.learn.common.enums.evaluation.QuestionType.GRADE.text=打分
com.wunding.learn.common.constant.plan.PlanStatusEnum.DRAFT.name=草稿
com.wunding.learn.common.constant.plan.PlanStatusEnum.TO_BE_REVIEWED.name=待审核
com.wunding.learn.common.constant.plan.PlanStatusEnum.REJECTED.name=驳回
com.wunding.learn.common.constant.plan.PlanStatusEnum.APPROVED.name=审核通过
com.wunding.learn.common.enums.evaluation.SourceEnum.REFERENCE.message=引用
com.wunding.learn.common.enums.evaluation.SourceEnum.MANUAL_UPLOAD.message=手动上传
com.wunding.learn.reading.service.enums.ReadingTaskDescription.READING_TASK_TYPE_SIGN.name=活动期间打卡次数
com.wunding.learn.reading.service.enums.ReadingTaskDescription.READING_TASK_TYPE_EXPERIENCE.name=活动期间提交心得次数
com.wunding.learn.reading.service.enums.ReadingTaskDescription.READING_TASK_TYPE_READING_N.name=活动期间阅读/听图书数量
com.wunding.learn.reading.service.enums.ReadingTaskDescription.READING_TASK_TYPE_COMMENT_N.name=评论心得的数量
com.wunding.learn.reading.service.enums.ReadingTaskDescription.READING_TASK_TYPE_READING.name=阅读某本书的数量
com.wunding.learn.reading.service.enums.ReadingTaskDescription.READING_TASK_TYPE_COMMENT.name=提交某本书的心得
com.wunding.learn.common.enums.other.TriggerTypeEnum.ANY.text=任意满足触发
com.wunding.learn.common.enums.other.TriggerTypeEnum.ALL.text=全部满足触发
com.wunding.learn.common.enums.other.GeneralJudgeEnum.CONFIRM.text=是
com.wunding.learn.common.enums.other.GeneralJudgeEnum.NEGATIVE.text=否
com.wunding.learn.user.service.enums.ItemTypeEnum.MIDDLE_ITEM.name=中间菜单
com.wunding.learn.user.service.enums.ItemTypeEnum.BOTTOM_ITEM.name=底部菜单
com.wunding.learn.user.service.enums.ItemTypeEnum.MY_LEARN_ITEM.name=我的学习
com.wunding.learn.user.service.enums.ItemTypeEnum.APPLICATION_ITEM.name=我的应用
com.wunding.learn.user.service.enums.ItemTypeEnum.MY_PAGE.name=我的页面

com.wunding.learn.user.service.enums.CustomMenuEnum.INTEGRAL.name=积分
com.wunding.learn.user.service.enums.CustomMenuEnum.HOURS.name=学时
com.wunding.learn.user.service.enums.CustomMenuEnum.CREDIT.name=学分
com.wunding.learn.user.service.enums.CustomMenuEnum.GOLD.name=金币
com.wunding.learn.user.service.enums.CustomMenuEnum.CERTIFICATE.name=证书
com.wunding.learn.user.service.enums.CustomMenuEnum.COLLECT.name=收藏
com.wunding.learn.user.service.enums.CustomMenuEnum.IDP.name=IDP
com.wunding.learn.user.service.enums.CustomMenuEnum.TASK.name=任务
com.wunding.learn.user.service.enums.CustomMenuEnum.COURSE.name=课程
com.wunding.learn.user.service.enums.CustomMenuEnum.EXAM.name=考试
com.wunding.learn.user.service.enums.CustomMenuEnum.SURVEY.name=调研
com.wunding.learn.user.service.enums.CustomMenuEnum.PROJECT.name=项目
com.wunding.learn.user.service.enums.CustomMenuEnum.LIVE.name=直播
com.wunding.learn.user.service.enums.CustomMenuEnum.TOPIC.name=话题
com.wunding.learn.user.service.enums.CustomMenuEnum.QUIZ.name=闯关
com.wunding.learn.user.service.enums.CustomMenuEnum.SIGN.name=签到
com.wunding.learn.user.service.enums.CustomMenuEnum.MY_INFO.name=我的信息
com.wunding.learn.user.service.enums.CustomMenuEnum.CHANGE_PASSWORD.name=修改密码
com.wunding.learn.user.service.enums.CustomMenuEnum.FEEDBACK.name=意见反馈
com.wunding.learn.user.service.enums.CustomMenuEnum.MY_TAG.name=我的标签
com.wunding.learn.user.service.enums.CustomMenuEnum.INCENTIVE_EXPLANATION.name=激励说明
com.wunding.learn.user.service.enums.CustomMenuEnum.THIRD_PARTY_BINDING.name=第三方绑定
com.wunding.learn.user.service.enums.CustomMenuEnum.PRIVACY_POLICY.name=隐私政策
com.wunding.learn.user.service.enums.CustomMenuEnum.SWITCH_LANGUAGE.name=选择语言
com.wunding.learn.user.service.enums.CustomMenuEnum.ENABLE_STUDENT_UPLOAD.name=启用PC端 “学员课件上传”
com.wunding.learn.user.service.enums.CustomMenuEnum.ENABLE_H5_IDP.name=启用H5端 “IDP”
com.wunding.learn.user.service.enums.CustomMenuEnum.ENABLE_H5_TARGET.name=启用H5端 “目标岗位”

com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_1.description=用于各处背景展示
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_2.description=登录页标题背景，含slogan
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_3.description=首页标题背景，不含slogan
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_4.description=首页背景号角人物图
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_5.description=除首页外所有页面通用背景
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_6.description=取消按钮
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_7.description=关卡完成时的弹窗中，关卡完成提示
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_8.description=关闭按钮
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_9.description=上传步数提示页中的按钮
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_10.description=
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_11.description=步数上传完成后的弹窗提示语
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_12.description=完成关卡后，点击去看故事的按钮
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_13.description=首次登录的游戏说明提示弹窗中的按钮
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_14.description=步数上传弹窗的立即上传按钮
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_15.description=今日里程的标题logo
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_16.description=进入“我的”页面的入口按钮
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_17.description=进入“排行榜”页面的入口按钮
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_18.description=活动说明的标题logo
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_19.description=上传昨日运动按钮的标题logo
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_20.description=学习专区的标题logo
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_21.description=首页团队进度条的小人
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_22.description=我的，故事中的旗帜
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_23.description=我的，故事中的旗帜
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_24.description=学习专区的顶部背景叠层
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_25.description=“我的”页的顶部背景叠层
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_26.description=除首页外的其他页面的剪影背景
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_27.description=地图链接箭头
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_28.description=地图链接箭头
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_29.description=地图链接箭头
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_30.description=地图链接箭头
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_31.description=地图链接箭头
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_32.description=地图链接箭头
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_33.description=地图链接箭头
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_34.description=地图链接箭头
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_35.description=地图链接箭头
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_36.description=地图链接箭头
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_37.description=地图链接箭头
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_38.description=地图链接箭头
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_39.description=地图链接箭头
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_40.description=地图链接箭头
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_41.description=地图链接箭头
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_42.description=地图链接箭头
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_43.description=地图链接箭头
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_44.description=地图链接箭头
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_45.description=地图链接箭头
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_46.description=地图链接箭头
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_47.description=地图链接箭头
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_48.description=地图链接箭头
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_49.description=首页地图地点的原点光环
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_50.description=首页地图地点的原点光环
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_51.description=首页地图地点的旗帜
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_52.description=说明等弹窗的头部背景
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_53.description=关卡完成的恭喜弹窗的背景
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_54.description=故事弹窗的背景
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_55.description=首次登录的游戏说明提示弹窗中的顶部背景)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_56.description=说明弹窗的标题旁的logo，每个说明配置一个旗帜
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_57.description=我的页的标题背景，显示在文字下方
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_58.description=我的页的卡片背景
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_59.description=学习专区的横幅
com.wunding.learn.common.enums.other.ModelEnums.MENU.name=菜单栏目
com.wunding.learn.common.enums.other.ModelEnums.ROTOGRAPH.name=轮播图
com.wunding.learn.common.enums.other.ModelEnums.NOTICE.name=滚动公告
com.wunding.learn.common.enums.other.ModelEnums.CASE.name=案例
com.wunding.learn.common.enums.other.ModelEnums.DATUM.name=资料
com.wunding.learn.common.enums.other.ModelEnums.TOPIC.name=话题
com.wunding.learn.common.enums.other.ModelEnums.COURSE.name=课程
com.wunding.learn.common.enums.other.ModelEnums.SCHEDULE.name=日程
com.wunding.learn.common.enums.other.ModelEnums.LEAFLET.name=宣传单
com.wunding.learn.common.enums.other.ModelEnums.RANK.name=排名
com.wunding.learn.common.enums.other.ModelEnums.PHOTO.name=照片
com.wunding.learn.common.enums.other.ModelEnums.CLASS.name=班级
com.wunding.learn.common.enums.other.ModelEnums.LIVE.name=直播
com.wunding.learn.common.enums.other.ModelEnums.LECTURER.name=讲师
com.wunding.learn.common.enums.other.ModelEnums.SPECIAL.name=专题
com.wunding.learn.common.enums.survey.SurveyQuestionTypeEnum.QUESTION_TYPE_RADIO.name=单选
com.wunding.learn.common.enums.survey.SurveyQuestionTypeEnum.QUESTION_TYPE_MULTI_SELECT.name=多选
com.wunding.learn.common.enums.survey.SurveyQuestionTypeEnum.QUESTION_TYPE_JUDGEMENT.name=判断
com.wunding.learn.common.enums.survey.SurveyQuestionTypeEnum.QUESTION_TYPE_CLOZE.name=填空
com.wunding.learn.common.enums.survey.SurveyQuestionTypeEnum.QUESTION_TYPE_QA.name=问答
com.wunding.learn.common.enums.survey.SurveyQuestionTypeEnum.QUESTION_TYPE_GRADE.name=打分
com.wunding.learn.common.enums.survey.SurveyQuestionTypeEnum.QUESTION_TYPE_MATERIALS.name=材料
com.wunding.learn.common.enums.market.RepeatDescriptionEnum.RECOMMENDED_DATE_DESC.message=按推荐时间倒排
com.wunding.learn.common.enums.market.RepeatDescriptionEnum.ALL_INTERIOR_TEACH.message=所有内部讲师
com.wunding.learn.common.enums.market.RepeatDescriptionEnum.LIVE_START_DATE_DESC.message=按直播开始时间倒排
com.wunding.learn.common.enums.market.RepeatDescriptionEnum.GIVE_LIKE_DESC.message=按点赞数倒排
com.wunding.learn.common.enums.other.ResourcesTypeEnum.COURSE_DOWNLOAD.name=课程下载
com.wunding.learn.common.enums.other.ResourcesTypeEnum.LECTURER.name=讲师
com.wunding.learn.common.enums.viewlimit.NewViewLimitTypeEnum.OrgLimit.viewName=部门下发
com.wunding.learn.common.enums.viewlimit.NewViewLimitTypeEnum.IdentityLimit.viewName=身份下发
com.wunding.learn.common.enums.viewlimit.NewViewLimitTypeEnum.UserLimit.viewName=用户下发
com.wunding.learn.common.enums.market.HeadContentRuleEnum.GUESSYOULIKE.showRuleDesc=猜你喜欢单独逻辑
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RECOMMENDCOURSE.showRuleDesc=下发范围标识为精选好课的课程
com.wunding.learn.common.enums.market.HeadContentRuleEnum.POPULARCOURSE1.showRuleDesc=下发范围 最近一个月内发布的 评论数+评星次数 大于{0}的
com.wunding.learn.common.enums.market.HeadContentRuleEnum.POPULARCOURSE2.showRuleDesc=下发范围 评论数+评星次数 大于{0}的
com.wunding.learn.common.enums.market.HeadContentRuleEnum.POSTCOURSE.showRuleDesc=与自己岗位匹配的课程
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LEARNINGCOURSE.showRuleDesc=我学习的，未完成的课程
com.wunding.learn.common.enums.market.HeadContentRuleEnum.NEWS.showRuleDesc=下发范围
com.wunding.learn.common.enums.market.HeadContentRuleEnum.STUDYPROJECT.showRuleDesc=下发范围包含我未参与或者未完成的学习项目
com.wunding.learn.common.enums.market.HeadContentRuleEnum.STUDYPROJECTSPECIFYCATEGORY.showRuleDesc=指定类别的学习项目
com.wunding.learn.common.enums.market.HeadContentRuleEnum.SPECIAL.showRuleDesc=下发范围包含我未参与或者未完成的学习项目
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LECTURERELEGANTDEMEANOUR1.showRuleDesc=所有内部讲师
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LECTURERELEGANTDEMEANOUR2.showRuleDesc=所有内部讲师
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LECTURERELEGANTDEMEANOUR3.showRuleDesc=所有内部讲师
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LIVINGBROADCAST.showRuleDesc=下发范围 直播结束时间大于当前时间并且直播开始时间小于当前时间
com.wunding.learn.common.enums.market.HeadContentRuleEnum.CURRENTLIVE.showRuleDesc=下发范围直播结束时间大于当前时间
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LIVEPLAYBACK.showRuleDesc=下发范围直播结束时间小于当前间，且有回看文件的直播
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LATESTTOPICS.showRuleDesc=版块下发范围
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTTOPICS1.showRuleDesc=版块下发范围发表时间在{0}天内回帖数量大于{1}个
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTTOPICS2.showRuleDesc=版块下发范围回帖数量大于{0}个
com.wunding.learn.common.enums.market.HeadContentRuleEnum.ELITEPOSTS1.showRuleDesc=版块下发范围设置为置顶的帖子
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASEOFTHEMONTH1.showRuleDesc=所有的范围本月发表的，且本月发生了点赞数大于{2}
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASEOFTHEMONTH2.showRuleDesc=公司范围内公开本月发表的，且本月发生了点赞数大于{2}
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASEOFTHEMONTH3.showRuleDesc=集团范围内公开本月发表的，且本月发生了点赞数大于{2}'
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASE1.showRuleDesc=所有的范围点赞数大于2
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASE2.showRuleDesc=公司范围内公开点赞数大于2
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASE3.showRuleDesc=集团范围内公开点赞数大于2
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RECOMMENDCASE1.showRuleDesc=标记为案例质量等级1的
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RECOMMENDCASE2.showRuleDesc=标记为案例质量等级2的
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RECOMMENDCASE3.showRuleDesc=标记为案例质量等级3的
com.wunding.learn.common.enums.market.HeadContentRuleEnum.TRAINPROJECT.showRuleDesc=下发范围包含我的培训项目
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RAINPROJECTSPECIFYCATEGORY.showRuleDesc=指定类别的培训项目
com.wunding.learn.common.enums.market.HeadContentRuleEnum.ORG_INFO.showRuleDesc=组织信息
com.wunding.learn.common.enums.market.HeadContentRuleEnum.showRuleDesc.name=每日点击签到打卡
com.wunding.learn.common.enums.market.HeadContentRuleEnum.CLICK_CLOCK_IN.showRuleDesc=每日点击签到，完成打卡
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LEARN_CLOCK_IN.showRuleDesc=每日学习课程达到10分钟，则视为完成打卡
com.wunding.learn.common.enums.market.HeadContentRuleEnum.NON_LONG_TASK.showRuleDesc=所有带有开始和结束日期的学习活动
com.wunding.learn.common.enums.market.HeadContentRuleEnum.CONTAIN_LONG_TASK.showRuleDesc=所有学习活动
com.wunding.learn.common.enums.course.ParaTypeEnum.MEMBER_DEFAULT_CARD.name=学院开放用户默认会员卡
com.wunding.learn.common.enums.course.ParaTypeEnum.IS_HANG_UP.name=是否播放防挂机
com.wunding.learn.common.enums.course.ParaTypeEnum.CAN_SPEED.name=首次播放是否倍数
com.wunding.learn.common.enums.course.ParaTypeEnum.CAN_DRAG.name=首次播放是否可拖拽
com.wunding.learn.common.enums.course.ParaTypeEnum.LECTURER_RESOURCE_APPLY_SPECIAL_ROLE.name=讲师资源申请特权角色
com.wunding.learn.common.enums.course.ParaTypeEnum.EVAL_PASS_VALUE.name=评估优良分值
com.wunding.learn.common.enums.course.ParaTypeEnum.SIGN_START_TIME_ADVANCE_MINUTES.name=培训签到(签退)开始提前时间(分钟)
com.wunding.learn.common.enums.course.ParaTypeEnum.SIGN_END_TIME_AFTER_MINUTES.name=培训签到(签退)结束延后时间(分钟)
com.wunding.learn.common.enums.course.ParaTypeEnum.EXCITATION_EXCHANGE_CONFIG.name=激励兑换属性配置
com.wunding.learn.common.enums.course.ParaTypeEnum.EVALUATION_START_DELAY_DEFAULT_TIME.name=评估默认开始时间(分钟)
com.wunding.learn.common.enums.course.ParaTypeEnum.EVALUATION_END_DELAY_DEFAULT_TIME.name=评估默认结束时间(分钟)
com.wunding.learn.common.enums.course.ParaTypeEnum.LECTURER_EXAMINATION_EVAL_AFTER_SALE_TEMPLATE.name=内训默认评估模板
com.wunding.learn.common.enums.course.ParaTypeEnum.NORMAL_ON_DUTY_TIME.name=正常上班时间
com.wunding.learn.common.enums.course.ParaTypeEnum.NORMAL_OFF_MORRING.name=正常上午下班时间
com.wunding.learn.common.enums.course.ParaTypeEnum.NORMAL_ON_AFTERNOON.name=正常下午上班时间
com.wunding.learn.common.enums.course.ParaTypeEnum.NORMAL_OFF_DUTY_TIME.name=正常下班时间
com.wunding.learn.common.enums.course.ParaTypeEnum.LECTURER_EXAMINATION_EVAL_INTERNAL_TRAINING_TEMPLATE.name=售后默认评估模板
com.wunding.learn.common.enums.course.ParaTypeEnum.NUMBER_OF_WECOM.name=企业微信对接数量
com.wunding.learn.common.enums.course.ParaTypeEnum.AVAILABLE_DEFAULT_APPLY_FORM_TEMPLATE.name=是否启用默认报名模板
com.wunding.learn.file.api.constant.ExportFileNameEnum.USER_POSTER_SHARE.type=用户海报分享列表
com.wunding.learn.common.enums.other.ShareChannelEnum.WE_CHAT.name=微信好友
com.wunding.learn.common.enums.other.ShareChannelEnum.WE_CHAT_MOMENTS.name=微信朋友圈
com.wunding.learn.common.enums.other.ShareResourceTypeEnum.COURSE.name=课程
com.wunding.learn.common.enums.other.ShareResourceTypeEnum.PROJECT.name=学习项目
com.wunding.learn.common.enums.other.ShareResourceTypeEnum.LIVE.name=直播
com.wunding.learn.common.enums.push.PushNoticeEventNameEnum.PUBLISH_NOTICE.name=发布通知
com.wunding.learn.common.enums.push.PushNoticeEventNameEnum.REMINDER_NOTICE.name=催办通知
com.wunding.learn.common.enums.push.PushNoticeEventNameEnum.CO_HANDLE_PENDING_NOTICE.name=协办待审通知
com.wunding.learn.common.enums.push.PushNoticeEventNameEnum.AUDIT_RESULT_NOTIFICATION.name=审核结果通知
com.wunding.learn.common.enums.push.PushNoticeEventNameEnum.PUBLISH_REGISTRATION_NOTICE.name=发布报名通知
com.wunding.learn.common.enums.push.PushNoticeEventNameEnum.NOTIFICATION_OF_EVALUATION_RESULTS.name=评价结果通知
com.wunding.learn.common.enums.push.PushNoticeEventNameEnum.CLASS_INFORMATION_NOTICE.name=开班信息通知
com.wunding.learn.common.enums.push.PushNoticeEventNameEnum.AUDIT_RESULTS_CANNOT_BE_MODIFIED_IF_THE_AUDIT_FAILS.name=审核未通过不可修改审核结果通知
com.wunding.learn.common.enums.push.PushNoticeEventNameEnum.AUDIT_FAILED_TO_MODIFY_THE_RESULT_NOTICE.name=审核未通过可修改结果通知
com.wunding.learn.common.enums.push.PushNoticeEventNameEnum.NOTIFICATION_OF_APPROVAL_RESULT.name=审核通过结果通知
com.wunding.learn.common.enums.push.PushNoticeEventNameEnum.NOTICE_OF_REPLY.name=答辩结果通知
com.wunding.learn.common.enums.push.PushNoticeEventNameEnum.CLASS_START_INFORMATION_NOTICE.name=开课信息通知
com.wunding.learn.file.api.constant.ExportFileNameEnum.SysTemTagStatAnalysis.type=系统标签统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.ApplyLecturer.type=讲师预约审核列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ApplyCourseDownload.type=下载申请列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectCompletionStatistic.type=项目完成情况统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectUserTask.type=用户项目任务完成情况统计
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectUserProjectExcitationRecord.type=用户项目激励获取记录
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectUserProjectCoursewareStudyDetail.type=用户学习项目课时统计
com.wunding.learn.certification.service.enums.AssessComputedModeEnum.OPTION_SCORE.name=选项结果对应分值
com.wunding.learn.certification.service.enums.AssessComputedModeEnum.OPTION_SUM_SCORE.name=选项结果总分分值
com.wunding.learn.certification.service.enums.AssessComputedModeEnum.OPTION_AVG_SCORE.name=选项结果平均分值
com.wunding.learn.certification.service.enums.AssessComputedModeEnum.OPTION_MIN_SCORE.name=选项结果中最低分值
com.wunding.learn.certification.service.enums.AssessComputedModeEnum.OPTION_MAX_SCORE.name=选项结果中最高分值
com.wunding.learn.march.service.enums.StyleCodeEnum.HOME_BACKGROUND.name=首页背景图
com.wunding.learn.march.service.enums.StyleCodeEnum.HOME_BACKGROUND.description=用于各处背景展示
com.wunding.learn.march.service.enums.StyleCodeEnum.LOGIN_TITLE_BACKGROUND.name=标题背景（登录页）
com.wunding.learn.march.service.enums.StyleCodeEnum.LOGIN_TITLE_BACKGROUND.description=登录页标题背景，含slogan
com.wunding.learn.march.service.enums.StyleCodeEnum.HOME_TITLE_BACKGROUND.name=标题背景
com.wunding.learn.march.service.enums.StyleCodeEnum.HOME_TITLE_BACKGROUND.description=首页标题背景，不含slogan
com.wunding.learn.march.service.enums.StyleCodeEnum.HOME_SILHOUETTE.name=首页背景剪影图
com.wunding.learn.march.service.enums.StyleCodeEnum.HOME_SILHOUETTE.description=首页背景号角人物图
com.wunding.learn.march.service.enums.StyleCodeEnum.OTHER_PAGE_BACKGROUND.name=其他页背景图
com.wunding.learn.march.service.enums.StyleCodeEnum.OTHER_PAGE_BACKGROUND.description=除首页外所有页面通用背景
com.wunding.learn.march.service.enums.StyleCodeEnum.CANCEL_BUTTON.name=取消按钮
com.wunding.learn.march.service.enums.StyleCodeEnum.CANCEL_BUTTON.description=取消按钮
com.wunding.learn.march.service.enums.StyleCodeEnum.KNOW_COMPLETED_LEVEL.name=我知道了（完成关卡）
com.wunding.learn.march.service.enums.StyleCodeEnum.KNOW_COMPLETED_LEVEL.description=关卡完成时的弹窗中，关卡完成提示
com.wunding.learn.march.service.enums.StyleCodeEnum.CLOSE_BUTTON.name=关闭
com.wunding.learn.march.service.enums.StyleCodeEnum.CLOSE_BUTTON.description=关闭按钮
com.wunding.learn.march.service.enums.StyleCodeEnum.KNOW_UPLOAD_STEPS.name=我知道了（上传步数）
com.wunding.learn.march.service.enums.StyleCodeEnum.KNOW_UPLOAD_STEPS.description=上传步数提示页中的按钮
com.wunding.learn.march.service.enums.StyleCodeEnum.OK_BUTTON.name=好的
com.wunding.learn.march.service.enums.StyleCodeEnum.OK_BUTTON.description=
com.wunding.learn.march.service.enums.StyleCodeEnum.REFRESH_STEPS.name=刷新步数
com.wunding.learn.march.service.enums.StyleCodeEnum.REFRESH_STEPS.description=步数上传完成后的弹窗提示语
com.wunding.learn.march.service.enums.StyleCodeEnum.VIEW_STORY.name=去看故事
com.wunding.learn.march.service.enums.StyleCodeEnum.VIEW_STORY.description=完成关卡后，点击去看故事的按钮
com.wunding.learn.march.service.enums.StyleCodeEnum.CONFIRM_FIRST_LOGIN.name=确定（首次登录提示语）
com.wunding.learn.march.service.enums.StyleCodeEnum.CONFIRM_FIRST_LOGIN.description=首次登录的游戏说明提示弹窗中的按钮
com.wunding.learn.march.service.enums.StyleCodeEnum.UPLOAD_NOW.name=立即上传
com.wunding.learn.march.service.enums.StyleCodeEnum.UPLOAD_NOW.description=步数上传弹窗的立即上传按钮
com.wunding.learn.march.service.enums.StyleCodeEnum.TODAY_MILEAGE.name=今日里程
com.wunding.learn.march.service.enums.StyleCodeEnum.TODAY_MILEAGE.description=今日里程的标题logo
com.wunding.learn.march.service.enums.StyleCodeEnum.MY_BUTTON.name=我的按钮
com.wunding.learn.march.service.enums.StyleCodeEnum.MY_BUTTON.description=进入“我的”页面的入口按钮
com.wunding.learn.march.service.enums.StyleCodeEnum.RANKING_BUTTON.name=排行榜按钮
com.wunding.learn.march.service.enums.StyleCodeEnum.RANKING_BUTTON.description=进入“排行榜”页面的入口按钮
com.wunding.learn.march.service.enums.StyleCodeEnum.ACTIVITY_DESCRIPTION.name=活动说明
com.wunding.learn.march.service.enums.StyleCodeEnum.ACTIVITY_DESCRIPTION.description=活动说明的标题logo
com.wunding.learn.march.service.enums.StyleCodeEnum.UPLOAD_YESTERDAY_ACTIVITY.name=上传昨日运动按钮
com.wunding.learn.march.service.enums.StyleCodeEnum.UPLOAD_YESTERDAY_ACTIVITY.description=上传昨日运动按钮的标题logo
com.wunding.learn.march.service.enums.StyleCodeEnum.LEARNING_ZONE_BUTTON.name=学习专区按钮
com.wunding.learn.march.service.enums.StyleCodeEnum.LEARNING_ZONE_BUTTON.description=学习专区的标题logo
com.wunding.learn.march.service.enums.StyleCodeEnum.PROGRESS_BAR_PERSON.name=进度条小人
com.wunding.learn.march.service.enums.StyleCodeEnum.PROGRESS_BAR_PERSON.description=首页团队进度条的小人
com.wunding.learn.march.service.enums.StyleCodeEnum.FLAG_LOCKED.name=旗帜（未解锁)
com.wunding.learn.march.service.enums.StyleCodeEnum.FLAG_LOCKED.description=我的，故事中的旗帜
com.wunding.learn.march.service.enums.StyleCodeEnum.FLAG_UNLOCKED.name=旗帜（已解锁)
com.wunding.learn.march.service.enums.StyleCodeEnum.FLAG_UNLOCKED.description=我的，故事中的旗帜
com.wunding.learn.march.service.enums.StyleCodeEnum.LEARNING_ZONE_BACKGROUND.name=背景（学习专区)
com.wunding.learn.march.service.enums.StyleCodeEnum.LEARNING_ZONE_BACKGROUND.description=学习专区的顶部背景叠层
com.wunding.learn.march.service.enums.StyleCodeEnum.MY_PAGE_BACKGROUND.name=背景（“我的”页)
com.wunding.learn.march.service.enums.StyleCodeEnum.MY_PAGE_BACKGROUND.description=“我的”页的顶部背景叠层
com.wunding.learn.march.service.enums.StyleCodeEnum.OTHER_PAGE_SILHOUETTE.name=其他页背景剪影图
com.wunding.learn.march.service.enums.StyleCodeEnum.OTHER_PAGE_SILHOUETTE.description=除首页外的其他页面的剪影背景
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_10_LOCKED.name=地点连接图10（未完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_10_LOCKED.description=地图链接箭头
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_10_UNLOCKED.name=地点连接图10（已完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_10_UNLOCKED.description=地图链接箭头
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_11_LOCKED.name=地点连接图11（未完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_11_LOCKED.description=地图链接箭头
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_11_UNLOCKED.name=地点连接图11（已完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_11_UNLOCKED.description=地图链接箭头
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_1_LOCKED.name=地点连接图1（未完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_1_LOCKED.description=地图链接箭头
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_1_UNLOCKED.name=地点连接图1（已完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_1_UNLOCKED.description=地图链接箭头
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_2_LOCKED.name=地点连接图2（未完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_2_LOCKED.description=地图链接箭头
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_2_UNLOCKED.name=地点连接图2（已完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_2_UNLOCKED.description=地图链接箭头
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_3_LOCKED.name=地点连接图3（未完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_3_LOCKED.description=地图链接箭头
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_3_UNLOCKED.name=地点连接图3（已完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_3_UNLOCKED.description=地图链接箭头
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_4_LOCKED.name=地点连接图4（未完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_4_LOCKED.description=地图链接箭头
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_4_UNLOCKED.name=地点连接图4（已完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_4_UNLOCKED.description=地图链接箭头
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_5_LOCKED.name=地点连接图5（未完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_5_LOCKED.description=地图链接箭头
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_5_UNLOCKED.name=地点连接图5（已完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_5_UNLOCKED.description=地图链接箭头
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_6_LOCKED.name=地点连接图6（未完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_6_LOCKED.description=地图链接箭头
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_6_UNLOCKED.name=地点连接图6（已完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_6_UNLOCKED.description=地图链接箭头
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_7_LOCKED.name=地点连接图7（未完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_7_LOCKED.description=地图链接箭头
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_7_UNLOCKED.name=地点连接图7（已完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_7_UNLOCKED.description=地图链接箭头
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_8_LOCKED.name=地点连接图8（未完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_8_LOCKED.description=地图链接箭头
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_8_UNLOCKED.name=地点连接图8（已完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_8_UNLOCKED.description=地图链接箭头
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_9_LOCKED.name=地点连接图9（未完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_9_LOCKED.description=地图链接箭头
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_9_UNLOCKED.name=地点连接图9（已完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_9_UNLOCKED.description=地图链接箭头
com.wunding.learn.march.service.enums.StyleCodeEnum.LEVEL_LOCKED_HALO.name=关卡未解锁光环
com.wunding.learn.march.service.enums.StyleCodeEnum.LEVEL_LOCKED_HALO.description=首页地图地点的原点光环
com.wunding.learn.march.service.enums.StyleCodeEnum.LEVEL_UNLOCKED_HALO.name=关卡解锁光环
com.wunding.learn.march.service.enums.StyleCodeEnum.LEVEL_UNLOCKED_HALO.description=首页地图地点的原点光环
com.wunding.learn.march.service.enums.StyleCodeEnum.LEVEL_UNLOCKED_FLAG.name=关卡解锁旗帜
com.wunding.learn.march.service.enums.StyleCodeEnum.LEVEL_UNLOCKED_FLAG.description=首页地图地点的旗帜
com.wunding.learn.march.service.enums.StyleCodeEnum.POPUP_HEADER_BACKGROUND.name=弹窗头部背景
com.wunding.learn.march.service.enums.StyleCodeEnum.POPUP_HEADER_BACKGROUND.description=说明等弹窗的头部背景
com.wunding.learn.march.service.enums.StyleCodeEnum.LEVEL_COMPLETED_BACKGROUND.name=关卡完成背景
com.wunding.learn.march.service.enums.StyleCodeEnum.LEVEL_COMPLETED_BACKGROUND.description=关卡完成的恭喜弹窗的背景
com.wunding.learn.march.service.enums.StyleCodeEnum.STORY_POPUP_BACKGROUND.name=故事弹窗背景
com.wunding.learn.march.service.enums.StyleCodeEnum.STORY_POPUP_BACKGROUND.description=故事弹窗的背景
com.wunding.learn.march.service.enums.StyleCodeEnum.FIRST_LOGIN_POPUP_BACKGROUND.name=提示弹窗背景)
com.wunding.learn.march.service.enums.StyleCodeEnum.FIRST_LOGIN_POPUP_BACKGROUND.description=首次登录的游戏说明提示弹窗中的顶部背景)
com.wunding.learn.march.service.enums.StyleCodeEnum.INSTRUCTION_TITLE_FLAG.name=说明标题旗帜)
com.wunding.learn.march.service.enums.StyleCodeEnum.INSTRUCTION_TITLE_FLAG.description=说明弹窗的标题旁的logo，每个说明配置一个旗帜
com.wunding.learn.march.service.enums.StyleCodeEnum.MY_PAGE_TITLE_BACKGROUND.name=我的页标题背景
com.wunding.learn.march.service.enums.StyleCodeEnum.MY_PAGE_TITLE_BACKGROUND.description=我的页的标题背景，显示在文字下方
com.wunding.learn.march.service.enums.StyleCodeEnum.MY_PAGE_CARD_BACKGROUND.name=我的页卡片背景
com.wunding.learn.march.service.enums.StyleCodeEnum.MY_PAGE_CARD_BACKGROUND.description=我的页的卡片背景
com.wunding.learn.march.service.enums.StyleCodeEnum.LEARNING_ZONE_TITLE.name=学习专区标题
com.wunding.learn.march.service.enums.StyleCodeEnum.LEARNING_ZONE_TITLE.description=学习专区的横幅
