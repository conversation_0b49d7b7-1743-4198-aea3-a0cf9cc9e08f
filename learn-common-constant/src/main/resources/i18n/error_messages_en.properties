#Wed Aug 28 11:27:30 CST 2024
-1=Server internal error
-127=Date parameter error
-14=Repeat operations are not allowed
-15=The QR code has expired, please refresh the QR code
-16=status has changed
-17=No permission to view
-20=Failed to join the group
-201=The start time cannot be greater than the end time
-21=Failed to create group
-25=Not operating within the allowed time
-26=does not exist
-27=Contains lower-level categories and cannot be deleted
-28=This type of file import is not supported
-29=Import error, please download the correct import template
-3=Client version is too low
-30=Please import the correct delivery scope file
-3019=The imported user does not exist in the user list
-3020=Account number and name do not match\!.
-3031=The resource does not exist or has been deleted
-3032=Resource not enabled
-3033=File does not exist
-3034=The resource is not published
-31=Level must be less than level 4
-36=The version numbers are the same and there is no need to re-obtain the questions.
-42=Sensitive words exist
-54=User is not an administrator
-55=The department is not within the jurisdiction
-56=Jurisdiction cannot be empty
-57=The delivery scope exceeds your authority, please reconfirm the delivery scope\!
-6=Public parameter error
-8=The content is incorrectly filled in
-9=Not allowed to be modified
0=Operation successful
1019=File format error
1022=Decompression failed
1023=Wrong content
1024=Incorrect format
1025=File packaging failed
1027=Upload content is empty
1028=Required files were not uploaded
1050=Export failed
1124=Courseware format is incorrect
1125=Courseware in flash format is not supported
1126=Incorrect courseware format: imsmanifest.xml or scorm.xml not found in the SCORM package root directory.
13001=Classroom does not exist
13002=The time period does not exist
13003=There are reservations for the future time in the classroom and cannot be deleted.
13004=The position does not exist
13005=There is a published job development plan and cannot be deleted.
13006=The job development plan contains undeleted activities and cannot be deleted.
13007=fail\! Please do not select the same team for replacement
13008=Duplicate team name
13040=Some of the selected resources cannot be published without a tutorial section, please select again.
13041=The exam does not exist
13042=Project does not exist
13043=Wrong activity type
13044=Activity does not exist
13045=Some of the selected resources contain training plans in non-draft status. Only draft status can be deleted.
13046=Activity update failed, please check whether the job development plan and activities exist
13047=The training plan does not exist or the status is not supported
13048=Some of the selected resources include training plans in non-draft status. Only draft approval is supported.
13049=The completion time of the job cannot exceed the project start and end date, please fill in again
13050=Job does not exist
13051=Job submission record does not exist
13052=The project has no tasks and is not allowed to be published
13053=Team does not exist
13054=The user team has been locked and cannot operate
13055=There are currently no completion conditions added and the course cannot be completed.
13056=Learning project task does not exist
13057=Task sorting already exists
13058=Expired evaluations cannot be republished
13059=Live broadcast failed
13060=The registration list already exists under the project and cannot be created\!
13061=Wrong learning project task type
13062=The assessment is not published and cannot be analyzed.
13063=Project application is not open or unavailable
13064=Sign in does not exist
13065=Please operate within the check-in time range (check-in time\: {0})
13066=Check-in has ended
13067=Check-in is not published or unavailable
13068=The assessment has been published and cannot be edited.
13070=The learning project has not started yet
13071=Study project has ended
13072=Registration has not started
13073=Registration has ended
13074=Registration does not exist
13075=This study program does not allow cancellation of registration
13076=You do not have permission to view the review information for this item
13077=Evaluation does not exist
13078=The evaluation has been completed and cannot be evaluated again
13079=Learning project task creation type error
13080=The operator type of the first column must be a student
13081=Announcement does not exist
13082=Live playback query failed
13084=This category already exists under the same level
13085=Level must be less than level 4
13086=This category does not exist
13087=The category has been referenced and cannot be deleted.
13088=The category has been referenced and the status cannot be modified.
13089=Please use an administrator account to view
13090=Please do not sign in repeatedly
13091=The number of applicants exceeds the planned number of people
13092=This comment does not exist\!
13093=The reminder object id cannot be empty
13094=Counseling does not exist
13095=Query parameters are empty
13096=When the assessment type is upload assessment, files must be uploaded
13097=Tutoring task does not exist
13098=Please upload evaluation documents\!
13099=The position and the target development position are not allowed to be the same\!
13100=The serial number cannot be empty\!
13101=Not within the registration period
13102=Registration is full
13103=This user has already registered
13104=The serial number is duplicated, please re-enter\!
13105=Tutoring template does not exist\!
13106=Task time cannot be empty
13107=Content length must be between 1-10000
13153=Project is not published
13154=User has not registered
13155=Users whose registration has not passed the review are not allowed to sign in.
13160=Training has not started yet
13161=Training has expired
13162=Task does not exist
13163=Task not started
13167=Mission has ended
13168=Missing mobile number
13169=Please try again
13170=After passing the review, it is not allowed to be modified to fail the review again.
13171=Registration ID number is wrong
13172=If you have already registered, you are not allowed to register again.
13173=If you have already registered, you are not allowed to register again.
13174=The file does not exist
13175=The file is being transcoded, please wait\!
13176=In-person classes do not exist\!
13177=Invoicing does not exist\!
13178=Exceeded the maximum number of leave schedules
13179=This leave does not exist
13180=Topic does not exist
13181=Schedule does not exist
13182=A schedule exists in the topic
13183=There is no leave schedule available during this time period
13184=Face-to-face classes have not started yet
13185=Face-to-face classes have ended
13186=The transfer preference is consistent with the current class, please choose again.
13187=Volunteer to repeat
13188=The student and the instructor cannot be the same person
13189=The actual primary key id cannot be empty\!
13190=Practical practice does not exist\!
13191=The actual operation record does not exist\!
13192=The actual operation record id does not exist\!
13193=Supervisory evaluator has been added\!
13194=Students have been added\!
13195=The supervisor and evaluator and the students cannot be the same person\!
13196=Evaluation and answer records already exist and editing is not allowed\!
13197=No files to download\!
13198=The imported data is empty\!
13199=The import file is empty\!
13200=Please select people\!
13201=Published content is not allowed to be deleted
13202=Learning projects do not need to be redeemed for learning
13203=No expediting staff
13204=Planned start time cannot be empty
13205=Planned end time cannot be empty
13206=This column has already been participated by students and cannot be deleted\!
13207=Illegal submission, the current user ID obtained is\: {0}, evaluation; I\: {1}
13208=Duplicate additions of related abilities are prohibited\!
13209=Name cannot be empty
13210=Mobile phone number cannot be empty
13211=The face-to-face teaching location cannot be empty
13212=Training department id cannot be empty
13213=The maximum number of leave classes cannot be empty
13214=Leave rule description cannot be empty
13215=Face-to-face introduction cannot be empty
13216=Planned start time cannot be empty
13217=Planned end time cannot be empty
13218=Registration start and end time cannot be empty
13219=The number of applicants cannot be empty
13220=The number of applicants cannot be less than 0
13221=The number of applicants cannot be greater than 999
13222=The attribute of whether the registration should be reviewed or not cannot be empty.
13223=Registration collection form template ID cannot be empty
13224=Registration conditions cannot be empty
13225=Registration conditions cannot exceed 500 words
13226=Registration instructions cannot exceed 500 words
13227=User id cannot be empty
13228=Type cannot be empty
13229=You can’t choose yourself as a mentor\!
13230=The mobile phone number filled in on behalf of the registration cannot be the current user’s mobile phone number
13231=Registration failed. The registered mobile phone number does not match the user's mobile phone number.
13232=The number of people who have passed the registration review cannot be greater than the planned number of registrations. The planned number of people is {0}, and the number of people who have passed the review is {1}.
13233=Registration record id does not exist
13234=Column does not exist
13235=Missing job import template
13236=Contains invoices that have been approved and cannot be operated in batches
13237=The review has been passed and no further modifications are supported.
13238=Bank account cannot be empty
13239=Organization address cannot be empty
13240=Unit contact number cannot be empty
13241=Invoice contact cannot be empty
13242=Invoice contact phone number cannot be empty
13243=Invoice contact recipient address cannot be empty
13244={0}The job development plan contains undeleted activities and cannot be deleted.
13245=Evaluation start time and end time cannot be empty
13246=Only created, unpublished, and undeleted tasks can be published\!
13247=Only created, unpublished, and undeleted tasks can be unpublished\!
13248=Audit configuration list must not be empty
13249=The permissions of the last member are only allowed to be [Final Review]
13250=Only one [Final Review] is allowed for all members' permissions.
13251={0}
13252={0}
13253={0}
13282=The quick training has been published and can only be deleted after unpublishing it.
13301=This application does not exist under this learning project
13302=The value corresponding to this operation type does not exist
13448=Audit configuration does not exist
13450=There are currently no graduation conditions added and the graduation list cannot be generated.
13477=When the project type is fixed cycle, the cycle days cannot be less than 1
13478=Illegal submission
13483=You have already signed up
13484=Class teachers do not need to register
13500=The current user is not a lecturer and the status of the lecturer is abnormal.
13501=Courseware must be selected when generating an assessment
13502=The training class has no tasks and is not allowed to be released.
13565=This category has been cited by the instructor and cannot be deleted.
13566=There are tutorials that are handled by personnel and cannot be deleted.
13577=Wrong way to add assessment
13581=Schedule tool does not exist
13582=Learning project id cannot be empty
13583=Please re-join the project
13591=Resource does not exist
13599=The tutoring template column does not exist\!
13783=There are no homework attachments to download.
13852=The assignment deadline has passed and cannot be submitted.
14000=Authentication failed
14001=The minimum value of title experience cannot be greater than the maximum value\!
14002=Title name already exists\!
14003=The entered experience value range is already occupied by other titles, please adjust the entered experience value range\!
14201=The prize has been referenced by the points game option and cannot be {0}
14202=Prize does not exist
14203=Insufficient prize stock
14204=This inventory has been fully redeemed, so the inventory cannot be less than {0}
14301=Points game option prize ID does not exist
1444=The imported data is empty
1445=Please download the correct template to import
1446=The exported data is out of range, please modify the export format.
1447=Copy error
1448=No data export
1449=The file resource is corrupted
1450=File must not be empty
14501=This category has been cited by the prize
14502=This category has been referenced by lower-level categories and cannot be disabled {0}
14503=This category is system initialization data and cannot be deleted {0}
1451=Space size limit reached, please contact the administrator
1452=Certificate image format error
1453=The classification management unit exceeds the scope of use authorization
1454=Category does not exist
1455=The current category or subcategory cannot be used as a superior category
1456=It has been referenced and cannot be deleted
1457=Category is not available, please select again
14571=This category has been referenced by the prize and cannot be {0}
1458=Please select a resource first\!
1459=Shared library classification, operation cannot be performed\!
1460=There are pictures or lower-level categories under this category and deletion is not allowed.
14601=The exchange order does not exist
14602=The exchange order has been processed
1461=Question {0}, the standard answer format is incorrect
1462=Please merge all shards after uploading them
1463=Please upload the fragments and merge them
1464=File format is incorrect
1465=File merge failed
1466=Official seal recognition failed
1467=No files to download
1468=Invalid file business type
1469=Failed to save uploaded file
1470=Image file information error
14701=The incentive configuration is locked and cannot be deleted.
14702=Incorrect resource type
14703=Resource data id cannot be empty
14704=Insufficient motivation
14705=Not enough points
14706=Insufficient credits
14707=Not enough hours
14708=Not enough gold coins
14709=Incorrect incentive type
1471=Source file download error
14710=The number of people is full
14711=The quantity available for redemption is zero
14712=No permission to view this course
14713=The points game is empty or the version number is empty
14714=The number of draws is zero
14715=The probability of winning cannot be greater than 100%\!
14716=Failed to redeem gold coins. Unable to redeem {0} gold coins.
14717=The redemption amount is full
1472=An exception occurred while downloading the file
1473=There are no files to compress for download
1474=Failed to decompress courseware
1475=Template lesson, template file does not exist
1476=Unsupported media type
1478=Upload file cannot be empty
14801=Already traded, no further transactions are allowed
14802=Sorry, your redemption has exceeded the maximum limit
14803=Invalid listing status
14804=The incentive event does not exist, please refresh and try again
14805=The upper and lower thresholds for incentive events of the count type cannot be empty.
14806=The upper and lower thresholds for incentive events of the count type cannot be empty.
14807=Invalid incentive event type
14808=The upper limit (number of times) of each content acquisition for the incentive event of the event type cannot be empty, and cannot be less than 1
14809=Inspiring event classification code is invalid
14810=Enable disabled status data is invalid
14811=Invalid lock/unlock status data
14812=The classification code of global synchronization is invalid
14813=Missing user id
14815=Incentive configuration does not exist
14816=[{0}][{1}] does not support global synchronization
14817=Redemption is not allowed and the minimum amount of incentive consumption has not been reached.
1500=Operation failed\!
15010=The topic has no tasks and is not allowed to be published.
15011=Topic does not exist
15012=Topic tag does not exist
15013=The category has been referenced and cannot be deleted.
15014=Thematic tasks do not exist
15015=Task sorting already exists
15016=Thematic task creation type is wrong
15017=No permission to view this topic
15018=The topic time has not started
15019=The topic time has ended
15020=The topic is not published
15021=Contains published resources and cannot be deleted
15022=The start time of the special task cannot be less than the start time of the special plan
15023=The end time of the special task cannot be greater than the end time of the special plan
1552=The usage authorization scope and classification management unit cannot be empty.
16001=Resource does not exist
16002=No access
16003=No repeated submissions
16004=Required files were not uploaded
16005=File does not exist
16006=Upload content is empty
16007=Please upload the materials and then save them
16008=Please operate within the time range for submitting materials (Material submission time\: {0})
16009=Related parameter errors
16010=Error saving file
16011=During the announcement, the data cannot be modified.
16012=The current qualification defense meeting already exists
16013=This defense meeting does not exist
16014=Required file type, cannot be empty
16015=Sample file is empty
16016=Sample file extension type not obtained
16017=Sample file type not obtained
16018=Please select material support type
16019=There is already a participation record and cannot be added.
16020=The reviewee has already uploaded materials in the selected material rules and cannot be updated or deleted.
16021=The selected user cannot be a judge or already a reviewee\!
16022=The judges do not exist
16023=The judges’ weight percentage cannot be modified after the evaluation time starts.
16024=Please enter legal numbers 1-200
16025=The judges do not exist
16026=Published and cannot be deleted
16027=Cover image not uploaded
16028=The start time of the judges' evaluation cannot be greater than the end time
16029=The start time for students to submit materials cannot be greater than the end time
16030=The start time of the judges' evaluation must be greater than the end time of the students' submission of materials
16031=The scoring time cannot be modified for a review that already has a review score record.
16032=Please add at least one required material
16033=Please add judges before publishing
16034=Please add reviewers before publishing
16035=The selected user cannot be a judge or already a respondent\!
16036=The selected user cannot be the reviewee or already a reviewer\!
16037=The break must be within the defense time
16038=Please add the respondent before publishing
16039=Failed to add, this person cannot serve as both the appraisee and the judge at the same time\!
17001=Resource does not exist
17002=No access
17003=No repeated submissions
17004=Required files were not uploaded
17005=File does not exist
17006=Upload content is empty
17007=Please upload the materials and then save them
17008=Please operate within the time range for material submission
17009=Parameter is incorrect
17010=Research materials failed
17011=The number of applicants has reached the upper limit
17012=There is no co-organizer in the recruitment
17013=File size limit cannot be empty
17014=Unsupported type
17015=Recruitment not released
17016=Please operate within the recruitment time range (Recruitment time\: {0}-{1})
17017=Opening hours have ended
17018=Please contact the administrator to set the review scope of the reviewer
17019=Recruitment cover not uploaded
17020=Promotional cover not uploaded
17021=Please enter the limit number of people
17022=Please select the delivery scope
17023=The publish immediately field cannot be empty
17024=The start time or end time is empty
17025=Recruitment end time cannot be earlier than recruitment start time
17026=The number of people limited shall not be less than the current number of registrations
17027=If a user has already registered, the recruitment type/co-organizer processing type cannot be modified.
17028=The survey form has been filled in by the user and cannot be changed.
17029=Material Rules-Only one can be added to the survey form at most
17030=The review has already been participated by users and cannot be changed.
17031=Promotional file is empty
17032=Please add a survey template
17033=The reviewee has uploaded materials in the selected material rules and cannot be deleted.
17034=The selected survey form has already been reviewed by reviewees and cannot be deleted.
17035=Promotional file extension type not obtained
17036=Promotional file type not obtained
17037=The operation failed, please select the user review status
17038=Operation failed, please enter the correct rating
17039=The operation failed. The user already has an audit record.
17040=The operation failed, the registration record does not exist or the review status is incorrect.
17041=There is no survey set up for this recruitment
17042=No one has been recruited to participate in the survey yet
17043=Download error, the user did not upload materials\!
17044=The promotional materials cannot be published because they are being transcoded.
17045=Not operating within the allowed time
17046=Deletion failed\: {0}
17047={0}
17048={0}
17049={0}
17106=Required material upload content is empty
18001=The category name already exists, please re-enter it\!
18002=The category has been referenced and cannot be disabled\!
18003=Published information cannot be deleted
18004=The text content cannot be empty
18005=Information comments do not exist\!
18006=Information transcoding is not completed
18007=Enabled categories cannot be deleted, please cancel and try again\!
18008=Information does not exist
19001=This category does not exist
19002=It has been referenced and cannot be deleted
19003=Has been referenced and cannot modify the status
19004=The level must be less than level 10\!
19005=This category already exists
19006=The case line has been used\!
19007=This user account has been deleted\!
19008=This user has been added as an expert\!
19009=This case does not exist\!
19010=The case upload file cannot be empty\!
19011=You cannot select yourself as the superior parent class\!
19012=This expert does not exist\!
19013=The enabled status cannot be deleted, please cancel and try again\!
19014=The line has been referenced and cannot be deleted
19015=Bar code must consist of numbers and letters
19016=The bar code length should be 2-36 bits
19017=All fields must be filled in when submitting a case
19018=Case staging must have at least one field filled in
19019=The upper level category is not enabled and cannot be modified.
2378=The maximum level can only be level 2!
19419=The level must be below level 5\!
19420=There was a problem with the imported file, please try again\!
19421=Already referenced and cannot be disabled
2000=System built-in users cannot be deleted
20001=Published and not allowed to be deleted
20002=Breakthrough does not exist
20003=The task exists in the level and cannot be deleted.
20004=Published announcements cannot be modified
20005=Announcement does not exist
20006=The announcement has been published and cannot be deleted.
20007=Publishing failed\! Each level must have at least 1 published mission.
20008=Task does not exist
20009=This level has a participation record and the task cannot be deleted.
2001=If you need to refresh the token, please obtain the token and call again.
20010=Team name already exists
20011=Team does not exist
20012=Level does not exist
20013=The task record does not exist
20014=Unable to delete task records generated by the system
20015=Please operate within the job submission time range (job submission time\: {0})
20016=No access
20017=Duplicate serial numbers
20018=The user ID selected for reminder cannot be empty.
20019=This level entry style template does not exist
2002=The client is not logged in and needs to automatically log in again before performing relevant access operations.
20020=The application has not been activated in the game
20021=There are currently no tasks in the current level
20022=Only tasks of job type are allowed
20023=There is no such task or assignment for this level
20024=Missing public parameters
20025=The word count of the assignment does not meet the requirements
20026=The job has been approved and cannot be modified or approved.
20027=Parameter error
20028=The job has not been submitted and cannot be approved.
20029=Rating cannot be empty
2003=Your account has been logged in from another place
20030=Rating does not meet requirements
20031=Assignment does not support grading
20032=The entry introduction cannot be empty
20033=Please operate within the clearance time range (level clearance time\: {0}-{1})
20034=Reference addition does not support adding jobs
20035=The mission does not exist
20036=Wrong type of mission created
20037=The level has been released and levels cannot be added.
20038=The level has been released and the task cannot be deleted.
20039=Cannot publish immediately\! Each level must have at least 1 published mission.
2004=jwt content is wrong or expired
20040=Breakthrough is not released yet\!
20041=The end time cannot be earlier than the start time
20042=Start time or end time cannot be empty
20043=Job description cannot be empty
20044=Homework submission time cannot be empty
20045=Whether the assignment requires a minimum number of words and cannot be empty
20046=Assignment word count cannot be empty
20047=The associated content cannot be empty
2005=Verification code error
2006=The current user does not have administrative rights. Please contact the administrator to activate it.
2007=The current user does not have administrative rights. Please contact the administrator to activate it.
2008=The parameter configuration parameter value does not meet the requirements
2009=The system’s built-in identity categories cannot be deleted
2010=Wrong username or password
2011=Username/account does not exist
2012=User does not exist
2013=This user has been disabled
2014=Login verification code cannot be empty
2015=Error generating verification code
2016=Login verification code error
2017=Login verification code has expired
2067=Student accounts cannot log in\!
2068=The login type is empty and login is not allowed\!
2069=The login key is invalid or expired, please obtain it again.
2070=The official account is not configured correctly, unable to use quick login
21002=The logo image cannot be empty\!
21003=System cannot be empty\!
2101=Duplicate organization number
2102=There is a lower level organization
210211=Certificate details not found
210212=No certificate found
210213=No user found
210214=Unable to obtain uploaded certificate details image
210215=Parameter is empty
210216=Image name format error
210217=The ID number is incorrectly filled in
210218=The qualification does not exist
210219=Abnormality in qualification system documents
210220=The position of the deleted qualification is associated with qualification certification, and the deletion failed.
210221=Contains open qualifications and cannot be deleted
210222=This position does not include qualifications, so qualification certification cannot be added.
210223=This qualification does not exist
210224=Failed to modify positions associated with job qualifications. Currently, students are applying for certification.
210225=Already qualified for this position, adding/editing failed\!
210226=There is already qualification certification for this position. Adding/editing failed\!
210227=The qualifications associated with this position are not public, and the open certification failed\!
210228=Failed to close the qualification certification. There are currently students applying for certification.
210229=The evidence requirements cannot require both materials and texts.
210230=The process link has been deleted or does not exist\!
210231=This position is not associated with qualifications, and the open certification failed\!
210232=The qualification certification review process configuration is abnormal and the submission failed\!
210233=The process record does not exist and submission is prohibited\!
210234=There are students participating in the qualification certification, and modifications to the review process are not allowed\!
210235=The respondent is empty and the evidence materials cannot be downloaded.
210236=The qualification certification application record does not exist
210237=This qualification certification application process record does not exist\!
210238=The qualification certification review configuration does not exist
210239=The review process has not been completed and the final evaluation cannot be made.
210240=This certification is not public and applications for certification are not currently supported.
210241=In this certification announcement, applications for certification are not currently supported.
210242=The certification announcement has ended and applications for certification are not currently supported.
210243=This certification has been applied for. Application for certification is not supported at the moment.
210244=The certification has not started, and application for certification is not supported at the moment.
210245=The certification has ended and applications for certification are not currently supported.
210246=This user has already registered for this major\!
210247=Tool category does not exist
210248=Tool parent class does not exist
210249=Tool classification has the same name
210250=The tool category is referenced and cannot be deleted
210251=The tool category has subcategories and cannot be deleted.
210252=The maximum level of tool classification is 5 levels
210253=The parent category cannot select itself
210254=There are subcategories and the parent category cannot be changed.
210255=Assessment tool does not exist
210256=Capability model does not exist
210257=Qualification does not exist
210258=Competency map does not exist
210259=The cumulative evaluation weight needs to be equal to 100
210260=There is a tool being used and cannot be deleted
210261=There is a tool with answer records and cannot be deleted.
210262=There are tools without questions. Please add questions before publishing.
210263=Not allowed to associate models without capabilities
210264=It is not allowed to associate job qualifications with incompetence
210265=Not allowed to associate maps without capabilities
210266=Drag and drop sorting sequence number is wrong
210267=Project category does not exist
210268=Start time cannot be after end time
210269=The assessment item does not exist
210270=There are already people participating in the project, and the evaluation tools cannot be modified.
210271=There must be at least one evaluation method
210272=Non-multiple-choice questions can only choose\: The corresponding score of the option result
210273=The assessment question does not exist
210274=There are used items and cannot be deleted
210275=There is a project without testees. Please add testees before publishing.
210276=The paper has been submitted and cannot be resubmitted.
210277=Ability scoring method filled in incorrectly
210278=The added users include existing tested users
210279=The added user contains a user that does not exist
210280=Parameter error, answer record does not exist
210281=No authority, there is currently an inactive evaluator
210282=The added user contains a user that does not exist
210283=The deleted users include users who have already started the evaluation
210284=The recorded ID of the user being tested is incorrect.
210285=Other evaluators are not allowed to be the same as the person being tested
210286=This assessment project does not allow self-assessment
210287=This assessment item does not allow for comments from superiors
210288=This assessment item does not allow for flat ratings
210289=This assessment item does not allow subordinates to rate
210290=The evaluation project notification event parameter transmission exception
210291=There are no users for this evaluation project to notify
210292=The associated tools have not been released yet
210293=Only categories under the same parent category can be sorted by dragging and dropping
210294=The question cannot have more than 7 options
210295=The associated tool has been published and cannot be deleted
210296=The added users include existing evaluation users
210297=The added users include users outside the jurisdiction
210298=The evaluation does not have associated capabilities configured, so the evaluation report cannot be generated.
210299=Failed to export evaluation report
2103=The organization already has users
210300=All testees have not yet generated evaluation reports, and all evaluation reports cannot be downloaded.
210301=The evaluation project has not been released yet
210302=The evaluation score ranges from 0-10, with at most one decimal place
210303=The evaluation score ranges from 0-5, with at most one decimal place
210304=The tool associated with the assessment item has no questions
210305=No corresponding qualification certification was found based on the ID\!
210306=No files to download
210307=Failed to obtain qualification certification\!
210308=This qualification certification has been announced and the announcement is not allowed to be modified\!
210309=Failed to obtain qualifications\!
210310=Tip\: There is currently no defense meeting and cannot be set\!
210311=The deadline for publicity must be greater than the end time of the defense\!
210312=It can only be announced after the defense meeting is over\!
210313=The deadline for public announcement has come and no cancellation is allowed\!
210327=The configuration of the qualification certification review process is abnormal, and the open certification fails\!
210330=The data has been referenced and cannot be deleted\!
210331=The certification system has been referenced and cannot be deleted\!
210332=The certified classification of the certification system is referenced and cannot be deleted\!
210333=Operation failed\!{0}
210334=The certified target already exists\!
210335=The certificate does not exist or has been deleted\!
210336=Authentication rule does not exist\!
210337=The parameter set is empty\!
210338=The qualification certification review configuration process does not exist
210339=Only one certificate holder can be imported at the same time, please wait for other imports to be completed.
210340=This data has already been added
210341=Uploading the zip image to update the external certificate failed, and the service was abnormal.
210342=Duplicate certificate category name
210343=The certification has been published and operations are prohibited.
210344=There is no initialization process record\!
210345=This qualification certification is being announced and no exceptions can be made\!
210346=Failed to save, {0} has existing registration and cannot remove the major.
210438=The authentication rule has been enabled and cannot be deleted\!
210439=The certificate name already exists and cannot be created\!
210440=The validity period cannot be empty\!
210441=The certificate has associated issuance rules and cannot be deleted\!
210442=The certification category has been referenced and cannot be disabled\!
210443=The registered users are not within the registration range\!
210444=The external training you signed up for does not exist\!
210445=The number of majors that this user can register for has reached the upper limit\!
2160=No permission to operate identity categories
2161=This identity category does not allow multiple levels of directories
2162=Identity category does not exist
2163=Identity directory does not exist
2164=The number of existing identity categories has reached the upper limit and no new ones are allowed.
2165=Identity category id is not allowed to be empty
2166=There is non-public data under this identity category and cannot be deleted.
2167=This identity category does not allow private identities
2168=This identity directory has subordinates and is not allowed to be deleted.
2169=This identity directory contains non-public data and is not allowed to be deleted.
2170=There is an associated user in the disabled identity, and the disablement failed.
2171=There are associated users in the deleted identity, and the deletion failed.
2172=This delivery plan does not exist
2173=Sync pool capacity is full
2174=The resource already exists in the sync pool
2175=The synchronization pool resource does not exist
2176=The synchronization pool resource is being calculated
2177=The delivery plan id cannot be empty
2178=The delivery components are empty and the delivery plan cannot be generated.
2179=Position/time identity category cannot modify multi-identity permissions
2180=This identity category does not allow users to have multiple identities
2181=The distributed plan shall not consist of more than 5,000 items.
2182=This resource has no associated distribution plan
2183=Position/time identity cannot be customized for user operation
2184=Multiple identity users already exist under the current identity category, unable to turn off 'User Multiple Identity'
22001=The shared reading does not exist\!
22002=Delete book id cannot be empty
22003=Deletion failed\!
22004=This experience does not exist\!
22005=This review does not exist
22006=The selected user ID set cannot be empty
22007={0} The number of tasks is 0 and cannot be activated\!
2201=Duplicate ID number
2202=Duplicate position number
2203=Identity does not exist
2204=The position does not exist
2205=This identity has been referenced by association, please cancel and try again
2206=The time interval of the current time identity coincides with the existing time identity and cannot be saved.
2207=The second month value needs to be greater than the first month value
2208=Working time cannot be empty
2209=Modification of admin permissions is prohibited
2210=User has logged out
22102=The number of tasks is zero and cannot be enabled.
2211=json format return result conversion error
2212=Illegal request entry type
2213=Code is invalid
2214=external users
2215=User is not bound
2216=Disable account and password login
2217=User unique identifier
2218=Login name must not contain Chinese characters
2219=Adjacent department nodes must be selected
2220=Custom department abbreviation cannot be empty
2221=The global department abbreviation configuration does not exist
2222=Please select the column/authority point related options
2223=Statistical analysis cannot be configured in the bottom menu
2224=It is forbidden to authorize the student role backend
2225=There cannot be sub-roles under the student
2226=There is already a user in the current role
2227=There are sub-roles under the current role
2228=Permission points should be hung in columns or other permission points
2229=The superior of the column cannot be the authority point
2230=Columns and authority points cannot be at the same level
2231=The column level cannot exceed level 3
2232=The level of authority points cannot exceed level 2
2233=There are other columns under this column, and the type cannot be modified.
2234=Superior authority does not exist
2235=There is a route for the upper-level authority point, and no sub-authority points can be added.
2236=id cannot be empty
2237=This system column does not exist
2238=The third-party link encryption public key is not configured
2239=Third-party link encryption error
2240=My column cannot be placed in my application
2241=Found that the column cannot be placed in the middle menu
2242=My study cannot configure IDP
2243=My study cannot configure my tasks
2244=My application cannot configure IDP
2245=My app cannot configure my tasks
2246=System columns are referenced
2247=The bottom menu must contain a discovery section
2248=System parameter does not exist
2249=The user's boss cannot be himself/herself
2250=Line code repeats
2251=time code duplication
2252=Duplicate rank codes
2253=Duplicate coding at manager level
2254=Topic does not exist
2255=Theme element does not exist
2256=The default theme cannot be deleted
2257=The bottom menu cannot configure exam competitions
2258=User position does not exist
2259=Duplicate ability codes
2260=Duplicate capability model coding
2261=The capacity encoding length is limited to 80 characters and Chinese characters are not allowed.
2262=The capability is referenced and cannot be deleted
2263=Capability model does not exist
2264=The basic information page of the capability import template is empty
2265=My column cannot be placed in My Study
2266=Authorization verification failed, please check parameters
2267=The superior of the organization cannot be oneself
2268=Encoding requires 1 to 36 characters
2269=Coding duplication
2270=Duplicate name
2271=Already referenced by association, please cancel and try again.
2272=Contains the first-level post architecture and is not allowed to be deleted.
2273=There is data under the category that cannot be deleted.
2274=The time span cannot exceed one month
2275=Qualifications must enable one content display and are not allowed to turn off all
2276=Organization level exceeds limit
2277=Please check whether the SMS verification code template matches. The template variable needs to be ${code}
2278=At least one transcoding configuration needs to be retained
2279=The middle menu cannot configure my agent
2280=My study cannot configure my agent
2281=The bottom menu cannot configure my proxy
2282=The third-party application does not exist. If you need to use a third-party account to log in, please contact the administrator to configure it in the synchronization configuration.
2283=Failed to delete user
2284=Configuration information error
2285=DingTalk getting access token error
2286=In the capability model, a capability can only be added once
2287=There are mountable positions in the current structure
2288=Under the same ability dictionary, ability levels cannot have the same name.
2289=Authorization verification failed, please check parameters
2290=The check-in QR code refresh interval cannot be less than 30 seconds.
2291=The login validity period is too short, minimum 600 seconds
2292=Please enter a correct and reasonable number, between 600 seconds and 5184000 seconds (60 days)
2293=The login validity period is too long, the longest is 5184000 seconds (60 days)
2294=The currently logged in user has been disabled
2295=The current logged in user does not exist
2296=cron expression configuration format error
2297=The operation was successful.<br/> It is expected that {0} will be refreshed and the learning resources will be released.
2298=The position system data cannot be empty\!
2299=Users cannot be added repeatedly
2300=The operation log function is not enabled on the current platform, and you cannot view relevant records. If you need to use this function, please contact the platform administrator to activate it.
2301=Required fields cannot be empty
2302=The number of enterprise-micro connection cannot be less than the authorized number
2303=Configuration data exception
2304=This mobile phone number is not bound to any account\!
2305=Encrypted data is abnormal\!
2306=The request is too frequent, please try again later
2307=Duoqiwei does not support application development
2308=Enumeration value is incorrect
2309=The string after base64 encoding of the face image cannot be empty\! \!
2310=The face recognition registration interface service request is abnormal, please contact the administrator\!
2311=Requesting the face recognition registration interface timed out\!
2312=The request face comparison interface is abnormal\!
2313=Requesting the face comparison interface timed out\!
2314=The face comparison does not match\!
2315=Exception in request to obtain token\!
2316=User ID cannot be empty\!
2317=The user interface for requesting face deletion is abnormal\!
2318=Mount the position structure, the position is required and cannot be "No"
2319=There is a mountable position at the subordinate level. The position must be filled in and cannot be "No"
2320=A mountable position structure must exist
2321=Enterprise type is empty
2322=Parameter encoding does not exist
2323=The superior authority cannot be oneself
2324=Route/ButtonId Duplicate
2325=The lower level has content and the current level is not allowed to be deleted.
2326=The current permission does not exist
2327=Duplicate permission codes
2328=There are sub-roles and users under the current role
2329=The upper level permissions of the column cannot be modified.
2330=This permission point is not a leaf node, and upper-level permissions cannot be modified.
2331=The data in the uploaded Excel is empty
2332=No permission or menu not found\!
2333=There is a problem with the routing data and importing is prohibited.
2334=Wrong resource type
2335=Email cannot be empty\!
2336=This email is not bound to any account\!
2337=Decryption data is abnormal\!
2338=The link has expired, more than 2 hours\!
2339=The link has expired. You have already changed your password\!
2340=User key invalid
2341=Please enter the correct engineering mode key
2342=This user is not a system administrator and cannot operate
2343={0} cannot be longer than {1} digits
2344={0} format is incorrect
2345={0} cannot be empty
2346=The current delivery range is being refreshed, please try again after {0}
2347=org_code_not_found\:{0}
2348=System parameter configuration enumeration does not exist
2349=The field name "{0}" is repeated, please re-enter it.
2350=The organization already has a plan
2351=Encoding requires 2 to 36 characters
2352=Account cannot be modified
2353=Department list is empty
2354=Abnormal birthday
2355=The system user limit has been reached. The current number of system users cannot exceed {0}.
2356=Please enter password
2357=This account already exists
2358=The current mobile phone number is already in use by\: {0}
2359=The ID number has been used, using the account number\: {0}
2360=ID number is incorrect
2361=Import error, please check whether the data is correct
2362=Import error\: {0}
2363=The third-party interface call failed, error code\: {0}, error message\: {1}
2364=The imported user does not exist in the user list {0}
2400=Only one user can be imported at the same time, please wait for other imports to be completed.
24001=This channel does not exist
24002=Incorrect push type
24003=The push template does not exist
24004=The push event does not exist
24005=APP does not support push links
24006=Failed to obtain third-party application information
24007=SMS sending failed\: {0}
24008={0}
2403=The interface is not authorized, please contact management
2501=Role number already exists
2502=The character's superior cannot be himself
26001=I can only like it once
26002=This comment does not exist\!
26003=The resource does not exist\!
26004=The resource is not published\!
26005=Resources do not enable comments
2601=Some of the selected organizations no longer exist, please select again.
2602=This organization does not exist, please select again
2608=Parameter error
2701=Bottom menu can have up to 5 items
2702=This feedback does not exist
2703=This feedback has been processed
28001=Advertisement image cannot be empty
28002=This ad does not exist
28005=Toutiao resource id cannot be empty
28006=Wrong headline type
28007=This headline does not exist
28008=External link url cannot be empty
28009=Toutiao pictures (PC) and Toutiao pictures (APP) must both upload pictures or be empty.
28013=Contains deleted or unpublished resources, please check and try again
28014=Upload at least one headline picture (including PC and mobile H5) and headline notification
28015=Notification mode cannot be empty
28016=Add vote failed
28017=Votes already exist
28018=Modification vote failed
28019=vote does not exist
28020=Delete vote failed
28021=Failed to change publishing status
28022=Failed to add voting content
28023=Failed to modify voting content
28024=Failed to delete voting content
28025=The voting number has exceeded the maximum 999, and voting options cannot be added.
28026=The voting content does not exist
28027=Voting has ended
28028=Voted today
28029=There are zero points redeemable
28030=No more votes
28031=The headline notification scene cannot be empty
28032=Votes not posted
28033=Voting has ended
28034=Voting has not started
28035=vote does not exist
28101=The check-in does not exist
28102=This check-in is not published
28103=Check-in is not published or unavailable
28104=Not within the check-in time range
28105=Already signed in
28106=Wrong sign-in type
28107=Check-in time range cannot be empty
28108=Sign-ins associated with course tasks do not support deletion.
28109=Event check-in does not exist
28110=Event check-in is not published
28111=Please operate within the check-in time range (check-in time\: {0})
28112=Event check-in has ended
28113=Not in the check-in address range
28114=The sign-in user does not exist
28115=The maximum number of votes that can be cast cannot exceed the number of voting days
28116=This check-in does not support generating dynamic QR codes
28117=This dynamic QR code does not exist
28118=The start time or end time is empty
28119=Voting number already exists
2900=The identity added to the user contains an invalid identity
29101=The evaluation has been completed and cannot be resubmitted.
29102=Evaluation does not exist
29103=Assessment record does not exist
29201=The parameter documentId cannot be empty
29301=Evaluation template does not exist
29302=The template file cannot be empty
29303=Please download the correct template to import
30001=This training program does not exist
30002=This announcement does not exist
30003=The file does not exist
30004=This supplier does not exist
30005=This activity does not exist
30006=Activity creation type error
30007=The supplier information does not exist
30008=The event name is already in use
30009=The training activities corresponding to the class do not exist
3001=The delivery range or the import file is empty
30010=Registration does not exist
30012=Registration has not started
30013=Registration has ended
30014=Registration does not exist
30015=This training program does not allow cancellation of registration
30016=Exceeded the registration limit
30017=The training project progress does not exist
30018=Reference creation, resource ID cannot be empty
30019=The training project has been published, please unpublish it and then delete it\!
3002=Failed to save, no questions imported\!
30020=The flyer has been published, please unpublish it and delete it\!
30021=The activity has been enabled, please deactivate it and delete it\!
30022=Contains people who have completed the approval process, please select again
30023=The prerequisite activity "%S" has not been completed, please complete it before proceeding with the activity
30024=The prerequisite activity "%S" has not been completed. Please complete %S days before starting the activity.
30026=This activity has been associated with the carousel image, please delete the carousel image {0} first
30027=Please deactivate the activity and try clearing it again\!
30028=You do not have permission to enter, please contact the administrator
30029=The file is being transcoded, please wait\!
3003=Failed to save, no test paper information\!
30030=The training program has not been released\!
30031=The training program has not started\!
30032=The training program has expired\!
30033=This check-in is not part of the training project activity/schedule
30034=There are also lecturers associated with this supplier and cannot be deleted.
3004=Exercise does not exist
3005=Exercise is not published
3006=Exam does not exist or is not published
3007=Exercise is not published
3008=No permission to access this exam
3009=The exam has not started yet
3010=The number of exams has been exhausted
30100=Learning map execution does not exist
30101=Study map does not exist
30102=A learning map with this configuration already exists
30103=This configuration will cause inverse selection in the learning map, please reconfigure it.
30104=There is currently a job type learning map for this position.
30105=Position type learning address, whether to transfer to another position, cannot be empty
30106=The learning map is configured incorrectly
30107=This type of learning map, application/development cannot be empty
30108=Contains resources referenced by learning map execution and cannot be deleted
30109=The learning map stage does not exist
3011=There is already another exam in progress. Please submit your papers before taking the exam again.
30110=Contains stages referenced by learning map activities and cannot be deleted
30111=Learning map activity type does not exist
30112=The learning map activity does not exist
30113=Learning map copy failed, please select again
30114=This learning map is not published, please contact the administrator
30115=The activities under the learning map are empty and are not allowed to be published.
30116=It is currently not within the effective activity period of the learning map
30117=Competency map major cannot be empty
30118=Binding map standard cannot be empty
30119=Applicable positions cannot be empty
3012=The exam has ended and the paper will be handed in automatically
30120=The selection ability is already in the map
30121=The version of the map stage bound to this activity is inconsistent with the current version of the map.
30122=This activity is required/locked and cannot be deleted
30123=The selected ability has been locked in the binding standard and cannot be deleted.
30124=There must be an enabled version in the map and cannot be deactivated.
30125=The cover image of the learning map cannot be empty
30126=This activity comes from ability, and its source is locked and cannot be modified to select compulsory courses.
30127=This activity comes from the ability and cannot be deleted
30128=Modification of non-competency standard maps is prohibited. Activities must be selected.
30129=Modification of non-competency standard maps is prohibited and the activity is locked.
3013=Not within the exam time range
30130=There is already a learning map with this name
30131=Co-organizers add duplicates
30132=Class does not exist
3014=Illegal submission
3015=The paper has not been submitted and the answers and analysis cannot be viewed
3016=Exam time cannot be empty
3017=Exam duration cannot be zero
3018=The passing score is wrong, please modify it. Range\: 0<passing score<total score
3019=,The imported user does not exist </br>
3020=This exercise/examination is imported in paper format and cannot be previewed.
3021=There can only be one of each question type\!
3022=The volume plan no longer exists
30222=The version of the activity is inconsistent with the version of the bound map stage.
30223=The planning parameters are filled in incorrectly
30224=There are no students who meet the requirements in this training mode who can arrange the plan.
30225=Training project id cannot be empty
30226=The leaflet has been quoted and cannot be deleted\!
30227=System-distributed items are not allowed to be deleted.
30228=Activity does not exist
3023={0}Volume formation has not been completed, please try again later.
30231=The imported data is empty\!
30323=There is currently an activity for this resource, and further additions are prohibited.
3033=The question does not exist
3034=This test paper does not exist in the test paper library
3035=Contains referenced test papers, please cancel and try again\!
3036=Contains enabled test papers, please cancel and try again\!
3037=Please select a resource first\!
3038=The test question bank/exercise bank no longer exists
3039=Contains referenced practice libraries, please cancel and try again\!
30391=Contains referenced test question bank, please cancel and try again\!
3040=Contains an enabled exercise library, please cancel and try again\!
30401=Contains an enabled test question bank, please cancel and try again\!
3041=Type does not match resource
3042=Please download the correct file to import
30427=There are activities under this stage and deletion is not allowed\!
30428=The event has not started and the time range is {0}~{1}\!
30429=The event has ended, the time range is {0}~{1}\!
3043=The last exam you took no longer exists
30433=Activity is not enabled
3044=Category does not exist
3045=Enabled categories cannot be deleted, please cancel and try again\!
3046=The source of the exam questions is the examination plan and cannot be viewed.
3047=The paper has not been submitted, please complete the exam as soon as possible
3048=The resource is not enabled and cannot be referenced
3049=The paper has been changed and the candidate has re-entered the exam. Please wait for the candidate to hand in the paper.
3050=The last exam is being marked, please wait for the teacher to mark it and take it again\!
3051=The paper has been submitted and cannot be resubmitted.
3060=Unable to meet volume requirements, please choose again
3061=Category is not available, please select again
3062=The volume plan is enabled and cannot be deleted
3063=Add question/update question options without reference answer
3064=Start time cannot be later than end time
3065=The video file size exceeds the limit of 200M, please upload again
3066=Includes candidates who are being marked/passed, please select again
3067=The volume plan has been used and cannot be deleted or modified.
3068=You are not the grader of the current exam and cannot make corrections.
3069=When setting retakes, the number of retakes must be set greater than 0
3070=There is an exam that references this paper composition plan and cannot be edited.
3071=This question is not a subjective question and the answer details list cannot be obtained.
3072=Question {0}, the correct answer to the question cannot be empty
3073=Question option format is incorrect
3074=The order of questions cannot be repeated
3075=No retake is allowed after passing the exam
3076=This exam does not allow retakes
3077=The current category or subcategory cannot be used as a superior category
3078=There are test papers or lower-level categories under this category and deletion is not allowed.
3079=There are test questions or lower-level categories under this category and deletion is not allowed.
3080=The test question score cannot be 0
3081=At least one question type needs to be set
3082=The usage authorization scope and classification management unit cannot be empty.
3083=The classification management unit exceeds the scope of use authorization
3084=The exam already has answer records and modifications are not allowed.
3085=The number of questions is inaccurate
3086=The exam competition does not exist or is not published
3087=No permission to access this exam competition
3088=There are no contests currently running for the current user
3089=The answer mode does not match the person mode
3090=No contests in progress
30901=The resource does not exist in the database
30902=There are no cited topics in this resource
3091=Wrong contest invitation code
3092=The competition has been dismissed or ended
3093=The competition is full
3094=The user has canceled the invitation
3095=The competition does not exist
3096=You are not the creator of this competition and do not have permission to operate.
3097=The competition has started PK
3098=Person pattern mismatch
3099=Exceeded the maximum number of people invited at one time
3100=No one is participating in this competition
31001=The original picture does not exist
31002=The target image does not exist
31003=This photo does not allow editing and sorting
31004=Data already exists and cannot be initialized.
31005=The modified configuration does not exist
31006=Duplicate medal name
31007=The medal has been associated with a person and cannot be deleted.
3101=No permission to cancel invitation
3102=The current user has ended this competition
3103=This competition is not over yet and there is no ranking result yet.
3104=Someone is not prepared for this competition
3105={0} is already in the room
3106=You are already in the room
3107=The two teams have different numbers and cannot start the game.
3108=Cannot remove people who are not in the room or are being invited
3109=He is not the captain of this competition and has no authority to operate.
3110=The exam competition has not started or has ended. The competition cannot be initiated.
3111=At least two people must participate in each group
3112=you are not in the room
3113=This user is not participating in this contest
3114=Question {0}, the question score cannot be empty
3115=Question {0}, the question score range is 0-999 and only supports two decimal places.
3116=Question {0}, difficulty level cannot be empty
3117=The maximum number of questions uploaded each time is 500
3118=Invitation canceled
3119=The team is full or refused to participate
3120=The invitee did not respond
3121=Already joined
3122=No permission to access this exercise
3123=It is currently in publishing status and cannot edit the question. Please cancel publishing and try again.
3124=Question {0}, the title cannot be empty
3125=The title is too long and should be less than 300 characters
3126=The number of exam papers exceeds the maximum\: {0}
3127=Examination failed
3128=Failed to generate exam plan
3129=The questions selected by this student are all of the system marking type, so there is no need to change the questions.
3130=Question {0}, the test to break through can only include single-choice questions, multiple-choice questions and true-false questions
3131=Failed to save the exam, please try again
3132=Can only contain multiple choice questions and true/false questions
3133=The question options allow up to 8 options, please modify it and try importing again\!
3134=Single-choice, multiple-choice, and judgment questions require at least 2 options\!
3135=The total number of questions in the currently referenced test paper is 0. Please add questions before submitting.
3136=Can only contain single-choice questions, multiple-choice questions and true-false questions
3137=The question bank test plan matrix data cannot be empty\!
3138=Exam records do not exist\!
3139=The operation failed, please check the data and try again\!
3140=Time range cannot exceed {0} days
3141=The question option format in line {0} is incorrect\!
3142=The question option format in line {0} is incorrect and must start with A. B. a. b. format\!
3143={0}The passing score is wrong, please modify it. Range\: 0<passing score<total score
3144=The imported test paper file cannot be empty
3145=The format of the question options is wrong, the length of the options cannot be greater than 300
3146=Question {0}, the title is too long and should be less than 300 characters.
3147=Question {0}, the standard answer is not among the options
3176=Contains referenced practice bank/exam question bank, please cancel and try again\!
3177=Contains enabled practice bank/question bank, please cancel and try again\!
3178=Question type cannot be empty
3200001=Plan does not exist
3200002=The selected plan id cannot be empty when deleting
3200011=Plan list does not exist
3200021=Plan category does not exist
3200031=The plan category review serial number cannot be empty
3200032=The data already exists and the form cannot be deleted.
3200033=There is no field data and the form cannot be published.
3200034=It has been submitted for review and cannot be submitted for review again.
3200035=Inventory data has been added under this category, and the inventory content template cannot be edited.
3200036=({0}) has been approved and cannot be deleted.
3200037=The plan list has been activated and cannot be deleted.
3200038=No reviewers
3200039=Template format is wrong
3200040={0} cannot be longer than {1} digits
3200041={0} format is incorrect
3200042={0} cannot be empty
3200043=The field name "{0}" is repeated, please re-enter it.
3200611=The selected plan list cannot be empty
33001=The field name {0} is repeated, please re-enter it.
34001=Poster images cannot be empty
34002=Poster background color cannot be empty
34003=Poster text color cannot be empty
34004=Poster sharing not configured
3999=You are not in the PK room
40001=Published and not allowed to be deleted
40002=Game does not exist
40003=The task exists in the level and cannot be deleted.
40004=Published announcements cannot be modified
40005=Announcement does not exist
40006=The announcement has been published and cannot be deleted.
40007=This game has no missions and cannot be published
40008=Task does not exist
40009=This game has a participation record and the mission cannot be deleted.
4001=The current user does not have a lecturer status and cannot access
40010=Team name already exists
40011=Team does not exist
40012=Level does not exist
40013=The task record does not exist
40014=Unable to delete task records generated by the system
40015=Please operate within the job submission time range (job submission time\: {0})
40016=No access
40017=The game has not activated the application
40018=There are currently no tasks in the current level
40019=Only tasks of job type are allowed
4002=Lecturer category does not exist
40021=Missing public parameters
40022=The word count of the assignment does not meet the requirements
40023=The job has been approved and cannot be modified or approved.
40024=Parameter error
40025=The job has not been submitted and cannot be approved.
40026=Rating cannot be empty
40027=Rating does not meet requirements
40028=Assignment does not support grading
40029=Game introduction cannot be empty
4003=Lecturer level does not exist
40030=Please operate within the game time range (game time\: {0})
40031=Game code duplication
40032=Save failed
40033=Not the first level, the mileage setting is wrong.
40034=There are users under the team
40035=Game coding error
40036=Please join the team before entering the game
40037=Announcement does not exist
40038=Parameter error, please reload the game\!
40039=Template id cannot be empty
4004=Repeated grade numbers under the same category
40040=This game does not have this quest or assignment
40041=This game does not exist or published
4005=This user is already a lecturer and cannot be added or applied again\!
4006=The selected instructor status does not need to be changed\!
4007=Please enter parameters for enabled configuration
4008=Please enable at least one rule
4009=Rule configuration information does not exist
401=Unauthorized request
4010=Not allowed to like yourself
4011=Not allowed to like yourself
40114=Duplicate serial numbers
40115=The user ID selected for reminder cannot be empty.
40116=This game style template does not exist
4012=There are levels under the selected lecturer category and cannot be deleted.
4013=This user has already applied for a lecturer and cannot apply again\!
4014=Lecturer details do not exist
4015=Rule information does not exist
4016=The selected category has an enabled level and cannot be disabled.
4017=The selected level has been associated with the instructor and cannot be deleted.
4018=The selected level is referenced by the instructor and cannot be disabled.
4019=Selected Instructor Instructor level is disabled
4020=Instructor classification for selected level is disabled
4021=generateCode fail, the code must be able to get a number\!
4022=The project instructor record already exists and cannot be added again.
4023=Contains instructors who have not been released from the library and cannot be deleted.
4024=The lecturer has related business data and cannot be deleted.
4025=Since the related courses have been deleted, editing is not supported. Sorry, please understand.
4030=The knowledge base material primary key cannot be empty
4031=Material name cannot be empty
4032=Knowledge base material file path cannot be empty
4033=The display order cannot be empty
4034=Knowledge base category cannot be empty
4035=Material primary key cannot be empty
4036=Data primary key cannot be empty
4037=The data primary key string of the operation cannot be empty
4038=Enable/disable status parameter error
4039=Classification category is invalid
4040=Category name cannot be empty
4041=The audit data primary key cannot be empty
4042=Review status is invalid
4043=The reason for rejection cannot be empty when reviewing the rejection.
4044=The knowledge base material primary key set cannot be empty
4045=Invalid disabled/enabled status value
4046=Parameter\: Invalid content rule
4047=This encoding already exists
4048=[Early warning rule parameter {1} of the {0} configuration rule cannot be empty]
4049={0} configuration rule [Rule parameter 1 cannot be empty]
4050=Activated rules cannot be deleted directly.
4051=The selected options include audited teaching records
4052=The following instructors cannot be enabled and their user accounts have been deleted\: {0}
4053=The category is in use and cannot be deleted
409=Duplicate submissions are not allowed
41001=Non-working day records do not exist
42001=Configuration rule does not exist
4401=Resource does not exist
4501=The category is already in use and cannot be disabled
4502=The category has been enabled and cannot be deleted
4601=Knowledge base material is enabled and cannot be deleted
4701=The lecturer has been reserved and cannot be canceled
4801=The imported lecturer login account is duplicated, please modify it and re-import it.
4802=The imported lecturer login account is invalid, please modify it and import it again.
4803=The imported phone number is invalid, please modify it and re-import it.
4804=The imported email address is invalid, please modify it and import it again.
4805=The imported name is wrong, please modify it and re-import it.
4901=The lecturer’s teaching record does not exist
505=Type conversion failed
506=client abort exception
510=Remote service error
6001=The level must be less than level 5\!
6002=The category has been referenced and cannot be deleted.
6003=Tag is already used
6004=This course does not exist
6005=No permission to view this course
6006=This category already exists under the same level
6007=This category does not exist
6008=The category has been referenced and the status cannot be modified.
6009=This courseware does not exist
6010=Course comments do not exist
6011=The selected distribution range exceeds the upper level category\!
6012={0} The number of courseware is 0. Please add courseware before publishing\!
6013=Delete tags include tags created with course classification, this part cannot be deleted\!
6014=Tag does not exist or does not support selection\!
6015=Saved successfully, no need to submit again\!
6016=Saved successfully, the resource does not exist\!
6017=You have not selected this tag and cannot delete it\!
6018=Contains enabled categories, please cancel and try again\!
6019=Contains enabled courseware data, please cancel and try again\!
6020=Contains enabled course tags, please cancel and try again\!
6021=Course chapters must be positive integers\!
6022=This course is not published\!
6023=The courseware has not been transcoded\!
6024=No valid courseware can be associated to create
6025=The courseware is associated with the chapter and cannot be deleted.
6026=The note content contains the following sensitive words and cannot be submitted\: {0}
6027=This note does not exist
6028=No permission to delete this note
6029=Note content cannot be empty
6030=Contains subcategories and cannot be deleted
6031=The superior category cannot be itself
6032=Time type parameter does not match
6033=The time point format of the question is wrong
6034=The time point format of the question cannot be 00\:00\:00
6035=This course is already in the study plan
6036=It is forbidden to recommend courses to yourself
6037=Recommendation failed, the person you recommended cannot access the current course
6038=This study plan does not exist
6039=Study plans for courses recommended by superiors cannot be deleted.
6040=Function is not enabled, please contact the administrator
6041=Courseware type is not supported yet
6042=The file is being transcoded, please wait.
6043=Chapter id cannot be empty
6044=Courseware type not supported\: {0}
6045=The course code ({0}) is duplicated, please try again or contact the system administrator
6046=Courseware format is incorrect
6047=The associated exam has ended
6048=The type of courseware whose source is student courseware does not support editing.
6049=The exam information cannot be viewed if the courseware is not completed.
6050=The courseware was not converted successfully and cannot be quoted.
6121=Chapter name cannot be empty\!
7001=Illegal submission
7002=Published surveys cannot be deleted, please select again.
7003=Research does not exist
7004=The imported research database cannot be disabled, please select again.
7005=The enabled research library cannot be deleted, please select again.
7006=Please upload the survey questionnaire first
7007=The enabled research database categories cannot be deleted, please select again.
7008=The research database category does not exist
7009=This category research database already exists and cannot be disabled. Please select again.
701=Unsupported operation
7011=This survey does not allow viewing of results
7012=You have already participated in the survey and cannot participate again
7013=This question is not a subjective question and the answer details list cannot be obtained.
7014=The task has expired (Research time\: {0}-{1})
7015=The task has not started yet (Research time\: {0}-{1})
7016=Research title cannot exceed 500 characters
7017=Research question categories cannot exceed 36 characters
7018=The selected user ID set cannot be empty
7019=Survey title cannot be empty
702=Contains published resources and cannot be deleted
7020=Research category cannot be empty
7021={0}
703=No access
704=Contains enabled resources and cannot be deleted
705=Initialization resources cannot be deleted
706=Initialization resources cannot be disabled
707=Containing referenced resources cannot be deleted
708=Containing referenced resources cannot be disabled
709=No resources available for download
710=Method execution exception
711={0}
712=The call failed, please contact the administrator, {0}
8001=Section name already exists
8002=The section has been referenced and cannot be disabled
8003=You cannot select yourself as the superior parent class\!
8004=The parent directory does not exist\!
8005=The directory you want to be ranked first does not exist\!
8006=This directory has sub-directories and cannot be deleted directly\!
8007=There are resources in this directory and cannot be deleted directly\!
8901=The directory does not exist
8902=The directory already exists under the same resource and at the same level
4056=The data of the same lecturer+teaching time+teaching project+teaching courseware already exists
4057=The data for the same lecturer: {0}+teaching time: {1} already exists
4058=The instructor certification course data already exists, please do not add it again
2366=Poster sharing promotional text cannot exceed {0} characters
2372=The overall brand name for poster sharing cannot exceed {0} characters
17051=recruiting assistant already exist and cannot be added again
17052=There is a posted recruitment, and the material in it cannot be deleted
17053=At least one mandatory material rule is required
210356=The selected user is not in the project and cannot be added with a certificate
210359=Each qualification can only manage one certificate. Please do not add again.
210360=The same resource cannot be associated multiple times within the same certificate!
16040=The location cannot exceed 80 words
3151=You can enter a maximum of 5 knowledge points
3150=Each knowledge point should not exceed 10 characters
3148=Line {0}, each knowledge point cannot exceed 10 characters in length!
17127=The default password is incorrectly configured. Please re-enter it
30234=The training program has not been initialized, please check it later
30235=During project initialization, no release is allowed
17130=The number of files the user has exceeded the limit.
30237=This activity has been associated with the flyer, please delete the flyer {0}
28120=The reference repository type cannot be empty
17128=The corresponding review process has been configured for the current course classification
17129=There are pending review applications in the current process that cannot be deleted
6081=The course already has a review process in place
6082=This course is not under review
6083=This course category has a defined review process and cannot be deleted
6084=The course classification has a defined review process, and the review process is not yet completed
6085=This course does not have a classification set
6086=The course review did not pass, the course has been cancelled
6087=This course category has a defined review process and cannot be disabled
17131=The classification of audit process management cannot be modified
17134=The tag has already been used and cannot be disabled
17135=Contains enabled tags, please cancel and try again
17136=Contains referenced tags, cannot be deleted, please cancel and try again
17137=There is no multi-level system label classification
17138=The system label classification name is not duplicated
17139=Up to 20 system tag classifications can be added
17140=Only one system label can be imported at the same time, please wait for other imports to complete
2376=Cannot move the category to its own child category.
2377=Only same-level categories can be dragged and sorted with each other.
16041=The length of the application statement cannot exceed 500
2031=The number of selected statistical cards should not exceed 4
1484=Confidential documents cannot be processed, please upload the resources again