com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_1.name=Home page background image
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_2.name=Title background (login page)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_3.name=title background
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_4.name=Home page background silhouette
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_5.name=Other page background images
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_6.name=Cancel button
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_7.name=I got it (Complete the level)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_8.name=closure
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_9.name=I got it (upload the number of steps)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_10.name=OK
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_11.name=Refresh steps
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_12.name=go read the story
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_13.name=OK (first login prompt)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_14.name=Upload now
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_15.name=Today's mileage
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_16.name=my button
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_17.name=Leaderboard button
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_18.name=Activity description
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_19.name=Upload yesterday's exercise button
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_20.name=Study area button
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_21.name=Progress bar villain
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_22.name=Flag (not unlocked)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_23.name=Flag (unlocked)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_24.name=Background (Learning Area)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_25.name=Background ("My" page)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_26.name=Other page background silhouettes
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_27.name=Location connection diagram 10 (unfinished)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_28.name=Location connection diagram 10 (completed)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_29.name=Location connection diagram 11 (unfinished)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_30.name=Location connection diagram 11 (completed)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_31.name=Location connection diagram 1 (unfinished)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_32.name=Location connection diagram 1 (completed)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_33.name=Location connection diagram 2 (unfinished)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_34.name=Location connection diagram 2 (completed)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_35.name=Location connection diagram 3 (unfinished)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_36.name=Location connection diagram 3 (completed)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_37.name=Location connection diagram 4 (unfinished)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_38.name=Location connection diagram 4 (completed)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_39.name=Location connection diagram 5 (unfinished)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_40.name=Location connection diagram 5 (completed)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_41.name=Location connection diagram 6 (unfinished)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_42.name=Location connection diagram 6 (completed)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_43.name=Location connection diagram 7 (unfinished)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_44.name=Location connection diagram 7 (completed)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_45.name=Location connection diagram 8 (unfinished)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_46.name=Location connection diagram 8 (completed)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_47.name=Location connection diagram 9 (unfinished)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_48.name=Location connection diagram 9 (completed)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_49.name=The level has not unlocked the halo
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_50.name=Level unlock halo
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_51.name=Level unlock flag
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_52.name=Pop-up header background
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_53.name=Level completion background
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_54.name=Story pop-up background
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_55.name=Prompt pop-up window background)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_56.name=description title banner)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_57.name=My page title background
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_58.name=My page card background
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_59.name=Study area title
com.wunding.learn.common.enums.other.TradeTypeEnum.PROJECT.value=learning project
com.wunding.learn.common.enums.other.TradeTypeEnum.COURSE.value=course
com.wunding.learn.common.enums.other.TradeTypeEnum.TRAIN.value=training projects
com.wunding.learn.common.enums.other.TradeTypeEnum.TRAIN_APP_FILE.value=training information files
com.wunding.learn.common.enums.other.TradeTypeEnum.TRAIN_APP_EXAMPLE.value=Training case files
com.wunding.learn.common.enums.exam.CategoryOrgTypeEnum.VIEWLIMITAREA.name=Use authorization scope
com.wunding.learn.common.enums.exam.CategoryOrgTypeEnum.MANAGELIMITAREA.name=Classified management unit
com.wunding.learn.common.enums.exam.ExamQuestionTypeEnum.QUESTION_TYPE_RADIO.name=Single choice
com.wunding.learn.common.enums.exam.ExamQuestionTypeEnum.QUESTION_TYPE_MULTI_SELECT.name=Multiple choice
com.wunding.learn.common.enums.exam.ExamQuestionTypeEnum.QUESTION_TYPE_CLOZE.name=fill in the blank
com.wunding.learn.common.enums.exam.ExamQuestionTypeEnum.QUESTION_TYPE_JUDGEMENT.name=judge
com.wunding.learn.common.enums.exam.ExamQuestionTypeEnum.QUESTION_TYPE_QA.name=Q&A
com.wunding.learn.common.enums.other.LayoutStyleEnums.ROLL_NOTICE.name=scrolling display layout
com.wunding.learn.common.enums.other.LayoutStyleEnums.LIST_NOTICE.name=List display layout
com.wunding.learn.common.enums.other.LayoutStyleEnums.ROLL_CASE.name=Horizontal scroll layout
com.wunding.learn.common.enums.other.LayoutStyleEnums.LIST_CASE.name=List display layout
com.wunding.learn.common.enums.other.LayoutStyleEnums.VIP_ROLL_CASE.name=Horizontal scrolling layout (including member mark)
com.wunding.learn.common.enums.other.LayoutStyleEnums.VIP_LIST_CASE.name=List display format (including member mark)
com.wunding.learn.common.enums.other.LayoutStyleEnums.LIST_DATUM.name=List display layout
com.wunding.learn.common.enums.other.LayoutStyleEnums.VIP_LIST_DATUM.name=List display format (including member mark)
com.wunding.learn.common.enums.other.LayoutStyleEnums.DETAIL_LIST_TOPIC.name=Detailed list layout
com.wunding.learn.common.enums.other.LayoutStyleEnums.LIST_TOPIC.name=Simple list layout
com.wunding.learn.common.enums.other.LayoutStyleEnums.CROSS_LIST_COURSE.name=Horizontal scroll layout
com.wunding.learn.common.enums.other.LayoutStyleEnums.LIST_COURSE.name=List display layout
com.wunding.learn.common.enums.other.LayoutStyleEnums.VIP_CROSS_LIST_COURSE.name=Horizontal scrolling layout (including member mark)
com.wunding.learn.common.enums.other.LayoutStyleEnums.VIP_LIST_COURSE.name=List display format (including member mark)
com.wunding.learn.common.enums.other.LayoutStyleEnums.LIST_SCHEDULE.name=List display layout
com.wunding.learn.common.enums.other.LayoutStyleEnums.VIP_LIST_SCHEDULE.name=List display format (including member mark)
com.wunding.learn.common.enums.other.LayoutStyleEnums.ROLL_PHOTO.name=Horizontal scroll layout
com.wunding.learn.common.enums.other.LayoutStyleEnums.BIG_PHOTO.name=Large picture layout
com.wunding.learn.common.enums.other.LayoutStyleEnums.LIST_CLASS.name=List display layout
com.wunding.learn.common.enums.other.LayoutStyleEnums.COVER_CLASS.name=Pure cover layout
com.wunding.learn.common.enums.other.LayoutStyleEnums.VIP_LIST_CLASS.name=List display format (including member mark)
com.wunding.learn.common.enums.other.LayoutStyleEnums.VIP_COVER_CLASS.name=Pure cover layout (including member mark)
com.wunding.learn.common.enums.other.LayoutStyleEnums.BIG_LIVE.name=Large picture layout
com.wunding.learn.common.enums.other.LayoutStyleEnums.LIST_LIVE.name=List display layout
com.wunding.learn.common.enums.other.LayoutStyleEnums.ROLL_LECTURER.name=Horizontal scroll layout
com.wunding.learn.common.enums.other.LayoutStyleEnums.LIST_LECTURER.name=List display layout
com.wunding.learn.common.enums.other.LayoutStyleEnums.LIST_SPECIAL.name=List display layout
com.wunding.learn.common.enums.other.LayoutStyleEnums.COVER_SPECIAL.name=Pure cover layout
com.wunding.learn.common.enums.other.LayoutStyleEnums.VIP_LIST_SPECIAL.name=List display format (including member mark)
com.wunding.learn.common.enums.other.LayoutStyleEnums.VIP_COVER_SPECIAL.name=Pure cover layout (including member mark)
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.COURSE.name=course
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.EXAM.name=take an exam
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.PROJECT.name=learning project
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.CLASS.name=Class management
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.FACE_CLASS.name=face-to-face classes
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.CYCLE_PROJECT.name=periodic project
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.QUICK_PROJECT.name=Quick training
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.COURSE_PROJECT.name=Course tasks
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.TRAIN.name=training projects
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.SPECIAL.name=Special
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.SURVEY.name=Research
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.LIVE.name=live streaming
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.SIGN.name=Sign in
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.VOTE.name=vote
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.POST.name=topic
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.PROMOTED_GAME.name=break through
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.READING.name=Read together
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.APPRAISE.name=evaluate
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.RECRUITING.name=recruit
com.wunding.learn.maxkb.api.constant.AiTypeConstant.AI_CW_DESC.name=summary
com.wunding.learn.maxkb.api.constant.AiTypeConstant.AI_COURSE_ASK.name=course Q&A
com.wunding.learn.maxkb.api.constant.AiTypeConstant.AI_CW_KEYWORD.name=keywords
com.wunding.learn.maxkb.api.constant.AiTypeConstant.AI_CW_QUESTION.name=topic
com.wunding.learn.maxkb.api.constant.AiTypeConstant.AI_CW_OUTLINE.name=outline
com.wunding.learn.common.enums.file.TranscodeStatusEnum.TRANSFORMING.videoTranscodeStatusName=Converting
com.wunding.learn.common.enums.file.TranscodeStatusEnum.TRANSFORMED.videoTranscodeStatusName=Conversion successful
com.wunding.learn.common.enums.file.TranscodeStatusEnum.TRANSFORM_FAILED.videoTranscodeStatusName=Conversion failed
com.wunding.learn.common.enums.push.PushType.COURSE.text=course
com.wunding.learn.common.enums.push.PushType.COURSE_Lib.text=Shared library course
com.wunding.learn.common.enums.push.PushType.NEWS.text=Information
com.wunding.learn.common.enums.push.PushType.EXAM.text=take an exam
com.wunding.learn.common.enums.push.PushType.EXAM_COMPETITION.text=exam competition
com.wunding.learn.common.enums.push.PushType.EXERCISE.text=practise
com.wunding.learn.common.enums.push.PushType.LIVE.text=live streaming
com.wunding.learn.common.enums.push.PushType.EXAM_UNFINISHED.text=Unexamined reminder
com.wunding.learn.common.enums.push.PushType.SURVEY.text=Research
com.wunding.learn.common.enums.push.PushType.SPECIAL_TOPIC.text=Topics
com.wunding.learn.common.enums.push.PushType.SPECIAL_TASK_COURSE.text=Special course tasks
com.wunding.learn.common.enums.push.PushType.SPECIAL_TASK_EXAM.text=Topic exam tasks
com.wunding.learn.common.enums.push.PushType.SPECIAL_TASK_EXERCISE.text=Special practice tasks
com.wunding.learn.common.enums.push.PushType.SPECIAL_TASK_SURVEY.text=Special research tasks
com.wunding.learn.common.enums.push.PushType.SPECIAL_TASK_LIVE.text=Thematic live broadcast tasks
com.wunding.learn.common.enums.push.PushType.TRAIN_CLASS.text=training course
com.wunding.learn.common.enums.push.PushType.APPLY.text=Training registration
com.wunding.learn.common.enums.push.PushType.SIGN_IN.text=Event check-in
com.wunding.learn.common.enums.push.PushType.AWARD.text=prize
com.wunding.learn.common.enums.push.PushType.PROMOTED_GAME.text=Breakthrough game
com.wunding.learn.common.enums.push.PushType.PROJECT.text=learning project
com.wunding.learn.common.enums.push.PushType.PROJECT_APPLY.text=Study project registration review
com.wunding.learn.common.enums.push.PushType.PROJECT_TASK_COURSE.text=Learning project course tasks
com.wunding.learn.common.enums.push.PushType.PROJECT_TASK_EXAM.text=Study project exam tasks
com.wunding.learn.common.enums.push.PushType.PROJECT_TASK_EXERCISE.text=Learning project practice tasks
com.wunding.learn.common.enums.push.PushType.PROJECT_TASK_SURVEY.text=Study project research tasks
com.wunding.learn.common.enums.push.PushType.PROJECT_TASK_LIVE.text=Learning project live tasks
com.wunding.learn.common.enums.push.PushType.PROJECT_TASK_TRAIN.text=Learning project training class tasks
com.wunding.learn.common.enums.push.PushType.PROJECT_TASK_FORM.text=Learning project coaching tasks
com.wunding.learn.common.enums.push.PushType.PROJECT_TASK_PROJECT.text=Learning Project Learning Project Tasks
com.wunding.learn.common.enums.push.PushType.PROJECT_TASK_PRACTICAL_OPERATION.text=Learning project practical tasks
com.wunding.learn.common.enums.push.PushType.QUICK_PROJECT.text=Quick training
com.wunding.learn.common.enums.push.PushType.QUICK_PROJECT_TASK_COURSE.text=Quick training course tasks
com.wunding.learn.common.enums.push.PushType.QUICK_PROJECT_TASK_EXAM.text=Quick training exam tasks
com.wunding.learn.common.enums.push.PushType.TB_RECRUITING.text=Recruitment activities
com.wunding.learn.common.enums.push.PushType.APPRAISE.text=evaluate
com.wunding.learn.common.enums.push.PushType.MEETING.text=defense conference
com.wunding.learn.common.enums.push.PushType.READ.text=Read together
com.wunding.learn.common.enums.push.PushType.EMIGRATED_TASK.text=Pass level missions
com.wunding.learn.common.enums.push.PushType.LECTURER_WARN.text=Lecturer warning
com.wunding.learn.common.enums.push.PushType.VOTE.text=vote
com.wunding.learn.common.enums.push.PushType.TRAIN.text=training projects
com.wunding.learn.common.enums.push.PushType.TRAIN_ACTIVITY.text=Training project activities
com.wunding.learn.common.enums.push.PushType.TRAIN_CLASSES.text=Training project class
com.wunding.learn.common.enums.push.PushType.LEARN_MAP.text=study map
com.wunding.learn.common.enums.push.PushType.FACE_PROJECT.text=Face-to-face projects
com.wunding.learn.common.enums.push.PushType.FACE_PROJECT_SCHEDULE.text=Face-to-face program schedule
com.wunding.learn.common.enums.push.PushType.FORM.text=Counseling
com.wunding.learn.common.enums.push.PushType.Evaluation.text=Evaluate
com.wunding.learn.common.enums.push.PushType.Work.text=Operation
com.wunding.learn.common.enums.push.PushType.TRAIN_PROGRAM.text=training projects
com.wunding.learn.common.enums.push.PushType.ASSESS_PROJECT.text=Assessment items
com.wunding.learn.common.enums.ResourceTypeEnum.EXAM.name=take an exam
com.wunding.learn.common.enums.ResourceTypeEnum.EXERCISE.name=practise
com.wunding.learn.common.enums.ResourceTypeEnum.COURSE.name=course
com.wunding.learn.common.enums.ResourceTypeEnum.SHARED_LIBRARY.name=Shared library course
com.wunding.learn.common.enums.ResourceTypeEnum.NEWS.name=Information
com.wunding.learn.common.enums.ResourceTypeEnum.LIVE.name=live streaming
com.wunding.learn.common.enums.ResourceTypeEnum.READ.name=Read together
com.wunding.learn.common.enums.ResourceTypeEnum.VOTE.name=vote
com.wunding.learn.common.enums.ResourceTypeEnum.QUIZ.name=break through
com.wunding.learn.common.enums.ResourceTypeEnum.RECRUIT.name=recruit
com.wunding.learn.common.enums.ResourceTypeEnum.APPRAISE.name=evaluate
com.wunding.learn.common.enums.ResourceTypeEnum.MEETING.name=defense conference
com.wunding.learn.common.enums.ResourceTypeEnum.TOPIC_SECTION.name=Topic section
com.wunding.learn.common.enums.ResourceTypeEnum.SURVEY.name=Research
com.wunding.learn.common.enums.ResourceTypeEnum.SIGN_IN.name=Sign in
com.wunding.learn.common.enums.ResourceTypeEnum.TEST_CONTEST.name=exam competition
com.wunding.learn.common.enums.ResourceTypeEnum.SPECIAL.name=Topics
com.wunding.learn.common.enums.ResourceTypeEnum.SPECIAL_TASK_EXAM.name=Topic exam tasks
com.wunding.learn.common.enums.ResourceTypeEnum.SPECIAL_TASK_EXERCISE.name=Special practice tasks
com.wunding.learn.common.enums.ResourceTypeEnum.SPECIAL_TASK_COURSE.name=Special course tasks
com.wunding.learn.common.enums.ResourceTypeEnum.SPECIAL_TASK_LIVE.name=Thematic live broadcast tasks
com.wunding.learn.common.enums.ResourceTypeEnum.SPECIAL_TASK_SURVEY.name=Special research tasks
com.wunding.learn.common.enums.ResourceTypeEnum.COURSE_LEARNING_TASK.name=Course tasks
com.wunding.learn.common.enums.ResourceTypeEnum.COURSE_LEARNING_TASK_EXAM.name=Course study exam tasks
com.wunding.learn.common.enums.ResourceTypeEnum.COURSE_LEARNING_TASK_COURSE.name=Course study course tasks
com.wunding.learn.common.enums.ResourceTypeEnum.RAPID_TRAIN.name=Quick training
com.wunding.learn.common.enums.ResourceTypeEnum.RAPID_TRAIN_APP_SIGN_IN.name=Quick training sign-in application
com.wunding.learn.common.enums.ResourceTypeEnum.RAPID_TRAIN_APP_EVALUATION.name=Rapid training assessment application
com.wunding.learn.common.enums.ResourceTypeEnum.RAPID_TRAIN_TASK_COURSE.name=Quick training course tasks
com.wunding.learn.common.enums.ResourceTypeEnum.RAPID_TRAIN_TASK_EXAM.name=Quick training exam tasks
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_COURSE.name=learning project
com.wunding.learn.common.enums.ResourceTypeEnum.PROJECT.name=learning project
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_COURSE_APP_NEWS.name=Training class announcement application
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_COURSE_APP_APPLY.name=Training class registration application
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_COURSE_APP_SIGN_IN.name=Training class sign-in application
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_COURSE_APP_TOPIC_SECTION.name=Training class topic section application
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_COURSE_APP_EVALUATION.name=Training course evaluation application
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_COURSE_TASK_COURSE.name=Training course tasks
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_COURSE_TASK_EXAM.name=Training class exam tasks
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_COURSE_TASK_EXERCISE.name=Training class practice tasks
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_COURSE_TASK_SURVEY.name=Training class research tasks
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_COURSE_TASK_LIVE.name=Training class live broadcast tasks
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_COURSE_TASK_PROJECT.name=Training course project tasks
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_COURSE_TASK_TUTOR.name=Training class coaching tasks
com.wunding.learn.common.enums.ResourceTypeEnum.PERIODIC_PROJECT.name=periodic project
com.wunding.learn.common.enums.ResourceTypeEnum.PERIODIC_PROJECT_APP_NEWS.name=Cycle project announcement application
com.wunding.learn.common.enums.ResourceTypeEnum.PERIODIC_PROJECT_APP_TOPIC_SECTION.name=Periodic project topic section application
com.wunding.learn.common.enums.ResourceTypeEnum.PERIODIC_PROJECT_APP_EVALUATION.name=Cycle project assessment application
com.wunding.learn.common.enums.ResourceTypeEnum.PERIODIC_PROJECT_TASK_COURSE.name=Periodic project course tasks
com.wunding.learn.common.enums.ResourceTypeEnum.PERIODIC_PROJECT_TASK_EXAM.name=Periodic project examination tasks
com.wunding.learn.common.enums.ResourceTypeEnum.PERIODIC_PROJECT_TASK_EXERCISE.name=Periodic project practice tasks
com.wunding.learn.common.enums.ResourceTypeEnum.PERIODIC_PROJECT_TASK_SURVEY.name=Periodic project research tasks
com.wunding.learn.common.enums.ResourceTypeEnum.PERIODIC_PROJECT_TASK_PROJECT.name=Periodic project project tasks
com.wunding.learn.common.enums.ResourceTypeEnum.PERIODIC_PROJECT_TASK_TUTOR.name=Periodic project coaching tasks
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_PROGRAM.name=training projects
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_PROGRAM_ACTIVITY_COURSE.name=Training project course activities
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_PROGRAM_ACTIVITY_EXAM.name=Training project exam activities
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_PROGRAM_ACTIVITY_CLASS.name=Training project class activities
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_PROGRAM_ACTIVITY_LIVE.name=Training project live event
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_PROGRAM_ACTIVITY_SURVEY.name=Training project research activities
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_PROGRAM_ACTIVITY_RECRUIT.name=Training project recruitment activities
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_PROGRAM_ACTIVITY_APPRAISE.name=Training project evaluation activities
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_PROGRAM_ACTIVITY_FORM.name=Training project form activity
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_PROGRAM_ACTIVITY_SIGN_IN.name=Training project sign-in activity
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_PROGRAM_ACTIVITY_VOTE.name=Training project voting activity
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_PROGRAM_ACTIVITY_PRACTICAL_OPERATION.name=Training project practical activities
com.wunding.learn.common.enums.ResourceTypeEnum.LEARN_MAP_EXEC.name=study map
com.wunding.learn.common.enums.ResourceTypeEnum.FACE_PROJECT.name=Face-to-face projects
com.wunding.learn.common.enums.ResourceTypeEnum.FACE_PROJECT_SCHEDULE.name=Face-to-face program schedule
com.wunding.learn.common.enums.ResourceTypeEnum.PRACTICAL_OPERATION.name=Practical operation
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.ACCEPT_FEEDBACK.name=Feedback acceptance
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.AUDIT_COURSEWARE.name=Upload courseware and approve it
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.BEST_REPLY_TOPIC.name=Best topic reply
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COLLECT_COURSE.name=Save course
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COMMENT_COURSE.name=Comment on a course
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COMMENT_COURSEWARE.name=Comment on courseware
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COMMENT_NEWS.name=Comment information
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COMMIT_EVALUATE.name=Submit assessment
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COMMIT_EXAM.name=Submit exam
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COMMIT_EXERCISE.name=Complete the exercise
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COMMIT_FEEDBACK.name=Submit feedback
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COMMIT_HOMEWORK.name=Submit assignment
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COMMIT_SIGN.name=Complete check-in
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COURSE_STAR.name=Course rating
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COURSE_VOTE.name=Like the course
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COURSEWARE_STAR.name=Courseware was rated with stars
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COURSEWARE_VIEW.name=Number of courseware views
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COURSEWARE_VOTE.name=Number of likes for the courseware
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.EVERYDAY_LEARN_COURSE.name=A user who completes a course every day counts as one check-in
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.EVERYDAY_LEARN_COURSEWARE.name=A user who completes one courseware every day counts as one check-in
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.EVERYDAY_LOGIN.name=When a user logs in to the platform, he or she clocks in once a day. If it is not continuous, it will be cleared and recalculated.
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.EXAMPLE_ADD.name=Reward each student when they submit a case
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.EXAMPLE_AUDIT.name=The rewards the judges get when they grade
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.EXAMPLE_AUDIT_OVER.name=During case review, the score obtained after the review is completed, and the reward when reaching a certain level
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.EXAMPLE_COMPANY.name=Rewards will be awarded when the case is adjusted to the company level, and the downgrade will no longer be cancelled.
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.EXAMPLE_EXTRA.name=If the case is marked as high quality by the administrator, it will no longer be canceled if it is canceled as high quality.
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.EXAMPLE_GROUP.name=Rewards will be awarded when the case is adjusted to the group level, and the downgrade will no longer be cancelled.
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.EXAM_PROCESS.name=Incentives based on test score ranges
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.FINISH_COURSE.name=Complete the course
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.FINISH_COURSEWARE.name=Finished the courseware(Fixed)
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.FINISH_COURSEWARE_BYTIME.name=Finished the courseware(According to the duration of the courseware)
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.FINISH_PROJECT.name=Complete all learning tasks in the project to gain incentives
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.FINISH_PROJECT_APPLY.name=Complete project registration
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.FINISH_SURVEY.name=Submit research
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.FIRST_LOGIN.name=Obtained when the user first accesses the system, only once
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.FOCUS_TOPIC.name=Follow the topic
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.INTEGRAL_CLEARING.name=Clear points (only used for point statistics, not used for actual point configuration rules)
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.LEARN_COURSE.name=Calculated when clicking on any entrance to open the course
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.LEARN_COURSEWARE.name=Calculated when clicking on any entrance to open the courseware
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.LECTURER_JOIN_PROJECT_TEACH.name=Only available 7 days after the end of the study program
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.LECTURER_TEACH_COURSEWARE.name=Only available 7 days after the end of the study program
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.LIVE_CONTRIBUTE.name=Contribute knowledge and obtain contribution credits by initiating live broadcasts
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.LIVE_INTERACT.name=Interact with the anchor in the live broadcast room to give points
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.LIVE_INTERACT_COUNT.name=Points will be awarded if the live broadcast initiated reaches a certain amount of viewing and interaction
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.LIVE_QUESTION_AND_ANSWER.name=Anchors can ask questions with gold coin rewards in the live broadcast room, and students can get gold coins by answering questions in the live broadcast room.
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.MAKE_LIVE.name=Start a live broadcast to give points
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.MAKE_TOPIC.name=Post a topic
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.PARTICIPATION_DOUBLE.name=Participate and obtain (double)
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.PARTICIPATION_GROUP.name=Participate in acquisition (team formation)
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.PARTICIPATION_MULTI.name=Participate in acquisition (multiple people)
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.PARTICIPATION_SINGLE.name=Participate in acquisition (single player)
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.PERSONAL_CHAMPION_MULTI.name=Individual championship (multiple people)
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.PERSONAL_SECOND_MULTI.name=Individual runner-up (multiple people)
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.PERSONAL_THIRD_MULTI.name=Individual third place (multiple people)
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.PERSONAL_WIN_DOUBLE.name=Individual victory (double)
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.PERSONAL_WIN_SINGLE.name=Personal victory (single player)
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.PROJECT_TASK_PROCESS.name=Only 7 days after the end of the learning project, incentives will be given based on the task completion rate range
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.PROMOTED_GAME_FINISH_CHECKPOINT.name=Complete the level
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.READING_BOOK_NUM.name=Number of books read in total
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.READING_CLOCK_TIMES.name=Number of check-ins for shared reading (more than 10 minutes of reading a book together per month will be counted as one valid check-in)
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.READING_COMMENT_EXPNUM.name=Number of comments read in total
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.READING_EXPERIENCE_TIMES.name=Number of submissions
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.REPLY_COURSE_COMMENT.name=Reply to course comments
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.REPLY_COURSEWARE_COMMENT.name=Reply to courseware comments
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.REPLY_TOPIC.name=Reply to the topic
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.SHARE_COURSE.name=Share course
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.SHARE_COURSEWARE.name=Share courseware
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.STAR_COURSEWARE.name=Courseware rating
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.TEAM_WIN_GROUP.name=Team wins (Team formation)
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.TOPIC_COMMENT_COUNT.name=Motivating moderators based on the number of topic replies
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.VIEW_COURSE_PROCESS.name=Progress percentage of all courseware durations under the course
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.VIEW_COURSEWARE_PROCESS.name=Courseware browsing time progress (percentage)
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.VIEW_LIVE.name=Click to watch live broadcast
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.VIEW_LIVE_TOTAL_TIME.name=The cumulative live viewing time reaches the specified time
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.VIEW_NEWS.name=Click to view information
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.VOTE_COURSE_COMMENT.name=Like course comments
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.VOTE_LECTURER.name=Students like the lecturer
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.VOTE_NEWS_COMMENT.name=Like information comments
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COURSE_POSTER_SHARE.name=Share course poster
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.PROJECT_POSTER_SHARE.name=Share project poster
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.LIVE_POSTER_SHARE.name=Share live broadcast posters
com.wunding.learn.common.enums.other.DifficultyTypeEnum.EASY.name=Low
com.wunding.learn.common.enums.other.DifficultyTypeEnum.MIDDLE.name=middle
com.wunding.learn.common.enums.other.DifficultyTypeEnum.HIGN.name=high
com.wunding.learn.common.enums.market.HeadContentRuleEnum.GUESSYOULIKE.orderRuleDesc=random order
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RECOMMENDCOURSE.orderRuleDesc=Selected good class time in reverse order
com.wunding.learn.common.enums.market.HeadContentRuleEnum.POPULARCOURSE1.orderRuleDesc=Number of comments + count of stars, total number in reverse order
com.wunding.learn.common.enums.market.HeadContentRuleEnum.POPULARCOURSE2.orderRuleDesc=Number of comments + count of stars, total number in reverse order
com.wunding.learn.common.enums.market.HeadContentRuleEnum.POSTCOURSE.orderRuleDesc=Sort by course release time
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LEARNINGCOURSE.orderRuleDesc=Sort by course start time
com.wunding.learn.common.enums.market.HeadContentRuleEnum.NEWS.orderRuleDesc=Release time inversion
com.wunding.learn.common.enums.market.HeadContentRuleEnum.STUDYPROJECT.orderRuleDesc=Date (the start date of the fixed period project or (the joining date of the periodic project, if not joined, the date is the release date)
com.wunding.learn.common.enums.market.HeadContentRuleEnum.STUDYPROJECTSPECIFYCATEGORY.orderRuleDesc=Date (the start date of the fixed period project or (the joining date of the periodic project, if not joined, the date is the release date)
com.wunding.learn.common.enums.market.HeadContentRuleEnum.SPECIAL.orderRuleDesc=Follow the order of study programs
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LECTURERELEGANTDEMEANOUR1.orderRuleDesc=Number of lectures in reverse order
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LECTURERELEGANTDEMEANOUR2.orderRuleDesc=Comprehensive score ranking of teaching
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LECTURERELEGANTDEMEANOUR3.orderRuleDesc=Sort by the most recent teaching time
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LIVINGBROADCAST.orderRuleDesc=Rearrange by live broadcast start time
com.wunding.learn.common.enums.market.HeadContentRuleEnum.CURRENTLIVE.orderRuleDesc=Rearrange by live broadcast start time
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LIVEPLAYBACK.orderRuleDesc=Rearrange by live broadcast start time
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LATESTTOPICS.orderRuleDesc=Publication time reversed
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTTOPICS1.orderRuleDesc=Sort by number of replies
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTTOPICS2.orderRuleDesc=Sort by number of replies
com.wunding.learn.common.enums.market.HeadContentRuleEnum.ELITEPOSTS1.orderRuleDesc=Sort by setting top time
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASEOFTHEMONTH1.orderRuleDesc=Sort by number of likes
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASEOFTHEMONTH2.orderRuleDesc=Sort by number of likes
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASEOFTHEMONTH3.orderRuleDesc=Sort by number of likes
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASE1.orderRuleDesc=Sort by number of likes
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASE2.orderRuleDesc=Sort by number of likes
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASE3.orderRuleDesc=Sort by number of likes
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RECOMMENDCASE1.orderRuleDesc=Sort by recommended time
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RECOMMENDCASE2.orderRuleDesc=Sort by recommended time
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RECOMMENDCASE3.orderRuleDesc=Sort by recommended time
com.wunding.learn.common.enums.market.HeadContentRuleEnum.TRAINPROJECT.orderRuleDesc=According to release time, the latest release is at the top
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RAINPROJECTSPECIFYCATEGORY.orderRuleDesc=According to release time, the latest release is at the top
com.wunding.learn.common.enums.market.HeadContentRuleEnum.ORG_INFO.orderRuleDesc=
com.wunding.learn.common.library.record.enums.HandleTypeEnum.DEFAULT.name=none
com.wunding.learn.common.library.record.enums.HandleTypeEnum.TP_ADD_RECORD.name=In stock
com.wunding.learn.common.library.record.enums.HandleTypeEnum.TP_DELETE_RECORD.name=deleted
com.wunding.learn.common.library.record.enums.HandleTypeEnum.TP_UPDATE_RECORD.name=edited
com.wunding.learn.common.library.record.enums.HandleTypeEnum.TP_ENABLE_RECORD.name=enabled
com.wunding.learn.common.library.record.enums.HandleTypeEnum.TP_DISABLE_RECORD.name=disabled
com.wunding.learn.common.library.record.enums.HandleTypeEnum.TP_IMPORT_QUESTION.name=imported
com.wunding.learn.common.library.record.enums.HandleTypeEnum.TP_DOWNLOAD_RECORD.name=downloaded
com.wunding.learn.common.library.record.enums.HandleTypeEnum.TP_EDIT_RECORD.name=have modified
com.wunding.learn.common.library.record.enums.HandleTypeEnum.TP_USE_RECORD.name=use
com.wunding.learn.common.library.record.enums.LibraryTypeEnum.DEFAULT.name=none
com.wunding.learn.common.library.record.enums.LibraryTypeEnum.LIB_EXAM.name=Test paper library
com.wunding.learn.common.library.record.enums.LibraryTypeEnum.LIB_QUESTION.name=Exam question bank
com.wunding.learn.common.library.record.enums.LibraryTypeEnum.LIB_EXERCISE.name=Exercise library
com.wunding.learn.common.library.record.enums.LibraryTypeEnum.LIB_SURVEY.name=Survey library
com.wunding.learn.common.library.record.enums.LibraryTypeEnum.LIB_COURSE.name=courseWare library
com.wunding.learn.common.library.record.enums.LibraryTypeEnum.LIB_EVALUATION.name=evaluation library
com.wunding.learn.common.library.record.enums.LibraryTypeEnum.LIB_MATERIAL.name=material library
com.wunding.learn.common.enums.other.ModuleEnum.TRAIN.name=training projects
com.wunding.learn.common.enums.other.ModuleEnum.CERTIFICATION.name=Certification
com.wunding.learn.common.enums.viewlimit.ViewLimitTypeEnum.TIME.name=temporal identity
com.wunding.learn.common.enums.viewlimit.ViewLimitTypeEnum.BUSINESS.name=Business lines
com.wunding.learn.common.enums.viewlimit.ViewLimitTypeEnum.MANAGE_LEVEL.name=Manager level
com.wunding.learn.common.enums.viewlimit.ViewLimitTypeEnum.POST.name=Job family, position, position level
com.wunding.learn.common.enums.viewlimit.ViewLimitTypeEnum.LECTURER.name=Lecturer level
com.wunding.learn.common.enums.viewlimit.ViewLimitTypeEnum.JOB_LEVEL.name=Rank
com.wunding.learn.common.enums.viewlimit.ViewLimitTypeEnum.MemberLimit.name=Academy member
com.wunding.learn.common.enums.viewlimit.ViewLimitTypeEnum.OrgLimit.name=department
com.wunding.learn.common.enums.viewlimit.ViewLimitTypeEnum.UserLimit.name=personnel
com.wunding.learn.common.enums.viewlimit.ViewLimitTypeEnum.WorkLimit.name=Type of work
com.wunding.learn.common.enums.MapBehaviorEnum.USER.name=Training activities (participating as students)
com.wunding.learn.common.enums.MapBehaviorEnum.TEACH.name=Training activities (participating as lecturer)
com.wunding.learn.common.enums.MapBehaviorEnum.SUPERVISOR.name=Tutoring activities (participating as a tutor)
com.wunding.learn.common.enums.MapBehaviorEnum.PRACTICAL.name=Practical activities (participating as a practical person)
com.wunding.learn.common.enums.MapBehaviorEnum.EVALUATION.name=Training activities (evaluation status participation)
com.wunding.learn.common.enums.push.NoticeTypeEnum.GENERIC_TEMPLATE.message=Universal template
com.wunding.learn.common.enums.push.NoticeTypeEnum.AUDIT_TEMPLATE.message=Audit-specific templates
com.wunding.learn.common.enums.push.NoticeTypeEnum.REMINDER_TEMPLATE.message=Special template for reminder
com.wunding.learn.common.enums.push.NoticeTypeEnum.APPROVED_TEMPLATE.message=Approval template
com.wunding.learn.common.enums.push.NoticeTypeEnum.APPROVED_FAILED_TEMPLATE.message=If the review fails, the template is allowed to be modified.
com.wunding.learn.common.enums.push.NoticeTypeEnum.APPROVED_FAILED_NOT_MODIFY_TEMPLATE.message=If the review fails, modification of the template is not allowed.
com.wunding.learn.common.enums.push.NoticeTypeEnum.NOTICE_OF_CLASS_SCHEDULE_START.message=Course opening notice template
com.wunding.learn.common.enums.push.NoticeTypeEnum.IDENTIFYING_CODE_TEMPLATE.message=Verification code template
com.wunding.learn.common.enums.push.NoticeTypeEnum.NOTICE_OF_CLASS_START.message=Class opening notice template
com.wunding.learn.common.enums.other.OperationEnum.CREATE.name=create
com.wunding.learn.common.enums.other.OperationEnum.PUBLISH.name=release
com.wunding.learn.common.enums.other.OperationEnum.PUBLISH_CANCEL.name=Unpublish
com.wunding.learn.common.enums.other.OperationEnum.DELETE.name=delete
com.wunding.learn.common.enums.other.OperationEnum.UPDATE.name=renew
com.wunding.learn.reading.service.enums.ReadingTaskDescription.READING_TASK_TYPE_SIGN.description=You can only check in once a day, and you can get corresponding operation points every time. You will no longer get it after exceeding the required number of times.
com.wunding.learn.reading.service.enums.ReadingTaskDescription.READING_TASK_TYPE_EXPERIENCE.description=The experience of each book is counted once. The first time you submit the experience of the book, you will get the operation points. After the number of times is exceeded, you will no longer get the operation points.
com.wunding.learn.reading.service.enums.ReadingTaskDescription.READING_TASK_TYPE_READING_N.description=You will get operational points every time you read a book, and you will no longer get them after the specified number is exceeded.
com.wunding.learn.reading.service.enums.ReadingTaskDescription.READING_TASK_TYPE_COMMENT_N.description=Each time you comment on your experience, you will receive operational points, and you will no longer receive them after the specified number is exceeded.
com.wunding.learn.reading.service.enums.ReadingTaskDescription.READING_TASK_TYPE_READING.description=Earn operational points after reading the designated books.
com.wunding.learn.reading.service.enums.ReadingTaskDescription.READING_TASK_TYPE_COMMENT.description=Earn operational points when you post your thoughts on a book for the first time.
com.wunding.learn.common.enums.push.PushNoticeEventEnum.RECRUITING_46.name=Co-organizer of pending review notice
com.wunding.learn.common.enums.push.PushNoticeEventEnum.RECRUITING_47.name=Notification of audit results
com.wunding.learn.common.enums.push.PushNoticeEventEnum.APPRAISE_49.name=Evaluation result notification
com.wunding.learn.common.enums.push.PushNoticeEventEnum.MEETING_58.name=Notice of defense result
com.wunding.learn.common.enums.evaluation.SourceTypeEnum.MANUAL_UPLOAD.message=Manual upload
com.wunding.learn.common.enums.evaluation.SourceTypeEnum.EVAL_LIB.message=Evaluation library
com.wunding.learn.common.enums.evaluation.SourceTypeEnum.DOCUMENT.message=Database
com.wunding.learn.common.enums.excitation.ExcitationTypeEnum.INTEGRAL.name=integral
com.wunding.learn.common.enums.excitation.ExcitationTypeEnum.CREDIT.name=credit
com.wunding.learn.common.enums.excitation.ExcitationTypeEnum.LEARN_TIME.name=hours
com.wunding.learn.common.enums.excitation.ExcitationTypeEnum.GOLD_COIN.name=gold
com.wunding.learn.common.enums.project.ProjectAppType.NOTICE.appName=announcement
com.wunding.learn.common.enums.project.ProjectAppType.MENTOR.appName=tutor
com.wunding.learn.common.enums.project.ProjectAppType.LECTURER.appName=lecturer
com.wunding.learn.common.enums.project.ProjectAppType.APPLY.appName=Sign up
com.wunding.learn.common.enums.project.ProjectAppType.SIGN.appName=Sign in
com.wunding.learn.common.enums.project.ProjectAppType.TOPIC.appName=topic
com.wunding.learn.common.enums.project.ProjectAppType.TOPIC_MANAGE.appName=Topic section
com.wunding.learn.common.enums.project.ProjectAppType.ASSESS.appName=Evaluate
com.wunding.learn.common.enums.project.ProjectAppType.TEAM.appName=team
com.wunding.learn.common.enums.project.ProjectAppType.HOMEWORK.appName=Operation
com.wunding.learn.common.enums.project.ProjectAppType.COMMENT.appName=Comment
com.wunding.learn.common.enums.project.ProjectAppType.COMPLETION.appName=Graduation
com.wunding.learn.common.enums.project.ProjectAppType.COST.appName=cost
com.wunding.learn.common.enums.project.ProjectAppType.DATA.appName=material
com.wunding.learn.common.enums.project.ProjectAppType.STATISTICS.appName=statistics
com.wunding.learn.common.enums.project.ProjectAppType.EXAM.appName=take an exam
com.wunding.learn.common.enums.project.ProjectAppType.VACATE.appName=Ask for leave
com.wunding.learn.common.enums.project.ProjectAppType.INVOICE.appName=Invoicing
com.wunding.learn.common.enums.project.ProjectAppType.FILE.appName=Related information
com.wunding.learn.common.enums.project.ProjectAppType.SCHEDULE.appName=schedule
com.wunding.learn.common.enums.other.PassStatusEnum.NO.name=no
com.wunding.learn.common.enums.other.PassStatusEnum.YES.name=yes
com.wunding.learn.common.enums.exam.CompetitionSessionUserTypeEnum.DEFAULT_VALUE.name=default value
com.wunding.learn.common.enums.exam.CompetitionSessionUserTypeEnum.INITIATIVE.name=Initiative
com.wunding.learn.common.enums.exam.CompetitionSessionUserTypeEnum.BE_INVITED.name=invited
com.wunding.learn.common.enums.exam.CompetitionSessionUserTypeEnum.RANDOM_MATCH.name=random match
com.wunding.learn.common.enums.exam.CompetitionSessionUserTypeEnum.INVITING.name=Inviting
com.wunding.learn.common.enums.exam.CompetitionSessionUserTypeEnum.NO_RESPONSE.name=No response
com.wunding.learn.common.enums.exam.CompetitionSessionUserTypeEnum.REFUSE.name=Rejected
com.wunding.learn.common.enums.other.LayoutStyleEnum.COURSE_1_4.desc=horizontally
com.wunding.learn.common.enums.other.LayoutStyleEnum.COURSE_2_4.desc=Vertical
com.wunding.learn.common.enums.other.LayoutStyleEnum.COURSE_3_8.desc=Slide with title
com.wunding.learn.common.enums.other.LayoutStyleEnum.NEWS_1_4.desc=Vertical layout without introduction
com.wunding.learn.common.enums.other.LayoutStyleEnum.NEWS_2_3.desc=Vertical layout with introduction
com.wunding.learn.common.enums.other.LayoutStyleEnum.NEWS_3_8.desc=Slide with title
com.wunding.learn.common.enums.other.LayoutStyleEnum.PROJECT_1_1.desc=single untitled
com.wunding.learn.common.enums.other.LayoutStyleEnum.PROJECT_2_3.desc=Vertical
com.wunding.learn.common.enums.other.LayoutStyleEnum.PROJECT_3_8.desc=Slide with title
com.wunding.learn.common.enums.other.LayoutStyleEnum.PROJECT_4_8.desc=sliding untitled
com.wunding.learn.common.enums.other.LayoutStyleEnum.SPECIAL_1_1.desc=single titled
com.wunding.learn.common.enums.other.LayoutStyleEnum.SPECIAL_2_3.desc=Vertical
com.wunding.learn.common.enums.other.LayoutStyleEnum.SPECIAL_3_8.desc=Slide with title
com.wunding.learn.common.enums.other.LayoutStyleEnum.SPECIAL_4_8.desc=sliding untitled
com.wunding.learn.common.enums.other.LayoutStyleEnum.SPECIAL_5_1.desc=single untitled
com.wunding.learn.common.enums.other.LayoutStyleEnum.LECTURER_1_8.desc=slide
com.wunding.learn.common.enums.other.LayoutStyleEnum.LECTURER_2_4.desc=Large horizontally
com.wunding.learn.common.enums.other.LayoutStyleEnum.LECTURER_3_4.desc=small horizontally
com.wunding.learn.common.enums.other.LayoutStyleEnum.LIVE_1_1.desc=single titled
com.wunding.learn.common.enums.other.LayoutStyleEnum.LIVE_2_8.desc=Slide with title 1
com.wunding.learn.common.enums.other.LayoutStyleEnum.LIVE_3_4.desc=horizontally
com.wunding.learn.common.enums.other.LayoutStyleEnum.LIVE_4_8.desc=Slide with title 2
com.wunding.learn.common.enums.other.LayoutStyleEnum.TOPICS_1_5.desc=Vertical layout without introduction
com.wunding.learn.common.enums.other.LayoutStyleEnum.TOPICS_2_3.desc=Vertical layout with introduction
com.wunding.learn.common.enums.other.LayoutStyleEnum.TOPICS_3_8.desc=Introduction horizontally
com.wunding.learn.common.enums.other.LayoutStyleEnum.CASE_1_4.desc=horizontally
com.wunding.learn.common.enums.other.LayoutStyleEnum.CASE_2_4.desc=Vertical
com.wunding.learn.common.enums.other.LayoutStyleEnum.CASE_3_8.desc=Slide with title
com.wunding.learn.common.enums.other.LayoutStyleEnum.ORG_1_4.desc=Horizontal version [h5 only]
com.wunding.learn.common.enums.other.LayoutStyleEnum.CLOCK_IN_1_0.desc=default
com.wunding.learn.common.enums.other.LayoutStyleEnum.MY_TASK_1_0.desc=default
com.wunding.learn.apply.service.enums.ApplyStatusEnum.DRAFT.name=draft
com.wunding.learn.apply.service.enums.ApplyStatusEnum.APPLYING.name=Applying
com.wunding.learn.apply.service.enums.ApplyStatusEnum.SUCCESS.name=Application successful
com.wunding.learn.apply.service.enums.ApplyStatusEnum.FAIL.name=Application failed
com.wunding.learn.apply.service.enums.ApplyStatusEnum.CANCEL.name=Apply for cancellation
com.wunding.learn.common.enums.other.ContentRuleEnum.HOT_COURSE.showRuleDesc=Popular courses
com.wunding.learn.common.enums.other.ContentRuleEnum.LEARNING_COURSE.showRuleDesc=I'm learning
com.wunding.learn.common.enums.other.ContentRuleEnum.SCORE_LECTURER.showRuleDesc=All internal lecturers
com.wunding.learn.common.enums.other.ContentRuleEnum.TIME_LECTURER.showRuleDesc=All internal lecturers
com.wunding.learn.common.enums.other.ContentRuleEnum.NEW_TOPIC_NOT_TOP.showRuleDesc=Section distribution scope
com.wunding.learn.common.enums.other.ContentRuleEnum.HOT_TOPIC_COMMENT.showRuleDesc=Section distribution scope
com.wunding.learn.common.enums.other.ContentRuleEnum.HOT_TOPIC_VIEW.showRuleDesc=Section distribution scope
com.wunding.learn.common.enums.other.ContentRuleEnum.NEW_TOPIC_TOP.showRuleDesc=The section's publishing range is set to pinned posts
com.wunding.learn.common.enums.other.ContentRuleEnum.HOT_CASE.showRuleDesc=
com.wunding.learn.common.enums.other.ContentRuleEnum.NEW_CASE.showRuleDesc=
com.wunding.learn.common.enums.other.ContentRuleEnum.LIVING_LIVE.showRuleDesc=Live broadcast that has started but has not ended yet
com.wunding.learn.common.enums.other.ContentRuleEnum.CURRENT_LIVE.showRuleDesc=Unfinished live broadcast
com.wunding.learn.common.enums.other.ContentRuleEnum.ROLLBACK_LIVE.showRuleDesc=Live broadcasts that have ended and have playback files
com.wunding.learn.push.api.enums.PushChannelEnum.APP.desc=APP
com.wunding.learn.push.api.enums.PushChannelEnum.EMAIL.desc=Mail
com.wunding.learn.push.api.enums.PushChannelEnum.WECOM.desc=Enterprise WeChat
com.wunding.learn.push.api.enums.PushChannelEnum.WECHAT_OFFICIAL_ACCOUNTS.desc=WeChat public account
com.wunding.learn.push.api.enums.PushChannelEnum.WECHAT_MINI_PROGRAMS.desc=WeChat applet
com.wunding.learn.push.api.enums.PushChannelEnum.DING_TALK.desc=DingTalk
com.wunding.learn.push.api.enums.PushChannelEnum.FEI_SHU.desc=Feishu
com.wunding.learn.push.api.enums.PushChannelEnum.SMS.desc=Short message
com.wunding.learn.train.service.enums.ActivityResourceTypeEnum.COURSE.name=course
com.wunding.learn.train.service.enums.ActivityResourceTypeEnum.EXAM.name=take an exam
com.wunding.learn.train.service.enums.ActivityResourceTypeEnum.PROJECT.name=training course
com.wunding.learn.train.service.enums.ActivityResourceTypeEnum.LIVE.name=live streaming
com.wunding.learn.train.service.enums.ActivityResourceTypeEnum.SURVEY.name=Research
com.wunding.learn.train.service.enums.ActivityResourceTypeEnum.RECRUITING.name=recruit
com.wunding.learn.train.service.enums.ActivityResourceTypeEnum.APPRAISE.name=evaluate
com.wunding.learn.train.service.enums.ActivityResourceTypeEnum.FORM.name=form
com.wunding.learn.train.service.enums.ActivityResourceTypeEnum.SIGN.name=Sign in
com.wunding.learn.train.service.enums.ActivityResourceTypeEnum.VOTE.name=vote
com.wunding.learn.train.service.enums.ActivityResourceTypeEnum.PRACTICE.name=Practical operation
com.wunding.learn.train.service.enums.ActivityResourceTypeEnum.SPECIAL.name=Topics
com.wunding.learn.common.enums.exam.CompetitionGroupNameEnum.A_GROUP.name=Group A
com.wunding.learn.common.enums.exam.CompetitionGroupNameEnum.B_GROUP.name=Group B
com.wunding.learn.common.enums.user.OrgTypeEnum.VIEWLIMITAREA.name=Use authorization scope
com.wunding.learn.common.enums.user.OrgTypeEnum.MANAGELIMITAREA.name=Classified management unit
com.wunding.learn.common.enums.language.LanguageModuleEnum.ORGANIZATION.moduleName=organize
com.wunding.learn.common.enums.language.LanguageModuleEnum.TIME.moduleName=time
com.wunding.learn.common.enums.language.LanguageModuleEnum.BUSINESS.moduleName=lines
com.wunding.learn.common.enums.language.LanguageModuleEnum.MANAGE_LEVEL.moduleName=Manager level
com.wunding.learn.common.enums.language.LanguageModuleEnum.POST.moduleName=Job family
com.wunding.learn.common.enums.language.LanguageModuleEnum.JOB_LEVEL.moduleName=Rank
com.wunding.learn.common.enums.language.LanguageModuleEnum.LECTURER.moduleName=lecturer
com.wunding.learn.common.enums.language.LanguageModuleEnum.SUPERIOR.moduleName=post
com.wunding.learn.common.enums.language.LanguageModuleEnum.CourseCate.moduleName=Course classification
com.wunding.learn.common.enums.language.LanguageModuleEnum.CourseTagCate.moduleName=Course Tags
com.wunding.learn.common.enums.language.LanguageModuleEnum.InfoCate.moduleName=Information classification
com.wunding.learn.common.enums.language.LanguageModuleEnum.AwardCate.moduleName=Prize categories
com.wunding.learn.common.enums.language.LanguageModuleEnum.ThematicTag.moduleName=Topic tags
com.wunding.learn.common.enums.language.LanguageModuleEnum.ThematicClass.moduleName=Topic classification
com.wunding.learn.common.enums.language.LanguageModuleEnum.MentorCate.moduleName=Tutor classification
com.wunding.learn.common.enums.language.LanguageModuleEnum.FormTaskCate.moduleName=Classification of tutoring tasks
com.wunding.learn.common.enums.language.LanguageModuleEnum.home.moduleName=middle menu
com.wunding.learn.common.enums.language.LanguageModuleEnum.myLearn.moduleName=my study
com.wunding.learn.common.enums.language.LanguageModuleEnum.botMenu.moduleName=bottom menu
com.wunding.learn.common.enums.language.LanguageModuleEnum.app.moduleName=My application
com.wunding.learn.common.enums.language.LanguageModuleEnum.systemConfig.moduleName=data dictionary
com.wunding.learn.common.enums.language.LanguageModuleEnum.titleManager.moduleName=title
com.wunding.learn.common.enums.language.LanguageModuleEnum.sysSpace.moduleName=Points rules
com.wunding.learn.common.enums.language.LanguageModuleEnum.firstInfo.moduleName=Headline notification
com.wunding.learn.common.enums.language.LanguageModuleEnum.award.moduleName=points game
com.wunding.learn.common.enums.language.LanguageModuleEnum.systemCertification.moduleName=System certification
com.wunding.learn.common.enums.language.LanguageModuleEnum.sysClientVersion.moduleName=Version management
com.wunding.learn.common.enums.language.LanguageModuleEnum.excitationEvent.moduleName=target management events
com.wunding.learn.common.enums.language.LanguageModuleEnum.KnowledgeBaseType.moduleName=Knowledge base classification
com.wunding.learn.common.enums.language.LanguageModuleEnum.KnowledgeTagCate.moduleName=Knowledge Base Tags
com.wunding.learn.common.enums.language.LanguageModuleEnum.EvaluateLibraryCate.moduleName=Assessment library classification
com.wunding.learn.common.enums.language.LanguageModuleEnum.ExamLibraryCate.moduleName=Test question bank classification
com.wunding.learn.common.enums.language.LanguageModuleEnum.ExerciseLibraryCate.moduleName=Exercise library classification
com.wunding.learn.common.enums.language.LanguageModuleEnum.TestPaperLibraryCate.moduleName=Test paper library classification
com.wunding.learn.common.enums.language.LanguageModuleEnum.CourseLibraryCate.moduleName=Courseware library classification
com.wunding.learn.common.enums.language.LanguageModuleEnum.SurveyLibraryCate.moduleName=Research database classification
com.wunding.learn.common.enums.language.LanguageModuleEnum.CourseTagNewCate.moduleName=Course label classification
com.wunding.learn.common.enums.language.LanguageModuleEnum.ExampleCate.moduleName=Case classification
com.wunding.learn.common.enums.language.LanguageModuleEnum.lecturerDomainCate.moduleName=Lecturer field classification
com.wunding.learn.common.enums.market.HeadContentEnum.COURSE.name=course
com.wunding.learn.common.enums.market.HeadContentEnum.NEWS.name=Information
com.wunding.learn.common.enums.market.HeadContentEnum.STUDYPROJECT.name=learning project
com.wunding.learn.common.enums.market.HeadContentEnum.SPECIAL.name=Topics
com.wunding.learn.common.enums.market.HeadContentEnum.LECTURER.name=lecturer
com.wunding.learn.common.enums.market.HeadContentEnum.LIVE.name=live streaming
com.wunding.learn.common.enums.market.HeadContentEnum.TOPIC.name=topic
com.wunding.learn.common.enums.market.HeadContentEnum.CASE_LIBRARY.name=Case library
com.wunding.learn.common.enums.market.HeadContentEnum.TRAIN_PROJECT.name=training projects
com.wunding.learn.common.enums.market.HeadContentEnum.ORG_INFO.name=organize information
com.wunding.learn.common.enums.market.HeadContentEnum.CONTINUOUS_CLOCK_IN_SECTION_H5.name=Continuous check-in
com.wunding.learn.common.enums.market.HeadContentEnum.MY_TASK_LIST.name=My task
com.wunding.learn.common.enums.push.SuperResourceTypeEnum.project.name=learning project
com.wunding.learn.common.enums.push.SuperResourceTypeEnum.special.name=Topics
com.wunding.learn.common.enums.push.SuperResourceTypeEnum.quick_project.name=Quick training
com.wunding.learn.common.enums.user.SexEnum.UNKNOW.alias=unknown
com.wunding.learn.common.enums.user.SexEnum.MAN.alias=gentlemen
com.wunding.learn.common.enums.user.SexEnum.WOMEN.alias=Miss
com.wunding.learn.common.enums.other.ExaminationStatusEnum.UN_AUDIT.description=Pending review
com.wunding.learn.common.enums.other.ExaminationStatusEnum.AUDIT_PASS.description=Approved
com.wunding.learn.common.enums.other.ExaminationStatusEnum.AUDIT_REFUSE.description=Review rejection
com.wunding.learn.common.enums.other.ExcitationOperationEnum.INCREASE.name=Increase
com.wunding.learn.common.enums.other.ExcitationOperationEnum.DECREASE.name=reduce
com.wunding.learn.common.enums.market.HeadContentRuleEnum.GUESSYOULIKE.name=Guess you like
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RECOMMENDCOURSE.name=Recommended courses
com.wunding.learn.common.enums.market.HeadContentRuleEnum.POPULARCOURSE1.name=Popular courses 1
com.wunding.learn.common.enums.market.HeadContentRuleEnum.POPULARCOURSE2.name=Popular courses 2
com.wunding.learn.common.enums.market.HeadContentRuleEnum.POSTCOURSE.name=Job courses
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LEARNINGCOURSE.name=I'm learning
com.wunding.learn.common.enums.market.HeadContentRuleEnum.NEWS.name=Information
com.wunding.learn.common.enums.market.HeadContentRuleEnum.STUDYPROJECT.name=learning project
com.wunding.learn.common.enums.market.HeadContentRuleEnum.STUDYPROJECTSPECIFYCATEGORY.name=Specify category
com.wunding.learn.common.enums.market.HeadContentRuleEnum.SPECIAL.name=Topics I participated in
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LECTURERELEGANTDEMEANOUR1.name=Lecturer style 1
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LECTURERELEGANTDEMEANOUR2.name=Lecturer style 2
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LECTURERELEGANTDEMEANOUR3.name=Lecturer style 3
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LIVINGBROADCAST.name=Live broadcast
com.wunding.learn.common.enums.market.HeadContentRuleEnum.CURRENTLIVE.name=Current live broadcast
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LIVEPLAYBACK.name=Watch live broadcast
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LATESTTOPICS.name=Latest topics
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTTOPICS1.name=Hot Topic 1
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTTOPICS2.name=Hot Topic 2
com.wunding.learn.common.enums.market.HeadContentRuleEnum.ELITEPOSTS1.name=Top topic 1
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASEOFTHEMONTH1.name=Popular cases this month 1
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASEOFTHEMONTH2.name=Popular cases this month 2
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASEOFTHEMONTH3.name=Popular cases this month 3
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASE1.name=Popular Case 1
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASE2.name=Popular Case 2
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASE3.name=Popular Case 3
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RECOMMENDCASE1.name=Case quality level 1
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RECOMMENDCASE2.name=Case quality level 2
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RECOMMENDCASE3.name=Case quality level 3
com.wunding.learn.common.enums.market.HeadContentRuleEnum.TRAINPROJECT.name=training projects
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RAINPROJECTSPECIFYCATEGORY.name=Specify category
com.wunding.learn.common.enums.market.HeadContentRuleEnum.ORG_INFO.name=organize information
com.wunding.learn.common.enums.market.HeadContentRuleEnum.CLICK_CLOCK_IN.name=Click to check-in and clock in daily
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LEARN_CLOCK_IN.name=10 minutes of daily course learning
com.wunding.learn.common.enums.market.HeadContentRuleEnum.NON_LONG_TASK.name=Non long-term tasks
com.wunding.learn.common.enums.market.HeadContentRuleEnum.CONTAIN_LONG_TASK.name=All tasks
com.wunding.learn.common.enums.exam.CompetitionPeopleModelEnum.SINGLE.text=Single
com.wunding.learn.common.enums.exam.CompetitionPeopleModelEnum.DOUBLE.text=2 people
com.wunding.learn.common.enums.exam.CompetitionPeopleModelEnum.MULTI.text=multiple people
com.wunding.learn.common.enums.exam.CompetitionPeopleModelEnum.GROUP.text=Group
com.wunding.learn.common.enums.market.FirstInfoContentEnum.course.name=course
com.wunding.learn.common.enums.market.FirstInfoContentEnum.exam.name=take an exam
com.wunding.learn.common.enums.market.FirstInfoContentEnum.live.name=live streaming
com.wunding.learn.common.enums.market.FirstInfoContentEnum.exercise.name=practise
com.wunding.learn.common.enums.market.FirstInfoContentEnum.survey.name=Research
com.wunding.learn.common.enums.market.FirstInfoContentEnum.info.name=Information
com.wunding.learn.common.enums.market.FirstInfoContentEnum.project.name=learning project
com.wunding.learn.common.enums.market.FirstInfoContentEnum.faceProject.name=Face-to-face projects
com.wunding.learn.common.enums.market.FirstInfoContentEnum.vote.name=vote
com.wunding.learn.common.enums.market.FirstInfoContentEnum.special.name=Special topic learning
com.wunding.learn.common.enums.market.FirstInfoContentEnum.hyperlink.name=external links
com.wunding.learn.common.enums.market.FirstInfoContentEnum.accountstatement.name=study bill
com.wunding.learn.common.enums.push.MyStatisticEnum.COLLECT.title=collect
com.wunding.learn.common.enums.push.MyStatisticEnum.DOWNLOAD.title=download
com.wunding.learn.common.enums.push.MyStatisticEnum.ALLSIGNIN.title=Sign in
com.wunding.learn.common.enums.push.MyStatisticEnum.SUBJECT.title=topic
com.wunding.learn.common.enums.push.MyStatisticEnum.CERTIFICATE.title=Certificate
com.wunding.learn.common.enums.push.MyStatisticEnum.COURSE_TIME.title=class
com.wunding.learn.common.enums.push.MyStatisticEnum.INTEGRAL.title=integral
com.wunding.learn.common.enums.push.MyStatisticEnum.LEARN_TIME.title=hours
com.wunding.learn.common.enums.push.MyStatisticEnum.LEARN_CREDIT.title=credit
com.wunding.learn.common.enums.push.MyStatisticEnum.GOLD_COIN.title=gold
com.wunding.learn.common.enums.push.MyStatisticEnum.DESIGNATION.title=title
com.wunding.learn.common.enums.push.MyStatisticEnum.MEDAL.title=medal
com.wunding.learn.common.enums.push.MyStatisticEnum.SUGGEST.title=recommend
com.wunding.learn.project.service.enums.ProjectTaskTypeEnum.COURSE.taskName=course
com.wunding.learn.project.service.enums.ProjectTaskTypeEnum.EXAM.taskName=take an exam
com.wunding.learn.project.service.enums.ProjectTaskTypeEnum.EXERCISE.taskName=practise
com.wunding.learn.project.service.enums.ProjectTaskTypeEnum.SURVEY.taskName=Research
com.wunding.learn.project.service.enums.ProjectTaskTypeEnum.LIVE.taskName=live streaming
com.wunding.learn.project.service.enums.ProjectTaskTypeEnum.TRAIN.taskName=training course
com.wunding.learn.project.service.enums.ProjectTaskTypeEnum.PROJECT.taskName=project
com.wunding.learn.project.service.enums.ProjectTaskTypeEnum.APPLY.taskName=Sign up
com.wunding.learn.project.service.enums.ProjectTaskTypeEnum.SIGN.taskName=Sign in
com.wunding.learn.project.service.enums.ProjectTaskTypeEnum.EVALUATION.taskName=Evaluate
com.wunding.learn.project.service.enums.ProjectTaskTypeEnum.FORM.taskName=form
com.wunding.learn.project.service.enums.ProjectTaskTypeEnum.PRACTICAL_OPERATION.taskName=Practical operation
com.wunding.learn.common.enums.other.VoteType.CourseVote.text=Like the course
com.wunding.learn.common.enums.other.VoteType.CourseCommentVote.text=Course comments like
com.wunding.learn.common.enums.other.VoteType.InfoVote.text=Information Like
com.wunding.learn.common.enums.other.VoteType.InfoCommentVote.text=Information comments like
com.wunding.learn.common.enums.other.VoteType.SpecialCommentVote.text=Like the topic comments
com.wunding.learn.common.enums.other.VoteType.TrainCommentVote.text=Training class comments and likes
com.wunding.learn.common.enums.other.VoteType.ExampleVote.text=Like the case
com.wunding.learn.common.enums.other.VoteType.ExampleCommentVote.text=Like case comments
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.SYSTEM.name=Global
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.COURSE.name=course
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.COURSE_WARE.name=Courseware
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.EXAM.name=take an exam
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.EXERCISE.name=practise
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.LIVE.name=live streaming
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.SURVEY.name=Research
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.PROJECT.name=learning project
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.NEWS.name=Information
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.EVALUATION.name=Evaluate
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.TOPIC.name=Topic section
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.LECTURER.name=lecturer
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.SIGN.name=Sign in
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.FEEDBACK.name=feedback
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.EXAMPLE.name=Case library
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.READING.name=Read together
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.PROMOTEDGAME_CHECKPOINT.name=Beat the levels
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.TRAIN.name=training projects
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.EXAM_COMPETITION_SINGLE.name=Exam competition (single player)
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.EXAM_COMPETITION_DOUBLE.name=Exam competition (double)
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.EXAM_COMPETITION_MULTI.name=Exam competition (multiple people)
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.EXAM_COMPETITION_GROUP.name=Exam competition (team)
com.wunding.learn.common.enums.other.OnWorkEnum.NON_WORK.name=Resign
com.wunding.learn.common.enums.other.OnWorkEnum.ON_WORK.name=On the job
com.wunding.learn.common.enums.other.OnWorkEnum.DELETE.name=delete
com.wunding.learn.common.enums.exam.ScoreRuleTypeEnum.COMPETITION.name=competition
com.wunding.learn.common.enums.exam.ScoreRuleTypeEnum.COMPETITION_SESSION.name=Number of sessions
com.wunding.learn.common.enums.other.ContentRuleEnum.HOT_COURSE.orderRuleDesc=
com.wunding.learn.common.enums.other.ContentRuleEnum.LEARNING_COURSE.orderRuleDesc=
com.wunding.learn.common.enums.other.ContentRuleEnum.SCORE_LECTURER.orderRuleDesc=Sort by rating
com.wunding.learn.common.enums.other.ContentRuleEnum.TIME_LECTURER.orderRuleDesc=Sort by latest teaching time
com.wunding.learn.common.enums.other.ContentRuleEnum.NEW_TOPIC_NOT_TOP.orderRuleDesc=Creation time inversion
com.wunding.learn.common.enums.other.ContentRuleEnum.HOT_TOPIC_COMMENT.orderRuleDesc=Sort by number of comments
com.wunding.learn.common.enums.other.ContentRuleEnum.HOT_TOPIC_VIEW.orderRuleDesc=Sort by number of tours
com.wunding.learn.common.enums.other.ContentRuleEnum.NEW_TOPIC_TOP.orderRuleDesc=Creation time inversion
com.wunding.learn.common.enums.other.ContentRuleEnum.HOT_CASE.orderRuleDesc=Sort by download count
com.wunding.learn.common.enums.other.ContentRuleEnum.NEW_CASE.orderRuleDesc=Sort by creation time
com.wunding.learn.common.enums.other.ContentRuleEnum.LIVING_LIVE.orderRuleDesc=
com.wunding.learn.common.enums.other.ContentRuleEnum.CURRENT_LIVE.orderRuleDesc=
com.wunding.learn.common.enums.other.ContentRuleEnum.ROLLBACK_LIVE.orderRuleDesc=
com.wunding.learn.common.enums.evaluation.EvaluationSourceEnum.MANUAL_UPLOAD.message=Manual upload
com.wunding.learn.common.enums.evaluation.EvaluationSourceEnum.EVAL_LIB.message=Evaluation library
com.wunding.learn.common.enums.evaluation.EvaluationSourceEnum.DOCUMENT.message=Database
com.wunding.learn.common.enums.exam.GroupPKCompetitionSessionUserTypeEnum.WAIT_JOIN.name=To be added
com.wunding.learn.common.enums.exam.GroupPKCompetitionSessionUserTypeEnum.JOINED.name=Already joined
com.wunding.learn.common.enums.exam.GroupPKCompetitionSessionUserTypeEnum.READY.name=Prepared
com.wunding.learn.common.enums.exam.GroupPKCompetitionSessionUserTypeEnum.START_ANSWER.name=Start answering questions
com.wunding.learn.common.enums.other.ResourceChangeEnum.course.name=course
com.wunding.learn.common.enums.other.ResourceChangeEnum.info.name=Information
com.wunding.learn.common.enums.other.ResourceChangeEnum.exam.name=take an exam
com.wunding.learn.common.enums.other.ResourceChangeEnum.live.name=live streaming
com.wunding.learn.common.enums.other.ResourceChangeEnum.exercise.name=practise
com.wunding.learn.common.enums.other.ResourceChangeEnum.survey.name=Research
com.wunding.learn.common.enums.other.ResourceChangeEnum.project.name=learning project
com.wunding.learn.common.enums.other.ResourceChangeEnum.vote.name=vote
com.wunding.learn.common.enums.other.ResourceChangeEnum.special.name=Special topic system
com.wunding.learn.common.enums.other.ResourceChangeEnum.hyperlink.name=external links
com.wunding.learn.common.enums.other.ResourceChangeEnum.accountstatement.name=study bill
com.wunding.learn.common.enums.other.ResourceChangeEnum.example.name=Case library
com.wunding.learn.common.enums.other.ResourceChangeEnum.orgCate.name=Organization classification
com.wunding.learn.common.enums.project.ProjectTaskType.course.name=course
com.wunding.learn.common.enums.project.ProjectTaskType.exam.name=take an exam
com.wunding.learn.common.enums.project.ProjectTaskType.exercise.name=practise
com.wunding.learn.common.enums.project.ProjectTaskType.survey.name=Research
com.wunding.learn.common.enums.project.ProjectTaskType.live.name=live streaming
com.wunding.learn.common.enums.project.ProjectTaskType.project.name=project
com.wunding.learn.common.enums.project.ProjectTaskType.mentor.name=tutor
com.wunding.learn.common.enums.project.ProjectTaskType.lecturer.name=lecturer
com.wunding.learn.common.enums.user.SexEnum.UNKNOW.name=unknown
com.wunding.learn.common.enums.user.SexEnum.MAN.name=male
com.wunding.learn.common.enums.user.SexEnum.WOMEN.name=female
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.ACCEPT_FEEDBACK.name=Feedback acceptance
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.AUDIT_COURSEWARE.name=The administrator approves the courseware uploaded by students
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.BEST_REPLY_TOPIC.name=Best topic reply
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COLLECT_COURSE.name=Save course
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COMMENT_COURSE.name=Comment on a course
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COMMENT_COURSEWARE.name=Comment on courseware
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COMMENT_NEWS.name=Comment information
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COMMIT_EVALUATE.name=Submit assessment
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COMMIT_EXAM.name=Submit exam
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COMMIT_EXERCISE.name=Complete the exercise
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COMMIT_FEEDBACK.name=Submit feedback
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COMMIT_HOMEWORK.name=Submit assignment
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COMMIT_SIGN.name=Complete check-in
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COURSE_STAR.name=Course rating
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COURSE_VOTE.name=Like the course
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COURSEWARE_STAR.name=Courseware was rated with stars
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COURSEWARE_VIEW.name=Number of courseware views
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COURSEWARE_VOTE.name=Number of likes for the courseware
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.EVERYDAY_LEARN_COURSE.name=Complete one course every day in a row (check-in times)
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.EVERYDAY_LEARN_COURSEWARE.name=Complete one courseware every day in a row (check-in times)
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.EVERYDAY_LOGIN.name=Continuously log in and clock in every day (number of clock ins)
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.EXAMPLE_ADD.name=Submit case
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.EXAMPLE_AUDIT.name=Participate in review cases
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.EXAMPLE_AUDIT_OVER.name=After the case review is passed, the review score will be rewarded
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.EXAMPLE_COMPANY.name=Case is set to company level
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.EXAMPLE_EXTRA.name=The case is marked as high quality
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.EXAMPLE_GROUP.name=Case is set to group level
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.EXAM_PROCESS.name=Exam score range incentives
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.FINISH_COURSE.name=Complete the course
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.FINISH_COURSEWARE.name=Finished the courseware(Fixed)
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.FINISH_COURSEWARE_BYTIME.name=Finished the courseware(According to the duration of the courseware)
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.FINISH_PROJECT.name=Complete project
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.FINISH_PROJECT_APPLY.name=Complete project registration
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.FINISH_SURVEY.name=Submit research
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.FIRST_LOGIN.name=Activating punch card for the first time
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.FOCUS_TOPIC.name=Follow the topic
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.INTEGRAL_CLEARING.name=Points cleared
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.LEARN_COURSE.name=Click on the course to learn
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.LEARN_COURSEWARE.name=Click on the courseware to learn
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.LECTURER_JOIN_PROJECT_TEACH.name=Participate in teaching projects
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.LECTURER_TEACH_COURSEWARE.name=Number of coursewares participated in teaching
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.LIVE_CONTRIBUTE.name=Contribute knowledge and obtain contribution credits by initiating live broadcasts
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.LIVE_INTERACT.name=Interact with the anchor in the live broadcast room to give points
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.LIVE_INTERACT_COUNT.name=Points will be awarded if the live broadcast initiated reaches a certain amount of viewing and interaction
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.LIVE_QUESTION_AND_ANSWER.name=Anchors can ask questions with gold coin rewards in the live broadcast room, and students can get gold coins by answering questions in the live broadcast room.
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.MAKE_LIVE.name=Start a live broadcast to give points
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.MAKE_TOPIC.name=Post a topic
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.PARTICIPATION_DOUBLE.name=Participate and obtain (double)
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.PARTICIPATION_GROUP.name=Participate in acquisition (team formation)
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.PARTICIPATION_MULTI.name=Participate in acquisition (multiple people)
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.PARTICIPATION_SINGLE.name=Participate in acquisition (single player)
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.PERSONAL_CHAMPION_MULTI.name=Individual championship (multiple people)
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.PERSONAL_SECOND_MULTI.name=Individual runner-up (multiple people)
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.PERSONAL_THIRD_MULTI.name=Individual third place (multiple people)
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.PERSONAL_WIN_DOUBLE.name=Individual victory (double)
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.PERSONAL_WIN_SINGLE.name=Personal victory (single player)
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.PROJECT_TASK_PROCESS.name=task completion rate
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.PROMOTED_GAME_FINISH_CHECKPOINT.name=Completed the level
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.READING_BOOK_NUM.name=Number of books read in total
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.READING_CLOCK_TIMES.name=Total number of punch-ins read
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.READING_COMMENT_EXPNUM.name=Number of comments read in total
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.READING_EXPERIENCE_TIMES.name=Number of submissions of shared reading experience
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.REPLY_COURSE_COMMENT.name=Reply to course comments
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.REPLY_COURSEWARE_COMMENT.name=Reply to courseware comments
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.REPLY_TOPIC.name=Reply
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.SHARE_COURSE.name=Share course
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.SHARE_COURSEWARE.name=Share courseware
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.STAR_COURSEWARE.name=Courseware rating
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.TEAM_WIN_GROUP.name=Team wins (Team formation)
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.TOPIC_COMMENT_COUNT.name=Number of topic replies
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.VIEW_COURSE_PROCESS.name=Browsing time progress of all courseware (percentage)
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.VIEW_COURSEWARE_PROCESS.name=Courseware browsing time progress (percentage)
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.VIEW_LIVE.name=Click to watch live broadcast
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.VIEW_LIVE_TOTAL_TIME.name=The cumulative live viewing time reaches the specified time
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.VIEW_NEWS.name=Click to view information
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.VOTE_COURSE_COMMENT.name=Like course comments
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.VOTE_LECTURER.name=Students like the lecturer
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.VOTE_NEWS_COMMENT.name=Like information comments
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COURSE_POSTER_SHARE.name=Sharing courses
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.PROJECT_POSTER_SHARE.name=Sharing project
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.LIVE_POSTER_SHARE.name=Sharing live
com.wunding.learn.train.service.enums.LearnMapCategoryEnum.POST.name=post
com.wunding.learn.train.service.enums.LearnMapCategoryEnum.GROUP.name=group scene type
com.wunding.learn.train.service.enums.LearnMapCategoryEnum.COMPETENCY_STANDARD.name=Job competency (standard)
com.wunding.learn.train.service.enums.LearnMapCategoryEnum.COMPETENCY.name=Job competency
com.wunding.learn.common.enums.other.CategoryTypeEnum.TrainType.name=Training Category
com.wunding.learn.common.enums.other.CategoryTypeEnum.ActivityType.name=Activities
com.wunding.learn.common.enums.other.CategoryTypeEnum.SupplierTerritory.name=Supplier cooperation field categories
com.wunding.learn.common.enums.other.CategoryTypeEnum.SupplierFile.name=Supplier profile category
com.wunding.learn.common.enums.other.CategoryTypeEnum.FormTemplateCate.name=Form template classification
com.wunding.learn.common.enums.other.CategoryTypeEnum.TrainSubjectCategory.name=Classification of training topics
com.wunding.learn.common.enums.other.CategoryTypeEnum.IndustryType.name=Industry type
com.wunding.learn.common.enums.other.CategoryTypeEnum.CompanySize.name=Company size
com.wunding.learn.common.enums.other.CategoryTypeEnum.CourseCategory.name=Course category
com.wunding.learn.common.enums.other.CategoryTypeEnum.TrainWithoutExam.name=External training-exam classification
com.wunding.learn.common.enums.other.CategoryTypeEnum.CourseWithoutResource.name=External course resources
com.wunding.learn.common.enums.other.CategoryTypeEnum.AllocationType.name=Incentive configuration type
com.wunding.learn.common.enums.other.CategoryTypeEnum.ExamCompetitionTrainType.name=Exam competition training categories
com.wunding.learn.common.enums.other.CategoryTypeEnum.LearnMapSpecialty.name=Competency map professional
com.wunding.learn.common.enums.other.CategoryTypeEnum.APPLICABLE_HIERARCHY.name=Applicable levels of assessment tools
com.wunding.learn.common.enums.other.CategoryTypeEnum.USAGE_CLASSIFICATION.name=Assessment tool usage classification
com.wunding.learn.common.enums.other.CategoryTypeEnum.ASSESS_PROJECT.name=Assessment items
com.wunding.learn.common.enums.other.CategoryTypeEnum.MentorCate.name=Mentor Classification
com.wunding.learn.common.enums.other.CategoryTypeEnum.EXAMPLE_BUSINESS.name=Case Line
com.wunding.learn.common.enums.other.CategoryTypeEnum.ExampleAudit.name=Case Review Criteria
com.wunding.learn.file.api.constant.ExportFileNameEnum.Course.type=Course List
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseStudyDetail.type=Course study details list
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseCategoryExport.type=Course classification list
com.wunding.learn.file.api.constant.ExportFileNameEnum.CoursewareStudyDetail.type=Courseware study details list
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseComment.type=Course review list
com.wunding.learn.file.api.constant.ExportFileNameEnum.SharedLibrary.type=Course shared library list
com.wunding.learn.file.api.constant.ExportFileNameEnum.CoursewarePackage.type=List of courseware uploaded by students
com.wunding.learn.file.api.constant.ExportFileNameEnum.MergeCourse.type=Merge courseware into course list
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseWareLearn.type=Student courseware learning details list
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseLearn.type=Course study details list
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseCategory.type=Course classification management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseTagManage.type=Course tag management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseSysCategory.type=Course tag classification management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseTagSta.type=Course tag statistics list
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseStudyStatistics.type=Course learning Statistics - by Department
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseStudyStatisticsUser.type=Personnel details
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseNote.type=Course Notes
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseStudyCourseLearnUser.type=Learning Statistics-by Course-Personnel Details
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseWithout.type=External Course List
com.wunding.learn.file.api.constant.ExportFileNameEnum.Exam.type=Exam management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamCorrect.type=Change volume management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamCorrectRecord.type=Answer record list
com.wunding.learn.file.api.constant.ExportFileNameEnum.SchemaList.type=Volume plan list
com.wunding.learn.file.api.constant.ExportFileNameEnum.Exercise.type=Exercise management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamEmployeeResultsDetail.type=List of Student Exam Scores Details
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamAnswerOfStatisticalOrg.type=Exam answer statistics by department
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamAnswerOfStatisticalPost.type=Exam answer statistics by position
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamAnswerRecord.type=Exam score details
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamAnswerRecordDetail.type=Exam answer record details
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamAnalysisQuestion.type=Answer analysis
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamQuestion.type=Exam question details
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamCompetition.type=Exam competition management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamCompetitionUserScoreRank.type=Score ranking
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamCompetitionUserAnswerRank.type=Answer ranking
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamCompetitionSessionUser.type=Event list
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamCompetitionAnswerRecord.type=Answer list
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamCompetitionSessionStat.type=Event details
com.wunding.learn.file.api.constant.ExportFileNameEnum.CompetitionAnswerRecordStat.type=Answer statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamStatistic.type=Statistics list by exam status
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamJoinUserByBizRecord.type=Exam status statistics-Participating users-Follow
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamNotJoinUserByBizRecord.type=Exam status statistics - users who did not participate - follow
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamPostUserByBizRecord.type=Examination statistics-submission users-according to
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamNotPassUserByBizRecord.type=Exam statistics - failed users - follow
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamPassUserByBizRecord.type=Examination Statistics-By User-Follow
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamLimitUserByBizRecord.type=Examination Statistics-Distributed to Users-Follow
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExampleLib.type=Case library management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExpertLib.type=Expert database management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExampleBusiness.type=Case line management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExampleComment.type=Case comment management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExampleCategory.type=Case classification management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExampleAuditCategory.type=Case review classification management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.Live.type=Live broadcast management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.LiveStatic.type=Live broadcast statistics list
com.wunding.learn.file.api.constant.ExportFileNameEnum.LiveVod.type=Live playback list
com.wunding.learn.file.api.constant.ExportFileNameEnum.Post.type=Topic management post list
com.wunding.learn.file.api.constant.ExportFileNameEnum.PostBanUser.type=Ban list management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.PostSection.type=Topic section management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.PostSectionExpert.type=Topic section expert management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.PostCountComment.type=Reply statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.Emigrated.type=Breakthrough game management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.Checkpoint.type=List of levels to pass
com.wunding.learn.file.api.constant.ExportFileNameEnum.CheckpointTask.type=Clearance task list
com.wunding.learn.file.api.constant.ExportFileNameEnum.StylesTemplate.type=Style management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.Notice.type=Announcement management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.Team.type=Team management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.Statistical.type=List of pass statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.TaskStatistical.type=View detailed list
com.wunding.learn.file.api.constant.ExportFileNameEnum.Info.type=News information list
com.wunding.learn.file.api.constant.ExportFileNameEnum.Category.type=Category management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.Comment.type=Information comment management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.Survey.type=Research list
com.wunding.learn.file.api.constant.ExportFileNameEnum.SurveyAnalysis.type=Research analysis details
com.wunding.learn.file.api.constant.ExportFileNameEnum.SurveyRecordDetail.type=All survey details
com.wunding.learn.file.api.constant.ExportFileNameEnum.StudyProject.type=Learn project management checklists
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectFixedDate.type=Date Project Management List
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectFixedCycle.type=Cycle project management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.QuickProject.type=Quick training management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseTaskProject.type=Course task list
com.wunding.learn.file.api.constant.ExportFileNameEnum.FormTemplate.type=Tutoring Template Management List
com.wunding.learn.file.api.constant.ExportFileNameEnum.Position.type=Job Development List
com.wunding.learn.file.api.constant.ExportFileNameEnum.Classroom.type=Classroom List
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainingPlan.type=Training plan management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectMentor.type=Tutor management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectLecturer.type=Lecturer management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectApplyUser.type=Register and manage user list
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectSignUser.type=Sign-in management user list
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectPost.type=Topic management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectWork.type=Job management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.EvalReplyUserList.type=List of evaluators
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectEvaluation.type=Evaluate statistical analysis results
com.wunding.learn.file.api.constant.ExportFileNameEnum.PROJECT_EVALUATION_DETAIL.type=Assessment Statistical Analysis Details
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectFormTemplate.type=Counseling results
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectHomeWork.type=Job details
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectCompletion.type=Completion status
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticTaskDetail.type=Learning project detailed statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.PROJECT_FIXED_CYCLE_STATISTIC_TASK_DETAIL.type=Detailed statistics of periodic projects
com.wunding.learn.file.api.constant.ExportFileNameEnum.QuickProjectStatisticTaskDetail.type=Quick training detailed statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseTaskProjectStatisticTaskDetail.type=Detailed statistics of course tasks
com.wunding.learn.file.api.constant.ExportFileNameEnum.FaceProjectStatisticTaskDetail.type=Detailed statistics of face-to-face project tasks
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticPersonRank.type=Personal learning ranking statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticOrgCompleteUserDetailIsFinish.type=Learning Project - Department Completion Statistics User Details - Completed
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticOrgCompleteUserDetailNotFinish.type=Learning Project - Department Completion Statistics User Details - Incomplete
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticTeamCompleteUserDetailIsFinish.type=Learning Project - Team Completion Statistics User Details - Completed
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticTeamCompleteUserDetailNotFinish.type=Learning Project - Team Completion Statistics User Details - Incomplete
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticTeamLearnRank.type=Team Learning Ranking Statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticOrgLearnRank.type=Department study ranking statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatistic.type=Training class statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectHoldStatistic.type=Learning project hosting statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectJoiningStatistic.type=Learning project participation statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerTrainStatistic.type=Instructor training statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerTrainStatisticByBiz.type=Instructor training statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticLecturer.type=Training class lecturer statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectOrgStatistic.type=Training class organization statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.FormTemplateColumn.type=Task record list
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticByType.type=Statistics by type-project
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticByTypeUser.type=By Type-Project Statistics-
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticJoinUser.type=Project statistics-participant details
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticByProject.type=Project statistics - by project
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticByOrg.type=Project Statistics - By Department
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticByUser.type=Project Statistics - By Personnel
com.wunding.learn.file.api.constant.ExportFileNameEnum.SpecialFixedDate.type=Special management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.SpecialFixedCycle.type=Periodic topic management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.SpecialCategory.type=Topic classification management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.SpecialLabel.type=Topic tag management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.SpecialCompletion.type=Special topic completion status
com.wunding.learn.file.api.constant.ExportFileNameEnum.SpecialStatisticTaskDetail.type=Topic detailed statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.Lecturer.type=Lecturer management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerCategory.type=Lecturer classification setting list
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerLevel.type=Instructor level setting list
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerModifyRecord.type=Lecturer movement record list
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerExaminationAudit.type=Course completion review list
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerUpgradeConfig.type=Rule configuration_promotion rule list
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerDemotionConfig.type=Rule configuration_downgrade rule list
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerRemovalConfig.type=Rule configuration_outbound rule list
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerExamination.type=Detailed list of teaching time information
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerExaminationAssess.type=Detailed list of teaching evaluation information
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerTeachDetail.type=Detailed list of offline teaching
com.wunding.learn.file.api.constant.ExportFileNameEnum.InsideLecturer.type=Lecturer statistics (by department)-lecturer management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.NotWorkday.type=List of non-working days
com.wunding.learn.file.api.constant.ExportFileNameEnum.Certification.type=Certificate management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.CertificationSetup.type=Certificate System List
com.wunding.learn.file.api.constant.ExportFileNameEnum.CertificationLevel.type=Certificate level management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.CertificationRule.type=List of certification rules
com.wunding.learn.file.api.constant.ExportFileNameEnum.CertificationRelate.type=List of certificates held
com.wunding.learn.file.api.constant.ExportFileNameEnum.CertificationRelateDept.type=List of department certificate details
com.wunding.learn.file.api.constant.ExportFileNameEnum.CertificationHoldUser.type=List of certificates to be managed
com.wunding.learn.file.api.constant.ExportFileNameEnum.CertificationHoldUserStat.type=List of statistical management certificates that should be held
com.wunding.learn.file.api.constant.ExportFileNameEnum.CertificationDeptTarget.type=Department certification target management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.CerDeptTargetReport.type=Department certification target statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.ASSESS_TOOL_LIST.type=Assessment tool list
com.wunding.learn.file.api.constant.ExportFileNameEnum.ASSESS_PROJECT_LIST.type=List of assessment items
com.wunding.learn.file.api.constant.ExportFileNameEnum.ASSESS_USER_LIST2.type=Evaluation user list-export method 2
com.wunding.learn.file.api.constant.ExportFileNameEnum.ASSESS_USER_LIST1.type=Evaluation user list-export method 1
com.wunding.learn.file.api.constant.ExportFileNameEnum.ASSESS_DEP_MANAGER_USER_LIST.type=Department evaluator management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.ASSESS_PROJECT_ASSESS_DETAILS.type=Evaluation item evaluation detailed list
com.wunding.learn.file.api.constant.ExportFileNameEnum.ASSESS_QUESTION_DETAIL_LIST.type=Detailed list of assessment questions
com.wunding.learn.file.api.constant.ExportFileNameEnum.Reading.type=Shared reading management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.ReadingBooksManage.type=Book management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.ReadingSign.type=Check-in list
com.wunding.learn.file.api.constant.ExportFileNameEnum.ReadingBookExperience.type=Experience list
com.wunding.learn.file.api.constant.ExportFileNameEnum.ReadingBookExperienceComment.type=Comment management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.ReadingBookExperienceStar.type=Like management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.ReadingReports.type=Report list
com.wunding.learn.file.api.constant.ExportFileNameEnum.ReadingStatistics.type=Book statistics list
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserRankReadingStatistics.type=Ranking statistics of students’ total reading
com.wunding.learn.file.api.constant.ExportFileNameEnum.TaskFinishReadingStatistics.type=Task completion statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.Appraise.type=Evaluation List
com.wunding.learn.file.api.constant.ExportFileNameEnum.AppraiseLecturer.type=Lecturer Evaluation List
com.wunding.learn.file.api.constant.ExportFileNameEnum.Meeting.type=Qualification Defense List
com.wunding.learn.file.api.constant.ExportFileNameEnum.AppraiseReferee.type=Judges list
com.wunding.learn.file.api.constant.ExportFileNameEnum.MeetingReferee.type=Judges list
com.wunding.learn.file.api.constant.ExportFileNameEnum.AppraiseProvider.type=Reviewee list
com.wunding.learn.file.api.constant.ExportFileNameEnum.MeetingProvider.type=Respondent list
com.wunding.learn.file.api.constant.ExportFileNameEnum.AppraiseFileType.type=Material rules list
com.wunding.learn.file.api.constant.ExportFileNameEnum.AppraiseProviderFile.type=Material management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.AppraiseDetail.type=Rating details list
com.wunding.learn.file.api.constant.ExportFileNameEnum.MeetingScoreDetail.type=Rating statistics list
com.wunding.learn.file.api.constant.ExportFileNameEnum.AppraiseShowDetail.type=View detailed list
com.wunding.learn.file.api.constant.ExportFileNameEnum.AppraiseHistory.type=Rating history list
com.wunding.learn.file.api.constant.ExportFileNameEnum.Recruiting.type=Recruitment List
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerRecruiting.type=Lecturer Recruitment List
com.wunding.learn.file.api.constant.ExportFileNameEnum.RecruitingAssistant.type=List of co-organizers
com.wunding.learn.file.api.constant.ExportFileNameEnum.RecruitingMaterial.type=List of promotional materials
com.wunding.learn.file.api.constant.ExportFileNameEnum.RecruitingMaterialRule.type=Material rules list
com.wunding.learn.file.api.constant.ExportFileNameEnum.RecruitingAudit.type=Recruitment review list
com.wunding.learn.file.api.constant.ExportFileNameEnum.RecruitingAuditSurveyDetail.type=Recruitment review survey detailed list
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseWareLib.type=Courseware library list
com.wunding.learn.file.api.constant.ExportFileNameEnum.courseWareLibraryCategory.type=Courseware library classification
com.wunding.learn.file.api.constant.ExportFileNameEnum.TestPaperLib.type=Test paper library list
com.wunding.learn.file.api.constant.ExportFileNameEnum.TestPaperLibraryCategory.type=Test paper library classification
com.wunding.learn.file.api.constant.ExportFileNameEnum.TestPaperQuestion.type=Question management
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamLib.type=Test question bank list
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamLibQuestion.type=Question management
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamLibCategory.type=Test question bank classification
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExerciseLib.type=Exercise library list
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExerciseLibCategory.type=Exercise library classification
com.wunding.learn.file.api.constant.ExportFileNameEnum.SurveyLib.type=Research database list
com.wunding.learn.file.api.constant.ExportFileNameEnum.SurveyLibCategory.type=Research database classification
com.wunding.learn.file.api.constant.ExportFileNameEnum.MaterialLib.type=Knowledge base list
com.wunding.learn.file.api.constant.ExportFileNameEnum.MaterialCategory.type=Knowledge Base Tags
com.wunding.learn.file.api.constant.ExportFileNameEnum.KnowledgeBaseType.type=Knowledge base classification
com.wunding.learn.file.api.constant.ExportFileNameEnum.ResearchField.type=Lecturer's research area
com.wunding.learn.file.api.constant.ExportFileNameEnum.EvaluationLib.type=Evaluation library list
com.wunding.learn.file.api.constant.ExportFileNameEnum.EvaluationCategory.type=Evaluation library classification list
com.wunding.learn.file.api.constant.ExportFileNameEnum.FormTemplateManage.type=Form template management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamOrgStatistic.type=List of exam statistics by department
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectLecturerTeachDetail.type=Lecturer’s teaching details list
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerTeachStatisticDetail.type=Lecturer’s teaching statistics list
com.wunding.learn.file.api.constant.ExportFileNameEnum.Award.type=Prize management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.AwardCategory.type=Prize classification management
com.wunding.learn.file.api.constant.ExportFileNameEnum.GameLottery.type=Points winning list
com.wunding.learn.file.api.constant.ExportFileNameEnum.MailTemplate.type=Email template list
com.wunding.learn.file.api.constant.ExportFileNameEnum.PushManage.type=Push management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.PushMessageManage.type=Message management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.SignStatistics.type=Sign-in statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.SignStat.type=Sign-in statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectSignStat.type=Project sign-in statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainSignStat.type=Training sign-in statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.SignList.type=Check-in list
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectSignList.type=Project sign-in list
com.wunding.learn.file.api.constant.ExportFileNameEnum.AwardRedeemRecord.type=gold coin exchange
com.wunding.learn.file.api.constant.ExportFileNameEnum.AwardExchangeRecord.type=Exchange record
com.wunding.learn.file.api.constant.ExportFileNameEnum.PaymentOrder.type=order list
com.wunding.learn.file.api.constant.ExportFileNameEnum.PaymentOrgOrderMember.type=Institutional order member list
com.wunding.learn.file.api.constant.ExportFileNameEnum.VoteManage.type=Voting management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.VoteContent.type=Voting content management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.VoteStatistics.type=Voting statistics list
com.wunding.learn.file.api.constant.ExportFileNameEnum.VoteDetail.type=Voting details list
com.wunding.learn.file.api.constant.ExportFileNameEnum.User.type=User management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserExpand.type=User management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserExpandTemplate.type=User import template
com.wunding.learn.file.api.constant.ExportFileNameEnum.Org.type=Organization list
com.wunding.learn.file.api.constant.ExportFileNameEnum.Role.type=Role management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.Dict.type=Data dictionary management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.Version.type=Version management
com.wunding.learn.file.api.constant.ExportFileNameEnum.Title.type=Title setting list
com.wunding.learn.file.api.constant.ExportFileNameEnum.HomePageConfig.type=Home page configuration list
com.wunding.learn.file.api.constant.ExportFileNameEnum.Feedback.type=Feedback management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.IdentityPostSystemTemplate.type=Position system import template
com.wunding.learn.file.api.constant.ExportFileNameEnum.IdentityPost.type=Position identity list
com.wunding.learn.file.api.constant.ExportFileNameEnum.IdentityPostTemplate.type=Position identity import template
com.wunding.learn.file.api.constant.ExportFileNameEnum.IdentityPostSystemData.type=Position system data list
com.wunding.learn.file.api.constant.ExportFileNameEnum.Expert.type=Expert review database data list
com.wunding.learn.file.api.constant.ExportFileNameEnum.IdentityTime.type=time identity list
com.wunding.learn.file.api.constant.ExportFileNameEnum.Identity.type=identity list
com.wunding.learn.file.api.constant.ExportFileNameEnum.InfoStatAnalysis.type=Information access statistics list
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnRecordStatAnalysis.type=Student profile list
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnRecordCourseStatAnalysis.type=Student profile statistics-list of new courses
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnRecordExamStatAnalysis.type=Student profile statistics-list of participating exams
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnRecordInfoStatAnalysis.type=Student profile statistics-View information list
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnRecordProjectStatAnalysis.type=Student profile statistics - list of participating learning projects
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnRecordFaceProjectStatAnalysis.type=Student profile statistics - a list of classes that have participated in face-to-face classes
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnRecordTrainStatAnalysis.type=Participant Profile Statistics - List of training programs participated
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnRecordSurveyStatAnalysis.type=Student profile statistics - survey participation list
com.wunding.learn.file.api.constant.ExportFileNameEnum.ScoreRankStatAnalysis.type=Experience ranking list
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserIntegralStatAnalysis.type=Points statistics list
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerIntegral.type=Points statistics list - by instructor
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserIntegralDetail.type=Points statistics details
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserIntegralDetailStatAnalysis.type=List of points statistics details
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseLearnState.type=Course Statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseLearned.type=Course learning records
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseAgreeDetail.type=Course likes record
com.wunding.learn.file.api.constant.ExportFileNameEnum.CoursewareLearnStatAnalysis.type=Courseware learning statistics list
com.wunding.learn.file.api.constant.ExportFileNameEnum.CoursewareLearnDetailStatAnalysis.type=List of courseware learning statistics details
com.wunding.learn.file.api.constant.ExportFileNameEnum.OrgLearnStatAnalysis.type=Department Learning Statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamStateStatAnalysis.type=Exam statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamStateStatPartAnalysis.type=Examination Statistics Number of Participants
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamStateStatPassAnalysis.type=Exam Statistics: Number of Passers
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamStateStatPostAnalysis.type=Exam statistics: Number of people submitting papers
com.wunding.learn.file.api.constant.ExportFileNameEnum.GldTradeStatAnalysis.type=Gold coin transaction inquiry
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExchangeRecordStatAnalysis.type=Exchange record query
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExcitationCollectStatAnalysis.type=Summary of student incentives
com.wunding.learn.file.api.constant.ExportFileNameEnum.InsideLecturerByDepartment.type=Lecturer Statistics (by Department)
com.wunding.learn.file.api.constant.ExportFileNameEnum.InsideLecturerByCategory.type=Instructor Statistics (by Category)
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserExcitationRecordStatAnalysis.type=Goal Incentive Detailed Inquiry
com.wunding.learn.file.api.constant.ExportFileNameEnum.OnlineUserStatAnalysis.type=Online user statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainPlanStatAnalysis.type=Training program statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.TimeRegionStatAnalysis.type=Visit period statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.StudentUploadCoursewareStatAnalysis.type=Statistics of courseware uploaded by students
com.wunding.learn.file.api.constant.ExportFileNameEnum.SearchKeyStatAnalysis.type=Search keyword statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.CertifiedCategory.type=Certification classification list
com.wunding.learn.file.api.constant.ExportFileNameEnum.Log.type=Operation log
com.wunding.learn.file.api.constant.ExportFileNameEnum.BizLog.type=Business operation log
com.wunding.learn.file.api.constant.ExportFileNameEnum.OrgThirdSyncRecord.type=Department synchronization history
com.wunding.learn.file.api.constant.ExportFileNameEnum.PostThirdSyncRecord.type=Job synchronization history
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserThirdSyncRecord.type=Personnel synchronization history
com.wunding.learn.file.api.constant.ExportFileNameEnum.CertificationRelateWatermarkImg.type=Detailed pictures
com.wunding.learn.file.api.constant.ExportFileNameEnum.Train.type=Training project list
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainActivity.type=Training project activity list
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainProject.type=Class management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainActivityPracticeRecord.type=Practical management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.MedalUserRelation.type=Medal list
com.wunding.learn.file.api.constant.ExportFileNameEnum.FormManage.type=Form management checklist list
com.wunding.learn.file.api.constant.ExportFileNameEnum.Plan.type=Training program list
com.wunding.learn.file.api.constant.ExportFileNameEnum.PlanAudit.type=Training Program Management-Audit List
com.wunding.learn.file.api.constant.ExportFileNameEnum.PlanInventory.type=Training plan checklist list
com.wunding.learn.file.api.constant.ExportFileNameEnum.PlanInventoryTemplate.type=Training plan checklist template
com.wunding.learn.file.api.constant.ExportFileNameEnum.PlanTrain.type=Training project list
com.wunding.learn.file.api.constant.ExportFileNameEnum.PlanStatistic.type=Training plan summary table
com.wunding.learn.file.api.constant.ExportFileNameEnum.PlanInventoryStatistic.type=Training plan item details
com.wunding.learn.file.api.constant.ExportFileNameEnum.PlanExecuteMonth.type=Training plan execution statistics - by month
com.wunding.learn.file.api.constant.ExportFileNameEnum.PlanExecuteYear.type=Training plan execution statistics - by year
com.wunding.learn.file.api.constant.ExportFileNameEnum.Leaflet.type=List of leaflets
com.wunding.learn.file.api.constant.ExportFileNameEnum.PlanCategory.type=Training Program Category
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerCourseAuthByLecturer.type=Course certified instructors are exported by instructor
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerCourseAuthByCourse.type=Course certified instructors are exported according to the course
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerCourseList.type=Instructor Development Course List
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerCourseAuthTeach.type=Course certified instructor-lecturer teaching details
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainWithOut.type=External training management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainWithOutAssist.type=External training co-organizer management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainWithoutStatistics.type=External training statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainWithoutApply.type=External training registration list
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainWithoutApplyAssist.type=Registration list for external training co-organizers
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainWithoutResult.type=List of external training results
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainWithoutApplyDetail.type=Detailed list of external training
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainWithoutWorkStatistics.type=External Training Statistics - Professional
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainMembers.type=Member Management-Students
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainCoOrganizer.type=Member Management-Co-organizers
com.wunding.learn.file.api.constant.ExportFileNameEnum.TaskScore.type=activity points
com.wunding.learn.file.api.constant.ExportFileNameEnum.Supplier.type=Supplier management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.SupplierFile.type=Supplier information management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainLecturerTeachDetail.type=Supplier lecturer teaching management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainCourse.type=Training course list
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainTaskDetail.type=Detailed statistics of training projects
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainPersonRank.type=Training project personal learning ranking statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainOrgLearn.type=Training project department learning ranking statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserWhiteRecord.type=Push whitelist
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserIdentityInfo.type=User identification information
com.wunding.learn.file.api.constant.ExportFileNameEnum.IdentityUserInfo.type=Identity user management
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserIdentityList.type=User identity management
com.wunding.learn.file.api.constant.ExportFileNameEnum.VisibleViewLimitUserList.type=Issue personnel details
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserVisitItemRecord.type=Link access details
com.wunding.learn.file.api.constant.ExportFileNameEnum.HomeRouterVisitDetail.type=Backend access details
com.wunding.learn.file.api.constant.ExportFileNameEnum.ItemVisitRecord.type=Column access record
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMap.type=study map
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapList.type=Study map list
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapExec.type=Learning map execution list
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapLearnDetail.type=Study detailed list
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapLearnUser.type=Learning user list
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapActivityHorizontalStat.type=Activity horizontal statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapPhaseHorizontalStat.type=Stage horizontal statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapActivity.type=List of learning map activities
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapProgressStat.type=Study map situation
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapActivityProgressStat.type=Study map activities
com.wunding.learn.file.api.constant.ExportFileNameEnum.AbilityDictionary.type=Competency Dictionary
com.wunding.learn.file.api.constant.ExportFileNameEnum.AbilityMode.type=Capability model
com.wunding.learn.file.api.constant.ExportFileNameEnum.PermissionConfig.type=Permission directory configuration
com.wunding.learn.file.api.constant.ExportFileNameEnum.PermissionRouter.type=Permission directory routing configuration
com.wunding.learn.file.api.constant.ExportFileNameEnum.CoursewareQuestionAnswerRecord.type=Courseware answer details
com.wunding.learn.file.api.constant.ExportFileNameEnum.ApplyTemplate.type=Face-to-face program registration template
com.wunding.learn.file.api.constant.ExportFileNameEnum.FaceProjectApplyUser.type=Face-to-face program user registration list
com.wunding.learn.file.api.constant.ExportFileNameEnum.FaceProject.type=Face-to-face project management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.JobQualification.type=List of qualifications
com.wunding.learn.file.api.constant.ExportFileNameEnum.JobAuthentication.type=Qualification list
com.wunding.learn.file.api.constant.ExportFileNameEnum.JobAuthApplyRecord.type=List of certified persons
com.wunding.learn.file.api.constant.ExportFileNameEnum.PracticalSuperviseUser.type=List of supervisory evaluators
com.wunding.learn.file.api.constant.ExportFileNameEnum.PracticalOperationUserBySupervise.type=Supervise student list
com.wunding.learn.file.api.constant.ExportFileNameEnum.PracticalOperationUser.type=List of practical records
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapAbilityOrgStatistic.type=Initial training statistics for competency departments
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapAbilityOrgStatistic2.type=Competency department refresher training statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapUserOrgStatistic.type=Competency Map Department Initial Training Statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapUserOrgStatistic2.type=Competency map department refresher training statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapUserResourcesStatistic.type=Competency map study detailed statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapUserStatistic.type=Competency map learning progress detailed statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapAbilityAsk.type=Learning map ability learning requirements
com.wunding.learn.file.api.constant.ExportFileNameEnum.InvoiceList.type=Invoicing management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectVacate.type=Leave management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.ScheduleStat.type=Schedule statistics list
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectFormTemplateAnnex.type=Counseling results attachment
com.wunding.learn.file.api.constant.ExportFileNameEnum.March.type=Game management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.MarchCheckpoint.type=Game level list
com.wunding.learn.file.api.constant.ExportFileNameEnum.MarchCheckpointTask.type=Game task list
com.wunding.learn.file.api.constant.ExportFileNameEnum.MarchTeam.type=Team management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.MarchPost.type=Topic management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.MarchNotice.type=Description management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.MarchPostCountComment.type=Reply statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.ASSESS_TOOL_QUESTION_TEM.type=Assessment tool question import template
com.wunding.learn.file.api.constant.ExportFileNameEnum.ASSESS_PROGRESS.type=Assessment project progress statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.ASSESS_IMPORT_USER.type=Evaluation user import template
com.wunding.learn.file.api.constant.ExportFileNameEnum.ASSESS_IMPORT_RECORD.type=Assessment record import template
com.wunding.learn.file.api.constant.ExportFileNameEnum.ASSESS_IMPORT_DETAILS.type=Assessment details import template
com.wunding.learn.file.api.constant.ExportFileNameEnum.RecordDetail.type=Answer details
com.wunding.learn.file.api.constant.ExportFileNameEnum.ASSESS_REPORT.type=Evaluation report
com.wunding.learn.file.api.constant.ExportFileNameEnum.COMPETITION_SESSION_USER_DETAIL.type=Exam competition session personnel information
com.wunding.learn.file.api.constant.ExportFileNameEnum.INTERVIEW_QUANTITY_STATISTICS.type=Visit statistics
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserGoldCoin.type=User gold coin list
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserGoldCoinBill.type=User gold coin bill list
com.wunding.learn.file.api.constant.ExportFileNameEnum.MemberCard.type=Membership card management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.MemberOrg.type=Member institution management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.Member.type=Member management list
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerProjectStatistic=Lecturer Project Statistic list
com.wunding.learn.file.api.constant.ExportFileNameEnum.FaceProjectStatistic=Face Project Statistic list
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnProjectStatistic=Learn Project Statistic list
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainProjectStatistic=Train Project Statistic list
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticUserDetail=Member list
com.wunding.learn.file.api.constant.ExportFileNameEnum.TASK_MENTOR_LIST=Task mentor list
com.wunding.learn.common.enums.other.SystemTypeEnum.AFTER_SALE.message=After-sales system
com.wunding.learn.common.enums.other.SystemTypeEnum.INTERNAL_TRAINING.message=Internal training system
com.wunding.learn.common.enums.other.ContentRuleEnum.HOT_COURSE.name=Popular courses
com.wunding.learn.common.enums.other.ContentRuleEnum.LEARNING_COURSE.name=I'm learning
com.wunding.learn.common.enums.other.ContentRuleEnum.SCORE_LECTURER.name=Lecturer style 1
com.wunding.learn.common.enums.other.ContentRuleEnum.TIME_LECTURER.name=Lecturer style 2
com.wunding.learn.common.enums.other.ContentRuleEnum.NEW_TOPIC_NOT_TOP.name=Latest topics
com.wunding.learn.common.enums.other.ContentRuleEnum.HOT_TOPIC_COMMENT.name=Hot topics (by number of replies)
com.wunding.learn.common.enums.other.ContentRuleEnum.HOT_TOPIC_VIEW.name=Popular topics (by number of visits)
com.wunding.learn.common.enums.other.ContentRuleEnum.NEW_TOPIC_TOP.name=Latest topics (including pinned)
com.wunding.learn.common.enums.other.ContentRuleEnum.HOT_CASE.name=Popular cases
com.wunding.learn.common.enums.other.ContentRuleEnum.NEW_CASE.name=Latest cases
com.wunding.learn.common.enums.other.ContentRuleEnum.LIVING_LIVE.name=Live broadcast
com.wunding.learn.common.enums.other.ContentRuleEnum.CURRENT_LIVE.name=Current live broadcast
com.wunding.learn.common.enums.other.ContentRuleEnum.ROLLBACK_LIVE.name=Watch live broadcast
com.wunding.learn.common.enums.promotedgame.GameQuestionType.Single.text=Single choice
com.wunding.learn.common.enums.promotedgame.GameQuestionType.Multi.text=Multiple choice
com.wunding.learn.common.enums.promotedgame.GameQuestionType.Judge.text=judge
com.wunding.learn.common.enums.exam.CompetitionSessionStatusEnum.DEFAULT_VALUE.name=default value
com.wunding.learn.common.enums.exam.CompetitionSessionStatusEnum.READY.name=In preparation
com.wunding.learn.common.enums.exam.CompetitionSessionStatusEnum.ON_GOING.name=in progress
com.wunding.learn.common.enums.exam.CompetitionSessionStatusEnum.END.name=has ended
com.wunding.learn.project.service.enums.QuickProjectRoleEnum.STUDENT.name=student
com.wunding.learn.project.service.enums.QuickProjectRoleEnum.TRAINER.name=trainer
com.wunding.learn.project.service.enums.QuickProjectRoleEnum.ADMIN.name=administrator
com.wunding.learn.common.enums.other.MenuItemEnums.INDEX.name=front page
com.wunding.learn.common.enums.other.MenuItemEnums.NOTICE.name=announcement
com.wunding.learn.common.enums.other.MenuItemEnums.COURSE.name=course
com.wunding.learn.common.enums.other.MenuItemEnums.SCHEDULE.name=schedule
com.wunding.learn.common.enums.other.MenuItemEnums.ACTIVITY.name=Activity
com.wunding.learn.common.enums.other.MenuItemEnums.CLASS.name=class
com.wunding.learn.common.enums.other.MenuItemEnums.PHOTO.name=photo wall
com.wunding.learn.common.enums.other.MenuItemEnums.DATUM.name=material
com.wunding.learn.common.enums.other.MenuItemEnums.CASE.name=Case
com.wunding.learn.common.enums.other.MenuItemEnums.TOPIC.name=topic
com.wunding.learn.common.enums.other.MenuItemEnums.RANK.name=Ranking
com.wunding.learn.common.enums.other.MenuItemEnums.LECTURER.name=lecturer
com.wunding.learn.common.enums.other.MenuItemEnums.SPECIAL.name=Topics
com.wunding.learn.common.enums.evaluation.QuestionType.SINGLE.text=Single choice
com.wunding.learn.common.enums.evaluation.QuestionType.MULTI.text=Multiple choice
com.wunding.learn.common.enums.evaluation.QuestionType.JUDGE.text=judge
com.wunding.learn.common.enums.evaluation.QuestionType.BLANKS.text=fill in the blank
com.wunding.learn.common.enums.evaluation.QuestionType.QA.text=Q&A
com.wunding.learn.common.enums.evaluation.QuestionType.GRADE.text=Score
com.wunding.learn.common.constant.plan.PlanStatusEnum.DRAFT.name=draft
com.wunding.learn.common.constant.plan.PlanStatusEnum.TO_BE_REVIEWED.name=Pending review
com.wunding.learn.common.constant.plan.PlanStatusEnum.REJECTED.name=turn down
com.wunding.learn.common.constant.plan.PlanStatusEnum.APPROVED.name=Approved
com.wunding.learn.common.enums.evaluation.SourceEnum.REFERENCE.message=Quote
com.wunding.learn.common.enums.evaluation.SourceEnum.MANUAL_UPLOAD.message=Manual upload
com.wunding.learn.reading.service.enums.ReadingTaskDescription.READING_TASK_TYPE_SIGN.name=Number of check-ins during the event
com.wunding.learn.reading.service.enums.ReadingTaskDescription.READING_TASK_TYPE_EXPERIENCE.name=Number of submissions during the event
com.wunding.learn.reading.service.enums.ReadingTaskDescription.READING_TASK_TYPE_READING_N.name=Number of books read/listened to during the event
com.wunding.learn.reading.service.enums.ReadingTaskDescription.READING_TASK_TYPE_COMMENT_N.name=Number of comments
com.wunding.learn.reading.service.enums.ReadingTaskDescription.READING_TASK_TYPE_READING.name=Number of books read
com.wunding.learn.reading.service.enums.ReadingTaskDescription.READING_TASK_TYPE_COMMENT.name=Submit your thoughts on a book
com.wunding.learn.common.enums.other.TriggerTypeEnum.ANY.text=Any satisfaction trigger
com.wunding.learn.common.enums.other.TriggerTypeEnum.ALL.text=All meet the trigger
com.wunding.learn.common.enums.other.GeneralJudgeEnum.CONFIRM.text=yes
com.wunding.learn.common.enums.other.GeneralJudgeEnum.NEGATIVE.text=no
com.wunding.learn.user.service.enums.ItemTypeEnum.MIDDLE_ITEM.name=middle menu
com.wunding.learn.user.service.enums.ItemTypeEnum.BOTTOM_ITEM.name=bottom menu
com.wunding.learn.user.service.enums.ItemTypeEnum.MY_LEARN_ITEM.name=my study
com.wunding.learn.user.service.enums.ItemTypeEnum.APPLICATION_ITEM.name=My application
com.wunding.learn.user.service.enums.ItemTypeEnum.MY_PAGE.name=My page
com.wunding.learn.user.service.enums.CustomMenuEnum.INTEGRAL.name=integral
com.wunding.learn.user.service.enums.CustomMenuEnum.HOURS.name=hours
com.wunding.learn.user.service.enums.CustomMenuEnum.CREDIT.name=credit
com.wunding.learn.user.service.enums.CustomMenuEnum.GOLD.name=gold
com.wunding.learn.user.service.enums.CustomMenuEnum.CERTIFICATE.name=certificate
com.wunding.learn.user.service.enums.CustomMenuEnum.COLLECT.name=collect
com.wunding.learn.user.service.enums.CustomMenuEnum.IDP.name=IDP
com.wunding.learn.user.service.enums.CustomMenuEnum.TASK.name=Task
com.wunding.learn.user.service.enums.CustomMenuEnum.COURSE.name=Course
com.wunding.learn.user.service.enums.CustomMenuEnum.EXAM.name=Exam
com.wunding.learn.user.service.enums.CustomMenuEnum.SURVEY.name=Survey
com.wunding.learn.user.service.enums.CustomMenuEnum.PROJECT.name=Project
com.wunding.learn.user.service.enums.CustomMenuEnum.LIVE.name=Live
com.wunding.learn.user.service.enums.CustomMenuEnum.TOPIC.name=Topic
com.wunding.learn.user.service.enums.CustomMenuEnum.QUIZ.name=Quiz
com.wunding.learn.user.service.enums.CustomMenuEnum.SIGN.name=Sign
com.wunding.learn.user.service.enums.CustomMenuEnum.MY_INFO.name=My info
com.wunding.learn.user.service.enums.CustomMenuEnum.CHANGE_PASSWORD.name=Change password
com.wunding.learn.user.service.enums.CustomMenuEnum.FEEDBACK.name=Feedback
com.wunding.learn.user.service.enums.CustomMenuEnum.MY_TAG.name=My tag
com.wunding.learn.user.service.enums.CustomMenuEnum.INCENTIVE_EXPLANATION.name=Incentive explanation
com.wunding.learn.user.service.enums.CustomMenuEnum.THIRD_PARTY_BINDING.name=Third party binding
com.wunding.learn.user.service.enums.CustomMenuEnum.PRIVACY_POLICY.name=Privacy policy
com.wunding.learn.user.service.enums.CustomMenuEnum.SWITCH_LANGUAGE.name=Switch language
com.wunding.learn.user.service.enums.CustomMenuEnum.ENABLE_STUDENT_UPLOAD.name=Enable the Student Courseware Upload on the PC
com.wunding.learn.user.service.enums.CustomMenuEnum.ENABLE_H5_IDP.name=Enable H5 Idp
com.wunding.learn.user.service.enums.CustomMenuEnum.ENABLE_H5_TARGET.name=Enable H5 target post
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_1.description=Used for background display everywhere
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_2.description=Login page title background, including slogan
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_3.description=Home page title background, excluding slogan
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_4.description=Home page background horn figure picture
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_5.description=Common background for all pages except the homepage
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_6.description=Cancel button
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_7.description=In the pop-up window when the level is completed, the level completion prompt
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_8.description=close button
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_9.description=Button in upload step prompt page
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_10.description=
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_11.description=Pop-up prompt after step upload is completed
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_12.description=After completing the level, click the button to read the story
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_13.description=Button in the game description pop-up window when logging in for the first time
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_14.description=Upload now button in the step upload pop-up window
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_15.description=Title logo of today’s mileage
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_16.description=Entry button to enter the "My" page
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_17.description=Entry button to enter the "Leaderboard" page
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_18.description=The title logo of the event description
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_19.description=Upload the title logo of yesterday’s sports button
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_20.description=The title logo of the learning area
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_21.description=The villain on the homepage team progress bar
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_22.description=Mine, the flag in the story
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_23.description=Mine, the flag in the story
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_24.description=Top background overlay for learning area
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_25.description=Top background overlay for My page
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_26.description=Silhouette background for pages other than home page
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_27.description=map link arrow
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_28.description=map link arrow
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_29.description=map link arrow
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_30.description=map link arrow
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_31.description=map link arrow
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_32.description=map link arrow
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_33.description=map link arrow
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_34.description=map link arrow
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_35.description=map link arrow
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_36.description=map link arrow
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_37.description=map link arrow
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_38.description=map link arrow
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_39.description=map link arrow
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_40.description=map link arrow
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_41.description=map link arrow
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_42.description=map link arrow
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_43.description=map link arrow
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_44.description=map link arrow
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_45.description=map link arrow
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_46.description=map link arrow
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_47.description=map link arrow
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_48.description=map link arrow
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_49.description=Home Map Locations Origin Halo
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_50.description=Home Map Locations Origin Halo
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_51.description=Home map locations flags
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_52.description=Header background of pop-up windows such as instructions
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_53.description=The background of the congratulations pop-up window for level completion
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_54.description=The background of the story pop-up window
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_55.description=The top background of the game description pop-up window when logging in for the first time)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_56.description=The logo next to the title of the description pop-up window, and each description is configured with a flag
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_57.description=My page's title background, shown below the text
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_58.description=Card background for my page
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_59.description=Study area banner
com.wunding.learn.common.enums.other.ModelEnums.MENU.name=Menu column
com.wunding.learn.common.enums.other.ModelEnums.ROTOGRAPH.name=carousel
com.wunding.learn.common.enums.other.ModelEnums.NOTICE.name=rolling announcement
com.wunding.learn.common.enums.other.ModelEnums.CASE.name=Case
com.wunding.learn.common.enums.other.ModelEnums.DATUM.name=material
com.wunding.learn.common.enums.other.ModelEnums.TOPIC.name=topic
com.wunding.learn.common.enums.other.ModelEnums.COURSE.name=course
com.wunding.learn.common.enums.other.ModelEnums.SCHEDULE.name=schedule
com.wunding.learn.common.enums.other.ModelEnums.LEAFLET.name=leaflet
com.wunding.learn.common.enums.other.ModelEnums.RANK.name=Ranking
com.wunding.learn.common.enums.other.ModelEnums.PHOTO.name=photo
com.wunding.learn.common.enums.other.ModelEnums.CLASS.name=class
com.wunding.learn.common.enums.other.ModelEnums.LIVE.name=live streaming
com.wunding.learn.common.enums.other.ModelEnums.LECTURER.name=lecturer
com.wunding.learn.common.enums.other.ModelEnums.SPECIAL.name=Topics
com.wunding.learn.common.enums.survey.SurveyQuestionTypeEnum.QUESTION_TYPE_RADIO.name=Single choice
com.wunding.learn.common.enums.survey.SurveyQuestionTypeEnum.QUESTION_TYPE_MULTI_SELECT.name=Multiple choice
com.wunding.learn.common.enums.survey.SurveyQuestionTypeEnum.QUESTION_TYPE_JUDGEMENT.name=judge
com.wunding.learn.common.enums.survey.SurveyQuestionTypeEnum.QUESTION_TYPE_CLOZE.name=fill in the blank
com.wunding.learn.common.enums.survey.SurveyQuestionTypeEnum.QUESTION_TYPE_QA.name=Q&A
com.wunding.learn.common.enums.survey.SurveyQuestionTypeEnum.QUESTION_TYPE_GRADE.name=Score
com.wunding.learn.common.enums.survey.SurveyQuestionTypeEnum.QUESTION_TYPE_MATERIALS.name=Material
com.wunding.learn.common.enums.market.RepeatDescriptionEnum.RECOMMENDED_DATE_DESC.message=Sort by recommended time
com.wunding.learn.common.enums.market.RepeatDescriptionEnum.ALL_INTERIOR_TEACH.message=All internal lecturers
com.wunding.learn.common.enums.market.RepeatDescriptionEnum.LIVE_START_DATE_DESC.message=Rearrange by live broadcast start time
com.wunding.learn.common.enums.market.RepeatDescriptionEnum.GIVE_LIKE_DESC.message=Sort by number of likes
com.wunding.learn.common.enums.other.ResourcesTypeEnum.COURSE_DOWNLOAD.name=Course download
com.wunding.learn.common.enums.other.ResourcesTypeEnum.LECTURER.name=lecturer
com.wunding.learn.common.enums.viewlimit.NewViewLimitTypeEnum.OrgLimit.viewName=Issued by department
com.wunding.learn.common.enums.viewlimit.NewViewLimitTypeEnum.IdentityLimit.viewName=Identity issuance
com.wunding.learn.common.enums.viewlimit.NewViewLimitTypeEnum.UserLimit.viewName=Issued by users
com.wunding.learn.common.enums.market.HeadContentRuleEnum.GUESSYOULIKE.showRuleDesc=Guess you like separate logic
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RECOMMENDCOURSE.showRuleDesc=Distribute courses whose scope is marked as Selected Good Courses
com.wunding.learn.common.enums.market.HeadContentRuleEnum.POPULARCOURSE1.showRuleDesc=Issuance scope: The number of comments + the number of stars published in the last month is greater than {0}
com.wunding.learn.common.enums.market.HeadContentRuleEnum.POPULARCOURSE2.showRuleDesc=Distribution range: number of comments + number of stars greater than {0}
com.wunding.learn.common.enums.market.HeadContentRuleEnum.POSTCOURSE.showRuleDesc=Courses that match your position
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LEARNINGCOURSE.showRuleDesc=Unfinished courses I studied
com.wunding.learn.common.enums.market.HeadContentRuleEnum.NEWS.showRuleDesc=Delivery scope
com.wunding.learn.common.enums.market.HeadContentRuleEnum.STUDYPROJECT.showRuleDesc=The delivery scope includes learning projects that I have not participated in or have not completed.
com.wunding.learn.common.enums.market.HeadContentRuleEnum.STUDYPROJECTSPECIFYCATEGORY.showRuleDesc=Learning projects of specified categories
com.wunding.learn.common.enums.market.HeadContentRuleEnum.SPECIAL.showRuleDesc=The delivery scope includes learning projects that I have not participated in or have not completed.
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LECTURERELEGANTDEMEANOUR1.showRuleDesc=All internal lecturers
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LECTURERELEGANTDEMEANOUR2.showRuleDesc=All internal lecturers
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LECTURERELEGANTDEMEANOUR3.showRuleDesc=All internal lecturers
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LIVINGBROADCAST.showRuleDesc=Delivery range The live broadcast end time is greater than the current time and the live broadcast start time is less than the current time
com.wunding.learn.common.enums.market.HeadContentRuleEnum.CURRENTLIVE.showRuleDesc=The live broadcast end time of the delivery range is greater than the current time
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LIVEPLAYBACK.showRuleDesc=The end time of the live broadcast in the delivery range is less than the current time, and there is a live broadcast with playback files
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LATESTTOPICS.showRuleDesc=Section distribution scope
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTTOPICS1.showRuleDesc=The number of replies within the publishing scope of the section is greater than {1} within {0} days.
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTTOPICS2.showRuleDesc=The number of replies in the section's distribution range is greater than {0}
com.wunding.learn.common.enums.market.HeadContentRuleEnum.ELITEPOSTS1.showRuleDesc=The section's publishing range is set to pinned posts
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASEOFTHEMONTH1.showRuleDesc=All ranges published this month and have more than {2} likes this month
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASEOFTHEMONTH2.showRuleDesc=Company-wide disclosure of posts published this month, and the number of likes this month is greater than {2}
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASEOFTHEMONTH3.showRuleDesc=Group-wide disclosure of posts published this month, and the number of likes this month is greater than {2}'
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASE1.showRuleDesc=All ranges have more than 2 likes
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASE2.showRuleDesc=The number of public likes within the company is greater than 2
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASE3.showRuleDesc=The number of public likes within the group is greater than 2
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RECOMMENDCASE1.showRuleDesc=Marked as case quality level 1
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RECOMMENDCASE2.showRuleDesc=Marked as case quality level 2
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RECOMMENDCASE3.showRuleDesc=Marked as case quality level 3
com.wunding.learn.common.enums.market.HeadContentRuleEnum.TRAINPROJECT.showRuleDesc=The delivery scope includes my training projects
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RAINPROJECTSPECIFYCATEGORY.showRuleDesc=Specific categories of training programs
com.wunding.learn.common.enums.market.HeadContentRuleEnum.ORG_INFO.showRuleDesc=organize information
com.wunding.learn.common.enums.market.HeadContentRuleEnum.CLICK_CLOCK_IN.showRuleDesc=Click to check in daily and complete check-in
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LEARN_CLOCK_IN.showRuleDesc=If the daily learning time reaches 10 minutes, it is considered as completing the check-in process
com.wunding.learn.common.enums.market.HeadContentRuleEnum.NON_LONG_TASK.showRuleDesc=All learning activities with start and end dates
com.wunding.learn.common.enums.market.HeadContentRuleEnum.CONTAIN_LONG_TASK.showRuleDesc=All learning activities
com.wunding.learn.common.enums.course.ParaTypeEnum.MEMBER_DEFAULT_CARD.name=The college opens the default membership card for users
com.wunding.learn.common.enums.course.ParaTypeEnum.IS_HANG_UP.name=Whether to play anti-hook
com.wunding.learn.common.enums.course.ParaTypeEnum.CAN_SPEED.name=Whether the first playback is a multiple
com.wunding.learn.common.enums.course.ParaTypeEnum.CAN_DRAG.name=Whether the first playback can be dragged
com.wunding.learn.common.enums.course.ParaTypeEnum.LECTURER_RESOURCE_APPLY_SPECIAL_ROLE.name=Lecturer resources apply for privileged roles
com.wunding.learn.common.enums.course.ParaTypeEnum.EVAL_PASS_VALUE.name=Evaluate excellent scores
com.wunding.learn.common.enums.course.ParaTypeEnum.SIGN_START_TIME_ADVANCE_MINUTES.name=Training check-in (check-out) start advance time (minutes)
com.wunding.learn.common.enums.course.ParaTypeEnum.SIGN_END_TIME_AFTER_MINUTES.name=Training sign-in (sign-out) end delay time (minutes)
com.wunding.learn.common.enums.course.ParaTypeEnum.EXCITATION_EXCHANGE_CONFIG.name=Incentive redemption attribute configuration
com.wunding.learn.common.enums.course.ParaTypeEnum.EVALUATION_START_DELAY_DEFAULT_TIME.name=Evaluation default start time (minutes)
com.wunding.learn.common.enums.course.ParaTypeEnum.EVALUATION_END_DELAY_DEFAULT_TIME.name=Evaluation default end time (minutes)
com.wunding.learn.common.enums.course.ParaTypeEnum.LECTURER_EXAMINATION_EVAL_AFTER_SALE_TEMPLATE.name=Internal training default evaluation template
com.wunding.learn.common.enums.course.ParaTypeEnum.NORMAL_ON_DUTY_TIME.name=normal working hours
com.wunding.learn.common.enums.course.ParaTypeEnum.NORMAL_OFF_MORRING.name=Normal morning get off work hours
com.wunding.learn.common.enums.course.ParaTypeEnum.NORMAL_ON_AFTERNOON.name=Normal afternoon working hours
com.wunding.learn.common.enums.course.ParaTypeEnum.NORMAL_OFF_DUTY_TIME.name=Normal off-duty hours
com.wunding.learn.common.enums.course.ParaTypeEnum.LECTURER_EXAMINATION_EVAL_INTERNAL_TRAINING_TEMPLATE.name=After-sales default evaluation template
com.wunding.learn.common.enums.course.ParaTypeEnum.NUMBER_OF_WECOM.name=Number of enterprise WeChat connections
com.wunding.learn.common.enums.course.ParaTypeEnum.AVAILABLE_DEFAULT_APPLY_FORM_TEMPLATE.name=Whether to enable the default registration template
com.wunding.learn.file.api.constant.ExportFileNameEnum.USER_POSTER_SHARE.type=User poster sharing list
com.wunding.learn.common.enums.other.ShareChannelEnum.WE_CHAT.name=WeChat friends
com.wunding.learn.common.enums.other.ShareChannelEnum.WE_CHAT_MOMENTS.name=WeChat Moments
com.wunding.learn.common.enums.other.ShareResourceTypeEnum.COURSE.name=Course
com.wunding.learn.common.enums.other.ShareResourceTypeEnum.PROJECT.name=Project
com.wunding.learn.common.enums.other.ShareResourceTypeEnum.LIVE.name=Live
com.wunding.learn.common.enums.push.PushNoticeEventNameEnum.PUBLISH_NOTICE.name=publish notice
com.wunding.learn.common.enums.push.PushNoticeEventNameEnum.REMINDER_NOTICE.name=reminder notice
com.wunding.learn.common.enums.push.PushNoticeEventNameEnum.CO_HANDLE_PENDING_NOTICE.name=Co-handle pending notice
com.wunding.learn.common.enums.push.PushNoticeEventNameEnum.AUDIT_RESULT_NOTIFICATION.name=audit result notification
com.wunding.learn.common.enums.push.PushNoticeEventNameEnum.PUBLISH_REGISTRATION_NOTICE.name=publish registration notice
com.wunding.learn.common.enums.push.PushNoticeEventNameEnum.NOTIFICATION_OF_EVALUATION_RESULTS.name=notification of evaluation results
com.wunding.learn.common.enums.push.PushNoticeEventNameEnum.CLASS_INFORMATION_NOTICE.name=class information notice
com.wunding.learn.common.enums.push.PushNoticeEventNameEnum.AUDIT_RESULTS_CANNOT_BE_MODIFIED_IF_THE_AUDIT_FAILS.name=audit results annot be modified if the audit fails
com.wunding.learn.common.enums.push.PushNoticeEventNameEnum.AUDIT_FAILED_TO_MODIFY_THE_RESULT_NOTICE.name=audit failed to modify the result notice
com.wunding.learn.common.enums.push.PushNoticeEventNameEnum.NOTIFICATION_OF_APPROVAL_RESULT.name=notification of approval result
com.wunding.learn.common.enums.push.PushNoticeEventNameEnum.NOTICE_OF_REPLY.name=notice of reply
com.wunding.learn.common.enums.push.PushNoticeEventNameEnum.CLASS_START_INFORMATION_NOTICE.name=class start information notice
com.wunding.learn.file.api.constant.ExportFileNameEnum.SysTemTagStatAnalysis.type=SysTemTagStatAnalysis
com.wunding.learn.file.api.constant.ExportFileNameEnum.ApplyLecturer.type=Lecturer Appointment Review List
com.wunding.learn.file.api.constant.ExportFileNameEnum.ApplyCourseDownload.type=Download the application list
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectCompletionStatistic.type=Statistics on the completion of the project
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectUserTask.type=Statistics on the completion of user project tasks
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectUserProjectExcitationRecord.type=User project incentives to obtain records
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectUserProjectCoursewareStudyDetail.type=Statistics on user learning project hours
com.wunding.learn.certification.service.enums.AssessComputedModeEnum.OPTION_SCORE.name=Corresponding score for option results
com.wunding.learn.certification.service.enums.AssessComputedModeEnum.OPTION_SUM_SCORE.name=Total score of option results
com.wunding.learn.certification.service.enums.AssessComputedModeEnum.OPTION_AVG_SCORE.name=Average score of option results
com.wunding.learn.certification.service.enums.AssessComputedModeEnum.OPTION_MIN_SCORE.name=The lowest score in the option results
com.wunding.learn.certification.service.enums.AssessComputedModeEnum.OPTION_MAX_SCORE.name=The highest score in the option results
com.wunding.learn.march.service.enums.StyleCodeEnum.HOME_BACKGROUND.name=Home Background
com.wunding.learn.march.service.enums.StyleCodeEnum.HOME_BACKGROUND.description=Background for various displays
com.wunding.learn.march.service.enums.StyleCodeEnum.LOGIN_TITLE_BACKGROUND.name=Login Page Title Background
com.wunding.learn.march.service.enums.StyleCodeEnum.LOGIN_TITLE_BACKGROUND.description=Title background on the login page, including slogan
com.wunding.learn.march.service.enums.StyleCodeEnum.HOME_TITLE_BACKGROUND.name=Title Background
com.wunding.learn.march.service.enums.StyleCodeEnum.HOME_TITLE_BACKGROUND.description=Title background on the home page, without slogan
com.wunding.learn.march.service.enums.StyleCodeEnum.HOME_SILHOUETTE.name=Home Background Silhouette
com.wunding.learn.march.service.enums.StyleCodeEnum.HOME_SILHOUETTE.description=Silhouette figure on the home background
com.wunding.learn.march.service.enums.StyleCodeEnum.OTHER_PAGE_BACKGROUND.name=Other Page Background
com.wunding.learn.march.service.enums.StyleCodeEnum.OTHER_PAGE_BACKGROUND.description=Generic background for all pages except the home page
com.wunding.learn.march.service.enums.StyleCodeEnum.CANCEL_BUTTON.name=Cancel Button
com.wunding.learn.march.service.enums.StyleCodeEnum.CANCEL_BUTTON.description=Cancel button
com.wunding.learn.march.service.enums.StyleCodeEnum.KNOW_COMPLETED_LEVEL.name=I Know (Level Completed)
com.wunding.learn.march.service.enums.StyleCodeEnum.KNOW_COMPLETED_LEVEL.description=Prompt in the popup when a level is completed
com.wunding.learn.march.service.enums.StyleCodeEnum.CLOSE_BUTTON.name=Close
com.wunding.learn.march.service.enums.StyleCodeEnum.CLOSE_BUTTON.description=Close button
com.wunding.learn.march.service.enums.StyleCodeEnum.KNOW_UPLOAD_STEPS.name=I Know (Upload Steps)
com.wunding.learn.march.service.enums.StyleCodeEnum.KNOW_UPLOAD_STEPS.description=Button on the step upload prompt page
com.wunding.learn.march.service.enums.StyleCodeEnum.OK_BUTTON.name=OK
com.wunding.learn.march.service.enums.StyleCodeEnum.OK_BUTTON.description=
com.wunding.learn.march.service.enums.StyleCodeEnum.REFRESH_STEPS.name=Refresh Steps
com.wunding.learn.march.service.enums.StyleCodeEnum.REFRESH_STEPS.description=Prompt in the popup after step upload is completed
com.wunding.learn.march.service.enums.StyleCodeEnum.VIEW_STORY.name=View Story
com.wunding.learn.march.service.enums.StyleCodeEnum.VIEW_STORY.description=Button to view the story after completing a level
com.wunding.learn.march.service.enums.StyleCodeEnum.CONFIRM_FIRST_LOGIN.name=Confirm (First Login Prompt)
com.wunding.learn.march.service.enums.StyleCodeEnum.CONFIRM_FIRST_LOGIN.description=Button in the game instructions popup for first login
com.wunding.learn.march.service.enums.StyleCodeEnum.UPLOAD_NOW.name=Upload Now
com.wunding.learn.march.service.enums.StyleCodeEnum.UPLOAD_NOW.description=Upload now button in the step upload popup
com.wunding.learn.march.service.enums.StyleCodeEnum.TODAY_MILEAGE.name=Today's Mileage
com.wunding.learn.march.service.enums.StyleCodeEnum.TODAY_MILEAGE.description=Logo for today's mileage title
com.wunding.learn.march.service.enums.StyleCodeEnum.MY_BUTTON.name=My Button
com.wunding.learn.march.service.enums.StyleCodeEnum.MY_BUTTON.description=Entrance button to the "My" page
com.wunding.learn.march.service.enums.StyleCodeEnum.RANKING_BUTTON.name=Ranking Button
com.wunding.learn.march.service.enums.StyleCodeEnum.RANKING_BUTTON.description=Entrance button to the "Ranking" page
com.wunding.learn.march.service.enums.StyleCodeEnum.ACTIVITY_DESCRIPTION.name=Activity Description
com.wunding.learn.march.service.enums.StyleCodeEnum.ACTIVITY_DESCRIPTION.description=Logo for activity description title
com.wunding.learn.march.service.enums.StyleCodeEnum.UPLOAD_YESTERDAY_ACTIVITY.name=Upload Yesterday's Activity Button
com.wunding.learn.march.service.enums.StyleCodeEnum.UPLOAD_YESTERDAY_ACTIVITY.description=Logo for the upload yesterday's activity button
com.wunding.learn.march.service.enums.StyleCodeEnum.LEARNING_ZONE_BUTTON.name=Learning Zone Button
com.wunding.learn.march.service.enums.StyleCodeEnum.LEARNING_ZONE_BUTTON.description=Logo for the learning zone title
com.wunding.learn.march.service.enums.StyleCodeEnum.PROGRESS_BAR_PERSON.name=Progress Bar Person
com.wunding.learn.march.service.enums.StyleCodeEnum.PROGRESS_BAR_PERSON.description=Small person icon on the team progress bar
com.wunding.learn.march.service.enums.StyleCodeEnum.FLAG_LOCKED.name=Flag (Locked)
com.wunding.learn.march.service.enums.StyleCodeEnum.FLAG_LOCKED.description=Flag in "My" and story sections (locked)
com.wunding.learn.march.service.enums.StyleCodeEnum.FLAG_UNLOCKED.name=Flag (Unlocked)
com.wunding.learn.march.service.enums.StyleCodeEnum.FLAG_UNLOCKED.description=Flag in "My" and story sections (unlocked)
com.wunding.learn.march.service.enums.StyleCodeEnum.LEARNING_ZONE_BACKGROUND.name=Learning Zone Background
com.wunding.learn.march.service.enums.StyleCodeEnum.LEARNING_ZONE_BACKGROUND.description=Top background overlay for the learning zone
com.wunding.learn.march.service.enums.StyleCodeEnum.MY_PAGE_BACKGROUND.name=My Page Background
com.wunding.learn.march.service.enums.StyleCodeEnum.MY_PAGE_BACKGROUND.description=Top background overlay for the "My" page
com.wunding.learn.march.service.enums.StyleCodeEnum.OTHER_PAGE_SILHOUETTE.name=Other Page Silhouette
com.wunding.learn.march.service.enums.StyleCodeEnum.OTHER_PAGE_SILHOUETTE.description=Silhouette background for pages other than the home page
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_10_LOCKED.name=Map Link Arrow 10 (Incomplete)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_10_LOCKED.description=Map link arrow
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_10_UNLOCKED.name=Map Link Arrow 10 (Completed)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_10_UNLOCKED.description=Map link arrow
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_11_LOCKED.name=Map Link Arrow 11 (Incomplete)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_11_LOCKED.description=Map link arrow
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_11_UNLOCKED.name=Map Link Arrow 11 (Completed)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_11_UNLOCKED.description=Map link arrow
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_1_LOCKED.name=Map Link Arrow 1 (Incomplete)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_1_LOCKED.description=Map link arrow
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_1_UNLOCKED.name=Map Link Arrow 1 (Completed)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_1_UNLOCKED.description=Map link arrow
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_2_LOCKED.name=Map Link Arrow 2 (Incomplete)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_2_LOCKED.description=Map link arrow
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_2_UNLOCKED.name=Map Link Arrow 2 (Completed)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_2_UNLOCKED.description=Map link arrow
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_3_LOCKED.name=Map Link Arrow 3 (Incomplete)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_3_LOCKED.description=Map link arrow
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_3_UNLOCKED.name=Map Link Arrow 3 (Completed)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_3_UNLOCKED.description=Map link arrow
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_4_LOCKED.name=Map Link Arrow 4 (Incomplete)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_4_LOCKED.description=Map link arrow
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_4_UNLOCKED.name=Map Link Arrow 4 (Completed)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_4_UNLOCKED.description=Map link arrow
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_5_LOCKED.name=Map Link Arrow 5 (Incomplete)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_5_LOCKED.description=Map link arrow
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_5_UNLOCKED.name=Map Link Arrow 5 (Completed)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_5_UNLOCKED.description=Map link arrow
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_6_LOCKED.name=Map Link Arrow 6 (Incomplete)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_6_LOCKED.description=Map link arrow
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_6_UNLOCKED.name=Map Link Arrow 6 (Completed)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_6_UNLOCKED.description=Map link arrow
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_7_LOCKED.name=Map Link Arrow 7 (Incomplete)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_7_LOCKED.description=Map link arrow
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_7_UNLOCKED.name=Map Link Arrow 7 (Completed)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_7_UNLOCKED.description=Map link arrow
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_8_LOCKED.name=Map Link Arrow 8 (Incomplete)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_8_LOCKED.description=Map link arrow
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_8_UNLOCKED.name=Map Link Arrow 8 (Completed)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_8_UNLOCKED.description=Map link arrow
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_9_LOCKED.name=Map Link Arrow 9 (Incomplete)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_9_LOCKED.description=Map link arrow
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_9_UNLOCKED.name=Map Link Arrow 9 (Completed)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_9_UNLOCKED.description=Map link arrow
com.wunding.learn.march.service.enums.StyleCodeEnum.LEVEL_LOCKED_HALO.name=Level Locked Halo
com.wunding.learn.march.service.enums.StyleCodeEnum.LEVEL_LOCKED_HALO.description=Halo around uncompleted map locations
com.wunding.learn.march.service.enums.StyleCodeEnum.LEVEL_UNLOCKED_HALO.name=Level Unlocked Halo
com.wunding.learn.march.service.enums.StyleCodeEnum.LEVEL_UNLOCKED_HALO.description=Halo around completed map locations
com.wunding.learn.march.service.enums.StyleCodeEnum.LEVEL_UNLOCKED_FLAG.name=Level Unlocked Flag
com.wunding.learn.march.service.enums.StyleCodeEnum.LEVEL_UNLOCKED_FLAG.description=Flag at completed map locations
com.wunding.learn.march.service.enums.StyleCodeEnum.POPUP_HEADER_BACKGROUND.name=Popup Header Background
com.wunding.learn.march.service.enums.StyleCodeEnum.POPUP_HEADER_BACKGROUND.description=Header background for instruction popups
com.wunding.learn.march.service.enums.StyleCodeEnum.LEVEL_COMPLETED_BACKGROUND.name=Level Completed Background
com.wunding.learn.march.service.enums.StyleCodeEnum.LEVEL_COMPLETED_BACKGROUND.description=Background for the congratulatory popup after completing a level
com.wunding.learn.march.service.enums.StyleCodeEnum.STORY_POPUP_BACKGROUND.name=Story Popup Background
com.wunding.learn.march.service.enums.StyleCodeEnum.STORY_POPUP_BACKGROUND.description=Background for the story popup
com.wunding.learn.march.service.enums.StyleCodeEnum.FIRST_LOGIN_POPUP_BACKGROUND.name=First Login Popup Background
com.wunding.learn.march.service.enums.StyleCodeEnum.FIRST_LOGIN_POPUP_BACKGROUND.description=Top background for the first login game instructions popup
com.wunding.learn.march.service.enums.StyleCodeEnum.INSTRUCTION_TITLE_FLAG.name=Instruction Title Flag
com.wunding.learn.march.service.enums.StyleCodeEnum.INSTRUCTION_TITLE_FLAG.description=Logo next to the title in instruction popups
com.wunding.learn.march.service.enums.StyleCodeEnum.MY_PAGE_TITLE_BACKGROUND.name=My Page Title Background
com.wunding.learn.march.service.enums.StyleCodeEnum.MY_PAGE_TITLE_BACKGROUND.description=Background below the text on the My page title
com.wunding.learn.march.service.enums.StyleCodeEnum.MY_PAGE_CARD_BACKGROUND.name=My Page Card Background
com.wunding.learn.march.service.enums.StyleCodeEnum.MY_PAGE_CARD_BACKGROUND.description=Background for cards on the My page
com.wunding.learn.march.service.enums.StyleCodeEnum.LEARNING_ZONE_TITLE.name=Learning Zone Title
com.wunding.learn.march.service.enums.StyleCodeEnum.LEARNING_ZONE_TITLE.description=Banner for the learning zone
