com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_1.name=首頁背景图
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_2.name=標題背景（登錄頁）
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_3.name=標題背景
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_4.name=首頁背景剪影圖
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_5.name=其他頁背景圖
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_6.name=取消按鈕
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_7.name=我知道了（完成關卡）
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_8.name=關閉
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_9.name=我知道了（上傳步數）
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_10.name=好的
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_11.name=刷新步數
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_12.name=去看故事
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_13.name=確定（首次登錄提示語）
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_14.name=立即上傳
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_15.name=今日裏程
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_16.name=我的按鈕
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_17.name=排行榜按鈕
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_18.name=活動說明
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_19.name=上傳昨日運動按鈕
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_20.name=學習專區按鈕
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_21.name=進度條小人
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_22.name=旗幟（未解鎖)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_23.name=旗幟（已解鎖)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_24.name=背景（學習專區)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_25.name=背景（“我的”頁)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_26.name=其他頁背景剪影圖
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_27.name=地點連接圖10（未完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_28.name=地點連接圖10（已完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_29.name=地點連接圖11（未完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_30.name=地點連接圖11（已完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_31.name=地點連接圖1（未完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_32.name=地點連接圖1（已完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_33.name=地點連接圖2（未完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_34.name=地點連接圖2（已完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_35.name=地點連接圖3（未完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_36.name=地點連接圖3（已完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_37.name=地點連接圖4（未完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_38.name=地點連接圖4（已完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_39.name=地點連接圖5（未完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_40.name=地點連接圖5（已完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_41.name=地點連接圖6（未完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_42.name=地點連接圖6（已完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_43.name=地點連接圖7（未完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_44.name=地點連接圖7（已完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_45.name=地點連接圖8（未完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_46.name=地點連接圖8（已完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_47.name=地點連接圖9（未完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_48.name=地點連接圖9（已完成)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_49.name=關卡未解鎖光環
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_50.name=關卡解鎖光環
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_51.name=關卡解鎖旗幟
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_52.name=彈窗頭部背景
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_53.name=關卡完成背景
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_54.name=故事彈窗背景
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_55.name=提示彈窗背景)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_56.name=說明標題旗幟)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_57.name=我的頁標題背景
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_58.name=我的頁卡片背景
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_59.name=學習專區標題
com.wunding.learn.common.enums.other.TradeTypeEnum.PROJECT.value=學習項目
com.wunding.learn.common.enums.other.TradeTypeEnum.COURSE.value=課程
com.wunding.learn.common.enums.other.TradeTypeEnum.TRAIN.value=培訓項目
com.wunding.learn.common.enums.other.TradeTypeEnum.TRAIN_APP_FILE.value=培訓資料文件
com.wunding.learn.common.enums.other.TradeTypeEnum.TRAIN_APP_EXAMPLE.value=培訓案例文件
com.wunding.learn.common.enums.exam.CategoryOrgTypeEnum.VIEWLIMITAREA.name=使用授權範圍
com.wunding.learn.common.enums.exam.CategoryOrgTypeEnum.MANAGELIMITAREA.name=分類管理單位
com.wunding.learn.common.enums.exam.ExamQuestionTypeEnum.QUESTION_TYPE_RADIO.name=單選
com.wunding.learn.common.enums.exam.ExamQuestionTypeEnum.QUESTION_TYPE_MULTI_SELECT.name=多選
com.wunding.learn.common.enums.exam.ExamQuestionTypeEnum.QUESTION_TYPE_CLOZE.name=填空
com.wunding.learn.common.enums.exam.ExamQuestionTypeEnum.QUESTION_TYPE_JUDGEMENT.name=判斷
com.wunding.learn.common.enums.exam.ExamQuestionTypeEnum.QUESTION_TYPE_QA.name=問答
com.wunding.learn.common.enums.other.LayoutStyleEnums.ROLL_NOTICE.name=滾動展示版式
com.wunding.learn.common.enums.other.LayoutStyleEnums.LIST_NOTICE.name=列表展示版式
com.wunding.learn.common.enums.other.LayoutStyleEnums.ROLL_CASE.name=橫向滾動版式
com.wunding.learn.common.enums.other.LayoutStyleEnums.LIST_CASE.name=列表展示版式
com.wunding.learn.common.enums.other.LayoutStyleEnums.VIP_ROLL_CASE.name=橫向滾動版式(含會員標記)
com.wunding.learn.common.enums.other.LayoutStyleEnums.VIP_LIST_CASE.name=列表展示版式(含會員標記)
com.wunding.learn.common.enums.other.LayoutStyleEnums.LIST_DATUM.name=列表展示版式
com.wunding.learn.common.enums.other.LayoutStyleEnums.VIP_LIST_DATUM.name=列表展示版式(含會員標記)
com.wunding.learn.common.enums.other.LayoutStyleEnums.DETAIL_LIST_TOPIC.name=詳細列表版式
com.wunding.learn.common.enums.other.LayoutStyleEnums.LIST_TOPIC.name=簡潔列表版式
com.wunding.learn.common.enums.other.LayoutStyleEnums.CROSS_LIST_COURSE.name=橫向滾動版式
com.wunding.learn.common.enums.other.LayoutStyleEnums.LIST_COURSE.name=列表展示版式
com.wunding.learn.common.enums.other.LayoutStyleEnums.VIP_CROSS_LIST_COURSE.name=橫向滾動版式(含會員標記)
com.wunding.learn.common.enums.other.LayoutStyleEnums.VIP_LIST_COURSE.name=列表展示版式(含會員標記)
com.wunding.learn.common.enums.other.LayoutStyleEnums.LIST_SCHEDULE.name=列表展示版式
com.wunding.learn.common.enums.other.LayoutStyleEnums.VIP_LIST_SCHEDULE.name=列表展示版式(含會員標記)
com.wunding.learn.common.enums.other.LayoutStyleEnums.ROLL_PHOTO.name=橫向滾動版式
com.wunding.learn.common.enums.other.LayoutStyleEnums.BIG_PHOTO.name=大圖版式
com.wunding.learn.common.enums.other.LayoutStyleEnums.LIST_CLASS.name=列表展示版式
com.wunding.learn.common.enums.other.LayoutStyleEnums.COVER_CLASS.name=純封面版式
com.wunding.learn.common.enums.other.LayoutStyleEnums.VIP_LIST_CLASS.name=列表展示版式(含會員標記)
com.wunding.learn.common.enums.other.LayoutStyleEnums.VIP_COVER_CLASS.name=純封面版式(含會員標記)
com.wunding.learn.common.enums.other.LayoutStyleEnums.BIG_LIVE.name=大圖版式
com.wunding.learn.common.enums.other.LayoutStyleEnums.LIST_LIVE.name=列表展示版式
com.wunding.learn.common.enums.other.LayoutStyleEnums.ROLL_LECTURER.name=橫向滾動版式
com.wunding.learn.common.enums.other.LayoutStyleEnums.LIST_LECTURER.name=列表展示版式
com.wunding.learn.common.enums.other.LayoutStyleEnums.LIST_SPECIAL.name=列表展示版式
com.wunding.learn.common.enums.other.LayoutStyleEnums.COVER_SPECIAL.name=純封面版式
com.wunding.learn.common.enums.other.LayoutStyleEnums.VIP_LIST_SPECIAL.name=列表展示版式(含會員標記)
com.wunding.learn.common.enums.other.LayoutStyleEnums.VIP_COVER_SPECIAL.name=純封面版式(含會員標記)
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.COURSE.name=課程
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.EXAM.name=考試
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.PROJECT.name=學習項目
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.CLASS.name=班級管理
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.FACE_CLASS.name=面授班級
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.CYCLE_PROJECT.name=週期項目
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.QUICK_PROJECT.name=快速培訓
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.COURSE_PROJECT.name=課程任務
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.TRAIN.name=培訓項目
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.SPECIAL.name=專題
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.SURVEY.name=調研
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.LIVE.name=直播
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.SIGN.name=簽到
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.VOTE.name=投票
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.POST.name=話題
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.PROMOTED_GAME.name=闖關
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.READING.name=共讀
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.APPRAISE.name=評價
com.wunding.learn.common.enums.other.ManageTaskTypeEnum.RECRUITING.name=招募
com.wunding.learn.maxkb.api.constant.AiTypeConstant.AI_CW_DESC.name=摘要
com.wunding.learn.maxkb.api.constant.AiTypeConstant.AI_COURSE_ASK.name=課程問答
com.wunding.learn.maxkb.api.constant.AiTypeConstant.AI_CW_KEYWORD.name=關鍵詞
com.wunding.learn.maxkb.api.constant.AiTypeConstant.AI_CW_QUESTION.name=題目
com.wunding.learn.maxkb.api.constant.AiTypeConstant.AI_CW_OUTLINE.name=大綱
com.wunding.learn.common.enums.file.TranscodeStatusEnum.TRANSFORMING.videoTranscodeStatusName=轉換中
com.wunding.learn.common.enums.file.TranscodeStatusEnum.TRANSFORMED.videoTranscodeStatusName=轉換成功
com.wunding.learn.common.enums.file.TranscodeStatusEnum.TRANSFORM_FAILED.videoTranscodeStatusName=轉換失敗
com.wunding.learn.common.enums.push.PushType.COURSE.text=課程
com.wunding.learn.common.enums.push.PushType.COURSE_Lib.text=共享庫課程
com.wunding.learn.common.enums.push.PushType.NEWS.text=資訊
com.wunding.learn.common.enums.push.PushType.EXAM.text=考試
com.wunding.learn.common.enums.push.PushType.EXAM_COMPETITION.text=考試競賽
com.wunding.learn.common.enums.push.PushType.EXERCISE.text=練習
com.wunding.learn.common.enums.push.PushType.LIVE.text=直播
com.wunding.learn.common.enums.push.PushType.EXAM_UNFINISHED.text=未考催辦
com.wunding.learn.common.enums.push.PushType.SURVEY.text=調研
com.wunding.learn.common.enums.push.PushType.SPECIAL_TOPIC.text=專題
com.wunding.learn.common.enums.push.PushType.SPECIAL_TASK_COURSE.text=專題課程任務
com.wunding.learn.common.enums.push.PushType.SPECIAL_TASK_EXAM.text=專題考試任務
com.wunding.learn.common.enums.push.PushType.SPECIAL_TASK_EXERCISE.text=專題練習任務
com.wunding.learn.common.enums.push.PushType.SPECIAL_TASK_SURVEY.text=專題調研任務
com.wunding.learn.common.enums.push.PushType.SPECIAL_TASK_LIVE.text=專題直播任務
com.wunding.learn.common.enums.push.PushType.TRAIN_CLASS.text=培訓班
com.wunding.learn.common.enums.push.PushType.APPLY.text=培訓報名
com.wunding.learn.common.enums.push.PushType.SIGN_IN.text=活動簽到
com.wunding.learn.common.enums.push.PushType.AWARD.text=獎品
com.wunding.learn.common.enums.push.PushType.PROMOTED_GAME.text=闖關遊戲
com.wunding.learn.common.enums.push.PushType.PROJECT.text=學習項目
com.wunding.learn.common.enums.push.PushType.PROJECT_APPLY.text=學習項目報名審覈
com.wunding.learn.common.enums.push.PushType.PROJECT_TASK_COURSE.text=學習項目課程任務
com.wunding.learn.common.enums.push.PushType.PROJECT_TASK_EXAM.text=學習項目考試任務
com.wunding.learn.common.enums.push.PushType.PROJECT_TASK_EXERCISE.text=學習項目練習任務
com.wunding.learn.common.enums.push.PushType.PROJECT_TASK_SURVEY.text=學習項目調研任務
com.wunding.learn.common.enums.push.PushType.PROJECT_TASK_LIVE.text=學習項目直播任務
com.wunding.learn.common.enums.push.PushType.PROJECT_TASK_TRAIN.text=學習項目培訓班任務
com.wunding.learn.common.enums.push.PushType.PROJECT_TASK_FORM.text=學習項目輔導任務
com.wunding.learn.common.enums.push.PushType.PROJECT_TASK_PROJECT.text=學習項目學習項目任務
com.wunding.learn.common.enums.push.PushType.PROJECT_TASK_PRACTICAL_OPERATION.text=學習項目實操任務
com.wunding.learn.common.enums.push.PushType.QUICK_PROJECT.text=快速培訓
com.wunding.learn.common.enums.push.PushType.QUICK_PROJECT_TASK_COURSE.text=快速培訓課程任務
com.wunding.learn.common.enums.push.PushType.QUICK_PROJECT_TASK_EXAM.text=快速培訓考試任務
com.wunding.learn.common.enums.push.PushType.TB_RECRUITING.text=招募活動
com.wunding.learn.common.enums.push.PushType.APPRAISE.text=評價
com.wunding.learn.common.enums.push.PushType.MEETING.text=答辯會議
com.wunding.learn.common.enums.push.PushType.READ.text=共讀
com.wunding.learn.common.enums.push.PushType.EMIGRATED_TASK.text=闖關關卡任務
com.wunding.learn.common.enums.push.PushType.LECTURER_WARN.text=講師預警
com.wunding.learn.common.enums.push.PushType.VOTE.text=投票
com.wunding.learn.common.enums.push.PushType.TRAIN.text=培訓項目
com.wunding.learn.common.enums.push.PushType.TRAIN_ACTIVITY.text=培訓項目活動
com.wunding.learn.common.enums.push.PushType.TRAIN_CLASSES.text=培訓項目班級
com.wunding.learn.common.enums.push.PushType.LEARN_MAP.text=學習地圖
com.wunding.learn.common.enums.push.PushType.FACE_PROJECT.text=面授項目
com.wunding.learn.common.enums.push.PushType.FACE_PROJECT_SCHEDULE.text=面授項目日程
com.wunding.learn.common.enums.push.PushType.FORM.text=輔導
com.wunding.learn.common.enums.push.PushType.Evaluation.text=評估
com.wunding.learn.common.enums.push.PushType.Work.text=作業
com.wunding.learn.common.enums.push.PushType.TRAIN_PROGRAM.text=培訓項目
com.wunding.learn.common.enums.push.PushType.ASSESS_PROJECT.text=測評項目
com.wunding.learn.common.enums.ResourceTypeEnum.EXAM.name=考試
com.wunding.learn.common.enums.ResourceTypeEnum.EXERCISE.name=練習
com.wunding.learn.common.enums.ResourceTypeEnum.COURSE.name=課程
com.wunding.learn.common.enums.ResourceTypeEnum.SHARED_LIBRARY.name=共享庫課程
com.wunding.learn.common.enums.ResourceTypeEnum.NEWS.name=資訊
com.wunding.learn.common.enums.ResourceTypeEnum.LIVE.name=直播
com.wunding.learn.common.enums.ResourceTypeEnum.READ.name=共讀
com.wunding.learn.common.enums.ResourceTypeEnum.VOTE.name=投票
com.wunding.learn.common.enums.ResourceTypeEnum.QUIZ.name=闖關
com.wunding.learn.common.enums.ResourceTypeEnum.RECRUIT.name=招募
com.wunding.learn.common.enums.ResourceTypeEnum.APPRAISE.name=評價
com.wunding.learn.common.enums.ResourceTypeEnum.MEETING.name=答辯會議
com.wunding.learn.common.enums.ResourceTypeEnum.TOPIC_SECTION.name=話題版塊
com.wunding.learn.common.enums.ResourceTypeEnum.SURVEY.name=調研
com.wunding.learn.common.enums.ResourceTypeEnum.SIGN_IN.name=簽到
com.wunding.learn.common.enums.ResourceTypeEnum.TEST_CONTEST.name=考試競賽
com.wunding.learn.common.enums.ResourceTypeEnum.SPECIAL.name=專題
com.wunding.learn.common.enums.ResourceTypeEnum.SPECIAL_TASK_EXAM.name=專題考試任務
com.wunding.learn.common.enums.ResourceTypeEnum.SPECIAL_TASK_EXERCISE.name=專題練習任務
com.wunding.learn.common.enums.ResourceTypeEnum.SPECIAL_TASK_COURSE.name=專題課程任務
com.wunding.learn.common.enums.ResourceTypeEnum.SPECIAL_TASK_LIVE.name=專題直播任務
com.wunding.learn.common.enums.ResourceTypeEnum.SPECIAL_TASK_SURVEY.name=專題調研任務
com.wunding.learn.common.enums.ResourceTypeEnum.COURSE_LEARNING_TASK.name=課程任務
com.wunding.learn.common.enums.ResourceTypeEnum.COURSE_LEARNING_TASK_EXAM.name=課程學習考試任務
com.wunding.learn.common.enums.ResourceTypeEnum.COURSE_LEARNING_TASK_COURSE.name=課程學習課程任務
com.wunding.learn.common.enums.ResourceTypeEnum.RAPID_TRAIN.name=快速培訓
com.wunding.learn.common.enums.ResourceTypeEnum.RAPID_TRAIN_APP_SIGN_IN.name=快速培訓簽到應用
com.wunding.learn.common.enums.ResourceTypeEnum.RAPID_TRAIN_APP_EVALUATION.name=快速培訓評估應用
com.wunding.learn.common.enums.ResourceTypeEnum.RAPID_TRAIN_TASK_COURSE.name=快速培訓課程任務
com.wunding.learn.common.enums.ResourceTypeEnum.RAPID_TRAIN_TASK_EXAM.name=快速培訓考試任務
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_COURSE.name=學習項目
com.wunding.learn.common.enums.ResourceTypeEnum.PROJECT.name=學習項目
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_COURSE_APP_NEWS.name=培訓班公告應用
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_COURSE_APP_APPLY.name=培訓班報名應用
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_COURSE_APP_SIGN_IN.name=培訓班簽到應用
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_COURSE_APP_TOPIC_SECTION.name=培訓班話題版塊應用
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_COURSE_APP_EVALUATION.name=培訓班評估應用
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_COURSE_TASK_COURSE.name=培訓班課程任務
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_COURSE_TASK_EXAM.name=培訓班考試任務
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_COURSE_TASK_EXERCISE.name=培訓班練習任務
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_COURSE_TASK_SURVEY.name=培訓班調研任務
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_COURSE_TASK_LIVE.name=培訓班直播任務
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_COURSE_TASK_PROJECT.name=培訓班項目任務
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_COURSE_TASK_TUTOR.name=培訓班輔導任務
com.wunding.learn.common.enums.ResourceTypeEnum.PERIODIC_PROJECT.name=週期項目
com.wunding.learn.common.enums.ResourceTypeEnum.PERIODIC_PROJECT_APP_NEWS.name=週期項目公告應用
com.wunding.learn.common.enums.ResourceTypeEnum.PERIODIC_PROJECT_APP_TOPIC_SECTION.name=週期項目話題版塊應用
com.wunding.learn.common.enums.ResourceTypeEnum.PERIODIC_PROJECT_APP_EVALUATION.name=週期項目評估應用
com.wunding.learn.common.enums.ResourceTypeEnum.PERIODIC_PROJECT_TASK_COURSE.name=週期項目課程任務
com.wunding.learn.common.enums.ResourceTypeEnum.PERIODIC_PROJECT_TASK_EXAM.name=週期項目考試任務
com.wunding.learn.common.enums.ResourceTypeEnum.PERIODIC_PROJECT_TASK_EXERCISE.name=週期項目練習任務
com.wunding.learn.common.enums.ResourceTypeEnum.PERIODIC_PROJECT_TASK_SURVEY.name=週期項目調研任務
com.wunding.learn.common.enums.ResourceTypeEnum.PERIODIC_PROJECT_TASK_PROJECT.name=週期項目項目任務
com.wunding.learn.common.enums.ResourceTypeEnum.PERIODIC_PROJECT_TASK_TUTOR.name=週期項目輔導任務
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_PROGRAM.name=培訓項目
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_PROGRAM_ACTIVITY_COURSE.name=培訓項目課程活動
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_PROGRAM_ACTIVITY_EXAM.name=培訓項目考試活動
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_PROGRAM_ACTIVITY_CLASS.name=培訓項目班級活動
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_PROGRAM_ACTIVITY_LIVE.name=培訓項目直播活動
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_PROGRAM_ACTIVITY_SURVEY.name=培訓項目調研活動
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_PROGRAM_ACTIVITY_RECRUIT.name=培訓項目招募活動
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_PROGRAM_ACTIVITY_APPRAISE.name=培訓項目評價活動
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_PROGRAM_ACTIVITY_FORM.name=培訓項目表單活動
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_PROGRAM_ACTIVITY_SIGN_IN.name=培訓項目簽到活動
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_PROGRAM_ACTIVITY_VOTE.name=培訓項目投票活動
com.wunding.learn.common.enums.ResourceTypeEnum.TRAIN_PROGRAM_ACTIVITY_PRACTICAL_OPERATION.name=培訓項目實操活動
com.wunding.learn.common.enums.ResourceTypeEnum.LEARN_MAP_EXEC.name=學習地圖
com.wunding.learn.common.enums.ResourceTypeEnum.FACE_PROJECT.name=面授項目
com.wunding.learn.common.enums.ResourceTypeEnum.FACE_PROJECT_SCHEDULE.name=面授項目日程
com.wunding.learn.common.enums.ResourceTypeEnum.PRACTICAL_OPERATION.name=實操
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.ACCEPT_FEEDBACK.name=反饋意見受理
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.AUDIT_COURSEWARE.name=上傳課件並審覈通過
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.BEST_REPLY_TOPIC.name=最佳話題回帖
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COLLECT_COURSE.name=收藏課程
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COMMENT_COURSE.name=評論課程
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COMMENT_COURSEWARE.name=評論課件
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COMMENT_NEWS.name=評論資訊
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COMMIT_EVALUATE.name=提交評估
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COMMIT_EXAM.name=提交考試
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COMMIT_EXERCISE.name=完成練習
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COMMIT_FEEDBACK.name=提交反饋意見
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COMMIT_HOMEWORK.name=提交作業
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COMMIT_SIGN.name=完成簽到
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COURSE_STAR.name=課程評星
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COURSE_VOTE.name=點贊課程
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COURSEWARE_STAR.name=課件被評星
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COURSEWARE_VIEW.name=課件被瀏覽數
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COURSEWARE_VOTE.name=課件被點贊數
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.EVERYDAY_LEARN_COURSE.name=用戶每日學完一個課程算1次打卡
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.EVERYDAY_LEARN_COURSEWARE.name=用戶每日學完一個課件算1次打卡
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.EVERYDAY_LOGIN.name=用戶每日登錄平臺即打卡1次，不連續則清零重新計算
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.EXAMPLE_ADD.name=每個學員提交一份案例時獎勵
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.EXAMPLE_AUDIT.name=評委進行評分評分時，自己獲得的獎勵
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.EXAMPLE_AUDIT_OVER.name=案例評審時，評審結束後獲得的分數，達到一定程度時的獎勵
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.EXAMPLE_COMPANY.name=案例被調整爲公司級時獎勵，降級不再取消
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.EXAMPLE_EXTRA.name=案例被管理員標識優質，取消優質不再取消
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.EXAMPLE_GROUP.name=案例被調整爲集團級時獎勵，降級不再取消
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.EXAM_PROCESS.name=根據考試成績區間進行激勵
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.FINISH_COURSE.name=學完課程
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.FINISH_COURSEWARE.name=學完課件
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.FINISH_COURSEWARE_BYTIME.name=學完課件（按課件時長）
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.FINISH_PROJECT.name=完成項目中所有學習任務獲得激勵
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.FINISH_PROJECT_APPLY.name=完成項目報名
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.FINISH_SURVEY.name=提交調研
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.FIRST_LOGIN.name=用戶首次訪問系統時獲得，僅1次
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.FOCUS_TOPIC.name=關注話題
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.INTEGRAL_CLEARING.name=積分清零(僅爲積分統計使用，實際積分配置規則不使用)
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.LEARN_COURSE.name=任意入口點擊打開課程時計算
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.LEARN_COURSEWARE.name=任意入口點擊打開課件時計算
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.LECTURER_JOIN_PROJECT_TEACH.name=僅在學習項目結束後7天獲得
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.LECTURER_TEACH_COURSEWARE.name=僅在學習項目結束後7天獲得
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.LIVE_CONTRIBUTE.name=通過發起直播貢獻知識獲得貢獻學分
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.LIVE_INTERACT.name=在直播間與主播進行互動給分
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.LIVE_INTERACT_COUNT.name=發起的直播達到一定觀看互動量給分
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.LIVE_QUESTION_AND_ANSWER.name=主播可在直播間內進行金幣懸賞提問，學員在直播間內回答問題可獲得金幣
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.MAKE_LIVE.name=發起直播給分
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.MAKE_TOPIC.name=發表話題
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.PARTICIPATION_DOUBLE.name=參與獲取（雙人）
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.PARTICIPATION_GROUP.name=參與獲取（組隊）
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.PARTICIPATION_MULTI.name=參與獲取（多人）
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.PARTICIPATION_SINGLE.name=參與獲取（單人）
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.PERSONAL_CHAMPION_MULTI.name=個人獲冠軍（多人）
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.PERSONAL_SECOND_MULTI.name=個人獲亞軍（多人）
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.PERSONAL_THIRD_MULTI.name=個人獲季軍（多人）
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.PERSONAL_WIN_DOUBLE.name=個人獲勝利（雙人）
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.PERSONAL_WIN_SINGLE.name=個人獲勝利（單人）
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.PROJECT_TASK_PROCESS.name=僅在學習項目結束後7天，根據任務完成率區間激勵
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.PROMOTED_GAME_FINISH_CHECKPOINT.name=完成闖關關卡
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.READING_BOOK_NUM.name=共讀閱讀圖書數量
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.READING_CLOCK_TIMES.name=共讀打卡次數(閱讀一本共讀圖書10分鐘以上，記作一次有效打卡)
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.READING_COMMENT_EXPNUM.name=共讀評論心得數量
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.READING_EXPERIENCE_TIMES.name=心得提交次數
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.REPLY_COURSE_COMMENT.name=回覆課程評論
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.REPLY_COURSEWARE_COMMENT.name=回覆課件評論
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.REPLY_TOPIC.name=對話題進行回帖
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.SHARE_COURSE.name=分享課程
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.SHARE_COURSEWARE.name=分享課件
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.STAR_COURSEWARE.name=課件評星
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.TEAM_WIN_GROUP.name=團隊獲勝（組隊）
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.TOPIC_COMMENT_COUNT.name=根據話題回帖數量區間激勵版主
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.VIEW_COURSE_PROCESS.name=課程下所有的課件時長進度百分比
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.VIEW_COURSEWARE_PROCESS.name=課件瀏覽時長進度(百分比)
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.VIEW_LIVE.name=點擊觀看直播
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.VIEW_LIVE_TOTAL_TIME.name=累計觀看直播時長達到指定時長
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.VIEW_NEWS.name=點擊查看資訊
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.VOTE_COURSE_COMMENT.name=點贊課程評論
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.VOTE_LECTURER.name=學員點贊講師
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.VOTE_NEWS_COMMENT.name=點贊資訊評論
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.COURSE_POSTER_SHARE.name=分享課程海報
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.PROJECT_POSTER_SHARE.name=分享項目海報
com.wunding.learn.common.constant.excitation.ExcitationEventIntroEnum.LIVE_POSTER_SHARE.name=分享直播海報
com.wunding.learn.common.enums.other.DifficultyTypeEnum.EASY.name=低
com.wunding.learn.common.enums.other.DifficultyTypeEnum.MIDDLE.name=中
com.wunding.learn.common.enums.other.DifficultyTypeEnum.HIGN.name=高
com.wunding.learn.common.enums.market.HeadContentRuleEnum.GUESSYOULIKE.orderRuleDesc=隨機排序
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RECOMMENDCOURSE.orderRuleDesc=精選好課時間倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.POPULARCOURSE1.orderRuleDesc=評論數+評星計數 總數倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.POPULARCOURSE2.orderRuleDesc=評論數+評星計數 總數倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.POSTCOURSE.orderRuleDesc=按課程發佈時間倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LEARNINGCOURSE.orderRuleDesc=按課程開始學習時間倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.NEWS.orderRuleDesc=發佈時間倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.STUDYPROJECT.orderRuleDesc=日期(固定週期項目的開始日期 或者(週期項目的 加入日期， 未加入時日期爲發佈日期)
com.wunding.learn.common.enums.market.HeadContentRuleEnum.STUDYPROJECTSPECIFYCATEGORY.orderRuleDesc=日期(固定週期項目的開始日期 或者(週期項目的 加入日期， 未加入時日期爲發佈日期)
com.wunding.learn.common.enums.market.HeadContentRuleEnum.SPECIAL.orderRuleDesc=遵循學習項目的排序
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LECTURERELEGANTDEMEANOUR1.orderRuleDesc=授課數量倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LECTURERELEGANTDEMEANOUR2.orderRuleDesc=授課綜合分倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LECTURERELEGANTDEMEANOUR3.orderRuleDesc=按最近授課時間倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LIVINGBROADCAST.orderRuleDesc=按直播開始時間倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.CURRENTLIVE.orderRuleDesc=按直播開始時間倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LIVEPLAYBACK.orderRuleDesc=按直播開始時間倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LATESTTOPICS.orderRuleDesc=發表時間倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTTOPICS1.orderRuleDesc=按回帖數量倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTTOPICS2.orderRuleDesc=按回帖數量倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.ELITEPOSTS1.orderRuleDesc=按設置置頂時間倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASEOFTHEMONTH1.orderRuleDesc=按點贊數倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASEOFTHEMONTH2.orderRuleDesc=按點贊數倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASEOFTHEMONTH3.orderRuleDesc=按點贊數倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASE1.orderRuleDesc=按點贊數倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASE2.orderRuleDesc=按點贊數倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASE3.orderRuleDesc=按點贊數倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RECOMMENDCASE1.orderRuleDesc=按推薦時間倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RECOMMENDCASE2.orderRuleDesc=按推薦時間倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RECOMMENDCASE3.orderRuleDesc=按推薦時間倒排
com.wunding.learn.common.enums.market.HeadContentRuleEnum.TRAINPROJECT.orderRuleDesc=按照發布時間，最新發布在最前面
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RAINPROJECTSPECIFYCATEGORY.orderRuleDesc=按照發布時間，最新發布在最前面
com.wunding.learn.common.enums.market.HeadContentRuleEnum.ORG_INFO.orderRuleDesc=
com.wunding.learn.common.library.record.enums.HandleTypeEnum.DEFAULT.name=無
com.wunding.learn.common.library.record.enums.HandleTypeEnum.TP_ADD_RECORD.name=入庫了
com.wunding.learn.common.library.record.enums.HandleTypeEnum.TP_DELETE_RECORD.name=刪除了
com.wunding.learn.common.library.record.enums.HandleTypeEnum.TP_UPDATE_RECORD.name=編輯了
com.wunding.learn.common.library.record.enums.HandleTypeEnum.TP_ENABLE_RECORD.name=啓用了
com.wunding.learn.common.library.record.enums.HandleTypeEnum.TP_DISABLE_RECORD.name=禁用了
com.wunding.learn.common.library.record.enums.HandleTypeEnum.TP_IMPORT_QUESTION.name=導入了
com.wunding.learn.common.library.record.enums.HandleTypeEnum.TP_DOWNLOAD_RECORD.name=下載了
com.wunding.learn.common.library.record.enums.HandleTypeEnum.TP_EDIT_RECORD.name=修改了
com.wunding.learn.common.library.record.enums.HandleTypeEnum.TP_USE_RECORD.name=使用
com.wunding.learn.common.library.record.enums.LibraryTypeEnum.DEFAULT.name=無
com.wunding.learn.common.library.record.enums.LibraryTypeEnum.LIB_EXAM.name=試卷庫
com.wunding.learn.common.library.record.enums.LibraryTypeEnum.LIB_QUESTION.name=考題庫
com.wunding.learn.common.library.record.enums.LibraryTypeEnum.LIB_EXERCISE.name=練習庫
com.wunding.learn.common.library.record.enums.LibraryTypeEnum.LIB_SURVEY.name=調研庫
com.wunding.learn.common.library.record.enums.LibraryTypeEnum.LIB_COURSE.name=課件庫
com.wunding.learn.common.library.record.enums.LibraryTypeEnum.LIB_EVALUATION.name=評估庫
com.wunding.learn.common.library.record.enums.LibraryTypeEnum.LIB_MATERIAL.name=知識庫
com.wunding.learn.common.enums.other.ModuleEnum.TRAIN.name=培訓項目
com.wunding.learn.common.enums.other.ModuleEnum.CERTIFICATION.name=認證
com.wunding.learn.common.enums.viewlimit.ViewLimitTypeEnum.TIME.name=時間身份
com.wunding.learn.common.enums.viewlimit.ViewLimitTypeEnum.BUSINESS.name=業務條線
com.wunding.learn.common.enums.viewlimit.ViewLimitTypeEnum.MANAGE_LEVEL.name=管理者層級
com.wunding.learn.common.enums.viewlimit.ViewLimitTypeEnum.POST.name=崗位族、崗位、崗位層級
com.wunding.learn.common.enums.viewlimit.ViewLimitTypeEnum.LECTURER.name=講師層級
com.wunding.learn.common.enums.viewlimit.ViewLimitTypeEnum.JOB_LEVEL.name=職級
com.wunding.learn.common.enums.viewlimit.ViewLimitTypeEnum.MemberLimit.name=學院會員
com.wunding.learn.common.enums.viewlimit.ViewLimitTypeEnum.OrgLimit.name=部門
com.wunding.learn.common.enums.viewlimit.ViewLimitTypeEnum.UserLimit.name=人員
com.wunding.learn.common.enums.viewlimit.ViewLimitTypeEnum.WorkLimit.name=工種
com.wunding.learn.common.enums.MapBehaviorEnum.USER.name=培訓活動(學員身份參與)
com.wunding.learn.common.enums.MapBehaviorEnum.TEACH.name=培訓活動(講師身份參與)
com.wunding.learn.common.enums.MapBehaviorEnum.SUPERVISOR.name=輔導活動(導師身份參與)
com.wunding.learn.common.enums.MapBehaviorEnum.PRACTICAL.name=實操活動(實操身份參與)
com.wunding.learn.common.enums.MapBehaviorEnum.EVALUATION.name=培訓活動(評價身份參與)
com.wunding.learn.common.enums.push.NoticeTypeEnum.GENERIC_TEMPLATE.message=通用模板
com.wunding.learn.common.enums.push.NoticeTypeEnum.AUDIT_TEMPLATE.message=審覈專用模板
com.wunding.learn.common.enums.push.NoticeTypeEnum.REMINDER_TEMPLATE.message=催辦專用模板
com.wunding.learn.common.enums.push.NoticeTypeEnum.APPROVED_TEMPLATE.message=審覈通過模板
com.wunding.learn.common.enums.push.NoticeTypeEnum.APPROVED_FAILED_TEMPLATE.message=審覈不通過,允許修改模板
com.wunding.learn.common.enums.push.NoticeTypeEnum.APPROVED_FAILED_NOT_MODIFY_TEMPLATE.message=審覈不通過,不允許修改模板
com.wunding.learn.common.enums.push.NoticeTypeEnum.NOTICE_OF_CLASS_SCHEDULE_START.message=開課通知模板
com.wunding.learn.common.enums.push.NoticeTypeEnum.IDENTIFYING_CODE_TEMPLATE.message=驗證碼模板
com.wunding.learn.common.enums.push.NoticeTypeEnum.NOTICE_OF_CLASS_START.message=開班通知模板
com.wunding.learn.common.enums.other.OperationEnum.CREATE.name=創建
com.wunding.learn.common.enums.other.OperationEnum.PUBLISH.name=發佈
com.wunding.learn.common.enums.other.OperationEnum.PUBLISH_CANCEL.name=取消發佈
com.wunding.learn.common.enums.other.OperationEnum.DELETE.name=刪除
com.wunding.learn.common.enums.other.OperationEnum.UPDATE.name=更新
com.wunding.learn.reading.service.enums.ReadingTaskDescription.READING_TASK_TYPE_SIGN.description=每日只能打卡一次，每次都可以獲得相應的運營分，超過要求次數後不再獲得。
com.wunding.learn.reading.service.enums.ReadingTaskDescription.READING_TASK_TYPE_EXPERIENCE.description=每本圖書的心得算一次，第一次對圖書提交心得可獲得運營分，超過次數後不再獲得。
com.wunding.learn.reading.service.enums.ReadingTaskDescription.READING_TASK_TYPE_READING_N.description=每讀完一本書即可獲得運營分，超過規定數量後不再獲取。
com.wunding.learn.reading.service.enums.ReadingTaskDescription.READING_TASK_TYPE_COMMENT_N.description=對心得每進行一次評論即可獲得運營分，超過規定數量後不再獲得。
com.wunding.learn.reading.service.enums.ReadingTaskDescription.READING_TASK_TYPE_READING.description=讀完指定圖書後獲得運營分。
com.wunding.learn.reading.service.enums.ReadingTaskDescription.READING_TASK_TYPE_COMMENT.description=第一次針對某本書發表心得體會時獲取運營分。
com.wunding.learn.common.enums.push.PushNoticeEventEnum.RECRUITING_46.name=協辦待審通知
com.wunding.learn.common.enums.push.PushNoticeEventEnum.RECRUITING_47.name=審覈結果通知
com.wunding.learn.common.enums.push.PushNoticeEventEnum.APPRAISE_49.name=評價結果通知
com.wunding.learn.common.enums.push.PushNoticeEventEnum.MEETING_58.name=答辯結果通知
com.wunding.learn.common.enums.evaluation.SourceTypeEnum.MANUAL_UPLOAD.message=手動上傳
com.wunding.learn.common.enums.evaluation.SourceTypeEnum.EVAL_LIB.message=評估庫
com.wunding.learn.common.enums.evaluation.SourceTypeEnum.DOCUMENT.message=資料庫
com.wunding.learn.common.enums.excitation.ExcitationTypeEnum.INTEGRAL.name=積分
com.wunding.learn.common.enums.excitation.ExcitationTypeEnum.CREDIT.name=學分
com.wunding.learn.common.enums.excitation.ExcitationTypeEnum.LEARN_TIME.name=學時
com.wunding.learn.common.enums.excitation.ExcitationTypeEnum.GOLD_COIN.name=金幣
com.wunding.learn.common.enums.project.ProjectAppType.NOTICE.appName=公告
com.wunding.learn.common.enums.project.ProjectAppType.MENTOR.appName=導師
com.wunding.learn.common.enums.project.ProjectAppType.LECTURER.appName=講師
com.wunding.learn.common.enums.project.ProjectAppType.APPLY.appName=報名
com.wunding.learn.common.enums.project.ProjectAppType.SIGN.appName=簽到
com.wunding.learn.common.enums.project.ProjectAppType.TOPIC.appName=話題
com.wunding.learn.common.enums.project.ProjectAppType.TOPIC_MANAGE.appName=話題版塊
com.wunding.learn.common.enums.project.ProjectAppType.ASSESS.appName=評估
com.wunding.learn.common.enums.project.ProjectAppType.TEAM.appName=團隊
com.wunding.learn.common.enums.project.ProjectAppType.HOMEWORK.appName=作業
com.wunding.learn.common.enums.project.ProjectAppType.COMMENT.appName=評論
com.wunding.learn.common.enums.project.ProjectAppType.COMPLETION.appName=結業
com.wunding.learn.common.enums.project.ProjectAppType.COST.appName=費用
com.wunding.learn.common.enums.project.ProjectAppType.DATA.appName=資料
com.wunding.learn.common.enums.project.ProjectAppType.STATISTICS.appName=統計
com.wunding.learn.common.enums.project.ProjectAppType.EXAM.appName=考試
com.wunding.learn.common.enums.project.ProjectAppType.VACATE.appName=請假
com.wunding.learn.common.enums.project.ProjectAppType.INVOICE.appName=開票
com.wunding.learn.common.enums.project.ProjectAppType.FILE.appName=相關資料
com.wunding.learn.common.enums.project.ProjectAppType.SCHEDULE.appName=日程安排
com.wunding.learn.common.enums.other.PassStatusEnum.NO.name=否
com.wunding.learn.common.enums.other.PassStatusEnum.YES.name=是
com.wunding.learn.common.enums.exam.CompetitionSessionUserTypeEnum.DEFAULT_VALUE.name=默認值
com.wunding.learn.common.enums.exam.CompetitionSessionUserTypeEnum.INITIATIVE.name=主動發起
com.wunding.learn.common.enums.exam.CompetitionSessionUserTypeEnum.BE_INVITED.name=被邀請
com.wunding.learn.common.enums.exam.CompetitionSessionUserTypeEnum.RANDOM_MATCH.name=隨機匹配
com.wunding.learn.common.enums.exam.CompetitionSessionUserTypeEnum.INVITING.name=邀請中
com.wunding.learn.common.enums.exam.CompetitionSessionUserTypeEnum.NO_RESPONSE.name=無響應
com.wunding.learn.common.enums.exam.CompetitionSessionUserTypeEnum.REFUSE.name=已拒絕
com.wunding.learn.common.enums.other.LayoutStyleEnum.COURSE_1_4.desc=橫排
com.wunding.learn.common.enums.other.LayoutStyleEnum.COURSE_2_4.desc=豎排
com.wunding.learn.common.enums.other.LayoutStyleEnum.COURSE_3_8.desc=滑動有標題
com.wunding.learn.common.enums.other.LayoutStyleEnum.NEWS_1_4.desc=豎排無簡介
com.wunding.learn.common.enums.other.LayoutStyleEnum.NEWS_2_3.desc=豎排有簡介
com.wunding.learn.common.enums.other.LayoutStyleEnum.NEWS_3_8.desc=滑動有標題
com.wunding.learn.common.enums.other.LayoutStyleEnum.PROJECT_1_1.desc=單個無標題
com.wunding.learn.common.enums.other.LayoutStyleEnum.PROJECT_2_3.desc=豎排
com.wunding.learn.common.enums.other.LayoutStyleEnum.PROJECT_3_8.desc=滑動有標題
com.wunding.learn.common.enums.other.LayoutStyleEnum.PROJECT_4_8.desc=滑動無標題
com.wunding.learn.common.enums.other.LayoutStyleEnum.SPECIAL_1_1.desc=單個有標題
com.wunding.learn.common.enums.other.LayoutStyleEnum.SPECIAL_2_3.desc=豎排
com.wunding.learn.common.enums.other.LayoutStyleEnum.SPECIAL_3_8.desc=滑動有標題
com.wunding.learn.common.enums.other.LayoutStyleEnum.SPECIAL_4_8.desc=滑動無標題
com.wunding.learn.common.enums.other.LayoutStyleEnum.SPECIAL_5_1.desc=單個無標題
com.wunding.learn.common.enums.other.LayoutStyleEnum.LECTURER_1_8.desc=滑動
com.wunding.learn.common.enums.other.LayoutStyleEnum.LECTURER_2_4.desc=橫排大
com.wunding.learn.common.enums.other.LayoutStyleEnum.LECTURER_3_4.desc=橫排小
com.wunding.learn.common.enums.other.LayoutStyleEnum.LIVE_1_1.desc=單個有標題
com.wunding.learn.common.enums.other.LayoutStyleEnum.LIVE_2_8.desc=滑動有標題1
com.wunding.learn.common.enums.other.LayoutStyleEnum.LIVE_3_4.desc=橫排
com.wunding.learn.common.enums.other.LayoutStyleEnum.LIVE_4_8.desc=滑動有標題2
com.wunding.learn.common.enums.other.LayoutStyleEnum.TOPICS_1_5.desc=豎排無簡介
com.wunding.learn.common.enums.other.LayoutStyleEnum.TOPICS_2_3.desc=豎排有簡介
com.wunding.learn.common.enums.other.LayoutStyleEnum.TOPICS_3_8.desc=橫排有簡介
com.wunding.learn.common.enums.other.LayoutStyleEnum.CASE_1_4.desc=橫排
com.wunding.learn.common.enums.other.LayoutStyleEnum.CASE_2_4.desc=豎排
com.wunding.learn.common.enums.other.LayoutStyleEnum.CASE_3_8.desc=滑動有標題
com.wunding.learn.common.enums.other.LayoutStyleEnum.ORG_1_4.desc=橫版【僅h5】
com.wunding.learn.common.enums.other.LayoutStyleEnum.CLOCK_IN_1_0.desc=默認
com.wunding.learn.common.enums.other.LayoutStyleEnum.MY_TASK_1_0.desc=默認
com.wunding.learn.apply.service.enums.ApplyStatusEnum.DRAFT.name=草稿
com.wunding.learn.apply.service.enums.ApplyStatusEnum.APPLYING.name=申請中
com.wunding.learn.apply.service.enums.ApplyStatusEnum.SUCCESS.name=申請成功
com.wunding.learn.apply.service.enums.ApplyStatusEnum.FAIL.name=申請失敗
com.wunding.learn.apply.service.enums.ApplyStatusEnum.CANCEL.name=申請取消
com.wunding.learn.common.enums.other.ContentRuleEnum.HOT_COURSE.showRuleDesc=熱門課程
com.wunding.learn.common.enums.other.ContentRuleEnum.LEARNING_COURSE.showRuleDesc=我正在學
com.wunding.learn.common.enums.other.ContentRuleEnum.SCORE_LECTURER.showRuleDesc=所有內部講師
com.wunding.learn.common.enums.other.ContentRuleEnum.TIME_LECTURER.showRuleDesc=所有內部講師
com.wunding.learn.common.enums.other.ContentRuleEnum.NEW_TOPIC_NOT_TOP.showRuleDesc=版塊下發範圍
com.wunding.learn.common.enums.other.ContentRuleEnum.HOT_TOPIC_COMMENT.showRuleDesc=版塊下發範圍
com.wunding.learn.common.enums.other.ContentRuleEnum.HOT_TOPIC_VIEW.showRuleDesc=版塊下發範圍
com.wunding.learn.common.enums.other.ContentRuleEnum.NEW_TOPIC_TOP.showRuleDesc=版塊下發範圍設置爲置頂的帖子
com.wunding.learn.common.enums.other.ContentRuleEnum.HOT_CASE.showRuleDesc=
com.wunding.learn.common.enums.other.ContentRuleEnum.NEW_CASE.showRuleDesc=
com.wunding.learn.common.enums.other.ContentRuleEnum.LIVING_LIVE.showRuleDesc=已經開始 未結束的直播
com.wunding.learn.common.enums.other.ContentRuleEnum.CURRENT_LIVE.showRuleDesc=未結束的直播
com.wunding.learn.common.enums.other.ContentRuleEnum.ROLLBACK_LIVE.showRuleDesc=已經結束的直播，且有回看文件的直播
com.wunding.learn.push.api.enums.PushChannelEnum.APP.desc=APP
com.wunding.learn.push.api.enums.PushChannelEnum.EMAIL.desc=郵箱
com.wunding.learn.push.api.enums.PushChannelEnum.WECOM.desc=企業微信
com.wunding.learn.push.api.enums.PushChannelEnum.WECHAT_OFFICIAL_ACCOUNTS.desc=微信公衆號
com.wunding.learn.push.api.enums.PushChannelEnum.WECHAT_MINI_PROGRAMS.desc=微信小程序
com.wunding.learn.push.api.enums.PushChannelEnum.DING_TALK.desc=釘釘
com.wunding.learn.push.api.enums.PushChannelEnum.FEI_SHU.desc=飛書
com.wunding.learn.push.api.enums.PushChannelEnum.SMS.desc=短信
com.wunding.learn.train.service.enums.ActivityResourceTypeEnum.COURSE.name=課程
com.wunding.learn.train.service.enums.ActivityResourceTypeEnum.EXAM.name=考試
com.wunding.learn.train.service.enums.ActivityResourceTypeEnum.PROJECT.name=培訓班
com.wunding.learn.train.service.enums.ActivityResourceTypeEnum.LIVE.name=直播
com.wunding.learn.train.service.enums.ActivityResourceTypeEnum.SURVEY.name=調研
com.wunding.learn.train.service.enums.ActivityResourceTypeEnum.RECRUITING.name=招募
com.wunding.learn.train.service.enums.ActivityResourceTypeEnum.APPRAISE.name=評價
com.wunding.learn.train.service.enums.ActivityResourceTypeEnum.FORM.name=表單
com.wunding.learn.train.service.enums.ActivityResourceTypeEnum.SIGN.name=簽到
com.wunding.learn.train.service.enums.ActivityResourceTypeEnum.VOTE.name=投票
com.wunding.learn.train.service.enums.ActivityResourceTypeEnum.PRACTICE.name=實操
com.wunding.learn.train.service.enums.ActivityResourceTypeEnum.SPECIAL.name=專題
com.wunding.learn.common.enums.exam.CompetitionGroupNameEnum.A_GROUP.name=A組
com.wunding.learn.common.enums.exam.CompetitionGroupNameEnum.B_GROUP.name=B組
com.wunding.learn.common.enums.user.OrgTypeEnum.VIEWLIMITAREA.name=使用授權範圍
com.wunding.learn.common.enums.user.OrgTypeEnum.MANAGELIMITAREA.name=分類管理單位
com.wunding.learn.common.enums.language.LanguageModuleEnum.ORGANIZATION.moduleName=組織
com.wunding.learn.common.enums.language.LanguageModuleEnum.TIME.moduleName=時間
com.wunding.learn.common.enums.language.LanguageModuleEnum.BUSINESS.moduleName=條線
com.wunding.learn.common.enums.language.LanguageModuleEnum.MANAGE_LEVEL.moduleName=管理者層級
com.wunding.learn.common.enums.language.LanguageModuleEnum.POST.moduleName=崗位族
com.wunding.learn.common.enums.language.LanguageModuleEnum.JOB_LEVEL.moduleName=職級
com.wunding.learn.common.enums.language.LanguageModuleEnum.LECTURER.moduleName=講師
com.wunding.learn.common.enums.language.LanguageModuleEnum.SUPERIOR.moduleName=崗位
com.wunding.learn.common.enums.language.LanguageModuleEnum.CourseCate.moduleName=課程分類
com.wunding.learn.common.enums.language.LanguageModuleEnum.CourseTagCate.moduleName=課程標籤
com.wunding.learn.common.enums.language.LanguageModuleEnum.InfoCate.moduleName=資訊分類
com.wunding.learn.common.enums.language.LanguageModuleEnum.AwardCate.moduleName=獎品分類
com.wunding.learn.common.enums.language.LanguageModuleEnum.ThematicTag.moduleName=專題標籤
com.wunding.learn.common.enums.language.LanguageModuleEnum.ThematicClass.moduleName=專題分類
com.wunding.learn.common.enums.language.LanguageModuleEnum.MentorCate.moduleName=導師分類
com.wunding.learn.common.enums.language.LanguageModuleEnum.FormTaskCate.moduleName=輔導任務分類
com.wunding.learn.common.enums.language.LanguageModuleEnum.home.moduleName=中部菜單
com.wunding.learn.common.enums.language.LanguageModuleEnum.myLearn.moduleName=我的學習
com.wunding.learn.common.enums.language.LanguageModuleEnum.botMenu.moduleName=底部菜單
com.wunding.learn.common.enums.language.LanguageModuleEnum.app.moduleName=我的應用
com.wunding.learn.common.enums.language.LanguageModuleEnum.systemConfig.moduleName=數據字典
com.wunding.learn.common.enums.language.LanguageModuleEnum.titleManager.moduleName=頭銜
com.wunding.learn.common.enums.language.LanguageModuleEnum.sysSpace.moduleName=積分規則
com.wunding.learn.common.enums.language.LanguageModuleEnum.firstInfo.moduleName=頭條通知
com.wunding.learn.common.enums.language.LanguageModuleEnum.award.moduleName=積分遊戲
com.wunding.learn.common.enums.language.LanguageModuleEnum.systemCertification.moduleName=系統認證
com.wunding.learn.common.enums.language.LanguageModuleEnum.sysClientVersion.moduleName=版本管理
com.wunding.learn.common.enums.language.LanguageModuleEnum.excitationEvent.moduleName=目標管理事件
com.wunding.learn.common.enums.language.LanguageModuleEnum.KnowledgeBaseType.moduleName=知識庫分類
com.wunding.learn.common.enums.language.LanguageModuleEnum.KnowledgeTagCate.moduleName=知識庫標籤
com.wunding.learn.common.enums.language.LanguageModuleEnum.EvaluateLibraryCate.moduleName=評估庫分類
com.wunding.learn.common.enums.language.LanguageModuleEnum.ExamLibraryCate.moduleName=考題庫分類
com.wunding.learn.common.enums.language.LanguageModuleEnum.ExerciseLibraryCate.moduleName=練習庫分類
com.wunding.learn.common.enums.language.LanguageModuleEnum.TestPaperLibraryCate.moduleName=試卷庫分類
com.wunding.learn.common.enums.language.LanguageModuleEnum.CourseLibraryCate.moduleName=課件庫分類
com.wunding.learn.common.enums.language.LanguageModuleEnum.SurveyLibraryCate.moduleName=調研庫分類
com.wunding.learn.common.enums.language.LanguageModuleEnum.CourseTagNewCate.moduleName=課程標籤分類
com.wunding.learn.common.enums.language.LanguageModuleEnum.ExampleCate.moduleName=案例分類
com.wunding.learn.common.enums.language.LanguageModuleEnum.lecturerDomainCate.moduleName=講師領域分類
com.wunding.learn.common.enums.market.HeadContentEnum.COURSE.name=課程
com.wunding.learn.common.enums.market.HeadContentEnum.NEWS.name=資訊
com.wunding.learn.common.enums.market.HeadContentEnum.STUDYPROJECT.name=學習項目
com.wunding.learn.common.enums.market.HeadContentEnum.SPECIAL.name=專題
com.wunding.learn.common.enums.market.HeadContentEnum.LECTURER.name=講師
com.wunding.learn.common.enums.market.HeadContentEnum.LIVE.name=直播
com.wunding.learn.common.enums.market.HeadContentEnum.TOPIC.name=話題
com.wunding.learn.common.enums.market.HeadContentEnum.CASE_LIBRARY.name=案例庫
com.wunding.learn.common.enums.market.HeadContentEnum.TRAIN_PROJECT.name=培訓項目
com.wunding.learn.common.enums.market.HeadContentEnum.ORG_INFO.name=組織信息
com.wunding.learn.common.enums.market.HeadContentEnum.CONTINUOUS_CLOCK_IN_SECTION_H5.name=連續簽到
com.wunding.learn.common.enums.market.HeadContentEnum.MY_TASK_LIST.name=我的任務
com.wunding.learn.common.enums.push.SuperResourceTypeEnum.project.name=學習項目
com.wunding.learn.common.enums.push.SuperResourceTypeEnum.special.name=專題
com.wunding.learn.common.enums.push.SuperResourceTypeEnum.quick_project.name=快速培訓
com.wunding.learn.common.enums.user.SexEnum.UNKNOW.alias=未知
com.wunding.learn.common.enums.user.SexEnum.MAN.alias=先生
com.wunding.learn.common.enums.user.SexEnum.WOMEN.alias=女士
com.wunding.learn.common.enums.other.ExaminationStatusEnum.UN_AUDIT.description=待審覈
com.wunding.learn.common.enums.other.ExaminationStatusEnum.AUDIT_PASS.description=審覈通過
com.wunding.learn.common.enums.other.ExaminationStatusEnum.AUDIT_REFUSE.description=審覈拒絕
com.wunding.learn.common.enums.other.ExcitationOperationEnum.INCREASE.name=增加
com.wunding.learn.common.enums.other.ExcitationOperationEnum.DECREASE.name=減少
com.wunding.learn.common.enums.market.HeadContentRuleEnum.GUESSYOULIKE.name=猜你喜歡
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RECOMMENDCOURSE.name=推薦課程
com.wunding.learn.common.enums.market.HeadContentRuleEnum.POPULARCOURSE1.name=熱門課程1
com.wunding.learn.common.enums.market.HeadContentRuleEnum.POPULARCOURSE2.name=熱門課程2
com.wunding.learn.common.enums.market.HeadContentRuleEnum.POSTCOURSE.name=崗位課程
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LEARNINGCOURSE.name=我正在學
com.wunding.learn.common.enums.market.HeadContentRuleEnum.NEWS.name=資訊
com.wunding.learn.common.enums.market.HeadContentRuleEnum.STUDYPROJECT.name=學習項目
com.wunding.learn.common.enums.market.HeadContentRuleEnum.STUDYPROJECTSPECIFYCATEGORY.name=指定類別
com.wunding.learn.common.enums.market.HeadContentRuleEnum.SPECIAL.name=我參加的專題
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LECTURERELEGANTDEMEANOUR1.name=講師風采1
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LECTURERELEGANTDEMEANOUR2.name=講師風采2
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LECTURERELEGANTDEMEANOUR3.name=講師風采3
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LIVINGBROADCAST.name=直播中
com.wunding.learn.common.enums.market.HeadContentRuleEnum.CURRENTLIVE.name=當前直播
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LIVEPLAYBACK.name=直播回看
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LATESTTOPICS.name=最新話題
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTTOPICS1.name=熱門話題1
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTTOPICS2.name=熱門話題2
com.wunding.learn.common.enums.market.HeadContentRuleEnum.ELITEPOSTS1.name=置頂話題1
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASEOFTHEMONTH1.name=本月熱門案例1
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASEOFTHEMONTH2.name=本月熱門案例2
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASEOFTHEMONTH3.name=本月熱門案例3
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASE1.name=熱門案例1
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASE2.name=熱門案例2
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASE3.name=熱門案例3
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RECOMMENDCASE1.name=案例質量等級1
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RECOMMENDCASE2.name=案例質量等級2
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RECOMMENDCASE3.name=案例質量等級3
com.wunding.learn.common.enums.market.HeadContentRuleEnum.TRAINPROJECT.name=培訓項目
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RAINPROJECTSPECIFYCATEGORY.name=指定類別
com.wunding.learn.common.enums.market.HeadContentRuleEnum.ORG_INFO.name=組織信息
com.wunding.learn.common.enums.market.HeadContentRuleEnum.CLICK_CLOCK_IN.name=每日點擊簽到打卡
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LEARN_CLOCK_IN.name=每日課程學習10分鐘
com.wunding.learn.common.enums.market.HeadContentRuleEnum.NON_LONG_TASK.name=非長期任務
com.wunding.learn.common.enums.market.HeadContentRuleEnum.CONTAIN_LONG_TASK.name=全部任務
com.wunding.learn.common.enums.exam.CompetitionPeopleModelEnum.SINGLE.text=單人
com.wunding.learn.common.enums.exam.CompetitionPeopleModelEnum.DOUBLE.text=2人
com.wunding.learn.common.enums.exam.CompetitionPeopleModelEnum.MULTI.text=多人
com.wunding.learn.common.enums.exam.CompetitionPeopleModelEnum.GROUP.text=分組
com.wunding.learn.common.enums.market.FirstInfoContentEnum.course.name=課程
com.wunding.learn.common.enums.market.FirstInfoContentEnum.exam.name=考試
com.wunding.learn.common.enums.market.FirstInfoContentEnum.live.name=直播
com.wunding.learn.common.enums.market.FirstInfoContentEnum.exercise.name=練習
com.wunding.learn.common.enums.market.FirstInfoContentEnum.survey.name=調研
com.wunding.learn.common.enums.market.FirstInfoContentEnum.info.name=資訊
com.wunding.learn.common.enums.market.FirstInfoContentEnum.project.name=學習項目
com.wunding.learn.common.enums.market.FirstInfoContentEnum.faceProject.name=面授項目
com.wunding.learn.common.enums.market.FirstInfoContentEnum.vote.name=投票
com.wunding.learn.common.enums.market.FirstInfoContentEnum.special.name=專題學習
com.wunding.learn.common.enums.market.FirstInfoContentEnum.hyperlink.name=外部鏈接
com.wunding.learn.common.enums.market.FirstInfoContentEnum.accountstatement.name=學習賬單
com.wunding.learn.common.enums.push.MyStatisticEnum.COLLECT.title=收藏
com.wunding.learn.common.enums.push.MyStatisticEnum.DOWNLOAD.title=下載
com.wunding.learn.common.enums.push.MyStatisticEnum.ALLSIGNIN.title=簽到
com.wunding.learn.common.enums.push.MyStatisticEnum.SUBJECT.title=話題
com.wunding.learn.common.enums.push.MyStatisticEnum.CERTIFICATE.title=證書
com.wunding.learn.common.enums.push.MyStatisticEnum.COURSE_TIME.title=課時
com.wunding.learn.common.enums.push.MyStatisticEnum.INTEGRAL.title=積分
com.wunding.learn.common.enums.push.MyStatisticEnum.LEARN_TIME.title=學時
com.wunding.learn.common.enums.push.MyStatisticEnum.LEARN_CREDIT.title=學分
com.wunding.learn.common.enums.push.MyStatisticEnum.GOLD_COIN.title=金幣
com.wunding.learn.common.enums.push.MyStatisticEnum.DESIGNATION.title=頭銜
com.wunding.learn.common.enums.push.MyStatisticEnum.MEDAL.title=勳章
com.wunding.learn.common.enums.push.MyStatisticEnum.SUGGEST.title=推薦
com.wunding.learn.project.service.enums.ProjectTaskTypeEnum.COURSE.taskName=課程
com.wunding.learn.project.service.enums.ProjectTaskTypeEnum.EXAM.taskName=考試
com.wunding.learn.project.service.enums.ProjectTaskTypeEnum.EXERCISE.taskName=練習
com.wunding.learn.project.service.enums.ProjectTaskTypeEnum.SURVEY.taskName=調研
com.wunding.learn.project.service.enums.ProjectTaskTypeEnum.LIVE.taskName=直播
com.wunding.learn.project.service.enums.ProjectTaskTypeEnum.TRAIN.taskName=培訓班
com.wunding.learn.project.service.enums.ProjectTaskTypeEnum.PROJECT.taskName=項目
com.wunding.learn.project.service.enums.ProjectTaskTypeEnum.APPLY.taskName=報名
com.wunding.learn.project.service.enums.ProjectTaskTypeEnum.SIGN.taskName=簽到
com.wunding.learn.project.service.enums.ProjectTaskTypeEnum.EVALUATION.taskName=評估
com.wunding.learn.project.service.enums.ProjectTaskTypeEnum.FORM.taskName=表單
com.wunding.learn.project.service.enums.ProjectTaskTypeEnum.PRACTICAL_OPERATION.taskName=實操
com.wunding.learn.common.enums.other.VoteType.CourseVote.text=課程點贊
com.wunding.learn.common.enums.other.VoteType.CourseCommentVote.text=課程評論點贊
com.wunding.learn.common.enums.other.VoteType.InfoVote.text=資訊點贊
com.wunding.learn.common.enums.other.VoteType.InfoCommentVote.text=資訊評論點贊
com.wunding.learn.common.enums.other.VoteType.SpecialCommentVote.text=專題評論點贊
com.wunding.learn.common.enums.other.VoteType.TrainCommentVote.text=培訓班評論點贊
com.wunding.learn.common.enums.other.VoteType.ExampleVote.text=案例點贊
com.wunding.learn.common.enums.other.VoteType.ExampleCommentVote.text=案例評論點贊
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.SYSTEM.name=全局
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.COURSE.name=課程
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.COURSE_WARE.name=課件
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.EXAM.name=考試
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.EXERCISE.name=練習
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.LIVE.name=直播
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.SURVEY.name=調研
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.PROJECT.name=學習項目
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.NEWS.name=資訊
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.EVALUATION.name=評估
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.TOPIC.name=話題版塊
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.LECTURER.name=講師
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.SIGN.name=簽到
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.FEEDBACK.name=反饋
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.EXAMPLE.name=案例庫
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.READING.name=共讀
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.PROMOTEDGAME_CHECKPOINT.name=闖關關卡
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.TRAIN.name=培訓項目
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.EXAM_COMPETITION_SINGLE.name=考試競賽(單人)
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.EXAM_COMPETITION_DOUBLE.name=考試競賽(雙人)
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.EXAM_COMPETITION_MULTI.name=考試競賽(多人)
com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum.EXAM_COMPETITION_GROUP.name=考試競賽(組隊)
com.wunding.learn.common.enums.other.OnWorkEnum.NON_WORK.name=離職
com.wunding.learn.common.enums.other.OnWorkEnum.ON_WORK.name=在職
com.wunding.learn.common.enums.other.OnWorkEnum.DELETE.name=刪除
com.wunding.learn.common.enums.exam.ScoreRuleTypeEnum.COMPETITION.name=競賽
com.wunding.learn.common.enums.exam.ScoreRuleTypeEnum.COMPETITION_SESSION.name=場次
com.wunding.learn.common.enums.other.ContentRuleEnum.HOT_COURSE.orderRuleDesc=
com.wunding.learn.common.enums.other.ContentRuleEnum.LEARNING_COURSE.orderRuleDesc=
com.wunding.learn.common.enums.other.ContentRuleEnum.SCORE_LECTURER.orderRuleDesc=按評分倒排
com.wunding.learn.common.enums.other.ContentRuleEnum.TIME_LECTURER.orderRuleDesc=按最新授課時間倒排
com.wunding.learn.common.enums.other.ContentRuleEnum.NEW_TOPIC_NOT_TOP.orderRuleDesc=創建時間倒排
com.wunding.learn.common.enums.other.ContentRuleEnum.HOT_TOPIC_COMMENT.orderRuleDesc=按評論數倒排
com.wunding.learn.common.enums.other.ContentRuleEnum.HOT_TOPIC_VIEW.orderRuleDesc=按遊覽數倒排
com.wunding.learn.common.enums.other.ContentRuleEnum.NEW_TOPIC_TOP.orderRuleDesc=創建時間倒排
com.wunding.learn.common.enums.other.ContentRuleEnum.HOT_CASE.orderRuleDesc=按下載數倒排
com.wunding.learn.common.enums.other.ContentRuleEnum.NEW_CASE.orderRuleDesc=按創建時間倒排
com.wunding.learn.common.enums.other.ContentRuleEnum.LIVING_LIVE.orderRuleDesc=
com.wunding.learn.common.enums.other.ContentRuleEnum.CURRENT_LIVE.orderRuleDesc=
com.wunding.learn.common.enums.other.ContentRuleEnum.ROLLBACK_LIVE.orderRuleDesc=
com.wunding.learn.common.enums.evaluation.EvaluationSourceEnum.MANUAL_UPLOAD.message=手動上傳
com.wunding.learn.common.enums.evaluation.EvaluationSourceEnum.EVAL_LIB.message=評估庫
com.wunding.learn.common.enums.evaluation.EvaluationSourceEnum.DOCUMENT.message=資料庫
com.wunding.learn.common.enums.exam.GroupPKCompetitionSessionUserTypeEnum.WAIT_JOIN.name=待加入
com.wunding.learn.common.enums.exam.GroupPKCompetitionSessionUserTypeEnum.JOINED.name=已加入
com.wunding.learn.common.enums.exam.GroupPKCompetitionSessionUserTypeEnum.READY.name=已準備
com.wunding.learn.common.enums.exam.GroupPKCompetitionSessionUserTypeEnum.START_ANSWER.name=開始答題
com.wunding.learn.common.enums.other.ResourceChangeEnum.course.name=課程
com.wunding.learn.common.enums.other.ResourceChangeEnum.info.name=資訊
com.wunding.learn.common.enums.other.ResourceChangeEnum.exam.name=考試
com.wunding.learn.common.enums.other.ResourceChangeEnum.live.name=直播
com.wunding.learn.common.enums.other.ResourceChangeEnum.exercise.name=練習
com.wunding.learn.common.enums.other.ResourceChangeEnum.survey.name=調研
com.wunding.learn.common.enums.other.ResourceChangeEnum.project.name=學習項目
com.wunding.learn.common.enums.other.ResourceChangeEnum.vote.name=投票
com.wunding.learn.common.enums.other.ResourceChangeEnum.special.name=專題制度
com.wunding.learn.common.enums.other.ResourceChangeEnum.hyperlink.name=外部鏈接
com.wunding.learn.common.enums.other.ResourceChangeEnum.accountstatement.name=學習賬單
com.wunding.learn.common.enums.other.ResourceChangeEnum.example.name=案例庫
com.wunding.learn.common.enums.other.ResourceChangeEnum.orgCate.name=組織分類
com.wunding.learn.common.enums.project.ProjectTaskType.course.name=課程
com.wunding.learn.common.enums.project.ProjectTaskType.exam.name=考試
com.wunding.learn.common.enums.project.ProjectTaskType.exercise.name=練習
com.wunding.learn.common.enums.project.ProjectTaskType.survey.name=調研
com.wunding.learn.common.enums.project.ProjectTaskType.live.name=直播
com.wunding.learn.common.enums.project.ProjectTaskType.project.name=項目
com.wunding.learn.common.enums.project.ProjectTaskType.mentor.name=導師
com.wunding.learn.common.enums.project.ProjectTaskType.lecturer.name=講師
com.wunding.learn.common.enums.user.SexEnum.UNKNOW.name=未知
com.wunding.learn.common.enums.user.SexEnum.MAN.name=男
com.wunding.learn.common.enums.user.SexEnum.WOMEN.name=女
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.ACCEPT_FEEDBACK.name=反饋意見受理
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.AUDIT_COURSEWARE.name=管理員審覈通過學員上傳課件
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.BEST_REPLY_TOPIC.name=最佳話題回帖
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COLLECT_COURSE.name=收藏課程
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COMMENT_COURSE.name=評論課程
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COMMENT_COURSEWARE.name=評論課件
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COMMENT_NEWS.name=評論資訊
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COMMIT_EVALUATE.name=提交評估
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COMMIT_EXAM.name=提交考試
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COMMIT_EXERCISE.name=完成練習
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COMMIT_FEEDBACK.name=提交反饋意見
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COMMIT_HOMEWORK.name=提交作業
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COMMIT_SIGN.name=完成簽到
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COURSE_STAR.name=課程評星
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COURSE_VOTE.name=點贊課程
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COURSEWARE_STAR.name=課件被評星
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COURSEWARE_VIEW.name=課件被瀏覽數
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COURSEWARE_VOTE.name=課件被點贊數
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.EVERYDAY_LEARN_COURSE.name=連續每日學完一個課程（打卡次數）
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.EVERYDAY_LEARN_COURSEWARE.name=連續每日學完一個課件（打卡次數）
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.EVERYDAY_LOGIN.name=每日連續登錄打卡（打卡次數)
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.EXAMPLE_ADD.name=提交案例
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.EXAMPLE_AUDIT.name=參與審覈案例
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.EXAMPLE_AUDIT_OVER.name=案例評審通過後，評審得分激勵
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.EXAMPLE_COMPANY.name=案例被設置爲公司級
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.EXAMPLE_EXTRA.name=案例被標識優質
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.EXAMPLE_GROUP.name=案例被設置爲集團級
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.EXAM_PROCESS.name=考試成績區間激勵
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.FINISH_COURSE.name=學完課程
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.FINISH_COURSEWARE.name=學完課件（固定）
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.FINISH_COURSEWARE_BYTIME.name=學完課件（按課件時長）
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.FINISH_PROJECT.name=完成項目
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.FINISH_PROJECT_APPLY.name=完成項目報名
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.FINISH_SURVEY.name=提交調研
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.FIRST_LOGIN.name=首次激活打卡
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.FOCUS_TOPIC.name=關注話題
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.INTEGRAL_CLEARING.name=積分清零
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.LEARN_COURSE.name=點擊課程學習
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.LEARN_COURSEWARE.name=點擊課件學習
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.LECTURER_JOIN_PROJECT_TEACH.name=參與授課項目
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.LECTURER_TEACH_COURSEWARE.name=參與授課課件個數
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.LIVE_CONTRIBUTE.name=通過發起直播貢獻知識獲得貢獻學分
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.LIVE_INTERACT.name=在直播間與主播進行互動給分
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.LIVE_INTERACT_COUNT.name=發起的直播達到一定觀看互動量給分
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.LIVE_QUESTION_AND_ANSWER.name=主播可在直播間內進行金幣懸賞提問，學員在直播間內回答問題可獲得金幣
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.MAKE_LIVE.name=發起直播給分
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.MAKE_TOPIC.name=發表話題
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.PARTICIPATION_DOUBLE.name=參與獲取（雙人）
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.PARTICIPATION_GROUP.name=參與獲取（組隊）
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.PARTICIPATION_MULTI.name=參與獲取（多人）
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.PARTICIPATION_SINGLE.name=參與獲取（單人）
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.PERSONAL_CHAMPION_MULTI.name=個人獲冠軍（多人）
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.PERSONAL_SECOND_MULTI.name=個人獲亞軍（多人）
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.PERSONAL_THIRD_MULTI.name=個人獲季軍（多人）
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.PERSONAL_WIN_DOUBLE.name=個人獲勝利（雙人）
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.PERSONAL_WIN_SINGLE.name=個人獲勝利（單人）
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.PROJECT_TASK_PROCESS.name=任務完成率
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.PROMOTED_GAME_FINISH_CHECKPOINT.name=闖關關卡完成
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.READING_BOOK_NUM.name=共讀閱讀圖書數量
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.READING_CLOCK_TIMES.name=共讀打卡次數
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.READING_COMMENT_EXPNUM.name=共讀評論心得數量
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.READING_EXPERIENCE_TIMES.name=共讀心得提交次數
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.REPLY_COURSE_COMMENT.name=回覆課程評論
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.REPLY_COURSEWARE_COMMENT.name=回覆課件評論
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.REPLY_TOPIC.name=回帖
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.SHARE_COURSE.name=分享課程
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.SHARE_COURSEWARE.name=分享課件
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.STAR_COURSEWARE.name=課件評星
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.TEAM_WIN_GROUP.name=團隊獲勝（組隊）
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.TOPIC_COMMENT_COUNT.name=話題回帖數量
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.VIEW_COURSE_PROCESS.name=所有課件的瀏覽時長進度(百分比)
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.VIEW_COURSEWARE_PROCESS.name=課件瀏覽時長進度(百分比)
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.VIEW_LIVE.name=點擊觀看直播
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.VIEW_LIVE_TOTAL_TIME.name=累計觀看直播時長達到指定時長
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.VIEW_NEWS.name=點擊查看資訊
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.VOTE_COURSE_COMMENT.name=點贊課程評論
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.VOTE_LECTURER.name=學員點贊講師
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.VOTE_NEWS_COMMENT.name=點贊資訊評論
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.COURSE_POSTER_SHARE.name=分享課程
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.PROJECT_POSTER_SHARE.name=分享項目
com.wunding.learn.common.constant.excitation.ExcitationEventNameEnum.LIVE_POSTER_SHARE.name=分享直播
com.wunding.learn.train.service.enums.LearnMapCategoryEnum.POST.name=崗位
com.wunding.learn.train.service.enums.LearnMapCategoryEnum.GROUP.name=羣體場景型
com.wunding.learn.train.service.enums.LearnMapCategoryEnum.COMPETENCY_STANDARD.name=崗位勝任力(標準)
com.wunding.learn.train.service.enums.LearnMapCategoryEnum.COMPETENCY.name=崗位勝任力
com.wunding.learn.common.enums.other.CategoryTypeEnum.TrainType.name=培訓類別
com.wunding.learn.common.enums.other.CategoryTypeEnum.ActivityType.name=活動方式
com.wunding.learn.common.enums.other.CategoryTypeEnum.SupplierTerritory.name=供應商合作領域類別
com.wunding.learn.common.enums.other.CategoryTypeEnum.SupplierFile.name=供應商資料類別
com.wunding.learn.common.enums.other.CategoryTypeEnum.FormTemplateCate.name=表單模板分類
com.wunding.learn.common.enums.other.CategoryTypeEnum.TrainSubjectCategory.name=開班培訓課題分類
com.wunding.learn.common.enums.other.CategoryTypeEnum.IndustryType.name=行業類型
com.wunding.learn.common.enums.other.CategoryTypeEnum.CompanySize.name=公司規模
com.wunding.learn.common.enums.other.CategoryTypeEnum.CourseCategory.name=課程類別
com.wunding.learn.common.enums.other.CategoryTypeEnum.TrainWithoutExam.name=外部培訓-考試分類
com.wunding.learn.common.enums.other.CategoryTypeEnum.CourseWithoutResource.name=外部課程資源
com.wunding.learn.common.enums.other.CategoryTypeEnum.AllocationType.name=激勵配置類型
com.wunding.learn.common.enums.other.CategoryTypeEnum.ExamCompetitionTrainType.name=考試競賽培訓類別
com.wunding.learn.common.enums.other.CategoryTypeEnum.LearnMapSpecialty.name=勝任力地圖專業
com.wunding.learn.common.enums.other.CategoryTypeEnum.APPLICABLE_HIERARCHY.name=測評工具適用層級
com.wunding.learn.common.enums.other.CategoryTypeEnum.USAGE_CLASSIFICATION.name=測評工具用途分類
com.wunding.learn.common.enums.other.CategoryTypeEnum.ASSESS_PROJECT.name=測評項目
com.wunding.learn.common.enums.other.CategoryTypeEnum.MentorCate.name=導師分類
com.wunding.learn.common.enums.other.CategoryTypeEnum.EXAMPLE_BUSINESS.name=案例條線
com.wunding.learn.common.enums.other.CategoryTypeEnum.ExampleAudit.name=案例審核標準
com.wunding.learn.file.api.constant.ExportFileNameEnum.Course.type=課程列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseStudyDetail.type=課程學習明細列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseCategoryExport.type=課程分類列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CoursewareStudyDetail.type=課件學習明細列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseComment.type=課程評論列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.SharedLibrary.type=課程共享庫列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CoursewarePackage.type=學員上傳課件列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.MergeCourse.type=合併課件到課程列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseWareLearn.type=學員課件學習明細列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseLearn.type=課程學習明細列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseCategory.type=課程分類管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseTagManage.type=課程標籤管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseSysCategory.type=課程標籤分類管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseTagSta.type=課程標籤統計列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseStudyStatistics.type=課程學習統計-按部門
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseStudyStatisticsUser.type=人員明細
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseNote.type=課程筆記
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseStudyCourseLearnUser.type=學習統計-按課程-人員明細
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseWithout.type=外部課程列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Exam.type=考試管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamCorrect.type=改卷管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamCorrectRecord.type=答題記錄列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.SchemaList.type=組卷方案列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Exercise.type=練習管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamEmployeeResultsDetail.type=學員考試成績明細列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamAnswerOfStatisticalOrg.type=考試答題統計按部門統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamAnswerOfStatisticalPost.type=考試答題統計按崗位統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamAnswerRecord.type=考試成績明細
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamAnswerRecordDetail.type=考試答題記錄明細
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamAnalysisQuestion.type=答題分析
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamQuestion.type=考題題目明細
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamCompetition.type=考試競賽管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamCompetitionUserScoreRank.type=得分排名
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamCompetitionUserAnswerRank.type=答題排名
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamCompetitionSessionUser.type=場次列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamCompetitionAnswerRecord.type=答題列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamCompetitionSessionStat.type=場次明細
com.wunding.learn.file.api.constant.ExportFileNameEnum.CompetitionAnswerRecordStat.type=答題統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamStatistic.type=按考試情況統計列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamJoinUserByBizRecord.type=考試情況統計-參加用戶-按照
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamNotJoinUserByBizRecord.type=考試情況統計-未參加用戶-按照
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamPostUserByBizRecord.type=考試情況統計-交卷用戶-按照
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamNotPassUserByBizRecord.type=考試情況統計-未通過用戶-按照
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamPassUserByBizRecord.type=考試情況統計-通過用戶-按照
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamLimitUserByBizRecord.type=考試情況統計-下發用戶-按照
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExampleLib.type=案例庫管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExpertLib.type=專家庫管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExampleBusiness.type=案例條線管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExampleComment.type=案例評論管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExampleCategory.type=案例分類管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExampleAuditCategory.type=案例審覈分類管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Live.type=直播管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LiveStatic.type=直播統計列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LiveVod.type=直播回放列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Post.type=話題管理帖子列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.PostBanUser.type=禁言名單管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.PostSection.type=話題板塊管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.PostSectionExpert.type=話題板塊專家管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.PostCountComment.type=回帖統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.Emigrated.type=闖關遊戲管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Checkpoint.type=闖關關卡列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CheckpointTask.type=闖關任務列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.StylesTemplate.type=樣式管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Notice.type=公告管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Team.type=團隊管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Statistical.type=闖關統計列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.TaskStatistical.type=查看明細列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Info.type=新聞資訊列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Category.type=分類管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Comment.type=資訊評論管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Survey.type=調研列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.SurveyAnalysis.type=調研分析詳情
com.wunding.learn.file.api.constant.ExportFileNameEnum.SurveyRecordDetail.type=全部調研明細
com.wunding.learn.file.api.constant.ExportFileNameEnum.StudyProject.type=學習項目管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectFixedDate.type=日期項目管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectFixedCycle.type=週期項目管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.QuickProject.type=快速培訓管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseTaskProject.type=課程任務列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.FormTemplate.type=輔導模板管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Position.type=崗位發展列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Classroom.type=教室列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainingPlan.type=培訓計劃管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectMentor.type=導師管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectLecturer.type=講師管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectApplyUser.type=報名管理用戶列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectSignUser.type=簽到管理用戶列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectPost.type=話題管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectWork.type=作業管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.EvalReplyUserList.type=評估人員列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectEvaluation.type=評估統計分析結果
com.wunding.learn.file.api.constant.ExportFileNameEnum.PROJECT_EVALUATION_DETAIL.type=評估統計分析明細
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectFormTemplate.type=輔導結果
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectHomeWork.type=作業詳情
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectCompletion.type=結業情況
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticTaskDetail.type=學習項目明細統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.PROJECT_FIXED_CYCLE_STATISTIC_TASK_DETAIL.type=週期項目明細統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.QuickProjectStatisticTaskDetail.type=快速培訓明細統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseTaskProjectStatisticTaskDetail.type=課程任務明細統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.FaceProjectStatisticTaskDetail.type=面授項目任務明細統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticPersonRank.type=個人學習排名統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticOrgCompleteUserDetailIsFinish.type=部門完成統計用戶詳情-已完成
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticOrgCompleteUserDetailNotFinish.type=部門完成統計用戶詳情-未完成
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticTeamCompleteUserDetailIsFinish.type=團隊完成統計用戶詳情-已完成
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticTeamCompleteUserDetailNotFinish.type=團隊完成統計用戶詳情-未完成
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticTeamLearnRank.type=團隊學習排名統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticOrgLearnRank.type=部門學習排名統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatistic.type=培訓班統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectHoldStatistic.type=學習項目舉辦統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectJoiningStatistic.type=學習項目參與統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerTrainStatistic.type=講師培訓統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerTrainStatisticByBiz.type=講師培訓統計按
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticLecturer.type=培訓班講師統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectOrgStatistic.type=培訓班組織統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.FormTemplateColumn.type=任務記錄列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticByType.type=按類型-項目統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticByTypeUser.type=按類型-項目統計-
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticJoinUser.type=項目統計-參加人員詳情
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticByProject.type=項目統計-按項目
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticByOrg.type=項目統計-按部門
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticByUser.type=項目統計-按人員
com.wunding.learn.file.api.constant.ExportFileNameEnum.SpecialFixedDate.type=專題管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.SpecialFixedCycle.type=週期專題管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.SpecialCategory.type=專題分類管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.SpecialLabel.type=專題標籤管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.SpecialCompletion.type=專題結業情況
com.wunding.learn.file.api.constant.ExportFileNameEnum.SpecialStatisticTaskDetail.type=專題明細統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.Lecturer.type=講師管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerCategory.type=講師分類設置列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerLevel.type=講師等級設置列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerModifyRecord.type=講師異動記錄列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerExaminationAudit.type=完課審核列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerUpgradeConfig.type=規則配置_晉級規則列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerDemotionConfig.type=規則配置_降級規則列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerRemovalConfig.type=規則配置_出庫規則列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerExamination.type=授課課時信息明細列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerExaminationAssess.type=授課評估信息明細列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerTeachDetail.type=線下授課明細列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.InsideLecturer.type=講師統計(按部門)-講師管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.NotWorkday.type=非工作日列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Certification.type=證書管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CertificationSetup.type=證書體係列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CertificationLevel.type=證書等級管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CertificationRule.type=發證規則列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CertificationRelate.type=持證明細列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CertificationRelateDept.type=部門持證明細列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CertificationHoldUser.type=應持證管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CertificationHoldUserStat.type=應持證統計管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CertificationDeptTarget.type=部門持證目標管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CerDeptTargetReport.type=部門持證目標統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.ASSESS_TOOL_LIST.type=測評工具列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ASSESS_PROJECT_LIST.type=測評項目列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ASSESS_USER_LIST2.type=測評用戶列表-導出方式2
com.wunding.learn.file.api.constant.ExportFileNameEnum.ASSESS_USER_LIST1.type=測評用戶列表-導出方式1
com.wunding.learn.file.api.constant.ExportFileNameEnum.ASSESS_DEP_MANAGER_USER_LIST.type=部門測評人員管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ASSESS_PROJECT_ASSESS_DETAILS.type=測評項目測評明細列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ASSESS_QUESTION_DETAIL_LIST.type=測評題目明細列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Reading.type=共讀管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ReadingBooksManage.type=圖書管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ReadingSign.type=打卡列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ReadingBookExperience.type=心得列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ReadingBookExperienceComment.type=心得評論管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ReadingBookExperienceStar.type=心得點贊管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ReadingReports.type=舉報列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ReadingStatistics.type=圖書統計列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserRankReadingStatistics.type=學員共讀排行統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.TaskFinishReadingStatistics.type=任務完成統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.Appraise.type=評價列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.AppraiseLecturer.type=講師評價列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Meeting.type=任職資格答辯列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.AppraiseReferee.type=評委列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.MeetingReferee.type=評委列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.AppraiseProvider.type=被評人列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.MeetingProvider.type=答辯人列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.AppraiseFileType.type=材料規則列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.AppraiseProviderFile.type=材料管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.AppraiseDetail.type=評分明細列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.MeetingScoreDetail.type=評分統計列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.AppraiseShowDetail.type=查看明細列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.AppraiseHistory.type=打分歷史列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Recruiting.type=招募列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerRecruiting.type=講師招募列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.RecruitingAssistant.type=協辦人員列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.RecruitingMaterial.type=宣傳材料列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.RecruitingMaterialRule.type=材料規則列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.RecruitingAudit.type=招募審覈列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.RecruitingAuditSurveyDetail.type=招募審覈調研明細列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseWareLib.type=課件庫列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.courseWareLibraryCategory.type=課件庫分類
com.wunding.learn.file.api.constant.ExportFileNameEnum.TestPaperLib.type=試卷庫列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.TestPaperLibraryCategory.type=試卷庫分類
com.wunding.learn.file.api.constant.ExportFileNameEnum.TestPaperQuestion.type=題目管理
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamLib.type=考題庫列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamLibQuestion.type=題目管理
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamLibCategory.type=考題庫分類
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExerciseLib.type=練習題庫列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExerciseLibCategory.type=練習庫分類
com.wunding.learn.file.api.constant.ExportFileNameEnum.SurveyLib.type=調研庫列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.SurveyLibCategory.type=調研庫分類
com.wunding.learn.file.api.constant.ExportFileNameEnum.MaterialLib.type=知識管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.MaterialCategory.type=知識庫標籤
com.wunding.learn.file.api.constant.ExportFileNameEnum.KnowledgeBaseType.type=知識庫分類
com.wunding.learn.file.api.constant.ExportFileNameEnum.ResearchField.type=講師研究領域
com.wunding.learn.file.api.constant.ExportFileNameEnum.EvaluationLib.type=評估庫列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.EvaluationCategory.type=評估庫分類列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.FormTemplateManage.type=表單模板管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamOrgStatistic.type=按部門考試情況統計列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectLecturerTeachDetail.type=講師授課明細列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerTeachStatisticDetail.type=講師授課統計明細列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Award.type=獎品管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.AwardCategory.type=獎品分類管理
com.wunding.learn.file.api.constant.ExportFileNameEnum.GameLottery.type=積分中獎列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.MailTemplate.type=郵箱模板列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.PushManage.type=推送管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.PushMessageManage.type=消息管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.SignStatistics.type=簽到統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.SignStat.type=簽到統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectSignStat.type=項目簽到統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainSignStat.type=培訓簽到統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.SignList.type=簽到列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectSignList.type=項目簽到列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.AwardRedeemRecord.type=金幣兌換
com.wunding.learn.file.api.constant.ExportFileNameEnum.AwardExchangeRecord.type=兌換記錄
com.wunding.learn.file.api.constant.ExportFileNameEnum.PaymentOrder.type=訂單列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.PaymentOrgOrderMember.type=機構訂單會員列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.VoteManage.type=投票管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.VoteContent.type=投票內容管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.VoteStatistics.type=投票統計列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.VoteDetail.type=投票明細列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.User.type=用戶管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserExpand.type=用戶管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserExpandTemplate.type=用戶導入模板
com.wunding.learn.file.api.constant.ExportFileNameEnum.Org.type=組織列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Role.type=角色管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Dict.type=數據字典管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Version.type=版本管理
com.wunding.learn.file.api.constant.ExportFileNameEnum.Title.type=頭銜設置列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.HomePageConfig.type=首頁配置列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Feedback.type=反饋管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.IdentityPostSystemTemplate.type=崗位體系導入模板
com.wunding.learn.file.api.constant.ExportFileNameEnum.IdentityPost.type=崗位身份列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.IdentityPostTemplate.type=崗位身份導入模板
com.wunding.learn.file.api.constant.ExportFileNameEnum.IdentityPostSystemData.type=崗位體系數據列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Expert.type=專家評委庫數據列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.IdentityTime.type=時間身份列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Identity.type=身份列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.InfoStatAnalysis.type=資訊訪問統計列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnRecordStatAnalysis.type=學員檔案列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnRecordCourseStatAnalysis.type=學員檔案統計-新學課程列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnRecordExamStatAnalysis.type=學員檔案統計-參與考試列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnRecordInfoStatAnalysis.type=學員檔案統計-查看資訊列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnRecordProjectStatAnalysis.type=學員檔案統計-參與學習項目列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnRecordFaceProjectStatAnalysis.type=學員檔案統計-參與面授班級列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnRecordTrainStatAnalysis.type=學員檔案統計-參與培訓項目列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnRecordSurveyStatAnalysis.type=學員檔案統計-參與調研列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ScoreRankStatAnalysis.type=經驗排行榜列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserIntegralStatAnalysis.type=積分統計列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerIntegral.type=積分統計列表-按講師
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserIntegralDetail.type=積分統計詳情
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserIntegralDetailStatAnalysis.type=積分統計詳情列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseLearnState.type=課程情況統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseLearned.type=課程學習情況記錄
com.wunding.learn.file.api.constant.ExportFileNameEnum.CourseAgreeDetail.type=課程點贊情況記錄
com.wunding.learn.file.api.constant.ExportFileNameEnum.CoursewareLearnStatAnalysis.type=課件學習統計列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.CoursewareLearnDetailStatAnalysis.type=課件學習統計詳情列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.OrgLearnStatAnalysis.type=部門學習統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamStateStatAnalysis.type=考試情況統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamStateStatPartAnalysis.type=考試情況統計參與人數
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamStateStatPassAnalysis.type=考試情況統計及格人數
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExamStateStatPostAnalysis.type=考試情況統計交卷人數
com.wunding.learn.file.api.constant.ExportFileNameEnum.GldTradeStatAnalysis.type=金幣交易查詢
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExchangeRecordStatAnalysis.type=兌換記錄查詢
com.wunding.learn.file.api.constant.ExportFileNameEnum.ExcitationCollectStatAnalysis.type=學員激勵彙總
com.wunding.learn.file.api.constant.ExportFileNameEnum.InsideLecturerByDepartment.type=講師統計（按部門）
com.wunding.learn.file.api.constant.ExportFileNameEnum.InsideLecturerByCategory.type=講師統計（按類別）
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserExcitationRecordStatAnalysis.type=目標激勵明細查詢
com.wunding.learn.file.api.constant.ExportFileNameEnum.OnlineUserStatAnalysis.type=上線用戶統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainPlanStatAnalysis.type=培訓計劃統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.TimeRegionStatAnalysis.type=訪問時段統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.StudentUploadCoursewareStatAnalysis.type=學員課件上傳統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.SearchKeyStatAnalysis.type=搜索關鍵字統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.CertifiedCategory.type=認證分類列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Log.type=操作日誌
com.wunding.learn.file.api.constant.ExportFileNameEnum.BizLog.type=業務操作日誌
com.wunding.learn.file.api.constant.ExportFileNameEnum.OrgThirdSyncRecord.type=部門同步歷史
com.wunding.learn.file.api.constant.ExportFileNameEnum.PostThirdSyncRecord.type=崗位同步歷史
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserThirdSyncRecord.type=人員同步歷史
com.wunding.learn.file.api.constant.ExportFileNameEnum.CertificationRelateWatermarkImg.type=明細圖片
com.wunding.learn.file.api.constant.ExportFileNameEnum.Train.type=培訓項目列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainActivity.type=培訓項目活動列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainProject.type=班級管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainActivityPracticeRecord.type=實操管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.MedalUserRelation.type=勳章列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.FormManage.type=表單管理清單列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Plan.type=培訓計劃列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.PlanAudit.type=培訓計劃管理-審覈列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.PlanInventory.type=培訓計劃清單列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.PlanInventoryTemplate.type=培訓計劃清單模板
com.wunding.learn.file.api.constant.ExportFileNameEnum.PlanTrain.type=培訓項目列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.PlanStatistic.type=培訓計劃彙總表
com.wunding.learn.file.api.constant.ExportFileNameEnum.PlanInventoryStatistic.type=培訓計劃條目明細
com.wunding.learn.file.api.constant.ExportFileNameEnum.PlanExecuteMonth.type=培訓計劃執行統計-按月
com.wunding.learn.file.api.constant.ExportFileNameEnum.PlanExecuteYear.type=培訓計劃執行統計-按年
com.wunding.learn.file.api.constant.ExportFileNameEnum.Leaflet.type=宣傳單列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.PlanCategory.type=培訓計劃類別
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerCourseAuthByLecturer.type=課程認證講師按照講師導出
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerCourseAuthByCourse.type=課程認證講師按照課程導出
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerCourseList.type=講師開發課程列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerCourseAuthTeach.type=課程認證講師-講師授課明細
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainWithOut.type=外部培訓管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainWithOutAssist.type=外部培訓協辦管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainWithoutStatistics.type=外部培訓統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainWithoutApply.type=外部培訓報名列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainWithoutApplyAssist.type=外部培訓協辦報名列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainWithoutResult.type=外部培訓結果列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainWithoutApplyDetail.type=外部培訓明細列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainWithoutWorkStatistics.type=外部培訓統計-專業
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainMembers.type=成員管理-學員
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainCoOrganizer.type=成員管理-協辦人員
com.wunding.learn.file.api.constant.ExportFileNameEnum.TaskScore.type=活動分
com.wunding.learn.file.api.constant.ExportFileNameEnum.Supplier.type=供應商管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.SupplierFile.type=供應商資料管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainLecturerTeachDetail.type=供應商講師授課管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainCourse.type=培訓班課程列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainTaskDetail.type=培訓項目明細統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainPersonRank.type=培訓項目個人學習排名統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainOrgLearn.type=培訓項目部門學習排名統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserWhiteRecord.type=推送白名單
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserIdentityInfo.type=用戶身份信息
com.wunding.learn.file.api.constant.ExportFileNameEnum.IdentityUserInfo.type=身份用戶管理
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserIdentityList.type=用戶身份管理
com.wunding.learn.file.api.constant.ExportFileNameEnum.VisibleViewLimitUserList.type=下發人員明細
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserVisitItemRecord.type=鏈接訪問明細
com.wunding.learn.file.api.constant.ExportFileNameEnum.HomeRouterVisitDetail.type=後臺訪問明細
com.wunding.learn.file.api.constant.ExportFileNameEnum.ItemVisitRecord.type=欄目訪問記錄
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMap.type=學習地圖
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapList.type=學習地圖列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapExec.type=學習地圖執行列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapLearnDetail.type=學習明細列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapLearnUser.type=學習用戶列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapActivityHorizontalStat.type=活動橫向統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapPhaseHorizontalStat.type=階段橫向統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapActivity.type=學習地圖活動列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapProgressStat.type=學習地圖情況
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapActivityProgressStat.type=學習地圖活動情況
com.wunding.learn.file.api.constant.ExportFileNameEnum.AbilityDictionary.type=能力詞典
com.wunding.learn.file.api.constant.ExportFileNameEnum.AbilityMode.type=能力模型
com.wunding.learn.file.api.constant.ExportFileNameEnum.PermissionConfig.type=權限目錄配置
com.wunding.learn.file.api.constant.ExportFileNameEnum.PermissionRouter.type=權限目錄路由配置
com.wunding.learn.file.api.constant.ExportFileNameEnum.CoursewareQuestionAnswerRecord.type=課件答題明細
com.wunding.learn.file.api.constant.ExportFileNameEnum.ApplyTemplate.type=面授項目報名模板
com.wunding.learn.file.api.constant.ExportFileNameEnum.FaceProjectApplyUser.type=面授項目用戶報名列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.FaceProject.type=面授項目管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.JobQualification.type=任職資格列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.JobAuthentication.type=資格認證列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.JobAuthApplyRecord.type=認證人員列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.PracticalSuperviseUser.type=監督評價員列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.PracticalOperationUserBySupervise.type=監督學員列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.PracticalOperationUser.type=實操記錄列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapAbilityOrgStatistic.type=能力項部門初訓統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapAbilityOrgStatistic2.type=能力項部門複訓統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapUserOrgStatistic.type=勝任力地圖部門初訓統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapUserOrgStatistic2.type=勝任力地圖部門複訓統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapUserResourcesStatistic.type=勝任力地圖學習明細統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapUserStatistic.type=勝任力地圖學習進度明細統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnMapAbilityAsk.type=學習地圖能力學習要求
com.wunding.learn.file.api.constant.ExportFileNameEnum.InvoiceList.type=開票管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectVacate.type=請假管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ScheduleStat.type=日程統計列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectFormTemplateAnnex.type=輔導結果附件
com.wunding.learn.file.api.constant.ExportFileNameEnum.March.type=遊戲管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.MarchCheckpoint.type=遊戲關卡列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.MarchCheckpointTask.type=遊戲任務列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.MarchTeam.type=團隊管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.MarchPost.type=話題管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.MarchNotice.type=說明管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.MarchPostCountComment.type=回帖統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.ASSESS_TOOL_QUESTION_TEM.type=測評工具題目導入模板
com.wunding.learn.file.api.constant.ExportFileNameEnum.ASSESS_PROGRESS.type=測評項目進度統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.ASSESS_IMPORT_USER.type=測評用戶導入模板
com.wunding.learn.file.api.constant.ExportFileNameEnum.ASSESS_IMPORT_RECORD.type=測評記錄導入模板
com.wunding.learn.file.api.constant.ExportFileNameEnum.ASSESS_IMPORT_DETAILS.type=測評明細導入模版
com.wunding.learn.file.api.constant.ExportFileNameEnum.RecordDetail.type=答題明細
com.wunding.learn.file.api.constant.ExportFileNameEnum.ASSESS_REPORT.type=測評報告
com.wunding.learn.file.api.constant.ExportFileNameEnum.COMPETITION_SESSION_USER_DETAIL.type=考試競賽場次人員信息
com.wunding.learn.file.api.constant.ExportFileNameEnum.INTERVIEW_QUANTITY_STATISTICS.type=訪問量統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserGoldCoin.type=用戶金幣列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.UserGoldCoinBill.type=用戶金幣賬單列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.MemberCard.type=會員卡管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.MemberOrg.type=會員機構管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.Member.type=會員管理列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LecturerProjectStatistic=講師統計列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.FaceProjectStatistic=面授項目培訓統計列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.LearnProjectStatistic=學習項目培訓統計列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.TrainProjectStatistic=培訓項目培訓統計列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectStatisticUserDetail=學員列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.TASK_MENTOR_LIST=任務導師列表
com.wunding.learn.common.enums.other.SystemTypeEnum.AFTER_SALE.message=售後體系
com.wunding.learn.common.enums.other.SystemTypeEnum.INTERNAL_TRAINING.message=內訓體系
com.wunding.learn.common.enums.other.ContentRuleEnum.HOT_COURSE.name=熱門課程
com.wunding.learn.common.enums.other.ContentRuleEnum.LEARNING_COURSE.name=我正在學
com.wunding.learn.common.enums.other.ContentRuleEnum.SCORE_LECTURER.name=講師風采1
com.wunding.learn.common.enums.other.ContentRuleEnum.TIME_LECTURER.name=講師風采2
com.wunding.learn.common.enums.other.ContentRuleEnum.NEW_TOPIC_NOT_TOP.name=最新話題
com.wunding.learn.common.enums.other.ContentRuleEnum.HOT_TOPIC_COMMENT.name=熱門話題(按回複數)
com.wunding.learn.common.enums.other.ContentRuleEnum.HOT_TOPIC_VIEW.name=熱門話題(按遊覽數)
com.wunding.learn.common.enums.other.ContentRuleEnum.NEW_TOPIC_TOP.name=最新話題(含置頂)
com.wunding.learn.common.enums.other.ContentRuleEnum.HOT_CASE.name=熱門案例
com.wunding.learn.common.enums.other.ContentRuleEnum.NEW_CASE.name=最新案例
com.wunding.learn.common.enums.other.ContentRuleEnum.LIVING_LIVE.name=直播中
com.wunding.learn.common.enums.other.ContentRuleEnum.CURRENT_LIVE.name=當前直播
com.wunding.learn.common.enums.other.ContentRuleEnum.ROLLBACK_LIVE.name=直播回看
com.wunding.learn.common.enums.promotedgame.GameQuestionType.Single.text=單選
com.wunding.learn.common.enums.promotedgame.GameQuestionType.Multi.text=多選
com.wunding.learn.common.enums.promotedgame.GameQuestionType.Judge.text=判斷
com.wunding.learn.common.enums.exam.CompetitionSessionStatusEnum.DEFAULT_VALUE.name=默認值
com.wunding.learn.common.enums.exam.CompetitionSessionStatusEnum.READY.name=準備中 
com.wunding.learn.common.enums.exam.CompetitionSessionStatusEnum.ON_GOING.name=進行中
com.wunding.learn.common.enums.exam.CompetitionSessionStatusEnum.END.name=已經結束
com.wunding.learn.project.service.enums.QuickProjectRoleEnum.STUDENT.name=學員
com.wunding.learn.project.service.enums.QuickProjectRoleEnum.TRAINER.name=培訓師
com.wunding.learn.project.service.enums.QuickProjectRoleEnum.ADMIN.name=管理員
com.wunding.learn.common.enums.other.MenuItemEnums.INDEX.name=首頁
com.wunding.learn.common.enums.other.MenuItemEnums.NOTICE.name=公告
com.wunding.learn.common.enums.other.MenuItemEnums.COURSE.name=課程
com.wunding.learn.common.enums.other.MenuItemEnums.SCHEDULE.name=日程
com.wunding.learn.common.enums.other.MenuItemEnums.ACTIVITY.name=活動
com.wunding.learn.common.enums.other.MenuItemEnums.CLASS.name=班級
com.wunding.learn.common.enums.other.MenuItemEnums.PHOTO.name=照片牆
com.wunding.learn.common.enums.other.MenuItemEnums.DATUM.name=資料
com.wunding.learn.common.enums.other.MenuItemEnums.CASE.name=案例
com.wunding.learn.common.enums.other.MenuItemEnums.TOPIC.name=話題
com.wunding.learn.common.enums.other.MenuItemEnums.RANK.name=排名
com.wunding.learn.common.enums.other.MenuItemEnums.LECTURER.name=講師
com.wunding.learn.common.enums.other.MenuItemEnums.SPECIAL.name=專題
com.wunding.learn.common.enums.evaluation.QuestionType.SINGLE.text=單選
com.wunding.learn.common.enums.evaluation.QuestionType.MULTI.text=多選
com.wunding.learn.common.enums.evaluation.QuestionType.JUDGE.text=判斷
com.wunding.learn.common.enums.evaluation.QuestionType.BLANKS.text=填空
com.wunding.learn.common.enums.evaluation.QuestionType.QA.text=問答
com.wunding.learn.common.enums.evaluation.QuestionType.GRADE.text=打分
com.wunding.learn.common.constant.plan.PlanStatusEnum.DRAFT.name=草稿
com.wunding.learn.common.constant.plan.PlanStatusEnum.TO_BE_REVIEWED.name=待審覈
com.wunding.learn.common.constant.plan.PlanStatusEnum.REJECTED.name=駁回
com.wunding.learn.common.constant.plan.PlanStatusEnum.APPROVED.name=審覈通過
com.wunding.learn.common.enums.evaluation.SourceEnum.REFERENCE.message=引用
com.wunding.learn.common.enums.evaluation.SourceEnum.MANUAL_UPLOAD.message=手動上傳
com.wunding.learn.reading.service.enums.ReadingTaskDescription.READING_TASK_TYPE_SIGN.name=活動期間打卡次數
com.wunding.learn.reading.service.enums.ReadingTaskDescription.READING_TASK_TYPE_EXPERIENCE.name=活動期間提交心得次數
com.wunding.learn.reading.service.enums.ReadingTaskDescription.READING_TASK_TYPE_READING_N.name=活動期間閱讀/聽圖書數量
com.wunding.learn.reading.service.enums.ReadingTaskDescription.READING_TASK_TYPE_COMMENT_N.name=評論心得的數量
com.wunding.learn.reading.service.enums.ReadingTaskDescription.READING_TASK_TYPE_READING.name=閱讀某本書的數量
com.wunding.learn.reading.service.enums.ReadingTaskDescription.READING_TASK_TYPE_COMMENT.name=提交某本書的心得
com.wunding.learn.common.enums.other.TriggerTypeEnum.ANY.text=任意滿足觸發
com.wunding.learn.common.enums.other.TriggerTypeEnum.ALL.text=全部滿足觸發
com.wunding.learn.common.enums.other.GeneralJudgeEnum.CONFIRM.text=是
com.wunding.learn.common.enums.other.GeneralJudgeEnum.NEGATIVE.text=否
com.wunding.learn.user.service.enums.ItemTypeEnum.MIDDLE_ITEM.name=中間菜單
com.wunding.learn.user.service.enums.ItemTypeEnum.BOTTOM_ITEM.name=底部菜單
com.wunding.learn.user.service.enums.ItemTypeEnum.MY_LEARN_ITEM.name=我的學習
com.wunding.learn.user.service.enums.ItemTypeEnum.APPLICATION_ITEM.name=我的應用
com.wunding.learn.user.service.enums.ItemTypeEnum.MY_PAGE.name=我的頁面
com.wunding.learn.user.service.enums.CustomMenuEnum.INTEGRAL.name=積分
com.wunding.learn.user.service.enums.CustomMenuEnum.HOURS.name=學時
com.wunding.learn.user.service.enums.CustomMenuEnum.CREDIT.name=學分
com.wunding.learn.user.service.enums.CustomMenuEnum.GOLD.name=金幣
com.wunding.learn.user.service.enums.CustomMenuEnum.CERTIFICATE.name=證書
com.wunding.learn.user.service.enums.CustomMenuEnum.COLLECT.name=收藏
com.wunding.learn.user.service.enums.CustomMenuEnum.IDP.name=IDP
com.wunding.learn.user.service.enums.CustomMenuEnum.TASK.name=任務
com.wunding.learn.user.service.enums.CustomMenuEnum.COURSE.name=課程
com.wunding.learn.user.service.enums.CustomMenuEnum.EXAM.name=考試
com.wunding.learn.user.service.enums.CustomMenuEnum.SURVEY.name=調研
com.wunding.learn.user.service.enums.CustomMenuEnum.PROJECT.name=項目
com.wunding.learn.user.service.enums.CustomMenuEnum.LIVE.name=直播
com.wunding.learn.user.service.enums.CustomMenuEnum.TOPIC.name=話題
com.wunding.learn.user.service.enums.CustomMenuEnum.QUIZ.name=闖關
com.wunding.learn.user.service.enums.CustomMenuEnum.SIGN.name=簽到
com.wunding.learn.user.service.enums.CustomMenuEnum.MY_INFO.name=我的信息
com.wunding.learn.user.service.enums.CustomMenuEnum.CHANGE_PASSWORD.name=修改密碼
com.wunding.learn.user.service.enums.CustomMenuEnum.FEEDBACK.name=意見反饋
com.wunding.learn.user.service.enums.CustomMenuEnum.MY_TAG.name=我的標簽
com.wunding.learn.user.service.enums.CustomMenuEnum.INCENTIVE_EXPLANATION.name=激勵說明
com.wunding.learn.user.service.enums.CustomMenuEnum.THIRD_PARTY_BINDING.name=第三方綁定
com.wunding.learn.user.service.enums.CustomMenuEnum.PRIVACY_POLICY.name=隱私政策
com.wunding.learn.user.service.enums.CustomMenuEnum.SWITCH_LANGUAGE.name=選擇語言
com.wunding.learn.user.service.enums.CustomMenuEnum.ENABLE_STUDENT_UPLOAD.name=啟用PC端 「學員課件上傳」
com.wunding.learn.user.service.enums.CustomMenuEnum.ENABLE_H5_IDP.name=啟用H5端 「IDP」
com.wunding.learn.user.service.enums.CustomMenuEnum.ENABLE_H5_TARGET.name=啟用H5端 「目標崗位」
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_1.description=用於各處背景展示
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_2.description=登錄頁標題背景，含slogan
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_3.description=首頁標題背景，不含slogan
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_4.description=首頁背景號角人物圖
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_5.description=除首頁外所有頁面通用背景
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_6.description=取消按鈕
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_7.description=關卡完成時的彈窗中，關卡完成提示
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_8.description=關閉按鈕
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_9.description=上傳步數提示頁中的按鈕
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_10.description=
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_11.description=步數上傳完成後的彈窗提示語
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_12.description=完成關卡後，點擊去看故事的按鈕
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_13.description=首次登錄的遊戲說明提示彈窗中的按鈕
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_14.description=步數上傳彈窗的立即上傳按鈕
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_15.description=今日裏程的標題logo
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_16.description=進入“我的”頁面的入口按鈕
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_17.description=進入“排行榜”頁面的入口按鈕
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_18.description=活動說明的標題logo
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_19.description=上傳昨日運動按鈕的標題logo
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_20.description=學習專區的標題logo
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_21.description=首頁團隊進度條的小人
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_22.description=我的，故事中的旗幟
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_23.description=我的，故事中的旗幟
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_24.description=學習專區的頂部背景疊層
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_25.description=“我的”頁的頂部背景疊層
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_26.description=除首頁外的其他頁面的剪影背景
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_27.description=地圖鏈接箭頭
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_28.description=地圖鏈接箭頭
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_29.description=地圖鏈接箭頭
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_30.description=地圖鏈接箭頭
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_31.description=地圖鏈接箭頭
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_32.description=地圖鏈接箭頭
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_33.description=地圖鏈接箭頭
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_34.description=地圖鏈接箭頭
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_35.description=地圖鏈接箭頭
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_36.description=地圖鏈接箭頭
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_37.description=地圖鏈接箭頭
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_38.description=地圖鏈接箭頭
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_39.description=地圖鏈接箭頭
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_40.description=地圖鏈接箭頭
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_41.description=地圖鏈接箭頭
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_42.description=地圖鏈接箭頭
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_43.description=地圖鏈接箭頭
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_44.description=地圖鏈接箭頭
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_45.description=地圖鏈接箭頭
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_46.description=地圖鏈接箭頭
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_47.description=地圖鏈接箭頭
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_48.description=地圖鏈接箭頭
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_49.description=首頁地圖地點的原點光環
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_50.description=首頁地圖地點的原點光環
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_51.description=首頁地圖地點的旗幟
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_52.description=說明等彈窗的頭部背景
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_53.description=關卡完成的恭喜彈窗的背景
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_54.description=故事彈窗的背景
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_55.description=首次登錄的遊戲說明提示彈窗中的頂部背景)
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_56.description=說明彈窗的標題旁的logo，每個說明配置一個旗幟
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_57.description=我的頁的標題背景，顯示在文字下方
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_58.description=我的頁的卡片背景
com.wunding.learn.common.enums.march.MarchStylesTemplateEnum.MARCH_IMAGE_59.description=學習專區的橫幅
com.wunding.learn.common.enums.other.ModelEnums.MENU.name=菜單欄目
com.wunding.learn.common.enums.other.ModelEnums.ROTOGRAPH.name=輪播圖
com.wunding.learn.common.enums.other.ModelEnums.NOTICE.name=滾動公告
com.wunding.learn.common.enums.other.ModelEnums.CASE.name=案例
com.wunding.learn.common.enums.other.ModelEnums.DATUM.name=資料
com.wunding.learn.common.enums.other.ModelEnums.TOPIC.name=話題
com.wunding.learn.common.enums.other.ModelEnums.COURSE.name=課程
com.wunding.learn.common.enums.other.ModelEnums.SCHEDULE.name=日程
com.wunding.learn.common.enums.other.ModelEnums.LEAFLET.name=宣傳單
com.wunding.learn.common.enums.other.ModelEnums.RANK.name=排名
com.wunding.learn.common.enums.other.ModelEnums.PHOTO.name=照片
com.wunding.learn.common.enums.other.ModelEnums.CLASS.name=班級
com.wunding.learn.common.enums.other.ModelEnums.LIVE.name=直播
com.wunding.learn.common.enums.other.ModelEnums.LECTURER.name=講師
com.wunding.learn.common.enums.other.ModelEnums.SPECIAL.name=專題
com.wunding.learn.common.enums.survey.SurveyQuestionTypeEnum.QUESTION_TYPE_RADIO.name=單選
com.wunding.learn.common.enums.survey.SurveyQuestionTypeEnum.QUESTION_TYPE_MULTI_SELECT.name=多選
com.wunding.learn.common.enums.survey.SurveyQuestionTypeEnum.QUESTION_TYPE_JUDGEMENT.name=判斷
com.wunding.learn.common.enums.survey.SurveyQuestionTypeEnum.QUESTION_TYPE_CLOZE.name=填空
com.wunding.learn.common.enums.survey.SurveyQuestionTypeEnum.QUESTION_TYPE_QA.name=問答
com.wunding.learn.common.enums.survey.SurveyQuestionTypeEnum.QUESTION_TYPE_GRADE.name=打分
com.wunding.learn.common.enums.survey.SurveyQuestionTypeEnum.QUESTION_TYPE_MATERIALS.name=材料
com.wunding.learn.common.enums.market.RepeatDescriptionEnum.RECOMMENDED_DATE_DESC.message=按推薦時間倒排
com.wunding.learn.common.enums.market.RepeatDescriptionEnum.ALL_INTERIOR_TEACH.message=所有內部講師
com.wunding.learn.common.enums.market.RepeatDescriptionEnum.LIVE_START_DATE_DESC.message=按直播開始時間倒排
com.wunding.learn.common.enums.market.RepeatDescriptionEnum.GIVE_LIKE_DESC.message=按點贊數倒排
com.wunding.learn.common.enums.other.ResourcesTypeEnum.COURSE_DOWNLOAD.name=課程下載
com.wunding.learn.common.enums.other.ResourcesTypeEnum.LECTURER.name=講師
com.wunding.learn.common.enums.viewlimit.NewViewLimitTypeEnum.OrgLimit.viewName=部門下發
com.wunding.learn.common.enums.viewlimit.NewViewLimitTypeEnum.IdentityLimit.viewName=身份下發
com.wunding.learn.common.enums.viewlimit.NewViewLimitTypeEnum.UserLimit.viewName=用戶下發
com.wunding.learn.common.enums.market.HeadContentRuleEnum.GUESSYOULIKE.showRuleDesc=猜你喜歡單獨邏輯
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RECOMMENDCOURSE.showRuleDesc=下發範圍標識爲精選好課的課程
com.wunding.learn.common.enums.market.HeadContentRuleEnum.POPULARCOURSE1.showRuleDesc=下發範圍 最近一個月內發佈的 評論數+評星次數 大於{0}的
com.wunding.learn.common.enums.market.HeadContentRuleEnum.POPULARCOURSE2.showRuleDesc=下發範圍 評論數+評星次數 大於{0}的
com.wunding.learn.common.enums.market.HeadContentRuleEnum.POSTCOURSE.showRuleDesc=與自己崗位匹配的課程
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LEARNINGCOURSE.showRuleDesc=我學習的，未完成的課程
com.wunding.learn.common.enums.market.HeadContentRuleEnum.NEWS.showRuleDesc=下發範圍
com.wunding.learn.common.enums.market.HeadContentRuleEnum.STUDYPROJECT.showRuleDesc=下發範圍包含我未參與或者未完成的學習項目
com.wunding.learn.common.enums.market.HeadContentRuleEnum.STUDYPROJECTSPECIFYCATEGORY.showRuleDesc=指定類別的學習項目
com.wunding.learn.common.enums.market.HeadContentRuleEnum.SPECIAL.showRuleDesc=下發範圍包含我未參與或者未完成的學習項目
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LECTURERELEGANTDEMEANOUR1.showRuleDesc=所有內部講師
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LECTURERELEGANTDEMEANOUR2.showRuleDesc=所有內部講師
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LECTURERELEGANTDEMEANOUR3.showRuleDesc=所有內部講師
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LIVINGBROADCAST.showRuleDesc=下發範圍 直播結束時間大於當前時間並且直播開始時間小於當前時間
com.wunding.learn.common.enums.market.HeadContentRuleEnum.CURRENTLIVE.showRuleDesc=下發範圍直播結束時間大於當前時間
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LIVEPLAYBACK.showRuleDesc=下發範圍直播結束時間小於當前間，且有回看文件的直播
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LATESTTOPICS.showRuleDesc=版塊下發範圍
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTTOPICS1.showRuleDesc=版塊下發範圍發表時間在{0}天內回帖數量大於{1}個
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTTOPICS2.showRuleDesc=版塊下發範圍回帖數量大於{0}個
com.wunding.learn.common.enums.market.HeadContentRuleEnum.ELITEPOSTS1.showRuleDesc=版塊下發範圍設置爲置頂的帖子
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASEOFTHEMONTH1.showRuleDesc=所有的範圍本月發表的，且本月發生了點贊數大於{2}
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASEOFTHEMONTH2.showRuleDesc=公司範圍內公開本月發表的，且本月發生了點贊數大於{2}
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASEOFTHEMONTH3.showRuleDesc=集團範圍內公開本月發表的，且本月發生了點贊數大於{2}'
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASE1.showRuleDesc=所有的範圍點贊數大於2
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASE2.showRuleDesc=公司範圍內公開點贊數大於2
com.wunding.learn.common.enums.market.HeadContentRuleEnum.HOTCASE3.showRuleDesc=集團範圍內公開點贊數大於2
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RECOMMENDCASE1.showRuleDesc=標記爲案例質量等級1的
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RECOMMENDCASE2.showRuleDesc=標記爲案例質量等級2的
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RECOMMENDCASE3.showRuleDesc=標記爲案例質量等級3的
com.wunding.learn.common.enums.market.HeadContentRuleEnum.TRAINPROJECT.showRuleDesc=下發範圍包含我的培訓項目
com.wunding.learn.common.enums.market.HeadContentRuleEnum.RAINPROJECTSPECIFYCATEGORY.showRuleDesc=指定類別的培訓項目
com.wunding.learn.common.enums.market.HeadContentRuleEnum.ORG_INFO.showRuleDesc=組織信息
com.wunding.learn.common.enums.market.HeadContentRuleEnum.CLICK_CLOCK_IN.showRuleDesc=每日點擊簽到，完成打卡
com.wunding.learn.common.enums.market.HeadContentRuleEnum.LEARN_CLOCK_IN.showRuleDesc=每日學習課程達到10分鐘，則視為完成打卡
com.wunding.learn.common.enums.market.HeadContentRuleEnum.NON_LONG_TASK.showRuleDesc=所有帶有開始和結束日期的學習活動
com.wunding.learn.common.enums.market.HeadContentRuleEnum.CONTAIN_LONG_TASK.showRuleDesc=所有學習活動
com.wunding.learn.common.enums.course.ParaTypeEnum.MEMBER_DEFAULT_CARD.name=學院開放用戶默認會員卡
com.wunding.learn.common.enums.course.ParaTypeEnum.IS_HANG_UP.name=是否播放防掛機
com.wunding.learn.common.enums.course.ParaTypeEnum.CAN_SPEED.name=首次播放是否倍數
com.wunding.learn.common.enums.course.ParaTypeEnum.CAN_DRAG.name=首次播放是否可拖拽
com.wunding.learn.common.enums.course.ParaTypeEnum.LECTURER_RESOURCE_APPLY_SPECIAL_ROLE.name=講師資源申請特權角色
com.wunding.learn.common.enums.course.ParaTypeEnum.EVAL_PASS_VALUE.name=評估優良分值
com.wunding.learn.common.enums.course.ParaTypeEnum.SIGN_START_TIME_ADVANCE_MINUTES.name=培訓簽到(簽退)開始提前時間(分鐘)
com.wunding.learn.common.enums.course.ParaTypeEnum.SIGN_END_TIME_AFTER_MINUTES.name=培訓簽到(簽退)結束延後時間(分鐘)
com.wunding.learn.common.enums.course.ParaTypeEnum.EXCITATION_EXCHANGE_CONFIG.name=激勵兌換屬性配置
com.wunding.learn.common.enums.course.ParaTypeEnum.EVALUATION_START_DELAY_DEFAULT_TIME.name=評估默認開始時間(分鐘)
com.wunding.learn.common.enums.course.ParaTypeEnum.EVALUATION_END_DELAY_DEFAULT_TIME.name=評估默認結束時間(分鐘)
com.wunding.learn.common.enums.course.ParaTypeEnum.LECTURER_EXAMINATION_EVAL_AFTER_SALE_TEMPLATE.name=內訓默認評估模板
com.wunding.learn.common.enums.course.ParaTypeEnum.NORMAL_ON_DUTY_TIME.name=正常上班時間
com.wunding.learn.common.enums.course.ParaTypeEnum.NORMAL_OFF_MORRING.name=正常上午下班時間
com.wunding.learn.common.enums.course.ParaTypeEnum.NORMAL_ON_AFTERNOON.name=正常下午上班時間
com.wunding.learn.common.enums.course.ParaTypeEnum.NORMAL_OFF_DUTY_TIME.name=正常下班時間
com.wunding.learn.common.enums.course.ParaTypeEnum.LECTURER_EXAMINATION_EVAL_INTERNAL_TRAINING_TEMPLATE.name=售後默認評估模板
com.wunding.learn.common.enums.course.ParaTypeEnum.NUMBER_OF_WECOM.name=企業微信對接數量
com.wunding.learn.common.enums.course.ParaTypeEnum.AVAILABLE_DEFAULT_APPLY_FORM_TEMPLATE.name=是否啓用默認報名模板
com.wunding.learn.file.api.constant.ExportFileNameEnum.USER_POSTER_SHARE.type=用戶海報分享列表
com.wunding.learn.common.enums.other.ShareChannelEnum.WE_CHAT.name=微信好友
com.wunding.learn.common.enums.other.ShareChannelEnum.WE_CHAT_MOMENTS.name=微信朋友圈
com.wunding.learn.common.enums.other.ShareResourceTypeEnum.COURSE.name=課程
com.wunding.learn.common.enums.other.ShareResourceTypeEnum.PROJECT.name=學習項目
com.wunding.learn.common.enums.other.ShareResourceTypeEnum.LIVE.name=直播
com.wunding.learn.common.enums.push.PushNoticeEventNameEnum.PUBLISH_NOTICE.name=發布通知
com.wunding.learn.common.enums.push.PushNoticeEventNameEnum.REMINDER_NOTICE.name=催辦通知
com.wunding.learn.common.enums.push.PushNoticeEventNameEnum.CO_HANDLE_PENDING_NOTICE.name=協辦待審通知
com.wunding.learn.common.enums.push.PushNoticeEventNameEnum.AUDIT_RESULT_NOTIFICATION.name=審核結果通知
com.wunding.learn.common.enums.push.PushNoticeEventNameEnum.PUBLISH_REGISTRATION_NOTICE.name=發布報名通知
com.wunding.learn.common.enums.push.PushNoticeEventNameEnum.NOTIFICATION_OF_EVALUATION_RESULTS.name=評價結果通知
com.wunding.learn.common.enums.push.PushNoticeEventNameEnum.CLASS_INFORMATION_NOTICE.name=開班資訊通知
com.wunding.learn.common.enums.push.PushNoticeEventNameEnum.AUDIT_RESULTS_CANNOT_BE_MODIFIED_IF_THE_AUDIT_FAILS.name=審核未通過不可修改審核結果通知
com.wunding.learn.common.enums.push.PushNoticeEventNameEnum.AUDIT_FAILED_TO_MODIFY_THE_RESULT_NOTICE.name=審核未通過可修改結果通知
com.wunding.learn.common.enums.push.PushNoticeEventNameEnum.NOTIFICATION_OF_APPROVAL_RESULT.name=審核通過結果通知
com.wunding.learn.common.enums.push.PushNoticeEventNameEnum.NOTICE_OF_REPLY.name=答辯結果通知
com.wunding.learn.common.enums.push.PushNoticeEventNameEnum.CLASS_START_INFORMATION_NOTICE.name=開課資訊通知
com.wunding.learn.file.api.constant.ExportFileNameEnum.SysTemTagStatAnalysis.type=系統標籤統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.ApplyLecturer.type=講師預約審核列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ApplyCourseDownload.type=下載申請列表
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectCompletionStatistic.type=項目完成情況統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectUserTask.type=用戶項目任務完成情況統計
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectUserProjectExcitationRecord.type=用戶項目激勵獲取記錄
com.wunding.learn.file.api.constant.ExportFileNameEnum.ProjectUserProjectCoursewareStudyDetail.type=用戶學習項目課時統計
com.wunding.learn.certification.service.enums.AssessComputedModeEnum.OPTION_SCORE.name=選項結果對應分值
com.wunding.learn.certification.service.enums.AssessComputedModeEnum.OPTION_SUM_SCORE.name=選項結果總分分值
com.wunding.learn.certification.service.enums.AssessComputedModeEnum.OPTION_AVG_SCORE.name=選項結果平均分值
com.wunding.learn.certification.service.enums.AssessComputedModeEnum.OPTION_MIN_SCORE.name=選項結果中最低分值
com.wunding.learn.certification.service.enums.AssessComputedModeEnum.OPTION_MAX_SCORE.name=選項結果中最高分值
com.wunding.learn.march.service.enums.StyleCodeEnum.HOME_BACKGROUND.name=首頁背景圖
com.wunding.learn.march.service.enums.StyleCodeEnum.HOME_BACKGROUND.description=用於各處背景展示
com.wunding.learn.march.service.enums.StyleCodeEnum.LOGIN_TITLE_BACKGROUND.name=標題背景（登錄頁）
com.wunding.learn.march.service.enums.StyleCodeEnum.LOGIN_TITLE_BACKGROUND.description=登錄頁標題背景，含slogan
com.wunding.learn.march.service.enums.StyleCodeEnum.HOME_TITLE_BACKGROUND.name=標題背景
com.wunding.learn.march.service.enums.StyleCodeEnum.HOME_TITLE_BACKGROUND.description=首頁標題背景，不含slogan
com.wunding.learn.march.service.enums.StyleCodeEnum.HOME_SILHOUETTE.name=首頁背景剪影圖
com.wunding.learn.march.service.enums.StyleCodeEnum.HOME_SILHOUETTE.description=首頁背景號角人物圖
com.wunding.learn.march.service.enums.StyleCodeEnum.OTHER_PAGE_BACKGROUND.name=其他頁背景圖
com.wunding.learn.march.service.enums.StyleCodeEnum.OTHER_PAGE_BACKGROUND.description=除首頁外所有頁面通用背景
com.wunding.learn.march.service.enums.StyleCodeEnum.CANCEL_BUTTON.name=取消按鈕
com.wunding.learn.march.service.enums.StyleCodeEnum.CANCEL_BUTTON.description=取消按鈕
com.wunding.learn.march.service.enums.StyleCodeEnum.KNOW_COMPLETED_LEVEL.name=我知道了（完成關卡）
com.wunding.learn.march.service.enums.StyleCodeEnum.KNOW_COMPLETED_LEVEL.description=關卡完成時的彈窗中，關卡完成提示
com.wunding.learn.march.service.enums.StyleCodeEnum.CLOSE_BUTTON.name=關閉
com.wunding.learn.march.service.enums.StyleCodeEnum.CLOSE_BUTTON.description=關閉按鈕
com.wunding.learn.march.service.enums.StyleCodeEnum.KNOW_UPLOAD_STEPS.name=我知道了（上傳步數）
com.wunding.learn.march.service.enums.StyleCodeEnum.KNOW_UPLOAD_STEPS.description=上傳步數提示頁中的按鈕
com.wunding.learn.march.service.enums.StyleCodeEnum.OK_BUTTON.name=好的
com.wunding.learn.march.service.enums.StyleCodeEnum.OK_BUTTON.description=
com.wunding.learn.march.service.enums.StyleCodeEnum.REFRESH_STEPS.name=刷新步數
com.wunding.learn.march.service.enums.StyleCodeEnum.REFRESH_STEPS.description=步數上傳完成後的彈窗提示語
com.wunding.learn.march.service.enums.StyleCodeEnum.VIEW_STORY.name=去看故事
com.wunding.learn.march.service.enums.StyleCodeEnum.VIEW_STORY.description=完成關卡後，點擊去看故事的按鈕
com.wunding.learn.march.service.enums.StyleCodeEnum.CONFIRM_FIRST_LOGIN.name=確定（首次登錄提示語）
com.wunding.learn.march.service.enums.StyleCodeEnum.CONFIRM_FIRST_LOGIN.description=首次登錄的遊戲說明提示彈窗中的按鈕
com.wunding.learn.march.service.enums.StyleCodeEnum.UPLOAD_NOW.name=立即上傳
com.wunding.learn.march.service.enums.StyleCodeEnum.UPLOAD_NOW.description=步數上傳彈窗的立即上傳按鈕
com.wunding.learn.march.service.enums.StyleCodeEnum.TODAY_MILEAGE.name=今日裏程
com.wunding.learn.march.service.enums.StyleCodeEnum.TODAY_MILEAGE.description=今日裏程的標題logo
com.wunding.learn.march.service.enums.StyleCodeEnum.MY_BUTTON.name=我的按鈕
com.wunding.learn.march.service.enums.StyleCodeEnum.MY_BUTTON.description=進入「我的」頁面的入口按鈕
com.wunding.learn.march.service.enums.StyleCodeEnum.RANKING_BUTTON.name=排行榜按鈕
com.wunding.learn.march.service.enums.StyleCodeEnum.RANKING_BUTTON.description=進入「排行榜」頁面的入口按鈕
com.wunding.learn.march.service.enums.StyleCodeEnum.ACTIVITY_DESCRIPTION.name=活動說明
com.wunding.learn.march.service.enums.StyleCodeEnum.ACTIVITY_DESCRIPTION.description=活動說明的標題logo
com.wunding.learn.march.service.enums.StyleCodeEnum.UPLOAD_YESTERDAY_ACTIVITY.name=上傳昨日運動按鈕
com.wunding.learn.march.service.enums.StyleCodeEnum.UPLOAD_YESTERDAY_ACTIVITY.description=上傳昨日運動按鈕的標題logo
com.wunding.learn.march.service.enums.StyleCodeEnum.LEARNING_ZONE_BUTTON.name=學習專區按鈕
com.wunding.learn.march.service.enums.StyleCodeEnum.LEARNING_ZONE_BUTTON.description=學習專區的標題logo
com.wunding.learn.march.service.enums.StyleCodeEnum.PROGRESS_BAR_PERSON.name=進度條小人
com.wunding.learn.march.service.enums.StyleCodeEnum.PROGRESS_BAR_PERSON.description=首頁團隊進度條的小人
com.wunding.learn.march.service.enums.StyleCodeEnum.FLAG_LOCKED.name=旗幟（未解鎖)
com.wunding.learn.march.service.enums.StyleCodeEnum.FLAG_LOCKED.description=我的，故事中的旗幟
com.wunding.learn.march.service.enums.StyleCodeEnum.FLAG_UNLOCKED.name=旗幟（已解鎖)
com.wunding.learn.march.service.enums.StyleCodeEnum.FLAG_UNLOCKED.description=我的，故事中的旗幟
com.wunding.learn.march.service.enums.StyleCodeEnum.LEARNING_ZONE_BACKGROUND.name=背景（學習專區)
com.wunding.learn.march.service.enums.StyleCodeEnum.LEARNING_ZONE_BACKGROUND.description=學習專區的頂部背景疊層
com.wunding.learn.march.service.enums.StyleCodeEnum.MY_PAGE_BACKGROUND.name=背景（「我的」頁)
com.wunding.learn.march.service.enums.StyleCodeEnum.MY_PAGE_BACKGROUND.description=「我的」頁的頂部背景疊層
com.wunding.learn.march.service.enums.StyleCodeEnum.OTHER_PAGE_SILHOUETTE.name=其他頁背景剪影圖
com.wunding.learn.march.service.enums.StyleCodeEnum.OTHER_PAGE_SILHOUETTE.description=除首頁外的其他頁面的剪影背景
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_10_LOCKED.name=地點連接圖10（未完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_10_LOCKED.description=地圖鏈接箭頭
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_10_UNLOCKED.name=地點連接圖10（已完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_10_UNLOCKED.description=地圖鏈接箭頭
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_11_LOCKED.name=地點連接圖11（未完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_11_LOCKED.description=地圖鏈接箭頭
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_11_UNLOCKED.name=地點連接圖11（已完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_11_UNLOCKED.description=地圖鏈接箭頭
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_1_LOCKED.name=地點連接圖1（未完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_1_LOCKED.description=地圖鏈接箭頭
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_1_UNLOCKED.name=地點連接圖1（已完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_1_UNLOCKED.description=地圖鏈接箭頭
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_2_LOCKED.name=地點連接圖2（未完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_2_LOCKED.description=地圖鏈接箭頭
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_2_UNLOCKED.name=地點連接圖2（已完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_2_UNLOCKED.description=地圖鏈接箭頭
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_3_LOCKED.name=地點連接圖3（未完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_3_LOCKED.description=地圖鏈接箭頭
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_3_UNLOCKED.name=地點連接圖3（已完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_3_UNLOCKED.description=地圖鏈接箭頭
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_4_LOCKED.name=地點連接圖4（未完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_4_LOCKED.description=地圖鏈接箭頭
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_4_UNLOCKED.name=地點連接圖4（已完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_4_UNLOCKED.description=地圖鏈接箭頭
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_5_LOCKED.name=地點連接圖5（未完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_5_LOCKED.description=地圖鏈接箭頭
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_5_UNLOCKED.name=地點連接圖5（已完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_5_UNLOCKED.description=地圖鏈接箭頭
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_6_LOCKED.name=地點連接圖6（未完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_6_LOCKED.description=地圖鏈接箭頭
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_6_UNLOCKED.name=地點連接圖6（已完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_6_UNLOCKED.description=地圖鏈接箭頭
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_7_LOCKED.name=地點連接圖7（未完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_7_LOCKED.description=地圖鏈接箭頭
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_7_UNLOCKED.name=地點連接圖7（已完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_7_UNLOCKED.description=地圖鏈接箭頭
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_8_LOCKED.name=地點連接圖8（未完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_8_LOCKED.description=地圖鏈接箭頭
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_8_UNLOCKED.name=地點連接圖8（已完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_8_UNLOCKED.description=地圖鏈接箭頭
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_9_LOCKED.name=地點連接圖9（未完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_9_LOCKED.description=地圖鏈接箭頭
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_9_UNLOCKED.name=地點連接圖9（已完成)
com.wunding.learn.march.service.enums.StyleCodeEnum.MAP_ARROW_9_UNLOCKED.description=地圖鏈接箭頭
com.wunding.learn.march.service.enums.StyleCodeEnum.LEVEL_LOCKED_HALO.name=關卡未解鎖光環
com.wunding.learn.march.service.enums.StyleCodeEnum.LEVEL_LOCKED_HALO.description=首頁地圖地點的原點光環
com.wunding.learn.march.service.enums.StyleCodeEnum.LEVEL_UNLOCKED_HALO.name=關卡解鎖光環
com.wunding.learn.march.service.enums.StyleCodeEnum.LEVEL_UNLOCKED_HALO.description=首頁地圖地點的原點光環
com.wunding.learn.march.service.enums.StyleCodeEnum.LEVEL_UNLOCKED_FLAG.name=關卡解鎖旗幟
com.wunding.learn.march.service.enums.StyleCodeEnum.LEVEL_UNLOCKED_FLAG.description=首頁地圖地點的旗幟
com.wunding.learn.march.service.enums.StyleCodeEnum.POPUP_HEADER_BACKGROUND.name=彈窗頭部背景
com.wunding.learn.march.service.enums.StyleCodeEnum.POPUP_HEADER_BACKGROUND.description=說明等彈窗的頭部背景
com.wunding.learn.march.service.enums.StyleCodeEnum.LEVEL_COMPLETED_BACKGROUND.name=關卡完成背景
com.wunding.learn.march.service.enums.StyleCodeEnum.LEVEL_COMPLETED_BACKGROUND.description=關卡完成的恭喜彈窗的背景
com.wunding.learn.march.service.enums.StyleCodeEnum.STORY_POPUP_BACKGROUND.name=故事彈窗背景
com.wunding.learn.march.service.enums.StyleCodeEnum.STORY_POPUP_BACKGROUND.description=故事彈窗的背景
com.wunding.learn.march.service.enums.StyleCodeEnum.FIRST_LOGIN_POPUP_BACKGROUND.name=提示彈窗背景)
com.wunding.learn.march.service.enums.StyleCodeEnum.FIRST_LOGIN_POPUP_BACKGROUND.description=首次登錄的遊戲說明提示彈窗中的頂部背景)
com.wunding.learn.march.service.enums.StyleCodeEnum.INSTRUCTION_TITLE_FLAG.name=說明標題旗幟)
com.wunding.learn.march.service.enums.StyleCodeEnum.INSTRUCTION_TITLE_FLAG.description=說明彈窗的標題旁的logo，每個說明配置一個旗幟
com.wunding.learn.march.service.enums.StyleCodeEnum.MY_PAGE_TITLE_BACKGROUND.name=我的頁標題背景
com.wunding.learn.march.service.enums.StyleCodeEnum.MY_PAGE_TITLE_BACKGROUND.description=我的頁的標題背景，顯示在文字下方
com.wunding.learn.march.service.enums.StyleCodeEnum.MY_PAGE_CARD_BACKGROUND.name=我的頁卡片背景
com.wunding.learn.march.service.enums.StyleCodeEnum.MY_PAGE_CARD_BACKGROUND.description=我的頁的卡片背景
com.wunding.learn.march.service.enums.StyleCodeEnum.LEARNING_ZONE_TITLE.name=學習專區標題
com.wunding.learn.march.service.enums.StyleCodeEnum.LEARNING_ZONE_TITLE.description=學習專區的橫幅
