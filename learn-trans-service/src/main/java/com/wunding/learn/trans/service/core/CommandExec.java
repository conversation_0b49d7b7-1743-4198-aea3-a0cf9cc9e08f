package com.wunding.learn.trans.service.core;

import com.alibaba.ttl.threadpool.TtlExecutors;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.trans.service.callback.LogCallback;
import com.wunding.learn.trans.service.config.Pdf2htmlConfig;
import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/7/12
 */
@Slf4j
@Component
public class CommandExec {

    /**
     * pdf转html参数对象
     */
    @Resource
    private Pdf2htmlConfig config;

    /**
     * 执行线程池
     */
    private ExecutorService execThreadPool;

    /**
     * 查检超进线程池
     */
    private ExecutorService checkTimeoutThreadPool;

    /**
     * 初始化线程池
     */
    @PostConstruct
    public void init() {
        execThreadPool = TtlExecutors.getTtlExecutorService(
            new ThreadPoolExecutor(config.getThreadNum(), config.getThreadNum(), 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(),
            new CustomizableThreadFactory("exec-pool-"))
        );
        checkTimeoutThreadPool = TtlExecutors.getTtlExecutorService(
            new ThreadPoolExecutor(config.getThreadNum(), config.getThreadNum(), 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(),
            new CustomizableThreadFactory("checkTimeout-pool-"))
        );
    }

    /**
     * 转换执行器
     *
     * @param cmd 转换命令
     */
    public int exec(String cmd, LogCallback logCallback) {
        return exec(cmd, null, logCallback);
    }

    /**
     * 转换执行器
     *
     * @param cmd         转换命令
     * @param pageTimeOut 单页超时时间
     * @return 执行状态，非0异常退出
     */
    public int exec(String[] cmd, Integer pageTimeOut, LogCallback logCallback) {
        log.info("exec cmd:" + JsonUtil.objToJson(cmd));
        ProcessBuilder pb = new ProcessBuilder(cmd);
        pb.directory(new File(config.getHome()));
        pb.redirectErrorStream(true);
        Process process;

        try {
            log.info("pb.start() 1");
            process = pb.start();
            log.info("pb.start() 2");
        } catch (IOException e) {
            log.error("执行失败", e);
            return -1;
        }

        // 是否执行完成状态
        AtomicBoolean isDone = new AtomicBoolean(false);

        log.info("readLog thread submit");
        execThreadPool.submit(() -> {
            log.info("readLog begin");
            //日志读取
            readLog(process, process.getInputStream(), logCallback, isDone, pageTimeOut);
            log.info("readLog end");
        });

        try {
            // 等待执行完成
            process.waitFor();
        } catch (InterruptedException e) {
            log.error("等待执行完成出现异常", e);
            Thread.currentThread().interrupt();
        }
        isDone.set(true);
        int exitValue = process.exitValue();
        log.info("exitValue:{}", exitValue);
        process.destroy();
        return exitValue;
    }

    /**
     * 转换执行器
     *
     * @param cmd         转换命令
     * @param pageTimeOut 单页超时时间
     * @return 执行状态，非0异常退出
     */
    public int exec(String cmd, Integer pageTimeOut, LogCallback logCallback) {
        return exec(cmd.split(" "), pageTimeOut, logCallback);
    }

    /**
     * 读取日志
     *
     * @param process 执行进制
     * @param isDone  是否执行完成
     */
    private void readLog(Process process, InputStream stream, LogCallback logCallback, AtomicBoolean isDone,
        Integer pageTimeOut) {
        // 获取错误输出
        BufferedReader readStderr;
        try {
            readStderr = new BufferedReader(new InputStreamReader(stream, "GBK"));
        } catch (UnsupportedEncodingException e) {
            log.error("执行失败", e);
            logCallback.call("执行失败");
            return;
        }
        //当前页使用的时间计数
        AtomicInteger pageTime = new AtomicInteger(0);

        log.info("checkTimeoutThread submit");
        //启线程判断是否超时
        checkTimeoutThreadPool.submit(() -> {
            log.info("checkTimeoutThread 1");
            //判断时间超时并加1 如果未超时并且未完成则计时间
            Integer timeout = pageTimeOut == null ? config.getPageTimeout() : pageTimeOut;
            log.info(
                "checkTimeoutThread timeout:" + timeout + ", isDone:" + isDone.get() + ",pageTime" + pageTime.get());
            while (pageTime.getAndIncrement() < timeout && !isDone.get()) {
                log.info(
                    "checkTimeoutThread sleep timeout:" + timeout + ",isDone:" + isDone.get() + ",pageTime" + pageTime
                        .get());
                sleep(1000);
            }
            log.info("checkOutTime timeout:" + timeout + ",isDone:" + isDone.get() + ",pageTime:" + pageTime.get());
            // 如果是超时，标记失败 并终止转换进程 回调异常给调用端
            if (!isDone.get()) {
                // 标记执行完成
                isDone.set(true);
                log.info("process.destroyForcibly()");
                // 停止进程
                process.destroyForcibly();
                // 回调
                logCallback.call("单页超时未成完转码");
                // 该错误无法在生产ERROR级别查看到，增加输出ERROR的输出
                log.error("转码失败，单页超时未成完转码");
            }

        });
        // 记录最后一行日志，如果连续重复的日志，只记录一次
        String lastLog = "";
        String errTemp;
        try {
            // 未执行完成 读取一行日志
            while (!isDone.get() && (errTemp = readStderr.readLine()) != null) {
                // 有日志说明程序在执行，重置超时时间
                pageTime.set(0);
                // 如果日志和上一次一样，不回调日志信息，读取下一行日志
                if (Objects.equals(lastLog, errTemp)) {
                    sleep(1);
                    continue;
                }
                //记录最后的日志
                lastLog = errTemp;
                // 日志回调
                logCallback.call(errTemp);
                log.info("readLog errTemp:[{}]", errTemp);
                sleep(1);
            }
            readStderr.close();
        } catch (IOException e) {
            log.error("发生异常",e);
        }
    }

    /**
     * 线程休眠
     */
    private void sleep(int time) {
        try {
            Thread.sleep(time);
        } catch (InterruptedException e) {
            log.error("线程休眠报错", e);
            Thread.currentThread().interrupt();
        }
    }

}
