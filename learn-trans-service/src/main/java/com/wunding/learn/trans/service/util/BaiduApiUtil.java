package com.wunding.learn.trans.service.util;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Maps;
import com.wunding.learn.common.constant.trans.TransErrorNumEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.util.http.HttpUtilManager;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.util.string.StringPool;
import com.wunding.learn.trans.service.dto.BaiduCreateDTO;
import com.wunding.learn.trans.service.dto.BaiduQueryDTO;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 百度api实用程序
 *
 * <AUTHOR>
 * @date 2023/06/06
 */
@Component
@Data
@Slf4j
public class BaiduApiUtil {


    /**
     * 每个token有30天的有效期,新建token也不影响老token使用,本地缓存token值以减少http请求调用
     */
    private Cache<String, String> cache = CacheBuilder.newBuilder()
        .expireAfterWrite(29, TimeUnit.DAYS)
        .maximumSize(10L)
        .build();

    private static final String ACCESS_TOKEN = "access_token";

    @Value("${baidu.createUrl}")
    private String createTaskUrl;

    @Value("${baidu.queryUrl}")
    private String queryUrl;

    @Value("${baidu.tokenUrl}")
    private String tokenUrl;

    @Value("${baidu.accessKey}")
    private String accessKey;

    @Value("${baidu.secretKey}")
    private String secretKey;

    /**
     * 查询文件转写任务
     *
     * @param taskId 任务id
     */
    public BaiduQueryDTO queryTextFromId(String taskId) {
        String body = JsonUtil.objToJson(Map.of("task_ids", List.of(taskId)));

        String response = HttpUtilManager.getInstance()
            .sendHttpPost(StringPool.EMPTY, queryUrl, Map.of(ACCESS_TOKEN, getAccessToken()), body);

        return JsonUtil.jsonToObj(response, BaiduQueryDTO.class);
    }


    /**
     * mp3文件转写任务创建
     *
     * @param speechUrl 可访问的外链地址语音url
     */
    public BaiduCreateDTO obtainMp3Text(String speechUrl)  {
        return coreAudioFileTranscription(speechUrl, "mp3", 80001, 16000);
    }

    /**
     * 核心音频文件转录api
     *
     * @param speechUrl 语音url
     * @param format    总体安排
     * @param pid       pid控制器
     * @param rate      速度
     */
    public BaiduCreateDTO coreAudioFileTranscription(String speechUrl, String format, int pid, int rate) {

        HashMap<String, Object> bodyMap = Maps.newHashMapWithExpectedSize(4);
        bodyMap.put("speech_url", speechUrl);
        bodyMap.put("format", format);
        bodyMap.put("pid", pid);
        bodyMap.put("rate", rate);
        String body = JsonUtil.objToJson(bodyMap);
        String response = HttpUtilManager.getInstance()
            .sendHttpPost(StringPool.EMPTY, createTaskUrl, Map.of(ACCESS_TOKEN, getAccessToken()), body);

        if (StringUtils.isEmpty(response)) {
            log.error("body:{},url:{}", body, createTaskUrl);
            throw new BusinessException(TransErrorNumEnum.CREATE_BAIDU_AUDIO_TASK_FAIL);
        }

        return JsonUtil.jsonToObj(response, BaiduCreateDTO.class);
    }

    /**
     * 从用户的AK，SK生成鉴权签名（Access Token）
     *
     * @return 鉴权签名（Access Token）
     * @throws IOException IO异常
     */
    private String getAccessToken() {
        String token = cache.getIfPresent(ACCESS_TOKEN);
        if (StringUtils.isNotEmpty(token)) {
            log.info("get_access_token_cache_hit");
            if (log.isDebugEnabled()) {
                log.debug("access_token:{}", token);
            }
            return token;
        }

        final Map<String, String> requestParamMap = Map.of("grant_type", "client_credentials", "client_id", accessKey,
            "client_secret", secretKey);

        try {
            final String response = HttpUtilManager.getInstance()
                .requestHttpPost(StringPool.EMPTY, tokenUrl, requestParamMap);
            if(StringUtils.isNotBlank((String)JsonUtil.parseObject(response).get("error_description"))){
                log.error("getAccessToken error msg:{}",response);
            }
            Map<String,Object> result = JsonUtil.parseObject(response);
            if(result != null){
                token = (String)result.get(ACCESS_TOKEN);
            }
            if(StringUtils.isBlank(token)){
                log.error("getAccessToken is blank");
                token = "";
            }
        } catch (IOException e) {
            cache.put(ACCESS_TOKEN, "");
            log.error("getAccessToken error",e);
        }
        cache.put(ACCESS_TOKEN, token);
        return token;
    }

}
