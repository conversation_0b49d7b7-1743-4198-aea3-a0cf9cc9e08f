package com.wunding.learn.trans.service.service.impl;

import com.wunding.learn.trans.service.config.SysConfig;
import com.wunding.learn.trans.service.service.ExtractText;
import jakarta.annotation.Resource;
import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.IOException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.extractor.POITextExtractor;
import org.apache.poi.hwpf.extractor.WordExtractor;
import org.apache.poi.poifs.filesystem.FileMagic;
import org.apache.poi.xwpf.extractor.XWPFWordExtractor;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component("wordExtractText")
public class WordExtractText implements ExtractText {

    @Resource
    private SysConfig sysConfig;

    @Override
    public String extractText(String filePath) throws IOException {

        File needTransFile = new File(sysConfig.getPhysicalPath(filePath));
        String text = null;
        try (FileInputStream fileInputStream = new FileInputStream(needTransFile);
            BufferedInputStream bufferedInputStream = new BufferedInputStream(fileInputStream)
        ) {
            POITextExtractor extractor = null;
            if (FileMagic.valueOf(bufferedInputStream) == FileMagic.OLE2) {
                extractor = new WordExtractor(bufferedInputStream);
                text = extractor.getText();
            } else if (FileMagic.valueOf(bufferedInputStream) == FileMagic.OOXML) {
                XWPFDocument doc = new XWPFDocument(bufferedInputStream);
                extractor = new XWPFWordExtractor(doc);
                text = extractor.getText();
            }
            IOUtils.close(extractor);
        } catch (IOException e) {
            log.error("param filePath: {} , error:", filePath, e);
        }

        // 文本为空不做处理
        if (StringUtils.isBlank(text)) {
            text = "";
        }

        String outTextFilePath = FilenameUtils.getPath(filePath) + FilenameUtils.getBaseName(filePath) + ".txt";
        File file = new File(sysConfig.getPhysicalPath(outTextFilePath));
        if (!file.exists()) {
            boolean newFileStatus = file.createNewFile();
            log.info("newFileStatus {}", newFileStatus);
        }

        IOUtils.write(text, new FileWriter(file));

        return outTextFilePath;
    }

}
