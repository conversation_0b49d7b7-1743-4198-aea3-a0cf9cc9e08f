package com.wunding.learn.trans.service.util;

import com.wunding.learn.common.constant.file.FileErrorNoEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.string.StringPool;
import com.wunding.learn.trans.service.config.SysConfig;
import com.wunding.learn.trans.service.dto.FileParseInfo;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.oro.text.regex.MalformedPatternException;
import org.apache.oro.text.regex.MatchResult;
import org.apache.oro.text.regex.Pattern;
import org.apache.oro.text.regex.PatternCompiler;
import org.apache.oro.text.regex.PatternMatcher;
import org.apache.oro.text.regex.Perl5Compiler;
import org.apache.oro.text.regex.Perl5Matcher;

/**
 * 提取视频码率、编码
 *
 * <AUTHOR> href="mailto:<EMAIL>">yangduanquan</a>
 */
@Slf4j
public class FileParseUtil {


    private FileParseUtil(){

    }
    private static final String FFMPEG_PATH;
    @SuppressWarnings("all")
    private static final String ffmpegPerformanceCpuNice;
    private static final String ffmpegPerformanceThreads;

    private static final List<String> HLS_TIME = List.of("-hls_time","3");

    private static final List<String> MAX_MUX_QUEUE_SIZE = List.of("-max_muxing_queue_size","1024");

    private static final List<String> LIB_X264 = List.of("-c:v","libx264");

    private static final List<String> MOV_FLAGS = List.of("-movflags","faststart");

    private static final List<String> IOS_PROFILE = List.of("-profile:v","high");

    private static final List<String> LEVEL = List.of("-level:v","4.1");

    private static final List<String> HLS_LIST_SIZE = List.of("-hls_list_size",""+Integer.MAX_VALUE);

    static {
        SysConfig sysConfig = SpringUtil.getBean(SysConfig.class);
        FFMPEG_PATH = sysConfig.getFfmpegPath();
        ffmpegPerformanceCpuNice = sysConfig.getFfmpegPerformanceCpuNice();
        ffmpegPerformanceThreads = sysConfig.getFfmpegPerformanceThreads();
    }

    /**
     * 提取视频、音频文件信息
     *
     * @param filePath 文件地址
     * @return {@link FileParseInfo}
     */
    public static FileParseInfo getFileParseInfoByFfmpeg(String filePath) {
        FileParseInfo info = new FileParseInfo();
        Map<String, String> stringStringMap = processFlv(filePath);

        //执行日志
        String processFlvResult = stringStringMap.get("processLog");

        // 处理视频返回的内容信息
        if (StringUtils.isNotBlank(processFlvResult)) {
            // 获取视频信息
            info = getVideoInfo(filePath, processFlvResult);
        }
        return info;
    }

    private static void setWidthHeight(FileParseInfo info) {
        if(StringUtils.isNotBlank(info.getResolutionRatio())){
            String[] xes = info.getResolutionRatio().split("x");
            if(xes.length>=2){
                try {
                    int width = Integer.parseInt(xes[0]);
                    info.setWidth(width);
                } catch (NumberFormatException e) {
                    log.error("视频宽获取失败");
                }
                try {
                    int height = Integer.parseInt(xes[1]);
                    info.setHeight(height);
                } catch (NumberFormatException e) {
                    log.error("视频高获取失败");
                }
            }
        }
    }


    private static double getFps(String processFlvResult) {

        PatternCompiler compiler = new Perl5Compiler();
        double fps = 0;
        try {
            String regex = "Stream (.*?): Video: (.*?), (.*?), (.*?), (.*?), (.*?), (.*?), (.*?), (.*?), (.*?)";
            Pattern pattern = compiler.compile(regex, Perl5Compiler.CASE_INSENSITIVE_MASK);
            PatternMatcher matcherDuration = new Perl5Matcher();
            if (matcherDuration.contains(processFlvResult, pattern)) {
                MatchResult re = matcherDuration.getMatch();
                fps = getFpsValue(re);
            }
        } catch (MalformedPatternException e) {
            log.error("获取分辨率失败", e);
        }
        return fps;
    }

    private static double getFpsValue(MatchResult re) {
        double fps = 0;
        for (int i = 0; i < re.groups(); i++) {
            String value = re.group(i).trim();
            if (value.contains(" ") && value.contains("fps")) {
                String[] s = value.split(" ");
                try {
                    if(StringUtils.isNumeric(StringUtils.replace(s[0],".",""))){
                        fps = Double.parseDouble(s[0].trim());
                    }
                } catch (NumberFormatException e) {
                    log.error("获取fps出错",e);
                }

            }
        }
        return fps;
    }

    private static String getResolutionRation(String processFlvResult) {

        PatternCompiler compiler = new Perl5Compiler();
        String resolutionRatio =  "";
        try {
            String regex = "Stream (.*?): Video: (.*?), (.*?), (.*?), (.*?), (.*?), (.*?), (.*?), (.*?), (.*?)";
            Pattern pattern = compiler.compile(regex, Perl5Compiler.CASE_INSENSITIVE_MASK);
            PatternMatcher matcherDuration = new Perl5Matcher();
            if (matcherDuration.contains(processFlvResult, pattern)) {
                MatchResult re = matcherDuration.getMatch();
                resolutionRatio = getResolutionRationValue(re);
            }
        } catch (MalformedPatternException e) {
            log.error("获取分辨率失败", e);
        }
        return resolutionRatio;
    }

    private static String getResolutionRationValue(MatchResult re) {
        String resolutionRatio = "";
        for (int i = 0; i < re.groups(); i++) {
            String value = re.group(i).trim();
            if (value.contains(" ")) {
                String[] s = value.split(" ");
                for (String item : s) {
                    if(item.contains("x") && !item.startsWith("0x") && !item.startsWith("#") && checkoutResolutionRatio(item)){
                        resolutionRatio = item;
                        log.info("分辨率:{}", item);
                    }
                }
            }else if (value.contains("x") && !value.startsWith("#") && checkoutResolutionRatio(value)) {
                resolutionRatio = value;
                log.info("分辨率:{}", value);
            }
            if(StringUtils.isNotBlank(resolutionRatio)){
                return resolutionRatio;
            }
        }
        return resolutionRatio;
    }

    private static boolean checkoutResolutionRatio(String resolutionRation){
        String[] xes = resolutionRation.split("x");
        try {
            Integer.parseInt(xes[0]);
            Integer.parseInt(xes[1]);
        } catch (NumberFormatException e) {
            return false;
        }
        return true;
    }

    private static FileParseInfo getVideoInfo(String filePath, String processFlvResult) {
        PatternCompiler compiler = new Perl5Compiler();
        FileParseInfo info = new FileParseInfo();
        try {
            String regexDuration = "Duration: (.*?), start: (.*?), bitrate: (\\d*) kb\\/s";
            Pattern patternDuration = compiler.compile(regexDuration, Perl5Compiler.CASE_INSENSITIVE_MASK);
            PatternMatcher matcherDuration = new Perl5Matcher();
            if (matcherDuration.contains(processFlvResult, patternDuration)) {
                MatchResult re = matcherDuration.getMatch();
                // 格式化时长，单位为秒
                long time = getTime(re.group(1));
                long sss = time % 60;
                long mi = time / 60;
                String formatTime = "(" + mi + "'" + sss + "\"" + ")";

                info.setDuration(time);
                info.setFormatDuration(formatTime);
                info.setStart(re.group(2));
                info.setBitrate(re.group(3));
                log.info("Duration: {}, FormatDuration：{}, start: {}, bitrate：{}", info.getDuration(),
                    info.getFormatDuration(), info.getStart(), info.getBitrate());
            } else if (filePath.endsWith(".3mv")) {
                // 暂无更好的方式解析.3mv格式文件，先规避获取时长失败时正则无法匹配的情况
                log.warn("can not get this video play time");
            } else {
                log.error("转码异常-视频资源已损坏-->{}", filePath);
                throw new BusinessException(FileErrorNoEnum.ERR_FILE_DATA_DESTROY);
            }
        } catch (MalformedPatternException e) {
            log.error("An error occurred when getFileParseInfoByFfmpeg", e);
        }
        String resolutionRatio = getResolutionRation(processFlvResult);
        if(StringUtils.isNotBlank(resolutionRatio)){
            info.setResolutionRatio(resolutionRatio);
        }
        setWidthHeight(info);
        info.setFps(getFps(processFlvResult));
        return info;
    }

    /**
     * 格式化时长，转换为‘秒’
     *
     * @param time 格式:"00:00:10.68"
     * @return 时长（秒）
     */
    private static long getTime(String time) {
        long min = 0;
        String minStr = "0";
        String[] timeStr = time.split(":");
        if (timeStr[0].compareTo(minStr) > 0) {
            min += Long.parseLong(timeStr[0]) * 60 * 60;
        }
        if (timeStr[1].compareTo(minStr) > 0) {
            min += Long.parseLong(timeStr[1]) * 60;
        }
        if (timeStr[2].compareTo(minStr) > 0) {
            min += (long) Math.floor(Float.parseFloat(timeStr[2]));
        }
        return min;
    }

    /**
     * ffmpeg能解析的格式：（asx，asf，mpg，wmv，3gp，mp4，mov，avi，flv等）
     */
    private static Map<String, String> processFlv(String inputPath) {

        Map<String, String> result = new HashMap<>(2);
        List<String> commend = new ArrayList<>();
        
        commend.add(FFMPEG_PATH);
        commend.add("-i");
        commend.add(inputPath);
        log.info("commend ======{}", commend);
        // 保存ffmpeg的输出结果流
        ProcessBuilder builder = new ProcessBuilder();
        builder.command(commend);
        builder.redirectErrorStream(true);
        Process p;
        try {
            p = builder.start();
        } catch (IOException e) {
            log.error("ProcessBuilder 执行失败 commend： {}", commend, e);
            return new HashMap<>(2);
        }
        try (BufferedReader buf = new BufferedReader(new InputStreamReader(p.getInputStream()))) {

            String line;
            StringBuilder sb = new StringBuilder();
            while ((line = buf.readLine()) != null) {
                sb.append(line);
            }
            // 这里线程阻塞，将等待外部转换进程运行成功运行结束后，才往下执行
            p.waitFor();
            result.put("processLog", sb.toString());
            return result;
        } catch (Exception e) {
            log.error("ProcessBuilder 执行失败 commend： {}", commend, e);
            Thread.currentThread().interrupt();
            return new HashMap<>(2);
        }
    }


    /**
     * 判断是否是宽屏视频
     */
    private static boolean isWidescreenVideo(String input,Map<String,Integer> resolutionInfo) {
        FileParseInfo fileParseInfoByFfmpeg = getFileParseInfoByFfmpeg(input);

        String resolutionRatio = fileParseInfoByFfmpeg.getResolutionRatio();
        if (null == resolutionRatio) {
            return true;
        }

        String[] split = resolutionRatio.split("x");
        if (split.length < 2) {
            return true;
        }

        String width = split[0];
        String height = split[1];
        BigDecimal heightDecimal = new BigDecimal(height);
        BigDecimal widthDecimal = new BigDecimal(width);
        resolutionInfo.put(StringPool.HEIGHT, heightDecimal.intValue());
        resolutionInfo.put(StringPool.WIDTH, widthDecimal.intValue());
        return 0 <= widthDecimal.compareTo(heightDecimal);
    }

    /**
     * 生成转码命令
     * @param input 原文件
     * @param bitrate 码率
     * @param scale 分辨率
     * @param isHls 是否是hls格式
     * @param output 转码后的输出文件
     * @return 转码命令
     */
    private static List<String> convertVideoCmd(String input,int bitrate,int scale,boolean isHls,String output){
        List<String> commend = new ArrayList<>();
        
        commend.add(FFMPEG_PATH);

        commend.add("-i");
        commend.add(input);
        commend.add("-threads");
        commend.add(StringUtils.defaultIfBlank(ffmpegPerformanceThreads, "2"));
        // 视频格式转换时，报错Too many packets buffered for output stream
        //原因是有些视频数据有问题，导致视频处理过快，容器封装时队列溢出，解决办法是增加容器封装队列大小，比如在命令之后增加一个参数
        commend.addAll(MAX_MUX_QUEUE_SIZE);

        //windows下转码会报错  Unknown encoder 'lib x264' 因为缺少对应的类库
        commend.addAll(LIB_X264);

        commend.add("-b:v");
        commend.add(getBitrate(input,bitrate));
        commend.addAll(fpsParams(input));
        commend.add("-vf");
        // 判断视频长宽比例
        Map<String, Integer> resolutionInfo = new HashMap<>();
        if (isWidescreenVideo(input,resolutionInfo)) {
            //宽视频
            commend.add("scale=-2:"+scale+",format=yuv420p");
            if(scale!=480 && resolutionInfo.get(StringPool.WIDTH) < scale){
                log.warn("视频分辨率低于{},宽视频,width:{},height:{}",scale,resolutionInfo.get(StringPool.WIDTH),resolutionInfo.get(StringPool.HEIGHT));
                return Collections.emptyList();
            }
        } else {
            //长视频
            commend.add("scale="+scale+":-2,format=yuv420p");
            if(scale!=480 && resolutionInfo.get(StringPool.HEIGHT) < scale){
                log.warn("视频分辨率低于{},长视频,width:{},height:{}",scale,resolutionInfo.get(StringPool.WIDTH),resolutionInfo.get(StringPool.HEIGHT));
                return Collections.emptyList();
            }
        }

        if(isHls){
            // 输出文件格式 hls
            commend.add("-f");
            commend.add("hls");
            // 设置播放列表保存的最多条目
            commend.addAll(HLS_LIST_SIZE);
            // 切片长度（以秒为单位）
            commend.addAll(HLS_TIME);
        }
        commend.add("-c:a");
        commend.add("aac");
        commend.add("-b:a");
        commend.add("128k");
        commend.addAll(MOV_FLAGS);

        //增加参数兼容部分视频在ios设备播放
        commend.addAll(IOS_PROFILE);
        commend.addAll(LEVEL);
        commend.add("-ac");
        commend.add("2");
        commend.add(output);

        return commend;
    }

    /**
     * 视频转码 视频480p h264 码率 768k pbs 音频 aac 码率 128kbps
     *
     * @param input  文件输入地址
     * @param output 文件输出地址
     * @throws Exception 异常信息
     */
    public static void convertVideoToMp4(String input, String output) throws Exception {

        List<String> commend = convertVideoCmd(input,768,480,false,output);
        if(commend.isEmpty()){
            return;
        }
        log.info(" convertVideoToMp4 ---执行命令---:{}", commend);
        ProcessBuilder builder = new ProcessBuilder(commend);
        Process process = builder.start();
        try (
            InputStream errorStream = process.getErrorStream();
            InputStreamReader isr = new InputStreamReader(errorStream);
            BufferedReader br = new BufferedReader(isr)
        ) {
            String line;
            while ((line = br.readLine()) != null) {
                log.info(line);
            }
            process.waitFor();
        } catch (Exception e) {
            log.error("convertVideoToMp4 error",e);
            throw e;
        }
    }

    private static List<String> fpsParams(String input){
        FileParseInfo fileParseInfo = getFileParseInfoByFfmpeg(input);
        if(fileParseInfo.getFps()>25){
            return List.of("-r", "25");
        }
        return new ArrayList<>();
    }

    private static String getBitrate(String input,Integer maxBitrate){
        FileParseInfo fileParseInfo = getFileParseInfoByFfmpeg(input);
        int bitrate;
        try {
            bitrate = Integer.parseInt(fileParseInfo.getBitrate());
        } catch (NumberFormatException e) {
            log.warn("获取码率失败");
            return maxBitrate + "k";
        }
        if(bitrate < maxBitrate){
            return bitrate + "k";
        }else {
            return maxBitrate + "k";
        }
    }

    /**
     * 视频转码 视频720p h264 码率 5000k pbs 音频 aac 码率 128kbps
     *
     * @param input  文件输入地址
     * @param output 文件输出地址
     * @throws Exception 异常信息
     */
    public static void convertVideoToMp4Of720p(String input, String output) throws Exception {

        List<String> commend = convertVideoCmd(input,5000,720,false,output);
        if(commend.isEmpty()){
            return;
        }
        log.info("convertVideoToMp4Of720p ---执行命令---:{}", commend);
        ProcessBuilder builder = new ProcessBuilder(commend);
        Process process = builder.start();
        try (
            InputStream errorStream = process.getErrorStream();
            InputStreamReader isr = new InputStreamReader(errorStream);
            BufferedReader br = new BufferedReader(isr)
        ) {
            String line;
            while ((line = br.readLine()) != null) {
                log.info(line);
            }
            process.waitFor();
        } catch (Exception e) {
            log.error("convertVideoToMp4Of720p error",e);
            throw e;
        }
    }

    /**
     * 视频转码 视频1080p h264 码率 20000k pbs 音频 aac 码率 128kbps
     *
     * @param input  文件输入地址
     * @param output 文件输出地址
     * @throws Exception 异常信息
     */
    public static void convertVideoToMp4Of1080p(String input, String output) throws Exception {

        List<String> commend = convertVideoCmd(input,20000,1080,false,output);
        if(commend.isEmpty()){
            return;
        }
        log.info("convertVideoToMp4Of1080p ---执行命令---:{}", commend);
        ProcessBuilder builder = new ProcessBuilder(commend);
        Process process = builder.start();
        try (
            InputStream errorStream = process.getErrorStream();
            InputStreamReader isr = new InputStreamReader(errorStream);
            BufferedReader br = new BufferedReader(isr)
        ) {
            String line;
            while ((line = br.readLine()) != null) {
                log.info(line);
            }
            process.waitFor();
        } catch (Exception e) {
            log.error("convertVideoToMp4Of1080p error",e);
            throw e;
        }
    }

    /**
     * 视频转码 视频480p h264 码率 768k pbs 音频 aac 码率 128kbps
     *
     * @param input  文件输入地址
     * @param output 文件输出地址
     * @throws Exception 异常信息
     */
    public static void convertVideoToM3u8(String input, String output) throws Exception {
        List<String> commend = convertVideoCmd(input,768,480,true,output);
        if(commend.isEmpty()){
            return;
        }
        log.info("convertVideoToM3u8 ---执行命令---:{}", commend);
        ProcessBuilder builder = new ProcessBuilder(commend);
        Process process = builder.start();
        try (
            InputStream errorStream = process.getErrorStream();
            InputStreamReader isr = new InputStreamReader(errorStream);
            BufferedReader br = new BufferedReader(isr)
        ) {
            String line;
            while ((line = br.readLine()) != null) {
                log.info(line);
            }
            process.waitFor();
        } catch (Exception e) {
            log.error("convertVideoToM3u8 error",e);
            throw e;
        }
    }

    /**
     * 视频转码 视频720p h264 码率 5000k pbs 音频 aac 码率 128kbps
     *
     * @param input  文件输入地址
     * @param output 文件输出地址
     * @throws Exception 异常信息
     */
    public static void convertVideoToM3u8Of720p(String input, String output) throws Exception {
        List<String> commend = convertVideoCmd(input,5000,720,true,output);
        if(commend.isEmpty()){
            return;
        }
        log.info("convertVideoToM3u8Of720p ---执行命令---:{}",commend);
        ProcessBuilder builder = new ProcessBuilder(commend);
        Process process = builder.start();
        try (
            InputStream errorStream = process.getErrorStream();
            InputStreamReader isr = new InputStreamReader(errorStream);
            BufferedReader br = new BufferedReader(isr)
        ) {
            String line;
            while ((line = br.readLine()) != null) {
                log.info(line);
            }
            process.waitFor();
        } catch (Exception e) {
            log.error("convertVideoToM3u8Of720p error",e);
            throw e;
        }
    }

    /**
     * 视频转码 视频1080p h264 码率 20000k pbs 音频 aac 码率 128kbps
     *
     * @param input  文件输入地址
     * @param output 文件输出地址
     * @throws Exception 异常信息
     */
    public static void convertVideoToM3u8Of1080p(String input, String output) throws Exception {
        List<String> commend = convertVideoCmd(input,20000,1080,true,output);
        if(commend.isEmpty()){
            return;
        }
        log.info("convertVideoToM3u8Of1080p ---执行命令---:{}", commend);

        ProcessBuilder builder = new ProcessBuilder(commend);
        Process process = builder.start();
        try (
            InputStream errorStream = process.getErrorStream();
            InputStreamReader isr = new InputStreamReader(errorStream);
            BufferedReader br = new BufferedReader(isr)
        ) {
            String line;
            while ((line = br.readLine()) != null) {
                log.info(line);
            }
            process.waitFor();
        } catch (Exception e) {
            log.error("convertVideoToM3u8Of1080p error",e);
            throw e;
        }
    }

    /**
     * 单声道 16000 采样率 16bits编码 的mp3文件
     *
     * @param input  文件输入地址
     * @param output 文件输出地址
     * @throws Exception 异常信息
     */
    public static void convertMp4ToMp3(String input, String output) throws Exception {
        List<String> commend = new ArrayList<>();
        
        commend.add(FFMPEG_PATH);
        commend.add("-i");
        commend.add(input);


        commend.add("-vn");
        commend.add("-ac");
        commend.add("1");

        commend.add("-ar");
        commend.add("16000");
        commend.add("-q:a");
        commend.add("0");
        commend.add(output);
        log.info("convertMp4ToMp3 ---执行命令---:{}", commend);

        ProcessBuilder builder = new ProcessBuilder(commend);
        Process process = builder.start();
        try (
            InputStream errorStream = process.getErrorStream();
            InputStreamReader isr = new InputStreamReader(errorStream);
            BufferedReader br = new BufferedReader(isr)
        ) {
            String line;
            while ((line = br.readLine()) != null) {
                log.info(line);
            }
            process.waitFor();
        } catch (Exception e) {
            log.error("convertMp4ToMp3 error",e);
            throw e;
        }
    }

    /**
     * 将音频转换为单声道 16000 采样率 16bits编码 的mp3文件
     *
     * @param input  输入
     * @param output 输出
     */
    public static void convertAudioToMp3(String input, String output) throws Exception {
        convertMp4ToMp3(input, output);
    }
}
