package com.wunding.learn.trans.service.service.impl;

import com.wunding.learn.common.constant.trans.TransErrorNumEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.redis.util.RedisLockUtil;
import com.wunding.learn.common.util.string.StringPool;
import com.wunding.learn.file.trans.grpc.lib.TransRequest;
import com.wunding.learn.file.trans.grpc.lib.VideoClarity;
import com.wunding.learn.trans.service.config.SysConfig;
import com.wunding.learn.trans.service.dto.FileParseInfo;
import com.wunding.learn.trans.service.util.FileParseUtil;
import com.wunding.learn.trans.service.util.FileUtil;
import jakarta.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> href="mailto:<EMAIL>">yuxueyi</a>
 * @version V7.0.0
 * @since 2022/5/26  10:35
 */
@Slf4j
@Service
public class VideoTransServiceImpl {

    private static final String M3U8_PATH = StringPool.SLASH+"m3u8"+StringPool.SLASH;

    private static final String M3U8_FILE_SUFFIX = ".m3u8";

    @Resource
    private SysConfig sysConfig;


    /**
     * 视频转码处理
     *
     * @throws Exception 异常信息
     */
    public List<VideoClarity> doTrans(TransRequest transRequest) throws Exception {

        String needTransVideoFileCurrentPath = transRequest.getFilePath();
        Integer videoFormatType = transRequest.getVideoFormat();

        // 原文件
        File needTransVideoFile = new File(sysConfig.getPhysicalPath(transRequest.getTempFilePath()));

        File needTransCurrentFile = new File(sysConfig.getPhysicalPath(needTransVideoFileCurrentPath));
        if(!needTransCurrentFile.exists()){
            try {
                FileUtils.copyFile(needTransVideoFile, needTransCurrentFile);
            } catch (IOException e) {
                log.error("pdf copy error", e);
            }
        }

        String key = "trans:file:"+transRequest.getFilePath();
        //视频文件3个小时等待时间，超过3个小时，则认为转码失败
        if (RedisLockUtil.acquire(key, 3L * 60 * 60, 3L * 60 * 60)) {
            try {
                return transVideo(transRequest, needTransVideoFile, needTransVideoFileCurrentPath, videoFormatType);
            }finally {
                RedisLockUtil.release(key);
            }
        }else{
            log.error("视频转码失败，等待超时,transRequest.FilePath={}, transRequest.Id={}, transRequest.BucketName={}, transRequest.TempFilePath={}",
                transRequest.getFilePath(), transRequest.getId(), transRequest.getBucketName(), transRequest.getTempFilePath());
            throw new BusinessException(TransErrorNumEnum.WAITING_FOR_TIMEOUT);
        }

    }

    private List<VideoClarity> transVideo(TransRequest transRequest, File needTransVideoFile, String needTransVideoFileCurrentPath,
        Integer videoFormatType) throws Exception {
        List<VideoClarity> result = new ArrayList<>();
        FileParseInfo fileParseInfo = FileParseUtil.getFileParseInfoByFfmpeg(needTransVideoFile.getAbsolutePath());
        String mp4480Path = mp4480(needTransVideoFile, needTransVideoFileCurrentPath, videoFormatType, transRequest.getEnable480());
        String m3u8480Path = m3u8480(needTransVideoFile, needTransVideoFileCurrentPath, videoFormatType, transRequest.getEnable480());

        String mp4720Path = "";
        String m3u8720Path = "";
        String mp41080Path = "";
        String m3u81080Path = "";
        if (checkWidthHeight(fileParseInfo,720)) {
            mp4720Path = mp4720(needTransVideoFile, needTransVideoFileCurrentPath, videoFormatType, transRequest.getEnable720());
            m3u8720Path = m3u8720(needTransVideoFile, needTransVideoFileCurrentPath, videoFormatType, transRequest.getEnable720());

        }
        if(checkWidthHeight(fileParseInfo,1080)){
            mp41080Path = mp41080(needTransVideoFile, needTransVideoFileCurrentPath, videoFormatType, transRequest.getEnable1080());
            m3u81080Path = m3u81080(needTransVideoFile, needTransVideoFileCurrentPath, videoFormatType, transRequest.getEnable1080());
        }

        log.info(
            "mp4480Path:{},mp4720Path:{},mp41080Path:{},m3u8480Path:{},m3u8720Path:{},m3u81080Path:{}",
            mp4480Path,
            mp4720Path,
            mp41080Path,
            m3u8480Path,
            m3u8720Path,
            m3u81080Path
        );

        if(StringUtils.isNotBlank(mp4480Path)){
            result.add(VideoClarity.newBuilder().setUrl(mp4480Path).setName("480").build());
        }else if(StringUtils.isNotBlank(m3u8480Path)){
            result.add(VideoClarity.newBuilder().setUrl(m3u8480Path).setName("480").build());
        }
        if(StringUtils.isNotBlank(mp4720Path)){
            result.add(VideoClarity.newBuilder().setUrl(mp4720Path).setName("720").build());
        }else if(StringUtils.isNotBlank(m3u8720Path)){
            result.add(VideoClarity.newBuilder().setUrl(m3u8720Path).setName("720").build());
        }
        if(StringUtils.isNotBlank(mp41080Path)){
            result.add(VideoClarity.newBuilder().setUrl(mp41080Path).setName("1080").build());
        }else if(StringUtils.isNotBlank(m3u81080Path)){
            result.add(VideoClarity.newBuilder().setUrl(m3u81080Path).setName("1080").build());
        }

        if(StringUtils.isNotBlank(transRequest.getTempFilePath())){
            String outMp3Path = FilenameUtils.getPath(needTransVideoFileCurrentPath) + FilenameUtils.getBaseName(
                needTransVideoFileCurrentPath) + ".mp3";
            if(!new File(sysConfig.getPhysicalPath(outMp3Path)).exists()){
                FileParseUtil.convertMp4ToMp3(sysConfig.getPhysicalPath(transRequest.getTempFilePath()), sysConfig.getPhysicalPath(outMp3Path));
            }
        }
        return result;
    }

    private boolean checkWidthHeight(FileParseInfo fileParseInfo,Integer value){
        return fileParseInfo.getWidth() > value || fileParseInfo.getHeight() > value;
    }

    private String mp4480(File needTransVideoFile,String needTransVideoFileCurrentPath,Integer isToMp4Trans,Integer enable) throws Exception {
        if(!Objects.equals(isToMp4Trans,0)){
            return "";
        }
        if(!Objects.equals(enable,1)){
            return "";
        }
        // 转码后文件路径
        String newFileName = needTransVideoFile.getName().substring(0, needTransVideoFile.getName().lastIndexOf("."))
            .concat(".mp4");
        String relativePath = needTransVideoFileCurrentPath.substring(0, needTransVideoFileCurrentPath.lastIndexOf("/"))
            .concat(StringPool.SLASH + "480" + StringPool.SLASH);

        FileUtil.mkdir(sysConfig.getPhysicalPath(relativePath));
        String targetFilePath = relativePath.concat(newFileName);
        String targetAllPath = sysConfig.getPhysicalPath(targetFilePath);
        File targetFile = new File(targetAllPath);

        // 防止重复转码
        if (!targetFile.exists()) {
            long time1 = System.currentTimeMillis();
            FileParseUtil.convertVideoToMp4(needTransVideoFile.getAbsolutePath(), targetFile.getAbsolutePath());
            long time2 = System.currentTimeMillis();
            log.info("480 mp4 time:{}",time2-time1);
        }
        return relativePath+newFileName;
    }

    private String mp4720(File needTransVideoFile,String needTransVideoFileCurrentPath,Integer isToMp4Trans,Integer enable) throws Exception {
        if(!Objects.equals(isToMp4Trans,0)){
            return "";
        }
        if(!Objects.equals(enable,1)){
            return "";
        }
        // 转码后文件路径
        String newFileName = needTransVideoFile.getName().substring(0, needTransVideoFile.getName().lastIndexOf("."))
            .concat(".mp4");
        String relativePath720 = needTransVideoFileCurrentPath.substring(0, needTransVideoFileCurrentPath.lastIndexOf(StringPool.SLASH))
            .concat(StringPool.SLASH + "720" + StringPool.SLASH);
        FileUtil.mkdir(sysConfig.getPhysicalPath(relativePath720));
        String targetFilePath720p = relativePath720.concat(newFileName);
        FileUtil.mkdir(targetFilePath720p);
        String targetAllPath720p = sysConfig.getPhysicalPath(targetFilePath720p);
        File targetFile720p = new File(targetAllPath720p);

        if(!targetFile720p.exists()){
            long time3 = System.currentTimeMillis();
            FileParseUtil.convertVideoToMp4Of720p(needTransVideoFile.getAbsolutePath(), targetFile720p.getAbsolutePath());
            long time4 = System.currentTimeMillis();
            log.info("720 mp4 time:{}",time4-time3);
        }
        return relativePath720+newFileName;
    }

    private String mp41080(File needTransVideoFile,String needTransVideoFileCurrentPath,Integer isToMp4Trans,Integer enable) throws Exception {
        if(!Objects.equals(isToMp4Trans,0)){
            return "";
        }
        if(!Objects.equals(enable,1)){
            return "";
        }
        // 转码后文件路径
        String newFileName = needTransVideoFile.getName().substring(0, needTransVideoFile.getName().lastIndexOf("."))
            .concat(".mp4");
        String relativePath1080 = needTransVideoFileCurrentPath.substring(0, needTransVideoFileCurrentPath.lastIndexOf(StringPool.SLASH))
            .concat(StringPool.SLASH + "1080" + StringPool.SLASH);
        FileUtil.mkdir(sysConfig.getPhysicalPath(relativePath1080));
        String targetFilePath1080p = relativePath1080.concat(newFileName);
        String targetAllPath1080p = sysConfig.getPhysicalPath(targetFilePath1080p);
        File targetFile1080p = new File(targetAllPath1080p);

        if(!targetFile1080p.exists()){
            long time5 = System.currentTimeMillis();
            FileParseUtil.convertVideoToMp4Of1080p(needTransVideoFile.getAbsolutePath(), targetFile1080p.getAbsolutePath());
            long time6 = System.currentTimeMillis();
            log.info("1080 mp4 time:{}",time6-time5);
        }
        return relativePath1080+newFileName;
    }

    private String m3u8480(File needTransVideoFile,String needTransVideoFileCurrentPath,Integer isToM3u8Trans,Integer enable) throws Exception {
        if(!Objects.equals(isToM3u8Trans,1)){
            return "";
        }
        if(!Objects.equals(enable,1)){
            return "";
        }
        String newFileNameM3u8 = needTransVideoFile.getName().substring(0, needTransVideoFile.getName().lastIndexOf("."))
            .concat(M3U8_FILE_SUFFIX);
        String relativePathM3u8 = needTransVideoFileCurrentPath.substring(0, needTransVideoFileCurrentPath.lastIndexOf(StringPool.SLASH))
            .concat(StringPool.SLASH + "480" + M3U8_PATH+ FilenameUtils.getBaseName(needTransVideoFile.getName())+StringPool.SLASH);

        FileUtil.mkdir(sysConfig.getPhysicalPath(relativePathM3u8));
        String targetFilePathM3u8 = relativePathM3u8.concat(newFileNameM3u8);
        String targetAllPathM3u8 = sysConfig.getPhysicalPath(targetFilePathM3u8);
        File targetFileM3u8 = new File(targetAllPathM3u8);

        if (!targetFileM3u8.exists()) {
            long time1 = System.currentTimeMillis();
            FileParseUtil.convertVideoToM3u8(needTransVideoFile.getAbsolutePath(), targetFileM3u8.getAbsolutePath());
            long time2 = System.currentTimeMillis();
            log.info("480 m3u8 time:{}",time2-time1);
        }
        return relativePathM3u8+newFileNameM3u8;
    }

    private String m3u8720(File needTransVideoFile,String needTransVideoFileCurrentPath,Integer isToM3u8Trans,Integer enable) throws Exception {
        if(!Objects.equals(isToM3u8Trans,1)){
            return "";
        }
        if(!Objects.equals(enable,1)){
            return "";
        }
        String newFileNameM3u8 = needTransVideoFile.getName().substring(0, needTransVideoFile.getName().lastIndexOf("."))
            .concat(M3U8_FILE_SUFFIX);
        String relativePath720M3u8 = needTransVideoFileCurrentPath.substring(0, needTransVideoFileCurrentPath.lastIndexOf(StringPool.SLASH))
            .concat(StringPool.SLASH + "720" + M3U8_PATH+ FilenameUtils.getBaseName(needTransVideoFile.getName())+StringPool.SLASH);
        FileUtil.mkdir(sysConfig.getPhysicalPath(relativePath720M3u8));
        String targetFilePath720pM3u8 = relativePath720M3u8.concat(newFileNameM3u8);
        String targetAllPath720pM3u8 = sysConfig.getPhysicalPath(targetFilePath720pM3u8);
        File targetFile720pM3u8 = new File(targetAllPath720pM3u8);

        if(!targetFile720pM3u8.exists()){
            long time3 = System.currentTimeMillis();
            FileParseUtil.convertVideoToM3u8Of720p(needTransVideoFile.getAbsolutePath(), targetFile720pM3u8.getAbsolutePath());
            long time4 = System.currentTimeMillis();
            log.info("720 m3u8 time:{}",time4-time3);
        }
        return relativePath720M3u8+newFileNameM3u8;
    }

    private String m3u81080(File needTransVideoFile,String needTransVideoFileCurrentPath,Integer isToM3u8Trans,Integer enable) throws Exception {
        if(!Objects.equals(isToM3u8Trans,1)){
            return "";
        }
        if(!Objects.equals(enable,1)){
            return "";
        }
        String newFileNameM3u8 = needTransVideoFile.getName().substring(0, needTransVideoFile.getName().lastIndexOf("."))
            .concat(M3U8_FILE_SUFFIX);
        String relativePath1080M3u8 = needTransVideoFileCurrentPath.substring(0, needTransVideoFileCurrentPath.lastIndexOf(StringPool.SLASH))
            .concat(StringPool.SLASH + "1080" + M3U8_PATH+ FilenameUtils.getBaseName(needTransVideoFile.getName())+StringPool.SLASH);
        FileUtil.mkdir(sysConfig.getPhysicalPath(relativePath1080M3u8));
        String targetFilePath1080pM3u8 = relativePath1080M3u8.concat(newFileNameM3u8);
        String targetAllPath1080pM3u8 = sysConfig.getPhysicalPath(targetFilePath1080pM3u8);
        File targetFile1080pM3u8 = new File(targetAllPath1080pM3u8);

        if(!targetFile1080pM3u8.exists()){
            long time5 = System.currentTimeMillis();
            FileParseUtil.convertVideoToM3u8Of1080p(needTransVideoFile.getAbsolutePath(), targetFile1080pM3u8.getAbsolutePath());
            long time6 = System.currentTimeMillis();
            log.info("1080 m3u8 time:{}",time6-time5);
        }
        return relativePath1080M3u8+newFileNameM3u8;
    }


}
