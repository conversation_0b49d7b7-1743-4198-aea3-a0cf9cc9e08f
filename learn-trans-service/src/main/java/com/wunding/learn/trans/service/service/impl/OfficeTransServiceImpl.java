package com.wunding.learn.trans.service.service.impl;

import com.wunding.learn.common.constant.trans.TransErrorNumEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.util.string.StringPool;
import com.wunding.learn.file.trans.grpc.lib.TransRequest;
import com.wunding.learn.trans.service.config.Pdf2htmlConfig;
import com.wunding.learn.trans.service.config.SysConfig;
import com.wunding.learn.trans.service.core.CommandExec;
import com.wunding.learn.trans.service.util.CmdUtil;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> href="mailto:<EMAIL>">yuxueyi</a>
 * @version V7.0.0
 * @since 2022/5/25  14:13
 */
@Service
@Slf4j
public class OfficeTransServiceImpl {

    @Resource
    private SysConfig sysConfig;

    @Resource
    private CommandExec commandExec;

    @Resource
    private PdfTransServiceImpl pdfTransServiceImpl;

    @Resource
    private Pdf2htmlConfig pdf2htmlConfig;

    /**
     * office 先转为pdf，再将pdf转为html
     */
    public String doTrans(TransRequest transRequest) {

        String needTransFilePath = transRequest.getTempFilePath();
        //数据库里正式文件路径
        String filePath = transRequest.getFilePath();

        // 原文件 取 消息 发过来文件路径，有可能是临时文件
        File needTransFile = new File(sysConfig.getPhysicalPath(needTransFilePath));

        File needTransCurrentFile = new File(sysConfig.getPhysicalPath(filePath));
        if(!needTransCurrentFile.exists()){
            try {
                FileUtils.copyFile(needTransFile, needTransCurrentFile);
            } catch (IOException e) {
                log.error("pdf copy error", e);
            }
        }

        //转码之后的文件输出路径 通过正式路径生成转码后的输出目录
        String outDir = sysConfig.getPhysicalPath(filePath);
        outDir = outDir.substring(0, outDir.lastIndexOf("."));
        String officeTransCmd = CmdUtil.buildOfficeTransCmd("soffice", outDir, needTransFile.getAbsolutePath());
        int timeout = pdf2htmlConfig.getOfficeTimeout() == null ? 600 : pdf2htmlConfig.getOfficeTimeout();

        /*
        //解决soffice bug
        // failed: 0x11b(Error Area:Io Class:Abort Code:27) at ./sfx2/source/doc/sfxbasemodel.cxx:3207 at ./sfx2/source/doc/sfxbasemodel.cxx:1783)
        // 提前创建文件
        */
        boolean mkdirSuccess = new File(outDir).mkdirs();
        if(!mkdirSuccess){
            log.warn("{}目录创建失败", outDir);
        }
        File file = new File(outDir + StringPool.SLASH + FilenameUtils.getBaseName(needTransFilePath) + ".pdf");
        if (!file.exists()) {
            try {
                boolean newFileSuccess = file.createNewFile();
                if(!newFileSuccess){
                    log.warn("{}文件创建失败", file.getAbsoluteFile());
                }
            } catch (IOException e) {
                log.error("file.createNewFile() error :", e);
            }
        }

        // 日志
        int execResult = commandExec.exec(officeTransCmd, timeout, log::info);
        if (execResult != 0) {
            //转码失败
            log.error("转码失败,FilePath:{},BucketName:{},commandExec fail execResult is [{}] and officeTransCmd :[{}]",
                transRequest.getFilePath(), transRequest.getBucketName(),
                execResult, officeTransCmd);
            throw new BusinessException(TransErrorNumEnum.EXECUTE_TRANS_FAILED);
        }

        //office 文件转pdf成功
        String pdfPath = outDir + outDir.substring(outDir.lastIndexOf(StringPool.SLASH)) + ".pdf";
        try {
            File outPdfFile = new File(pdfPath);
            String targetPath = outPdfFile.getParent() + "/index.pdf";
            File targetFile = new File(targetPath);
            if (targetFile.exists()) {
                Files.delete(targetFile.toPath());
            }
            FileUtils.moveFile(outPdfFile, targetFile);
            pdfPath = targetPath;
        } catch (IOException e) {
            log.error("moveFile fail :" + pdfPath, e);
            throw new BusinessException(TransErrorNumEnum.PDF_FILE_COPY_FAILED);
        }

        log.info("pdfPath: [{}]", pdfPath);

        //office转码后的pdf文件地址
        String indexPdfPath = "";
        pdfPath = FilenameUtils.separatorsToUnix(pdfPath);
        if (pdfPath.startsWith(sysConfig.getLocation())) {
            indexPdfPath = pdfPath.replace(sysConfig.getLocation(), "");
        }
        log.info("indexPdfPath:" + indexPdfPath);

        TransRequest request = TransRequest.newBuilder(transRequest)
            .setFilePath(indexPdfPath)
            .setTempFilePath(indexPdfPath)
            .build();
        return pdfTransServiceImpl.doTrans(request);
    }

    public String transWaterMarkImage(String tempDir, String sourcePath, String newImageName) throws IOException {

        String[] officeTransCmd = CmdUtil.transDocxToImageCmd("soffice", sysConfig.getPhysicalPath(tempDir),
            sourcePath);
        String waterMarkImgPath = tempDir + newImageName + ".jpg";
        String physicalPath = sysConfig.getPhysicalPath(waterMarkImgPath);
        File outDir = new File(sysConfig.getPhysicalPath(tempDir));
        if (!outDir.exists()) {
            boolean mkdir = outDir.mkdirs();
            log.info("transWaterMarkImage mkdir :{}",mkdir);
        }
        log.info("savePath length:{}", new File(sourcePath).length());
        File jpgFile = new File(physicalPath);
        if (!jpgFile.exists()) {
            // 线上nas性能不够，提交创建文件，转码时覆盖文件
            boolean newFile = jpgFile.createNewFile();
            log.info("transWaterMarkImage createNewFile :{}",newFile);
        }

        // 日志
        int execResult = commandExec.exec(officeTransCmd, 60, log::info);
        if (execResult != 0) {
            //转码失败
            log.error("transWaterMarkImage commandExec fail execResult is [{}] and officeTransCmd :[{}]", execResult, officeTransCmd);
            throw new BusinessException(TransErrorNumEnum.EXECUTE_TRANS_FAILED);
        }
        return waterMarkImgPath;
    }



}
