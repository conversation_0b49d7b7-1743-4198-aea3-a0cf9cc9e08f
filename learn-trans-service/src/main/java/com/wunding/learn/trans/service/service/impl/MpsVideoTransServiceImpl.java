package com.wunding.learn.trans.service.service.impl;

import com.wunding.learn.common.constant.trans.TransErrorNumEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.redis.util.RedisLockUtil;
import com.wunding.learn.common.util.string.StringPool;
import com.wunding.learn.file.trans.grpc.lib.TransRequest;
import com.wunding.learn.file.trans.grpc.lib.VideoClarity;
import com.wunding.learn.trans.service.config.SysConfig;
import com.wunding.learn.trans.service.dto.FileParseInfo;
import com.wunding.learn.trans.service.util.FileParseUtil;
import com.wunding.learn.trans.service.util.FileUtil;
import com.wunding.learn.trans.service.util.MpsUtil;
import jakarta.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> href="mailto:<EMAIL>">yuxueyi</a>
 * @version V7.0.0
 * @since 2022/5/26  10:35
 */
@Slf4j
@Service
public class MpsVideoTransServiceImpl {

    @Resource
    private SysConfig sysConfig;


    /**
     * 视频转码处理
     *
     * @throws Exception 异常信息
     */
    public List<VideoClarity> doTrans(TransRequest transRequest) throws Exception {

        String needTransVideoFileCurrentPath = transRequest.getFilePath();
        Integer videoFormatType = transRequest.getVideoFormat();

        // 原文件
        File needTransVideoFile = new File(sysConfig.getPhysicalPath(transRequest.getTempFilePath()));

        File needTransCurrentFile = new File(sysConfig.getPhysicalPath(needTransVideoFileCurrentPath));
        if(!needTransCurrentFile.exists()){
            try {
                FileUtils.copyFile(needTransVideoFile, needTransCurrentFile);
            } catch (IOException e) {
                log.error("MPS转码失败 copy error", e);
            }
        }

        return transVideo(transRequest, needTransVideoFile, needTransVideoFileCurrentPath, videoFormatType);


    }

    private List<VideoClarity> transVideo(TransRequest transRequest, File needTransVideoFile, String needTransVideoFileCurrentPath,
        Integer videoFormatType) throws Exception {

        String inputObject = transRequest.getTempFilePath();
        if (inputObject.startsWith(StringPool.SLASH)) {
            inputObject = inputObject.substring(1);
        }
        if (inputObject.startsWith(transRequest.getBucketName())) {
            inputObject = inputObject.substring(transRequest.getBucketName().length() + 1);
        }

        String outputObject = transRequest.getFilePath();
        if (outputObject.startsWith(StringPool.SLASH)) {
            outputObject = outputObject.substring(1);
        }
        if (outputObject.startsWith(transRequest.getBucketName())) {
            outputObject = outputObject.substring(transRequest.getBucketName().length() + 1);
        }

        List < VideoClarity > result = new ArrayList<>();
        FileParseInfo fileParseInfo = FileParseUtil.getFileParseInfoByFfmpeg(needTransVideoFile.getAbsolutePath());
        String mp4480Path = mp4(transRequest, inputObject, outputObject, videoFormatType,
            transRequest.getEnable480(),"480");
        String m3u8480Path = m3u8(transRequest, inputObject, outputObject, videoFormatType, transRequest.getEnable480(),"480");

        String mp4720Path = "";
        String m3u8720Path = "";
        String mp41080Path = "";
        String m3u81080Path = "";
        if (checkWidthHeight(fileParseInfo,720)) {
            mp4720Path = mp4(transRequest, inputObject, outputObject, videoFormatType, transRequest.getEnable720(),"720");
            m3u8720Path = m3u8(transRequest, inputObject, outputObject, videoFormatType, transRequest.getEnable720(),"720");

        }
        if(checkWidthHeight(fileParseInfo,1080)){
            mp41080Path = mp4(transRequest, inputObject, outputObject, videoFormatType, transRequest.getEnable1080(),"1080");
            m3u81080Path = m3u8(transRequest, inputObject, outputObject, videoFormatType, transRequest.getEnable1080(),"1080");
        }

        log.info(
            "mp4480Path:{},mp4720Path:{},mp41080Path:{},m3u8480Path:{},m3u8720Path:{},m3u81080Path:{}",
            mp4480Path,
            mp4720Path,
            mp41080Path,
            m3u8480Path,
            m3u8720Path,
            m3u81080Path
        );

        if(StringUtils.isNotBlank(mp4480Path)){
            result.add(VideoClarity.newBuilder().setUrl(mp4480Path).setName("480").build());
        }else if(StringUtils.isNotBlank(m3u8480Path)){
            result.add(VideoClarity.newBuilder().setUrl(m3u8480Path).setName("480").build());
        }
        if(StringUtils.isNotBlank(mp4720Path)){
            result.add(VideoClarity.newBuilder().setUrl(mp4720Path).setName("720").build());
        }else if(StringUtils.isNotBlank(m3u8720Path)){
            result.add(VideoClarity.newBuilder().setUrl(m3u8720Path).setName("720").build());
        }
        if(StringUtils.isNotBlank(mp41080Path)){
            result.add(VideoClarity.newBuilder().setUrl(mp41080Path).setName("1080").build());
        }else if(StringUtils.isNotBlank(m3u81080Path)){
            result.add(VideoClarity.newBuilder().setUrl(m3u81080Path).setName("1080").build());
        }

        if(StringUtils.isNotBlank(transRequest.getTempFilePath())){
            String outMp3Path = FilenameUtils.getPath(needTransVideoFileCurrentPath) + FilenameUtils.getBaseName(
                needTransVideoFileCurrentPath) + ".mp3";
            if(!new File(sysConfig.getPhysicalPath(outMp3Path)).exists()){
                FileParseUtil.convertMp4ToMp3(sysConfig.getPhysicalPath(transRequest.getTempFilePath()), sysConfig.getPhysicalPath(outMp3Path));
            }
        }
        return result;
    }

    private boolean checkWidthHeight(FileParseInfo fileParseInfo,Integer value){
        return fileParseInfo.getWidth() > value || fileParseInfo.getHeight() > value;
    }

    private String mp4(TransRequest transRequest,String inputObject,String outputObject,Integer isToMp4Trans,Integer enable,String rate) throws Exception {
        if(!Objects.equals(isToMp4Trans,0)){
            return "";
        }
        if(!Objects.equals(enable,1)){
            return "";
        }

        if(!outputObject.endsWith(".mp4")){
            outputObject = outputObject.concat(".mp4");
        }

        outputObject = FilenameUtils.getPath(outputObject) + rate+"/"+FilenameUtils.getName(outputObject);

        MpsUtil.submitJobs(transRequest.getMpsAccessKeyId(), transRequest.getMpsAccessKeySecret(), transRequest.getBucketName(), inputObject, outputObject, "mp4", rate);

        return outputObject;
    }


    private String m3u8(TransRequest transRequest,String inputObject,String outputObject,Integer isToM3u8Trans,Integer enable,String rate) throws Exception {
        if(!Objects.equals(isToM3u8Trans,1)){
            return "";
        }
        if(!Objects.equals(enable,1)){
            return "";
        }


        if(outputObject.endsWith(".mp4")){
            outputObject = outputObject.substring(0, outputObject.length() - 4);
        }else if(outputObject.endsWith(".m3u8")){
            outputObject = outputObject.substring(0, outputObject.length() - 5);
        }

        outputObject = FilenameUtils.getPath(outputObject) + rate+"/m3u8/"+FilenameUtils.getName(outputObject);

        MpsUtil.submitJobs(transRequest.getMpsAccessKeyId(), transRequest.getMpsAccessKeySecret(), transRequest.getBucketName(), inputObject, outputObject, "m3u8", rate);

        return outputObject+".m3u8";
    }


}
