package com.wunding.learn.exam.api.query;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;

/**
 * <p>
 * 考试分数查询
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">wgr</a>
 * @date 2022/9/27 10:00
 */
@Data
@Schema(name = "ExamScoreQuery", description = "考试分数查询对象")
public class ExamScoreQuery implements Serializable {

    @Schema(description = "用户ID")
    private String userId;

    @Schema(description = "考试ID")
    private String examId;

}
