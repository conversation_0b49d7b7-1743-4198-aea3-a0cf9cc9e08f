package com.wunding.learn.exam.api.service;

import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.bean.BaseEntity;
import com.wunding.learn.common.bean.Result;
import com.wunding.learn.common.dto.CerDitchDTO;
import com.wunding.learn.common.dto.CertificationContentDTO;
import com.wunding.learn.common.dto.CompleteNumQuery;
import com.wunding.learn.common.dto.LearningCalendarTaskDTO;
import com.wunding.learn.common.dto.LearningCalendarTaskQuery;
import com.wunding.learn.common.dto.ResourceBaseDTO;
import com.wunding.learn.common.dto.ResourceDeleteInfoDTO;
import com.wunding.learn.common.dto.ResourceIdAndUserIdDTO;
import com.wunding.learn.common.dto.ResourceMemberDTO;
import com.wunding.learn.common.dto.TaskAppResourceDTO;
import com.wunding.learn.common.query.ResourceBaseQuery;
import com.wunding.learn.common.query.ResourceUserQuery;
import com.wunding.learn.exam.api.dto.AiQuestionExamFeignDTO;
import com.wunding.learn.exam.api.dto.ExamInfoDTO;
import com.wunding.learn.exam.api.dto.ExamScoreDTO;
import com.wunding.learn.exam.api.dto.ExamTimeDTO;
import com.wunding.learn.exam.api.dto.ExamUserScoreDTO;
import com.wunding.learn.exam.api.dto.ImportUserExamRecordDTO;
import com.wunding.learn.exam.api.dto.ViewExamFeignDTO;
import com.wunding.learn.exam.api.query.ExamQuery;
import com.wunding.learn.exam.api.query.ExamScoreQuery;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @create 2022/5/11 16:11
 * @description
 */
@FeignClient(url = "${learn.service.learn-exam-service}", name = "learn-exam-service", path = "/exam")
public interface ExamFeign {

    /**
     * feign-获得某个考试
     *
     * @param examQuery 考试查询对象
     * @return 考试信息
     */
    @PostMapping("/exam/getExamInfoById")
    ViewExamFeignDTO getExamInfoById(@RequestBody ExamQuery examQuery);

    /**
     * feign-获得某个考试
     *
     * @param examQuery 考试查询对象
     * @return 考试信息
     */
    @PostMapping("/exam/getExamInfoMapByIds")
    Map<String, ViewExamFeignDTO> getExamInfoMapByIds(@RequestBody ExamQuery examQuery);

    /**
     * 获取有效考试或练习id列表（未删除，结束时间大于当前时间）
     *
     * @param examType 考试类型 1考试，2练习
     * @return
     */
    @GetMapping("/exam/getEffectiveExamIds")
    List<String> getEffectiveExamIds(@RequestParam("examType") Integer examType);

    /**
     * 考试完成情况
     *
     * @param userId 用户id
     * @param examId 考试id
     * @return 完成状态
     */
    @GetMapping("/exam/getExamFinishStatus")
    Integer getExamFinishStatus(@RequestParam("userId") String userId, @RequestParam("examId") String examId);

    /**
     * 用户考试得分（已交卷、已改卷、最新考试记录的及格分和用户得分）
     *
     * @param userId  用户id
     * @param examIds 考试id集合
     * @return 及格分和用户得分
     */
    @PostMapping("/exam/getUserScoreInfo")
    List<ExamUserScoreDTO> getUserScoreInfo(@RequestParam("userId") String userId,
        @RequestParam("examIds") Collection<String> examIds);

    /**
     * 考试完成情况
     *
     * @param userId  用户id
     * @param examIds 考试id
     * @return 完成状态
     */
    @GetMapping("/exam/getExamFinishStatusByExamIds")
    Map<String, Integer> getExamFinishStatusByExamIds(@RequestParam("userId") String userId,
        @RequestBody Collection<String> examIds);

    /**
     * 批量获得考试(只根据id无视是否发布删除)
     *
     * @param examIds 考试id集合
     * @return 考试信息
     */
    @PostMapping("/exam/getExamInfoMapByExamIds")
    Map<String, ViewExamFeignDTO> getExamInfoMapByExamIds(@RequestParam("examIds") Collection<String> examIds);

    /**
     * 批量获得考试(根据id只过滤已删除)
     *
     * @param examIds 考试id集合
     * @return 考试信息
     */
    @PostMapping("/exam/getValidExamInfoMapByExamIds")
    Map<String, ViewExamFeignDTO> getValidExamInfoMapByExamIds(@RequestParam("examIds") Collection<String> examIds);

    /**
     * 批量获得考试交卷数
     *
     * @param examIds 考试id集合
     * @return 考试信息
     */
    @PostMapping("/exam/getPostExamCountMapByExamIds")
    Map<String, Long> getPostExamCountMapByExamIds(@RequestParam("examIds") Collection<String> examIds);

    /**
     * 批量获得考试改卷数
     *
     * @param examIds 考试id集合
     * @return 考试信息
     */
    @PostMapping("/exam/getCheckExamCountMapByExamIds")
    Map<String, Long> getCheckExamCountMapByExamIds(@RequestParam("examIds") Collection<String> examIds);

    /**
     * 获取单个考试信息
     *
     * @param examId 考试id
     * @return 考试信息
     */
    @GetMapping("/exam/getById")
    ExamInfoDTO getById(@RequestParam("examId") String examId);

    /**
     * 获取单个考试信息(不过滤删除)
     *
     * @param examId 考试id
     * @return 考试信息
     */
    @GetMapping("/exam/getRealityById")
    ExamInfoDTO getRealityById(@RequestParam("examId") String examId);

    /**
     * 获取单个考试发布状态信息
     *
     * @param examId 考试id
     * @return state 0-未发布 1-已发布
     */
    @GetMapping("/exam/getPubState")
    int getExamPubState(@RequestParam("resourceId") String examId);

    /**
     * 更新单个考试开始结束时间
     *
     * @param examTimeDTO
     * @return
     */
    @PostMapping("/exam/updateExamTime")
    void updateExamTime(@RequestBody ExamTimeDTO examTimeDTO);

    /**
     * 发布/取消发布引用创建考试
     *
     * @param id        考试id
     * @param isPublish 0-取消发布 1-发布
     */
    @PutMapping("/exam/publishExam")
    void publishExam(@RequestParam("id") String id, @RequestParam("isPublish") Integer isPublish);

    /**
     * 删除考试
     *
     * @param id
     * @param checkPublish 是否检测发布状态 0=不检测发布状态 1=检测发布状态
     */
    @PostMapping("/exam/remove")
    void removeExam(@RequestParam("id") String id, @RequestParam("checkPublish") Integer checkPublish);

    /**
     * 批量获取用户指定考试中的及格数
     *
     * @param completeNumQuery 查询
     * @return {@link Map}<{@link String}, {@link Integer}>
     */
    @PostMapping("/exam/getExamPassNum")
    Map<String, Integer> getExamPassNum(@RequestBody CompleteNumQuery completeNumQuery);

    /**
     * 获取用户的最后得分
     *
     * @param userId
     * @param examId
     * @return
     */
    @GetMapping("/exam/getUserScore")
    BigDecimal getUserScore(@RequestParam("userId") String userId, @RequestParam("examId") String examId);

    /**
     * 删除所有题目
     *
     * @param examId
     */
    @GetMapping("/exam/delAllQuestions")
    void delAllQuestions(@RequestParam("examId") String examId);

    /**
     * 通过id列表获取考试名 不过滤被删除数据，认证模块使用
     *
     * @param batchIds 考试表ids
     * @return {@link Map}<{@link String}, {@link String}> id与考试名称映射
     */
    @GetMapping("/exam/getNameBatchIds")
    Map<String, String> getNameBatchIds(@RequestParam("batchIds") Collection<String> batchIds);


    /**
     * 通过id列表获取考试信息 不过滤被删除数据
     *
     * @param batchIds 考试表ids
     * @return {@link Map}<{@link String}, {@link ResourceMemberDTO}> id与考试信息映射
     */
    @GetMapping("/exam/getResourceMemberBatchIds")
    Map<String, ResourceMemberDTO> getResourceMemberBatchIds(@RequestParam("batchIds") Collection<String> batchIds);

    /**
     * 获取单个考试信息
     *
     * @param examId 考试id
     * @return 考试信息
     */
    @GetMapping("/exam/getOneExam")
    ExamInfoDTO getOneExam(@RequestParam("examId") String examId);

    /**
     * 同步保存考试下发范围
     *
     * @param resourceId
     * @param programmeId
     */
    @PostMapping("/exam/saveSyncExamViewLimit")
    void saveSyncExamViewLimit(@RequestParam("resourceId") String resourceId,
        @RequestParam("programmeId") Long programmeId);

    /**
     * 检查下发范围
     *
     * @param userId     用户id
     * @param resourceId 资源id
     * @return {@link Boolean}
     */
    @GetMapping("/exam/checkViewLimit")
    Boolean checkViewLimit(@RequestParam("userId") String userId, @RequestParam("resourceId") String resourceId);

    /**
     * 根据渠道id 获取一个渠道信息
     *
     * @param contentId 内容识别
     * @return {@link CerDitchDTO}
     */
    @GetMapping("/exam/getDitch")
    CerDitchDTO getDitch(@RequestParam("contentId") String contentId);

    /**
     * 获取考试 开始结束时间
     *
     * @param id 资源id
     * @return 开始时间 结束时间
     */
    @GetMapping("/exam/getExamTime")
    List<Date> getExamTime(@RequestParam("id") String id);

    /**
     * 查询考试的学习日历 任务
     *
     * @param learningCalendarTaskQuery
     * @return
     */
    @PostMapping("/exam/findLearningCalenderExamTaskList")
    List<LearningCalendarTaskDTO> findLearningCalenderExamTaskList(
        @RequestBody LearningCalendarTaskQuery learningCalendarTaskQuery);


    /**
     * 根据考试ID集合 查询考试未删除，已经发布得课程Id
     *
     * @param examIdList
     * @return
     */
    @PostMapping("/exam/findExamStatusByIdList")
    List<String> findExamStatusByIdList(@RequestBody Set<String> examIdList);

    /**
     * 获取单个考试信息 -包含已删除数据
     *
     * @param id
     * @return
     */
    @GetMapping("/exam/getOneExamIncludeDel")
    ExamInfoDTO getOneExamIncludeDel(@RequestParam("id") String id);


    /**
     * 用户考试分数
     *
     * @param examScoreQueryList
     * @return
     */
    @PostMapping("/exam/findUserExamScoreList")
    List<ExamScoreDTO> findUserExamScoreList(@RequestBody List<ExamScoreQuery> examScoreQueryList);


    /**
     * 用户考试分数,未改卷数据(userScore 为 null)
     *
     * @param examScoreQueryList 考试成绩查询列表
     * @return {@link List }<{@link ExamScoreDTO }>
     */
    @PostMapping("/exam/findUserExamScoreListFilterNotCheck")
    List<ExamScoreDTO> findUserExamScoreListFilterNotCheck(@RequestBody List<ExamScoreQuery> examScoreQueryList);

    /**
     * * 2520获取考试完成数量
     *
     * @param year
     * @param type
     * @return
     */
    @GetMapping("/exam/getIdpStatistic")
    int getIdpStatistic(@RequestParam("year") String year, @RequestParam("type") String type);

    /**
     * 过滤获取已被删除的资源id
     *
     * @param examIdList
     * @returnl
     */
    @GetMapping("/exam/getInvalidExamId")
    List<String> getInvalidExamId(@RequestParam("examIdList") Collection<String> examIdList);

    /**
     * 获取该用户已完成考试id
     *
     * @param userId 用户id
     * @param ids    考试id集合
     * @return {@link List}<{@link String}>
     */
    @GetMapping("/exam/getFinishedExamIds")
    List<String> getFinishedExamIds(@RequestParam("userId") String userId, @RequestParam("ids") Collection<String> ids);

    /**
     * 获取任务列表中考试部分总数
     *
     * @param status 任务状态,  0-待办 1-已办 2-过期未完成
     * @param userId
     * @return
     */
    @GetMapping("/exam/getMenuTaskCount")
    Integer getMenuTaskCount(@RequestParam("status") Integer status, @RequestParam("userId") String userId);

    /**
     * 获取任务列表中考试部分 分页查询
     *
     * @param status 任务状态,  0-待办 1-已办 2-过期未完成
     * @param entity
     * @return
     */
    @GetMapping("/exam/getMenuTask")
    Result<PageInfo<ExamInfoDTO>> getMenuTask(@RequestParam("status") Integer status, @RequestBody BaseEntity entity);

    /**
     * 考试任务获取最高分数
     *
     * @param userIds
     * @param examId
     * @return
     */
    @GetMapping("/exam/getMaxScore")
    Map<String, BigDecimal> getMaxScore(@RequestBody Collection<String> userIds,
        @RequestParam("examId") String examId);

    @PostMapping("/exam/getUserScoreByListParams")
    Map<String, BigDecimal> getUserScoreByListParams(
        @RequestBody Collection<ResourceIdAndUserIdDTO> userIdAndContendId);

    @GetMapping("/exam/getResourceIsNotDeleteAndIsPublish")
    int getResourceIsNotDeleteAndIsPublish(@RequestParam("id") String resourceId);

    /**
     * 修改考试一些属性
     *
     * @param examInfoDTO
     */
    @PostMapping("/exam/updateExamById")
    void updateExamById(@RequestBody ExamInfoDTO examInfoDTO);


    /**
     * 根据考试id获取任务考试信息
     *
     * @param ids
     */
    @PostMapping("/getTaskExamByIds")
    List<TaskAppResourceDTO> getTaskExamByIds(@RequestBody Collection<String> ids);

    /**
     * 启用禁用考试
     *
     * @param id
     * @param publishType
     */
    @PostMapping("/exam/publishOrUnPublishExam")
    void publishOrUnPublishExam(@RequestParam("id") String id, @RequestParam("publishType") Integer publishType);

    /**
     * 批量获取用户完成状态
     *
     * @param resourceUserQuery
     * @return
     */
    @PostMapping("/exam/getExamFinish")
    Map<String, Integer> getExamFinish(@RequestBody ResourceUserQuery resourceUserQuery);

    /**
     * 获取考试基本信息
     *
     * @param resourceBaseQuery
     * @return
     */
    @PostMapping("/exam/getExamBaseInfo")
    Map<String, ResourceBaseDTO> getExamBaseInfo(@RequestBody ResourceBaseQuery resourceBaseQuery);

    /**
     * 批量获取用户完成状态
     *
     * @param resourceUserQuery
     * @return
     */
    @PostMapping("/exam/getExamScore")
    Map<String, BigDecimal> getExamScore(@RequestBody ResourceUserQuery resourceUserQuery);

    /**
     * 获取用户考试通过状态
     *
     * @param userId 用户id
     * @param ids    考试id
     * @return {@link Map}
     */
    @GetMapping("/exam/getExamPass")
    Map<String, Integer> getExamPass(@RequestParam("userId") String userId,
        @RequestParam("ids") Collection<String> ids);

    /**
     * 创建导入用户考试记录
     *
     * @param userExamRecordDTOS
     */
    @PostMapping("/exam/createImportUserExamRecord")
    void createImportUserExamRecord(@RequestBody List<ImportUserExamRecordDTO> userExamRecordDTOS);

    /**
     * 获取考试基本信息
     *
     * @param batchIds
     * @return
     */
    @GetMapping("/exam/getCertificationContentList")
    Map<String, CertificationContentDTO> getCertificationContentList(
        @RequestParam("batchIds") Collection<String> batchIds);

    /**
     * 根据用户管理范围的组织信息和资源ID校验是否具有资源管理权限
     *
     * @param userManageAreaOrgId 户管理范围的组织信息ID列表
     * @param id                  考试或练习ID
     * @return 0 无权限，1 有权限
     */
    @PostMapping("/exam/checkExamManagePermissions")
    Integer checkExamManagePermissions(
        @RequestParam("userManageAreaOrgId") Collection<String> userManageAreaOrgId, @RequestParam("id") String id);


    /**
     * 获取用户考试通过数量
     * @param examIds
     * @param userId
     * @return
     */
    @PostMapping("/exam/getUserExamPassNum")
    Integer getUserExamPassNum(@RequestParam("examIds") Collection<String> examIds, @RequestParam("userId") String userId);

    /**
    * 刷新成绩更新时间
    * @param userIds
    * @return
    */
    @PostMapping("/exam/updateExamAnswerRecordTimeByUserId")
    void updateExamAnswerRecordTimeByUserId(@RequestBody Collection<String> userIds);

    /**
     * 课件里ai生成的题目添加到考题库
     *
     * @param aiQuestionList 题目信息集合
     * @param libId 题库id
     */
    @PostMapping("/exam/addAiQuestionToExamLib")
    void addAiQuestionToExamLib(@RequestBody List<AiQuestionExamFeignDTO> aiQuestionList, @RequestParam("libId") String libId);

    /**
     * 根据考题库id获取题库名称
     *
     * @param libIdList 题库id集合
     * @return key-libId, value-libName
     */
    @PostMapping("/exam/getExamLibNameByLibId")
    Map<String, String> getExamLibNameByLibId(@RequestBody List<String> libIdList);

    /**
     * 删除题目
     *
     * @param questionId 题目id
     */
    @GetMapping("/exam/deleteAiQuestionByQuestionIdAndLibId")
    void deleteAiQuestionByQuestionIdAndLibId(@RequestParam("questionId") String questionId);

    /**
     * 获取考试的删除信息
     * @param resourceId 考试id
     * @return ResourceDeleteInfoDTO
     */
    @GetMapping("/exam/getExamIsDelById")
    ResourceDeleteInfoDTO getExamIsDelById(@RequestParam("resourceId") String resourceId);

    /**
     * 获取资源删除状态
     * @param resourceId 资源id
     * @return
     */
    @GetMapping("/exam/getResourceIsDel")
    int getResourceIsDel(@RequestParam("resourceId") String resourceId);
}
