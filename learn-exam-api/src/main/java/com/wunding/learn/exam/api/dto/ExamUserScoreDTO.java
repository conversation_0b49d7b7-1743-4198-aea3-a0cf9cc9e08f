package com.wunding.learn.exam.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 用户考试分数DTO
 *
 * <AUTHOR>
 * @since 2025-06-15
 */
@Data
@Schema(name = "ExamUserScoreDTO", description = "用户考试分数DTO")
public class ExamUserScoreDTO implements Serializable {

    private static final long serialVersionUID = -6872880195382810536L;

    @Schema(description = "考试ID")
    private String examId;

    @Schema(description = "及格分数")
    private BigDecimal passScore;

    @Schema(description = "用户分数")
    private BigDecimal userScore;

}
