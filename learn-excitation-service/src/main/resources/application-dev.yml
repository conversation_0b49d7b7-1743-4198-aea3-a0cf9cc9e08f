# 应用服务WEB访问端口
server:
  port: 28014

spring:

  # 数据库设置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *************************************************************************************************
    username: 1f73af99-60bc-40c5-9bf4-097bba486883
    password: k7zcvNGQMx8QcSEy


  #redis
  data:
    redis:
      #Redis 服务器地址
      host: ************
      #Redis服务器连接端口
      port: 30079
      #Redis数据库索引(默认为0)
      database: 0
      # Redis服务器连接密码
      password: M0eHdhs9kk4VKLjRAb7J41
      # 连接超时时间(毫秒)
      timeout: 10000


  # rabbitmq 配置
  rabbitmq:
    host: ************
    port: 30672
    virtual-host: /dev
    username: guest
    password: guest


management:
  tracing:
    sampling:
      probability: 1.0
    enabled: true
    propagation:
      type: B3
  otlp:
    metrics:
      export:
        enabled: true
  zipkin:
    tracing:
      endpoint: http://************:30917

opentelemetry:
  traces:
    exporter: otlp
  service:
    name: ${spring.application.name}
  jaeger:
    endpoint: http://************:30915
    protocol: grpc
  instrumentation:
    feign:
      enabled: true
    spring-webmvc:
      enabled: true

xxl:
  job:
    admin:
      addresses: http://************/xxl-job-admin
    executor:
      appName: ${spring.application.name}
      ip:
      port: 9999
      logPath: /data/applogs/xxl-job/jobhandler
      logRetentionDays: -1
    accessToken: vopeqn943epfaspdf

learn:
  service:
    learn-file-service: "http://************:28003"
    learn-user-service: "http://************:28001"
    learn-project-service: "http://************:28013"
    learn-course-service: "http://************:28006"
    learn-certification-service: "http://************:28021"
    learn-forum-service: "http://************:28012"
    learn-exam-service: "http://************:28004"
    learn-example-service: "http://************:28019"
    learn-live-service: "http://************:28009"
    learn-survey-service: "http://************:28008"
    learn-info-service: "http://************:28018"
    learn-evaluation-service: "http://************:28029"
    learn-market-service: "http://************:28028"
    learn-train-service: "http://************:28030"
    learn-operation-service: "http://************:28031"
    learn-payment-service: "http://localhost:30053"
app:
  signKey: bladexisapowerfulmicroservicearchitectureupgradedandoptimizedfromacommercialproject
  single:
    - api


#seata:
#  client:
#    undo:
#      # kryo序列化工具
#      log-serialization: kryo
#  config:
#    type: file
#  application-id: ${spring.application.name}
#  # enable-auto-data-source-proxy: false
#  registry:
#    type: file
#  service:
#    grouplist:
#      default: ************:8091
#    vgroup-mapping:
#      springboot-seata-group: default
#  # seata 事务组编号 用于TC集群名
#  tx-service-group: springboot-seata-group


debug: false
############# springDoc配置 #############
springdoc:
  version: '@springdoc.version@'
  api-docs:
    groups:
      enabled: true
    enabled: true
  swagger-ui:
    operationsSorter: function
    tagsSorter: alpha
    docExpansion: none
    path: /swagger-ui.html
  group-configs:
    - group: api
      packages-to-scan: com.wunding.learn.excitation.service.client.rest
    - group: web
      packages-to-scan: com.wunding.learn.excitation.service.admin.rest
  # 不配置下面 get请求为对象会变为 json
  default-flat-param-object: true
  disable-i18n: true

excitation:
  systemTypeList:
    - name: internalTraining
      type: 1
      enable: true
    - name: afterSale
      type: 2
      enable: true
















