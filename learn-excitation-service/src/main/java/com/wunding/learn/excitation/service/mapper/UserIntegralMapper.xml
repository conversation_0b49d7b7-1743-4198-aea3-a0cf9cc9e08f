<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.excitation.service.mapper.UserIntegralMapper">
    <!-- 开启二级缓存 -->
    <!--
<cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.excitation.service.mapper.UserIntegralMapper"/>

    <resultMap id="ExcitationExplainResultMap"
      type="com.wunding.learn.excitation.service.client.dto.ExcitationExplainEventVO">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="bound" property="bound" jdbcType="INTEGER"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <collection property="list"
          ofType="com.wunding.learn.excitation.service.client.dto.ExcitationExplainEventInfoVO"
          column="configId">
            <id column="configId" property="id" jdbcType="VARCHAR"/>
            <result column="configBound" property="bound" jdbcType="VARCHAR"/>
            <result column="configType" property="type" jdbcType="VARCHAR"/>
            <result column="minNum" property="minNum" jdbcType="INTEGER"/>
            <result column="maxNum" property="maxNum" jdbcType="INTEGER"/>
            <result column="score" property="score" jdbcType="INTEGER"/>
            <result column="typeId" property="typeId" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.excitation.service.model.UserIntegral">
        <!--@Table user_integral-->
        <id column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="num" jdbcType="DECIMAL"
          property="num"/>
        <result column="available_num" jdbcType="DECIMAL"
          property="availableNum"/>
        <result column="origin_num" jdbcType="DECIMAL"
          property="originNum"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        user_id, num, available_num, origin_num
    </sql>

    <select id="selectUserExcitationTradeRecord"
      resultType="com.wunding.learn.excitation.service.client.dto.UserExcitationTradeRecordDTO" useCache="false">
        select a.id,
        a.user_id,
        a.operate_type,
        a.operate_num,
        a.current_num,
        a.trade_type,
        a.target_id,
        a.target_name,
        a.event_id,
        (select e.name from excitation_event e where e.id = a.event_id) eventName,
        a.summary,
        a.create_time,
        a.is_exchange
        from excitation_trade_record a
        where a.user_id = #{userId}
        <if test="excitationId != null and excitationId != ''">
            and a.excitation_id = #{excitationId}
        </if>
        <if test="targetIds != null and targetIds.size() > 0">
            and a.target_id in  <foreach collection="targetIds" item="id" open="(" close=")" separator=","> #{id} </foreach>
        </if>
        order by a.create_time desc
    </select>

    <select id="getExcitationExplain" resultMap="ExcitationExplainResultMap" useCache="false">
        select b.id id,
        b.name name,
        ifnull(d.bound, 0) bound,
        b.type type,
        a.id configId,
        a.same_event_bound configBound,
        a.min_bound minNum,
        a.max_bound maxNum,
        c.name configType,
        c.id typeId,
        a.excitation_num score
        from
        <choose>
            <when test="type == 1">
                excitation_config_user a
            </when>
            <otherwise>
                excitation_config_global a
            </otherwise>
        </choose>
        inner join excitation_event b ON a.event_id = b.id
        inner join excitation_type c ON a.type_id = c.id
        left join excitation_event_config d on a.id = d.event_id and d.system_type = #{systemType}
        where a.is_del = 0
        and a.is_available = 1
        <choose>
            <when test="type == 1">
                and a.excitation_category = #{flag} and a.target_id = #{id}
            </when>
            <otherwise>
                and b.category IN ('system', 'lecturer', 'feedback')
            </otherwise>
        </choose>
        <if test="systemType != null">
            and a.system_type = #{systemType}
        </if>
        order by a.event_id, a.type_id, a.same_event_bound, a.min_bound
    </select>
    
    <select id="getUserIntegralRecorder"
            resultType="com.wunding.learn.excitation.service.admin.dto.UserIntegralDetailDTO">
        select id, create_time, operate_num score, summary description
        from excitation_trade_record etr
        where excitation_id = 'integral' and etr.create_by = #{userId}
        <if test="type !=null and type != '' and type == 3">
          and event_id in ('lecturerJoinProjectTeach', 'voteLecturer')
        </if>
        <if test="startTime !=null and endTime!= null">
            and date_format(saui.time,'%Y-%m') >= date_format(#{startTime},'%Y-%m')
            and date_format(#{endTime},'%Y-%m') >= date_format(saui.time,'%Y-%m')
        </if>
    </select>
    
</mapper>
