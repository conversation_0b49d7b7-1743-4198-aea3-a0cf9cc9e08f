package com.wunding.learn.excitation.service.service.impl;

import static org.apache.commons.lang3.StringUtils.isEmpty;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.wunding.learn.certification.api.service.CertificationFeign;
import com.wunding.learn.common.constant.excitation.ExcitationErrorNoEnum;
import com.wunding.learn.common.constant.other.ErrorNoEnum;
import com.wunding.learn.common.constant.other.ParaConstant;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.dto.ImportResultDTO;
import com.wunding.learn.common.dto.MyStatisticListDTO;
import com.wunding.learn.common.enums.ResourceTypeEnum;
import com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum;
import com.wunding.learn.common.enums.excitation.ExcitationEventEnum;
import com.wunding.learn.common.enums.excitation.ExcitationTypeEnum;
import com.wunding.learn.common.enums.excitation.OtherEventCategoryEnum;
import com.wunding.learn.common.enums.other.ExcitationEventCategoryTypeEnum;
import com.wunding.learn.common.enums.other.ExcitationOperationEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.other.SystemConfigCodeEnum;
import com.wunding.learn.common.enums.push.MyStatisticEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.excel.ExcelCheckMessage;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.common.util.string.TranslateUtil;
import com.wunding.learn.course.api.service.CourseFeign;
import com.wunding.learn.excitation.api.dto.UserExcitationRecordDTO;
import com.wunding.learn.excitation.api.query.UserProjectExcitationRecordQuery;
import com.wunding.learn.excitation.service.admin.dto.ImportIntegralDTO;
import com.wunding.learn.excitation.service.admin.dto.UserIntegralDTO;
import com.wunding.learn.excitation.service.admin.dto.UserIntegralDetailDTO;
import com.wunding.learn.excitation.service.admin.query.IntegralClearQuery;
import com.wunding.learn.excitation.service.admin.query.UserIntegralQuery;
import com.wunding.learn.excitation.service.client.dto.CommitExchangeGoldCoin;
import com.wunding.learn.excitation.service.client.dto.ExchangeConfig;
import com.wunding.learn.excitation.service.client.dto.ExcitationEventObjectDTO;
import com.wunding.learn.excitation.service.client.dto.ExcitationExplainDTO;
import com.wunding.learn.excitation.service.client.dto.ExcitationExplainEventInfoVO;
import com.wunding.learn.excitation.service.client.dto.ExcitationExplainEventVO;
import com.wunding.learn.excitation.service.client.dto.ExcitationExplainItemDTO;
import com.wunding.learn.excitation.service.client.dto.MyStatisticDTO;
import com.wunding.learn.excitation.service.client.dto.PointsDTO;
import com.wunding.learn.excitation.service.client.dto.UserExcitationItemDTO;
import com.wunding.learn.excitation.service.client.dto.UserExcitationTradeRecordDTO;
import com.wunding.learn.excitation.service.client.query.ExcitationApiQuery;
import com.wunding.learn.excitation.service.client.query.ExcitationExplainQuery;
import com.wunding.learn.excitation.service.dao.UserIntegralDao;
import com.wunding.learn.excitation.service.imports.UserIntegralTemplate;
import com.wunding.learn.excitation.service.mapper.ExcitationTradeRecordMapper;
import com.wunding.learn.excitation.service.mapper.UserIntegralMapper;
import com.wunding.learn.excitation.service.model.EventConfig;
import com.wunding.learn.excitation.service.model.ExchangeRecord;
import com.wunding.learn.excitation.service.model.ExcitationTradeRecord;
import com.wunding.learn.excitation.service.model.UserCredit;
import com.wunding.learn.excitation.service.model.UserExperience;
import com.wunding.learn.excitation.service.model.UserGoldCoin;
import com.wunding.learn.excitation.service.model.UserIntegral;
import com.wunding.learn.excitation.service.model.UserLearnTime;
import com.wunding.learn.excitation.service.service.IEventConfigService;
import com.wunding.learn.excitation.service.service.IExchangeRecordService;
import com.wunding.learn.excitation.service.service.IExchangeRuleConfigService;
import com.wunding.learn.excitation.service.service.IExcitationEventService;
import com.wunding.learn.excitation.service.service.IExcitationOperation;
import com.wunding.learn.excitation.service.service.ITitleService;
import com.wunding.learn.excitation.service.service.IUserCreditService;
import com.wunding.learn.excitation.service.service.IUserExperienceService;
import com.wunding.learn.excitation.service.service.IUserGoldCoinService;
import com.wunding.learn.excitation.service.service.IUserIntegralService;
import com.wunding.learn.excitation.service.service.IUserLearnTimeService;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.dto.AbstractExportDataDTO;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.file.api.dto.ImportDataDTO;
import com.wunding.learn.file.api.service.ImportDataFeign;
import com.wunding.learn.forum.api.service.ForumFeign;
import com.wunding.learn.operation.api.service.MedalFeign;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.service.CustomMenuFeign;
import com.wunding.learn.user.api.service.OrgFeign;
import com.wunding.learn.user.api.service.ParaFeign;
import com.wunding.learn.user.api.service.RouterFeign;
import com.wunding.learn.user.api.service.UserFeign;
import jakarta.annotation.Resource;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.ReflectionUtils;

/**
 * <p> 用户积分表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi</a>
 * @since 2022-08-08
 */
@Slf4j
@Service("userIntegralService")
public class UserIntegralServiceImpl extends ServiceImpl<UserIntegralMapper, UserIntegral> implements
    IUserIntegralService, IExcitationOperation {

    public static final String INVOLVED = "不涉及";
    /**
     * 没激励数量的事件
     */
    private static final List<String> EVENLIST = List.of(ExcitationEventEnum.finishCourseWareByTime.name());
    public static final String STATISTICS = "Statistics";

    @Resource
    private CourseFeign courseFeign;
    @Resource
    private RouterFeign routerFeign;
    @Resource
    @Lazy
    private IUserGoldCoinService userGoldCoinService;
    @Resource
    private IUserCreditService userCreditService;
    @Resource
    private IUserLearnTimeService userLearnTimeService;
    @Resource
    private IUserExperienceService userExperienceService;
    @Resource
    private ITitleService titleService;
    @Resource
    private UserFeign userFeign;
    @Resource
    private OrgFeign orgFeign;
    @Resource
    private ExcitationTradeRecordMapper excitationTradeRecordMapper;
    @Resource
    private ImportDataFeign importDataFeign;
    @Resource
    private IExchangeRuleConfigService iExchangeRuleConfigService;
    @Resource
    private IExcitationEventService excitationEventService;
    @Resource
    private IExchangeRecordService exchangeRecordService;
    @Resource
    private ExportComponent exportComponent;
    @Resource(name = "userIntegralDao")
    private UserIntegralDao userIntegralDao;
    @Resource
    private ParaFeign paraFeign;

    @Resource
    private CustomMenuFeign customMenuFeign;

    @Lazy
    @Resource
    private ForumFeign forumFeign;
    @Resource
    @Lazy
    private CertificationFeign certificationFeign;

    @Resource
    private MedalFeign medalFeign;

    @Resource
    private IEventConfigService eventConfigService;

    @Override
    public void saveOrUpdateUserIntegral(ExcitationEventObjectDTO eventObject) {
        long count = count(new LambdaQueryWrapper<UserIntegral>().eq(UserIntegral::getUserId, eventObject.getUserId()));
        if (count == 0) {
            // 新增
            save(new UserIntegral().setUserId(eventObject.getUserId()).setNum(eventObject.getScore())
                .setAvailableNum(eventObject.getScore()).setConvertibleNum(eventObject.getScore())
                .setOriginNum(BigDecimal.valueOf(0)));
        } else {
            // 更新
            LambdaUpdateWrapper<UserIntegral> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(UserIntegral::getUserId, eventObject.getUserId());
            String sql = eventObject.getOperateType() == 0 ? "num = num + " + eventObject.getScore()
                + ", available_num = available_num + " + eventObject.getScore()
                + ", convertible_num = convertible_num + " + eventObject.getScore()
                : "available_num = available_num - " + eventObject.getScore() + ", convertible_num = convertible_num - "
                    + eventObject.getScore();
            updateWrapper.setSql(sql);
            update(updateWrapper);
        }
    }

    private void initCodeList(List<String> codeList) {
        codeList.add(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_338.getCode());
        codeList.add(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_339.getCode());
        codeList.add(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_340.getCode());
        codeList.add(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_341.getCode());
        codeList.add(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_342.getCode());
        codeList.add(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_333.getCode());
        codeList.add(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_336.getCode());
        codeList.add(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_337.getCode());
        codeList.add(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_343.getCode());
    }

    @Override
    public List<MyStatisticListDTO> getPersonalInfo(boolean checkSysConfig) throws IllegalAccessException {

        MyStatisticDTO myStatisticDTO = getMyStatisticDTO();
        myStatisticDTO.setCollect(courseFeign.getCourseCollectCount(UserThreadContext.getUserId()));
        myStatisticDTO.setCertificate(certificationFeign.getCertificationCount(UserThreadContext.getUserId()));
        myStatisticDTO.setSubject(forumFeign.getPostCount(UserThreadContext.getUserId()));
        Integer medalNum = Optional.ofNullable(medalFeign.getMyMedals(UserThreadContext.getUserId())).map(List::size)
            .orElse(0);
        myStatisticDTO.setMedal(medalNum);
        myStatisticDTO.setSuggest(courseFeign.getSuggestToMe(UserThreadContext.getUserId()));
        ArrayList<String> codeList = new ArrayList<>();
        initCodeList(codeList);
        Map<String, Integer> statisticsMap = customMenuFeign.getCustomMenuAvailable(STATISTICS);
        String courseTime = statisticsMap.getOrDefault("courseTime", 1).toString();
        String integral = statisticsMap.getOrDefault("integral", 1).toString();
        String learnTime = statisticsMap.getOrDefault("learnTime", 1).toString();
        String learnCredit = statisticsMap.getOrDefault("learnCredit", 1).toString();
        String goldCoin = statisticsMap.getOrDefault("goldCoin", 1).toString();
        String collect = statisticsMap.getOrDefault("collect", 1).toString();
        String subject = ParaConstant.ON;
        String certificate = statisticsMap.getOrDefault("certificate", 1).toString();
        // 个人中心推荐归属 人才测评模块。多模块这里需要校验路由权限
        String suggest = ParaConstant.ON;
        List<String> routerIds = routerFeign.getRouterNames();

        List<MyStatisticListDTO> list = new ArrayList<>();
        Field[] fields = myStatisticDTO.getClass().getDeclaredFields();
        MyStatisticListDTO myStatisticListDTO;
        for (Field field : fields) {
            if (!routerIds.contains(MyStatisticEnum.getRouter(field.getName()))) {
                continue;
            }
            ReflectionUtils.makeAccessible(field);
            // 根据系统配置，进行过滤
            if (checkConfig1(checkSysConfig, field, courseTime, integral, learnTime, learnCredit, suggest)) {
                continue;
            }
            if (checkConfig2(checkSysConfig, field, goldCoin, collect, subject, certificate)) {
                continue;
            }
            if ("suggest".equals(field.getName()) && !routerIds.contains(ResourceTypeEnum.TALENT_ASSESS.getRouter())) {
                continue;
            }
            myStatisticListDTO = new MyStatisticListDTO();
            myStatisticListDTO.setId(field.getName());
            myStatisticListDTO.setFlag(field.getName());
            myStatisticListDTO.setTitle(MyStatisticEnum.getText(field.getName()));
            myStatisticListDTO.setValue(field.get(myStatisticDTO));
            list.add(myStatisticListDTO);
        }
        list.remove(0);
        return list;
    }

    @Override
    public BigDecimal getCourseExchangeable(String excitationType) {
        String userId = UserThreadContext.getUserId();
        switch (ExcitationTypeEnum.get(excitationType)) {
            case GOLD_COIN:
                UserGoldCoin userGoldCoin = userGoldCoinService.getDataById(userId);
                return userGoldCoin == null ? BigDecimal.ZERO : userGoldCoin.getConvertibleNum();
            case INTEGRAL:
                UserIntegral dbUserIntegral = getDataById(userId);
                return dbUserIntegral == null ? BigDecimal.ZERO : dbUserIntegral.getConvertibleNum();
            case CREDIT:
                UserCredit userCredit = userCreditService.getDataById(userId);
                return userCredit == null ? BigDecimal.ZERO : userCredit.getConvertibleNum();
            case LEARN_TIME:
                UserLearnTime userLearnTime = userLearnTimeService.getDataById(userId);
                return userLearnTime == null ? BigDecimal.ZERO : userLearnTime.getConvertibleNum();
            default:
                log.error("excitationType error, param: {}", excitationType);
        }
        return BigDecimal.ZERO;
    }

    // 减少代码复杂度
    private static boolean checkConfig2(boolean checkSysConfig, Field field, String goldCoin, String collect,
        String subject, String certificate) {
        if (checkGoldCoin(checkSysConfig, field, goldCoin)) {
            return true;
        }
        if (checkCollect(checkSysConfig, field, collect)) {
            return true;
        }
        if (checkSubject(checkSysConfig, field, subject)) {
            return true;
        }
        return checkCertificate(checkSysConfig, field, certificate);
    }

    // 减少代码复杂度
    private static boolean checkConfig1(boolean checkSysConfig, Field field, String courseTime, String integral,
        String learnTime, String learnCredit, String suggest) {
        if (checkCourseTime(checkSysConfig, field, courseTime)) {
            return true;
        }
        if (checkIntegral(checkSysConfig, field, integral)) {
            return true;
        }
        if (checkLearnTime(checkSysConfig, field, learnTime)) {
            return true;
        }
        if (checkSuggest(checkSysConfig, field, suggest)) {
            return true;
        }
        return checkLearnCredit(checkSysConfig, field, learnCredit);
    }

    private static boolean checkSuggest(boolean checkSysConfig, Field field, String suggest) {
        return checkSysConfig && "suggest".equals(field.getName()) && StringUtils.equals(suggest,
            ParaConstant.OFF);
    }

    private static boolean checkCourseTime(boolean checkSysConfig, Field field, String courseTime) {
        return checkSysConfig && "courseTime".equals(field.getName()) && StringUtils.equals(courseTime,
            ParaConstant.OFF);
    }

    private static boolean checkIntegral(boolean checkSysConfig, Field field, String integral) {
        return checkSysConfig && "integral".equals(field.getName()) && StringUtils.equals(integral,
            ParaConstant.OFF);
    }

    private static boolean checkLearnTime(boolean checkSysConfig, Field field, String learnTime) {
        return checkSysConfig && "learnTime".equals(field.getName()) && StringUtils.equals(learnTime,
            ParaConstant.OFF);
    }

    private static boolean checkLearnCredit(boolean checkSysConfig, Field field, String learnCredit) {
        return checkSysConfig && "learnCredit".equals(field.getName()) && StringUtils.equals(learnCredit,
            ParaConstant.OFF);
    }

    private static boolean checkGoldCoin(boolean checkSysConfig, Field field, String goldCoin) {
        return checkSysConfig && "goldCoin".equals(field.getName()) && StringUtils.equals(goldCoin,
            ParaConstant.OFF);
    }

    private static boolean checkCollect(boolean checkSysConfig, Field field, String collect) {
        return checkSysConfig && "collect".equals(field.getName()) && StringUtils.equals(collect,
            ParaConstant.OFF);
    }

    private static boolean checkSubject(boolean checkSysConfig, Field field, String subject) {
        return checkSysConfig && "subject".equals(field.getName()) && StringUtils.equals(subject,
            ParaConstant.OFF);
    }

    private static boolean checkCertificate(boolean checkSysConfig, Field field, String certificate) {
        return checkSysConfig && "certificate".equals(field.getName()) && StringUtils.equals(certificate,
            ParaConstant.OFF);
    }

    @Override
    public MyStatisticDTO getMyStatisticDTO() {
        String userId = UserThreadContext.getUserId();
        MyStatisticDTO myStatisticDTO = new MyStatisticDTO();
        UserIntegral dbUserIntegral = getDataById(userId);
        myStatisticDTO.setIntegral(dbUserIntegral.getAvailableNum());
        myStatisticDTO.setExchangeableIntegral(dbUserIntegral.getConvertibleNum());

        Double userCourseDuration = courseFeign.getUserCourseDruation(userId);
        if (userCourseDuration != 0.00) {
            double v = userCourseDuration / (Long.parseLong("60") * 60);
            String courseTime = String.format("%.2f", v);
            myStatisticDTO.setCourseTime(courseTime);
        } else {
            myStatisticDTO.setCourseTime("0");
        }
        UserGoldCoin userGoldCoin = userGoldCoinService.getDataById(userId);
        myStatisticDTO.setGoldCoin(userGoldCoin.getRemainNum());
        myStatisticDTO.setExchangeableGoldCoin(userGoldCoin.getConvertibleNum());
        UserCredit userCredit = userCreditService.getDataById(userId);
        myStatisticDTO.setLearnCredit(userCredit.getRemainNum());
        myStatisticDTO.setExchangeableLearnCredit(userCredit.getConvertibleNum());
        UserLearnTime userLearnTime = userLearnTimeService.getDataById(userId);
        myStatisticDTO.setLearnTime(userLearnTime.getAvailableNum());
        myStatisticDTO.setExchangeableLearnTime(userLearnTime.getConvertibleNum());

        UserExperience userExperience = userExperienceService.getDataById(userId);
        String title = titleService.getTitleByExperience(userExperience.getNum());
        myStatisticDTO.setDesignation(title);
        return myStatisticDTO;
    }

    @Override
    public MyStatisticDTO getMyStatisticAvailableNumDTO() {
        String userId = UserThreadContext.getUserId();
        MyStatisticDTO myStatisticDTO = new MyStatisticDTO();
        UserIntegral dbUserIntegral = getById(userId);
        if (dbUserIntegral != null) {
            myStatisticDTO.setExchangeableIntegral(dbUserIntegral.getConvertibleNum());
        }
        BigDecimal userGold = userGoldCoinService.getUserGold(userId);
        myStatisticDTO.setGoldCoin(userGold);
        BigDecimal userCredit = userCreditService.getUserConvertibleNumCredit(userId);
        myStatisticDTO.setExchangeableLearnCredit(userCredit);
        BigDecimal userLearTime = userLearnTimeService.getLearnConvertibleNum(userId);
        myStatisticDTO.setExchangeableLearnTime(userLearTime);
        return myStatisticDTO;
    }

    @Override
    public Boolean isExchange() {
        Integer exchange = iExchangeRuleConfigService.isExchange(
            userFeign.getUserById(UserThreadContext.getUserId()).getSystemType());
        return exchange > 0;
    }

    @Override
    public List<PointsDTO> getMyPointsInfo() {
        // 取用户激励体系
        UserDTO userDTO = userFeign.getUserById(UserThreadContext.getUserId());
        // 获取兑换金币配置
        List<ExchangeConfig> list = iExchangeRuleConfigService.getExchangeConfigs(userDTO.getSystemType());
        List<PointsDTO> pointsVOList = new ArrayList<>();
        MyStatisticDTO shopper = getMyStatisticAvailableNumDTO();

        for (ExchangeConfig config : list) {
            PointsDTO pointsVO = new PointsDTO();
            pointsVO.setId(config.getId());
            ExcitationTypeEnum typeEnum = ExcitationTypeEnum.get(config.getId());
            pointsVO.setFlag(typeEnum.getCode());
            pointsVO.setValue(getBalance(typeEnum, shopper));
            pointsVO.setCoinNum(config.getCoinNum());
            pointsVO.setPayNum(config.getPayNum());
            pointsVO.setLowerBound(config.getLowerBound());
            pointsVOList.add(pointsVO);
        }
        return pointsVOList;
    }

    @Override
    public void commitExchangeGoldCoin(CommitExchangeGoldCoin commitExchangeGoldCoin) {
        log.info("---------commitExchangeGoldCoin params:{}", JsonUtil.objToJson(commitExchangeGoldCoin));
        String userId = UserThreadContext.getUserId();
        String flag = commitExchangeGoldCoin.getFlag();
        Integer value = commitExchangeGoldCoin.getValue();
        Integer coinNum = commitExchangeGoldCoin.getGoldCoinCount();

        // 校验兑换类型是否合法
        ExcitationTypeEnum typeEnum = ExcitationTypeEnum.getByFlag(flag);
        if (typeEnum == null) {
            throw new BusinessException(ErrorNoEnum.ERR_PARAMS);
        }

        UserDTO userDTO = userFeign.getUserById(userId);
        // 获取对应兑换金币配置
        ExchangeConfig config = iExchangeRuleConfigService.getExchangeConfigById(typeEnum.getCode(),
            userDTO.getSystemType());
        // 是否达到了最小兑换数量
        if (value.compareTo(config.getLowerBound()) < 0) {
            throw new BusinessException(ExcitationErrorNoEnum.ERR_NO_EXCHANGE_ALLOWED);
        }

        // 顾客信息
        MyStatisticDTO shopper = getMyStatisticDTO();
        if (isEmpty(config.getId()) || StringUtil.isEmpty(userId)) {
            throw new BusinessException(ErrorNoEnum.ERR_PARAMS);
        } else {
            // 实际兑换金币值
            BigDecimal actualCoinNum = (new BigDecimal(value).divide(new BigDecimal(config.getPayNum()), 0,
                RoundingMode.DOWN)).multiply(new BigDecimal(config.getCoinNum()));
            if (actualCoinNum.compareTo(new BigDecimal(coinNum)) < 0) {
                throw new BusinessException(ExcitationErrorNoEnum.ERR_EXCHANGE_GOLD_COIN_FAIL, null, coinNum + "");
            } else {
                // 获得对应兑换类型余额
                BigDecimal balance = getBalance(typeEnum, shopper);
                if (balance.compareTo(new BigDecimal(config.getLowerBound())) < 0
                    || new BigDecimal(value).compareTo(balance) > 0) {
                    throw new BusinessException(ExcitationErrorNoEnum.ERR_TICKET_FULL);
                } else {
                    // 兑换值消耗
                    ExcitationEventObjectDTO excitationEventObject1 = new ExcitationEventObjectDTO(userId,
                        config.getId(),
                        OtherEventCategoryEnum.exchangeGoldCoin.name(), new BigDecimal(value), typeEnum.getCode(), 1,
                        userId);
                    excitationEventObject1.setRemark("兑换".concat(coinNum.toString()).concat("金币"));
                    excitationEventService.otherEventHandler(excitationEventObject1);
                    // 金币增加
                    ExcitationEventObjectDTO excitationEventObjectDTO2 = new ExcitationEventObjectDTO(userId,
                        config.getId(),
                        OtherEventCategoryEnum.exchangeGoldCoin.name(), new BigDecimal(coinNum),
                        ExcitationTypeEnum.GOLD_COIN.getCode(), 0,
                        userId);
                    excitationEventObjectDTO2.setRemark(value.toString().concat(typeEnum.getName()).concat("兑换"));
                    excitationEventService.otherEventHandler(excitationEventObjectDTO2);

                    // 兑换记录
                    ExchangeRecord exchangeRecord = new ExchangeRecord().setId(StringUtil.newId()).setUserId(userId)
                        .setConsumeNum(new BigDecimal(value)).setRewardNum(new BigDecimal(coinNum))
                        .setRestNum(shopper.getGoldCoin().add(new BigDecimal(coinNum))).setType(2)
                        .setRemark(typeEnum.getName().concat("兑换金币")).setExcitationTypeId(typeEnum.getCode());
                    exchangeRecordService.save(exchangeRecord);
                }
            }
        }
    }

    /**
     * 根据配置返回余额
     *
     * @param typeEnum
     * @param shopper
     * @return
     */
    private BigDecimal getBalance(ExcitationTypeEnum typeEnum, MyStatisticDTO shopper) {
        BigDecimal balance = BigDecimal.ZERO;
        //根据系统参数设置,是否是关闭可兑换区分配置
        Integer isClose = Integer.valueOf(
            paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_912.getCode()));
        if (Objects.equals(isClose, GeneralJudgeEnum.CONFIRM.getValue())) {
            //1，关闭可兑换区分配置，优先使用可兑换的激励，再使用不可兑换
            if (typeEnum.getCode().equals(ExcitationTypeEnum.INTEGRAL.getCode())) {
                balance = shopper.getIntegral();
            } else if (typeEnum.getCode().equals(ExcitationTypeEnum.CREDIT.getCode())) {
                balance = shopper.getLearnCredit();
            } else if (typeEnum.getCode().equals(ExcitationTypeEnum.LEARN_TIME.getCode())) {
                balance = shopper.getLearnTime();
            }
        } else {
            //2，开启可兑换区分配置，只使用可兑换的
            if (typeEnum.getCode().equals(ExcitationTypeEnum.INTEGRAL.getCode())) {
                balance = shopper.getExchangeableIntegral();
            } else if (typeEnum.getCode().equals(ExcitationTypeEnum.CREDIT.getCode())) {
                balance = shopper.getExchangeableLearnCredit();
            } else if (typeEnum.getCode().equals(ExcitationTypeEnum.LEARN_TIME.getCode())) {
                balance = shopper.getExchangeableLearnTime();
            }
        }

        return balance;
    }

    @Override
    public PageInfo<UserExcitationItemDTO> getIntegralPageInfo(ExcitationApiQuery queryDTO) {
        queryDTO.setExcitationId(ExcitationTypeEnum.INTEGRAL.getCode());
        return getUserExcitationTradeRecord(queryDTO);
    }

    private PageInfo<UserExcitationItemDTO> getUserExcitationTradeRecord(ExcitationApiQuery queryDTO) {

        // 如果未提供查询用户ID参数，则默认使用当前登录用户ID
        String userId = StringUtil.isEmpty(queryDTO.getUserId())
            ? UserThreadContext.getUserId()
            : queryDTO.getUserId();

        queryDTO.setUserId(userId);
        PageInfo<UserExcitationTradeRecordDTO> pageInfo = PageMethod.startPage(queryDTO.getPageNo(),
            queryDTO.getPageSize()).doSelectPageInfo(() -> baseMapper.selectUserExcitationTradeRecord(queryDTO));

        PageInfo<UserExcitationItemDTO> result = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, result);

        List<UserExcitationItemDTO> userExcitationItemDTOList = new ArrayList<>();

        List<UserExcitationTradeRecordDTO> userIntegralRecordDTOList = pageInfo.getList();
        for (UserExcitationTradeRecordDTO tradeRecord : userIntegralRecordDTOList) {

            UserExcitationItemDTO creditRecordVO = new UserExcitationItemDTO();
            creditRecordVO.setId(tradeRecord.getId());
            StringBuilder sb = new StringBuilder();
            // 资源目标激励
            buildExcitation(tradeRecord, sb);
            creditRecordVO.setName(sb.toString());
            creditRecordVO.setTime(tradeRecord.getCreateTime());

            if (tradeRecord.getOperateType() == ExcitationOperationEnum.INCREASE.getValue()) {
                creditRecordVO.setValue(tradeRecord.getOperateNum());
            } else {
                creditRecordVO.setValue(tradeRecord.getOperateNum().compareTo(BigDecimal.ZERO) > 0
                    ? tradeRecord.getOperateNum().negate() : tradeRecord.getOperateNum());
            }
            // 设置默认不是兑换学习事件
            creditRecordVO.setIsExchangeStudy(GeneralJudgeEnum.NEGATIVE.getValue());
            creditRecordVO.setIsExchange(tradeRecord.getIsExchange());
            if (ExcitationEventEnum.isExchangeStudyEvent(tradeRecord.getEventId())) {
                creditRecordVO.setIsExchangeStudy(GeneralJudgeEnum.CONFIRM.getValue());
            }
            if (creditRecordVO.getValue().compareTo(BigDecimal.ZERO) <= 0) {
                creditRecordVO.setIsExchange(null);
            }
            userExcitationItemDTOList.add(creditRecordVO);
        }

        result.setList(userExcitationItemDTOList);
        return result;
    }

    // 资源目标激励 减少代码复杂度
    private static void buildExcitation(UserExcitationTradeRecordDTO tradeRecord, StringBuilder sb) {
        if (StringUtils.isNotEmpty(tradeRecord.getEventName()) && StringUtils.isNotEmpty(
            tradeRecord.getTargetName())) {
            sb.append(tradeRecord.getEventName()).append("《").append(tradeRecord.getTargetName()).append("》");
            // 全局目标激励
        } else if (StringUtils.isNotEmpty(tradeRecord.getEventName()) && isEmpty(
            tradeRecord.getTargetName())) {
            sb.append(tradeRecord.getEventName());
            // 其他目标激励
        } else if (isEmpty(tradeRecord.getEventName()) && OtherEventCategoryEnum.isOtherEvent(
            tradeRecord.getEventId())) {
            sb.append(tradeRecord.getSummary());
            //积分兑换学习
        } else if (StringUtils.isBlank(tradeRecord.getEventName()) && Objects.nonNull(
            tradeRecord.getOperateType())) {
            sb.append(tradeRecord.getSummary());
        }
    }

    @Override
    public PageInfo<UserExcitationItemDTO> getLearnTimePageInfo(ExcitationApiQuery queryDTO) {
        queryDTO.setExcitationId(ExcitationTypeEnum.LEARN_TIME.getCode());
        return getUserExcitationTradeRecord(queryDTO);
    }

    @Override
    public PageInfo<UserExcitationItemDTO> getLearnCreditPageInfo(ExcitationApiQuery queryDTO) {
        queryDTO.setExcitationId(ExcitationTypeEnum.CREDIT.getCode());
        return getUserExcitationTradeRecord(queryDTO);
    }

    @Override
    public PageInfo<UserExcitationItemDTO> getGoldCoinPageInfo(ExcitationApiQuery queryDTO) {
        queryDTO.setExcitationId(ExcitationTypeEnum.GOLD_COIN.getCode());
        return getUserExcitationTradeRecord(queryDTO);
    }

    @Override
    public UserIntegral initUserIntegral(String userId, BigDecimal amount) {
        return initUserIntegral(userId, amount, GeneralJudgeEnum.NEGATIVE.getValue());
    }

    @Override
    public UserIntegral initUserIntegral(String userId, BigDecimal amount, Integer isExchange) {
        UserIntegral integral = getById(userId);
        if (Objects.nonNull(integral)) {
            return integral;
        }
        UserIntegral userIntegral = new UserIntegral().setUserId(userId).setAvailableNum(amount).setNum(amount)
            .setOriginNum(amount);
        if (Objects.equals(isExchange, GeneralJudgeEnum.CONFIRM.getValue())) {
            userIntegral.setConvertibleNum(amount);
        }
        save(userIntegral);
        integral = getById(userId);
        return integral;
    }

    @Override
    public BigDecimal add(String userId, BigDecimal amount) {
        return add(userId, amount, GeneralJudgeEnum.NEGATIVE.getValue());
    }

    /**
     * 根据系统参数设置,是否是关闭可兑换区分配置 SYSTEM_CONFIG_CODE_912 1，关闭可兑换区分配置，强制修改可兑换积分成为不可兑换积分入库 2，开启可兑换区分配置，根据传入的是否可兑换积分入库
     *
     * @param userId
     * @param amount
     * @param isExchange
     * @return
     */
    @Override
    public BigDecimal add(String userId, BigDecimal amount, Integer isExchange) {
        //根据系统参数设置,是否是关闭可兑换区分配置
        Integer isClose = Integer.valueOf(
            paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_912.getCode()));
        if (Objects.equals(isClose, GeneralJudgeEnum.CONFIRM.getValue())) {
            //1，关闭可兑换区分配置，强制修改可兑换积分成为不可兑换积分入库
            isExchange = GeneralJudgeEnum.NEGATIVE.getValue();
        } else {
            //2，开启可兑换区分配置，根据传入的是否可兑换积分入库
        }
        return baseAdd(userId, amount, isExchange);
    }

    @Override
    public BigDecimal baseAdd(String userId, BigDecimal amount, Integer isExchange) {
        UserIntegral integral = initUserIntegral(userId, BigDecimal.ZERO, isExchange);
        // 增加积分
        UserIntegral integralUpdate = new UserIntegral().setUserId(userId).setNum(integral.getNum().add(amount))
            .setAvailableNum(integral.getAvailableNum().add(amount));
        if (Objects.equals(isExchange, GeneralJudgeEnum.CONFIRM.getValue())) {
            integralUpdate.setConvertibleNum(integral.getConvertibleNum().add(amount));
        }
        updateById(integralUpdate);
        return integralUpdate.getAvailableNum();
    }

    /**
     * 根据系统参数设置,是否是关闭可兑换区分配置 SYSTEM_CONFIG_CODE_912 1，关闭可兑换区分配置，优先消耗可兑换激励值，可兑换激励值不够时，再继续消耗不可兑换
     * 2，开启可兑换区分配置，优先消耗可兑换激励值，若是可兑换激励值不够时，直接报错
     *
     * @param userId
     * @param amount
     * @return
     */
    @Override
    public BigDecimal subtract(String userId, BigDecimal amount) {
        //根据系统参数设置,是否是关闭可兑换区分配置
        Integer isClose = Integer.valueOf(
            paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_912.getCode()));
        UserIntegral integral = initUserIntegral(userId, BigDecimal.ZERO);
        UserIntegral userIntegral;
        BigDecimal convertibleNum = integral.getConvertibleNum();
        BigDecimal convertibleNumAmount;

        if (Objects.equals(isClose, GeneralJudgeEnum.CONFIRM.getValue())) {
            //1，关闭可兑换区分配置，优先消耗可兑换激励值，可兑换激励值不够时，再继续消耗不可兑换
            if (integral.getAvailableNum().compareTo(amount) < 0) {
                throw new BusinessException(ExcitationErrorNoEnum.ERR_INTEGRAL_NOT_ENOUGH);
            }
            convertibleNumAmount = integral.getConvertibleNum().compareTo(amount) < 0 ? new BigDecimal(0)
                : convertibleNum.subtract(amount);
        } else {
            //2，开启可兑换区分配置，优先消耗可兑换激励值，若是可兑换激励值不够时，直接报错
            if (integral.getAvailableNum().compareTo(amount) < 0
                || integral.getConvertibleNum().compareTo(amount) < 0) {
                throw new BusinessException(ExcitationErrorNoEnum.ERR_INTEGRAL_NOT_ENOUGH);
            }
            convertibleNumAmount = convertibleNum.subtract(amount);
        }

        // 减少积分
        userIntegral = new UserIntegral().setUserId(userId)
            .setAvailableNum(integral.getAvailableNum().subtract(amount))
            .setConvertibleNum(convertibleNumAmount);
        updateById(userIntegral);
        return userIntegral.getAvailableNum();
    }

    @Override
    public List<ExcitationExplainDTO> getExcitationExplain(ExcitationExplainQuery excitationExplainQuery) {
        ExcitationEventCategoryEnum categoryEnum = ExcitationEventCategoryEnum.get(excitationExplainQuery.getFlag());
        if (Optional.ofNullable(categoryEnum).isEmpty()) {
            throw new BusinessException(ExcitationErrorNoEnum.ERR_EXCITATION_CATEGORY_NULL);
        }

        if (categoryEnum.getType() == ExcitationEventCategoryTypeEnum.USER_CONFIG.getValue() && StringUtils.isBlank(
            excitationExplainQuery.getId())) {
            throw new BusinessException(ExcitationErrorNoEnum.ERR_EXCITATION_ID_NULL);
        }
        List<ExcitationExplainDTO> excitationExplainList = new ArrayList<>();
        UserDTO userDTO = userFeign.getUserById(UserThreadContext.getUserId());
        Map<String, EventConfig> eventConfigMap = eventConfigService.list().stream()
            .collect(Collectors.toMap(EventConfig::getEventId, Function.identity(), (k1, k2) -> k1));
        List<ExcitationExplainEventVO> list = baseMapper.getExcitationExplain(excitationExplainQuery.getId(),
            excitationExplainQuery.getFlag(), categoryEnum.getType(), userDTO.getSystemType());
        for (ExcitationExplainEventVO eventVO : list) {
            ExcitationExplainDTO category = new ExcitationExplainDTO();
            category.setId(eventVO.getId());
            if (eventVO.getBound() > 0) {
                category.setName(
                    eventVO.getName().concat("（每日限制").concat(eventVO.getBound().toString()).concat("次奖励）"));
            } else {
                category.setName(eventVO.getName());
            }
            // item对象
            List<ExcitationExplainItemDTO> items = getExcitationExplainItemDTOS(
                eventVO, eventConfigMap, category);
            category.setList(items);
            excitationExplainList.add(category);
        }
        return excitationExplainList;
    }

    private List<ExcitationExplainItemDTO> getExcitationExplainItemDTOS(ExcitationExplainEventVO eventVO,
        Map<String, EventConfig> eventConfigMap, ExcitationExplainDTO category) {
        List<ExcitationExplainItemDTO> items = new ArrayList<>();
        for (ExcitationExplainEventInfoVO infoVO : eventVO.getList()) {

            ExcitationExplainItemDTO item = new ExcitationExplainItemDTO();
            item.setId(infoVO.getId());
            // 阈值区间
            if (eventVO.getType() == 1) {
                item.setBound(INVOLVED);
                item.setRange(infoVO.getMinNum().toString().concat("≤a<").concat(infoVO.getMaxNum().toString()));
            } else {
                item.setBound(infoVO.getBound() + "次");
                item.setRange(INVOLVED);
            }
            // 值（激励数量）
            if (EVENLIST.contains(category.getId())) {
                item.setScore(INVOLVED);
            } else {
                item.setScore(String.valueOf(infoVO.getScore()));
            }
            // 激励类型
            item.setType(infoVO.getType());
            EventConfig eventConfig = eventConfigMap.get(eventVO.getId());
            // 每日上限
            if (eventConfig != null) {
                item.setDayLimit(eventConfig.getBound() + "次");
            } else {
                item.setDayLimit(INVOLVED);
            }
            items.add(item);
        }
        return items;
    }

    @Override
    @Async
    public void cleanIntegral(IntegralClearQuery query) {
        // 需要清除的用户id集合
        Collection<String> userIdCollection = new ArrayList<>();

        if (StringUtils.isNotBlank(query.getUserIds())) {
            userIdCollection = TranslateUtil.translateBySplit(query.getUserIds(), String.class);
        }

        //不传任何条件时，根据管辖范围清除所有积分
        if (CollectionUtils.isEmpty(userIdCollection)) {
            Set<String> manageAreaOrgIds = orgFeign.findUserManageAreaLevelPath(UserThreadContext.getUserId());
            for (String areaOrgId : manageAreaOrgIds) {
                if (CollectionUtils.isEmpty(userIdCollection)) {
                    userIdCollection = userFeign.getUserIdsByOrgId(areaOrgId);
                } else {
                    userIdCollection.addAll(userFeign.getUserIdsByOrgId(areaOrgId));
                }
            }
        }

        if (CollectionUtils.isEmpty(userIdCollection)) {
            return;
        }

        Collection<UserIntegral> userIntegrals = new ArrayList<>(userIdCollection.size());
        for (String userId : userIdCollection) {
            // 获取数据库积分信息
            BigDecimal currentNum = excitationTradeRecordMapper.getUserRecentIntegral(userId);
            // 用户积分不存在/可用积分为0，直接返回
            if (currentNum.compareTo(BigDecimal.ZERO) == 0) {
                break;
            }

            // 可用积分取反
            BigDecimal negate = currentNum.negate();
            // 创建积分交易记录对象
            ExcitationTradeRecord tradeRecord = new ExcitationTradeRecord().setId(StringUtil.newId())
                .setExcitationId(ExcitationTypeEnum.INTEGRAL.getCode())
                .setOperateType(ExcitationOperationEnum.DECREASE.getValue()).setOperateNum(negate)
                .setCurrentNum(BigDecimal.ZERO).setTradeType(ExcitationTypeEnum.INTEGRAL.getCode()).setTargetId(userId)
                .setEventId(OtherEventCategoryEnum.integralClearing.name()).setUserId(userId)
                .setSummary("积分清零" + negate)
                .setCreateBy(userId);
            excitationTradeRecordMapper.insert(tradeRecord);
            // 创建用户积分更新对象
            UserIntegral integral = new UserIntegral().setUserId(userId)
                .setAvailableNum(BigDecimal.ZERO);
            userIntegrals.add(integral);
        }
        // 遍历单条更新并记录业务日志
        Map<String, UserDTO> userMap = userFeign.getFullNameAndOrgNameByUserId(new HashSet<>(userIdCollection));
        userIntegrals.forEach(userIntegral -> {
            UserDTO userDTO = userMap.get(userIntegral.getUserId());
            userIntegralDao.updateUserIntegral(userIntegral,
                Optional.ofNullable(userDTO).isPresent() ? userDTO.getFullName() : "");
        });
    }

    /**
     * 积分导入 1，先获得导入的积分记录 2，用导入的积分用户id去获取历史现有积分 3，把两部分记录合并，根据用户进行分组 4，全部使用+法运算,进行积分计算 5，更新到用户积分表，新增到积分交易记录表
     */
    @Override
    public ImportResultDTO importData(ImportIntegralDTO dto) {
        ImportResultDTO importResultDTO = new ImportResultDTO();
        importResultDTO.setIsSuccess(false);
        long beginTime = System.currentTimeMillis();
        ImportDataDTO importDataDTO = importDataFeign.getImportData(dto.getFilePath());
        log.info("获取导入的数据耗时：{}，共{}条数据", System.currentTimeMillis() - beginTime,
            importDataDTO.getRowCount());
        String[][] excel = importDataDTO.getExcel();

        beginTime = System.currentTimeMillis();
        ExcelCheckMessage excelCheckMessage = new UserIntegralTemplate(userFeign).check(excel);
        log.info("UserIntegralTemplate check 耗时：{}", System.currentTimeMillis() - beginTime);

        if (CollectionUtils.isEmpty(excelCheckMessage.getMessage())) {
            if (CollectionUtils.isEmpty(excelCheckMessage.getObjects())) {
                excelCheckMessage.getMessage().add("导入数据为空！");
            }
            log.info("积分导入开始");
            int count = excelCheckMessage.getObjects().size();
            beginTime = System.currentTimeMillis();
            Collection<UserIntegralDTO> userIntegrals = (Collection<UserIntegralDTO>) excelCheckMessage.getObjects();
            //最终计算结果集合
            List<UserIntegral> saveUserIntegralList = new ArrayList<>();
            //导入的积分集合
            List<UserIntegral> integralList = userIntegrals.stream().map(integralDTO -> {
                UserIntegral userIntegral = new UserIntegral();
                BeanUtils.copyProperties(integralDTO, userIntegral);
                //可兑换积分
                BigDecimal convertibleNum = new BigDecimal(0);
                //积分可兑换
                if (1 == integralDTO.getIsExchange()) {
                    convertibleNum = integralDTO.getAvailableNum();
                }
                userIntegral.setConvertibleNum(convertibleNum);
                return userIntegral;
            }).collect(Collectors.toList());

            //获取现有积分集合
            Set<String> integralUserIds = integralList.stream().map(UserIntegral::getUserId)
                .collect(Collectors.toSet());
            LambdaQueryWrapper<UserIntegral> historyWrapper = new LambdaQueryWrapper<>();
            historyWrapper.in(UserIntegral::getUserId, integralUserIds);
            List<UserIntegral> historyIntegralList = baseMapper.selectList(historyWrapper);
            List<String> historyUserIds = historyIntegralList.stream().map(UserIntegral::getUserId)
                .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(historyIntegralList)) {
                integralList.addAll(historyIntegralList);
            }

            //对积分进行运算合并成一条记录
            Map<String, List<UserIntegral>> integralMap = integralList.stream()
                .collect(Collectors.groupingBy(UserIntegral::getUserId));

            for (Entry<String, List<UserIntegral>> stringListEntry : integralMap.entrySet()) {
                UserIntegral item = new UserIntegral();
                //初始化积分数据：总积分数,可用积分数,可兑换积分
                BigDecimal num = new BigDecimal(0), availableNum = new BigDecimal(0), convertibleNum = new BigDecimal(
                    0);
                List<UserIntegral> userIntegralList = integralMap.get(stringListEntry.getKey());
                for (UserIntegral integral : userIntegralList) {
                    num = num.add(integral.getNum());
                    availableNum = availableNum.add(integral.getAvailableNum());
                    convertibleNum = convertibleNum.add(integral.getConvertibleNum());
                }
                item.setUserId(stringListEntry.getKey());
                item.setNum(num).setAvailableNum(availableNum).setConvertibleNum(convertibleNum);
                saveUserIntegralList.add(item);
            }

            // 遍历单条更新并记录业务日志
            updateOrSaveWithLogs(integralUserIds, saveUserIntegralList, historyUserIds);
            // 批量保存积分交易记录
            saveIntegralTradeRecord(userIntegrals);
            log.info("成功导入" + count + "条积分数据, 耗时{}", System.currentTimeMillis() - beginTime);
            importResultDTO.setMsg("成功导入" + count + "条积分数据");
            importResultDTO.setIsSuccess(true);
        }
        importResultDTO.setMsg(JsonUtil.objToJson(excelCheckMessage.getMessage()));
        return importResultDTO;
    }

    // 遍历单条更新并记录业务日志
    private void updateOrSaveWithLogs(Set<String> integralUserIds, List<UserIntegral> saveUserIntegralList,
        List<String> historyUserIds) {
        Map<String, UserDTO> userMap = userFeign.getFullNameAndOrgNameByUserId(integralUserIds);
        saveUserIntegralList.forEach(userIntegral -> {
            UserDTO userDTO = userMap.get(userIntegral.getUserId());
            if (historyUserIds.contains(userIntegral.getUserId())) {
                userIntegralDao.updateUserIntegral(userIntegral,
                    Optional.ofNullable(userDTO).isPresent() ? userDTO.getFullName() : "");
            } else {
                userIntegralDao.saveUserIntegral(userIntegral,
                    Optional.ofNullable(userDTO).isPresent() ? userDTO.getFullName() : "");
            }
        });
    }

    /**
     * 保存用户积分交易记录
     */
    private void saveIntegralTradeRecord(Collection<UserIntegralDTO> userIntegrals) {
        ExcitationTradeRecord tradeRecord;
        for (UserIntegralDTO integral : userIntegrals) {
            tradeRecord = new ExcitationTradeRecord().setId(StringUtil.newId()).setUserId(integral.getUserId())
                .setEventId(ExcitationTypeEnum.INTEGRAL.getCode()).setTargetName(ExcitationTypeEnum.INTEGRAL.getName())
                .setEventId(OtherEventCategoryEnum.manualImportIntegral.name()).setOperateNum(integral.getOperateNum())
                .setOperateType(integral.getOperateType().getValue()).setCurrentNum(integral.getAvailableNum())
                .setExcitationId(ExcitationTypeEnum.INTEGRAL.getCode())
                .setTradeType(ExcitationTypeEnum.INTEGRAL.getCode()).setBizType(integral.getBizType())
                .setIsExchange(integral.getIsExchange());
            String symbol;
            if (new BigDecimal(0).compareTo(integral.getOperateNum()) < 0) {
                symbol = ExcitationOperationEnum.INCREASE.getSymbol();
            } else {
                //分数原本带有负号，所以不用加符号
                symbol = "";
            }
            String summary = isEmpty(integral.getSummary()) ? "无" : integral.getSummary();
            tradeRecord.setSummary(
                String.format("手动导入积分%s%s积分 (说明：%s)", symbol, integral.getOperateNum(), summary));
            excitationTradeRecordMapper.insert(tradeRecord);
        }

    }

    @Override
    public PageInfo<UserIntegralDetailDTO> getUserIntegralRecorder(UserIntegralQuery query) {
        return PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> baseMapper.getUserIntegralRecorder(query));
    }

    @Override
    public void exportUserIntegralRecorderData(UserIntegralQuery query) {

        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IUserIntegralService, UserIntegralDetailDTO>(query) {

            @Override
            protected IUserIntegralService getBean() {
                return SpringUtil.getBean("userIntegralService", IUserIntegralService.class);
            }

            @Override
            protected PageInfo<UserIntegralDetailDTO> getPageInfo() {
                return getBean().getUserIntegralRecorder((UserIntegralQuery) queryDTO);

            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.UserIntegralDetail;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.UserIntegralDetail.getType();
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public UserIntegral getDataById(String userId) {
        return Optional.ofNullable(getById(userId)).orElse(initUserIntegral(userId, BigDecimal.ZERO));
    }

    @Override
    public BigDecimal getUserIntegral(String userId) {
        //根据系统参数设置,是否是关闭可兑换区分配置
        String paraValue = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_912.getCode());
        Integer isClose = StringUtils.isBlank(paraValue) ? Integer.valueOf(0) : Integer.valueOf(paraValue);
        LambdaQueryWrapper<UserIntegral> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserIntegral::getUserId, userId);
        UserIntegral one = getOne(queryWrapper);
        if (one != null) {
            return Objects.equals(isClose, GeneralJudgeEnum.CONFIRM.getValue()) ? one.getAvailableNum()
                : one.getConvertibleNum();
        }
        return BigDecimal.ZERO;
    }


    @Override
    public PageInfo<UserExcitationRecordDTO> getUserProjectExcitationRecord(UserProjectExcitationRecordQuery query) {

        if (StringUtil.isEmpty(query.getUserId()) || StringUtil.isEmpty(query.getProjectId()) || query.getTargetIds()
            .isEmpty()) {
            return PageInfo.emptyPageInfo();
        }

        // 查询类型：0-学时，1-积分，2-金币，3-学分
        ExcitationApiQuery excitationApiQuery = getExcitationApiQuery(query);
        PageInfo<UserExcitationItemDTO> pageInfo = getUserExcitationTradeRecord(excitationApiQuery);

        // 数据转换
        AtomicInteger no = new AtomicInteger((query.getPageNo() - 1) * query.getPageSize());
        List<UserExcitationRecordDTO> list = pageInfo.getList().stream().map(item -> {
            UserExcitationRecordDTO dto = new UserExcitationRecordDTO();
            dto.setNo(no.incrementAndGet());
            dto.setName(item.getName());
            dto.setValue(item.getValue());
            return dto;
        }).toList();

        PageInfo<UserExcitationRecordDTO> page = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, page);
        page.setList(list);

        return page;
    }

    private static ExcitationApiQuery getExcitationApiQuery(UserProjectExcitationRecordQuery query) {
        String excitationTypeId = switch (query.getType()) {
            case 0 -> ExcitationTypeEnum.LEARN_TIME.getCode();
            case 1 -> ExcitationTypeEnum.INTEGRAL.getCode();
            case 2 -> ExcitationTypeEnum.GOLD_COIN.getCode();
            case null, default -> ExcitationTypeEnum.CREDIT.getCode();
        };

        // 调用API查询获取用户项目激励获取记录
        ExcitationApiQuery excitationApiQuery = new ExcitationApiQuery();
        excitationApiQuery.setUserId(query.getUserId());
        excitationApiQuery.setExcitationId(excitationTypeId);
        excitationApiQuery.setTargetIds(query.getTargetIds());
        excitationApiQuery.setPageNo(query.getPageNo());
        excitationApiQuery.setPageSize(query.getPageSize());
        excitationApiQuery.setExport(query.getExport());
        return excitationApiQuery;
    }
}
