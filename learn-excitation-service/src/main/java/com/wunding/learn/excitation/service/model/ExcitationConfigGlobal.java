package com.wunding.learn.excitation.service.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 全局激励配置表
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi</a>
 * @since 2022-08-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("excitation_config_global")
@Schema(name = "ExcitationConfigGlobal对象", description = "全局激励配置表")
public class ExcitationConfigGlobal implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;


    /**
     * 激励事件ID
     */
    @Schema(description = "激励事件ID")
    @TableField("event_id")
    private String eventId;


    /**
     * 激励类型ID
     */
    @Schema(description = "激励类型ID")
    @TableField("type_id")
    private String typeId;


    /**
     * 每内容获取上限(次数)
     */
    @Schema(description = "每内容获取上限(次数)")
    @TableField("same_event_bound")
    private Long sameEventBound;


    /**
     * 计数类型事件使用 阀值下限(大于等于)
     */
    @Schema(description = "计数类型事件使用 阀值下限(大于等于)")
    @TableField("min_bound")
    private Long minBound;


    /**
     * 计数类型事件使用 阀值上限(小于)
     */
    @Schema(description = "计数类型事件使用 阀值上限(小于)")
    @TableField("max_bound")
    private Long maxBound;


    /**
     * 事件激励的数量:积分分数、金币数等。
     */
    @Schema(description = "事件激励的数量:积分分数、金币数等。")
    @TableField("excitation_num")
    private BigDecimal excitationNum;


    /**
     * 事件详细说明
     */
    @Schema(description = "事件详细说明")
    @TableField("intro")
    private String intro;


    /**
     * 是否锁定:0-未锁定;1-锁定
     */
    @Schema(description = "是否锁定:0-未锁定;1-锁定")
    @TableField("is_lock")
    private Integer isLock;


    /**
     * 是否删除 默认0
     */
    @Schema(description = "是否删除 默认0")
    @TableField("is_del")
    @TableLogic(value = "0", delval = "1")
    private Integer isDel;


    /**
     * 是否启用 默认0
     */
    @Schema(description = "是否启用 默认0")
    @TableField("is_available")
    private Integer isAvailable;


    /**
     * 添加人
     */
    @Schema(description = "添加人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;


    /**
     * 添加时间
     */
    @Schema(description = "添加时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 更新人
     */
    @Schema(description = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;


    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 激励方案ID
     */
    @Schema(description = "激励方案ID")
    @TableField("scheme_id")
    private Long schemeId;

    @Schema(description = "是否可兑换 0否 1是")
    @TableField(value = "is_exchange")
    private Integer isExchange;

    @Schema(description = "体系类型 1售后体系 2内训体系")
    @TableField(value = "system_type")
    private Integer systemType;

    /**
     * 是否正在同步 0否 1是
     */
    @Schema(description = "是否正在同步 0否 1是")
    @TableField(value = "is_syncing")
    private Integer isSyncing;
}
