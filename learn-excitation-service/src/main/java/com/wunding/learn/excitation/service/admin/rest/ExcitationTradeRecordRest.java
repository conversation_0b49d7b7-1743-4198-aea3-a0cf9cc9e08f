package com.wunding.learn.excitation.service.admin.rest;


import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.bean.BasePageQuery;
import com.wunding.learn.common.bean.Result;
import com.wunding.learn.excitation.service.admin.dto.IntegralTradePageDTO;
import com.wunding.learn.excitation.service.service.IExcitationTradeRecordService;
import com.wunding.learn.file.api.dto.ExportResultDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.format.annotation.DateTimeFormat.ISO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>  激励交易记录 前端控制器
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi</a>
 * @since 2022-08-09
 */
@RestController
@RequestMapping("${module.excitation.contentPath:/}excitationTradeRecord")
@Tag(description = "后台-激励交易记录管理", name = "ExcitationTradeRecordRest")
public class ExcitationTradeRecordRest {

    @Resource
    private IExcitationTradeRecordService tradeRecordService;

    @GetMapping("/{userId}/{type}")
    @Operation(operationId = "getIntegralTradePage_ExcitationTradeRecord", description = "获取用户积分交易记录数据分页列表", summary = "获取用户积分交易记录数据分页列表")
    public Result<PageInfo<IntegralTradePageDTO>> getIntegralTradePage(
        @PathVariable("userId") @Parameter(description = "用户id") String userId,
        @PathVariable("type") @Parameter(description = "查询类型：0 所有，-1 扣分，1 加分") Integer type,
        @RequestParam(required = false) @Parameter(description = "开始时间") @DateTimeFormat(iso = ISO.DATE_TIME) Date startTime,
        @RequestParam(required = false) @Parameter(description = "结束时间") @DateTimeFormat(iso = ISO.DATE_TIME) Date endTime,
        BasePageQuery basePageQuery) {
        return Result.success(tradeRecordService.getIntegralTradePage(userId, type, startTime, endTime, basePageQuery));
    }

    @PostMapping("/{userId}/{type}/exportDetail")
    @Operation(operationId = "exportDetail_UserIntegral", summary = "导出积分详情列表的数据", description = "导出积分详情列表的数据")
    public Result<ExportResultDTO> exportDetail(
        @PathVariable @Parameter(description = "用户id", example = "admin", required = true) String userId,
        @PathVariable("type") @Parameter(description = "查询类型：0 所有，-1 扣分，1 加分") Integer type,
        @RequestParam(required = false) @Parameter(description = "开始时间") @DateTimeFormat(iso = ISO.DATE_TIME) Date startTime,
        @RequestParam(required = false) @Parameter(description = "结束时间") @DateTimeFormat(iso = ISO.DATE_TIME) Date endTime,
        BasePageQuery basePageQuery) {
        tradeRecordService.export(userId, type, startTime, endTime, basePageQuery);
        return Result.success();
    }

    @GetMapping("/{userId}/{type}/getLecturerIntegralTradePage")
    @Operation(operationId = "getLecturerIntegralTradePage_ExcitationTradeRecord", description = "获取讲师积分交易记录数据分页列表", summary = "获取讲师积分交易记录数据分页列表")
    public Result<PageInfo<IntegralTradePageDTO>> getLecturerIntegralTradePage(
        @PathVariable("userId") @Parameter(description = "用户id") String userId,
        @PathVariable("type") @Parameter(description = "查询类型：0 所有，-1 扣分，1 加分") Integer type,
        BasePageQuery basePageQuery) {
        return Result.success(tradeRecordService.getLecturerIntegralTradePage(userId, type, basePageQuery));
    }

    @PostMapping("/{userId}/{type}/exportLecturerIntegralTrade")
    @Operation(operationId = "exportLecturerIntegralTrade_UserIntegral", summary = "导出讲师积分详情列表的数据", description = "导出讲师积分详情列表的数据")
    public Result<ExportResultDTO> exportLecturerIntegralTrade(
        @PathVariable @Parameter(description = "用户id", example = "admin", required = true) String userId,
        @PathVariable("type") @Parameter(description = "查询类型：0 所有，-1 扣分，1 加分") Integer type,
        BasePageQuery basePageQuery) {
        tradeRecordService.exportLecturerIntegralTrade(userId, type, basePageQuery);
        return Result.success();
    }
}
