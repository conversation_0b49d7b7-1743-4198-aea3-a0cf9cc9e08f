package com.wunding.learn.excitation.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.wunding.learn.excitation.api.dto.TargetValueGottenDetailClientDTO;
import com.wunding.learn.excitation.api.dto.UserGottenTargetValueDetailDTO;
import com.wunding.learn.excitation.api.query.UserTargetValueQuery;
import com.wunding.learn.excitation.service.admin.dto.UserGottenTargetValueDTO;
import com.wunding.learn.excitation.service.mapper.UserExcitationRecordMapper;
import com.wunding.learn.excitation.service.model.UserExcitationRecord;
import com.wunding.learn.excitation.service.service.IUserExcitationRecordService;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <p> 用户激励分值总记录表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi</a>
 * @since 2022-08-08
 */
@Slf4j
@Service("userExcitationRecordService")
public class UserExcitationRecordServiceImpl extends ServiceImpl<UserExcitationRecordMapper, UserExcitationRecord> implements IUserExcitationRecordService {

    @Override
    public Map<String, Integer> getUserHasBeenGotTargetValue(UserTargetValueQuery query) {
        if (CollectionUtils.isEmpty(query.getUserIdSet()) || CollectionUtils.isEmpty(query.getActiveTypeList())) {
            return Map.of();
        }
        List<UserGottenTargetValueDTO> list = baseMapper.getUserHasBeenGotTargetValue(query);
        if (CollectionUtils.isEmpty(list)) {
            return Map.of();
        }
        return list.stream().collect(Collectors.toMap(UserGottenTargetValueDTO::getUserId,
                a -> a.getTargetValue() != null ? a.getTargetValue().intValue() : 0));

    }

    @Override
    public PageInfo<UserGottenTargetValueDetailDTO> queryUserGottenTargetValueDetailList(UserTargetValueQuery query) {
        return PageMethod.startPage(query.getPageNo(), query.getPageSize())
                .doSelectPageInfo(() -> baseMapper.queryUserGottenTargetValueDetailList(query));
    }

    @Override
    public PageInfo<TargetValueGottenDetailClientDTO> queryUserGottenTargetValueDetailsClient(UserTargetValueQuery query) {
        PageInfo<TargetValueGottenDetailClientDTO> pageInfo = PageMethod.startPage(query.getPageNo(), query.getPageSize())
                .doSelectPageInfo(() -> baseMapper.queryUserGottenTargetValueDetailsClient(query));
        List<TargetValueGottenDetailClientDTO> list = pageInfo.getList();
        if (CollectionUtils.isEmpty(list)) {
            return pageInfo;
        }
        int count = list.stream().mapToInt(TargetValueGottenDetailClientDTO::getObtainValue).sum();
        list.forEach(bean -> bean.setTotalNum(count));
        return pageInfo;
    }
}
