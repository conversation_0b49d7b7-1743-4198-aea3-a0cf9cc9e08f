package com.wunding.learn.excitation.service.service.impl;

import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.wunding.learn.common.bean.BasePageQuery;
import com.wunding.learn.common.enums.excitation.ExcitationTypeEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.user.UserTypeEnum;
import com.wunding.learn.common.i18n.util.I18nUtil;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.date.DateHelper;
import com.wunding.learn.common.util.date.DateUtil;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.common.util.string.TranslateUtil;
import com.wunding.learn.excitation.api.dto.UserExcitationStatisticDTO;
import com.wunding.learn.excitation.service.admin.dto.IntegralTradePageDTO;
import com.wunding.learn.excitation.service.admin.dto.UserGoldCoinBillDTO;
import com.wunding.learn.excitation.service.admin.query.UserGoldCoinBillsQuery;
import com.wunding.learn.excitation.service.client.dto.UserExcitationByTimeDTO;
import com.wunding.learn.excitation.service.enums.TradeTypeEnum;
import com.wunding.learn.excitation.service.mapper.ExcitationTradeRecordMapper;
import com.wunding.learn.excitation.service.model.ExcitationTradeRecord;
import com.wunding.learn.excitation.service.service.IExcitationTradeRecordService;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.dto.AbstractExportDataDTO;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.user.api.dto.MemberOrgFeignDTO;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.service.MemberOrgFeign;
import com.wunding.learn.user.api.service.OrgFeign;
import com.wunding.learn.user.api.service.UserFeign;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * <p> 激励交易记录 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi</a>
 * @since 2022-08-08
 */
@Slf4j
@Service("excitationTradeRecordService")
public class ExcitationTradeRecordServiceImpl extends
    ServiceImpl<ExcitationTradeRecordMapper, ExcitationTradeRecord> implements IExcitationTradeRecordService {

    private static final String EXCITATION_TRADE_RECORD_SERVICE = "excitationTradeRecordService";

    @Resource
    private ExportComponent exportComponent;

    @Resource
    private OrgFeign orgFeign;

    @Resource
    private MemberOrgFeign memberOrgFeign;

    @Resource
    private UserFeign userFeign;

    @Override
    public PageInfo<IntegralTradePageDTO> getIntegralTradePage(String userId, Integer type, Date startTime,
        Date endTime,
        BasePageQuery query) {
        PageInfo<ExcitationTradeRecord> objectPageInfo = PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> list(
                new LambdaQueryWrapper<ExcitationTradeRecord>()
                    .eq(ExcitationTradeRecord::getUserId, userId)
                    .eq(ExcitationTradeRecord::getExcitationId, ExcitationTypeEnum.INTEGRAL.getCode())
                    .eq(type.equals(1), ExcitationTradeRecord::getOperateType, 0)
                    .eq(type.equals(-1), ExcitationTradeRecord::getOperateType, 1)
                    .ge(Objects.nonNull(startTime), ExcitationTradeRecord::getCreateTime,
                        DateUtil.formatDate2Date(startTime, DateHelper.YYYYMMDD))
                    .le(Objects.nonNull(endTime), ExcitationTradeRecord::getCreateTime,
                        DateUtil.formatDate2Date(DateUtil.addMonthsTime(endTime, 1), DateHelper.YYYYMMDD))
                    .orderByDesc(ExcitationTradeRecord::getCreateTime)
            ));
        PageInfo<IntegralTradePageDTO> pageInfo = new PageInfo<>();
        BeanUtils.copyProperties(objectPageInfo, pageInfo);

        List<ExcitationTradeRecord> list = objectPageInfo.getList();
        if (CollectionUtils.isEmpty(list)) {
            return pageInfo;
        }
        List<IntegralTradePageDTO> targetList = list.stream().map(tradeRecord -> {
            IntegralTradePageDTO dto = new IntegralTradePageDTO();
            BeanUtils.copyProperties(tradeRecord, dto);
            dto.setDescription(tradeRecord.getSummary());
            // option 兼容老数据写入的负数数据
            BigDecimal value = tradeRecord.getOperateType() == 0
                ? tradeRecord.getOperateNum()
                : Optional.ofNullable(tradeRecord.getOperateNum())
                    .map(item -> {
                        if (item.compareTo(BigDecimal.ZERO) <= 0) {
                            return item;
                        }
                        return item.negate();
                    }).orElse(null);
            // 填充业务类型
            String bizType = TradeTypeEnum.getDes(tradeRecord.getBizType());
            // 兼容积分记录中业务类型为空的情况，如果业务类型为空，则读取积分交易类型字段
            if (StringUtil.isEmpty(bizType)) {
                bizType = TradeTypeEnum.getDes(tradeRecord.getTradeType());
            }
            dto.setBizTypeDes(I18nUtil.getMessage(bizType));
            dto.setScore(value);
            return dto;
        }).collect(Collectors.toList());
        pageInfo.setList(targetList);
        return pageInfo;
    }

    @Override
    public void export(String userId, Integer type, Date startTime, Date endTime, BasePageQuery basePageQuery) {

        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IExcitationTradeRecordService, IntegralTradePageDTO>(
            basePageQuery) {

            @Override
            protected IExcitationTradeRecordService getBean() {
                return SpringUtil
                    .getBean(EXCITATION_TRADE_RECORD_SERVICE, IExcitationTradeRecordService.class);
            }

            @Override
            protected PageInfo<IntegralTradePageDTO> getPageInfo() {
                return getBean().getIntegralTradePage(userId, type, startTime, endTime, pageQueryDTO);

            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.UserIntegralDetailStatAnalysis;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.UserIntegralDetailStatAnalysis.getType();
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public PageInfo<IntegralTradePageDTO> getLecturerIntegralTradePage(String userId, Integer type,
        BasePageQuery query) {
        List<String> eventId1 = new ArrayList<>(Arrays.asList("lecturerJoinProjectTeach", "voteLecturer"));
        List<String> eventId2 = new ArrayList<>(List.of("manualImportIntegral"));

        //where excitation_id = 'integral' and (event_id in ('lecturerJoinProjectTeach', 'voteLecturer')
        // or (event_id in('manualImportIntegral') and biz_type='lecturer') or(biz_type='lectureClean'))
        PageInfo<ExcitationTradeRecord> objectPageInfo = PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> list(
                new LambdaQueryWrapper<ExcitationTradeRecord>()
                    .eq(ExcitationTradeRecord::getUserId, userId)
                    .eq(ExcitationTradeRecord::getExcitationId, ExcitationTypeEnum.INTEGRAL.getCode())
                    .eq(type.equals(1), ExcitationTradeRecord::getOperateType, 0)
                    .eq(type.equals(-1), ExcitationTradeRecord::getOperateType, 1)
                    .and(wrapper -> wrapper
                        .in(ExcitationTradeRecord::getEventId, eventId1)
                        .or()
                        .in(ExcitationTradeRecord::getEventId, eventId2)
                        .eq(ExcitationTradeRecord::getBizType, "lecturer")
                        .or()
                        .eq(ExcitationTradeRecord::getBizType, "lectureClean")
                    )
                    .orderByDesc(ExcitationTradeRecord::getCreateTime)
            ));
        PageInfo<IntegralTradePageDTO> pageInfo = new PageInfo<>();
        BeanUtils.copyProperties(objectPageInfo, pageInfo);

        List<ExcitationTradeRecord> list = objectPageInfo.getList();
        if (CollectionUtils.isEmpty(list)) {
            return pageInfo;
        }
        List<IntegralTradePageDTO> targetList = list.stream().map(excitationTradeRecord -> {
            IntegralTradePageDTO dto = new IntegralTradePageDTO();
            BeanUtils.copyProperties(excitationTradeRecord, dto);
            dto.setDescription(excitationTradeRecord.getSummary());
            dto.setScore(excitationTradeRecord.getOperateNum());

            // 填充业务类型
            String bizType = TradeTypeEnum.getDes(excitationTradeRecord.getBizType());
            // 兼容积分记录中业务类型为空的情况，如果业务类型为空，则读取积分交易类型字段
            if (StringUtil.isEmpty(bizType)) {
                bizType = TradeTypeEnum.getDes(excitationTradeRecord.getTradeType());
            }
            dto.setBizTypeDes(bizType);
            return dto;
        }).collect(Collectors.toList());
        pageInfo.setList(targetList);
        return pageInfo;
    }

    @Override
    public void exportLecturerIntegralTrade(String userId, Integer type, BasePageQuery basePageQuery) {

        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IExcitationTradeRecordService, IntegralTradePageDTO>(
            basePageQuery) {

            @Override
            protected IExcitationTradeRecordService getBean() {
                return SpringUtil
                    .getBean(EXCITATION_TRADE_RECORD_SERVICE, IExcitationTradeRecordService.class);
            }

            @Override
            protected PageInfo<IntegralTradePageDTO> getPageInfo() {
                return getBean().getLecturerIntegralTradePage(userId, type, pageQueryDTO);

            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.UserIntegralDetailStatAnalysis;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.UserIntegralDetailStatAnalysis.getType();
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }


    @Override
    public Boolean isFinishExcitationTrade(ExcitationTradeRecord tradeRecord) {
        List<ExcitationTradeRecord> list = baseMapper.getListWithOutRefund(tradeRecord);
        return !CollectionUtils.isEmpty(list);
    }

    @Override
    public List<ExcitationTradeRecord> getListWithOutRefund(ExcitationTradeRecord tradeRecord) {
        return baseMapper.getListWithOutRefund(tradeRecord);
    }

    @Override
    public PageInfo<UserGoldCoinBillDTO> pageUserGoldCoinBills(UserGoldCoinBillsQuery query) {
        if (StringUtils.isNotBlank(query.getOrgId())) {
            Optional.ofNullable(orgFeign.getById(query.getOrgId()))
                .ifPresent(org -> query.setOrgLevelPath(org.getLevelPath()));
        }
        if (StringUtils.isNotBlank(query.getUserIds())) {
            query.setUserIdCollection(TranslateUtil.translateBySplit(query.getUserIds(), String.class));
        }
        PageInfo<UserGoldCoinBillDTO> objects = PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> baseMapper.listUserGoldCoinBills(query));
        if (CollectionUtils.isEmpty(objects.getList())) {
            return objects;
        }

        Set<String> userIds = objects.getList().stream().map(billDTO -> {
            if (Objects.nonNull(billDTO) && Objects.nonNull(billDTO.getUserInfo())) {
                return billDTO.getUserInfo().getId();
            }
            return StringUtils.EMPTY;
        }).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        Map<String, UserDTO> userNameMap =
            CollectionUtils.isEmpty(userIds) ? new HashMap<>() : userFeign.getUserNameMapByIds(userIds);

        Set<String> memberOrgIds = objects.getList().stream().map(billDTO -> {
            if (Objects.nonNull(billDTO) && Objects.nonNull(billDTO.getUserInfo())) {
                return billDTO.getUserInfo().getMemberOrgId();
            }
            return StringUtils.EMPTY;
        }).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        Map<String, MemberOrgFeignDTO> map =
            CollectionUtils.isEmpty(memberOrgIds) ? new HashMap<>() : memberOrgFeign.getMapByIds(memberOrgIds);

        extracted(objects, map, userNameMap);
        return objects;
    }

    private static void extracted(PageInfo<UserGoldCoinBillDTO> objects, Map<String, MemberOrgFeignDTO> map,
        Map<String, UserDTO> userNameMap) {
        objects.getList().stream().filter(billDTO -> Objects.nonNull(billDTO.getUserInfo())).forEach(billDTO -> {
            billDTO.setIsRefunded(null);
            Optional.ofNullable(map.get(billDTO.getUserInfo().getMemberOrgId())).ifPresent(memberOrgFeignDTO ->
                billDTO.getUserInfo().setMemberOrgName(memberOrgFeignDTO.getName()));
            Optional.ofNullable(userNameMap.get(billDTO.getUserInfo().getId())).ifPresent(userDTO ->
                billDTO.getUserInfo().setType(userDTO.getUserType()));
            Optional.ofNullable(billDTO.getExcitationOperation()).ifPresent(recordDTO ->{
                // 如果有订单id且允许退款
                if(StringUtils.isNotBlank(billDTO.getOrderId()) && Objects.equals(recordDTO.getIsRefundable(),
                    GeneralJudgeEnum.CONFIRM.getValue())){
                    billDTO.setIsRefunded(false);
                }else if(StringUtils.isNotBlank(billDTO.getOrderId()) && Objects.equals(recordDTO.getIsRefundable(),
                    GeneralJudgeEnum.NEGATIVE.getValue())){
                    billDTO.setIsRefunded(true);
                }
            });
        });
    }

    @Override
    public void exportGoldCoinBill(UserGoldCoinBillsQuery query) {
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IExcitationTradeRecordService, UserGoldCoinBillDTO>(
            query) {

            @Override
            protected IExcitationTradeRecordService getBean() {
                return SpringUtil.getBean(EXCITATION_TRADE_RECORD_SERVICE, IExcitationTradeRecordService.class);
            }

            @Override
            protected PageInfo<UserGoldCoinBillDTO> getPageInfo() {
                return getBean().pageUserGoldCoinBills(query);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.UserGoldCoinBill;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.UserGoldCoinBill.getType();
            }

            @Override
            protected void afterCreateBeanMap(Map<String, Object> map) {
                Map<String, Object> userInfoDTO =
                    Objects.isNull(map.get("userInfo")) ? null : (Map) map.get("userInfo");
                if (userInfoDTO != null) {
                    map.put("type", Objects.nonNull(userInfoDTO.get("type")) ?
                        UserTypeEnum.getTextByValue((Integer) userInfoDTO.get("type")) : "未知类型用户");
                    map.put("name", userInfoDTO.get("name"));
                    map.put("phone", userInfoDTO.get("phone"));
                    map.put("loginName", userInfoDTO.get("loginName"));
                    map.put("memberOrgName", userInfoDTO.get("memberOrgName"));
                    map.put("orgName", userInfoDTO.get("orgName"));
                }

                Map<String, Object> operationDTO = Objects.isNull(map.get("excitationOperation")) ? null
                    : (Map) map.get("excitationOperation");
                if (operationDTO != null) {
                    map.put("operateType",
                        Objects.equals(operationDTO.get("operateType").toString(), "0") ? "收入" : "支出");
                    map.put("operateNum", operationDTO.get("operateNum").toString());
                    map.put("balanceNum", operationDTO.get("balanceNum").toString());
                    map.put("resourceName", operationDTO.get("resourceName"));
                    map.put("description", operationDTO.get("description"));
                }
            }
        };

        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public List<UserExcitationByTimeDTO> getUserIntegralByDate(String userId, List<Date> dateList) {
        return baseMapper.getUserIntegralByDate(userId, dateList);
    }


    @Override
    public Map<String, UserExcitationStatisticDTO> getUserExcitationStatistic(Collection<String> userIds,
        Collection<String> targetIds) {

        if (userIds.isEmpty()) {
            return Map.of();
        }

        // 查询条件
        LambdaQueryWrapper<ExcitationTradeRecord> queryWrapper = new LambdaQueryWrapper<ExcitationTradeRecord>()
            .in(ExcitationTradeRecord::getUserId, userIds)
            .in(!targetIds.isEmpty(), ExcitationTradeRecord::getTargetId, targetIds);

        // 所有数据
        List<ExcitationTradeRecord> userExcitationRecords = baseMapper.selectList(queryWrapper);

        if (userExcitationRecords.isEmpty()) {
            return Map.of();
        }
        // 根据激励类型分组 SUM ，获取指定用户的激励分值
        return userExcitationRecords.stream()
            .collect(Collectors.groupingBy(
                ExcitationTradeRecord::getUserId,
                Collectors.collectingAndThen(
                    Collectors.toList(),
                    list -> {
                        UserExcitationStatisticDTO dto = new UserExcitationStatisticDTO();
                        dto.setUserId(list.get(0).getUserId());
                        dto.setLearnTime(list.stream()
                            .filter(r -> ExcitationTypeEnum.LEARN_TIME.getCode().equals(r.getExcitationId()))
                            .map(ExcitationTradeRecord::getOperateNum)
                            .reduce(BigDecimal.ZERO, BigDecimal::add));
                        dto.setIntegral(list.stream()
                            .filter(r -> ExcitationTypeEnum.INTEGRAL.getCode().equals(r.getExcitationId()))
                            .map(ExcitationTradeRecord::getOperateNum)
                            .reduce(BigDecimal.ZERO, BigDecimal::add));
                        dto.setGoldCoin(list.stream()
                            .filter(r -> ExcitationTypeEnum.GOLD_COIN.getCode().equals(r.getExcitationId()))
                            .map(ExcitationTradeRecord::getOperateNum)
                            .reduce(BigDecimal.ZERO, BigDecimal::add));
                        dto.setCredit(list.stream()
                            .filter(r -> ExcitationTypeEnum.CREDIT.getCode().equals(r.getExcitationId()))
                            .map(ExcitationTradeRecord::getOperateNum)
                            .reduce(BigDecimal.ZERO, BigDecimal::add));
                        return dto;
                    }
                )
            ));
    }
}
