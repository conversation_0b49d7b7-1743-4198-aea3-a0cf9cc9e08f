package com.wunding.learn.excitation.service.feign;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.constant.excitation.ExcitationErrorNoEnum;
import com.wunding.learn.common.dto.MyStatisticListDTO;
import com.wunding.learn.common.enums.excitation.ExcitationEventEnum;
import com.wunding.learn.common.enums.excitation.ExcitationTypeEnum;
import com.wunding.learn.common.enums.other.ExcitationOperationEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.other.SystemConfigCodeEnum;
import com.wunding.learn.common.enums.other.TradeTypeEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.excitation.api.dto.TargetValueGottenDetailClientDTO;
import com.wunding.learn.excitation.api.dto.UserExcitationDataDTO;
import com.wunding.learn.excitation.api.dto.UserExcitationInitDTO;
import com.wunding.learn.excitation.api.dto.UserExcitationRecordDTO;
import com.wunding.learn.excitation.api.dto.UserExcitationReduceDTO;
import com.wunding.learn.excitation.api.dto.UserExcitationStatisticDTO;
import com.wunding.learn.excitation.api.dto.UserExcitationTradeBaseDTO;
import com.wunding.learn.excitation.api.dto.UserGottenTargetValueDetailDTO;
import com.wunding.learn.excitation.api.query.UserProjectExcitationRecordQuery;
import com.wunding.learn.excitation.api.query.UserTargetValueQuery;
import com.wunding.learn.excitation.api.service.ExcitationFeign;
import com.wunding.learn.excitation.service.mapper.UserExcitationRecordMapper;
import com.wunding.learn.excitation.service.model.ExcitationTradeRecord;
import com.wunding.learn.excitation.service.model.UserCredit;
import com.wunding.learn.excitation.service.model.UserGoldCoin;
import com.wunding.learn.excitation.service.model.UserIntegral;
import com.wunding.learn.excitation.service.model.UserLearnTime;
import com.wunding.learn.excitation.service.service.IExcitationOperation;
import com.wunding.learn.excitation.service.service.IExcitationTradeRecordService;
import com.wunding.learn.excitation.service.service.IUserExcitationRecordService;
import com.wunding.learn.excitation.service.service.IUserExperienceService;
import com.wunding.learn.excitation.service.service.impl.UserCreditServiceImpl;
import com.wunding.learn.excitation.service.service.impl.UserGoldCoinServiceImpl;
import com.wunding.learn.excitation.service.service.impl.UserIntegralServiceImpl;
import com.wunding.learn.excitation.service.service.impl.UserLearnTimeServiceImpl;
import com.wunding.learn.user.api.service.ParaFeign;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since  2022/11/9
 */
@RestController
@RequestMapping("${module.excitation.contentPath:/}")
@Slf4j
public class ExcitationFeignImpl implements ExcitationFeign {

    @Resource
    @Lazy
    private UserIntegralServiceImpl userIntegralService;
    @Resource
    private UserCreditServiceImpl userCreditService;
    @Resource
    private UserLearnTimeServiceImpl userLearnTimeService;
    @Resource
    private UserGoldCoinServiceImpl userGoldCoinService;
    @Resource
    private IUserExperienceService userExperienceService;
    @Resource
    private IExcitationTradeRecordService excitationTradeRecordService;
    @Resource
    private ParaFeign paraFeign;
    @Resource
    private IUserExcitationRecordService userExcitationRecordService;
    @Resource
    private UserExcitationRecordMapper userExcitationRecordMapper;

    @Override
    public void initUserExcitationInfo(UserExcitationInitDTO dto) {
        if (StringUtils.isEmpty(dto.getUserId())) {
            throw new BusinessException(ExcitationErrorNoEnum.ERR_USER_ID_NULL);
        }
        userIntegralService.initUserIntegral(dto.getUserId(), dto.getFund());
        userCreditService.initUserCredit(dto.getUserId(), dto.getFund());
        userLearnTimeService.initUserLearnTime(dto.getUserId(), dto.getFund());
        userGoldCoinService.initUserGoldCoin(dto.getUserId(), dto.getFund());
    }

    @Override
    public Map<String, BigDecimal> getUserIntegral(Collection<String> userIdList) {
        return userIntegralService.listByIds(userIdList).stream()
            .collect(Collectors.toMap(UserIntegral::getUserId, UserIntegral::getAvailableNum));
    }

    @Override
    public Integer getUserGold(String curUserId) {
        return Optional.ofNullable(userGoldCoinService.getById(curUserId)).map(UserGoldCoin::getRemainNum)
            .orElse(BigDecimal.ZERO).intValue();
    }

    @Override
    public Map<String, String> getUsersTitle(Collection<String> userIds) {
        return userExperienceService.getUsersTitle(userIds);
    }

    @Override
    public <T extends UserExcitationTradeBaseDTO> Boolean isFinishExcitationTrade(T dto) {
        return excitationTradeRecordService.isFinishExcitationTrade(new ExcitationTradeRecord()
            .setUserId(dto.getUserId()).setEventId(dto.getEventEnum().name())
            .setTargetId(dto.getTargetId()).setTradeType(dto.getTradeType()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reduceUserExcitationWithCheck(UserExcitationReduceDTO reduceDTO) {
        initBizReduceBizProperties(reduceDTO);
        IExcitationOperation excitationOperation = getOperationService(reduceDTO.getExcitationType());
        // 校验是否是允许重复交易，且是否已经扣除过了
        if (!reduceDTO.getEnableRepeatReduce()
            && isFinishExcitationTrade(reduceDTO)) {
            throw new BusinessException(ExcitationErrorNoEnum.ERR_EXCITATION_TRADED_NO_REPEAT);
        }
        // 以下业务使用ExcitationSubtractContext替代，当前业务不够完善
        BigDecimal afterValue = excitationOperation.subtract(reduceDTO.getUserId(), reduceDTO.getExcitationNum());
        // 增加交易记录,兑换的交易记录都应该是是否可兑换为1才行
        String excitationTypeName = ExcitationTypeEnum.getNameByCode(reduceDTO.getExcitationType());
        ExcitationTradeRecord tradeRecord = new ExcitationTradeRecord()
            .setId(StringUtil.newId())
            .setUserId(reduceDTO.getUserId())
            .setOperateType(ExcitationOperationEnum.DECREASE.getValue())
            .setExcitationId(reduceDTO.getExcitationType())
            .setOperateNum(reduceDTO.getExcitationNum())
            .setCurrentNum(afterValue)
            .setTradeType(reduceDTO.getTradeType())
            .setTargetId(reduceDTO.getTargetId())
            .setTargetName(reduceDTO.getTargetName())
            .setEventId(reduceDTO.getEventEnum().name())
            .setIsExchange(GeneralJudgeEnum.CONFIRM.getValue())
            .setBizId(reduceDTO.getBizId())
            .setBizType(reduceDTO.getBizType())
            .setSummary("兑换" + TradeTypeEnum.getItem(reduceDTO.getTradeType()) + "《" + reduceDTO.getTargetName() + "》"
                +ExcitationOperationEnum.DECREASE.getSymbol()+reduceDTO.getExcitationNum()+excitationTypeName);
            //根据系统参数设置,是否是关闭可兑换区分配置 SYSTEM_CONFIG_CODE_912
            Integer isClose = Integer
                .valueOf(paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_912.getCode()));
            //SYSTEM_CONFIG_CODE_912 为关闭可兑换区分，强制修改激励都为不可兑换
            if (Objects.equals(isClose, GeneralJudgeEnum.CONFIRM.getValue())) {
                tradeRecord.setIsExchange(GeneralJudgeEnum.NEGATIVE.getValue());
            }else{
                //交易的记录应该都是是否可兑换为1才行
                tradeRecord.setIsExchange(GeneralJudgeEnum.CONFIRM.getValue());
            }
        excitationTradeRecordService.save(tradeRecord);
    }

    private void initBizReduceBizProperties(UserExcitationReduceDTO reduceDTO) {
        //根据系统参数设置,是否是关闭可兑换区分配置 SYSTEM_CONFIG_CODE_912
        Integer isClose = Integer.valueOf(
            paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_912.getCode()));
        if (Objects.equals(isClose, GeneralJudgeEnum.CONFIRM.getValue())) {
            //SYSTEM_CONFIG_CODE_912 若是关闭,强制设置成不可兑换
            reduceDTO.setIsExchange(GeneralJudgeEnum.NEGATIVE.getValue());
        } else {
            reduceDTO.setIsExchange(
                Optional.ofNullable(reduceDTO.getIsExchange()).orElse(GeneralJudgeEnum.NEGATIVE.getValue()));
        }

        reduceDTO.setBizType(Optional.ofNullable(reduceDTO.getBizType()).orElse(reduceDTO.getTradeType()));
        reduceDTO.setBizId(Optional.ofNullable(reduceDTO.getBizId()).orElse(reduceDTO.getTargetId()));
    }

    @Override
    public Integer getUserExcitationRecordCount(String userId, String sourceId, String eventId,
        Integer operateType) {
        LambdaQueryWrapper<ExcitationTradeRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExcitationTradeRecord::getUserId, userId);
        queryWrapper.eq(ExcitationTradeRecord::getTargetId, sourceId);
        queryWrapper.eq(ExcitationTradeRecord::getEventId, eventId);
        queryWrapper.eq(ExcitationTradeRecord::getOperateType, operateType);
        long count = excitationTradeRecordService.count(queryWrapper);
        return Math.toIntExact(count == 0L ? 0 : count);
    }

    @Override
    public void returnUserProjectExchange(String projectId, String userId) {
        // 用户是否消费积分加入
        ExcitationTradeRecord tradeRecord = new ExcitationTradeRecord();
        tradeRecord.setUserId(userId).setTargetId(projectId)
            .setEventId(ExcitationEventEnum.PROJECT_CONSUME_STUDY.name())
            .setTradeType("project");
        List<ExcitationTradeRecord> recordList = excitationTradeRecordService.getListWithOutRefund(tradeRecord);
        if (CollectionUtils.isEmpty(recordList)) {
            // 无未返还的消费记录
            return;
        }
        // 返还消费记录
        recordList.forEach(data -> {
            IExcitationOperation excitationOperation = getOperationService(data.getExcitationId());
            BigDecimal current = excitationOperation.add(userId, data.getOperateNum(), data.getIsExchange());
            // 写入日志
            excitationTradeRecordService.save(new ExcitationTradeRecord()
                .setId(StringUtil.newId())
                .setUserId(userId)
                .setOperateType(ExcitationOperationEnum.INCREASE.getValue())
                .setExcitationId(data.getExcitationId())
                .setOperateNum(data.getOperateNum())
                .setCurrentNum(current)
                .setTradeType(data.getTradeType())
                .setTargetId(data.getTargetId())
                .setTargetName(data.getTargetName())
                .setIsExchange(data.getIsExchange())
                .setEventId(ExcitationEventEnum.PROJECT_CONSUME_STUDY_REFUND.name())
                .setRefundRecordId(data.getId())
                .setBizId(data.getBizId())
                .setBizType(data.getBizType())
                .setSummary("退出学习项目《" + data.getTargetName() + "》，退还交易")
            );
        });
    }

    private IExcitationOperation getOperationService(String excitationType) {
        IExcitationOperation excitationOperation;
        switch (ExcitationTypeEnum.get(excitationType)) {
            case INTEGRAL:
                excitationOperation = userIntegralService;
                break;
            case GOLD_COIN:
                excitationOperation = userGoldCoinService;
                break;
            case CREDIT:
                excitationOperation = userCreditService;
                break;
            case LEARN_TIME:
                excitationOperation = userLearnTimeService;
                break;
            default:
                throw new BusinessException(ExcitationErrorNoEnum.ERR_EXCITATION_TYPE);
        }
        return excitationOperation;
    }

    @Override
    public Object getCourseExchangeable(String excitationType) {
        return userIntegralService.getCourseExchangeable(excitationType);
    }

    @Override
    public Object getExcitation(String excitationType) {
        try {
            List<MyStatisticListDTO> personalInfo = userIntegralService.getPersonalInfo(true);
            if (CollectionUtils.isNotEmpty(personalInfo)) {
                for (MyStatisticListDTO myStatisticListDTO : personalInfo) {
                    String id;
                    if (Objects.equals(excitationType, "credit")) {
                        id = "exchangeableLearn" + StringUtils.capitalize(excitationType);
                    } else {
                        id = "exchangeable" + StringUtils.capitalize(excitationType);
                    }
                    if (Objects.equals(myStatisticListDTO.getId(), id)) {
                        return myStatisticListDTO.getValue();
                    }
                }
            }
            return BigDecimal.ZERO;
        } catch (Exception e) {
            log.error("获取个人可兑换激励数异常", e);
            return BigDecimal.ZERO;
        }
    }

    @Override
    public Map<String, Integer> getUserHasBeenGotTargetValue(UserTargetValueQuery query) {
        return userExcitationRecordService.getUserHasBeenGotTargetValue(query);
    }

    @Override
    public PageInfo<TargetValueGottenDetailClientDTO> queryUserGottenTargetValueDetailClientList(UserTargetValueQuery query) {
        return userExcitationRecordService.queryUserGottenTargetValueDetailsClient(query);
    }

    @Override
    public PageInfo<UserGottenTargetValueDetailDTO> queryUserGottenTargetValueDetailList(UserTargetValueQuery query) {
        return userExcitationRecordService.queryUserGottenTargetValueDetailList(query);
    }

    @Override
    public List<MyStatisticListDTO> getPersonalInfo(boolean checkSysConfig) throws IllegalAccessException {
        return userIntegralService.getPersonalInfo(checkSysConfig);
    }

    @Override
    public UserExcitationDataDTO getUserExcitationData(String userId) {
        UserExcitationDataDTO result = new UserExcitationDataDTO();
        UserIntegral dbUserIntegral = userIntegralService.getDataById(userId);
        result.setIntegral(dbUserIntegral.getAvailableNum());
        UserCredit userCredit = userCreditService.getDataById(userId);
        result.setCredit(userCredit.getRemainNum());
        UserLearnTime userLearnTime = userLearnTimeService.getDataById(userId);
        result.setLearnTime(userLearnTime.getAvailableNum());
        return result;
    }


    @Override
    public Map<String, UserExcitationStatisticDTO> getUserExcitationStatistic(Collection<String> userIds,
        Collection<String> targetIds) {
        return excitationTradeRecordService.getUserExcitationStatistic(userIds, targetIds);
    }


    @Override
    public PageInfo<UserExcitationRecordDTO> getUserProjectExcitationRecord(UserProjectExcitationRecordQuery query) {
        return userIntegralService.getUserProjectExcitationRecord(query);
    }
}
