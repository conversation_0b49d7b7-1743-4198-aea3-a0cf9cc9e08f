package com.wunding.learn.excitation.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.bean.BasePageQuery;
import com.wunding.learn.excitation.api.dto.UserExcitationStatisticDTO;
import com.wunding.learn.excitation.service.admin.dto.IntegralTradePageDTO;
import com.wunding.learn.excitation.service.admin.dto.UserGoldCoinBillDTO;
import com.wunding.learn.excitation.service.admin.query.UserGoldCoinBillsQuery;
import com.wunding.learn.excitation.service.client.dto.UserExcitationByTimeDTO;
import com.wunding.learn.excitation.service.model.ExcitationTradeRecord;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.springframework.scheduling.annotation.Async;

/**
 * <p> 激励交易记录 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi</a>
 * @since 2022-08-08
 */
public interface IExcitationTradeRecordService extends IService<ExcitationTradeRecord> {

    /**
     * 获取用户积分交易明细
     *
     * @param userId    用户id
     * @param type 查询类型：0 所有，-1 扣分，1 加分
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param query     查询条件
     * @return 用户积分交易明细分页数据
     */
    PageInfo<IntegralTradePageDTO> getIntegralTradePage(String userId, Integer type, Date startTime, Date endTime,
        BasePageQuery query);

    /**
     * 导出积分详情列表的数据
     *
     * @param userId        用户id
     * @param type 查询类型：0 所有，-1 扣分，1 加分
     * @param startTime
     * @param endTime
     * @param basePageQuery 查询条件 {@link  BasePageQuery}
     */
    @Async
    void export(String userId, Integer type, Date startTime, Date endTime, BasePageQuery basePageQuery);

    /**
     * 获取讲师积分交易明细
     *
     * @param userId 用户id
     * @param type 查询类型：0 所有，-1 扣分，1 加分
     * @param query  查询条件 {@link  BasePageQuery}
     * @return 用户积分交易明细分页数据
     */
    PageInfo<IntegralTradePageDTO> getLecturerIntegralTradePage(String userId, Integer type, BasePageQuery query);

    /**
     * 导出讲师积分详情列表的数据
     *
     * @param type 查询类型：0 所有，-1 扣分，1 加分
     * @param userId        用户id
     * @param basePageQuery 查询条件 {@link  BasePageQuery}
     */
    @Async
    void exportLecturerIntegralTrade(String userId, Integer type, BasePageQuery basePageQuery);

    /**
     * 是否完成激励交易
     *
     * @param tradeRecord 激励交易记录 {@link  ExcitationTradeRecord}
     */
    Boolean isFinishExcitationTrade(ExcitationTradeRecord tradeRecord);

    /**
     * 获取未返还的消费记录
     *
     * @param tradeRecord 激励交易记录 {@link  ExcitationTradeRecord}
     * @return 用户积分交易明细
     */
    List<ExcitationTradeRecord> getListWithOutRefund(ExcitationTradeRecord tradeRecord);

    /**
     * 获取用户金币账单
     *
     * @param query
     * @return
     */
    PageInfo<UserGoldCoinBillDTO> pageUserGoldCoinBills(UserGoldCoinBillsQuery query);

    @Async
    void exportGoldCoinBill(UserGoldCoinBillsQuery query);

    /**
     * 获取用户积分
     *
     * @param userId
     * @param dateList
     * @return
     */
    List<UserExcitationByTimeDTO> getUserIntegralByDate(String userId, List<Date> dateList);

    /**
     * 根据用户ID列表获取用户激励统计数据
     *
     * @param userIds   用户id集合
     * @param targetIds 目标id集合
     * @return 用户激励统计数据
     */
    Map<String, UserExcitationStatisticDTO> getUserExcitationStatistic(Collection<String> userIds,
        Collection<String> targetIds);
}
