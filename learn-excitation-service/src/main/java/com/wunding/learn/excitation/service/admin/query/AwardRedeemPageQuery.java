package com.wunding.learn.excitation.service.admin.query;

import com.wunding.learn.common.bean.BasePageQuery;
import io.swagger.v3.oas.annotations.Parameter;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.format.annotation.DateTimeFormat.ISO;

/**
 * 奖品兑换记录分页数据查询对象
 *
 * <AUTHOR>
 * @date 2022/10/28
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AwardRedeemPageQuery extends BasePageQuery {

    @Parameter(description = "奖品ID")
    private String awardId;

    @Parameter(description = "奖品名称")
    private String awardName;

    @Parameter(description = "兑换状态(0:未领取 1:已领取 2:已返还)")
    private Integer status;

    @Parameter(description = "兑换时间区间截止值")
    @DateTimeFormat(iso = ISO.DATE_TIME)
    private Date startTime;

    @Parameter(description = "兑换时间区间开始值")
    @DateTimeFormat(iso = ISO.DATE_TIME)
    private Date endTime;

    @Parameter(description = "用户主键字符串")
    private String userIds;

    @Parameter(description = "用户主键ID集合", hidden = true)
    private List<String> userIdList;

    @Parameter(description = "奖品ID集合", hidden = true)
    private Collection<String> awardIdCollection;

    @Parameter(description = "姓名/账号")
    private String userName;

    @Parameter(description = "管辖范围id", hidden = true)
    private Set<String> userManageAreaOrgId;

    /**
     * 当前登录用户ID
     */
    @Parameter(description = "当前登录用户ID", hidden = true)
    private String currentUserId;

    /**
     * 当前用户组织ID
     */
    @Parameter(description = "当前用户组织ID", hidden = true)
    private String currentOrgId;
}
