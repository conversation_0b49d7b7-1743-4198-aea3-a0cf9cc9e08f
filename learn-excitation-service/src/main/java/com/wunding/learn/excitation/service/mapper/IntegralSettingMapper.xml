<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.excitation.service.mapper.IntegralSettingMapper">

        <!-- 开启二级缓存 -->
        <!--
    <cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
    -->

        <!-- 使用缓存 -->
        <cache-ref namespace="com.wunding.learn.excitation.service.mapper.IntegralSettingMapper"/>

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.wunding.learn.excitation.service.model.IntegralSetting">
            <!--@Table sys_integral_setting-->
                    <id column="id" jdbcType="VARCHAR" property="id"/>
                    <result column="name" jdbcType="VARCHAR"
                            property="name"/>
                    <result column="category_type" jdbcType="TINYINT"
                            property="categoryType"/>
                    <result column="cycle" jdbcType="TINYINT"
                            property="cycle"/>
                    <result column="start_type" jdbcType="TINYINT"
                            property="startType"/>
                    <result column="frequency" jdbcType="TINYINT"
                            property="frequency"/>
                    <result column="integral_num" jdbcType="DECIMAL"
                            property="integralNum"/>
                    <result column="is_available" jdbcType="TINYINT"
                            property="isAvailable"/>
                    <result column="is_del" jdbcType="TINYINT"
                            property="isDel"/>
                    <result column="sort_no" jdbcType="INTEGER"
                            property="sortNo"/>
                    <result column="create_by" jdbcType="VARCHAR"
                            property="createBy"/>
                    <result column="create_time" jdbcType="TIMESTAMP"
                            property="createTime"/>
                    <result column="update_by" jdbcType="VARCHAR"
                            property="updateBy"/>
                    <result column="update_time" jdbcType="TIMESTAMP"
                            property="updateTime"/>
                    <result column="vail_words" jdbcType="INTEGER"
                            property="vailWords"/>
                    <result column="max_integral_num" jdbcType="DECIMAL"
                            property="maxIntegralNum"/>
                    <result column="is_contribution" jdbcType="TINYINT"
                            property="isContribution"/>
                    <result column="integral_type" jdbcType="VARCHAR"
                            property="integralType"/>
                    <result column="type" jdbcType="VARCHAR"
                            property="type"/>
                    <result column="times" jdbcType="INTEGER"
                            property="times"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, name, category_type, cycle, start_type, frequency, integral_num, is_available, is_del, sort_no, create_by, create_time, update_by, update_time, vail_words, max_integral_num, is_contribution, integral_type, type, times
        </sql>

</mapper>
