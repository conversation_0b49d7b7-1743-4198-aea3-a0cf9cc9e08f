package com.wunding.learn.excitation.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import com.wunding.learn.excitation.service.admin.query.AwardRedeemPageQuery;
import com.wunding.learn.excitation.service.client.dto.AwardRedeemRecordClientInfoDTO;
import com.wunding.learn.excitation.service.client.dto.ExchangeCommodityRecordVO;
import com.wunding.learn.excitation.service.client.query.ExcitationApiQuery;
import com.wunding.learn.excitation.service.model.AwardRedeemRecord;
import java.util.List;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 奖品兑换记录表 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">gao<PERSON></a>
 * @since 2022-08-08
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")
})
public interface AwardRedeemRecordMapper extends BaseMapper<AwardRedeemRecord> {

    List<ExchangeCommodityRecordVO> getExchangeCommodityRecord(@Param("params") ExcitationApiQuery query);

    /**
     * 兑换奖品详细信息
     *
     * @param id
     * @return
     */
    AwardRedeemRecordClientInfoDTO getExchangeCommodityInfo(String id);

    /**
     * 分页查询兑换记录
     *
     * @param query
     * @return {@link List}<{@link AwardRedeemRecord}>
     */
    List<AwardRedeemRecord> queryPage(AwardRedeemPageQuery query);
}
