package com.wunding.learn.excitation.service.client.query;

import com.wunding.learn.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.Parameter;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 此接口用于获取积分列表相关信息查询对象
 *
 * <AUTHOR>
 * @title: ExcitationApiQueryDTO
 * @projectName: learn
 * @description：
 * @date 2022/2/15 9:58
 */
@Data
public class ExcitationApiQuery extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 5411000951665265018L;

    /**
     * 激励类型 学分 = credit  金币 = goldCoin 积分 = integral 学时 = learnTime
     */
    @Parameter(hidden = true)
    private String excitationId;

    @Parameter(hidden = true)
    private String userId;

    /**
     * 激励目标对象id集合
     */
    @Parameter(hidden = true)
    private List<String> targetIds;

}
