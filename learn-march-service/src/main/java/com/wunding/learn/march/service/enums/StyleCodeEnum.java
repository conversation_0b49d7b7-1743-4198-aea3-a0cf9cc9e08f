package com.wunding.learn.march.service.enums;

import com.wunding.learn.common.annotation.EnumI18nProperty;
import com.wunding.learn.common.i18n.util.EnumI18n;
import lombok.Getter;

/**
 * <p> 游戏样式模板表 枚举
 *
 * <AUTHOR> href="mailto:<EMAIL>">liangzm</a>
 * @since 2025/6/20
 */
public enum StyleCodeEnum implements EnumI18n {

    HOME_BACKGROUND(1, "首页背景图", "用于各处背景展示"),
    LOGIN_TITLE_BACKGROUND(2, "标题背景（登录页）", "登录页标题背景，含slogan"),
    HOME_TITLE_BACKGROUND(3, "标题背景", "首页标题背景，不含slogan"),
    HOME_SILHOUETTE(4, "首页背景剪影图", "首页背景号角人物图"),
    OTHER_PAGE_BACKGROUND(5, "其他页背景图", "除首页外所有页面通用背景"),
    CANCEL_BUTTON(6, "取消按钮", "取消按钮"),
    KNOW_COMPLETED_LEVEL(7, "我知道了（完成关卡）", "关卡完成时的弹窗中，关卡完成提示"),
    CLOSE_BUTTON(8, "关闭", "关闭按钮"),
    KNOW_UPLOAD_STEPS(9, "我知道了（上传步数）", "上传步数提示页中的按钮"),
    OK_BUTTON(10, "好的", ""),
    REFRESH_STEPS(11, "刷新步数", "步数上传完成后的弹窗提示语"),
    VIEW_STORY(12, "去看故事", "完成关卡后，点击去看故事的按钮"),
    CONFIRM_FIRST_LOGIN(13, "确定（首次登录提示语）", "首次登录的游戏说明提示弹窗中的按钮"),
    UPLOAD_NOW(14, "立即上传", "步数上传弹窗的立即上传按钮"),
    TODAY_MILEAGE(15, "今日里程", "今日里程的标题logo"),
    MY_BUTTON(16, "我的按钮", "进入“我的”页面的入口按钮"),
    RANKING_BUTTON(17, "排行榜按钮", "进入“排行榜”页面的入口按钮"),
    ACTIVITY_DESCRIPTION(18, "活动说明", "活动说明的标题logo"),
    UPLOAD_YESTERDAY_ACTIVITY(19, "上传昨日运动按钮", "上传昨日运动按钮的标题logo"), // 修正了此处的描述
    LEARNING_ZONE_BUTTON(20, "学习专区按钮", "学习专区的标题logo"),
    PROGRESS_BAR_PERSON(21, "进度条小人", "首页团队进度条的小人"),
    FLAG_LOCKED(22, "旗帜（未解锁)", "我的，故事中的旗帜"),
    FLAG_UNLOCKED(23, "旗帜（已解锁)", "我的，故事中的旗帜"),
    LEARNING_ZONE_BACKGROUND(24, "背景（学习专区)", "学习专区的顶部背景叠层"),
    MY_PAGE_BACKGROUND(25, "背景（“我的”页)", "“我的”页的顶部背景叠层"),
    OTHER_PAGE_SILHOUETTE(26, "其他页背景剪影图", "除首页外的其他页面的剪影背景"),
    MAP_ARROW_10_LOCKED(27, "地点连接图10（未完成)", Constants.STRING),
    MAP_ARROW_10_UNLOCKED(28, "地点连接图10（已完成)", Constants.STRING),
    MAP_ARROW_11_LOCKED(29, "地点连接图11（未完成)", Constants.STRING),
    MAP_ARROW_11_UNLOCKED(30, "地点连接图11（已完成)", Constants.STRING),
    MAP_ARROW_1_LOCKED(31, "地点连接图1（未完成)", Constants.STRING),
    MAP_ARROW_1_UNLOCKED(32, "地点连接图1（已完成)", Constants.STRING),
    MAP_ARROW_2_LOCKED(33, "地点连接图2（未完成)", Constants.STRING),
    MAP_ARROW_2_UNLOCKED(34, "地点连接图2（已完成)", Constants.STRING),
    MAP_ARROW_3_LOCKED(35, "地点连接图3（未完成)", Constants.STRING),
    MAP_ARROW_3_UNLOCKED(36, "地点连接图3（已完成)", Constants.STRING),
    MAP_ARROW_4_LOCKED(37, "地点连接图4（未完成)", Constants.STRING),
    MAP_ARROW_4_UNLOCKED(38, "地点连接图4（已完成)", Constants.STRING),
    MAP_ARROW_5_LOCKED(39, "地点连接图5（未完成)", Constants.STRING),
    MAP_ARROW_5_UNLOCKED(40, "地点连接图5（已完成)", Constants.STRING),
    MAP_ARROW_6_LOCKED(41, "地点连接图6（未完成)", Constants.STRING),
    MAP_ARROW_6_UNLOCKED(42, "地点连接图6（已完成)", Constants.STRING),
    MAP_ARROW_7_LOCKED(43, "地点连接图7（未完成)", Constants.STRING),
    MAP_ARROW_7_UNLOCKED(44, "地点连接图7（已完成)", Constants.STRING),
    MAP_ARROW_8_LOCKED(45, "地点连接图8（未完成)", Constants.STRING),
    MAP_ARROW_8_UNLOCKED(46, "地点连接图8（已完成)", Constants.STRING),
    MAP_ARROW_9_LOCKED(47, "地点连接图9（未完成)", Constants.STRING),
    MAP_ARROW_9_UNLOCKED(48, "地点连接图9（已完成)", Constants.STRING),
    LEVEL_LOCKED_HALO(49, "关卡未解锁光环", "首页地图地点的原点光环"),
    LEVEL_UNLOCKED_HALO(50, "关卡解锁光环", "首页地图地点的原点光环"),
    LEVEL_UNLOCKED_FLAG(51, "关卡解锁旗帜", "首页地图地点的旗帜"),
    POPUP_HEADER_BACKGROUND(52, "弹窗头部背景", "说明等弹窗的头部背景"),
    LEVEL_COMPLETED_BACKGROUND(53, "关卡完成背景", "关卡完成的恭喜弹窗的背景"),
    STORY_POPUP_BACKGROUND(54, "故事弹窗背景", "故事弹窗的背景"),
    FIRST_LOGIN_POPUP_BACKGROUND(55, "提示弹窗背景)", "首次登录的游戏说明提示弹窗中的顶部背景)"),
    INSTRUCTION_TITLE_FLAG(56, "说明标题旗帜)", "说明弹窗的标题旁的logo，每个说明配置一个旗帜"),
    MY_PAGE_TITLE_BACKGROUND(57, "我的页标题背景", "我的页的标题背景，显示在文字下方"),
    MY_PAGE_CARD_BACKGROUND(58, "我的页卡片背景", "我的页的卡片背景"),
    LEARNING_ZONE_TITLE(59, "学习专区标题", "学习专区的横幅");

    @Getter
    private final int styleCode;

    @EnumI18nProperty
    private final String name;

    @EnumI18nProperty
    private final String description;



    StyleCodeEnum(int styleCode, String name, String description) {
        this.styleCode = styleCode;
        this.name = name;
        this.description = description;
    }

    public String getName() {
        return i18n(name(), name);
    }

    public String getDescription() {
        return i18n(name(), description);
    }

    /**
     * 根据 styleCode 获取对应的枚举值
     *
     * @param styleCode styleCode
     * @return 对应的枚举值，如果找不到则返回 null
     */
    public static StyleCodeEnum getByStyleCode(int styleCode) {
        for (StyleCodeEnum style : values()) {
            if (style.getStyleCode() == styleCode) {
                return style;
            }
        }
        return null;
    }

    private static class Constants {

        public static final String STRING = "地图链接箭头";
    }
}
