package com.wunding.learn.march.service.biz.impl;

import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.constant.march.MarchErrorNoEnum;
import com.wunding.learn.common.enums.exam.IsTrainEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.string.StringPool;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.dto.AbstractExportDataDTO;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.forum.api.dto.MarchCountCommentDTO;
import com.wunding.learn.forum.api.dto.MarchCountPostCommentQuery;
import com.wunding.learn.forum.api.dto.MarchForumPostApiDTO;
import com.wunding.learn.forum.api.dto.MarchPostCommentApiDTO;
import com.wunding.learn.forum.api.dto.MarchPostCommentApiDTO.MarchPostSubCommentApiDTO;
import com.wunding.learn.forum.api.dto.MarchPostCommentDTO;
import com.wunding.learn.forum.api.dto.MarchPostInfoDTO;
import com.wunding.learn.forum.api.dto.MarchPostReplyDTO;
import com.wunding.learn.forum.api.dto.MarchPostTopStatusDTO;
import com.wunding.learn.forum.api.dto.MarchSavePostDTO;
import com.wunding.learn.forum.api.dto.PostCommentBaseDTO;
import com.wunding.learn.forum.api.dto.PostDelStatusDTO;
import com.wunding.learn.forum.api.dto.PostListDTO;
import com.wunding.learn.forum.api.query.MarchForumPostApiQuery;
import com.wunding.learn.forum.api.query.MarchPostCommentQuery;
import com.wunding.learn.forum.api.query.PostQuery;
import com.wunding.learn.forum.api.service.ForumFeign;
import com.wunding.learn.march.service.admin.dto.MarchPostDelStatusDTO;
import com.wunding.learn.march.service.admin.dto.MarchPostListDTO;
import com.wunding.learn.march.service.admin.query.MarchPostQuery;
import com.wunding.learn.march.service.biz.IMarchForumBiz;
import com.wunding.learn.march.service.model.MarchTeamUser;
import com.wunding.learn.march.service.service.IMarchService;
import com.wunding.learn.march.service.service.IMarchTeamUserService;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

/**
 * <p> 游戏-话题业务层实现
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2024-06-17
 */
@Slf4j
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Component("marchForumBiz")
public class IMarchForumBizImpl implements IMarchForumBiz {

    private final ForumFeign forumFeign;
    private final IMarchTeamUserService teamUserService;
    private final ExportComponent exportComponent;
    private final IMarchService marchService;

    @Override
    public PageInfo<MarchPostListDTO> findPostListByPage(
        MarchPostQuery marchPostQuery) {
        PostQuery postQuery = new PostQuery();
        BeanUtils.copyProperties(marchPostQuery, postQuery);
        postQuery.setSectionId(marchPostQuery.getCheckpointId());
        postQuery.setProjectId(marchPostQuery.getMarchId());
        postQuery.setIsTrain(IsTrainEnum.MARCH.getValue());
        PageInfo<PostListDTO> pageInfo = forumFeign.findMarchPostListByPage(postQuery);
        List<PostListDTO> infoList = pageInfo.getList();
        if (CollectionUtils.isEmpty(infoList)) {
            return PageInfo.emptyPageInfo();
        }

        Set<String> userId = infoList.stream().map(PostListDTO::getCreateBy).collect(Collectors.toSet());

        Map<String, String> userTeamNameMap = teamUserService.getUserTeamNameMap(userId, marchPostQuery.getMarchId());
        List<MarchPostListDTO> resultList = infoList.stream().map(item -> {
            MarchPostListDTO dto = new MarchPostListDTO();
            BeanUtils.copyProperties(item, dto);
            dto.setTeamName(userTeamNameMap.getOrDefault(item.getCreateBy(), StringPool.EMPTY));
            return dto;
        }).collect(Collectors.toList());

        PageInfo<MarchPostListDTO> result = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, result);
        result.setList(resultList);
        return result;
    }

    @Override
    public void exportData(MarchPostQuery queryDTO) {
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IMarchForumBiz, MarchPostListDTO>(
            queryDTO) {

            @Override
            protected IMarchForumBiz getBean() {
                return SpringUtil.getBean("marchForumBiz", IMarchForumBiz.class);
            }

            @Override
            protected PageInfo<MarchPostListDTO> getPageInfo() {
                return getBean().findPostListByPage((MarchPostQuery) queryDTO);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.MarchPost;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.MarchPost.getType();
            }

            @Override
            protected void afterCreateBeanMap(Map<String, Object> map) {
                Object isTop = map.get("isTop");
                Object isEssence = map.get("isEssence");
                Object isFavourite = map.get("isFavourite");
                StringBuilder status = new StringBuilder();
                StringBuilder state = new StringBuilder();
                if (Objects.nonNull(isTop) && isTop.equals(1)) {
                    status.append(" [置顶]");
                    state.append(" 置顶");
                }
                if (Objects.nonNull(isEssence) && isEssence.equals(1)) {
                    status.append(" [推荐]");
                    state.append(" 推荐");
                }
                if (Objects.nonNull(isFavourite) && isFavourite.equals(1)) {
                    status.append(" [热门]");
                    state.append(" 热门");
                }
                Object title = map.get("title");
                map.put("title", title.toString() + status);
                Object teamName = map.get("teamName");
                map.put("teamName", ObjectUtils.isEmpty(teamName) ? "全部" : teamName);
                map.put("state", state);
                String postTypeConstant = "postType";
                Object postType = map.get(postTypeConstant);
                if (Objects.nonNull(postType) && postType.equals(1)) {
                    map.put(postTypeConstant, "主题帖");
                } else if (Objects.nonNull(postType) && postType.equals(2)) {
                    map.put(postTypeConstant, "投票帖");
                }
            }
        };

        exportComponent.exportRecord(exportDataDTO);
    }


    @Override
    public boolean delOrRestorePost(MarchPostDelStatusDTO marchPostDelStatusDTO) {
        PostDelStatusDTO requestBody = new PostDelStatusDTO();
        BeanUtils.copyProperties(marchPostDelStatusDTO, requestBody);
        requestBody.setPostIdList(marchPostDelStatusDTO.getPostIdList());
        return forumFeign.delOrRestorePost(requestBody);
    }

    @Override
    public MarchPostInfoDTO getPostDetailInfo(String id, String marchId) {
        MarchPostInfoDTO post = forumFeign.findMarchPostInfoByPostId(id);
        Map<String, String> teamNameMap = teamUserService.getUserTeamNameMap(List.of(post.getCreateBy()), marchId);
        post.setTeamName(teamNameMap.getOrDefault(post.getCreateBy(), StringPool.EMPTY));
        return post;
    }

    @Override
    public PageInfo<MarchPostCommentDTO> getPostCommentList(MarchPostCommentQuery postCommentQuery) {
        PageInfo<MarchPostCommentDTO> postCommentList = forumFeign.getMarchPostCommentList(postCommentQuery);
        List<MarchPostCommentDTO> infoList = postCommentList.getList();
        if (CollectionUtils.isEmpty(infoList)) {
            return PageInfo.emptyPageInfo();
        }

        // 浅拷贝列表里面元素改变一起改变
        List<PostCommentBaseDTO> cloneList = new ArrayList<>(infoList.size());
        for (MarchPostCommentDTO marchPostCommentApiDTO : infoList) {
            cloneList.addAll(marchPostCommentApiDTO.getPostComments());
            cloneList.add(marchPostCommentApiDTO);
        }
        handleTeamName(postCommentQuery.getMarchId(), cloneList);

        return postCommentList;
    }

    @Override
    public boolean topOrCancelPost(MarchPostTopStatusDTO postTopStatusDTO) {
        return forumFeign.marchTopOrCancelPost(postTopStatusDTO);
    }

    @Override
    public PageInfo<MarchCountCommentDTO> countComment(MarchCountPostCommentQuery query) {
        String teamId = query.getTeamId();
        String userIdStr = query.getUserIdStr();
        if (StringUtils.isNotEmpty(userIdStr)) {
            String[] userIdArray = userIdStr.split(",");
            List<String> userIdQueryList = Arrays.asList(userIdArray);
            // 存在选了用户id则进行
            query.setUserIdList(userIdQueryList);
        }
        if (StringUtils.isNotEmpty(teamId)) {
            List<MarchTeamUser> teamUsers = teamUserService.lambdaQuery().eq(MarchTeamUser::getMarchId, query.getMarchId())
                .eq(MarchTeamUser::getTeamId, query.getTeamId())
                .list();
            List<String> userIds = teamUsers.stream().map(MarchTeamUser::getUserId).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(query.getUserIdList())) {
                userIds.retainAll(query.getUserIdList());
            }
            // 如果团队都没有成员则直接返回即可
            if (CollectionUtils.isEmpty(userIds)) {
                return PageInfo.emptyPageInfo();
            }
            query.setUserIdList(userIds);
        }

        PageInfo<MarchCountCommentDTO> pageInfo = forumFeign.marchCountComment(query);

        List<MarchCountCommentDTO> infoList = pageInfo.getList();
        if (CollectionUtils.isEmpty(infoList)) {
            return PageInfo.emptyPageInfo();
        }
        Set<String> userIds = infoList.stream().map(MarchCountCommentDTO::getCommentBy).collect(Collectors.toSet());
        Map<String, String> userTeamNameMap = teamUserService.getUserTeamNameMap(userIds,
            query.getMarchId());

        infoList.forEach(item -> item.setTeamName(userTeamNameMap.getOrDefault(item.getCommentBy(), StringPool.EMPTY)));
        return pageInfo;
    }

    @Override
    public void exportDataCountComment(MarchCountPostCommentQuery countPostCommentQuery) {

        // 构建并实现接口类的方法 返回导出的接口类对象
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IMarchForumBiz, MarchCountCommentDTO>(
            countPostCommentQuery) {

            @Override
            protected IMarchForumBiz getBean() {
                return SpringUtil.getBean("marchForumBiz", IMarchForumBiz.class);
            }

            @Override
            protected PageInfo<MarchCountCommentDTO> getPageInfo() {
                return getBean().countComment(countPostCommentQuery);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.MarchPostCountComment;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.MarchPostCountComment.getType();
            }

        };

        exportComponent.exportRecord(exportDataDTO);

    }

    @Override
    public void savePost(MarchSavePostDTO marchSavePostDTO) {
        marchSavePostDTO.setIsTrain(IsTrainEnum.MARCH.getValue());
        marchSavePostDTO.setPostType(1);
        forumFeign.saveMarchPost(marchSavePostDTO);
    }

    @Override
    public PageInfo<MarchForumPostApiDTO> getPostPageByCheckpointId(MarchForumPostApiQuery forumPostApiQuery) {
        marchService.checkApiMarch(forumPostApiQuery.getMarchId());
        PageInfo<MarchForumPostApiDTO> result = forumFeign.getPostPageByCheckpointId(forumPostApiQuery);
        List<MarchForumPostApiDTO> infoList = result.getList();
        if (CollectionUtils.isEmpty(infoList)) {
            return PageInfo.emptyPageInfo();
        }
        List<String> userIds = infoList.stream().map(MarchForumPostApiDTO::getCreateBy).collect(Collectors.toList());

        Map<String, String> userTeamNameMap = teamUserService.getUserTeamNameMap(userIds,
            forumPostApiQuery.getMarchId());
        infoList.forEach(item -> item.setTeamName(userTeamNameMap.getOrDefault(item.getCreateBy(), StringPool.EMPTY)));

        return result;
    }

    @Override
    public MarchForumPostApiDTO getPostDetailByPostId(String postId, String marchId) {
        MarchForumPostApiDTO result = forumFeign.getPostDetailByPostId(postId);
        if (Objects.isNull(result)) {
            return null;
        }
        Map<String, String> userTeamNameMap = teamUserService.getUserTeamNameMap(List.of(result.getCreateBy()),
            marchId);
        result.setTeamName(userTeamNameMap.getOrDefault(result.getCreateBy(), StringPool.EMPTY));
        return result;
    }

    @Override
    public Long addOrUpdateMarchPostVote(String postId) {
        return forumFeign.addOrUpdateMarchPostVote(postId);
    }

    @Override
    public void deletePostByPostId(String postIds) {
        String[] strIds = postIds.split(",");
        List<String> postIdList = Arrays.asList(strIds);
        if (CollectionUtils.isEmpty(postIdList)) {
            return;
        }
        forumFeign.deletePostByPostId(postIdList);
    }

    @Override
    public void updatePost(MarchSavePostDTO updatePostDTO) {
        forumFeign.updateMarchPost(updatePostDTO);
    }

    @Override
    public Long addOrUpdateCommentVote(String commentId) {
        return forumFeign.addOrUpdateMarchCommentVote(commentId);
    }

    @Override
    public void deleteMarchPostCommentByCommentId(String commentId) {
        forumFeign.deleteMarchPostCommentByCommentId(commentId);
    }

    @Override
    public void saveMarchPostReply(MarchPostReplyDTO postReplyDTO) {
        postReplyDTO.setParentId(StringPool.ZERO);
        forumFeign.saveMarchPostReply(postReplyDTO);
    }

    @Override
    public PageInfo<MarchPostCommentApiDTO> getApiMarchPostComment(MarchPostCommentQuery postCommentQuery) {
        PageInfo<MarchPostCommentApiDTO> pageInfo = forumFeign.getApiMarchPostComment(postCommentQuery);
        List<MarchPostCommentApiDTO> infoList = pageInfo.getList();
        if (CollectionUtils.isEmpty(infoList)) {
            PageInfo.emptyPageInfo();
        }
        // 浅拷贝列表里面元素改变一起改变
        List<PostCommentBaseDTO> cloneList = new ArrayList<>(infoList.size());
        for (MarchPostCommentApiDTO marchPostCommentApiDTO : infoList) {
            cloneList.addAll(marchPostCommentApiDTO.getPostComments());
            cloneList.add(marchPostCommentApiDTO);
        }
        handleTeamName(postCommentQuery.getMarchId(), cloneList);
        return pageInfo;
    }

    @Override
    public void savePostSubCommentReply(MarchPostReplyDTO marchPostSubReplyDTO) {
        if (StringUtils.isEmpty(marchPostSubReplyDTO.getParentId())) {
            throw new BusinessException(MarchErrorNoEnum.ERR_COMMON_PARAMS);
        }
        // 二级评论不保存图片
        marchPostSubReplyDTO.setImgList(null);
        forumFeign.savePostSubCommentReply(marchPostSubReplyDTO);
    }

    @Override
    public MarchPostCommentApiDTO detailComment(String commentId, String marchId) {
        MarchPostCommentApiDTO dto = forumFeign.detailMarchComment(commentId);
        if (Objects.isNull(dto)) {
            return null;
        }

        Map<String, String> userTeamNameMap = teamUserService.getUserTeamNameMap(List.of(dto.getCommentBy()), marchId);
        dto.setTeamName(userTeamNameMap.getOrDefault(dto.getCommentBy(), StringPool.EMPTY));

        return dto;
    }

    @Override
    public PageInfo<MarchPostSubCommentApiDTO> getMarchSubComment(MarchPostCommentQuery postSubCommentQuery) {
        PageInfo<MarchPostSubCommentApiDTO> pageInfo = forumFeign.getMarchSubComment(postSubCommentQuery);
        List<MarchPostSubCommentApiDTO> infoList = pageInfo.getList();
        if (CollectionUtils.isEmpty(infoList)) {
            PageInfo.emptyPageInfo();
        }

        handleTeamName(postSubCommentQuery.getMarchId(), infoList);

        return pageInfo;
    }

    @Override
    public void delOrRestorePostComment(String commentId, Integer status) {
        forumFeign.delOrRestorePostComment(commentId, status);
    }

    /**
     * 处理团队名称
     *
     * @param marchId  游戏id
     * @param infoList 结果列表
     */
    private void handleTeamName(String marchId, List<? extends PostCommentBaseDTO> infoList) {
        List<String> userIds = infoList.stream().map(PostCommentBaseDTO::getCommentBy).collect(Collectors.toList());

        Map<String, String> userTeamNameMap = teamUserService.getUserTeamNameMap(userIds, marchId);
        infoList.forEach(item -> item.setTeamName(userTeamNameMap.getOrDefault(item.getCommentBy(), StringPool.EMPTY)));
    }
}

