package com.wunding.learn.march.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: TaskStatisticalListDTO
 * @projectName: mlearn
 * @description：
 * @date 2022/8/31 10:47
 */
@Data
@Schema(name = "TaskStatisticalListDTO", description = "游戏统计任务分列表返回对象")
public class TaskStatisticalListDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键id
     */
    @Schema(description = "主键id")
    private String id;


    /**
     * 类型(1:系统产生,2:手工录入)
     */
    @Schema(description = "类型(1:系统产生,2:手工录入)")
    private Integer type;


    /**
     * 任务分摘要 summary
     */
    @Schema(description = "任务分摘要 summary")
    private String summary;


    /**
     * 分数
     */
    @Schema(description = "分数")
    private Long score;


    /**
     * 发生时间
     */
    @Schema(description = "发生时间 ")
    private Date happenTime;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;


    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;
}
