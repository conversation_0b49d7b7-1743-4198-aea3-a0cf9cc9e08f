package com.wunding.learn.march.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import com.wunding.learn.march.service.admin.dto.TeamUserListDTO;
import com.wunding.learn.march.service.model.MarchTeamUser;
import java.util.Collection;
import java.util.List;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 游戏团队用户表 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">liyihui</a>
 * @since 2022-08-19
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")
})
public interface MarchTeamUserMapper extends BaseMapper<MarchTeamUser> {

    /**
     * 获取用户团队名称映射
     *
     * @param userIds 用户ids
     * @param marchId 游戏id
     * @return {@link List }<{@link TeamUserListDTO }>
     */
    List<TeamUserListDTO> getUserTeamNameMap(Collection<String> userIds, String marchId);

    /**
     * 选择用户列表
     *
     * @param teamId     团队id
     * @param userIdList 用户id列表
     * @return {@link MarchTeamUser }
     */
    List<MarchTeamUser> selectUserList(String teamId, List<String> userIdList);
}
