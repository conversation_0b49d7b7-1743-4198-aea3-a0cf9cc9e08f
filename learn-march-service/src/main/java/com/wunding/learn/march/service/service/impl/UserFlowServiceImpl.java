package com.wunding.learn.march.service.service.impl;

import com.wunding.learn.march.service.model.MarchUserFlow;
import com.wunding.learn.march.service.mapper.UserFlowMapper;
import com.wunding.learn.march.service.service.IMarchUserFlowService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p> 游戏用户流程记录表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
    * @since 2024-06-15
 */
@Slf4j
@Service("userFlowService")
public class UserFlowServiceImpl extends ServiceImpl<UserFlowMapper, MarchUserFlow> implements IMarchUserFlowService {

}
