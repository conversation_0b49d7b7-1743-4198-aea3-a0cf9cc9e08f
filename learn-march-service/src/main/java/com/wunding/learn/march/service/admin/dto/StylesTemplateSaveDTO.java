package com.wunding.learn.march.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @title: StylesTemplateSaveDTO
 * @projectName: mlearn
 * @description：
 * @date 2022/8/22 15:08
 */
@Data
@Schema(name = "StylesTemplateSaveDTO", description = "样式模板修改图片保存对象")
public class StylesTemplateSaveDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 样式模板id
     */
    @Schema(description = "样式模板id", required = true)
    @NotBlank(message = "样式模板id不能为空")
    @Length(max = 36, message = "样式模板id长度不能超过36")
    private String id;

    /**
     * 样式模板图片地址
     */
    @Schema(description = "样式模板图片地址", required = true)
    @NotBlank(message = "样式模板图片地址不能为空")
    private String imgUrl;

    /**
     * 样式模板图片地址
     */
    @Schema(description = "样式模板图片名称", required = true)
    @NotBlank(message = "样式模板图片名称不能为空")
    private String imgName;


}
