package com.wunding.learn.march.service.biz.impl;

import static org.springframework.cache.interceptor.SimpleKeyGenerator.generateKey;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.wunding.learn.common.constant.march.MarchErrorNoEnum;
import com.wunding.learn.common.enums.march.MarchStylesTemplateEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.exception.handler.ApiAssert;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.constant.ImageBizType;
import com.wunding.learn.file.api.dto.AbstractExportDataDTO;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.file.api.dto.SaveFileDTO;
import com.wunding.learn.file.api.service.FileFeign;
import com.wunding.learn.march.service.admin.dto.CheckpointQueryDTO;
import com.wunding.learn.march.service.admin.dto.StylesTemplateListDTO;
import com.wunding.learn.march.service.admin.dto.StylesTemplateSaveDTO;
import com.wunding.learn.march.service.biz.IMarchStylesTemplateBiz;
import com.wunding.learn.march.service.client.dto.MarchStyleTemplateDTO;
import com.wunding.learn.march.service.constsnts.MarchConstants;
import com.wunding.learn.march.service.enums.StyleCodeEnum;
import com.wunding.learn.march.service.model.StylesTemplate;
import com.wunding.learn.march.service.service.IMarchService;
import com.wunding.learn.march.service.service.IMarchStylesTemplateService;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * <p> 样式管理业务层实现
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2024-06-19
 */
@Slf4j
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Component("marchStylesTemplateBiz")
public class IMarchStylesTemplateBizImpl implements IMarchStylesTemplateBiz {

    private final IMarchStylesTemplateService marchStylesTemplateService;

    private final FileFeign fileFeign;

    private final IMarchService marchService;

    private final ExportComponent exportComponent;

    @Override
    public Boolean haveTemplate(String marchId) {
        return marchStylesTemplateService.count(new LambdaQueryWrapper<StylesTemplate>()
            .eq(StylesTemplate::getMarchId, marchId)) > 0;
    }

    @Override
    public List<StylesTemplate> styleValue(String marchId) {
        return marchStylesTemplateService.list(new LambdaQueryWrapper<StylesTemplate>()
            .eq(StylesTemplate::getMarchId, marchId));
    }

    @Override
    public MarchStyleTemplateDTO getMarchStyleTemplate(String marchId) {
        List<StylesTemplate> stylesTemplates = marchStylesTemplateService.list(new LambdaQueryWrapper<StylesTemplate>()
            .eq(StylesTemplate::getMarchId, marchId));

        if (CollectionUtils.isEmpty(stylesTemplates)) {
            throw new BusinessException(MarchErrorNoEnum.ERR_MARCH_STYLE_NOT_EXIST);
        }

        long maxUpdateTime = 0L;
        for (StylesTemplate stylesTemplate : stylesTemplates) {
            //如果是上传的
            if (Objects.equals(stylesTemplate.getImgUpdate(), MarchConstants.STYLES_TEMPLATE_IMG_IS_UPDATE)) {
                stylesTemplate
                    .setImgUrl(
                        fileFeign.getImageUrl(stylesTemplate.getId(), ImageBizType.MARCH_GAME_STYLES_TEMPLATE.name()));
            } else {
                stylesTemplate.setImgUrl(fileFeign.getFileUrl(stylesTemplate.getImgUrl()));
            }

            long timeStamp = stylesTemplate.getUpdateTime().getTime();
            maxUpdateTime = Math.max(maxUpdateTime, timeStamp);
        }

        Map<Object, StylesTemplate> styleCodeMap = new HashMap<>(stylesTemplates.size());
        Map<String, String> resultMap = new HashMap<>(stylesTemplates.size());
        for (StylesTemplate stylesTemplate : stylesTemplates) {
            Object key = generateKey(stylesTemplate.getImgCode(), stylesTemplate.getStyleCode());
            styleCodeMap.put(key, stylesTemplate);
        }

        //一个循环把需要插入数据库的都设值
        for (Entry<Object, StylesTemplate> entry : styleCodeMap.entrySet()) {
            String mapKey = MarchStylesTemplateEnum.getUrlNameBy(entry.getKey());
            resultMap.put(mapKey, entry.getValue().getImgUrl());
        }
        return new MarchStyleTemplateDTO().setTemplateDetailList(resultMap).setTimeStamp(maxUpdateTime);
    }

    @Override
    public PageInfo<StylesTemplateListDTO> list(CheckpointQueryDTO checkpointQueryDTO) {
        PageInfo<StylesTemplate> selectPageInfo = PageMethod.startPage(checkpointQueryDTO.getPageNo(),
                checkpointQueryDTO.getPageSize())
            .doSelectPageInfo(() ->
                marchStylesTemplateService.list(new LambdaQueryWrapper<StylesTemplate>()
                    .eq(StylesTemplate::getMarchId, checkpointQueryDTO.getMarchId())
                    .orderByAsc(StylesTemplate::getStyleCode))
            );

        List<StylesTemplateListDTO> dtoList = selectPageInfo.getList().stream().map(stylesTemplate -> {
            StylesTemplateListDTO stylesTemplateListDTO = new StylesTemplateListDTO();
            BeanUtils.copyProperties(stylesTemplate, stylesTemplateListDTO);
            //如果是上传的
            if (Objects.equals(stylesTemplate.getImgUpdate(), MarchConstants.STYLES_TEMPLATE_IMG_IS_UPDATE)) {
                stylesTemplateListDTO
                    .setImgUrl(
                        fileFeign.getImageUrl(stylesTemplate.getId(), ImageBizType.MARCH_GAME_STYLES_TEMPLATE.name()));
                stylesTemplateListDTO.setDownUrl(
                    fileFeign.getImageUrl(stylesTemplate.getId(), ImageBizType.MARCH_GAME_STYLES_TEMPLATE.name()));
            } else {
                stylesTemplateListDTO.setImgUrl(fileFeign.getFileUrl(stylesTemplate.getImgUrl()));
                stylesTemplateListDTO.setDownUrl(fileFeign.getFileUrl(stylesTemplate.getImgUrl()));
            }
            StyleCodeEnum styleCodeEnum = StyleCodeEnum.getByStyleCode(stylesTemplateListDTO.getStyleCode());
            if (styleCodeEnum != null) {
                stylesTemplateListDTO.setName(styleCodeEnum.getName());
                stylesTemplateListDTO.setDescription(styleCodeEnum.getDescription());
            }
            return stylesTemplateListDTO;
        }).collect(Collectors.toList());

        PageInfo<StylesTemplateListDTO> pageInfo = new PageInfo<>();
        BeanUtils.copyProperties(selectPageInfo, pageInfo);
        pageInfo.setList(dtoList);
        return pageInfo;
    }

    @Override
    public void create(String marchId, int templateCode) {
        marchStylesTemplateService.create(marchId, templateCode);
    }

    @Override
    public Boolean update(StylesTemplateSaveDTO stylesTemplateSaveDTO) {
        StylesTemplate stylesTemplate = marchStylesTemplateService.getById(stylesTemplateSaveDTO.getId());
        ApiAssert.check(stylesTemplate == null, MarchErrorNoEnum.ERR_MARCH_STYLE_NOT_EXIST);
        fileFeign.deleteImageByBizIdAndBizType(stylesTemplateSaveDTO.getId(),
            ImageBizType.MARCH_GAME_STYLES_TEMPLATE.name());
        SaveFileDTO saveFileDTO = fileFeign
            .saveImage(stylesTemplateSaveDTO.getId(), ImageBizType.MARCH_GAME_STYLES_TEMPLATE.name(),
                stylesTemplateSaveDTO.getImgName(), stylesTemplateSaveDTO.getImgUrl());
        stylesTemplate.setImgUrl(saveFileDTO.getUrl());
        stylesTemplate.setUpdateTime(new Date());
        stylesTemplate.setImgUpdate(MarchConstants.STYLES_TEMPLATE_IMG_IS_UPDATE);
        return marchStylesTemplateService.updateById(stylesTemplate);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStyleTemplate(String marchId, Integer templateCode) {

        //先删除原有样式模板
        marchStylesTemplateService.remove(new LambdaQueryWrapper<StylesTemplate>()
            .eq(StylesTemplate::getMarchId, marchId));

        //再插入新的样式模板
        this.create(marchId, templateCode);

        marchService.updateStyle(marchId, templateCode);

    }

    @Override
    public void exportData(CheckpointQueryDTO checkpointQueryDTO) {

        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IMarchStylesTemplateBiz, StylesTemplateListDTO>(
            checkpointQueryDTO) {

            @Override
            protected IMarchStylesTemplateBiz getBean() {
                return SpringUtil.getBean("marchStylesTemplateBiz", IMarchStylesTemplateBiz.class);
            }

            @Override
            protected PageInfo<StylesTemplateListDTO> getPageInfo() {
                return getBean().list((CheckpointQueryDTO) queryDTO);

            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.StylesTemplate;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.StylesTemplate.getType();
            }
        };

        exportComponent.exportRecord(exportDataDTO);
    }
}
