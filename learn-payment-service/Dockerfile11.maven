FROM registry-test.wunding.com/wunding/arthas:4.0.1-no-jdk

FROM registry-test.wunding.com/wunding/amazoncorretto:11.0.25.9-1-debian12 as builder
WORKDIR /app/
COPY ./target/learn-payment-service-*.jar /app/payment.jar
RUN java -Djarmode=layertools -jar payment.jar extract

FROM registry-test.wunding.com/wunding/amazoncorretto:11.0.25.9-1-debian12

COPY ./docker-entrypoint.sh /app/docker-entrypoint.sh
COPY --from=builder app/dependencies/ /app
COPY --from=builder app/snapshot-dependencies/ /app
COPY --from=builder app/spring-boot-loader/ /app
COPY --from=0 /opt/arthas /app/arthas
# 保证几乎只更新代码层
COPY --from=builder app/application/ /app

WORKDIR /app/

# 仅arthas运行需要，相关jdk命令运行需要有实际的用户存在
RUN groupadd -r myuser -g 1000 && useradd -d /home/<USER>/bin/bash -g myuser myuser && chmod -R 755 /app

ENTRYPOINT ["/app/docker-entrypoint.sh"]
# ENTRYPOINT ["java","-Duser.timezone=GMT+08","org.springframework.boot.loader.JarLauncher"]

EXPOSE 28033
