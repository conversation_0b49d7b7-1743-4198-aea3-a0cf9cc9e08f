package com.wunding.learn.certification.service.service.impl;

import com.wunding.learn.certification.service.model.AssessAnswerRecordDetail;
import com.wunding.learn.certification.service.mapper.AssessAnswerRecordDetailMapper;
import com.wunding.learn.certification.service.service.IAssessAnswerRecordDetailService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p> 测评答题-答题明细表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">李恒</a>
 * @since 2024-05-29
 */
@Slf4j
@Service("assessAnswerRecordDetailService")
public class AssessAnswerRecordDetailServiceImpl extends ServiceImpl<AssessAnswerRecordDetailMapper, AssessAnswerRecordDetail> implements IAssessAnswerRecordDetailService {

}
