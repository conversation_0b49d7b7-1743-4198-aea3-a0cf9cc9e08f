package com.wunding.learn.certification.service.consumer;

import com.rabbitmq.client.Channel;
import com.wunding.learn.certification.service.admin.dto.SaveOrUpdateRelateDTO;
import com.wunding.learn.certification.service.enums.CertificationRuleContentTypeEnum;
import com.wunding.learn.certification.service.service.ICertificationRelateService;
import com.wunding.learn.certification.service.service.ICertificationRuleService;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.dto.CertificationContentDTO;
import com.wunding.learn.common.mq.event.ResourceFinishEvent;
import com.wunding.learn.common.mq.event.ResourceFinishEvent.ResourceEventRoutingKeyConstants;
import com.wunding.learn.common.mq.util.ConsumerAckUtil;
import java.io.IOException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;


/**
 * 认证消费者
 *
 * <AUTHOR>
 * @date 2022/08/26
 */
@Component
@Slf4j
public class CertificationConsumer {

    /**
     * 认证消费队列
     */
    private static final String CERTIFICATION_QUEUE = "certification_queue";
    @Resource
    private ICertificationRelateService certificationRelateService;
    @Resource
    private ICertificationRuleService certificationRuleService;

    /**
     * 将资源类型转换为获证渠道
     *
     * @param resourceType 资源类型
     * @return {@link CertificationRuleContentTypeEnum} 认证规则内容类型
     */
    private static CertificationRuleContentTypeEnum translateResourceType(String resourceType) {

        if (ResourceFinishEvent.ResourceEventRoutingKeyConstants.COURSE_FINISH_EVENT.equals(resourceType)) {
            return CertificationRuleContentTypeEnum.CourseRelate;
        }

        if (ResourceFinishEvent.ResourceEventRoutingKeyConstants.EXAM_PASS_EVENT.equals(resourceType)) {
            return CertificationRuleContentTypeEnum.ExamRelate;
        }

        if (ResourceFinishEvent.ResourceEventRoutingKeyConstants.SURVEY_FINISH_EVENT.equals(resourceType)) {
            return CertificationRuleContentTypeEnum.SurveyRelate;
        }

        if (ResourceFinishEvent.ResourceEventRoutingKeyConstants.PROJECT_COMPLETION_EVENT.equals(resourceType)) {
            return CertificationRuleContentTypeEnum.ProjectRelate;
        }

        if (ResourceFinishEvent.ResourceEventRoutingKeyConstants.PROMOTED_GAME_FINISH_EVENT.equals(resourceType)) {
            return CertificationRuleContentTypeEnum.PROMOTED_GAME;
        }

        if (ResourceFinishEvent.ResourceEventRoutingKeyConstants.APPRAISE_FINISH_EVENT.equals(resourceType)) {
            return CertificationRuleContentTypeEnum.Appraise;
        }

        if (ResourceFinishEvent.ResourceEventRoutingKeyConstants.RECRUIT_FINISH_EVENT.equals(resourceType)) {
            return CertificationRuleContentTypeEnum.Recruit;
        }

        if (ResourceFinishEvent.ResourceEventRoutingKeyConstants.LECTURER_LEVEL_GET_EVENT.equals(resourceType)) {
            return CertificationRuleContentTypeEnum.LecturerLevel;
        }

        if (ResourceFinishEvent.ResourceEventRoutingKeyConstants.TRAIN_WITHOUT_FINISH_EVENT.equals(resourceType)) {
            return CertificationRuleContentTypeEnum.TrainWithOut;
        }

        if (ResourceFinishEvent.ResourceEventRoutingKeyConstants.LEARN_MAP_FINISH_EVENT.equals(resourceType)) {
            return CertificationRuleContentTypeEnum.LearnMap;
        }

        return null;
    }

    @RabbitListener(
        bindings = @QueueBinding(
            value = @Queue(value = CERTIFICATION_QUEUE),
            exchange = @Exchange(value = ResourceFinishEvent.SYSTEM_RESOURCE_FINISH_EXCHANGE,
                type = ResourceFinishEvent.SYSTEM_RESOURCE_FINISH_EXCHANGE_TYPE)
            , key = {
            ResourceEventRoutingKeyConstants.COURSE_FINISH_EVENT
            , ResourceEventRoutingKeyConstants.EXAM_PASS_EVENT
            , ResourceEventRoutingKeyConstants.SURVEY_FINISH_EVENT
            , ResourceEventRoutingKeyConstants.PROJECT_COMPLETION_EVENT
            , ResourceEventRoutingKeyConstants.APPRAISE_FINISH_EVENT
            , ResourceEventRoutingKeyConstants.RECRUIT_FINISH_EVENT
            , ResourceEventRoutingKeyConstants.PROMOTED_GAME_FINISH_EVENT
            , ResourceEventRoutingKeyConstants.LECTURER_LEVEL_GET_EVENT
            , ResourceEventRoutingKeyConstants.TRAIN_WITHOUT_FINISH_EVENT
            , ResourceEventRoutingKeyConstants.LEARN_MAP_FINISH_EVENT
        })
        , id = "certificationConsumer")
    public void certificationConsumer(
        @Payload ResourceFinishEvent resourceFinishEvent
        , @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag
        , Channel channel) throws IOException {
        log.info("认证消费开始 resource_finish_event:[{}]", resourceFinishEvent);
        SaveOrUpdateRelateDTO certificationRelateContentDTO = new SaveOrUpdateRelateDTO();
        try {
            UserThreadContext.setTenantId(resourceFinishEvent.getTenantId());

            String resourceType = resourceFinishEvent.getResourceType();
            CertificationRuleContentTypeEnum certificationRuleContentTypeEnum = translateResourceType(resourceType);
            if (null != certificationRuleContentTypeEnum) {
                certificationRelateContentDTO.setUserId(resourceFinishEvent.getUserId());
                certificationRelateContentDTO.setCertificationRuleContentTypeEnum(certificationRuleContentTypeEnum);
                certificationRelateContentDTO.setContentId(resourceFinishEvent.getContentId());
                CertificationContentDTO contentDTO = certificationRuleService.getContentInfo(
                    certificationRuleContentTypeEnum.getType(),
                    resourceFinishEvent.getContentId());
                certificationRelateContentDTO.setContentName(contentDTO.getName());
                certificationRelateContentDTO.setStartTime(contentDTO.getStartTime());
                certificationRelateContentDTO.setCreateBy(resourceFinishEvent.getCreateBy());

                certificationRelateService.awardCertification(certificationRelateContentDTO);
            }
        } catch (Exception e) {
            log.error("certificationConsumer certificationRelateContentDTO:[{}]", certificationRelateContentDTO);
            log.error("certificationConsumer error:", e);
            channel.basicNack(deliveryTag, false, true);
        }

        log.info("认证消费结束");
        UserThreadContext.remove();
        //手动ack
        ConsumerAckUtil.basicAck(resourceFinishEvent, channel, deliveryTag, false);
    }
}
