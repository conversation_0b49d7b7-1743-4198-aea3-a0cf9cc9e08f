package com.wunding.learn.certification.service.client.rest;

import com.github.pagehelper.PageInfo;
import com.wunding.learn.certification.service.client.dto.TrainWithoutApplyClientDTO;
import com.wunding.learn.certification.service.client.dto.TrainWithoutApplyClientSaveDTO;
import com.wunding.learn.certification.service.client.dto.TrainWithoutClientDTO;
import com.wunding.learn.certification.service.client.query.TrainWithoutClientQuery;
import com.wunding.learn.certification.service.service.ITrainWithoutApplyService;
import com.wunding.learn.certification.service.service.ITrainWithoutService;
import com.wunding.learn.common.bean.Result;
import com.wunding.learn.common.dto.SysCategoryDTO;
import com.wunding.learn.common.query.SysCategoryQuery;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import jakarta.annotation.Resource;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 学员端外部培训报名接口
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">zhongchaoheng</a>
 * @date 2023/4/27 16:08
 */
@RestController
@RequestMapping("${module.certification.contentPath:/}trainWithoutApplyClient")
@Tag(description = "学员端外部培训报名", name = "TrainWithoutApplyClientRest")
public class TrainWithoutApplyClientRest {

    @Resource
    ITrainWithoutService trainWithoutService;

    @Resource
    ITrainWithoutApplyService trainWithoutApplyService;

    @GetMapping("/page")
    @Operation(operationId = "page_trainWithoutApplyClient", summary = "获取学员端外部培训列表-分页", description = "获取学员端外部培训列表-分页")
    public Result<PageInfo<TrainWithoutClientDTO>> getTrainWithoutPageClient(TrainWithoutClientQuery query) {
        PageInfo<TrainWithoutClientDTO> pageInfo = trainWithoutService.getTrainWithoutPageClient(query);
        return Result.success(pageInfo);
    }

    @PostMapping("/save")
    @Operation(operationId = "save_trainWithoutApplyClient", summary = "保存学员端自主报名信息", description = "保存学员端自主报名信息")
    public Result<String> saveTrainWithoutApplyClient(@RequestBody TrainWithoutApplyClientSaveDTO dto) {
        String result = trainWithoutApplyService.saveTrainWithoutApplyClient(dto);
        return !StringUtils.hasText(result) ? Result.success() : Result.fail(-1, result);
    }

    @GetMapping("/get")
    @Operation(operationId = "get_trainWithoutApplyClient", summary = "获取学员端外部培训报名详情", description = "获取学员端外部培训报名详情")
    public Result<TrainWithoutApplyClientDTO> getTrainWithoutApplyClient(
        @Parameter(description = "培训项目id") @RequestParam("trainId") String trainId,
        @Parameter(description = "类型： 0为未报名,1为已报名,2为已获证") @RequestParam("type") String type) {
        TrainWithoutApplyClientDTO result = trainWithoutApplyService.getTrainWithoutApplyClient(trainId, type);
        return Result.success(result);
    }


    @GetMapping("/getTrainWithoutExamCategory")
    @Operation(operationId = "getTrainWithoutExamCategory_trainWithoutApplyClient", summary = "获取学员端外部培训考试类型", description = "获取学员端外部培训考试类型")
    public Result<List<SysCategoryDTO>> getTrainWithoutApplyClient(SysCategoryQuery query) {
        query.setCategoryType("TrainWithoutExam");
        query.setIsAvailable(1);
        List<SysCategoryDTO> result = trainWithoutApplyService.getTrainWithoutExamCategory(query);
        return Result.success(result);
    }

}
