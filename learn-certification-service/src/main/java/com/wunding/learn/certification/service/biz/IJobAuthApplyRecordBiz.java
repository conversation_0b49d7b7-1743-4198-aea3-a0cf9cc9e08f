package com.wunding.learn.certification.service.biz;

import com.github.pagehelper.PageInfo;
import com.wunding.learn.certification.service.admin.dto.AuthenticationUpdateDTO;
import com.wunding.learn.certification.service.admin.query.JobAuthApplyRecordQuery;
import com.wunding.learn.certification.service.client.dto.JobAuthApplyRecordDTO;
import com.wunding.learn.certification.service.client.dto.JobAuthApplyRecordListDTO;
import com.wunding.learn.certification.service.client.query.JobAuthApplyRecordClientQuery;

/**
 * <p> 资格认证申请表 业务服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @since 2024-03-02
 */
public interface IJobAuthApplyRecordBiz {

    /**
     * 查询我的申请资格认证记录
     *
     * @param query {@link JobAuthApplyRecordClientQuery}
     * @return {@link PageInfo}<{@link JobAuthApplyRecordDTO}>
     */
    PageInfo<JobAuthApplyRecordDTO> queryApplyRecordPage(JobAuthApplyRecordClientQuery query);

    /**
     * 查询我的审核资格认证记录
     *
     * @param query {@link JobAuthApplyRecordClientQuery}
     * @return {@link PageInfo}<{@link JobAuthApplyRecordDTO}>
     */
    PageInfo<JobAuthApplyRecordDTO> queryJudgeRecordPage(JobAuthApplyRecordClientQuery query);

    /**
     * 获取分页列表
     *
     * @param query
     * @return
     */
    PageInfo<JobAuthApplyRecordListDTO> queryPage(JobAuthApplyRecordQuery query);

    /**
     * 记录我知道了当前进行中的状态
     *
     * @param id
     */
    void knowJobAuthApplyRecord(String id);

    /**
     * 添加破格人员
     *
     * @param authenticationId
     * @param applyUserId
     */
    void isException(String authenticationId, String applyUserId);

    /**
     * 下载
     *
     * @param authenticationId
     * @param applyUserId
     * @param applyUserLoginName
     * @param applyUserFullName
     */
    void downloadPack(String authenticationId, String applyUserId, String applyUserLoginName, String applyUserFullName);

    /**
     * 更新记录状态
     *
     * @param authenticationId
     */
    void updateStatus(String authenticationId);

    /**
     * 回滚记录状态
     *
     * @param authenticationId
     */
    void rollbackStatus(String authenticationId);

    /**
     * 获取结果公示日期
     *
     * @return
     */
    AuthenticationUpdateDTO getPublicity(String authenticationId);

    /**
     * 取消结果公示
     *
     * @param dto
     */
    void updatePublicity(AuthenticationUpdateDTO dto);

    /**
     * @param authenticationId
     */
    void removePublicity(String authenticationId);

    /**
     * 资格认证发证
     */
    void issuanceByAuthentication();

    /**
     * 导出认证人员列表
     *
     * @param query
     */
    void exportData(JobAuthApplyRecordQuery query);
}
