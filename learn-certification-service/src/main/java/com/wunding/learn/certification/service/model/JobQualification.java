package com.wunding.learn.certification.service.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 任职资格表
 *
 * <AUTHOR> href="mailto:<EMAIL>">axr</a>
 * @since 2024-01-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("job_qualification")
@Schema(name = "JobQualification对象", description = "任职资格表")
public class JobQualification implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键id
     */
    @Schema(description = "主键id")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;


    /**
     * 任职资格名称
     */
    @Schema(description = "任职资格名称")
    @TableField("name")
    private String name;


    /**
     * 岗位id
     */
    @Schema(description = "岗位id")
    @TableField("post_id")
    private String postId;


    /**
     * 证书ID
     */
    @Schema(description = "证书ID")
    @TableField("certification_id")
    private String certificationId;


    /**
     * 专业范围
     */
    @Schema(description = "专业范围")
    @TableField("professional_scope")
    private String professionalScope;


    /**
     * 备注信息
     */
    @Schema(description = "备注信息")
    @TableField("remark")
    private String remark;


    /**
     * 是否公开 0否 1是
     */
    @Schema(description = "是否公开 0否 1是")
    @TableField("is_public")
    private Integer isPublic;

    /**
     * 文件媒体类型
     */
    @Schema(description = "文件媒体类型")
    @TableField("mime")
    private String mime;


    /**
     * 文件类型  与courseware 表cwType保持一致
     */
    @Schema(description = "文件类型  与courseware 表cwType保持一致")
    @TableField("file_type")
    private String fileType;


    /**
     * 转码状态  1 转换中  2 转换完成 3 转换失败
     */
    @Schema(description = "转码状态  1 转换中  2 转换完成 3 转换失败")
    @TableField("transform_status")
    private Integer transformStatus;


    /**
     * 归属部门
     */
    @Schema(description = "归属部门")
    @TableField("org_id")
    private String orgId;


    /**
     * 是否删除 0否 1是
     */
    @Schema(description = "是否删除 0否 1是")
    @TableField("is_del")
    @TableLogic(value = "0", delval = "1")
    private Integer isDel;


    /**
     * 新增人
     */
    @Schema(description = "新增人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;


    /**
     * 新增时间
     */
    @Schema(description = "新增时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 最后编辑人
     */
    @Schema(description = "最后编辑人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;


    /**
     * 最后编辑时间
     */
    @Schema(description = "最后编辑时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;



}
