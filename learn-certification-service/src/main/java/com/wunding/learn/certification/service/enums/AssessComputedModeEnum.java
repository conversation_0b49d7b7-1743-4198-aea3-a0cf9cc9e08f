package com.wunding.learn.certification.service.enums;

import com.wunding.learn.common.annotation.EnumI18nProperty;
import com.wunding.learn.common.i18n.util.EnumI18n;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>测评题目计分方式枚举
 *
 * <AUTHOR> href="mailto:<EMAIL>">liangzm</a>
 * @since 2024/6/5
 */
@Getter
@AllArgsConstructor
public enum AssessComputedModeEnum implements EnumI18n {

    /**
     * 选项结果对应分值
     */
    OPTION_SCORE(1, "选项结果对应分值"),

    /**
     * 选项结果总分分值
     */
    OPTION_SUM_SCORE(2, "选项结果总分分值"),

    /**
     * 选项结果平均分值
     */
    OPTION_AVG_SCORE(3, "选项结果平均分值"),

    /**
     * 选项结果中最低分值
     */
    OPTION_MIN_SCORE(4, "选项结果中最低分值"),

    /**
     * 选项结果中最高分值
     */
    OPTION_MAX_SCORE(5, "选项结果中最高分值"),
    ;

    private final Integer value;

    @EnumI18nProperty
    private final String name;

    public String getName() {
        return i18n(name(), name);
    }

    /**
     * 根据枚举值获取测评题目计分方式
     *
     * @param value 枚举值
     * @return 枚举类型
     */
    public static AssessComputedModeEnum getItem(Integer value) {
        for (AssessComputedModeEnum item : values()) {
            if (item.getValue().equals(value)) {
                return item;
            }
        }
        return null;
    }
    
    /**
     * 是否存在计分方式枚举
     *
     * @param name 称
     * @return 是否存在
     */
    public static boolean contains(String name) {
        for (AssessComputedModeEnum value : AssessComputedModeEnum.values()) {
            if (StringUtils.equals(name, value.getName())) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 根据名称获取type
     * @param questionType
     * @return type
     */
    public static Integer getTypeByName(String questionType) {
        for (AssessComputedModeEnum value : AssessComputedModeEnum.values()) {
            if (StringUtils.equals(questionType, value.getName())) {
                return value.getValue();
            }
        }
        return null;
    }
}
