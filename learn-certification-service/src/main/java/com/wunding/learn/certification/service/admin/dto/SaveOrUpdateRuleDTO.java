package com.wunding.learn.certification.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

/**
 * 保存或更新认证规则dto
 *
 * <AUTHOR>
 * @date 2022/08/23
 */
@Data
@Accessors(chain = true)
@Schema(name = "SaveOrUpdateRuleDTO", description = "保存或更新认证规则对象")
public class SaveOrUpdateRuleDTO {

    /**
     * 主键
     */
    @Schema(description = "主键id(更新时携带)")
    private String id;

    /**
     * 规则名称
     */
    @Schema(description = "规则名称")
    @NotBlank(message = "名称不能为空")
    @Length(max = 128, message = "规则名称长度不能超过128")
    private String name;

    /**
     * 证书id
     */
    @Schema(description = "证书id")
    @NotBlank(message = "证书不能为空")
    @Length(max = 36, message = "证书id长度不能超过36")
    private String certificationId;

    /**
     * 认证体系id
     */
    @Schema(description = "认证体系id")
    @NotBlank(message = "认证体系id不能为空")
    @Length(max = 36, message = "认证体系id长度不能超过36")
    private String certificationSetupId;

    /**
     * 证书等级id
     */
    @Schema(description = "证书等级id")
    @Length(max = 36, message = "证书等级id长度不能超过36")
    private String certificationLevelId;

    /**
     * 证书分类id
     */
    @Schema(description = "证书分类id")
    @NotBlank(message = "证书分类id不能为空")
    @Length(max = 36, message = "证书分类id长度不能超过36")
    private String certCategoryId;

    /**
     * 发证组织（长度不能超过30）
     */
    @Schema(description = "发证组织")
    @Length(max = 30, message = "发证组织长度不能超过30")
    private String orgName;

    /**
     * 关联内容类型
     */
    @Schema(description = "关联内容类型8种(Course,Exam,Project,Survey,PromotedGame,recruit,Appraise,lecturerLevel")
    @NotBlank(message = "关联内容类型不能为空")
    private String contentType;

    /**
     * 关联内容id
     */
    @Schema(description = "关联内容id")
    @NotBlank(message = "关联内容id不能为空")
    @Length(max = 36, message = "关联内容id长度不能超过36")
    private String contentId;

    /**
     * 是否续期 1-是,0-否
     */
    @Schema(description = "是否续期 1-是,0-否")
    @NotNull(message = "续期不能为空")
    private Integer isRenewal;

    /**
     * 发证规则归属组织, 部门id
     */
    @Schema(description = "发证规则归属组织, 部门Id")
    @Length(max = 50, message = "发证规则归属组织, 部门Id长度不能超过36")
    private String createOrgId;

    /**
     * 发证规则归属组织, 部门name
     */
    @Schema(description = "发证规则创建、归属部门")
    @Length(max = 128, message = "发证规则创建、归属部门名称长度不能超过128")
    private String createOrgName;

}
