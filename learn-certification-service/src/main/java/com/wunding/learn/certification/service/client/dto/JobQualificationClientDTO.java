package com.wunding.learn.certification.service.client.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 资格认证添加编辑对象
 *
 * <AUTHOR>
 * @date 2022/08/22
 */
@Data
@Accessors(chain = true)
@Schema(name = "JobQualificationClientDTO", description = "任职资格详情对象")
public class JobQualificationClientDTO {

    @Schema(description = "任职资格id")
    private String id;

    @Schema(description = "任职资格是否公开 0否 1是")
    private Integer isPublic;

    @Schema(description = "资格认证id")
    private String authenticationId;

    @Schema(description = "资格认证是否开放 0否 1是")
    private Integer isOpen;

    @Schema(description = "是否资格认证开放时间范围内 0否 1是")
    private Integer isOpenPeriod;

    @Schema(description = "资格认证申请id")
    private String applyRecordId;

    @Schema(description = "测评工具,是否关联测评工具AssessTool,有才会有值")
    private String assessToolId;
}
