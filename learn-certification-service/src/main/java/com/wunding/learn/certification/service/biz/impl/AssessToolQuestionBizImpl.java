package com.wunding.learn.certification.service.biz.impl;

import static com.wunding.learn.common.util.string.StringUtil.newId;
import static java.util.stream.Collectors.toMap;
import static java.util.stream.Collectors.toSet;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.wunding.learn.certification.service.admin.dto.AssessAnswerDetailListDTO;
import com.wunding.learn.certification.service.admin.dto.AssessRelateAbilityDTO;
import com.wunding.learn.certification.service.admin.dto.AssessToolAbilityConfigDTO;
import com.wunding.learn.certification.service.admin.dto.AssessToolAbilityConfigDetailDTO;
import com.wunding.learn.certification.service.admin.dto.AssessToolAbilityConfigDetailDTO.AbilityCriticalDTO;
import com.wunding.learn.certification.service.admin.dto.AssessToolAbilityDTO;
import com.wunding.learn.certification.service.admin.dto.AssessToolModeAbilitiesTree;
import com.wunding.learn.certification.service.admin.dto.AssessToolQuestionDTO;
import com.wunding.learn.certification.service.admin.dto.AssessToolQuestionOptionDTO;
import com.wunding.learn.certification.service.admin.dto.AssessToolQuestionSimpleDTO;
import com.wunding.learn.certification.service.admin.dto.ComputeModeDTO;
import com.wunding.learn.certification.service.admin.dto.QuestionSortDTO;
import com.wunding.learn.certification.service.admin.dto.SaveAssessToolQuestionDTO;
import com.wunding.learn.certification.service.admin.dto.SaveQuestionOptionDTO;
import com.wunding.learn.certification.service.admin.dto.TableDTO;
import com.wunding.learn.certification.service.admin.dto.TableDTO.TableDetailDTO;
import com.wunding.learn.certification.service.admin.dto.UpdateAbilityCriticalDTO;
import com.wunding.learn.certification.service.admin.query.AbilityQuery;
import com.wunding.learn.certification.service.admin.query.AssessAnswerDetailQuery;
import com.wunding.learn.certification.service.admin.query.AssessToolQuestionPageQuery;
import com.wunding.learn.certification.service.admin.query.AssessToolQuestionQueryDTO;
import com.wunding.learn.certification.service.biz.IAssessToolQuestionBiz;
import com.wunding.learn.certification.service.client.dto.AssessChartDTO;
import com.wunding.learn.certification.service.client.dto.AssessChartDetailDTO;
import com.wunding.learn.certification.service.client.dto.AssessQuestionDTO;
import com.wunding.learn.certification.service.client.dto.AssessQuestionListDTO;
import com.wunding.learn.certification.service.client.dto.AssessQuestionOptionDTO;
import com.wunding.learn.certification.service.client.dto.AssessReportClientDTO;
import com.wunding.learn.certification.service.client.dto.AssessReportDetailClientDTO;
import com.wunding.learn.certification.service.client.dto.AssessResultClientDTO;
import com.wunding.learn.certification.service.client.dto.AssessResultDetailClientDTO;
import com.wunding.learn.certification.service.client.dto.AssessSubmitAnswerDTO;
import com.wunding.learn.certification.service.client.dto.AssessSubmitDTO;
import com.wunding.learn.certification.service.constant.CertificationConstants;
import com.wunding.learn.certification.service.enums.AssessAbilityComputedModeEnum;
import com.wunding.learn.certification.service.enums.AssessComputedModeEnum;
import com.wunding.learn.certification.service.enums.AssessEvalStatusEnum;
import com.wunding.learn.certification.service.enums.AssessMethodTypeEnum;
import com.wunding.learn.certification.service.enums.AssessQuestionTypeEnum;
import com.wunding.learn.certification.service.enums.AssessScoreTypeEnum;
import com.wunding.learn.certification.service.enums.AssessToolRelationTypeEnum;
import com.wunding.learn.certification.service.enums.TableEnum;
import com.wunding.learn.certification.service.imports.AssessToolQuestionImportTemplate;
import com.wunding.learn.certification.service.model.AssessAnswerRecord;
import com.wunding.learn.certification.service.model.AssessAnswerRecordDetail;
import com.wunding.learn.certification.service.model.AssessProject;
import com.wunding.learn.certification.service.model.AssessTool;
import com.wunding.learn.certification.service.model.AssessToolAbilityConfig;
import com.wunding.learn.certification.service.model.AssessToolAbilityLevel;
import com.wunding.learn.certification.service.model.AssessToolQuestion;
import com.wunding.learn.certification.service.model.AssessToolQuestionOption;
import com.wunding.learn.certification.service.model.AssessToolStatistics;
import com.wunding.learn.certification.service.model.AssessUser;
import com.wunding.learn.certification.service.model.AssessUserEvaluator;
import com.wunding.learn.certification.service.model.AssessUserReportDetail;
import com.wunding.learn.certification.service.model.AssessUserReportSummary;
import com.wunding.learn.certification.service.model.JobQualificationKnowResource;
import com.wunding.learn.certification.service.service.IAssessAnswerRecordDetailService;
import com.wunding.learn.certification.service.service.IAssessAnswerRecordService;
import com.wunding.learn.certification.service.service.IAssessProjectService;
import com.wunding.learn.certification.service.service.IAssessToolAbilityConfigService;
import com.wunding.learn.certification.service.service.IAssessToolAbilityLevelService;
import com.wunding.learn.certification.service.service.IAssessToolQuestionOptionService;
import com.wunding.learn.certification.service.service.IAssessToolQuestionService;
import com.wunding.learn.certification.service.service.IAssessToolService;
import com.wunding.learn.certification.service.service.IAssessToolStatisticsService;
import com.wunding.learn.certification.service.service.IAssessUserEvaluatorService;
import com.wunding.learn.certification.service.service.IAssessUserReportDetailService;
import com.wunding.learn.certification.service.service.IAssessUserReportSummaryService;
import com.wunding.learn.certification.service.service.IAssessUserService;
import com.wunding.learn.certification.service.service.IJobQualificationKnowResourceService;
import com.wunding.learn.common.bean.BaseEntity;
import com.wunding.learn.common.constant.certification.CertificationErrorNoEnum;
import com.wunding.learn.common.constant.other.ErrorNoEnum;
import com.wunding.learn.common.constant.other.JudgeEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.dto.ImportExcelDTO;
import com.wunding.learn.common.dto.ImportResultDTO;
import com.wunding.learn.common.enums.ResourceTypeEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.other.PublishStatusEnum;
import com.wunding.learn.common.enums.other.SystemConfigCodeEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.i18n.util.I18nUtil;
import com.wunding.learn.common.util.bean.BeanListUtils;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.date.DateUtil;
import com.wunding.learn.common.util.excel.ExcelCheckMessage;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.common.viewlimit.constant.LimitTable;
import com.wunding.learn.common.viewlimit.service.IResourceViewLimitService;
import com.wunding.learn.course.api.service.CourseFeign;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.dto.AbstractExportDataDTO;
import com.wunding.learn.file.api.dto.AbstractExportNoEntityDataDTO;
import com.wunding.learn.file.api.dto.ExcelHead;
import com.wunding.learn.file.api.dto.ExportResultDTO;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.file.api.dto.IExportNoEntityDataDTO;
import com.wunding.learn.file.api.dto.ImportDataDTO;
import com.wunding.learn.file.api.service.FileFeign;
import com.wunding.learn.file.api.service.ImportDataFeign;
import com.wunding.learn.user.api.dto.AbilityLevelListDTO;
import com.wunding.learn.user.api.dto.AbilityRelateCourseDTO;
import com.wunding.learn.user.api.dto.OrgDTO;
import com.wunding.learn.user.api.dto.OrgShowDTO;
import com.wunding.learn.user.api.dto.ParaDTO;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.service.AbilityFeign;
import com.wunding.learn.user.api.service.OrgFeign;
import com.wunding.learn.user.api.service.ParaFeign;
import com.wunding.learn.user.api.service.UserFeign;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.OptionalDouble;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * <p>人才测评-测评工具题目业务层
 *
 * <AUTHOR> href="mailto:<EMAIL>">liangzm</a>
 * @since 2024/5/29
 */
@Slf4j
@Service("assessToolQuestionBiz")
public class AssessToolQuestionBizImpl implements IAssessToolQuestionBiz {

    public static final String ASSESS_TOOL_QUESTION_BIZ = "assessToolQuestionBiz";
    public static final String OTHER_ABILITY_TYPE = "other";
    public static final String OTHER_ABILITY_TYPE_NAME = "其它";
    public static final int MAX_OPTION_SIZE = 7;
    @Resource
    IAssessUserEvaluatorService assessUserEvaluatorService;
    @Resource
    IAssessAnswerRecordService assessAnswerRecordService;
    @Resource
    IAssessAnswerRecordDetailService assessAnswerRecordDetailService;
    @Resource
    IAssessUserReportDetailService assessUserReportDetailService;
    @Resource
    IAssessUserReportSummaryService assessUserReportSummaryService;
    @Resource
    ExportComponent exportComponent;
    @Resource
    FileFeign fileFeign;
    @Resource
    ImportDataFeign importDataFeign;
    @Resource
    ParaFeign paraFeign;
    @Resource
    OrgFeign orgFeign;
    @Resource
    UserFeign userFeign;
    @Resource
    AbilityFeign abilityFeign;
    @Resource
    CourseFeign courseFeign;
    @Resource
    private IAssessToolService assessToolService;
    @Resource
    private IAssessToolQuestionService assessToolQuestionService;
    @Resource
    private IAssessToolQuestionOptionService assessToolQuestionOptionService;
    @Resource
    private IAssessToolAbilityConfigService assessToolAbilityConfigService;
    @Resource
    private IAssessProjectService assessProjectService;
    @Resource
    private IResourceViewLimitService resourceViewLimitService;
    @Resource
    private IAssessToolStatisticsService assessToolStatisticsService;
    @Resource
    private IAssessUserService assessUserService;
    @Resource
    private IAssessToolAbilityLevelService assessToolAbilityLevelService;

    @Resource
    private IJobQualificationKnowResourceService jobQualificationKnowResourceService;

    // 提交测评或获取题目时，校验是否已经交卷
    private static void checkIsEvalPost(AssessAnswerRecord assessAnswerRecord) {
        // 是否交卷
        if (assessAnswerRecord.getIsPost().equals(GeneralJudgeEnum.CONFIRM.getValue())) {
            throw new BusinessException(CertificationErrorNoEnum.ERR_ASSESS_IS_POST);
        }
    }

    /**
     * 获取测评报告能力项对应测评方式的平均值 map
     *
     * @param methodTypeEnum         测评方式枚举
     * @param methodAndEvalIdListMap 测评方式和测评人员列表 map
     * @param detailList             测评结果列表
     * @return 测评报告能力项对应测评方式的平均值 map
     */
    private static Map<String, Double> getAbilityIdScoreMap(
        AssessMethodTypeEnum methodTypeEnum,
        Map<Integer, List<String>> methodAndEvalIdListMap,
        List<AssessUserReportDetail> detailList) {
        List<String> evalIdList = methodAndEvalIdListMap.get(methodTypeEnum.getValue());
        return null == evalIdList || evalIdList.isEmpty()
            ? new HashMap<>()
            : detailList.stream()
                .filter(p -> evalIdList.contains(p.getAssessUserEvaluatorId()))
                .collect(Collectors.groupingBy(AssessUserReportDetail::getAbilityId,
                    Collectors.averagingDouble(s -> s.getScore().doubleValue())));
    }

    // 设置各种测评方式的分值
    private static void setMethodScore(AssessUserReportSummary reportSummary,
        Map<String, Double> selfAbilityIdScoreMap, Map<String, Double> upperAbilityIdScoreMap,
        Map<String, Double> peersAbilityIdScoreMap, Map<String, Double> lowerAbilityIdScoreMap) {
        // 各种测评方式得分
        reportSummary.setScoreSelf(
            BigDecimal.valueOf(Optional.ofNullable(selfAbilityIdScoreMap.get(reportSummary.getAbilityId())).isPresent()
                ? selfAbilityIdScoreMap.get(reportSummary.getAbilityId()) : 0));
        reportSummary.setScoreUpper(BigDecimal.valueOf(
            Optional.ofNullable(upperAbilityIdScoreMap.get(reportSummary.getAbilityId())).isPresent()
                ? upperAbilityIdScoreMap.get(reportSummary.getAbilityId()) : 0));
        reportSummary.setScorePeers(BigDecimal.valueOf(
            Optional.ofNullable(peersAbilityIdScoreMap.get(reportSummary.getAbilityId())).isPresent()
                ? peersAbilityIdScoreMap.get(reportSummary.getAbilityId()) : 0));
        reportSummary.setScoreLower(BigDecimal.valueOf(
            Optional.ofNullable(lowerAbilityIdScoreMap.get(reportSummary.getAbilityId())).isPresent()
                ? lowerAbilityIdScoreMap.get(reportSummary.getAbilityId()) : 0));
    }

    // 校验测试工具发布状态
    private static void checkToolPublish(AssessTool tool) {
        if (tool.getIsPublish().equals(PublishStatusEnum.IS_NO_PUBLISH.getValue())) {
            throw new BusinessException(CertificationErrorNoEnum.TOOL_NOT_PUBLISH);
        }
    }

    // 校验测评项目发布状态
    private static void checkProjectPublish(AssessProject assessProject) {
        if (assessProject.getIsPublish().equals(PublishStatusEnum.IS_NO_PUBLISH.getValue())) {
            throw new BusinessException(CertificationErrorNoEnum.PROJECT_NOT_PUBLISH);
        }
    }

    // 设置测评工具 不存在的测评结果值为 NULL  非自评的结果为 NULL
    private static void setToolNullScore(AssessReportDetailClientDTO p, AssessTool tool) {
        if (tool.getIsMethodSelf().equals(GeneralJudgeEnum.NEGATIVE.getValue())) {
            p.setScoreSelf(null);
        }
        p.setScoreUpper(null);
        p.setScorePeers(null);
        p.setScoreLower(null);
    }

    // 设置测评项目 不存在的测评结果值为 NULL
    private static void setProjectNullScore(AssessReportDetailClientDTO p, AssessProject project) {
        if (project.getIsMethodSelf().equals(GeneralJudgeEnum.NEGATIVE.getValue())) {
            p.setScoreSelf(null);
        }
        if (project.getIsMethodUpper().equals(GeneralJudgeEnum.NEGATIVE.getValue())) {
            p.setScoreUpper(null);
        }
        if (project.getIsMethodPeers().equals(GeneralJudgeEnum.NEGATIVE.getValue())) {
            p.setScorePeers(null);
        }
        if (project.getIsMethodLower().equals(GeneralJudgeEnum.NEGATIVE.getValue())) {
            p.setScoreLower(null);
        }
    }

    /**
     * 根据能力等级配置和能力分值，获取能力雷达图数值（百分比）
     *
     * @param scoreType 标准分制（0-十分制，1-五分制）  当能力项无等级时，使用标准分制的分值进行雷达图数值百分比云计算
     * @param reportDto 测评报告明细对象
     * @param levelList 能力等级列表（全量数据）
     * @param config    能力项配置信息
     * @return 测评能力雷达图边角数据对象
     */
    private static AssessChartDetailDTO getAssessChartDetailDTO(
        Integer scoreType,
        AssessReportDetailClientDTO reportDto,
        List<AssessToolAbilityLevel> levelList,
        AssessToolAbilityConfig config) {
        AssessChartDetailDTO chart = new AssessChartDetailDTO();

        // 判断能力是否有等级（无等级则能力等级ID为空字符）
        boolean isNoLevel = levelList.stream()
            .filter(l -> l.getAbilityConfigId().equals(config.getId()))
            .allMatch(l -> StringUtils.isBlank(l.getAbilityLevelId()));

        if (isNoLevel) {
            double max = 0 == scoreType ? Double.valueOf(10) : Double.valueOf(5);
            setChartPercentage(reportDto, chart, max);
        } else {

            // 获取对应能力配置等级的最高临界值
            OptionalDouble max = levelList.stream()
                .filter(l -> l.getAbilityConfigId().equals(config.getId()))
                .mapToDouble(l -> l.getLevelScore().doubleValue()).max();
            // 要考试最大值等于0 或 负数的异常情况
            if (max.isPresent() && max.getAsDouble() > 0) {
                setChartPercentage(reportDto, chart, max.getAsDouble());
            } else {
                chart.setScorePercentage(0.00);
                chart.setStdPercentage(0.00);
                chart.setAvgPercentage(0.00);
                chart.setScoreSelfPercentage(0.00);
                chart.setScoreUpperPercentage(0.00);
                chart.setScorePeersPercentage(0.00);
                chart.setScoreLowerPercentage(0.00);
            }
        }
        return chart;
    }

    // 计算雷达图值百分比
    private static void setChartPercentage(AssessReportDetailClientDTO reportDto, AssessChartDetailDTO chart,
        double max) {
        chart.setStdPercentage(
            null == reportDto.getStdScore() ? 0
                : BigDecimal.valueOf(reportDto.getStdScore().doubleValue() / max).setScale(2, RoundingMode.HALF_UP)
                    .doubleValue());
        chart.setAvgPercentage(
            null == reportDto.getAvgScore() ? 0
                : BigDecimal.valueOf(reportDto.getAvgScore().doubleValue() / max).setScale(2, RoundingMode.HALF_UP)
                    .doubleValue());
        chart.setScorePercentage(
            null == reportDto.getScore() ? 0
                : BigDecimal.valueOf(reportDto.getScore().doubleValue() / max).setScale(2, RoundingMode.HALF_UP)
                    .doubleValue());
        chart.setScoreSelfPercentage(
            null == reportDto.getScoreSelf() ? 0
                : BigDecimal.valueOf(reportDto.getScoreSelf().doubleValue() / max).setScale(2, RoundingMode.HALF_UP)
                    .doubleValue());
        chart.setScoreUpperPercentage(
            null == reportDto.getScoreUpper() ? 0
                : BigDecimal.valueOf(reportDto.getScoreUpper().doubleValue() / max)
                    .setScale(2, RoundingMode.HALF_UP)
                    .doubleValue());
        chart.setScorePeersPercentage(
            null == reportDto.getScorePeers() ? 0
                : BigDecimal.valueOf(reportDto.getScorePeers().doubleValue() / max)
                    .setScale(2, RoundingMode.HALF_UP)
                    .doubleValue());
        chart.setScoreLowerPercentage(
            null == reportDto.getScoreLower() ? 0
                : BigDecimal.valueOf(reportDto.getScoreLower().doubleValue() / max)
                    .setScale(2, RoundingMode.HALF_UP)
                    .doubleValue());
    }

    // 数据修正——防止出现超过 100% 的数据
    private static void fixMaxScorePercentageData(AssessChartDetailDTO detail) {

        if (detail.getStdPercentage() > 1) {
            detail.setStdPercentage(1.00);
        }
        if (detail.getAvgPercentage() > 1) {
            detail.setAvgPercentage(1.00);
        }

        if (detail.getScorePercentage() > 1) {
            detail.setScorePercentage(1.00);
        }

        if (detail.getScoreSelfPercentage() > 1) {
            detail.setScoreSelfPercentage(1.00);
        }

        if (detail.getScoreUpperPercentage() > 1) {
            detail.setScoreUpperPercentage(1.00);
        }

        if (detail.getScorePeersPercentage() > 1) {
            detail.setScorePeersPercentage(1.00);
        }

        if (detail.getScoreLowerPercentage() > 1) {
            detail.setScoreLowerPercentage(1.00);
        }
    }

    // 数据修正 如果没有能力类型，则设置该能力为其他能力
    private static void fixAbilityTypeData(List<AssessReportDetailClientDTO> dtoList, Set<String> abilityTypeList) {

        boolean hasOther = false;

        // 分类名称中是否已有 “其它” 这个名称，如果已有“其它”，则设置名称为 “其它1”
        String otherName = dtoList.stream().anyMatch(p -> p.getAbilityCategoryName().equals(OTHER_ABILITY_TYPE_NAME)) ?
            OTHER_ABILITY_TYPE_NAME + "1" : OTHER_ABILITY_TYPE_NAME;
        String otherType = dtoList.stream().anyMatch(p -> p.getAbilityTypeName().equals(OTHER_ABILITY_TYPE_NAME)) ?
            OTHER_ABILITY_TYPE_NAME + "1" : OTHER_ABILITY_TYPE_NAME;

        for (AssessReportDetailClientDTO p : dtoList) {

            // 能力类型为空
            if (StringUtils.isBlank(p.getAbilityType())) {
                p.setAbilityType(OTHER_ABILITY_TYPE);
                p.setAbilityTypeName(otherType);
                hasOther = true;
            }

            // 能力分类为空
            if (StringUtils.isBlank(p.getAbilityCategoryId())) {
                p.setAbilityCategoryId(OTHER_ABILITY_TYPE);
                p.setAbilityCategoryName(otherName);
            }
        }

        // 如果有“其它”能力类型，则在总分类列表中添加一条 other 类型
        if (hasOther) {
            abilityTypeList.add(OTHER_ABILITY_TYPE);
        }
    }

    /**
     * 校验测评项目是否所有测评方式均完成
     *
     * @param assessProject 测评项目
     * @param evaluatorList 测评人列表
     * @return 是否所有测评方式均完成
     */
    private static boolean checkProjectAllEvalTypeComplete(AssessProject assessProject,
        List<AssessUserEvaluator> evaluatorList) {

        if (null == assessProject) {
            return true;
        }

        // 测评项目所有已完成测评的测评方式 Set
        Set<Integer> allCompleteTypeSet = evaluatorList.stream()
            .filter(p -> p.getIsComplete().equals(GeneralJudgeEnum.CONFIRM.getValue()))
            .map(AssessUserEvaluator::getMethodType)
            .collect(Collectors.toSet());

        // 已启用自评
        if (assessProject.getIsMethodSelf().equals(GeneralJudgeEnum.CONFIRM.getValue())
            && !allCompleteTypeSet.contains(AssessMethodTypeEnum.SELF.getValue())) {
            return false;
        }

        // 已启用上级评
        if (assessProject.getIsMethodUpper().equals(GeneralJudgeEnum.CONFIRM.getValue())
            && !allCompleteTypeSet.contains(AssessMethodTypeEnum.UPPER.getValue())) {
            return false;
        }

        // 已启用平级评
        if (assessProject.getIsMethodPeers().equals(GeneralJudgeEnum.CONFIRM.getValue())
            && !allCompleteTypeSet.contains(AssessMethodTypeEnum.PEERS.getValue())) {
            return false;
        }

        // 已调用下级评
        if (assessProject.getIsMethodLower().equals(GeneralJudgeEnum.CONFIRM.getValue())
            && !allCompleteTypeSet.contains(AssessMethodTypeEnum.LOWER.getValue())) {
            return false;
        }

        return true;
    }

    @Override
    public AssessToolAbilityDTO getAbilityTree(String id) {
        // 校验
        AssessTool assessTool = assessToolService.checkAssessTool(id);

        AssessToolAbilityDTO result = new AssessToolAbilityDTO();
        result.setRelationType(assessTool.getRelationType())
            .setRelationTypeName(I18nUtil.getMessage(AssessToolRelationTypeEnum.getTypeName(assessTool.getRelationType())))
            .setSourceName(assessTool.getRelationObjectName());

        List<AssessRelateAbilityDTO> abilityDTOList = assessToolAbilityConfigService.getAssessRelateAbility(
            assessTool.getId());
        if (CollectionUtils.isEmpty(abilityDTOList)) {
            return result;
        }
        List<AssessToolModeAbilitiesTree> assessToolModeAbilitiesTrees = new ArrayList<>();
        Map<String, List<AssessRelateAbilityDTO>> firstLevel = abilityDTOList.stream()
            .collect(Collectors.groupingBy(AssessRelateAbilityDTO::getAbilityType));
        // 一级分类(专业能力、通用能力)
        firstLevel.forEach((key, value) -> {
            // 二级分类
            Map<String, List<AssessRelateAbilityDTO>> secondLevel = value.stream()
                .collect(Collectors.groupingBy(AssessRelateAbilityDTO::getAbilityCategoryId));
            secondLevel.forEach((k, v) -> {
                AssessToolModeAbilitiesTree assessToolModeAbilitiesTree = new AssessToolModeAbilitiesTree();
                // 一级分类名称
                assessToolModeAbilitiesTree.setAbilityType(key);
                assessToolModeAbilitiesTree.setCategoryName(value.get(0).getAbilityTypeName());
                // 二级分类名称
                assessToolModeAbilitiesTree.setSecondCategoryName(v.get(0).getAbilityCategoryName());
                assessToolModeAbilitiesTree.setAbilityCategoryId(v.get(0).getAbilityCategoryId());
                assessToolModeAbilitiesTree.setAbilityDTOList(v);
                assessToolModeAbilitiesTrees.add(assessToolModeAbilitiesTree);
            });
        });
        // 获取能力对应的题目数量
        Map<String, Long> questionCountMap = assessToolQuestionService.getAbilityQuestionCount(assessTool.getId());

        result.setModeAbilitiesTree(assessToolModeAbilitiesTrees);
        assessToolModeAbilitiesTrees.forEach(assessToolModeAbilitiesTree -> {
            for (AssessRelateAbilityDTO assessRelateAbilityDTO : assessToolModeAbilitiesTree.getAbilityDTOList()) {
                Optional.ofNullable(questionCountMap.get(assessRelateAbilityDTO.getAbilityId())).ifPresent(
                    assessRelateAbilityDTO::setQuestionCount);
            }
        });

        return result;
    }

    @Override
    public List<AssessToolQuestionDTO> findQuestionList(AssessToolQuestionQueryDTO questionQueryDTO) {
        assessToolService.checkAssessTool(questionQueryDTO.getToolId());

        List<AssessToolQuestionDTO> questionDTOList = assessToolQuestionService.getListByQuery(questionQueryDTO);
        if (CollectionUtils.isEmpty(questionDTOList)) {
            return questionDTOList;
        }
        List<String> questionIds = questionDTOList.stream().map(AssessToolQuestionDTO::getId)
            .collect(Collectors.toList());
        // 获取题目选项
        Map<String, List<AssessToolQuestionOption>> optionMap = assessToolQuestionOptionService.getQuestionOptionMap(
            questionIds);

        questionDTOList.forEach(questionDTO -> questionDTO.setQuestionOptionDTOList(
            buildQuestionOption(optionMap.get(questionDTO.getId()))));
        return questionDTOList;
    }

    @Override
    public List<ComputeModeDTO> computeModeList() {
        return Arrays.stream(AssessComputedModeEnum.values())
            .map(modeEnum -> new ComputeModeDTO().setValue(modeEnum.getValue()).setName(modeEnum.getName()))
            .collect(Collectors.toList());
    }

    @Override
    public PageInfo<AssessToolAbilityConfigDTO> abilityList(String toolId, AbilityQuery abilityQuery) {
        assessToolService.checkAssessTool(toolId);
        PageInfo<AssessToolAbilityConfig> pageInfo = PageMethod.startPage(abilityQuery.getPageNo(),
                abilityQuery.getPageSize())
            .doSelectPageInfo(() -> assessToolAbilityConfigService.list(
                new LambdaQueryWrapper<AssessToolAbilityConfig>().eq(AssessToolAbilityConfig::getToolId, toolId)
                    .like(StringUtils.isNotBlank(abilityQuery.getAbilityName()),
                        AssessToolAbilityConfig::getAbilityName, abilityQuery.getAbilityName())));
        PageInfo<AssessToolAbilityConfigDTO> result = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, result);
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            result.setList(Collections.emptyList());
            return result;
        }
        result.setList(BeanListUtils.copyListProperties(pageInfo.getList(), AssessToolAbilityConfigDTO::new));
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AssessToolQuestionDTO saveOrUpdateQuestion(SaveAssessToolQuestionDTO questionDTO) {
        // 当前操作人
        String userId = UserThreadContext.getUserId();
        AssessToolQuestion question = new AssessToolQuestion();
        // 判断是新增还是更新操作
        boolean insert = StringUtils.isBlank(questionDTO.getId());
        if (Objects.isNull(questionDTO.getQuestionType())) {
            questionDTO.setQuestionType(AssessQuestionTypeEnum.RADIO.getType());
        }
        BeanUtils.copyProperties(questionDTO, question);
        // 校验题目计分方式
        assessToolQuestionService.checkComputeMode(questionDTO);
        // 添加
        if (insert) {
            int questionMaxSortNo =
                assessToolQuestionService.getQuestionMaxSortNo(questionDTO.getToolId());
            question.setId(newId());
            question.setQuestionName(I18nUtil.getMessage("请输入题目"));
            question.setCreateBy(userId);
            question.setSortNo(questionMaxSortNo + 1);
            questionDTO.setId(question.getId());
            // 保存量表类型题目选项
            List<AssessToolQuestionOption> optionList =
                genQuestionOptions(
                    question.getId(), questionDTO.getQuestionType(), TableEnum.SATISFACTION_LEVEL);
            assessToolQuestionOptionService.saveBatch(optionList);
            assessToolQuestionService.saveQuestion(question);
        } else {
            // 更新
            question.setUpdateBy(userId).setUpdateTime(new Date());
            assessToolQuestionService.updateQuestion(question);
            // 量表类型变更，更新题目选项
            if (!Objects.isNull(questionDTO.getQuestionSubType())
                && Objects.equals(question.getQuestionType(), AssessQuestionTypeEnum.TABLE.getType())
                && Objects.equals(question.getQuestionSubType(), questionDTO.getQuestionSubType())) {
                assessToolQuestionOptionService.remove(new LambdaQueryWrapper<AssessToolQuestionOption>()
                    .eq(AssessToolQuestionOption::getQuestionId, question.getId()));
                assessToolQuestionOptionService.saveBatch(
                    genQuestionOptions(question.getId(), questionDTO.getQuestionType(),
                        TableEnum.getTableEnum(question.getQuestionSubType())));
            }
        }
        Optional<AssessToolQuestionDTO> toolQuestionDTO = findQuestionList(
            new AssessToolQuestionQueryDTO().setToolId(questionDTO.getToolId())
                .setQuestionId(questionDTO.getId())).stream()
            .findFirst();
        return toolQuestionDTO.orElseGet(AssessToolQuestionDTO::new);
    }

    /**
     * 生成题目选项
     *
     * @param questionId   题目id
     * @param questionType 题目类型
     * @param tableEnum    量表类型枚举
     */
    private List<AssessToolQuestionOption> genQuestionOptions(String questionId, Integer questionType,
        TableEnum tableEnum) {
        String userId = UserThreadContext.getUserId();
        Date date = new Date();
        List<AssessToolQuestionOption> optionList = new ArrayList<>();
        // 生成题目选项
        AssessQuestionTypeEnum questionTypeEnum = AssessQuestionTypeEnum.getItem(questionType);
        switch (Objects.requireNonNull(questionTypeEnum)) {
            case RADIO:
            case MULTI_SELECT:
            case JUDGEMENT:
                return buildDefaultQuestionOptions(questionId);
            case TABLE:
                List<ParaDTO> paramList = paraFeign.getParaListByCategoryId(tableEnum.getParentId());
                paramList.sort(Comparator.comparingInt(param -> Integer.parseInt(param.getParaValue())));
                AssessToolQuestionOption option;
                for (ParaDTO param : paramList) {
                    option = new AssessToolQuestionOption();
                    char optionCode = (char) (Integer.parseInt(param.getParaValue()) + 64);
                    option.setQuestionId(questionId)
                        .setId(newId())
                        .setOptionName(param.getParaName())
                        .setScore(new BigDecimal(param.getParaValue()))
                        .setOptionCode(String.valueOf(optionCode))
                        .setOptionIndex(Integer.parseInt(param.getParaValue()))
                        .setCreateBy(userId)
                        .setUpdateBy(userId)
                        .setCreateTime(date)
                        .setUpdateTime(date);
                    optionList.add(option);
                }
                return optionList;
            default:
                break;
        }
        return optionList;
    }

    private List<AssessToolQuestionOption> buildDefaultQuestionOptions(String questionId) {
        List<AssessToolQuestionOption> optionList = new ArrayList<>();
        optionList.add(new AssessToolQuestionOption().setId(newId()).setQuestionId(questionId).setOptionName(I18nUtil.getMessage("选项1"))
            .setOptionCode("A").setOptionIndex(1));
        optionList.add(new AssessToolQuestionOption().setId(newId()).setQuestionId(questionId).setOptionName(I18nUtil.getMessage("选项2"))
            .setOptionCode("B").setOptionIndex(2));
        return optionList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delQuestion(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessException(CertificationErrorNoEnum.ASSESS_QUESTION_NOT_EXIST);
        }

        List<AssessToolQuestion> assessToolQuestions = assessToolQuestionService.listByIds(ids);
        Set<String> toolIdSet = assessToolQuestions.stream().map(AssessToolQuestion::getToolId).collect(toSet());
        long publishCount = assessToolService.count(new LambdaQueryWrapper<AssessTool>()
            .in(AssessTool::getId, toolIdSet)
            .eq(AssessTool::getIsPublish, PublishStatusEnum.IS_PUBLISH.getValue())
        );

        if (publishCount > 0) {
            throw new BusinessException(CertificationErrorNoEnum.ASSESS_QUESTION_DELETE_TOOL_IS_PUBLISH);
        }

        assessToolQuestionService.delQuestion(ids);
    }

    @Override
    public SaveQuestionOptionDTO saveOrUpdateQuestionOption(SaveQuestionOptionDTO questionDTO) {
        String questionId = questionDTO.getId();
        assessToolQuestionService.checkQuestion(questionId);
        List<AssessToolQuestionOptionDTO> optionDTOS = questionDTO.getQuestionOptionDTOList();
        if (Objects.nonNull(optionDTOS) && optionDTOS.size() > MAX_OPTION_SIZE) {
            throw new BusinessException(CertificationErrorNoEnum.ASSESS_QUESTION_OPTION_SIZE_ERR);
        }
        assessToolQuestionOptionService.saveOrUpdateQuestionOption(questionDTO);

        return getAssessQuestionOption(questionId);
    }

    @Override
    public SaveQuestionOptionDTO getAssessQuestionOption(String questionId) {
        assessToolQuestionService.checkQuestion(questionId);
        SaveQuestionOptionDTO saveQuestionOptionDTO = new SaveQuestionOptionDTO();
        saveQuestionOptionDTO.setId(questionId);
        List<AssessToolQuestionOption> options = assessToolQuestionOptionService.list(
            new LambdaQueryWrapper<AssessToolQuestionOption>().eq(AssessToolQuestionOption::getQuestionId, questionId)
                .orderByAsc(AssessToolQuestionOption::getOptionIndex));
        saveQuestionOptionDTO.setQuestionOptionDTOList(
            BeanListUtils.copyListProperties(options, AssessToolQuestionOptionDTO::new));
        return saveQuestionOptionDTO;
    }

    @Override
    public void delQuestionOption(String id) {
        AssessToolQuestionOption questionOption = assessToolQuestionOptionService.getById(id);
        if (questionOption == null) {
            return;
        }

        AssessToolQuestion question = assessToolQuestionService.getById(questionOption.getQuestionId());
        if (question == null) {
            return;
        }

        AssessTool tool = assessToolService.getById(question.getToolId());
        if (tool == null) {
            return;
        }

        if (tool.getIsPublish().equals(PublishStatusEnum.IS_PUBLISH.getValue())) {
            throw new BusinessException(CertificationErrorNoEnum.ASSESS_QUESTION_DELETE_TOOL_IS_PUBLISH);
        }

        assessToolQuestionOptionService.removeById(id);
        SaveQuestionOptionDTO questionOptionDTO = getAssessQuestionOption(questionOption.getQuestionId());
        // 更新题目
        List<AssessToolQuestionOption> optionList = assessToolQuestionOptionService
            .buildQuestionOptions(questionOptionDTO);
        assessToolQuestionOptionService.saveOrUpdateBatch(optionList);
    }

    @Override
    public AssessToolAbilityConfigDetailDTO getAssessToolAbilityConfigDetail(String toolId, String abilityId) {
        AssessToolAbilityConfigDetailDTO dto = new AssessToolAbilityConfigDetailDTO();
        AssessTool assessTool = assessToolService.checkAssessTool(toolId);
        dto.setScoreType(assessTool.getScoreType());

        // 能力达标分值配置 (容错处理，由于前置流程的数据错误，同一模型可能返回多条数据，此处查询列表，仅返回第一条)
        List<AssessToolAbilityConfig> abilityConfigList = assessToolAbilityConfigService.list(
            new LambdaQueryWrapper<AssessToolAbilityConfig>().eq(AssessToolAbilityConfig::getToolId, toolId)
                .eq(AssessToolAbilityConfig::getAbilityId, abilityId));
        if (abilityConfigList.isEmpty()) {
            return dto;
        }
        // 容错处理，由于前置流程的数据错误，同一模型可能返回多条数据，此处查询列表，仅返回第一条
        AssessToolAbilityConfig abilityConfig = abilityConfigList.get(0);

        dto.setAbilityName(abilityConfig.getAbilityName());
        dto.setScoreMode(abilityConfig.getScoreMode());
        dto.setId(abilityConfig.getId());

        // 获取能力等级
        String abilityConfigId = abilityConfig.getId();
        List<AssessToolAbilityLevel> toolAbilityLevels = assessToolAbilityLevelService.list(
            new LambdaQueryWrapper<AssessToolAbilityLevel>()
                .eq(AssessToolAbilityLevel::getAbilityConfigId, abilityConfigId));

        //获取能力等级排序
        List<AbilityLevelListDTO> abilityLevelList = abilityFeign.getLevelsByAbilityIds(
            Collections.singletonList(abilityId));
        Map<String, Integer> levelSortNoMap = abilityLevelList.stream()
            .collect(toMap(AbilityLevelListDTO::getLevelId, AbilityLevelListDTO::getSortNo));
        if (!CollectionUtils.isEmpty(toolAbilityLevels)) {
            List<AbilityCriticalDTO> abilityCriticalDtoS = BeanListUtils
                .copyListProperties(toolAbilityLevels, AbilityCriticalDTO::new);
            abilityCriticalDtoS.forEach(abilityCriticalDTO -> abilityCriticalDTO.setSortNo(
                levelSortNoMap.get(abilityCriticalDTO.getAbilityLevelId())));
            //根据sortNo排序
            abilityCriticalDtoS = abilityCriticalDtoS.stream()
                .sorted(Comparator.comparingInt(AbilityCriticalDTO::getSortNo))
                .collect(Collectors.toList());
            dto.setAbilityCriticalDtoS(abilityCriticalDtoS);
        }
        return dto;
    }

    /**
     * 构建题目选项
     *
     * @param optionList 题目选项列表
     * @return 题目选项列表
     */
    private List<AssessToolQuestionOptionDTO> buildQuestionOption(
        List<AssessToolQuestionOption> optionList) {
        if (CollectionUtils.isEmpty(optionList)) {
            return Collections.emptyList();
        }
        return BeanListUtils.copyListProperties(optionList, AssessToolQuestionOptionDTO::new);
    }

    // 自行测评，需要初始化用户测评主表及测评人表数据，同时需要校验测评工具的使用范围。
    @Override
    public AssessQuestionListDTO getSelfAssessQuestionList(String toolId, String recordId, String version) {

        // 测评工具详情
        AssessTool tool = assessToolService.checkAssessTool(toolId);

        // 发布状态校验
        if (Objects.equals(tool.getIsPublish(), PublishStatusEnum.IS_NO_PUBLISH.getValue())) {
            throw new BusinessException(ErrorNoEnum.ERR_IS_NO_PUBLISH);
        }

        // 使用范围校验
        checkAssessToolViewLimit(toolId);

        // 当前登录用户
        String userId = UserThreadContext.getUserId();

        // 获取测评工具题目列表
        List<AssessQuestionDTO> questionList = getQuestionDtoListByToolId(toolId);

        // 版本号
        Date date = new Date();
        AssessAnswerRecord assessAnswerRecord = null;

        // 是否正在测评
        boolean isOnEval = false;
        String userEvaluatorId = null;
        Integer reEval = GeneralJudgeEnum.CONFIRM.getValue();
        int remainTime = tool.getDuration() * 60;

        // 如果是继续答题
        if (StringUtils.isNotBlank(recordId) && StringUtils.isNotBlank(version)) {
            assessAnswerRecord = assessAnswerRecordService.getById(recordId);

            if (Optional.ofNullable(assessAnswerRecord).isPresent() && version.equals(
                String.valueOf(assessAnswerRecord.getStartTime().getTime()))) {

                // 提交测评或获取题目时，校验是否已经交卷
                checkIsEvalPost(assessAnswerRecord);

                date = assessAnswerRecord.getStartTime();
                isOnEval = true;

                // 有限定测评时长
                if (!tool.getDuration().equals(0)) {
                    // 已用时长（秒）
                    Integer second = DateUtil.secondBetweenDate(assessAnswerRecord.getStartTime(), new Date());

                    // 剩余时长(秒)
                    int i = tool.getDuration() * 60 - second;
                    if (i <= 0) {
                        reEval = GeneralJudgeEnum.NEGATIVE.getValue();
                        remainTime = 0;
                    } else {
                        reEval = GeneralJudgeEnum.CONFIRM.getValue();
                        remainTime = i;
                    }
                }
                userEvaluatorId = assessAnswerRecord.getUserEvaluatorId();
            }
        }

        if (!isOnEval) {

            // 初始化-用户测评-主表
            AssessUser assessUser = new AssessUser()
                .setId(newId())
                .setProId("")
                .setToolId(toolId)
                .setUserId(userId)
                .setEvalStatus(AssessEvalStatusEnum.NO_START.getValue())
                .setReportStatus(GeneralJudgeEnum.NEGATIVE.getValue());
            assessUserService.save(assessUser);

            // 初始化-用户测评-评估人表
            userEvaluatorId = newId();
            AssessUserEvaluator assessUserEvaluator = new AssessUserEvaluator()
                .setId(userEvaluatorId)
                .setToolId(toolId)
                .setProId("")
                .setAssessUserId(assessUser.getId())
                .setUserId(userId)
                .setEvaluatorId(userId)
                .setMethodType(AssessMethodTypeEnum.SELF.getValue())
                .setIsComplete(GeneralJudgeEnum.NEGATIVE.getValue());
            assessUserEvaluatorService.save(assessUserEvaluator);

            // 初始化-用户测评-答题记录表
            assessAnswerRecord = new AssessAnswerRecord()
                .setId(newId())
                .setUserEvaluatorId(assessUserEvaluator.getId())
                .setUserId(userId)
                .setAnswerBy(userId)
                .setStartTime(date)
                .setIsPost(GeneralJudgeEnum.NEGATIVE.getValue());
            assessAnswerRecordService.save(assessAnswerRecord);
        }

        String newVersion = String.valueOf(date.getTime());

        // 返回题目
        return new AssessQuestionListDTO()
            .setId(assessAnswerRecord.getId())
            .setUserEvaluatorId(userEvaluatorId)
            .setToolId(toolId)
            .setToolName(tool.getName())
            .setPageMode(tool.getPageMode())
            .setDuration(tool.getDuration())
            .setReEval(reEval)
            .setRemainTime(remainTime)
            .setQuestionList(questionList)
            .setQuestionCount(questionList.size())
            .setVersion(newVersion);
    }

    // 项目测评，需要校验测评人身份。
    @Override
    public AssessQuestionListDTO getEvalAssessQuestionList(String userEvaluatorId, String recordId, String version) {

        // 当前登录用户
        String userId = UserThreadContext.getUserId();

        // 测评任务（测评用户与测评人关联表）
        AssessUserEvaluator userEvaluator = assessUserEvaluatorService.checkAssessUserEvaluator(userEvaluatorId);

        // 测评人列表身份校验（当前用户是否为测评人）
        if (!userEvaluator.getEvaluatorId().equals(userId)) {
            throw new BusinessException(ErrorNoEnum.ERR_NO_POWER);
        }

        // 测评用户
        AssessUser assessUser = assessUserService.checkAssessUser(userEvaluator.getAssessUserId());

        // 测评项目
        AssessProject assessProject = Optional
            .ofNullable(assessProjectService.getById(assessUser.getProId()))
            .orElseThrow(() -> new BusinessException(ErrorNoEnum.ERR_RESOURCE_NOT_EXIST));

        // 发布状态校验
        checkProjectPublish(assessProject);

        // 测评工具
        AssessTool tool = Optional
            .ofNullable(assessToolService.getById(assessProject.getToolId()))
            .orElseThrow(() -> new BusinessException(ErrorNoEnum.ERR_RESOURCE_NOT_EXIST));

        // 发布状态校验
        checkToolPublish(tool);

        // 获取测评工具题目列表
        List<AssessQuestionDTO> questionList = getQuestionDtoListByToolId(assessUser.getToolId());

        // 版本号
        Date date = new Date();
        AssessAnswerRecord assessAnswerRecord = null;
        boolean isOnEval = false;
        Integer reEval = GeneralJudgeEnum.CONFIRM.getValue();
        int remainTime = tool.getDuration() * 60;

        // 如果是继续答题
        if (StringUtils.isNotBlank(recordId) && StringUtils.isNotBlank(version)) {
            assessAnswerRecord = assessAnswerRecordService.getById(recordId);
            if (Optional.ofNullable(assessAnswerRecord).isPresent() && version.equals(
                String.valueOf(assessAnswerRecord.getStartTime().getTime()))) {
                date = assessAnswerRecord.getStartTime();
                isOnEval = true;

                // 是否交卷
                checkIsEvalPost(assessAnswerRecord);

                // 有限定测评时长
                if (!tool.getDuration().equals(0)) {
                    // 已用时长（秒）
                    Integer second = DateUtil.secondBetweenDate(assessAnswerRecord.getStartTime(), new Date());

                    // 剩余时长（秒）
                    int i = tool.getDuration() * 60 - second;
                    if (i <= 0) {
                        reEval = GeneralJudgeEnum.NEGATIVE.getValue();
                        remainTime = 0;
                    } else {
                        reEval = GeneralJudgeEnum.CONFIRM.getValue();
                        remainTime = i;
                    }
                }
            }
        }

        if (!isOnEval) {
            // 初始化-用户测评-答题记录表
            assessAnswerRecord = new AssessAnswerRecord()
                .setId(newId())
                .setUserEvaluatorId(userEvaluator.getId())
                .setUserId(userEvaluator.getUserId())
                .setAnswerBy(userId)
                .setStartTime(date)
                .setIsPost(GeneralJudgeEnum.NEGATIVE.getValue());
            assessAnswerRecordService.save(assessAnswerRecord);
        }

        String newVersion = String.valueOf(date.getTime());

        // 返回题目
        return new AssessQuestionListDTO()
            .setId(assessAnswerRecord.getId())
            .setUserEvaluatorId(userEvaluator.getId())
            .setToolId(tool.getId())
            .setToolName(tool.getName())
            .setProId(assessProject.getId())
            .setProName(assessProject.getName())
            .setPageMode(tool.getPageMode())
            .setDuration(tool.getDuration())
            .setReEval(reEval)
            .setRemainTime(remainTime)
            .setQuestionList(questionList)
            .setQuestionCount(questionList.size())
            .setVersion(newVersion);
    }

    //region 提交测评
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void answerSubmit(String recordId, String userId, AssessSubmitDTO assessSubmitDTO) {

        //region 01 数据校验

        // 测评记录
        AssessAnswerRecord answerRecord = assessAnswerRecordService.getById(recordId);
        if (answerRecord == null) {
            log.error("测评答题 - 无答题记录,异常提交,recordId:{}, userId:{}", recordId, userId);
            throw new BusinessException(CertificationErrorNoEnum.ERR_ASSESS_RECORD_EMPTY);
        }

        // 提交测评或获取题目时，校验是否已经交卷
        checkIsEvalPost(answerRecord);

        // 测评人
        AssessUserEvaluator userEvaluator = assessUserEvaluatorService.checkAssessUserEvaluator(
            answerRecord.getUserEvaluatorId());
        if (!userEvaluator.getEvaluatorId().equals(userId)) {
            throw new BusinessException(CertificationErrorNoEnum.ERR_ASSESS_NO_POWER);
        }

        // 被测人
        AssessUser assessUser = assessUserService.checkAssessUser(userEvaluator.getAssessUserId());

        // 是否自行测评（自行测评的测评项目ID为空字符串）
        boolean isSelfEval = StringUtils.isBlank(assessUser.getProId());

        // 测评项目
        AssessProject assessProject = null;
        if (!isSelfEval) {
            assessProject = Optional
                .ofNullable(assessProjectService.getById(assessUser.getProId()))
                .orElseThrow(() -> new BusinessException(ErrorNoEnum.ERR_RESOURCE_NOT_EXIST));
            checkProjectPublish(assessProject);
        }
        // 测评工具
        AssessTool tool = Optional
            .ofNullable(assessToolService.getById(assessUser.getToolId()))
            .orElseThrow(() -> new BusinessException(ErrorNoEnum.ERR_RESOURCE_NOT_EXIST));
        checkToolPublish(tool);

        //endregion

        //region 02 答题明细

        // 批量获取测评工具题目列表
        List<AssessToolQuestion> toolQuestionList = assessToolQuestionService.list(
            new LambdaQueryWrapper<AssessToolQuestion>()
                .eq(AssessToolQuestion::getToolId, assessUser.getToolId()));
        List<String> questionIds = toolQuestionList.stream().map(AssessToolQuestion::getId)
            .collect(Collectors.toList());

        // 批量获取题目选项列表
        List<AssessToolQuestionOption> optionList = assessToolQuestionOptionService.list(
            new LambdaQueryWrapper<AssessToolQuestionOption>()
                .in(AssessToolQuestionOption::getQuestionId, questionIds)
                .orderByAsc(AssessToolQuestionOption::getOptionIndex)
        );

        // 用户提交的答案 map 以题目ID分组
        Map<String, AssessSubmitAnswerDTO> answerDtoMap = assessSubmitDTO.getAnswer().stream()
            .collect(Collectors.toMap(AssessSubmitAnswerDTO::getQuestionId, dto -> dto, (v1, v2) -> v2));

        // 遍历所有题目，根据用户提交的答案进行处理
        List<AssessAnswerRecordDetail> answerDetailList = new ArrayList<>();
        for (AssessToolQuestion toolQuestion : toolQuestionList) {
            AssessSubmitAnswerDTO assessSubmitAnswerDTO = answerDtoMap.get(toolQuestion.getId());
            AssessAnswerRecordDetail answerDetail;

            if (Optional.ofNullable(assessSubmitAnswerDTO).isPresent()) {
                // 有提交答案
                answerDetail = new AssessAnswerRecordDetail()
                    .setId(newId())
                    .setRecordId(recordId)
                    .setProId(assessUser.getProId())
                    .setToolId(assessUser.getToolId())
                    .setUserId(assessUser.getUserId())
                    .setQuestionId(toolQuestion.getId())
                    .setAnswerBy(userId)
                    .setAnswerTime(new Date());

                // 当前题目选项列表
                List<AssessToolQuestionOption> currentOptionList = optionList.stream()
                    .filter(o -> o.getQuestionId().equals(toolQuestion.getId()))
                    .collect(Collectors.toList());

                // 设置答案
                String userAnswer = getUserAnswer(
                    assessSubmitAnswerDTO,
                    Objects.requireNonNull(AssessQuestionTypeEnum.getItem(toolQuestion.getQuestionType())),
                    currentOptionList
                );
                answerDetail.setUserAnswer(userAnswer);

                //  设置分数
                BigDecimal score = getUserAnswerScore(
                    assessSubmitAnswerDTO.getOptionAnswer(),
                    AssessComputedModeEnum.getItem(toolQuestion.getComputeMode()),
                    currentOptionList);
                answerDetail.setScore(score);

            } else {
                // 未提交答案
                answerDetail = new AssessAnswerRecordDetail()
                    .setId(newId())
                    .setRecordId(recordId)
                    .setProId(assessUser.getProId())
                    .setToolId(assessUser.getToolId())
                    .setUserId(assessUser.getUserId())
                    .setQuestionId(toolQuestion.getId())
                    .setAnswerBy(userId)
                    .setAnswerTime(new Date())
                    .setUserAnswer(StringUtils.EMPTY)
                    .setScore(BigDecimal.ZERO);
            }
            answerDetailList.add(answerDetail);

        }
        // 答题明细批量保存
        assessAnswerRecordDetailService.saveBatch(answerDetailList);

        //endregion

        //region 03 答题记录

        // 答题记录更新
        LambdaUpdateWrapper<AssessAnswerRecord> recordUpdateWrapper = new LambdaUpdateWrapper<AssessAnswerRecord>()
            .eq(AssessAnswerRecord::getId, recordId)
            .set(AssessAnswerRecord::getIsPost, CertificationConstants.ASSESS_IS_POST)
            .set(AssessAnswerRecord::getAnswerTime, new Date());
        assessAnswerRecordService.update(recordUpdateWrapper);
        //endregion

        //region 04 测评结果

        // 测评工具能力配置列表
        List<AssessToolAbilityConfig> abilityConfigList = assessToolAbilityConfigService.list(
            new LambdaQueryWrapper<AssessToolAbilityConfig>()
                .eq(AssessToolAbilityConfig::getToolId, assessUser.getToolId()));

        // 测评工具能力等级列表
        List<String> configIdList = abilityConfigList.stream().map(AssessToolAbilityConfig::getId)
            .collect(Collectors.toList());
        List<AssessToolAbilityLevel> levelList = assessToolAbilityLevelService.list(
            new LambdaQueryWrapper<AssessToolAbilityLevel>()
                .in(AssessToolAbilityLevel::getAbilityConfigId, configIdList));

        // 执行测评结果生成或更新
        saveOrUpdateReportDetail(
            answerDetailList,
            toolQuestionList,
            assessUser,
            userEvaluator,
            abilityConfigList,
            levelList);

        // 更新测评结果
        LambdaUpdateWrapper<AssessUserEvaluator> userEvaluatorUpdateWrapper = new LambdaUpdateWrapper<AssessUserEvaluator>()
            .eq(AssessUserEvaluator::getId, answerRecord.getUserEvaluatorId())
            .set(AssessUserEvaluator::getIsComplete, GeneralJudgeEnum.CONFIRM.getValue())
            .set(AssessUserEvaluator::getCompleteTime, new Date());
        assessUserEvaluatorService.update(userEvaluatorUpdateWrapper);
        //endregion

        //region 05 测评报告

        // 所有测评人
        List<AssessUserEvaluator> evaluatorList = assessUserEvaluatorService.list(
            new LambdaQueryWrapper<AssessUserEvaluator>()
                .eq(AssessUserEvaluator::getAssessUserId, assessUser.getId())
                .select(AssessUserEvaluator::getIsComplete, AssessUserEvaluator::getMethodType));

        // 是否所有测评人均完成测评
        boolean isAllEvalCompleted = evaluatorList.stream()
            .allMatch(p -> p.getIsComplete().equals(GeneralJudgeEnum.CONFIRM.getValue()));

        // 校验测评项目是否所有测评方式均完成
        boolean isAllMethodCompleted = checkProjectAllEvalTypeComplete(assessProject, evaluatorList);

        // 生成测评报告
        LambdaUpdateWrapper<AssessUser> userUpdateWrapper = new LambdaUpdateWrapper<AssessUser>()
            .eq(AssessUser::getId, assessUser.getId());
        if (isAllEvalCompleted && isAllMethodCompleted) {

            // 执行测评报告生成或更新
            saveOrUpdateReport(assessUser, isSelfEval, assessProject, abilityConfigList, levelList);

            // 更新用户测评状态（已完成）
            userUpdateWrapper
                .set(AssessUser::getEvalStatus, AssessEvalStatusEnum.COMPLETE.getValue())
                .set(AssessUser::getReportStatus, GeneralJudgeEnum.CONFIRM.getValue())
                .set(AssessUser::getReportTime, new Date());

        } else {
            // 更新用户测评状态（进行中）
            userUpdateWrapper
                .set(AssessUser::getEvalStatus, AssessEvalStatusEnum.IN_PROGRESS.getValue());
        }
        assessUserService.update(userUpdateWrapper);
        //endregion

        //region 06 推荐课程

        // 执行条件  1:不是自行测评；2:测评人为自己或上级；3:系统参数配置已开启“测评报告建议课程是否自动纳入学习计划”
        if (assessProject != null && (
            Objects.equals(userEvaluator.getMethodType(), AssessMethodTypeEnum.SELF.getValue())
                || Objects.equals(userEvaluator.getMethodType(), AssessMethodTypeEnum.UPPER.getValue()))
            && Objects.equals(getConfigAutoSuggestToPlan(), GeneralJudgeEnum.CONFIRM.getValue())
        ) {
            List<String> suggestCourseIdList = getSuggestCourseIdList(tool,
                abilityConfigList.stream().map(AssessToolAbilityConfig::getAbilityId).collect(Collectors.toList()));
            courseFeign.assessCourseToPlan(suggestCourseIdList, userEvaluator.getUserId());
        }

        //endregion

        //region 07 项目完成率
        if (assessProject != null) {
            assessProjectService.updateCompleteRate(assessProject.getId());
        }
        //endregion
    }
    //endregion

    /**
     * 生成或更新测评报告
     *
     * @param assessUser        测评用户（被测人对象）
     * @param isSelfEval        是否自行测评
     * @param assessProject     测评项目信息
     * @param abilityConfigList 测评工具能力项配置信息列表
     * @param levelList         测评工具能力项等级配置列表
     */
    private void saveOrUpdateReport(
        AssessUser assessUser,
        boolean isSelfEval,
        AssessProject assessProject,
        List<AssessToolAbilityConfig> abilityConfigList,
        List<AssessToolAbilityLevel> levelList) {

        // 获取所有测评人（查询测评人表主键和测评方式）
        List<AssessUserEvaluator> evaluatorList = assessUserEvaluatorService.list(
            new LambdaQueryWrapper<AssessUserEvaluator>()
                .eq(AssessUserEvaluator::getAssessUserId, assessUser.getId())
                .select(AssessUserEvaluator::getId, AssessUserEvaluator::getMethodType));
        List<String> userEvaluatorIdList = evaluatorList.stream().map(AssessUserEvaluator::getId)
            .collect(Collectors.toList());

        // 获取所有测评结果（每一个测评人有且仅有一份）
        List<AssessUserReportDetail> detailList = assessUserReportDetailService.list(
            new LambdaQueryWrapper<AssessUserReportDetail>()
                .in(AssessUserReportDetail::getAssessUserEvaluatorId, userEvaluatorIdList));
        if (detailList.isEmpty()) {
            return;
        }

        List<AssessUserReportSummary> reportSummaryList;

        // 自行测评 —— 测评结果即测评报告 且  仅有一个人的测评结果
        if (isSelfEval) {
            reportSummaryList = detailList.stream().map(p ->
                    new AssessUserReportSummary()
                        .setAssessUserId(assessUser.getId())
                        .setToolId(assessUser.getToolId())
                        .setProId(assessUser.getProId())
                        .setUserId(assessUser.getUserId())
                        .setAbilityId(p.getAbilityId())
                        .setAbilityName(p.getAbilityName())
                        .setAbilityType(p.getAbilityType())
                        .setAbilityTypeName(p.getAbilityTypeName())
                        .setAbilityCategoryId(p.getAbilityCategoryId())
                        .setAbilityCategoryName(p.getAbilityCategoryName())
                        .setScore(p.getScore())
                        .setScoreSelf(p.getScore())
                        .setScoreLevel(p.getScoreLevel())
                        .setSuggestion(p.getSuggestion()))
                .collect(Collectors.toList());
        }
        // 项目测评 —— 根据测评权重计算能力分值
        else {

            // 根据测评方式进行分组 - 用于根据测评人身份进行分组求平均值
            Map<Integer, List<String>> methodAndEvalIdListMap = evaluatorList.stream().collect(
                Collectors.groupingBy(AssessUserEvaluator::getMethodType,
                    Collectors.mapping(AssessUserEvaluator::getId, Collectors.toList())));

            // 自评
            Map<String, Double> selfAbilityIdScoreMap = getAbilityIdScoreMap(AssessMethodTypeEnum.SELF,
                methodAndEvalIdListMap, detailList);
            // 上级
            Map<String, Double> upperAbilityIdScoreMap = getAbilityIdScoreMap(AssessMethodTypeEnum.UPPER,
                methodAndEvalIdListMap, detailList);
            // 平级
            Map<String, Double> peersAbilityIdScoreMap = getAbilityIdScoreMap(AssessMethodTypeEnum.PEERS,
                methodAndEvalIdListMap, detailList);
            // 下级
            Map<String, Double> lowerAbilityIdScoreMap = getAbilityIdScoreMap(AssessMethodTypeEnum.LOWER,
                methodAndEvalIdListMap, detailList);

            // 获取有效能力配置项列表（如果测评结果中无对应的能力项，则需要从能力配置列表中过滤）
            Set<String> abilityIdSet = detailList.stream().map(AssessUserReportDetail::getAbilityId).collect(toSet());
            List<AssessToolAbilityConfig> effectiveConfigList = abilityConfigList.stream()
                .filter(c -> abilityIdSet.contains(c.getAbilityId())).collect(Collectors.toList());

            reportSummaryList = effectiveConfigList.stream().map(p -> {
                AssessUserReportSummary reportSummary = new AssessUserReportSummary()
                    .setAssessUserId(assessUser.getId())
                    .setToolId(assessUser.getToolId())
                    .setProId(assessUser.getProId())
                    .setUserId(assessUser.getUserId())
                    .setAbilityId(p.getAbilityId())
                    .setAbilityName(p.getAbilityName())
                    .setAbilityType(p.getAbilityType())
                    .setAbilityTypeName(p.getAbilityTypeName())
                    .setAbilityCategoryId(p.getAbilityCategoryId())
                    .setAbilityCategoryName(p.getAbilityCategoryName());

                // 当前能力综合分
                BigDecimal score = calcReportAbilityScore(p.getAbilityId(),
                    selfAbilityIdScoreMap,
                    upperAbilityIdScoreMap,
                    peersAbilityIdScoreMap,
                    lowerAbilityIdScoreMap,
                    assessProject);
                reportSummary.setScore(score);

                // 设置各种测评方式的分值
                setMethodScore(reportSummary, selfAbilityIdScoreMap, upperAbilityIdScoreMap, peersAbilityIdScoreMap,
                    lowerAbilityIdScoreMap);

                // 当前能力配置
                Optional<String> configId = abilityConfigList.stream()
                    .filter(c -> c.getAbilityId().equals(p.getAbilityId())).map(AssessToolAbilityConfig::getId)
                    .findAny();

                if (configId.isPresent()) {

                    // 当前能力的等级配置列表
                    List<AssessToolAbilityLevel> configLevelList = levelList.stream()
                        .filter(c -> c.getAbilityConfigId().equals(configId.get())).collect(Collectors.toList());

                    // 根据能力评分，获取能力评级及达标建议
                    setReportAbilityLevel(score, configLevelList, reportSummary);
                }

                return reportSummary;
            }).collect(Collectors.toList());
        }

        // 数据库中已有的测评报告项
        List<AssessUserReportSummary> oldReportSummaryList = assessUserReportSummaryService.list(
            new LambdaQueryWrapper<AssessUserReportSummary>()
                .select(AssessUserReportSummary::getId, AssessUserReportSummary::getAbilityId)
                .eq(AssessUserReportSummary::getAssessUserId, assessUser.getId()));

        // 遍历已有报告，确认新的报告是新增还是更新
        for (AssessUserReportSummary reportSummary : reportSummaryList) {
            // 测评报告项主键ID（用于控制数据是更新还是新增）
            Optional<AssessUserReportSummary> any = oldReportSummaryList.stream()
                .filter(o -> o.getAbilityId().equals(reportSummary.getAbilityId())).findAny();

            if (any.isPresent()) {
                reportSummary.setId(any.get().getId());
            } else {
                reportSummary.setId(StringUtil.newId());
            }

        }

        assessUserReportSummaryService.saveOrUpdateBatch(reportSummaryList);
    }

    /**
     * 根据测评项目配置，计算测试报告最终得分
     *
     * @param abilityId              能力ID
     * @param selfAbilityIdScoreMap  自评平均分
     * @param upperAbilityIdScoreMap 上级平均分
     * @param peersAbilityIdScoreMap 平级平均分
     * @param lowerAbilityIdScoreMap 下级平均分
     * @param assessProject          测试项目对象
     */
    private BigDecimal calcReportAbilityScore(
        String abilityId,
        Map<String, Double> selfAbilityIdScoreMap,
        Map<String, Double> upperAbilityIdScoreMap,
        Map<String, Double> peersAbilityIdScoreMap,
        Map<String, Double> lowerAbilityIdScoreMap,
        AssessProject assessProject) {

        BigDecimal score = BigDecimal.ZERO;
        if (null == assessProject) {
            return score;
        }

        if (Optional.ofNullable(selfAbilityIdScoreMap.get(abilityId)).isPresent()) {
            score = score.add(
                BigDecimal.valueOf(selfAbilityIdScoreMap.get(abilityId)).multiply(assessProject.getWeightSelf()));
        }

        if (Optional.ofNullable(upperAbilityIdScoreMap.get(abilityId)).isPresent()) {
            score = score.add(
                BigDecimal.valueOf(upperAbilityIdScoreMap.get(abilityId)).multiply(assessProject.getWeightUpper()));
        }

        if (Optional.ofNullable(peersAbilityIdScoreMap.get(abilityId)).isPresent()) {
            score = score.add(
                BigDecimal.valueOf(peersAbilityIdScoreMap.get(abilityId)).multiply(assessProject.getWeightPeers()));
        }

        if (Optional.ofNullable(lowerAbilityIdScoreMap.get(abilityId)).isPresent()) {
            score = score.add(
                BigDecimal.valueOf(lowerAbilityIdScoreMap.get(abilityId)).multiply(assessProject.getWeightLower()));
        }

        // 评分分值换算 数据库存的测评方式权重存的不是百分比，而是100倍的实际数值
        score = score.divide(BigDecimal.valueOf(100), 1, RoundingMode.HALF_UP);

        return score;
    }

    /**
     * 新增或修改测评结果
     *
     * @param answerDetailList  答题明细列表
     * @param toolQuestionList  原始题目列表
     * @param assessUser        用户测评信息
     * @param userEvaluator     测评人信息
     * @param abilityConfigList 测评工具能力配置列表
     * @param levelList         测试工具能力等级列表
     */
    private void saveOrUpdateReportDetail(
        List<AssessAnswerRecordDetail> answerDetailList,
        List<AssessToolQuestion> toolQuestionList,
        AssessUser assessUser,
        AssessUserEvaluator userEvaluator,
        List<AssessToolAbilityConfig> abilityConfigList,
        List<AssessToolAbilityLevel> levelList) {

        // 获取已存在的测评结果列表
        List<AssessUserReportDetail> oldReportDetailList = assessUserReportDetailService.list(
            new LambdaQueryWrapper<AssessUserReportDetail>()
                .eq(AssessUserReportDetail::getAssessUserEvaluatorId, userEvaluator.getId()));
        // 旧测评结果基于能力ID进行分组map
        Map<String, AssessUserReportDetail> reportDetailMap = oldReportDetailList.stream()
            .collect(Collectors.toMap(AssessUserReportDetail::getAbilityId, p -> p));

        // 能力与题目id集合Map
        Map<String, Set<String>> abilityIdAndQuestionIdSetMap = toolQuestionList.stream()
            // 过滤分值归属为个人评分的题目
            .filter(p -> p.getScoreType().equals(AssessScoreTypeEnum.PERSON.getType()))
            // 根据能力ID进行分组
            .collect(Collectors.groupingBy(AssessToolQuestion::getAbilityId,
                Collectors.mapping(AssessToolQuestion::getId, Collectors.toSet())));

        // 测评结果明细列表
        List<AssessUserReportDetail> newReportDetailList = new ArrayList<>();

        // 遍历测评能力配置，组装测评明细
        for (AssessToolAbilityConfig config : abilityConfigList) {
            Set<String> questionIdSet = abilityIdAndQuestionIdSetMap.get(config.getAbilityId());
            if (questionIdSet != null) {
                AssessUserReportDetail reportDetail = reportDetailMap.get(config.getAbilityId());

                // 无历史数据，定义新对象，生成新主键ID
                if (Optional.ofNullable(reportDetail).isEmpty()) {
                    reportDetail = new AssessUserReportDetail();
                    reportDetail.setId(newId());
                }

                reportDetail
                    .setAssessUserEvaluatorId(userEvaluator.getId())
                    .setToolId(assessUser.getToolId())
                    .setProId(assessUser.getProId())
                    .setUserId(assessUser.getUserId())
                    .setEvaluatorId(userEvaluator.getEvaluatorId())
                    .setAbilityId(config.getAbilityId())
                    .setAbilityName(config.getAbilityName())
                    .setAbilityType(config.getAbilityType())
                    .setAbilityTypeName(config.getAbilityTypeName())
                    .setAbilityCategoryId(config.getAbilityCategoryId())
                    .setAbilityCategoryName(config.getAbilityCategoryName());

                // 能力计分方式枚举
                AssessAbilityComputedModeEnum scoreModel = AssessAbilityComputedModeEnum.getItem(config.getScoreMode());
                List<AssessAnswerRecordDetail> answerList = answerDetailList.stream()
                    .filter(p -> questionIdSet.contains(p.getQuestionId())).collect(Collectors.toList());
                BigDecimal abilityScore = getAbilityScore(Objects.requireNonNull(scoreModel), answerList);
                reportDetail.setScore(abilityScore);

                // 当前通知的等级配置列表
                List<AssessToolAbilityLevel> configLevelList = levelList.stream()
                    .filter(p -> p.getAbilityConfigId().equals(config.getId())).collect(Collectors.toList());

                // 根据能力评分，获取能力评级及达标建议
                setAbilityLevel(abilityScore, configLevelList, reportDetail);

                newReportDetailList.add(reportDetail);
            }
        }

        assessUserReportDetailService.saveOrUpdateBatch(newReportDetailList);

    }

    /**
     * 根据用户答题结果获取能力评分
     *
     * @param scoreModel 能力计分方式
     * @param answerList 能力相关答题记录
     * @return 能力分值
     */
    private BigDecimal getAbilityScore(AssessAbilityComputedModeEnum scoreModel,
        List<AssessAnswerRecordDetail> answerList) {
        switch (scoreModel) {
            // 题目平均分
            case AVG_SCORE:
                OptionalDouble average = answerList.stream()
                    .mapToDouble(p -> p.getScore().doubleValue())
                    .average();
                return average.isPresent() ? BigDecimal.valueOf(average.getAsDouble()) : BigDecimal.ZERO;

            // 题目合计分
            case SUM_SCORE:
                double sumValue = answerList.stream()
                    .mapToDouble(p -> p.getScore().doubleValue())
                    .sum();
                return BigDecimal.valueOf(sumValue);

            // 题目最低分
            case MIN_SCORE:
                OptionalDouble min = answerList.stream()
                    .mapToDouble(p -> p.getScore().doubleValue())
                    .min();
                return min.isPresent() ? BigDecimal.valueOf(min.getAsDouble()) : BigDecimal.ZERO;

            // 题目最高分
            case MAX_SCORE:
                OptionalDouble max = answerList.stream()
                    .mapToDouble(p -> p.getScore().doubleValue())
                    .max();
                return max.isPresent() ? BigDecimal.valueOf(max.getAsDouble()) : BigDecimal.ZERO;

            default:
                return BigDecimal.ZERO;
        }
    }

    /**
     * 根据题目配置获取用户提交的答案分数
     *
     * @param optionAnswer     用户提交的答案信息（选项列表）
     * @param computedModeEnum 题目计分方式枚举
     * @param optionList       题目答题选项信息（每个选项中有分数值）
     * @return 用户答题分数
     */
    private BigDecimal getUserAnswerScore(List<String> optionAnswer, AssessComputedModeEnum computedModeEnum,
        List<AssessToolQuestionOption> optionList) {

        // 无答题选项提交，可能是问答题或场景说明
        if (optionAnswer.isEmpty()) {
            return BigDecimal.ZERO;
        }

        switch (computedModeEnum) {
            // 选项结果对应分值 非多选题（单选、判断、量表） 此类题目仅会提交一个选项
            case OPTION_SCORE:
                String id = optionAnswer.get(0);
                Optional<AssessToolQuestionOption> first = optionList.stream()
                    .filter(p -> p.getId().equals(id)).findFirst();
                return first.isPresent() ? first.get().getScore() : BigDecimal.ZERO;

            // 选项结果总分分值 多选题
            case OPTION_SUM_SCORE:
                double sumValue = optionList.stream()
                    .filter(p -> optionAnswer.contains(p.getId()))
                    .mapToDouble(p -> p.getScore().doubleValue())
                    .sum();
                return BigDecimal.valueOf(sumValue);

            // 选项结果平均分值 多选题
            case OPTION_AVG_SCORE:
                OptionalDouble average = optionList.stream()
                    .filter(p -> optionAnswer.contains(p.getId()))
                    .mapToDouble(p -> p.getScore().doubleValue())
                    .average();
                return average.isPresent() ? BigDecimal.valueOf(average.getAsDouble()) : BigDecimal.ZERO;

            // 选项结果中最低分值 多选题
            case OPTION_MIN_SCORE:
                OptionalDouble min = optionList.stream()
                    .filter(p -> optionAnswer.contains(p.getId()))
                    .mapToDouble(p -> p.getScore().doubleValue())
                    .min();
                return min.isPresent() ? BigDecimal.valueOf(min.getAsDouble()) : BigDecimal.ZERO;

            // 选项结果中最高分值 多选题
            case OPTION_MAX_SCORE:
                OptionalDouble max = optionList.stream()
                    .filter(p -> optionAnswer.contains(p.getId()))
                    .mapToDouble(p -> p.getScore().doubleValue())
                    .max();
                return max.isPresent() ? BigDecimal.valueOf(max.getAsDouble()) : BigDecimal.ZERO;

            // 异常处理
            default:
                log.error("测评答题 - 计算答题分数出现异常：无匹配的题目计分类型。");
                return BigDecimal.ZERO;
        }
    }

    // 根据题目类型获取用户提交的答案（问答题、场景说明直接返回提交的文本；其他类型的题目返回题目选项CODE的拼接字符串）
    private String getUserAnswer(AssessSubmitAnswerDTO assessSubmitAnswerDTO, AssessQuestionTypeEnum questionTypeEnum,
        List<AssessToolQuestionOption> optionList) {

        switch (questionTypeEnum) {
            case RADIO:
            case JUDGEMENT:
            case TABLE:
                String id = assessSubmitAnswerDTO.getOptionAnswer().get(0);
                Optional<AssessToolQuestionOption> first = optionList.stream()
                    .filter(p -> p.getId().equals(id)).findFirst();
                return first.isPresent() ? first.get().getOptionCode() : StringUtils.EMPTY;
            case MULTI_SELECT:
                List<String> codeList = optionList.stream()
                    .filter(p -> assessSubmitAnswerDTO.getOptionAnswer().contains(p.getId()))
                    .map(AssessToolQuestionOption::getOptionCode).collect(Collectors.toList());
                return String.join(StringUtils.EMPTY, codeList);
            default:
                return assessSubmitAnswerDTO.getShortAnswer();
        }
    }

    @Override
    public void updateAbilityComputeMode(String id, Integer computeMode) {
        List<Integer> collect = Arrays.stream(AssessAbilityComputedModeEnum.values())
            .map(AssessAbilityComputedModeEnum::getValue).collect(
                Collectors.toList());
        if (!collect.contains(computeMode)) {
            throw new BusinessException(CertificationErrorNoEnum.ERR_ABILITY_COMPUTED_MODE);
        }
        assessToolAbilityConfigService.updateById(new AssessToolAbilityConfig().setId(id).setScoreMode(computeMode));
    }

    @Override
    public List<ComputeModeDTO> getAbilityComputeMode() {
        return Arrays.stream(AssessAbilityComputedModeEnum.values())
            .map(modeEnum -> new ComputeModeDTO().setValue(modeEnum.getValue()).setName(I18nUtil.getMessage(modeEnum.getName())))
            .collect(Collectors.toList());
    }

    @Override
    public void updateAbilityCritical(UpdateAbilityCriticalDTO updateAbilityCriticalDTO) {
        List<AbilityCriticalDTO> abilityCriticalDtoList = updateAbilityCriticalDTO
            .getAbilityCriticalDTOS();

        if (CollectionUtils.isEmpty(abilityCriticalDtoList)) {
            return;
        }
        List<AssessToolAbilityLevel> toolAbilityLevels = abilityCriticalDtoList.stream().map(
                abilityCriticalDTO -> new AssessToolAbilityLevel().setId(abilityCriticalDTO.getId())
                    .setLevelScore(abilityCriticalDTO.getLevelScore()).setSuggestion(abilityCriticalDTO.getSuggestion()))
            .collect(
                Collectors.toList());
        assessToolAbilityLevelService.updateBatchById(toolAbilityLevels);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copyAssessToolQuestion(String id) {
        String userId = UserThreadContext.getUserId();
        AssessToolQuestion question = assessToolQuestionService.checkQuestion(id);
        // 复制题目
        AssessToolQuestion copyQuestion = new AssessToolQuestion();
        BeanUtils.copyProperties(question, copyQuestion);
        String copyQuestionId = newId();
        // 复制的题目自动放到该题目的下一个位置
        Integer sortNo = question.getSortNo();
        int newSortNo = sortNo + 1;
        copyQuestion.setId(copyQuestionId)
            .setCreateBy(userId)
            .setCreateTime(new Date())
            .setUpdateTime(new Date())
            .setSortNo(newSortNo)
            .setQuestionName("复制_" + question.getQuestionName())
            .setUpdateBy(userId);
        // 复制题目选项
        List<AssessToolQuestionOption> questionOptions = assessToolQuestionOptionService.getByQuestionId(id);
        questionOptions.forEach(option -> option
            .setId(newId())
            .setQuestionId(copyQuestionId)
            .setCreateBy(userId)
            .setCreateTime(new Date())
            .setUpdateTime(new Date())
            .setUpdateBy(userId));
        //需要更新序号的题目
        List<AssessToolQuestion> questionList = assessToolQuestionService
            .list(new LambdaQueryWrapper<AssessToolQuestion>().gt(AssessToolQuestion::getSortNo, sortNo));
        List<AssessToolQuestion> updateList = questionList.stream().map(toolQuestion -> {
            AssessToolQuestion update = new AssessToolQuestion();
            update.setId(toolQuestion.getId());
            Integer curSortNo = toolQuestion.getSortNo();
            update.setSortNo(curSortNo + 1)
                .setUpdateTime(new Date())
                .setUpdateBy(userId);
            return update;
        }).collect(Collectors.toList());
        // 保存题目
        assessToolQuestionService.saveOrUpdate(copyQuestion);
        // 更新排序
        assessToolQuestionService.updateBatchById(updateList);
        // 保存选项
        assessToolQuestionOptionService.saveOrUpdateBatch(questionOptions);
    }

    @Override
    public void dragSort(QuestionSortDTO questionSortDTO) {
        AssessToolQuestion question = assessToolQuestionService.checkQuestion(questionSortDTO.getId());
        // 获取题目最大排序序号
        int maxSortNo = assessToolQuestionService.getQuestionMaxSortNo(question.getToolId());
        if (Objects.isNull(questionSortDTO.getSortNo())) {
            //如果传进来的排序序号是null，则置为最大值 + 1
            question.setSortNo(maxSortNo + 1);
            assessToolQuestionService.updateById(question);
            return;
        }
        Integer dragType = questionSortDTO.getDragType();
        // 向上拖拽
        if (JudgeEnum.DENY.getValue().equals(dragType)) {
            question.setSortNo(questionSortDTO.getSortNo());
            // 将后面的题目序号递增1
            List<AssessToolQuestion> sortQuestionList = assessToolQuestionService
                .list(new LambdaQueryWrapper<AssessToolQuestion>()
                    .eq(AssessToolQuestion::getToolId, question.getToolId())
                    .ge(AssessToolQuestion::getSortNo, questionSortDTO.getSortNo())
                    .orderByAsc(AssessToolQuestion::getSortNo));
            for (AssessToolQuestion assessToolQuestion : sortQuestionList) {
                assessToolQuestion.setSortNo(assessToolQuestion.getSortNo() + 1);
            }
            sortQuestionList.add(question);
            assessToolQuestionService.updateBatchById(sortQuestionList);
        } else {
            // 向下拖拽
            question.setSortNo(questionSortDTO.getSortNo() + 1);
            // 将后面的题目序号递增1
            List<AssessToolQuestion> sortQuestionList = assessToolQuestionService
                .list(new LambdaQueryWrapper<AssessToolQuestion>()
                    .eq(AssessToolQuestion::getToolId, question.getToolId())
                    .gt(AssessToolQuestion::getSortNo, questionSortDTO.getSortNo())
                    .orderByAsc(AssessToolQuestion::getSortNo));
            for (AssessToolQuestion assessToolQuestion : sortQuestionList) {
                assessToolQuestion.setSortNo(assessToolQuestion.getSortNo() + 1);
            }
            sortQuestionList.add(question);
            assessToolQuestionService.updateBatchById(sortQuestionList);
        }
        // 重置编号
        List<AssessToolQuestion> assessToolQuestions = assessToolQuestionService
            .list(new LambdaQueryWrapper<AssessToolQuestion>()
                .select(AssessToolQuestion::getId, AssessToolQuestion::getSortNo)
                .eq(AssessToolQuestion::getToolId, question.getToolId())
                .orderByAsc(AssessToolQuestion::getSortNo));
        int sortNo = 1;
        for (AssessToolQuestion assessToolQuestion : assessToolQuestions) {
            assessToolQuestion.setSortNo(sortNo++);
        }
        assessToolQuestionService.updateBatchById(assessToolQuestions);
    }

    @Override
    public List<TableDTO> getTableList() {
        Map<String, List<ParaDTO>> paraMapByCategoryIds = paraFeign
            .getParaListByCategoryIdList(Arrays.stream(TableEnum.values()).map(TableEnum::getParentId).collect(
                Collectors.toList()));
        return Arrays.stream(TableEnum.values())
            .map(tableEnum -> {
                List<ParaDTO> paraDTOList = paraMapByCategoryIds.get(tableEnum.getParentId());
                if (paraDTOList != null) {
                    List<TableDetailDTO> tableDTOList = paraDTOList.stream()
                        .map(para -> new TableDetailDTO().setScore(Integer.valueOf(para.getParaValue()))
                            .setName(I18nUtil.getMessage(para.getParaName())))
                        .collect(Collectors.toList());
                    return new TableDTO()
                        .setValue(tableEnum.getValue())
                        .setName(I18nUtil.getMessage(tableEnum.getName()))
                        .setTableDTOS(tableDTOList);
                }
                return new TableDTO();
            })
            .collect(Collectors.toList());
    }

    @Override
    public String downloadAssessQuestionTemplate(String toolId) {
        List<ExcelHead> head = getExcelHeads();

        IExportNoEntityDataDTO exportDataDTO =
            new AbstractExportNoEntityDataDTO<IAssessToolQuestionBiz, AssessToolAbilityDTO>(
                new BaseEntity()) {

                @Override
                protected IAssessToolQuestionBiz getBean() {
                    return SpringUtil.getBean(ASSESS_TOOL_QUESTION_BIZ, IAssessToolQuestionBiz.class);
                }

                @Override
                protected List<List<Object>> getPageInfo() {
                    AssessToolAbilityDTO abilityTree = getBean().getAbilityTree(toolId);
                    List<AssessToolModeAbilitiesTree> modeAbilitiesTree = abilityTree.getModeAbilitiesTree();
                    if (CollectionUtils.isEmpty(modeAbilitiesTree)) {
                        return Collections.emptyList();
                    }
                    List<List<Object>> result = new ArrayList<>();
                    for (AssessToolModeAbilitiesTree assessToolModeAbilitiesTree : modeAbilitiesTree) {
                        for (AssessRelateAbilityDTO assessRelateAbilityDTO : assessToolModeAbilitiesTree.getAbilityDTOList()) {
                            List<Object> columns = new ArrayList<>();
                            columns.add(assessRelateAbilityDTO.getAbilityCategoryName());
                            columns.add(assessRelateAbilityDTO.getAbilityCode());
                            columns.add(assessRelateAbilityDTO.getAbilityName());
                            result.add(columns);
                        }
                    }
                    return result;
                }

                @Override
                public ExportBizType getType() {
                    return ExportBizType.ASSESS_TOOL_QUESTION;
                }

                @Override
                public String getFileName() {
                    return ExportFileNameEnum.ASSESS_TOOL_QUESTION_TEM.getType();
                }
            };
        ExportResultDTO exportResultDTO = exportComponent.exportRecordCustom(exportDataDTO, head);
        return fileFeign.getFileUrl(exportResultDTO.getUrl());
    }

    @Override
    public ImportResultDTO importAssessToolQuestions(ImportExcelDTO importExcelDTO) {
        ImportResultDTO importResultDTO = new ImportResultDTO();
        importResultDTO.setIsSuccess(true);
        // excel表信息
        ImportDataDTO importData = importDataFeign.getImportData(importExcelDTO.getExcelFile());
        String[][] excel = importData.getExcel();
        List<AssessToolQuestionOption> optionList = new ArrayList<>();
        AssessTool assessTool = assessToolService.checkAssessTool(importExcelDTO.getId());
        // 导入校验
        ExcelCheckMessage checkMessage = new AssessToolQuestionImportTemplate(assessToolAbilityConfigService,
            assessToolQuestionService, importExcelDTO.getId(), optionList, assessTool.getScoreType()).check(excel);
        if (!checkMessage.getMessage().isEmpty()) {
            importResultDTO.setIsSuccess(false);
            importResultDTO.setMsg(JsonUtil.objToJson(checkMessage.getMessage()));
            return importResultDTO;
        }
        long beginTime = System.currentTimeMillis();
        // 导入
        @SuppressWarnings("unchecked")
        List<AssessToolQuestion> questionList = (List<AssessToolQuestion>) checkMessage.getObjects();
        if (!CollectionUtils.isEmpty(questionList)) {
            assessToolQuestionService.saveBatch(questionList);
            assessToolQuestionOptionService.saveOrUpdateBatch(optionList);
        }
        log.info(" 耗时：{}", System.currentTimeMillis() - beginTime);
        return importResultDTO;
    }

    @Override
    public PageInfo<AssessAnswerDetailListDTO> answerDetailList(AssessAnswerDetailQuery query) {
        assessToolService.checkAssessTool(query.getToolId());
        if (StringUtils.isNotBlank(query.getOrgId())) {
            OrgDTO orgDTO = orgFeign.getById(query.getOrgId());
            if (orgDTO != null) {
                query.setLevelPath(orgDTO.getLevelPath());
            }
        }
        // 用户Ids
        if (StringUtils.isNotEmpty(query.getUserIds())) {
            String[] stringArray = query.getUserIds().split(",");
            query.setUserIdSet(Arrays.stream(stringArray).collect(toSet()));
        }
        // 测评用户Ids
        if (StringUtils.isNotEmpty(query.getAnswerByIds())) {
            String[] stringArray = query.getAnswerByIds().split(",");
            query.setAnswerByIdSet(Arrays.stream(stringArray).collect(toSet()));
        }
        // 题目Ids
        if (StringUtils.isNotEmpty(query.getQuestionIds())) {
            String[] stringArray = query.getQuestionIds().split(",");
            query.setQuestionIdSet(Arrays.stream(stringArray).collect(toSet()));
        }

        PageInfo<AssessAnswerDetailListDTO> pageInfo = PageMethod.startPage(query.getPageNo(), query.getPageSize())
            .doSelectPageInfo(() -> assessToolQuestionService.answerDetailList(query));

        buildAnswerDetailList(pageInfo.getList());
        return pageInfo;
    }

    private void buildAnswerDetailList(List<AssessAnswerDetailListDTO> detailListDtoList) {
        // 用户id集合
        Set<String> userIdSet = new HashSet<>();
        userIdSet.addAll(detailListDtoList.stream().map(AssessAnswerDetailListDTO::getUserId).collect(toSet()));
        userIdSet.addAll(detailListDtoList.stream().map(AssessAnswerDetailListDTO::getAnswerBy).collect(toSet()));
        Map<String, UserDTO> simpleUserMap = userFeign.getSimpleUserMapIgnoreDel(userIdSet);
        Map<String, OrgShowDTO> orgShowDtoMap = orgFeign
            .getOrgShowDTO(simpleUserMap.values().stream().map(UserDTO::getOrgId).collect(toSet()));
        // 题目id集合
        Set<String> questionIds = detailListDtoList.stream().map(AssessAnswerDetailListDTO::getQuestionId)
            .collect(toSet());
        Map<String, AssessToolQuestion> questionMap = assessToolQuestionService.findQuestionMapByIds(questionIds);
        // 项目id集合
        Set<String> proIds = detailListDtoList.stream().map(AssessAnswerDetailListDTO::getProId).collect(toSet());
        Map<String, AssessProject> projectMap = assessProjectService.findProjectMapByIds(proIds);
        for (AssessAnswerDetailListDTO detailListDTO : detailListDtoList) {

            detailListDTO.setMethodTypeName(AssessMethodTypeEnum.getNameByValue(detailListDTO.getMethodType()));

            Optional.ofNullable(simpleUserMap.get(detailListDTO.getUserId()))
                .ifPresent(u -> {
                    detailListDTO.setLoginName(
                            u.getIsDel() == 0 ? u.getLoginName() : u.getLoginName().replaceAll(">.*", "(已删除)"))
                        .setFullName(u.getFullName());

                    Optional.ofNullable(orgShowDtoMap.get(u.getOrgId())).ifPresent(
                        orgShowDTO -> detailListDTO.setOrgName(orgShowDTO.getOrgShortName())
                            .setOrgPath(orgShowDTO.getLevelPathName()));
                });
            Optional.ofNullable(questionMap.get(detailListDTO.getQuestionId())).ifPresent(question -> {
                detailListDTO.setQuestionName(question.getQuestionName());
                detailListDTO.setQuestionType(question.getQuestionType());
                detailListDTO.setQuestionTypeName(AssessQuestionTypeEnum.getNameByType(question.getQuestionType()));
            });

            Optional.ofNullable(projectMap.get(detailListDTO.getProId())).ifPresent(assessProject -> {
                detailListDTO.setProName(assessProject.getName());
                detailListDTO.setProNo(assessProject.getProNo());
                detailListDTO.setAssessDate(assessProject.getStartTime());
            });

            Optional.ofNullable(simpleUserMap.get(detailListDTO.getAnswerBy()))
                .ifPresent(u -> detailListDTO.setAssessLoginName(
                        u.getIsDel() == 0 ? u.getLoginName() : u.getLoginName().replaceAll(">.*", "(已删除)"))
                    .setAssessUser(u.getFullName()));
        }
    }

    @Override
    public PageInfo<AssessToolQuestionSimpleDTO> listPage(AssessToolQuestionPageQuery assessToolQuestionPageQuery) {
        String toolId = assessToolQuestionPageQuery.getToolId();
        assessToolService.checkAssessTool(toolId);
        String questionName = assessToolQuestionPageQuery.getQuestionName();
        PageInfo<AssessToolQuestion> pageInfo = PageMethod
            .startPage(assessToolQuestionPageQuery.getPageNo(), assessToolQuestionPageQuery.getPageSize())
            .doSelectPageInfo(() -> assessToolQuestionService
                .list(new LambdaQueryWrapper<AssessToolQuestion>().eq(AssessToolQuestion::getToolId, toolId)
                    .like(StringUtils.isNotBlank(questionName), AssessToolQuestion::getQuestionName, questionName)
                    .orderByAsc(AssessToolQuestion::getSortNo)));
        PageInfo<AssessToolQuestionSimpleDTO> result = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, result);

        List<AssessToolQuestion> toolQuestions = pageInfo.getList();
        if (CollectionUtils.isEmpty(toolQuestions)) {
            result.setList(Collections.emptyList());
            return result;
        }
        List<AssessToolQuestionSimpleDTO> assessToolQuestionSimpleDtoList = BeanListUtils
            .copyListProperties(toolQuestions, AssessToolQuestionSimpleDTO::new);
        result.setList(assessToolQuestionSimpleDtoList);
        // 填充属性
        assessToolQuestionSimpleDtoList.forEach(assessToolQuestionSimpleDTO -> assessToolQuestionSimpleDTO
            .setQuestionTypeName(AssessQuestionTypeEnum.getNameByType(assessToolQuestionSimpleDTO.getQuestionType())));
        return result;
    }

    @Override
    public void exportAnswerDetail(AssessAnswerDetailQuery assessAnswerDetailQuery) {
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IAssessToolQuestionBiz, AssessAnswerDetailListDTO>(
            assessAnswerDetailQuery) {
            @Override
            protected IAssessToolQuestionBiz getBean() {
                return SpringUtil.getBean(ASSESS_TOOL_QUESTION_BIZ, IAssessToolQuestionBiz.class);
            }

            @Override
            protected PageInfo<AssessAnswerDetailListDTO> getPageInfo() {
                return getBean().answerDetailList(assessAnswerDetailQuery);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.ASSESS_QUESTION_DETAIL;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.ASSESS_QUESTION_DETAIL_LIST.getType();
            }

        };
        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public AssessRelateAbilityDTO getAbilityQuestionInfo(String id) {
        AssessRelateAbilityDTO abilityDTO = new AssessRelateAbilityDTO();
        AssessToolAbilityConfig abilityConfig = assessToolAbilityConfigService.getById(id);
        if (Objects.nonNull(abilityConfig)) {
            BeanUtils.copyProperties(abilityConfig, abilityDTO);
            long questionCount = assessToolQuestionService.count(new LambdaQueryWrapper<AssessToolQuestion>()
                .eq(AssessToolQuestion::getToolId, abilityConfig.getToolId())
                .eq(AssessToolQuestion::getAbilityId, abilityConfig.getAbilityId()));
            abilityDTO.setQuestionCount(questionCount);
        }
        return abilityDTO;
    }

    private List<ExcelHead> getExcelHeads() {
        List<ExcelHead> head = new ArrayList<>(21);

        // 表头字段
        head.add(new ExcelHead().setHeadName("能力分类").setFillForegroundColor(IndexedColors.YELLOW.index));
        head.add(new ExcelHead().setHeadName("能力编码").setFillForegroundColor(IndexedColors.YELLOW.index));
        head.add(new ExcelHead().setHeadName("能力名称").setFillForegroundColor(IndexedColors.YELLOW.index));
        head.add(
            new ExcelHead()
                .setHeadName("分值归属")
                .setHeadFrontColor(IndexedColors.RED.index)
                .setFillForegroundColor(IndexedColors.YELLOW.index)
                .setSelect(true)
                .setSelectList(
                    Arrays.stream(AssessScoreTypeEnum.values())
                        .map(AssessScoreTypeEnum::getName)
                        .collect(Collectors.toList())));
        head.add(
            new ExcelHead()
                .setHeadName("题型")
                .setHeadFrontColor(IndexedColors.RED.index)
                .setFillForegroundColor(IndexedColors.YELLOW.index)
                .setSelect(true)
                .setSelectList(
                    Arrays.stream(AssessQuestionTypeEnum.values())
                        .map(AssessQuestionTypeEnum::getName)
                        .collect(Collectors.toList())));
        head.add(
            new ExcelHead()
                .setHeadName("计分方式")
                .setHeadFrontColor(IndexedColors.RED.index)
                .setFillForegroundColor(IndexedColors.YELLOW.index)
                .setSelect(true)
                .setSelectList(
                    Arrays.stream(AssessComputedModeEnum.values())
                        .map(AssessComputedModeEnum::getName)
                        .collect(Collectors.toList())));
        head.add(
            new ExcelHead()
                .setHeadName("题目")
                .setHeadFrontColor(IndexedColors.RED.index)
                .setFillForegroundColor(IndexedColors.YELLOW.index));
        head.add(new ExcelHead().setHeadName("选项A").setFillForegroundColor(IndexedColors.YELLOW.index));
        head.add(new ExcelHead().setHeadName("选项B").setFillForegroundColor(IndexedColors.YELLOW.index));
        head.add(new ExcelHead().setHeadName("选项C").setFillForegroundColor(IndexedColors.YELLOW.index));
        head.add(new ExcelHead().setHeadName("选项D").setFillForegroundColor(IndexedColors.YELLOW.index));
        head.add(new ExcelHead().setHeadName("选项E").setFillForegroundColor(IndexedColors.YELLOW.index));
        head.add(new ExcelHead().setHeadName("选项F").setFillForegroundColor(IndexedColors.YELLOW.index));
        head.add(new ExcelHead().setHeadName("选项G").setFillForegroundColor(IndexedColors.YELLOW.index));
        head.add(new ExcelHead().setHeadName("选项A分值").setFillForegroundColor(IndexedColors.YELLOW.index));
        head.add(new ExcelHead().setHeadName("选项B分值").setFillForegroundColor(IndexedColors.YELLOW.index));
        head.add(new ExcelHead().setHeadName("选项C分值").setFillForegroundColor(IndexedColors.YELLOW.index));
        head.add(new ExcelHead().setHeadName("选项D分值").setFillForegroundColor(IndexedColors.YELLOW.index));
        head.add(new ExcelHead().setHeadName("选项E分值").setFillForegroundColor(IndexedColors.YELLOW.index));
        head.add(new ExcelHead().setHeadName("选项F分值").setFillForegroundColor(IndexedColors.YELLOW.index));
        head.add(new ExcelHead().setHeadName("选项G分值").setFillForegroundColor(IndexedColors.YELLOW.index));
        return head;
    }

    private void setAbilityLevel(BigDecimal abilityScore, List<AssessToolAbilityLevel> levelList,
        AssessUserReportDetail reportDetail) {

        if (levelList.isEmpty()) {
            return;
        }

        // 根据临界分值对当前等级数据进行排序(降序)
        List<AssessToolAbilityLevel> descSortLevelList = levelList.stream()
            .sorted(Comparator.comparing(AssessToolAbilityLevel::getLevelScore, Comparator.reverseOrder()))
            .collect(Collectors.toList());

        // 从大到小遍历，数据命中即返回
        for (AssessToolAbilityLevel assessToolAbilityLevel : descSortLevelList) {
            if (abilityScore.compareTo(assessToolAbilityLevel.getLevelScore()) >= 0) {
                reportDetail.setScoreLevel(assessToolAbilityLevel.getLevelName());
                reportDetail.setSuggestion(assessToolAbilityLevel.getSuggestion());
                return;
            }
        }

        // 如果未命中，则取最小等级
        AssessToolAbilityLevel last = descSortLevelList.get(descSortLevelList.size() - 1);
        reportDetail.setScoreLevel(last.getLevelName());
        reportDetail.setSuggestion(last.getSuggestion());

    }

    private void setReportAbilityLevel(BigDecimal abilityScore, List<AssessToolAbilityLevel> levelList,
        AssessUserReportSummary reportSummary) {

        if (!levelList.isEmpty()) {
            // 根据临界分值对当前等级数据进行排序(降序)
            List<AssessToolAbilityLevel> descSortLevelList = levelList.stream()
                .sorted(Comparator.comparing(AssessToolAbilityLevel::getLevelScore, Comparator.reverseOrder()))
                .collect(Collectors.toList());

            // 从大到小遍历，数据命中即返回
            for (AssessToolAbilityLevel assessToolAbilityLevel : descSortLevelList) {
                if (abilityScore.compareTo(assessToolAbilityLevel.getLevelScore()) >= 0) {
                    reportSummary.setScoreLevel(assessToolAbilityLevel.getLevelName());
                    reportSummary.setSuggestion(assessToolAbilityLevel.getSuggestion());
                    return;
                }
            }

            // 如果未命中，则取最小等级
            AssessToolAbilityLevel last = descSortLevelList.get(descSortLevelList.size() - 1);
            reportSummary.setScoreLevel(last.getLevelName());
            reportSummary.setSuggestion(last.getSuggestion());
        }
    }

    /**
     * 校验测评工具下发范围
     *
     * @param toolId 测评工具id
     */
    private void checkAssessToolViewLimit(String toolId) {
        String userId = UserThreadContext.getUserId();
        if (!resourceViewLimitService.checkViewLimit(toolId, LimitTable.AssessToolViewLimit.name(), userId)) {
            throw new BusinessException(ErrorNoEnum.ERR_NO_POWER);
        }
    }

    /**
     * 获取测评工具题目列表
     *
     * @param toolId 测评工具id
     * @return 测评工具题目列表
     */
    private List<AssessQuestionDTO> getQuestionDtoListByToolId(String toolId) {
        // 获取题目列表
        List<AssessToolQuestion> toolQuestionList = assessToolQuestionService.list(
            new LambdaQueryWrapper<AssessToolQuestion>()
                .eq(AssessToolQuestion::getToolId, toolId)
                .orderByAsc(AssessToolQuestion::getSortNo));

        if (toolQuestionList.isEmpty()) {
            return new ArrayList<>();
        }

        // 题目id列表
        List<String> questionIds = toolQuestionList.stream().map(AssessToolQuestion::getId)
            .collect(Collectors.toList());

        // 获取题目选项列表
        List<AssessToolQuestionOption> optionList = assessToolQuestionOptionService.list(
            new LambdaQueryWrapper<AssessToolQuestionOption>()
                .in(AssessToolQuestionOption::getQuestionId, questionIds)
                .orderByAsc(AssessToolQuestionOption::getOptionIndex)
        );

        // 封装题目列表
        List<AssessQuestionDTO> questionList = new ArrayList<>(toolQuestionList.size());
        toolQuestionList.forEach(q -> {
            List<AssessQuestionOptionDTO> optionDtoList = optionList.stream()
                .filter(o -> o.getQuestionId().equals(q.getId())).map(o -> new AssessQuestionOptionDTO()
                    .setOptionId(o.getId())
                    .setOptionDesc(o.getOptionName())
                    .setOptionCode(o.getOptionCode())
                    .setOptionIndex(o.getOptionIndex())).collect(Collectors.toList());

            AssessQuestionDTO dto = new AssessQuestionDTO()
                .setOptionList(optionDtoList)
                .setQuestionId(q.getId())
                .setQuestionType(q.getQuestionType())
                .setQuestionName(q.getQuestionName())
                .setSortNo(q.getSortNo())
                .setUserAnswer("");

            questionList.add(dto);
        });
        return questionList;
    }

    @Override
    public Integer getConfigAutoSuggestToPlan() {
        String paraValue = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_22014.getCode());
        return Objects.equals(GeneralJudgeEnum.CONFIRM.getValue(), Integer.parseInt(paraValue))
            ? GeneralJudgeEnum.CONFIRM.getValue()
            : GeneralJudgeEnum.NEGATIVE.getValue();
    }

    @Override
    public AssessResultClientDTO getEvalResult(String taskId) {

        AssessUserEvaluator task = assessUserEvaluatorService.checkAssessUserEvaluator(taskId);

        AssessResultClientDTO dto = new AssessResultClientDTO();

        // 获取测评结果明细列表
        List<AssessUserReportDetail> detailList = assessUserReportDetailService.list(
            new LambdaQueryWrapper<AssessUserReportDetail>()
                .eq(AssessUserReportDetail::getAssessUserEvaluatorId, taskId));

        // 程序说明：此处不验证测评结果数据是否为空，哪怕是空的，也要返回测评工具和测评项目信息

        // 数据转换
        List<AssessResultDetailClientDTO> dtoList = BeanListUtils.copyList(detailList,
            AssessResultDetailClientDTO.class);

        // 设置能力项的标准值和平均值
        setResultDetailScoreValue(task.getToolId(), dtoList);
        dto.setDetailList(dtoList);

        // 测评结果生成时间
        dto.setCompleteTime(task.getCompleteTime());

        // 设置测评工具和测评项目信息
        setResultToolAndProjectInfo(task.getToolId(), task.getProId(), task.getUserId(), dto);

        // 设置雷达图信息
        List<AssessReportDetailClientDTO> rdList = BeanListUtils.copyListProperties(dtoList,
            AssessReportDetailClientDTO::new);
        List<AssessChartDTO> chartList = getAssessChartData(task.getToolId(), rdList, dto.getScoreType());
        dto.setChartList(chartList);

        return dto;
    }

    /**
     * 设置测评工具和测评项目信息
     *
     * @param toolId 测评工具ID
     * @param proId  测评项目ID（可能是空字符串）
     * @param userId 被测评人ID
     * @param dto    待更新对象
     */
    private void setReportToolAndProjectInfo(String toolId, String proId,
        String userId, AssessReportClientDTO dto) {

        // 是否测评项目
        boolean isProject = false;
        AssessProject project = null;
        // 测评项目信息
        if (StringUtils.isNotBlank(proId)) {
            project = assessProjectService.getById(proId);
            if (project != null) {
                dto.setProId(project.getId());
                dto.setProName(project.getName());
                dto.setOrganization(project.getOrganization());
                isProject = true;
            }
        }

        // 测评工具信息
        AssessTool tool = assessToolService.getById(toolId);
        if (tool != null) {
            dto.setToolId(tool.getId());
            dto.setToolName(tool.getName());
            dto.setScoreType(tool.getScoreType());
        }

        // 前端要求返回的数据，当测评方式不存在时，对应的分值返回NULL值
        if (isProject) {

            for (AssessReportDetailClientDTO p : dto.getDetailList()) {
                // 设置测评项目 不存在的测评结果值为 NULL
                setProjectNullScore(p, project);
            }

        } else if (null != tool) {
            for (AssessReportDetailClientDTO p : dto.getDetailList()) {
                // 设置测评工具 不存在的测评结果值为 NULL   非自评的结果为 NULL
                setToolNullScore(p, tool);
            }
        }

        dto.setUserId(userId);
        UserDTO userDTO = userFeign.getUserById(userId);
        dto.setUserName(userDTO.getFullName());
    }

    /**
     * 设置测评工具和测评项目信息
     *
     * @param toolId 测评工具ID
     * @param proId  测评项目ID（可能是空字符串）
     * @param userId 被测评人ID
     * @param dto    待更新对象
     */
    private void setResultToolAndProjectInfo(String toolId, String proId,
        String userId, AssessResultClientDTO dto) {
        // 测评工具信息
        Optional.ofNullable(assessToolService.getById(toolId))
            .ifPresent(p -> {
                dto.setToolId(p.getId());
                dto.setToolName(p.getName());
                dto.setScoreType(p.getScoreType());
            });

        // 测评项目信息
        if (StringUtils.isNotBlank(proId)) {
            Optional.ofNullable(assessProjectService.getById(proId)).ifPresent(p -> {
                dto.setProId(p.getId());
                dto.setProName(p.getName());
                dto.setOrganization(p.getOrganization());
            });
        }

        dto.setUserId(userId);
        UserDTO userDTO = userFeign.getUserById(userId);
        dto.setUserName(userDTO.getFullName());
    }

    @Override
    public AssessReportClientDTO getEvalReport(String assessUserId) {

        AssessUser assessUser = assessUserService.checkAssessUser(assessUserId);

        AssessReportClientDTO dto = new AssessReportClientDTO();

        // 获取测评结果明细列表
        List<AssessUserReportSummary> summaryList = assessUserReportSummaryService.list(
            new LambdaQueryWrapper<AssessUserReportSummary>()
                .eq(AssessUserReportSummary::getAssessUserId, assessUserId)
                .orderBy(true, true, AssessUserReportSummary::getAbilityType)
                .orderBy(true, true, AssessUserReportSummary::getAbilityCategoryId)
        );

        // 程序说明：此处不验证测评报告数据是否为空，哪怕是空的，也要返回测评工具和测评项目信息

        // 数据转换
        List<AssessReportDetailClientDTO> dtoList = BeanListUtils.copyList(summaryList,
            AssessReportDetailClientDTO.class);

        // 设置能力项的标准值和平均值
        setReportDetailScoreValue(assessUser.getToolId(), dtoList);

        // 设置测评报告明细数据
        dto.setDetailList(dtoList);

        // 测评报告生成时间
        dto.setCompleteTime(assessUser.getReportTime());

        // 补充测评工具和测评项目信息
        setReportToolAndProjectInfo(assessUser.getToolId(), assessUser.getProId(), assessUser.getUserId(), dto);

        // 设置雷达图信息
        List<AssessChartDTO> chartList = getAssessChartData(assessUser.getToolId(), dtoList, dto.getScoreType());
        dto.setChartList(chartList);

        return dto;
    }

    // 设置能力项的标准值和平均值
    private void setReportDetailScoreValue(String toolId, List<AssessReportDetailClientDTO> dtoList) {

        // 查询测评工具统计信息
        List<AssessToolStatistics> statisticsList = assessToolStatisticsService.list(
            new LambdaQueryWrapper<AssessToolStatistics>()
                .eq(AssessToolStatistics::getToolId, toolId));

        for (AssessReportDetailClientDTO dto : dtoList) {
            Optional<AssessToolStatistics> statOptional = statisticsList.stream()
                .filter(p -> p.getAbilityId().equals(dto.getAbilityId())).findAny();
            statOptional.ifPresentOrElse(
                stat -> {
                    // 标准分
                    dto.setStdScore(stat.getStandardValue());

                    // 平均分
                    dto.setAvgScore(stat.getNearMonthAverage());
                },
                () -> {
                    // 标准分
                    dto.setStdScore(BigDecimal.valueOf(0));

                    // 平均分
                    dto.setAvgScore(BigDecimal.valueOf(0));
                }
            );
        }
    }

    // 设置能力项的标准值和平均值
    private void setResultDetailScoreValue(String toolId, List<AssessResultDetailClientDTO> dtoList) {

        // 查询测评工具统计信息
        List<AssessToolStatistics> statisticsList = assessToolStatisticsService.list(
            new LambdaQueryWrapper<AssessToolStatistics>()
                .eq(AssessToolStatistics::getToolId, toolId));

        for (AssessResultDetailClientDTO dto : dtoList) {
            Optional<AssessToolStatistics> statOptional = statisticsList.stream()
                .filter(p -> p.getAbilityId().equals(dto.getAbilityId())).findAny();
            statOptional.ifPresentOrElse(
                stat -> {
                    // 标准分
                    dto.setStdScore(stat.getStandardValue());

                    // 平均分
                    dto.setAvgScore(stat.getNearMonthAverage());
                },
                () -> {
                    // 标准分
                    dto.setStdScore(BigDecimal.valueOf(0));

                    // 平均分
                    dto.setAvgScore(BigDecimal.valueOf(0));
                }
            );
        }
    }

    /**
     * 获取雷达图信息
     *
     * @param toolId    测评工具ID
     * @param dtoList   测评报告数据列表
     * @param scoreType 标准分制（0-十分制，1-五分制）  当能力项无等级时，使用标准分制的分值进行雷达图数值百分比云计算
     * @return 雷达图信息列表
     */
    private List<AssessChartDTO> getAssessChartData(String toolId, List<AssessReportDetailClientDTO> dtoList,
        Integer scoreType) {

        /*
        测评报告能力雷达图显示规则：
        关键词：一级大类、二级小类、能力项

        1、如果能力没有一级大类，则统一放到名为“其它“大类
        2、如果能力没有二级分类，则统一放到名为“其它”的二级分类中。
        3、如果大类下面无二级分类，或二级分类仅有一个，则直接显示能力项。
        4、如果大类下面有二级分类，且二级分类超过一个，则把能力项的值汇总平均显示。
        */

        // 声明顶级能力类型列表对象
        List<AssessChartDTO> chartList = new ArrayList<>();

        //region 01 获取能力等级相关信息，用于获取能力等级最大值，用于雷达图数值百分比计算
        // 测评工具能力配置列表
        List<AssessToolAbilityConfig> abilityConfigList = assessToolAbilityConfigService.list(
            new LambdaQueryWrapper<AssessToolAbilityConfig>()
                .eq(AssessToolAbilityConfig::getToolId, toolId));

        // 测评工具能力等级列表
        List<String> configIdList = abilityConfigList.stream().map(AssessToolAbilityConfig::getId)
            .collect(Collectors.toList());
        List<AssessToolAbilityLevel> levelList = assessToolAbilityLevelService.list(
            new LambdaQueryWrapper<AssessToolAbilityLevel>()
                .in(AssessToolAbilityLevel::getAbilityConfigId, configIdList));
        //endregion

        // 获取顶级能力类型 type 列表
        Set<String> abilityTypeList = abilityFeign.getInitModeAbilityCategoryTypeList();

        // 数据修正 如果没有能力类型，则设置该能力为其他能力
        fixAbilityTypeData(dtoList, abilityTypeList);

        // 遍历顶级分类
        for (String type : abilityTypeList) {

            // 一级分类
            AssessChartDTO typeDto = new AssessChartDTO();

            // 一级分类下的雷达图数据列表
            List<AssessChartDetailDTO> chartDetailList = new ArrayList<>();

            // 对一级分类下的能力项根据分类进行分组（如果分类超过1个，则雷达图2级数据显示分类下的能力均值汇总；如果分类小于等于1个，则直接显示能力项）
            Map<String, List<AssessReportDetailClientDTO>> cateMap = dtoList.stream()
                .filter(p -> type.equals(p.getAbilityType()))
                .collect(Collectors.groupingBy(AssessReportDetailClientDTO::getAbilityCategoryId));

            // 如果该类型下分类超过1个，则雷达图2级数据显示分类下的能力均值汇总
            if (cateMap.size() > 1) {

                cateMap.forEach((key, valueList) -> {

                    AssessChartDetailDTO chartDto = new AssessChartDetailDTO();

                    double totalStd = 0;
                    double totalAvg = 0;
                    double totalScore = 0;
                    double totalScoreSelf = 0;
                    double totalScoreUpper = 0;
                    double totalScorePeers = 0;
                    double totalScoreLower = 0;

                    // 遍历求该分类下能力项
                    for (AssessReportDetailClientDTO reportDetailDto : valueList) {
                        // 获取对应的能力配置
                        Optional<AssessToolAbilityConfig> config = abilityConfigList.stream()
                            .filter(c -> c.getAbilityId().equals(reportDetailDto.getAbilityId())).findAny();

                        if (config.isPresent()) {

                            // 获取能力评分百分比
                            AssessChartDetailDTO subDetail = getAssessChartDetailDTO(scoreType, reportDetailDto,
                                levelList, config.get());

                            // 数据修正——防止出现超过 100% 的数据
                            fixMaxScorePercentageData(subDetail);

                            totalStd += subDetail.getStdPercentage();
                            totalAvg += subDetail.getAvgPercentage();
                            totalScore += subDetail.getScorePercentage();
                            totalScoreSelf += subDetail.getScoreSelfPercentage();
                            totalScoreUpper += subDetail.getScoreUpperPercentage();
                            totalScorePeers += subDetail.getScorePeersPercentage();
                            totalScoreLower += subDetail.getScoreLowerPercentage();
                        }
                    }

                    // 各评分的平均值
                    chartDto.setStdPercentage(
                        BigDecimal.valueOf(totalStd / valueList.size()).setScale(2, RoundingMode.HALF_UP)
                            .doubleValue());
                    chartDto.setAvgPercentage(
                        BigDecimal.valueOf(totalAvg / valueList.size()).setScale(2, RoundingMode.HALF_UP)
                            .doubleValue());
                    chartDto.setScorePercentage(
                        BigDecimal.valueOf(totalScore / valueList.size()).setScale(2, RoundingMode.HALF_UP)
                            .doubleValue());
                    chartDto.setScoreSelfPercentage(
                        BigDecimal.valueOf(totalScoreSelf / valueList.size()).setScale(2, RoundingMode.HALF_UP)
                            .doubleValue());
                    chartDto.setScoreUpperPercentage(
                        BigDecimal.valueOf(totalScoreUpper / valueList.size()).setScale(2, RoundingMode.HALF_UP)
                            .doubleValue());
                    chartDto.setScorePeersPercentage(
                        BigDecimal.valueOf(totalScorePeers / valueList.size()).setScale(2, RoundingMode.HALF_UP)
                            .doubleValue());
                    chartDto.setScoreLowerPercentage(
                        BigDecimal.valueOf(totalScoreLower / valueList.size()).setScale(2, RoundingMode.HALF_UP)
                            .doubleValue());

                    // 设置二级分类名称
                    Optional<AssessReportDetailClientDTO> anyCategory = dtoList.stream()
                        .filter(p -> p.getAbilityCategoryId().equals(key)).findAny();
                    anyCategory.ifPresent(c -> chartDto.setCategoryName(c.getAbilityCategoryName()));

                    chartDetailList.add(chartDto);
                });
            }

            // 如果该类型下分类小于等于1个，则直接显示能力项
            else {

                // 该能力类型下的所有能力项列表
                List<AssessReportDetailClientDTO> topList = dtoList.stream()
                    .filter(p -> type.equals(p.getAbilityType()))
                    .collect(Collectors.toList());

                topList.forEach(p -> {

                    // 获取对应的能力配置
                    Optional<AssessToolAbilityConfig> config = abilityConfigList.stream()
                        .filter(c -> c.getAbilityId().equals(p.getAbilityId())).findAny();

                    config.ifPresent(c -> {

                        // 获取能力评分百分比
                        AssessChartDetailDTO detail = getAssessChartDetailDTO(scoreType, p, levelList, config.get());

                        // 数据修正——防止出现超过 100% 的数据
                        fixMaxScorePercentageData(detail);

                        // 一级分类下的名称就是能力名称
                        detail.setCategoryName(c.getAbilityName());

                        chartDetailList.add(detail);
                    });
                });
            }

            // 如果雷达图明细列表为空，则跳过。继续处理其他大类
            if (chartDetailList.isEmpty()) {
                continue;
            }

            // 设置顶级分类的名称和分类编码
            Optional<AssessReportDetailClientDTO> any =
                dtoList.stream().filter(p -> p.getAbilityType().equals(type)).findAny();
            any.ifPresent(p -> {
                typeDto.setTypeName(p.getAbilityTypeName());
                typeDto.setAbilityType(p.getAbilityType());
            });

            typeDto.setList(chartDetailList);

            chartList.add(typeDto);
        }

        return chartList;
    }

    /**
     * 根据测试工具和能力列表获取推荐课程列表
     *
     * @param assessTool    测评工具信息
     * @param abilityIdList 测评工具能力Id列表
     * @return 推荐课程Id列表
     */
    private List<String> getSuggestCourseIdList(AssessTool assessTool, List<String> abilityIdList) {

        // 关联能力类型
        AssessToolRelationTypeEnum typeEnum = AssessToolRelationTypeEnum.getItem(assessTool.getRelationType());

        // 关联课程ID
        List<String> courseIdList;

        switch (Objects.requireNonNull(typeEnum)) {
            // 能力模型 【数据源：用户库 能力模型 ability_mode_relate_course 表 - 能力模型关联课程表】
            case ABILITY_MODE:
                List<AbilityRelateCourseDTO> abilityCourseById = abilityFeign.getAbilityCourseById(
                    assessTool.getRelationObjectId());
                courseIdList = abilityCourseById.stream().map(AbilityRelateCourseDTO::getCourseId)
                    .collect(Collectors.toList());
                break;

            // 胜任力地图 【数据源：用户库 ability_relate_course 表 - 能力关联课程表】
            case LEARN_MAP_ABILITY:
                Map<String, List<String>> abilityCourseListMap = abilityFeign.getAbilityCourseListByAbilityId(
                    abilityIdList);
                courseIdList = new ArrayList<>();
                abilityCourseListMap.values().forEach(courseIdList::addAll);
                break;

            // 任职资格 【数据源：认证库 job_qualification_know_resource 表 - 任职资格-知识内容-资源表】
            case QUALIFICATION:
                List<JobQualificationKnowResource> resourceList = jobQualificationKnowResourceService.list(
                    new LambdaQueryWrapper<JobQualificationKnowResource>()
                        .eq(JobQualificationKnowResource::getQualificationId, assessTool.getRelationObjectId())
                        .eq(JobQualificationKnowResource::getResourceType, ResourceTypeEnum.COURSE.getType())
                        .select(JobQualificationKnowResource::getResourceId)
                );
                courseIdList = resourceList.stream().map(JobQualificationKnowResource::getResourceId)
                    .collect(Collectors.toList());
                break;

            // 异常类型
            default:
                courseIdList = Collections.emptyList();
                break;
        }

        return courseIdList;
    }
}
