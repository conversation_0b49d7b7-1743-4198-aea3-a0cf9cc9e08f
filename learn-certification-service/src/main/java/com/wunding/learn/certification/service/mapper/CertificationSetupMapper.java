package com.wunding.learn.certification.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.certification.service.admin.dto.CertificationSetupDTO;
import com.wunding.learn.certification.service.admin.query.CerSetupQuery;
import com.wunding.learn.certification.service.model.CertificationSetup;
import com.wunding.learn.certification.service.model.CertifiedCategory;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import java.util.List;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

/**
 * <p> 认证体系表 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">chh</a>
 * @since 2022-08-18
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class,  properties = {
    @Property(name = "appName", value = "${appName}")
})
public interface CertificationSetupMapper extends BaseMapper<CertificationSetup> {

    /**
     * 分页获取认证体系
     *
     * @param cerSetupQuery 认证体系查询对象
     * @return {@link List}<{@link CertificationSetupDTO}>
     */
    List<CertificationSetupDTO> selectSetupByPage(@Param("params") CerSetupQuery cerSetupQuery);

    List<CertifiedCategory> selectCertCategoryListBySetupId(String id);
}
