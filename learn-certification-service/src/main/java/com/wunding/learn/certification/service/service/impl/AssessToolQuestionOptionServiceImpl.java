package com.wunding.learn.certification.service.service.impl;

import static com.wunding.learn.common.util.string.StringUtil.newId;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wunding.learn.certification.service.admin.dto.AssessToolQuestionOptionDTO;
import com.wunding.learn.certification.service.admin.dto.SaveAssessToolQuestionDTO;
import com.wunding.learn.certification.service.admin.dto.SaveQuestionOptionDTO;
import com.wunding.learn.certification.service.mapper.AssessToolQuestionOptionMapper;
import com.wunding.learn.certification.service.model.AssessToolQuestionOption;
import com.wunding.learn.certification.service.service.IAssessToolQuestionOptionService;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.mybatis.service.impl.BaseServiceImpl;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <p> 测评工具-题目选项表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">李恒</a>
 * @since 2024-05-29
 */
@Slf4j
@Service("assessToolQuestionOptionService")
public class AssessToolQuestionOptionServiceImpl extends
    BaseServiceImpl<AssessToolQuestionOptionMapper, AssessToolQuestionOption> implements
    IAssessToolQuestionOptionService {

    @Override
    public Map<String, List<AssessToolQuestionOption>> getQuestionOptionMap(List<String> questionIds) {
        if (CollectionUtils.isEmpty(questionIds)) {
            return Collections.emptyMap();
        }
        return list(
            new LambdaQueryWrapper<AssessToolQuestionOption>().in(AssessToolQuestionOption::getQuestionId, questionIds)
                .orderByAsc(AssessToolQuestionOption::getQuestionId)
                .orderByAsc(AssessToolQuestionOption::getOptionIndex)).stream().collect(
            Collectors.groupingBy(AssessToolQuestionOption::getQuestionId));
    }

    // 保存题目选项
    @Override
    public void saveOrUpdateQuestionOption(SaveQuestionOptionDTO question) {
        List<String> dbOptionIds = getOptionIdByQuestionId(question.getId());
        List<String> curOptionIds = question.getQuestionOptionDTOList().stream().map(AssessToolQuestionOptionDTO::getId)
            .collect(Collectors.toList());

        // 删除的题目
        List<String> delOptionIds = dbOptionIds.stream().filter(optionId -> !curOptionIds.contains(optionId))
            .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(delOptionIds)) {
            removeBatchByIds2(delOptionIds);
        }
        saveOrUpdateBatch2(buildQuestionOptions(question));
    }

    /**
     * 获取选项id集合
     *
     * @param questionId 题目id
     * @return 选项id集合
     */
    private List<String> getOptionIdByQuestionId(String questionId) {
        return list(
            new LambdaQueryWrapper<AssessToolQuestionOption>().select(AssessToolQuestionOption::getId)
                .eq(AssessToolQuestionOption::getQuestionId, questionId)).stream().map(AssessToolQuestionOption::getId)
            .collect(Collectors.toList());
    }

    @Override
    public List<AssessToolQuestionOption> buildQuestionOptions(SaveQuestionOptionDTO question) {
        List<AssessToolQuestionOption> questionOptions = new ArrayList<>();
        String userId = UserThreadContext.getUserId();
        List<AssessToolQuestionOptionDTO> optionDTOList = question.getQuestionOptionDTOList();
        AssessToolQuestionOption questionOption;
        int i = 0;
        for (AssessToolQuestionOptionDTO optionDTO : optionDTOList) {
            questionOption = new AssessToolQuestionOption();
            BeanUtils.copyProperties(optionDTO, questionOption);
            String id = newId();
            if (StringUtils.isBlank(optionDTO.getId())) {
                questionOption.setId(id);
                questionOption.setCreateBy(userId);
            }
            questionOption.setUpdateBy(userId);
            questionOption.setQuestionId(question.getId());
            // 选项索引
            questionOption.setOptionIndex(i);
            // 选项Code
            char s = (char) (i + 65);
            questionOption.setOptionCode(String.valueOf(s));
            //索引递增
            i++;
            questionOptions.add(questionOption);
        }
        return questionOptions;
    }

    @Override
    public List<AssessToolQuestionOption> getByQuestionId(String id) {
        return list(
            new LambdaQueryWrapper<AssessToolQuestionOption>().eq(AssessToolQuestionOption::getQuestionId, id)
                .orderByAsc(AssessToolQuestionOption::getOptionIndex));
    }

    @Override
    public void updateQuestionOption(SaveAssessToolQuestionDTO question) {
        // 待实现
    }
}
