<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.certification.service.mapper.JobAuthExamineFlowCriterionMapper">
    <!-- 开启二级缓存 -->
    <!--
<cache type="com.wunding.learn.common.mybatis.cache.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.certification.service.mapper.JobAuthExamineFlowCriterionMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.certification.service.model.JobAuthExamineFlowCriterion">
        <!--@Table job_auth_examine_flow_criterion-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="flow_id" jdbcType="VARCHAR"
          property="flowId"/>
        <result column="question_name" jdbcType="VARCHAR"
          property="questionName"/>
        <result column="point_desc" jdbcType="VARCHAR"
          property="pointDesc"/>
        <result column="score_institution" jdbcType="INTEGER"
          property="scoreInstitution"/>
        <result column="sort_no" jdbcType="INTEGER"
          property="sortNo"/>
        <result column="is_del" jdbcType="TINYINT"
          property="isDel"/>
        <result column="create_by" jdbcType="VARCHAR"
          property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP"
          property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR"
          property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP"
          property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        flow_id,
        question_name,
        point_desc,
        score_institution,
        sort_no,
        is_del,
        create_by,
        create_time,
        update_by,
        update_time
    </sql>
</mapper>
