<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.certification.service.mapper.TrainWithoutViewLimitMapper">
    <!-- 开启二级缓存 -->
    <!--
<cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.certification.service.mapper.TrainWithoutViewLimitMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.certification.service.model.TrainWithoutViewLimit">
        <!--@Table Train_view_limit-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="content_id" jdbcType="VARCHAR"
          property="contentId"/>
        <result column="category_id" jdbcType="VARCHAR"
          property="categoryId"/>
        <result column="category_type" jdbcType="VARCHAR"
          property="categoryType"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , content_id, category_id, category_type
    </sql>

    <select id="checkViewLimit" resultType="int" parameterType="map" useCache="false">
        select count(*)
        from w_view_limit_user u
        where u.view_limit_id = (select w_resource_view_limit.view_limit_id
                                 from w_resource_view_limit
                                 where resource_type = 'TrainWithoutViewLimit'
                                   and resource_id = #{resourceId})
          and u.user_id = #{userId}
    </select>
</mapper>
