package com.wunding.learn.certification.service.biz;

import com.github.pagehelper.PageInfo;
import com.wunding.learn.certification.service.admin.dto.CerSetupDTO;
import com.wunding.learn.certification.service.admin.dto.CertCategoryVO;
import com.wunding.learn.certification.service.client.dto.CertificationGroupByOrgStatDTO;
import com.wunding.learn.certification.service.client.dto.CertificationUserFollowQueryDTO;
import com.wunding.learn.certification.service.client.dto.CertificationUserFollowStatDTO;
import com.wunding.learn.certification.service.client.query.CertificationOrgGroupStatQuery;
import com.wunding.learn.certification.service.client.query.CertificationUserFollowQuery;
import java.util.List;

/**
 * <p> 认证用户关注业务层
 *
 * <AUTHOR> href="mailto:<EMAIL>">chenhouhua</a>
 * @since 2024-04-17
 */
public interface CertificationUserFollowBiz {

    /**
     * 按安装id列出
     *
     * @param setupId 安装id
     * @return {@link List}<{@link CertCategoryVO}>
     */
    List<CertCategoryVO> listBySetupId(String setupId);


    /**
     * 分页查找证书
     *
     * @param query 查询条件
     * @return {@link PageInfo}<{@link CertificationUserFollowQueryDTO}>
     */
    PageInfo<CertificationUserFollowQueryDTO> searchCertificationPage(CertificationUserFollowQuery query);


    /**
     * 删除关注证书
     *
     * @param cerFollowId 证书id
     */
    void delFollow(String cerFollowId);

    /**
     * 添加关注
     *
     * @param cerId 请求id
     */
    void addFollow(String cerId);

    /**
     * 用户关注证书持证统数据计查询
     *
     * @return {@link List}<{@link CertificationUserFollowStatDTO}>
     */
    List<CertificationUserFollowStatDTO> listFollowStat();

    /**
     * 关注证书按部门分组统计数据
     *
     * @param query 查询条件
     * @return {@link PageInfo}<{@link CertificationGroupByOrgStatDTO}>
     */
    PageInfo<CertificationGroupByOrgStatDTO> orgGroupStat(CertificationOrgGroupStatQuery query);

    /**
     * 证书体系id列表
     *
     * @return {@link List}<{@link CerSetupDTO}>
     */
    List<CerSetupDTO> setupIdList();

}
