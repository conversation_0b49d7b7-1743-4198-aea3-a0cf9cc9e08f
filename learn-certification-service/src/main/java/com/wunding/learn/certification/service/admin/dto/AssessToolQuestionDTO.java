package com.wunding.learn.certification.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

/**
 * <p>测评工具题目对象
 *
 * <AUTHOR> href="mailto:<EMAIL>">liangzm</a>
 * @since 2024/6/5
 */
@Data
@Schema(name = "AssessToolQuestionDTO", description = "测评工具题目对象")
public class AssessToolQuestionDTO {
    

    @Schema(description = "测评题目id")
    private String id;

    @Schema(description = "测评工具id")
    private String toolId;

    @Schema(description = "能力id")
    private String abilityId;
    
    @Schema(description = "能力名称")
    private String abilityName;
    
    @Schema(description = "题目标题")
    private String questionName;
    
    @Schema(description = "计分方式（1-选项结果对应分值， 2-选项结果总分分值， 3-选项结果平均分值， 4-选项结果中最低分值， 5-选项结果中最高分值）")
    private Integer computeMode;

    @Schema(description = "分值归属（1-个人评分， 2-方案评分）")
    private Integer scoreType;

    @Schema(description = "题目类型（1-单选题， 2-多选题， 3-判断题， 4-量表题， 5-问答题， 6-场景说明）")
    private Integer questionType;
    
    @Schema(description = "题目类型-量表类型（0-满意程度，1-符合程度）")
    private Integer questionSubType;
    
    @Schema(description = "显示顺序（程序控制：添加时值为当前测评工具中排序最大值+1）")
    private Integer sortNo;
    
    @Schema(description = "题目选项")
    private List<AssessToolQuestionOptionDTO> questionOptionDTOList;
}
