package com.wunding.learn.certification.service.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * </p> 测评工具-能力均值统计表
 *
 * <AUTHOR> href="mailto:<EMAIL>">李恒</a>
 * @since 2024-05-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("assess_tool_statistics")
@Schema(name = "AssessToolStatistics对象", description = "测评工具-能力均值统计表")
public class AssessToolStatistics implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键id
     */
    @Schema(description = "主键id")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;


    /**
     * 工具id
     */
    @Schema(description = "工具id")
    @TableField("tool_id")
    private String toolId;


    /**
     * 能力id
     */
    @Schema(description = "能力id")
    @TableField("ability_id")
    private String abilityId;


    /**
     * 能力编码
     */
    @Schema(description = "能力编码")
    @TableField("ability_code")
    private String abilityCode;


    /**
     * 能力名称
     */
    @Schema(description = "能力名称")
    @TableField("ability_name")
    private String abilityName;


    /**
     * 能力分类
     */
    @Schema(description = "能力分类")
    @TableField("ability_category")
    private String abilityCategory;


    /**
     * 标准值
     */
    @Schema(description = "标准值")
    @TableField("standard_value")
    private BigDecimal standardValue;


    /**
     * 标准等级
     */
    @Schema(description = "标准等级")
    @TableField("standard_level")
    private String standardLevel;


    /**
     * 近一年平均值（时间区间读取系统配置）
     */
    @Schema(description = "近一年平均值（时间区间读取系统配置）")
    @TableField("near_year_average")
    private BigDecimal nearYearAverage;


    /**
     * 近一年平均等级（时间区间读取系统配置）
     */
    @Schema(description = "近一年平均等级（时间区间读取系统配置")
    @TableField("near_year_level")
    private String nearYearLevel;


    /**
     * 去年平均值
     */
    @Schema(description = "去年平均值")
    @TableField("last_year_average")
    private BigDecimal lastYearAverage;


    /**
     * 去年平均等级
     */
    @Schema(description = "去年平均等级")
    @TableField("last_year_level")
    private String lastYearLevel;


    /**
     * 前年平均值
     */
    @Schema(description = "前年平均值")
    @TableField("before_last_average")
    private BigDecimal beforeLastAverage;


    /**
     * 前年平均等级
     */
    @Schema(description = "前年平均等级")
    @TableField("before_last_level")
    private String beforeLastLevel;

    /**
     * 最近N月份平均值（系统配置）
     */
    @Schema(description = "最近N月份平均值（系统配置）")
    @TableField("near_month_average")
    private BigDecimal nearMonthAverage;


    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    @TableField("is_del")
    @TableLogic(value = "0", delval = "1")
    private Integer isDel;


    /**
     * 新增人
     */
    @Schema(description = "新增人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;


    /**
     * 新增时间
     */
    @Schema(description = "新增时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 更新人
     */
    @Schema(description = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;


    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


}
