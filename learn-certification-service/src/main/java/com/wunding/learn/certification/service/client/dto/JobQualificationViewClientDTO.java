package com.wunding.learn.certification.service.client.dto;

import com.wunding.learn.certification.service.admin.dto.JobQualificationAbilityInfoDTO;
import com.wunding.learn.certification.service.admin.dto.JobQualificationConditionInfoDTO;
import com.wunding.learn.certification.service.admin.dto.JobQualificationFeedbackInfoDTO;
import com.wunding.learn.certification.service.admin.dto.JobQualificationTaskInfoDTO;
import com.wunding.learn.file.api.dto.NamePath;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 任职资格列表对象
 */
@Data
@Accessors(chain = true)
@Schema(name = "JobQualificationViewClientDTO", description = "任职资格展示对象")
public class JobQualificationViewClientDTO {

    @Schema(description = "任职资格id")
    private String id;

    @Schema(description = "任职资格-基本条件")
    private JobQualificationConditionInfoDTO conditionInfoDTO;

    @Schema(description = "任职资格-知识内容")
    private JobQuaKnowClientDTO knowInfoDTO;

    @Schema(description = "任职资格-关键能力")
    private JobQualificationAbilityInfoDTO abilityInfoDTO;

    @Schema(description = "任职资格-关键任务")
    private JobQualificationTaskInfoDTO taskInfoDTO;

    @Schema(description = "任职资格-组织回馈")
    private JobQualificationFeedbackInfoDTO feedbackInfoDTO;

    @Schema(description = "任职资格-制度文件")
    private NamePath institutionalFile;

    @Schema(description = "文件媒体类型")
    private String mime;

    @Schema(description = "文件类型  与courseware 表cwType保持一致")
    private String fileType;

    @Schema(description = "转码状态  1-转码中 2-转码成功 3-转码失败 4-排队中")
    private Integer transformStatus;

    @Schema(description = "是否公开 0否 1是")
    private Integer isPublic;

}
