package com.wunding.learn.certification.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.certification.service.model.TrainWithoutViewLimit;

/**
 * <p> 外部培训下发范围表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">余学益</a>
 * @date 2022-05-10
 */
public interface ITrainWithoutViewLimitService extends IService<TrainWithoutViewLimit> {


    /**
     * 检查下发范围
     *
     * @param userId     用户id
     * @param resourceId 资源id
     * @return boolean
     */
    boolean checkViewLimit(String userId, String resourceId);
}
