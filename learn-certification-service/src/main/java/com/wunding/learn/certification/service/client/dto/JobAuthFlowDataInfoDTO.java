package com.wunding.learn.certification.service.client.dto;

import com.wunding.learn.file.api.dto.NamePath;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p> 资格认证申请流程数据信息保存dto对象
 *
 * <AUTHOR> href="mailto:<EMAIL>">Herilo</a>
 * @since 2024-03-05
 */
@Data
@Accessors(chain = true)
@Schema(name = "JobAuthFlowDataInfoDTO", description = "资格认证申请流程数据信息保存dto对象")
public class JobAuthFlowDataInfoDTO {

    @Schema(description = "资格认证流程数据id")
    private String id;

    @Schema(description = "是否草稿 0否 1是，注：举证材料/文件提交是传1，所有环节提交时传0")
    @NotNull(message = "是否草稿不能为空")
    private Integer isDraft;

    @Schema(description = "是否通过/提交 0否 1是，注：基本条件/员工申请环节存在草稿状态时，该参数不传值否则传值")
    private Integer isPass;

    @Schema(description = "文本内容")
    private String content;

    @Schema(description = "审核评分，仅岗位序列专家组使用")
    private BigDecimal examineScore;

    @Schema(description = "举证材料，仅基本条件、员工申请使用")
    private NamePath file;

    @Schema(description = "业务类型 baseCondition基本条件 keyCapability关键能力 knowledgeContent知识内容 missionCritical关键任务 orgFeedback组织回馈，仅基本条件、员工申请使用")
    private String businessType;

    @Schema(description = "资格认证申请流程数据信息详情列表，仅岗位序列专家组使用")
    private List<JobAuthFlowDataDetailInfoDTO> list;

}
