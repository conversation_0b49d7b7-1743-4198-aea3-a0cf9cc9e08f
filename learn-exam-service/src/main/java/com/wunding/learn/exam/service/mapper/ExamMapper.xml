<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wunding.learn.exam.service.mapper.ExamMapper">
    <!-- 开启二级缓存 -->
    <!--
    <cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
    -->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.exam.service.mapper.ExamMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.exam.service.model.Exam">
        <!--@Table ex_exam-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="exam_type" jdbcType="VARCHAR" property="examType"/>
        <result column="exam_name" jdbcType="VARCHAR" property="examName"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="category_id" jdbcType="VARCHAR" property="categoryId"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="exam_time_count" jdbcType="BIGINT" property="examTimeCount"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="total_score" jdbcType="DECIMAL" property="totalScore"/>
        <result column="pass_score" jdbcType="DECIMAL" property="passScore"/>
        <result column="degree_difficult" jdbcType="DECIMAL" property="degreeDifficult"/>
        <result column="is_allow_late" jdbcType="TINYINT" property="isAllowLate"/>
        <result column="late_time" jdbcType="INTEGER" property="lateTime"/>
        <result column="check_paper_method" jdbcType="TINYINT" property="checkPaperMethod"/>
        <result column="source_type" jdbcType="VARCHAR" property="sourceType"/>
        <result column="schema_id" jdbcType="VARCHAR" property="schemaId"/>
        <result column="is_re_exam" jdbcType="TINYINT" property="isReExam"/>
        <result column="re_exam_count" jdbcType="TINYINT" property="reExamCount"/>
        <result column="is_view_answer" jdbcType="TINYINT" property="isViewAnswer"/>
        <result column="is_view_score" jdbcType="TINYINT" property="isViewScore"/>
        <result column="is_view_ranking" jdbcType="TINYINT" property="isViewRanking"/>
        <result column="is_question_seq" jdbcType="TINYINT" property="isQuestionSeq"/>
        <result column="is_option_seq" jdbcType="TINYINT" property="isOptionSeq"/>
        <result column="is_publish" jdbcType="TINYINT" property="isPublish"/>
        <result column="publish_by" jdbcType="VARCHAR" property="publishBy"/>
        <result column="publish_time" jdbcType="TIMESTAMP" property="publishTime"/>
        <result column="sort_no" jdbcType="INTEGER" property="sortNo"/>
        <result column="is_available" jdbcType="TINYINT" property="isAvailable"/>
        <result column="is_del" jdbcType="TINYINT" property="isDel"/>
        <result column="view_type" jdbcType="INTEGER" property="viewType"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="org_id" jdbcType="VARCHAR" property="orgId"/>
        <result column="customer_id" jdbcType="VARCHAR" property="customerId"/>
        <result column="is_train" jdbcType="TINYINT" property="isTrain"/>
        <result column="add_method" jdbcType="TINYINT" property="addMethod"/>
        <result column="equipment" jdbcType="VARCHAR" property="equipment"/>
        <result column="excel_flag" jdbcType="TINYINT" property="excelFlag"/>
        <result column="secret_level" jdbcType="TINYINT" property="secretLevel"/>
        <result column="anonymous" jdbcType="TINYINT" property="anonymous"/>
        <result column="exam_no" jdbcType="VARCHAR" property="examNo"/>
        <result column="quote_exam_id" jdbcType="VARCHAR" property="testPaperId"/>
        <result column="cut_screen" jdbcType="TINYINT" property="cutScreen"/>
        <result column="is_pass_no_re" jdbcType="TINYINT" property="isPassNoRe"/>
        <result column="schema_mode" jdbcType="TINYINT" property="schemaMode"/>
        <result column="is_can_view_answer" jdbcType="TINYINT" property="isCanViewAnswer"/>
        <result column="limit_screen_cuts" jdbcType="TINYINT"
          property="limitScreenCuts"/>
        <result column="limit_screen_shots" jdbcType="TINYINT"
          property="limitScreenShots"/>
        <result column="limit_screen_records" jdbcType="TINYINT"
          property="limitScreenRecords"/>
        <result column="is_face_recognition" jdbcType="TINYINT"
          property="isFaceRecognition"/>
        <result column="composition_count" jdbcType="TINYINT"
          property="compositionCount"/>
        <result column="composition_status" jdbcType="TINYINT"
          property="compositionStatus"/>
        <result column="composition_time" jdbcType="TIMESTAMP"
          property="compositionTime"/>
        <result column="is_auto_publish" jdbcType="TINYINT"
          property="isAutoPublish"/>
        <result column="pre_exam_instructions" jdbcType="VARCHAR" property="preExamInstructions"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
          ,
        exam_type,
        exam_name,
        description,
        category_id,
        start_time,
        exam_time_count,
        end_time,
        total_score,
        pass_score,
        degree_difficult,
        is_allow_late,
        late_time,
        check_paper_method,
        source_type,
        schema_id,
        is_re_exam,
        re_exam_count,
        is_view_answer,
        is_view_score,
        is_view_ranking,
        is_question_seq,
        is_option_seq,
        is_publish,
        publish_by,
        publish_time,
        sort_no,
        is_available,
        is_del,
        view_type,
        create_by,
        create_time,
        update_by,
        update_time,
        org_id,
        customer_id,
        is_train,
        add_method,
        equipment,
        excel_flag,
        secret_level,
        anonymous,
        exam_no,
        quote_exam_id,
        is_pass_no_re,
        schema_mode,
        is_can_view_answer,
        cut_screen,
        limit_screen_cuts,
        limit_screen_shots,
        limit_screen_records,
        is_face_recognition,
        composition_count,
        composition_status,
        composition_time,
        is_auto_publish,
        pre_exam_instructions
    </sql>
    <update id="changeExamCompositionStatus">
        update ex_exam
        set composition_status = #{compositionStatus},
            update_by          = #{updateBy},
            composition_time   = #{compositionTime}
        where id = #{id}
    </update>

    <!--嵌套子查询-待优化-->
    <select id="selectListByPage" parameterType="com.wunding.learn.exam.service.admin.dto.ExamQueryDTO"
      resultType="com.wunding.learn.exam.service.admin.dto.ExamListDTO" useCache="false">
        select e.id,
               e.exam_name,
               e.exam_no,
               ((select count(1)
                 from ex_exam_question eq
                 where e.id = eq.exam_id and eq.parent_id = '' and eq.is_del = 0) +
                (select ifnull(sum(sm.question_num), 0)
                 from ex_schema_detail sm
                 where e.schema_id = sm.schema_id
                   and e.schema_id != ''
                   and sm.is_del = 0))
                                                    as question_count,
               e.pass_score,
               e.total_score,
               e.exam_time_count                       examTimeCount,
               if(end_time &lt; now(), true, false) as isEnd,
               e.check_paper_method,
               e.source_type,
               e.publish_by,
               e.publish_time,
               e.start_Time,
               e.create_time                           createTime,
               e.end_Time,
               e.org_id                             as orgId,
               e.is_publish,
               e.is_can_view_answer                    isCanViewAnswer,
               e.composition_count,
               e.composition_status,
               e.composition_time,
               e.is_auto_publish,
               e.is_view_ranking                    AS isViewRanking
        from ex_exam e
                 inner join sys_org g on g.id = e.org_id
        <where>
            <if test="params.checkPaperMethod != null">
                and e.check_paper_method = #{params.checkPaperMethod}
            </if>
            <if test="params.id != null and params.id != ''">
                and e.id = #{params.id}
            </if>
            <if test="params.isTrain != null">
                and e.is_train = #{params.isTrain}
            </if>
            <if test="params.examIds != null and params.examIds.size() > 0">
                and e.id in
                <foreach collection="params.examIds" item="examId" open="(" close=")" separator=",">
                    #{examId}
                </foreach>
            </if>
            <if test="params.isAvailable != null and params.isAvailable != ''">
                and e.is_available = #{params.isAvailable}
            </if>
            <if test="params.examNo != null and params.examNo != ''">
                and instr(e.exam_no, #{params.examNo}) > 0
            </if>
            <if test="params.isPublish != null">
                and e.is_publish = #{params.isPublish}
            </if>
            <if test="params.publishBy != null and params.publishBy != ''">
                and e.publish_by in
                <foreach collection="params.publishBy.split(',')" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="params.examName != null and params.examName.trim() != ''">
                and instr(e.exam_name, #{params.examName}) > 0
            </if>
            <if test="params.startTime != null">
                and e.publish_time >= #{params.startTime}
            </if>
            <if test="params.endTime != null">
                and #{params.endTime} >= e.publish_time
            </if>
            <if test="params.examStatus != null and params.examStatus == 1">
                and e.end_time <![CDATA[>= now()
                ]]>
            </if>
            <if test="params.examStatus != null and params.examStatus == 2">
                and e.end_time <![CDATA[<= now()
                ]]>
            </if>
            <if test="params.excludeFinished != null and params.excludeFinished == 1">
                and e.end_time  <![CDATA[>= now()
                ]]>
            </if>
            <if test="params.examType != null">
                and e.exam_Type = #{params.examType}
            </if>
            <if test="params.sourceTypes != null and params.sourceTypes.size() > 0">
                and e.source_type in
                <foreach collection="params.sourceTypes" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.managerAreaOrgIds != null and params.managerAreaOrgIds.size() > 0">
                <foreach collection="params.managerAreaOrgIds" item="item" open="and (" separator="or">
                    g.level_path like concat(#{item}, '%')
                </foreach>
                or e.org_id = #{params.currentOrgId} or e.create_by = #{params.currentUserId}
                    )
            </if>
            and e.is_del = 0
        </where>
        order by e.create_time desc
    </select>

    <select id="selectListByPageBak" parameterType="com.wunding.learn.exam.service.admin.dto.ExamQueryDTO"
      resultType="com.wunding.learn.exam.service.admin.dto.ExamListDTO" useCache="false">
        select e.id,
               e.exam_name,
               e.exam_no,
               ((select count(1) from ex_exam_question eq where e.id = eq.exam_id and eq.is_del = 0) +
                (select ifnull(sum(sm.question_num), 0)
                 from ex_schema_detail sm
                 where e.schema_id = sm.schema_id
                   and e.schema_id != ''
                   and sm.is_del = 0))
                                                    as question_count,
               (select count(1) from ex_answer_record ar where ar.exam_id = e.id and is_Del = 0 and valid_status = 0)
                                                    as record_count,
               e.pass_score,
               e.total_score,
               e.exam_time_count                       examTimeCount,
               if(end_time &lt; now(), true, false) as isEnd,
               e.check_paper_method,
               e.source_type,
               e.publish_by,
               e.publish_time,
               e.start_Time,
               e.create_time                           createTime,
               e.end_Time,
               e.org_id                             as orgId,
               e.is_publish,
               e.is_can_view_answer                    isCanViewAnswer
        from ex_exam_bak e
                 inner join sys_org g on g.id = e.org_id
        <where>
            <if test="params.checkPaperMethod != null">
                and e.check_paper_method = #{params.checkPaperMethod}
            </if>
            <if test="params.id != null and params.id != ''">
                and e.id = #{params.id}
            </if>
            <if test="params.isTrain != null">
                and e.is_train = #{params.isTrain}
            </if>
            <if test="params.examIds != null and params.examIds.size() > 0">
                and e.id in
                <foreach collection="params.examIds" item="examId" open="(" close=")" separator=",">
                    #{examId}
                </foreach>
            </if>
            <if test="params.isAvailable != null and params.isAvailable != ''">
                and e.is_available = #{params.isAvailable}
            </if>
            <if test="params.examNo != null and params.examNo != ''">
                and e.exam_no like concat('%', #{params.examNo}, '%')
            </if>
            <if test="params.isPublish != null">
                and e.is_publish = #{params.isPublish}
            </if>
            <if test="params.examName != null and params.examName.trim() != ''">
                and e.exam_name like concat('%', #{params.examName}, '%')
            </if>
            <if test="params.startTime != null">
                and e.publish_time >= #{params.startTime}
            </if>
            <if test="params.endTime != null">
                and #{params.endTime} >= e.publish_time
            </if>
            <if test="params.excludeFinished != null and params.excludeFinished == 1">
                and e.end_time  <![CDATA[>= now()
                ]]>
            </if>
            <if test="params.examType != null">
                and e.exam_Type = #{params.examType}
            </if>
            <if test="params.sourceTypes != null and params.sourceTypes.size() > 0">
                and e.source_type in
                <foreach collection="params.sourceTypes" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.managerAreaOrgIds != null and params.managerAreaOrgIds.size() > 0">
                <foreach collection="params.managerAreaOrgIds" item="item" open="and (" separator="or">
                    g.level_path like concat(#{item}, '%')
                </foreach>
                or e.create_by = #{params.currentUserId}
                    )
            </if>
            and e.is_del = 0
        </where>
        order by e.create_time desc
    </select>

    <select id="selectCountByPage" parameterType="com.wunding.learn.exam.service.admin.dto.ExamQueryDTO"
      resultType="int" useCache="false">
        select count(1)
        from ex_exam e
                 inner join sys_org g on g.id = e.org_id
        <where>
            <if test="params.checkPaperMethod != null">
                and e.check_paper_method = #{params.checkPaperMethod}
            </if>
            <if test="params.id != null and params.id != ''">
                and e.id = #{params.id}
            </if>
            <if test="params.isTrain != null">
                and e.is_train = #{params.isTrain}
            </if>
            <if test="null != params.examIds and params.examIds.size() > 0">
                <foreach collection="params.examIds" open="and e.id in(" close=")" item="item"
                  separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="params.isAvailable != null and params.isAvailable != ''">
                and e.is_available = #{params.isAvailable}
            </if>
            <if test="params.examNo != null and params.examNo != ''">
                and instr(e.exam_no, #{params.examNo}) > 0
            </if>
            <if test="params.sourceTypes != null and params.sourceTypes.size() > 0">
                and e.source_type in
                <foreach collection="params.sourceTypes" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.isPublish != null">
                and e.is_publish = #{params.isPublish}
            </if>
            <if test="params.publishBy != null and params.publishBy != ''">
                and e.publish_by in
                <foreach collection="params.publishBy.split(',')" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="params.examName != null and params.examName.trim() != ''">
                and instr(e.exam_name, #{params.examName}) > 0
            </if>
            <if test="params.startTime != null">
                and e.publish_time >= #{params.startTime}
            </if>
            <if test="params.endTime != null">
                and #{params.endTime} >= e.publish_time
            </if>
            <if test="params.excludeFinished != null and params.excludeFinished == 1">
                and e.end_time  <![CDATA[>= now()
                ]]>
            </if>
            <if test="params.examType != null">
                and e.exam_Type = #{params.examType}
            </if>
            <if test="params.managerAreaOrgIds != null and params.managerAreaOrgIds.size() > 0">
                <foreach collection="params.managerAreaOrgIds" item="item" open="and (" separator="or">
                    g.level_path like concat(#{item}, '%')
                </foreach>
                or e.create_by = #{params.currentUserId})
            </if>
            and e.is_del = 0
        </where>
        order by e.is_publish, e.create_time desc
    </select>

    <select id="selectCountByPageBak" parameterType="com.wunding.learn.exam.service.admin.dto.ExamQueryDTO"
      resultType="int" useCache="false">
        SELECT count(1)
        from ex_exam_bak e
                 inner join sys_org g on g.id = e.org_id
        <where>
            <if test="params.checkPaperMethod != null">
                and e.check_paper_method = #{params.checkPaperMethod}
            </if>
            <if test="params.id != null and params.id != ''">
                and e.id = #{params.id}
            </if>
            <if test="params.isTrain != null">
                and e.is_train = #{params.isTrain}
            </if>
            <if test="null != params.examIds and params.examIds.size() > 0">
                <foreach collection="params.examIds" open="and e.id in(" close=")" item="item"
                  separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="params.isAvailable != null and params.isAvailable != ''">
                and e.is_available = #{params.isAvailable}
            </if>
            <if test="params.examNo != null and params.examNo != ''">
                and e.exam_no like concat('%', #{params.examNo}, '%')
            </if>
            <if test="params.sourceTypes != null and params.sourceTypes.size() > 0">
                and e.source_type in
                <foreach collection="params.sourceTypes" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.isPublish != null">
                and e.is_publish = #{params.isPublish}
            </if>
            <if test="params.examName != null and params.examName.trim() != ''">
                and instr(e.exam_name, #{params.examName}) > 0
            </if>
            <if test="params.startTime != null">
                and e.publish_time >= #{params.startTime}
            </if>
            <if test="params.endTime != null">
                and #{params.endTime} >= e.publish_time
            </if>
            <if test="params.excludeFinished != null and params.excludeFinished == 1">
                and e.end_time  <![CDATA[>= now()
                ]]>
            </if>
            <if test="params.examType != null">
                and e.exam_Type = #{params.examType}
            </if>
            <if test="params.managerAreaOrgIds != null and params.managerAreaOrgIds.size() > 0">
                <foreach collection="params.managerAreaOrgIds" item="item" open="and (" separator="or">
                    g.level_path like concat(#{item}, '%')
                </foreach>
                or e.org_id = #{params.currentOrgId} or e.create_by = #{params.currentUserId})
            </if>
            and e.is_del = 0
        </where>
        order by e.is_publish, e.create_time desc
    </select>

    <select id="getRank" resultType="com.wunding.learn.exam.service.client.dto.ExamRankUserDTO" useCache="false">
        <include refid="exam_rank_sql"/>
    </select>

    <!-- 查询用户在考试中的排行-->
    <select id="getMyRank" resultType="java.lang.Integer" useCache="false">
        select r.userRank from
        (<include refid="exam_rank_sql"/>) as r
    where r.userId = #{userId}
    </select>

    <select id="getExamFinishStatus" resultType="java.lang.Integer" useCache="false">
        select count(1)
        from ex_answer_record a
        where a.is_del = 0
          and a.valid_status = 0
          and a.exam_id = #{examId}
          and a.user_id = #{userId}
          and a.is_post = 1
    </select>

    <select id="getUserScoreInfo" resultType="com.wunding.learn.exam.api.dto.ExamUserScoreDTO" useCache="false">
        select
            a.exam_id,
            a.pass_score,
            a.user_score
        from ex_answer_record a
        where a.is_del = 0
          and a.valid_status = 0
          and a.exam_id in <foreach collection="examIds" open="(" item="id" separator="," close=")">#{id} </foreach>
          and a.user_id = #{userId}
          and a.is_post = 1
          and a.is_check_finish = 1
        order by a.answer_time desc
    </select>

    <!--考试排行榜查询-->
    <sql id="exam_rank_sql">
        select a.*,
               row_number() over ( order by a.user_score desc,a.valid_status, a.answer_time - a.start_time) userRank
        from (select ear.id,
                     ear.user_id         as                                                                      userId,
                     ear.answer_time,
                     ear.user_score,
                     ear.pass_score,
                     ear.valid_status,
                     ear.start_time,
                     ear.is_post         as                                                                      completed,
                     ear.is_check_finish as                                                                      checkFinish,
                     rank() over ( partition by ear.user_id order by ear.user_score desc,ear.answer_time,ear.id) maxScoreRank
              from ex_answer_record ear
              where ear.exam_id = #{examId}
                and ear.is_del = 0
                and ear.is_check_finish = 1) a
        where a.maxScoreRank = 1
    </sql>

    <select id="getCorrectExamList" useCache="false"
      resultType="com.wunding.learn.exam.service.admin.dto.ExamCorrectExamListDTO">
        select ee.id         as examId,
               ee.schema_id  as schemaId,
               ee.start_time as examStartTime,
               ee.create_by  as create_by
        from (ex_exam ee ,ex_exam_reviewer er)
                 inner join sys_org g on g.id = ee.org_id
        where ee.id = er.exam_id
          and er.is_del = 0
          and (er.reviewer_id = 'ALL' or er.reviewer_id = #{userId})
          and exists (
            (select 1 a
             from ex_answer_record ear
             where ear.exam_id = ee.id
               and ear.check_paper_method = 1
               and ear.is_del = 0
               and ear.valid_status = 0
               and (ear.check_status != -1 and ear.check_status != 0)
             limit 1))
          and ee.exam_type = 1
          and ee.is_del = 0
        <if test="startTime != null">
            and date_format(ee.publish_time, '%Y-%m-%d') >= date_format(#{startTime}, '%Y-%m-%d')
        </if>
        <if test="endTime != null">
            and date_format(#{endTime}, '%Y-%m-%d') >= date_format(ee.publish_time, '%Y-%m-%d')
        </if>
        <if test="examName != null and examName != ''">
            and instr(ee.exam_name, #{examName}) > 0
        </if>
        <if test="params.managerAreaOrgIds != null and params.managerAreaOrgIds.size() > 0">
            <foreach collection="params.managerAreaOrgIds" item="item" open="and (" separator="or">
                g.level_path like concat(#{item}, '%')
            </foreach>
            or (ee.org_id = #{params.currentOrgId} and ee.create_by = #{params.currentUserId})
                or (er.reviewer_id = #{userId})
                )
        </if>
        order by ee.id desc, ee.start_time desc
    </select>

    <select id="myExamList" parameterType="com.wunding.learn.exam.service.client.dto.MyExamListQueryDTO"
      resultMap="BaseResultMap" useCache="false">
        select id,
               exam_type,
               exam_name,
               description,
               start_time,
               exam_time_count,
               end_time,
               total_score,
               pass_score,
               is_allow_late,
               late_time,
               check_paper_method,
               source_type,
               schema_id,
               is_re_exam,
               re_exam_count,
               is_view_answer,
               is_view_score,
               is_view_ranking,
               is_question_seq,
               is_option_seq,
               is_publish,
               publish_by,
               publish_time,
               is_available,
               is_del,
               is_train,
               equipment,
               secret_level,
               anonymous,
               quote_exam_id,
               cut_screen,
               is_pass_no_re,
               is_can_view_answer,
               is_face_recognition
        <if test="completed != null and completed == 1">
            ,
                ear.answer_time
        </if>
        from ex_exam e
        <if test="completed != null and completed == 1">
            inner join (select ar.answer_time,
                               ar.exam_id,
                               row_number() over ( partition by ar.exam_id order by ar.answer_time desc ) as rowNumber
                        from ex_answer_record ar
                        where ar.`user_id` = #{userId}
                          and ar.`is_post` = 1
                          and ar.valid_status = 0
                          and ar.is_del = 0) as ear on ear.exam_id = e.id
        </if>
        where exists(select 1
                     from w_resource_view_limit r
                     where e.id = r.resource_id
                       and r.resource_type = 'ExamViewLimit'
                       and r.view_limit_id in (select u.view_limit_id
                                               from w_view_limit_user u
                                               where u.user_id = #{userId}))
        <if test="searchKey != null and searchKey != ''">
            and instr(e.`exam_name`, #{searchKey}) > 0
        </if>
        <if test="query != null and query != ''">
            and instr(e.`exam_name`, #{query}) > 0
        </if>
        and e.`exam_type` = '1'
        and e.`is_publish` = 1
        and e.`is_train` = 0
        and e.`start_time` <![CDATA[ <= ]]>  now()
        and e.is_del = 0
        <if test="completed != null and completed == 1">
            and ear.rowNumber = 1
            and exists (select 1
                        from ex_answer_record ar
                        where ar.`user_id` = #{userId}
                          and ar.`is_post` = 1
                          and ar.valid_status = 0
                          and ar.is_del = 0
                          and ar.exam_id = e.id)
        </if>
        <if test="completed != null and completed != 1">
            and not exists (select 1
                            from ex_answer_record ar
                            where ar.`user_id` = #{userId}
                              and ar.`is_post` = 1
                              and ar.exam_id = e.id
                              and ar.is_del = 0
                              and ar.valid_status = 0)
        </if>
        <if test="end == 1">
            and e.end_time <![CDATA[ < ]]> now() <!-- order by e.end_time desc -->
        </if>
        <if test="end == 0">
            and e.end_time <![CDATA[ >= ]]> now() <!-- order by e.end_time asc -->
        </if>

        <if test="completed != null and completed == 1">
            order by ear.answer_time desc
        </if>
        <if test="completed != null and completed != 1">
            order by e.end_time asc
        </if>
        <if test="completed == null">
            order by e.end_time asc
        </if>
    </select>

    <select id="getExamInfoMapByExamIds" resultType="com.wunding.learn.exam.api.dto.ViewExamFeignDTO" useCache="false">
        select e.id,
               e.exam_name,
               e.description,
               e.exam_no,
               e.total_score,
               e.pass_score,
               e.exam_time_count,
               e.source_type,
               e.check_paper_method,
               ((select count(1) from ex_exam_question eq where e.id = eq.exam_id and eq.is_del = 0) +
                (select ifnull(sum(sm.question_num), 0)
                 from ex_schema_detail sm
                 where e.schema_id = sm.schema_id
                   and e.schema_id != ''
                   and sm.is_del = 0))
                                                        as question_count,
               (select count(1)
                from ex_answer_record er
                where er.exam_id = e.id
                  and er.is_post = 1
                  and er.valid_status =
                      0)                                as postExamCount,
               (select count(distinct er.user_id)
                from ex_answer_record er
                where er.exam_id = e.id
                  and er.is_post = 1
                  and er.valid_status =
                      0)                                as memberNum,
               (select count(1)
                from (select 1
                      from ex_answer_record a
                      where a.user_score <![CDATA[>=]]> a.pass_score
                        and a.exam_id = e.id
                      group by a.exam_id, a.user_id) b) as passNum,
               e.is_publish,
               e.composition_status,
               e.is_auto_publish,
               e.is_del
        from ex_exam e
        <foreach collection="examIds" item="examId" open="where e.id in (" close=")"
          separator=",">
            #{examId}
        </foreach>
    </select>

    <select id="getValidExamInfoMapByExamIds" resultType="com.wunding.learn.exam.api.dto.ViewExamFeignDTO"
      useCache="false">
        select e.id,
               e.exam_name,
               e.description,
               e.exam_no,
               e.total_score,
               e.pass_score,
               e.exam_time_count,
               e.source_type,
               e.start_time,
               e.end_time,
               ((select count(1) from ex_exam_question eq where e.id = eq.exam_id and eq.is_del = 0) +
                (select ifnull(sum(sm.question_num), 0)
                 from ex_schema_detail sm
                 where e.schema_id = sm.schema_id
                   and e.schema_id != ''
                   and sm.is_del = 0))
                                                                                                                      as question_count,
               (select count(1)
                from ex_answer_record er
                where er.exam_id = e.id
                  and er.is_post = 1
                  and er.valid_status =
                      0
                  and er.is_del = 0)                                                                                  as postExamCount,
               (select count(1) from ex_answer_record er where er.exam_id = e.id and er.is_max = 1 and er.is_del = 0) as
                                                                                                                         checkExamCount
        from ex_exam e
        <foreach collection="examIds" item="examId" open="where e.id in (" close=")"
          separator=",">
            #{examId}
        </foreach>
        and e.is_del = 0
    </select>

    <select id="getExamQuestionCountMap" resultType="java.util.Map" useCache="false">
        select id,
               (select count(1)
                from ex_exam_question
                where exam_id = e.id and parent_id = '' and e.is_del = 0 and is_del = 0) +
               (select ifnull(sum(question_num), 0)
                from ex_schema_detail
                where schema_id = e.schema_id
                  and e.is_del = 0
                  and is_del = 0
                  and e.schema_id != '') cn
        from ex_exam e where id in
        <foreach collection="examIds" item="examId" open="(" close=")" separator=",">
            #{examId}
        </foreach>
    </select>

    <select id="getExamPassNum" resultType="java.lang.Integer" useCache="false">
        select count(1) from (
        select exam_id
        from ex_answer_record ar,
             ex_exam e
        where ar.exam_id = e.id
          and e.id in
        <foreach collection="examIds" item="examId" open="(" close=")" separator=",">
            #{examId}
        </foreach>
        and ar.user_id = #{userId}
        and e.is_publish = 1
        and ar.user_score >= ar.pass_score
        and ar.is_del = 0
        group by exam_id  )a
    </select>

    <select id="getDitch" resultType="com.wunding.learn.common.dto.CerDitchDTO">
        select id,
               exam_name name,
        <!--考试状态，-1已结束 0 未开始 1 进行中-->
        (case
             when now() > end_time then -1
             when now()
                 &lt; start_time then
                 0
             else 1 end) state
        from ex_exam
        where is_del = 0
          and is_publish = 1
          and is_available = 1
          and id = #{contentId}
    </select>


    <!-- 查询一段时间内给用户下发的考试列表 -->
    <select id="selectProTaskExamListByTime"
      resultType="com.wunding.learn.common.dto.LearningCalendarTaskDTO" useCache="false">
        select e.id                                                                               id,
               e.id                                                                               activityId,
               e.exam_name                                                                        title,
               'exam'                                                                          as flag,
               e.start_time                                                                       startTime,
               e.end_time                                                                         endTime,
               case when e.start_time > now() then 0 when now() > e.end_time then 2 else 1 end as status,
               case
                   when (select ec.id
                         from ex_answer_record ec
                         where ec.exam_id = e.id
                           and ec.user_id = #{userId}
                           and ec.user_score > ec.pass_score
                           and ec.is_del = 0
                           and ec.valid_status = 0) is null then 0
                   else 2 end                                                                  as userStatus,
               0                                                                                  isOperation
        from ex_exam e
                 inner join w_resource_view_limit rv on e.id = rv.resource_id and rv.resource_type = 'ExamViewLimit'
        where e.is_del = 0
          and e.exam_type = 1
          and e.is_publish = 1
          and e.is_train = 0
          and (exists(select 1
                      from w_view_limit_user v
                      where rv.view_limit_id = v.view_limit_id
                        and v.user_id
                          = #{userId,jdbcType=VARCHAR}))
        <if test="null == whichDay">
            and ((date_format(e.start_time, '%Y-%m-%d') >= date_format(#{startTime}, '%Y-%m-%d') and
                  date_format(e.start_time, '%Y-%m-%d') &lt;=
                  date_format(#{endTime}, '%Y-%m-%d')) or
                 (date_format(e.end_time, '%Y-%m-%d') >= date_format(#{startTime}, '%Y-%m-%d') and
                  date_format(e.end_time,
                              '%Y-%m-%d') &lt;= date_format(#{endTime}, '%Y-%m-%d')) or
                 (date_format(e.start_time, '%Y-%m-%d') &lt;= date_format(#{startTime}, '%Y-%m-%d') and
                  date_format(e.end_time, '%Y-%m-%d') >= date_format(#{endTime}, '%Y-%m-%d')))
        </if>
        <if test="null != whichDay and null == today">
            and #{whichDay} = date_format(e.start_time, '%Y-%m-%d')
        </if>
        <if test="null != whichDay and null != today">
            and #{whichDay} >= date_format(e.start_time, '%Y-%m-%d')
            and #{whichDay} &lt;= date_format(e.end_time,
                                              '%Y-%m-%d')
        </if>
        order by e.start_time
    </select>

    <select id="selectOneExamIncludeDel" resultType="com.wunding.learn.exam.service.model.Exam">
        select
        <include refid="Base_Column_List"/>
        from ex_exam
        where id = #{id,jdbcType=VARCHAR}
    </select>

    <select id="getSubordinateExamList" parameterType="com.wunding.learn.exam.service.client.query.SubordinateExamQuery"
      resultType="com.wunding.learn.exam.service.client.dto.SubordinateExamDTO" useCache="false">
        select b.id,
               b.exam_name,
               a.answer_time,
               a.is_check_finish,
               a.user_score,
               a.pass_score
        from ex_answer_record a
                 inner join ex_exam b on a.exam_id = b.id
        where a.is_post = 1
          and a.is_del = 0
          and a.valid_status = 0
          and a.answer_time >= #{params.startTime}
          and #{params.endTime} >= a.answer_time
          and a.user_id = #{params.userId}
        order by a.answer_time desc
    </select>

    <select id="getExerciseTime" resultType="com.wunding.learn.exam.api.dto.ExerciseTimeDTO">
        select *
        from ex_exam
        where is_publish = 1
          and is_del = 0
          and id = #{id};
    </select>

    <select id="getInvalidExamId" resultType="java.lang.String">
        select id
        from ex_exam
        where id in (
        <foreach collection="examIdList" item="item" separator=",">
            #{item}
        </foreach>
        )
          and (is_del = 1 or is_publish = 0)
    </select>

    <select id="getMenuTaskCount" resultType="java.lang.Integer" useCache="false">
        select count(1)
        from ex_exam e
                 inner join w_resource_view_limit rv on e.id = rv.resource_id and rv.resource_type = 'ExamViewLimit'
                 left join (select ac.exam_id, 1 isFinish
                            from ex_answer_record ac
                            where ac.user_id = #{userId}
                              and ac.is_del = 0
                              and ac.valid_status = 0
                              and is_post = 1) t on t.exam_id = e.id
        where e.is_publish = 1
          and exists(select 1
                     from w_view_limit_user v
                     where rv.view_limit_id = v.view_limit_id
                       and v.user_id = #{userId})
          and e.exam_type = 1
          and e.is_train = 0
          and e.is_del = 0
        <if test="status != null">
            <if test="status == 0">
                and t.isFinish is null
                <![CDATA[ and e.end_time > now()]]>
                <![CDATA[and now() > e.start_time
                ]]>
            </if>
            <if test="status == 1">
                and t.isFinish = 1
            </if>
            <if test="status == 2">
                and t.isFinish is null
                <![CDATA[ and e.end_time < now()
                ]]>
            </if>
        </if>
    </select>

    <select id="getMenuTask" resultType="com.wunding.learn.exam.api.dto.ExamInfoDTO" useCache="false">
        select e.id,
               exam_name,
               ((select count(id) from ex_exam_question eeq where eeq.exam_id = e.id)
                   +
                (select ifnull(sum(sd.question_num), 0) from ex_schema_detail sd where sd.schema_id = e.schema_id)
                   ) questionCount,
               e.exam_time_count,
               e.end_time
        from ex_exam e
                 inner join w_resource_view_limit rv on e.id = rv.resource_id and rv.resource_type = 'ExamViewLimit'
                 left join (select ac.exam_id, 1 isFinish
                            from ex_answer_record ac
                            where ac.user_id = #{userId}
                              and ac.is_del = 0
                              and ac.valid_status = 0
                              and is_post = 1) t on t.exam_id = e.id
        where e.is_publish = 1
          and exists(select 1
                     from w_view_limit_user v
                     where rv.view_limit_id = v.view_limit_id
                       and v.user_id = #{userId})
          and e.exam_type = 1
          and e.is_train = 0
          and e.is_del = 0
        <if test="status != null">
            <if test="status == 0">
                and t.isFinish is null
                <![CDATA[ and e.end_time > now()]]>
                <![CDATA[and now() > e.start_time
                ]]>
            </if>
            <if test="status == 1">
                and t.isFinish = 1
            </if>
            <if test="status == 2">
                and t.isFinish is null
                <![CDATA[ and e.end_time < now()
                ]]>
            </if>
        </if>
    </select>

    <select id="getRealityById" resultType="com.wunding.learn.exam.api.dto.ExamInfoDTO">
        select
        <include refid="Base_Column_List">
        </include>
        from ex_exam
        where id = #{examId}
    </select>

    <select id="list" resultMap="BaseResultMap" useCache="false">
        select e.id                 as id,
               e.exam_type          as exam_type,
               e.exam_name          as exam_name,
               e.description        as description,
               e.category_id        as category_id,
               e.start_time         as start_time,
               e.exam_time_count    as exam_time_count,
               e.end_time           as end_time,
               e.total_score        as total_score,
               e.pass_score         as pass_score,
               e.degree_difficult   as degree_difficult,
               e.is_allow_late      as is_allow_late,
               e.check_paper_method as check_paper_method,
               e.source_type        as source_type,
               e.schema_id          as schema_id,
               e.is_re_exam         as is_re_exam,
               e.re_exam_count      as re_exam_count,
               e.is_view_answer     as is_view_answer,
               e.is_view_score      as is_view_score,
               e.is_view_ranking    as is_view_ranking,
               e.is_question_seq    as is_question_seq,
               e.is_option_seq      as is_option_seq,
               e.publish_by         as publish_by,
               e.is_publish         as is_publish,
               e.publish_time       as publish_time,
               e.sort_no            as sort_no,
               e.is_available       as is_available,
               e.is_del             as is_del,
               e.create_by          as create_by,
               e.create_time        as create_time,
               e.update_by          as update_by,
               e.update_time        as update_time,
               e.customer_id        as customer_id,
               e.is_train           as is_train,
               e.add_method         as add_method,
               e.equipment          as equipment,
               e.excel_flag         as excel_flag,
               e.org_id,
               g.org_name
        from ex_exam e
                 inner join sys_org g on g.id = e.org_id
        <where>
            <if test="params.isTrain != null">
                and e.is_train = #{params.isTrain}
            </if>
            <if test="params.startTime != null">
                and e.publish_time >= #{params.startTime}
            </if>
            <if test="params.endTime != null">
                and #{params.endTime} >= e.publish_time
            </if>
            <if test="params.isPublish != null and params.isPublish != ''">
                and e.is_publish = #{params.isPublish}
            </if>
            <if test="params.examName != null and params.examName.trim() != ''">
                and instr(e.exam_name, #{params.examName}) > 0
            </if>
            <if test="params.examType != null">
                and e.exam_Type = #{params.examType}
            </if>
            <if test="params.managerAreaOrgIds != null and params.managerAreaOrgIds.size() > 0">
                <foreach collection="params.managerAreaOrgIds" item="item" open="and (" separator="or">
                    g.level_path like concat(#{item}, '%')
                </foreach>
                or e.create_by = #{params.currentUserId}
                    )
            </if>
            and e.is_del = 0
        </where>
        order by e.create_time desc
    </select>

    <select id="getTaskAppResourceByIds" parameterType="com.wunding.learn.exam.api.query.TaskExamAppResourceQuery"
      resultType="com.wunding.learn.common.dto.TaskAppResourceDTO" useCache="false">
        select resourceId,
               resourceName,
               status,
               case
                   when records > 0
                       then 1
                   else 0 end isFinish
        from (select a.id        as        resourceId,
                     a.exam_name as        resourceName,
                     case
                         when a.end_time <![CDATA[ < ]]> current_timestamp then 2
                         when a.start_time <![CDATA[ > ]]> current_timestamp then 0
                         else 1 end        status,
                     (select count(1)
                      from ex_answer_record b
                      where b.user_id = #{currentUserId}
                        and b.exam_id = a.id
                        and b.is_post = 1) records
              from ex_exam a
        where
        a.id in
        <foreach collection="ids" open="(" item="id" separator="," close=")">
            #{id}
        </foreach>
        and a.is_publish = 1) c
    </select>

    <select id="getCorrectExamCashList" useCache="true"
      resultType="com.wunding.learn.exam.service.admin.dto.ExamCorrectExamListDTO">
        select ee.id          as examId,
               ee.schema_id   as schemaId,
               ee.exam_type   as exam_type,
               ee.exam_name   as exam_name,
               ee.description as description,
               ee.category_id as category_id,
               ee.start_time  as examStartTime,
               ee.end_time    as examEndTime,
               ee.total_score as totalScore,
               ee.pass_score  as passScore,
               ee.source_type as source_type,
               ee.sort_no     as sort_no,
               ee.is_del      as is_del,
               ee.create_by   as create_by
        from ex_exam ee
        where ee.id in
        <foreach collection="examIds" open="(" item="item" separator="," close=")">
            #{item}
        </foreach>
        order by ee.id desc,ee.start_time desc
    </select>

    <select id="getExamBaseList" resultType="com.wunding.learn.common.dto.ResourceBaseDTO" useCache="false">
        select e.id,
               e.exam_name as title,
               e.is_publish,
               e.is_del,
               e.start_time,
               e.end_time,
               o.level_path
        from ex_exam e
                 inner join sys_org o on e.org_id = o.id
        where e.id in
        <foreach collection="params.resourceIdList" open="(" close=")" separator="," item="resourceId">
            #{resourceId}
        </foreach>
    </select>

    <select id="getCertificationContentList" resultType="com.wunding.learn.common.dto.CertificationContentDTO">
        select e.id,
               e.exam_name as name,
               e.start_time,
               e.is_del
        from ex_exam e
        <where>
            e.id in
            <foreach item="id" collection="batchIds" separator="," open="(" close=")" index="">
                #{id}
            </foreach>
        </where>
    </select>

    <select id="checkExamManagePermissions" resultType="java.lang.Integer" useCache="false">
        select count(1)
        from ex_exam tp
                 inner join sys_org g on g.id = tp.org_id
        where tp.id = #{id,jdbcType=VARCHAR}
        <if test="userManageAreaOrgId != null and userManageAreaOrgId.size > 0">
            and
            <foreach collection="userManageAreaOrgId" open="(" close=")" item="levelPath" separator="or">
                g.level_path like concat(#{levelPath}, '%')
            </foreach>
        </if>
    </select>

    <select id="getExamIsDelById" parameterType="java.lang.String"
      resultType="com.wunding.learn.common.dto.ResourceDeleteInfoDTO">
        select id,
               is_del      isDel,
               update_time updateTime,
               update_by   updateBy
        from ex_exam
        where id = #{id}
    </select>

    <select id="getResourceIsDel" resultType="java.lang.Integer" parameterType="java.lang.String">
        select is_del
        from ex_exam
        where id = #{resourceId}
    </select>
</mapper>
