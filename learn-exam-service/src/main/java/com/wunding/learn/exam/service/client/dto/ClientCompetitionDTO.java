package com.wunding.learn.exam.service.client.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 竞赛列表接口
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">wgr</a>
 * @date 2023/9/25 14:47
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(name = "ClientCompetitionDTO", description = "考试竞赛详情接口")
public class ClientCompetitionDTO implements Serializable {

    @Schema(description = "竞赛ID")
    private String id;

    @Schema(description = "竞赛名称")
    private String name;


    @Schema(description = "开始时间")
    private Date startTime;

    @Schema(description = "结束时间")
    private Date endTime;

    @Schema(description = "是否已经结束 0=未结束 1=结束")
    private Integer isEnd;


    @Schema(description = "是否已经开始 0=未开始 1=开始")
    private Integer isStart;

    @Schema(description = "封面图片")
    private String coverImageUrl;


    /**
     * 人员模式 single=单人 double=双人  multi=多人 group=分组
     */
    @Schema(description = "人员模式 single=单人 double=双人  multi=多人 group=分组")
    private String peopleModel;


    /**
     * 答题模式 pk=PK答题模式  keep=一站到底 time=计时赛
     */
    @Schema(description = "答题模式 pk=PK答题模式  keep=一站到底 time=计时赛")
    private String answerModel;

    @Schema(description = "使用机器人 0=不使用 1=使用")
    private Integer useRobot;

    /**
     * 显示在线参与人数 0=不显示 1=显示
     */
    @Schema(description = "显示在线参与人数 0=不显示 1=显示")
    private Integer showOnlineNum;

    /**
     * 允许随机人员进入
     */
    @Schema(description = "允许随机人员进入 0=不允许 1=允许")
    private Integer randomJoin;


    /**
     * 允许随机人员被踢出 0=不允许 1=允许
     */
    @Schema(description = "允许随机人员被踢出 0=不允许 1=允许")
    private Integer kickOutRandom;


    /**
     * 匹配时间 单位秒
     */
    @Schema(description = "匹配时间 单位秒")
    private Integer matchTime;


    @Schema(description = "是否还能够参与 0=次数已经超过限制数不能参与 1=能够参与")
    private Integer canJoin;

    /**
     * 简介和规则
     */
    @Schema(description = "简介和规则")
    private String intro;

    @Schema(description = "排名规则 0=默认值 1=按积分 2=按答题正确数")
    private Integer rankRule;

}
