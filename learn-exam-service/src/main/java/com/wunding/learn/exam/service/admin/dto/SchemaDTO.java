package com.wunding.learn.exam.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @create 2022/4/14 15:54
 * @description
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(name = "SchemaDTO", description = "组卷管理-列表对象")
public class SchemaDTO implements Serializable {

    @Schema(description = "主键id")
    private String id;

    @Schema(description = "名称")
    private String schemaName;

    @Schema(description = "总分")
    private BigDecimal schemaTotalScore;

    @Schema(description = "备注")
    private String schemaDescription;

    @Schema(description = "添加时间")
    private Date createTime;

    @Schema(description = "是否启用 0否1是")
    private Integer available;

    @Schema(description = "是否难易度抽提配置 0否 1是")
    private Integer isDifficulty;
}
