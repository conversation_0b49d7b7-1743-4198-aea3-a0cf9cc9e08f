package com.wunding.learn.exam.service.consumer;

import com.rabbitmq.client.Channel;
import com.wunding.learn.common.constant.mq.MqConst;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.mq.event.ExamStatisticsInitFinishEvent;
import com.wunding.learn.common.mq.util.ConsumerAckUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.exam.service.constant.ExamConstant;
import com.wunding.learn.exam.service.service.IExamAnswerStatisticsService;
import java.io.IOException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

/**
 * <p>
 * 考试统计消费者
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">zhongchaoheng</a>
 * @date 2023/9/22 14:53
 */
@Component
@Slf4j
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class ExamStatisticsConsumer {


    private final IExamAnswerStatisticsService examAnswerStatisticsService;

    //此处不指定队列名称,由系统自动生成随机队列名称，以达到多个节点产生多条不一样的队列，消费同一个消息内容（相当于广播到各个节点）

    @RabbitListener(bindings = @QueueBinding(value = @Queue, exchange = @Exchange(value = MqConst.STATISTICS_INIT_FINISH_EXCHANGE, type = ExchangeTypes.TOPIC), key = {
        MqConst.EXAM_STATISTICS_INIT_FINISH_ROUTING_KEY}))
    public void examStatisticsConsumer(@Payload ExamStatisticsInitFinishEvent finishEvent,
        @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag, Channel channel) throws IOException {

        log.info("examStatisticsConsumer receive event : "+JsonUtil.objToJson(finishEvent));
        UserThreadContext.setTenantId(finishEvent.getTenantId());
        //执行考试统计定时任务消费
        String type = finishEvent.getType();
        String batchId = finishEvent.getBatchId();
        String resourceId = finishEvent.getResourceId();
        if (ExamConstant.EXAM_STATISTICS_DEPT_JOB.equals(type)) {
            //按部门统计定时任务
            examAnswerStatisticsService.examStatisticsExecute(batchId);
        }else if(ExamConstant.EXAM_STATISTICS_DEPT_COLLECT_JOB.equals(type)){
            //按部门统计汇总定时任务
            examAnswerStatisticsService.makeExamAnswerOrgStatisticCollect(batchId,resourceId);
        }
        UserThreadContext.remove();
        //手动ACK
        ConsumerAckUtil.basicAck(finishEvent,channel,deliveryTag, true);
    }
}
