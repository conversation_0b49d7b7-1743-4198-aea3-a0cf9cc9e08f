package com.wunding.learn.exam.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wunding.learn.exam.service.client.dto.CompetitionWrongQuestionDTO;
import com.wunding.learn.exam.service.client.query.CompetitionWrongQuestionQuery;
import com.wunding.learn.exam.service.model.CompetitionWrongQuestion;
import java.util.List;

/**
 * pk赛错题集
 *
 * <AUTHOR>
 * @date 2023/10/23
 */
public interface ICompetitionWrongQuestionService extends IService<CompetitionWrongQuestion> {

    /**
     * 查询错题集
     * @param query
     * @return
     */
    List<CompetitionWrongQuestionDTO> queryWrongQuestionList(CompetitionWrongQuestionQuery query);

    /**
     * 保存错题集
     * @param competitionId
     * @param userId
     * @param questionId
     */
    void saveWrongQuestion(String competitionId, String userId, String questionId);

    /**
     * 删除错题集
     * @param id
     */
    void deleteWrongQuestion(String id);
}
