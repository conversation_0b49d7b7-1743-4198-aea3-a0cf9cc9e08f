package com.wunding.learn.exam.service.client.query;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <p>
 * 竞赛邀请参数
 * </p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">wgr</a>
 * @date 2023/10/17 14:52
 */
@Data
@Schema(name = "CompetitionInviteQuery", description = "竞赛邀请对象")
public class CompetitionInviteQuery {


    @Parameter(description = "被邀请的用户ID")
    @NotNull(message = "被邀请的用户ID不能为空")
    private String beInviteUserId;

    @Parameter(description = "竞赛ID")
    @NotNull(message = "竞赛ID不能为空")
    private String competitionId;
}
