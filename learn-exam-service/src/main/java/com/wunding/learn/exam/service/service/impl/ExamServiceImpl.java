package com.wunding.learn.exam.service.service.impl;

import static com.wunding.learn.common.util.string.StringUtil.newId;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import com.wunding.learn.common.bean.BaseEntity;
import com.wunding.learn.common.bean.BasePageQuery;
import com.wunding.learn.common.constant.dynamicDataSource.DynamicDataSourceConstant;
import com.wunding.learn.common.constant.exam.ExamErrorNoEnum;
import com.wunding.learn.common.constant.excel.ExcelTitleBaseCheckUtil;
import com.wunding.learn.common.constant.other.BaseErrorNoEnum;
import com.wunding.learn.common.constant.other.CommonConstants;
import com.wunding.learn.common.constant.other.ErrorNoEnum;
import com.wunding.learn.common.constant.tenant.TenantRedisKeyConstant;
import com.wunding.learn.common.constant.user.UserErrorNoEnum;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.dto.CerDitchDTO;
import com.wunding.learn.common.dto.ForImportExamDTO;
import com.wunding.learn.common.dto.LearningCalendarTaskDTO;
import com.wunding.learn.common.dto.LearningCalendarTaskQuery;
import com.wunding.learn.common.dto.ResourceBaseDTO;
import com.wunding.learn.common.dto.ResourceRecordSyncDTO;
import com.wunding.learn.common.dto.ResourceSyncDTO;
import com.wunding.learn.common.dto.TaskAppResourceDTO;
import com.wunding.learn.common.enums.ResourceTypeEnum;
import com.wunding.learn.common.enums.exam.IsTrainEnum;
import com.wunding.learn.common.enums.excitation.ExcitationEventCategoryEnum;
import com.wunding.learn.common.enums.excitation.ExcitationEventEnum;
import com.wunding.learn.common.enums.market.FirstInfoContentEnum;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.other.OperationEnum;
import com.wunding.learn.common.enums.other.PublishStatusEnum;
import com.wunding.learn.common.enums.other.ResourceTypeCodeEnum;
import com.wunding.learn.common.enums.other.RouterVisitEnum;
import com.wunding.learn.common.enums.other.SystemConfigCodeEnum;
import com.wunding.learn.common.enums.push.PushType;
import com.wunding.learn.common.enums.redis.ExamRedisKeyEnum;
import com.wunding.learn.common.exception.BusinessException;
import com.wunding.learn.common.library.record.enums.HandleTypeEnum;
import com.wunding.learn.common.library.record.enums.LibraryTypeEnum;
import com.wunding.learn.common.library.record.service.BaseLibraryRecordService;
import com.wunding.learn.common.mq.dto.ExcitationMQEventDTO;
import com.wunding.learn.common.mq.dto.ResourceConfigInitDTO;
import com.wunding.learn.common.mq.event.HomeRouterVisitEvent;
import com.wunding.learn.common.mq.event.ResourceChangeEvent;
import com.wunding.learn.common.mq.event.ResourceOperateEvent;
import com.wunding.learn.common.mq.event.ResourceRecordSyncEvent;
import com.wunding.learn.common.mq.event.ResourceSyncEvent;
import com.wunding.learn.common.mq.event.exam.ExamFinishEvent;
import com.wunding.learn.common.mq.event.exam.ExamPassEvent;
import com.wunding.learn.common.mq.event.exam.ExamSubmitEvent;
import com.wunding.learn.common.mq.event.exam.ExamSystemHandleQuestionEvent;
import com.wunding.learn.common.mq.event.exam.ExamUnPassEvent;
import com.wunding.learn.common.mq.event.exam.ExerciseFinishEvent;
import com.wunding.learn.common.mq.event.excitation.ExcitationInitMqEvent;
import com.wunding.learn.common.mq.event.excitation.ExcitationMQEvent;
import com.wunding.learn.common.mq.event.market.FirstInfoViewLimitChangeEvent;
import com.wunding.learn.common.mq.event.project.ActivityStatusChangeEvent;
import com.wunding.learn.common.mq.service.MqProducer;
import com.wunding.learn.common.mybatis.service.impl.BaseServiceImpl;
import com.wunding.learn.common.query.ResourceBaseQuery;
import com.wunding.learn.common.redis.util.RedisLockUtil;
import com.wunding.learn.common.util.bean.BeanListUtils;
import com.wunding.learn.common.util.bean.SpringUtil;
import com.wunding.learn.common.util.exam.ExamQuestionCheckUtil;
import com.wunding.learn.common.util.excel.ExcelCheckImportUtil;
import com.wunding.learn.common.util.http.WebUtil;
import com.wunding.learn.common.util.json.JsonUtil;
import com.wunding.learn.common.util.string.StringPool;
import com.wunding.learn.common.util.string.StringUtil;
import com.wunding.learn.common.viewlimit.constant.LimitTable;
import com.wunding.learn.common.viewlimit.service.IResourceViewLimitService;
import com.wunding.learn.exam.api.constraint.AnswerRecordValidStatusEnum;
import com.wunding.learn.exam.api.dto.ExamUserScoreDTO;
import com.wunding.learn.exam.api.dto.ImportUserExamRecordDTO;
import com.wunding.learn.exam.api.dto.ViewExamFeignDTO;
import com.wunding.learn.exam.api.mq.event.ExamGenerateQuestionEvent;
import com.wunding.learn.exam.api.query.TaskExamAppResourceQuery;
import com.wunding.learn.exam.service.admin.dto.ExamAnalysisQuestionDTO;
import com.wunding.learn.exam.service.admin.dto.ExamAnalysisQuestionOptionDTO;
import com.wunding.learn.exam.service.admin.dto.ExamCorrectExamListDTO;
import com.wunding.learn.exam.service.admin.dto.ExamCorrectExamListQueryDTO;
import com.wunding.learn.exam.service.admin.dto.ExamEditReviewerDTO;
import com.wunding.learn.exam.service.admin.dto.ExamListDTO;
import com.wunding.learn.exam.service.admin.dto.ExamQueryDTO;
import com.wunding.learn.exam.service.admin.dto.ExamReviewerDTO;
import com.wunding.learn.exam.service.admin.dto.ExamSchemaDetailDTO;
import com.wunding.learn.exam.service.admin.dto.ExamSchemaDetailMatrixDTO;
import com.wunding.learn.exam.service.admin.dto.ExamYearMonthStatDTO;
import com.wunding.learn.exam.service.admin.dto.ExerciseInfoDTO;
import com.wunding.learn.exam.service.admin.dto.ExercisePageDTO;
import com.wunding.learn.exam.service.admin.dto.ExerciseQueryDTO;
import com.wunding.learn.exam.service.admin.dto.LibQuestionQueryDTO;
import com.wunding.learn.exam.service.admin.dto.PreviewExamDTO;
import com.wunding.learn.exam.service.admin.dto.PreviewExerciseDTO;
import com.wunding.learn.exam.service.admin.dto.PublishExamDTO;
import com.wunding.learn.exam.service.admin.dto.SaveExamDTO;
import com.wunding.learn.exam.service.admin.dto.SaveExamImportQuestionDTO;
import com.wunding.learn.exam.service.admin.dto.SaveExamQuestionDTO;
import com.wunding.learn.exam.service.admin.dto.SaveExerciseDTO;
import com.wunding.learn.exam.service.admin.dto.SaveLibConfigDTO;
import com.wunding.learn.exam.service.admin.dto.SaveQuestionOptionDTO;
import com.wunding.learn.exam.service.admin.dto.SaveSchemaDetailDTO;
import com.wunding.learn.exam.service.admin.dto.UpdateExerciseDTO;
import com.wunding.learn.exam.service.admin.dto.ViewExamDTO;
import com.wunding.learn.exam.service.client.dto.AnswerRecordForSystemPostDTO;
import com.wunding.learn.exam.service.client.dto.ExamCompositionDTO;
import com.wunding.learn.exam.service.client.dto.ExamInfoDTO;
import com.wunding.learn.exam.service.client.dto.ExamQuestionDTO;
import com.wunding.learn.exam.service.client.dto.ExamQuestionListDTO;
import com.wunding.learn.exam.service.client.dto.ExamQuestionOptionDTO;
import com.wunding.learn.exam.service.client.dto.ExamRankMyDTO;
import com.wunding.learn.exam.service.client.dto.ExamRankUserDTO;
import com.wunding.learn.exam.service.client.dto.ExamResultAnalyseDTO;
import com.wunding.learn.exam.service.client.dto.ExamSubmitAnswerDTO;
import com.wunding.learn.exam.service.client.dto.ExamSubmitDTO;
import com.wunding.learn.exam.service.client.dto.ExamSystemHandleQuestionDTO;
import com.wunding.learn.exam.service.client.dto.ExerciserInfoDTO;
import com.wunding.learn.exam.service.client.dto.ExerciserQuestionDTO;
import com.wunding.learn.exam.service.client.dto.ExerciserQuestionListDTO;
import com.wunding.learn.exam.service.client.dto.ExerciserQuestionOptionDTO;
import com.wunding.learn.exam.service.client.dto.MyExamAnalysisDTO;
import com.wunding.learn.exam.service.client.dto.MyExamListDTO;
import com.wunding.learn.exam.service.client.dto.MyExamListQueryDTO;
import com.wunding.learn.exam.service.client.dto.MyExamQuestionAnalysisDTO;
import com.wunding.learn.exam.service.client.dto.MyExerciserDTO;
import com.wunding.learn.exam.service.client.dto.MyExerciserListQueryDTO;
import com.wunding.learn.exam.service.client.dto.OnGoingExamDTO;
import com.wunding.learn.exam.service.client.dto.QuestionAnalyseDTO;
import com.wunding.learn.exam.service.client.dto.ScreenEventCountDTO;
import com.wunding.learn.exam.service.client.dto.SubordinateExamDTO;
import com.wunding.learn.exam.service.client.query.ExamSearchQuery;
import com.wunding.learn.exam.service.client.query.SubordinateExamQuery;
import com.wunding.learn.exam.service.component.ExamViewLimitComponent;
import com.wunding.learn.exam.service.constant.ExamConstant;
import com.wunding.learn.exam.service.dao.ExamDao;
import com.wunding.learn.exam.service.enums.EquipmentTypeEnum;
import com.wunding.learn.exam.service.enums.ExamCheckStatusEnum;
import com.wunding.learn.exam.service.enums.ExamPostTypeEnum;
import com.wunding.learn.exam.service.enums.ExamQuestionTypeEnum;
import com.wunding.learn.exam.service.enums.ExamTypeEnum;
import com.wunding.learn.exam.service.enums.QuestionSourceTypeEnum;
import com.wunding.learn.exam.service.mapper.AnswerRecordBizAttrMapper;
import com.wunding.learn.exam.service.mapper.ExamMapper;
import com.wunding.learn.exam.service.mapper.ExamQuestionMapper;
import com.wunding.learn.exam.service.mapper.SchemaDetailMapper;
import com.wunding.learn.exam.service.model.AnswerRecord;
import com.wunding.learn.exam.service.model.AnswerRecordBizAttr;
import com.wunding.learn.exam.service.model.AnswerRecordDetail;
import com.wunding.learn.exam.service.model.Exam;
import com.wunding.learn.exam.service.model.ExamComposition;
import com.wunding.learn.exam.service.model.ExamLib;
import com.wunding.learn.exam.service.model.ExamQuestion;
import com.wunding.learn.exam.service.model.ExamQuestionOption;
import com.wunding.learn.exam.service.model.ExamReviewer;
import com.wunding.learn.exam.service.model.ExerciseRecord;
import com.wunding.learn.exam.service.model.LibConfigs;
import com.wunding.learn.exam.service.model.LibQuestion;
import com.wunding.learn.exam.service.model.LibQuestionOption;
import com.wunding.learn.exam.service.model.Library;
import com.wunding.learn.exam.service.model.Schema;
import com.wunding.learn.exam.service.model.SchemaDetail;
import com.wunding.learn.exam.service.model.TestPaper;
import com.wunding.learn.exam.service.service.IAnswerRecordDetailService;
import com.wunding.learn.exam.service.service.IAnswerRecordInThePastYearService;
import com.wunding.learn.exam.service.service.IAnswerRecordService;
import com.wunding.learn.exam.service.service.IExamCompositionService;
import com.wunding.learn.exam.service.service.IExamInThePastYearService;
import com.wunding.learn.exam.service.service.IExamLibService;
import com.wunding.learn.exam.service.service.IExamQuestionOptionService;
import com.wunding.learn.exam.service.service.IExamQuestionService;
import com.wunding.learn.exam.service.service.IExamReviewerService;
import com.wunding.learn.exam.service.service.IExamService;
import com.wunding.learn.exam.service.service.IExerciseRecordService;
import com.wunding.learn.exam.service.service.ILibConfigsService;
import com.wunding.learn.exam.service.service.ILibQuestionOptionService;
import com.wunding.learn.exam.service.service.ILibQuestionService;
import com.wunding.learn.exam.service.service.ILibraryService;
import com.wunding.learn.exam.service.service.ISchemaDetailService;
import com.wunding.learn.exam.service.service.ISchemaService;
import com.wunding.learn.exam.service.service.IScreenEventLogService;
import com.wunding.learn.exam.service.service.ITestPaperQuestionOptionService;
import com.wunding.learn.exam.service.service.ITestPaperQuestionService;
import com.wunding.learn.exam.service.service.ITestPaperService;
import com.wunding.learn.file.api.component.ExportComponent;
import com.wunding.learn.file.api.constant.ExportBizType;
import com.wunding.learn.file.api.constant.ExportFileNameEnum;
import com.wunding.learn.file.api.constant.FileBizType;
import com.wunding.learn.file.api.constant.ImageBizType;
import com.wunding.learn.file.api.dto.AbstractExportDataDTO;
import com.wunding.learn.file.api.dto.CopyFileDTO;
import com.wunding.learn.file.api.dto.IExportDataDTO;
import com.wunding.learn.file.api.dto.ImportDataDTO;
import com.wunding.learn.file.api.service.FileFeign;
import com.wunding.learn.file.api.service.ImportDataFeign;
import com.wunding.learn.push.api.dto.PushAttributeDTO;
import com.wunding.learn.push.api.dto.PushNoticeSetDTO;
import com.wunding.learn.push.api.dto.PushResourceDTO;
import com.wunding.learn.push.api.dto.SendPushDTO;
import com.wunding.learn.push.api.service.PushFeign;
import com.wunding.learn.user.api.dto.OrgDTO;
import com.wunding.learn.user.api.dto.UserDTO;
import com.wunding.learn.user.api.dto.UserOrgDTO;
import com.wunding.learn.user.api.dto.UserRankBaseInfoDTO;
import com.wunding.learn.user.api.dto.viewlimit.ViewLimitBaseInfoDTO;
import com.wunding.learn.user.api.query.ProgrammedIdQuery;
import com.wunding.learn.user.api.service.OrgFeign;
import com.wunding.learn.user.api.service.ParaFeign;
import com.wunding.learn.user.api.service.UserFeign;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;


/**
 * 考试表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">xiao8</a>
 * @date 2022-03-19
 */
@Slf4j
@Service("examService")
public class ExamServiceImpl extends BaseServiceImpl<ExamMapper, Exam> implements IExamService, CommandLineRunner {

    private static final String EXAM_ID_KEY = "examId";
    public static final String UNEXPECTED_VALUE = "Unexpected value: ";

    @Resource
    ILibConfigsService libConfigsService;
    @Resource
    IScreenEventLogService screenEventLogService;
    @Resource
    private OrgFeign orgFeign;
    @Resource
    private UserFeign userFeign;
    @Resource
    private ParaFeign paraFeign;
    @Resource
    private IExerciseRecordService exerciseRecordService;
    @Resource
    private FileFeign fileFeign;
    @Resource
    private IExamLibService examLibService;
    @Resource
    @Lazy
    private ILibraryService libraryService;
    @Resource
    private IResourceViewLimitService resourceViewLimitService;
    @Resource
    private ISchemaDetailService schemaDetailService;
    @Resource
    @Lazy
    private IExamQuestionService examQuestionService;
    @Resource
    @Lazy
    private IAnswerRecordService answerRecordService;
    @Resource
    private ExamViewLimitComponent examViewLimitComponent;
    @Resource
    @Lazy
    private ILibQuestionService libQuestionService;
    @Resource
    private ILibQuestionOptionService libQuestionOptionService;
    @Resource
    private IExamReviewerService examReviewerService;
    @Resource(name = "redisTemplate")
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    @Lazy
    private IAnswerRecordDetailService answerRecordDetailService;
    @Resource
    private IExamQuestionOptionService examQuestionOptionService;
    @Resource
    @Lazy
    private ITestPaperService testPaperService;
    @Resource
    private ITestPaperQuestionService testPaperQuestionService;
    @Resource
    private ITestPaperQuestionOptionService testPaperQuestionOptionService;
    @Resource
    @Lazy
    private ISchemaService schemaService;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private ExportComponent exportComponent;
    @Resource
    private ExamQuestionMapper examQuestionMapper;
    @Resource
    private ImportDataFeign importDataFeign;
    @Resource
    private AnswerRecordBizAttrMapper answerRecordBizAttrMapper;
    @Resource
    private SchemaDetailMapper schemaDetailMapper;
    @Resource
    private PushFeign pushFeign;
    @Resource(name = "examDao")
    private ExamDao examDao;
    @Resource
    private IExamCompositionService examCompositionService;
    @Resource
    private BaseLibraryRecordService baseLibraryRecordService;
    @Resource
    private IAnswerRecordInThePastYearService answerRecordInThePastYearService;
    @Resource
    private IExamInThePastYearService examInThePastYearService;

    @Override
    public PageInfo<ExamListDTO> findExamListByPage(ExamQueryDTO examQueryDTO) {

        //1，设置考试列表查询条件
        setFindExamListByPageQuery(examQueryDTO);

        //2，进行数据查询
        PageInfo<ExamListDTO> listDTOPageInfo;
        int totalCount = baseMapper.selectCountByPage(examQueryDTO);
        if (Objects.equals(totalCount, 0)) {
            // 如果无数据,直接返回
            listDTOPageInfo = new PageInfo<>();
            listDTOPageInfo.setTotal(totalCount);
            return listDTOPageInfo;
        }
        listDTOPageInfo = PageMethod.startPage(examQueryDTO.getPageNo(), examQueryDTO.getPageSize())
            .doSelectPageInfo(() -> baseMapper.selectListByPage(examQueryDTO));

        //3,处理查询结果
        setFindExamListByPageResult(listDTOPageInfo);

        //4,路由访问埋点
        mqProducer.sendMsg(
            new HomeRouterVisitEvent(RouterVisitEnum.ExamManagement.getRouterId(), UserThreadContext.getUserId(),
                RouterVisitEnum.ExamManagement.getName()));

        return listDTOPageInfo;
    }

    /**
     * 设置考试列表查询条件
     *
     * @param examQueryDTO
     * @return
     */
    private void setFindExamListByPageQuery(ExamQueryDTO examQueryDTO) {
        Set<String> managerAreaOrgIds = orgFeign.findUserManageAreaLevelPath(UserThreadContext.getUserId());
        examQueryDTO.setCurrentOrgId(UserThreadContext.getOrgId());
        examQueryDTO.setCurrentUserId(UserThreadContext.getUserId());
        examQueryDTO.setManagerAreaOrgIds(managerAreaOrgIds);
        // 兼容旧业务 默认查资源本身
        if (Objects.isNull(examQueryDTO.getIsTrain())) {
            examQueryDTO.setIsTrain(0);
        } else if (Objects.equals(examQueryDTO.getIsTrain(), 3)) {
            examQueryDTO.setIsTrain(null);
        }
        // sourceType前端不传
        if (StringUtils.isBlank(examQueryDTO.getSourceType())) {
            examQueryDTO.setSourceType(null);
        } else {
            if (examQueryDTO.getSourceType().contains(",")) {
                String[] split = examQueryDTO.getSourceType().split(",");
                List<String> sourceTypeList = List.of(split);
                examQueryDTO.setSourceTypes(sourceTypeList);
            } else {
                examQueryDTO.setSourceTypes(Collections.singletonList(examQueryDTO.getSourceType()));
            }
        }
    }

    /**
     * 处理考试列表查询结果
     *
     * @param listDTOPageInfo
     * @return
     */
    private void setFindExamListByPageResult(PageInfo<ExamListDTO> listDTOPageInfo) {
        List<ExamListDTO> list = listDTOPageInfo.getList();
        //设置考试列表组织数据
        setFindExamListOrg(list);
        //设置考试列表用户数据
        setFindExamListUser(list);
    }

    /**
     * 设置考试列表组织数据
     *
     * @param list
     * @return
     */
    private void setFindExamListOrg(List<ExamListDTO> list) {
        Set<String> orgCollect = list.stream().map(ExamListDTO::getOrgId)
            .filter(StringUtils::isNotBlank).collect(Collectors.toSet());

        Map<String, OrgDTO> userAndOrgMapByOrgIds = new HashMap<>();
        if (!CollectionUtils.isEmpty(orgCollect)) {
            userAndOrgMapByOrgIds = orgFeign.getUserAndOrgMapByOrgIds(orgCollect);
        }
        for (ExamListDTO dto : list) {
            if (!CollectionUtils.isEmpty(userAndOrgMapByOrgIds)) {
                OrgDTO orgDTO = userAndOrgMapByOrgIds.get(dto.getOrgId());
                if (orgDTO != null && StringUtils.isNotEmpty(dto.getOrgId())) {
                    dto.setOrgName(orgDTO.getOrgName());
                }
            }
        }
    }

    /**
     * 设置考试列表用户数据
     *
     * @param list
     * @return
     */
    private void setFindExamListUser(List<ExamListDTO> list) {
        Map<String, UserOrgDTO> userAndOrgMapByUserIds;
        Set<String> userCollect = list.stream().map(ExamListDTO::getPublishBy)
            .filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(userCollect)) {
            return;
        }
        userAndOrgMapByUserIds = orgFeign.getUserAndOrgMapByUserIds(userCollect);
        if (null == userAndOrgMapByUserIds) {
            return;
        }
        for (ExamListDTO dto : list) {
            if (!dto.getIsPublish().equals(ExamConstant.EXAM_IS_PUBLISH)) {
                continue;
            }
            UserOrgDTO userOrgDTO = userAndOrgMapByUserIds.get(dto.getPublishBy());
            if (userOrgDTO != null) {
                dto.setPublishBy(userOrgDTO.getFullName());
            } else {
                dto.setPublishBy(CommonConstants.DELETED);
            }
        }
    }

    @Override
    public List<ExamListDTO> findExamList(ExamQueryDTO examQueryDTO) {
        return baseMapper.selectListByPage(examQueryDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Exam saveOrUpdateExam(SaveExamDTO saveExamDTO) {
        // 基础校验
        commonCheck(saveExamDTO);

        // 当前登录用户
        String curUserId = UserThreadContext.getUserId();

        // 考试信息初始化
        Exam exam = initializeExam(saveExamDTO, curUserId);

        Exam examDB = getById(exam.getId());
        if (examDB != null && Objects.equals(ExamConstant.EXAM_TYPE_ENTER, saveExamDTO.getSourceType())
            && StringUtils.isEmpty(saveExamDTO.getTestPaperId())) {
            exam.setTestPaperId(examDB.getTestPaperId());
        }

        // 默认发布场景处理
        handleDefaultPublishScenario(saveExamDTO, exam, curUserId);

        // 考试规则设置
        setExamRules(saveExamDTO, exam);

        StringUtils.isBlank(saveExamDTO.getId());

        // 保存或更新考试信息
        if (StringUtils.isBlank(saveExamDTO.getId())) {
            examDao.saveExam(exam);
        } else {
            examDao.updateExam(exam);
        }

        // 处理考试题目来源和激励配置
        handleExamSourceAndIncentive(saveExamDTO, exam);

        // 设置阅卷方式
        setExamCheckPaperMethod(exam);

        // 处理下发范围
        examViewLimitComponent.handleNewViewLimit(saveExamDTO.getProgrammeId(), saveExamDTO.getId());

        // 更新头条下发范围
        mqProducer.sendMsg(new FirstInfoViewLimitChangeEvent(exam.getId(), FirstInfoContentEnum.exam.name(),
            saveExamDTO.getProgrammeId()));

        // 处理审核人列表
        examReviewerService.handleExamReviewerList(StringUtils.isBlank(saveExamDTO.getId()), exam.getId(),
            saveExamDTO.getExamReviewer());

        // 更新人和更新时间
        exam.setUpdateBy(curUserId);
        exam.setUpdateTime(new Date());

        // 保存或更新考试信息
        saveOrUpdate2(exam);

        // 设置推送通知配置
        setLastPushNoticeSetRedis(exam.getId(), saveExamDTO.getPushNoticeSetDTO(),
            StringUtils.isBlank(saveExamDTO.getId()) ? 0 : 1);

        // 判断是否需要进行组卷
        log.info("checkIsGenerate(exam.getId()):" + checkIsGenerate(exam.getId()));
        if (checkIsGenerate(exam.getId())) {
            // 处理重新组卷或生成考试组卷方案及自动发布
            boolean isSourceTypeImportExam = saveExamDTO.getSourceType().equals(ExamConstant.EXAM_TYPE_IMPORT);
            handleGenerateExam(isSourceTypeImportExam, exam, curUserId);
        } else {
            // 自动发布检测
            autoPublish(exam.getId(), UserThreadContext.getUserId());
        }

        return exam;
    }

    /**
     * 初始化考试saveExamDTO 转换 exam 实体
     *
     * @param saveExamDTO 考试数据DTO
     * @param curUserId   当前用户id
     * @return {@link Exam } 考试实体
     */
    private Exam initializeExam(SaveExamDTO saveExamDTO, String curUserId) {
        // 操作时间
        Date now = new Date();
        Exam exam = new Exam();
        BeanUtils.copyProperties(saveExamDTO, exam);

        boolean isInsert = StringUtils.isBlank(saveExamDTO.getId());
        if (isInsert) {
            exam.setId(newId());
            exam.setExamNo(generateCode());
            exam.setOrgId(UserThreadContext.getOrgId());
            exam.setCreateBy(curUserId);
            exam.setCreateTime(now);
            // 当下发范围为仅创建人可见,不用鉴权
            if (saveExamDTO.getViewType() != 1 && saveExamDTO.getIsTrain() == 0) {
                //对调整的下发范围进行鉴权
                ProgrammedIdQuery programmedIdQuery = new ProgrammedIdQuery();
                programmedIdQuery.setNewProgrammeId(saveExamDTO.getProgrammeId());
                boolean checkViewLimit = userFeign.checkUserManageArea(programmedIdQuery);
                if (!checkViewLimit) {
                    throw new BusinessException(ErrorNoEnum.ERR_VIEW_LIMIT);
                }
            }
            return exam;
        }
        // 更新操作
        exam.setId(saveExamDTO.getId());
        exam.setUpdateBy(curUserId);
        exam.setUpdateTime(now);
        // 当下发范围为仅创建人可见,不用鉴权
        if (saveExamDTO.getViewType() != 1 && saveExamDTO.getIsTrain() == 0) {
            //查询考试的历史下发方案
            Long programmeId = resourceViewLimitService.getViewLimitIdByResourceId(saveExamDTO.getId());
            //下发范围方案调整时,对调整的下发范围进行鉴权
            if (!programmeId.equals(saveExamDTO.getProgrammeId())) {
                ProgrammedIdQuery programmedIdQuery = new ProgrammedIdQuery();
                programmedIdQuery.setNewProgrammeId(saveExamDTO.getProgrammeId());
                programmedIdQuery.setOldProgrammeId(programmeId);
                boolean checkViewLimit = userFeign.checkUserManageArea(programmedIdQuery);
                if (!checkViewLimit) {
                    throw new BusinessException(ErrorNoEnum.ERR_VIEW_LIMIT);
                }
            }
        }
        return exam;
    }

    /**
     * 处理默认发布场景
     *
     * @param saveExamDTO 考试数据DTO
     * @param exam        考试实体
     * @param curUserId   当前用户id
     */
    private void handleDefaultPublishScenario(SaveExamDTO saveExamDTO, Exam exam, String curUserId) {
        // 1.开启自动发布
        boolean isAutoPublish = saveExamDTO.getIsAutoPublish() != null && saveExamDTO.getIsAutoPublish() == 1;
        // 2.学习项目考试
        boolean isProjectExam =
            Objects.equals(exam.getIsTrain(), IsTrainEnum.PROJECT.getValue()) && Objects.isNull(exam.getIsPublish());

        if (isAutoPublish || isProjectExam) {
            exam.setPublishBy(curUserId);
        }
    }

    /**
     * 设置考试规则
     *
     * @param saveExamDTO 考试数据DTO
     * @param exam        考试实体
     */
    private void setExamRules(SaveExamDTO saveExamDTO, Exam exam) {
        // 考试规则
        exam.setLimitScreenCuts(
            saveExamDTO.getIsLimitScreenCuts() == null || Objects.equals(saveExamDTO.getIsLimitScreenCuts(), 0)
                ? -1
                : saveExamDTO.getLimitScreenCuts());
        exam.setLimitScreenShots(
            saveExamDTO.getIsLimitScreenShots() == null || Objects.equals(saveExamDTO.getIsLimitScreenShots(), 0)
                ? -1
                : saveExamDTO.getLimitScreenShots());
        exam.setLimitScreenRecords(
            saveExamDTO.getIsLimitScreenRecords() == null || Objects.equals(saveExamDTO.getIsLimitScreenRecords(), 0)
                ? -1
                : saveExamDTO.getLimitScreenRecords());
        // 是否显示排名，默认为1-显示
        exam.setIsViewRanking(
            saveExamDTO.getIsViewRanking() == null || Objects.equals(saveExamDTO.getIsViewRanking(), 1)
                ? 1 : 0);

        if (StringUtils.isBlank(exam.getOrgId())) {
            // 创建组织ID
            exam.setOrgId(UserThreadContext.getOrgId());
        }

        // 是否防止切屏
        exam.setCutScreen(saveExamDTO.getIsScreenCut());
        // 允许查看答题详情
        exam.setIsCanViewAnswer(saveExamDTO.getIsCanViewAnswer());
        // sourceType
        exam.setSourceType(String.valueOf(saveExamDTO.getSourceType()));
        // 考试类型
        exam.setExamType(String.valueOf(ExamTypeEnum.EXAM.getCode()));
        // 考试资源本身的来源处理
        if (Objects.equals(saveExamDTO.getIsTrain(), IsTrainEnum.ITSELF.getValue())) {
            // 开始时间
            exam.setStartTime(saveExamDTO.getStartTimeAndEndTime().get(0));
            // 结束时间
            exam.setEndTime(saveExamDTO.getStartTimeAndEndTime().get(1));
        }
    }

    /**
     * 处理考试来源与激励
     *
     * @param saveExamDTO 将考试数据保存到
     * @param exam        考试
     */
    private void handleExamSourceAndIncentive(SaveExamDTO saveExamDTO, Exam exam) {
        //如果考试题目来源是引入题库试卷,获取上一次组卷矩阵的内容，存入redis用于判断矩阵内容是否有发生变化，考试是否需要重新进行组卷
        if (Objects.equals(ExamConstant.EXAM_TYPE_SCHEMA, saveExamDTO.getSourceType())) {
            setLastExamLibRedis(exam.getId());
        }
        boolean isInsert = StringUtils.isBlank(saveExamDTO.getId());
        if (isInsert) {
            saveExamDTO.setId(exam.getId());
            // 处理题目来源 1.导入试卷 2.引入试卷 3.引入题库试卷
            handleSourceType(saveExamDTO, exam);
            // 发送考激励配置规则初始化事件
            mqProducer.sendMsg(new ExcitationInitMqEvent(new ResourceConfigInitDTO().setResourceId(exam.getId())
                .setResourceType(ExcitationEventCategoryEnum.EXAM.getCode())));
        } else {
            if (saveExamDTO.getSourceType().equals(ExamConstant.EXAM_TYPE_SCHEMA)
                && saveExamDTO.getExamSchemaDetail() != null
                && !saveExamDTO.getExamSchemaDetail().isEmpty()) {
                saveExamLib(saveExamDTO, exam);
            }
            if (saveExamDTO.getSourceType().equals(ExamConstant.EXAM_TYPE_AI)) {
                handleAiType(saveExamDTO, exam);
            } else if (Objects.equals(ExamConstant.EXAM_TYPE_ENTER, saveExamDTO.getSourceType())
                && StringUtils.isNotEmpty(saveExamDTO.getTestPaperId())) {
                examQuestionService.delAllByexamId(exam.getId());
                examCompositionService.deleteByExamId(exam.getId());
                handleEnterType(saveExamDTO, exam);
            }
            mqProducer.sendMsg(
                new ResourceOperateEvent(OperationEnum.UPDATE, PushType.EXAM.getKey(), saveExamDTO.getId()));
        }
    }

    @Override
    public ForImportExamDTO getExerciseExcelInfo(String excelFileUrl) {
        ImportDataDTO importData = importDataFeign.getImportData(excelFileUrl);
        String[][] excel = importData.getExcel();
        ForImportExamDTO importExamDTO = ExcelCheckImportUtil.checkForImportExam(excel, true, false);
        // 需求改造使用考试一样的模版导入
//      ForImportExamDTO importExamDTO = ExcelCheckImportUtil.checkForImportExam(excel, false, false, false, true);
        String errorMsg = importExamDTO.getMsg();
        if (StringUtils.isNotBlank(errorMsg)) {
            throw new BusinessException(BaseErrorNoEnum.ERR_IMPORT_DATA_CHECK, null, errorMsg);
        }
        return importExamDTO;
    }

    /**
     * 处理重新组卷或生成考试组卷方案及自动发布
     *
     * @param isSourceTypeImportExam 是否是导入试卷类型
     * @param exam                   考试实体
     * @param curUserId              当前用户id
     */
    @Override
    public void handleGenerateExam(boolean isSourceTypeImportExam, Exam exam, String curUserId) {
        // 需要进行重新组卷
        if (!isSourceTypeImportExam) {
            //异步组卷,(题库组卷,或者引用组卷都是异步进行生成考题)
            mqProducer.sendMsg(new ExamGenerateQuestionEvent(exam.getId(), curUserId));
            return;
        }
        //同步组卷,(导入组卷是同步生成(因为导入是只有一份，速度快，给用户响应))
        //更新考试为未发布状态(记录原来的自动发布状态)
        log.info("saveOrUpdateExam examId:{} 更新考试为未发布状态", exam.getId());
        PublishExamDTO publishExamDTO = new PublishExamDTO();
        publishExamDTO.setIsPublish(PublishStatusEnum.IS_NO_PUBLISH.getValue());
        publishExamDTO.setIds(Collections.singletonList(exam.getId()));
        publishExamDTO.setIsAutoPublish(exam.getIsAutoPublish());
        this.publish(publishExamDTO);
        log.info("saveOrUpdateExam examId:{} 进行组卷开始", exam.getId());
        generateComposition(exam.getId());
        log.info("saveOrUpdateExam examId:{} 进行组卷结束", exam.getId());
        //进行自动发布检测
        log.info("saveOrUpdateExam examId:{} 自动发布检测开始", exam.getId());
        autoPublish(exam.getId(), curUserId);
        log.info("saveOrUpdateExam examId:{} 自动发布检测结束", exam.getId());
    }

    /**
     * 设置上一份题库组卷信息到redis
     *
     * @param examId
     * @return
     */
    private void setLastExamLibRedis(String examId) {
        Map<String, Object> redisData = new HashMap<>();

        // 组卷策略详情
        List<SchemaDetail> schemaDetails = schemaDetailService.list(
            new LambdaQueryWrapper<SchemaDetail>().eq(SchemaDetail::getSchemaId, examId));
        // 组卷配置
        List<LibConfigs> libConfigs = libConfigsService.list(
            new LambdaQueryWrapper<LibConfigs>().eq(LibConfigs::getExamId, examId));
        // 题库表
        List<ExamLib> examLibs = examLibService.list(new LambdaQueryWrapper<ExamLib>().eq(ExamLib::getExamId, examId));

        redisData.put("schemaDetails", schemaDetails);
        redisData.put("libConfigs", libConfigs);
        redisData.put("examLibs", examLibs);

        redisTemplate.opsForHash().put(ExamRedisKeyEnum.LAST_EXAM_LIB.getKey(), examId, redisData);

    }

    /**
     * 从redis获取上一份题库组卷信息
     *
     * @param examId
     * @return
     */
    private Map<String, Object> getLastExamLibRedis(String examId) {
        return (Map<String, Object>) redisTemplate.opsForHash().get(ExamRedisKeyEnum.LAST_EXAM_LIB.getKey(), examId);
    }

    /**
     * 检查考试是否需要重新进行组卷 重新生成组卷方案条件： 1,考试之前组卷失败,需要进行组卷 2,考试正在组卷中，不需要进行组卷 3,考试没有在进行组卷,数据库中没有组卷方案;
     * 4,考试没有在进行组卷,数据库中组卷方案数量与当前考试的配置组卷方案数量不一致 5,组卷方式是题库组卷，组卷方案发生变化
     *
     * @param examId
     * @return
     */
    @Override
    public boolean checkIsGenerate(String examId) {
        Boolean result = null;
        Exam exam = baseMapper.selectById(examId);
        if (null == exam) {
            result = false;
            return result;
        }
        // 检查考试组卷状态
        result = checkCompositionStatus(exam);
        if (null != result) {
            return result;
        }
        // 检查当前题目数量与组卷方案数量是否一致
        result = checkCompositionCount(exam);
        if (null != result) {
            return result;
        }
        // 检查组卷方案是否变化
        result = checkCompositionSchema(exam);
        if (null != result) {
            return result;
        }
        result = false;
        log.info("checkIsGenerate result:{}", result);
        return result;
    }

    /**
     * 检查考试组卷状态
     *
     * @param exam
     * @return
     */
    private Boolean checkCompositionStatus(Exam exam) {
        Boolean result = null;
        if (ExamConstant.COMPOSITION_FAIL == exam.getCompositionStatus()) {
            log.info("checkIsGenerate examId :{},考试之前组卷失败,需要进行组卷", exam.getId());
            result = true;
            return result;
        }
        if (ExamConstant.COMPOSITION_ING == exam.getCompositionStatus()) {
            // 1,考试正在组卷中，不需要进行组卷
            log.info("checkIsGenerate examId :{},考试正在组卷中，不需要进行组卷", exam.getId());
            result = false;
            return result;
        }
        return result;
    }

    /**
     * 检查当前题目数量与组卷方案数量是否一致
     *
     * @param exam
     * @return
     */
    private Boolean checkCompositionCount(Exam exam) {
        Boolean result = null;
        Integer compositionCount =
            (exam.getCompositionCount() == null || Objects.equals(0, exam.getCompositionCount())) ? 1
                : exam.getCompositionCount();
        List<ExamComposition> examCompositionListInDb = examCompositionService
            .list(new LambdaQueryWrapper<ExamComposition>().eq(ExamComposition::getExamId, exam.getId()));

        if (CollectionUtils.isEmpty(examCompositionListInDb)) {
            log.info("checkIsGenerate examId :{},考试没有在进行组卷,数据库中没有组卷方案,需要进行组卷", exam.getId());
            result = true;
            return result;
        }
        if (compositionCount.compareTo(examCompositionListInDb.size()) != 0) {
            // 3,考试没有在进行组卷,数据库中组卷方案数量与当前考试的配置组卷方案数量不一致,需要进行组卷
            log.info(
                "checkIsGenerate examId :{},考试没有在进行组卷,数据库中组卷方案数量与当前考试的配置组卷方案数量不一致,需要进行组卷",
                exam.getId());
            result = true;
            return result;
        }
        return result;
    }

    /**
     * 检查组卷方案是否变化
     *
     * @param exam
     * @return
     */
    private Boolean checkCompositionSchema(Exam exam) {
        // 4,组卷方式是题库组卷，组卷方案发生变化
        Boolean result = null;
        String examId = exam.getId();
        if (!Objects.equals(String.valueOf(ExamConstant.EXAM_TYPE_SCHEMA), exam.getSourceType())) {
            return result;
        }
        Map<String, Object> redisData = getLastExamLibRedis(examId);
        // redis没有数据，需要进行组卷
        if (null == redisData) {
            log.info("checkIsGenerate examId :{},redis没有数据，需要进行组卷", examId);
            result = true;
            return result;
        }

        List<SchemaDetail> lastSchemaDetails = (List) redisData.get("schemaDetails");
        List<LibConfigs> lastLibConfigs = (List) redisData.get("libConfigs");
        List<ExamLib> lastExamLibs = (List) redisData.get("examLibs");
        // 组卷策略详情
        List<SchemaDetail> schemaDetails = schemaDetailService.list(
            new LambdaQueryWrapper<SchemaDetail>().eq(SchemaDetail::getSchemaId, examId));
        if (!org.apache.commons.collections4.CollectionUtils.isEqualCollection(lastSchemaDetails, schemaDetails)) {
            log.info("checkIsGenerate examId :{},考试组卷策略详情发生变化,需要进行组卷", examId);
            result = true;
            return result;
        }
        // 组卷配置
        List<LibConfigs> libConfigs = libConfigsService
            .list(new LambdaQueryWrapper<LibConfigs>().eq(LibConfigs::getExamId, examId));
        if (!org.apache.commons.collections4.CollectionUtils.isEqualCollection(lastLibConfigs, libConfigs)) {
            log.info("checkIsGenerate examId :{},考试组卷配置发生变化,需要进行组卷", examId);
            result = true;
            return result;
        }
        // 题库表
        List<ExamLib> examLibs = examLibService
            .list(new LambdaQueryWrapper<ExamLib>().eq(ExamLib::getExamId, examId));
        if (!org.apache.commons.collections4.CollectionUtils.isEqualCollection(lastExamLibs, examLibs)) {
            log.info("checkIsGenerate examId :{},考试题库表发生变化,需要进行组卷", examId);
            result = true;
            return result;
        }
        return result;
    }

    /**
     * 考试修改发布操作后做的操作
     *
     * @param examId
     * @return
     */
    public void doAfterChangePublish(String examId) {
        Exam exam = baseMapper.selectById(examId);
        // 1,推送业务处理,发布后才给资源配置推送
        if (GeneralJudgeEnum.CONFIRM.getValue().equals(exam.getIsPublish()) && exam.getIsTrain() != 5) {
            try {
                sendPushFeign(exam);
            } catch (Exception e) {
                log.error("doAfterChangePublish 推送失败:", e);
            }
        }
    }

    /**
     * 发送推送
     *
     * @param exam
     * @return
     */
    private void sendPushFeign(Exam exam) {
        //从redis获取最后一次修改的推送配置信息
        Map<String, Object> redisData = getLastPushNoticeSetRedis(exam.getId());
        if (null == redisData || null == redisData.get(ExamConstant.PUSH_NOTICE_SET)) {
            return;
        }
        //获取操作类型
        Integer operateState = (Integer) redisData.get("operateState");
        String language = (String) redisData.get("language");
        PushNoticeSetDTO pushNoticeSetDTO = (PushNoticeSetDTO) redisData.get(ExamConstant.PUSH_NOTICE_SET);
        //当前系统服务名
        String serverName = (String) redisData.get("serverName");
        //获取下发策略
        ViewLimitBaseInfoDTO viewLimitBaseInfo = examViewLimitComponent.getViewLimitBaseInfo(exam.getId());

        SendPushDTO sendPushDTO = new SendPushDTO();
        sendPushDTO.setLang(language);
        sendPushDTO.setServerName(serverName);
        sendPushDTO.setPushNoticeSetDTO(pushNoticeSetDTO);

        // 为空默认是0
        Integer isTrain = Optional.ofNullable(exam.getIsTrain()).orElse(0);

        PushResourceDTO pushResourceDTO = new PushResourceDTO().setIsTrain(isTrain).setOperateState(operateState)
            .setProgrammeId(viewLimitBaseInfo.getProgrammeId());

        sendPushDTO.setPushResourceDTO(pushResourceDTO);

        PushAttributeDTO pushAttributeDTO = new PushAttributeDTO();

        boolean condition = isTrain != 0;

        if (condition) {
            // 非 0 证明是属于二级内容过来
            String[] name = pushNoticeSetDTO.getResourceName().split(StringPool.UNDERSCORE);
            String[] resourceId = pushNoticeSetDTO.getResourceId().split(StringPool.UNDERSCORE);
            pushAttributeDTO.setName(name[0]);
            pushAttributeDTO.setSecondaryName(name[1]);

            // 此时中 pushNoticeSetDTO 前端只会传 projectId_ 所以拼接好id
            pushResourceDTO.setResourceId(resourceId[0] + StringPool.UNDERSCORE + exam.getId());
        } else {
            pushAttributeDTO.setName(pushNoticeSetDTO.getResourceName());
            pushResourceDTO.setResourceId(exam.getId());
        }

        pushResourceDTO.setResourceName(pushNoticeSetDTO.getResourceName());
        pushResourceDTO.setResourceType(pushNoticeSetDTO.getResourceType());
        pushAttributeDTO.setExistSecondary(condition);
        pushAttributeDTO.setStartTime(exam.getStartTime());
        pushAttributeDTO.setEndTime(exam.getEndTime());
        pushAttributeDTO.setIntro(exam.getDescription());
        pushAttributeDTO.setExamDuration(String.valueOf(exam.getExamTimeCount()));

        @SuppressWarnings("unchecked") String systemName = (String) redisTemplate.opsForHash()
            .get(TenantRedisKeyConstant.SYSTEM_NAME_KEY, UserThreadContext.getTenantId());
        pushAttributeDTO.setSystemName(systemName);

        sendPushDTO.setPushAttributeDTO(pushAttributeDTO);

        pushFeign.sendPush(sendPushDTO);
    }

    @Override
    public void setExamCheckPaperMethod(String id) {
        Exam exam = getById(id);
        // 设置考试阅卷方式字段
        setExamCheckPaperMethod(exam);
        // 组卷方案和
        updateById(exam);
    }

    /**
     * 设置阅卷方式未操作数据库
     *
     * @param exam 考试
     */
    public void setExamCheckPaperMethod(Exam exam) {
        int sourceType = Integer.parseInt(exam.getSourceType());
        if (sourceType == ExamConstant.EXAM_TYPE_IMPORT || sourceType == ExamConstant.EXAM_TYPE_ENTER
            || sourceType == ExamConstant.EXAM_TYPE_AI
        ) {
            boolean artificial = examQuestionService.getTestPaperType(exam.getId());
            if (artificial) {
                // 有问答，1-人工改卷
                exam.setCheckPaperMethod(ExamConstant.EXAM_MANUAL_REWINDING);
            } else {
                exam.setCheckPaperMethod(ExamConstant.EXAM_IS_SYSTEM_CHECK);
            }
        }
    }

    @Override
    public void delExamById(String ids) {
        delById(ids, true);
    }

    @Override
    public void delExerciseById(String ids) {
        delById(ids, false);
    }

    private void delById(String ids, boolean isExam) {
        // 查询待删除考试信息
        List<String> idsList = Arrays.asList(ids.split(","));
        List<Exam> exams = list(new LambdaQueryWrapper<Exam>().in(Exam::getId, idsList));
        // 包含已发布的资源，无法删除
        long count = exams.stream().filter(e -> e.getIsPublish() == ExamConstant.EXAM_IS_PUBLISH).count();
        if (count > 0) {
            throw new BusinessException(BaseErrorNoEnum.INCLUDING_IS_PUBLISH);
        }
        // 引入题库试卷考试，同步删除考试对应题库记录
        Set<String> examLibIds = exams.stream()
            .filter(e -> e.getSourceType().equals(String.valueOf(ExamConstant.EXAM_TYPE_SCHEMA))).map(Exam::getId)
            .collect(Collectors.toSet());
        if (!examLibIds.isEmpty()) {
            examLibService.deletedByExamIds(examLibIds);
        }
        // 批量删除考试
        exams.forEach(e -> {
            if (isExam) {
                examDao.delExam(e);
            } else {
                examDao.delExercise(e);
            }
        });
        // 发送消息
        sendDelMsg(exams, isExam);

        idsList.forEach(id -> examViewLimitComponent.delViewLimit(id));
    }

    private void sendDelMsg(List<Exam> exams, boolean isExam) {
        exams.forEach(exam -> {
            // 发送资源修改信息
            mqProducer.sendMsg(new ResourceChangeEvent(
                isExam ? FirstInfoContentEnum.exam.name() : FirstInfoContentEnum.exercise.name(),
                Collections.singletonList(exam.getId()), GeneralJudgeEnum.CONFIRM.getValue(),
                GeneralJudgeEnum.NEGATIVE.getValue()));
            // 发送同步状态到岗位发展管理事件消息
            ActivityStatusChangeEvent.sendMsg(Collections.singletonList(exam.getId()),
                isExam ? ResourceTypeCodeEnum.EXAM : ResourceTypeCodeEnum.EXERCISE, null, 1, mqProducer);
            // 发送资源操作事件消息
            mqProducer.sendMsg(new ResourceOperateEvent(OperationEnum.DELETE,
                isExam ? PushType.EXAM.getKey() : PushType.EXERCISE.getKey(), exam.getId()));
            // 发送资源同步事件消息
            if (Objects.equals(exam.getIsTrain(), IsTrainEnum.ITSELF.getValue())) {
                mqProducer.sendMsg(new ResourceSyncEvent(new ResourceSyncDTO(OperationEnum.UPDATE,
                    isExam ? ResourceTypeEnum.EXAM.name() : ResourceTypeEnum.EXERCISE.name(), exam.getId(), null, null,
                    null, null, null, 1, null, null, UserThreadContext.getUserId(), new Date())));
            }
        });
    }

    @Override
    public void delNotCheckExamById(String ids) {
        String[] strIds = ids.split(",");
        List<String> idsList = Arrays.asList(strIds);
        List<Exam> exams = examDao.getListByIds(idsList);

        Set<String> delExamLibIds = exams.stream()
            .filter(e -> e.getSourceType().equals(String.valueOf(ExamConstant.EXAM_TYPE_SCHEMA))).map(Exam::getId)
            .collect(Collectors.toSet());
        if (!CollectionUtils.isEmpty(delExamLibIds)) {
            examLibService.deletedByExamIds(delExamLibIds);
        }

        removeBatchByIds2(idsList);

        // 发送消息，同步状态到岗位发展管理
        ActivityStatusChangeEvent.sendMsg(idsList, ResourceTypeCodeEnum.EXAM, null, 1, mqProducer);
        // 发送资源修改信息
        mqProducer.sendMsg(new ResourceChangeEvent(
            exams.get(0).getExamType().equals(GeneralJudgeEnum.CONFIRM.getValue().toString())
                ? FirstInfoContentEnum.exam.name() : FirstInfoContentEnum.exercise.name(), idsList,
            GeneralJudgeEnum.CONFIRM.getValue(), GeneralJudgeEnum.NEGATIVE.getValue()));

    }

    @Override
    public ViewExamDTO getExamById(String id) {
        ViewExamDTO saveExamDTO = new ViewExamDTO();
        Exam dbExam = baseMapper.selectById(id);
        if (dbExam == null) {
            throw new BusinessException(ExamErrorNoEnum.ERR_EXAM_IS_NOT_EXIST);
        }
        // 生成返回对象
        BeanUtils.copyProperties(dbExam, saveExamDTO);

        // 返回归属部门名称
        saveExamDTO.setOrgName(orgFeign.getById(saveExamDTO.getOrgId()).getOrgName());

        if (Objects.equals(dbExam.getLimitScreenCuts(), -1)) {
            saveExamDTO.setIsLimitScreenCuts(0);
            saveExamDTO.setLimitScreenCuts(null);
        } else {
            saveExamDTO.setIsLimitScreenCuts(1);
        }
        if (Objects.equals(dbExam.getLimitScreenShots(), -1)) {
            saveExamDTO.setIsLimitScreenShots(0);
            saveExamDTO.setLimitScreenShots(null);
        } else {
            saveExamDTO.setIsLimitScreenShots(1);
        }
        if (Objects.equals(dbExam.getLimitScreenRecords(), -1)) {
            saveExamDTO.setIsLimitScreenRecords(0);
            saveExamDTO.setLimitScreenRecords(null);
        } else {
            saveExamDTO.setIsLimitScreenRecords(1);
        }
        // 题目来源
        saveExamDTO.setSourceType(Integer.valueOf(dbExam.getSourceType()));
        //设备类型
        saveExamDTO.setEquipment(dbExam.getEquipment());
        // 时间处理
        List<Date> time = new ArrayList<>();
        if (dbExam.getStartTime() != null) {
            time.add(dbExam.getStartTime());
        }
        if (dbExam.getEndTime() != null) {
            time.add(dbExam.getEndTime());
        }
        saveExamDTO.setStartTimeAndEndTime(time);

        // 是否防止切屏
        saveExamDTO.setIsScreenCut(dbExam.getCutScreen());

        // 拼装下发范围
        saveExamDTO.setLimit(examViewLimitComponent.getViewLimitBaseInfo(id));

        //拼接阅卷人
        List<ExamReviewerDTO> examReviewerDTO = examReviewerService.getExamReviewerDTOListByExamId(id);
        List<ExamEditReviewerDTO> examEditReviewerDTOS = BeanListUtils.copyListProperties(examReviewerDTO,
            ExamEditReviewerDTO::new);
        saveExamDTO.setExamReviewer(examEditReviewerDTOS);
        //引入试卷
        if (StringUtils.isNotEmpty(dbExam.getTestPaperId())) {
            saveExamDTO.setTestPaperId(dbExam.getTestPaperId());
            saveExamDTO.setTestPaperName(testPaperService.getById(dbExam.getTestPaperId()).getTestPaperName());
        }
        // 是否有答题记录
        saveExamDTO.setIsExistRecord(
            answerRecordService.count(new LambdaQueryWrapper<AnswerRecord>().eq(AnswerRecord::getExamId, id)) > 0 ? 1
                : 0);

        //阅卷方案 题库ID和题库名称
        List<String> libIdByExamId = new ArrayList<>();
        if (StringUtils.isNotEmpty(saveExamDTO.getSchemaId())) {
            libIdByExamId = examLibService.getLibIdByExamId(id);
            List<String> libNamesByLibIds = libraryService.getLibNamesByLibIds(libIdByExamId);
            //题库ID和题库名称
            saveExamDTO.setExamLibIds(libIdByExamId);
            saveExamDTO.setExamLibNames(libNamesByLibIds);
        }

        // 返回组卷方案
        getSchemaInfo(saveExamDTO, dbExam, libIdByExamId);
        return saveExamDTO;
    }

    private void getSchemaInfo(ViewExamDTO saveExamDTO, Exam dbExam, List<String> libIdByExamId) {
        if (dbExam.getSourceType().equals(ExamConstant.EXAM_TYPE_SCHEMA + "")) {
            // 查询组卷策略详情
            List<SchemaDetail> schemaDetailList = schemaDetailService.list(
                new LambdaQueryWrapper<SchemaDetail>().eq(SchemaDetail::getSchemaId, dbExam.getSchemaId())
                    .eq(SchemaDetail::getIsDel, 0));
            List<ExamSchemaDetailMatrixDTO> examSchemaDetailList = new ArrayList<>();
            ExamSchemaDetailMatrixDTO dto1 = new ExamSchemaDetailMatrixDTO("1");
            ExamSchemaDetailMatrixDTO dto2 = new ExamSchemaDetailMatrixDTO("2");
            ExamSchemaDetailMatrixDTO dto3 = new ExamSchemaDetailMatrixDTO("3");
            ExamSchemaDetailMatrixDTO dto4 = new ExamSchemaDetailMatrixDTO("4");
            ExamSchemaDetailMatrixDTO dto5 = new ExamSchemaDetailMatrixDTO("5");
            ExamSchemaDetailMatrixDTO dto6 = new ExamSchemaDetailMatrixDTO("6");
            for (SchemaDetail schemaDetail : schemaDetailList) {
                setExamShemaDetailDTO(dto1, dto2, dto3, dto4, dto5, dto6, schemaDetail);
            }

            // 查询考试题组配置
            if (Objects.equals(0, dbExam.getSchemaMode())) {
                //按题组组卷
                getSchemaType0(dbExam, libIdByExamId, examSchemaDetailList, dto1, dto2, dto3);
            } else if (Objects.equals(3, dbExam.getSchemaMode())) {
                // 知识点组卷
                getSchemaType3(dbExam, examSchemaDetailList, dto1, dto2, dto3);
            } else {
                //按难易度组卷
                getSchemaType1(examSchemaDetailList, dto1, dto2, dto3, dto4, dto5, dto6);
            }

            saveExamDTO.setExamSchemaDetail(examSchemaDetailList);
        }
    }

    /**
     * 按题组组卷
     *
     * @param dbExam
     * @param libIdByExamId
     * @param examSchemaDetailList
     * @param dto1
     * @param dto2
     * @param dto3
     * @return
     */
    private void getSchemaType0(Exam dbExam, List<String> libIdByExamId,
        List<ExamSchemaDetailMatrixDTO> examSchemaDetailList, ExamSchemaDetailMatrixDTO dto1,
        ExamSchemaDetailMatrixDTO dto2, ExamSchemaDetailMatrixDTO dto3) {
        examSchemaDetailList.add(dto1);
        examSchemaDetailList.add(dto2);
        examSchemaDetailList.add(dto3);

        List<LibConfigs> libConfigs = libConfigsService.list(
            new LambdaQueryWrapper<LibConfigs>().eq(LibConfigs::getExamId, dbExam.getId()));
        if (CollectionUtils.isEmpty(libConfigs)) {
            return;
        }
        // 把libConfigs按选择的题组id顺序排序
        libConfigs.sort(Comparator.comparing(o -> libIdByExamId.get(libConfigs.indexOf(o))));
        for (LibConfigs vo : libConfigs) {
            Library library = libraryService.getById(vo.getLibId());
            ExamSchemaDetailMatrixDTO dto = new ExamSchemaDetailMatrixDTO(vo.getLibId());
            dto.setLibraryName(library == null ? vo.getLibId() : library.getLibraryName());
            dto.setSingle(new BigDecimal(vo.getSingle() == null ? 0 : vo.getSingle()));
            dto.setMulti(new BigDecimal(vo.getMulti() == null ? 0 : vo.getMulti()));
            dto.setJudge(new BigDecimal(vo.getJudge() == null ? 0 : vo.getJudge()));
            dto.setBlanks(new BigDecimal(vo.getBlanks() == null ? 0 : vo.getBlanks()));
            dto.setQa(new BigDecimal(vo.getQa() == null ? 0 : vo.getQa()));
            examSchemaDetailList.add(dto);
        }


    }

    private void getSchemaType3(Exam dbExam, List<ExamSchemaDetailMatrixDTO> examSchemaDetailList,
        ExamSchemaDetailMatrixDTO dto1, ExamSchemaDetailMatrixDTO dto2, ExamSchemaDetailMatrixDTO dto3) {
        examSchemaDetailList.add(dto1);
        examSchemaDetailList.add(dto2);
        examSchemaDetailList.add(dto3);

        List<LibConfigs> libConfigs = libConfigsService.list(
            new LambdaQueryWrapper<LibConfigs>().eq(LibConfigs::getExamId, dbExam.getId())
                .orderByDesc(LibConfigs::getLibId).orderByDesc(LibConfigs::getId));
        if (CollectionUtils.isEmpty(libConfigs)) {
            return;
        }
        for (LibConfigs vo : libConfigs) {
            ExamSchemaDetailMatrixDTO dto = new ExamSchemaDetailMatrixDTO(vo.getLibId());
            dto.setLibraryName(vo.getLibId());
            dto.setSingle(new BigDecimal(vo.getSingle() == null ? 0 : vo.getSingle()));
            dto.setMulti(new BigDecimal(vo.getMulti() == null ? 0 : vo.getMulti()));
            dto.setJudge(new BigDecimal(vo.getJudge() == null ? 0 : vo.getJudge()));
            dto.setBlanks(new BigDecimal(vo.getBlanks() == null ? 0 : vo.getBlanks()));
            dto.setQa(new BigDecimal(vo.getQa() == null ? 0 : vo.getQa()));
            examSchemaDetailList.add(dto);
        }
    }

    /**
     * 按难易度组卷
     *
     * @param examSchemaDetailList
     * @param dto1
     * @param dto2
     * @param dto3
     * @param dto4
     * @param dto5
     * @param dto6
     * @return
     */
    private void getSchemaType1(List<ExamSchemaDetailMatrixDTO> examSchemaDetailList, ExamSchemaDetailMatrixDTO dto1,
        ExamSchemaDetailMatrixDTO dto2,
        ExamSchemaDetailMatrixDTO dto3, ExamSchemaDetailMatrixDTO dto4, ExamSchemaDetailMatrixDTO dto5,
        ExamSchemaDetailMatrixDTO dto6) {
        examSchemaDetailList.add(dto1);
        examSchemaDetailList.add(dto2);
        examSchemaDetailList.add(dto3);
        examSchemaDetailList.add(dto4);
        examSchemaDetailList.add(dto5);
        examSchemaDetailList.add(dto6);
    }

    @Override
    public void setExamShemaDetailDTO(ExamSchemaDetailMatrixDTO dto1, ExamSchemaDetailMatrixDTO dto2,
        ExamSchemaDetailMatrixDTO dto3, ExamSchemaDetailMatrixDTO dto4, ExamSchemaDetailMatrixDTO dto5,
        ExamSchemaDetailMatrixDTO dto6, SchemaDetail schemaDetail) {
        if ((ExamQuestionTypeEnum.QUESTION_TYPE_RADIO.getType() + "").equals(schemaDetail.getQuestionType())) {
            //处理单选
            dealSingle(dto1, dto2, dto3, dto4, dto5, dto6, schemaDetail);
        } else if ((ExamQuestionTypeEnum.QUESTION_TYPE_MULTI_SELECT.getType() + "").equals(
            schemaDetail.getQuestionType())) {
            //处理多选
            dealMulti(dto1, dto2, dto3, dto4, dto5, dto6, schemaDetail);
        } else if ((ExamQuestionTypeEnum.QUESTION_TYPE_JUDGEMENT.getType() + "").equals(
            schemaDetail.getQuestionType())) {
            //处理判断
            dealJudge(dto1, dto2, dto3, dto4, dto5, dto6, schemaDetail);
        } else if ((ExamQuestionTypeEnum.QUESTION_TYPE_CLOZE.getType() + "").equals(schemaDetail.getQuestionType())) {
            //处理填空
            dealBlanks(dto1, dto2, dto3, dto4, dto5, dto6, schemaDetail);
        } else if ((ExamQuestionTypeEnum.QUESTION_TYPE_QA.getType() + "").equals(schemaDetail.getQuestionType())) {
            //处理问答
            dealQa(dto1, dto2, dto3, dto4, dto5, dto6, schemaDetail);
        }
    }

    /**
     * 处理单选
     *
     * @param dto1
     * @param dto2
     * @param dto3
     * @param dto4
     * @param dto5
     * @param dto6
     * @param schemaDetail
     * @return
     */
    private void dealSingle(ExamSchemaDetailMatrixDTO dto1, ExamSchemaDetailMatrixDTO dto2,
        ExamSchemaDetailMatrixDTO dto3, ExamSchemaDetailMatrixDTO dto4, ExamSchemaDetailMatrixDTO dto5,
        ExamSchemaDetailMatrixDTO dto6, SchemaDetail schemaDetail) {
        dto1.setSingle(new BigDecimal(schemaDetail.getQuestionNum() == null ? 0 : schemaDetail.getQuestionNum()));
        dto2.setSingle(
            schemaDetail.getQuestionScore() == null ? new BigDecimal(0) : schemaDetail.getQuestionScore());
        dto3.setSingle(new BigDecimal(schemaDetail.getSortNo() == null ? 0 : schemaDetail.getSortNo()));
        dto4.setSingle(new BigDecimal(schemaDetail.getHighLevelNum() == null ? 0 : schemaDetail.getHighLevelNum()));
        dto5.setSingle(
            new BigDecimal(schemaDetail.getMediumLevelNum() == null ? 0 : schemaDetail.getMediumLevelNum()));
        dto6.setSingle(new BigDecimal(schemaDetail.getLowLevelNum() == null ? 0 : schemaDetail.getLowLevelNum()));
    }

    /**
     * 处理多选
     *
     * @param dto1
     * @param dto2
     * @param dto3
     * @param dto4
     * @param dto5
     * @param dto6
     * @param schemaDetail
     * @return
     */
    private void dealMulti(ExamSchemaDetailMatrixDTO dto1, ExamSchemaDetailMatrixDTO dto2,
        ExamSchemaDetailMatrixDTO dto3, ExamSchemaDetailMatrixDTO dto4, ExamSchemaDetailMatrixDTO dto5,
        ExamSchemaDetailMatrixDTO dto6, SchemaDetail schemaDetail) {

        dto1.setMulti(new BigDecimal(schemaDetail.getQuestionNum() == null ? 0 : schemaDetail.getQuestionNum()));
        dto2.setMulti(
            schemaDetail.getQuestionScore() == null ? new BigDecimal(0) : schemaDetail.getQuestionScore());
        dto3.setMulti(new BigDecimal(schemaDetail.getSortNo() == null ? 0 : schemaDetail.getSortNo()));
        dto4.setMulti(new BigDecimal(schemaDetail.getHighLevelNum() == null ? 0 : schemaDetail.getHighLevelNum()));
        dto5.setMulti(
            new BigDecimal(schemaDetail.getMediumLevelNum() == null ? 0 : schemaDetail.getMediumLevelNum()));
        dto6.setMulti(new BigDecimal(schemaDetail.getLowLevelNum() == null ? 0 : schemaDetail.getLowLevelNum()));
    }

    /**
     * 处理判断
     *
     * @param dto1
     * @param dto2
     * @param dto3
     * @param dto4
     * @param dto5
     * @param dto6
     * @param schemaDetail
     * @return
     */
    private void dealJudge(ExamSchemaDetailMatrixDTO dto1, ExamSchemaDetailMatrixDTO dto2,
        ExamSchemaDetailMatrixDTO dto3, ExamSchemaDetailMatrixDTO dto4, ExamSchemaDetailMatrixDTO dto5,
        ExamSchemaDetailMatrixDTO dto6, SchemaDetail schemaDetail) {

        dto1.setJudge(new BigDecimal(schemaDetail.getQuestionNum() == null ? 0 : schemaDetail.getQuestionNum()));
        dto2.setJudge(
            schemaDetail.getQuestionScore() == null ? new BigDecimal(0) : schemaDetail.getQuestionScore());
        dto3.setJudge(new BigDecimal(schemaDetail.getSortNo() == null ? 0 : schemaDetail.getSortNo()));
        dto4.setJudge(new BigDecimal(schemaDetail.getHighLevelNum() == null ? 0 : schemaDetail.getHighLevelNum()));
        dto5.setJudge(
            new BigDecimal(schemaDetail.getMediumLevelNum() == null ? 0 : schemaDetail.getMediumLevelNum()));
        dto6.setJudge(new BigDecimal(schemaDetail.getLowLevelNum() == null ? 0 : schemaDetail.getLowLevelNum()));
    }

    /**
     * 处理填空
     *
     * @param dto1
     * @param dto2
     * @param dto3
     * @param dto4
     * @param dto5
     * @param dto6
     * @param schemaDetail
     * @return
     */
    private void dealBlanks(ExamSchemaDetailMatrixDTO dto1, ExamSchemaDetailMatrixDTO dto2,
        ExamSchemaDetailMatrixDTO dto3, ExamSchemaDetailMatrixDTO dto4, ExamSchemaDetailMatrixDTO dto5,
        ExamSchemaDetailMatrixDTO dto6, SchemaDetail schemaDetail) {

        dto1.setBlanks(new BigDecimal(schemaDetail.getQuestionNum() == null ? 0 : schemaDetail.getQuestionNum()));
        dto2.setBlanks(
            schemaDetail.getQuestionScore() == null ? new BigDecimal(0) : schemaDetail.getQuestionScore());
        dto3.setBlanks(new BigDecimal(schemaDetail.getSortNo() == null ? 0 : schemaDetail.getSortNo()));
        dto4.setBlanks(new BigDecimal(schemaDetail.getHighLevelNum() == null ? 0 : schemaDetail.getHighLevelNum()));
        dto5.setBlanks(
            new BigDecimal(schemaDetail.getMediumLevelNum() == null ? 0 : schemaDetail.getMediumLevelNum()));
        dto6.setBlanks(new BigDecimal(schemaDetail.getLowLevelNum() == null ? 0 : schemaDetail.getLowLevelNum()));
    }

    /**
     * 处理问答
     *
     * @param dto1
     * @param dto2
     * @param dto3
     * @param dto4
     * @param dto5
     * @param dto6
     * @param schemaDetail
     * @return
     */
    private void dealQa(ExamSchemaDetailMatrixDTO dto1, ExamSchemaDetailMatrixDTO dto2,
        ExamSchemaDetailMatrixDTO dto3, ExamSchemaDetailMatrixDTO dto4, ExamSchemaDetailMatrixDTO dto5,
        ExamSchemaDetailMatrixDTO dto6, SchemaDetail schemaDetail) {

        dto1.setQa(new BigDecimal(schemaDetail.getQuestionNum() == null ? 0 : schemaDetail.getQuestionNum()));
        dto2.setQa(schemaDetail.getQuestionScore() == null ? new BigDecimal(0) : schemaDetail.getQuestionScore());
        dto3.setQa(new BigDecimal(schemaDetail.getSortNo() == null ? 0 : schemaDetail.getSortNo()));
        dto4.setQa(new BigDecimal(schemaDetail.getHighLevelNum() == null ? 0 : schemaDetail.getHighLevelNum()));
        dto5.setQa(new BigDecimal(schemaDetail.getMediumLevelNum() == null ? 0 : schemaDetail.getMediumLevelNum()));
        dto6.setQa(new BigDecimal(schemaDetail.getLowLevelNum() == null ? 0 : schemaDetail.getLowLevelNum()));
    }

    @Override
    public void run(String... args) throws Exception {
        for (Object tenantId : redisTemplate.opsForHash().keys(TenantRedisKeyConstant.DB_KEY)) {
            String tid = ((String) tenantId).replace(DynamicDataSourceConstant.DB_LOOK_UP_KEY_PREFIX, "");
            UserThreadContext.setTenantId(tid);
            initRedisExamCodeNo();
        }
        UserThreadContext.remove();
    }

    //@PostConstruct
    public void initRedisExamCodeNo() {
        QueryWrapper<Exam> query = new QueryWrapper<>();
        query.select("max(exam_no) exam_no");
        Exam exam = baseMapper.selectOne(query);
        long currNum = 1000000L;
        if (exam == null || StringUtils.isBlank(exam.getExamNo())) {
            redisTemplate.opsForValue().set(ExamRedisKeyEnum.EXAM_CODE_NUM.getKey(), currNum);
        } else {
            String oldNo = exam.getExamNo();
            Long redisNum = null;
            try {
                currNum = Long.parseLong(oldNo.substring(1));
                Object v = redisTemplate.opsForValue().get(ExamRedisKeyEnum.EXAM_CODE_NUM.getKey());
                if (v instanceof Integer) {
                    redisNum = ((Integer) v).longValue();
                } else if (v instanceof Long) {
                    redisNum = (Long) v;
                }
            } catch (NumberFormatException e) {
                log.error("发生异常", e);
                currNum = 1000000L;
            }
            if (redisNum == null || redisNum < currNum) {
                redisTemplate.opsForValue().set(ExamRedisKeyEnum.EXAM_CODE_NUM.getKey(), currNum);
            }
        }
    }

    @Override
    public String generateCode() {
        Object v = redisTemplate.opsForValue().get(ExamRedisKeyEnum.EXAM_CODE_NUM.getKey());
        if (v == null) {
            initRedisExamCodeNo();
        }
        return "E" + redisTemplate.opsForValue().increment(ExamRedisKeyEnum.EXAM_CODE_NUM.getKey(), 1);
    }

    /**
     * 后台-发布考试
     */
    @Override
    public void publish(PublishExamDTO examPublishDTO) {
        extractedPublish(examPublishDTO, true);

    }

    /**
     * 后台-发布练习
     */
    @Override
    public void publishExercise(PublishExamDTO examPublishDTO) {
        extractedPublish(examPublishDTO, false);
    }

    /**
     * 后台-发布考试
     */
    private void extractedPublish(PublishExamDTO examPublishDTO, boolean isExam) {
        // 是否发布
        boolean isPublish = examPublishDTO.getIsPublish() == PublishStatusEnum.IS_PUBLISH.getValue();
        // 查询待发布/取消发布考试信息
        List<Exam> exams = list(new LambdaQueryWrapper<Exam>().in(Exam::getId, examPublishDTO.getIds()));
        if (exams.isEmpty()) {
            throw new BusinessException(ExamErrorNoEnum.ERR_EXAM_IS_NOT_EXIST);
        }
        // 发布前进行数据检查
        checkBeforePublish(exams, isExam, isPublish);
        // 批量发布/取消发布
        exams.forEach(exam -> {
            String publishBy = examPublishDTO.getPublishBy();
            //是否自动发布以传入的为准
            Integer isAutoPublish = examPublishDTO.getIsAutoPublish();
            if (isPublish) {
                //进行发布操作
                doPush(exam, publishBy, isAutoPublish, isExam);
            } else {
                //进行取消发布操作
                doUnPush(exam, isAutoPublish, isExam);
            }
        });

        //发布后处理的动作
        doAfterPublish(exams, examPublishDTO, isExam, isPublish);
    }

    /**
     * 进行发布操作
     *
     * @param exam
     * @param publishBy
     * @param isAutoPublish
     * @param isExam
     * @return
     */
    private void doPush(Exam exam, String publishBy, Integer isAutoPublish, boolean isExam) {
        exam.setIsPublish(PublishStatusEnum.IS_PUBLISH.getValue());
        exam.setPublishTime(new Date());
        publishBy = StringUtils.isEmpty(publishBy) ? UserThreadContext.getUserId() : publishBy;
        exam.setPublishBy(publishBy);
        isAutoPublish = Objects.isNull(isAutoPublish) ? PublishStatusEnum.IS_PUBLISH.getValue() : isAutoPublish;
        exam.setIsAutoPublish(isAutoPublish);
        if (isExam) {
            examDao.publish(exam);
        } else {
            examDao.publishExercise(exam);
        }
    }

    /**
     * 进行取消发布操作
     *
     * @param exam
     * @param isAutoPublish
     * @param isExam
     * @return
     */
    private void doUnPush(Exam exam, Integer isAutoPublish, boolean isExam) {
        exam.setIsPublish(PublishStatusEnum.IS_NO_PUBLISH.getValue());
        exam.setPublishTime(null);
        exam.setPublishBy("");
        isAutoPublish = Objects.isNull(isAutoPublish) ? PublishStatusEnum.IS_NO_PUBLISH.getValue() : isAutoPublish;
        exam.setIsAutoPublish(isAutoPublish);
        if (isExam) {
            examDao.unPublish(exam);
        } else {
            examDao.unPublishExercise(exam);
        }
    }

    /**
     * 发布前进行数据校验
     *
     * @param
     * @return
     */
    private void checkBeforePublish(List<Exam> exams, boolean isExam, boolean isPublish) {
        if (!isPublish) {
            return;
        }
        // 1，需要进行一个及格分和总分的判断校验 区间0《及格分《总分,
        for (Exam exam : exams) {
            if (Integer.parseInt(exam.getExamType()) == ExamTypeEnum.EXAM.getCode() && (
                exam.getPassScore().doubleValue() <= 0 || exam.getPassScore().doubleValue() > exam.getTotalScore()
                    .doubleValue())) {
                throw new BusinessException(ExamErrorNoEnum.ERR_EXAM_PASS_AND_TOTAL_SCORE_WITH_ARGS, null,
                    exam.getExamName());
            }
            // 2，考试需要校验组卷是否存在
            if (Integer.parseInt(exam.getExamType()) == ExamTypeEnum.EXAM.getCode() && isExam
                && ExamConstant.COMPOSITION_FINISH != exam.getCompositionStatus()) {
                throw new BusinessException(ExamErrorNoEnum.ERR_EXAM_COMPOSITION_NOT_FINISH, null, exam.getExamName());
            }
        }
    }

    /**
     * 发布后处理的动作
     *
     * @param exams
     * @param examPublishDTO
     * @param isExam
     * @param isPublish
     * @return
     */
    private void doAfterPublish(List<Exam> exams, PublishExamDTO examPublishDTO, boolean isExam, boolean isPublish) {
        //发送消息
        sendPublishMsg(exams, examPublishDTO, isExam, isPublish);
    }

    private void sendPublishMsg(List<Exam> exams, PublishExamDTO examPublishDTO, boolean isExam, boolean isPublish) {
        // 发送消息
        exams.forEach(exam -> {
            // 发送同步状态到岗位发展管理消息
            ActivityStatusChangeEvent.sendMsg(Collections.singletonList(exam.getId()), null,
                examPublishDTO.getIsPublish(), null, mqProducer);
            // 发送资源修改信息
            mqProducer.sendMsg(new ResourceChangeEvent(
                isExam ? FirstInfoContentEnum.exam.name() : FirstInfoContentEnum.exercise.name(),
                Collections.singletonList(exam.getId()), GeneralJudgeEnum.NEGATIVE.getValue(),
                examPublishDTO.getIsPublish()));
            // 发送资源操作事件消息
            mqProducer.sendMsg(
                new ResourceOperateEvent(isPublish ? OperationEnum.PUBLISH : OperationEnum.PUBLISH_CANCEL,
                    isExam ? PushType.EXAM.getKey() : PushType.EXERCISE.getKey(), exam.getId()));
            // 发送资源同步事件消息
            if (Objects.equals(exam.getIsTrain(), IsTrainEnum.ITSELF.getValue())) {
                mqProducer.sendMsg(new ResourceSyncEvent(new ResourceSyncDTO(OperationEnum.UPDATE,
                    isExam ? ResourceTypeEnum.EXAM.name() : ResourceTypeEnum.EXERCISE.name(), exam.getId(),
                    exam.getExamName(), exam.getStartTime(),
                    exam.getEndTime(), exam.getIsAvailable(), examPublishDTO.getIsPublish(), null, null, null,
                    UserThreadContext.getUserId(),
                    new Date())));
            }
        });
    }

    @Override
    public ExerciserQuestionListDTO getExerciserQuestionList(String examId, Integer isIgnoreView) {
        Date now = new Date();
        // 1 校验下发范围
        String userId = UserThreadContext.getUserId();
        if (Objects.equals(GeneralJudgeEnum.NEGATIVE.getValue(), isIgnoreView)) {
            verifyExamViewLimit(examId, true);
        }

        // 2 校验当前练习是否存在且已发布
        Exam exam = baseMapper.selectById(examId);
        if (null == exam || Integer.parseInt(exam.getExamType()) != (ExamTypeEnum.EXERCISE.getCode())) {
            throw new BusinessException(ExamErrorNoEnum.ERR_EXERCISE_IS_NOT_EXIST);
        }
        if (null == exam.getIsPublish() || exam.getIsPublish() != ExamConstant.EXAM_IS_PUBLISH) {
            throw new BusinessException(ExamErrorNoEnum.ERR_EXERCISE_IS_NOT_PUBLISH);
        }

        // 3 查询考试对应的题目列表
        List<ExamQuestion> examQuestionList = examQuestionService.getByExamId(examId);

        // 4 需要添加练习记录
        ExerciseRecord exerciseRecord = exerciseRecordService.lambdaQuery().eq(ExerciseRecord::getExerciseId, examId)
            .eq(ExerciseRecord::getUserId, userId).one();
        if (exerciseRecord == null) {
            exerciseRecord = new ExerciseRecord().setExerciseId(examId).setId(newId()).setUserId(userId)
                .setIsFinish(ExamConstant.EXERCISE_IS_FINISH).setFinishTime(now);
            exerciseRecordService.save(exerciseRecord);
            // 练习结束需要发送消息
            mqProducer.sendMsg(new ExerciseFinishEvent(examId, userId, userId));
            // 发送资源记录同步事件消息
            if (Objects.equals(exam.getIsTrain(), IsTrainEnum.ITSELF.getValue())) {
                mqProducer.send(new ResourceRecordSyncEvent(
                    new ResourceRecordSyncDTO(OperationEnum.CREATE, ResourceTypeEnum.EXERCISE.name(),
                        exerciseRecord.getId(), examId, userId, GeneralJudgeEnum.CONFIRM.getValue(), userId, now,
                        userId, now)));
            }
        }
        // 触发练习结束激励规则
        mqProducer.sendMsg(new ExcitationMQEvent(
            new ExcitationMQEventDTO(userId, ExcitationEventEnum.commitExercise.name(), examId,
                ExcitationEventCategoryEnum.EXERCISE.getCode()).setTargetName(exam.getExamName())));
        // 5 填充返回结果
        List<ExerciserQuestionDTO> exerciserQuestionDTOList = getExerciserQuestionDTOS(examQuestionList);
        return ExerciserQuestionListDTO.builder().questionList(exerciserQuestionDTOList)
            .questionCount(examQuestionList.size()).degreeDifficult(exam.getDegreeDifficult()).name(exam.getExamName())
            .description(exam.getDescription()).id(examId).build();
    }

    @Override
    public PageInfo<MyExamListDTO> myExamList(MyExamListQueryDTO myExamListQueryDTO) {
        // 需要用到的参数
        String userId = UserThreadContext.getUserId();
        Integer pageSize = myExamListQueryDTO.getPageSize();
        Integer pageNum = myExamListQueryDTO.getPageNo();
        Integer completed = myExamListQueryDTO.getCompleted();
        boolean count = myExamListQueryDTO.isCount();
        PageInfo<MyExamListDTO> resultPageInfo = new PageInfo<>();
        resultPageInfo.setList(new ArrayList<>());

        myExamListQueryDTO.setUserId(UserThreadContext.getUserId());
        PageInfo<Exam> pageInfo = PageMethod.startPage(pageNum, pageSize, count)
            .doSelectPageInfo(() -> baseMapper.myExamList(myExamListQueryDTO));

        Set<String> ids = pageInfo.getList().stream().map(Exam::getId).collect(Collectors.toSet());

        // 考试最新记录
        Map<String, AnswerRecord> lastAnswerRecordMap = answerRecordService.getLastAnswerRecordMap(ids, userId);
        // 考试已考次数
        Map<String, Integer> examOverCountMap = answerRecordService.getExamOverCountMap(ids, userId);
        // 考试题目数
        Map<String, Integer> examQuestionCount = getExamQuestionCountMap(ids);

        Map<String, Integer> canViewMap = getCanViewMap(pageInfo.getList(), lastAnswerRecordMap);

        // 处理结果
        List<MyExamListDTO> result = Lists.newArrayList();
        String paraValue = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_703.getCode());
        // 4 填充返回结果、其他字段补充查询
        for (Exam exam : pageInfo.getList()) {
            MyExamListDTO myExamListDTO = MyExamListDTO.builder().build();
            BeanUtils.copyProperties(exam, myExamListDTO);
            myExamListDTO.setCompleted(completed);
            myExamListDTO.setExpireDate(exam.getEndTime());
            if ("0".equals(paraValue)) {
                myExamListDTO.setIsFaceRecognition(0);
            }
            // 设备类型
            myExamListDTO.setEquipment(EquipmentTypeEnum.getNameByCode(exam.getEquipment()));
            // 设置最新考试记录相关的信息(顺序不能变，有数据依赖)
            setMyLastAnswerRecordInfo(lastAnswerRecordMap, exam, myExamListDTO);

            myExamListDTO.setCompleteCount(0);
            // 考试的题目总数
            myExamListDTO.setQuestionCount(
                examQuestionCount.get(exam.getId()) == null ? 0 : examQuestionCount.get(exam.getId()));
            // 可考试次数
            myExamListDTO.setAnswerCount(exam.getReExamCount() + 1);
            // 当前考了几次
            myExamListDTO.setAnsweredCount(
                examOverCountMap.get(exam.getId()) == null ? 0 : examOverCountMap.get(exam.getId()));

            // 设置是否能够重考(顺序不能变，有数据依赖)
            setMyLastAnswerRecordIsReExam(exam, myExamListDTO);

            // 是否防切屏
            myExamListDTO.setBCutScreen(exam.getCutScreen());
            int canView = canViewMap.get(exam.getId());
            myExamListDTO.setViewAnswer(canView);
            myExamListDTO.setDuration(exam.getExamTimeCount());
            result.add(myExamListDTO);
        }

        // 5分页
        BeanUtils.copyProperties(pageInfo, resultPageInfo);
        resultPageInfo.setList(result);
        resultPageInfo.setTotal(pageInfo.getTotal());
        resultPageInfo.setPageSize(pageSize);
        resultPageInfo.setPageNum(pageNum);
        resultPageInfo.setIsLastPage(result.size() < pageSize);
        return resultPageInfo;
    }

    /**
     * 设置最新考试记录相关的信息
     *
     * @param lastAnswerRecordMap
     * @param exam
     * @param myExamListDTO
     * @return
     */
    private void setMyLastAnswerRecordInfo(Map<String, AnswerRecord> lastAnswerRecordMap, Exam exam,
        MyExamListDTO myExamListDTO) {
        // 获取考试最新记录
        AnswerRecord myLastAnswerRecord = lastAnswerRecordMap.get(exam.getId());
        // 该考试用户得分、交卷时间
        // null: 还没开始考试  交卷、未交卷
        // 当未开始考试或未交卷
        if (Objects.isNull(myLastAnswerRecord) || Objects.equals(myLastAnswerRecord.getIsPost(),
            ExamConstant.EXAM_IS_NOT_POST)) {
            myExamListDTO.setMyScore(new BigDecimal("0.0"));
            myExamListDTO.setCompletedTime(null);
        } else {
            // 考了试且交卷
            myExamListDTO.setCompletedTime(myLastAnswerRecord.getAnswerTime());
            //根据考试记录调整考试及格分
            myExamListDTO.setPassScore(myLastAnswerRecord.getPassScore());
            // 已改卷
            if (Objects.equals(myLastAnswerRecord.getIsCheckFinish(), ExamConstant.EXAM_IS_CHECK_FINISH)) {
                myExamListDTO.setMyScore(
                    new BigDecimal(String.valueOf(myLastAnswerRecord.getUserScore().doubleValue())));
            } else {
                // 阅卷中
                myExamListDTO.setMyScore(new BigDecimal("-1.0"));
            }
        }
    }

    /**
     * 设置是否能够重考
     *
     * @param exam
     * @param myExamListDTO
     * @return
     */
    private void setMyLastAnswerRecordIsReExam(Exam exam, MyExamListDTO myExamListDTO) {
        // 考试是否过期
        boolean isExpire = false;
        if (exam.getStartTime().getTime() > System.currentTimeMillis()
            || exam.getEndTime().getTime() < System.currentTimeMillis()) {
            myExamListDTO.setEnableExam(ExamConstant.EXAM_DISABLE_ANSWER);
            isExpire = true;
        }
        // 考试未过期时，是否可考试
        if (!isExpire) {
            if (Objects.equals(exam.getIsReExam(), 1)) {
                // 及格后可以重考
                myExamListDTO.setEnableExam(
                    myExamListDTO.getAnswerCount() > myExamListDTO.getAnsweredCount()
                        ? ExamConstant.EXAM_ENABLE_ANSWER
                        : ExamConstant.EXAM_DISABLE_ANSWER);
            } else {
                // 及格后不可以重考
                myExamListDTO.setEnableExam(myExamListDTO.getAnswerCount() > myExamListDTO.getAnsweredCount()
                    && myExamListDTO.getMyScore().doubleValue() < exam.getPassScore().doubleValue()
                    ? ExamConstant.EXAM_ENABLE_ANSWER : ExamConstant.EXAM_DISABLE_ANSWER);
            }
        }
    }

    private Map<String, Integer> getCanViewMap(List<Exam> examList, Map<String, AnswerRecord> recordMap) {
        Map<String, Integer> res = new HashMap<>();

        for (Exam exam : examList) {
            int canView = 1;
            if (exam.getIsViewAnswer().equals(ExamConstant.EXAM_IS_VIEW_TYPE_NO)
                || exam.getIsViewAnswer().equals(ExamConstant.CAN_VIEW_ANSWER_AFTER_ANSWERING)) {
                canView = 0;
            }

            if (exam.getIsViewAnswer() == ExamConstant.EXAM_IS_VIEW_TYPE_SUBMIT) {
                AnswerRecord answerRecord = recordMap.get(exam.getId());
                if (null == answerRecord) {
                    canView = 0;
                    res.put(exam.getId(), canView);
                    continue;
                } else if (answerRecord.getIsPost() == null || answerRecord.getIsPost() != ExamConstant.EXAM_IS_POST) {
                    canView = 0;
                }

            }
            canView = getIsViewForExamIsViewTypeEnd(exam, canView);
            res.put(exam.getId(), canView);
        }

        return res;
    }

    private static int getIsViewForExamIsViewTypeEnd(Exam exam, int canView) {
        if (exam.getIsViewAnswer() == ExamConstant.EXAM_IS_VIEW_TYPE_END) {
            // 结束时间加上考试时长为最终时间
            LocalDateTime finalTime = LocalDateTime.ofInstant(exam.getEndTime().toInstant(), ZoneId.systemDefault())
                .plusMinutes(exam.getExamTimeCount());
            // 判断考试是否结束
            if (finalTime.isAfter(LocalDateTime.now())) {
                // 考试未结束，无法查看答案和解析,只返回我的答案，不返回解析和正确答案
                canView = 0;
            }
        }
        return canView;
    }

    /**
     * 判断是否可以查看答案
     *
     * @param exam      考试
     * @param queryType 1 查询考试结果 2 查询考试信息
     * @return
     */
    private int getCanView(Exam exam, int queryType) {
        String userId = UserThreadContext.getUserId();
        int canView = 1;
        if (queryType == 2 || (exam.getIsCanViewAnswer() != null && exam.getIsCanViewAnswer()
            .equals(ExamConstant.EXAM_IS_VIEW_TYPE_NO))) {
            int isViewAnswer = exam.getIsViewAnswer();
            switch (isViewAnswer) {
                case ExamConstant.EXAM_IS_VIEW_TYPE_NO:
                case ExamConstant.CAN_VIEW_ANSWER_AFTER_ANSWERING:
                    canView = 0;
                    break;
                case ExamConstant.EXAM_IS_VIEW_TYPE_SUBMIT:
                    canView = checkTypeSubmit(exam.getId(), userId);
                    break;
                case ExamConstant.EXAM_IS_VIEW_TYPE_END:
                    canView = checkTypeEnd(exam);
                    break;
                default:
                    break;
            }
        }
        return canView;
    }

    private int checkTypeSubmit(String examId, String userId) {
        int canView = 1;
        AnswerRecord answerRecord = answerRecordService.getByUserIdAndExamId(examId, userId);
        if (null == answerRecord || answerRecord.getIsPost() == null
            || answerRecord.getIsPost() != ExamConstant.EXAM_IS_POST) {
            canView = 0;
        }
        return canView;
    }

    private int checkTypeEnd(Exam exam) {
        int canView = 1;
        // 结束时间加上考试时长为最终时间
        LocalDateTime finalTime = LocalDateTime.ofInstant(exam.getEndTime().toInstant(), ZoneId.systemDefault())
            .plusMinutes(exam.getExamTimeCount());
        // 判断考试是否结束
        if (finalTime.isAfter(LocalDateTime.now())) {
            // 考试未结束，无法查看答案和解析,只返回我的答案，不返回解析和正确答案
            canView = 0;
        }
        return canView;
    }

    @Override
    public PageInfo<ExamRankUserDTO> rank(String examId, BasePageQuery basePageQuery) {
        // 1 参数
        Integer pageNum = basePageQuery.getPageNo();
        Integer pageSize = basePageQuery.getPageSize();

        // 2 本地数据库查询出排行榜
        PageInfo<ExamRankUserDTO> rankPageInfo = PageMethod.startPage(pageNum, pageSize)
            .doSelectPageInfo(() -> baseMapper.getRank(examId));

        // 提取用户id
        List<String> userIds = rankPageInfo.getList().stream().map(ExamRankUserDTO::getUserId)
            .collect(Collectors.toList());
        List<ExamRankUserDTO> examRankUserDTOList = new ArrayList<>();

        // 3 调用用户微服务查询用户信息
        if (!CollectionUtils.isEmpty(userIds)) {
            List<UserRankBaseInfoDTO> baseInfoList = userFeign.getUserRankBaseInfo(userIds);

            // 4 将用户数据填充到返回结果
            examRankUserDTOList = rankPageInfo.getList();
            for (ExamRankUserDTO examRankUserDTO : examRankUserDTOList) {
                for (UserRankBaseInfoDTO baseInfoDTO : baseInfoList) {
                    if (examRankUserDTO.getUserId().equals(baseInfoDTO.getUserId())) {
                        examRankUserDTO.setUserName(baseInfoDTO.getUserName());
                        examRankUserDTO.setUsrOrgName(baseInfoDTO.getUserOrgName());
                        examRankUserDTO.setUserAvatar(baseInfoDTO.getUserAvatar());
                        break;
                    }
                }
            }
        }

        rankPageInfo.setList(examRankUserDTOList);
        return rankPageInfo;
    }

    @Override
    public ExamRankMyDTO myRank(String examId) {
        // 当前登录用户
        String userId = UserThreadContext.getUserId();
        // 查用户成绩最高的一次考试答题记录
        AnswerRecord maxAnswerRecord = answerRecordService.getMaxAnswerRecord(examId, userId);
        AnswerRecord lastAnswerRecord = answerRecordService.getLastAnswerRecord(examId, userId);
        // 考试排名列表返回对象
        ExamRankMyDTO examRankMyDTO = new ExamRankMyDTO();
        examRankMyDTO.setFullName(userFeign.getUserFullNameById(userId));
        examRankMyDTO.setUserAvatars(fileFeign.getImageUrl(userId, ImageBizType.Avatar.name()));
        // 已参加考试
        if (maxAnswerRecord != null) {
            examRankMyDTO.setPassScore(maxAnswerRecord.getPassScore());

            examRankMyDTO.setCheckFinish(maxAnswerRecord.getIsCheckFinish());
            examRankMyDTO.setCompleted(maxAnswerRecord.getIsPost());

            // 学员端-我的考试排名信息
            if (maxAnswerRecord.getIsPost().equals(ExamConstant.EXAM_IS_POST) && maxAnswerRecord.getIsCheckFinish()
                .equals(ExamConstant.EXAM_IS_CHECK_FINISH)) {
                examRankMyDTO.setUserScore(maxAnswerRecord.getUserScore());
                examRankMyDTO.setPassStatus(
                    examRankMyDTO.getUserScore().compareTo(examRankMyDTO.getPassScore()) >= 0
                        ? ExamConstant.EXAM_IS_PASS
                        : ExamConstant.EXAM_IS_NOT_PASS);
                examRankMyDTO.setUserRank(baseMapper.getMyRank(userId, examId));
            }
        } else if (lastAnswerRecord != null) {
            examRankMyDTO.setCheckFinish(lastAnswerRecord.getIsCheckFinish());
            examRankMyDTO.setCompleted(lastAnswerRecord.getIsPost());
        }
        return examRankMyDTO;
    }

    @Override
    public ExamInfoDTO getExamInfo(String examId, Integer isIgnoreView) {
        // 变量
        String userId = UserThreadContext.getUserId();

        // 1 校验下发范围
        if (Objects.equals(GeneralJudgeEnum.NEGATIVE.getValue(), isIgnoreView)) {
            verifyExamViewLimit(examId, false);
        }

        // 2 查该考试的基本信息
        Exam exam = baseMapper.selectOne(new LambdaQueryWrapper<Exam>().eq(Exam::getId, examId)
            .eq(Exam::getExamType, String.valueOf(ExamTypeEnum.EXAM.getCode()))
            .eq(Exam::getIsPublish, ExamConstant.EXAM_IS_PUBLISH));

        if (null == exam) {
            throw new BusinessException(ExamErrorNoEnum.ERR_EXAM_IS_NOT_EXIST);
        }

        // 3 查该试卷交卷人数
        int completeCount = getCompleteCountByExamId(examId);

        // 4 查当前登录用户该考试最后一次提交的得分
        BigDecimal myScore = new BigDecimal("0.0");
        // 5 用户是否通过考试
        int passed = 0;
        // 6 用户是否交卷
        int isPost = 0;
        // 7 我的交卷时间
        Date myAnswerTime = null;
        // 阅卷状态
        int checkStatus = 0;
        AnswerRecord myLastAnswerRecord = answerRecordService.getLastAnswerRecord(examId, userId);
        if (null != myLastAnswerRecord && myLastAnswerRecord.getIsPost() == ExamConstant.EXAM_IS_POST) {
            if (Objects.equals(myLastAnswerRecord.getIsCheckFinish(), ExamConstant.EXAM_IS_CHECK_FINISH)) {
                myScore = new BigDecimal(String.valueOf(myLastAnswerRecord.getUserScore().doubleValue()));
            } else {
                myScore = new BigDecimal("-1.0");
            }
            passed =
                myLastAnswerRecord.getPassScore().doubleValue() <= myScore.doubleValue() ? ExamConstant.EXAM_IS_PASS
                    : ExamConstant.EXAM_IS_NOT_PASS;
            isPost = myLastAnswerRecord.getIsPost();
            myAnswerTime = myLastAnswerRecord.getAnswerTime();
            checkStatus = myLastAnswerRecord.getCheckStatus();
        }

        // 8 查当前考试题目总数
        int questionCount = getExamQuestionCount(examId);

        // 9 查用户已考试次数
        int examOverCount = answerRecordService.getExamOverCount(examId, userId);

        // 10 当前用户是否可考试
        int enableAnswer = ((examOverCount < exam.getReExamCount() + 1) && exam.getEndTime().after(new Date())) ? 1 : 0;
        String paraValue = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_703.getCode());
        if ("0".equals(paraValue)) {
            exam.setIsFaceRecognition(0);
        }

        // 11 查用户已交卷次数+补考记录数
        int examPostNum = answerRecordService.getUserExamPostNumAndReExamNumSum(examId, userId);

        // 12 填充返回结果
        ExamInfoDTO examInfoDTO = ExamInfoDTO.builder().build();
        BeanUtils.copyProperties(exam, examInfoDTO);
        examInfoDTO.setDuration(exam.getExamTimeCount());
        examInfoDTO.setExpireDate(exam.getEndTime());
        examInfoDTO.setQuestionCount(questionCount);
        examInfoDTO.setMyScore(myScore);
        examInfoDTO.setCompleted(isPost);
        if (null != myAnswerTime) {
            examInfoDTO.setCompletedTime(myAnswerTime);
        }
        examInfoDTO.setPassed(passed);
        examInfoDTO.setStartTime(exam.getStartTime());
        examInfoDTO.setEndTime(exam.getEndTime());
        examInfoDTO.setCompleteCount(completeCount);
        examInfoDTO.setMyExamOverCount(examOverCount);
        examInfoDTO.setMyExamPostNum(examPostNum);
        examInfoDTO.setIsReExam(exam.getIsReExam());
        examInfoDTO.setTotalExamCount(exam.getReExamCount() + 1);

        int canView = getCanView(exam, 2);
        examInfoDTO.setViewAnswer(canView);
        examInfoDTO.setEnableExam(enableAnswer);
        examInfoDTO.setEquipment(EquipmentTypeEnum.getNameByCode(exam.getEquipment()));
        examInfoDTO.setPublishTime(exam.getPublishTime());
        examInfoDTO.setIsCanViewAnswer(exam.getIsCanViewAnswer());
        examInfoDTO.setCategoryName("TODO");
        examInfoDTO.setCheckStatus(checkStatus);
        return examInfoDTO;
    }

    @Override
    public ExerciserInfoDTO getExerciserInfo(String examId, Integer isIgnoreView) {
        // 1 校验下发范围
        if (Objects.equals(GeneralJudgeEnum.NEGATIVE.getValue(), isIgnoreView)) {
            verifyExamViewLimit(examId, true);
        }
        // 2 查考试
        Exam exerciser = baseMapper.selectOne(new LambdaQueryWrapper<Exam>().eq(Exam::getId, examId)
            .eq(Exam::getExamType, String.valueOf(ExamTypeEnum.EXERCISE.getCode()))
            .eq(Exam::getIsPublish, ExamConstant.EXAM_IS_PUBLISH));
        if (null == exerciser) {
            throw new BusinessException(ExamErrorNoEnum.ERR_EXERCISE_IS_NOT_EXIST);
        }
        // 3 查考试题目数
        int exerciserQuestionCount = getExamQuestionCount(examId);
        // 4 填充返回结果
        return ExerciserInfoDTO.builder().exerciserId(exerciser.getId()).exerciserName(exerciser.getExamName())
            .questionCount(exerciserQuestionCount).exerciserDesc(exerciser.getDescription())
            .exerciserCategory("TODO").exerciserPublicTime(exerciser.getPublishTime())
            .degreeDifficult(exerciser.getDegreeDifficult()).build();
    }

    @SuppressWarnings("squid:S3776")
    @Override
    public ExamQuestionListDTO getExamQuestionList(String examId, String version, Integer isIgnoreView) {
        // 参数
        String userId = UserThreadContext.getUserId();
        String redisLockKey = "getExamQuestionList:" + examId + ":" + userId;
        Exam exam = new Exam();
        int reExam = ExamConstant.EXAM_IS_PASS;
        String newVersion = null;
        long remainTime = 0L;
        // 考试题目
        List<ExamQuestionDTO> examQuestionDTOList = new ArrayList<>();
        // 组卷方案类型
        String examOrderType = null;
        //组卷id
        String examCompositionId = null;
        boolean isViewAnswer3 = (getById(examId).getIsViewAnswer() == 3);
        try {
            RedisLockUtil.acquire(redisLockKey, 60);
            Date date = new Date();
            AnswerRecord lastAnswerRecord = answerRecordService.getLastAnswerRecordRedis(examId, userId);
            //redis中数据被删了，说明是已经进行过交卷了，从数据库查询用于在下面做是否能够重考的校验
            if (null == lastAnswerRecord) {
                lastAnswerRecord = answerRecordService.getLastAnswerRecord(examId, userId);
            }

            // 1 获取考试，校验是否可以开始考试
            exam = valid2Exam(examId, userId, lastAnswerRecord, isIgnoreView);

            if (StringUtils.isNotBlank(exam.getSchemaId())) {
                Schema schema = schemaService.getById(exam.getSchemaId());
                examOrderType = null != schema ? schema.getCategoryType() : null;
            }
            // 剩余考试时长
            remainTime = exam.getExamTimeCount() * Duration.ofMinutes(1).getSeconds();

            //0 = 不能继续考试  1= 能继续考试
            // 如果重考次数为0 则不能继续考试
            if (exam.getIsReExam() == 0 && (null != lastAnswerRecord
                && lastAnswerRecord.getIsPost() == ExamConstant.EXAM_IS_POST
                && lastAnswerRecord.getUserScore().doubleValue() >= exam.getPassScore().doubleValue())) {
                //设置了考试通过后可以重考
                //如果未及格，有考试次数则都可以重考
                reExam = ExamConstant.EXAM_IS_NOT_PASS;
            }

            // 判断考试时间
            if (null != lastAnswerRecord) {
                examCompositionId = lastAnswerRecord.getExamCompositionId();
            }
            OnGoingExamDTO onGoingExamDTO = getOnGoingExamDTO(examId, userId);
            if (null != onGoingExamDTO) {
                if (!Objects.equals(examCompositionId, onGoingExamDTO.getExamCompositionId())) {
                    log.error(
                        "正在作答的examCompositionId:{},与最新作答的examCompositionId:{},数据不一致,lastAnswerRecord:{}",
                        onGoingExamDTO.getExamCompositionId(), examCompositionId, lastAnswerRecord);
                }

                long enterTime = onGoingExamDTO.getEnterTime().getTime();
                newVersion = String.valueOf(enterTime);
                long nowTime = System.currentTimeMillis();
                // 考试开始到现在多久了
                long castTime = (nowTime - enterTime) / 1000;
                // 原来的题目、考试剩余时长
                examQuestionDTOList = onGoingExamDTO.getQuestionList();

                setUserAnswerByRedis(examQuestionDTOList, lastAnswerRecord);

                remainTime = exam.getExamTimeCount() * Duration.ofMinutes(1).getSeconds() - castTime;
                if (remainTime < 0) {
                    remainTime = 0;
                }
                if (StringUtils.isNotEmpty(version) && version.equals(String.valueOf(enterTime))) {
                    if (isViewAnswer3) {
                        for (ExamQuestionDTO examQuestionDTO : examQuestionDTOList) {
                            String answer = examQuestionDTO.getAnswer();
                            examQuestionDTO.setQuestionAnswer(answer);

                            if (ExamQuestionTypeEnum.QUESTION_TYPE_CLOZE.getType()
                                == examQuestionDTO.getQuestionType()) {
                                examQuestionDTO.setClozeAnswerList(
                                    Arrays.asList(examQuestionDTO.getQuestionAnswer()
                                        .split(ExamConstant.CLOZE_ANSWER_DELIMITER)));
                            }
                            if (ExamQuestionCheckUtil.isComposeQuestion(examQuestionDTO.getQuestionType())) {
                                List<ExamQuestionDTO> subQuestionList = examQuestionService.getQuestionDTOByParentId(
                                    examQuestionDTO.getQuestionId());
                                for (ExamQuestionDTO examQuestionDTO1 : subQuestionList) {
                                    examQuestionDTO1.setQuestionAnswer(examQuestionDTO1.getAnswer());
                                    if (ExamQuestionTypeEnum.QUESTION_TYPE_CLOZE.getType()
                                        == examQuestionDTO1.getQuestionType()) {
                                        examQuestionDTO.setClozeAnswerList(
                                            Arrays.asList(examQuestionDTO.getQuestionAnswer()
                                                .split(ExamConstant.CLOZE_ANSWER_DELIMITER)));
                                    }
                                }
                                examQuestionDTO.setSubQuestionList(subQuestionList);
                            }
                        }
                    }

                    for (ExamQuestionDTO examQuestionDTO : examQuestionDTOList) {
                        examQuestionDTO.setAnswer(null);
                        if (!CollectionUtils.isEmpty(examQuestionDTO.getSubQuestionList())) {
                            for (ExamQuestionDTO questionDTO : examQuestionDTO.getSubQuestionList()) {
                                questionDTO.setAnswer(null);
                            }
                        }

                        // 兼容Redis历史旧数据，去除选项中的编号显示
                        if (!CollectionUtils.isEmpty(examQuestionDTO.getOptionList())) {
                            for (ExamQuestionOptionDTO examQuestionOptionDTO : examQuestionDTO.getOptionList()) {
                                examQuestionOptionDTO.setRightAnswer(null);
                                if (examQuestionOptionDTO.getOptionDesc() == null
                                    || examQuestionOptionDTO.getOptionDesc().length() <= 2) {
                                    continue;
                                }
                                // 如果第二个字符是.（例如选项格式为A. 选项内容）
                                if (Objects.equals(examQuestionOptionDTO.getOptionDesc().charAt(1), '.')) {
                                    // 代码会从第三个字符（索引为 2）开始截取字符串，从而去除选项标识（如A.），只保留实际的选项内容。
                                    examQuestionOptionDTO.setOptionDesc(
                                        examQuestionOptionDTO.getOptionDesc().substring(2));
                                }
                            }
                        }
                    }

                    return ExamQuestionListDTO.builder().examCompositionId(examCompositionId).reExam(reExam)
                        .disOrder(exam.getIsQuestionSeq())
                        .examNo(exam.getExamNo()).id(examId).examOrderType(examOrderType)
                        .isViewAnswer(isViewAnswer3 ? 1 : 0)
                        .questionList(examQuestionDTOList)
                        .questionCount(examQuestionDTOList.size()).remainTime(remainTime).version(version).build();
                }
            } else {
                // 2 从数据库拿考试组卷
                newVersion = String.valueOf(date.getTime());
                ExamCompositionDTO examCompositionDTO = this.getRandomComposition(examId);
                log.info("get new examComposition userId:{},id:{},examId:{},questionCount:{},create_time:{}"
                    , UserThreadContext.getUserId(), examCompositionDTO.getId(), examCompositionDTO.getExamId(),
                    examCompositionDTO.getQuestionCount(), examCompositionDTO.getCreateTime());
                examQuestionDTOList = examCompositionDTO.getExamQuestionList();
                examCompositionId = examCompositionDTO.getId();

                // 将用户本次考试的数据缓存到redis
                OnGoingExamDTO redisQuestion = OnGoingExamDTO.builder().examCompositionId(examCompositionId)
                    .questionList(examQuestionDTOList).enterTime(date)
                    .examId(examId).build();
                redisTemplate.opsForHash().put(ExamRedisKeyEnum.ON_GOING_EXAM.getKey() + userId, examId, redisQuestion);

                //作答记录存入redis
                String newId = newId();
                AnswerRecord answerRecord = AnswerRecord.builder().id(newId).examId(examId).userId(userId)
                    .totalScore(exam.getTotalScore())
                    .passScore(exam.getPassScore()).userScore(new BigDecimal("-1.0"))
                    .questionCount(examCompositionDTO.getQuestionCount()).startTime(date)
                    .isPost(ExamConstant.EXAM_IS_NOT_POST)
                    .validStatus(AnswerRecordValidStatusEnum.NEWEST.getValue())
                    .checkPaperMethod(exam.getCheckPaperMethod())
                    .examCompositionId(examCompositionId)
                    .checkStatus(ExamCheckStatusEnum.NOT_CHECK.getCode())
                    .build();
                answerRecordService.setLastAnswerRecordRedis(examId, userId, answerRecord);
                //作答记录异步保存入库,更新之前的作答记录为历史数据
                // 在一个类中就没异步呀
                ExamServiceImpl bean = SpringUtil.getBean(ExamServiceImpl.class);
                bean.asyncSaveAnswerRecord(answerRecord);

                // 发送资源记录同步事件消息
                Date now = new Date();
                if (Objects.equals(exam.getIsTrain(), IsTrainEnum.ITSELF.getValue())) {
                    mqProducer.send(new ResourceRecordSyncEvent(
                        new ResourceRecordSyncDTO(OperationEnum.CREATE, ResourceTypeEnum.EXAM.name(), newId, examId,
                            userId,
                            GeneralJudgeEnum.NEGATIVE.getValue(), userId, now, userId, now)));
                }
            }

            List<String> questionIdList = examQuestionDTOList.stream().map(ExamQuestionDTO::getQuestionId)
                .collect(Collectors.toList());
            Map<String, Integer> questionIdToDifficultyMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(questionIdList)) {
                List<ExamQuestion> examQuestions = examQuestionService.listByIds(questionIdList);
                questionIdToDifficultyMap = examQuestions.stream()
                    .filter(q -> q.getId() != null && q.getDifficulty() != null) // 过滤掉包含 null 的元素
                    .collect(Collectors.toMap(ExamQuestion::getId, ExamQuestion::getDifficulty, (a, b) -> a));
            }
            for (ExamQuestionDTO examQuestionDTO : examQuestionDTOList) {
                String answer = examQuestionDTO.getAnswer();
                if (isViewAnswer3) {
                    examQuestionDTO.setQuestionAnswer(answer);
                }
                examQuestionDTO.setAnswer(null);
                Integer difficulty = questionIdToDifficultyMap.get(examQuestionDTO.getQuestionId());
                if (difficulty != null) {
                    examQuestionDTO.setDifficulty(difficulty);
                }
                if (ExamQuestionTypeEnum.QUESTION_TYPE_CLOZE.getType() == examQuestionDTO.getQuestionType()) {
                    examQuestionDTO.setClozeAnswerNum(answer.split(ExamConstant.CLOZE_ANSWER_DELIMITER).length);
                    examQuestionDTO.setQuestionName(
                        StringUtil.removeAnswerInQuestionName(examQuestionDTO.getQuestionName()));
                    if (isViewAnswer3) {
                        examQuestionDTO.setClozeAnswerList(
                            Arrays.asList(
                                examQuestionDTO.getQuestionAnswer().split(ExamConstant.CLOZE_ANSWER_DELIMITER)));
                    }
                }
                if (!CollectionUtils.isEmpty(examQuestionDTO.getOptionList())) {
                    for (ExamQuestionOptionDTO examQuestionOptionDTO : examQuestionDTO.getOptionList()) {
                        examQuestionOptionDTO.setRightAnswer(null);
                        if (examQuestionOptionDTO.getOptionDesc() == null
                            || examQuestionOptionDTO.getOptionDesc().length() <= 2) {
                            continue;
                        }
                        if (Objects.equals(examQuestionOptionDTO.getOptionDesc().charAt(1), '.')) {
                            examQuestionOptionDTO.setOptionDesc(examQuestionOptionDTO.getOptionDesc().substring(2));
                        } else {
                            examQuestionOptionDTO.setOptionDesc(examQuestionOptionDTO.getOptionDesc().substring(1));
                        }
                    }
                }
                if (ExamQuestionCheckUtil.isComposeQuestion(examQuestionDTO.getQuestionType())) {
                    List<ExamQuestionDTO> subQuestionList = examQuestionService.getQuestionDTOByParentId(
                        examQuestionDTO.getQuestionId());
                    for (ExamQuestionDTO examQuestionDTO1 : subQuestionList) {
                        examQuestionDTO1.setQuestionAnswer(examQuestionDTO1.getAnswer());
                        if (ExamQuestionTypeEnum.QUESTION_TYPE_CLOZE.getType() == examQuestionDTO1.getQuestionType()) {
                            examQuestionDTO1.setClozeAnswerNum(
                                examQuestionDTO1.getAnswer().split(ExamConstant.CLOZE_ANSWER_DELIMITER).length);
                            examQuestionDTO1.setQuestionName(
                                StringUtil.removeAnswerInQuestionName(examQuestionDTO1.getQuestionName()));
                            if (isViewAnswer3) {
                                examQuestionDTO.setClozeAnswerList(
                                    Arrays.asList(examQuestionDTO.getQuestionAnswer()
                                        .split(ExamConstant.CLOZE_ANSWER_DELIMITER)));
                            }
                        }
                    }
                    examQuestionDTO.setSubQuestionList(subQuestionList);
                }
            }


        } catch (BusinessException e) {
            //业务异常要抛给前端
            throw e;
        } catch (Exception e) {
            log.error("getExamQuestionList exception", e);
        } finally {
            RedisLockUtil.release(redisLockKey);
        }
        if (!isViewAnswer3) {
            for (ExamQuestionDTO examQuestionDTO : examQuestionDTOList) {
                examQuestionDTO.setAnswer(null);
                if (!CollectionUtils.isEmpty(examQuestionDTO.getSubQuestionList())) {
                    for (ExamQuestionDTO questionDTO : examQuestionDTO.getSubQuestionList()) {
                        questionDTO.setAnswer(null);
                    }
                }
            }
        }
        return ExamQuestionListDTO.builder().examCompositionId(examCompositionId).questionList(examQuestionDTOList)
            .reExam(reExam).isViewAnswer(isViewAnswer3 ? 1 : 0)
            .disOrder(exam.getIsQuestionSeq()).examNo(exam.getExamNo()).id(examId).examOrderType(examOrderType)
            .questionCount(examQuestionDTOList.size()).remainTime(remainTime).version(newVersion).build();
    }

    private void setUserAnswerByRedis(List<ExamQuestionDTO> examQuestionDTOList, AnswerRecord lastAnswerRecord) {
        // 获取Redis答题记录
        if (lastAnswerRecord == null) {
            return;
        }
        ExamSubmitDTO examSubmitDTO = answerRecordService.getUserAnswerByRedis(lastAnswerRecord.getId());
        if (examSubmitDTO == null) {
            return;
        }
        Map<String, ExamSubmitAnswerDTO> examSubmitAnswerMap = examSubmitDTO.getAnswer().stream().collect(
            Collectors.toMap(ExamSubmitAnswerDTO::getExamQuestionId, dto -> dto, (key1, key2) -> key1));
        for (ExamQuestionDTO eq : examQuestionDTOList) {
            ExamSubmitAnswerDTO userQuestionAnswer = examSubmitAnswerMap.get(eq.getQuestionId());
            if (userQuestionAnswer == null) {
                continue;
            }
            if (ExamQuestionCheckUtil.isClozeOrQaType(eq.getQuestionType())) {
                // 用户主观题答案
                eq.setClozeUserAnswerList(userQuestionAnswer.getExamShortAnswer());
                continue;
            }
            if (CollectionUtils.isEmpty(userQuestionAnswer.getExamOptionAnswer())) {
                continue;
            }
            for (ExamQuestionOptionDTO option : eq.getOptionList()) {
                if (userQuestionAnswer.getExamOptionAnswer().contains(option.getOptionId())) {
                    // 用户客观题答案
                    option.setUserAnswer(1);
                }
            }
        }
    }

    /**
     * 校验用户是否可以开始考试
     *
     * @param examId       考试id
     * @param userId       用户id
     * @param answerRecord 答题记录
     * @param isIgnoreView 是否忽略可见范围：0-否 1-是
     * @return 考试对象
     */
    private Exam valid2Exam(String examId, String userId, AnswerRecord answerRecord, Integer isIgnoreView) {
        Exam exam = selectExamById(examId);
        if (null == exam) {
            throw new BusinessException(ExamErrorNoEnum.ERR_EXAM_IS_NOT_EXIST);
        }
        // 校验下发范围
        if (Objects.equals(GeneralJudgeEnum.NEGATIVE.getValue(), isIgnoreView)) {
            verifyExamViewLimit(examId, false);
        }
        if (null == exam.getStartTime() || null == exam.getEndTime()) {
            throw new BusinessException(ExamErrorNoEnum.ERR_NO_PERMISSION_TO_EXAM);
        }
        // 校验考试开始时间
        long examStartTime = exam.getStartTime().getTime();
        long now = System.currentTimeMillis();
        if (now < examStartTime) {
            throw new BusinessException(ExamErrorNoEnum.ERR_EXAM_HAS_NOT_GEGUN);
        }
        // 校验考试结束时间
        /**
         * 正常逻辑如考试10点结束，考试时长1小时，
         * 如果用户在10点之前没有进入考试，即没有正在进行中的(未交卷的)考试记录，那么不可以进入考试
         * 那么该考生9:50进入考试， 但10:10的时候，仍然可以考试，因为考试时长是1小时，该用户应该是在10:50的时候才能不可以考试
         */
        long examEndTime = exam.getEndTime().getTime();
        if (Objects.isNull(answerRecord)
            && now > examEndTime) {
            throw new BusinessException(ExamErrorNoEnum.ERR_NOT_IN_THE_EXAM_TIME_FRAME);
        }
        // 校验考试次数
        int examOverCount = answerRecordService.getExamOverCount(examId, userId);
        if (examOverCount >= exam.getReExamCount() + 1) {
            throw new BusinessException(ExamErrorNoEnum.ERR_EXAM_COUNT_HAS_RUN_OUT);
        }
        // 如果没有考试记录
        if (null == answerRecord) {
            return exam;
        }
        // 如果进入了考试，但是未交卷
        if (Objects.equals(answerRecord.getIsPost(), ExamConstant.EXAM_IS_NOT_POST)) {
            return exam;
        }
        // 如果考试过了，但是未完成改卷
        if (Objects.equals(ExamConstant.EXAM_IS_NOT_CHECK_FINISH, answerRecord.getIsCheckFinish())) {
            throw new BusinessException(ExamErrorNoEnum.ERR_WAIT_CORRECT);
        }
        // 如果考试过了，而且完成改卷，但是考试不允许重考
        if (exam.getReExamCount() <= ExamConstant.EXAM_IS_NOT_RE) {
            throw new BusinessException(ExamErrorNoEnum.EXAM_IS_NOT_RE);
        }
        // 如果考试过了，而且完成改卷，而且考试允许重考，而且考试及格，但是考试不允许及格的重考
        if (answerRecord.getUserScore().doubleValue() >= exam.getPassScore().doubleValue()
            && Objects.equals(exam.getIsReExam(), ExamConstant.EXAM_IS_NOT_RE)) {
            throw new BusinessException(ExamErrorNoEnum.EXAM_PASS_CAN_NOT_REEXAMINE);
        }

        return exam;
    }

    private Exam selectExamById(String examId) {
        return baseMapper.selectOne(new LambdaQueryWrapper<Exam>().eq(Exam::getId, examId)
            .eq(Exam::getExamType, String.valueOf(ExamTypeEnum.EXAM.getCode())));
    }

    @Override
    public MyExamAnalysisDTO getExamResult(String examId, Integer isIgnoreView, String enterType) {
        log.info("getExamResult enterType:{}", enterType);
        // 变量
        String userId = UserThreadContext.getUserId();

        // 1 校验下发范围
        if (Objects.equals(GeneralJudgeEnum.NEGATIVE.getValue(), isIgnoreView)) {
            verifyExamViewLimit(examId, false);
        }

        // 2 校验当前能否查看考试答案
        Exam exam = baseMapper.selectById(examId);
        AnswerRecord lastAnswerRecord = answerRecordService.getLastAnswerRecord(examId, userId);
        if (null == lastAnswerRecord || lastAnswerRecord.getIsPost() == ExamConstant.EXAM_IS_NOT_POST) {
            throw new BusinessException(ExamErrorNoEnum.ERR_EXAM_NOT_SUBMIT_CANNOT_VIEW_ANSWER);
        }

        boolean canView = true;
        canView = getCanView(exam, 1) == 1;

        // 3 考试基本信息填充
        MyExamAnalysisDTO myExamAnalysisDTO = MyExamAnalysisDTO.builder().id(exam.getId()).name(exam.getExamName())
            .description(exam.getDescription()).startTime(exam.getStartTime()).endTime(exam.getEndTime())
            .enterTime(lastAnswerRecord.getStartTime()).answerTime(lastAnswerRecord.getAnswerTime()).myTimeCount(
                lastAnswerRecord.getAnswerTime().getTime() - lastAnswerRecord.getStartTime().getTime() / 1000 / 60)
            .questionCount(getExamQuestionCount(examId)).degreeDifficult(exam.getDegreeDifficult())
            .myScore(new BigDecimal("-1")).build();
        BeanUtils.copyProperties(exam, myExamAnalysisDTO);

        // 得分
        if (lastAnswerRecord.getIsCheckFinish() == ExamConstant.EXAM_IS_USER_CHECK) {
            myExamAnalysisDTO.setMyScore(lastAnswerRecord.getUserScore());
        }

        // 4 查该考试的题目和选项
        List<MyExamQuestionAnalysisDTO> myExamQuestionAnalysisDTOList = getMyExamQuestionAnalysisDTOS(examId,
            lastAnswerRecord, canView);

        myExamAnalysisDTO.setQuestionList(myExamQuestionAnalysisDTOList);
        return myExamAnalysisDTO;
    }

    /**
     * 提交考试答案 主要流程： 1. 校验考试和答题状态 2. 更新答题记录 3. 进行系统阅卷（同步或异步） 4. 发送相关消息
     *
     * @param examId        考试ID
     * @param userId        用户ID
     * @param examSubmitDTO 提交的答题信息
     */
    @Override
    public void submit(String examId, String userId, ExamSubmitDTO examSubmitDTO) {
        Date now = new Date();
        String submitLockName = ExamRedisKeyEnum.ANSWER_SUBMIT.getKey() + examId + ":" + userId;

        try {
            // 获取提交锁，防止并发提交
            RedisLockUtil.acquire(submitLockName, 30, 60);

            // 1. 执行前置校验
            Exam exam = validateSubmission(examId, examSubmitDTO);

            // 2. 获取考试数据
            OnGoingExamDTO onGoingExamDTO = getExamDataFromRedis(examId, userId);

            // 3. 获取并验证答题记录
            AnswerRecord answerRecord = getAndValidateAnswerRecord(examId, userId);

            // 4. 保存关联的业务属性
            saveAnswerBizAttr(examSubmitDTO, answerRecord.getId());

            // 5. 更新答题记录状态
            updateAnswerRecordStatus(exam, userId, answerRecord, onGoingExamDTO, examSubmitDTO, now);

            // 6. 确定阅卷方式
            boolean isSystemCheck = determineMarkingMethod(onGoingExamDTO.getQuestionList());
            boolean isAsyncHandleQuestion = determineIfAsyncMarking();

            // 7. 执行阅卷处理
            answerRecord = processQuestionsAndMarking(examId, userId, examSubmitDTO,
                onGoingExamDTO, answerRecord, isSystemCheck, isAsyncHandleQuestion);

            // 8. 发送相关消息通知
            sendNotifications(userId, examId, exam, examSubmitDTO, answerRecord, now);

        } finally {
            // 释放提交锁
            RedisLockUtil.release(submitLockName);
        }
    }

    /**
     * 验证提交的合法性
     *
     * @param examId        考试ID
     * @param examSubmitDTO 提交DTO
     * @return 考试对象
     */
    private Exam validateSubmission(String examId, ExamSubmitDTO examSubmitDTO) {
        // 验证业务类型和下发范围
        if (!StringUtils.isEmpty(examSubmitDTO.getBizType()) &&
            ExcitationEventCategoryEnum.EXAM.getCode().equals(examSubmitDTO.getBizType())) {
            verifyExamViewLimit(examId, false);
        }

        // 验证考试状态
        Exam exam = baseMapper.selectById(examId);
        if (exam.getIsPublish() == ExamConstant.EXAM_IS_NOT_PUBLISH) {
            throw new BusinessException(ExamErrorNoEnum.ERR_EXAM_IS_NOT_PUBLISH);
        }

        return exam;
    }

    /**
     * 从Redis获取考试数据
     *
     * @param examId 考试ID
     * @param userId 用户ID
     * @return 进行中考试DTO
     */
    private OnGoingExamDTO getExamDataFromRedis(String examId, String userId) {
        OnGoingExamDTO onGoingExamDTO = getOnGoingExamDTO(examId, userId);
        if (onGoingExamDTO == null) {
            log.error("submit onGoingExamDTO 在redis中无数据,异常提交,examId:{}, userId:{}", examId, userId);
            throw new BusinessException(ExamErrorNoEnum.ERR_EXAM_IS_POST);
        }
        return onGoingExamDTO;
    }

    /**
     * 获取并验证答题记录
     *
     * @param examId 考试ID
     * @param userId 用户ID
     * @return 答题记录
     */
    private AnswerRecord getAndValidateAnswerRecord(String examId, String userId) {
        // 获取Redis中的答题记录
        AnswerRecord answerRecord = answerRecordService.getLastAnswerRecordRedis(examId, userId);

        // 验证答题记录是否存在
        if (answerRecord == null) {
            log.error("submit answerRecord 在redis中无数据,异常提交,examId:{}, userId:{}", examId, userId);
            // 删除正在作答题目缓存
            log.error("submit answerRecord 删除正在作答题目缓存,examId:{},userId:{}", examId, userId);
            redisTemplate.opsForHash().delete(ExamRedisKeyEnum.ON_GOING_EXAM.getKey() + userId, examId);
            throw new BusinessException(ExamErrorNoEnum.ERR_EXAM_IS_POST);
        }
        // 验证答题记录是否已交卷
        else if (Objects.equals(ExamConstant.EXAM_IS_POST, answerRecord.getIsPost())) {
            handleAlreadySubmittedRecord(examId, userId, answerRecord);
            //Redis中是已交卷，重复提交
            log.error("submit answerRecord 在redis中是已交卷,重复提交,examId:{}, userId:{}", examId, userId);
            throw new BusinessException(ExamErrorNoEnum.ERR_EXAM_IS_POST);
        }

        return answerRecord;
    }

    /**
     * 处理已提交的答题记录
     */
    private void handleAlreadySubmittedRecord(String examId, String userId, AnswerRecord answerRecord) {
        // 根据key，找到redis里面answerRecord数据，得到answerRecord
        // 根据answerRecord的id去数据库查是不是已交卷。若数据库找不到数据、或数据库中的状态是已阅卷的，就清掉redis
        AnswerRecord recordFromDb = answerRecordService.getByRecordId(answerRecord.getId());
        if (Objects.nonNull(recordFromDb)) {
            if (Objects.equals(recordFromDb.getIsPost(), ExamConstant.EXAM_IS_POST)) {
                answerRecordService.removeLastAnswerRecordRedis(examId, userId);
            }
        } else {
            answerRecordService.removeLastAnswerRecordRedis(examId, userId);
        }
    }

    /**
     * 更新答题记录状态
     */
    private void updateAnswerRecordStatus(Exam exam, String userId, AnswerRecord answerRecord,
        OnGoingExamDTO onGoingExamDTO, ExamSubmitDTO examSubmitDTO, Date now) {
        OrgDTO orgDTO = orgFeign.getOrgByUserId(userId);

        // 设置作答记录属性
        answerRecord.setExamId(exam.getId());
        answerRecord.setUserId(userId);
        answerRecord.setStartTime(onGoingExamDTO.getEnterTime());
        answerRecord.setQuestionCount(getExamQuestionCount(exam.getId()));
        answerRecord.setIsPost(ExamConstant.EXAM_IS_POST);
        answerRecord.setAnswerTime(now);
        answerRecord.setLevelPath(null == orgDTO ? null : orgDTO.getLevelPath());
        answerRecord.setPostType(examSubmitDTO.getPostType());

        // 更新Redis中的答题记录为已交卷
        answerRecordService.setLastAnswerRecordRedis(exam.getId(), userId, answerRecord);
    }

    /**
     * 确定阅卷方式：是否为系统自动阅卷
     */
    private boolean determineMarkingMethod(List<ExamQuestionDTO> questionList) {
        // 判断是否是单纯的系统阅卷，只要题卷中包含主观题都是人工阅卷
        boolean isSystemCheck = questionList.stream()
            .noneMatch(question -> ExamQuestionCheckUtil.isNeedToManualMark(
                question.getQuestionType(), question.getIsAiCheck()));

        // 检查组合题中的子题
        isSystemCheck = subQuestionIsSystemCheck(isSystemCheck, questionList);
        log.info("isSystemCheck: {}", isSystemCheck);

        return isSystemCheck;
    }

    /**
     * 检查组合题中的子题是否需要人工阅卷
     *
     * @param isSystemCheck 当前是否为系统阅卷
     * @param questionList  题目列表
     * @return 是否为系统阅卷
     */
    private static boolean subQuestionIsSystemCheck(boolean isSystemCheck, List<ExamQuestionDTO> questionList) {
        if (isSystemCheck) {
            for (ExamQuestionDTO examQuestionDTO : questionList) {
                if (ExamQuestionCheckUtil.isComposeQuestion(examQuestionDTO.getQuestionType())) {
                    for (ExamQuestionDTO questionDTO : examQuestionDTO.getSubQuestionList()) {
                        if (ExamQuestionCheckUtil.isNeedToManualMark(questionDTO.getQuestionType(),
                            questionDTO.getIsAiCheck())) {
                            isSystemCheck = false;
                            break;
                        }
                    }
                }
            }
        }
        return isSystemCheck;
    }

    /**
     * 确定是否异步阅卷
     */
    private boolean determineIfAsyncMarking() {
        // 根据参数配置SYSTEM_CONFIG_CODE_715，判断是否需要异步阅卷
        String isAsyncHandleQuestionParam = paraFeign
            .getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_715.getCode());
        return !StringUtils.isEmpty(isAsyncHandleQuestionParam) && !"0".equals(isAsyncHandleQuestionParam);
    }

    /**
     * 处理题目并进行阅卷
     */
    private AnswerRecord processQuestionsAndMarking(String examId, String userId, ExamSubmitDTO examSubmitDTO,
        OnGoingExamDTO onGoingExamDTO, AnswerRecord answerRecord,
        boolean isSystemCheck, boolean isAsyncHandleQuestion) {
        // 准备阅卷数据
        List<ExamSubmitAnswerDTO> examSubmitAnswerDTO = examSubmitDTO.getAnswer();
        ExamSystemHandleQuestionDTO examSystemHandleQuestionDTO = ExamSystemHandleQuestionDTO.builder()
            .examId(examId)
            .userId(userId)
            .isSystemCheck(isSystemCheck)
            .examSubmitAnswerDTO(examSubmitAnswerDTO)
            .onGoingExamDTO(onGoingExamDTO)
            .answerRecord(answerRecord)
            .build();

        // 复制biz属性
        BeanUtils.copyProperties(examSubmitDTO, examSystemHandleQuestionDTO);

        // 更新答题记录状态
        answerRecordService.updateNewestValidStatusByUserIdAndExamId(userId, examId,
            AnswerRecordValidStatusEnum.REPAIR.getValue());
        examSystemHandleQuestionDTO.getAnswerRecord().setIsCheckFinish(ExamConstant.EXAM_IS_NOT_CHECK_FINISH);
        examSystemHandleQuestionDTO.getAnswerRecord().setCheckStatus(ExamCheckStatusEnum.NOT_CHECK.getCode());
        answerRecordService.saveOrUpdate(answerRecord);

        // 根据配置选择同步或异步阅卷
        if (isAsyncHandleQuestion) {
            // 异步阅卷
            return asyncSystemHandleQuestion(examSystemHandleQuestionDTO);
        } else {
            // 同步阅卷
            return syncSystemHandleQuestion(examSystemHandleQuestionDTO);
        }
    }

    /**
     * 发送相关通知消息
     */
    private void sendNotifications(String userId, String examId, Exam exam,
        ExamSubmitDTO examSubmitDTO, AnswerRecord answerRecord, Date now) {
        // 触发提交考试激励规则
        mqProducer.sendMsg(new ExcitationMQEvent(
            new ExcitationMQEventDTO()
                .setUserId(userId)
                .setEventId(ExcitationEventEnum.commitExam.name())
                .setTargetId(examId)
                .setTargetName(exam.getExamName())
                .setBizId(examSubmitDTO.getBizId())
                .setBizType(examSubmitDTO.getBizType())
                .setIsExchange(examSubmitDTO.getIsExchange())
                .setIsTrain(exam.getIsTrain())));

        // 发送资源记录同步事件消息
        if (Objects.equals(exam.getIsTrain(), IsTrainEnum.ITSELF.getValue())) {
            mqProducer.send(new ResourceRecordSyncEvent(
                new ResourceRecordSyncDTO(
                    OperationEnum.UPDATE,
                    ResourceTypeEnum.EXAM.name(),
                    answerRecord.getId(),
                    examId,
                    userId,
                    GeneralJudgeEnum.CONFIRM.getValue(),
                    userId,
                    now,
                    userId,
                    now)));
        }

        // 发送考试提交事件消息
        mqProducer.send(new ExamSubmitEvent(examId, userId, userId));
    }

    @Override
    public void asyncSaveAnswerRecord(AnswerRecord answerRecord) {
        //将之前最新的答题记录置为补考记录,插入答题记录数据,该数据为最新的记录
        answerRecordService.updateNewestValidStatusByUserIdAndExamId(answerRecord.getUserId(), answerRecord.getExamId(),
            AnswerRecordValidStatusEnum.REPAIR.getValue());
        answerRecordService.save(answerRecord);
    }

    /**
     * 异步阅卷
     *
     * @param examSystemHandleQuestionDTO
     * @return
     */
    private AnswerRecord asyncSystemHandleQuestion(ExamSystemHandleQuestionDTO examSystemHandleQuestionDTO) {
        String message = JsonUtil.objToJson(examSystemHandleQuestionDTO);
        mqProducer.sendMsg(new ExamSystemHandleQuestionEvent(message));
        return examSystemHandleQuestionDTO.getAnswerRecord();
    }

    /**
     * 同步阅卷
     *
     * @param dto
     * @return
     */
    private AnswerRecord syncSystemHandleQuestion(ExamSystemHandleQuestionDTO dto) {
        //进行系统阅卷
        return systemHandleQuestion(dto);
    }

    /**
     * 系统自动阅卷后的处理动作
     *
     * @param examSystemHandleQuestionDTO
     * @return
     */
    private void doAfterSystemHandleQuestion(ExamSystemHandleQuestionDTO examSystemHandleQuestionDTO) {
        AnswerRecord answerRecord = examSystemHandleQuestionDTO.getAnswerRecord();
        String examId = examSystemHandleQuestionDTO.getExamId();
        String userId = examSystemHandleQuestionDTO.getUserId();

        // 发送完成[考试及格]事件
        if (answerRecord.getUserScore().compareTo(answerRecord.getPassScore()) >= 0) {
            mqProducer.send(new ExamPassEvent(examId, userId, userId, answerRecord.getUserScore().floatValue()));
        }
        // 发送完成[考试不及格]事件
        if (answerRecord.getUserScore().compareTo(answerRecord.getPassScore()) < 0) {
            mqProducer.send(new ExamUnPassEvent(examId, userId, userId, answerRecord.getUserScore().floatValue()));
        }
        // 触发考试成绩区间激励规则
        Exam exam = getById(examId);
        mqProducer.sendMsg(new ExcitationMQEvent(
            new ExcitationMQEventDTO().setUserId(userId).setEventId(ExcitationEventEnum.examProcess.name())
                .setValue(answerRecord.getUserScore()).setTargetId(examId)
                .setTargetName(examSystemHandleQuestionDTO.getExamName())
                .setBizId(examSystemHandleQuestionDTO.getBizId()).setBizType(examSystemHandleQuestionDTO.getBizType())
                .setIsExchange(examSystemHandleQuestionDTO.getIsExchange()).setIsTrain(exam.getIsTrain())));
        // 发送考试完成消息
        mqProducer.send(new ExamFinishEvent(examId, answerRecord.getUserId(), userId));
    }

    @Transactional
    @Override
    public void examSystemHandleQuestionConsumerExecute(ExamSystemHandleQuestionDTO dto) {
        String lockName = ExamRedisKeyEnum.SYSTEM_HANDLE_QUESTION.getKey() + dto.getExamId() + ":" + dto.getUserId();
        try {
            RedisLockUtil.acquire(lockName, 30);
            long startTime = System.currentTimeMillis();
            log.info("examSystemHandleQuestionConsumerExecute start");
            // 进行系统阅卷
            systemHandleQuestion(dto);
            log.info("examSystemHandleQuestionConsumerExecute end,耗时:{}", System.currentTimeMillis() - startTime);
        } finally {
            RedisLockUtil.release(lockName);
        }
    }

    /**
     * 获取在作答的试题记录
     *
     * @param examId
     * @param userId
     * @return
     */
    private OnGoingExamDTO getOnGoingExamDTO(String examId, String userId) {
        //先从redis中拿在作答的考题记录
        return (OnGoingExamDTO) redisTemplate.opsForHash()
            .get(ExamRedisKeyEnum.ON_GOING_EXAM.getKey() + userId, examId);
    }

    private void saveAnswerBizAttr(ExamSubmitDTO examSubmitDTO, String recordId) {
        List<AnswerRecordBizAttr> bizAttrs = answerRecordBizAttrMapper.selectList(
            new LambdaQueryWrapper<AnswerRecordBizAttr>().eq(AnswerRecordBizAttr::getRecordId, recordId));
        if (!CollectionUtils.isEmpty(bizAttrs)) {
            return;
        }
        AnswerRecordBizAttr recordBizAttr = new AnswerRecordBizAttr();
        BeanUtils.copyProperties(examSubmitDTO, recordBizAttr);
        answerRecordBizAttrMapper.insert(recordBizAttr.setId(newId()).setRecordId(recordId));
    }

    @Override
    public PageInfo<MyExerciserDTO> myExerciserList(MyExerciserListQueryDTO myExerciserListQueryDTO) {
        // 1 参数
        String query = myExerciserListQueryDTO.getQuery();
        Integer pageNo = myExerciserListQueryDTO.getPageNo();
        Integer pageSize = myExerciserListQueryDTO.getPageSize();

        // 2 获取当前用户可见的考试和练习id
        List<String> limitExamId = resourceViewLimitService.getResourceIdByUserId(UserThreadContext.getUserId(),
            LimitTable.ExamViewLimit.name());

        // 3 查询考试表中的练习列表
        LambdaQueryWrapper<Exam> examLambdaQueryWrapper = new LambdaQueryWrapper<Exam>().in(Exam::getId, limitExamId)
            .eq(Exam::getExamType, String.valueOf(ExamTypeEnum.EXERCISE.getCode()))
            .eq(Exam::getIsPublish, ExamConstant.EXAM_IS_PUBLISH).eq(Exam::getIsTrain, ExamConstant.EXAM_IS_NOT_TRAIN)
            .orderByDesc(Exam::getPublishTime);
        if (StringUtils.isNotEmpty(query)) {
            examLambdaQueryWrapper.like(Exam::getExamName, query);
        }

        PageInfo<Exam> pageInfo = PageMethod.startPage(pageNo, pageSize)
            .doSelectPageInfo(() -> baseMapper.selectList(examLambdaQueryWrapper));

        // 4 填充返回结果
        List<MyExerciserDTO> myExerciserDTOList = pageInfo.getList().stream().map(
            exercise -> MyExerciserDTO.builder().exerciserId(exercise.getId()).exerciserName(exercise.getExamName())
                .exerciserDesc(exercise.getDescription())
                .exerciserCategory("TODO")
                .exerciserAccuracy("TODO").exerciserPublicTime(exercise.getPublishTime())
                .exerciserQuestionCount(getExamQuestionCount(exercise.getId())).build()).collect(Collectors.toList());

        // 5 分页

        PageInfo<MyExerciserDTO> resultPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, resultPageInfo);
        resultPageInfo.setList(myExerciserDTOList);
        return resultPageInfo;
    }

    @Override
    public int getExamQuestionCount(String examId) {

        //  查该考试(类型)的基本信息
        Exam exam = baseMapper.selectOne(
            new LambdaQueryWrapper<Exam>().eq(Exam::getId, examId).select(Exam::getId, Exam::getSchemaId));

        // 非组卷题库题目总数
        int examQuestionCount = (int) examQuestionService.count(
            new LambdaQueryWrapper<ExamQuestion>()
                .eq(ExamQuestion::getExamId, examId)
                .eq(ExamQuestion::getParentId, "")
        );
        // 组卷题库题目总数
        int schemaExamQuestionCount = 0;
        if (exam != null && StringUtils.isNotEmpty(exam.getSchemaId())) {
            List<SchemaDetail> list = schemaDetailService.getBaseMapper()
                .selectList(new LambdaQueryWrapper<SchemaDetail>().eq(SchemaDetail::getSchemaId, exam.getSchemaId()));
            if (!CollectionUtils.isEmpty(list)) {
                for (SchemaDetail detail : list) {
                    schemaExamQuestionCount =
                        schemaExamQuestionCount + (detail.getQuestionNum() == null ? 0 : detail.getQuestionNum());
                }
            }
        }
        // 总数
        return examQuestionCount + schemaExamQuestionCount;
    }

    @Override
    public Map<String, Integer> getExamQuestionCountMap(Collection<String> examIds) {
        if (CollectionUtils.isEmpty(examIds)) {
            return new HashMap<>();
        }
        return baseMapper.getExamQuestionCountMap(examIds).stream().collect(
            Collectors.toMap(m -> m.get("id").toString(), m -> Integer.parseInt(m.get("cn").toString()),
                (key1, key2) -> key1));
    }

    @Override
    public void verifyExamViewLimit(String examId, boolean isExercise) {
        String userId = UserThreadContext.getUserId();
        if (!resourceViewLimitService.checkViewLimit(examId, LimitTable.ExamViewLimit.name(), userId)) {
            if (isExercise) {
                throw new BusinessException(ExamErrorNoEnum.ERR_NO_PERMISSION_TO_EXERCISE);
            } else {
                throw new BusinessException(ExamErrorNoEnum.ERR_NO_PERMISSION_TO_EXAM);
            }
        }
    }

    @Override
    public int getCompleteCountByExamId(String examId) {
        return answerRecordService.getCompleteCountByExamId(examId);
    }

    @Override
    public Map<String, Integer> getCompleteCountByExamId(Collection<String> examIds) {
        return answerRecordService.getCompleteCountByExamIdList(examIds);
    }

    @Override
    public PageInfo<ExercisePageDTO> getExercisePage(ExerciseQueryDTO examQueryDTO) {
        // 1 参数
        Integer pageNo = examQueryDTO.getPageNo();
        Integer pageSize = examQueryDTO.getPageSize();
        examQueryDTO.setExamType(ExamTypeEnum.EXERCISE.getCode());
        String userId = UserThreadContext.getUserId();
        String orgId = UserThreadContext.getOrgId();
        Set<String> userManageAreaLevelPath = orgFeign.findUserManageAreaLevelPath(userId);
        examQueryDTO.setCurrentOrgId(orgId);
        examQueryDTO.setCurrentUserId(userId);
        examQueryDTO.setManagerAreaOrgIds(userManageAreaLevelPath);
        // 2 查询考试表
        PageInfo<Exam> examPage = PageMethod.startPage(pageNo, pageSize)
            .doSelectPageInfo(() -> baseMapper.list(examQueryDTO));

        // 3 拼装返回结果
        List<ExercisePageDTO> exerciseDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(examPage.getList())) {
            exerciseDTOList = examPage.getList().stream().map(exam -> {
                UserDTO user = userFeign.getUserById(exam.getPublishBy());
                ExercisePageDTO exercisePageDTO = ExercisePageDTO.builder().id(exam.getId())
                    .exerciseName(exam.getExamName()).exerciseNo(exam.getExamNo())
                    .questionCount(getExamQuestionCount(exam.getId())).exerciseTimeCount(exam.getExamTimeCount())
                    .sourceType(Integer.parseInt(exam.getSourceType()))
                    .publishBy(null == user ? null : user.getFullName()).isPublish(exam.getIsPublish())
                    .orgId(exam.getOrgId()).orgName(exam.getOrgName()).build();
                if (null != exam.getCreateTime()) {
                    exercisePageDTO.setCreateTime(exam.getCreateTime());
                }
                if (null != exam.getPublishTime()) {
                    exercisePageDTO.setPublishTime(exam.getPublishTime());
                }
                return exercisePageDTO;
            }).collect(Collectors.toList());
        }

        PageInfo<ExercisePageDTO> resultPage = new PageInfo<>(exerciseDTOList);
        resultPage.setPageNum(pageNo);
        resultPage.setPageSize(pageSize);
        resultPage.setTotal(examPage.getTotal());

        //路由访问埋点
        mqProducer.sendMsg(
            new HomeRouterVisitEvent(RouterVisitEnum.PracticeManagement.getRouterId(), UserThreadContext.getUserId(),
                RouterVisitEnum.PracticeManagement.getName()));

        return resultPage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveExercise(SaveExerciseDTO saveExerciseDTO) {
        //当isTrain为空时,添加默认值
        Integer isTrain = Optional.ofNullable(saveExerciseDTO.getIsTrain()).orElse(0);
        saveExerciseDTO.setIsTrain(isTrain);
        // 当下发范围为仅创建人可见,不用鉴权
        if (saveExerciseDTO.getViewType() != 1 && saveExerciseDTO.getIsTrain() == 0) {
            //对下发范围进行鉴权
            ProgrammedIdQuery programmedIdQuery = new ProgrammedIdQuery();
            programmedIdQuery.setNewProgrammeId(saveExerciseDTO.getProgrammeId());
            boolean checkViewLimit = userFeign.checkUserManageArea(programmedIdQuery);
            if (!checkViewLimit) {
                throw new BusinessException(ErrorNoEnum.ERR_VIEW_LIMIT);
            }
        }
        // 1 参数
        Integer sourceType = saveExerciseDTO.getSourceType();
        String excelQuestionFile = saveExerciseDTO.getExcelQuestionFile();
        String exerciseName = saveExerciseDTO.getExerciseName();
        String description = saveExerciseDTO.getExerciseDesc();
        String userId = UserThreadContext.getUserId();
        String libraryId = saveExerciseDTO.getLibraryId();
        // 2 获取用户信息并处理下发范围
        Exam exam = new Exam();
        String examId = newId();
        exam.setId(examId);
        OrgDTO orgByUserId = orgFeign.getOrgByUserId(userId);
        exam.setOrgId(orgByUserId.getId());

        // 3 判断添加类型，导入题目
        if (sourceType == QuestionSourceTypeEnum.EXCEL_SOURCE.getCode()) {
            // 处理导入的练习
            SaveExamImportQuestionDTO saveExamImportQuestionDTO = SaveExamImportQuestionDTO.builder()
                .examOrExercise(ExamTypeEnum.EXERCISE.getCode()).sourceId(exam.getId())
                .clearQuestion(ExamConstant.EXAM_QUESTION_IS_NO_CLEAR).build();
            saveExamImportQuestionDTO.setExcelFile(excelQuestionFile);
            examQuestionService.importData(saveExamImportQuestionDTO);

        } else if (sourceType == QuestionSourceTypeEnum.LIB_SOURCE.getCode()) {
            // 处理从练习库导入的练习
            copyExerciseOrExamQuestionAndOption(libraryId, examId);
        }

        // 4 处理练习(考试表)信息
        exam.setDescription(description);
        exam.setExamType(String.valueOf(ExamTypeEnum.EXERCISE.getCode()));
        exam.setExamName(exerciseName);
        exam.setTestPaperId(libraryId);
        exam.setSourceType(String.valueOf(sourceType));
        exam.setIsTrain(
            saveExerciseDTO.getIsTrain() == null ? IsTrainEnum.ITSELF.getValue() : saveExerciseDTO.getIsTrain());
        exam.setViewType(saveExerciseDTO.getViewType());

        // 默认发布时
        Integer isPublish = saveExerciseDTO.getIsPublish();
        if (isPublish != null && isPublish == PublishStatusEnum.IS_PUBLISH.getValue()) {
            exam.setIsPublish(isPublish);
            exam.setPublishBy(UserThreadContext.getUserId());
            exam.setPublishTime(new Date());
        }

        examDao.saveExercise(exam);
        // 下发范围
        examViewLimitComponent.handleNewViewLimit(saveExerciseDTO.getProgrammeId(), exam.getId());
        // 初始化练习资源的激励配置
        mqProducer.sendMsg(new ExcitationInitMqEvent(new ResourceConfigInitDTO().setResourceId(exam.getId())
            .setResourceType(ExcitationEventCategoryEnum.EXERCISE.getCode())));

        // 与添加逻辑类似
        saveExerciseDTO.setId(examId);
        if (GeneralJudgeEnum.CONFIRM.getValue().equals(saveExerciseDTO.getIsPublish()) && Optional.ofNullable(
            saveExerciseDTO.getPushNoticeSetDTO()).isPresent()) {
            sendPushFeignSave(saveExerciseDTO, 1);
        }

        // 发送资源操作事件消息
        mqProducer.sendMsg(new ResourceOperateEvent(
            Objects.equals(saveExerciseDTO.getIsPublish(), GeneralJudgeEnum.NEGATIVE.getValue())
                ? OperationEnum.PUBLISH_CANCEL : OperationEnum.PUBLISH, PushType.EXERCISE.getKey(),
            saveExerciseDTO.getId()));
        // 发送资源同步事件消息
        if (Objects.equals(exam.getIsTrain(), IsTrainEnum.ITSELF.getValue())) {
            mqProducer.sendMsg(new ResourceSyncEvent(
                new ResourceSyncDTO(OperationEnum.CREATE, ResourceTypeEnum.EXERCISE.name(), exam.getId(),
                    exam.getExamName(), exam.getStartTime(), exam.getEndTime(), exam.getIsAvailable(),
                    exam.getIsPublish(), exam.getIsDel(), exam.getCreateBy(), exam.getCreateTime(), exam.getUpdateBy(),
                    exam.getUpdateTime())));
        }

        return examId;
    }

    /**
     * 引用练习/考试时将资源库的练习/考试复制
     *
     * @param libraryId 资源库考试/练习id
     * @param examId    当前的考试/练习id
     */
    private void copyExerciseOrExamQuestionAndOption(String libraryId, String examId) {
        // 判断是否启用
        Library library = libraryService.getById(libraryId);
        if (library.getIsAvailable() != ExamConstant.EXAM_LIBRARY_IS_ENABLE) {
            throw new BusinessException(ExamErrorNoEnum.ERR_SOURCE_NOT_AVAILABLE);
        }
        //  获取练习库题目
        List<LibQuestion> libQuestions = libQuestionService.getLibQuestionListByLibraryId(libraryId);

        Set<String> ids = libQuestions.stream().map(LibQuestion::getId).collect(Collectors.toSet());
        List<LibQuestionOption> questionOptionList = libQuestionOptionService.getByQuestionIds(ids);
        Map<String, List<LibQuestionOption>> questionOptionMap = questionOptionList.stream()
            .collect(Collectors.groupingBy(LibQuestionOption::getQuestionId));

        List<CopyFileDTO> questionImageList = new ArrayList<>();
        List<CopyFileDTO> questionVideoList = new ArrayList<>();
        List<CopyFileDTO> optionImageList = new ArrayList<>();
        List<ExamQuestionOption> examQuestionOptionsList = new ArrayList<>();

        List<ExamQuestion> examQuestions = libQuestions.stream().map(libQuestion -> {
            ExamQuestion examQuestion = new ExamQuestion();
            BeanUtils.copyProperties(libQuestion, examQuestion);
            String newQuestionId = newId();
            examQuestion.setId(newQuestionId);
            examQuestion.setExamId(examId);
            // 处理题目图片和视频
            questionImageList.add(new CopyFileDTO(libQuestion.getId(), newQuestionId, ImageBizType.QuestionImg.name()));
            questionVideoList.add(
                new CopyFileDTO(libQuestion.getId(), newQuestionId, FileBizType.QuestionVideoFile.name()));

            // 处理选项
            List<LibQuestionOption> questionOptions = questionOptionMap.get(libQuestion.getId());
            if (questionOptions != null && !questionOptions.isEmpty()) {
                List<ExamQuestionOption> examQuestionOptions = questionOptions.stream()
                    .map(libQuestionOption -> {
                        ExamQuestionOption examQuestionOption = new ExamQuestionOption();
                        BeanUtils.copyProperties(libQuestionOption, examQuestionOption);
                        String newOptionId = newId();
                        examQuestionOption.setId(newOptionId);
                        examQuestionOption.setQuestionId(newQuestionId);
                        // 处理选项图片
                        optionImageList.add(new CopyFileDTO(libQuestionOption.getId(), newOptionId,
                            ImageBizType.QuestionOptionImg.name()));
                        return examQuestionOption;
                    }).collect(Collectors.toList());
                examQuestionOptionsList.addAll(examQuestionOptions);
            }

            return examQuestion;
        }).collect(Collectors.toList());

        if (!questionImageList.isEmpty()) {
            fileFeign.copySameBizImageList(questionImageList);
        }
        if (!questionVideoList.isEmpty()) {
            fileFeign.copySameBizFileList(questionVideoList);
        }
        if (!optionImageList.isEmpty()) {
            fileFeign.copySameBizImageList(optionImageList);
        }
        examQuestionOptionService.saveBatch(examQuestionOptionsList);
        examQuestionService.saveBatch(examQuestions);
    }

    @Override
    public ExerciseInfoDTO getExerciseById(String id) {
        // 1 获取练习信息并构造返回结果
        Exam exam = baseMapper.selectById(id);
        ExerciseInfoDTO exerciseInfoDTO = ExerciseInfoDTO.builder().sourceType(Integer.parseInt(exam.getSourceType()))
            .exerciseName(exam.getExamName()).exerciseDesc(exam.getDescription()).id(exam.getId())
            .orgId(exam.getOrgId()).createBy(exam.getCreateBy()).viewType(exam.getViewType()).build();
        // 返回归属部门名称
        exerciseInfoDTO.setOrgName(orgFeign.getById(exerciseInfoDTO.getOrgId()).getOrgName());
        // 判断是否启用
        Library library = libraryService.getById(exam.getTestPaperId());
        exerciseInfoDTO.setLibraryId(library == null ? "" : library.getId());
        exerciseInfoDTO.setLibraryName(library == null ? "" : library.getLibraryName());
        // 2 查询下发范围
        exerciseInfoDTO.setLimit(examViewLimitComponent.getViewLimitBaseInfo(id));
        exerciseInfoDTO.setIsPublish(exam.getIsPublish());
        return exerciseInfoDTO;
    }

    @Override
    public void updateExercise(String id, UpdateExerciseDTO updateExerciseDTO) {
        //当isTrain为空时,添加默认值
        Integer isTrain = Optional.ofNullable(updateExerciseDTO.getIsTrain()).orElse(0);
        updateExerciseDTO.setIsTrain(isTrain);
        // 当下发范围为仅创建人可见,不用鉴权
        if (updateExerciseDTO.getViewType() != 1 && updateExerciseDTO.getIsTrain() == 0) {
            //查询练习的历史下发方案
            Long programmeId = resourceViewLimitService.getViewLimitIdByResourceId(updateExerciseDTO.getId());
            //下发范围方案调整时,对调整的下发范围进行鉴权
            if (!programmeId.equals(updateExerciseDTO.getProgrammeId())) {
                ProgrammedIdQuery programmedIdQuery = new ProgrammedIdQuery();
                programmedIdQuery.setNewProgrammeId(updateExerciseDTO.getProgrammeId());
                programmedIdQuery.setOldProgrammeId(programmeId);
                boolean checkViewLimit = userFeign.checkUserManageArea(programmedIdQuery);
                if (!checkViewLimit) {
                    throw new BusinessException(ErrorNoEnum.ERR_VIEW_LIMIT);
                }
            }
        }
        // 1 参数
        String exerciseName = updateExerciseDTO.getExerciseName();
        String exerciseDesc = updateExerciseDTO.getExerciseDesc();

        // 2 查询原来的练习
        Exam exam = baseMapper.selectById(id);

        exam.setExamName(exerciseName);
        exam.setDescription(exerciseDesc);
        exam.setUpdateTime(new Date());
        exam.setUpdateBy(UserThreadContext.getUserId());
        exam.setViewType(updateExerciseDTO.getViewType());

        // 3 处理下发范围
        examViewLimitComponent.handleNewViewLimit(updateExerciseDTO.getProgrammeId(), id);

        if (StringUtils.isNotBlank(updateExerciseDTO.getOrgId())) {
            exam.setOrgId(updateExerciseDTO.getOrgId());
        }

        // 4 更新练习信息
        examDao.updateExercise(exam);

        mqProducer.sendMsg(new ResourceOperateEvent(
            GeneralJudgeEnum.CONFIRM.getValue().equals(updateExerciseDTO.getIsPublish()) ? OperationEnum.PUBLISH
                : OperationEnum.PUBLISH_CANCEL, PushType.EXERCISE.getKey(), exam.getId()));
        mqProducer.sendMsg(new ResourceOperateEvent(OperationEnum.UPDATE, PushType.EXERCISE.getKey(), exam.getId()));

        // 与添加逻辑类似
        if (GeneralJudgeEnum.CONFIRM.getValue().equals(updateExerciseDTO.getIsPublish()) && Optional.ofNullable(
            updateExerciseDTO.getPushNoticeSetDTO()).isPresent()) {
            updateExerciseDTO.setId(id);
            sendPushFeignUpdate(updateExerciseDTO, 1);
        }
        // 发送资源同步事件消息
        if (Objects.equals(exam.getIsTrain(), IsTrainEnum.ITSELF.getValue())) {
            mqProducer.sendMsg(new ResourceSyncEvent(
                new ResourceSyncDTO(OperationEnum.UPDATE, ResourceTypeEnum.EXERCISE.name(), exam.getId(),
                    exam.getExamName(), exam.getStartTime(), exam.getEndTime(), exam.getIsAvailable(),
                    exam.getIsPublish(), exam.getIsDel(), exam.getCreateBy(), exam.getCreateTime(), exam.getUpdateBy(),
                    exam.getUpdateTime())));
        }
    }

    @Override
    public PreviewExerciseDTO previewExerciseById(String id) {
        // 1 查询练习基本信息 校验是否可以预览
        Exam exam = baseMapper.selectById(id);
        if (null == exam) {
            throw new BusinessException(ExamErrorNoEnum.ERR_EXERCISE_IS_NOT_EXIST);
        }
        // 2 获取练习题目
        List<ExamQuestion> examQuestionList = examQuestionService.list(
            new LambdaQueryWrapper<ExamQuestion>().eq(ExamQuestion::getExamId, id));
        List<ExerciserQuestionDTO> exerciserQuestionDTOList = getExerciserQuestionDTOS(examQuestionList);

        // 3 填充返回结果
        return PreviewExerciseDTO.builder().exerciseId(exam.getId()).exerciseName(exam.getExamName())
            .questionList(exerciserQuestionDTOList).build();
    }

    @Override
    public PreviewExamDTO previewExamById(String id) {
        // 1 查询考试基本信息 校验是否可以预览
        Exam exam = baseMapper.selectById(id);
        if (null == exam) {
            throw new BusinessException(ExamErrorNoEnum.ERR_EXERCISE_IS_NOT_EXIST);
        }
        if (Integer.parseInt(exam.getSourceType()) == QuestionSourceTypeEnum.ENTER_SOURCE.getCode()) {
            throw new BusinessException(ExamErrorNoEnum.ERR_EXAM_OR_EXERCISE_CAN_NOT_PREVIEW);
        }

        // 2 获取考试题目
        List<ExamQuestionDTO> examQuestionDTOList = getExamImportQuestionDTOS(id);

        for (ExamQuestionDTO examQuestionDTO : examQuestionDTOList) {
            examQuestionDTO.setAnswer(null);
            if (ExamQuestionCheckUtil.isAiCheckClozeQusetion(examQuestionDTO.getQuestionType(),
                examQuestionDTO.getIsAiCheck())) {
                examQuestionDTO.setQuestionName(
                    StringUtil.removeAnswerInQuestionName(examQuestionDTO.getQuestionName()));
            }
            if (CollectionUtils.isEmpty(examQuestionDTO.getOptionList())) {
                continue;
            }
            for (ExamQuestionOptionDTO examQuestionOptionDTO : examQuestionDTO.getOptionList()) {
                examQuestionOptionDTO.setRightAnswer(null);
                examQuestionOptionDTO.setOptionDesc(examQuestionOptionDTO.getOptionDesc().substring(2));
            }
        }

        // 3 返回结果
        return PreviewExamDTO.builder().examId(exam.getId()).examName(exam.getExamName())
            .questionList(examQuestionDTOList).build();
    }

    @SuppressWarnings("squid:S3776")
    @Override
    public List<MyExamQuestionAnalysisDTO> getMyExamQuestionAnalysisDTOS(String examId, AnswerRecord lastAnswerRecord,
        boolean canView) {
        List<MyExamQuestionAnalysisDTO> myExamQuestionAnalysisDTOResult = new ArrayList<>();
        Exam exam = baseMapper.selectById(examId);
        List<String> questionIdList = answerRecordDetailService.getQuestionIdListByAnswerRecordId(
            lastAnswerRecord.getId());
        Set<String> questionIds = new HashSet<>(questionIdList);
        Map<String, List<String>> questionImageUrlMap = fileFeign.getImagesUrlsByIds(questionIdList,
            ImageBizType.QuestionImg.name());
        Map<String, String> questionVideoUrlsMap = fileFeign.getFileUrlByIds(questionIds,
            FileBizType.QuestionVideoFile.name());

        List<ExamQuestionOption> optionsList;
        if (Integer.parseInt(exam.getSourceType()) == QuestionSourceTypeEnum.ENTER_SOURCE.getCode()) {
            List<LibQuestionOption> libQuestionOptions = libQuestionOptionService.getByQuestionIds(questionIds);
            optionsList = BeanListUtils.copyList(libQuestionOptions, ExamQuestionOption.class);
        } else {
            optionsList = examQuestionOptionService.getOptionByQuestionIdListIgnoreLogicallyDelete(questionIds);
        }

        Set<String> optionIds = optionsList.stream().map(ExamQuestionOption::getId).collect(Collectors.toSet());
        Map<String, String> optionImageMap = fileFeign.getImageUrlsByIds(optionIds,
            ImageBizType.QuestionOptionImg.name());

        Map<String, List<ExamQuestionOption>> optionsMap = optionsList.stream()
            .collect(Collectors.groupingBy(ExamQuestionOption::getQuestionId));

        List<ExamQuestion> examQuestionList;
        if (Integer.parseInt(exam.getSourceType()) == QuestionSourceTypeEnum.ENTER_SOURCE.getCode()) {
            List<AnswerRecordDetail> recordDetails = answerRecordDetailService.getByRecordId(lastAnswerRecord.getId());
            List<String> libQuestionIds = recordDetails.stream().map(AnswerRecordDetail::getQuestionId)
                .collect(Collectors.toList());
            List<LibQuestion> libQuestions = libQuestionService.getListByQuestionIdListIgnoreLogicallyDelete(
                libQuestionIds);
            List<ExamQuestion> examQuestions = new ArrayList<>();
            // 获取组卷方案，根据组卷方案的题型排序对考试题目进行排序
            List<SchemaDetail> schemaDetails = schemaDetailService.getDetailsBySchemaId(exam.getSchemaId());
            Set<Integer> questionTypes = new HashSet<>();
            schemaDetails.forEach(schemaDetail -> {
                List<LibQuestion> tempList = libQuestions.stream()
                    .filter(
                        libQuestion -> (libQuestion.getQuestionType() != null && libQuestion.getQuestionType()
                            .toString().equals(schemaDetail.getQuestionType())))
                    .sorted(Comparator.comparingInt(LibQuestion::getSortNo))
                    .collect(Collectors.toList());
                examQuestions.addAll(BeanListUtils.copyList(tempList, ExamQuestion.class));
                if (StringUtils.isNotBlank(schemaDetail.getQuestionType())) {
                    try {
                        questionTypes.add(Integer.parseInt(schemaDetail.getQuestionType()));
                    } catch (NumberFormatException e) {
                        log.error("schemaDetail.getQuestionType() is not number", e);
                    }
                }
            });

            Map<Integer, BigDecimal> questionTypeMarkMap = new HashMap<>();
            for (Integer questionType : questionTypes) {
                BigDecimal score = schemaDetailService.getScoreBySchemaIdAndQuestionType(exam.getSchemaId(),
                    questionType);
                questionTypeMarkMap.put(questionType, score);
            }

            for (ExamQuestion examQuestion : examQuestions) {
                examQuestion.setMark(questionTypeMarkMap.get(examQuestion.getQuestionType()));
            }
            examQuestionList = examQuestions;
        } else {
            examQuestionList = examQuestionService.getListByExamIdAndQuestionIdListIgnoreLogicallyDelete(examId,
                    questionIdList).stream()
                .filter(q -> StringUtils.isBlank(q.getParentId()))
                .collect(Collectors.toList());
            // 题目排序
            examQuestionList.sort(Comparator.comparingInt(ExamQuestion::getSortNo));
        }

        Set<String> examQuestionIds = examQuestionList.stream().map(ExamQuestion::getId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(examQuestionIds)) {
            return myExamQuestionAnalysisDTOResult;
        }
        Map<String, AnswerRecordDetail> answerRecordDetailMap = answerRecordDetailService.list(
            new LambdaQueryWrapper<AnswerRecordDetail>().eq(AnswerRecordDetail::getExamId, examId)
                .in(AnswerRecordDetail::getQuestionId, examQuestionIds)
                .eq(AnswerRecordDetail::getRecordId, lastAnswerRecord.getId())
        ).stream().collect(Collectors.toMap(AnswerRecordDetail::getQuestionId, Function.identity(), (v1, v2) -> v1));

        myExamQuestionAnalysisDTOResult = examQuestionList.stream().map(examQuestion -> {
            AnswerRecordDetail answerRecordDetail = answerRecordDetailMap.get(examQuestion.getId());
            // 填充题目信息
            MyExamQuestionAnalysisDTO myExamQuestionAnalysisDTO = MyExamQuestionAnalysisDTO.builder()
                .questionId(examQuestion.getId())
                .questionName(StringUtil.removeAnswerInQuestionName(examQuestion.getQuestionName()))
                .questionType(examQuestion.getQuestionType()).questionDifficulty(examQuestion.getDifficulty())
                .sortNo(examQuestion.getSortNo()).questionScore(examQuestion.getMark())
                .correct(answerRecordDetail == null ? null : answerRecordDetail.getIsCorrect())
                .questionImage(questionImageUrlMap.get(examQuestion.getId()))
                .questionVideo(questionVideoUrlsMap.get(examQuestion.getId())).isAiCheck(examQuestion.getIsAiCheck())
                .build();
            if (null != answerRecordDetail) {
                if (Objects
                    .equals(ExamQuestionTypeEnum.QUESTION_TYPE_CLOZE.getType(), examQuestion.getQuestionType())) {
                    myExamQuestionAnalysisDTO.setClozeUserAnswerList(
                        Arrays.asList(answerRecordDetail.getUserAnswer().split(ExamConstant.CLOZE_ANSWER_DELIMITER)));
                    myExamQuestionAnalysisDTO.setClozeAnswerNum(
                        answerRecordDetail.getUserAnswer().split(ExamConstant.CLOZE_ANSWER_DELIMITER).length);
                } else if (ExamQuestionCheckUtil.isSubjectiveType(examQuestion.getQuestionType())) {
                    myExamQuestionAnalysisDTO.setUserAnswer(answerRecordDetail.getUserAnswer());
                    myExamQuestionAnalysisDTO
                        .setClozeUserAnswerList(Collections.singletonList(answerRecordDetail.getUserAnswer()));
                } else {
                    myExamQuestionAnalysisDTO.setUserAnswer(answerRecordDetail.getUserAnswer());
                    myExamQuestionAnalysisDTO.setQuestionAnswer(answerRecordDetail.getReferenceAnswer());
                }
            }
            if (canView) {
                myExamQuestionAnalysisDTO.setQuestionDesc(examQuestion.getQuestionDesc());
                myExamQuestionAnalysisDTO.setQuestionAnswer(StringUtils.isEmpty(
                    myExamQuestionAnalysisDTO.getQuestionAnswer()) ?
                    examQuestion.getAnswer() : myExamQuestionAnalysisDTO.getQuestionAnswer());
                myExamQuestionAnalysisDTO.setQuestionPointDesc(examQuestion.getPointDesc());
                if (ExamQuestionTypeEnum.QUESTION_TYPE_CLOZE.getType() == examQuestion.getQuestionType()) {
                    myExamQuestionAnalysisDTO.setClozeAnswerList(
                        Arrays.asList(examQuestion.getAnswer().split(ExamConstant.CLOZE_ANSWER_DELIMITER)));
                    myExamQuestionAnalysisDTO.setQuestionAnswer("");
                }
            } else {
                myExamQuestionAnalysisDTO.setQuestionDesc(ExamConstant.EXAM_ANSWER_CAN_NOT_VIEW_END);
                myExamQuestionAnalysisDTO.setQuestionAnswer(ExamConstant.EXAM_ANSWER_CAN_NOT_VIEW_END);
                myExamQuestionAnalysisDTO.setQuestionPointDesc(ExamConstant.EXAM_ANSWER_CAN_NOT_VIEW_END);
            }

            // 获取选项
            List<ExamQuestionOption> examQuestionOptionList = optionsMap.get(examQuestion.getId());
            if (!CollectionUtils.isEmpty(examQuestionOptionList)) {
                List<ExamQuestionOptionDTO> examQuestionOptionDTOList = examQuestionOptionList.stream()
                    .map(examQuestionOption -> {
                        ExamQuestionOptionDTO examQuestionOptionDTO = ExamQuestionOptionDTO.builder()
                            .optionId(examQuestionOption.getId()).optionIndex(examQuestionOption.getOptionIndex())
                            .optionCode(examQuestionOption.getOptionCode())
                            .optionDesc(examQuestionOption.getOptionName().substring(2))
                            .optionImage(optionImageMap.get(examQuestionOption.getId()))
                            .build();
                        if (canView) {
                            // 是否为我的答案
                            if (null != answerRecordDetail && answerRecordDetail.getUserAnswer()
                                .contains(examQuestionOption.getOptionCode())) {
                                examQuestionOptionDTO.setUserAnswer(ExamConstant.EXAM_IS_MY_ANSWER);
                            } else {
                                examQuestionOptionDTO.setUserAnswer(ExamConstant.EXAM_IS_NOT_MY_ANSWER);
                            }
                            examQuestionOptionDTO.setRightAnswer(examQuestionOption.getIsReference());
                        }
                        return examQuestionOptionDTO;
                    }).sorted((Comparator.comparingInt(ExamQuestionOptionDTO::getOptionIndex)))
                    .collect(Collectors.toList());
                myExamQuestionAnalysisDTO.setOptionList(examQuestionOptionDTOList);
            }

            if (ExamQuestionCheckUtil.isComposeQuestion(myExamQuestionAnalysisDTO.getQuestionType())) {
                AtomicInteger correct = new AtomicInteger(1);
                LambdaQueryWrapper<ExamQuestion> subQuestionQueryWrapper = new LambdaQueryWrapper<>();
                subQuestionQueryWrapper.eq(ExamQuestion::getParentId, myExamQuestionAnalysisDTO.getQuestionId());
                subQuestionQueryWrapper.orderByAsc(ExamQuestion::getSortNo);
                List<ExamQuestion> subExamQuestionList = examQuestionService.list(subQuestionQueryWrapper);
                Set<String> subQuestionIds = subExamQuestionList.stream().map(ExamQuestion::getId)
                    .collect(Collectors.toSet());
                List<ExamQuestionOption> subQuestionOptions = examQuestionOptionService.getOptionByQuestionIdListIgnoreLogicallyDelete(
                    subQuestionIds);
                Map<String, List<ExamQuestionOption>> subQuestionOptionMap = subQuestionOptions.stream()
                    .collect(Collectors.groupingBy(ExamQuestionOption::getQuestionId));
                Map<String, List<String>> subQuestionImageUrlMap = fileFeign.getImagesUrlsByIds(subQuestionIds,
                    ImageBizType.QuestionImg.name());
                Map<String, String> subQuestionVideoUrlsMap = fileFeign.getFileUrlByIds(subQuestionIds,
                    FileBizType.QuestionVideoFile.name());
                Map<String, String> subOptionImageMap = fileFeign.getImageUrlsByIds(subQuestionIds,
                    ImageBizType.QuestionOptionImg.name());

                Map<String, AnswerRecordDetail> subAnswerRecordDetailMap = answerRecordDetailService.list(
                        new LambdaQueryWrapper<AnswerRecordDetail>().eq(AnswerRecordDetail::getExamId, examId)
                            .in(AnswerRecordDetail::getQuestionId, subQuestionIds)
                            .eq(AnswerRecordDetail::getRecordId, lastAnswerRecord.getId())
                    ).stream()
                    .collect(Collectors.toMap(AnswerRecordDetail::getQuestionId, Function.identity(), (v1, v2) -> v1));

                List<MyExamQuestionAnalysisDTO> subQuestionList = subExamQuestionList.stream().map(examQuestionDTO -> {
                    MyExamQuestionAnalysisDTO dto = new MyExamQuestionAnalysisDTO();
                    dto.setQuestionId(examQuestionDTO.getId());
                    dto.setQuestionName(StringUtil.removeAnswerInQuestionName(examQuestionDTO.getQuestionName()));
                    dto.setQuestionType(examQuestionDTO.getQuestionType());
                    dto.setQuestionScore(examQuestionDTO.getMark());
                    dto.setQuestionDifficulty(examQuestionDTO.getDifficulty());
                    AnswerRecordDetail recordDetail = subAnswerRecordDetailMap.get(examQuestionDTO.getId());
                    if (recordDetail == null) {
                        dto.setCorrect(0);
                    } else {
                        dto.setCorrect(
                            Objects.equals(examQuestionDTO.getAnswer(), recordDetail.getUserAnswer()) ? 1 : 0);
                        dto.setUserAnswer(recordDetail.getUserAnswer());
                        if (Objects
                            .equals(ExamQuestionTypeEnum.QUESTION_TYPE_CLOZE.getType(),
                                examQuestionDTO.getQuestionType())) {
                            dto.setClozeUserAnswerList(
                                Arrays.asList(recordDetail.getUserAnswer().split(ExamConstant.CLOZE_ANSWER_DELIMITER)));
                            dto.setClozeAnswerNum(
                                recordDetail.getUserAnswer().split(ExamConstant.CLOZE_ANSWER_DELIMITER).length);
                        } else if (ExamQuestionCheckUtil.isSubjectiveType(examQuestion.getQuestionType())) {
                            dto.setUserAnswer(recordDetail.getUserAnswer());
                            dto
                                .setClozeUserAnswerList(Collections.singletonList(recordDetail.getUserAnswer()));
                        } else {
                            dto.setUserAnswer(recordDetail.getUserAnswer());
                            dto.setQuestionAnswer(recordDetail.getReferenceAnswer());
                        }
                    }
                    if (canView) {
                        dto.setQuestionDesc(examQuestionDTO.getQuestionDesc());
                        dto.setQuestionAnswer(examQuestionDTO.getAnswer());
                        dto.setQuestionPointDesc(examQuestionDTO.getPointDesc());
                        if (ExamQuestionTypeEnum.QUESTION_TYPE_CLOZE.getType() == examQuestionDTO.getQuestionType()) {
                            dto.setClozeAnswerList(
                                Arrays.asList(examQuestionDTO.getAnswer().split(ExamConstant.CLOZE_ANSWER_DELIMITER)));
                            dto.setQuestionAnswer("");
                        }
                    } else {
                        dto.setQuestionDesc(ExamConstant.EXAM_ANSWER_CAN_NOT_VIEW_END);
                        dto.setQuestionAnswer(ExamConstant.EXAM_ANSWER_CAN_NOT_VIEW_END);
                        dto.setQuestionPointDesc(ExamConstant.EXAM_ANSWER_CAN_NOT_VIEW_END);
                    }
                    dto.setQuestionImage(subQuestionImageUrlMap.get(examQuestionDTO.getId()));
                    dto.setQuestionVideo(subQuestionVideoUrlsMap.get(examQuestionDTO.getId()));
                    dto.setSortNo(examQuestionDTO.getSortNo());
                    // 获取选项
                    List<ExamQuestionOption> subQuestionOptionList = subQuestionOptionMap.get(examQuestionDTO.getId());
                    if (!CollectionUtils.isEmpty(subQuestionOptionList)) {
                        List<ExamQuestionOptionDTO> subQuestionOptionDTOList = subQuestionOptionList.stream()
                            .map(subQuestionOption -> {
                                ExamQuestionOptionDTO subQuestionOptionDTO = ExamQuestionOptionDTO.builder()
                                    .optionId(subQuestionOption.getId())
                                    .optionIndex(subQuestionOption.getOptionIndex())
                                    .optionCode(subQuestionOption.getOptionCode())
                                    .optionDesc(subQuestionOption.getOptionName().substring(2))
                                    .optionImage(subOptionImageMap.get(subQuestionOption.getId()))
                                    .build();
                                if (canView) {
                                    // 是否为我的答案
                                    if (null != recordDetail && recordDetail.getUserAnswer()
                                        .contains(subQuestionOption.getOptionCode())) {
                                        subQuestionOptionDTO.setUserAnswer(ExamConstant.EXAM_IS_MY_ANSWER);
                                    } else {
                                        subQuestionOptionDTO.setUserAnswer(ExamConstant.EXAM_IS_NOT_MY_ANSWER);
                                    }
                                    subQuestionOptionDTO.setRightAnswer(subQuestionOption.getIsReference());
                                }
                                return subQuestionOptionDTO;
                            }).sorted((Comparator.comparingInt(ExamQuestionOptionDTO::getOptionIndex)))
                            .collect(Collectors.toList());
                        dto.setOptionList(subQuestionOptionDTOList);
                    }
                    if (dto.getCorrect() == 0) {
                        correct.set(0);
                    }
                    return dto;
                }).collect(Collectors.toList());
                myExamQuestionAnalysisDTO.setCorrect(correct.get());
                myExamQuestionAnalysisDTO.setSubQuestionList(subQuestionList);
            }

            return myExamQuestionAnalysisDTO;
        }).collect(Collectors.toList());
        return myExamQuestionAnalysisDTOResult;
    }

    /**
     * @param saveExamDTO 添加/修改考试表单
     * @param exam        考试对象
     * @return 考试对象
     */
    @SuppressWarnings("java:S2583")
    @Override
    public Exam handleSourceType(SaveExamDTO saveExamDTO, Exam exam) {

        // 根据题目来源类型处理不同逻辑
        switch (saveExamDTO.getSourceType()) {
            case ExamConstant.EXAM_TYPE_IMPORT:
                // 如果没有选择题库，则清空所选方案[因为表单中方案默认选中了第一个]
                // 如果需要人工改卷，设置人工改卷方式
                if (handleImportType(saveExamDTO, exam)) {
                    exam.setCheckPaperMethod(ExamConstant.EXAM_MANUAL_REWINDING);
                }
                break;
            case ExamConstant.EXAM_TYPE_ENTER:
                handleEnterType(saveExamDTO, exam);
                break;
            case ExamConstant.EXAM_TYPE_SCHEMA:
                handleSchemaType(saveExamDTO, exam);
                break;
            case ExamConstant.EXAM_TYPE_AI:
                handleAiType(saveExamDTO, exam);
                break;
            default:
                break;
        }
        return exam;
    }

    /**
     * 处理AI类型的题目来源
     *
     * @param saveExamDTO 考试数据DTO
     * @param exam        考试
     * @return
     */
    private void handleAiType(SaveExamDTO saveExamDTO, Exam exam) {
        // 如果有引用考试ID但没有Excel题目文件，则复制题目
        if (StringUtils.isNotBlank(saveExamDTO.getReferenceExamId())) {
            // 这里复制题目
            examQuestionService.copyQuestion(saveExamDTO.getReferenceExamId(), exam.getId(),
                saveExamDTO.getIsCompetition(), saveExamDTO.getIsTrain());
        } else {
            examQuestionService.saveAiQuestion(exam.getId(), saveExamDTO.getAiRecognizeQuestionList());
        }
    }

    /**
     * 处理导入类型的题目来源
     *
     * @param saveExamDTO 考试数据DTO
     * @param exam        考试
     * @return boolean
     */
    @SuppressWarnings("java:S2583")
    private boolean handleImportType(SaveExamDTO saveExamDTO, Exam exam) {
        // 阅卷方式
        boolean checkPaperMethod;

        // 检查引用考试ID和Excel题目文件是否为空
        if (StringUtils.isEmpty(saveExamDTO.getReferenceExamId())
            && StringUtils.isEmpty(saveExamDTO.getExcelQuestionFile())) {
            throw new BusinessException(ExamErrorNoEnum.EXAM_FILE_NULL);
        }

        // 如果有引用考试ID但没有Excel题目文件，则复制题目
        if (StringUtils.isNotEmpty(saveExamDTO.getReferenceExamId()) && StringUtils.isEmpty(
            saveExamDTO.getExcelQuestionFile())) {
            // 这里复制题目
            examQuestionService.copyQuestion(saveExamDTO.getReferenceExamId(), exam.getId(),
                saveExamDTO.getIsCompetition(), saveExamDTO.getIsTrain());
            checkPaperMethod = false;
        } else {
            exam.setSchemaId(null);
            // 处理导入试卷到ex_exam
            SaveExamImportQuestionDTO importQuestionDTO = createImportQuestionDTO(saveExamDTO);
            // 导入题目
            checkPaperMethod = examQuestionService.importData(importQuestionDTO);
            // 处理导入时间到testPaper,testPaperQuestion
            handleTestPaper(importQuestionDTO, saveExamDTO.getExamName());
        }

        return checkPaperMethod;
    }

    /**
     * 创建导入题目DTO
     *
     * @param saveExamDTO 考试数据DTO
     * @return {@link SaveExamImportQuestionDTO }
     */
    private SaveExamImportQuestionDTO createImportQuestionDTO(SaveExamDTO saveExamDTO) {
        SaveExamImportQuestionDTO importQuestionDTO = new SaveExamImportQuestionDTO();
        importQuestionDTO.setExamOrExercise(ExamTypeEnum.EXAM.getCode());
        importQuestionDTO.setSourceId(saveExamDTO.getId());
        importQuestionDTO.setExcelFile(saveExamDTO.getExcelQuestionFile());
        importQuestionDTO.setIsCompetition(saveExamDTO.getIsCompetition());
        importQuestionDTO.setClearQuestion(ExamConstant.EXAM_QUESTION_IS_NO_CLEAR);
        return importQuestionDTO;
    }

    /**
     * 处理导入时间到testPaper,testPaperQuestion
     *
     * @param importQuestionDTO 导入问题dto
     * @param examName          考试名称
     */
    private void handleTestPaper(SaveExamImportQuestionDTO importQuestionDTO, String examName) {
        TestPaper testPaper = new TestPaper();
        String testPaperId = newId();
        testPaper.setId(testPaperId);
        testPaper.setTestPaperName(examName);
        testPaper.setTestPaperType(String.valueOf(QuestionSourceTypeEnum.EXCEL_SOURCE.getCode()));
        testPaper.setOrgId(UserThreadContext.getOrgId());
        testPaperService.save(testPaper);
        importQuestionDTO.setSourceId(testPaper.getId());
        testPaperQuestionService.importData(importQuestionDTO);
    }

    /**
     * 处理引入类型的题目来源
     *
     * @param saveExamDTO 考试数据DTO
     * @param exam        考试实体
     */
    private void handleEnterType(SaveExamDTO saveExamDTO, Exam exam) {
        exam.setSchemaId(null);

        // 关联的题库试卷
        exam.setTestPaperId(saveExamDTO.getTestPaperId());
        log.error("handleEnterType 1:" + JsonUtil.objToJson(saveExamDTO));
        // 准备考试问题列表
        List<SaveExamQuestionDTO> examQuestions = prepareExamQuestions(saveExamDTO);
        examQuestions.stream()
            .filter(e -> ExamQuestionCheckUtil.isComposeQuestion(e.getQuestionType()))
            .forEach(examQuestion -> {
                List<SaveExamQuestionDTO> subQuestionList = testPaperQuestionService.getTestPaperQuestionDTOListByParentId(
                    examQuestion.getId());
                examQuestion.setSubQuestionList(subQuestionList);
            });
        log.error("handleEnterType 2:" + JsonUtil.objToJson(examQuestions));
        log.error("handleEnterType 2:" + JsonUtil.objToJson(saveExamDTO));
        // 保存考试题目
        saveExamQuestions(examQuestions);
        baseLibraryRecordService.saveOperationRecord(
            Optional.ofNullable(testPaperService.getById(saveExamDTO.getTestPaperId()))
                .orElseGet(() -> new TestPaper().setTestPaperName("未知")).getTestPaperName() + "_"
                + exam.getExamName(), 1, HandleTypeEnum.TP_USE_RECORD,
            LibraryTypeEnum.LIB_EXAM);
    }

    /**
     * 准备考试问题列表
     *
     * @param saveExamDTO 将考试数据保存到
     * @return {@link List }<{@link SaveExamQuestionDTO }>
     */
    private List<SaveExamQuestionDTO> prepareExamQuestions(SaveExamDTO saveExamDTO) {
        // 需要获取试卷库的所有题目ID
        log.error("prepareExamQuestions 1:" + JsonUtil.objToJson(saveExamDTO));
        List<ExamQuestionDTO> questionDTOs = testPaperQuestionService.getTestPaperQuestionAndOptionDTOList(
            saveExamDTO.getTestPaperId());
        log.error("prepareExamQuestions 2:" + JsonUtil.objToJson(questionDTOs));
        // 如果是pk赛，判断是否只包含单选和判断题
        if (GeneralJudgeEnum.CONFIRM.getValue().equals(saveExamDTO.getIsCompetition())) {
            for (ExamQuestionDTO questionDTO : questionDTOs) {
                if (!ExamQuestionCheckUtil.isCompetitionType(questionDTO.getQuestionType())) {
                    throw new BusinessException(ExamErrorNoEnum.ERR_ONLY_SINGLE_AND_CHOICE);
                }
            }
        }

        // 如果是闯关， 只能包含单选题、多选和判断题
        if (Objects.equals(saveExamDTO.getIsTrain(), IsTrainEnum.PROMOTED_GAME.getValue())) {
            for (ExamQuestionDTO questionDTO : questionDTOs) {
                if (!ExamQuestionCheckUtil.isPromotedGameType(questionDTO.getQuestionType())) {
                    throw new BusinessException(ExamErrorNoEnum.ERR_ONLY_SINGLE_AND_MULTIPLE_CHOICE);
                }
            }
        }

        // 转换ExamQuestionDTO列表为SaveExamQuestionDTO列表, 把试卷库的题目复制到这边来 包括试卷选项
        return convertToSaveExamQuestionDTOList(questionDTOs, saveExamDTO.getId());
    }

    /**
     * 转换ExamQuestionDTO为SaveExamQuestionDTO
     *
     * @param questionDTOs 问题dto
     * @param examId       考试id
     * @return {@link SaveExamQuestionDTO }
     */
    private List<SaveExamQuestionDTO> convertToSaveExamQuestionDTOList(List<ExamQuestionDTO> questionDTOs,
        String examId) {
        if (CollectionUtils.isEmpty(questionDTOs)) {
            return Collections.emptyList();
        }

        Set<String> questionIds = questionDTOs.stream().map(ExamQuestionDTO::getQuestionId)
            .collect(Collectors.toSet());
        // 题目id和选项列表映射Map
        log.error("convertToSaveExamQuestionDTOList 1:" + JsonUtil.objToJson(questionDTOs));
        Map<String, List<SaveQuestionOptionDTO>> optionListMap = testPaperQuestionOptionService.getOfDTOByQuestionIds(
            questionIds);
        log.error("convertToSaveExamQuestionDTOList 2:" + JsonUtil.objToJson(optionListMap));
        ArrayList<SaveExamQuestionDTO> result = new ArrayList<>(questionDTOs.size());
        for (ExamQuestionDTO questionDTO : questionDTOs) {
            List<SaveQuestionOptionDTO> ofDTOByQuestionId = optionListMap.getOrDefault(questionDTO.getQuestionId(),
                Collections.emptyList());
            SaveExamQuestionDTO saveExamQuestionDTO = new SaveExamQuestionDTO();
            BeanUtils.copyProperties(questionDTO, saveExamQuestionDTO);
            saveExamQuestionDTO.setId(questionDTO.getQuestionId());
            saveExamQuestionDTO.setExamId(examId);
            saveExamQuestionDTO.setMark(questionDTO.getQuestionScore());
            saveExamQuestionDTO.setExamQuestionOptionDTOList(ofDTOByQuestionId);
            result.add(saveExamQuestionDTO);
        }
        log.error("convertToSaveExamQuestionDTOList 3:" + JsonUtil.objToJson(result));
        return result;
    }

    /**
     * 保存考试题目
     *
     * @param examQuestions 考试题目列表
     */
    private void saveExamQuestions(List<SaveExamQuestionDTO> examQuestions) {
        List<CopyFileDTO> copyImageMultiBizId = new ArrayList<>();
        List<CopyFileDTO> copyFileMultiBizId = new ArrayList<>();
        log.error("saveExamQuestions 1:" + JsonUtil.objToJson(examQuestions));
        Map<String, String> map = examQuestionService.saveOrUpdate(examQuestions, false);
        log.error("saveExamQuestions 2:" + JsonUtil.objToJson(examQuestions));
        for (Entry<String, String> entry : map.entrySet()) {
            String sourceBizId = entry.getKey();
            String bizId = entry.getValue();
            CopyFileDTO copyFileDTO = new CopyFileDTO(sourceBizId, bizId, FileBizType.QuestionVideoFile.name());
            CopyFileDTO copyImageDTO = new CopyFileDTO(sourceBizId, bizId, ImageBizType.QuestionImg.name());
            copyFileMultiBizId.add(copyFileDTO);
            copyImageMultiBizId.add(copyImageDTO);
        }

        fileFeign.copyImagesToDb(copyImageMultiBizId);
        fileFeign.copyFilesToDb(copyFileMultiBizId);

    }

    /**
     * 处理组卷方案类型的题目来源
     *
     * @param saveExamDTO 将考试数据保存到
     */
    private void handleSchemaType(SaveExamDTO saveExamDTO, Exam exam) {
        if (saveExamDTO.getExamSchemaDetail() == null || saveExamDTO.getExamSchemaDetail().isEmpty()) {
            throw new BusinessException(ExamErrorNoEnum.ERR_EXAM_LIB_COMPOSITION_DATA_NOT_NULL);
        }
        // 题库组卷
        saveExamLib(saveExamDTO, exam);
        List<String> examLibNameList = saveExamDTO.getExamLibNames();
        if (!CollectionUtils.isEmpty(examLibNameList)) {
            String examLibName = StringUtils.join(examLibNameList, "和");
            baseLibraryRecordService.saveOperationRecord(examLibName + "_" + exam.getExamName(), 1,
                HandleTypeEnum.TP_USE_RECORD, LibraryTypeEnum.LIB_QUESTION);
        }
    }

    @Override
    public void saveExamLib(SaveExamDTO saveExamDTO, Exam exam) {

        // 矩阵数据转化
        transforDate(saveExamDTO);

        // 如果是pk赛，只能包含单选题和判断题
        if (GeneralJudgeEnum.CONFIRM.getValue().equals(saveExamDTO.getIsCompetition())) {
            examLibValidateForCompetition(saveExamDTO.getSchemaDetailList());
        }

        // 如果是闯关， 只能包含单选题、多选和判断题
        if (Objects.equals(saveExamDTO.getIsTrain(), IsTrainEnum.PROMOTED_GAME.getValue())) {
            examLibValidateForPromotedGame(saveExamDTO.getSchemaDetailList());
        }

        // 保存组卷方案、每个考试的组卷都会有新的组卷方案，组卷方案id为考试id
        if (saveExamDTO.getSchemaDetailList() != null || !saveExamDTO.getSchemaDetailList().isEmpty()) {
            // 组卷方案/组卷策略ID(新需求：每个考试的组卷都会有新的组卷方案，只保存组卷详情)
            exam.setSchemaId(exam.getId());

            //取矩阵中配置的题目数量，如果填空或问答题目数大于零则为人工阅卷
            ExamSchemaDetailMatrixDTO examSchemaDetailMatrixDTO = saveExamDTO.getExamSchemaDetail().get(0);
            if (examSchemaDetailMatrixDTO.getBlanks().compareTo(BigDecimal.ZERO) > 0
                || examSchemaDetailMatrixDTO.getQa().compareTo(BigDecimal.ZERO) > 0) {
                exam.setCheckPaperMethod(ExamConstant.EXAM_MANUAL_REWINDING);
            }

            // 删除
            schemaDetailService.remove(
                new LambdaQueryWrapper<SchemaDetail>().eq(SchemaDetail::getSchemaId, exam.getId()));

            List<SchemaDetail> schemaDetails = saveExamDTO.getSchemaDetailList().stream().map(saveSchemaDetailDTO -> {
                SchemaDetail schemaDetailObj = new SchemaDetail();
                schemaDetailObj.setSchemaId(exam.getSchemaId());
                schemaDetailObj.setId(newId());
                schemaDetailObj.setQuestionType(saveSchemaDetailDTO.getQuestionType().toString());
                schemaDetailObj.setQuestionScore(saveSchemaDetailDTO.getQuestionScore());
                schemaDetailObj.setQuestionNum(saveSchemaDetailDTO.getQuestionCount());
                schemaDetailObj.setSortNo(saveSchemaDetailDTO.getSortNo());
                schemaDetailObj.setIsAvailable(1);
                schemaDetailObj.setLowLevelNum(saveSchemaDetailDTO.getLowLevelNum());
                schemaDetailObj.setMediumLevelNum(saveSchemaDetailDTO.getMediumLevelNum());
                schemaDetailObj.setHighLevelNum(saveSchemaDetailDTO.getHighLevelNum());
                return schemaDetailObj;
            }).collect(Collectors.toList());
            schemaDetailService.saveBatch(schemaDetails);
        }

        // 保存各个题组组卷配置详情对象
        if (saveExamDTO.getLibConfigList() != null && !saveExamDTO.getLibConfigList().isEmpty()) {

            // 删除
            libConfigsService.remove(new LambdaQueryWrapper<LibConfigs>().eq(LibConfigs::getExamId, exam.getId()));

            List<LibConfigs> libConfigs = saveExamDTO.getLibConfigList().stream().map(dto -> {
                LibConfigs obj = new LibConfigs();
                BeanUtils.copyProperties(dto, obj);
                obj.setId(newId());
                obj.setExamId(exam.getId());
                obj.setCreateTime(new Date());
                obj.setCreateBy(UserThreadContext.getUserId());
                obj.setUpdateTime(new Date());
                obj.setUpdateBy(UserThreadContext.getUserId());
                return obj;
            }).collect(Collectors.toList());
            libConfigsService.saveBatch(libConfigs);
        }

        // 保存题组
        if (saveExamDTO.getExamLibIds() != null && !saveExamDTO.getExamLibIds().isEmpty()) {

            // 删除
            examLibService.remove(new LambdaQueryWrapper<ExamLib>().eq(ExamLib::getExamId, exam.getId()));

            List<ExamLib> examLibList = new ArrayList<>();
            ExamLib examLib;
            for (String examLibId : saveExamDTO.getExamLibIds()) {
                examLib = new ExamLib();
                examLib.setId(newId());
                examLib.setExamId(saveExamDTO.getId());
                examLib.setCategoryId(examLibId);
                examLibList.add(examLib);
            }
            examLibService.saveBatch(examLibList);
        }
    }

    private void examLibValidateForPromotedGame(List<SaveSchemaDetailDTO> saveSchemaDetailDTOList) {
        for (SaveSchemaDetailDTO saveSchemaDetailDTO : saveSchemaDetailDTOList) {
            if (!ExamQuestionCheckUtil.isPromotedGameType(saveSchemaDetailDTO.getQuestionType())
                && saveSchemaDetailDTO.getQuestionCount() > 0) {
                throw new BusinessException(ExamErrorNoEnum.ERR_ONLY_SINGLE_AND_MULTIPLE_CHOICE);
            }
        }
    }

    /**
     * pk赛-题库组件校验
     */
    public void examLibValidateForCompetition(List<SaveSchemaDetailDTO> saveSchemaDetailDTOList) {
        for (SaveSchemaDetailDTO saveSchemaDetailDTO : saveSchemaDetailDTOList) {
            if (!ExamQuestionCheckUtil.isCompetitionType(saveSchemaDetailDTO.getQuestionType())
                && saveSchemaDetailDTO.getQuestionCount() > 0) {
                throw new BusinessException(ExamErrorNoEnum.ERR_ONLY_SINGLE_AND_CHOICE);
            }
        }
    }

    private void transforDate(SaveExamDTO saveExamDTO) {
        // 矩阵数据转化
        List<SaveSchemaDetailDTO> schemaDetailList = new ArrayList<>();
        List<SaveLibConfigDTO> libConfigList = new ArrayList<>();
        SaveSchemaDetailDTO dto1 = new SaveSchemaDetailDTO();
        SaveSchemaDetailDTO dto2 = new SaveSchemaDetailDTO();
        SaveSchemaDetailDTO dto3 = new SaveSchemaDetailDTO();
        SaveSchemaDetailDTO dto4 = new SaveSchemaDetailDTO();
        SaveSchemaDetailDTO dto5 = new SaveSchemaDetailDTO();
        dto1.setQuestionType(ExamQuestionTypeEnum.QUESTION_TYPE_RADIO.getType());
        dto2.setQuestionType(ExamQuestionTypeEnum.QUESTION_TYPE_MULTI_SELECT.getType());
        dto3.setQuestionType(ExamQuestionTypeEnum.QUESTION_TYPE_JUDGEMENT.getType());
        dto4.setQuestionType(ExamQuestionTypeEnum.QUESTION_TYPE_CLOZE.getType());
        dto5.setQuestionType(ExamQuestionTypeEnum.QUESTION_TYPE_QA.getType());
        for (ExamSchemaDetailMatrixDTO examSchemaDetailDTO : saveExamDTO.getExamSchemaDetail()) {
            if ("1".equals(examSchemaDetailDTO.getId())) {
                dto1.setQuestionCount(examSchemaDetailDTO.getSingle().intValue());
                dto2.setQuestionCount(examSchemaDetailDTO.getMulti().intValue());
                dto3.setQuestionCount(examSchemaDetailDTO.getJudge().intValue());
                dto4.setQuestionCount(examSchemaDetailDTO.getBlanks().intValue());
                dto5.setQuestionCount(examSchemaDetailDTO.getQa().intValue());
            } else if ("2".equals(examSchemaDetailDTO.getId())) {
                dto1.setQuestionScore(examSchemaDetailDTO.getSingle());
                dto2.setQuestionScore(examSchemaDetailDTO.getMulti());
                dto3.setQuestionScore(examSchemaDetailDTO.getJudge());
                dto4.setQuestionScore(examSchemaDetailDTO.getBlanks());
                dto5.setQuestionScore(examSchemaDetailDTO.getQa());
            } else if ("3".equals(examSchemaDetailDTO.getId())) {
                dto1.setSortNo(examSchemaDetailDTO.getSingle().intValue());
                dto2.setSortNo(examSchemaDetailDTO.getMulti().intValue());
                dto3.setSortNo(examSchemaDetailDTO.getJudge().intValue());
                dto4.setSortNo(examSchemaDetailDTO.getBlanks().intValue());
                dto5.setSortNo(examSchemaDetailDTO.getQa().intValue());
            } else if ("4".equals(examSchemaDetailDTO.getId())) {
                dto1.setHighLevelNum(examSchemaDetailDTO.getSingle().intValue());
                dto2.setHighLevelNum(examSchemaDetailDTO.getMulti().intValue());
                dto3.setHighLevelNum(examSchemaDetailDTO.getJudge().intValue());
                dto4.setHighLevelNum(examSchemaDetailDTO.getBlanks().intValue());
                dto5.setHighLevelNum(examSchemaDetailDTO.getQa().intValue());
            } else if ("5".equals(examSchemaDetailDTO.getId())) {
                dto1.setMediumLevelNum(examSchemaDetailDTO.getSingle().intValue());
                dto2.setMediumLevelNum(examSchemaDetailDTO.getMulti().intValue());
                dto3.setMediumLevelNum(examSchemaDetailDTO.getJudge().intValue());
                dto4.setMediumLevelNum(examSchemaDetailDTO.getBlanks().intValue());
                dto5.setMediumLevelNum(examSchemaDetailDTO.getQa().intValue());
            } else if ("6".equals(examSchemaDetailDTO.getId())) {
                dto1.setLowLevelNum(examSchemaDetailDTO.getSingle().intValue());
                dto2.setLowLevelNum(examSchemaDetailDTO.getMulti().intValue());
                dto3.setLowLevelNum(examSchemaDetailDTO.getJudge().intValue());
                dto4.setLowLevelNum(examSchemaDetailDTO.getBlanks().intValue());
                dto5.setLowLevelNum(examSchemaDetailDTO.getQa().intValue());
            } else {
                // 题库
                SaveLibConfigDTO configDto = new SaveLibConfigDTO();
                configDto.setLibId(StringUtils.isBlank(examSchemaDetailDTO.getId()) ? "" : examSchemaDetailDTO.getId());
                configDto.setSingle(examSchemaDetailDTO.getSingle().intValue());
                configDto.setMulti(examSchemaDetailDTO.getMulti().intValue());
                configDto.setJudge(examSchemaDetailDTO.getJudge().intValue());
                configDto.setBlanks(examSchemaDetailDTO.getBlanks().intValue());
                configDto.setQa(examSchemaDetailDTO.getQa().intValue());
                libConfigList.add(configDto);
            }
        }
        schemaDetailList.add(dto1);
        schemaDetailList.add(dto2);
        schemaDetailList.add(dto3);
        schemaDetailList.add(dto4);
        schemaDetailList.add(dto5);
        saveExamDTO.setSchemaDetailList(schemaDetailList);
        saveExamDTO.setLibConfigList(libConfigList);
    }

    /**
     * @param saveExamDTO 保存表单
     */
    @SuppressWarnings("squid:S3776")
    private void commonCheck(SaveExamDTO saveExamDTO) {
        // 只有在考试管理时才校验时间 因为学习项目/专题 周期项目时没有时间，并且没有下发
        if (Objects.equals(saveExamDTO.getIsTrain(), IsTrainEnum.ITSELF.getValue())) {
            if (saveExamDTO.getStartTimeAndEndTime().get(0) == null
                || saveExamDTO.getStartTimeAndEndTime().get(1) == null) {
                throw new BusinessException(ExamErrorNoEnum.ERR_EXAM_TIME_IS_NO_NULL);
            }
            // 校验时间
            long startTime = saveExamDTO.getStartTimeAndEndTime().get(0).getTime();
            long endTime = saveExamDTO.getStartTimeAndEndTime().get(1).getTime();
            if (startTime > endTime) {
                throw new BusinessException(ExamErrorNoEnum.EXAM_STARTTIME_EARLIER_THEN_ENDTIME);
            }
            // 如果是新增就要检测试卷
            if (StringUtils.isBlank(saveExamDTO.getId())) {
                if (saveExamDTO.getSourceType() == QuestionSourceTypeEnum.EXCEL_SOURCE.getCode() && StringUtils.isEmpty(
                    saveExamDTO.getExcelQuestionFile()) && StringUtils.isEmpty(saveExamDTO.getReferenceExamId())) {
                    throw new BusinessException(ExamErrorNoEnum.ERR_FAIL_TO_SAVE_QUESTION);
                }
                // 如果引用试卷就检验试卷ID是否为空
                if (saveExamDTO.getSourceType() == QuestionSourceTypeEnum.ENTER_SOURCE.getCode()
                    && null != saveExamDTO.getTestPaperId() && StringUtils.isBlank(saveExamDTO.getExamName())) {
                    throw new BusinessException(ExamErrorNoEnum.ERR_FAIL_TO_SAVE_EXAM);
                }
            }
        }
        // 检验考试时长
        if (saveExamDTO.getExamTimeCount() <= 0) {
            throw new BusinessException(ExamErrorNoEnum.ERR_EXAM_TIME_COUNT_IS_NO_NULL);
        }
        // 检验及格分要大于0小于总分
        if (saveExamDTO.getPassScore().compareTo(BigDecimal.ZERO) < 0
            || saveExamDTO.getPassScore().compareTo(saveExamDTO.getTotalScore()) > 0) {
            throw new BusinessException(ExamErrorNoEnum.ERR_EXAM_PASS_AND_TOTAL_SCORE_M);
        }
        // 这里需要校验最多两张图片 检验是否重考 是的话不给设置0
        if (saveExamDTO.getIsReExam() != null
            && saveExamDTO.getIsReExam() == 1
            && saveExamDTO.getReExamCount() <= 0) {
            throw new BusinessException(ExamErrorNoEnum.ERR_EXAM_RE_ZERO);
        }
        // 题型排序
        List<ExamSchemaDetailMatrixDTO> examSchemaDetail = saveExamDTO.getExamSchemaDetail();
        if (!ObjectUtils.isEmpty(examSchemaDetail)) {
            List<ExamSchemaDetailDTO> list = new ArrayList<>();
            for (Object source : examSchemaDetail) {
                ExamSchemaDetailDTO target = new ExamSchemaDetailDTO();
                BeanUtils.copyProperties(source, target);
                list.add(target);
            }
            List<SaveSchemaDetailDTO> schemaDetail = new SchemaServiceImpl().transforData(list);
            List<Integer> sortNoList = schemaDetail.stream().filter(detail -> detail.getQuestionCount() > 0)
                .map(SaveSchemaDetailDTO::getSortNo).collect(Collectors.toList());
            long count = sortNoList.stream().distinct().count();
            if (sortNoList.size() > count) {
                throw new BusinessException(ExamErrorNoEnum.ERR_SCHEMA_SORTNO_CAN_NOT_REPEAT);
            }
        }
        // AI识别校验

    }

    /**
     * 获取某个考试/练习的题目和选项、不包括答案
     *
     * @param examId 考试id
     * @return 考试题目列表
     */
    @Override
    public List<ExamQuestionDTO> getExamImportQuestionDTOS(String examId) {
        List<ExamQuestionDTO> examQuestionDTOS;
        List<ExamQuestion> examQuestionList = examQuestionService.getByExamId(examId);
        Set<String> questionIds = examQuestionList.stream().map(ExamQuestion::getId).collect(Collectors.toSet());
        Map<String, List<String>> questionImageUrlMap = fileFeign.getImagesUrlsByIds(questionIds,
            ImageBizType.QuestionImg.name());
        Map<String, String> questionVideoUrlsMap = fileFeign.getFileUrlByIds(questionIds,
            FileBizType.QuestionVideoFile.name());

        List<ExamQuestionOption> optionsList = examQuestionOptionService.getByQuestionIds(questionIds);
        Map<String, List<ExamQuestionOption>> optionsMap = optionsList.stream()
            .collect(Collectors.groupingBy(ExamQuestionOption::getQuestionId));

        Set<String> optionIds = optionsList.stream().map(ExamQuestionOption::getId).collect(Collectors.toSet());
        Map<String, String> optionImageMap = fileFeign.getImageUrlsByIds(optionIds,
            ImageBizType.QuestionOptionImg.name());
        examQuestionDTOS = examQuestionList.stream().map(examQuestion -> {
            ExamQuestionDTO questionDTO = ExamQuestionDTO.builder()
                .questionId(examQuestion.getId())
                .questionType(examQuestion.getQuestionType())
                .questionName(StringUtil.removeAnswerInQuestionName(examQuestion.getQuestionName()))
                .answer(examQuestion.getAnswer())
                .questionScore(examQuestion.getMark())
                .sortNo(examQuestion.getSortNo())
                .questionDesc(examQuestion.getQuestionDesc())
                .difficulty(examQuestion.getDifficulty())
                .questionImage(questionImageUrlMap.get(examQuestion.getId()))
                .isAiCheck(examQuestion.getIsAiCheck())
                .questionVideo(questionVideoUrlsMap.get(examQuestion.getId()))
                .build();
            if (ExamQuestionTypeEnum.QUESTION_TYPE_CLOZE.getType() == examQuestion.getQuestionType()) {
                questionDTO.setClozeAnswerNum(
                    examQuestion.getAnswer().split(ExamConstant.CLOZE_ANSWER_DELIMITER).length);
            }

            setOptionList(examQuestion, optionsMap, optionImageMap, questionDTO);

            if (ExamQuestionCheckUtil.isComposeQuestion(examQuestion.getQuestionType())) {
                List<ExamQuestionDTO> subQuestionList = examQuestionService.getQuestionDTOByParentId(
                    examQuestion.getId());
                for (ExamQuestionDTO examQuestionDTO : subQuestionList) {
                    if (ExamQuestionTypeEnum.QUESTION_TYPE_CLOZE.getType() == examQuestionDTO.getQuestionType()) {
                        examQuestionDTO.setClozeAnswerNum(
                            examQuestionDTO.getAnswer().split(ExamConstant.CLOZE_ANSWER_DELIMITER).length);
                        examQuestionDTO.setQuestionName(
                            StringUtil.removeAnswerInQuestionName(examQuestionDTO.getQuestionName()));
                    }
                }
                questionDTO.setSubQuestionList(subQuestionList);
            }

            return questionDTO;
        }).collect(Collectors.toList());
        // 题目排序
        examQuestionDTOS.sort((Comparator.comparingInt(ExamQuestionDTO::getSortNo)));
        // 选项排序
        for (ExamQuestionDTO examQuestionDTO : examQuestionDTOS) {
            List<ExamQuestionOptionDTO> optionList = examQuestionDTO.getOptionList();
            if (!CollectionUtils.isEmpty(optionList)) {
                optionList.sort(Comparator.comparingInt(ExamQuestionOptionDTO::getOptionIndex));
            }
        }
        return examQuestionDTOS;
    }

    private static void setOptionList(ExamQuestion examQuestion, Map<String, List<ExamQuestionOption>> optionsMap,
        Map<String, String> optionImageMap, ExamQuestionDTO questionDTO) {
        // 获取选项
        List<ExamQuestionOption> examQuestionOptionList = optionsMap.get(examQuestion.getId());
        if (!CollectionUtils.isEmpty(examQuestionOptionList)) {
            List<ExamQuestionOptionDTO> examQuestionOptionDTOS = examQuestionOptionList.stream().map(
                    examQuestionOption -> ExamQuestionOptionDTO.builder().optionId(examQuestionOption.getId())
                        .optionCode(examQuestionOption.getOptionCode()).optionIndex(examQuestionOption.getOptionIndex())
                        .rightAnswer(examQuestionOption.getIsReference()).optionDesc(examQuestionOption.getOptionName())
                        .optionImage(optionImageMap.get(examQuestionOption.getId())).build())
                .collect(Collectors.toList());
            questionDTO.setOptionList(examQuestionOptionDTOS);
        }
    }

    @Override
    public List<ExamAnalysisQuestionDTO> getAnalysisQuestionDTO(String examId) {
        List<ExamAnalysisQuestionDTO> examQuestionDTOS;
        // 获取考试
        Exam exam = getById(examId);
        // 题目来源
        if (exam == null) {
            throw new BusinessException(ExamErrorNoEnum.ERR_EXAM_IS_NOT_EXIST);
        }
        Map<String, String> map = new HashMap<>(8);

        map.put(EXAM_ID_KEY, examId);
        map.put("sourceType", exam.getSourceType());
        // 只展示单选、多选、判断
        List<ExamQuestion> examQuestionList = examQuestionMapper
            .getExamQuestionListOnlyIncludeRadioAndMultiSelectAndJudgementByExamId(
                map);
        Set<String> questionIds = examQuestionList.stream().map(ExamQuestion::getId).collect(Collectors.toSet());
        Map<String, List<String>> questionImageUrlMap = fileFeign
            .getImagesUrlsByIds(questionIds, ImageBizType.QuestionImg.name());
        Map<String, String> questionVideoUrlsMap = fileFeign
            .getFileUrlByIds(questionIds, FileBizType.QuestionVideoFile.name());
        if ("3".equals(exam.getSourceType())) {
            examQuestionDTOS = handAnalysisSourceType3(examId, questionIds, examQuestionList, questionImageUrlMap,
                questionVideoUrlsMap);
        } else {
            examQuestionDTOS = handAnalysisOther(questionIds, examQuestionList, questionImageUrlMap,
                questionVideoUrlsMap);
        }
        // 选项排序
        for (ExamAnalysisQuestionDTO examQuestionDTO : examQuestionDTOS) {
            if (examQuestionDTO.getQuestionType().equals(ExamQuestionTypeEnum.QUESTION_TYPE_RADIO.getType())
                || examQuestionDTO.getQuestionType().equals(ExamQuestionTypeEnum.QUESTION_TYPE_MULTI_SELECT.getType())
                || examQuestionDTO.getQuestionType().equals(ExamQuestionTypeEnum.QUESTION_TYPE_JUDGEMENT.getType())) {
                List<ExamAnalysisQuestionOptionDTO> optionList = examQuestionDTO.getOptionList();
                optionList.sort(Comparator.comparingInt(ExamQuestionOptionDTO::getOptionIndex));
            }
        }
        return addSubQuestion(examQuestionDTOS, examQuestionList);
    }

    private static List<ExamAnalysisQuestionDTO> addSubQuestion(
        List<ExamAnalysisQuestionDTO> examQuestionDTOS, List<ExamQuestion> examQuestionList) {
        Map<String, ExamAnalysisQuestionDTO> questionMap = examQuestionDTOS.stream()
            .collect(Collectors.toMap(ExamAnalysisQuestionDTO::getId, Function.identity(), (v1, v2) -> v1));
        List<ExamAnalysisQuestionDTO> result = new ArrayList<>();
        Map<String, List<ExamQuestion>> subQuestionMap = examQuestionList.stream()
            .filter(examQuestion -> StringUtils.isNotBlank(examQuestion.getParentId()))
            .collect(Collectors.groupingBy(ExamQuestion::getParentId));
        for (ExamAnalysisQuestionDTO examQuestion : examQuestionDTOS) {
            if (subQuestionMap.containsKey(examQuestion.getId())) {
                List<ExamQuestion> subQuestionList = subQuestionMap.get(examQuestion.getId());
                examQuestion.setSubQuestionList(subQuestionList.stream()
                    .map(question -> {
                        ExamAnalysisQuestionDTO subQuestionDTO = new ExamAnalysisQuestionDTO();
                        BeanUtils.copyProperties(question, subQuestionDTO);
                        subQuestionDTO.setOptionList(questionMap.get(question.getId()).getOptionList());
                        return subQuestionDTO;
                    }).collect(Collectors.toList()));
            }
            for (ExamQuestion question : examQuestionList) {
                if (StringUtils.isBlank(question.getParentId()) && Objects.equals(question.getId(),
                    examQuestion.getId())) {
                    result.add(examQuestion);
                }
            }

        }
        return result;
    }

    /**
     * 处理：3.引入题库试卷
     *
     * @param examId
     * @param questionIds
     * @param examQuestionList
     * @param questionImageUrlMap
     * @param questionVideoUrlsMap
     * @return
     */
    private List<ExamAnalysisQuestionDTO> handAnalysisSourceType3(String examId, Set<String> questionIds,
        List<ExamQuestion> examQuestionList, Map<String, List<String>> questionImageUrlMap,
        Map<String, String> questionVideoUrlsMap) {
        List<ExamAnalysisQuestionDTO> examQuestionDTOS;
        List<ExamQuestionOption> optionsList;
        List<LibQuestionOption> libQuestionOptionList = libQuestionOptionService.getByQuestionIds(questionIds);
        optionsList = BeanListUtils.copyList(libQuestionOptionList, ExamQuestionOption.class);
        Map<String, List<ExamQuestionOption>> optionsMap = optionsList.stream()
            .collect(Collectors.groupingBy(ExamQuestionOption::getQuestionId));
        Set<String> optionIds = optionsList.stream().map(ExamQuestionOption::getId).collect(Collectors.toSet());
        Map<String, String> optionImageMap = fileFeign
            .getImageUrlsByIds(optionIds, ImageBizType.QuestionOptionImg.name());
        examQuestionDTOS = examQuestionList.stream().map(examQuestion -> {
            ExamAnalysisQuestionDTO questionDTO = new ExamAnalysisQuestionDTO();
            BeanUtils.copyProperties(examQuestion, questionDTO);
            questionDTO.setQuestionPic(questionImageUrlMap.get(examQuestion.getId()));
            questionDTO.setQuestionVideo(questionVideoUrlsMap.get(examQuestion.getId()));
            questionDTO.setAnswer(examQuestion.getAnswer());
            // 获取选项
            List<ExamQuestionOption> questionOptionList = optionsMap.get(examQuestion.getId());
            List<ExamAnalysisQuestionOptionDTO> examQuestionOptionDTOS = new ArrayList<>();
            for (ExamQuestionOption questionOption : questionOptionList) {
                ExamAnalysisQuestionOptionDTO dto = new ExamAnalysisQuestionOptionDTO();
                dto.setOptionDesc(questionOption.getOptionName());
                dto.setOptionCode(questionOption.getOptionCode());
                dto.setOptionImage(optionImageMap.get(questionOption.getId()));
                dto.setOptionIndex(questionOption.getOptionIndex());
                examQuestionOptionDTOS.add(dto);
            }
            questionDTO.setOptionList(examQuestionOptionDTOS);
            return questionDTO;
        }).collect(Collectors.toList());
        // 获取试卷组卷策略题目类型排序
        List<Integer> questionTypeSort = schemaDetailMapper.getSchemaDetailListByExamId(examId);
        for (ExamAnalysisQuestionDTO question : examQuestionDTOS) {
            question.setSortNo(questionTypeSort.get(question.getQuestionType() - 1));
        }
        // 题目排序
        examQuestionDTOS.sort((Comparator.comparingInt(ExamAnalysisQuestionDTO::getSortNo)));

        return examQuestionDTOS;
    }

    /**
     * 处理: 其他
     *
     * @param questionIds
     * @param examQuestionList
     * @param questionImageUrlMap
     * @param questionVideoUrlsMap
     * @return
     */
    private List<ExamAnalysisQuestionDTO> handAnalysisOther(Set<String> questionIds,
        List<ExamQuestion> examQuestionList, Map<String, List<String>> questionImageUrlMap,
        Map<String, String> questionVideoUrlsMap) {
        List<ExamAnalysisQuestionDTO> examQuestionDTOS;
        List<ExamQuestionOption> optionsList = examQuestionOptionService.getByQuestionIds(questionIds);
        Map<String, List<ExamQuestionOption>> optionsMap = optionsList.stream()
            .collect(Collectors.groupingBy(ExamQuestionOption::getQuestionId));
        Set<String> optionIds = optionsList.stream().map(ExamQuestionOption::getId).collect(Collectors.toSet());
        Map<String, String> optionImageMap = fileFeign
            .getImageUrlsByIds(optionIds, ImageBizType.QuestionOptionImg.name());
        examQuestionDTOS = examQuestionList.stream().map(examQuestion -> {
            ExamAnalysisQuestionDTO questionDTO = new ExamAnalysisQuestionDTO();
            BeanUtils.copyProperties(examQuestion, questionDTO);
            questionDTO.setQuestionPic(questionImageUrlMap.get(examQuestion.getId()));
            questionDTO.setQuestionVideo(questionVideoUrlsMap.get(examQuestion.getId()));
            questionDTO.setAnswer(examQuestion.getAnswer());
            // 获取选项
            List<ExamQuestionOption> questionOptionList = optionsMap.get(examQuestion.getId());
            if (null == questionOptionList) {
                questionOptionList = new ArrayList<>();
            }
            List<ExamAnalysisQuestionOptionDTO> examQuestionOptionDTOS = new ArrayList<>();
            for (ExamQuestionOption questionOption : questionOptionList) {
                ExamAnalysisQuestionOptionDTO dto = new ExamAnalysisQuestionOptionDTO();
                dto.setOptionDesc(questionOption.getOptionName());
                dto.setOptionCode(questionOption.getOptionCode());
                dto.setOptionImage(optionImageMap.get(questionOption.getId()));
                dto.setOptionIndex(questionOption.getOptionIndex());
                examQuestionOptionDTOS.add(dto);
            }
            questionDTO.setOptionList(examQuestionOptionDTOS);
            return questionDTO;
        }).collect(Collectors.toList());
        // 题目排序
        examQuestionDTOS.sort((Comparator.comparingInt(ExamAnalysisQuestionDTO::getSortNo)));
        return examQuestionDTOS;
    }

    @Override
    public Integer getExamFinishStatus(String userId, String examId) {
        return baseMapper.getExamFinishStatus(userId, examId);
    }

    /**
     * 系统自动阅卷
     */
    public AnswerRecord systemHandleQuestion(ExamSystemHandleQuestionDTO dto) {
        AnswerRecord answerRecord = dto.getAnswerRecord();
        String examId = dto.getExamId();
        String userId = dto.getUserId();
        List<ExamSubmitAnswerDTO> examSubmitAnswerDTO = dto.getExamSubmitAnswerDTO();
        OnGoingExamDTO onGoingExamDTO = dto.getOnGoingExamDTO();

        // 处理用户已作答的题目生成答题详情
        handleSubmitQuestion(examId, userId, examSubmitAnswerDTO, onGoingExamDTO, answerRecord);
        // 处理用户未作答的题目生成答题详情
        handleUnSubmitQuestion(examId, userId, examSubmitAnswerDTO, onGoingExamDTO, answerRecord);

        //根据是否单纯的系统阅卷,设置阅卷状态是否完成,阅卷时间
        if (dto.getIsSystemCheck()) {
            //纯系统阅卷,直接阅卷完成
            answerRecord.setIsCheckFinish(ExamConstant.EXAM_IS_CHECK_FINISH);
            answerRecord.setCheckStatus(ExamCheckStatusEnum.CHECK_FINISH.getCode());
            answerRecord.setCheckTime(new Date());
            //更新作答记录信息
            answerRecordService.saveOrUpdate(answerRecord);
            //系统阅卷完成后,需要处理的动作
            doAfterSystemHandleQuestion(dto);
        } else {
            //非纯系统阅卷,存在人工阅卷,设置成阅卷中
            answerRecord.setIsCheckFinish(ExamConstant.EXAM_IS_NOT_CHECK_FINISH);
            //设置成人工阅卷中
            answerRecord.setCheckStatus(ExamCheckStatusEnum.USER_CHECKING.getCode());
            //更新作答记录信息
            answerRecordService.saveOrUpdate(answerRecord);
        }

        // 删除正在作答题目缓存
        redisTemplate.opsForHash().delete(ExamRedisKeyEnum.ON_GOING_EXAM.getKey() + userId, dto.getExamId());
        log.info("删除正在作答题目缓存,examId:{},userId:{}", dto.getExamId(), userId);
        // 删除正在作答的答题记录缓存
        answerRecordService.removeLastAnswerRecordRedis(dto.getExamId(), userId);
        log.info("删除正在作答记录缓存,examId:{},userId:{}", dto.getExamId(), userId);
        // 删除提交答案缓存
        answerRecordService.removeUserAnswerRedis(answerRecord.getId());
        log.info("删除提交答案缓存,examId:{},userId:{},answerRecordId:{}", examId, userId, answerRecord.getId());

        return answerRecord;
    }

    /**
     * 提交考试时为用户没有作答的题目生成答题记录
     *
     * @param examId              考试id
     * @param userId              用户id
     * @param examSubmitAnswerDTO 用户提交的答题对象
     * @param onGoingExamDTO      redis中该考试信息(题目，进入考试的时间等)的对象
     * @param answerRecord        答题记录
     */
    private void handleUnSubmitQuestion(String examId, String userId, List<ExamSubmitAnswerDTO> examSubmitAnswerDTO,
        OnGoingExamDTO onGoingExamDTO, AnswerRecord answerRecord) {
        List<ExamQuestionDTO> redisQuestionList = onGoingExamDTO.getQuestionList();
        List<String> answeredQuestionList = examSubmitAnswerDTO.stream().map(ExamSubmitAnswerDTO::getExamQuestionId)
            .collect(Collectors.toList());

        OrgDTO orgDTO = orgFeign.getOrgByUserId(userId);

        List<ExamQuestionDTO> subQuestionList = new ArrayList<>();
        onGoingExamDTO.getQuestionList().stream()
            .filter(examQuestionDTO -> ExamQuestionCheckUtil.isComposeQuestion(examQuestionDTO.getQuestionType()))
            .forEach(examQuestionDTO -> subQuestionList.addAll(examQuestionDTO.getSubQuestionList()));

        log.info("handleUnSubmitQuestion redisQuestionList:{}", JsonUtil.objToJson(redisQuestionList));
        redisQuestionList.addAll(subQuestionList);
        log.info("handleUnSubmitQuestion add subQuestion redisQuestionList:{}", JsonUtil.objToJson(redisQuestionList));

        // 筛选出未作答的题目id
        List<String> unAnswerQuestionIdList = redisQuestionList.stream().map(ExamQuestionDTO::getQuestionId)
            .filter(redisQuestionId -> !answeredQuestionList.contains(redisQuestionId)).collect(Collectors.toList());

        log.info("handleUnSubmitQuestion unAnswerQuestionIdList:{}", JsonUtil.objToJson(unAnswerQuestionIdList));

        Map<String, ExamQuestionDTO> redisQuestionMap = redisQuestionList.stream()
            .collect(Collectors.toMap(ExamQuestionDTO::getQuestionId, Function.identity(), (key1, key2) -> key1));

        List<Map<String, Object>> saveAnswerRecordDetailList = new ArrayList<>();
        // 生成答题详情
        for (String unAnswerQuestionId : unAnswerQuestionIdList) {
            AnswerRecordDetail answerRecordDetail = AnswerRecordDetail.builder().answerTime(new Date()).examId(examId)
                .questionId(unAnswerQuestionId).id(newId()).recordId(answerRecord.getId()).answerBy(userId)
                .userAnswer(null).build();
            ExamQuestionDTO examQuestionDTO = redisQuestionMap.get(unAnswerQuestionId);
            // 未作答算答错答错
            fullAnswerRecordDetail(examQuestionDTO, answerRecordDetail, false);

            answerRecordDetail.setLevelPath(null == orgDTO ? null : orgDTO.getLevelPath());
            answerRecordDetail.setDataFlag(
                Objects.equals(answerRecord.getIsCheckFinish(), ExamConstant.EXAM_IS_CHECK_FINISH) ? 1 : 0);
            saveAnswerRecordDetailList.add(
                Map.of("userId", userId, EXAM_ID_KEY, examId, "questionId", examQuestionDTO.getQuestionId(),
                    "answerRecordDetail", answerRecordDetail));
        }
        answerRecordDetailService.asyncSaveList(saveAnswerRecordDetailList);
    }

    /**
     * 提交考试时为用户已作答的题目生成答题记录
     *
     * @param examId              考试id
     * @param userId              用户id
     * @param examSubmitAnswerDTO 用户前端答题表单
     * @param onGoingExamDTO      redis里的数据
     * @param answerRecord        答题记录
     */
    private void handleSubmitQuestion(String examId, String userId, List<ExamSubmitAnswerDTO> examSubmitAnswerDTO,
        OnGoingExamDTO onGoingExamDTO, AnswerRecord answerRecord) {
        OrgDTO orgDTO = orgFeign.getOrgByUserId(userId);
        Map<String, ExamQuestionDTO> redisQuestionMap = onGoingExamDTO.getQuestionList().stream()
            .collect(Collectors.toMap(ExamQuestionDTO::getQuestionId, Function.identity(), (key1, key2) -> key1));
        List<ExamQuestionDTO> subQuestionList = new ArrayList<>();
        onGoingExamDTO.getQuestionList().stream()
            .filter(examQuestionDTO -> ExamQuestionCheckUtil.isComposeQuestion(examQuestionDTO.getQuestionType()))
            .forEach(examQuestionDTO -> subQuestionList.addAll(examQuestionDTO.getSubQuestionList()));
        Map<String, ExamQuestionDTO> subQuestionMap = subQuestionList.stream()
            .collect(Collectors.toMap(ExamQuestionDTO::getQuestionId, Function.identity(), (v1, v2) -> v1));
        // 答对题目数
        int rightCount = 0;
        // 客观题得分
        AtomicReference<BigDecimal> userScore = new AtomicReference<>(new BigDecimal("0.00"));
        List<Map<String, Object>> saveAnswerRecordDetailList = new ArrayList<>();
        for (ExamSubmitAnswerDTO submitAnswerDTO : examSubmitAnswerDTO) {
            String examQuestionId = submitAnswerDTO.getExamQuestionId();
            Map<String, ExamQuestionOption> dbOptionMap = examQuestionOptionService.getByQuestionId(examQuestionId)
                .stream()
                .collect(Collectors.toMap(ExamQuestionOption::getId, Function.identity(), (v1, v2) -> v1));

            AnswerRecordDetail answerRecordDetail = AnswerRecordDetail.builder().answerTime(new Date()).examId(examId)
                .questionId(examQuestionId).id(newId()).recordId(answerRecord.getId()).answerBy(userId).build();

            ExamQuestionDTO examQuestion = redisQuestionMap.get(examQuestionId);
            if (examQuestion == null) {
                examQuestion = subQuestionMap.get(examQuestionId);
            }
            if (examQuestion == null) {
                throw new BusinessException(ExamErrorNoEnum.ERR_ILLEGAL_TO_SUBMIT);
            }
            setSubQuestionRightAnswer(examQuestion, dbOptionMap);
            List<String> examOptionAnswers = submitAnswerDTO.getExamOptionAnswer();
            if (CollectionUtils.isEmpty(examOptionAnswers)) {
                examOptionAnswers = List.of("");
            }
            //改卷处理答案和成绩
            rightCount = handAnswerAndScore(examQuestion, userScore, answerRecordDetail, examOptionAnswers,
                submitAnswerDTO);
            answerRecordDetail.setLevelPath(null == orgDTO ? null : orgDTO.getLevelPath());
            answerRecordDetail.setDataFlag(
                Objects.equals(answerRecord.getIsCheckFinish(), ExamConstant.EXAM_IS_CHECK_FINISH) ? 1 : 0);
            saveAnswerRecordDetailList.add(
                Map.of("userId", userId, EXAM_ID_KEY, examId, "questionId", examQuestion.getQuestionId(),
                    "answerRecordDetail", answerRecordDetail));
        }
        answerRecordDetailService.asyncSaveList(saveAnswerRecordDetailList);
        answerRecord.setRightQuestions(rightCount);
        answerRecord.setUserScore(userScore.get());
    }

    private static void setSubQuestionRightAnswer(ExamQuestionDTO examQuestion,
        Map<String, ExamQuestionOption> dbOptionMap) {
        if (!CollectionUtils.isEmpty(examQuestion.getOptionList())) {
            for (ExamQuestionOptionDTO examQuestionOptionDTO : examQuestion.getOptionList()) {
                ExamQuestionOption option = dbOptionMap.get(examQuestionOptionDTO.getOptionId());
                if (option != null) {
                    examQuestionOptionDTO.setRightAnswer(option.getIsReference());
                }

            }
        }
    }

    /**
     * 进行改卷和算分
     *
     * @param examQuestion
     * @param userScore
     * @param answerRecordDetail
     * @param examOptionAnswers
     * @param submitAnswerDTO
     * @return
     */
    private int handAnswerAndScore(ExamQuestionDTO examQuestion, AtomicReference<BigDecimal> userScore,
        AnswerRecordDetail answerRecordDetail, List<String> examOptionAnswers, ExamSubmitAnswerDTO submitAnswerDTO) {
        boolean isCorrectAnswer;
        int rightCount = 0;
        switch (examQuestion.getQuestionType()) {
            case 1:
            case 4:
                // 单选和判断
                isCorrectAnswer = automaticMarkingRadioAndJudgement(examQuestion, answerRecordDetail,
                    examOptionAnswers);
                if (isCorrectAnswer) {
                    rightCount++;
                    userScore.updateAndGet(score -> score.add(answerRecordDetail.getScore()));
                }
                break;
            case 2:
                // 多选
                isCorrectAnswer = automaticMarkingMultiSelect(examQuestion, answerRecordDetail, examOptionAnswers);
                if (isCorrectAnswer) {
                    rightCount++;
                    userScore.updateAndGet(score -> score.add(answerRecordDetail.getScore()));
                }
                break;
            case 3:
                // 填空
                isCorrectAnswer = automaticMarkingCloze(examQuestion, answerRecordDetail, submitAnswerDTO);
                if (isCorrectAnswer) {
                    rightCount++;
                    userScore.updateAndGet(score -> score.add(answerRecordDetail.getScore()));
                }
                break;
            case 5:
                // 简答
                if (submitAnswerDTO.getExamShortAnswer() == null) {
                    answerRecordDetail.setUserAnswer("");
                } else {
                    answerRecordDetail.setUserAnswer(submitAnswerDTO.getExamShortAnswer().get(0));
                }
                answerRecordDetail.setReferenceAnswer(examQuestion.getAnswer());
                break;
            default:
                throw new IllegalStateException(UNEXPECTED_VALUE + examQuestion.getQuestionType());
        }
        log.debug("handAnswerAndScore current userScore:{}", userScore);
        return rightCount;
    }

    private boolean automaticMarkingRadioAndJudgement(ExamQuestionDTO examQuestion,
        AnswerRecordDetail answerRecordDetail,
        List<String> examOptionAnswers) {
        String optionAnswerId = examOptionAnswers.get(0);
        boolean isRight = false;
        // 不直接拿题目表里的answer字段做比较是因为选项可能乱序，导致判断错误。
        for (ExamQuestionOptionDTO examQuestionOptionDTO : examQuestion.getOptionList()) {
            if (!Objects.equals(examQuestionOptionDTO.getOptionId(), optionAnswerId)) {
                continue;
            }
            answerRecordDetail.setUserAnswer(examQuestionOptionDTO.getOptionCode());
            if (Objects.equals(examQuestionOptionDTO.getRightAnswer(), 1)) {
                // 答对
                fullAnswerRecordDetail(examQuestion, answerRecordDetail, true);
                isRight = true;
                break;
            }
        }
        if (!isRight) {
            fullAnswerRecordDetail(examQuestion, answerRecordDetail, false);
        }
        return isRight;
    }

    private boolean automaticMarkingMultiSelect(ExamQuestionDTO examQuestion, AnswerRecordDetail answerRecordDetail,
        List<String> examOptionAnswers) {
        List<ExamQuestionOptionDTO> optionList = examQuestion.getOptionList();
        if (CollectionUtils.isEmpty(optionList)) {
            return false;
        }
        List<String> rightOptions = new ArrayList<>();
        List<String> answerCode = new ArrayList<>();
        for (ExamQuestionOptionDTO examQuestionOptionDTO : optionList) {
            if (Objects.equals(examQuestionOptionDTO.getRightAnswer(), 1)) {
                rightOptions.add(examQuestionOptionDTO.getOptionId());
            }
            for (String examOptionAnswer : examOptionAnswers) {
                if (Objects.equals(examOptionAnswer, examQuestionOptionDTO.getOptionId())) {
                    answerCode.add(examQuestionOptionDTO.getOptionCode());
                }
            }
        }
        if (!CollectionUtils.isEmpty(examOptionAnswers) && !CollectionUtils.isEmpty(answerCode)) {
            Collections.sort(answerCode);
            Collections.sort(examOptionAnswers);
        }
        Collections.sort(rightOptions);

        boolean isCorrect = Objects.equals(examOptionAnswers.toString(), rightOptions.toString());
        fullAnswerRecordDetail(examQuestion, answerRecordDetail, isCorrect);

        StringBuilder userMultiSelect = new StringBuilder();
        for (String s : answerCode) {
            userMultiSelect.append(s);
        }
        answerRecordDetail.setUserAnswer(userMultiSelect.toString());
        return isCorrect;
    }

    private boolean automaticMarkingCloze(ExamQuestionDTO examQuestion, AnswerRecordDetail answerRecordDetail,
        ExamSubmitAnswerDTO submitAnswerDTO) {
        // 用户的答案
        List<String> examShortAnswerList = submitAnswerDTO.getExamShortAnswer();
        if (examShortAnswerList == null) {
            answerRecordDetail.setUserAnswer("");
        } else {
            answerRecordDetail.setUserAnswer(String.join("^", examShortAnswerList));
        }
        // 参考答案
        answerRecordDetail.setReferenceAnswer(examQuestion.getAnswer());
        if (!Objects.equals(1, examQuestion.getIsAiCheck())) {
            return false;
        }
        boolean isCorrect = true;
        // 数据库里存的标准答案
        String standardAnswer = examQuestion.getAnswer();
        if (standardAnswer == null) {
            standardAnswer = "";
        }
        // 填空题可能会有多个答案，要把它按顺序转成数组，一个答案一个元素.
        String[] standardAnswerArray = standardAnswer.split(ExamConstant.CLOZE_ANSWER_DELIMITER);
        if (CollectionUtils.isEmpty(examShortAnswerList) || !Objects.equals(examShortAnswerList.size(),
            standardAnswerArray.length)) {
            fullAnswerRecordDetail(examQuestion, answerRecordDetail, false);
            return false;
        }
        int i = 0;
        for (String userAnswer : examShortAnswerList) {
            if (!userAnswer.equals(standardAnswerArray[i])) {
                isCorrect = false;
                break;
            }
            i++;
        }
        fullAnswerRecordDetail(examQuestion, answerRecordDetail, isCorrect);
        return isCorrect;
    }

    /**
     * 提交考试时用为用户作答的答题详情填充字段
     *
     * @param question           题目对象
     * @param answerRecordDetail 答题详情对象
     * @param correct            是否答对
     */
    private void fullAnswerRecordDetail(ExamQuestionDTO question, AnswerRecordDetail answerRecordDetail,
        Boolean correct) {
        if (Boolean.TRUE.equals(correct)) {
            // 答对
            answerRecordDetail.setIsCorrect(ExamConstant.EXAM_OPTION_IS_CORRECT);
            answerRecordDetail.setScore(question.getQuestionScore());
            answerRecordDetail.setReferenceAnswer(question.getAnswer());
            answerRecordDetail.setIsWrongQuestion(ExamConstant.EXAM_QUESTION_IS_NOT_WRONG_QUESTION);
        } else {
            // 答错
            answerRecordDetail.setIsCorrect(ExamConstant.EXAM_OPTION_IS_NOT_CORRECT);
            answerRecordDetail.setScore(new BigDecimal(0));
            answerRecordDetail.setReferenceAnswer(question.getAnswer());
            answerRecordDetail.setIsWrongQuestion(ExamConstant.EXAM_QUESTION_IS_WRONG_QUESTION);
        }
    }

    /**
     * 开始练习时获取练习的题目列表返回对象
     *
     * @param examQuestionList 练习题目列表
     * @return 练习的题目列表返回对象
     */
    private List<ExerciserQuestionDTO> getExerciserQuestionDTOS(List<ExamQuestion> examQuestionList) {

        Set<String> ids = examQuestionList.stream().map(ExamQuestion::getId).collect(Collectors.toSet());
        //如果题目表id集合为空，后面的in条件查询会报错
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        Map<String, List<String>> questionImageUrlMap = fileFeign.getImagesUrlsByIds(ids,
            ImageBizType.QuestionImg.name());

        Map<String, String> questionVideoUrlMap = fileFeign.getFileUrlByIds(ids, FileBizType.QuestionVideoFile.name());

        LambdaQueryWrapper<ExamQuestionOption> query = new LambdaQueryWrapper<>();
        query.in(ExamQuestionOption::getQuestionId, ids);
        List<ExamQuestionOption> allQuestionOptionList = examQuestionOptionService.list(query);
        Map<String, List<ExamQuestionOption>> questionOptionMap = allQuestionOptionList.stream()
            .collect(Collectors.groupingBy(ExamQuestionOption::getQuestionId));

        Set<String> optionIds = allQuestionOptionList.stream().map(ExamQuestionOption::getId)
            .collect(Collectors.toSet());
        Map<String, String> optionImageUrlMap = fileFeign.getImageUrlsByIds(optionIds,
            ImageBizType.QuestionOptionImg.name());

        return examQuestionList.stream()
            .map(examQuestion -> {
                ExerciserQuestionDTO res = ExerciserQuestionDTO.builder().questionId(examQuestion.getId())
                    .questionType(examQuestion.getQuestionType()).questionName(examQuestion.getQuestionName())
                    .questionAnswer(examQuestion.getAnswer()).questionScore(examQuestion.getMark())
                    .isAiCheck(examQuestion.getIsAiCheck())
                    .questionDesc(examQuestion.getQuestionDesc()).sortNo(examQuestion.getSortNo()).build();
                // 调用微服务查询题目图片
                res.setQuestionImage(questionImageUrlMap.get(examQuestion.getId()));
                // 调用微服务查询题目视频
                res.setQuestionVideo(questionVideoUrlMap.get(examQuestion.getId()));

                if (ExamQuestionTypeEnum.QUESTION_TYPE_CLOZE.getType() == examQuestion.getQuestionType()) {
                    res.setClozeAnswerNum(examQuestion.getAnswer().split(ExamConstant.CLOZE_ANSWER_DELIMITER).length);
                    res.setQuestionName(StringUtil.removeAnswerInQuestionName(examQuestion.getQuestionName()));
                    res.setClozeAnswerList(
                        Arrays.asList(examQuestion.getAnswer().split(ExamConstant.CLOZE_ANSWER_DELIMITER)));
                    res.setQuestionAnswer(null);
                }
                // 获取题目选项
                List<ExamQuestionOption> examQuestionOptionList = questionOptionMap.get(examQuestion.getId());
                if (examQuestionOptionList != null && !examQuestionOptionList.isEmpty()) {
                    List<ExerciserQuestionOptionDTO> exerciserQuestionOptionDTOList = examQuestionOptionList.stream()
                        .map(examQuestionOption -> {
                            if (Objects.equals(examQuestionOption.getOptionName().charAt(1), '.')) {
                                examQuestionOption.setOptionName(examQuestionOption.getOptionName().substring(2));
                            } else {
                                examQuestionOption.setOptionName(examQuestionOption.getOptionName().substring(1));
                            }
                            return ExerciserQuestionOptionDTO.builder().optionId(examQuestionOption.getId())
                                .optionDesc(examQuestionOption.getOptionName())
                                .rightAnswer(examQuestionOption.getIsReference())
                                .optionImage(optionImageUrlMap.get(examQuestionOption.getId()))
                                .optionIndex(examQuestionOption.getOptionIndex()).build();
                        }).sorted(Comparator.comparingInt(ExerciserQuestionOptionDTO::getOptionIndex))
                        .collect(Collectors.toList());
                    res.setOptionList(exerciserQuestionOptionDTOList);
                }
                return res;
            })
            .sorted(Comparator.comparingInt(ExerciserQuestionDTO::getSortNo))
            .collect(Collectors.toList());
    }

    @Override
    public Integer generateComposition(String examId) {
        Integer compositionStatus = ExamConstant.COMPOSITION_NOT;
        String lockName = ExamRedisKeyEnum.EXAM_COMPOSITION + examId;
        boolean isGetLock = false;
        try {
            isGetLock = RedisLockUtil.acquire(lockName, 300L, 300L);
            Exam exam = this.getById(examId);

            //更新考试组卷状态为组卷中
            compositionStatus = ExamConstant.COMPOSITION_ING;
            changeExamCompositionStatus(examId, compositionStatus);
            log.info("generateComposition examId:{} 更新考试组卷状态为组卷中", examId);

            Integer compositionCount =
                (exam.getCompositionCount() == null || Objects.equals(0, exam.getCompositionCount())) ? 1
                    : exam.getCompositionCount();

            List<ExamComposition> examCompositionList = new ArrayList<>();

            //根据考试配置组卷方案数量，设置每个考试有多少套题
            String maxCompositionParam = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_714.getCode());
            Integer maxCompositionCount =
                StringUtils.isEmpty(maxCompositionParam) ? null : Integer.valueOf(maxCompositionParam);
            if (null != maxCompositionCount && compositionCount.compareTo(maxCompositionCount) > 0) {
                throw new BusinessException(ExamErrorNoEnum.ERR_OVER_MAX_COMPOSITION_COUNT, null,
                    String.valueOf(maxCompositionCount));
            }

            long compositionTime = System.currentTimeMillis();
            log.info("generateComposition examId：{} 进行考试组卷开始,组卷数量:{}", examId, compositionCount);
            for (int i = 0; i < compositionCount; i++) {
                ExamCompositionDTO examCompositionDTO = buildExamComposition(exam);
                ExamComposition saveInfo = new ExamComposition();
                BeanUtils.copyProperties(examCompositionDTO, saveInfo);
                String examQuestions = JsonUtil.objToJson(examCompositionDTO.getExamQuestionList());
                saveInfo.setExamQuestions(examQuestions);
                examCompositionList.add(saveInfo);
            }
            //删除历史的组卷方案
            examCompositionService
                .remove(new LambdaQueryWrapper<ExamComposition>().eq(ExamComposition::getExamId, examId));

            //持久化考题组卷数据入数据库
            examCompositionService.saveBatch(examCompositionList);

            //考试组卷状态为组卷完成
            compositionStatus = ExamConstant.COMPOSITION_FINISH;

            log.info("generateComposition 进行考试组卷结束,组卷数量:{},耗时:{}", compositionCount,
                System.currentTimeMillis() - compositionTime);
        } catch (Exception e) {
            log.error("generateComposition examId:{} 进行组卷失败 error:{}", examId, e.getMessage());
            //考试组卷状态为组卷失败
            compositionStatus = ExamConstant.COMPOSITION_FAIL;
            throw new BusinessException(ExamErrorNoEnum.ERR_COMPOSITION);
        } finally {
            //更新考试组卷状态为组卷成功/组卷失败
            changeExamCompositionStatus(examId, compositionStatus);
            log.info("generateComposition examId:{} 更新考试组卷状态为:{}", examId, compositionStatus);
            if (isGetLock) {
                RedisLockUtil.release(lockName);
            }
        }
        return compositionStatus;
    }

    /**
     * 获取考试推送配置-从redis拿
     *
     * @param examId
     * @return
     */
    private Map<String, Object> getLastPushNoticeSetRedis(String examId) {
        return (Map<String, Object>) redisTemplate.opsForHash().get(ExamRedisKeyEnum.EXAM_PUSH_CONFIG.getKey(), examId);
    }

    /**
     * 设置考试推送配置-存入redis
     *
     * @param examId
     * @return
     */
    private void setLastPushNoticeSetRedis(String examId, PushNoticeSetDTO pushNoticeSet, Integer operateState) {
        Map<String, Object> redisData = new HashMap<>();
        redisData.put(EXAM_ID_KEY, examId);
        redisData.put("language", LocaleContextHolder.getLocale().getLanguage());
        redisData.put("operateState", operateState);
        redisData.put("pushNoticeSet", pushNoticeSet);
        redisData.put("serverName", Objects.requireNonNull(WebUtil.getRequest()).getServerName());
        redisTemplate.opsForHash().put(ExamRedisKeyEnum.EXAM_PUSH_CONFIG.getKey(), examId, redisData);
    }

    /**
     * 自动发布
     *
     * @param examId
     * @return
     */
    @Override
    public void autoPublish(String examId, String publishBy) {
        Exam exam = baseMapper.selectById(examId);
        if (null == exam) {
            return;
        }
        if (ExamConstant.COMPOSITION_FINISH != exam.getCompositionStatus()) {
            //考试组卷没有完成,不能进行自动发布
            log.error("autoPublish examId:{} 自动发布失败,考试组卷状态:{}", examId, exam.getCompositionStatus());
            return;
        }
        //变更发布状态为自动发布配置的状态
        PublishExamDTO publishExamDTO = new PublishExamDTO();
        publishExamDTO.setIsPublish(exam.getIsAutoPublish());
        //找到上次记录的发布人，设置成当前发布人,因为之前的发布是延后发布的
        publishExamDTO.setPublishBy(publishBy);
        publishExamDTO.setIds(Collections.singletonList(exam.getId()));
        this.publish(publishExamDTO);
        //变更发布状态后触发的动作
        doAfterChangePublish(examId);
    }

    /**
     * 修改组卷状态
     *
     * @param examId
     * @param compositionStatus
     * @return
     */
    @Override
    public void changeExamCompositionStatus(String examId, Integer compositionStatus) {
        Exam updateExamInfo = new Exam();
        updateExamInfo.setId(examId);
        updateExamInfo.setCompositionStatus(compositionStatus);
        updateExamInfo.setUpdateBy("system-composition");
        updateExamInfo.setCompositionTime(new Date());
        baseMapper.changeExamCompositionStatus(updateExamInfo);
    }

    /**
     * 随机获取一套考题
     *
     * @param examId
     * @return
     */
    public ExamCompositionDTO getRandomComposition(String examId) {
        ExamCompositionDTO result = new ExamCompositionDTO();
        Exam exam = this.getById(examId);
        if (null == exam) {
            throw new BusinessException(ErrorNoEnum.ERR_RESOURCE_NOT_EXIST);
        }
        // 进一步优化空间：优化sql查询中的rand()函数
        ExamComposition examComposition = examCompositionService.getRandomExamCompositionByExamId(examId);
        if (examComposition == null) {
            throw new BusinessException(ExamErrorNoEnum.ERR_EXAM_QUESTION_IS_NOT_EXIST);
        }
        BeanUtils.copyProperties(examComposition, result);
        List<ExamQuestionDTO> examQuestionDTOList = JsonUtil
            .json2List(examComposition.getExamQuestions(), ExamQuestionDTO.class);
        result.setExamQuestionList(examQuestionDTOList);
        return result;
    }

    /**
     * 构建组卷
     *
     * @param exam
     * @return
     */
    private ExamCompositionDTO buildExamComposition(Exam exam) {
        //组卷方案
        ExamCompositionDTO examCompositionDTO = new ExamCompositionDTO();
        List<ExamQuestionDTO> examQuestionList;
        if (Integer.parseInt(exam.getSourceType()) == QuestionSourceTypeEnum.ENTER_SOURCE.getCode()) {
            //组卷策略
            examQuestionList = this.getExamSchemaQuestionDTOS2(exam);
        } else {
            // 导入的题目和引用的试卷则直接查考试题目表
            examQuestionList = this.getExamImportQuestionDTOS(exam.getId());
        }
        examCompositionDTO.setId(newId());
        examCompositionDTO.setExamId(exam.getId());
        examCompositionDTO.setExamQuestionList(examQuestionList);
        examCompositionDTO.setCreateTime(new Date());
        examCompositionDTO.setCreateBy("system");
        examCompositionDTO.setQuestionCount(CollectionUtils.isEmpty(examQuestionList) ? 0 : examQuestionList.size());
        return examCompositionDTO;
    }

    @SuppressWarnings("squid:S3776")
    @Override
    public List<ExamQuestionDTO> getExamSchemaQuestionDTOS2(Exam exam) {
        // 最终返回结果：所有题目
        List<ExamQuestionDTO> allExamQuestionDTOS = new ArrayList<>();

        String examId = exam.getId();
        // 1 当前考试抽题所用的题组的id列表
        List<String> libIds = examLibService.getLibIdByExamId(examId);

        // 2 组卷方案详情列表
        List<SchemaDetail> schemaDetails = schemaDetailService.getDetailsBySchemaId(exam.getSchemaId());

        // 按题组组卷
        List<LibConfigs> libConfigs = libConfigsService.list(
            new LambdaQueryWrapper<LibConfigs>().eq(LibConfigs::getExamId, exam.getId()));

        // 3 遍历每个组卷方案详情，按每个组卷方案详情的要求抽题目
        for (SchemaDetail schemaDetail : schemaDetails) {

            List<LibQuestionQueryDTO> query = getLibQuestionQueryDTOS(exam, libIds, schemaDetail, libConfigs);
            if (CollectionUtils.isEmpty(query)) {
                continue;
            }
            List<LibQuestion> libQuestions = libQuestionService.getQuestionByRand(query);
            Set<String> questionIds = libQuestions.stream().map(LibQuestion::getId).collect(Collectors.toSet());
            List<LibQuestionOption> questionOptionList = libQuestionOptionService.getByQuestionIds(questionIds);
            Map<String, List<LibQuestionOption>> questionOptionListMap = questionOptionList.stream()
                .collect(Collectors.groupingBy(LibQuestionOption::getQuestionId));
            Set<String> optionIds = questionOptionList.stream().map(LibQuestionOption::getId)
                .collect(Collectors.toSet());

            Map<String, List<String>> questionImageMap = fileFeign.getImagesUrlsByIds(questionIds,
                ImageBizType.QuestionImg.name());

            Map<String, String> questionVideoMap = fileFeign.getFileUrlByIds(questionIds,
                FileBizType.QuestionVideoFile.name());

            Map<String, String> optionImageMap = fileFeign.getImageUrlsByIds(optionIds,
                ImageBizType.QuestionOptionImg.name());

            // 用于保存当前组卷方案详情抽到的所有题
            List<ExamQuestionDTO> schemaExamQuestionDTOS = new ArrayList<>();
            for (LibQuestion libQuestion : libQuestions) {
                // 处理题目对象，封装成DTO
                ExamQuestionDTO questionDTO = ExamQuestionDTO.builder().answer(libQuestion.getAnswer())
                    .questionDesc(libQuestion.getQuestionDesc()).questionId(libQuestion.getId())
                    .questionImage(questionImageMap.get(libQuestion.getId()))
                    .questionName(StringUtil.removeAnswerInQuestionName(libQuestion.getQuestionName()))
                    .questionScore(schemaDetail.getQuestionScore()).difficulty(libQuestion.getDifficulty())
                    .questionType(libQuestion.getQuestionType()).isAiCheck(libQuestion.getIsAiCheck())
                    .questionVideo(questionVideoMap.get(libQuestion.getId()))
                    .sortNo(libQuestion.getSortNo()).build();
                if (ExamQuestionTypeEnum.QUESTION_TYPE_CLOZE.getType() == libQuestion.getQuestionType()) {
                    questionDTO.setClozeAnswerNum(
                        libQuestion.getAnswer().split(ExamConstant.CLOZE_ANSWER_DELIMITER).length);
                }
                // 处理题目选项，封装成DTO
                List<LibQuestionOption> options = questionOptionListMap.get(libQuestion.getId());
                List<ExamQuestionOptionDTO> optionDTOS = new ArrayList<>();
                if (options != null && !options.isEmpty()) {
                    for (LibQuestionOption option : options) {
                        ExamQuestionOptionDTO optionDTO = ExamQuestionOptionDTO.builder()
                            .optionCode(option.getOptionCode())
                            .rightAnswer(option.getIsReference()).optionDesc(option.getOptionName())
                            .optionId(option.getId())
                            .optionImage(optionImageMap.get(option.getId()))
                            .optionIndex(option.getOptionIndex()).build();
                        optionDTOS.add(optionDTO);
                    }
                }

                // 选项是否乱序？排序？
                if (exam.getIsOptionSeq() != null && exam.getIsOptionSeq() == ExamConstant.EXAM_OPTION_SEQ) {
                    Collections.shuffle(optionDTOS);
                } else {
                    optionDTOS.sort(Comparator.comparingInt(ExamQuestionOptionDTO::getOptionIndex));
                }
                questionDTO.setOptionList(optionDTOS);
                schemaExamQuestionDTOS.add(questionDTO);
            }
            // 每个组卷方案详情抽完题，加到最终返回结果
            allExamQuestionDTOS.addAll(schemaExamQuestionDTOS);
        }
        return allExamQuestionDTOS;
    }

    @SuppressWarnings("squid:S3776")
    private List<LibQuestionQueryDTO> getLibQuestionQueryDTOS(Exam exam, List<String> libIds, SchemaDetail schemaDetail,
        List<LibConfigs> libConfigs) {
        List<LibQuestionQueryDTO> query = new ArrayList<>();
        if (1 == exam.getSchemaMode()) {
            // 按难易度组卷
            if (schemaDetail.getHighLevelNum() != null && schemaDetail.getHighLevelNum() > 0) {
                addSchemaDetail(libIds, query, schemaDetail, 2, schemaDetail.getHighLevelNum());
            }
            if (schemaDetail.getMediumLevelNum() != null && schemaDetail.getMediumLevelNum() > 0) {
                addSchemaDetail(libIds, query, schemaDetail, 1, schemaDetail.getMediumLevelNum());
            }
            if (schemaDetail.getLowLevelNum() != null && schemaDetail.getLowLevelNum() > 0) {
                addSchemaDetail(libIds, query, schemaDetail, 0, schemaDetail.getLowLevelNum());
            }
        } else {
            // 题库组卷中的题组组卷和基础组卷
            if (libConfigs != null && !libConfigs.isEmpty()) {
                // 根据每个题组配置组卷
                for (LibConfigs libConf : libConfigs) {
                    if ((ExamQuestionTypeEnum.QUESTION_TYPE_RADIO.getType() + "").equals(schemaDetail.getQuestionType())
                        && libConf.getSingle() != null
                        && libConf.getSingle() > 0) {
                        if (Objects.equals(3, exam.getSchemaMode())) {
                            addKnowledgePointsConfigs(libIds, ExamQuestionTypeEnum.QUESTION_TYPE_RADIO,
                                libConf.getSingle(), libConf, query);
                        } else {
                            addLibConfigs(query, libConf, ExamQuestionTypeEnum.QUESTION_TYPE_RADIO.getType(),
                                libConf.getSingle());
                        }
                    }
                    if ((ExamQuestionTypeEnum.QUESTION_TYPE_MULTI_SELECT.getType() + "").equals(
                        schemaDetail.getQuestionType())
                        && libConf.getMulti() != null
                        && libConf.getMulti() > 0) {
                        if (Objects.equals(3, exam.getSchemaMode())) {
                            addKnowledgePointsConfigs(libIds, ExamQuestionTypeEnum.QUESTION_TYPE_MULTI_SELECT,
                                libConf.getMulti(), libConf, query);
                        } else {
                            addLibConfigs(query, libConf, ExamQuestionTypeEnum.QUESTION_TYPE_MULTI_SELECT.getType(),
                                libConf.getMulti());
                        }
                    }
                    if ((ExamQuestionTypeEnum.QUESTION_TYPE_JUDGEMENT.getType() + "").equals(
                        schemaDetail.getQuestionType())
                        && libConf.getJudge() != null
                        && libConf.getJudge() > 0) {
                        if (Objects.equals(3, exam.getSchemaMode())) {
                            addKnowledgePointsConfigs(libIds, ExamQuestionTypeEnum.QUESTION_TYPE_JUDGEMENT,
                                libConf.getJudge(), libConf, query);
                        } else {
                            addLibConfigs(query, libConf, ExamQuestionTypeEnum.QUESTION_TYPE_JUDGEMENT.getType(),
                                libConf.getJudge());
                        }

                    }
                    if ((ExamQuestionTypeEnum.QUESTION_TYPE_CLOZE.getType() + "").equals(schemaDetail.getQuestionType())
                        && libConf.getBlanks() != null
                        && libConf.getBlanks() > 0) {
                        if (Objects.equals(3, exam.getSchemaMode())) {
                            addKnowledgePointsConfigs(libIds, ExamQuestionTypeEnum.QUESTION_TYPE_CLOZE,
                                libConf.getBlanks(), libConf, query);
                        } else {
                            addLibConfigs(query, libConf, ExamQuestionTypeEnum.QUESTION_TYPE_CLOZE.getType(),
                                libConf.getBlanks());
                        }
                    }
                    if ((ExamQuestionTypeEnum.QUESTION_TYPE_QA.getType() + "").equals(schemaDetail.getQuestionType())
                        && libConf.getQa() != null
                        && libConf.getQa() > 0) {
                        if (Objects.equals(3, exam.getSchemaMode())) {
                            addKnowledgePointsConfigs(libIds, ExamQuestionTypeEnum.QUESTION_TYPE_QA, libConf.getQa(),
                                libConf, query);
                        } else {
                            addLibConfigs(query, libConf, ExamQuestionTypeEnum.QUESTION_TYPE_QA.getType(),
                                libConf.getQa());
                        }
                    }
                }
            } else {
                // 没有各个题组配置，按原组卷方案处理
                LibQuestionQueryDTO dto = new LibQuestionQueryDTO();
                dto.setLibIds(libIds);
                dto.setQuestionType(schemaDetail.getQuestionType());
                dto.setQuestionCount(schemaDetail.getQuestionNum());
                query.add(dto);
            }
        }
        return query;
    }

    private static void addKnowledgePointsConfigs(List<String> libIds, ExamQuestionTypeEnum questionTypeQa,
        Integer libConf,
        LibConfigs libConf1, List<LibQuestionQueryDTO> query) {
        LibQuestionQueryDTO dto = new LibQuestionQueryDTO();
        dto.setLibIds(libIds);
        dto.setQuestionType(questionTypeQa.getType() + "");
        dto.setQuestionCount(libConf);
        dto.setKnowledgePoints(libConf1.getLibId());
        query.add(dto);
    }

    private void addSchemaDetail(List<String> libIds, List<LibQuestionQueryDTO> query, SchemaDetail schemaDetail, int i,
        Integer highLevelNum) {
        LibQuestionQueryDTO dto = new LibQuestionQueryDTO();
        dto.setLibIds(libIds);
        dto.setQuestionType(schemaDetail.getQuestionType());
        dto.setDifficulty(i);
        dto.setQuestionCount(highLevelNum);
        query.add(dto);
    }

    private void addLibConfigs(List<LibQuestionQueryDTO> query, LibConfigs libConf, int questionType, Integer single2) {
        List<String> ids = new ArrayList<>();
        ids.add(libConf.getLibId());
        LibQuestionQueryDTO dto = new LibQuestionQueryDTO();
        dto.setLibIds(ids);
        dto.setQuestionType(questionType + "");
        dto.setQuestionCount(single2);
        query.add(dto);
    }

    @Override
    public List<ExamCorrectExamListDTO> getCorrectExamList(Date startTime, Date endTime, String examName, String userId,
        ExamCorrectExamListQueryDTO examCorrectExamListQueryDTO) {
        return baseMapper.getCorrectExamList(startTime, endTime, examName, userId, examCorrectExamListQueryDTO);
    }

    @Override
    public List<ExamCorrectExamListDTO> getCorrectExamCashList(Collection<String> examIds) {
        return baseMapper.getCorrectExamCashList(examIds);
    }

    @Override
    public List<ViewExamFeignDTO> getExamInfoMapByExamIds(Collection<String> examIds) {
        List<ViewExamFeignDTO> examInfoMapByExamIds = baseMapper.getExamInfoMapByExamIds(examIds);
        Map<String, List<ExamReviewer>> reviewerMap = examReviewerService.getExamReviewerByExamIdBatch(examIds);
        String userId = UserThreadContext.getUserId();
        // 计算通过率
        examInfoMapByExamIds.stream().forEach(data -> {
            //是否能进行改卷,没有配置默认可以进行改卷,纯客观题不能改卷
            Integer checkExam = Objects.equals(data.getCheckPaperMethod(), 0) ? 0 : 1;
            if (data.getMemberNum() == 0) {
                data.setCheckExam(checkExam);
                return;
            }
            data.setPassRate(BigDecimal.valueOf(data.getPassNum())
                .divide(BigDecimal.valueOf(data.getMemberNum()), 4, RoundingMode.HALF_UP));
            List<ExamReviewer> reviewers = reviewerMap.get(data.getId());
            if (!CollectionUtils.isEmpty(reviewers)) {
                ExamReviewer match = reviewers.stream()
                    .filter(item -> userId.equals(item.getReviewerId()) || "ALL".equals(item.getReviewerId())).findAny()
                    .orElse(null);
                if (null == match) {
                    checkExam = 0;
                }
            }
            data.setCheckExam(checkExam);
        });
        return examInfoMapByExamIds;
    }

    @Override
    public List<ViewExamFeignDTO> getValidExamInfoMapByExamIds(Collection<String> examIds) {
        return baseMapper.getValidExamInfoMapByExamIds(examIds);
    }

    @Override
    public Integer getExamPassNum(String userId, Collection<String> examIds) {
        return baseMapper.getExamPassNum(userId, examIds);
    }

    @Override
    public CerDitchDTO getDitch(String contentId) {
        return baseMapper.getDitch(contentId);
    }

    @Override
    public List<LearningCalendarTaskDTO> findLearningCalenderExamTaskList(
        LearningCalendarTaskQuery learningCalendarTaskQuery) {
        return baseMapper.selectProTaskExamListByTime(learningCalendarTaskQuery);
    }

    @Override
    public PageInfo<SubordinateExamDTO> getSubordinateExamList(SubordinateExamQuery subordinateExamQuery) {
        //判断用户名
        UserDTO user = userFeign.getUserById(subordinateExamQuery.getUserId());
        if (Optional.ofNullable(user).isEmpty()) {
            throw new BusinessException(UserErrorNoEnum.USER_NOT_EXISTS);
        }
        //获取考试数据
        PageInfo<SubordinateExamDTO> pageInfo = PageMethod.startPage(subordinateExamQuery.getPageNo(),
                subordinateExamQuery.getPageSize())
            .doSelectPageInfo(() -> baseMapper.getSubordinateExamList(subordinateExamQuery));

        for (SubordinateExamDTO subordinateExamDTO : pageInfo.getList()) {
            if (subordinateExamDTO.getIsCheckFinish() == 1) {
                if (subordinateExamDTO.getUserScore() >= subordinateExamDTO.getPassScore()) {
                    subordinateExamDTO.setResult(1);
                } else {
                    subordinateExamDTO.setResult(-1);
                }
            } else {
                subordinateExamDTO.setResult(0);
            }
        }
        return pageInfo;
    }

    @Override
    public List<String> getInvalidExamId(Collection<String> examIdList) {
        return baseMapper.getInvalidExamId(examIdList);
    }

    @Override
    @Async
    public void exportData(ExamQueryDTO examQueryDTO) {
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IExamService, ExamListDTO>(examQueryDTO) {

            @Override
            protected IExamService getBean() {
                return SpringUtil.getBean("examService", IExamService.class);
            }

            @Override
            protected PageInfo<ExamListDTO> getPageInfo() {
                return getBean().findExamListByPage((ExamQueryDTO) queryDTO);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.Exam;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.Exam.getType();
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public void exportExerciseData(ExerciseQueryDTO examQueryDTO) {
        IExportDataDTO exportDataDTO = new AbstractExportDataDTO<IExamService, ExercisePageDTO>(examQueryDTO) {

            @Override
            protected IExamService getBean() {
                return SpringUtil.getBean("examService", IExamService.class);
            }

            @Override
            protected PageInfo<ExercisePageDTO> getPageInfo() {
                return getBean().getExercisePage((ExerciseQueryDTO) pageQueryDTO);
            }

            @Override
            public ExportBizType getType() {
                return ExportBizType.Exercise;
            }

            @Override
            public String getFileName() {
                return ExportFileNameEnum.Exercise.getType();
            }

        };

        exportComponent.exportRecord(exportDataDTO);
    }

    @Override
    public PageInfo<MyExamListDTO> searchExam(ExamSearchQuery examSearchQuery) {
        MyExamListQueryDTO myExamListQueryDTO = new MyExamListQueryDTO();
        BeanUtils.copyProperties(examSearchQuery, myExamListQueryDTO);
        //需要计数
        myExamListQueryDTO.setCount(true);
        return myExamList(myExamListQueryDTO);
    }

    @Override
    public PageInfo<com.wunding.learn.exam.api.dto.ExamInfoDTO> getMenuTask(Integer status, BaseEntity entity) {
        return PageMethod.startPage(entity.getPageNo(), entity.getPageSize())
            .doSelectPageInfo(() -> baseMapper.getMenuTask(status, entity.getCurrentUserId()));
    }

    @Override
    public Integer getMenuTaskCount(Integer status, String userId) {
        return baseMapper.getMenuTaskCount(status, userId);
    }

    @Override
    public void viewLimitChange(Integer viewType, Map<String, Long> viewLimitChangeMap) {
        log.info("viewLimitChangeByUserId:" + JsonUtil.objToJson(viewLimitChangeMap));
        for (Entry<String, Long> entry : viewLimitChangeMap.entrySet()) {
            String key = entry.getKey();
            Long value = viewLimitChangeMap.get(key);
            // 更新下发范围
            examViewLimitComponent.handleNewViewLimit(value, key);
            if (null != viewType) {
                //更新下发范围类型
                Exam saveInfo = new Exam();
                saveInfo.setId(key);
                saveInfo.setViewType(viewType);
                baseMapper.updateById(saveInfo);
            }
        }
    }

    @Override
    public com.wunding.learn.exam.api.dto.ExamInfoDTO getRealityById(String examId) {
        return baseMapper.getRealityById(examId);
    }

    @Override
    public ExamResultAnalyseDTO getExamResultAnalyse(String examId) {
        ExamResultAnalyseDTO examResultAnalyse = answerRecordService.getResultAnalyse(examId);
        // 及格率
        Integer postExamCount = examResultAnalyse.getPostExamCount();
        examResultAnalyse.setPassRate(postExamCount == 0 ? new BigDecimal(0) : getPassRate(postExamCount, examId));
        examResultAnalyse.setQuestionAnalyseList(getQuestionAnalyse(examId));
        return examResultAnalyse;
    }

    @Override
    public List<TaskAppResourceDTO> getTaskAppResourceByIds(TaskExamAppResourceQuery query) {
        return baseMapper.getTaskAppResourceByIds(query);
    }

    private List<QuestionAnalyseDTO> getQuestionAnalyse(String examId) {
        LambdaQueryWrapper<ExamQuestion> questionQuery = new LambdaQueryWrapper<>();
        questionQuery.eq(ExamQuestion::getExamId, examId);
        questionQuery.orderByAsc(ExamQuestion::getSortNo);
        List<ExamQuestion> examQuestionList = examQuestionService.list(questionQuery);
        List<QuestionAnalyseDTO> questionAnalyseList = BeanListUtils.copyList(examQuestionList,
            QuestionAnalyseDTO.class);
        // 非主观题正确率
        questionAnalyseList.stream()
            .filter(question -> !ExamQuestionCheckUtil.isSubjectiveType(question.getQuestionType()))
            .forEach(question -> {
                // 该题的答题人数
                Integer questionAnswerCount = answerRecordService.getQuestionAnswerCount(examId, question.getId());
                if (!GeneralJudgeEnum.NEGATIVE.getValue().equals(questionAnswerCount)) {
                    // 该题的答对人数
                    Integer questionCorrectAnswerCount = answerRecordService.getQuestionCorrectAnswerCount(examId,
                        question.getId());
                    DecimalFormat df = new DecimalFormat("0.0");
                    Double correctRate = questionCorrectAnswerCount / Double.valueOf(questionAnswerCount);
                    question.setCorrectRate(new BigDecimal(df.format(correctRate * 100)));
                } else {
                    question.setCorrectRate(BigDecimal.ZERO);
                }
            });
        // 填空题特殊处理题目
        questionAnalyseList.stream()
            .filter(question -> ExamQuestionCheckUtil
                .isAiCheckClozeQusetion(question.getQuestionType(), question.getIsAiCheck()))
            .forEach(
                dto -> dto.setQuestionName(StringUtil.removeAnswerInQuestionName(dto.getQuestionName()))
            );
        return questionAnalyseList;
    }

    private BigDecimal getPassRate(Integer postExamCount, String examId) {
        Integer passCount = answerRecordService.getPassCount(examId);
        DecimalFormat df = new DecimalFormat("0.0");
        Double passRate = passCount / Double.valueOf(postExamCount);
        return new BigDecimal(df.format(passRate * 100));
    }

    @Override
    public void checkExcel(String excelFile) {
        ImportDataDTO importData = importDataFeign.getImportData(excelFile);

        // excel数据行数限制 每次上传题库数量最多500条
        String[][] excel = importData.getExcel();
        if (null != excel && excel.length > 501) {
            throw new BusinessException(ExamErrorNoEnum.ERR_EXAM_QUESTION_IMPORT_COUNT_LIMIT);
        }

        ExcelTitleBaseCheckUtil.baseCheck(importData.getExcel(), ExcelTitleBaseCheckUtil.EXAM_PAPER_IMPORT_EXCEL);
    }

    /**
     * 调用推送feign
     *
     * @param saveDTO      保存实时dto
     * @param operateState 操作状态
     */
    private void sendPushFeignSave(SaveExerciseDTO saveDTO, Integer operateState) {
        SendPushDTO sendPushDTO = new SendPushDTO();
        sendPushDTO.setLang(LocaleContextHolder.getLocale().getLanguage());
        sendPushDTO.setServerName(Objects.requireNonNull(WebUtil.getRequest()).getServerName());
        PushNoticeSetDTO pushNoticeSetDTO = saveDTO.getPushNoticeSetDTO();
        sendPushDTO.setPushNoticeSetDTO(pushNoticeSetDTO);

        // 为空默认是0
        Integer isTrain = Optional.ofNullable(saveDTO.getIsTrain()).orElse(0);
        PushResourceDTO pushResourceDTO = new PushResourceDTO().setResourceId(pushNoticeSetDTO.getResourceId())
            .setIsTrain(isTrain).setOperateState(operateState).setProgrammeId(saveDTO.getProgrammeId());

        sendPushDTO.setPushResourceDTO(pushResourceDTO);

        PushAttributeDTO pushAttributeDTO = new PushAttributeDTO();

        boolean condition = isTrain != 0;
        if (condition) {
            // 非 0 证明是属于二级内容过来
            String[] name = pushNoticeSetDTO.getResourceName().split(StringPool.UNDERSCORE);
            String[] resourceId = pushNoticeSetDTO.getResourceId().split(StringPool.UNDERSCORE);
            pushAttributeDTO.setName(name[0]);
            pushAttributeDTO.setSecondaryName(name[1]);

            // 此时中 pushNoticeSetDTO 前端只会传 projectId_ 所以拼接好id
            pushResourceDTO.setResourceId(resourceId[0] + StringPool.UNDERSCORE + saveDTO.getId());
        } else {
            pushAttributeDTO.setName(pushNoticeSetDTO.getResourceName());
        }

        pushResourceDTO.setResourceName(pushNoticeSetDTO.getResourceName());
        pushResourceDTO.setResourceType(pushNoticeSetDTO.getResourceType());
        pushAttributeDTO.setExistSecondary(condition);
        pushAttributeDTO.setIntro(saveDTO.getExerciseDesc());

        @SuppressWarnings("unchecked") String systemName = (String) redisTemplate.opsForHash()
            .get(TenantRedisKeyConstant.SYSTEM_NAME_KEY, UserThreadContext.getTenantId());
        pushAttributeDTO.setSystemName(systemName);

        sendPushDTO.setPushAttributeDTO(pushAttributeDTO);

        pushFeign.sendPush(sendPushDTO);

        // 发送资源操作事件消息
        mqProducer.sendMsg(new ResourceOperateEvent(
            Objects.equals(saveDTO.getIsPublish(), GeneralJudgeEnum.NEGATIVE.getValue()) ? OperationEnum.PUBLISH_CANCEL
                : OperationEnum.PUBLISH, PushType.EXERCISE.getKey(), saveDTO.getId()));
    }

    /**
     * 调用推送feign
     *
     * @param saveDTO      保存实时dto
     * @param operateState 操作状态
     */
    private void sendPushFeignUpdate(UpdateExerciseDTO saveDTO, Integer operateState) {
        SendPushDTO sendPushDTO = new SendPushDTO();
        sendPushDTO.setLang(LocaleContextHolder.getLocale().getLanguage());
        sendPushDTO.setServerName(Objects.requireNonNull(WebUtil.getRequest()).getServerName());
        PushNoticeSetDTO pushNoticeSetDTO = saveDTO.getPushNoticeSetDTO();
        sendPushDTO.setPushNoticeSetDTO(pushNoticeSetDTO);

        // 为空默认是0
        Integer isTrain = Optional.ofNullable(saveDTO.getIsTrain()).orElse(0);
        PushResourceDTO pushResourceDTO = new PushResourceDTO().setResourceId(pushNoticeSetDTO.getResourceId())
            .setIsTrain(isTrain).setOperateState(operateState).setProgrammeId(saveDTO.getProgrammeId());

        sendPushDTO.setPushResourceDTO(pushResourceDTO);

        PushAttributeDTO pushAttributeDTO = new PushAttributeDTO();

        boolean condition = isTrain != 0;
        if (condition) {
            // 非 0 证明是属于二级内容过来
            String[] name = pushNoticeSetDTO.getResourceName().split(StringPool.UNDERSCORE);
            String[] resourceId = pushNoticeSetDTO.getResourceId().split(StringPool.UNDERSCORE);
            pushAttributeDTO.setName(name[0]);
            pushAttributeDTO.setSecondaryName(name[1]);

            // 此时中 pushNoticeSetDTO 前端只会传 projectId_ 所以拼接好id
            pushResourceDTO.setResourceId(resourceId[0] + StringPool.UNDERSCORE + saveDTO.getId());
        } else {
            pushAttributeDTO.setName(pushNoticeSetDTO.getResourceName());
        }

        pushResourceDTO.setResourceName(pushNoticeSetDTO.getResourceName());
        pushResourceDTO.setResourceType(pushNoticeSetDTO.getResourceType());
        pushAttributeDTO.setExistSecondary(condition);
        pushAttributeDTO.setIntro(saveDTO.getExerciseDesc());

        @SuppressWarnings("unchecked") String systemName = (String) redisTemplate.opsForHash()
            .get(TenantRedisKeyConstant.SYSTEM_NAME_KEY, UserThreadContext.getTenantId());
        pushAttributeDTO.setSystemName(systemName);

        sendPushDTO.setPushAttributeDTO(pushAttributeDTO);

        pushFeign.sendPush(sendPushDTO);

        // 发送资源操作事件消息
        mqProducer.sendMsg(new ResourceOperateEvent(
            Objects.equals(saveDTO.getIsPublish(), GeneralJudgeEnum.NEGATIVE.getValue()) ? OperationEnum.PUBLISH_CANCEL
                : OperationEnum.PUBLISH, PushType.EXERCISE.getKey(), saveDTO.getId()));
    }

    @Override
    public void answerSaveToRedis(String examId, Integer isIgnoreView, ExamSubmitDTO examSubmitDTO) {
        // 1 参数
        String userId = UserThreadContext.getUserId();
        Exam exam = baseMapper.selectById(examId);

        // 2 校验下发范围
        if (Objects.equals(GeneralJudgeEnum.NEGATIVE.getValue(), isIgnoreView)) {
            verifyExamViewLimit(examId, false);
        }

        // 3 校验考试是否结束、用户是否开始考试
        if (exam == null || exam.getIsPublish() == ExamConstant.EXAM_IS_NOT_PUBLISH) {
            throw new BusinessException(ExamErrorNoEnum.ERR_EXAM_IS_NOT_EXIST);
        }

        // 4 从redis中获取在考的作答记录
        AnswerRecord lastAnswerRecord = answerRecordService.getLastAnswerRecordRedis(examId, userId);
        if (null == lastAnswerRecord) {
            log.error("answerSaveToRedis redisData 找不到试题,examId:{},userId:{}", examId, userId);
            throw new BusinessException(ExamErrorNoEnum.ERR_EXAM_IS_POST);
        }
        if (Objects.equals(ExamConstant.EXAM_IS_POST, lastAnswerRecord.getIsPost())) {
            //redis中是已交卷，重复提交
            log.error("answerSaveToRedis lastAnswerRecord 在redis中是已交卷,重复提交,examId:{}, userId:{}", examId,
                userId);
            throw new BusinessException(ExamErrorNoEnum.ERR_EXAM_IS_POST);
        }

        // 5 从redis中获取在作答的试题
        OnGoingExamDTO onGoingExamDTO = getOnGoingExamDTO(examId, userId);
        if (null == onGoingExamDTO || CollectionUtils.isEmpty(onGoingExamDTO.getQuestionList())) {
            log.error("answerSaveToRedis onGoingExamDTO为空 找不到试题,examId:{},userId:{},examCompositionId:{}",
                examId,
                userId, lastAnswerRecord.getExamCompositionId());
            throw new BusinessException(ExamErrorNoEnum.ERR_EXAM_IS_POST);
        }

        //6 更新Redis中的提交答案
        answerRecordService.setUserAnswerToRedis(examSubmitDTO, lastAnswerRecord.getId());

        //7 检查考试时间,时间到则自动交卷
        long enterTime = onGoingExamDTO.getEnterTime().getTime();
        examSubmitDTO.setPostType(ExamPostTypeEnum.AUTO_SAVE_SUBMIT.getCode());
        autoSubmit(examId, userId, examSubmitDTO, enterTime, exam.getExamTimeCount());

    }

    @Override
    public void systemPostExam(String examId) {

        //redis中未交卷的异常数据(考试时间已经结束了的)，进行兜底操作

        //获取redis中的数据
        List<AnswerRecord> unSubmitAnswerRecords = answerRecordService.getUnSubmitAnswerRecordRedis();
        Set<String> examIds = new HashSet<>();
        Set<String> answerRecordIdsInRedis = new HashSet<>();
        List<AnswerRecord> answerRecordsInRedis = new ArrayList<>();
        //设置交卷类型，过滤出的待交卷的考试记录id
        int postType = setSystemPostType(examId, unSubmitAnswerRecords, answerRecordIdsInRedis, answerRecordsInRedis,
            examIds);

        //没有未交卷的异常数据
        if (CollectionUtils.isEmpty(examIds) || CollectionUtils.isEmpty(answerRecordIdsInRedis)) {
            return;
        }

        //查找考试信息
        List<Exam> exams = this.listByIds(examIds);
        Map<String, Exam> examMap = exams.stream()
            .collect(Collectors.toMap(Exam::getId, item -> item, (key1, key2) -> key1));
        //查找数据库中的作答记录
        List<AnswerRecord> answerRecordsInDb = answerRecordService.listByIds(answerRecordIdsInRedis);
        Map<String, AnswerRecord> answerRecordsInDbMap = answerRecordsInDb.stream()
            .collect(Collectors.toMap(AnswerRecord::getId, item -> item, (key1, key2) -> key1));

        for (AnswerRecord item : answerRecordsInRedis) {
            OnGoingExamDTO onGoingExamDTO = (OnGoingExamDTO) redisTemplate.opsForHash()
                .get(ExamRedisKeyEnum.ON_GOING_EXAM.getKey() + item.getUserId(), item.getExamId());
            Exam examInfo = examMap.get(item.getExamId());
            ExamSubmitDTO examSubmitDTO = answerRecordService.getUserAnswerByRedis(item.getId());
            if (null == examSubmitDTO) {
                continue;
            }
            examSubmitDTO.setPostType(postType);

            boolean isAutoSubmit = checkAutoSubmit(answerRecordsInDbMap, item, examInfo, onGoingExamDTO,
                examSubmitDTO);

            if (isAutoSubmit) {
                long enterTime = onGoingExamDTO.getEnterTime().getTime();
                long examTimeCount = examInfo.getExamTimeCount();
                try {
                    autoSubmit(item.getExamId(), item.getUserId(), examSubmitDTO, enterTime, examTimeCount);
                } catch (BusinessException e) {
                    //只抓取业务异常,业务异常说明不是代码错误，是某个考试有问题，该考试不进行交卷不影响其他考试交卷,打印日志
                    log.error("systemPostExam BusinessException,AnswerRecord:" + JsonUtil.objToJson(item) +
                        ",examSubmitDTO:" + JsonUtil.objToJson(examSubmitDTO) + ",onGoingExamDTO:" + JsonUtil.objToJson(
                        onGoingExamDTO) + " error:", e);
                    if (StringUtils.isNotBlank(examId)) {
                        //有考试id说明是前端页面触发,返回异常给前端进行反馈
                        throw e;
                    }
                }

            }

        }

    }

    /**
     * 设置交卷类型，过滤出的待交卷的考试记录id
     *
     * @param examId
     * @param unSubmitAnswerRecords
     * @param answerRecordIdsInRedis
     * @param answerRecordsInRedis
     * @param examIds
     * @return
     */
    private int setSystemPostType(String examId, List<AnswerRecord> unSubmitAnswerRecords,
        Set<String> answerRecordIdsInRedis, List<AnswerRecord> answerRecordsInRedis, Set<String> examIds) {
        int postType;
        //有条件说明是管理端按钮点击的，则根据条件过滤
        if (!StringUtils.isEmpty(examId)) {
            postType = ExamPostTypeEnum.ADMIN_SYSTEM_SUBMIT.getCode();
            for (AnswerRecord item : unSubmitAnswerRecords) {
                if (examId.equals(item.getExamId())) {
                    examIds.add(item.getExamId());
                    answerRecordsInRedis.add(item);
                    answerRecordIdsInRedis.add(item.getId());
                }
            }
        } else {
            postType = ExamPostTypeEnum.SYSTEM_JOB_SUBMIT.getCode();
            for (AnswerRecord item : unSubmitAnswerRecords) {
                examIds.add(item.getExamId());
                answerRecordsInRedis.add(item);
                answerRecordIdsInRedis.add(item.getId());
            }
        }
        return postType;
    }

    /**
     * 考试自动交卷
     *
     * @param examId
     * @param userId
     * @param examSubmitDTO
     * @param enterTime
     * @param examTimeCount
     * @return
     */
    private void autoSubmit(String examId, String userId, ExamSubmitDTO examSubmitDTO, long enterTime,
        long examTimeCount) {
        if (null == examSubmitDTO) {
            return;
        }
        // 考试时间到,自动交卷
        long nowTime = System.currentTimeMillis();
        // 考试开始到现在多久了
        long castTime = (nowTime - enterTime) / 1000;
        // 考试剩余时长
        long remainTime = examTimeCount * Duration.ofMinutes(1).getSeconds() - castTime;
        if (remainTime <= 0) {
            // 交卷时间到了。用户没有回到考试，系统仍然进行交卷操作   从Redis获取用户答题记录
            log.info("autoSubmit 考试时间到,自动交卷,examId:{},userId:{}", examId, userId);
            submit(examId, userId, examSubmitDTO);
        }
    }

    /**
     * 检查是否符合自动提交条件
     *
     * @param answerRecordsInDbMap
     * @param item
     * @param examInfo
     * @param onGoingExamDTO
     * @param examSubmitDTO
     * @return
     */
    private boolean checkAutoSubmit(Map<String, AnswerRecord> answerRecordsInDbMap, AnswerRecord item, Exam examInfo,
        OnGoingExamDTO onGoingExamDTO, ExamSubmitDTO examSubmitDTO) {
        boolean result = true;
        //1,redis中没有题目,false
        if (null == onGoingExamDTO) {
            result = false;
            return result;
        }
        //2,考试不存在,false
        if (null == examInfo) {
            result = false;
            return result;
        }
        //3,redis中没有提交答案,false
        if (null == examSubmitDTO) {
            result = false;
            return result;
        }

        //4,redis中有作答记录,数据库中没有作答记录，需要自动提交,true
        AnswerRecord dbRecord = answerRecordsInDbMap.get(item.getId());
        if (null == dbRecord) {
            return result;
        }
        return result;
    }

    @Override
    public int getSystemPostExamCount(String examId) {
        int count = 0;
        List<AnswerRecordForSystemPostDTO> list = answerRecordService.getAnswerRecordForSystemPost(examId);
        // 剩余考试时长
        long remainTime;
        long nowTime = System.currentTimeMillis();
        long enterTime;
        long castTime;
        for (AnswerRecordForSystemPostDTO item : list) {
            // 判断考试时间
            OnGoingExamDTO onGoingExamDTO = (OnGoingExamDTO) redisTemplate.opsForHash()
                .get(ExamRedisKeyEnum.ON_GOING_EXAM.getKey() + item.getUserId(), item.getExamId());
            if (null != onGoingExamDTO) {
                enterTime = onGoingExamDTO.getEnterTime().getTime();
                // 考试开始到现在多久了
                castTime = (nowTime - enterTime) / 1000;
                // 考试剩余时长
                remainTime = item.getExamTimeCount() * Duration.ofMinutes(1).getSeconds() - castTime;
                if (remainTime <= 0 && answerRecordService.getUserAnswerByRedis(item.getId()) != null) {
                    count++;
                }
            }
        }
        return count;
    }

    @Override
    public ScreenEventCountDTO getScreenEventCount(String examId) {
        String paraValue = paraFeign.getParaValue(SystemConfigCodeEnum.SYSTEM_CONFIG_CODE_702.getCode());
        // 获取屏幕限制数量
        ScreenEventCountDTO vo;
        if ("0".equals(paraValue)) {
            vo = new ScreenEventCountDTO();
            vo.setLimitScreenCuts(-1);
            vo.setLimitScreenShots(-1);
            vo.setLimitScreenRecords(-1);
        } else {
            // 参数
            String userId = UserThreadContext.getUserId();
            AnswerRecord lastAnswerRecord = answerRecordService.getLastAnswerRecord(examId, userId);
            log.info("<<<<<<<<<<<<<< getScreenEventCount, exam:{}, user:{}, time:{}", examId, userId,
                System.currentTimeMillis());
            if (lastAnswerRecord == null) {
                vo = new ScreenEventCountDTO();
                // 1 获取考试
                Exam exam = getById(examId);
                BeanUtils.copyProperties(exam, vo);
            } else {
                vo = screenEventLogService.getScreenCountByRecordId(lastAnswerRecord.getId());
            }
        }
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeExamById(String id, Integer checkPublish) {
        String[] strIds = id.split(",");
        List<String> idsList = Arrays.asList(strIds);
        List<Exam> exams = examDao.getListByIds(idsList);

        //如果考试是在发布状态中，则不能进行删除操作
        //学习项目任务删除，需要检测任务是否是发布状态，如果是发布状态则不能直接删除
        if (GeneralJudgeEnum.CONFIRM.getValue().equals(checkPublish)) {
            List<Integer> collect = exams.stream().filter(e -> e.getIsPublish() == ExamConstant.EXAM_IS_PUBLISH)
                .collect(Collectors.toList()).stream().map(Exam::getIsPublish).collect(Collectors.toList());
            if (!collect.isEmpty()) {
                throw new BusinessException(BaseErrorNoEnum.INCLUDING_IS_PUBLISH);
            }
        }

        Set<String> delExamLibIds = exams.stream()
            .filter(e -> e.getSourceType().equals(String.valueOf(ExamConstant.EXAM_TYPE_SCHEMA))).map(Exam::getId)
            .collect(Collectors.toSet());
        if (!CollectionUtils.isEmpty(delExamLibIds)) {
            examLibService.deletedByExamIds(delExamLibIds);
        }
        exams.forEach(exam -> examDao.delExam(exam));

        // 发送消息，同步状态到岗位发展管理
        ActivityStatusChangeEvent.sendMsg(idsList, ResourceTypeCodeEnum.EXAM, null, 1, mqProducer);

        exams.forEach(e -> {
            if (e.getExamType().equals(ExamTypeEnum.EXAM.getCode().toString())) {
                mqProducer.sendMsg(new ResourceOperateEvent(OperationEnum.DELETE, PushType.EXAM.getKey(), e.getId()));
            } else {
                mqProducer.sendMsg(
                    new ResourceOperateEvent(OperationEnum.DELETE, PushType.EXERCISE.getKey(), e.getId()));
            }
        });
    }

    @Override
    public List<ResourceBaseDTO> getExamBaseList(ResourceBaseQuery resourceBaseQuery) {
        return baseMapper.getExamBaseList(resourceBaseQuery);
    }

    @Override
    public void createImportUserExamRecord(List<ImportUserExamRecordDTO> recordDTOS) {
        if (CollectionUtils.isEmpty(recordDTOS)) {
            return;
        }
        Set<String> examIds = recordDTOS.stream().map(ImportUserExamRecordDTO::getExamId).collect(Collectors.toSet());
        Set<String> userIds = recordDTOS.stream().map(ImportUserExamRecordDTO::getUserId).collect(Collectors.toSet());
        // 考试信息
        Map<String, Exam> examMap = list(new LambdaQueryWrapper<Exam>().in(Exam::getId, examIds)).stream()
            .collect(Collectors.toMap(Exam::getId, Function.identity(), (k1, k2) -> k1));
        // 用户信息
        Map<String, UserDTO> userMap = userFeign.getSimpleUserMap(userIds);
        List<AnswerRecord> answerRecordList = new ArrayList<>();
        Date date = new Date();
        for (ImportUserExamRecordDTO examRecordDTO : recordDTOS) {
            String examId = examRecordDTO.getExamId();
            String userId = examRecordDTO.getUserId();
            Exam exam = examMap.get(examId);
            if (exam == null) {
                log.info("---createImportUserExamRecord----: 考试不存在: {}", examId);
                continue;
            }
            UserDTO userDTO = userMap.get(examRecordDTO.getUserId());
            if (userDTO == null) {
                log.info("---createImportUserExamRecord----: 用户不存在: {}", userId);
                continue;
            }
            AnswerRecord answerRecord = answerRecordService.getLastImportRecord(examId, userId);
            if (answerRecord == null) {
                answerRecord = new AnswerRecord();
                answerRecord.setId(newId());
                log.info("---createImportUserExamRecord----: 创建考试记录: {}", answerRecord);
            }
            answerRecord.setExamId(examId);
            answerRecord.setUserId(userId);
            answerRecord.setLevelPath(userDTO.getLevelPath());
            answerRecord.setTotalScore(exam.getTotalScore());
            answerRecord.setPassScore(exam.getPassScore());
            answerRecord.setUserScore(examRecordDTO.getScore());
            answerRecord.setStartTime(examRecordDTO.getStartTime());
            answerRecord.setIsPost(1);
            answerRecord.setPostType(1);
            answerRecord.setAnswerTime(examRecordDTO.getStartTime());
            answerRecord.setIsCheckFinish(1);
            answerRecord.setUserCheckId("admin");
            answerRecord.setCheckTime(date);
            answerRecord.setSourceType(2);
            log.info("---createImportUserExamRecord----: 考试记录: {}", JsonUtil.objToJson(answerRecord));
            answerRecordList.add(answerRecord);
        }
        if (!CollectionUtils.isEmpty(answerRecordList)) {
            log.info("---createImportUserExamRecord----: 保存考试记录: {} 条", answerRecordList.size());
            answerRecordService.saveOrUpdateBatch(answerRecordList);
        }
    }

    @Override
    public void initGenerateComposition() {
        //查找所有的考试数据
        List<Exam> examList = this.list();
        long startTime = System.currentTimeMillis();
        log.info("initGenerateComposition 开始,总数量:{}", examList.size());
        for (Exam exam : examList) {
            //进行组卷
            generateComposition(exam.getId());
        }
        log.info("initGenerateComposition 结束,总数量:{},耗时:{}", examList.size(),
            System.currentTimeMillis() - startTime);
    }

    @Override
    public List<ExamYearMonthStatDTO> getExamYearMonthStat(Integer queryType) {
        String currentUserId = UserThreadContext.getUserId();
        Set<String> managerAreaOrgIds = orgFeign.findUserManageAreaLevelPath(currentUserId);
        String currentOrgId = UserThreadContext.getOrgId();
        List<String> dateList;
        dateList = switch (queryType) {
            case 0 -> getTheRecentThirtyDays();
            case 1, 2, 3 -> getTheRecentYearMonth(queryType);
            default -> throw new IllegalStateException(UNEXPECTED_VALUE + queryType);
        };
        List<ExamYearMonthStatDTO> resultList = new ArrayList<>();
        Map<String, Integer> answerPeopleCountMap = answerRecordInThePastYearService.getAnswerPeopleCount(queryType,
            currentUserId,
            currentOrgId, managerAreaOrgIds).stream().collect(
            Collectors.toMap(ExamYearMonthStatDTO::getDate, ExamYearMonthStatDTO::getAnswerPeopleCount,
                (key1, key2) -> key1));
        Map<String, Integer> startExamCountMap = examInThePastYearService.getStartExamCount(queryType, currentUserId,
            currentOrgId, managerAreaOrgIds).stream().collect(
            Collectors.toMap(ExamYearMonthStatDTO::getDate, ExamYearMonthStatDTO::getStartExamCount,
                (key1, key2) -> key1));
        Map<String, Integer> inProgressExamCountMap = examInThePastYearService.getInProgressExamCount(queryType,
            currentUserId,
            currentOrgId, managerAreaOrgIds).stream().collect(
            Collectors.toMap(ExamYearMonthStatDTO::getDate, ExamYearMonthStatDTO::getInProgressExamCount,
                (key1, key2) -> key1));
        for (String date : dateList) {
            ExamYearMonthStatDTO result = new ExamYearMonthStatDTO();
            result.setDate(date);
            result.setAnswerPeopleCount(Optional.ofNullable(answerPeopleCountMap.get(date)).orElse(0));
            result.setStartExamCount(Optional.ofNullable(startExamCountMap.get(date)).orElse(0));
            result.setInProgressExamCount(Optional.ofNullable(inProgressExamCountMap.get(date)).orElse(0));
            resultList.add(result);
        }
        return resultList;
    }


    @Override
    public List<ExamUserScoreDTO> getUserScoreInfo(String userId, Collection<String> examIds) {
        return baseMapper.getUserScoreInfo(userId, examIds);
    }

    private List<String> getTheRecentYearMonth(Integer queryType) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        int recentMonth = switch (queryType) {
            case 1 -> 3;
            case 2 -> 6;
            case 3 -> 12;
            default -> throw new IllegalStateException(UNEXPECTED_VALUE + queryType);
        };
        return IntStream.rangeClosed(1, recentMonth)  // 1 到 12
            .mapToObj(i -> YearMonth.now().minusMonths(recentMonth - i))   // 从12个月前到当前月
            .map(month -> month.format(formatter))                // 格式化为 yyyy-MM
            .toList();
    }

    private List<String> getTheRecentThirtyDays() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return IntStream.rangeClosed(1, 30)  // 1 到 30
            .mapToObj(i -> LocalDate.now().minusDays(30 - i))   // 先减30，最后减1
            .map(date -> date.format(formatter))
            .toList();
    }
}
