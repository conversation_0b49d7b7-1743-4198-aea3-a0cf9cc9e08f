package com.wunding.learn.exam.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.bean.BaseEntity;
import com.wunding.learn.common.bean.BasePageQuery;
import com.wunding.learn.common.dto.CerDitchDTO;
import com.wunding.learn.common.dto.ForImportExamDTO;
import com.wunding.learn.common.dto.LearningCalendarTaskDTO;
import com.wunding.learn.common.dto.LearningCalendarTaskQuery;
import com.wunding.learn.common.dto.ResourceBaseDTO;
import com.wunding.learn.common.dto.TaskAppResourceDTO;
import com.wunding.learn.common.query.ResourceBaseQuery;
import com.wunding.learn.exam.api.dto.ExamUserScoreDTO;
import com.wunding.learn.exam.api.dto.ImportUserExamRecordDTO;
import com.wunding.learn.exam.api.dto.ViewExamFeignDTO;
import com.wunding.learn.exam.api.query.TaskExamAppResourceQuery;
import com.wunding.learn.exam.service.admin.dto.ExamAnalysisQuestionDTO;
import com.wunding.learn.exam.service.admin.dto.ExamCorrectExamListDTO;
import com.wunding.learn.exam.service.admin.dto.ExamCorrectExamListQueryDTO;
import com.wunding.learn.exam.service.admin.dto.ExamListDTO;
import com.wunding.learn.exam.service.admin.dto.ExamQueryDTO;
import com.wunding.learn.exam.service.admin.dto.ExamSchemaDetailMatrixDTO;
import com.wunding.learn.exam.service.admin.dto.ExamYearMonthStatDTO;
import com.wunding.learn.exam.service.admin.dto.ExerciseInfoDTO;
import com.wunding.learn.exam.service.admin.dto.ExercisePageDTO;
import com.wunding.learn.exam.service.admin.dto.ExerciseQueryDTO;
import com.wunding.learn.exam.service.admin.dto.PreviewExamDTO;
import com.wunding.learn.exam.service.admin.dto.PreviewExerciseDTO;
import com.wunding.learn.exam.service.admin.dto.PublishExamDTO;
import com.wunding.learn.exam.service.admin.dto.SaveExamDTO;
import com.wunding.learn.exam.service.admin.dto.SaveExerciseDTO;
import com.wunding.learn.exam.service.admin.dto.UpdateExerciseDTO;
import com.wunding.learn.exam.service.admin.dto.ViewExamDTO;
import com.wunding.learn.exam.service.client.dto.ExamInfoDTO;
import com.wunding.learn.exam.service.client.dto.ExamQuestionDTO;
import com.wunding.learn.exam.service.client.dto.ExamQuestionListDTO;
import com.wunding.learn.exam.service.client.dto.ExamRankMyDTO;
import com.wunding.learn.exam.service.client.dto.ExamRankUserDTO;
import com.wunding.learn.exam.service.client.dto.ExamResultAnalyseDTO;
import com.wunding.learn.exam.service.client.dto.ExamSubmitDTO;
import com.wunding.learn.exam.service.client.dto.ExamSystemHandleQuestionDTO;
import com.wunding.learn.exam.service.client.dto.ExerciserInfoDTO;
import com.wunding.learn.exam.service.client.dto.ExerciserQuestionListDTO;
import com.wunding.learn.exam.service.client.dto.MyExamAnalysisDTO;
import com.wunding.learn.exam.service.client.dto.MyExamListDTO;
import com.wunding.learn.exam.service.client.dto.MyExamListQueryDTO;
import com.wunding.learn.exam.service.client.dto.MyExamQuestionAnalysisDTO;
import com.wunding.learn.exam.service.client.dto.MyExerciserDTO;
import com.wunding.learn.exam.service.client.dto.MyExerciserListQueryDTO;
import com.wunding.learn.exam.service.client.dto.ScreenEventCountDTO;
import com.wunding.learn.exam.service.client.dto.SubordinateExamDTO;
import com.wunding.learn.exam.service.client.query.ExamSearchQuery;
import com.wunding.learn.exam.service.client.query.SubordinateExamQuery;
import com.wunding.learn.exam.service.model.AnswerRecord;
import com.wunding.learn.exam.service.model.Exam;
import com.wunding.learn.exam.service.model.SchemaDetail;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.springframework.scheduling.annotation.Async;

/**
 * 考试表 服务类
 *
 * <AUTHOR> href="mailto:<EMAIL>">xiao8</a>
 * @date 2022-03-19
 */
public interface IExamService extends IService<Exam> {

    /**
     * 后台-获取考试列表
     *
     * @param examQueryDTO 参数对象
     * @return 考试列表
     */
    PageInfo<ExamListDTO> findExamListByPage(ExamQueryDTO examQueryDTO);

    /**
     * 获取考试列表
     *
     * @param examQueryDTO
     * @return
     */
    List<ExamListDTO> findExamList(ExamQueryDTO examQueryDTO);

    /**
     * 后台-保存/更新考试
     *
     * @param saveExamDTO 参数对象
     * @return
     */
    Exam saveOrUpdateExam(SaveExamDTO saveExamDTO);


    /**
     * 处理考试题目
     *
     * @param saveExamDTO
     * @param exam
     * @return
     */
    Exam handleSourceType(SaveExamDTO saveExamDTO, Exam exam);


    /**
     * 保存题库组卷
     *
     * @param saveExamDTO
     * @param exam
     */
    void saveExamLib(SaveExamDTO saveExamDTO, Exam exam);

    /**
     * 设置阅卷方式 0-系统阅卷 1-人工改卷
     *
     * @param examId
     * @return
     */
    void setExamCheckPaperMethod(String examId);

    /**
     * 根据ids删除考试
     *
     * @param ids 考试id
     */
    void delExamById(String ids);

    /**
     * 根据ids删除练习
     *
     * @param ids 考试id
     */
    void delExerciseById(String ids);

    /**
     * 不检查，根据ids删除考试
     *
     * @param ids
     */
    void delNotCheckExamById(String ids);

    /**
     * 后台-发布考试
     *
     * @param examPublishDTO 参数对象
     */
    void publish(PublishExamDTO examPublishDTO);

    /**
     * 后台-发布练习
     *
     * @param examPublishDTO 参数对象
     */
    void publishExercise(PublishExamDTO examPublishDTO);

    /**
     * 管理端-获得某个考试
     *
     * @param id 考试id
     * @return 考试信息
     */
    ViewExamDTO getExamById(String id);

    /**
     * 生成考试编号
     *
     * @return 考试编号
     */
    String generateCode();

    /**
     * 学员端-获取我的考试列表
     *
     * @param myExamListQueryDTO 查询条件
     * @return 考试列表
     */
    PageInfo<MyExamListDTO> myExamList(MyExamListQueryDTO myExamListQueryDTO);

    /**
     * 学员端-考试排名
     *
     * @param examId        考试id
     * @param basePageQuery 查询条件
     * @return 排名列表
     */
    PageInfo<ExamRankUserDTO> rank(String examId, BasePageQuery basePageQuery);

    /**
     * 学员端-考试排名-
     *
     * @param examId
     * @return 我的排名信息
     */
    ExamRankMyDTO myRank(String examId);

    /**
     * 学员端--根据考试id查询考试详情
     *
     * @param examId       考试id
     * @param isIgnoreView 是否忽略可见范围：0-否 1-是
     * @return 考试详情
     */
    ExamInfoDTO getExamInfo(String examId, Integer isIgnoreView);

    /**
     * 学员端--根据考试id(与练习通用) 查练习详情
     *
     * @param examId       练习id
     * @param isIgnoreView 是否忽略可见范围：0-否 1-是
     * @return 练习详情
     */
    ExerciserInfoDTO getExerciserInfo(String examId, Integer isIgnoreView);

    /**
     * 学员端-开始考试-获取考试题目列表(开始答题)
     *
     * @param examId       考试id
     * @param version      版本号
     * @param isIgnoreView 是否忽略可见范围：0-否 1-是
     * @return 考试题目列表
     */
    ExamQuestionListDTO getExamQuestionList(String examId, String version, Integer isIgnoreView);


    /**
     * 异步保存作答记录
     *
     * @param answerRecord
     * @return
     */
    @Async
    void asyncSaveAnswerRecord(AnswerRecord answerRecord);


    /**
     * 根据考试ID获取 组卷的题目
     *
     * @param exam
     * @return
     */
    List<ExamQuestionDTO> getExamSchemaQuestionDTOS2(Exam exam);


    /**
     * 学员端-获取考试结果和解析
     *
     * @param examId       考试id
     * @param isIgnoreView 是否忽略可见范围：0-否 1-是
     * @return {@link MyExamAnalysisDTO}
     */
    MyExamAnalysisDTO getExamResult(String examId, Integer isIgnoreView, String enterType);

    /**
     * 学员端-我的练习列表
     *
     * @param myExerciserListQueryDTO
     * @return
     */
    PageInfo<MyExerciserDTO> myExerciserList(MyExerciserListQueryDTO myExerciserListQueryDTO);

    /**
     * 学员端-获取练习题目
     *
     * @param examId       练习id
     * @param isIgnoreView 是否忽略可见范围：0-否 1-是
     * @return {@link ExerciserQuestionListDTO}
     */
    ExerciserQuestionListDTO getExerciserQuestionList(String examId, Integer isIgnoreView);

    /**
     * 学员端-提交考试
     *
     * @param
     */
    void submit(String examId, String userId, ExamSubmitDTO examSubmitDTO);

    /**
     * 系统阅卷-消费者执行体
     *
     * @param examSystemHandleQuestionDTO
     * @return
     */
    void examSystemHandleQuestionConsumerExecute(ExamSystemHandleQuestionDTO examSystemHandleQuestionDTO);

    /**
     * 获取考试/练习题目总数
     *
     * @param examId
     * @return
     */
    int getExamQuestionCount(String examId);

    /**
     * 获取考试/练习题目总数
     *
     * @param examIds 考试id集合
     * @return 考试/练习题目总数
     */
    Map<String, Integer> getExamQuestionCountMap(Collection<String> examIds);


    /**
     * 校验用户是否可见当前考试
     *
     * @param examId 考试id
     */
    void verifyExamViewLimit(String examId, boolean isExercise);

    /**
     * 查考试交卷人数
     *
     * @param examId 考试id
     * @return
     */
    int getCompleteCountByExamId(String examId);

    /**
     * 查考试交卷人数
     *
     * @param examIds 考试id
     * @return 交卷人数
     */
    Map<String, Integer> getCompleteCountByExamId(Collection<String> examIds);

    /**
     * 后台-练习管理-练习列表
     *
     * @param examQueryDTO 查询参数
     * @return 分页数据
     */
    PageInfo<ExercisePageDTO> getExercisePage(ExerciseQueryDTO examQueryDTO);

    /**
     * 后台-练习管理-添加练习
     *
     * @param saveExerciseDTO 表单信息
     */
    String saveExercise(SaveExerciseDTO saveExerciseDTO);

    /**
     * 后台-练习管理-编辑练习前查看练习信息
     *
     * @param id 练习id
     * @return
     */
    ExerciseInfoDTO getExerciseById(String id);

    /**
     * 后台-练习管理-编辑练习
     *
     * @param id                练习id
     * @param updateExerciseDTO 表单
     */
    void updateExercise(String id, UpdateExerciseDTO updateExerciseDTO);

    /**
     * 预览练习
     *
     * @param id 练习id
     * @return 预览对象
     */
    PreviewExerciseDTO previewExerciseById(String id);

    /**
     * 预览考试
     *
     * @param id 考试id
     * @return 预览考试对象
     */
    PreviewExamDTO previewExamById(String id);

    /**
     * 查看答案和解析-获取考试中的题目、选项、答案、我的作答
     *
     * @param examId           考试id
     * @param lastAnswerRecord 最后一次答题记录
     * @param canView          是否可以查看答案
     * @return 答案和解析
     */
    List<MyExamQuestionAnalysisDTO> getMyExamQuestionAnalysisDTOS(String examId, AnswerRecord lastAnswerRecord,
        boolean canView);

    /**
     * 获取某个考试/练习的题目和选项、不包括答案
     *
     * @param examId
     * @return
     */
    List<ExamQuestionDTO> getExamImportQuestionDTOS(String examId);

    /**
     * 获取某个考试/练习的题目和选项、包括答案
     *
     * @param examId
     * @return
     */
    List<ExamAnalysisQuestionDTO> getAnalysisQuestionDTO(String examId);


    /**
     * 考试完成情况
     *
     * @param userId 用户id
     * @param examId 考试id
     * @return 完成状态
     */
    Integer getExamFinishStatus(String userId, String examId);

    /**
     * 获取待改卷的考试列表
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param examName  考试名称
     * @param userId    用户id
     * @return {@link List}<{@link Exam}>
     */
    List<ExamCorrectExamListDTO> getCorrectExamList(Date startTime, Date endTime, String examName, String userId,
        ExamCorrectExamListQueryDTO examCorrectExamListQueryDTO);

    /**
     * 获取改卷考试缓存信息
     *
     * @param examIds
     * @return
     */
    List<ExamCorrectExamListDTO> getCorrectExamCashList(Collection<String> examIds);

    /**
     * 批量获取考试(不过滤已删除的)
     *
     * @param examIds
     * @return
     */
    List<ViewExamFeignDTO> getExamInfoMapByExamIds(Collection<String> examIds);

    /**
     * 批量获取考试(过滤已删除的)
     *
     * @param examIds
     * @return
     */
    List<ViewExamFeignDTO> getValidExamInfoMapByExamIds(Collection<String> examIds);

    /**
     * 获取用户指定考试中的及格数
     *
     * @param userId
     * @param examIds
     * @return
     */
    Integer getExamPassNum(String userId, Collection<String> examIds);

    /**
     * 根据渠道id 获取一个渠道信息
     *
     * @param contentId 内容识别
     * @return {@link CerDitchDTO}
     */
    CerDitchDTO getDitch(String contentId);


    /**
     * 查询考试任务列表
     *
     * @param learningCalendarTaskQuery
     * @return
     */
    List<LearningCalendarTaskDTO> findLearningCalenderExamTaskList(LearningCalendarTaskQuery learningCalendarTaskQuery);

    /**
     * * 234 获取下属考试列表
     *
     * @param subordinateExamQuery 下属考试查询对象
     * @return 下属考试结果
     */
    PageInfo<SubordinateExamDTO> getSubordinateExamList(SubordinateExamQuery subordinateExamQuery);

    /**
     * 过滤获取已被删除的资源id
     *
     * @param examIdList
     * @return
     */
    List<String> getInvalidExamId(Collection<String> examIdList);

    /**
     * 导出考试管理列表
     */
    void exportData(ExamQueryDTO examQueryDTO);

    /**
     * 导出练习管理列表
     */
    void exportExerciseData(ExerciseQueryDTO examQueryDTO);


    /**
     * 搜索考试
     *
     * @param examSearchQuery
     * @return
     */
    PageInfo<MyExamListDTO> searchExam(ExamSearchQuery examSearchQuery);

    /**
     * 根据状态获取任务列表 考试任务部分
     *
     * @param status
     * @param entity
     * @return
     */
    PageInfo<com.wunding.learn.exam.api.dto.ExamInfoDTO> getMenuTask(Integer status, BaseEntity entity);

    /**
     * 根据状态获取任务列表 考试任务部分总数
     *
     * @param status
     * @param userId
     * @return
     */
    Integer getMenuTaskCount(Integer status, String userId);

    /**
     * 下发范围，用户ID 变更
     *
     * @param viewLimitChangeMap
     */
    void viewLimitChange(Integer viewType, Map<String, Long> viewLimitChangeMap);

    /**
     * 获取单个考试信息(不过滤删除)
     *
     * @param examId
     * @return
     */
    com.wunding.learn.exam.api.dto.ExamInfoDTO getRealityById(String examId);

    /**
     * 查看考试结果
     *
     * @param examId
     * @return
     */
    ExamResultAnalyseDTO getExamResultAnalyse(String examId);

    /**
     * 获取任务考试应用信息
     *
     * @param query
     * @return
     */
    List<TaskAppResourceDTO> getTaskAppResourceByIds(TaskExamAppResourceQuery query);

    /**
     * 检查导入文件
     *
     * @param excelFile
     */
    void checkExcel(String excelFile);

    /**
     * 保存答题记录到Redis
     *
     * @param examId        考试id
     * @param isIgnoreView  是否忽略可见范围：0-否 1-是
     * @param examSubmitDTO 考试提交内容对象
     */
    void answerSaveToRedis(String examId, Integer isIgnoreView, ExamSubmitDTO examSubmitDTO);

    /**
     * 系统交卷
     */
    void systemPostExam(String examId);

    /**
     * 获取可以系统交卷的人数
     *
     * @param examId
     * @return
     */
    int getSystemPostExamCount(String examId);

    /**
     * 获取屏幕事件相关数量
     *
     * @param examId
     * @return
     */
    ScreenEventCountDTO getScreenEventCount(String examId);

    /**
     * 根据ids删除考试
     *
     * @param id           考试id
     * @param checkPublish 是否检测发布状态 0=不检测发布状态 1=检测发布状态
     */
    void removeExamById(String id, Integer checkPublish);

    /**
     * 填充矩阵数据-组卷详情数据
     */
    void setExamShemaDetailDTO(ExamSchemaDetailMatrixDTO dto1, ExamSchemaDetailMatrixDTO dto2,
        ExamSchemaDetailMatrixDTO dto3, ExamSchemaDetailMatrixDTO dto4, ExamSchemaDetailMatrixDTO dto5,
        ExamSchemaDetailMatrixDTO dto6, SchemaDetail schemaDetail);

    /**
     * 获取考试基本信息
     *
     * @param resourceBaseQuery
     * @return
     */
    List<ResourceBaseDTO> getExamBaseList(ResourceBaseQuery resourceBaseQuery);

    /**
     * 创建导入用户考试记录
     *
     * @param recordDTOS
     */
    void createImportUserExamRecord(List<ImportUserExamRecordDTO> recordDTOS);


    /**
     * 生成考试组卷方案
     *
     * @param examId
     * @return
     */
    Integer generateComposition(String examId);

    /**
     * 自动发布
     *
     * @param examId
     * @return
     */
    void autoPublish(String examId, String publishBy);

    /**
     * 修改组卷状态
     *
     * @param examId
     * @param compositionStatus
     * @return
     */
    void changeExamCompositionStatus(String examId, Integer compositionStatus);

    /**
     * 初始化历史考试组卷定时任务(只执行一次)
     *
     * @param
     * @return
     */
    void initGenerateComposition();

    /**
     * 检查考试是否需要重新进行组卷 重新生成组卷方案条件： 1,考试之前组卷失败,需要进行组卷 2,考试正在组卷中，不需要进行组卷 3,考试没有在进行组卷,数据库中没有组卷方案;
     * 4,考试没有在进行组卷,数据库中组卷方案数量与当前考试的配置组卷方案数量不一致 5,组卷方式是题库组卷，组卷方案发生变化
     *
     * @param examId 考试id
     * @return
     */
    boolean checkIsGenerate(String examId);

    /**
     * 处理重新组卷或生成考试组卷方案及自动发布
     *
     * @param isSourceTypeImportExam 是否是导入试卷类型
     * @param exam                   考试实体
     * @param curUserId              当前用户id
     */
    void handleGenerateExam(boolean isSourceTypeImportExam, Exam exam, String curUserId);

    /**
     * 获得导入Excel题目文件详情
     *
     * @param excelFileUrl
     * @return
     */
    ForImportExamDTO getExerciseExcelInfo(String excelFileUrl);

    /**
     * 获取考试情况统计
     *
     * @param queryType 查询类型：0-最近30天 1-最近3个月 2-最近6个月 3-最近12个月
     * @return {@link ExamYearMonthStatDTO}
     */
    List<ExamYearMonthStatDTO> getExamYearMonthStat(Integer queryType);

    /**
     * 用户考试得分（已交卷、已改卷、最新考试记录的及格分和用户得分）
     *
     * @param userId  用户id
     * @param examIds 考试id集合
     * @return 及格分和用户得分
     */
    List<ExamUserScoreDTO> getUserScoreInfo(String userId, Collection<String> examIds);
}
