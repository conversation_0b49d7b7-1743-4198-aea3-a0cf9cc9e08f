package com.wunding.learn.exam.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/5/23
 */
@Data
@Accessors(chain = true)
@Schema(name = "ExamUserByBizDTO", description = "根据业务查询到的考试用户数据对象")
public class ExamUserByBizDTO {

    @Schema(description = "用户id")
    private String id;


    /**
     * 用户账号
     */
    @Schema(description = "用户账号")
    private String loginName;


    /**
     * 岗位
     */
    @Schema(description = "岗位")
    private String postName;

    /**
     * 姓名
     */
    @Schema(description = "姓名")
    private String fullName;

    /**
     * 考试成绩
     */
    @Schema(description = "考试成绩")
    private BigDecimal examScore;

    /**
     * 考试成绩
     */
    @Schema(description = "考试总分")
    private BigDecimal score;


    /**
     * 所属部门
     */
    @Schema(description = "所属部门")
    private String orgName;

    /**
     * 组织全名称
     */
    @Schema(description = "组织全名称")
    private String levelPathName;

    /**
     * 是否交卷 0否 1是
     */
    @Schema(description = "是否交卷 0否 1是")
    private Integer isPost;


    @Schema(description = "交卷时间")
    private Date answerTime;

    @Schema(description = "交卷次数")
    private Integer answerCount;

    @Schema(description = "剩余考试次数")
    private Integer residueAnswerCount;

    @Schema(description = "首次成绩")
    private BigDecimal firstScore;

    @Schema(description = "最终成绩")
    private BigDecimal finalScore;



    /**
     * 是否完成改卷 0否 1是
     */
    @Schema(description = "是否完成改卷 0否 1是")
    private Integer isCheckFinish;

    @Schema(description = "部门全称 orgPath")
    private String orgPath;
}
