package com.wunding.learn.exam.service.client.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * </p> 屏幕事件记录表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(name = "ScreenEventLog对象", description = "屏幕事件记录表")
public class AnswerRecordForSystemPostDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 答题记录id
     */
    @Schema(description = "答题记录id")
    private String id;

    /**
     * 考试ID
     */
    @Schema(description = "考试ID")
    private String examId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private String userId;

    /**
     * 答题开始时间
     */
    @Schema(description = "答题开始时间")
    private Date startTime;

    /**
     * 答题开始时间
     */
    @Schema(description = "考试开始时间")
    private Date examStartTime;

    /**
     * 答题开始时间
     */
    @Schema(description = "考试结束时间")
    private Date examEndTime;

    /**
     * 考试时长
     */
    @Schema(description = "考试时长")
    private Long examTimeCount;
}
