package com.wunding.learn.exam.service.feign;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageInfo;
import com.wunding.learn.common.bean.BaseEntity;
import com.wunding.learn.common.bean.Result;
import com.wunding.learn.common.context.user.UserThreadContext;
import com.wunding.learn.common.dto.CerDitchDTO;
import com.wunding.learn.common.dto.CertificationContentDTO;
import com.wunding.learn.common.dto.CompleteNumQuery;
import com.wunding.learn.common.dto.LearningCalendarTaskDTO;
import com.wunding.learn.common.dto.LearningCalendarTaskQuery;
import com.wunding.learn.common.dto.ResourceBaseDTO;
import com.wunding.learn.common.dto.ResourceDeleteInfoDTO;
import com.wunding.learn.common.dto.ResourceIdAndUserIdDTO;
import com.wunding.learn.common.dto.ResourceMemberDTO;
import com.wunding.learn.common.dto.ResourceUserDTO;
import com.wunding.learn.common.dto.TaskAppResourceDTO;
import com.wunding.learn.common.enums.other.GeneralJudgeEnum;
import com.wunding.learn.common.enums.other.PublishStatusEnum;
import com.wunding.learn.common.query.ResourceBaseQuery;
import com.wunding.learn.common.query.ResourceUserQuery;
import com.wunding.learn.common.util.bean.BeanListUtils;
import com.wunding.learn.exam.api.dto.AiQuestionExamFeignDTO;
import com.wunding.learn.exam.api.dto.ExamInfoDTO;
import com.wunding.learn.exam.api.dto.ExamScoreDTO;
import com.wunding.learn.exam.api.dto.ExamTimeDTO;
import com.wunding.learn.exam.api.dto.ExamUserScoreDTO;
import com.wunding.learn.exam.api.dto.ImportUserExamRecordDTO;
import com.wunding.learn.exam.api.dto.ViewExamFeignDTO;
import com.wunding.learn.exam.api.query.ExamQuery;
import com.wunding.learn.exam.api.query.ExamScoreQuery;
import com.wunding.learn.exam.api.query.TaskExamAppResourceQuery;
import com.wunding.learn.exam.api.query.UserIdpStatisticQuery;
import com.wunding.learn.exam.api.service.ExamFeign;
import com.wunding.learn.exam.service.admin.dto.ExamListDTO;
import com.wunding.learn.exam.service.admin.dto.ExamQueryDTO;
import com.wunding.learn.exam.service.admin.dto.PublishExamDTO;
import com.wunding.learn.exam.service.component.ExamViewLimitComponent;
import com.wunding.learn.exam.service.constant.ExamConstant;
import com.wunding.learn.exam.service.dao.ExamDao;
import com.wunding.learn.exam.service.dao.ExamQuestionDao;
import com.wunding.learn.exam.service.mapper.AnswerRecordMapper;
import com.wunding.learn.exam.service.mapper.ExamMapper;
import com.wunding.learn.exam.service.model.AnswerRecord;
import com.wunding.learn.exam.service.model.Exam;
import com.wunding.learn.exam.service.model.LibQuestionOption;
import com.wunding.learn.exam.service.model.Library;
import com.wunding.learn.exam.service.service.IAnswerRecordService;
import com.wunding.learn.exam.service.service.IExamService;
import com.wunding.learn.exam.service.service.IExamViewLimitService;
import com.wunding.learn.exam.service.service.ILibQuestionOptionService;
import com.wunding.learn.exam.service.service.ILibQuestionService;
import com.wunding.learn.exam.service.service.ILibraryService;
import com.wunding.learn.user.api.dto.MemberCardFeignDTO;
import com.wunding.learn.user.api.dto.viewlimit.ViewLimitBaseInfoDTO;
import com.wunding.learn.user.api.service.MemberCardFeign;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @create 2022/5/11 16:14
 * @description
 */
@RestController
@RequestMapping("${module.exam.contentPath:/}")
public class ExamFeignImpl implements ExamFeign {

    @Resource
    private IExamService examService;
    @Resource
    private IExamViewLimitService examViewLimitService;
    @Resource
    private IAnswerRecordService answerRecordService;
    @Resource
    private ExamViewLimitComponent examViewLimitComponent;
    @Resource
    private ExamMapper examMapper;
    @Resource
    private AnswerRecordMapper answerRecordMapper;
    @Resource(name = "examDao")
    private ExamDao examDao;
    @Resource(name = "examQuestionDao")
    private ExamQuestionDao examQuestionDao;
    @Resource
    private MemberCardFeign memberCardFeign;

    @Resource
    private ILibQuestionService libQuestionService;

    @Resource
    private ILibraryService libraryService;

    @Resource
    private ILibQuestionOptionService libQuestionOptionService;

    @Override
    public ViewExamFeignDTO getExamInfoById(ExamQuery examQuery) {
        ViewExamFeignDTO viewExamFeignDTO = new ViewExamFeignDTO();
        if (StringUtils.isBlank(examQuery.getId()) && CollectionUtils.isEmpty(examQuery.getExamIds())) {
            return viewExamFeignDTO;
        }
        ExamQueryDTO examQueryDTO = new ExamQueryDTO();
        BeanUtils.copyProperties(examQuery, examQueryDTO);
        PageInfo<ExamListDTO> examListByPage = examService.findExamListByPage(examQueryDTO);
        List<ExamListDTO> list = examListByPage.getList();
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        BeanUtils.copyProperties(list.get(0), viewExamFeignDTO);
        return viewExamFeignDTO;
    }

    @Override
    public Map<String, ViewExamFeignDTO> getExamInfoMapByIds(ExamQuery examQuery) {
        if (StringUtils.isBlank(examQuery.getId()) && CollectionUtils.isEmpty(examQuery.getExamIds())) {
            return new HashMap<>();
        }
        ExamQueryDTO examQueryDTO = new ExamQueryDTO();
        BeanUtils.copyProperties(examQuery, examQueryDTO);
        examQueryDTO.setExamIds(examQuery.getExamIds());
        List<ExamListDTO> examList = examService.findExamList(examQueryDTO);
        List<ViewExamFeignDTO> viewExamFeignList = BeanListUtils.copyList(examList, ViewExamFeignDTO.class);
        Map<String, ViewExamFeignDTO> collect = viewExamFeignList.stream()
            .collect(Collectors.toMap(ViewExamFeignDTO::getId, dto -> dto));
        return collect;
    }

    @Override
    public List<String> getEffectiveExamIds(Integer examType) {
        LambdaQueryWrapper<Exam> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Exam::getId)
            .eq(Exam::getIsDel, 0)
            .eq(Exam::getExamType, examType);
        if (Integer.valueOf(1).equals(examType)) {
            // 只有考试才有结束时间，练习没有开始时间和结束时间
            queryWrapper.gt(Exam::getEndTime, new Date());
        }

        List<Exam> examList = examService.getBaseMapper().selectList(queryWrapper);
        return examList.stream().map(Exam::getId).collect(Collectors.toList());
    }

    @Override
    public Integer getExamFinishStatus(String userId, String examId) {
        return examService.getExamFinishStatus(userId, examId);
    }


    @Override
    public List<ExamUserScoreDTO> getUserScoreInfo(String userId, Collection<String> examIds) {
        return examService.getUserScoreInfo(userId, examIds);
    }

    @Override
    public Map<String, Integer> getExamFinishStatusByExamIds(String userId, Collection<String> examIds) {
        Map<String, Integer> map = new HashMap<>();
        if (!CollectionUtils.isEmpty(examIds)) {
            examIds.forEach(dto -> {
                Integer examFinishStatus = examService.getExamFinishStatus(userId, dto);
                if (examFinishStatus != null) {
                    map.put(dto, examFinishStatus);
                }
            });
        }
        return map;
    }

    @Override
    public Map<String, ViewExamFeignDTO> getExamInfoMapByExamIds(Collection<String> examIds) {
        if (CollectionUtils.isEmpty(examIds)) {
            return new HashMap<>(1);
        }
        List<ViewExamFeignDTO> viewExamFeignList = examService.getExamInfoMapByExamIds(examIds);
        return viewExamFeignList.stream().collect(Collectors.toMap(ViewExamFeignDTO::getId, dto -> dto));
    }

    @Override
    public Map<String, ViewExamFeignDTO> getValidExamInfoMapByExamIds(Collection<String> examIds) {
        if (CollectionUtils.isEmpty(examIds)) {
            return new HashMap<>(1);
        }
        List<ViewExamFeignDTO> viewExamFeignList = examService.getValidExamInfoMapByExamIds(examIds);
        return viewExamFeignList.stream().collect(Collectors.toMap(ViewExamFeignDTO::getId, dto -> dto));
    }


    @Override
    public Map<String, Long> getPostExamCountMapByExamIds(Collection<String> examIds) {
        if (CollectionUtils.isEmpty(examIds)) {
            return new HashMap<>(1);
        }
        Map<String, Long> postExamCount = new HashMap<>();
        examIds.forEach(examId -> {
            LambdaQueryWrapper<AnswerRecord> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AnswerRecord::getExamId, examId);
            queryWrapper.eq(AnswerRecord::getIsPost, 1);
            queryWrapper.eq(AnswerRecord::getValidStatus, 0);
            postExamCount.put(examId, answerRecordService.count(queryWrapper));
        });
        return postExamCount;
    }

    @Override
    public Map<String, Long> getCheckExamCountMapByExamIds(Collection<String> examIds) {
        if (CollectionUtils.isEmpty(examIds)) {
            return new HashMap<>(1);
        }
        Map<String, Long> checkExamCount = new HashMap<>();
        examIds.forEach(examId -> {
            LambdaQueryWrapper<AnswerRecord> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AnswerRecord::getExamId, examId);
            queryWrapper.eq(AnswerRecord::getIsMax, 1);
            checkExamCount.put(examId, answerRecordService.count(queryWrapper));
        });
        return checkExamCount;
    }

    @Override
    public ExamInfoDTO getById(String examId) {
        Exam exam = examService.getById(examId);
        if (exam == null) {
            return null;
        }
        ExamInfoDTO examInfoDTO = new ExamInfoDTO();
        BeanUtils.copyProperties(exam, examInfoDTO);
        ViewLimitBaseInfoDTO viewLimitBaseInfo = examViewLimitComponent.getViewLimitBaseInfo(examId);
        examInfoDTO.setProgrammeId(viewLimitBaseInfo.getProgrammeId());
        return examInfoDTO;
    }

    @Override
    public ExamInfoDTO getRealityById(String examId) {
        ExamInfoDTO examInfoDTO = examService.getRealityById(examId);
        if (examInfoDTO == null) {
            return null;
        }
//        Optional<ViewLimitBaseInfoDTO> viewLimitBaseInfo = Optional.ofNullable(
//            examViewLimitComponent.getViewLimitBaseInfo(examId));
//        examInfoDTO.setProgrammeId(viewLimitBaseInfo.map(ViewLimitBaseInfoDTO::getProgrammeId).orElse(null));
        return examInfoDTO;
    }

    @Override
    public int getExamPubState(String examId) {
        Exam exam = examService.getById(examId);
        if (exam == null) {
            return 0;
        }
        return exam.getIsPublish();
    }

    @Override
    public void updateExamTime(ExamTimeDTO examTimeDTO) {
        Exam exam = new Exam();
        BeanUtils.copyProperties(examTimeDTO, exam);
        examDao.updateExam(exam);
    }

    @Override
    public void removeExam(String id, Integer checkPublish) {
        examService.removeExamById(id, checkPublish);
    }

    @Override
    public Map<String, Integer> getExamPassNum(CompleteNumQuery completeNumQuery) {
        Collection<String> examIds = completeNumQuery.getSourceId();
        Collection<String> userIds = completeNumQuery.getUserIds();
        ConcurrentHashMap<String, Integer> result = new ConcurrentHashMap<>(userIds.size());
        for (String userId : userIds) {
            Integer num = examService.getExamPassNum(userId, examIds);
            result.put(userId, num);
        }
        return result;
    }

    @Override
    public void publishExam(String id, Integer isPublish) {
        PublishExamDTO publishExamDTO = new PublishExamDTO();
        publishExamDTO.setIds(List.of(id));
        publishExamDTO.setIsPublish(isPublish);
        examService.publish(publishExamDTO);
    }

    @Override
    public BigDecimal getUserScore(String userId, String examId) {
        AnswerRecord answerRecord = answerRecordService.getLastAnswerRecord(examId, userId);
        return answerRecord == null ? null : answerRecord.getUserScore();
    }

    @Override
    public void delAllQuestions(String examId) {
        Exam byId = examService.getById(examId);
        String examName = Optional.ofNullable(byId).isPresent() ? byId.getExamName() : "";
        examQuestionDao.delAllExamQuestion(examId, examName);
    }

    @Override
    public Map<String, String> getNameBatchIds(Collection<String> batchIds) {
        if (CollectionUtils.isEmpty(batchIds)) {
            return Collections.emptyMap();
        }
        return examService.getBaseMapper().selectBatchIds(batchIds).stream()
            .collect(Collectors.toMap(Exam::getId, Exam::getExamName, (key1, key2) -> key1));
    }

    @Override
    public Map<String, ResourceMemberDTO> getResourceMemberBatchIds(Collection<String> batchIds) {
        Map<String, ResourceMemberDTO> result = new HashMap<>();
        if (org.springframework.util.CollectionUtils.isEmpty(batchIds)) {
            return result;
        }
        Map<String, String> map = getNameBatchIds(batchIds);

        // 取资源对应会员卡的信息
        Map<String, Set<MemberCardFeignDTO>> resourceCardMap = memberCardFeign.getMemberCardMapByResourceIds(batchIds);
        result = map.entrySet().stream().collect(Collectors.toMap(entry -> entry.getKey(), entry -> {
            ResourceMemberDTO resourceMemberDTO = new ResourceMemberDTO();
            resourceMemberDTO.setName(entry.getValue());
            if(null != resourceCardMap){
                Set<MemberCardFeignDTO> cardMap = resourceCardMap.get(entry.getKey());
                if(!CollectionUtils.isEmpty(cardMap)){
                    List<String> memberIconUrls = cardMap.stream().filter(p->null != p).distinct()
                        .sorted((dto1, dto2) -> dto1.getSort().compareTo(dto2.getSort()))
                        .map(MemberCardFeignDTO::getCoinImgUrl)
                        .collect(Collectors.toList());
                    resourceMemberDTO.setMemberIconUrls(memberIconUrls);
                }

            }
            return resourceMemberDTO;
        }));
        return result;
    }


    @Override
    public ExamInfoDTO getOneExam(String examId) {
        // getById 带上了isDel 而这个就是需要isDel状态
        Exam exam = examService.getOne(new QueryWrapper<Exam>().lambda().eq(Exam::getId, examId));
        if (exam == null) {
            return null;
        }
        ExamInfoDTO examInfoDTO = new ExamInfoDTO();
        BeanUtils.copyProperties(exam, examInfoDTO);
        return examInfoDTO;
    }

    @Override
    public void saveSyncExamViewLimit(String resourceId, Long programmeId) {
        examViewLimitComponent.handleNewViewLimit(programmeId, resourceId);
    }

    @Override
    public Boolean checkViewLimit(String userId, String resourceId) {
        return examViewLimitService.checkViewLimit(userId, resourceId);
    }

    @Override
    public CerDitchDTO getDitch(String contentId) {
        return examService.getDitch(contentId);
    }

    @Override
    public List<Date> getExamTime(String id) {
        ArrayList<Date> list = new ArrayList<>();
        Exam exam = examService.getById(id);
        if (exam == null) {
            return list;
        }
        list.add(exam.getStartTime());
        list.add(exam.getEndTime());
        return list;
    }

    @Override
    public List<LearningCalendarTaskDTO> findLearningCalenderExamTaskList(
        LearningCalendarTaskQuery learningCalendarTaskQuery) {
        return examService.findLearningCalenderExamTaskList(learningCalendarTaskQuery);
    }


    @Override
    public List<String> findExamStatusByIdList(Set<String> examIdList) {
        if (CollectionUtils.isEmpty(examIdList)) {
            return new ArrayList<>();
        }
        List<Exam> examList = examService.lambdaQuery().eq(Exam::getIsDel, 0).eq(Exam::getIsPublish, 1)
            .in(Exam::getId, examIdList).list();

        List<String> resultExamIdList = examList.stream().map(Exam::getId).collect(Collectors.toList());

        return resultExamIdList;
    }

    @Override
    public ExamInfoDTO getOneExamIncludeDel(String id) {
        Exam exam = examMapper.selectOneExamIncludeDel(id);
        if (exam == null) {
            return null;
        }
        ExamInfoDTO examInfoDTO = new ExamInfoDTO();
        BeanUtils.copyProperties(exam, examInfoDTO);
        return examInfoDTO;
    }

    @Override
    public List<ExamScoreDTO> findUserExamScoreList(List<ExamScoreQuery> examScoreQueryList) {
        List<ExamScoreDTO> examScoreDTOList = new ArrayList<>();

        if (!CollectionUtils.isEmpty(examScoreQueryList)) {
            for (ExamScoreQuery scoreQuery : examScoreQueryList) {
                String userId = scoreQuery.getUserId();
                String examId = scoreQuery.getExamId();
                AnswerRecord answerRecord = answerRecordMapper.getRecordMaxUserScore(userId, examId);
                if (answerRecord != null) {
                    ExamScoreDTO examScoreDTO = new ExamScoreDTO();
                    examScoreDTO.setUserId(userId);
                    examScoreDTO.setExamId(examId);
                    examScoreDTO.setUserScore(answerRecord.getUserScore());
                    examScoreDTOList.add(examScoreDTO);
                }
            }
        }

        return examScoreDTOList;
    }

    @Override
    public List<ExamScoreDTO> findUserExamScoreListFilterNotCheck(List<ExamScoreQuery> examScoreQueryList) {
        if (CollectionUtils.isEmpty(examScoreQueryList)) {
            return Collections.emptyList();
        }
        List<ExamScoreDTO> examScoreDTOList = new ArrayList<>();
        for (ExamScoreQuery scoreQuery : examScoreQueryList) {
            String userId = scoreQuery.getUserId();
            String examId = scoreQuery.getExamId();
            AnswerRecord answerRecord = answerRecordMapper.getRecordMaxUserScore(userId, examId);
            if (answerRecord != null) {
                ExamScoreDTO examScoreDTO = new ExamScoreDTO();
                examScoreDTO.setUserId(userId);
                examScoreDTO.setExamId(examId);
                BigDecimal score = answerRecord.getUserScore();
                boolean isNotCheck =
                    Objects.equals(answerRecord.getIsCheckFinish(), ExamConstant.EXAM_IS_NOT_CHECK_FINISH)
                        || Objects.equals(answerRecord.getIsPost(), ExamConstant.EXAM_IS_NOT_POST);
                if (Objects.equals(answerRecord.getCheckPaperMethod(), ExamConstant.EXAM_MANUAL_REWINDING)
                    && isNotCheck) {
                    score = null;
                }
                examScoreDTO.setUserScore(score);
                examScoreDTOList.add(examScoreDTO);
            }
        }
        return examScoreDTOList;
    }

    @Override
    public int getIdpStatistic(String year, String type) {
        UserIdpStatisticQuery userIdpStatisticQuery = new UserIdpStatisticQuery();
        userIdpStatisticQuery.setUserId(UserThreadContext.getUserId());
        userIdpStatisticQuery.setType(type);
        userIdpStatisticQuery.setYear(year);
        return answerRecordMapper.getIdpStatistic(userIdpStatisticQuery);
    }

    @Override
    public List<String> getInvalidExamId(Collection<String> examIdList) {
        return examService.getInvalidExamId(examIdList);
    }

    @Override
    public List<String> getFinishedExamIds(String userId, Collection<String> ids) {
        return answerRecordService.getFinishedExamIds(userId, ids);
    }

    @Override
    public Integer getMenuTaskCount(Integer status, String userId) {
        return examService.getMenuTaskCount(status, userId);
    }

    @Override
    public Result<PageInfo<ExamInfoDTO>> getMenuTask(Integer status, BaseEntity entity) {
        PageInfo<ExamInfoDTO> data = examService.getMenuTask(status, entity);
        return Result.success(data);
    }

    @Override
    public Map<String, BigDecimal> getMaxScore(Collection<String> userIds, String examId) {
        Map<String, BigDecimal> maxScoreMap = new HashMap<>();
        if (userIds.isEmpty()) {
            return maxScoreMap;
        }
        for (String userId : userIds) {
            Optional.ofNullable(answerRecordMapper.getHaveMaxAnswerStatusRecord(examId, userId))
                .ifPresent(o -> maxScoreMap.put(userId, o.getUserScore()));
        }
        return maxScoreMap;
    }

    @Override
    public Map<String, BigDecimal> getUserScoreByListParams(Collection<ResourceIdAndUserIdDTO> userIdAndContendId) {
        Map<String, BigDecimal> map = new HashMap<>();
        for (ResourceIdAndUserIdDTO dto : userIdAndContendId) {
            BigDecimal userScore = getUserScore(dto.getUserId(), dto.getResourceId());
            map.put(dto.getUserId() + dto.getResourceId(), userScore);
        }
        return map;
    }

    @Override
    public int getResourceIsNotDeleteAndIsPublish(String resourceId) {
        LambdaQueryWrapper<Exam> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Exam::getId);
        queryWrapper.select(Exam::getIsPublish);
        queryWrapper.eq(Exam::getId, resourceId);
        Exam exam = examService.getOne(queryWrapper);
        return exam == null || exam.getIsPublish().equals(PublishStatusEnum.IS_NO_PUBLISH.getValue()) ? 1 : 0;
    }

    @Override
    public void updateExamById(ExamInfoDTO examInfoDTO) {
        Exam exam = new Exam();
        BeanUtils.copyProperties(examInfoDTO, exam);
        examService.updateById(exam);
    }

    @Override
    public List<TaskAppResourceDTO> getTaskExamByIds(Collection<String> ids) {
        return examService.getTaskAppResourceByIds(
            new TaskExamAppResourceQuery().setIds(ids).setCurrentUserId(UserThreadContext.getUserId()));
    }

    @Override
    public void publishOrUnPublishExam(String id, Integer publishType) {
        PublishExamDTO examPublishDTO = new PublishExamDTO();
        List<String> ids = new ArrayList<>();
        ids.add(id);
        examPublishDTO.setIds(ids);
        examPublishDTO.setIsPublish(publishType);

        examService.publish(examPublishDTO);
    }

    @Override
    public Map<String, Integer> getExamFinish(ResourceUserQuery resourceUserQuery) {
        if (StringUtils.isBlank(resourceUserQuery.getResourceId()) || CollectionUtils.isEmpty(
            resourceUserQuery.getUserIdList())) {
            return new HashMap<>();
        }
        return answerRecordMapper.getExamRecordStatusList(resourceUserQuery).stream()
            .collect(Collectors.toMap(ResourceUserDTO::getUserId, ResourceUserDTO::getIsFinish, (k1, k2) -> k1));
    }

    @Override
    public Map<String, BigDecimal> getExamScore(ResourceUserQuery resourceUserQuery) {
        if (StringUtils.isBlank(resourceUserQuery.getResourceId()) || CollectionUtils.isEmpty(
            resourceUserQuery.getUserIdList())) {
            return new HashMap<>();
        }
        return answerRecordMapper.getExamRecordStatusList(resourceUserQuery).stream()
            .collect(Collectors.toMap(ResourceUserDTO::getUserId, ResourceUserDTO::getUserScore, (k1, k2) -> k1));
    }

    @Override
    public Map<String, ResourceBaseDTO> getExamBaseInfo(ResourceBaseQuery resourceBaseQuery) {
        if (CollectionUtils.isEmpty(resourceBaseQuery.getResourceIdList())) {
            return new HashMap<>();
        }
        List<ResourceBaseDTO> resourceBaseList = examService.getExamBaseList(resourceBaseQuery);
        if (!CollectionUtils.isEmpty(resourceBaseQuery.getManagerAreaOrgIds())) {
            resourceBaseList.forEach(resource -> {
                Integer inManageArea = GeneralJudgeEnum.NEGATIVE.getValue();
                for (String managerOrgId : resourceBaseQuery.getManagerAreaOrgIds()) {
                    if (resource.getLevelPath().startsWith(managerOrgId)) {
                        inManageArea = GeneralJudgeEnum.CONFIRM.getValue();
                        break;
                    }
                }
                resource.setInManageArea(inManageArea);
            });
        }
        return resourceBaseList.stream()
            .collect(Collectors.toMap(ResourceBaseDTO::getId, Function.identity(), (key1, key2) -> key1));
    }

    @Override
    public Map<String, Integer> getExamPass(String userId, Collection<String> ids) {
        Map<String, Integer> examPassMap = new HashMap<>();
        if (CollectionUtils.isEmpty(ids)) {
            return examPassMap;
        }
        LambdaQueryWrapper<AnswerRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AnswerRecord::getUserId, userId);
        queryWrapper.in(AnswerRecord::getExamId, ids);
        queryWrapper.eq(AnswerRecord::getIsCheckFinish, GeneralJudgeEnum.CONFIRM.getValue());
        queryWrapper.eq(AnswerRecord::getIsMax, GeneralJudgeEnum.CONFIRM.getValue());
        queryWrapper.orderByDesc(AnswerRecord::getUpdateTime);
        answerRecordService.list(queryWrapper).forEach(ar -> {
            examPassMap.put(ar.getExamId(),
                ar.getUserScore().compareTo(ar.getPassScore()) >= 0 ? GeneralJudgeEnum.CONFIRM.getValue()
                    : GeneralJudgeEnum.NEGATIVE.getValue());
        });
        return examPassMap;
    }

    @Override
    public void createImportUserExamRecord(List<ImportUserExamRecordDTO> recordDTOS) {
        examService.createImportUserExamRecord(recordDTOS);
    }

    @Override
    public Map<String, CertificationContentDTO> getCertificationContentList(Collection<String> batchIds) {
        if (CollectionUtils.isEmpty(batchIds)) {
            return new HashMap<>();
        }
        return examMapper.getCertificationContentList(batchIds).stream()
            .collect(Collectors.toMap(CertificationContentDTO::getId, examBaseInfoDTO -> examBaseInfoDTO));
    }

    @Override
    public Integer checkExamManagePermissions(Collection<String> userManageAreaOrgId, String id) {
        return examMapper.checkExamManagePermissions(userManageAreaOrgId, id);
    }

    @Override
    public Integer getUserExamPassNum(Collection<String> examIds, String userId) {
        return examService.getExamPassNum(userId, examIds);
    }

    @Override
    public void updateExamAnswerRecordTimeByUserId(Collection<String> userIds) {
        answerRecordMapper.updateExamAnswerRecordTimeByUserId(userIds);
    }

    @Override
    public void addAiQuestionToExamLib(List<AiQuestionExamFeignDTO> aiQuestionList, String libId) {
        libQuestionService.addAiQuestionToExamLib(aiQuestionList, libId);
    }

    @Override
    public Map<String, String> getExamLibNameByLibId(List<String> libIdList) {
        if (CollectionUtils.isEmpty(libIdList)) {
            return new HashMap<>();
        }
        List<Library> list = libraryService.lambdaQuery().in(Library::getId, libIdList).list();
        if (CollectionUtils.isEmpty(list)) {
            return new HashMap<>();
        }
        return list.stream().collect(Collectors.toMap(Library::getId, Library::getLibraryName, (a, b) -> a));
    }

    @Override
    public void deleteAiQuestionByQuestionIdAndLibId(String questionId) {
        libQuestionService.removeById(questionId);
        libQuestionOptionService
            .remove(new LambdaQueryWrapper<LibQuestionOption>().eq(LibQuestionOption::getQuestionId, questionId));
    }

    @Override
    public ResourceDeleteInfoDTO getExamIsDelById(String resourceId) {
        return examMapper.getExamIsDelById(resourceId);
    }

    @Override
    public int getResourceIsDel(String resourceId) {
        return examMapper.getResourceIsDel(resourceId);
    }
}
