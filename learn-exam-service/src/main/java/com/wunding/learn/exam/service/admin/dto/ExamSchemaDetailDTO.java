package com.wunding.learn.exam.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import jakarta.validation.constraints.Min;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *构造适合前端展示的矩阵数据
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(name = "ExamSchemaDetailDTO", description = "适合前端展示的矩阵数据-组卷详情对象")
public class ExamSchemaDetailDTO implements Serializable {

    @Schema(description = "1题目数量 2分值/个  3题型排序 4难度高 5难度中 6难度低  其他默认题组ID")
    private String id;

    @Schema(description = "题库名称")
    private String libraryName;

    /**
     * 单选数量
     */
    @Min(value = 0, message = "数量不能小于0")
    @Schema(description = "单选数据,接受数字，否则不给")
    private BigDecimal single;


    /**
     * 多选数量
     */
    @Min(value = 0, message = "数量不能小于0")
    @Schema(description = "多选数据,接受数字，否则不给")
    private BigDecimal multi;


    /**
     * 判断数量
     */
    @Min(value = 0, message = "数量不能小于0")
    @Schema(description = "判断数据,接受数字，否则不给")
    private BigDecimal judge;


    /**
     * 填空数量
     */
    @Min(value = 0, message = "数量不能小于0")
    @Schema(description = "填空数据,接受数字，否则不给")
    private BigDecimal blanks;

    /**
     * 问答数量
     */
    @Min(value = 0, message = "数量不能小于0")
    @Schema(description = "问答数据,接受数字，否则不给")
    private BigDecimal qa;

    public ExamSchemaDetailDTO (String id){
        this.id=id;
    }
}
