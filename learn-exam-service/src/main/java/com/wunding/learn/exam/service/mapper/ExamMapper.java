package com.wunding.learn.exam.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wunding.learn.common.dto.CerDitchDTO;
import com.wunding.learn.common.dto.CertificationContentDTO;
import com.wunding.learn.common.dto.LearningCalendarTaskDTO;
import com.wunding.learn.common.dto.LearningCalendarTaskQuery;
import com.wunding.learn.common.dto.ResourceBaseDTO;
import com.wunding.learn.common.dto.ResourceDeleteInfoDTO;
import com.wunding.learn.common.dto.TaskAppResourceDTO;
import com.wunding.learn.common.mybatis.cache.MyBatisCacheEviction;
import com.wunding.learn.common.query.ResourceBaseQuery;
import com.wunding.learn.exam.api.dto.ExamInfoDTO;
import com.wunding.learn.exam.api.dto.ExamUserScoreDTO;
import com.wunding.learn.exam.api.dto.ExerciseTimeDTO;
import com.wunding.learn.exam.api.dto.ViewExamFeignDTO;
import com.wunding.learn.exam.api.query.TaskExamAppResourceQuery;
import com.wunding.learn.exam.service.admin.dto.ExamCorrectExamListDTO;
import com.wunding.learn.exam.service.admin.dto.ExamCorrectExamListQueryDTO;
import com.wunding.learn.exam.service.admin.dto.ExamListDTO;
import com.wunding.learn.exam.service.admin.dto.ExamQueryDTO;
import com.wunding.learn.exam.service.admin.dto.ExerciseQueryDTO;
import com.wunding.learn.exam.service.client.dto.ExamRankUserDTO;
import com.wunding.learn.exam.service.client.dto.MyExamListQueryDTO;
import com.wunding.learn.exam.service.client.dto.SubordinateExamDTO;
import com.wunding.learn.exam.service.client.query.SubordinateExamQuery;
import com.wunding.learn.exam.service.model.Exam;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Property;

/**
 * 考试表 Mapper 接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">xiao8</a>
 * @date 2022-03-19
 */
@Mapper
@CacheNamespace(implementation = MyBatisCacheEviction.class, properties = {
    @Property(name = "appName", value = "${appName}")})
public interface ExamMapper extends BaseMapper<Exam> {

    /**
     * 管理端-条件查询/分页查询
     *
     * @param examQueryDTO
     * @return
     */
    List<ExamListDTO> selectListByPage(@Param("params") ExamQueryDTO examQueryDTO);

    /**
     * 管理端-条件查询/分页查询-备份数据
     *
     * @param examQueryDTO
     * @return
     */
    List<ExamListDTO> selectListByPageBak(@Param("params") ExamQueryDTO examQueryDTO);

    /**
     * 管理端-条件查询/获取总条数
     *
     * @param examQueryDTO
     * @return
     */
    int selectCountByPage(@Param("params") ExamQueryDTO examQueryDTO);

    /**
     * 管理端-条件查询/获取总条数-备份数据
     *
     * @param examQueryDTO
     * @return
     */
    int selectCountByPageBak(@Param("params") ExamQueryDTO examQueryDTO);


    /**
     * 学员端-考试排行榜（从1到N，依次排名）
     *
     * @param examId 考试id
     * @return 考试排行榜
     */
    List<ExamRankUserDTO> getRank(String examId);

    /**
     * 学员端-我的考试排名信息
     *
     * @param userId 用户id
     * @param examId 考试id
     * @return 我的考试排名信息
     */
    Integer getMyRank(String userId, String examId);

    /**
     * 考试完成情况
     *
     * @param userId 用户id
     * @param examId 考试id
     * @return 完成状态
     */
    Integer getExamFinishStatus(@Param("userId") String userId, @Param("examId") String examId);

    /**
     * 用户考试得分（已交卷、已改卷、最新考试记录的及格分和用户得分）
     *
     * @param userId  用户id
     * @param examIds 考试id集合
     * @return 及格分和用户得分
     */
    List<ExamUserScoreDTO> getUserScoreInfo(@Param("userId") String userId,
        @Param("examIds") Collection<String> examIds);

    /**
     * 获取待改卷的考试列表
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param examName  考试名称
     * @param userId    用户id
     * @return {@link List}<{@link Exam}>
     */
    List<ExamCorrectExamListDTO> getCorrectExamList(@Param("startTime") Date startTime, @Param("endTime") Date endTime,
        @Param("examName") String examName, @Param("userId") String userId,
        @Param("params") ExamCorrectExamListQueryDTO examCorrectExamListQueryDTO);

    /**
     * 获得待改卷考试列表缓存
     *
     * @param
     * @return
     */
    List<ExamCorrectExamListDTO> getCorrectExamCashList(Collection<String> examIds);

    /**
     * 我的考试
     *
     * @param myExamListQueryDTO 参数
     * @return 考试列表
     */
    List<Exam> myExamList(MyExamListQueryDTO myExamListQueryDTO);

    /**
     * 批量获取考试(只根据id不过滤已删除的)
     *
     * @param examIds
     * @return
     */
    List<ViewExamFeignDTO> getExamInfoMapByExamIds(@Param("examIds") Collection<String> examIds);

    /**
     * 批量获取考试(只根据id过滤已删除的)
     *
     * @param examIds
     * @return
     */
    List<ViewExamFeignDTO> getValidExamInfoMapByExamIds(@Param("examIds") Collection<String> examIds);

    /**
     * 获取考试/练习题目总数
     *
     * @param examIds 考试id集合
     * @return 考试/练习题目总数
     */
    List<Map<String, Object>> getExamQuestionCountMap(@Param("examIds") Collection<String> examIds);

    /**
     * 批量获取用户指定考试中的及格数
     *
     * @param userId
     * @param examIds
     * @return
     */
    Integer getExamPassNum(@Param("userId") String userId, @Param("examIds") Collection<String> examIds);

    /**
     * 根据渠道id 获取一个渠道信息
     *
     * @param contentId 内容识别
     * @return {@link CerDitchDTO}
     */
    CerDitchDTO getDitch(String contentId);


    /**
     * 查询学习日历 评估列表
     *
     * @param learningCalendarTaskQuery
     * @return
     */
    List<LearningCalendarTaskDTO> selectProTaskExamListByTime(LearningCalendarTaskQuery learningCalendarTaskQuery);


    /**
     * 获取一个考试信息 包含已删除
     *
     * @param id
     * @return
     */
    Exam selectOneExamIncludeDel(@Param("id") String id);

    /**
     * * 234 查询下属考试结果
     *
     * @param subordinateExamQuery 下属考试查询对象
     * @return 下属考试结果
     */
    List<SubordinateExamDTO> getSubordinateExamList(@Param("params") SubordinateExamQuery subordinateExamQuery);

    /**
     * * 考试/练习时间查询
     *
     * @param id 考试/练习id
     * @return
     */
    ExerciseTimeDTO getExerciseTime(String id);

    /**
     * 过滤获取已被删除的资源id
     *
     * @param examIdList
     * @return
     */
    List<String> getInvalidExamId(@Param("examIdList") Collection<String> examIdList);

    /**
     * 获取用户任务列表 考试部分
     *
     * @param status
     * @param userId
     * @return
     */
    List<ExamInfoDTO> getMenuTask(@Param("status") Integer status, @Param("userId") String userId);

    /**
     * 获取用户任务列表 考试部分总数
     *
     * @param status
     * @param userId
     * @return
     */
    Integer getMenuTaskCount(@Param("status") Integer status, @Param("userId") String userId);

    /**
     * 获取单个考试信息(不过滤删除)
     *
     * @param examId
     * @return
     */
    ExamInfoDTO getRealityById(@Param("examId") String examId);

    /**
     * @param examQueryDTO
     * @return
     */
    List<Exam> list(@Param("params") ExerciseQueryDTO examQueryDTO);

    /**
     * 获取任务的考试应用信息
     *
     * @param query
     * @return
     */
    List<TaskAppResourceDTO> getTaskAppResourceByIds(TaskExamAppResourceQuery query);

    /**
     * 获取考试基本信息
     *
     * @param resourceBaseQuery
     * @return
     */
    List<ResourceBaseDTO> getExamBaseList(@Param("params") ResourceBaseQuery resourceBaseQuery);

    List<CertificationContentDTO> getCertificationContentList(@Param("batchIds") Collection<String> batchIds);


    /**
     * 根据用户管理范围的组织信息和资源ID校验是否具有资源管理权限
     *
     * @param userManageAreaOrgId 用户管辖范围ID列表
     * @param id                  考试或练习ID
     * @return 0 无权限，1 有权限
     */
    Integer checkExamManagePermissions(@Param("userManageAreaOrgId") Collection<String> userManageAreaOrgId,
        @Param("id") String id);

    /**
    * 修改组卷状态
    * @param updateExamInfo
    * @return
    */
    void changeExamCompositionStatus(Exam updateExamInfo);

    /**
     * 获取考试的删除信息
     * @param resourceId 资源id
     * @return ResourceDeleteInfoDTO
     */
    ResourceDeleteInfoDTO getExamIsDelById(String resourceId);

    /**
     * 获取资源删除状态
     * @param resourceId 资源id
     * @return
     */
    int getResourceIsDel(String resourceId);
}
