package com.wunding.learn.survey.service.client.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <h4>mlearn</h4>
 * <p>调研题目提交对象</p>
 *
 * <AUTHOR> 赖卓成
 * @date : 2022-05-23 16:16
 **/
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(name = "SurveyQuestionSubmitDTO", description = "调研题目提交对象")
public class SurveyQuestionSubmitDTO implements Serializable {

    /**
     * 题目id
     */
    @Schema(description = "题目id")
    private String questionId;


    /**
     * 主观题答案、与选项二选一
     */
    @Schema(description = "主观题答案或选项id")
    private List<String> answer;
}
