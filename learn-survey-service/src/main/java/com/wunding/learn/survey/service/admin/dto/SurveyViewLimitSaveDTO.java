package com.wunding.learn.survey.service.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <h4>mlearn</h4>
 * <p>调研下发范围保存对象</p>
 *
 * <AUTHOR> 赖卓成
 * @date : 2022-05-20 17:12
 **/
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(name = "SurveyViewLimitSaveDTO", description = "调研下发范围保存对象")
public class SurveyViewLimitSaveDTO implements Serializable {


    /**
     * ID
     */
    @Schema(description = "唯一标识 ")
    private String categoryId;

    /**
     * categoryType
     */
    @Schema(description = "权限类别 部门:OrgLimit 时间身份:TimeLimit 业务条线:BusinessLimit 管理者层级:ManageLevelLimit 岗位族、岗位、岗位层级:PostLimit 讲师层级:LecturerLimit 职级:JobLevelLimit 人员:UserLimit  ")
    private String categoryType;


}
