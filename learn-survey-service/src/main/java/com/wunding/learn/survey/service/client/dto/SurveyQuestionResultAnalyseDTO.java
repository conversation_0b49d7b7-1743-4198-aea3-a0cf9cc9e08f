package com.wunding.learn.survey.service.client.dto;

import com.wunding.learn.survey.service.admin.dto.base.BaseSurveyQuestionDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Author: aixinrong
 * @Date: 2023/2/13 15:30
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
 @Schema(name = "SurveyQuestionResultAnalyseDTO", description = "调研结果对象")
public class SurveyQuestionResultAnalyseDTO extends BaseSurveyQuestionDTO {

    /**
     * 题目选项列表
     */
    @Schema(description = "题目选项列表/选择题才有")
    private List<SurveyQuestionOptionAnalyseDTO> optionList;


}
