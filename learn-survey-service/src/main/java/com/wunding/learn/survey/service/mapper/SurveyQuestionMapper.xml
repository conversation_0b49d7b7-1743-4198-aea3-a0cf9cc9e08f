<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wunding.learn.survey.service.mapper.SurveyQuestionMapper">
    <!-- 开启二级缓存 -->
    <!--
<cache type="com.wunding.learn.common.redis.MyBatisPlusRedisCache"/>
-->

    <!-- 使用缓存 -->
    <cache-ref namespace="com.wunding.learn.survey.service.mapper.SurveyQuestionMapper"/>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.wunding.learn.survey.service.model.SurveyQuestion">
        <!--@Table survey_question-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="survey_id" jdbcType="VARCHAR"
          property="surveyId"/>
        <result column="question_no" jdbcType="INTEGER"
          property="questionNo"/>
        <result column="question_name" jdbcType="VARCHAR"
          property="questionName"/>
        <result column="question_type" jdbcType="INTEGER"
          property="questionType"/>
        <result column="sort_no" jdbcType="INTEGER"
          property="sortNo"/>
        <result column="available" jdbcType="INTEGER"
          property="available"/>
        <result column="is_del" jdbcType="INTEGER"
          property="isDel"/>
        <result column="create_by" jdbcType="VARCHAR"
          property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP"
          property="createTime"/>
        <result column="question_category" jdbcType="VARCHAR"
          property="questionCategory"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, survey_id, question_no, question_name, question_type, sort_no, available, is_del, create_by, create_time, question_category
    </sql>

    <select id="getSubjectiveAnswerList" resultType="com.wunding.learn.common.dto.SubjectiveAnswerDTO" useCache="false">
        select srd.id,
               srd.create_by as answerBy,
               srd.answer    as userAnswer
        from survey_question sq
                 inner join survey_record_detail srd on sq.id = srd.question_id
                 inner join survey_record sr on srd.record_id = sr.id
        where sq.id = #{params.id}
          and srd.answer != ''
    </select>
</mapper>
