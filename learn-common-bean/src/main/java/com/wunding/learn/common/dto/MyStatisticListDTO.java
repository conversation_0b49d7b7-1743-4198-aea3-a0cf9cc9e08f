package com.wunding.learn.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR> aixinrong
 * @since : 2022/9/29 14:00
 */
@Data
@Schema(name = "MyStatisticListDTO", description = "个人统计数据对象")
public class MyStatisticListDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "Id")
    private String id;

    @Schema(description = "标志")
    private String flag;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "数值")
    private Object value;

    @Schema(description = "目标")
    private Object targetValue;

    @Schema(description = "百分比")
    private Integer percent;
}
