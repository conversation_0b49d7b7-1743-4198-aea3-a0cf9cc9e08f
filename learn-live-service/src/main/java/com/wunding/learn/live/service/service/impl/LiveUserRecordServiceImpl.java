package com.wunding.learn.live.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wunding.learn.live.service.mapper.LiveUserRecordMapper;
import com.wunding.learn.live.service.model.LiveUserRecord;
import com.wunding.learn.live.service.service.ILiveUserRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <p> 用户第一次观看直播记录表 服务实现类
 *
 * <AUTHOR> href="mailto:<EMAIL>">axr</a>
 * @since 2023-11-28
 */
@Slf4j
@Service("liveUserRecordService")
public class LiveUserRecordServiceImpl extends ServiceImpl<LiveUserRecordMapper, LiveUserRecord> implements
    ILiveUserRecordService {

    @Override
    public void saveOnlyRecord(String levelId, String userId) {
        if (StringUtils.isNotBlank(levelId) && StringUtils.isNotBlank(userId)) {
            baseMapper.saveOnlyRecord(levelId, userId);
        }
    }
}
