package com.wunding.learn.live.service.admin.query;

import com.wunding.learn.common.bean.BaseEntity;
import io.swagger.v3.oas.annotations.Parameter;
import java.util.Set;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 直播列表查询对象
 *
 * <AUTHOR> href="mailto:<EMAIL>">yuxueyi</a>
 * @version V7.0.0
 * @since 2022/5/10  13:40
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LiveVodQuery extends BaseEntity {

    /**
     * 直播ID/直播频道ID
     */
    @Parameter(description = "直播ID/直播频道ID", required = true)
    @NotBlank(message = "直播ID/直播频道ID不能为空")
    private String idOrChannelId;

    /**
     * 直播名称
     */
    @Parameter(description = "直播回放名称")
    private String liveVodName;

    @Parameter(description = "createAndUnderOrgIds 自己及其组织以下的所有组织id", hidden = true)
    private Set<String> createAndUnderOrgIds;

    @Parameter(description = "管辖范围orgId", hidden = true)
    private Set<String> managerAreaOrgIds;

    @Parameter(description = "当前直播所属的供应商", hidden = true)
    private Integer supplier;

    @Parameter(description = "时间戳", hidden = true)
    private Long timestamp;

    @Parameter(description = "回调签名，回调时要传", hidden = true)
    private String callBackSign;
}
