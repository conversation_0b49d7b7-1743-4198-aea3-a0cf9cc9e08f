package com.wunding.learn.common.util.date;


import static com.wunding.learn.common.util.date.DateHelper.YYYYMMDD;
import static com.wunding.learn.common.util.date.DateHelper.YYYYMMDD235959;
import static com.wunding.learn.common.util.date.DateHelper.YYYYMMDD_HHMMSS;

import com.wunding.learn.common.constant.other.CommonConstants;
import com.wunding.learn.common.constant.other.ErrorNoEnum;
import com.wunding.learn.common.enums.language.LanguageEnum;
import com.wunding.learn.common.enums.lecture.lecturerCalendarEnum;
import com.wunding.learn.common.exception.BusinessException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;

/**
 * 日期辅助类
 *
 * <AUTHOR> href="mailto:<EMAIL>">ZhangPan</a>
 */
@Slf4j
public class DateUtil {

    public static final String YYYY_MM_DD = "yyyy-MM-dd";
    public static final String YYYY_MM = "yyyy-MM";

    public static final String YYMMDD_HHMM = "yyyy-MM-dd HH:mm";
    private static final String YYMMDD = "yyyy-MM-dd";

    public static final String YYMMDD_HHMMSS = "yyyy-MM-dd HH:mm:ss";

    public static final String ISO_FORMAT_STR = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX";

    private static String[] parsePatterns = {
        YYMMDD, YYMMDD_HHMMSS, YYMMDD_HHMM, YYYY_MM,
        "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
        "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM",
        "yyyyMMddHHmmss", "HH:mm", "yyyy-M-dd", "yyyy/M/dd", "yyyy.M.dd",
        "yyyy-M-d", "yyyy/M/d", "yyyy.M.d", "yyyy-MM-dd HH:mm:ss.S",
        "yyyy-MM-dd HH:mm:ssX",    // 支持时区格式 +08 格式
        "yyyy-MM-dd HH:mm:ssXX",   // 支持时区格式 +0800 格式
        "yyyy-MM-dd HH:mm:ssXXX"   // 支持时区格式 +08:00 格式
    };


    /**
     * 生成当前时间的年月日时分秒字符串
     *
     * @return String
     * <AUTHOR>
     * @date 2015年4月14日下午2:15:39
     */
    public static String getYmdhmsStr() {
        return DateFormatUtils.format(new Date(), "yyyyMMddHHmmss");
    }

    /**
     * 生成当前时间的年月字符串
     *
     * @return String
     * <AUTHOR>
     * @date 2015年4月15日上午9:09:12
     */
    public static String getYmStr() {
        return DateFormatUtils.format(new Date(), "yyyyMM");
    }

    /**
     * 获取日历
     *
     * @return
     */
    public static String getYmdStr(Date date) {
        if (date == null) {
            return "";
        }
        return DateFormatUtils.format(date, YYMMDD);
    }

    /**
     * 获取时间的月份
     *
     * @param date
     * @return
     */
    public static String getYmStr(Date date) {
        if (date == null) {
            return "";
        }
        return DateFormatUtils.format(date, YYYY_MM);
    }

    public static String getYStr(Date date) {
        if (date == null) {
            return "";
        }
        return DateFormatUtils.format(date, "yyyy");
    }

    public static Date parseDate(String date) {
        try {
            return DateUtils.parseDate(date, YYYYMMDD);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 生成当前时间的年月日时分秒毫秒字符串
     *
     * @return String
     * <AUTHOR>
     * @date 2023年4月28日上午9:15:39
     */
    public static String getYmdhmsSStr() {
        return DateFormatUtils.format(new Date(), "yyyyMMddHHmmssSSS");
    }


    /**
     * 格式化时间，支持数组包含的格式(自行添加拓展)
     *
     * @param date
     * @return
     * <AUTHOR>
     * @date 2023年4月06日下午4:15:39
     */
    public static Date formatToDate(String date) throws ParseException {
        if (date == null) {
            return null;
        }
        return org.apache.commons.lang3.time.DateUtils.parseDate(date, parsePatterns);
    }

    /**
     * 时间字符串格式修改
     */
    public static String convertDateStr(String dateStr, String format) {
        try {
            Date date = formatToDate(dateStr);
            return formatToStr(date, format);
        } catch (Exception e) {
            return null;
        }
    }


    /**
     * 格式化时间
     *
     * @param date
     * @param format 指定日期格式 如：DateHelper1.YYYYMMDD_HHMMSS
     * @return
     * @throws ParseException
     */
    public static Date formatToDate(String date, String format) {
        if (StringUtils.isBlank(date)) {
            return null;
        }
        if (null == format) {
            format = YYYYMMDD_HHMMSS;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        try {
            return sdf.parse(date);
        } catch (ParseException e) {
            log.error("An error occurred when formatToDate", e);
        }
        return null;
    }

    /**
     * 用于校验前端日期传参是否正确,系统日期统一使用:yyyy-MM-dd HH:mm:ss
     *
     * @param str 前端传参-日期参数
     */
    public static void validStr2Date(String str) {
        if (StringUtils.isNotBlank(str)) {
            SimpleDateFormat sdf = new SimpleDateFormat(YYYYMMDD_HHMMSS);
            try {
                sdf.parse(str);
            } catch (ParseException e) {
                throw new BusinessException(ErrorNoEnum.ERR_DATE_PARAM_ERROR);
            }
        }
    }

    /**
     * 格式化时间
     *
     * @param date
     * @param format 指定日期格式 如：DateHelper.YYYYMMDD_HHMMSS
     * @return
     */
    public static String formatToStr(Date date, String format) {
        if (null == date) {
            return null;
        }
        return DateFormatUtils.format(date, format);
    }

    /**
     * 日期格式转换
     *
     * @param date   日期
     * @param format 格式
     * @return {@link Date }
     */
    public static Date formatDate2Date(Date date, String format) {
        return formatToDate(formatDate(date, format), format);
    }

    /**
     * 时长（秒）格式化：1小时24分钟
     *
     * @param time 时长（秒）
     * @param lang 语言
     * @return 格式化字符串
     */
    public static String convertTimeRules3(Long time, String lang) {
        String hourUnit =
            LanguageEnum.ENGLISH.getLang().equals(lang) ? CommonConstants.LANG.get(CommonConstants.CONVERT_TIME_HOUR) :
                CommonConstants.CONVERT_TIME_HOUR;
        String minUnit = LanguageEnum.ENGLISH.getLang().equals(lang) ?
            CommonConstants.LANG.get(CommonConstants.CONVERT_TIME_MINUTE) : CommonConstants.CONVERT_TIME_MINUTE;
        String result = "0".concat(minUnit);
        if (time != null && time > 0) {
            long mi = time / 60;
            if (mi > 60) {
                Long hh = mi / 60;
                mi = mi % 60;
                result = hh + hourUnit + mi + minUnit;
            } else {
                result = mi + minUnit;
            }
        }
        return result;
    }

    /**
     * 时长（秒）格式化：1小时5′15″
     *
     * @param time 时长（秒）
     * @return 格式化字符串
     */
    public static String convertTimeRules2(Long time) {
        String result = "0″";
        if (time != null && time > 0) {
            Long ss = time % 60;
            Long mi = time / 60;
            if (mi > 60) {
                Long hh = mi / 60;
                mi = mi % 60;
                if (hh > 24) {
                    Long day = hh / 24;
                    hh = hh % 24;
                    result = day + "天" + hh + "小时" + mi + "′" + ss + "″";
                } else {
                    result = hh + "小时" + mi + "′" + ss + "″";
                }
            } else {
                result = mi + "′" + ss + "″";
            }
        }
        return result;
    }

    /**
     * 获取格式化时间
     *
     * @param date 时间
     * @param lang 语言
     * @return 格式化时间
     */
    public static String getFormatTimeStr(Date date, String lang) {
        String yearUnit =
            LanguageEnum.ENGLISH.getLang().equals(lang) ? CommonConstants.LANG.get(CommonConstants.CONVERT_TIME_YEAR) :
                CommonConstants.CONVERT_TIME_YEAR;
        String monthUnit =
            LanguageEnum.ENGLISH.getLang().equals(lang) ? CommonConstants.LANG.get(CommonConstants.CONVERT_TIME_MONTH) :
                CommonConstants.CONVERT_TIME_MONTH;
        String dayUnit =
            LanguageEnum.ENGLISH.getLang().equals(lang) ? CommonConstants.LANG.get(CommonConstants.CONVERT_TIME_DAY) :
                CommonConstants.CONVERT_TIME_DAY;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        Calendar now = Calendar.getInstance();
        now.setTime(new Date());
        int day = now.get(Calendar.DAY_OF_MONTH) - calendar.get(Calendar.DAY_OF_MONTH);
        int month = now.get(Calendar.MONTH) - calendar.get(Calendar.MONTH);
        int year = now.get(Calendar.YEAR) - calendar.get(Calendar.YEAR);
        if (day < 0) {
            month -= 1;
            now.add(Calendar.MONTH, -1);
            day = day + now.getActualMaximum(Calendar.DAY_OF_MONTH);
        }
        if (month < 0) {
            month = (month + 12) % 12;
            year--;
        }
        StringBuilder builder = new StringBuilder();
        if (year > 0) {
            builder.append(year).append(yearUnit);
        }
        if (month > 0) {
            builder.append(month).append(monthUnit);
        }
        builder.append(day).append(dayUnit);
        return builder.toString();
    }

    /**
     * 获取某年某月每一天日期
     *
     * @return 知道日期的月份天数集合
     */
    public static List<Date> getMonthEveryDays(String begin, String end) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        ArrayList<Date> list = new ArrayList<>();
        try {
            Date date = sdf.parse(begin);
            Date monthEnd = sdf.parse(end);
            while (!date.after(monthEnd)) {
                list.add(date);
                date = getNext(date);
            }
        } catch (ParseException e) {
            log.error("An error occurred when getMonthEveryDays", e);
        }
        return list;
    }

    /**
     * 获取某年某月每一天日期
     *
     * @return 知道日期的月份天数集合
     */
    public static List<Date> getMonthEveryDays(Date begin, Date end) {
        ArrayList<Date> list = new ArrayList<>();
        while (!begin.after(end)) {
            list.add(begin);
            begin = getNext(begin);
        }
        return list;
    }

    /**
     * 指定日期的后一天
     *
     * @param date date
     * @return 知道日期的下一天时间
     */
    public static Date getNext(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, 1);
        return calendar.getTime();
    }

    /**
     * 指定日期的前一天
     *
     * @param date date
     * @return 指定日期的上一天时间
     */
    public static Date getLast(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, -1);
        return calendar.getTime();
    }

    /**
     * 返回指定月数后的日期
     *
     * @param date   日期
     * @param months 月数  可以为负数
     * @return
     */
    public static Date addMonthsTime(Date date, int months) {
        if (date == null) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, months);
        return calendar.getTime();
    }

    /**
     * 返回指定分钟后的日期
     *
     * @param date    日期
     * @param minutes 分钟  可以为负数
     * @return
     */
    public static Date addMinutesTime(Date date, int minutes) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MINUTE, minutes);
        return calendar.getTime();
    }

    /**
     * 获取某个时间前n个月的日期
     *
     * @param date date
     * @return 指定日期的下一天时间
     */
    public static Date getBeforeMonthDate(Date date, int n) {
        Calendar ca = Calendar.getInstance();
        ca.setTime(date);
        ca.add(Calendar.MONTH, -n);
        return ca.getTime();
    }

    /**
     * 时间戳转换成日期格式字符串
     *
     * @param timeStamp 时间戳Long型
     * @param
     * @return
     */
    public static String timeStampToDate(long timeStamp, String format) {
        if (timeStamp <= 0) {
            log.info("时间戳不能为空");
            return "";
        }
        if (format == null || format.isEmpty()) {
            format = YYMMDD_HHMMSS;
        }
        Date date = new Date(timeStamp);
        SimpleDateFormat dateFormat = new SimpleDateFormat(format);

        return dateFormat.format(date);
    }

    /**
     * 日期格式字符串转换成时间戳
     *
     * @param dateStr
     * @param format  如：yyyy-MM-dd HH:mm:ss
     * @return
     */
    public static String dateToTimeStamp(String dateStr, String format) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(format);
            return String.valueOf(sdf.parse(dateStr).getTime() / 1000);
        } catch (Exception e) {
            log.error("发生异常", e);
        }
        return "";
    }

    public static void main(String[] args) {
        log.info(dateToTimeStamp("2023-11-16 15:00:00", YYMMDD_HHMM));
    }

    /**
     * 获得该月第一天
     *
     * @param year  年
     * @param month 月
     * @return 该月第一天
     */
    public static Date getFirstDayOfMonth(int year, int month) {
        Date date;
        Calendar cal = Calendar.getInstance();
        // 设置年份
        cal.set(Calendar.YEAR, year);
        // 设置月份
        cal.set(Calendar.MONTH, month - 1);
        // 获取某月最小天数
        int firstDay = cal.getActualMinimum(Calendar.DAY_OF_MONTH);
        // 设置日历中月份的最小天数
        cal.set(Calendar.DAY_OF_MONTH, firstDay);
        // 格式化日期
        date = cal.getTime();
        SimpleDateFormat sdf = new SimpleDateFormat(YYYYMMDD);
        String dateStr = sdf.format(cal.getTime());

        try {
            date = sdf.parse(dateStr);
        } catch (ParseException e) {
            log.error("发生异常", e);
        }
        return date;
    }

    /**
     * 获得该月最后一天
     *
     * @param year  年
     * @param month 月
     * @return 该月最后一天
     */
    public static Date getLastDayOfMonth(int year, int month) {
        Date date;
        Calendar cal = Calendar.getInstance();
        // 设置年份
        cal.set(Calendar.YEAR, year);
        // 设置月份
        cal.set(Calendar.MONTH, month - 1);
        // 获取某月最大天数
        int lastDay = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
        // 设置日历中月份的最大天数
        cal.set(Calendar.DAY_OF_MONTH, lastDay);

        // 格式化日期
        date = cal.getTime();
        SimpleDateFormat sdf = new SimpleDateFormat(YYYYMMDD235959);
        String dateStr = sdf.format(cal.getTime());

        try {
            date = new SimpleDateFormat(YYYYMMDD_HHMMSS).parse(dateStr);
        } catch (ParseException e) {
            log.error("发生异常", e);
        }
        return date;
    }

    /**
     * 判断时间是否在时间段内
     *
     * @param nowTime   知道日期
     * @param beginTime 开始日期
     * @param endTime   结束日期
     * @return boolean
     */
    public static boolean belongCalendar(Date nowTime, Date beginTime, Date endTime) {
        Calendar date = Calendar.getInstance();
        date.setTime(nowTime);
        Calendar begin = Calendar.getInstance();
        begin.setTime(beginTime);
        Calendar end = Calendar.getInstance();
        end.setTime(endTime);
        return date.after(begin) && date.before(end);
    }

    /**
     * 根据日期拼接时间段返回毫秒值
     *
     * @param day 日期
     * @return
     */
    public static Map<String, Long> getMillis(String day) {
        String forenoonBegin = day + " " + lecturerCalendarEnum.MORNINGBEGIN.getValue() + ":00";
        String forenoonEnd = day + " " + lecturerCalendarEnum.MORNINGEND.getValue() + ":00";
        String afternoonBegin = day + " " + lecturerCalendarEnum.AFTERNOONBEGIN.getValue() + ":00";
        String afternoonEnd = day + " " + lecturerCalendarEnum.AFTERNOONEND.getValue() + ":00";
        String eveningBegin = day + " " + lecturerCalendarEnum.EVENINGBEGIN.getValue() + ":00";
        String eveningEnd = day + " " + lecturerCalendarEnum.EVENINGEND.getValue() + ":00";

        Map<String, Long> map = new HashMap<>(6);

        Date forenoonBeginDate = DateUtil.formatToDate(forenoonBegin, null);
        map.put("forenoonBegin", null != forenoonBeginDate ? forenoonBeginDate.getTime() : 0L);

        Date forenoonEndDate = DateUtil.formatToDate(forenoonEnd, null);
        map.put("forenoonEnd", null != forenoonEndDate ? forenoonEndDate.getTime() : 0L);

        Date afternoonBeginDate = DateUtil.formatToDate(afternoonBegin, null);
        map.put("afternoonBegin", null != afternoonBeginDate ? afternoonBeginDate.getTime() : 0L);

        Date afternoonEndDate = DateUtil.formatToDate(afternoonEnd, null);
        map.put("afternoonEnd", null != afternoonEndDate ? afternoonEndDate.getTime() : 0L);

        Date eveningBeginDate = DateUtil.formatToDate(eveningBegin, null);
        map.put("eveningBegin", null != eveningBeginDate ? eveningBeginDate.getTime() : 0L);

        Date eveningEndDate = DateUtil.formatToDate(eveningEnd, null);
        map.put("eveningEnd", null != eveningEndDate ? eveningEndDate.getTime() : 0L);
        return map;
    }

    /**
     * 获取指定时间
     *
     * @param calendar
     * @param hour     时
     * @param minute   分
     * @param second   秒
     * @return
     */
    public static Date getSettingDate(Calendar calendar, Integer hour, Integer minute, Integer second) {
        if (hour != null) {
            calendar.set(Calendar.HOUR_OF_DAY, hour);
        }
        if (minute != null) {
            calendar.set(Calendar.MINUTE, minute);
        }
        if (second != null) {
            calendar.set(Calendar.SECOND, second);
        }
        return calendar.getTime();
    }

    /**
     * 获取当前时间
     *
     * @param format 指定日期格式 如：DateHelper1.YYYYMMDD_HHMMSS
     * @return
     */
    public static String getCurrentTime(String format) {
        return DateFormatUtils.format(new Date(), format);
    }

    /**
     * 计算两个日期之间相差的天数
     *
     * @param date1 较小日期
     * @param date2 较大日期
     * @return 相差天数
     */
    public static int daysBetween(Date date1, Date date2) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date1);
        long time1 = cal.getTimeInMillis();
        cal.setTime(date2);
        long time2 = cal.getTimeInMillis();
        long betweenDays = (time2 - time1) / (1000 * 3600 * 24);
        return Integer.parseInt(String.valueOf(betweenDays));
    }

    /**
     * 计算两个日期之间相差的天数（不到一天，也算作一天）
     *
     * @param date1 较小日期
     * @param date2 较大日期
     * @return 相差天数
     */
    public static int daysBetween3(Date date1, Date date2) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date1);
        long time1 = cal.getTimeInMillis();
        cal.setTime(date2);
        long time2 = cal.getTimeInMillis();
        long betweenDays = (time2 - time1) / (1000 * 3600 * 24);
        long remainder = (time2 - time1) % (1000 * 3600 * 24);
        if(remainder>0){
            betweenDays++;
        }
        return Integer.parseInt(String.valueOf(betweenDays));
    }


    /**
     * 计算两个日期之间相差的天数, 和上面的方法不同的是：不满24小时也算作一天
     *
     * @param start 较小日期
     * @param end 较大日期
     * @return 相差天数
     */
    public static int calculateDaysDifference(Date start, Date end) {
        // 计算两个日期之间的毫秒差
        long millisecondsDifference = end.getTime() - start.getTime();

        // 计算天数，确保即使不满24小时也算作一天
        long daysDifference = (millisecondsDifference + TimeUnit.DAYS.toMillis(1) - 1) / TimeUnit.DAYS.toMillis(1);

        return (int) daysDifference;
    }

    /**
     * 计算两个日期之间相差的月份
     *
     * @param date1 较小日期
     * @param date2 较大日期
     * @return int
     */
    public static int monthBetween(Date date1, Date date2) {
        // 将Date对象转换为LocalDate对象
        LocalDate localDate1 = date1.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
        LocalDate localDate2 = date2.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();

        // 计算时间差，将年份和月份转换成总月数
        return (localDate2.getYear() - localDate1.getYear()) * 12 +
            localDate2.getMonthValue() - localDate1.getMonthValue();
    }

    /**
     * 共读所需,设置时间为0时 计算距离活动结束天数
     *
     * @param early
     * @param late
     * @return
     */
    public static int daysBetween2(Date early, Date late) {
        Calendar calst = Calendar.getInstance();
        Calendar caled = Calendar.getInstance();
        calst.setTime(early);
        caled.setTime(late);
        // 设置时间为0时
        calst.set(Calendar.HOUR_OF_DAY, 0);
        calst.set(Calendar.MINUTE, 0);
        calst.set(Calendar.SECOND, 0);
        caled.set(Calendar.HOUR_OF_DAY, 0);
        caled.set(Calendar.MINUTE, 0);
        caled.set(Calendar.SECOND, 0);
        // 得到两个日期相差的天数
        return ((int) (caled.getTime().getTime() / 1000) - (int) (calst.getTime().getTime() / 1000)) / 3600 / 24;
    }

    public static int workAge(Date nowTime, Date workTime){
        int year = 0;
        //当前时间的年月日
        Calendar cal = Calendar.getInstance();
        cal.setTime(nowTime);
        int nowYear = cal.get(Calendar.YEAR);
        int nowMonth = cal.get(Calendar.MONTH);
        int nowDay = cal.get(Calendar.DAY_OF_MONTH);

        //开始工作时间的年月日
        cal.setTime(workTime);
        int workYear = cal.get(Calendar.YEAR);
        int workMonth = cal.get(Calendar.MONTH);
        int workDay = cal.get(Calendar.DAY_OF_MONTH);

        //得到工龄
        year = nowYear - workYear; //得到年差
        //若目前月数少于开始工作时间的月数，年差-1
        //当月数相等时，判断日数，若当月的日数小于开始工作时间的日数，年差-1
        if ((nowMonth < workMonth) || (nowMonth == workMonth && nowDay < workDay)) {
            year = year - 1;
        }
        if(year <0){
            year  = 0;
        }
        return year;
    }

    /**
     * 获取两个日期相差的年数
     *
     * @param date1 较大的日期
     * @param date2 较小的日期
     * @return 如果d1>d2返回 月数差 否则返回0
     */
    public static int yearsBetween(Date date1, Date date2) {
        Calendar beforeDate = Calendar.getInstance();
        beforeDate.setTime(date1);
        Calendar afterDate = Calendar.getInstance();
        afterDate.setTime(date2);
        beforeDate.add(Calendar.YEAR, 1);
        int years = 0;
        if (beforeDate.after(afterDate)) {
            return years;
        }
        while (beforeDate.before(afterDate)) {
            years++;
            beforeDate.add(Calendar.YEAR, 1);
        }
        return years;
    }

    public static Date getDateAfterDays(Date date, Integer days) {
        if (Objects.isNull(date)) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_YEAR, days);
        return calendar.getTime();
    }

    public static String convertTimeRules(Long time) {
        String result = "0秒";
        if (time != null && time > 0) {
            Long ss = time % 60;
            Long mi = time / 60;
            if (mi > 60) {
                Long hh = mi / 60;
                mi = mi % 60;
                if (hh > 24) {
                    Long day = hh / 24;
                    hh = hh % 24;
                    result = day + "天" + hh + "时" + mi + "分" + ss + "秒";
                } else {
                    result = hh + "时" + mi + "分" + ss + "秒";
                }
            } else {
                result = mi + "分" + ss + "秒";
            }
        }
        return result;
    }

    /**
     * 共读需要 获取当前时间
     *
     * @return Date
     */
    public static Date getCurrentYmdDate() {
        return formatToDate(getCurrentTime(YYYYMMDD), YYYYMMDD);
    }

    /**
     * 格式化时间转字符串
     *
     * @param date   日期
     * @param format 格式
     * @return 字符串日期
     */
    public static String formatDate(Date date, String format) {
        if (date != null) {
            SimpleDateFormat sdf = new SimpleDateFormat(format);
            return sdf.format(date);
        } else {
            return "";
        }
    }

    /**
     * 生成指定时间的年月日时分字符串
     *
     * @param date 指定时间
     * @return 格式化字符串
     */
    public static String formatDate(Date date) {
        if (date != null) {
            return formatDate(date, YYMMDD_HHMM);
        } else {
            return "";
        }
    }

    /**
     * 获取当前时间前一天的年月日字符串
     *
     * @return
     */
    public static String getLastDayYmdStr() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, -1);
        Date d = cal.getTime();
        SimpleDateFormat sp = new SimpleDateFormat(YYMMDD);
        return sp.format(d);
    }

    /**
     * 获取当前月的上一个月
     *
     * @return
     */
    public static Date getLastMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date); // 设置为当前时间
        calendar.add(Calendar.MONTH, -1);
        return calendar.getTime();
    }

    /**
     * 获取当前时间前一天的年月日字符串
     *
     * @return
     */
    public static String getLastDayYmdStr(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DATE, -1);
        Date d = cal.getTime();
        SimpleDateFormat sp = new SimpleDateFormat(YYMMDD);
        return sp.format(d);
    }

    /**
     * 获取指定日期月份的第一天
     *
     * @param date date
     * @return 当月第一天
     */
    public static Date getMonthStart(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int index = calendar.get(Calendar.DAY_OF_MONTH);
        calendar.add(Calendar.DATE, (1 - index));
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }


    /**
     * 获取当前时间时间的下一个月时间
     *
     * @param date
     * @return
     */
    public static Date getNextDateMinTime(Date date) {
        return getMinTimeOfDate(getNext(date));
    }

    /**
     * 获取指定日期的开始时间
     *
     * @param date
     * @return
     */
    public static Date getTimeStart(Date date){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取当前时间时间的下一个月时间
     *
     * @param date
     * @return
     */
    public static Date getMonthNextDate(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.MONTH, 1);
        return c.getTime();
    }

    /**
     * 获取当前时间的上一个月时间
     *
     * @param date
     * @return
     */
    public static Date getMonthLastDate(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.MONTH, -1);
        return c.getTime();
    }

    /**
     * 判断日期是否是该月的第一天
     *
     * @param date 日期
     * @return 是否
     */
    public static boolean isFirstDayOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.DAY_OF_MONTH) == 1;
    }

    /**
     * 获取某月每天
     *
     * @param date 日期
     * @return 天数
     */
    public static List<Date> getDayArray(Date date) {
        List<Date> timeUnit = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int day = calendar.getActualMaximum(Calendar.DATE);
        for (int i = 1; i <= day; i++) {
            calendar.set(Calendar.DAY_OF_MONTH, i);
            timeUnit.add(calendar.getTime());
        }
        return timeUnit;
    }

    /**
     * 获取指定日期月份的最后一天
     *
     * @param date date
     * @return 当月最后一天
     */
    public static Date getMonthEnd(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, 1);
        int index = calendar.get(Calendar.DAY_OF_MONTH);
        calendar.add(Calendar.DATE, (-index));
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        return calendar.getTime();
    }

    /**
     * 指定日期的下一月日期
     *
     * @param date date
     * @return 指定日期的下一月日期
     */
    public static Date getNextMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, 1);
        return calendar.getTime();
    }

    public static Date getTimeOnlyDate(Date date) {
        Date newDate;
        String dateStr = new SimpleDateFormat(DateHelper.YYYYMMDD000000).format(date);
        try {
            newDate = new SimpleDateFormat(YYYYMMDD_HHMMSS).parse(dateStr);
        } catch (ParseException e) {
            log.error("发生异常", e);
            newDate = date;
        }
        return newDate;
    }

    /**
     * 获取本周第一天，0时
     *
     * @return
     */
    public static Date getThisWeekMonday() {
        Calendar cal = Calendar.getInstance();
        // 设置星期一为一个星期的第一天
        cal.setFirstDayOfWeek(Calendar.MONDAY);
        cal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    // 本周末
    public static Date getThisWeekSunday() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, -1);
        cal.add(Calendar.DATE, 1 * 7);
        return cal.getTime();
    }

    // 上周一
    public static Date getLastWeekMonday() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);
        cal.add(Calendar.DATE, -1 * 7);
        return cal.getTime();
    }

    // 上周日
    public static Date getLastWeekSunday() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, -1);
        return cal.getTime();
    }

    // 本月第一天
    public static Date getThisMonthStartDay() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.DAY_OF_MONTH, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    // 本月最后一天
    public static Date getThisMonthEndDay() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.DAY_OF_MONTH, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, -1);

        cal.add(Calendar.MONTH, 1);
        return cal.getTime();
    }

    // 上月第一天
    public static Date getLastMonthStartDay() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.DAY_OF_MONTH, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);

        cal.add(Calendar.MONTH, -1);
        return cal.getTime();
    }

    // 上月末
    public static Date getLastMonthEndDay() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.DAY_OF_MONTH, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, -1);
        return cal.getTime();
    }

    // 本年第一天
    public static Date getThisYearStartDay() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.DAY_OF_YEAR, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);

        return cal.getTime();
    }

    // 本年最后一天
    public static Date getThisYearEndDay() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.DAY_OF_YEAR, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, -1);

        cal.add(Calendar.YEAR, 1);
        return cal.getTime();
    }

    // 上年第一天
    public static Date getLastYearStartDay() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.DAY_OF_YEAR, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);

        cal.add(Calendar.YEAR, -1);
        return cal.getTime();
    }

    // 上年末
    public static Date getLastYearEndDay() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.DAY_OF_YEAR, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, -1);
        return cal.getTime();
    }

    /**
     * 获取前后时间
     *
     * @param currentDate  当前时间
     * @param calendarType 类型
     * @param amount       相差数
     * @return
     */
    public static Date getBeforeOrAfterTime(Date currentDate, int calendarType, int amount) {
        if (ObjectUtils.isEmpty(currentDate)) {
            currentDate = new Date();
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentDate);
        calendar.add(calendarType, amount);
        return calendar.getTime();
    }


    /**
     * 获取指定日期月份的最后一天
     *
     * @param date date
     * @return 当月最后一天
     */
    public static Date getDayEnd(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        return calendar.getTime();
    }

    //根据当前日期获得最近n周的日期区间（不包含本周）
    public static Date getFromToDate(Date date, int n, int option, int k) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK) - 1;
        int offset = 0 == option ? 1 - dayOfWeek : 7 - dayOfWeek;
        int amount = 0 == option ? offset - (n - 1 + k) * 7 : offset - k * 7;
        calendar.add(Calendar.DATE, amount);
        return calendar.getTime();
    }

    /**
     * 获取当前时间上一天的开始时间
     *
     * @return
     */
    public static Date getLastDayStartTime(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DATE, -1);
        setMinTimeOfDay(cal);
        return cal.getTime();
    }

    /**
     * 获取当前时间上一天的开始时间
     *
     * @return
     */
    public static Date getLastDayEndTime(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DATE, -1);
        setMaxTimeOfDay(cal);
        return cal.getTime();
    }

    /**
     * 获取当前时间上周的开始时间
     *
     * @param date
     * @return
     */
    public static Date getLastWeekStartTime(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.setTime(getFromToDate(date, 1, 0, 1));
        setMinTimeOfDay(calendar);
        return calendar.getTime();
    }

    /**
     * 获取当前时间上周的结束时间
     *
     * @param date
     * @return
     */
    public static Date getLastWeekEndTime(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.setTime(getFromToDate(date, 1, 1, 1));
        setMaxTimeOfDay(calendar);
        return calendar.getTime();
    }

    /**
     * 获取指定日期的一天开始时间
     *
     * @param date
     * @return
     */
    public static Date getDateStartTime(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.setTime(date);
        setMinTimeOfDay(calendar);
        return calendar.getTime();
    }

    /**
     * 获取指定日期的一天结束时间
     *
     * @param date
     * @return
     */
    public static Date getDateEndTime(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.setTime(date);
        setMaxTimeOfDay(calendar);
        return calendar.getTime();
    }


    /**
     * 获取当前时间上月的开始时间
     *
     * @param date
     * @return
     */
    public static Date getLastMonthStartTime(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, -1);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMinimum(Calendar.DAY_OF_MONTH));
        setMinTimeOfDay(calendar);
        return calendar.getTime();
    }

    /**
     * 获取当前时间上月的结束
     *
     * @param date
     * @return
     */
    public static Date getLastMonthEndTime(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, -1);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        setMaxTimeOfDay(calendar);
        return calendar.getTime();
    }

    /**
     * 设置当天的开始时间
     *
     * @param calendar
     */
    private static void setMinTimeOfDay(Calendar calendar) {
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.MILLISECOND, 0);
    }

    /**
     * 设置当天的结束时间
     *
     * @param calendar
     */
    private static void setMaxTimeOfDay(Calendar calendar) {
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.MILLISECOND, 999);
    }

    /**
     * 获取指定时间前past天的日期
     *
     * @param date
     * @param past
     * @return
     */
    public static List<Date> getPastDayDate(Date date, Integer past) {
        List<Date> result = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        for (int i = past; i > 0; i--) {
            calendar.clear();
            //获取前面的时间用-负号
            calendar.setTime(date);
            calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) - i);
            result.add(calendar.getTime());
        }
        return result;
    }

    /**
     * 获取指定时间past周的周一的时间
     *
     * @param date
     * @param past
     * @return
     */
    public static List<Date> getPastWeekStartDate(Date date, Integer past) {
        List<Date> result = new ArrayList<>();
        for (int i = past; i > 0; i--) {
            Date weekDate = getFromToDate(date, i, 0, 1);
            result.add(weekDate);
        }
        return result;
    }

    /**
     * 获取当前时间past月的开始时间
     *
     * @param date
     * @param past
     * @return
     */
    public static List<Date> getPastMonthStartDate(Date date, Integer past) {
        List<Date> result = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        for (int i = past; i > 0; i--) {
            calendar.clear();
            calendar.setTime(date);
            calendar.add(Calendar.MONTH, -i);
            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMinimum(Calendar.DAY_OF_MONTH));
            result.add(calendar.getTime());
        }
        return result;
    }

    /**
     * 获取指定时间 年份的第一天
     *
     * @param year
     * @return
     */
    public static Date getDateYearStart(Date year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.setTime(year);
        calendar.set(Calendar.YEAR, calendar.get(Calendar.YEAR));
        calendar.set(Calendar.DAY_OF_YEAR, calendar.getActualMinimum(Calendar.DAY_OF_YEAR));
        setMinTimeOfDay(calendar);
        return calendar.getTime();
    }

    /**
     * 获取指定时间 年份最后一天
     *
     * @param year
     * @return
     */
    public static Date getDateYearEnd(Date year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.setTime(year);
        calendar.set(Calendar.YEAR, calendar.get(Calendar.YEAR));
        calendar.set(Calendar.DAY_OF_YEAR, calendar.getActualMaximum(Calendar.DAY_OF_YEAR));
        setMaxTimeOfDay(calendar);
        return calendar.getTime();
    }

    /**
     * iso时间转date
     *
     * @param isoDateStr
     * @return
     */
    public static Date isoDateStr2Date(String isoDateStr) {
        SimpleDateFormat sdf = new SimpleDateFormat(ISO_FORMAT_STR);
        Date time = null;
        try {
            time = sdf.parse(isoDateStr);
        } catch (ParseException e) {
            log.error("发生异常", e);
            log.info("An error occurred when isoDateStr2Date");
        }
        return time;
    }

    /**
     * 把时间戳转换为：时分秒
     *
     * @param millisecond ：毫秒，传入单位为毫秒
     */
    public static String getTimeString(final long millisecond) {
        if (millisecond < 1000) {
            return "00:00:00";
        }
        long second = millisecond / 1000;
        long seconds = second % 60;
        long minutes = second / 60;
        long hours = 0;
        if (minutes >= 60) {
            hours = minutes / 60;
            minutes = minutes % 60;
        }
        String secondString = "";
        String minuteString = "";
        String hourString = "";
        if (seconds < 10) {
            secondString = "0" + seconds;
        } else {
            secondString = seconds + "";
        }
        if (minutes < 10) {
            minuteString = "0" + minutes + ":";
        } else {
            minuteString = minutes + ":";
        }
        if (hours < 10) {
            hourString = "0" + hours + ":";
        } else {
            hourString = hours + ":";
        }
        return hourString + minuteString + secondString;
    }

    /**
     * 取日期的00:00:00
     *
     * @param date
     * @return
     */
    public static Date getMinTimeOfDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        setMinTimeOfDay(calendar);
        return calendar.getTime();
    }

    /**
     * 取日期的23:59:59
     *
     * @param date
     * @return
     */
    public static Date getMaxTimeOfDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        setMaxTimeOfDay(calendar);
        return calendar.getTime();
    }

    /**
     * 计算两个日期相差的分钟
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 两个日期相差的分钟
     */
    public static Integer minutesBetweenDate(Date startTime, Date endTime) {
        long l1 = endTime.getTime() - startTime.getTime();
        return BigDecimal.valueOf(l1).divide(BigDecimal.valueOf(1000 * 60L), 0, RoundingMode.UP).intValue();
    }

    /**
     * 计算两个日期相差的秒数
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 两个日期相差的秒数
     */
    public static Integer secondBetweenDate(Date startTime, Date endTime) {
        long l1 = endTime.getTime() - startTime.getTime();
        return BigDecimal.valueOf(l1).divide(BigDecimal.valueOf(1000), 0, RoundingMode.UP).intValue();
    }

    /**
     * 计算两个时间相差的分钟
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static Integer minutesBetweenTime(Date startTime, Date endTime) {
        Calendar calendarStart = Calendar.getInstance();
        calendarStart.setTime(startTime);
        calendarStart.set(2000, 1, 1);
        Calendar calendarEnd = Calendar.getInstance();
        calendarEnd.setTime(endTime);
        calendarEnd.set(2000, 1, 1);
        return minutesBetweenDate(calendarStart.getTime(), calendarEnd.getTime());
    }

    /**
     * 比较两个时间大小(忽略日期)
     *
     * @param date1
     * @param date2
     * @return
     */
    public static int compareTimeIgnoreDate(Date date1, Date date2) {
        Calendar calendar1 = Calendar.getInstance();
        calendar1.setTime(date1);
        Calendar calendar2 = Calendar.getInstance();
        calendar2.setTime(date2);

        calendar1.set(2000, 1, 1);
        calendar2.set(2000, 1, 1);

        return calendar1.getTime().compareTo(calendar2.getTime());
    }


    /**
     * 计算两个日期相差的年数
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static BigDecimal yearsBetweenDate(Date startTime, Date endTime) {
        long l1 = endTime.getTime() - startTime.getTime();
        return BigDecimal.valueOf(l1).divide(BigDecimal.valueOf(1000 * 60L * 60 * 24 * 365), 1, RoundingMode.DOWN);
    }

    /**
     * 计算两个日期的月数差
     *
     * @param date1 日期1
     * @param date2 日期2
     * @return 月数差
     */
    public static int getMonthDiff(Date date1, Date date2) {
        LocalDate localDate1 = parseDateToLocalDate(date1);
        LocalDate localDate2 = parseDateToLocalDate(date2);

        Period period = Period.between(localDate1.withDayOfMonth(1), localDate2.withDayOfMonth(1));
        int months = period.getMonths();
        int days = period.getDays();
        if (days > 0) {
            months++;
        }
        return months;
    }

    public static LocalDate parseDateToLocalDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        int dayOfMonth = calendar.get(Calendar.DAY_OF_MONTH);
        return LocalDate.of(year, month, dayOfMonth);
    }

    /**
     * 获取指定分钟后的时间
     *
     * @return
     */
    public static String getAppointedTime() {
// 获取当前时间
        LocalDateTime currentTime = LocalDateTime.now();

        // 增加十分钟
        LocalDateTime tenMinutesLater = currentTime.plusMinutes(10);

        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(YYMMDD_HHMM);

        // 格式化日期时间
        return tenMinutesLater.format(formatter);
    }

    public static String getHMSBySecond(Integer seconds) {
        if (seconds < 1) {
            return "00:00:00";
        }
        int hours = seconds / 3600; // 计算小时部分
        int minutes = (seconds % 3600) / 60; // 计算分钟部分
        int remainingSeconds = seconds % 60; // 计算余下的秒数
        return (hours < 10 ? "0" + hours : hours) + ":" + (minutes < 10 ? "0" + minutes : minutes) + ":" + (
            remainingSeconds < 10 ? "0" + remainingSeconds : remainingSeconds);
    }

    /**
     * 判断是否相差一个月
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return true 相差大于一个月，false 相差小于一个月
     */
    public static boolean isOneMonthApart(Date startTime, Date endTime) {
        LocalDate localDate1 = startTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localDate2 = endTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        // 计算日期差值
        Period period = Period.between(localDate1, localDate2);

        // 判断是否相差一个月
        return period.getMonths() > 1;
    }

    /**
     * 获取本周所有日期
     *
     * @return
     */
    public static List<Date> getDatesOfCurrentWeek() {
        List<Date> dates = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();

        //设置一周星期一为第一天
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        int firstDayOfWeek = calendar.getFirstDayOfWeek();
        calendar.set(Calendar.DAY_OF_WEEK, firstDayOfWeek);

        // 将calendar设置到本周第一天的开始时间
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        // 添加本周的所有日期 周一开始
        dates.add(calendar.getTime());
        for (int i = 0; i < 6; i++) {
            calendar.add(Calendar.DAY_OF_WEEK, 1);
            dates.add(calendar.getTime());
        }
        return dates;
    }

    // 传入isMax字段来控制选择较大还是较小的时间
    public static Date getDateByComparison(Date date1, Date date2, boolean isMax) {
        if (date1 == null) {
            return date2;
        }
        if (date2 == null) {
            return date1;
        }
        // 如果两个 Date 都不为 null，进行比较
        Date date3 = date1.compareTo(date2) > 0 ? date1 : date2;
        Date date4 = date1.compareTo(date2) < 0 ? date1 : date2;
        return isMax ? date3 : date4;
    }
}
