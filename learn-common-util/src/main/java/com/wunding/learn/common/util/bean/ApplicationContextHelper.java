package com.wunding.learn.common.util.bean;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> href="mailto:<EMAIL>">yuxueyi</a>
 * @version V7.0.0
 * @since 2022/5/13  18:17
 */
@Component
public class ApplicationContextHelper implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext context)
        throws BeansException {
        setApplicationContextProperty(context);
    }

    private static void setApplicationContextProperty(ApplicationContext context) {
        if (null == ApplicationContextHelper.applicationContext) {
            ApplicationContextHelper.applicationContext = context;
        }
    }

    public static ApplicationContext getApplicationContext() {
        return applicationContext;
    }

    public static Object getBean(Class<?> clazz) {
        return null == applicationContext ? null : applicationContext.getBean(clazz);
    }

    public static Object getBean(String beanName) {
        return getApplicationContext().getBean(beanName);
    }

    public static void cleanHolder() {
        applicationContext = null;
    }
}
